package com.ruicar.afs.cloud.product.enums;

import com.ruicar.afs.cloud.common.core.enums.AfsBaseEnum;
import com.ruicar.afs.cloud.common.core.enums.annotations.AfsEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * 状态是否
 * @date 2021/8/3 16:48
 */
public enum LimitTypeEnum implements AfsBaseEnum {

    /**
     * 合作商
     */
    @AfsEnum(key = "dealer", desc = "合作商")
    dealer,
    /**
     * 总额
     */
    @AfsEnum(key = "total", desc = "总额")
    total,
}
