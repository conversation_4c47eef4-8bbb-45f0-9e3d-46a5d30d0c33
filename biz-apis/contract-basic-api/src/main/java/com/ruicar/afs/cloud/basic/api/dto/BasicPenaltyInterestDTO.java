package com.ruicar.afs.cloud.basic.api.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruicar.afs.cloud.common.modules.contract.enums.ExpenseStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 2 * @Author: yangxi
 * 3 * @Date: 2022/6/23 10:27
 * 4
 */
@Getter
@Setter
@ToString
public class BasicPenaltyInterestDTO {
    /** 合同号 */
    @ApiModelProperty("合同号")
    private String contractNo;
    /** 客户姓名 */
    @ApiModelProperty("客户姓名")
    private String custName;
    /** 期数 */
    @ApiModelProperty("期数")
    private Integer termNo;
    /** 罚息总计 */
    @ApiModelProperty("罚息总计")
    private BigDecimal totalPenalty;
    /** 已收金额 */
    @ApiModelProperty("已收金额")
    private BigDecimal receivedAmt;
    /** 已豁免金额 批量豁免金额、单笔罚息豁免金额、银企异常豁免，记录此表。 因为批量豁免后面还可能会收取罚息 */
    @ApiModelProperty("已豁免金额 批量豁免金额、单笔罚息豁免金额、银企异常豁免，记录此表。 因为批量豁免后面还可能会收取罚息")
    private BigDecimal exemptedAmt;
    /** 真实豁免金额 银企直连异常豁免金额、单笔罚息豁免办理会累加 */
    @ApiModelProperty("真实豁免金额 银企直连异常豁免金额、单笔罚息豁免办理会累加")
    private BigDecimal realExemptionAmt;
    /** 逾期天数 */
    @ApiModelProperty("逾期天数")
    private Integer overdueDays;
    /** 逾期利率 */
    @ApiModelProperty("逾期利率")
    private BigDecimal overdueRate;
    /** 状态 枚举值：罚息未还清、罚息已还清 */
    @ApiModelProperty("状态 枚举值：罚息未还清、罚息已还清")
    private ExpenseStatusEnum status;
    /** 完成日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("完成日期")
    private Date completionDate;
    /** 开始结算日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始结算日期")
    private Date startDate;
    /** 结束结算日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束结算日期")
    private Date endDate;
    /** 还款日 */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("还款日")
    private Date repaymentDate;
    /** 罚息金额 */
    @TableField(exist = false)
    @ApiModelProperty("罚息金额")
    private BigDecimal penaltyAmt;
    /** 净逾期额 */
    @ApiModelProperty("净逾期额")
    private BigDecimal overdueAmt;
    /** 豁免单冲抵费用 */
    @ApiModelProperty("豁免单冲抵费用")
    private BigDecimal exemptBillMitigate;
    /** 罚息金额 */
    @TableField(exist = false)
    @ApiModelProperty("小计金额")
    private BigDecimal subtotalAmt;
}
