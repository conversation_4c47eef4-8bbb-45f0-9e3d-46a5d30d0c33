package com.ruicar.afs.cloud.basic.api.reappear;

import com.ruicar.afs.cloud.common.modules.contract.enums.BusinessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/8/28 21:45
 * @description:
 */
@Data
public class BasicMainInfoVo {

    /**
     * 合同号码
     */
    @ApiModelProperty("合同号码")
    private String contractNo;


    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String businessType;


    /**
     * start_date  合同开启时间
     */
    private Date time;


/**
 * end_date  合同到期时间+90天
 */
}
