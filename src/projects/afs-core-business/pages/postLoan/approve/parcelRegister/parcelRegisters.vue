<template>
    <div class="mt-30">
<!--        邮包登记池-->
        <Modal
            v-model="modalChoose"
            title="操作"
            @on-ok="chooseConfirm"
            @on-cancel="chooseCancel">
            <p>是否确认签收</p>
        </Modal>
        <Modal
            v-model="modalCancel"
            title="操作"
            @on-ok="clickCancel"
            >
            <Form ref="formCancel" :model="formCancel"  :label-width="80">
                <Form-item label="拒绝原因" prop="passwd">
                    <Input  v-model="formCancel"></Input>
                </Form-item>
            </Form>
        </Modal>
        <div class="d-flex">
                     <div></div>
                     <Button type="success" @click="addParcel">新增邮包登记</Button>
                 </div>
                <Form inline :label-width="140"  :model="condition">
                    <Row>
                        <Col span="8">
                        <FormItem label='寄件人' prop='waybillMailerName'>
                             <Input v-model="condition.waybillMailerName" placeholder="" ></Input>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label='运单/交接单编号' prop='waybillNo' >
                             <Input v-model="condition.waybillNo" placeholder="" ></Input>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label='邮寄时间' prop='' >
                            <Date-picker v-model="condition.waybillTime" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="选择日期" style="width: 100%"></Date-picker>
                        </FormItem>
                        </Col>
                    </Row>
                     <Row>
                        <Col span="8">
                            <FormItem label='经销商'>
                                <Input v-model="condition.channelName" placeholder="" ></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label='邮寄状态'>
                                 <Select v-model="condition.waybillOrderStatus">
                                   <Option v-for="item in dataDic.waybillOrderStatus" :value="item.value" :key="item.value"
                                           :label="item.title">
                                        {{ item.title }}
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label='签收时间'>
                                <DatePicker v-model="condition.signForTime" type="daterange" placement="bottom-end" placeholder="选择日期" style="width: 100%"></DatePicker>
                            </FormItem>
                        </Col>
                    </Row>
                     <Row>
                        <Col span="8">
                            <FormItem label='申请编号'>
                                <Input v-model="condition.applyNo" placeholder="" ></Input>
                            </FormItem>
                        </Col>
                    </Row>
                    <Form-item style="text-align: center">
                            <Button type="success" @click="query">查询</Button>
                            <Button type="success" style="margin-left:20px" @click="resetClick">重置</Button>
                    </Form-item>
                </Form>

                <div style="margin-top:20px">
                    <Table highlight-row :columns="receiptHeads" :data="historyData">
                        <template slot-scope="{ row, index }" slot="transaction">
                        <div class="btn-no-border">
                            <Button ghost style="margin-right:5px" v-if="row.waybillOrderStatus === 'notSend' || row.waybillOrderStatus  === 'inSend'"  type="primary" size="small" @click="updateParcel(row)">编辑</Button>
                            <Button ghost style="margin-right:5px" v-if="row.waybillOrderStatus === 'signFoNotConfirm'"  type="primary" size="small" @click="transaction(row)">办理</Button>
                            <Button ghost style="margin-right:5px" v-if="row.waybillOrderStatus === 'inSend'"  type="primary" size="small" @click="receiving(row)">签收</Button>
                            <Button ghost style="margin-right:5px" v-if="row.waybillOrderStatus === 'inSend'" type="primary" size="small" @click="accept(row)">拒收</Button>
                        </div>

                        </template>
                        <template slot="waybill" slot-scope="{ row, index }">
                            <div class="btn-no-border">
                                <a @click="waybillNumber(row)">{{row.waybillNo}}</a>
                            </div>
                        </template>
                        <template slot="waybillOrderStatus" slot-scope="{ row,index }">
                            <div class="btn-no-border">
                                <span>{{setDictTitleByType("waybillOrderStatus", row.waybillOrderStatus)}}</span>
                            </div>
                        </template>
                        <template slot="waybillExpressCompany" slot-scope="{ row,index }">
                            <div class="btn-no-border">
                                <span>{{setDictTitleByType("waybillExpressCompany", row.waybillExpressCompany)}}</span>
                            </div>
                        </template>
                    </Table>
                    <div class="d-flex">
                 <div></div>
                    <Page :total="queryData.total" show-elevator show-sizer :page-size="queryData.pageSize" :current="queryData.pageNumber"  show-total  @on-change="changepage" @on-page-size-change = 'indexChange'></Page>

                </div>
                </div>
    </div>
</template>

<script>
    import {
        listMessage,receiptHead,singMessage,updateStatus,updateNotStatus
    } from "../../../../../../projects/afs-core-business/api/afs-case/approve-parcel/approveParcel.js";
    import {getByTypes} from "_p/basic/api/admin/datadic";
    import {deepClone} from "@/libs/utils/ObjectClone";
    export default {
        name: '',
        data() {
            return {
                formCancel:"",
                modalChoose:false,
                clientName:"",
                modalCancel:false,
                // 贷后列表上传页面
                ajaxHistoryData:[],
                parcelNo:"",
                parcel:{
                    waybillMailerName:"",
                    waybillNo:"",
                    mailerStartTime:"",
                    channelName:"",
                    waybillTime:[],
                    submitStartTime:'',
                    submitEndTime:''
                },
                receiptHeads:receiptHead,
                historyData: [],
                condition:{
                    waybillOrderStatus: 'inSend'
                },
                queryData: {
                    pageNumber:1,
                    pageSize:10,
                    total:0,
                },
                information:{
                    parcelNo:""
                },
                dicArr: ["isDefault", "fileStatus", "archiveingStatus", "waybillOrderStatus","waybillExpressCompany"],
                dataDic: {},
            }
        },
        methods: {
            transaction(row){
                let myparams = {
                    waybillNo:row.waybillNo,
                    parcelNo:row.parcelNo,
                    waybillMailerName:row.waybillMailerName,
                    waybillMailerPhone:row.waybillMailerPhone,
                    waybillTime:row.waybillTime,
                    remarks:row.remarks,
                    channelName:row.channelName,
                    channelId:row.channelId
                }
                this.afs.newTab(this, 'projects/afs-channel/pages/postRegister/postRegister', '邮包办理', 'ios-view', myparams, 'parcelRegister' + row.parcelNo, [], true)
            },
            addParcel(){
            let myparams = {}
            this.afs.newTab(this,'projects/afs-channel/pages/postRegister/parcelNew','邮包新增','ios-view',myparams,'parcelRegister',[],true)
            },
            updateParcel(row){
                let params = {
                    parcelNo:row.parcelNo,
                    channelName:row.channelName,
                    channelId:row.channelId
                }
                this.afs.newTab(this, 'projects/afs-channel/pages/postRegister/parcelNew', '邮包办理', 'ios-view', params, 'parcelRegister' + row.parcelNo, [], true)
            },
            query(){
                this.queryData.pageNumber = 1;
                this.queryData.pageSize = 10;
                this.refer();
            },
            refer(){
                this.queryData.condition = this.condition;
                listMessage(this.queryData).then(res=>{
                   if(res.code === "0000"){
                       this.historyData = res.data.records
                       this.queryData.total = res.data.total
                   }
                })

            },
            resetClick(){
                this.condition={}
                this.queryData.pageNumber = 1;//设置为1
                this.refer();
            },
            indexChange(currentPage){
                console.log("当前PageSize",currentPage);
                this.queryData.pageSize = currentPage;
                this.queryData.pageNumber = 1;//设置为1
                this.refer();
            },
            changepage(index){
                console.log("当前index",index);
                this.queryData.pageNumber = index;
                this.refer();
            },
            waybillNumber(row){
                let params = {
                    parcelNo:row.parcelNo
                }
                this.afs.newTab(this, 'projects/afs-channel/pages/postRegister/filePost', '邮包邮寄页面', 'ios-add',
                    params,'filePost'+row.parcelNo, [], true);
            },
            getDicData(){
                getByTypes(this.dicArr).then(res => {
                    if (res.code === "0000") {
                        this.dataDic = deepClone(res.data);
                    }
                });
            },
            setDictTitleByType(v1, v2) {
                let result = "";
                if (!this.getDic(v1)) return '';
                this.getDic(v1).forEach(column => {
                    if (column.value == v2) {
                        result = column.title;
                    }
                });
                return result;
            },
            getDic(v) {
                let dic = []
                switch (v) {
                    case "fileStatus":
                        dic = this.dataDic.fileStatus;
                        break;
                    case "waybillOrderStatus":
                        dic = this.dataDic.waybillOrderStatus;
                        break;
                    case "waybillExpressCompany":
                        dic = this.dataDic.waybillExpressCompany;
                        break;
                    default:
                }
                return dic;
            },
            setFileType(v1,v2) {
                let result = "";
                if (!this.getDic(v1)) return '';
                this.getDic(v1).forEach(column => {
                    if (column.value == v2) {
                        result = column.title;
                    }
                });
                return result;
            },
            //签收
            receiving(row){
                this.modalChoose = true
                this.receivingParcelNo = row.parcelNo

            },
            //拒收
            accept(row){
                this.modalCancel = true
                this.receivingParcelNo =row.parcelNo
            },
            //确定签收
            chooseConfirm(){
                    updateStatus(this.receivingParcelNo).then(res=>{
                        if(res.code === '0000'){
                            this.refer()
                        }
                })
            },
            chooseCancel(){},
            //拒收
            clickCancel(){
                updateNotStatus(this.receivingParcelNo,this.formCancel).then(res=>{
                    if(res.code === '0000'){
                        this.formCancel=""
                        this.refer()
                    }
                })
            }

        },
        activated(){
            this.refer()
            this.getDicData();
            if(this.afs.getPageParams(this)){
                this.information.parcelNo = this.afs.getPageParams(this).parcelNo;
            }
        },

    }
</script>

<style lang="less" scoped>
            .mt-10{
        margin-top: 20px;

    }
    .j-sb{
            display: flex;
            justify-content: space-between;
            align-content: center;
        }
    .a-center{
        display: flex;
        justify-content: center;
    }
    .queryList{
        display: flex;
        margin-bottom: 20px;
        justify-content: center;
        .ml-10{
            margin-left: 20px;
        }
    }
    .mt-30{
        border: 1px solid #ccc;
        padding: 20px;
    }
    .pagination{
        margin-top: 30px;
    }
    .d-flex{
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }
    .message{
        text-align: center;
        color: #000;
        margin: 20px;
    }
    .ivu-form-inline /deep/ .ivu-form-item{
        display: block;
    }
</style>
