<style lang="less">
    @import "loanAnalyze.less";
</style>
<template>
    <div class="loanAnalyze"  style="padding:0px 2px;" >
        <Collapse v-model="values" style="display: flex;flex-direction: column;">
<!--            <Panel name="2">
                客户概况
                <div slot="content"  >
                    <Row style="margin-left: 30px">
                        <Col style="font-size: 14px">
                            车型:&nbsp&nbsp<Input type="text" style="width: 400px" v-model="custInfo.modelName" disabled >
                            </Input>
                            首付比例:&nbsp&nbsp<Input type="text" style="width: 100px" v-model="custInfo.downPayScale" disabled >
                            </Input>%
                            租赁期限:&nbsp&nbsp<Input type="text" style="width: 100px" v-model="custInfo.loanTerms" disabled >
                            </Input>
                            租赁金额:&nbsp&nbsp<Input type="text" style="width: 100px" v-model="custInfo.loanAmt" disabled>
                            </Input>
                            累加租赁金额:&nbsp&nbsp<Input type="text" style="width: 100px" v-model="custInfo.totalLoanAmt" disabled placeholder="请输入">
                            </Input>
&lt;!&ndash;                            <Button type="primary" @click="updateAddAmt" icon="md-refresh" ghost title="点击更新累加贷额"/>&ndash;&gt;
                        </Col>

                    </Row>
                    <br/>
                    <Row style="background-color: #fadb14">
                        <Table  width="100%" border  :columns="custInfoColums" :data="custInfo.custInfoList">
                            <template slot-scope="{ row, index }" slot="custRelation">
                                <span>{{setDictTitleByType("custRelation",row.custRelation)}}</span>
                            </template>
                            <template slot-scope="{ row, index }" slot="sex">
                                <span>{{setDictTitleByType("sex",row.sex)}}</span>
                            </template>
                            <template slot-scope="{ row, index }" slot="maritalStatus">
                                <span>{{setDictTitleByType("maritalStatus",row.maritalStatus)}}</span>
                            </template>
                        </Table>
                    </Row>
                </div>
            </Panel>-->
<!--            <Panel class="creditCardBo" name="2"  :style="getOrderByNavId('loanAnalyze_4')" id="hong_l_4">-->
<!--                量富报告-->
<!--                <span id="loanAnalyze_4"></span>-->
<!--                <div slot="content">-->
<!--                    <Table :columns="liangfuColumns" :data="liangfuData" border>-->
<!--                    </Table>-->
<!--                </div>-->
<!--            </Panel>-->
<!--            <Panel class="creditCardBo" name="3"  :style="getOrderByNavId('asset_140')" id="hong_c_140">
                信贷选项
                <span id="asset_140"></span>
                <div slot="content">
                    <Row  v-if="isCheck">
                        <div class="j-sb">
                            <div></div>
                            <div>
                                <Button  @click="() => saveApprove(false)" style="color:#fff;background:#22bd6f;">保存</Button>
                            </div>
                        </div>
                    </Row>
                    <Row v-for="(option,index) in caseCreditOptionList" :key="index" class="creditRow">
                        <div v-if="option.optionType=='special'" class="hongcreditLabel">{{option.optionName}}</div>

                        <div class="creditSpecial" v-if="option.optionType=='special'">
                            <Input :disabled="!routerParams.firstTrail && option.optionNo!='10016'" :autosize="{minRows: 2,maxRows: 5}" style="width:500px;margin:5px 0" type="textarea" size="100px" v-model="option.value"/>
                        </div>
                        <RadioGroup v-if="option.optionType=='normal'" v-model="option.value" :name="option.optionName"  class="radioGroup"
                                    @on-change="afterRadioChange" >
                            <div  class="hongcreditLabel">{{option.optionName}}</div>

                            <Radio :disabled="!routerParams.firstTrail" class="creditRadio" :key='index' v-for="(item,index) in getDicData(option.optionDicKey)" :label="item.value">
                                {{item.title}}
                            </Radio>
                        </RadioGroup>
                    </Row>
                </div>
            </Panel>-->
<!--            <Panel class="creditCardBo"  name="4" v-show="fraudDetailDataList && fraudDetailDataList.length > 0"
                :style="getOrderByNavId('asset_210')" id="hong_c_210">
                反欺诈反馈明细
                <span id="asset_210"></span>
                <div slot="content">
                    <Table :columns="fraudDetailColumns" :data="fraudDetailDataList" border ref="fraudDetailTable" closeable="false ">
                        <template slot-scope="{ row, index }" slot="action">
                            <a v-if="isCheck" @click="removeFraudRecord(row)">删除</a>
                        </template>
                    </Table>
                </div>
            </Panel> -->

            <Panel class="creditCardBo" name="5" :style="getOrderByNavId('asset_160')" id="hong_c_160">
                经销商备注
                <span></span><span id="asset_160"></span>
                <div slot="content" >
                    <h3  style="margin-left: 15px" v-html="remarks"></h3>
                </div>
            </Panel>
            <Panel class="creditCardBo" name="6" :style="getOrderByNavId('asset_150')" id="hong_c_150" v-if="$store.state.user.roles.toString().indexOf('ROLE_SALETEAM_MNG') == -1">
                审批历史记录
                <span id="asset_150"></span>
                <div slot="content">
                    <Row v-if="isRecord">
                        <Button  style="float: right;margin-right: 35px"   @click="showRecord" type="primary">录音记录</Button>
                    </Row>
                    <Row v-if="isTable && this.visitData.status === 'done'">
                        <Button  style="float: right" @click="showVisitLevel" type="primary">质量评分表</Button>
                    </Row>
                    <Row style="margin-left: -30px" class="honogrecordHistory" v-for="(record,index) in recordHisData" :key='index'>
<!--                        <Icon type="ios-contact" class="icon" :size="70"
                              v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'"/>
                        <Card :bordered="false" class="recordHistoryMsg" :style="record.style"
                              v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'">
                            <h4 slot="title">{{record.title}}</h4>
                            <h3 v-if="record.approveSuggest === 'suggestCondition' || record.approveSuggest === 'suggestConditionF'" style="margin-left: 15px">附条件原因：{{conditionReason}}。{{record.approveMessage}}</h3>
                            <h3 v-if="record.approveSuggest === 'suggestRejectF'" style="margin-left: 15px">拒绝原因：{{rejectReason}}</h3>
                            <h3 style="margin-left: 15px">{{record.content}}</h3>
                        </Card> -->
                        <Col span="24" style="display: flex;flex-direction: row;align-items: center;border-radius: 5px;">
                            <div style="margin-left: 10px;">
                                <Icon type="ios-contact" class="icon" :size="70"
                                      v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'"/>
                            </div>
                            <div :bordered="false" class="hong_recordHistoryMsg" :style="record.style"
                                  v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'">
                                <h4 slot="title">{{record.title}}</h4>
                                <h3 v-if="record.approveSuggest === 'suggestCondition' || record.approveSuggest === 'suggestConditionF'" style="margin-left: 15px">附条件原因：{{conditionReason}}。{{record.approveMessage}}</h3>
                                <h3 v-if="record.approveSuggest === 'suggestRejectF'" style="margin-left: 15px">拒绝原因：{{rejectReason}}</h3>
                                <h3 style="margin-left: 15px">{{record.content}}</h3>
                                <h4 v-if="record.approveRemark" class="load_hong_beizhu">{{record.approveRemark}}</h4>
                            </div>
                        </Col>
                    </Row>
                </div>
            </Panel>

            <Panel class="creditCardBo" name="24" :style="getOrderByNavId('asset_240')" id="hong_c_240" v-if="(isChannel && this.$store.state.user.roles.toString().indexOf('CREDIT_AUDIT_SPECIALIST') != -1) || (isChannel && this.$store.state.user.roles.toString().indexOf('ROLE_APPROVER_ADMIN') != -1) || (!isChannel && this.$store.state.user.roles.toString().indexOf('CREDIT_AUDIT_SPECIALIST') != -1) || (!isChannel && this.$store.state.user.roles.toString().indexOf('ROLE_APPROVER_ADMIN') != -1)">
                电核人工功能勾选项
                <span id="asset_240"></span>
                <div slot="content">
                    <div style="float: right;width: 50px;margin-top: 5px">
                        <Button @click="saveElectronuclearArtificial('formValidate')"  type="success"
                                style="background-color: #19be6b;border: none;margin-right: 10px;" v-if="isApproveOn">
                            保存
                        </Button>
                    </div>
                    <br>
                    <row>
                        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="250" :disabled="!isApproveOn" style="display: flex; flex-direction: row">
                            <div style="width:33%">
                                <Form-item label="法院网是否异常:" prop="isCourtNetwork">
                                    <Radio-group v-model="formValidate.isCourtNetwork">
                                        <Radio label="Y">是</Radio>
                                        <Radio label="N">否</Radio>
                                    </Radio-group>
                                </Form-item>
                                <Form-item label="人证是否可查:" prop="isLife">
                                    <Radio-group v-model="formValidate.isLife">
                                        <Radio label="1">非营运产品</Radio>
                                        <Radio label="2">是</Radio>
                                        <Radio label="3">否</Radio>
                                    </Radio-group>
                                </Form-item>
                                <Form-item label="是否为免核:" prop="isNuclearFree">
                                    <Radio-group v-model="formValidate.isNuclearFree">
                                        <Radio label="Y">是</Radio>
                                        <Radio label="N">否</Radio>
                                    </Radio-group>
                                </Form-item>
                                <Form-item label="营运司机资历:" prop="driverQualification">
                                    <Radio-group v-model="formValidate.driverQualification">
                                      <Radio label="1">新司机</Radio>
                                      <Radio label="2">老司机</Radio>
                                      <Radio label="3">非营运产品</Radio>
                                    </Radio-group>
                                </Form-item>
                            </div>
                            <div style="width:33%">
                                <Form-item label="首次是否清楚车辆信息:" prop="isCarInformation">
                                    <Radio-group v-model="formValidate.isCarInformation">
                                        <Radio label="Y">清楚</Radio>
                                        <Radio label="N">不清楚</Radio>
                                    </Radio-group>
                                </Form-item>
                                <Form-item label="驾驶证是否为本人:" prop="isSelf">
                                    <Radio-group v-model="formValidate.isSelf">
                                        <Radio label="Y">是</Radio>
                                        <Radio label="N">否</Radio>
                                    </Radio-group>
                                </Form-item>
                                <Form-item label="是否有打卡工资:" prop="isWages">
                                    <Radio-group v-model="formValidate.isWages">
                                        <Radio label="Y">是</Radio>
                                        <Radio label="N">否</Radio>
                                    </Radio-group>
                                </Form-item>
                            </div>
                            <div>
                                <Form-item label="月实际收入:" prop="income" >
                                    <InputNumber v-model.number="formValidate.income" placeholder="请输入实际月收入" ></InputNumber>
                                </Form-item>
                                <Form-item label="月负债金额:" prop="amountLiabilities">
                                    <InputNumber v-model.number="formValidate.amountLiabilities" placeholder="请输入月负债金额"></InputNumber>
                                </Form-item>
                                <Form-item label="还租比:" prop="rentingRatio">
                                    <Input v-model="rentingRatio" disabled placeholder="系统自动计算"></Input> %
                                </Form-item>
                            </div>
                        </Form>
                    </row>
                </div>
            </Panel>
        </Collapse>
       <Collapse v-model="contentvalues" id="otherContent" @on-change="otherClick">
           <Panel name="1" :hide-arrow="true">
               <div slot="content">
                   <div>
                        <Row v-if="this.taskId" style="margin-top: -30px">
                            <Form :model="suggestData" :label-width="80" class="suggestForm" ref="suggestForm">
                                <FormItem  label="审批建议" prop="approveSuggest" class="suggestSelect">
                                    <div style="float: left;width:350px">
                                        <Button type="success" :disabled="commentsModalVisible || suspendViewVisible || isSubmit"  size="small" @click="comments('submit',true,'formValidate')" style="margin-right: 10px;" v-show="!((this.userDefinedIndex==='END_NODE') && isrefusecheck)">通过</Button>
                                        <Button type="warning" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" v-if="(this.userDefinedIndex==='SECOND_NODE')" size="small" @click="comments('submit',false,'formValidate')" style="margin-right: 10px;">通过(需复核)</Button>
                                        <Button type="error" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" v-if="!(this.userDefinedIndex==='FIRST_NODE')" size="small" @click="comments('refuse')" style="margin-right: 10px;">拒绝</Button>
                                        <Button type="info" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" v-if="!(this.userDefinedIndex==='FIRST_NODE')"   size="small" @click="comments('back')" style="margin-right: 10px;">退回</Button>
                                        <Button type="info" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" size="small" @click="comments('back2dealer')"  style="margin-right: 10px;">退回经销商</Button>
                                        <Button type="primary" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" size="small" @click="comments('pending')" style="margin-right: 10px;">挂起</Button>
                                        <Button type="error" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" v-if="(this.userDefinedIndex==='SECOND_NODE')" size="small" @click="comments('refusecheck')">拒绝(需复核)</Button>
                                        <Button type="warning" :disabled="commentsModalVisible || suspendViewVisible || isSubmit" v-if="(this.userDefinedIndex==='SECOND_NODE')" size="small" @click="comments('faceReview')" style="margin-right: 10px;">转视频面审</Button>
                                    </div>
                                    <div style="float: left;padding-left: 30px;font-size: 14px" v-show="this.isCheck&&this.remindShowBo">添加共同承租人</div>
                                    <div style="float: left;padding-left: 30px;font-size: 14px" v-show="this.isCheck&&this.remindShowGu">添加保证人</div>
                                </FormItem>
                                <FormItem label="家访是否完成" prop="status" class="visitSelect displayItem" ref="visitItem"
                                          v-if="visitData.status=='done'" >
                                    <i-select class="formSelect" v-model="visitData.status" disabled>
                                        <i-option v-for="item in visitStatusDic" :value="item.value">{{item.title}}</i-option>
                                    </i-select>
                                    <Button type="primary" ghost @click="showVisitLevel" class="visitLevel" v-show="visitData.status=='done'">质量评分表</Button>
                                </FormItem>
                                <FormItem    prop="approveMessage"  ref="remarkItem"></FormItem>
                                <Spin fix size="large" v-if="submitApproveLoading"></Spin>
                            </Form>
                        </Row>
                    </div>
               </div>
           </Panel>
        </Collapse>
        <div>
            <Modal title="家访质量评分表"
                   v-model="visitLevelModalVisible"
                   :width="300"
                   :mask-closable='false'
            >
                <Form :model="visitData"  :label-width="100" :disabled="!routerParams.firstTrail">
                    <FormItem prop="visitDoneLevel" label="家访目标完程度" >
                        <InputNumber :min="0" :max="5" v-model="visitData.visitDoneLevel" />
                    </FormItem>
                    <FormItem prop="visitCarefulLevel" label="走访细致程度">
                        <InputNumber :min="0" :max="5"  v-model="visitData.visitCarefulLevel" />
                    </FormItem>
                    <FormItem prop="visitReportLevel" label="报告撰写质量" >
                        <InputNumber :min="0" :max="5" v-model="visitData.visitReportLevel" />
                    </FormItem>
                    <FormItem prop="visitFileLevel" label="资料收集质量" >
                        <InputNumber :min="0" :max="5" v-model="visitData.visitFileLevel" />
                    </FormItem>
                    <FormItem prop="visitGrade" label="总分">
                        <InputNumber readonly v-model="visitData.visitGrade" />
                    </FormItem>
                    <FormItem prop="visitSuggest" label="意见" >
                        <Input type="textarea" v-model="visitData.visitSuggest" />
                    </FormItem>
                </Form>
                <div slot="footer" v-show="routerParams.firstTrail">
                    <Button type="primary" size="small" @click="saveVisitLevel" >保存</Button>
                    {{visitGradeSum}}
                </div>
            </Modal>
        </div>

        <div>
            <Modal title="录音记录" v-model="recording" draggable

                   :mask-closable='false'
                   :width="870" :styles="{top: '80px'}">
                <Row>
                    <Table border :columns="recordingColums" :data="recordingList" height="300">
                        <template slot-scope="{ row, index }" slot="custRelation">
                            <span>{{setDictTitleByType("custRelation",row.custRelation)}}</span>
                        </template>
                        <template slot-scope="{ row, index }" slot="recordUrl">
                            <Button style="margin-right:5px" type="primary" icon="ios-play" size="small" @click="playInfo(row)">播放
                            </Button>
                        </template>
                        <template slot-scope="{ row, index }" slot="recordText">
                            <Button style="margin-right:5px" type="primary" icon="ios-paper" size="small" @click="showText(row)">阅读
                            </Button>
                            <Modal :width="450" v-model="isShowText"
                                   :closable="false"
                                   :mask-closable='false' title="录音文本"  draggable class="isShowText">
                                <div style="font-weight: bolder">{{recordTextInfo}}
                                </div>
                                <div slot="footer">
                                    <Button type="text" @click="backGo">关闭</Button>
                                </div>
                            </Modal>
                        </template>
                    </Table>
                </Row>
                <div slot="footer">
                    <Button type="text" @click="backFinishGo">关闭</Button>
                </div>
            </Modal>
        </div>
        <div>
            <Modal
                v-model="isSubmit"
                :styles="{top: '360px',width: '420px',right: '180px'}"
                title="确认"
                :draggable="true"
                :mask="false"
                :mask-closable="false"
                :closable="false">
                <h3 style="margin-left: 30px;color: #17233d">确认提交?</h3>
                <div slot="footer">
                    <Row style="margin-left: 10px">
                        <Button class="tailButton normalButton"  size="small" @click="cancel">取消</Button>
                        <Button v-if="isComments=='pending'" :loading="submitApproveLoading" class="tailButton normalButton" size="small" @click="submitSuspend">确认</Button>
                        <Button v-else :loading="submitApproveLoading" class="tailButton normalButton" size="small" @click="ok">确认</Button>
                    </Row>
                </div>
            </Modal>
        </div>
        <div>
            <Modal
                v-model="isFraud"
                :styles="{top: '360px',width: '420px',right: '180px'}"
                title="反欺诈触发"
                @on-ok="isOk"
                @on-cancel="isCancel"
                :mask-closable="false"
                :closable="false">
                <h3 style="margin-left: 30px;color: #17233d">您确定要手工触发反欺诈吗?</h3>
            </Modal>
        </div>
        <div>
            <Modal title="审批意见"
                   v-model="commentsModalVisible"
                   :width="850"
                   :mask-closable='false'
                   :draggable="true"
                   :mask="false"
            >
                <Form :model="suggestData"  :label-width="1">
                    <div>
                        <Input v-show="isComments=='submit'" style="margin-left: 20px; width: 300px" v-model="flowDict.submitReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('submit')"  placeholder="请选择通过原因"></Input>
                        <Input v-show="isComments=='refuse' || isComments=='refusecheck'" style="margin-left: 20px; width: 300px" v-model="flowDict.refuseReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('refuse')"  placeholder="请选择拒绝原因"></Input>
                        <Input v-show="isComments=='pending'" style="margin-left: 20px;width: 300px" v-model="flowDict.pendingReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('pending')" placeholder="请选择挂起原因"></Input>
                        <i-select :disabled="flowDict.backPoint.length==0"  style="width:160px;margin-left: 20px;" v-model="flowDict.backPointId" @on-change="setRemindRemark"
                                  class="templateSelect" v-if="isComments=='back' && flowDict.backPoint.length" clearable placeholder="请选择退回节点"
                        >
                            <i-option v-for="(item,index) in flowDict.backPoint" :value="item.nodeId" :key="index">{{item.nodeName}}</i-option>
                        </i-select>
                         <Input v-if="(this.userDefinedIndex==='FIRST_NODE') && (isComments=='back' || isComments=='back2dealer')" style="margin-left: 20px; width: 300px" v-model="flowDict.csbackReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('back')"  placeholder="请选择退回原因"></Input>
                        <Input v-if="!(this.userDefinedIndex==='FIRST_NODE') && (isComments=='back' || isComments=='back2dealer')" style="margin-left: 20px; width: 300px" v-model="flowDict.scspbackReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('back')"  placeholder="请选择退回原因"></Input>
                        <Input v-show="isComments=='faceReview'" style="margin-left: 20px; width: 300px" v-model="flowDict.faceReviewReasonStr" readonly icon="ios-search" @on-focus="onModalFocus('faceReview')"  placeholder="请选择转视频面审原因"></Input>
<!--                         <i-select  style="margin-left: 500px;" v-model="suggestTemplateId" class="templateSelect" v-if="isCheck"
                                   clearable
                                   @on-clear="clearSelectTemplate"
                                   @on-change="templateChange"

                        >
                            <i-option v-for="item in suggestTemplateData" :value="item.templateContent">{{item.templateName}}</i-option>
                        </i-select>-->
                         <Button v-show="isComments!='pending'" @click="showRecord" type="error">录音记录</Button>
                    </div>
                    <FormItem prop="approveRemark" >
                        <Input type="textarea" v-model="suggestData.approveRemark"
                               :rows="6"
                               class="remarkTextarea"
                               @on-change="setRemindRemark"
                               style="width: 745px;font-weight: 900;font-size:16px;"
                               placeholder="请输入备注" maxlength="1024" />
                    </FormItem>
                </Form>
                <Row style="margin-left: 20px" v-show="hasBackOption">
                    <RadioGroup v-model="backOption" >
                        <Radio label="1">
                            <span>退回直达</span>
                        </Radio>
                        <Radio label="2">
                            <span>退回重审</span>
                        </Radio>
                    </RadioGroup>
                </Row>
                <div slot="footer">
                    <Row style="margin-left: 10px">
                        <Button class="tailButton normalButton"  size="small" @click="cancelSubmit">取消</Button>
                        <Button class="tailButton normalButton" size="small" @click="submitApprove">提交</Button>
                        <!-- <Button class="tailButton cheatButton" icon="md-add" size="small" @click="discoverFraud">发现欺诈</Button>
                        <Button class="tailButton cheatButton" icon="md-return-right" size="small" @click="emitAntiFraud">手工触发反欺诈</Button> -->
                    </Row>
                </div>
            </Modal>
            <!-- 拒绝原因 退回原因 弹窗 -->
            <checkRadio :radioData="flowDict.submitReason" :isShowRemindModel="submitModalVisiable" @onSubmit="onModalSubmit" @cancle="submitModalVisiable=false"></checkRadio>
            <!-- <check-box-modal  title="通过原因" :data="flowDict.submitReason" :visiable="submitModalVisiable" @cancle="submitModalVisiable=false" @onSubmit="onModalSubmit"></check-box-modal> -->
            <check-box-modal title="挂起原因" :data="flowDict.pendingReason" :visiable="pendingModalVisiable" @cancle="pendingModalVisiable=false" @onSubmit="onModalPendingSubmit"></check-box-modal>
            <checkRadio title="拒绝原因" :radioData="flowDict.refuseReason" :isShowRemindModel="refuseModalVisiable" @onSubmit="onModalRefuseSubmit" @cancle="refuseModalVisiable=false"></checkRadio>
<!--            <check-box-modal  title="拒绝原因" :data="flowDict.refuseReason" :visiable="refuseModalVisiable" @cancle="refuseModalVisiable=false" @onSubmit="onModalRefuseSubmit"></check-box-modal>-->
            <check-box-modal v-if="!(this.userDefinedIndex==='FIRST_NODE')" title="退回原因" :data="flowDict.scspbackReason" :visiable="backModalVisiable" @cancle="backModalVisiable=false" @onSubmit="onModalScspBackSubmit"></check-box-modal>
            <check-box-modal v-if="(this.userDefinedIndex==='FIRST_NODE')" title="退回原因" :data="flowDict.csbackReason" :visiable="backModalVisiable" @cancle="backModalVisiable=false" @onSubmit="onModalCsBackSubmit"></check-box-modal>
            <check-box-modal v-if="(this.userDefinedIndex==='SECOND_NODE')" title="转视频面审原因" :data="flowDict.faceReviewReason" :visiable="faceReviewVisiable" @cancle="faceReviewVisiable=false" @onSubmit="onModalFaceReviewSubmit"></check-box-modal>
        </div>
        <!-- callStep:22 调用步骤常规审批固定值 标识 案件初审疑似欺诈 -->
        <tortoise-fraud-push v-model="fraudModalVisible" :apply-no="applyNo" :call-step="22" @onClose="cancelDiscoverFraud"/>
    </div>
</template>

<script>
    import {mapGetters} from "vuex";
    import {
        getPigeonType,
        notifyUsersUsedTypes
    } from '_p/basic/api/image/image-pigeon';
    import checkRadio from './checkRadio.vue'
    import {
        queryRemindListByApplyNo,
        queryCustSummaryInfoApplyNo,
        cancelRemindtById,
        queryCode,
        querySuspendStatus,
        getArtificial,
        saveInfo
    } from "@/projects/afs-core-business/api/afs-case/approve/loanAnaylyze";
    import {
        getRemark,checkReviewInformation
    } from "@/projects/afs-core-business/api/afs-case/infomationDetail/assetDetail";
    import {
        queryRecordListByApplyNo,queryRecordListAll
    } from "@/projects/afs-core-business/api/afs-case/approve-tel/approveTel";

    import {
        getByTypes
    } from "_p/basic/api/admin/datadic";
    import {
        modifyCaseSuspend
    } from "@/projects/afs-core-business/api/afs-case/approve/taskpoolWait.js";
    import {getDictDataByType} from "../../../../../../basic/api/admin/datadic";
    import {listTemplateByCurrentUser} from "../../../../../api/afs-case/credit-template/creditTemplate";
    import {
        queryApprove,
        saveApprove,
        submitApprove,
        getOverduePercentage,
        querySomeApprove,
        getFlowCmd,
        getRefuseCheck
    } from "../../../../../api/afs-case/approve/approveTask";
    import {
        handInvokeAntiFraud, listAntiFraudPush, removeFraudRecord,
    } from "../../../../../api/afs-case/tortoise/tortoise";
    import {queryVisitLevel,saveVisitLevel,queryCaseLiangfuReport} from "../../../../../api/afs-case/approve-visit/approve-visit-level";
    import {listCreditOptionInfos,listByApplyNo} from "../../../../../api/afs-case/credit-option/creditOptionInfo";
    import {listHistoryContent} from "../../../../../api/afs-case/approve/loanAnaylyze";
    import TortoiseFraudPush from "../../tortoise/tortoise-fraud-push";
    //更新累加贷额
    import {updateAddAmt} from "../../../../../api/afs-case/approve/regularApproveDetail";
    // 工作流 字典
    import {
        getReasonDict,getFlowPonit
    } from "@/projects/afs-core-business/api/afs-case/approve-todo/approve-todo";
    // 拒绝原因 退回原因 checkbox
    import CheckBoxModal from "./checkbox.vue";
    export default {
        name: "loan-analyze",
        components: {TortoiseFraudPush,CheckBoxModal,checkRadio},
        props: {
            orderList: {
              type: Array,
              default: () => []
            },
            applyNo: {
                required: false,
                default:()=>{
                    return '';
                }
            },
            stageId:{
                type:[String,Number],
                default:''
            },
            taskId:{
                type:[String],
                default:''
            },
            userDefinedIndex:{
                type:[String],
                default:''
            },
            packageId:{
                type:[String],
                default:''
            },
            templateId:{
                type:[String],
                default:''
            },
            isCheck: {
                type: Boolean
            },
            tabName:{
                type:String

            },
            isLung: {
                type: Boolean
            },
            businessStateIn: {
                type: String,
                default: ""
            },
            isChannel:{
                type:Boolean
            }
        },
        data:function () {
            return {
                pigeonListType: '',
                hasBackOption:false,
                submitButton:false,
                backOption:"1",
                contentvalues:['1'],
                list: this.orderList,
                isRecord:false,
                remindShowBo:false,
                remindShowGu:false,
                isSubmit: false,
                isFraud: false,
                isTable: false,
                classStyle: 'msgTextarea',
                isNumber: 0,
                applyNumber:'',
                conditionReason: '',
                rejectReason: '',
                suggestConditionFTypeList: [],
                suggestRejectFTypeList: [],
                templateIdstageNumber:'',
                values:["1","2","3","4","5","7","8"],
                recording:false,
                dicKeys: [
                    "msgType","custRelation","sex","maritalStatus"
                ],
                suspendViewVisible:false,
                modalTitle: "标签信息",
                isApproveOn: false,
                dataDic:{},
                isrefusecheck: false,
                remindColums:[
                    {title: "操作",key: "action",width: 80,align: "center",slot:"action"},
                    {title: "留言类型",key: "remindType",width: 130,align: "center",slot:"remindType"},
                    {title: "处理人",key: "disposeUser",width: 130,align: "center"},
                    {title: "处理时间",key: "disposeTime",width: 160,align: "center"},
                    {title: "处理意见",key: "remindContent",minWidth: 200,align: "left"}

                ],
                remindList:[],
                custInfo:{
                    custInfoList:[]
                },
                custInfoColums:[
                    {title: "与承租人关系",key: "custRelation",minWidth: 100,align: "center",slot:"custRelation"},
                    {title: "姓名",key: "custName",minWidth: 100,align: "center"},
                    {title: "性别",key: "sex",width: 80,align: "center",slot:"sex"},
                    {title: "年龄",key: "age",width: 80,align: "center"},
                    {title: "婚姻",key: "maritalStatus",width: 80,align: "center",slot:"maritalStatus"},
                    {title: "居住地",key: "livingAddress",minWidth: 120,align: "center"},
                    {title: "工作地",key: "workAddress",minWidth: 120,align: "center"},
                    {title: "上牌地",key: "plateAddress",minWidth: 120,align: "center"},
                    {title: "购车地",key: "purchaseAddress",minWidth: 100,align: "center"},
                    {title: "工作单位",key: "unitName",minWidth: 100,align: "center"}
                ],
                recordingColums:[
                    {title: "录音",key: "recordUrl",width: 90,align: "center",slot:"recordUrl"},
                    {title: "文本",key: "recordText",width: 90,align: "center",slot:"recordText"},
                    {title: "申请编号",key: "applyNo",width: 110,align: "center",},
                    {title: "与申请人关系",key: "custRelation",width: 120,align: "center",slot:"custRelation"},
                    {title: "坐席姓名",key: "userName",width: 100,align: "center"},
                    {title: "客户姓名",key: "custName",width: 120,align: "center"},
                    {title: "通讯时长",key: "communicatTime",width: 100,align: "center"},
                    {title: "被叫号码",key: "phone",width: 120,align: "center"},
                    {title: "开始时间",key: "callTime",width: 150,align: "center"},
                    {title: "挂机时间",key: "endTime",width: 150,align: "center"},

                ],
                formValidate:{
                    applyNo:'',
                    isCourtNetwork:'',
                    isLife:'',
                    isNuclearFree:'',
                    isCarInformation:'',
                    isSelf:'',
                    isWages:'',
                    income:'',
                    amountLiabilities:'',
                    rentingRatio:'',
                    rent:'',
                    driverQualification:'',
                },
                ruleValidate:{
                    income:[
                        {required: true, message:'月实际收入不能为空', trigger:'blur',type:'number'}
                    ],
                    amountLiabilities:[
                        {required: true, message:'月负债不能为空', trigger:'blur',type:'number'}
                    ],
                    isWages:[
                        {required: true, message:'请选择是否有打卡工资', trigger:'change'}
                    ],
                    isNuclearFree:[
                        {required: true, message:'请选择是否为免核', trigger:'change'}
                    ],
                    isCarInformation:[
                        {required: true, message:'请选择是否清楚车辆信息', trigger:'change'}
                    ],
                    isSelf:[
                        {required: true, message:'请选择驾驶证是否为本人', trigger:'change'}
                    ],
                    isCourtNetwork:[
                        {required: true, message:'请选择法院网是否异常', trigger:'change'}
                    ],
                    isLife:[
                        {required: true, message:'请选择人证是否可查', trigger:'change'}
                    ],
                    driverQualification:[
                      {required: true, message:'请选择营运司机资历', trigger:'change'}
                    ],
                },
                rent:'',
                recordingList:[],
                creditOptions:[],
                creditData:[],
                creditDataDicKeys:'',
                suggestTemplateId:'',
                uncertainCode: '',
                suggestTemplateData:[],
                recordHisData:[],
                suggestData:{},
                approveSuggestDicKey:'approveSuggest',
                approveSuggestDic:[],
                currentReasonDic:[],
                visitStatusDicKey:'visitStatus',
                visitStatusDic:[],
                formRemarkTitle:'',
                visitData:{},
                change:false,
                visitLevelModalVisible:false,
                commentsModalVisible:false,
                reasonVisible:false,
                remarkVisible:false,
                submitLoading:false,
                submitApproveLoading:false,
                reasonSelectMultiple:true,
                routerParams:{},
                approveEnd: false, // 案件常规审批是否结束
                isComments: "",
                isFinallyAgree:false,
                businessStateInList: [
                    "approved",
                    "reject",
                    "repeal",
                    "conditionalApproval",
                    "cancelConditional"
                ],
                suggestConst: {
                    check: {
                        title: "建议核准",
                        val: "suggestCheck"
                    },
                    condition: {
                        title: "建议附条件核准",
                        val:"suggestCondition"
                    },
                    reject: {
                        title: "建议拒绝",
                        val:"suggestReject"
                    },
                    visit: {
                        title: "建议家访",
                        val:"suggestVisit"
                    },
                    cancel: {
                        title: "撤销",
                        val:"suggestCancel"
                    },
                    rejectFinal: {
                        title: "拒绝",
                        val:"suggestRejectF"
                    },
                    checkFinal: {
                        title: "核准",
                        val:"suggestCheckF"
                    },
                    conditionFinal: {
                        title: "附条件核准",
                        val:"suggestConditionF"
                    },
                    sendBack:{
                        title:"退回",
                        val:"back"
                    },
                    sendBackToDealer:{
                        title: "退回至经销商",
                        val: "sendBackToDealer"
                    },
                    reformist:{
                        title:"改派",
                        val: "suggestReformist"
                    },
                    submit:{
                        title:"通过",
                        val: "submit"
                    },
                    antiFraud:{
                        title:"反欺诈",
                        val: "antiFraud"
                    },
                    back:{
                        title:"退回",
                        val: "back"
                    },
                    refuse:{
                        title:"拒绝",
                        val: "refuse"
                    },
                    back2dealer:{
                        title:"退回经销商修改",
                        val: "back2dealer"
                    }
                },
                fraudModalVisible:false,
                fraudModalLoading:false,
                fraudDetailColumns:[
                    {title: "反馈时间",key: "operateDate",align: "center"},
                    {title: "反馈节点",key: "callStepDesc",align: "center"},
                    {title: "反馈人",key: "operateStaffName",align: "center"},
                    {title: "状态",key: "pushResult",align: "center"},
                    {title: "欺诈表现",key: "fraudType",align: "center"},
                    {title: "经销商参与",key: "dealerJoin",align: "center"},
                    {title: "疑似欺诈案件详情",key: "detailMessage",minWidth: 500,align: "center"},
                    {title: "操作",key: "action",minWidth: 80,align: "center",slot:"action"}
                ],
                liangfuData:[],
                liangfuColumns:[
                    {
                        title:'客户评级',
                        key:'cusRank',
                        align: "center",
                        width:'300'
                    },
                    {
                        title:'审批结果',
                        key:'decision',
                        align: "center",
                        width:'300'
                    },
                    {
                        title:'策略编号',
                        key:'applyNo',
                        align: "center",
                        width:'150'
                    },
                    {
                        title:'详细描述',
                        key:'categories',
                        align: "center",
                    }
                ],
                fraudDetailDataList:[],
                initialized: false,
                isShowText:false,
                recordTextInfo:"",
                caseCreditOptionList:[],
                flowDict:{
                    submitReason:[],//通过原因
                    refuseReason:[],//拒绝原因
                    csbackReason:[],//初审退回原因
                    scspbackReason:[],//审查审批退回原因
                    pendingReason:[],//挂起原因
                    faceReviewReason:[],//转视频面审原因
                    backPoint:[],//退回节点
                    refuseReasonId:[],
                    refuseReasonStr:'',
                    submitReasonId:[],
                    submitReasonStr:'',
                    csbackReasonId:[],
                    csbackReasonStr:[],
                    scspbackReasonId:[],
                    scspbackReasonStr:[],
                    pendingReasonId:[],
                    pendingReasonStr:'',
                    faceReviewReasonId:[],
                    faceReviewReasonStr:[],
                    backPointId:'',
                },
                refuseModalVisiable:false,
                backModalVisiable:false,
                submitModalVisiable:false,
                pendingModalVisiable:false,
                faceReviewVisiable: false,
                remarks:null,
                // isLock:null,
                suspendForm:{
                    processRemark: ''
                },
                setSuspendLoading:false,
                operationLoading: false,
                loading: true,
                operationList:[],
                backUserDefineIndex:"",
                lastComments: ''
            }
        },
        methods: {
            otherClick(){
                this.contentvalues = ['1'];
            },
            getOrderByNavId(id) {
              let sty;
              const item = this.list.find(e => e.asset === id);
              if (item) {
                sty = { order: item.order || 0 };
              }
              return sty;
            },
            initqueryCaseLiangfuReport(){
                let params = {
                    applyNo: this.applyNumber,
                    stageId: this.stageNumber,
                    useScene: 'approve'
                }
                queryCaseLiangfuReport(params).then((res)=>{
                    console.log("ceshi----",res);
                    if(res.data&&res.data){
                        this.liangfuData = res.data;
                    }
                });
            },
            setRemindRemark() {
                this.$emit('on-set-approve', this.suggestData);
            },
            showRecord() {
                this.loadRecordList();
                this.recording = true;
            },
            getDic(v) {
                let dic = []
                switch (v) {
                    case "msgType":
                        dic = this.dataDic.msgType;
                        break;
                    case "custRelation":
                        dic = this.dataDic.custRelation;
                        break;
                    case "sex":
                        dic = this.dataDic.sex;
                        break;
                    case "maritalStatus":
                        dic = this.dataDic.maritalStatus;
                        break;
                    default:
                }
                return dic;
            },
            setDictTitleByType(v1, v2) {
                let dic = {}
                if (!this.getDic(v1)) return '';
                this.getDic(v1).forEach(column => {
                    if (column.value === v2) {
                        dic = column;
                    } else {
                        if(v1=='custRelation'){
                            if (!v2) {
                                dic = {
                                    value: '02',
                                    title: '承租人(本人)'
                                }
                            }
                        }
                    }
                });
                return dic.title;
            },
            initDataDic() {
                let self = this;
                getByTypes(this.dicKeys).then(res => {
                    if (res.code === "0000" && res.data) {
                        self.dataDic = res.data;
                    }
                });
            },
            // 原因 节点 字典
            queryDictData(){
                let params = {
                    packageId: this.packageId,
                    templateId: this.templateId,
                    userDefinedIndex: this.userDefinedIndex,
                }
                getReasonDict(params).then(res => {
                    if (res.code === "0000" && res.data) {
                        res.data.REFUSE_REASON && res.data.REFUSE_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                                j.checked=false;
                            }):''
                        })
                        res.data.CSBACK_REASON && res.data.CSBACK_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                                j.checked=false;
                            }):''
                        })
                        res.data.SCSPBACK_REASON && res.data.SCSPBACK_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                                j.checked=false;
                            }):''
                        })
                        res.data.SUBMIT_REASON && res.data.SUBMIT_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                                j.checked=false;
                            }):''
                        })
                        res.data.PENDING_REASON && res.data.PENDING_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                              j.checked=false;
                            }):''
                        })
                        res.data.FACE_REVIEW_REASON && res.data.FACE_REVIEW_REASON.map(item=>{
                            item.checked=false;
                            item.indeterminate=false;
                            item.checkList=[];
                            item.childList?item.childList.map(j=>{
                                j.checked=false;
                            }):''
                        })
                        this.flowDict.refuseReason=res.data.REFUSE_REASON;
                        this.flowDict.csbackReason=res.data.CSBACK_REASON;
                        this.flowDict.scspbackReason=res.data.SCSPBACK_REASON;
                        this.flowDict.submitReason=res.data.SUBMIT_REASON;
                        this.flowDict.pendingReason=res.data.PENDING_REASON;
                        this.flowDict.faceReviewReason=res.data.FACE_REVIEW_REASON

                        //获取信鸽通知类型
                        if(this.$store.getters.permissions['homing-pigeon-report'] && (this.userDefinedIndex == "SECOND_NODE" || this.userDefinedIndex == "END_NODE")) {
                            getPigeonType().then(res => {
                                if (res.code === "0000" && res.data) {
                                    this.pigeonListType = res.data || {};
                                    let arr = [];
                                    Object.keys(res.data).forEach(item => {
                                        let firstArr = [];
                                        if(res.data[item] && res.data[item].length>0) {
                                            res.data[item].forEach(firstItem => {
                                                firstArr.push({
                                                    paramDesc: firstItem.description,
                                                    checked: false,
                                                    paramCode: firstItem.site
                                                })
                                            })
                                        }
                                        arr.push({
                                            paramDesc: item,
                                            checked: false,
                                            indeterminate: false,
                                            checkList: [],
                                            childList: firstArr
                                        })
                                    })
                                    this.flowDict.scspbackReason=[...this.flowDict.scspbackReason, ...arr]
                                }
                            })
                        }

                    }
                });
                getFlowPonit({taskId:this.taskId}).then(res => {
                    if (res.code === "0000" && res.data) {
                        let nodeId='';
                        res.data.map(item=>{
                            if(item.nodeId.indexOf('EndNode')!==-1){
                                nodeId=item.nodeId;
                            }
                        })
                        this.flowDict.nodeId=nodeId;
                        this.flowDict.backPoint=res.data.filter(item=>item.operationType=='BACK').filter(item=>item.nodeType=='TASK');
                        this.operationList = res.data;
                    }
                });
            },
            // 格式化
            format (labels, selectedData) {
                const index = labels.length - 1;
                const data = selectedData[index] || false;
                if (data && data.code) {
                    return  data.label;
                }
                return labels[index];
            },
            onModalFocus(str){
                if(str=='refuse'){
                    this.refuseModalVisiable=true;
                }
                if(str=='back'){
                    this.backModalVisiable=true;
                }
                if(str=='submit'){
                    this.submitModalVisiable=true;
                }
                if(str=='pending'){
                  this.pendingModalVisiable=true;
                }
                if(str=='faceReview'){
                    this.faceReviewVisiable=true;
                }
            },
            onModalRefuseSubmit(data){
                console.log(data)
                this.refuseModalVisiable=false;
                this.flowDict.refuseReasonId=data.codeArr;
                this.flowDict.refuseReasonStr=data.nameArr;
                this.suggestData.approveRemark=data.nameArr.join(',');
            },
            onModalPendingSubmit(data){
              console.log("-----------------"+data)
              this.pendingModalVisiable=false;
              this.flowDict.pendingReasonId=data.codeArr;
              this.flowDict.pendingReasonStr=data.nameArr;
              this.suggestData.approveRemark=data.nameArr.join(',');
            },
            onModalSubmit(data){
                console.log('debugger---',data);
                this.submitModalVisiable=false;
                this.flowDict.submitReasonId=data.codeArr;
                this.flowDict.submitReasonStr=data.nameArr;
                this.suggestData.approveRemark=data.nameArr.join(',');
            },
            onModalCsBackSubmit(data){
                console.log(data)
                this.backModalVisiable=false;
                this.flowDict.csbackReasonId=data.codeArr;
                this.flowDict.csbackReasonStr=data.nameArr;
                this.suggestData.approveRemark=data.nameArr.join(',');
            },
            onModalScspBackSubmit(data){
                console.log(data)
                this.backModalVisiable=false;
                this.flowDict.scspbackReasonId=data.codeArr;
                this.flowDict.scspbackReasonStr=data.nameArr;
                this.suggestData.approveRemark=data.nameArr.join(',');
            },
            onModalFaceReviewSubmit(data){
                console.log(data)
                this.faceReviewVisiable=false;
                this.flowDict.faceReviewReasonId=data.codeArr;
                this.flowDict.faceReviewReasonStr=data.nameArr;
                this.suggestData.approveRemark=data.nameArr.join(',');
            },
            changeAddition(val) {
                this.change = true;
                this.checkCondition();
            },
            queryRemind() {
                let params = {
                    applyNo: this.applyNumber,
                    stageId: this.stageNumber,
                    useScene: 'approve'
                }

                queryRemindListByApplyNo(params).then(res => {
                    let  remindBo=false;
                    let  remindGu=false;
                    if (res.code === "0000") {
                        res.data.forEach(function (re) {
                            if(re.remindType==="reasonAdd"){
                               if(re.remindReason=="0"){
                                   remindBo=true;
                               }
                               if(re.remindReason=="1"){
                                   remindGu=true;
                               }
                            }
                        })
                        this.remindList = res.data;
                    }
                    this.remindShowBo=remindBo;
                    this.remindShowGu=remindGu;
                });
            },
            initInfo() {
                this.queryRemind();
                this.flushAddAmt();
            },
            flushAddAmt(){
                let params = {
                    applyNo: this.applyNumber
                }
                queryCustSummaryInfoApplyNo(params).then(res => {
                    if (res.code === "0000") {
                        this.custInfo = res.data;
                    }
                });
            },
            init() {
                this.initDataDic();
                this.initSuggestConditionFType();
                this.initSuggestRejectFType();
                this.initInfo();
                this.getSuggestDic();
                this.queryCreditTemplate();
                this.initCreditOption();
/*                this.queryVisitLevel();*/
                this.queryVisitStatus();
                this.initFraudPushData();
                this.querySomeApprove();
                this.queryApprove();
                //自动拒绝标识暂时去掉
                // this.initQueryCode();
                this.checkVisitAndStatus();
                // 查询原因 及节点字典
                // this.queryDictData();
                // 容错处理
                if(this.taskId){
                    this.queryDictData();
                    this.isCheck=true;
                }else{
                    this.isCheck=false;
                }
                this.getRemark();
                this.initqueryCaseLiangfuReport();
                this.getRole();
                this.getElectronuclearArtificial();
                //判断是否为拒绝(需复核)
                this.isRefuseCheck();
            },
            loadRecordList() {
                let params = {
                    applyNo: this.applyNumber
                }
                console.log("当前用户角色"+this.$store.state.user.roles.toString())
                if(this.isRecord){
                    if (this.$store.state.user.roles.includes("ROLE_APPROVER")) {
                        queryRecordListByApplyNo(params).then(res => {
                            if (res.code === "0000") {
                                this.recordingList = res.data;
                            }
                        });
                    }else{
                        queryRecordListAll(params).then(res => {
                            if (res.code === "0000") {
                                this.recordingList = res.data;
                            }
                        });
                    }

                }else{
                    queryRecordListAll(params).then(res => {
                        if (res.code === "0000") {
                            this.recordingList = res.data;
                        }
                    });
                }
            },
            getRole(){
                if (this.isChannel === true && this.$store.state.user.roles.toString().indexOf('CREDIT_AUDIT_SPECIALIST') != -1){
                    this.isApproveOn = true;
                }
            },
            isRefuseCheck(){
                if(this.userDefinedIndex==='END_NODE'){
                    getRefuseCheck(this.applyNo).then(res => {
                        if (res.code === '0000'){
                            this.isrefusecheck = res.data;
                        }
                    })
                }
            },
            saveElectronuclearArtificial(name){
                this.$refs[name].validate((valid =>{
                        if (valid){
                            this.formValidate.applyNo = this.applyNo;
                            let formValidate = JSON.parse(JSON.stringify(this.formValidate));
                            saveInfo(formValidate).then(res=>{
                                if (res.code == '0000'){
                                    this.$Message.success("保存成功");
                                    this.getElectronuclearArtificial();
                                }else {
                                    this.$Message.error(res.msg);
                                }
                            })
                        }
                    })
                )
            },
            getElectronuclearArtificial(){
                getArtificial(this.applyNo).then(res=>{
                    if (res.code === '0000'){
                        this.formValidate = res.data;
                        this.rent = res.data.rent;
                    }
                })
            },
            checkVisitAndStatus() {
                this.businessStateInList.forEach(val => {
                    if (val === this.businessStateIn) {
                        this.isTable = true;
                    }
                })
            },
            cancelRemind(v) {
                this.$Modal.confirm({
                    title: "撤销留言",
                    content: "确认撤销当前留言?",
                    onOk: () => {
                        let params = {
                            remindId: v.id
                        }
                        cancelRemindtById(params).then(res => {
                            if (res.code === "0000") {
                                if (res.msg === "success") {
                                    params = {
                                        applyNo: this.applyNumber,
                                        useScene: 'approve'
                                    }
                                    queryRemindListByApplyNo(params).then(result => {
                                        if (result.code === "0000") {
                                            this.remindList = result.data;
                                        }
                                    });
                                    this.$Message['success']({
                                        background: true,
                                        content: '留言撤销成功'
                                    });
                                } else {
                                    this.$Message['error']({
                                        background: true,
                                        content: '留言撤销失败'
                                    });
                                }
                            }
                        });
                    }
                })
            },
            getDicData(dicKey) {
                if (this.creditDataDicKeys.indexOf(dicKey) == -1) {
                    getDictDataByType(dicKey).then(res => {
                        this.creditData.push({
                            key: dicKey,
                            dataList: res.data
                        });
                    })
                    this.creditDataDicKeys += "[" + dicKey + "]";
                }

                let retData = {};
                this.creditData.forEach(data => {
                    if (dicKey == data.key) {
                        retData = data.dataList;
                    }
                })
                return retData;
            },
            afterRadioChange(val) {
            },
            clearSelectTemplate() {
                this.suggestData.approveRemark = '';
                this.setRemindRemark();
            },
            templateChange(val) {
                this.suggestData.approveRemark = val;
                this.setRemindRemark();
            },
            getSuggestDic() {
                let allCmdDic = [];
                getDictDataByType(this.approveSuggestDicKey).then(res => {
                    allCmdDic = res.data;
                }).then(() => {
                    if(this.stageId && this.isCheck) {
                        getFlowCmd({stageId: this.stageId}).then(res => {
                            this.approveSuggestDic = [];
                            let flowCmdSet = res.data;
                            flowCmdSet.forEach(flowCmd => {
                                allCmdDic.forEach(dic => {
                                    if (dic.value == flowCmd) {
                                        this.approveSuggestDic.push(dic);
                                    }
                                });
                            });
                        });
                    }
                })
            },
            getDicCmd(){
                // 暂时废弃，可操作命令通过流程引擎获取，再匹配系统字典
                getDictDataByType(this.approveSuggestDicKey).then(res => {
                    if (res.code == '0000') {
                        // 默认复审
                        let suggestKey = [
                            this.suggestConst.check,
                            this.suggestConst.condition,
                            this.suggestConst.reject,
                            this.suggestConst.cancel
                        ];
                        // 只有一个审核节点，即是初审，也是终审
                        if (this.routerParams.firstTrail && this.routerParams.lastTrail) {
                            suggestKey = [
                                this.suggestConst.checkFinal,
                                this.suggestConst.conditionFinal,
                                this.suggestConst.rejectFinal,
                                this.suggestConst.visit,
                                this.suggestConst.cancel
                            ]
                            // 终审
                        } else if (this.routerParams.lastTrail) {
                            suggestKey = [
                                this.suggestConst.checkFinal,
                                this.suggestConst.conditionFinal,
                                this.suggestConst.rejectFinal,
                                this.suggestConst.cancel
                            ]
                            // 初审
                        } else if (this.routerParams.firstTrail) {
                            suggestKey = [
                                this.suggestConst.check,
                                this.suggestConst.condition,
                                this.suggestConst.reject,
                                this.suggestConst.visit,
                                this.suggestConst.cancel
                            ]
                        }
                        this.approveSuggestDic = [];
                        suggestKey.forEach(suggestConst => {
                            res.data.forEach(suggest => {
                                if (suggestConst.val == suggest.value) {
                                    this.approveSuggestDic.push(suggest);
                                }
                            })
                        })
                    }
                })
            },
            suggestSelected(val) {
                this.suggestData.approveSuggestName = val.label;
            },
            getSuggestReasonDic(clear) {
                if (this.suggestData && this.suggestData.approveSuggest) {
                    getDictDataByType(this.suggestData.approveSuggest).then(res => {
                        if (res.code == "0000") {
                            this.currentReasonDic =[];
                            if(this.suggestData.approveSuggest == "suggestRejectF"){
                                res.data.forEach(dic=>{
                                    if(dic.value != '21'){ // 排除 系统自动处理的拒绝原因
                                        this.currentReasonDic.push(dic);
                                    }
                                })
                            }else{
                                this.currentReasonDic = res.data || [];
                            }
                        }
                    });
                    if (clear) {
                        if (this.suggestData) {
                            this.suggestData.approveReason = "";
                            this.suggestData.approveMessage = "";
                            this.$refs.reasonSelect.reset();
                        }
                    }
                    // 审批原因
                    if (this.suggestData.approveSuggest == this.suggestConst.check.val
                        || this.suggestData.approveSuggest == this.suggestConst.checkFinal.val
                        || this.suggestData.approveSuggest == this.suggestConst.cancel.val
                        || this.suggestData.approveSuggest == this.suggestConst.reject.val
                        || this.suggestData.approveSuggest == this.suggestConst.sendBackToDealer.val) {
                        this.reasonVisible = false;
                    } else {
                        this.reasonVisible = true;
                    }
                    // 审批留言
                    if (this.suggestData.approveSuggest == this.suggestConst.condition.val
                        || this.suggestData.approveSuggest == this.suggestConst.conditionFinal.val) {
                        this.suggestData.approveReason = "";
                        if (this.visitData.status === "done") {
                            this.classStyle = "conditionTextarea";
                        } else {
                            this.classStyle = "msgTextarea";
                        }
                        this.remarkVisible = true;
                        this.formRemarkTitle = '留言';
                        this.isNumber = 20;
                        this.reasonSelectMultiple = false;
                    } else if (this.suggestData.approveSuggest == this.suggestConst.visit.val) {
                        this.suggestData.approveReason = "";
                        if (this.visitData.status === "done") {
                            this.classStyle = "visitTextarea";
                        }else {
                            this.classStyle = "msgTextarea";
                        }
                        this.remarkVisible = true;
                        this.formRemarkTitle = '家访需求';
                        this.isNumber = 1000;
                        this.reasonSelectMultiple = true;

                    } else {
                        this.remarkVisible = false;
                        this.reasonSelectMultiple = true;
                    }
                    if (this.suggestData.approveSuggest == this.suggestConst.rejectFinal.val) {
                        this.reasonSelectMultiple = false;
                    }
                }
            },
            showVisitLevel() {
                this.visitLevelModalVisible = true;
            },
            queryCreditTemplate() {
                listTemplateByCurrentUser({}).then(res => {
                    if (res.code == '0000') {
                        this.suggestTemplateData = res.data;
                    }
                })
            },
            // add by  gjq 播放录音
            playInfo(row){
                window.open(row.recordUrl);
            },
            showText(row){
                this.recordTextInfo=row.recordText;
                this.isShowText=true;
            },
            backGo(){
                this.isShowText=false;
            },
            backFinishGo(){
                this.isShowText = false;
                this.recording = false;
            },
            initRecordHistory(hasRemainData) {
                let params = {
                    stageId: this.stageId,
                    applyNo: this.applyNo,
                    check: this.isCheck
                };
                listHistoryContent(params).then(res => {
                    console.log(res)
                    console.log('res----------------------')
                    if (res.code == '0000') {
                        this.recordHisData = res.data.taskHistories || [];
                        this.approveEnd = res.data.approveEnd;
                        let lastNormalRecord = res.data.beforeReformistContent || {}; // 上一岗非改派审批操作
                        let lastOprIsReformist = res.data.lastOprIsReformist;
                        this.recordHisData.forEach(record => {
                                let color;
                                if (record.approveSuggest == this.suggestConst.back.val || record.approveSuggest == this.suggestConst.back2dealer.val) {
                                    color = "#FAEBD7";
                                /*} else if (record.approveSuggest == this.suggestConst.refuse.val) {
                                    color = "#D2691E";
                                } else if (record.approveSuggest == this.suggestConst.submit.val || record.approveSuggest == this.suggestConst.antiFraud.val) {
                                    color = "#86f3af";*/
                                } else {
                                    color = "#F0FFFF";
                                }
                            record.style = {
                                    backgroundColor: color
                                };
                                // 处理default
                                if(record.approveRemark && record.approveRemark.indexOf('[default-default]')>-1){
                                    let arr =record.approveRemark.split('[default-default]');
                                    record.approveRemark=arr[0]+''+arr[1];
                                }

                            // if(record.approveSuggest === this.suggestConst.refuse.val) {
                            //     this.suggestRejectFTypeList.forEach(column => {
                            //         if(column.value === record.approveReason){
                            //             this.rejectReason = column.title;
                            //         }
                            //     });
                            // }
                        });
                        // 上一岗位为附条件核准时，带入审批原因,审批建议默认置为附条件核准
                        if (this.isCheck && !hasRemainData) {
                            if (lastOprIsReformist) {
                                this.suggestData.approveMessage = lastNormalRecord.approveMessage;
                                this.suggestData.approveSuggest = lastNormalRecord.approveSuggest;
                                this.suggestData.approveReason = lastNormalRecord.approveReason ? lastNormalRecord.approveReason.split(",") : "";
                                this.suggestData.approveRemark = lastNormalRecord.approveRemark;
                                this.getSuggestReasonDic(false);
                            } else if (this.recordHisData && this.recordHisData.length > 0
                                && this.recordHisData[0].approveSuggest == this.suggestConst.condition.val) {
                                if (this.routerParams.lastTrail) {
                                    this.suggestData.approveSuggest = this.suggestConst.conditionFinal.val;
                                } else {
                                    this.suggestData.approveSuggest = this.suggestConst.condition.val;
                                }
                                this.suggestData.approveReason = this.recordHisData[0].approveReason.split(",");
                                this.suggestData.approveMessage = this.recordHisData[0].approveMessage;
                                //this.suggestData.approveRemark = this.recordHisData[0].content;
                                this.getSuggestReasonDic(false);

                            }
                        }
                    }
                })
            },
            initSuggestConditionFType(){
                let params = {
                    type: "suggestConditionF"
                }
                getDictDataByType(params.type).then(res =>{
                    if (res.code === "0000"){
                        this.suggestConditionFTypeList = res.data;
                    }
                });
            },
            initSuggestRejectFType(){
                let params = {
                    type: "suggestRejectF"
                }
                getDictDataByType(params.type).then(res =>{
                    if (res.code === "0000"){
                        this.suggestRejectFTypeList = res.data;
                    }
                });
            },
            initCreditOption() {
                listByApplyNo({applyNo: this.applyNo}).then(res=>{
                    console.log(res.data,this.applyNo);
                    console.log('res.data==============================================================');
                    this.caseCreditOptionList = res.data;
                })
            },
            saveVisitLevel() {
                this.submitLoading = true;
                saveVisitLevel(this.visitData).then(res => {
                    if (res.code == "0000") {
                      /*  this.queryVisitLevel();*/
                    }
                    this.$Message.info("保存成功");
                    this.visitLevelModalVisible = false;
                }).finally(() => {
                    this.submitLoading = false;
                })
            },
            saveApprove(silently) {
                this.suggestData.approveReason = this.suggestData.approveReason
                    ? this.suggestData.approveReason.toString()
                    : "";
                this.suggestData.applyNo = this.applyNo;
                this.suggestData.stageId = this.stageId;
                let params = {
                    approveRecord: this.suggestData,
                    creditOptionList: this.caseCreditOptionList
                };
                this.submitApproveLoading = !silently;
                saveApprove(params).then(res => {
                    if (res.code == "0000") {
                        if(!silently) {
                            this.$Message.info("保存成功!")
                        }
                        this.creditOptions = res.data.creditOptionInfos;
                        this.suggestData = res.data.approveRecord;
                        if (this.suggestData.approveReason) {
                            this.suggestData.approveReason = this.suggestData.approveReason.split(',');
                        }
                    }
                }).finally(() => {
                    this.submitApproveLoading = false;
                })
            },
            // findCascaderStr(arr,target){
            //     let str='';
            //     arr.map(item=>{
            //         if(target[0]===item.value){
            //             item.children.map(j=>{
            //                 if(j.value===target[1]){
            //                     console.log('arr',target)
            //                     str=j.paramDesc;
            //                 }
            //             })
            //         }
            //     })
            //     return str;
            // },
            cancelSubmit() {
                this.flowDict.faceReviewReasonId = [];
                this.flowDict.refuseReasonId = [];
                this.flowDict.csbackReasonId = [];
                this.flowDict.scspbackReasonId = [];
                this.flowDict.backPointId = '';
                this.commentsModalVisible = false;

            },
            cancelSuspend(){
                this.suspendViewVisible = false;
                this.suspendForm.processRemark = "";
            },
            //挂起备注提交
            submitSuspend(){
                this.suggestData.approveRemark = this.suggestData.approveRemark ? this.suggestData.approveRemark : "";
                this.suggestData.approveReason = this.suggestData.approveReason ? this.suggestData.approveReason.toString() : "";
                let data = [];
                let obj={
                    taskId:this.taskId,
                    applyNo:this.applyNo
                }
                data.push(obj);
                let params ={
                    dataList:data,
                    approveRecord:this.suggestData,
                    status:'yes',
                    approveSuggestList:this.flowDict.pendingReasonId,
                    approveSuggestNameList:this.flowDict.pendingReasonStr,
                };
                modifyCaseSuspend(params).then(res => {
                    if (res.code == "0000") {
                      this.$Message.success("操作成功");
                      this.$emit("whenClose");
                      this.$emit('closeWindow')
                      window.close();
                    }
                });
            },

            submitApprove() {
                this.suggestData.applyNo = this.applyNo;
                this.suggestData.stageId = this.stageId;
                this.suggestData.taskId = this.taskId;
                // if (!(this.suggestData.approveSuggest == this.suggestConst.cancel.val)) {
                // }
                // 通过
                if (this.isComments=='submit') {
                    if (!this.flowDict.submitReasonId.length || !this.suggestData.approveRemark.length) {
                        this.$Message.warning("请选择具体通过原因");
                        return;
                    }
                    // this.suggestData.approveSuggest=this.flowDict.refuseReasonId[1];
                    // this.suggestData.approveSuggestName=this.findCascaderStr(this.flowDict.refuseReason,this.flowDict.refuseReasonId);
                    this.suggestData.flowNode=this.flowDict.nodeId;
                }
                // 拒绝
                if (this.isComments=='refuse' || this.isComments=='refusecheck') {
                    if (!this.flowDict.refuseReasonId.length) {
                        this.$Message.warning("请选择拒绝原因");
                        return;
                    }
                    // this.suggestData.approveSuggest=this.flowDict.refuseReasonId[1];
                    // this.suggestData.approveSuggestName=this.findCascaderStr(this.flowDict.refuseReason,this.flowDict.refuseReasonId);
                    this.suggestData.flowNode=this.flowDict.nodeId;
                }
                //挂起
                if(this.isComments=='pending'){
                    if(!this.flowDict.pendingReasonId.length){
                        this.$Message.warning("请选择挂起原因");
                        return;
                    }
                }
                // 退回
                if (this.isComments=='back') {
                    if (!this.flowDict.backPoint.length) {
                        this.$Message.warning("该节点不支持退回");
                        return;
                    }
                    if (!this.flowDict.backPointId) {
                        this.$Message.warning("请选择退回节点");
                        return;
                    }
                    if (!this.flowDict.csbackReasonId.length && !this.flowDict.scspbackReasonId.length) {
                        this.$Message.warning("请选择退回原因");
                        return;
                    }
                    // this.suggestData.approveSuggest=this.flowDict.backReasonId[1];
                    this.suggestData.flowNode=this.flowDict.backPointId;
                    this.operationList.map(item=>{
                        if(item.nodeId === this.suggestData.flowNode){
                            this.backUserDefineIndex = item.userDefinedIndex;
                        }
                    })
                    console.log("this.backUserDefineIndex"+this.backUserDefineIndex);
                    // this.suggestData.approveSuggestName=this.findCascaderStr(this.flowDict.backReason,this.flowDict.backReasonId);
                }
                // 退回 经销商
                if (this.isComments=='back2dealer') {
                    // if (!this.flowDict.backPointId) {
                    //     this.$Message.warning("请选择退回节点");
                    //     return;
                    // }
                    // if (!this.flowDict.backPoint.length) {
                    //     this.$Message.warning("该节点不支持退回");
                    //     return;
                    // }
                    if (!this.flowDict.csbackReasonId.length && !this.flowDict.scspbackReasonId.length) {
                        this.$Message.warning("请选择退回原因");
                        return;
                    }
                    // this.suggestData.approveSuggest=this.flowDict.backReasonId[1];
                    this.suggestData.flowNode=this.flowDict.backPointId?this.flowDict.backPointId:null;
                    // this.suggestData.approveSuggestName=this.findCascaderStr(this.flowDict.backReason,this.flowDict.backReasonId);
                }
                // if (this.suggestData.approveSuggest === this.suggestConst.rejectFinal.val) {
                //     if (!this.suggestData.approveReason) {
                //         this.$Message.warning("请选择原因");
                //         return;
                //     }
                // }
                // if (this.routerParams.lastTrail && this.routerParams.firstTrail) {
                //     if (this.suggestData.approveSuggest === this.suggestConst.conditionFinal.val) {
                //         if (!this.suggestData.approveReason) {
                //             this.$Message.warning("请选择原因");
                //             return;
                //         }
                //     }
                // }
                if (this.routerParams.firstTrail && this.routerParams.lastTrail) {
                    //自动拒绝的单子标志为yes，信贷报告为非必填，为no的则必填
                    if (this.uncertainCode === "no") {
                        if (!this.suggestData.approveRemark) {
                            this.$Message.warning("请填写信贷分析报告");
                            return;
                        }
                    }
                }
                // if (this.routerParams.firstTrail && !this.routerParams.lastTrail) {
                //     if (this.suggestData.approveSuggest === this.suggestConst.condition.val) {
                //         if (!this.suggestData.approveReason) {
                //             this.$Message.warning("请选择原因");
                //             return;
                //         }
                //     }
                // }
                if (!this.routerParams.lastTrail) {
                    if (this.uncertainCode === "no") {
                        if (!this.suggestData.approveRemark) {
                            this.$Message.warning("请填写信贷分析报告");
                            return;
                        }
                    }
                }
             /*   if (!this.suggestData.approveSuggest) {
                    this.$Message.warning("请选择审批建议");
                    return;
                }*/
                if(this.caseCreditOptionList&&this.routerParams.firstTrail){
                    for (let i = 0; i < this.caseCreditOptionList.length ; i++) {
                        let option = this.caseCreditOptionList[i];
                        //初审增加校验
                        if(option.optionType === "normal" &&option.value===""){
                            this.$Message.warning("信贷选项[ <span style='color: red'>" + option.optionName + "</span> ]不能为空!")
                            return false;
                        }
                    }
                }
                if (this.isComments=='faceReview') {
                    if (!this.flowDict.faceReviewReasonId.length && !this.flowDict.faceReviewReasonStr.length) {
                        this.$Message.warning("请选择转视频面审原因");
                        return;
                    }
                }
                // if (this.suggestData.approveSuggest === this.suggestConst.visit.val) {
                //     if (!this.suggestData.approveReason
                //         ||this.suggestData.approveReason.length<1) {
                //         this.$Message.warning("请选择原因");
                //         return;
                //     }
                //     if (!this.suggestData.approveMessage) {
                //         this.$Message.warning("请填写家访需求");
                //         return;
                //     }
                //     if (this.visitData.status === 'done' || this.visitData.status === 'cancelVisit' || this.visitData.status === 'rejectVisit') {
                //         this.$Message.warning("已经进行过家访，不可再次家访");
                //         return;
                //     }
                // }
                // if (this.visitData.status === 'done') {
                //     if (this.visitData.visitSuggest === undefined) {
                //         this.$Message.warning("请先填写质量评分表!");
                //         return;
                //     }
                // }
                // if (this.suggestData.approveSuggest == this.suggestConst.cancel.val
                //     && (!this.suggestData.approveRemark || !this.suggestData.approveRemark.trim())) {
                //     this.$Message.warning("撤销备注不可为空");
                //     return false;
                // }
                // if (this.suggestData.approveSuggest != this.suggestConst.cancel.val && !this.routerParams.firstTrail) {
                //     if (this.recordHisData.length > 0 && this.recordHisData[0].approveSuggest != this.suggestConst.sendBack.val
                //         && this.recordHisData[0].approveSuggest != this.suggestConst.sendBackToDealer.val) {
                //         let lastApproveSuggest = this.recordHisData[0].approveSuggest;
                //         let currentApproveSuggest = this.suggestData.approveSuggest;
                //         if (this.routerParams.lastTrail) {
                //             if ((lastApproveSuggest == this.suggestConst.condition.val
                //                 && currentApproveSuggest != this.suggestConst.conditionFinal.val)
                //                 || (lastApproveSuggest == this.suggestConst.check.val
                //                     && currentApproveSuggest != this.suggestConst.checkFinal.val)
                //                 || (lastApproveSuggest == this.suggestConst.reject.val
                //                     && currentApproveSuggest != this.suggestConst.rejectFinal.val)) {
                //                 this.$Message.warning("审批建议必须与上一岗位审核意见保持一致.");
                //                 return false;
                //             }
                //         } else if (lastApproveSuggest != currentApproveSuggest) {
                //             this.$Message.warning("审批建议必须与上一岗位审核意见保持一致.");
                //             return false;
                //         }
                //     }
                // }
                this.isSubmit = true;
            },
            ok () {
                    this.suggestData.approveRemark = this.suggestData.approveRemark ? this.suggestData.approveRemark : "";
                    this.suggestData.approveReason = this.suggestData.approveReason ? this.suggestData.approveReason.toString() : "";
                    let params = {
                        creditOptionList: this.caseCreditOptionList,
                        approveRecord: this.suggestData,
                        operationType: this.isComments,
                        approveSuggestList:this.isComments==='faceReview'?this.flowDict.faceReviewReasonId :this.isComments==='refuse' || this.isComments=='refusecheck'?this.flowDict.refuseReasonId:this.userDefinedIndex==='FIRST_NODE'?this.flowDict.csbackReasonId:this.flowDict.scspbackReasonId || [],
                        approveSuggestNameList:this.isComments==='faceReview'?this.flowDict.faceReviewReasonStr :this.isComments==='refuse'|| this.isComments=='refusecheck'?this.flowDict.refuseReasonStr:this.userDefinedIndex==='FIRST_NODE'?this.flowDict.csbackReasonStr:this.flowDict.scspbackReasonStr || [],
                        finallyAgree: this.isFinallyAgree,
                        backOption: this.backOption,
                        userDefineIndex: this.userDefinedIndex,
                        backUserDefineIndex: this.backUserDefineIndex
                    };
                   console.log("params"+JSON.stringify(params));
                    if (!this.submitButton) {
                        this.submitButton = true;
                        setTimeout(() => {
                            this.submitButton = false
                        }, 8000)
                        this.submitApproveLoading = true;

                        //信鸽审查主动通知用户类型
                        if(this.$store.getters.permissions['homing-pigeon-report'] && (this.userDefinedIndex == "SECOND_NODE" || this.userDefinedIndex == "END_NODE")) {
                            let pigeonTypes = [];
                            Object.keys(this.pigeonListType).forEach(firstItem => {
                                if(this.pigeonListType[firstItem] && this.pigeonListType[firstItem].length>0) {
                                    this.pigeonListType[firstItem].forEach(twoItem => {
                                        if(this.flowDict.scspbackReasonId.includes(twoItem.site)) {
                                            pigeonTypes.push(twoItem.site);
                                        }
                                    })
                                }
                            })

                            if(pigeonTypes.length > 0) { //未选中信鸽退回类型 不通知信鸽
                                let pageonParams = {
                                    appNo: this.applyNo,
                                    way: 'web',
                                    types: pigeonTypes
                                }
                                notifyUsersUsedTypes(pageonParams).then(res => {
                                    if (res.code === "0000" && res.data) {
                                        console.log("信鸽通知成功");
                                    }else {
                                        this.$Message.error("信鸽通知异常");
                                    }
                                }).catch(e => {
                                    this.$Message.error("信鸽通知异常");
                                })
                            }
                        }

                        submitApprove(params).then(res => {
                            if (res.code == "0000") {
                                //二次调用报错，未知使用。
                                // submitApprove({applyNo:this.applyNo}).then(res => {
                                //     if (res.code == "0000") {
                                //         console.log("操作留言",res)
                                //     }
                                // })
                                // if(this.isComments=='submit'){
                                //    getOverduePercentage({applyNo:this.applyNo}).then(res => {
                                //         if (res.code == "0000") {
                                //             console.log("getOverduePercentage",res)
                                //         }
                                //     })
                                // }
                                this.$emit("whenClose");
                                this.$Message.info("提交成功!");
                                this.$emit('closeWindow')
                                window.close();
                            } else if (res.code === "0001") {
                                this.$Message.warning(res.msg);
                            }
                        }).finally(() => {
                            this.submitApproveLoading = false;
                        })
                    }
                },
            cancel() {
                this.isSubmit = false;
            },
            queryApprove() {
                let params = {
                    applyNo: this.applyNo,
                    stageId: this.stageId
                };
                let queried = false;
                queryApprove(params).then(res => {
                    if (res.code == "0000") {
                        this.suggestData = res.data || {};
                        if (res.data.queried) {
                            if (this.suggestData.approveReason) {
                                this.suggestData.approveReason = this.suggestData.approveReason.split(',');
                            }
                            this.getSuggestReasonDic(false);
                        }
                        queried = res.data.queried;
                    }
                }).then(()=>{
                    this.initRecordHistory(queried);
                }).then(()=>{
                    this.checkCondition();
                }).then(()=>{
                    if(this.suggestData.approveRemark){
                        this.setRemindRemark();
                    }
                });
            },
            checkCondition(){
                if(this.change) {
                    if (this.suggestData.approveSuggest != this.suggestConst.conditionFinal.val
                        && this.suggestData.approveSuggest != this.suggestConst.condition.val) {
                        if (this.routerParams.lastTrail) {
                            this.suggestData.approveSuggest = this.suggestConst.conditionFinal.val;
                            this.suggestData.approveSuggestName = this.suggestConst.conditionFinal.title;
                        } else {
                            this.suggestData.approveSuggest = this.suggestConst.condition.val;
                            this.suggestData.approveSuggestName = this.suggestConst.condition.title;
                        }
                        this.getSuggestReasonDic(true);
                    }
                    this.saveApprove(true);
                }
            },
            initQueryCode() {
                let params = {
                    applyNo: this.applyNumber,
                    stageId: this.stageNumber
                }
                queryCode(params).then(res => {
                    if (res.code === "0000") {
                        if ( res.data.uncertainNode != undefined ) {
                            this.uncertainCode = res.data.uncertainNode;
                        }
                    }
                });
            },
            querySomeApprove() {
                if (this.isFire){
                    let params = {
                        applyNo: this.applyNo
                    };
                    querySomeApprove(params).then(res => {
                        if (res.code == "0000") {
                            this.suggestData = res.data || {};
                            if (res.data) {
                                if (this.suggestData.approveReason) {
                                    this.suggestData.approveReason = this.suggestData.approveReason.split(',');
                                }
                                if (this.suggestData.approveSuggest) {
                                    getByTypes('approveSuggest').then(res => {
                                        if (res.code == "0000") {
                                            res.data.approveSuggest.forEach(suggest => {
                                                if (suggest.value == this.suggestData.approveSuggest) {
                                                    this.suggestData.approveSuggest = suggest.title;
                                                }
                                            })
                                        }
                                    });
                                }
                            }
                            this.getSuggestReasonDic(false);
                        }
                    });
                }
            },
            queryVisitStatus() {
                getDictDataByType(this.visitStatusDicKey).then(res => {
                    if (res.code == "0000") {
                        this.visitStatusDic = res.data;
                    }
                })
            },
            discoverFraud() {
                this.fraudModalVisible = true;
            },
            cancelDiscoverFraud(doFlush) {
                this.fraudModalVisible = false;
                if(doFlush) {
                    this.initFraudPushData();
                }
            },
            emitAntiFraud() {
                this.isFraud = true;
            },
            isCancel() {
                this.isFraud = false;
            },
            isOk() {
                let params = {
                    applyNo: this.applyNo,
                    callStep: "21" // 常规审批时固定值，标识 案件初审手工核查
                };
                this.submitApproveLoading = true;
                handInvokeAntiFraud(params).then(res => {
                    if (res.code == "0000") {
                        this.$Message.info("操作成功!");
                    }
                }).finally(() => {
                    this.submitApproveLoading = false;
                });
            },
            initFraudPushData() {
                listAntiFraudPush({
                    applyNo: this.applyNumber
                }).then(res => {
                    if (res.code == "0000") {
                        this.fraudDetailDataList = res.data;
                        if(res.data.length > 0) {
                            // this.$emit('updateNavList',[{order:21,asset:'asset_210',id:'hong_c_210',title: '反欺诈反馈明细', hide: false}])
                        }
                    }
                })
            },
            removeFraudRecord(row){
                this.$Modal.confirm({
                    title: "删除",
                    content: "您确定要删除当前反欺诈信息吗?",
                    onOk: () => {
                        this.$Spin.show();
                        removeFraudRecord(row.id).then(res=>{
                            this.$Message.info("操作成功!");
                            this.initFraudPushData();
                        }).finally(() => {
                            this.$Spin.hide();
                        });
                    }
                });

            },
            //更新累加贷额
            updateAddAmt(){
                let params = {
                    applyNo: this.applyNumber
                };
                updateAddAmt(params).then(res=>{
                    if(res.data){
                        this.$Message.success("累加贷额更新成功!");
                        this.flushAddAmt();
                    }
                })
            },
            comments(val,finallyAgree,name) {
                if (!this.lastComments) {
                    this.lastComments = val
                } else {
                    if  (this.lastComments !== val) {
                        this.flowDict.submitReasonStr = '';
                        this.suggestData.approveRemark = '';
                    }
                }
                if (val === "submit" || val === "refuse" || val === "refusecheck") {
                    if (this.currentCustBasic.operateWay === '00' && !this.currentCustDetail.networkCarCertStartDate) {
                        this.$Message.error("请输入人证取得时间")
                        return
                    }
                    /*if(this.userDefinedIndex == "SECOND_NODE"){
                        let flag = await this.checkReviewInfo();
                        if (!flag){
                            this.$Message.error("此订单还有人工审核项未确认异常，请先确认!");
                            return
                        }
                    }*/
                }
                this.hasBackOption = false;
                if(val === "submit") {
                    if (this.userDefinedIndex == "FIRST_NODE") {
                        this.isFinallyAgree = false;
                    } else if (this.userDefinedIndex == "SECOND_NODE") {
                        this.isFinallyAgree = finallyAgree;
                    } else {
                        this.isFinallyAgree = true;
                    }
                }else{
                    if(val === "back2dealer" && this.userDefinedIndex != "FIRST_NODE"){
                        this.hasBackOption = true;
                    }
                }
                this.isComments = val;
                let params = {
                    applyNo: this.applyNumber
                }
                querySuspendStatus(params).then(res => {
                    if (res.data == "yes"){
                        this.$Message.error("此订单已挂起,请先人工解除挂起状态");
                        return;
                    }else{
                        /*if("submit" == val && this.isLock =="yes" ){
                            this.$Message.error("此订单已锁定,请先人工解锁");
                            return;
                        }*/
                        if (val === "submit" && (this.$store.state.user.roles.toString().indexOf('CREDIT_AUDIT_SPECIALIST') != -1)){
                            this.$refs[name].validate((valid =>{
                                if (valid){
                                    this.formValidate.applyNo = this.applyNo;
                                    let formValidate = JSON.parse(JSON.stringify(this.formValidate));
                                    saveInfo(formValidate).then(res=>{
                                        if (res.code == '0000'){
                                            this.$Message.success("保存成功");
                                            this.getElectronuclearArtificial();
                                            this.commentsModalVisible = true;
                                        }else {
                                            this.$Message.error(res.msg);
                                        }
                                    })
                                }else {
                                    this.$Modal.warning({
                                        title: "警告",
                                        content: "电核人工功能勾选项-必填项未填写"
                                    })
                                }
                            }))
                        }else {
                            this.commentsModalVisible = true;
                        }
                    }
                })
            },
            checkReviewInfo(){
                return new Promise((resolve, reject) =>{
                    let params = {
                        applyNo: this.applyNumber
                    }
                    checkReviewInformation(params).then(res => {
                        if (res.data == false){
                            resolve(false);
                        }else {
                            resolve(true);
                        }
                    }).catch(err => {
                        reject(err);
                    })
                })
            },
            getRemark() {
                getRemark({applyNo :this.applyNumber}).then(res => {
                    if (res.code === "0000" && res.data) {
                        // this.remarks = res.data.remarks;
                        this.remarks='';
                        if(res.data.oldRemark.length>0){
                            for(var i=0;i<res.data.oldRemark.length;i++){
                                this.remarks+=res.data.oldRemark[i]+"<br/>";
                            }
                        }
                        this.remarks += res.data.remarks;
                        // this.isLock = res.data.isLock;
                    }
                });
            }
        },
        mounted() {

        },
        created() {
            this.applyNumber = this.applyNo!=''?this.applyNo:this.afs.getPageParams(this).applyNo;
            this.stageNumber = this.stageId!=''?this.stageId:this.afs.getPageParams(this).stageId;
            this.suggestData.applyNo = this.applyNo;
            this.suggestData.stageId = this.stageId;
            this.routerParams = this.afs.getPageParams(this);
            this.isFire=this.routerParams.isFire;
            // this.stageId=this.routerParams.stageId;
            this.applyNo= this.routerParams.applyNo;
            this.isRecord= this.routerParams.isRecord;
            this.isChannel=this.afs.getPageParams(this).isChannel;
            this.init();

            setTimeout(()=>{//暂时这样；因为order需要时间
                if(this.fraudDetailDataList.length > 0) {
                    this.$emit('updateNavList',[{order:21,asset:'asset_210',id:'hong_c_210',title: '反欺诈反馈明细', hide: false}])
                }
            },4500)
        },
        watch:{
            orderList(val) {
                this.list = val
            },
        },
       /* watch:{
            tabName(val){
                // 仅加载一次，tab页切换点击不再加载
               if(val=='name4' && !this.initialized){
                    this.init();
                    this.initialized = true;
               }
            }
        },*/
        computed:{
            ...mapGetters({currentCustDetail: "custDetail", currentCustBasic: "custBasic"}),
            visitGradeSum : function(){
                if(this.visitData) {
                    this.visitData.visitGrade = (this.visitData.visitCarefulLevel
                        + this.visitData.visitDoneLevel
                        + this.visitData.visitReportLevel
                        + this.visitData.visitFileLevel) || 0;
                }
                return '';
            },
            rentingRatio(){
                if (this.formValidate.income && this.rent){
                    if (this.formValidate.amountLiabilities != null){
                        let a = ((parseInt(this.rent) + parseInt(this.formValidate.amountLiabilities)) * 100/parseInt(this.formValidate.income)).toFixed(2);
                        this.formValidate.rentingRatio = a;
                        return a;
                    }
                }else {
                    return ''
                }
            }
        }
    }
</script>

<style scoped>
    .ivu-select-item:hover{
        background: #79E0FF;
    }
    .creditCardBo{
        width: 100%;
    }
    .j-sb{
        display:flex;
        align-items:center;
        justify-content:space-between;
        height:25px;
        padding-right:10px;
    }
    #otherContent /deep/ .ivu-collapse-header{
        background:#ffffff;
        border:none;
        cursor: auto;
    }
</style>
