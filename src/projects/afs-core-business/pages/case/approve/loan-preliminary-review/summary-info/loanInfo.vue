<style lang="less" xmlns:margin-right="http://www.w3.org/1999/xhtml">
@import "summaryInfo.less";
.width-input {
    width: 80%;
}
.page_summaryInfo {
    padding-right: 68px;
    .ivu-select-selection,
    .ivu-input-wrapper {
        width: 250px;
    }
}
.page_summaryInfo .nav {
    width: 100px;
    position: fixed;
    top: 100px;
    right: 0;
    z-index: 99;
}
.m-left-three {
    margin-left: 3%;
}
.page_summaryInfo .nav-order {
    display: flex;
    flex-flow: column;
}
.carInvoiceInfoActiveDev{
    -webkit-text-fill-color: #FF0000;
    font-size: 20px;
    color: #FF0000;
}
.carInvoiceInfoNormal{
    font-size: 20px;
    color: #0c0c0c;
}
.ifDealerGuaranteeDevs input:disabled{
    opacity:1;
    -webkit-text-fill-color: #FF0000;
}
.ifDealerGuaranteeDev input:disabled{
    opacity:1;
    -webkit-text-fill-color: black;
}
</style>
<template>
    <div class="asset page_summaryInfo">
<!--        <rui-anchor class="nav" :list="navList" />-->
        <!--        <Softphone :isCallOut="isCallOut" :callNumber="callNumber"  />-->
        <div class="head-au">
            <!-- <div class="head-date">
                <Button type="primary" size="default" @click="initInfo">影像资料</Button>
                <Button type="primary" size="default" @click="initInfo">欺诈反馈</Button>&nbsp;
                <Button type="primary" size="default" @click="initInfo">保存</Button>
                <Button type="primary" size="default" @click="reject">提交</Button>
                <Button
                    type="primary"
                    size="default"
                    icon="md-return-left"
                    @click="closeThis"
                    >返回</Button>
            </div>-->
        </div>
        <Card class="body-t" shadow>
            <Collapse v-model="values" simple class="collapse-list nav-order">
                <Panel name="135" :style="getOrderByNavId('asset_135')">
                    <span id="asset_135"></span>
                    放款自动审核结果
                    <div slot="content">
                        <div class="j-sbt">
                            <div>
                                <Form ref="pageInfo" :label-width="160" label-position="right" inline>
                                    <Row>
                                        <Col span="24" class="iphone-invoice">
                                            <FormItem prop="title" label="放款自动审核结果">
                                                <span  style="font-weight: bold;color: red; white-space: pre-wrap">{{autoLoanApproveResult}}</span>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Panel>
                <Panel name="1" :style="getOrderByNavId('asset_70')">
                    概要信息
                    <span id="asset_70"></span>
                    <div slot="content">
                        <div> <Button
                            @click="changeRecord"
                            style="margin-top: 5px;background-color:#19be6b;border: none;"
                        >查看变更记录</Button
                        ></div>
                        <Form
                            ref="pageInfo"
                            :label-width="170"
                            :model="pageInfo"
                            label-position="right"
                            inline
                        >
                            <Row>
                                <Col span="8">
                                    <FormItem prop="applyNo" label="申请编号">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.applyNo"
                                            disabled
                                            @click="changeRecord"
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="custName" label="客户名称">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.custName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="loanAmt" label="融资额">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.loanAmt"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="qualityGrade" label="是否先放后抵">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.lendingMode"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>

                                <Col span="8">
                                    <FormItem prop="productName" label="产品方案名称">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.productName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem
                                        v-if="outlineVo.channelGrade === '01'"
                                        prop="channelFullName"
                                        label="合作商名称"
                                    >
                                        <Input
                                            type="text"
                                            v-model="outlineVo.channelFullName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                    <FormItem
                                        v-if="outlineVo.channelGrade !== '01'"
                                        prop="channelFullName"
                                        label="合作商名称"
                                    >
                                        <Input
                                            type="text"
                                            v-model="outlineVo.channelFullName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="productName" label="联合方">
                                        <Input
                                            type="text"
                                            v-model="financeItem.channelUnitName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="ifDealerGuarantee" label="是否经销商担保">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.ifDealerGuarantee"
                                            disabled
                                        >
                                        </Input>
                                        <!--<Select style="width:100px" v-model="outlineVo.ifDealerGuarantee">
                                            <Option :key="item.value" :value="item.value" v-for="item in basic">{{ item.label }}</Option>
                                        </Select>-->
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="loanAmt" label="合同号码">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.contractNo"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="title" label="授权区域">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.title"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="totalRent" label="经销商省市">
                                        <Input type="text" v-model="outlineVo.channelAddress"
                                               disabled > </Input>
                                    </FormItem>
                                </Col>

                                <!--<Col span="6">
                                    <FormItem
                                        prop="totalRent"
                                        label="是否需要连带责任保证书"
                                    >
                                        <Input
                                            type="text"
                                            v-model="outlineVo"
                                            disabled
                                            placeholder="请输入"
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>-->
                                <Col span="8">
                                    <FormItem prop="saleAdvisor" label="是否自动审批通过">
                                        <Input type="text" value="否" disabled placeholder="请输入">
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="title" label="签约方式">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.signType"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>

                                <Col span="8">
                                    <FormItem prop="qualityGrade" label="上牌地是否备案">
                                        <Input type="text" value="否" disabled placeholder=""> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="qualityGrade" label="是否需要连带责任保证书">
                                        <div :class="outlineVo.ifDealerGuarantee !='是' ? 'ifDealerGuaranteeDevs':'ifDealerGuaranteeDev'">
                                            <Input type="text" v-model="outlineVo.ifDealerGuarantee" disabled>
                                            </Input>
                                        </div>
                                    </FormItem>
                                </Col>
                                <!--<Col span="6">
                                    <FormItem
                                        prop="saleAdvisor"
                                        label="是否上传信息查询授权书"
                                    >
                                        <Input
                                            type="text"
                                            v-model="outlineVo"
                                            disabled
                                            placeholder="请输入"
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>-->
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="title" label="合同最后签约时间">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.lastGenerationDate"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="dealerName" label="车商名称">
                                        <Input
                                            type="text"
                                            v-model="pageInfo.dealerName"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8" >
                                    <FormItem label="主体名称" prop="subjectName">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.subjectName"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="抵押性质" prop="mortgageStatus">
                                        <Select v-model="outlineVo.mortgageStatus"  style="width: 200px;" disabled>
                                            <Option
                                                v-for="item in dataDic.mortgageStatus"
                                                :label="item.title"
                                                :value="item.value"
                                                :key="item.value"
                                            >{{ item.title }}</Option
                                            >
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="manageRealName" label="客户经理">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.manageRealName"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="managePhone" label="客户经理手机号">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.managePhone"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="qualityLevel"
                                              label="资质等级"
                                    >
                                        <Select
                                            v-model="outlineVo.qualityLevel"
                                            disabled
                                            placeholder=""
                                        >
                                            <Option
                                                v-for="item in dataDic.qualityGrade"
                                                :label="item.title"
                                                :value="item.value"
                                                :key="item.value"
                                            >{{ item.title }}</Option
                                            >
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="saleAdvisor" label="金融人员">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.saleAdvisor"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="saleAdvisorPhone" label="金融人员电话">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.saleAdvisorPhone"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Panel>
                <Panel name="2" :style="getOrderByNavId('asset_180')">
                    风险提示
                    <span id="asset_180"></span>
                    <div slot="content">
                        <Col style="width: 80%">
                            &nbsp;&nbsp;&nbsp;<Tag
                            v-for="(item, index) in labelList"
                            :color="item.labelColor"
                            :key="index"
                        >{{ item.labelName }}</Tag
                        >
                        </Col>
                    </div>
                </Panel>
                <remind
                    v-model="remindVisible"
                    @afterSubmit="afterRemindSubmit"
                    @beforeSubmit="beforeRemindSubmit"
                    :approve-record="remindApproveRecord"
                    :is-dealer-call-back="this.isDealerCallBack"
                    @cancel="remindCancel"
                />
<!--                <Panel name="50" :style="getOrderByNavId('asset_50')" id="hong_50">-->
<!--                    <span id="asset_50">历史匹配信息</span>-->
<!--                    <div slot="content">-->
<!--                        <div  class="j-sb">-->
<!--                            <div></div>-->
<!--                            <Row>-->
<!--                                <Button-->
<!--                                    @click="initHistoryInfo"-->
<!--                                    type="success"-->
<!--                                    style="margin-top: 5px;background-color:#19be6b;border: none;"-->
<!--                                >刷新历史信息</Button-->
<!--                                >-->
<!--                            </Row>-->
<!--                        </div>-->
<!--                        <div class="form-box">-->
<!--                            <Table-->
<!--                                border-->
<!--                                :row-class-name="rowClassName"-->
<!--                                v-model="this.historyInfoList"-->
<!--                                :columns="historyColums"-->
<!--                                :data="this.historyInfoList"-->
<!--                            >-->
<!--                                <template slot-scope="{ row, index }" slot="custRole">-->
<!--                                    &lt;!&ndash;数据字典对比常量不同, 暂做直显汉字&ndash;&gt;-->
<!--                                    <span>{{ row.custRole }}</span>-->
<!--                                </template>-->
<!--                                <template-->
<!--                                    slot-scope="{ row, index }"-->
<!--                                    slot="socUniCrtCodeNumber"-->
<!--                                >-->
<!--                  <span v-if="row.socUniCrtCodeNumber == 0">{{-->
<!--                          row.socUniCrtCodeNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.socUniCrtCodeNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.socUniCrtCodeList)"-->
<!--                                    >{{ row.socUniCrtCodeNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="idCardNoNumber">-->
<!--                  <span v-if="row.idCardNoNumber == 0">{{-->
<!--                          row.idCardNoNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.idCardNoNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.idCardNoList)"-->
<!--                                    >{{ row.idCardNoNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="unitNameNumber">-->
<!--                  <span v-if="row.unitNameNumber == 0">{{-->
<!--                          row.unitNameNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.unitNameNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.unitNameList )"-->
<!--                                    >{{ row.unitNameNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="phoneNoNumber">-->
<!--                  <span v-if="row.phoneNoNumber == 0">{{-->
<!--                          row.phoneNoNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.phoneNoNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.phoneNoList )"-->
<!--                                    >{{ row.phoneNoNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="addressNumber">-->
<!--                  <span v-if="row.addressNumber == 0">{{-->
<!--                          row.addressNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.addressNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.addressList)"-->
<!--                                    >{{ row.addressNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="carVinNumber">-->
<!--                  <span v-if="row.carVinNumber == 0">{{-->
<!--                          row.carVinNumber-->
<!--                      }}</span>-->
<!--                                    <a-->
<!--                                        v-if="row.carVinNumber > 0"-->
<!--                                        style="color: red"-->
<!--                                        @click="showHisDetail(row.carVinList)"-->
<!--                                    >{{ row.carVinNumber }}</a-->
<!--                                    >-->
<!--                                </template>-->
<!--                            </Table>-->
<!--                        </div>-->
<!--                        <div>-->
<!--                            <Modal-->
<!--                                v-model="hisDetailVisible"-->
<!--                                :title="detailTitle"-->
<!--                                :closable="false"-->
<!--                                :width="1050"-->
<!--                                :mask-closable="false"-->
<!--                                :footerHide="true"-->
<!--                                :draggable="true"-->
<!--                            >-->
<!--                                <Row>-->
<!--                                    <Table-->
<!--                                        border-->
<!--                                        :row-class-name="rowClassName"-->
<!--                                        v-model="historyDetail"-->
<!--                                        :columns="guanlianColumns"-->
<!--                                        :data="historyDetail"-->
<!--                                    >-->
<!--                                        <template slot-scope="{ row, index }" slot="applyNo" >-->
<!--                                            &lt;!&ndash; 信审跳转 &ndash;&gt;-->
<!--                                            <DynamicLink-->
<!--                                                component="projects/afs-core-business/pages/case/approve/task-detail/regular-approve/historyInfo"-->
<!--                                                :is-full="true"-->
<!--                                                :params="{businessStateIn:row.businessStateIn,taskId:row.id,applyNo:row.applyNo,isFire:'true',isCheck:'false',isCase:'true',isRecord:'true',custName:row.custName,inputType:row.inputType}"-->
<!--                                                :name="row.applyNo" page-title="信审详情" ></DynamicLink>-->
<!--                                        </template>-->
<!--                                        <template slot-scope="{ row, index }" slot="applyNode">-->
<!--                                      <span v-if="row.applyNo.indexOf('P')!=-1">-->
<!--                              {{-->
<!--                                              setDictTitleByType("preApproveStatus", row.applyNode)-->
<!--                                          }}-->
<!--                          </span>-->
<!--                                            <span v-else>-->
<!--                              {{setDictTitleByType("businessStateIn", row.applyNode)}}-->
<!--                          </span>-->
<!--                                        </template>-->
<!--                                    </Table>-->
<!--                                    <div style="margin-left: 900px; margin-top: 12px">-->
<!--                                        <Button-->
<!--                                            size="small"-->
<!--                                            type="primary"-->
<!--                                            @click="cancelDetail"-->
<!--                                            icon="ios-backspace"-->
<!--                                        >关闭</Button-->
<!--                                        >-->
<!--                                    </div>-->
<!--                                </Row>-->
<!--                            </Modal>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </Panel>-->
                <Panel name="3" :style="getOrderByNavId('asset_40')">
                    内部提醒信息
                    <span id="asset_40"></span>
                    <div slot="content">
                        <div class="j-sb">
                            <div></div>
                            <div>
                                <Row>
                                    <Button @click="addMessage" type="success" icon="md-send"
                                    >添加留言</Button
                                    >
                                </Row>
                            </div>
                        </div>
                        <Row style="font-size: 14px">
                            <Table
                                border
                                :columns="innerRemindColums"
                                :data="remindListInside"
                            >
                                <template slot-scope="{ row, index }" slot="remindType">
                  <span>{{
                          setDictTitleByType("msgType", row.remindType)
                      }}</span>
                                </template>
                                <template slot-scope="{ row, index }" slot="remindContent">
                  <span>{{
                          setDictTitleByType("msgType", row.remindContent)
                      }}</span>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
                <Panel name="4" :style="getOrderByNavId('asset_50')">
                    <span id="asset_50"></span>
                    外部提醒信息
                    <div slot="content">
                        <div class="j-sb">
                            <div></div>
                            <div>
                                <Row>
                                    <Button @click="addMessage" type="success" icon="md-send"
                                    >添加留言</Button
                                    >
                                </Row>
                            </div>
                        </div>
                        <Row style="font-size: 14px">
                            <Table border :columns="outRemindColums" :data="remindListOut">
                                <template slot-scope="{ row, index }" slot="remindType">
                  <span>{{
                          setDictTitleByType("msgType", row.remindType)
                      }}</span>
                                </template>
                                <template slot-scope="{ row, index }" slot="remindContent">
                  <span>{{
                          setDictTitleByType("msgType", row.remindContent)
                      }}</span>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
<!--                <Panel name="100" :style="getOrderByNavId('asset_100')">-->
<!--                    <span id="asset_100"></span>-->
<!--                    特殊业务申请-->
<!--                    <div slot="content">-->
<!--                        <div class="form-box">-->
<!--                            <Table-->
<!--                                border-->
<!--                                :row-class-name="rowClassName"-->
<!--                                v-model="specialBusinessInfoList"-->
<!--                                :columns="specialApplyColums"-->
<!--                                :data="specialBusinessInfoList"-->
<!--                            >-->
<!--                                <template slot-scope="{ row, index }" slot="specialApplyStatus">-->
<!--                              <span>-->
<!--                                  {{setDictTitleByType("specialApplyStatus", row.specialApplyStatus) }}-->
<!--                              </span>-->
<!--                                </template>-->
<!--                                <template slot-scope="{ row, index }" slot="applyReason">-->
<!--                              <span>-->
<!--                                  {{setDictTitleByType("specialForRejection", row.applyReason) }}-->
<!--                              </span>-->
<!--                                </template>-->
<!--                            </Table>-->
<!--                        </div>-->
<!--                        <div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </Panel>-->
<!--                <Panel name="5" :style="getOrderByNavId('asset_5')">-->
<!--                    <span id="asset_5"></span>-->
<!--                    融资信息&#45;&#45;&#45;&#45;&#45;&#45;66-->
<!--                    <div slot="content">-->
<!--                        <financial-prod-->
<!--                            :additionalProessForm="additionalProessForm"-->
<!--                            :isShowDetails="true"-->
<!--                            ref="financialProd"-->
<!--                            @isShowModel="isShowModel"-->
<!--                            @scheduleTitle="scheduleTitle"-->
<!--                            @showCalculate="showCalculate"-->
<!--                            :uuid="uuid"-->
<!--                            :intropath="intropath"-->
<!--                            :curTab="tabName"-->
<!--                            :applyNo="applyNumber"-->
<!--                            @caluMaxValue="caluMaxValue"-->
<!--                            @passValiate="passValiate"-->
<!--                            :queryFormEl="queryFormEl"-->
<!--                            :isAppear="isAppear"-->
<!--                            :loanAmtMax="loanAmtMax"-->
<!--                        >-->
<!--                        </financial-prod>-->
<!--                        &lt;!&ndash; <apply-cost-detail ref="applyCostDetail" :apply-no="this.applyNumber" :is-check="isCheck"></apply-cost-detail> &ndash;&gt;-->
<!--                    </div>-->
<!--                </Panel>-->
                <Panel name="5" :style="getOrderByNavId('asset_130')">
                    融资信息
                    <span id="asset_130"></span>
                    <div slot="content">
                        <financial @getProductInfo="getProductInfo" :applyNo="applyNumber"></financial>
                    </div>
                </Panel>
                <Panel name="6" :style="getOrderByNavId('asset_80')" v-if="mainBasePersonal">
                    <span id="asset_80"></span>
                    客户信息
                    <div slot="content">
                        <!--<Row>
                            <Button @click="saveMainInfo" style="float: right" type="success" v-if="isCheck">保存</Button>
                        </Row>-->
                        <!-- 企业基本信息 -->
                        <!-- <companyBaseInfo v-if="isCompanyApply" ref="companyBaseInfoDom" /> -->
                        <!-- 个人基本信息 -->
                        <template>
                            <Form
                                ref="mainBaseInfo"
                                :label-width="160"
                                :model="mainBaseInfo"
                                label-position="right"
                                inline
                                class="mainBaseInfo"
                            >
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="custName" label="申请人姓名">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.custName"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="custLevel" label="客户等级">
                                            <Input
                                                v-model="mainBaseInfo.custLevel"
                                                type="text"
                                                disabled
                                                placeholder=""
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="certType" label="证件类型">
                                            <Select
                                                style="width: 150px"
                                                v-model="mainBaseInfo.certType"
                                                disabled
                                            >
                                                <Option
                                                    v-for="item in dataDic.certType"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}</Option
                                                >
                                            </Select>
                                            <!--<Input
                                                type="text"
                                                v-model="mainBaseInfo.certType"
                                                disabled
                                                placeholder="请输入",
                                                slot="mainBaseInfo.certType"
                                            >
                                            </Input>-->
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="certNo" label="证件号码">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.certNo"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="isLongTerm" label="现居住地详细地">
                                            <rui-region
                                                v-model="mainBaseInfo.detailAddressTemp"
                                                style="width: 249px"
                                                disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="ifPersonalToEnterprise" label="客户是否由个人转企业">
                                            <Select v-model="mainBaseInfo.ifPersonalToEnterprise" disabled>
                                                <Option
                                                    v-for="item in dataDic.isDefault"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Form>
                        </template>
                    </div>
                </Panel>
                <Panel name="7" :style="getOrderByNavId('asset_20')" v-if="mainBaseEnterprise">
                    <span id="asset_20"></span>
                    企业信息
                    <div slot="content">
                        <template>
                            <Form
                                ref="mainBaseInfo"
                                :label-width="160"
                                :model="mainBaseInfo"
                                label-position="right"
                                inline
                                class="mainBaseInfo">
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="enterpriseName" label="企业名称">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.enterpriseName"
                                                disabled>
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="custLevel" label="客户等级">
                                            <Input
                                                v-model="mainBaseInfo.custLevel"
                                                type="text"
                                                disabled>
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="custName" label="法人姓名">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.custName"
                                                disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="certNo" label="法人身份证">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.certNo"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="socunicrtCode" label="统一社会信用代码">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.socunicrtCode"
                                                disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="enterprisesType" label="企业性质">
                                            <Select
                                                style="width: 150px"
                                                v-model="mainBaseInfo.natureEnterprise"
                                                disabled
                                            >
                                                <Option
                                                    v-for="item in dataDic.natureEnterprise"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}</Option
                                                >
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="detailAddressTemp" label="企业经营地">
                                            <rui-region
                                                v-model="mainBaseInfo.detailAddressTemp"
                                                style="width: 249px"
                                                disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <!--                              <Col span="16">-->
                                    <!--                                  <FormItem prop="isLongTerm" label="企业经营地详细地">-->
                                    <!--                                      <rui-region-->
                                    <!--                                          v-model="mainBaseInfo.detailAddressTemp"-->
                                    <!--                                          style="width: 249px"-->
                                    <!--                                          disabled-->
                                    <!--                                      />-->
                                    <!--                                  </FormItem>-->
                                    <!--                              </Col>-->
                                </Row>
                            </Form>
                        </template>
                    </div>
                </Panel>
                <Panel name="25" :style="getOrderByNavId('asset_90')" v-if="personalChangeEnterprise">
                    <span id="asset_90"></span>
                    企业信息（个人转企业）
                    <div slot="content">
                        <template>
                            <Form
                                :label-width="160"
                                :model="mainBaseInfo"
                                label-position="right"
                                inline
                                class="mainBaseInfo">
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="enterpriseName" label="企业名称">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.enterpriseName"
                                                disabled>
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="socunicrtCode" label="统一社会信用代码">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.socunicrtCode"
                                                disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="natureEnterprise" label="企业性质">
                                            <Select
                                                v-model="mainBaseInfo.natureEnterprise"
                                                disabled
                                            >
                                                <Option
                                                    v-for="item in dataDic.natureEnterprise"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="custName" label="法人姓名">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.custName"
                                                disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="certNo" label="法人身份证">
                                            <Input
                                                type="text"
                                                v-model="mainBaseInfo.certNo"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="businessLicenseAddress" label="营业执照地址">
                                            <rui-region
                                                v-model="mainBaseInfo.businessLicenseAddress"
                                                style="width: 249px"
                                                :town="false"
                                                :road="false"
                                                disabled
                                            />
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Form>
                        </template>
                    </div>
                </Panel>
                <Panel
                    name="8"
                    :style="getOrderByNavId('asset_100')"
                    id="hong_24"
                    v-if="isShowAffiliateInfo"
                >
                    <span id="asset_100"></span>
                    挂靠信息
                    <div slot="content">
                        <Form
                            ref="baseInfo"
                            :label-width="170"
                            label-position="right"
                            inline
                        >
                            <div>
                                <Row>
                                    <Col span="8">
                                        <FormItem prop="busiType" label="挂靠方式">
                                            <Select
                                                v-model="affiliateInfo.busiType"
                                                disabled
                                                placeholder="请选择"
                                            >
                                                <Option
                                                    v-for="item in dataDic.affiliatedWay"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}</Option
                                                >
                                            </Select>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="affiliatedName" label="公司名称">
                                            <Input
                                                type="text"
                                                v-model="affiliateInfo.affiliatedName"
                                                disabled
                                                placeholder="请输入"
                                                style="width: 200px"
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="socUniCrtCode" label="统一社会信用代码">
                                            <Input
                                                type="text"
                                                v-model="affiliateInfo.socUniCrtCode"
                                                disabled
                                                placeholder="请输入"
                                                style="width: 200px"
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8">
                                        <Form-item label="法定代表人姓名" prop="legalPersonName">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="affiliateInfo.legalPersonName"
                                                clearable
                                                placeholder="请输入"
                                            />
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="企业联系电话" prop="companyPhone">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="affiliateInfo.companyPhone"
                                                clearable
                                                placeholder="请输入"
                                            />
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="注册地址详情" prop="registeredAddress">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="affiliateInfo.registeredAddress"
                                                clearable
                                                placeholder="请输入"
                                                style="width: 300px"
                                            />
                                        </Form-item>
                                    </Col>
                                </Row>
                                <Row v-if="pageInfo.affiliateInfo.signatoryType === 'authorizer'">
                                    <Col span="8">
                                        <Form-item label="办理人" prop="signatoryType">
                                            <Select
                                                v-model="pageInfo.affiliateInfo.signatoryType"
                                                disabled
                                                placeholder="请选择"
                                            >
                                                <Option
                                                    v-for="item in dataDic.signatoryType"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}</Option
                                                >
                                            </Select>
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="授权代表姓名" prop="authorizerName">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="pageInfo.affiliateInfo.authorizerName"
                                                clearable
                                                placeholder="请输入"
                                            />
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="授权代表联系电话" prop="authorizerPhone">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="pageInfo.affiliateInfo.authorizerPhone"
                                                clearable
                                                placeholder="请输入"
                                                style="width: 300px"
                                            />
                                        </Form-item>
                                    </Col>
                                </Row>
                                <Row v-if="pageInfo.affiliateInfo.signatoryType === 'authorizer'">
                                    <Col span="8">
                                        <Form-item label="授权代表身份证号" prop="authorizerIdcard">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="pageInfo.affiliateInfo.authorizerIdcard"
                                                clearable
                                                placeholder="请输入"
                                            />
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="授权代表银行卡号" prop="authorizerBankCode">
                                            <Input
                                                type="text"
                                                v-bind:disabled="true"
                                                v-model="pageInfo.affiliateInfo.authorizerBankCode"
                                                clearable
                                                placeholder="请输入"
                                            />
                                        </Form-item>
                                    </Col>
                                    <Col span="8">
                                        <Form-item label="授权代表职务" prop="authorizerDuty">
                                            <Select
                                                v-model="pageInfo.affiliateInfo.authorizerDuty"
                                                disabled
                                                placeholder="请选择"
                                            >
                                                <Option
                                                    v-for="item in dataDic.authorizerDuty"
                                                    :label="item.title"
                                                    :value="item.value"
                                                    :key="item.value"
                                                >{{ item.title }}</Option
                                                >
                                            </Select>
                                        </Form-item>
                                    </Col>
                                </Row>
                            </div>
                        </Form>
                    </div>
                </Panel>
<!--                <Panel name="23" v-if="isShowAffiliateInfo" :style="getOrderByNavId('asset_23')" id="hong_23">-->
<!--                    挂靠实时数据-->
<!--                    <span id="asset_23"></span>-->
<!--                    <div slot="content">-->
<!--                        <div class="form-box">-->
<!--                            <Table-->
<!--                                border-->
<!--                                :row-class-name="rowClassName"-->
<!--                                v-model="this.realTimeDataBody"-->
<!--                                :columns="realTimeDataColumns"-->
<!--                                :data="this.realTimeDataBody"-->
<!--                            >-->
<!--                            </Table>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </Panel>-->
                <Panel name="9" :style="getOrderByNavId('asset_110')">
                    <span id="asset_110"></span>
                    担保人信息
                    <div slot="content">
                        <div class="form-box">
                            <Table
                                border
                                :row-class-name="rowClassName"
                                v-model="caseinfoList"
                                :columns="caseinfoColums"
                                :data="caseinfoList"
                            >
                                <template slot-scope="{ row, index }" slot="custRelation">
                  <span>{{
                          setDictTitleByType("custRelation", row.custRelation)
                      }}</span>
                                </template>
                                <template slot-scope="{ row, index }" slot="isFirstGuarantor">
                                    <i-switch :value="row.isFirstGuarantor" size="large" true-color="green" false-color="orange" true-value="1" false-value="0" :disabled="true">
                                        <span slot="open">是</span>
                                        <span slot="close">否</span>
                                    </i-switch>
                                </template>
                            </Table>
                        </div>
                        <div>
<!--                            <Modal-->
<!--                                v-model="hisDetailVisible"-->
<!--                                :title="detailTitle"-->
<!--                                :closable="false"-->
<!--                                :width="600"-->
<!--                                :mask-closable="false"-->
<!--                                :footerHide="true"-->
<!--                                :draggable="true"-->
<!--                            >-->
<!--                                <Row>-->
<!--                                    <Table-->
<!--                                        border-->
<!--                                        :row-class-name="rowClassName"-->
<!--                                        v-model="historyDetail"-->
<!--                                        :columns="guanlianColumns"-->
<!--                                        :data="historyDetail"-->
<!--                                    >-->
<!--                                    </Table>-->
<!--                                    <div style="margin-left: 500px; margin-top: 12px">-->
<!--                                        <Button size="small" type="primary" icon="ios-backspace"-->
<!--                                        >关闭</Button-->
<!--                                        >-->
<!--                                    </div>-->
<!--                                </Row>-->
<!--                            </Modal>-->
                        </div>
                    </div>
                </Panel>
                <Panel name="10" :style="getOrderByNavId('asset_200')">
                    <span id="asset_200"></span>
                    银行卡鉴权
                    <div slot="content">
                        <Form
                            ref="pageInfo"
                            :label-width="160"
                            :model="loanBankCardInfo"
                            label-position="right"
                            inline
                        >
                            <!--                            <Row>-->
                            <!--                                <Button @click="showWarningInfo" style="float: right" type="success" v-if="carInfo.caseFlag && this.pageTwo">打开</Button>-->
                            <!--                            </Row>-->
                            <Row>
                                <Col span="8">
                                    <FormItem prop="applyNo" label="开户人">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.accountName"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="custName" label="手机号">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.bankPhone"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="applyNo" label="开户行">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.bankBranch"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="custName" label="银行卡号">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.accountNo"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="loanAmt" label="签约渠道">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.verChannelBelong"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="loanAmt" label="鉴权结果">
                                        <Input
                                            type="text"
                                            v-model="loanBankCardInfo.verStatus"
                                            disabled
                                            placeholder=""
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="企业开户名称">
                                        <Input type="text" v-model="loanBankCardInfo.enterpriseAccountName" disabled/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="开户人银行卡号">
                                        <Input type="text" v-model="loanBankCardInfo.enterpriseAccountNo" disabled />
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="开户行" >
                                        <Input type="text" disabled v-model="loanBankCardInfo.enterpriseBankName"/>
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Panel>
                <Panel name="11" v-if="this.examineAssetPageInfoVo.businessType === '01'" :style="getOrderByNavId('asset_150')">
                    <span id="asset_150"></span>
                    发票信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div>
                                <Form ref="pageInfo" :label-width="160" :model="carInvoiceInfo" label-position="right" disabled inline>
                                <Row v-if="isChannelBelong">
                                    <Col span="8">
                                        <FormItem label="是否后补发票" prop="carInvoice.isLaterSupply" >
                                            <Select v-model="carInvoiceInfo.isLaterSupply"  disabled>
                                                <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                        :key="item.value" :label="item.title">
                                                    {{ item.title }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceCode"
                                            label="发票代码"
                                            :rules="{required: carInvoiceInfo.isLaterSupply!='1',message: '发票代码不能为空',trigger: 'blur',}">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.invoiceCode"
                                                placeholder="请输入">
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceNumber"
                                            label="发票号码">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.invoiceNumber"
                                                placeholder="请输入">
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="makeInvoiceUnit"
                                            label="开票方名称"
                                            :rules="{required: carInvoiceInfo.isLaterSupply!='1',message: '开票方名称不能为空',trigger: 'blur',}">
                                            <div :class="currentIndex ==0 ? 'carInvoiceInfoActiveDev':'carInvoiceInfoNormal'">
                                                <Input
                                                    type="text"
                                                    v-model="carInvoiceInfo.makeInvoiceUnit" disabled>
                                                </Input>
                                            </div>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceAmt"
                                            label="发票金额"
                                            :rules="{required: carInvoiceInfo.isLaterSupply!='1',message: '发票金额不能为空',trigger: 'blur',}">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.invoiceAmt"
                                                placeholder="请输入" disabled>
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceRate"
                                            label="发票税率(%)"
                                            :rules="{required: carInvoiceInfo.isLaterSupply!='1',message: '发票税率不能为空',trigger: 'blur',}">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.invoiceRate"
                                                placeholder="请输入" disabled>
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="excludingTaxAmt"
                                            label="不含税价"
                                            :rules="{required: carInvoiceInfo.isLaterSupply!='1',message: '不含税价不能为空',trigger: 'blur',}">
                                            <Input
                                                v-model="carInvoiceInfo.excludingTaxAmt"
                                                placeholder="请输入"
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceDate"
                                            label="开票日期"
                                            :rules="{type: 'date',required: carInvoiceInfo.isLaterSupply!='1',message: '开票日期不能为空',trigger: 'change',}">
                                            <DatePicker
                                                type="date"
                                                v-model="carInvoiceInfo.invoiceDate"
                                                format="yyyy-MM-dd"
                                            ></DatePicker>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="invoiceAmt" label="总金额">
                                            <Input type="text" v-model="carInvoiceInfo.invoiceAmt" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="buyerName" label="购买方名称">
                                            <Input type="text" v-model="carInvoiceInfo.buyerName" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="buyerIdcardNo" label="购买方纳税人识别号">
                                            <Input type="text" v-model="carInvoiceInfo.buyerIdcardNo" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="carVin" label="车架号/车辆识别代码">
                                            <Input type="text" v-model="carInvoiceInfo.carVin" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="saleName" label="销售方名称">
                                            <Input type="text" v-model="carInvoiceInfo.makeInvoiceUnit" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem prop="totalRent" label="发票状态">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.checkResult"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem prop="title" label="验真结果">
                                            <Input :class="carInvoiceInfo.resultDescribe !='红冲或作废' ? 'ifDealerGuaranteeDev':'ifDealerGuaranteeDevs'"
                                                type="text"
                                                v-model="carInvoiceInfo.resultDescribe"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem prop="title" label="验真姓名">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.checkName"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem prop="title" label="验真金额">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.checkAmount"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem prop="title" label="验真车架号">
                                            <Input
                                                type="text"
                                                v-model="carInvoiceInfo.checkVin"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="24" class="iphone-invoice">
                                        <FormItem prop="title" label="购车发票识别项">
                                            <span style="font-weight: bold;color: red;">{{invoiceInfoResult}}</span>
                                        </FormItem>
                                    </Col>
                                </Row>
<!--                                <Row>-->
<!--                                    <Col span="24" class="iphone-invoice">-->
<!--                                        <FormItem prop="title" label="购车发票验真项">-->
<!--                                            <span style="font-weight: bold;color: red;">{{invoiceInfoResultTemp}}</span>-->
<!--                                        </FormItem>-->
<!--                                    </Col>-->
<!--                                </Row>-->
                            </Form></div>
<!--                            <div>
                                <Button
                                    type="primary"
                                    @click="saveInvoiceInfo"
                                    @click.stop
                                    class="blues"
                                >保存</Button
                                >
                            </div> -->
                        </div>

                    </div>
                </Panel>
                <Panel name="14.1" :style="getOrderByNavId('asset_160')">
                    <span id="asset_160"></span>
                    保单信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div><Form
                                ref="pageInfo"
                                :label-width="160"
                                :model="guaranteeInfo"
                                label-position="right"
                                inline
                            >
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceCode"
                                            label="被保险人"
                                        >
                                            <Input
                                                type="text"
                                                v-model="guaranteeInfo.assured" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceNumber"
                                            label="行驶证车主"
                                        >
                                            <Input
                                                type="text"
                                                v-model="guaranteeInfo.drivingLicenseOwner" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="makeInvoiceUnit"
                                            label="投保人"
                                        >
                                            <Input
                                                type="text"
                                                v-model="guaranteeInfo.applicant"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceAmt"
                                            label="车架号/车辆识别代码"
                                        >
                                            <Input
                                                type="text"
                                                v-model="guaranteeInfo.vin"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceRate"
                                            label="发动机号"
                                        >
                                            <Input
                                                type="text"
                                                v-model="guaranteeInfo.engineNo" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="excludingTaxAmt"
                                            label="使用性质"
                                        >
                                            <Input
                                                v-model="guaranteeInfo.useCharacter" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row >
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="invoiceDate"
                                            label="车损险保额"
                                        >
                                            <Input type="text"  v-model="guaranteeInfo.damageCoverage" disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="invoiceAmt" label="三责险">
                                            <Input type="text" v-model="guaranteeInfo.threeLiabilityInsurance" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span="8">
                                        <FormItem prop="buyerName" label="保单生效日期">
                                            <Input type="text" v-model="guaranteeInfo.policyDate" disabled
                                            />
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="24" class="iphone-invoice">
                                        <FormItem prop="title" label="车辆保险单据识别结果">
                                            <span  style="font-weight: bold;color: red; white-space: pre-wrap">{{guaranteeCheckResult}}</span>
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Form>
                            </div>
                        </div>
                    </div>
                </Panel>
                <Panel name="170" :style="getOrderByNavId('asset_170')">
                    <span id="asset_170"></span>
                    机动车登记证书信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div><Form
                                ref="pageInfo"
                                :label-width="160"
                                :model="certificateInfo"
                                label-position="right"
                                inline
                            >
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="ownName"
                                            label="机动车所有人"
                                        >
                                            <Input
                                                type="text"
                                                v-model="certificateInfo.ownName" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="ownCertNo"
                                            label="机动车所有人身份证号码"
                                        >
                                            <Input
                                                type="text"
                                                v-model="certificateInfo.ownCertNo" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="carVin"
                                            label="车架号"
                                        >
                                            <Input
                                                type="text"
                                                v-model="certificateInfo.carVin"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="engineNo"
                                            label="发动机号"
                                        >
                                            <Input
                                                type="text"
                                                v-model="certificateInfo.engineNo"
                                                disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="actualMortgagorName"
                                            label="抵押权人"
                                        >
                                            <Input
                                                type="text"
                                                v-model="certificateInfo.actualMortgagorName" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="actualMortgagorCertNo"
                                            label="抵押权人身份证明号码"
                                        >
                                            <Input
                                                v-model="certificateInfo.actualMortgagorCertNo" disabled
                                            >
                                            </Input>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row >
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="certificateNumberFirstPage"
                                            label="机动车登记证书编号(信息栏)"
                                        >
                                            <Input type="text"  v-model="certificateInfo.certificateNumberFirstPage" disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                    <Col span="8" class="iphone-invoice">
                                        <FormItem
                                            prop="certificateNumberRegistrationBar"
                                            label="机动车登记证书编号(登记栏)"
                                        >
                                            <Input type="text"  v-model="certificateInfo.certificateNumberRegistrationBar" disabled
                                            ></Input>
                                        </FormItem>
                                    </Col>
                                <Row>
                                    <Col span="24" class="iphone-invoice">
                                        <FormItem prop="title" label="机动车登记证书信息识别结果">
                                            <span  style="font-weight: bold;color: red; white-space: pre-wrap"> {{ certificateInfoMsg }}</span>
                                        </FormItem>
                                    </Col>
                                </Row>
                                </Row>
                            </Form>
                            </div>
                        </div>
                    </div>
                </Panel>
                <Panel name="111" :style="getOrderByNavId('asset_111')" v-if="isChannelBelong">
                    <span id="asset_101"></span>
                    开票申请信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div>
                                <Form ref="caseReceiptInfo" :model="caseReceiptInfo" :label-width="160"  inline>
                                    <Row>
                                        <Col span="8">
                                            <FormItem label="是否开票" >
                                                <Select v-model="caseReceiptInfo.invoiceFlag"  disabled>
                                                    <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                            :key="item.value" :label="item.title">
                                                        {{ item.title }}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem label="发票抬头" >
                                                <Input type="text" v-model="caseReceiptInfo.invoiceTitle" disabled/>
                                            </FormItem>
                                        </Col>

                                        <Col span="8">
                                            <FormItem  label="纳税人识别号">
                                                <Input type="text" v-model="caseReceiptInfo.taxpayerNum"  disabled/>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8">
                                            <FormItem label="开户银行">
                                                <Input type="text" v-model="caseReceiptInfo.bankName"
                                                       :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem label="银行账号">
                                                <Input type="text" v-model="caseReceiptInfo.bankAccount"
                                                       :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem label="电子邮箱" >
                                                <Input type="text" v-model="caseReceiptInfo.email" :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="16" class="addressBox">
                                            <FormItem label="企业地址">
                                                <rui-region
                                                    v-model="caseReceiptInfo.businessAddressBak"
                                                    :detail="false"
                                                    :disabled="isDisabled"
                                                    class="tw100"
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem   label="企业电话">
                                                <Input type="text" v-model="caseReceiptInfo.businessPhone" :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8">
                                            <FormItem label="收件人">
                                                <Input type="text" v-model="caseReceiptInfo.recipient"
                                                       :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem   label="收件人联系方式">
                                                <Input type="text" v-model="caseReceiptInfo.recipientContact"
                                                       :disabled="isDisabled"/>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem label="邮政编号">
                                                <Input  v-model="caseReceiptInfo.postalCode"  type="text"  :disabled="isDisabled" />
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="16" class="addressBox">
                                            <FormItem label="邮寄地址">
                                                <rui-region
                                                    v-model="caseReceiptInfo.sendAddressBak"
                                                    :detail="false"
                                                    :disabled="isDisabled"
                                                    class="tw100"
                                                />
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Form>

                            </div>

                        </div>

                    </div>
                </Panel>

                <Panel
                    name="13"
                    v-if="isDecorationHide"
                    :style="getOrderByNavId('asset_40')"
                >
                    <span id="asset_12"></span>
                    精品装潢发票信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div>
                                <Form
                                    ref="pageInfo"
                                    :label-width="160"
                                    :model="carDecorationInvoiceInfo"
                                    label-position="right"
                                    inline
                                >
                                    <Row>
                                        <Col span="8">
                                            <FormItem
                                                prop="invoiceCode"
                                                label="发票代码"
                                                :rules="{required: true,message: '发票代码不能为空',trigger: 'blur',}">
                                                <Input
                                                    type="text"
                                                    v-model="carDecorationInvoiceInfo.invoiceCode"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem
                                                prop="invoiceNumber"
                                                label="发票号码"
                                                :rules="{required: true,message: '发票号码不能为空',trigger: 'blur',}">
                                                <Input
                                                    type="text"
                                                    v-model="carDecorationInvoiceInfo.invoiceNumber"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8">
                                            <FormItem
                                                prop="invoiceAmt"
                                                label="发票金额"
                                                :rules="{required: true,message: '发票金额不能为空',trigger: 'blur',}">
                                                <Input
                                                    type="text"
                                                    v-model="carDecorationInvoiceInfo.invoiceAmt"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8">
                                            <FormItem
                                                prop="excludingTaxAmt"
                                                label="不含税价"
                                                :rules="{required: true,message: '不含税价不能为空',trigger: 'blur',}">
                                                <Input
                                                    type="text"
                                                    v-model="carDecorationInvoiceInfo.excludingTaxAmt"
                                                    placeholder="请输入">
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Form>
                            </div>
                            <div>
                                <Button
                                    type="primary"
                                    @click="toSaveDecorationInvoiceInfo"
                                    @click.stop
                                    class="blues"
                                >保存</Button>
                            </div>
                        </div>
                    </div>
                </Panel>
                <Panel name="14" :style="getOrderByNavId('asset_120')">
                    车辆信息
                    <span id="asset_120"></span>
                    <div slot="content">
                        <div  class="j-sb">
                            <div></div>
                            <div> <Button
                                type="primary"
                                @click="saveGpsPerform"
                                @click.stop
                                class="blues"
                            >保存</Button></div>
                        </div>
                        <Form
                            ref="pageInfo"
                            :label-width="170"
                            :model="caseCarInfo"
                            label-position="right"
                            inline>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="title" label="品牌">
                                        <Input type="text" v-model="caseCarInfo.brandName" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="totalRent" label="车系">
                                        <Input type="text" v-model="caseCarInfo.seriesName" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="saleAdvisor" label="车型">
                                        <Tooltip :content="caseCarInfo.modelName" placement="bottom" max-width="300">
                                            <Input type="text" v-model="caseCarInfo.modelName"  disabled></Input>
                                        </Tooltip>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row v-if="this.examineAssetPageInfoVo.businessType === '02'">
                                <Col span="8">
                                    <FormItem prop="yearTransNum" label="半年内过户次数">
                                        <Input type="text" v-model="caseCarInfo.yearTransNum" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="transNums" label="总过户次数">
                                        <Input type="text" v-model="caseCarInfo.transNums" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="evaluatingPrice" label="评估价格">
                                        <Input type="text" v-model="caseCarInfo.evaluatingPrice" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="qualityGrade" label="车辆制造日期">
                                        <div>
                                            <Input type="text" v-model="caseCarInfo.vehicleMadeDate" disabled>
                                            </Input>
                                        </div>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem prop="salePrice" label="实际销售价">
                                        <Input type="text" v-model="caseCarInfo.salePrice" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="guidePrice" label="新车指导价">
                                        <Input type="text" v-model="caseCarInfo.guidePrice" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="title" label="车架号">
                                        <Input type="text" v-model="caseCarInfo.carVin" disabled></Input>
                                    </FormItem>
                                </Col>

                                <Col span="8">
                                    <FormItem prop="gbCode" label="国标码">
                                        <Input type="text" v-model="caseCarInfo.gbCode" disabled> </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="isGreen" label="是否新能源车">
                                        <Input type="text" v-model="carInfoVo.isGreen"  disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="carTypeTemp" label="车辆类型">
                                        <Select
                                            v-model="carTypeTemp"
                                            disabled
                                            placeholder=""
                                        >
                                            <Option
                                                v-for="item in dataDic.carType"
                                                :label="item.title"
                                                :value="item.value"
                                                :key="item.value"
                                            >{{ item.title }}</Option
                                            >
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="plateAddress" label="上牌地">
                                        <Input type="text" v-model="outlineVo.plateAddress" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="engineNo" label="发动机号">
                                        <Input type="text" v-model="caseCarInfo.engineNo" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem prop="gpsSupplierTemp" label="GPS厂家">
                                        <Select v-model="gpsSupplierTemp" style="width:180px">
                                            <Option v-for="it in dataDic.gpsSupplier" :value="it.value" :key="it.value">{{it.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                    <Col span="8">
                                        <FormItem prop="gpsPerform" label="是否含履约">
                                            <Select v-model="gpsPerform">
                                                <Option :key="item.value" :value="item.value" v-for="item in gpsPerformList">
                                                    {{
                                                        item.label
                                                    }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                            </Row>
                        </Form>
                    </div>
                </Panel>
                <Panel name="16" v-show="'0' === pageInfo.rentType" :style="getOrderByNavId('asset_13')">
                    <span id="asset_13"></span>
                    首保信息
                    <div slot="content">
                        <div class="j-sbt">
                            <div>
                                <Form
                                    ref="carInsuranceInfo"
                                    :label-width="160"
                                    :model="carInsuranceInfo"
                                    label-position="right"
                                    inline
                                >
                                    <Row v-if="isChannelBelong">
                                        <Col span="8">
                                            <FormItem label="是否后补商业险" >
                                                <Select v-model="carInsuranceInfo.isLaterSupply" :disabled="isDisabled">
                                                    <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                            :key="item.value" :label="item.title">
                                                        {{ item.title }}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem prop="channelFullName" label="是否融商业险">
                                                <Input
                                                    type="text"
                                                    v-model="financeItem.isHaveBisness"
                                                    disabled
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                prop="insuranceNo"
                                                label="商业险保单号"
                                                :rules="{
                      required: carInsuranceInfo.isLaterSupply!='1',
                      message: '商业险保单号不能为空',
                      trigger: 'blur',
                    }"
                                            >
                                                <Input
                                                    disabled
                                                    type="text"
                                                    v-model="carInsuranceInfo.insuranceNo"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="6" class="iphone-invoice">
                                            <FormItem
                                                prop="insuranceAmt"
                                                label="商业险保费"
                                                :rules="{
                      required: carInsuranceInfo.isLaterSupply!='1',
                      message: '商业险保费不能为空',
                      trigger: 'blur',
                    }"
                                            >
                                                <Input
                                                    disabled
                                                    type="text"
                                                    v-model="carInsuranceInfo.insuranceAmt"
                                                    placeholder="请输入"
                                                />
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem prop="bisnessAmt" label="商业险融资额">
                                                <Input
                                                    type="text"
                                                    v-model="financeItem.bisnessAmt"
                                                    disabled
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                label="商业险生效日"
                                                :class="bisnessAmtInput ?'inputindex' : 'inputindexs'"
                                                prop="insuranceStartTime"
                                                :rules="{type: 'date',required: carInsuranceInfo.isLaterSupply!='1',message: '商业险生效日不能为空',trigger: 'change',}">
                                                <DatePicker
                                                    disabled
                                                    type="date"
                                                    v-model="carInsuranceInfo.insuranceStartTime"
                                                    @on-change="businessDate"
                                                    format="yyyy-MM-dd"
                                                    transfer
                                                ></DatePicker>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                label="商业险失效日"
                                                prop="insuranceEndTime"
                                                :rules="{type: 'date',required: carInsuranceInfo.isLaterSupply!='1',message: '商业险失效日不能为空',trigger: 'change',}">
                                                <DatePicker
                                                    disabled
                                                    type="date"
                                                    v-model="carInsuranceInfo.insuranceEndTime"
                                                    format="yyyy-MM-dd"
                                                ></DatePicker>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem prop="insuranceMoney"> </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                prop="insuranceMoney"
                                                label="车损险保额"
                                                :rules="{required: carInsuranceInfo.isLaterSupply!='1',message: '车损险保额不能为空',trigger: 'blur',}">
                                                <Input
                                                    disabled
                                                    type="text"
                                                    v-model="carInsuranceInfo.insuranceMoney"
                                                    @on-blur="checkCarDamageMoney"
                                                    placeholder="请输入"
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                prop="thirdInsurance"
                                                label="三者险"
                                                :rules="{required: carInsuranceInfo.isLaterSupply!='1',message: '三者险不能为空',trigger: 'blur',}">
                                                <Input
                                                    disabled
                                                    v-model="carInsuranceInfo.thirdInsurance"
                                                    type="text"
                                                    @on-blur="checkThirdMoney"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Form>
                                <Form
                                    ref="pageInfo"
                                    :label-width="160"
                                    :model="carStrongInsuranceInfo"
                                    label-position="right"
                                    inline
                                >
                                    <Row v-if="isChannelBelong">
                                        <Col span="8">
                                            <FormItem label="是否后补交强险">
                                                <Select v-model="carStrongInsuranceInfo.isLaterSupply"  disabled>
                                                    <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                            :key="item.value" :label="item.title">
                                                        {{ item.title }}
                                                    </Option>
                                                </Select>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem prop="isHaveCompulsory" label="是否融交强险">
                                                <Input
                                                    type="text"
                                                    v-model="financeItem.isHaveCompulsory"
                                                    disabled
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                prop="insuranceNo"
                                                label="交强险保单号"
                                                :rules="{required: carStrongInsuranceInfo.isLaterSupply!='1',message: '交强险保单号不能为空',trigger: 'blur',}">
                                                <Input
                                                    disabled
                                                    type="text"
                                                    v-model="carStrongInsuranceInfo.insuranceNo"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                prop="insuranceAmt"
                                                label="交强险保费"
                                                :rules="{required: carStrongInsuranceInfo.isLaterSupply!='1',message: '交强险保费不能为空',trigger: 'blur',}">
                                                <Input
                                                    disabled
                                                    type="text"
                                                    v-model="carStrongInsuranceInfo.insuranceAmt"
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem prop="compulsoryAmt" label="交强险融资额">
                                                <Input
                                                    type="text"
                                                    v-model="financeItem.compulsoryAmt"
                                                    disabled
                                                    placeholder="请输入"
                                                >
                                                </Input>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                label="交强险生效日"
                                                prop="insuranceStartTime"
                                                :class="insuranceStartTimeIndex?'insuranceStartTimeIndex':'insuranceStartTimeIndexs'"
                                                :rules="{type: 'date',required: carStrongInsuranceInfo.isLaterSupply!='1',message: '交强险生效日不能为空',trigger: 'change',}">
                                                <DatePicker
                                                    disabled
                                                    type="date"
                                                    v-model="carStrongInsuranceInfo.insuranceStartTime"
                                                    @on-change="strongDate"
                                                    format="yyyy-MM-dd"
                                                ></DatePicker>
                                            </FormItem>
                                        </Col>
                                        <Col span="8" class="iphone-invoice">
                                            <FormItem
                                                label="交强险失效日"
                                                prop="insuranceEndTime"
                                                :rules="{type: 'date',required: carStrongInsuranceInfo.isLaterSupply!='1',message: '交强险生效日不能为空',trigger: 'change',}">
                                                <DatePicker
                                                    disabled
                                                    type="date"
                                                    v-model="carStrongInsuranceInfo.insuranceEndTime"
                                                    format="yyyy-MM-dd"
                                                ></DatePicker>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Form>
                            </div>
                            <div>
                                <Button
                                    type="primary"
                                    @click="saveInsurance"
                                    @click.stop
                                    class="blues"
                                >保存</Button
                                >

                            </div>
                        </div>

                    </div>
                </Panel>
                <Panel name="240" :style="getOrderByNavId('asset_240')"  v-show="innerShow" >
                    内购车信息
                    <span id="asset_240" ></span>
                    <div slot="content">
                        <Form
                            ref="pageInfo"
                            :label-width="170"
                            :model="innerCarInfo"
                            label-position="right"
                            inline>
                            <Row >
                                <Col  span="8" >
                                    <FormItem prop="plateNo" label="车牌号">
                                        <Input type="text" v-model="innerCarInfo.plateNo" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8" >
                                    <FormItem prop="color" label="颜色">
                                        <Input type="text" v-model="innerCarInfo.color" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="registerData" label="上牌日期">
                                        <Input type="text" v-model="innerCarInfo.registerData" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row >
                            <Row >
                                <Col  span="8">
                                    <FormItem prop="status" label="车辆状态">
                                        <Input type="text" v-model="innerCarInfo.status" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="plateFlag" label="是否带车牌">
                                        <Input type="text" v-model="innerCarInfo.plateFlag" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>


                                <Col  span="8">
                                    <FormItem prop="isOperateCar" label="是否运营车辆">
                                        <Input type="text" v-model="innerCarInfo.isOperateCar" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row >
                            <Row >
                                <Col  span="8">
                                    <FormItem prop="vehicleNature" label="使用性质">
                                        <Input type="text" v-model="innerCarInfo.vehicleNature" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="empNo" label="用户工号">
                                        <Input type="text" v-model="innerCarInfo.empNo" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="userName" label="用户名">
                                        <Input type="text" v-model="innerCarInfo.userName" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row >
                            <Row >
                                <Col  span="8">
                                    <FormItem prop="idNo" label="身份证">
                                        <Input type="text" v-model="innerCarInfo.idNo" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="mobile" label="电话">
                                        <Input type="text" v-model="innerCarInfo.mobile" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="closingCost" label="成交价">
                                        <Input type="text" v-model="innerCarInfo.closingCost" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row >
                            <Row >
                                <Col  span="8">
                                    <FormItem prop="bidSuccessfulTime" label="成交时间">
                                        <Input type="text" v-model="innerCarInfo.bidSuccessfulTime" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="vinNo" label="车架号">
                                        <Input type="text" v-model="innerCarInfo.vinNo" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col  span="8">
                                    <FormItem prop="engineNumber" label="发动机号">
                                        <Input type="text" v-model="innerCarInfo.engineNumber" disabled >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Panel>
<!--                <Panel name="17.1" :style="getOrderByNavId('asset_131')">
                    <span id="asset_131"></span>
                    电子签约地址信息
                    <div slot="content">
                        <electronic :applyNo="this.applyNumber"></electronic>
                    </div>
                </Panel>-->
<!--                <Panel name="7" :style="getOrderByNavId('asset_15')">-->
<!--                    <span>致电信息</span>-->
<!--                    <span id="asset_15"></span>-->
<!--                    <div class="panel-content" slot="content">-->
<!--                        <div class="form-box">-->
<!--                            <div class="form-box">-->
<!--                                <div class="j-sb">-->
<!--                                    <div></div>-->
<!--                                    <div>-->
<!--                                        <Row>-->
<!--                                            <Button-->
<!--                                                @click="addContact"-->
<!--                                                icon="md-add"-->
<!--                                                class="blue"-->
<!--                                                type="success"-->
<!--                                            >新增联系人</Button-->
<!--                                            >-->
<!--                                        </Row>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="form-box">-->
<!--                                <Table-->
<!--                                    border-->
<!--                                    :row-class-name="rowClassName"-->
<!--                                    :columns="personPhone"-->
<!--                                    :data="caseCustContact"-->
<!--                                >-->
<!--                                    <template slot-scope="{ row, index }" slot="action">-->
<!--                                        <Button-->
<!--                                            @click="editRemark(row)"-->
<!--                                            type="success"-->
<!--                                            size="small"-->
<!--                                            icon="md-create"-->
<!--                                        >备注</Button-->
<!--                                        >-->
<!--                                    </template>-->
<!--                                    <template slot-scope="{ row, index }" slot="callType">-->
<!--                    <span>{{setDictTitleByType("callType", row.callType) }}</span>-->
<!--                                    </template>-->
<!--                                    <template slot-scope="{ row, index }" slot="custRelation">-->
<!--                    <span>{{-->
<!--                            setDictTitleByType("custRelation", row.custRelation)-->
<!--                        }}</span>-->
<!--                                    </template>-->
<!--                                    <template slot-scope="{ row, index }" slot="telPhone">-->
<!--                    <span-->
<!--                    >{{ row.telPhone }}-->
<!--                      &nbsp&nbsp&nbsp-->
<!--                      <Button-->
<!--                          type="success"-->
<!--                          size="small"-->
<!--                          @click="-->
<!--                          callPhone(-->
<!--                            row.telPhone,-->
<!--                            row.custRelation,-->
<!--                            row.custId,-->
<!--                            row.custName-->
<!--                          )-->
<!--                        "-->
<!--                          icon="md-call"-->
<!--                      />-->
<!--                    </span>-->
<!--                                    </template>-->
<!--                                </Table>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </Panel>-->
                <Panel name="52" :style="getOrderByNavId('asset_60')">
                    <span>经销商备注</span><span id="asset_60"></span>
                    <!--              <span id="asset_15"></span>-->
                    <div class="panel-content" slot="content">
                        <div class="form-box">
                            <div>
                                <Form :label-width="160" inline>
                                    <FormItem label="进件备注">
                                        <Input v-model="remarks" type="textarea"
                                               style="width:800px"
                                               disabled/>
                                    </FormItem>
                                </Form>
                                <Form :label-width="160" inline>
                                    <FormItem label="放款备注">
                                        <Input v-model="loanRemarks" type="textarea"
                                               style="width:800px"
                                               disabled/>
                                    </FormItem>
                                </Form>
                            </div>
                        </div>
                    </div>
                </Panel>
                <Panel name="01" :style="getOrderByNavId('asset_11')">
                    汇总信息
                    <span id="asset_11"></span>
                    <div slot="content">
                        <Form
                            :label-width="160"
                            label-position="right"
                            inline
                        >
                            <Row>
                                <Col span="8">
                                    <FormItem label="客户名称">
                                        <Input
                                            type="text"
                                            v-model="mainBaseInfo.custName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="客户身份证">
                                        <Input
                                            type="text"
                                            v-model="mainBaseInfo.certNo"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="产品名称">
                                        <Input
                                            type="text"
                                            v-model="outlineVo.productName"
                                            disabled
                                        >
                                        </Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="购车目的">
                                        <Select
                                            v-model="carPurpose"
                                            disabled
                                        >
                                            <Option
                                                v-for="item in dataDic.carPurpose"
                                                :label="item.title"
                                                :value="item.value"
                                                :key="item.value"
                                            >{{ item.title }}</Option
                                            >
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="经销商名称">
                                        <Input type="text" v-model="outlineVo.channelFullName" disabled></Input>
                                    </FormItem>
                                </Col>
                                <!-- <Col span="8">
                                    <FormItem label="车辆总价款">
                                        <Input type="text" v-model="caseCarInfo.salePrice" disabled></Input>
                                    </FormItem>
                                </Col> -->
                                <Col span="8">
                                    <FormItem label="车辆价格">
                                        <Input type="text" v-model="caseCarInfo.salePrice" disabled></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="附加融金额">
                                        <Input type="text" v-model="productInfo.addAmt" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="合同编号">
                                        <Input type="text" v-model="outlineVo.contractNo" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="车系">
                                        <Input type="text" v-model="caseCarInfo.seriesName" disabled></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="车型">
                                        <Input type="text" v-model="caseCarInfo.modelName" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="车架号">
                                        <Input type="text" v-model="caseCarInfo.carVin" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="发动机号">
                                        <Input type="text" v-model="caseCarInfo.engineNo" disabled></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="融资金额">
                                        <Input type="text" v-model="outlineVo.loanAmt" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="租赁期限">
                                        <Input type="text" v-model="productInfo.loanTerm" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="银行名称">
                                        <Input type="text" v-model="loanBankCardInfo.bankBranch" disabled></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="首付款">
                                        <Input type="text" v-model="productInfo.downPayAmt" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="银行卡号">
                                        <Input type="text" v-model="loanBankCardInfo.accountNo" disabled></Input>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="订单类型">
                                        <Select v-model="orderType" disabled>
                                            <Option
                                                v-for="item in dataDic.orderType"
                                                :label="item.title"
                                                :value="item.value"
                                                :key="item.value"
                                            >{{ item.title }}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="是否先放后抵">
                                    <span :class="outlineVo.lendingMode !='是' ? 'ifDealerGuaranteeDevs':'ifDealerGuaranteeDev'">
                                        <Input type="text" v-model="outlineVo.lendingMode" disabled></Input>
                                    </span>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="所属资方">
                                        <Input v-model="mainBaseInfo.belongingCapital" type="text" disabled></Input>
                                    </FormItem>
                                </Col>
                                <span v-for="item in tableList" :key="item.financeItemCode">
                                    <Col span="8">
                                        <FormItem :label="item.financeItemName">
                                            <Input type="text" v-model="item.financeItemAmt" disabled></Input>
                                        </FormItem>
                                    </Col>
                                </span>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="是否赋强公证">
                                    <span :class="outlineVo.enforceableNotarizationStatus =='是' ? 'ifDealerGuaranteeDevs':'ifDealerGuaranteeDev'">
                                        <Input type="text" v-model="outlineVo.enforceableNotarizationStatus" disabled></Input>
                                    </span>
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Panel>
                <Panel name="18" :style="getOrderByNavId('asset_20')">
                    <span id="asset_20"></span>
                    <span>审批历史记录</span>
                    <div slot="content">
                        <Row style="margin-left: -30px" class="recordHistory" v-for="record in recordHisData">
                            <div  class="j-sbt">
                                <div style="margin-left:20px">
                                    <Icon type="ios-contact" class="icon" :size="70"
                                          v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'"/>
                                </div>
                                <div :bordered="false" class="recordHistoryMsg recordHistoryRecord" :style="record.style"
                                     v-if="record.approveSuggest !== 'referTo' && record.approveSuggest !== 'submitToVisit' && record.approveSuggest !== 'suggestVisit'">
                                    <h4 slot="title" style="font-size:13px">{{record.title}}</h4>
                                    <h3 v-if="record.approveSuggest === 'suggestCondition' || record.approveSuggest === 'suggestConditionF'" style="margin-left: 15px">附条件原因：{{conditionReason}}。{{record.approveMessage}}</h3>
                                    <h3 v-if="record.approveSuggest === 'suggestRejectF'" style="margin-left: 15px">拒绝原因：{{rejectReason}}</h3>
                                    <h3 style="margin-left: 15px;">{{record.content}}</h3>
                                    <h4 v-if="record.approveRemark" class="load_hong_beizhu">{{record.approveRemark}}</h4>
                                </div>
                            </div>

                        </Row>
                    </div>
                </Panel>
                <Panel name="210" :style="getOrderByNavId('asset_210')">
                  <span>联系人信息</span>
                  <span id="asset_210"></span>
                  <div class="panel-content" slot="content">
                    <div class="form-box">
                      <div class="form-box">
                        <div class="j-sb">
                          <div></div>
                          <div>
                            <Row>
                              <Button
                                @click="addContact"
                                icon="md-add"
                                class="blue"
                                type="success"
                              >新增联系人</Button
                              >
                            </Row>
                          </div>
                        </div>
                      </div>
                      <div class="form-box">
                        <Table
                          border
                          :row-class-name="rowClassName"
                          :columns="personPhone"
                          :data="caseCustContact"
                        >
                          <template slot-scope="{ row, index }" slot="action">
                            <Button
                              @click="editRemark(row)"
                              type="success"
                              size="small"
                              icon="md-create"
                            >备注</Button
                            >
                          </template>
                          <template slot-scope="{ row, index }" slot="callType">
                              <span>{{
                                  setDictTitleByType("callType", row.callType)
                                }}</span>
                          </template>
                          <!--                  <template slot-scope="{ row, index }" slot="custRelation">-->
                          <!--                    <span>{{-->
                          <!--                      setDictTitleByType("custRelation", row.custRelation)-->
                          <!--                    }}</span>-->
                          <!--                  </template>-->
                          <template slot-scope="{ row, index }" slot="telPhone">
                      <span
                      >{{ row.telPhone }}
                        &nbsp&nbsp&nbsp
                        <Button
                          v-if="$store.getters.permissions['case_call_loan']"
                          type="success"
                          size="small"
                          @click="callPhone(row.telPhone,row.custRelation,row.id,row.custName,null,row.custType)"
                          icon="md-call"
                        />
                        <Button v-if="row.telPhone && row.custRelation==='00006' && $store.getters.permissions['case_send_sms'] && this.detailParams.userDefinedIndex === 'SECOND_NODE'" style="margin-right:5px" type="success" size="small" @click="getMsgTemp(row.custName,row.telPhone)" icon="md-mail">
                        </Button>
                        <Button v-if="row.telPhone && row.custRelation!=='00006' && $store.getters.permissions['case_send_sms'] && this.detailParams.userDefinedIndex === 'SECOND_NODE'" style="margin-right:5px" type="success" size="small" @click="getMsgTemp(row.custName,row.telPhone)" icon="md-mail">
                        </Button>
                      </span>
                          </template>
                        </Table>
                      </div>
                    </div>
                  </div>
                </Panel>
                <Panel name="220" :style="getOrderByNavId('asset_220')">
                  <span>致电情况历史</span>
                  <span id="asset_220"></span>
                  <div class="panel-content" slot="content">
                    <div class="form-box">
                      <div class="form-box">
                        <Row>
                          <Table
                            border
                            :row-class-name="rowClassName"
                            :columns="personPhoneHistory"
                            :data="callRecord.records"
                          >
                            <template slot-scope="{ row, index }" slot="action">
                              <Button v-if="row.callId && row.callType.length === 0 && row.remark.length === 0"  @click="editCallRecordRemark(row)" type="success" size="small"  icon="md-create"  v-show="isHide">备注</Button>
                            </template>
                            <template slot-scope="{row,index}" slot="phone">
                              <span>{{setCallInNumber(row.phone)}}</span>
                            </template>
                            <template slot-scope="{row,index}" slot="custRelation">
                              <span>{{ setCustRelation(row.custRelation) }}</span>
                            </template>
                            <template slot-scope="{ row, index }" slot="telPhone" >
                              <div style="display:flex;justify-content:center; align-items:center">
                                <span>{{row.telPhone}}</span>
                                &nbsp
                                <Button v-if="$store.getters.permissions['case_call_loan']" type="success" size="small" @click="callPhone(row.telPhone,row.custRelation,row.id,row.custName,null,row.custType)" icon="md-call" v-show="isHide"/>
                              </div>
                            </template>
                          </Table>
                        </Row>
                        <Row type="flex" justify="end" class="page">
                          <Page
                            :current="callRecord.pageNumber"
                            :total="callRecord.total"
                            :page-size="callRecord.pageSize"
                            @on-change="changePage"
                            @on-page-size-change="changePageSize"
                            :page-size-opts="[10,20,50]"
                            size="small"
                            show-total
                            show-elevator
                            show-sizer
                          ></Page>
                        </Row>
                      </div>
                    </div>
                  </div>
                </Panel>
                <Panel name="230" :style="getOrderByNavId('asset_230')" id="hong_c_230">
                  短信发送历史
                  <span id="asset_230"></span>
                  <div slot="content">
                    <Row>
                      <Table border :columns="smsRecordColumns" :data="smsRecord.records">
                        <template slot-scope="{ row, index }" slot="telPhone" >
                          <div style="display:flex;justify-content:center; align-items:center">
                            <span>{{row.telPhone}}</span>
                            &nbsp
                            <Button  type="success" size="small" @click="callPhone(row.telPhone,row.custRelation,row.id,row.custName,null,row.custType)" icon="md-call" v-show="isCheck&&isHide"/>
                          </div>
                        </template>
                        <template slot-scope="{ row, index }" slot="sendStatus">
                          <span>{{convertSMSSendStatus(row.sendStatus)}}</span>
                        </template>
                      </Table>
                    </Row>
                    <br>
                    <Row type="flex" justify="end" class="page">
                      <Page :current="smsRecord.pageNumber" :transfer="true" :total="smsRecord.total" :page-size="smsRecord.pageSize" @on-change="changeSMSRecordPage"
                            @on-page-size-change="changeSMSRecordPageSize" :page-size-opts="smsRecordPageSizeOpts" size="small" show-total
                            show-elevator show-sizer></Page>
                        </Row>
                    </div>
                </Panel>
            </Collapse>
        </Card>

        <!-- 致电情况备注 -->
        <Modal
            v-model="remarkVisible"
            :label-width="80"
            :closable="false"
            :mask-closable="false"
            :scrollable="false"
            :width="580"
            :styles="{ top: '180px' }"
        >
            <div
                slot="header"
                style="text-align: center; font-size: 24px; font-weight: bolder"
            >
                {{ messageTitle }}
            </div>
            <Form ref="remarkFrom" :model="remarkFrom" inline :label-width="100">
                <FormItem label="致电情况" prop="callType" style="width: 600px">
                    <Select
                        style="width: 160px"
                        v-model="remarkFrom.callType"
                        placeholder="请选择"
                    >
                        <Option
                            v-for="item in dataDic.callType"
                            :label="item.title"
                            :value="item.value"
                            :key="item.value"
                        >{{ item.title }}</Option
                        >
                    </Select>
                </FormItem>
                <FormItem label="备注" prop="remark">
          <textarea
              v-model="remarkFrom.remark"
              type="textarea"
              :rows="4"
              style="width: 350px; height: 100px; line-height: 20px"
              placeholder="请输入备注"
              maxlength="512"
          />
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="text" @click="cancelRemark">取消</Button>
                <Button type="primary" @click="saveRemark">确定</Button>
            </div>
        </Modal>
        <!-- 联系人新增a -->
        <Modal
            :title="contactTitle"
            v-model="contactVisible"
            :mask-closable="false"
            :width="450"
            :styles="{ top: '50px' }"
            :closable="false"
        >
            <Form
                ref="contactFrom"
                :model="contactFrom"
                :label-width="150"
                :rules="contactFromValidate"
            >
                <FormItem label="姓名" prop="custName">
                    <Input v-model="contactFrom.custName" autocomplete="off" />
                </FormItem>
                <FormItem label="与承租人关系" prop="custRelation" style="width: 300px">
                    <Select
                        style="width: 150px"
                        v-model="contactFrom.custRelation"
                        placeholder="请选择"
                    >
                        <Option
                            v-for="item in dataDic.custRelation"
                            :label="item.title"
                            :value="item.value"
                            :key="item.value"
                        >{{ item.title }}</Option
                        >
                    </Select>
                </FormItem>
                <FormItem label="证件类型" prop="certType">
                    <Select
                        style="width: 150px"
                        v-model="contactFrom.certType"
                        placeholder="请选择"
                    >
                        <Option
                            v-for="item in dataDic.certType"
                            :label="item.title"
                            :value="item.value"
                            :key="item.value"
                        >{{ item.title }}</Option
                        >
                    </Select>
                </FormItem>
                <FormItem label="证件号码" prop="certNo">
                    <Input v-model="contactFrom.certNo" autocomplete="off" />
                </FormItem>
                <FormItem label="手机号码" prop="telPhone">
                    <Input type="tel" v-model="contactFrom.telPhone" autocomplete="off" />
                </FormItem>
                <FormItem label="公司名称" prop="companyName">
                    <Input v-model="contactFrom.companyName" autocomplete="off" />
                </FormItem>
                <FormItem label="公司电话" prop="companyPhone">
                    <Input v-model="contactFrom.companyPhone" autocomplete="off" />
                </FormItem>
                <FormItem label="月收入" prop="monthlyIncome">
                    <Input
                        type="number"
                        v-model="contactFrom.monthlyIncome"
                        autocomplete="off"
                    />
                </FormItem>
                <FormItem label="现居住地" prop="detailAddress">
                    <Input v-model="contactFrom.detailAddress" autocomplete="off" />
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="text" @click="cancelContact">取消</Button>
                <Button type="primary" :loading="submitLoading" @click="submitContact"
                >提交</Button
                >
            </div>
        </Modal>
         <Modal v-model="msgModelVisible" title="短信模板" :mask-closable='false' :width="350" :closable="false">
          <Spin fix size="large" v-if="msgModelLoading">
            <slot name="loading"></slot>
          </Spin>
          <Form v-model="msgTemplate">
            <Form-item label="模板内容:" prop="templateContent">
                          <textarea v-model="msgTemplate.templateContent" type="textarea" :rows="4" style="width: 320px;height: 200px;line-height: 20px"
                                    disabled maxlength="512"/>
            </Form-item>
          </Form>
          <div slot="footer">
            <Button type="primary" @click="sendMsg">发送</Button>
            <Button type="primary" ghost @click="closeMsg" >关闭</Button>
          </div>
        </Modal>
        <Modal v-model="callRemarkVisible" :label-width="80" :closable="false" :mask-closable='false' :scrollable="false" :width="580" :styles="{top: '180px'}" :draggable="true">
          <div slot="header" style="text-align: center;font-size: 24px;font-weight: bolder;">修改备注</div>
          <Form ref="callRemarkFroms" :model="callRemarkFrom" inline :label-width="100">
            <FormItem label="致电情况" prop="callType" style="width: 600px;" >
              <Select style="width: 160px" v-model="callRemarkFrom.callType" placeholder="请选择" ref="callTypeSelect" >
                <Option v-for="item in dataDic.callType" :label="item.title" :value="item.value" :key="item.value">{{ item.title }}</Option>
              </Select>
            </FormItem>
            <FormItem label="备注" prop="remark">
                          <textarea v-model="callRemarkFrom.remark" type="textarea" :rows="4" style="width: 350px;height: 100px;line-height: 20px"
                                    placeholder="请输入备注" maxlength="512"/>
            </FormItem>
          </Form>
          <div slot="footer">
            <Button type="text" @click="cancelCallRemark">取消</Button>
            <Button type="primary" @click="saveCallRemark">确定</Button>
            </div>
        </Modal>
        <!-- 审批建议 -->
        <Modal
            title="审批意见"
            v-model="commentsModalVisible"
            :width="850"
            :mask-closable="false"
        >
            <Form :model="suggestData" :label-width="1">
                <div>
                    <i-select
                        :disabled="flowDict.backPoint.length == 0"
                        style="width: 160px"
                        v-model="flowDict.backPointId"
                        class="templateSelect"
                        v-if="isComments == 'back' && flowDict.backPoint.length"
                        clearable
                        placeholder="请选择退回节点"
                    >
                        <i-option
                            v-for="(item, index) in flowDict.backPoint"
                            :value="item.nodeId"
                            :key="index"
                        >{{ item.nodeName }}</i-option
                        >
                    </i-select>
                    <!-- <Input
                        v-if="
                            isComments == 'back' || isComments == 'back2dealer'
                        "
                        v-model="flowDict.backReasonStr"
                        readonly
                        icon="ios-search"
                        @on-focus="onModalFocus('back')"
                        placeholder="请选择退回原因"
                    ></Input> -->
                </div>
                <FormItem prop="approveRemark">
                    <Input
                        type="textarea"
                        v-model="suggestData.approveRemark"
                        :rows="6"
                        class="remarkTextarea"
                        style="width: 745px; font-weight: 900; font-size: 16px"
                        placeholder="请输入备注"
                        maxlength="1024"
                    />
                </FormItem>
            </Form>
            <div slot="footer">
                <Row style="margin-left: 10px">
                    <Button
                        class="tailButton normalButton"
                        size="small"
                        @click="commentsModalVisible = false"
                    >取消</Button
                    >
                    <Button
                        class="tailButton normalButton"
                        size="small"
                        @click="submitApprove"
                    >提交</Button
                    >
                </Row>
            </div>
        </Modal>
        <Modal
            v-model="isSubmit"
            :styles="{ top: '360px', width: '420px', right: '180px' }"
            title="确认"
            @on-ok="ok"
            @on-cancel="cancel"
            :mask-closable="false"
            :closable="false"
        >
            <h3 style="margin-left: 30px; color: #17233d">确认提交?</h3>
        </Modal>
        <!-- <check-box-modal
              title="退回原因"
              :data="flowDict.backReason"
              :visiable="backModalVisiable"
              @cancle="backModalVisiable = false"
              @onSubmit="onModalBackSubmit"
          ></check-box-modal> -->
        <loan-back-reason
            :loanBackReasonParam="loanBackReasonParam"
            :loanBaseData="loanBaseData"
            :loanBackReasonVisible="this.loanBackReasonVisible"
            @backBtn="backBtn"
            @closepop="closepop"
            @getFileList="getFileList"
        ></loan-back-reason>
        <change-record :apply-no="detailParams.applyNo"  v-model="isModel"/>
    </div>
</template>

<script>
import { mapGetters, mapMutations } from "vuex";
import Softphone from "_p/basic/pages/softphone/softphone";
//短信发送
import {
  getSmsTemplateByTemId,
  sendPurchaseBackVisitMsg,
  queryPurchaseBackVisitMsgList
} from "@/projects/afs-core-business/api/afs-case/message-template/messageTemplate";
import { getByTypes } from "_p/basic/api/admin/datadic";
import {getInnerCarInfo, queryAddressList} from "../../../../../api/afs-case/infomationDetail/applyCustomerDetail";
import Remind from "@/projects/afs-core-business/pages/case/approve/remind/remind";
import { queryRemindListByApplyNo } from "_p/afs-core-business/api/afs-case/approve/loanAnaylyze";
import {saveGpsPerform} from "@/projects/afs-core-business/api/afs-case/summary-info/summaryInfo";
import {
    queryExamineAssetSummaryInfo,
    queryPersionOrEnterpriseInfo,
    locationbute,
    invoiceCheck,
    invoiceHistory,
    carModelScreening,
    saveInsurance,
    queryGpsPerform,
    saveInvoiceInfo,
    checkInvoiceInfo,
    saveDecorationInvoiceInfo,
    caseCustContactList,
    updateLiaisonManById,
    saveSummaryContact,
    BankCardAuthentication,
    guaranteeCheck,
    queryGuarantee,
    getCertificateInfo,
    getResultByApplyNo,
} from "@/projects/afs-core-business/api/afs-case/summary-info/summaryInfo";
import {
    guanlianColumns,
    personPhone,
    car300Info,
    caseApproveRecords,
    invoiceVerification,
    caseinfoColums,
    outRemindColums,
    innerRemindColums,
    specialApplyColums,
    realTimeDataColumns
} from "@/projects/afs-core-business/api/afs-case/infomationDetail/infomationDetail-table-head";
import { deepClone } from "@/libs/utils/ObjectClone";
// 工作流 字典
import {
    getReasonDict,
    getFlowPonit
} from "@/projects/afs-core-business/api/afs-case/approve-todo/approve-todo";
// 打电话
import { setStatusIsOpen } from "@/projects/afs-core-business/api/afs-case/approve/approveTask";
// 拒绝原因 退回原因 checkbox
import CheckBoxModal from "../../task-detail/loan-analyze/checkbox.vue";
import LoanBackReason from "@/projects/basic/pages/image/file-audit/loanBackReason";
import utils from "@/libs/util.js";
import { loanSubmitApprove } from "../../../../../api/afs-case/approve/approveTask";
import { listHistoryContent } from "../../../../../api/afs-case/approve/loanAnaylyze";
import FinancialProd from "../../../infomationDetail/cost/financialProd";
import {getHistoryInfo, getRealTimeDataList,queryAssetSummaryInfo} from "_p/afs-core-business/api/afs-case/infomationDetail/assetDetail";
import AfsUtils from "@/libs/afs-utils";
import changeRecord from "_p/afs-core-business/pages/case/approve/record/changeRecord";
import electronic from "_p/afs-core-business/pages/case/approve/loan-preliminary-review/summary-info/electronic";
import financial from '../financial/financial.vue';
import {
  queryCallDetailList,
  saveCallRemarkHistory,
  saveRemarkHistory
} from "../../../../../api/afs-case/summary-info/summaryInfo";
export default {
    name: "asset-detail",
    //Softphone
    components: { CheckBoxModal, Remind, LoanBackReason,FinancialProd,changeRecord,electronic,financial},
    props: {
        applyNo: {
            required: false,
            default: () => {
                return "";
            }
        },
        isCheck: {
            type: Boolean
        },
        isAppear: {
            type: Boolean
        },
        tabName: {
            type: String
        },
        approveRemark: {
            type: String
        },
        loanBaseData: {
            type: Array
        },
        navList: {
            type: Array,
            default: () => []
        },
        loanBackReasonParam: {
            type: Object
        },
        refreshCallRecord: {
          type:String
        },
        templateId: {
          type:String
        }
    },
    watch: {
        tabName(val) {
            if (val == "name2" && !this.initialized) {
                this.initialized = true;
            }
            this.curTab = val;
        },
        refreshCallRecord: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.initCallRecord()
                }
            }
        }
    },
    data() {
        return {
            innerShow: false,
            innerCarInfo:{},
            carPurpose:"",
            orderType:"",
            tableList: [],
            productInfo:{},
            caseReceiptInfo:{},
            guaranteeInfo:{},
            certificateInfo:{
                ownName:"",
                ownCertNo:"",
                carVin:"",
                engineNo:"",
                actualMortgagorName:"",
                actualMortgagorCertNo:"",
                ownNameConsistentFlag:null,
                ownCertNoConsistentFlag:null,
                carVinConsistentFlag:null,
                engineNoConsistentFlag:null,
                actualMortgagorNameConsistentFlag:null,
                actualMortgagorCertNoConsistentFlag:null,
                certificateNumberFirstPage:null,
                certificateNumberRegistrationBar:null,
            },
            certificateInfoMsg:"",
            guaranteeCheckResult:"",
            autoLoanApproveResult:"",
            isChannelBelong:false,
            detailParams: this.afs.getPageParams(this),
            isComments: "",
            gpsSupplierTemp:"",
            invoiceInfoResult: "",
            invoiceInfoResultTemp: "",
            gpsPerform:"",
            gpsPerformList:[
                {
                    value: "0",
                    label: "否"
                },
                {
                    value: "1",
                    label: "是"
                }
            ],
            contactVisible: false,
            remarkVisible: false,
            isDecorationHide: false,
            submitLoading: false,
            commentsModalVisible: false,
            loanBackReasonVisible: false,
            isModel: false,
            isHide:true,
            basic: [
                {
                    value: "N",
                    label: "否"
                },
                {
                    value: "Y",
                    label: "是"
                }
            ],
            historyColums: [
                {
                    title: "姓名",
                    key: "custName",
                    width: 230,
                    align: "center",
                },
                {
                    title: "与承租人关系",
                    key: "custRole",
                    minWidth: 100,
                    align: "center",
                    slot: "custRole"

                },
                {
                    title: "统一社会编码",
                    key: "socUniCrtCodeNumber",
                    minWidth: 100,
                    align: "center",
                    slot: "socUniCrtCodeNumber"
                },
                {
                    title: "身份认证号码",
                    key: "idCardNoNumber",
                    minWidth: 160,
                    align: "center",
                    slot: "idCardNoNumber"
                },
                {
                    title: "单位名称",
                    key: "unitNameNumber",
                    minWidth: 100,
                    align: "center",
                    slot: "unitNameNumber"
                },
                {
                    title: "手机号码",
                    key: "phoneNoNumber",
                    minWidth: 100,
                    align: "center",
                    slot: "phoneNoNumber"
                },
                {
                    title: "居住地址",
                    key: "addressNumber",
                    minWidth: 100,
                    align: "center",
                    slot: "addressNumber"
                },
                {
                    title: "车架号",
                    key: "carVinNumber",
                    minWidth: 100,
                    align: "center",
                    slot: "carVinNumber"
                }
            ],
            suggestData: {},
            flowDict: {
                backReason: [], //退回原因
                backPoint: [], //退回节点
                backReasonId: [],
                backReasonStr: [],
                backPointId: ""
            },
            backModalVisiable: false,
            isSubmit: false,
            affiliateInfo: {},
            isShowAffiliateInfo: false,
            screeningResult: "",
            recordHisData: [],
            currentIndex:null,
            examineAssetPageInfoVo: {
                affixed: "",
                bussinessType:"",
                affiliatedWay: "",
                qualityGrade: "",
                saleAdvisor: "",
                saleAdvisorPhone: "",
                channelAddress: "",
                affiliateInfo: {},
                cooperPlat: "",
                isNominal: "",
                outlineVo: {},
                riskLoanAmt: "",
                marginAmount: "",
                handlingFeeAmount: "",
                corPorateName: "",
                corPorateTel: "",
                socUniCrtCode: "",
                carDealerName: "",
                carDealerAddr: "",
                contractAmt: "",
                downPayAmt: "",
                loanAmt: "",
                loanAmtRepeat: "",
                financeItemAmt: "",
                labelList: [],
                caseinfoList: [],
                caseCarInfo: {},
                carInvoiceInfo: {},
                caseCustContact: [],
                caseApproveRecord: [],
                carInsuranceInfo: {},
                carStrongInsuranceInfo: {},
                blacklist: [],
                carInfoVo: {},
            },
            caseinfoList: [],
            loanBankCardInfo: {},
            loanBankCardInfoDisable: false,
            caseApproveRecord: [],
            carInvoiceInfo: {},
            caseCarInfo: {},
            carInfoVo: {},
            carDecorationInvoiceInfo: {},
            carStrongInsuranceInfo: {},
            financeItem: {}, //融资项目信息
            bisnessAmtInput:false,
            insuranceStartTimeIndex:false,
            carInsuranceInfo: {},
            labelList: [],
            gpsDataList: [],
            gpsDataColumns: [
                {
                    title: "GPS厂商",
                    key: "gpsSupplier",
                    align: "center",
                },
                {
                    title: "GPS是否安装",
                    key: "gpsEquiStatus",
                    align: "center",

                },
                {
                    title: "GPS状态",
                    key: "gpsRunStatus",
                    align: "center",
                    slot: "gpsRunStatus"
                },
                {
                    title: "GPS地址",
                    key: "curLocationAdd",
                    align: "center",
                }
            ],
            outlineVo: {},
            gpsInfo: {},
            getAllEqPositionsReqDTO: {
                //GPS派单返回值
                isFlag: "", //有线设备号
                sbcstatus: "", //有线设备是否在线
                isNotFlag: "", //无线设备号
                isNotsbcStatus: "" //无线设备是否在线
            },
            isGPShide: true,
            isWiredHide:false,
            isWirelessHide:false,
            invoiceVerifications: [],
            realTimeDataBody: [],
            realTimeDataColumns: realTimeDataColumns,//发票查验 table
            guanlianBody: [],
            loading: true,
            initialized: false,
            guanlianColumns: guanlianColumns, //历史匹配详情 table
            //理论最大额度
            loanAmtMax: 0,
            applyNumber: "",
            platData: "",
            values: [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "25",
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
                "14",
                "14.1",
                "15",
                "16",
                '17.1',
                "17",
                "18",
                "19",
                "20",
                "21",
                "135",
            ],
            isAffixed: false,
            dataDic: {},
            pageInfo: {
                baseInfo: {},
                affiliateInfo: {},
                corPorateName: "",
                corPorateTel: "",
                socUniCrtCode: "",
                carDealerName: "",
                carDealerAddr: "",
                affiliatedWay: ""
            },
            certificateDiscern: {
                engineNo: ""
            }, //车辆信息
            carDetails: {},
            invoiceDiscern: {
                id: "",
                invoiceUnit: "",
                invoiceNo: "",
                invoiceCode: "",
                invoiceDate: "",
                checkCode: "",
                invoiceAmt: "", //开票金额
                invoiceTaxAmt: "", //增值税额
                excludingTaxAmt: "", //不含税价
                invoiceRate: "" //税率
            },
            carBaseInfos: [],
            caseCustContact: [],
            //isViewGps: true, //是否展示GPS模块
            car300Info: car300Info, //car300 table
            caseApproveRecords: caseApproveRecords, //审批历史记录 table
            callRemarkVisible: false,
            callRemarkFrom: {
              callId: "",
              callType: "",
              remark: ""
            },
            callReocrdLoading: false,
            callRecordSizeChanging: false,
            callRecord: {
              records:[],
              pageNumber:1,
              pageSize:10,
              total:0
            },
            msgTemplate:{
              templateContent: ''
            },
            msgModelVisible:false,
            msgModelLoading:false,
            personPhone: personPhone, //联系人 table
            personPhoneHistory: [
            {
              title: "操作",
              key: "action",
              width: 80,
              align: "center",
              slot: "action"
            },
            {
              title: "主叫号码",
              key: "callNo",
              sortable: true,
              align: "center",
              minWidth: 120
            },
            {
              title: "被叫号码",
              key: "telPhone",
              sortable: true,
              align: "center",
              minWidth: 130,
              slot: "telPhone"
            },
            {
              title: "被呼叫姓名",
              key: "custName",
              sortable: true,
              align: "center",
              minWidth: 120
            },
            {
              title: "振铃时间",
              key: "ringTime",
              sortable: true,
              align: "center",
              width: 150,
            },
            {
              title: "通话开始时间",
              key: "begin",
              sortable: true,
              align: "center",
              minWidth: 150,
            },
            {
              title: "通话结束时间",
              key: "end",
              sortable: true,
              align: "center",
              minWidth: 150,
            },
            {
              title: "致电情况",
              key: "callType",
              sortable: true,
              align: "center",
              minWidth: 100,
            },
            {
              title: "与申请人关系",
              key: "custRelation",
              sortable: true,
              align: "center",
              minWidth: 120
            },
            {
              title: "坐席",
              key: "remarkBy",
              sortable: true,
              align: "center",
              width: 130,
            },
            {
              title: "备注信息",
              key: "remark",
              sortable: true,
              align: "center",
              minWidth: 110
            },
            {
              title: "备注时间",
              key: "remarkTime",
              sortable: true,
              align: "center",
              width: 150
            },
            {
              title: "录音",
              key: "audio",
              sortable: true,
              align: "center",
              width: 330,
              render:(h,params)=>{
                return <audio src={ params.row.fileServerPrefix + '/' + params.row.mediano + '?file=' + params.row.recordFile} controls style="height: 30px; width:270px"/>
              }
            },
          ],
            smsRecordPageSizeOpts: [10, 20, 50, 100],
            smsRecord: {
              records:[],
              pageNumber:1,
              pageSize:10,
              total:0
            },
            smsRecordColumns: [
            {
              title: "发送手机号",
              key: "telPhone",
              align: "center",
              slot: "telPhone"
            },
            {
              title: "发送人姓名",
              key: "custName",
              align: "center",
            },
            {
              title: "发送内容",
              key: "content",
              align: "center",
            },
            {
              title: "状态",
              key: "sendStatus",
              align: "center",
              slot: "sendStatus"
            },
            {
              title: "发送时间",
              key: "sendTime",
              align: "center",
            },
            {
              title: "操作人员",
              key: "createBy",
              align: "center",
            },
          ],
            isDisabled: true, //设置页面禁用元素
            pageTwo: false,
            carInfo: {
                warningInfo: {
                    price: {
                        showflag: "",
                        title: "",
                        msgs: [],
                        flag: "",
                        describe: "",
                        accidenttyp: ""
                    }
                },
                backSign: "",
                reconsiderSign: "",
                caseFlag: false,
                levelType: "",
                ownSubTypeDesc: ""
            },
            profilerInfo: {},
            caseinfoColums: caseinfoColums,
            innerRemindColums: innerRemindColums,
            outRemindColums: outRemindColums,
            invoiceVerification: invoiceVerification,
            specialApplyColums:specialApplyColums,
            specialBusinessInfoList:[],
            remindListInside: [],
            remindListOut: [],
            isDealerCallBack: false,
            remindVisible: false,
            remindApproveRecord: {
                applyNo: "",
                stageId: "",
                approveRemark: "",
                useScene: "generalLoan"
            },
            dicKeys: [
                "carPurpose",
                "orderType",
                "certType",
                "carType",
                "qualityGrade",
                "mortgageStatus",
                "maritalStatus",
                "isType",
                "drivingType",
                "highestEducation",
                "unitNature",
                "industryType",
                "jobsType",
                "position",
                "custRelation",
                "sex",
                "addressType",
                "houseType",
                "nationality",
                "callType",
                "natureEnterprise",
                "isDefault",
                "specialApplyStatus",
                "specialForRejection",
                "gpsRunStatus",
                "gpsSupplier",
                "affiliatedWay",
                "signatoryType",
                "authorizerDuty"
            ],
            contactFromValidate: {
                custName: [
                    { required: true, message: "请输入姓名", trigger: "blur" }
                ],
                // 业务类型校验
                custRelation: [
                    {
                        required: true,
                        message: "请选择与承租人关系",
                        trigger: "change"
                    }
                ],
                certType: [
                    {
                        required: false,
                        message: "请选择证件类型",
                        trigger: "change"
                    }
                ],
                certNo: [
                    {
                        required: false,
                        message: "请输入证件号码",
                        trigger: "blur"
                    }
                ],
                telPhone: [
                    {
                        required: true,
                        message: "请输入手机号码",
                        trigger: "blur"
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: "请输入正确的手机号码",
                        trigger: "blur"
                    }
                ],
                companyName: [
                    {
                        required: false,
                        message: "请输入公司名称",
                        trigger: "blur"
                    }
                ],
                companyPhone: [
                    {
                        required: false,
                        message: "请输入公司电话",
                        trigger: "blur"
                    }
                ],
                detailAddress: [
                    {
                        required: false,
                        message: "请输入详细地址",
                        trigger: "blur"
                    }
                ]
            },
            mainBasePersonal:false,//个人信息
            mainBaseEnterprise:false,//企业信息
            personalChangeEnterprise: false, //个人转企业
            mainBaseInfo: {
                province: "",
                city: "",
                detailAddress: "",
                detailAddressTemp: []
            },
            historyInfoList: [],
            tableHeight: 312,
            remarks:"",
            loanRemarks:"",
            detailTitle: "详情",
            hisDetailVisible: false,
            historyDetail: [],
            contactTitle: "",
            messageTitle: "添加备注",
            carTypeTemp:"",
            remarkFrom: {
                id: "",
                custRelation: "",
                custId: "",
                custName: "",
                telPhone: "",
                callType: "",
                remark: "",
                applyNo: "",
                certNo: "",
                certType: "",
                detailAddress: "",
                validStatus: "",
                companyName: ""
            },
            contactFrom: {
                applyNo: "",
                custId: "",
                id: "",
                custName: "",
                custRelation: "",
                certType: "",
                certNo: "",
                telPhone: "",
                companyName: "",
                companyPhone: "",
                monthlyIncome: "",
                detailAddress: ""
            },

        };
    },

    computed: {
        ...mapGetters({userDetail: "userDetail", phoneCallable: "phoneCallable"}),
        isCompanyApply() {
            return this.inputType === "1";
        }
    },
    methods: {
        showReportInfo(){
            window.open(this.caseCarInfo.reportUrl);
        },
        closeThis() {
            this.whenClose();
            window.close();
        },
        whenClose() {
            this.afs.afsEmit(this, "flushData");
        },
        // 审批弹窗
        comments(val) {
            this.isComments = val;
            if ("back2dealer" == val) {
                this.detailParams.belongNo = this.detailParams.applyNo;
                this.detailParams.busiNo = this.detailParams.contractNo;
                this.loanBackReasonVisible = true;
            } else {
                this.commentsModalVisible = true;
            }
        },
        onModalFocus(str) {
            if (str == "back") {
                this.backModalVisiable = true;
            }
        },
        getTableList(data) {
            this.tableList = data;
            console.log('tableList', this.tableList)
        },
        getProductInfo(data){
            this.productInfo= data;
        },
        strongDate() {
            // TODO yusijun 处理保险信息的结束日期不能早于开始日期，默认间隔一年，可修改的需求问题
            /*if (this.insuranceInfoBusiness.insuranceStartTime != "") {
    this.insuranceInfoBusiness.insuranceEndTime = this.getTime(this.insuranceInfoBusiness.insuranceStartTime);
}*/
            var today = new Date();
            var strongTime = new Date(
                this.carStrongInsuranceInfo.insuranceStartTime
            );
            // var strongDayTime = (today - strongTime) / (1000 * 60 * 60 * 24);
            // if (strongDayTime < 30&&strongDayTime>1) {
            //     this.$Message.error("交强险保单超期，请提供特殊投保承诺函");
            //     this.insuranceStartTimeIndex=true;
            // }else{
            //     this.insuranceStartTimeIndex=false;
            // }
            // if (strongDayTime < -2 && strongDayTime > -15) {
            //     this.$Message.error("交强险保单超期，请提供批单修改生效日期");
            // }
        },
        onModalBackSubmit(data) {
            console.log(data);
            this.backModalVisiable = false;
            this.flowDict.backReasonId = data.codeArr;
            this.flowDict.backReasonStr = data.nameArr;
        },
        initRecordHistory() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            listHistoryContent(params).then(res => {
                if (res.code == "0000") {
                    this.recordHisData = res.data.taskHistories || [];
                    this.recordHisData.forEach(record => {
                        let color;
                        //record.title
                        if (record.approveSuggest == "back2dealer" || record.approveSuggest == "back") {
                            color = "#FAEBD7";
                        } else {
                            color = "#F0FFFF";
                        }
                        /*if (record.approveSuggest == this.suggestConst.back.val || record.approveSuggest == this.suggestConst.back2dealer.val) {
    color = "#79E0FF";
} else if (record.approveSuggest == this.suggestConst.refuse.val) {
    color = "#EE8D8F";
} else if (record.approveSuggest == this.suggestConst.submit.val) {
    color = "#86F3AF";
}*/
                        record.style = {
                            backgroundColor: color
                        };
                        // 处理default
                        if(record.approveRemark && record.approveRemark.indexOf('[default-default]')>-1){
                            let arr =record.approveRemark.split('[default-default]');
                            record.approveRemark=arr[0]+''+arr[1];
                        }
                    });
                }
            });
        },
        businessDate() {
            // TODO yusijun 处理保险信息的结束日期不能早于开始日期，默认间隔一年，可修改的需求问题
            /*if (this.insuranceInfoBusiness.insuranceStartTime != "") {
    this.insuranceInfoBusiness.insuranceEndTime = this.getTime(this.insuranceInfoBusiness.insuranceStartTime);
}*/
            var today = new Date();
            var businessTime = new Date(
                this.carInsuranceInfo.insuranceStartTime
            );
            var businessDayTime =
                (today - businessTime) / (1000 * 60 * 60 * 24);
            if (businessDayTime < 30&&businessDayTime>1) {
                this.$Message.error("商业险保单超期，请提供特殊投保承诺函");
                this.bisnessAmtInput=true;
            }else{
                this.bisnessAmtInput=false;
            }
            if (businessDayTime < -2 && businessDayTime > -15) {
                this.$Message.error("商业险保单超期，请提供批单修改生效日期");
            }
        },
        // 原因 节点 字典
        queryDictData() {
            console.log("this.detailParams", this.detailParams);
            if(this.detailParams.templateId){
                let params = {
                    packageId: this.detailParams.packageId,
                    templateId: this.detailParams.templateId,
                    userDefinedIndex: this.detailParams.userDefinedIndex
                };
                getReasonDict(params).then(res => {
                    if (res.code === "0000" && res.data) {
                        res.data.BACK_REASON &&
                        res.data.BACK_REASON.map(item => {
                            item.checked = false;
                            item.indeterminate = false;
                            item.checkList = [];
                            item.childList
                                ? item.childList.map(j => {
                                    j.checked = false;
                                })
                                : "";
                        });
                        this.flowDict.backReason = res.data.BACK_REASON;
                    }
                });
            getFlowPonit({ taskId: this.detailParams.taskId }).then(res => {
                if (res.code === "0000" && res.data) {
                    let nodeId = "";
                    res.data.map(item => {
                        if (item.nodeId.indexOf("EndNode") !== -1) {
                            nodeId = item.nodeId;
                        }
                    });
                    this.flowDict.nodeId = nodeId;
                    this.flowDict.backPoint = res.data.filter(
                        item => item.operationType == "BACK"
                    );
                }
            });
            }
        },
        submitApprove() {
            this.suggestData.applyNo = this.detailParams.applyNo;
            this.suggestData.stageId = this.detailParams.stageId;
            this.suggestData.taskId = this.detailParams.taskId;
            // 退回
            if (this.isComments == "back") {
                // if (!this.flowDict.backPoint.length) {
                //     this.$Message.warning("该节点不支持退回");
                //     return;
                // }
                if (!this.flowDict.backPointId) {
                    this.$Message.warning("请选择退回节点");
                    return;
                }
                // if (!this.flowDict.backReasonId.length) {
                //     this.$Message.warning("请选择退回原因");
                //     return;
                // }
                this.suggestData.flowNode = this.flowDict.backPointId;
            }
            // 退回 经销商
            if (this.isComments == "back2dealer") {
                if (!this.flowDict.backReasonId.length) {
                    this.$Message.warning("请选择退回原因");
                    return;
                }
                this.suggestData.flowNode = this.flowDict.backPointId
                    ? this.flowDict.backPointId
                    : null;
            }
            this.isSubmit = true;
        },
        ok() {
            this.suggestData.approveRemark = this.suggestData.approveRemark
                ? this.suggestData.approveRemark
                : "";
            let params = {
                contractNo: this.outlineVo.contractNo,
                approveRecord: this.suggestData,
                operationType: this.isComments,
                approveSuggestList: this.flowDict.backReasonId || [],
                approveSuggestNameList: this.flowDict.backReasonStr || []
            };
            // this.submitApproveLoading = true;
            loanSubmitApprove(params)
                .then(res => {
                    if (res.code == "0000") {
                        this.$Message.info("提交成功!");
                        this.$emit("whenClose");
                        window.close();
                    } else if (res.code === "0001") {
                        this.$Message.warning(res.msg);
                    }
                })
                .finally(() => {
                    // this.submitApproveLoading = false;
                });
        },
        cancel() {
            this.isSubmit = false;
        },
        updateTotalLoanAmt(loanAmtMax) {
            this.loanAmtMax = loanAmtMax;
            this.$refs.financialProd.getTheoryloanAmt(loanAmtMax);
        },
        /* 拨打电话
     *   注意：  mapMutations 需要从vuex引入 照着写就行。 //import { mapMutations } from "vuex";
     *  要变的是 传给软电话的数据
     */
        ...mapMutations(["updatePhoneParams"]),
        // 打电话 调这个方法
        callPhone(phone,custRelation,custId,custName,applyNo,custType,needDict) {
          if (needDict) {
            custRelation = this.setDictTitleByType("custRelation",custRelation)
          }
          let parm = {
            phone:phone,
            custRelation:custRelation,
            custId:custId,
            custName:custName,
            applyNo:applyNo,
            custType:custType
          }
          this.$emit('callphone',parm);
            //更新打开
            this.updateStatusIsOpen();
            // 传给软电话的数据
            let params = {
                isCallOut: true, //是否是一键拨打电话 必填
                callNumber: phone, // 拨打的电话号码 必填
                phone: phone,
                applyNo: this.applyNumber,
                custRelation: custRelation,
                custId: custId,
                custName: custName,
                eventGroupId:this.eventGroupId,
                custType: custType
                // ... // 其他 需要传给后台的参数
            };
            this.updatePhoneParams(params);
        },
        // 拨打记录
        initCallRecord() {
          let params = {
            pageNumber: this.callRecord.pageNumber,
            pageSize: this.callRecord.pageSize,
            condition: {
              applyNo: this.applyNumber,
              businessType: this.userDetail.templateId || this.templateId,
              // 20231129 先不查 todo: fixit
              // sourceType: this.userDefinedIndex
            }
          }
          queryCallDetailList(params).then(res => {
            if (res.code === "0000") {
              this.callRecord = res.data;
            }
          })
          this.callReocrdLoading = false;
          this.callRecordSizeChanging = false;
        },
        changePage(v) {
          this.callRecord.pageNumber = v;
          if(this.callRecordSizeChanging){
            return;
          }
          this.initCallRecord();
        },
        changePageSize(v) {
          this.callReocrdLoading = true;
          this.callRecord.pageSize = v;
          this.callRecord.pageNumber = 1;
          this.callRecordSizeChanging = true;
          this.initCallRecord();
        },
        cancelCallRemark(){
          this.callRemarkVisible = false;
          this.$refs.callRemarkFroms.resetFields();
          this.initCallRecord()
        },
        saveCallRemark() {
          if(this.callRemarkFrom.callType === "请选择") {
            this.callRemarkFrom.callType = ''
          }
          saveCallRemarkHistory(this.callRemarkFrom).then(res => {
            if (res.code === "0000") {
              this.initCallRecord();
              this.callRemarkVisible = false;
              this.$refs.callRemarkFroms.resetFields();
              if (res.msg === "success") {
                this.$Message['success']({content: '编辑成功'})
              } else {
                this.$Message['error']({content: '编辑失败'})
              }
            }
          })
        },
        editCallRecordRemark(v) {
          this.callRemarkVisible = true;
          this.callRemarkFrom.callId = v.callId
        },
        initSMSRecord() {
          let params = {
            pageNumber: this.smsRecord.pageNumber,
            pageSize: this.smsRecord.pageSize,
            condition: {
              applyNo: this.detailParams.applyNo,
              templateId: 'PURCHASE_BACk_VISIT',
            }
          }
          queryPurchaseBackVisitMsgList(params).then(res => {
            if (res.code === "0000") {
              this.smsRecord = res.data;
            }
          })
        },
        getMsgTemp(name,phone,tempType){
          this.msgModelVisible = true;
          this.msgModelLoading = true;
          this.msg = {
            templateId: tempType,
            applyNo: this.applyNo,
            phone,
            name
          }
          if (tempType) {
            getSmsTemplateByTemId(tempType).then(res=>{
              this.msgTemplate = res.data;
              if (res.data.templateContent) {
                this.msgTemplate.templateContent = res.data.templateContent;
              }
            })
          }
          this.$nextTick(() => {
            this.msgModelLoading = false;
          })
        },
        //发送短信
        sendMsg() {
          this.$Modal.confirm({
            title: ("发送短信"),
            content: "确认发送?",
            onOk: () => {
              this.msgModelLoading = true;
              sendPurchaseBackVisitMsg(this.msg).then(res=>{
                this.$Message.info("操作成功！");
              }).finally(()=>{
                this.msgModelLoading = false;
                this.closeMsg();
              })
            }
          });
        },
        closeMsg(){
          this.msg = {
            templateId: '',
            applyNo: '',
            phone: ''
          }
          this.initSMSRecord();
          this.msgModelVisible = false;
        },
        changeSMSRecordPage(v) {
          this.smsRecord.pageNumber = v;
          if(this.callRecordSizeChanging){
            return;
          }
          this.initSMSRecord();
          // console.log('page change')
        },
        changeSMSRecordPageSize(v) {
          this.smsRecord.pageSize = v;
          this.smsRecord.pageNumber = 1;
          this.initSMSRecord();
        },
        convertSMSSendStatus(status) {
          switch (status) {
            case 'wait':
              return '待发送'
            case 'cancel':
              return '取消发送'
            case 'success':
              return '发送成功'
            case 'fail':
              return '发送失败'
            default:
              return ''
          }
        },

        isDeal() {
            this.$refs.financialProd.isDeal();
        },
        BankCardAuthentication(params){
            BankCardAuthentication(params).then(res => {
                 if(res.code=='0000'){
                    this.loanBankCardInfo = res.data.loanBankCardInfo || {};
                    this.queryLiaisonMan();
                }

            });
        },
        checkInvoiceInfo() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            checkInvoiceInfo(params).then(res => {
                if (res.code === "0000") {
                    this.invoiceInfoResult = res.data[0];
                    this.invoiceInfoResultTemp = res.data[1];
                }
            });
        },
        //查询gps履约信息
        queryGpsPerform() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            queryGpsPerform(params).then(res => {
                if (res.code === "0000") {
                    this.gpsSupplierTemp = res.data.gpsSupplier;
                    if (!res.data.gpsPerform) {
                        if(res.data.gpsSupplier === "0"){
                            this.gpsPerform = "0";
                        }else{
                            this.gpsPerform = "1";
                        }
                    } else {
                        this.gpsPerform = res.data.gpsPerform;
                    }
                }
            });
        },
        queryGuarantee(){
            const opt={
                applyNo:this.detailParams.applyNo
            }
            queryGuarantee(opt).then(res => {
                if (res.code === "0000"  && res.data ) {
                    this.guaranteeInfo=res.data;
                }
            });
        },

        getCertificateInfo(){
            const opt={
                applyNo:this.detailParams.applyNo
            }
            getCertificateInfo(opt).then(res => {
                if (res.code === "0000"  && res.data ) {
                    this.certificateInfo=res.data;
                    let certificateIssues = '';
                    if(this.certificateInfo.ownNameConsistentFlag=="0"){
                        certificateIssues += "承租人（挂靠订单与挂靠公司名称校验）不一致\n"
                    }
                    if(this.certificateInfo.ownNameConsistentFlag==null){
                        certificateIssues += "承租人（挂靠订单与挂靠公司名称校验）识别异常\n"
                    }
                    if(this.certificateInfo.ownCertNoConsistentFlag=="0"){
                        certificateIssues+= "承租人身份证号码（挂靠订单与挂靠公司统一信用代码校验）不一致\n"
                    }
                    if(this.certificateInfo.ownCertNoConsistentFlag==null){
                        certificateIssues += "承租人身份证号码（挂靠订单与挂靠公司统一信用代码校验）识别异常\n"
                    }
                    if(this.certificateInfo.carVinConsistentFlag=="0"){
                        certificateIssues += "车架号/车辆识别代码不一致\n"
                    }
                    if(this.certificateInfo.carVinConsistentFlag==null){
                        certificateIssues += "车架号/车辆识别代码识别异常\n"
                    }
                    if(this.certificateInfo.engineNoConsistentFlag=="0"){
                        certificateIssues+= "发动机号码不一致\n"
                    }
                    if(this.certificateInfo.engineNoConsistentFlag==null){
                        certificateIssues += "发动机号码识别异常\n"
                    }
                    if(this.certificateInfo.actualMortgagorNameConsistentFlag=="0"){
                        certificateIssues += "抵押权人不一致\n"
                    }
                    if(this.certificateInfo.actualMortgagorNameConsistentFlag==null){
                        certificateIssues += "抵押权人识别异常\n"
                    }
                    if(this.certificateInfo.actualMortgagorCertNoConsistentFlag=="0"){
                        certificateIssues += "抵押权人身份证明号码不一致\n"
                    }
                    if(this.certificateInfo.actualMortgagorCertNoConsistentFlag==null){
                        certificateIssues += "抵押权人身份证明号码识别异常\n"
                    }
                    if(this.certificateInfo.certificateNumberFirstPage != this.certificateInfo.certificateNumberRegistrationBar){
                        certificateIssues += "机动车登记证书编号不一致\n"
                    }
                    if(this.certificateInfo.certificateNumberFirstPage == null){
                        certificateIssues += "机动车登记证书编号(信息栏)识别异常\n"
                    }if(this.certificateInfo.certificateNumberRegistrationBar == null) {
                        certificateIssues += " 机动车登记证书编号(登记栏)识别异常\n"
                    }
                    if(certificateIssues == ''){
                        certificateIssues = "一致！"
                    }
                    this.certificateInfoMsg = certificateIssues;
                }
            });
        },
        guaranteeCheck(){
            const opt={
                applyNo:this.detailParams.applyNo
            }
            guaranteeCheck(opt).then(res => {
                if (res.code === "0000" && res.data ) {

                    this.guaranteeCheckResult = res.data;
                }
            });
        },

        // 获取放款自动审核结果
        getResultByApplyNo(){
            let param = {
                applyNo: this.detailParams.applyNo,
            }
            getResultByApplyNo(param).then(res => {
                if (res.code === "0000") {
                    this.autoLoanApproveResult=res.data;
                }
            });
        },

        initInfo() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            var that =this;
            queryExamineAssetSummaryInfo(params).then(res => {
                console.log("initInfo=>res", res);
                // debugger
                if (res.code === "0000") {
                    this.carTypeTemp = res.data.carType;
                    this.orderType=res.data.orderType;
                    this.carPurpose=res.data.carInfoVo.carPurpose;
                    if(res.data.outlineVo&&res.data.outlineVo.remarkList){
                        this.remarks='';
                        if(res.data.outlineVo.remarkList.length>0){
                            for(var i=0;i<res.data.outlineVo.remarkList.length;i++){
                                this.remarks+=res.data.outlineVo.remarkList[i]+"\n";
                            }
                        }
                    }

                    if(res.data.outlineVo&&res.data.outlineVo.specialBusinessInfoList){
                        this.specialBusinessInfoList=res.data.outlineVo.specialBusinessInfoList
                    }
                    if(res.data.outlineVo&&res.data.outlineVo.remarks){
                        this.remarks+=res.data.outlineVo.remarks
                    }
                    if(res.data.outlineVo&&res.data.outlineVo.loanRemarkList){
                        this.loanRemarks='';
                        if(res.data.outlineVo.loanRemarkList.length>0){
                            for(var j=0;j<res.data.outlineVo.loanRemarkList.length;j++){
                                this.loanRemarks+=res.data.outlineVo.loanRemarkList[j]+'\n';
                            }
                        }
                    }
                    // if(res.data.outlineVo&&res.data.outlineVo.loanRemarks){
                    //     this.loanRemarks+=res.data.outlineVo.loanRemarks
                    // }
                    //没有GPS信息隐藏
                    if (res.data.carGpsApply && res.data.carGpsApply.gpsType) {
                        if (res.data.carGpsApply.gpsType == "noInstall") {
                            this.isGPShide = false;
                            console.log("是否显示导航.....",this.isGPShide);
                            // 显示GPS信息导航
                            // this.updateNavList([{id:'asset_16',hide: false}])

                        } else {
                            this.isGPShide = true;
                            if(res.data.carGpsApply.gpsType == "wireless"|| res.data.carGpsApply.gpsType == "wirelessFree"){
                                this.isWirelessHide=true;
                            }
                            //有线或者有线免费都显示
                            if(res.data.carGpsApply.gpsType == "wired" || res.data.carGpsApply.gpsType == "wiredFree"){
                                this.isWiredHide=true;
                            }
                            //无线或者无线免费都显示
                            if(res.data.carGpsApply.gpsType == "wiredAndWireless" || res.data.carGpsApply.gpsType == "wiredAndWirelessFree"){
                                this.isWiredHide=true;
                                this.isWirelessHide=true;
                            }
                            this.locationbute();
                        }
                    }else{
                        this.isGPShide = false;
                    }
                    // var today = new Date();
                    // var strongTime = new Date(res.data.carStrongInsuranceInfo.insuranceStartTime);
                    // var strongDayTime = (today - strongTime) / (1000 * 60 * 60 * 24);
                    // if (strongDayTime < 30&&strongDayTime>1) {
                    //     this.$Message.error("交强险保单超期，请提供特殊投保承诺函");
                    //     this.insuranceStartTimeIndex=true;
                    // }else{
                    //     this.insuranceStartTimeIndex=false;
                    // }
                    // var businessTime = new Date(res.data.carInsuranceInfo.insuranceStartTime);
                    // var businessDayTime =(today - businessTime) / (1000 * 60 * 60 * 24);
                    // if (businessDayTime < 30&&businessDayTime>1) {
                    //     this.$Message.error("商业险保单超期，请提供特殊投保承诺函");
                    //     this.bisnessAmtInput=true;
                    // }else{
                    //     this.bisnessAmtInput=false;
                    // }
                    this.caseCustContact = res.data.caseCustContact || [];
                    this.caseCarInfo =res.data.caseCarInfo || {};
                    this.carInfoVo =res.data.carInfoVo || {};
                    this.examineAssetPageInfoVo = res.data;
                    console.log("hongfuqing---examineAssetPageInfoVo",this.examineAssetPageInfoVo);

                    this.caseReceiptInfo=res.data.caseReceiptInfo || {}
                    if(this.caseReceiptInfo.sendJsonAddress){
                        this.caseReceiptInfo.sendAddressBak=JSON.parse(this.caseReceiptInfo.sendJsonAddress)
                    }
                    if(this.caseReceiptInfo.businessJsonAddress){
                        this.caseReceiptInfo.businessAddressBak=JSON.parse(this.caseReceiptInfo.businessJsonAddress)
                    }

                    if(res.data.blacklist){
                        that.examineAssetPageInfoVo.blacklist = res.data.blacklist||'';
                        // const result = res.data.blacklist;
                        //黑名单数据
                        that.examineAssetPageInfoVo.blacklist.forEach((list,index,array)=>{
                            console.log('item------',list);
                            if(list.blackGrayListIdentification){
                                if(list.blackGrayListIdentification === "01"){
                                    if(list.showBalckGrayStatus){
                                        if(list.showBalckGrayStatus==="01"){
                                            this.currentIndex=0;
                                        }
                                    }
                                }
                            }
                        });
                    }
                    if (res.data.loanAmtRepeat) {
                        this.examineAssetPageInfoVo.loanAmtRepeat = AfsUtils.commafy(res.data.loanAmtRepeat, { digits: 2 });
                    }
                    if (res.data && res.data.contractAmt) {
                        this.examineAssetPageInfoVo.contractAmt = AfsUtils.commafy(
                            res.data.contractAmt,
                            { digits: 2 }
                        );
                    }
                    if (res.data && res.data.financeItemAmt) {
                        this.examineAssetPageInfoVo.financeItemAmt = AfsUtils.commafy(
                            res.data.financeItemAmt,
                            { digits: 2 }
                        );
                    }
                    if (res.data && res.data.downPayAmt) {
                        this.examineAssetPageInfoVo.downPayAmt = AfsUtils.commafy(
                            res.data.downPayAmt,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.caseCarInfo &&
                        res.data.caseCarInfo.salePrice
                    ) {
                        this.examineAssetPageInfoVo.caseCarInfo.salePrice = AfsUtils.commafy(
                            res.data.caseCarInfo.salePrice,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.caseCarInfo &&
                        res.data.caseCarInfo.guidePrice
                    ) {
                        this.examineAssetPageInfoVo.caseCarInfo.guidePrice = AfsUtils.commafy(
                            res.data.caseCarInfo.guidePrice,
                            { digits: 2 }
                        );
                    }
                    if (res.data.carDecorationInvoiceInfo) {
                        this.carDecorationInvoiceInfo =
                            res.data.carDecorationInvoiceInfo;
                        if (
                            res.data.carDecorationInvoiceInfo &&
                            res.data.carDecorationInvoiceInfo.invoiceAmt
                        ) {
                            this.carDecorationInvoiceInfo.invoiceAmt = AfsUtils.commafy(
                                res.data.carDecorationInvoiceInfo.invoiceAmt,
                                { digits: 2 }
                            );
                        }
                        if (
                            res.data.carDecorationInvoiceInfo &&
                            res.data.carDecorationInvoiceInfo.invoiceTaxAmt
                        ) {
                            this.carDecorationInvoiceInfo.invoiceTaxAmt = AfsUtils.commafy(
                                res.data.carDecorationInvoiceInfo.invoiceTaxAmt,
                                { digits: 2 }
                            );
                        }
                        this.isDecorationHide = true;
                        //根本用不到
                    }
                    this.carInvoiceInfo = res.data.carInvoiceInfo || {};
                    if (
                        res.data.carInvoiceInfo &&
                        res.data.carInvoiceInfo.invoiceAmt
                    ) {
                        this.carInvoiceInfo.invoiceAmt = AfsUtils.commafy(
                            res.data.carInvoiceInfo.invoiceAmt,
                            { digits: 2 }
                        );
                    }
                    if (res.data.carInvoiceInfo && res.data.carInvoiceInfo.checkResult) {
                        if ("1" === res.data.carInvoiceInfo.checkResult) {
                            this.carInvoiceInfo.checkResult = "已查验";
                        } else {
                            this.carInvoiceInfo.checkResult = "未查验";
                        }
                    }
                    if (
                        res.data.carInvoiceInfo &&
                        res.data.carInvoiceInfo.invoiceRate
                    ) {
                        this.carInvoiceInfo.invoiceRate = AfsUtils.commafy(
                            res.data.carInvoiceInfo.invoiceRate,
                            { digits: 0 }
                        );
                    }
                    if (
                        res.data.carInvoiceInfo &&
                        res.data.carInvoiceInfo.invoiceTaxAmt
                    ) {
                        this.carInvoiceInfo.invoiceTaxAmt = AfsUtils.commafy(
                            res.data.carInvoiceInfo.invoiceTaxAmt,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.carInvoiceInfo &&
                        res.data.carInvoiceInfo.excludingTaxAmt
                    ) {
                        this.carInvoiceInfo.excludingTaxAmt = AfsUtils.commafy(
                            res.data.carInvoiceInfo.excludingTaxAmt,
                            { digits: 2 }
                        );
                    }
                    this.carStrongInsuranceInfo =
                        res.data.carStrongInsuranceInfo || {};
                    if (
                        res.data.carStrongInsuranceInfo &&
                        res.data.carStrongInsuranceInfo.insuranceAmt
                    ) {
                        this.carStrongInsuranceInfo.insuranceAmt = AfsUtils.commafy(
                            res.data.carStrongInsuranceInfo.insuranceAmt,
                            { digits: 2 }
                        );
                    }
                    this.carInsuranceInfo = res.data.carInsuranceInfo || {};
                    if (
                        res.data.carInsuranceInfo &&
                        res.data.carInsuranceInfo.insuranceAmt
                    ) {
                        this.carInsuranceInfo.insuranceAmt = AfsUtils.commafy(
                            res.data.carInsuranceInfo.insuranceAmt,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.carInsuranceInfo &&
                        res.data.carInsuranceInfo.thirdInsurance
                    ) {
                        this.carInsuranceInfo.thirdInsurance = AfsUtils.commafy(
                            res.data.carInsuranceInfo.thirdInsurance,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.carInsuranceInfo &&
                        res.data.carInsuranceInfo.insuranceMoney
                    ) {
                        this.carInsuranceInfo.insuranceMoney = AfsUtils.commafy(
                            res.data.carInsuranceInfo.insuranceMoney,
                            { digits: 2 }
                        );
                    }
                    this.$set(
                        this.carInsuranceInfo,
                        "insuranceMoney",
                        AfsUtils.commafy(
                            res.data.carInsuranceInfo.insuranceMoney,
                            { digits: 2 }
                        )
                    );
                    if (
                        res.data.financeItem &&
                        res.data.financeItem.bisnessAmt
                    ) {
                        this.financeItem.bisnessAmt = AfsUtils.commafy(
                            res.data.financeItem.bisnessAmt,
                            { digits: 2 }
                        );
                    }
                    if (
                        res.data.financeItem &&
                        res.data.financeItem.compulsoryAmt
                    ) {
                        this.financeItem.compulsoryAmt = AfsUtils.commafy(
                            res.data.financeItem.compulsoryAmt,
                            { digits: 2 }
                        );
                    }
                    this.$set(
                        this.financeItem,
                        "isHaveBisness",
                        res.data.isHaveBusiness
                    );
                    this.$set(
                        this.financeItem,
                        "bisnessAmt",
                        AfsUtils.commafy(res.data.businessAmt, { digits: 2 })
                    );
                    this.$set(
                        this.financeItem,
                        "isHaveCompulsory",
                        res.data.isHaveCompulsory
                    );
                    this.$set(
                        this.financeItem,
                        "compulsoryAmt",
                        AfsUtils.commafy(res.data.compulsoryAmt, { digits: 2 })
                    );
                    this.$set(
                        this.financeItem,
                        "channelUnitName",
                        res.data.channelUnitName
                    );
                    this.caseApproveRecord = res.data.caseApproveRecord || {};
                    if (res.data.custLevel) {
                        this.$set(
                            this.mainBaseInfo,
                            "custLevel",
                            res.data.custLevel
                        );
                    }
                    res.data.outlineVo.ifDealerGuarantee =
                        res.data.outlineVo.ifDealerGuarantee == "0"
                            ? "否"
                            : "是";
                    //debugger
                    if(res.data.outlineVo.signType){
                        if(res.data.outlineVo.signType == "online"){
                            res.data.outlineVo.signType="电子签约"
                        }
                        if(res.data.outlineVo.signType == "offline"){
                            res.data.outlineVo.signType="纸质签约"
                        }
                    }
                    //新增代码逻辑

                    this.outlineVo = res.data.outlineVo || {};
                    console.log(res.data)
                    this.channelBelongFun(this.outlineVo.channelBelong);
                    this.$set(
                        this.outlineVo,
                        "loanAmt",
                        AfsUtils.commafy(res.data.outlineVo.loanAmt, {
                            digits: 2
                        })
                    );
                    this.labelList = res.data.labelList || [];
                    this.caseinfoList = res.data.caseinfoList || [];
                    this.carBaseInfos = res.data.carModelScreenList;
                    this.screeningResult = res.msg;
                    this.BankCardAuthentication(params);
                    console.log(res.data.caseCustContact);
                    this.invoiceHistory();
                 //   this.checkVehicleMadeDate();
                    //是否符合低开标准
                    //乘用车
                    if (res.data.carType == "2") {
                        if (
                            res.data.caseCarInfo.salePrice &&
                            res.data.carInvoiceInfo.invoiceTaxAmt
                        ) {
                            let invoicePrice =
                                res.data.carInvoiceInfo.invoiceTaxAmt;
                            let salePrice = res.data.caseCarInfo.salePrice;
                            if (invoicePrice != "NaN" && salePrice != "NaN") {
                                //发票价低于销售价大于等于3000元且未针对此项已经特批
                                if (salePrice - invoicePrice >= 3000) {
                                    this.carInvoiceInfo.isLowOpen = "符合";
                                } else {
                                    this.carInvoiceInfo.isLowOpen = "不符合";
                                }
                            }
                        }
                    } else {
                        this.carInvoiceInfo.isLowOpen = "符合";
                    }
                } else {
                    this.$Message.fail("数据加载失败！");
                }
            });
            this.queryRemind();
            this.initMainBaseInfo();
            // 获取拨打记录
            this.initCallRecord();
            // 获取短信记录
            this.initSMSRecord();
            queryAssetSummaryInfo(params).then(res => {
                if (res.code === "0000") {
                    this.pageInfo = res.data;
                    this.affiliateInfo = res.data.affiliateInfo || {};
                    if (
                        res.data.affiliateInfo &&
                        res.data.affiliateInfo.affiliatedName
                    ) {
                        this.isShowAffiliateInfo = true;
                        // 显示挂靠信息导航
                        this.$emit("updateNavList",
                            { order: 7, id: 'asset_100', title: '挂靠信息', hide: false }
                        );
                    }
                }
            });
            // this.initMainAddressList();
        },
        init() {
            this.queryDictData();
            this.initDataDic();
            this.initInfo();
            this.initRecordHistory();
            this.toGetRealTimeDataList(); // 挂靠实时数据
            this.queryGpsPerform();
            this.checkInvoiceInfo();
            this.queryGuarantee();
            this.guaranteeCheck();
            this.getResultByApplyNo();
            // 获取拨打记录
            this.initCallRecord();
            // 获取短信记录
            this.initSMSRecord();
            this.getCertificateInfo();
            this.getInnerCarInfo();
        },
        cancelDetail() {
            this.hisDetailVisible = false;
        },
        //展示历史匹配详情
        showHisDetail(applyNos) {
            this.hisDetailVisible = true;
            this.historyDetail = applyNos;
        },
        //挂靠实时数据
        toGetRealTimeDataList() {
            getRealTimeDataList(this.applyNumber).then(res => {
                if (res.code === "0000") {
                    console.log(res.data)
                    this.realTimeDataBody = res.data.data;
                }
            });
        },
        //留言查询
        queryRemind() {
            let params = {
                applyNo: this.detailParams.applyNo,
                //applyNo: "DP-A002687",
                stageId: this.detailParams.stageId,
                useScene: "generalLoan"
            };
            queryRemindListByApplyNo(params).then(res => {
                if (res.code === "0000") {
                    this.remindListInside = [];
                    this.remindListOut = [];
                    res.data.forEach(item => {
                        //内部留言
                        if (item.remindType === "reasonInner") {
                            this.remindListInside.push(item);
                        } else {
                            this.remindListOut.push(item);
                        }
                    });
                }
            });
        },
        //保存gps履约信息
        saveGpsPerform() {
            let params={
                gpsInfo:{
                    applyNo: this.detailParams.applyNo,
                    gpsPerform: this.gpsPerform,
                    gpsSupplier:this.gpsSupplierTemp
                },
                record:this.remindApproveRecord
            }
            saveGpsPerform(params).then(res => {
                if (res.code === "0000") {
                    this.queryRemind();
                    this.$Message.success("保存成功");
                }
            });
        },
        queryLiaisonMan() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            caseCustContactList(params).then(res => {
                if (res.code === "0000") {
                    this.caseCustContact = res.data;
                    // res.data.forEach(item => {
                    //   this.caseCustContact.push(item);
                    // });
                }
            });
        },
        afterRemindSubmit(type, approveRecord, remind) {
            if (type == "save") {
                this.queryRemind();
            } else if (type == "reject") {
                this.afs.afsEmit(this, "flushData");
                /*   window.close();*/
            } else if (type == "parse") {
                this.queryRemind();
            }
        },
        beforeRemindSubmit(type, record, remind) {
            if (type == "reject") {
                this.remindApproveRecord.approveRemark = record.approveRemark;
            }
        },
        addMessage() {
            this.remindVisible = true;
            console.log("remindVisible" + this.remindVisible);
        },
        remindCancel() {
            this.remindVisible = false;
        },
        locationbute() {
            // this.gpsInfo.applyNo = this.applyNo;
            this.gpsInfo.applyNo = this.detailParams.applyNo;
            locationbute(this.gpsInfo).then(res => {
                if (res.code === "0000") {
                    if (res.data == null || res.data.length === 0) {
                        this.$Message.error("未查询到GPS信息！");
                        return;
                    }
                    this.gpsDataList = res.data;

                    /*res.data.forEach(resultDTOS => {
                        //是否有线
                        if (resultDTOS.typestr.indexOf("有线") > -1) {
                            this.getAllEqPositionsReqDTO.isFlag = resultDTOS.equipment;
                            this.getAllEqPositionsReqDTO.sbcstatus = resultDTOS.sbcstatus;
                            this.getAllEqPositionsReqDTO.locationAdd = resultDTOS.locationAdd;
                        } else {
                            this.getAllEqPositionsReqDTO.isNotFlag = resultDTOS.equipment;
                            this.getAllEqPositionsReqDTO.isNotsbcStatus = resultDTOS.sbcstatus;
                            this.getAllEqPositionsReqDTO.locationAdd = resultDTOS.locationAdd;
                        }
                    });
                    this.$Message.success("GPS定位成功");*/
                } else {
                    this.$Message.error("未查询GPS设备信息！");
                    return false;
                }
            });
        },
        invoiceCheck() {
            if (!this.carInvoiceInfo.invoiceDate) {
                this.$Message.success("请选择开票日期");
                return false;
            }
            this.carInvoiceInfo.applyNo = this.detailParams.applyNo;
            this.carInvoiceInfo.isAlreadyCheck = "是";
            let invoiceInfo = {
                applyNo: this.detailParams.applyNo,
                invoiceCode: this.carInvoiceInfo.invoiceCode,
                invoiceNo: this.carInvoiceInfo.invoiceNumber,
                invoiceDate: utils.formatDate(this.carInvoiceInfo.invoiceDate, 'YYYY-MM-DD'),
                totalAmount: this.carInvoiceInfo.invoiceTaxAmt.replaceAll(",", ""),
            };
            invoiceCheck(invoiceInfo)
                .then(res => {
                    if (res.code === "0000") {
                        this.invoiceVerifications = [];
                        this.invoiceVerifications.push(res.data);
                        let carInvoiceData = deepClone(res.data);
                        this.carInvoiceInfo.invoiceCode = carInvoiceData.invoiceCode;
                        this.carInvoiceInfo.invoiceNumber = carInvoiceData.invoiceNumber;
                        this.carInvoiceInfo.makeInvoiceUnit = carInvoiceData.purchaserName;
                        this.carInvoiceInfo.invoiceAmt = AfsUtils.commafy(carInvoiceData.amountTax,{ digits: 2 });
                        this.carInvoiceInfo.invoiceRate = AfsUtils.commafy(carInvoiceData.taxRate,{ digits: 0 });
                        this.carInvoiceInfo.invoiceTaxAmt = AfsUtils.commafy(carInvoiceData.amount,{ digits: 2 });
                        this.carInvoiceInfo.excludingTaxAmt = AfsUtils.commafy(carInvoiceData.tax,{ digits: 2 });
                        this.carInvoiceInfo.invoiceDate = carInvoiceData.billingDate;
                        //发票查验通过
                        //发票查验通过
                        if(res.data.checkIsSame){
                            this.carInvoiceInfo.isPass = "正常";
                            this.carInvoiceInfo.abnormalReason = "无";
                        }else{
                            this.carInvoiceInfo.isPass = "异常";
                            this.carInvoiceInfo.abnormalReason = res.data.checkMessage;
                        }
                        this.$Message.success("发票查验操作成功!");
                    } else {
                        this.carInvoiceInfo.isPass = "异常";
                        this.carInvoiceInfo.abnormalReason = res.msg;
                        // this.$Message.error(res.msg);
                        // this.$Message.error("发票未查验");
                    }
                })
                .catch(() => {
                    this.$set(this.carInvoiceInfo, "isPass", "异常");
                    this.$set(
                        this.carInvoiceInfo,
                        "abnormalReason",
                        "发票数据异常"
                    );
                    //this.carInvoiceInfo.isPass = "异常";
                    //this.carInvoiceInfo.abnormalReason = "发票数据异常";
                });
        },
        invoiceHistory() {

            this.carInvoiceInfo.applyNo = this.detailParams.applyNo;
            this.carInvoiceInfo.isAlreadyCheck = "是";
            let invoiceInfo = {
                applyNo: this.detailParams.applyNo,
            };
            invoiceHistory(invoiceInfo)
                .then(res => {
                    if (res.code === "0000" && res.data) {
                        this.invoiceVerifications = [];
                        this.invoiceVerifications.push(res.data);
                        //发票查验通过
                        if(res.data.checkIsSame){
                            this.carInvoiceInfo.isPass = "正常";
                            this.carInvoiceInfo.abnormalReason = "无";
                        }else{
                            this.carInvoiceInfo.isPass = "异常";
                            this.carInvoiceInfo.abnormalReason = res.data.checkMessage;
                        }
                    } else {
                        this.carInvoiceInfo.isPass = "异常";
                        this.carInvoiceInfo.abnormalReason = res.msg;
                        // this.$Message.error(res.msg);
                        // this.$Message.error("发票未查验");
                    }
                })
                .catch(() => {
                    this.$set(this.carInvoiceInfo, "isPass", "异常");
                    this.$set(
                        this.carInvoiceInfo,
                        "abnormalReason",
                        "发票数据异常"
                    );
                    //this.carInvoiceInfo.isPass = "异常";
                    //this.carInvoiceInfo.abnormalReason = "发票数据异常";
                });
        },
        //车型甄别
        toCarModelScreening() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            carModelScreening(params).then(res => {
                if (res.code === "0000") {
                    this.carBaseInfos = res.data;
                    this.screeningResult = res.msg;
                }
            });
        },
        //三者险check
        checkThirdMoney() {
            let thirdInsurance = parseFloat(
                this.carInsuranceInfo.thirdInsurance.replaceAll(",", "")
            );
            if (thirdInsurance) {
                if (thirdInsurance < 500000) {
                    this.$Message.error("三者险不能少于50万");
                    this.saveLoading = false;
                    return;
                }
            }
        },
        changeRecord(){
            this.isModel=true;
        },
        //车损险保额check
        checkCarDamageMoney() {
            let insuranceMoney = parseFloat(
                this.carInsuranceInfo.insuranceMoney.replaceAll(",", "")
            );
            let loanAmt = parseFloat(
                this.outlineVo.loanAmt.replaceAll(",", "")
            );
            if (insuranceMoney && loanAmt) {
                if (insuranceMoney < loanAmt) {
                    this.$Message.error("车损险保额必须大于融资额");
                    this.saveLoading = false;
                    return;
                }
            }
        },
        //保存精品装潢发票信息
        toSaveDecorationInvoiceInfo() {
            this.carDecorationInvoiceInfo.invoiceAmt = this.carDecorationInvoiceInfo.invoiceAmt.replaceAll(",","");
            this.carDecorationInvoiceInfo.invoiceTaxAmt = this.carDecorationInvoiceInfo.invoiceTaxAmt.replaceAll(",","");
            saveDecorationInvoiceInfo(this.carDecorationInvoiceInfo)
                .then(res => {
                    if (res.code == "0000") {
                        console.log("成功保存");
                        this.$Message.success("保存成功");
                    } else {
                        this.$Message.error("保存失败！");
                    }
                })
                .catch(error => {
                    // this.$Message.error("GPS保存失败！");
                });
        },
        //保存发票信息
        saveInvoiceInfo() {
            if (!this.carInvoiceInfo.invoiceCode) {
                this.$Message.error("请填写发票代码");
                return;
            }
            if (!this.carInvoiceInfo.invoiceNumber) {
                this.$Message.error("请填写发票号码");
                return;
            }

            if (!this.carInvoiceInfo.invoiceDate ) {
                this.$Message.error("请填写开票日期");
                return;
            }

            if (!this.carInvoiceInfo.excludingTaxAmt) {
                this.$Message.error("请填写不含税价");
                return;
            }
            if(this.carInvoiceInfo.excludingTaxAmt){
                this.carInvoiceInfo.excludingTaxAmt = parseFloat(this.carInvoiceInfo.excludingTaxAmt.replaceAll(",", ""));
            }
            let params={
                carInvoiceInfo:{
                    excludingTaxAmt : this.carInvoiceInfo.excludingTaxAmt,
                    invoiceCode : this.carInvoiceInfo.invoiceCode,
                    invoiceNumber : this.carInvoiceInfo.invoiceNumber,
                    invoiceDate : this.carInvoiceInfo.invoiceDate,
                    id : this.carInvoiceInfo.id,
                    applyNo : this.carInvoiceInfo.applyNo
                },
                record:this.remindApproveRecord
            }
            saveInvoiceInfo(params).then(res => {
                if (res.code == "0000") {
                    console.log("成功保存");
                    this.carInvoiceInfo.excludingTaxAmt=AfsUtils.commafy(this.carInvoiceInfo.excludingTaxAmt, { digits: 2 });
                    this.queryRemind();
                    this.$Message.success("发票信息保存成功");
                } else {
                    this.$Message.error("发票信息保存失败！");
                }
            })
                .catch(error => {
                    this.$Message.error("发票保存失败！");
                });
        },
        //保存首保信息
        saveInsurance() {
            let insuranceMoney = parseFloat(
                this.carInsuranceInfo.insuranceMoney.replaceAll(",", "")
            );
            let loanAmt = parseFloat(
                this.outlineVo.loanAmt.replaceAll(",", "")
            );
            let thirdInsurance = parseFloat(
                this.carInsuranceInfo.thirdInsurance.replaceAll(",", "")
            );
            if(this.carInsuranceInfo.isLaterSupply!='1'){


                if ( typeof this.carInsuranceInfo.insuranceNo === "undefined" || this.carInsuranceInfo.insuranceNo === "" ) {
                    this.$Message.error("请填写商业险保单号");
                    this.saveLoading = false;
                    return;
                }
                if (  typeof this.carInsuranceInfo.insuranceAmt === "undefined" ||  this.carInsuranceInfo.insuranceAmt === "" ) {
                    this.$Message.error("请填写商业险保费");
                    this.saveLoading = false;
                    return;
                }
                if (
                    typeof this.carInsuranceInfo.insuranceStartTime ===
                    "undefined" ||
                    this.carInsuranceInfo.insuranceStartTime === ""
                ) {
                    this.$Message.error("请选择商业险生效日");
                    this.saveLoading = false;
                    return;
                }
                if (
                    typeof this.carInsuranceInfo.insuranceEndTime === "undefined" ||
                    this.carInsuranceInfo.insuranceEndTime === ""
                ) {
                    this.$Message.error("请选择商业险失效日");
                    this.saveLoading = false;
                    return;
                }
                if (
                    typeof this.carInsuranceInfo.insuranceMoney === "undefined" ||
                    this.carInsuranceInfo.insuranceMoney === ""
                ) {
                    this.$Message.error("请输入车损险保额");
                    this.saveLoading = false;
                    return;
                }

                if (insuranceMoney && loanAmt) {
                    if (insuranceMoney < loanAmt) {
                        this.$Message.error("车损险保额必须大于融资额");
                        this.saveLoading = false;
                        return;
                    }
                }
                if (
                    typeof this.carInsuranceInfo.thirdInsurance === "undefined" ||
                    this.carInsuranceInfo.thirdInsurance === ""
                ) {
                    this.$Message.error("请填写三者险");
                    this.saveLoading = false;
                    return;
                }

                if (thirdInsurance) {
                    if (thirdInsurance < 500000) {
                        this.$Message.error("三者险不能少于50万");
                        this.saveLoading = false;
                        return;
                    }
                }


            }

            let today = new Date();
            let strongTime = new Date(
                this.carStrongInsuranceInfo.insuranceStartTime
            );
            let strongDayTime = (today - strongTime) / (1000 * 60 * 60 * 24);

            let businessTime = new Date(this.carInsuranceInfo.insuranceStartTime);
            let businessDayTime =(today - businessTime) / (1000 * 60 * 60 * 24);

            if(this.carStrongInsuranceInfo.isLaterSupply!='1'){

                if (
                    typeof this.carStrongInsuranceInfo.insuranceNo ===
                    "undefined" ||
                    this.carStrongInsuranceInfo.insuranceNo === ""
                ) {
                    this.$Message.error("请填写交强险保单号");
                    this.saveLoading = false;
                    return;
                }
                if (
                    typeof this.carStrongInsuranceInfo.insuranceAmt ===
                    "undefined" ||
                    this.carStrongInsuranceInfo.insuranceAmt === ""
                ) {
                    this.$Message.error("请填写交强险保费");
                    this.saveLoading = false;
                    return;
                }
                if (
                    typeof this.carStrongInsuranceInfo.insuranceStartTime ===
                    "undefined" ||
                    this.carStrongInsuranceInfo.insuranceStartTime === ""
                ) {
                    this.$Message.error("请选择交强险生效日");
                    this.saveLoading = false;
                    return;
                }
                if ( typeof this.carStrongInsuranceInfo.insuranceEndTime === "undefined" || this.carStrongInsuranceInfo.insuranceEndTime === "" ) {
                    this.$Message.error("请选择交强险失效日");
                    this.saveLoading = false;
                    return;
                }

                if (  this.financeItem.bisnessAmt > 0 ||   this.financeItem.compulsoryAmt > 0) {
                    if (this.examineAssetPageInfoVo.caseCarInfo.salePrice) {
                        this.examineAssetPageInfoVo.caseCarInfo.salePrice = 0;
                    }
                    if (this.financeItem.bisnessAmt) {this.financeItem.bisnessAmt = 0;}
                    if (this.financeItem.compulsoryAmt) {this.financeItem.compulsoryAmt = 0;}
                    if (this.examineAssetPageInfoVo.businessType === "01") {
                        if (this.financeItem.bisnessAmt + this.financeItem.compulsoryAmt - this.carStrongInsuranceInfo.insuranceAmt - carInsuranceInfo.insuranceAmt > 1000) {
                            this.$Message.error( "新车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于1000元" );
                        }
                    }
                    if (this.examineAssetPageInfoVo.carType === "01") {
                        if (
                            this.financeItem.bisnessAmt +
                            this.financeItem.compulsoryAmt -
                            this.carStrongInsuranceInfo.insuranceAmt -
                            carInsuranceInfo.insuranceAmt >
                            this.examineAssetPageInfoVo.caseCarInfo.salePrice / 100
                        ) {
                            this.$Message.error(
                                "商用车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于车价的1%"
                            );
                        }
                    }
                }
                if (strongDayTime < -15) {
                    this.$Message.error("交强险保单超期，请提供批单修改生效日期");
                    return;
                }

                if (businessDayTime < -15) {
                    this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                    return;
                }
            }




            this.carStrongInsuranceInfo.insuranceAmt = this.carStrongInsuranceInfo.insuranceAmt.replaceAll(",","");
            this.carInsuranceInfo.thirdInsurance = this.carInsuranceInfo.thirdInsurance.replaceAll(",","");
            this.carInsuranceInfo.insuranceMoney = this.carInsuranceInfo.insuranceMoney.replaceAll(",","");
            this.carInsuranceInfo.insuranceAmt = this.carInsuranceInfo.insuranceAmt.replaceAll(",","");
            var params = [];
            params[0] = this.carStrongInsuranceInfo;
            params[1] = this.carInsuranceInfo;
            saveInsurance(params)
                .then(res => {
                    if (res.code == "0000") {
                        console.log("成功保存");
                        this.$Message.success("保存成功");
                    } else {
                        this.$Message.error("保存失败！");
                    }
                })
                .catch(error => {
                    // this.$Message.error("GPS保存失败！");
                });
        },
        calculateIntrate() {
            this.$refs.financialProd.calculateIntrate("calculate");
        },
        passValiate() {
            this.initProessValiate();
        },
        queryFormEl() {
            return this.$refs.proessForm;
        },
        rowClassName(row, index) {
            if (row.isExpire) {
                return "demo-table-info-row";
            }
            return null;
        },
        initMainBaseInfo() {
            let params = {
                applyNo: this.detailParams.applyNo
            };
            queryPersionOrEnterpriseInfo(params).then(res => {
                if (res.code === "0000" && res.data) {
                    //区分企业和个人
                    this.mainBaseInfo = res.data;

                        // this.initMainAddressList();
                        /*if (res.data.detailAddressTemp) {
                  for (var i = 0; i < res.data.detailAddressTemp.length; i++) {
                      if (res.data.detailAddressTemp[i] == "" || res.data.detailAddressTemp[i] == null || typeof res.data.detailAddressTemp[i] == undefined) {
                          res.data.detailAddressTemp.splice(i, 1);
                          i = i - 1;
                      }
                  }
                  this.mainBaseInfo.detailAddressTemp = res.data.detailAddressTemp;
              }*/
                        /*let houseHoldAddress = []
houseHoldAddress[0] = res.data.province;
houseHoldAddress[1] = res.data.city;
this.houseHoldAddress = houseHoldAddress;*/

                        //this.mainBaseInfo.zodiacList = this.getZodiacList(this.mainBaseInfo.birthday);
                        //this.mainBaseInfo.zod = this.getZodiac(this.mainBaseInfo.birthday);
                    // 个人转企业
                    this.personalChangeEnterprise = this.mainBaseInfo.ifPersonalToEnterprise === '1';
                    if (this.personalChangeEnterprise) {
                        if (this.mainBaseInfo.businessLicenseAddress && this.mainBaseInfo.businessLicenseAddress.detailAddressTemp) {
                            this.mainBaseInfo.businessLicenseAddress = this.handleBusinessAddress(this.mainBaseInfo.businessLicenseAddress.detailAddressTemp);
                        }
                        this.mainBasePersonal = true;
                        this.mainBaseEnterprise = false;
                        this.$emit('updateNavList', {order: 5, id: 'asset_80', title: '客户信息',hide: false});
                        this.$emit('updateNavList', [{order: 6, id: 'asset_90', title: '企业信息',hide: true}])
                        this.$emit('updateNavList', [{order: 7, id: 'asset_90', title: '企业信息', hide: false}]);
                        // 保证人增加第一担保人列
                        const isFirstGuarantor = {
                            title: "第一担保人",
                            key: "isFirstGuarantor",
                            minWidth: 160,
                            align: "center",
                            slot: "isFirstGuarantor"
                        };
                        this.caseinfoColums.push(isFirstGuarantor);
                    } else {
                        // 原有逻辑
                        if (res.data.inputType == "1") {
                            this.mainBaseEnterprise = true;
                            // this.$refs.companyBaseInfoDom.updateFormData(res.data);
                            this.$emit('updateNavList', [ { order: 6, id: 'asset_90', title: '企业信息',hide:false }])
                        } else {
                            this.mainBasePersonal = true;
                            this.backSign = res.data.backSign;
                        }
                    }
                }
            });
        },
        // 解析营业地址
        handleBusinessAddress(data) {
            for (let i = 0; i < data.length; i++) {
                if (data[i] === "" || data[i] === null || typeof data[i] === undefined) {
                    data.splice(i, 1);
                    i = i - 1;
                }
            }
            return data;
        },
        initHistoryInfo() {
            let params = {
                applyNo: this.applyNumber
            }
            getHistoryInfo(params).then(res => {
                if (res.code === "0000") {
                    this.historyInfoList = res.data;
                }
            });
        },
        initMainAddressList() {
            let params = {
                applyNo: this.applyNumber,
                custRole: "01"
            };
            console.log("##########################################");
            console.log("initMainAddressList");
            console.log(this.applyNumber);
            queryAddressList(params).then(res => {
                console.log(res);
                console.log(this.isCompanyApply);
                console.log("##########################################");
                if (res.code === "0000" && res.data) {
                    this.companyAddressList = res.data;
                    this.companyAddressList.forEach((item, index) => {
                        if(item.addressType == "6"){
                            this.mainBaseInfo.detailAddress = item.detailAddress;
                        }
                    });
                    /*if (this.isCompanyApply) {
            this.$refs.companyBaseInfoDom.updateAddressData(
              res.data
            );
          } else {
            this.mainAddressList = res.data;
          }*/
                }
            });
        },
        initDataDic() {
            let self = this;
            console.log("==========获取数据字典");
            getByTypes(this.dicKeys).then(res => {
                console.log("======获取字典数据：" + JSON.stringify(res));
                if (res.code === "0000" && res.data) {
                    self.dataDic = res.data;
                }
            });
        },
        getDic(v){
            let dic = []
            switch(v) {
                case "addressType":
                    dic = this.dataDic.addressType;
                    break;
                case "houseType":
                    dic = this.dataDic.houseType;
                    break;
                case "certType":
                    dic = this.dataDic.certType;
                    break;
                case "custRelation":
                    dic = this.dataDic.custRelation;
                    break;
                case "nationality":
                    dic = this.dataDic.nationality;
                    break;
                case "callType":
                    dic = this.dataDic.callType;
                    break;
                case "specialApplyStatus":
                    dic=this.dataDic.specialApplyStatus;
                    break;
                case "specialForRejection":
                    dic=this.dataDic.specialForRejection
                    break;
                case "gpsRunStatus":
                    dic=this.dataDic.gpsRunStatus
                    break;
                default:
            }
            return dic;
        },
        setDictTitleByType(v1,v2){
            let dic = {}
            if(!this.getDic(v1)) return '';
            this.getDic(v1).forEach(column => {
                if(column.value === v2){
                    dic = column;
                }
            });
            return dic.title;
        },
        setBalickList(row) {
            if (row === "01") {
                row = "黑";
                return row;
            }
            if (row === "02") {
                row = "灰";
                return row;
            }
        },
        editRemark(v) {
            this.remarkVisible = true;
            this.remarkFrom = v;
            if(v.callType){
              this.remarkFrom.remark=v.remark;
              this.remarkFrom.callType=v.callType;
            }else{
              this.remarkFrom.callType= '';
            }
        },
        cancelRemark() {
            this.remarkVisible = false;
            //this.queryLiaisonMan();
        },
        saveRemark() {
             if (this.remarkFrom.callType === "请选择") {
                this.remarkFrom.callType = ''
            }
            if (!this.remarkFrom.businessType) {
                this.remarkFrom.businessType = this.userDetail.templateId || this.templateId
            }
            if (!this.remarkFrom.sourceType) {
                this.remarkFrom.sourceType = this.userDetail.userDefinedIndex || ""
            }
            this.$refs.remarkFrom.validate(valid => {
                if (valid) {
                    saveRemarkHistory(this.remarkFrom).then(res => {
                        if (res.code === "0000") {
                            this.initCallRecord();
                            this.remarkVisible = false;
                            this.$refs.remarkFrom.resetFields();
                            if (res.msg === "success") {
                                this.$Message['success']({content: '编辑成功'})
                            } else {
                                this.$Message['error']({content: '编辑失败'})
                            }
                        }
                    });
                }
            });
        },
        addContact() {
            this.contactFrom.id = "";
            this.editContactOrSave = "1";
            this.contactModalType = 0;
            this.contactTitle = "新增联系人";
            this.$refs.contactFrom.resetFields();
            this.contactVisible = true;
            this.contactFrom.applyNo = this.detailParams.applyNo;
            this.contactFrom.custId = this.mainBaseInfo.id;
        },
        cancelContact() {
            this.contactVisible = false;
            this.$refs.contactFrom.resetFields();
            this.contactFromValidate.certType[0].required = false;
            this.contactFromValidate.certNo[0].required = false;
        },
        submitContact() {
            this.$refs.contactFrom.validate(valid => {
                if (valid) {
                    console.log(
                        "this.editContactOrSave====================" +
                        this.editContactOrSave
                    );
                    if (this.editContactOrSave === "1") {
                        var random_no = "";
                        for (var i = 0; i < 6; i++) {
                            random_no += Math.floor(Math.random() * 10);
                        }
                        random_no = new Date().getTime() + random_no;
                        this.contactFrom.id = random_no;
                        console.log(
                            "this.contactFrom.id=" + this.contactFrom.id
                        );
                        saveSummaryContact(this.contactFrom).then(res => {
                            if (res.code === "0000" && res.data) {
                                this.queryLiaisonMan();
                                this.contactVisible = false;
                                this.$refs.contactFrom.resetFields();
                            } else {
                                this.$Message.error(
                                    "联系人新增失败，请重新操作"
                                );
                            }
                        });
                    }
                    if (this.editContactOrSave === "2") {
                        updateContactById(this.contactFrom).then(res => {
                            if (res.code === "0000") {
                                this.queryLiaisonMan();
                                this.contactVisible = false;
                                this.$refs.contactFrom.resetFields();
                                if (res.msg === "success") {
                                    this.$Message["success"]({
                                        content: "编辑成功"
                                    });
                                } else {
                                    this.$Message["error"]({
                                        content: "删除失败"
                                    });
                                }
                            }
                        });
                    }
                }
            });
        },
        updateStatusIsOpen() {
            let params = "";
            params = {
                approveStaff: this.detailParams.disposeCode,
                stageId: this.detailParams.stageId,
                processType: this.detailParams.processType,
                applyNo: this.detailParams.applyNo
            };
            setStatusIsOpen(params).then(res => {
                if (res.code === "0000") {
                }
            });
        },
        getFileList() {
            this.$refs.fileAudit.getFileList();
        },
        closepop() {
            this.loanBackReasonVisible = false;
        },
        async backBtn() {
            await this.whenClose();
            window.close();
        },
        getBaseData(val) {
            this.baseData = val;
        },
        getOrderByNavId(id) {
            let sty;
            const item = this.navList.find(e => e.id === id);
            if (item) {
                sty = { order: item.order || 0 };
            }
            return sty;
        },
        channelBelongFun(type){
            if(type=="01"){
                this.isChannelBelong=true
            }else{
                this.isChannelBelong=false
            }
        },
        getInnerCarInfo(){
            let param = {
                applyNo: this.applyNo
            }
            getInnerCarInfo(param).then(res=>{
                if (res.code === "0000"&& res.data) {
                    this.innerShow = true;
                    this.innerCarInfo = res.data;
                    this.innerCarInfo.vinNo = res.data.vinNo;  //车架号
                    this.innerCarInfo.color = res.data.color;  //颜色
                    this.innerCarInfo.vehicleNature = res.data.vehicleNature;  //使用性质
                    this.innerCarInfo.assetResidual = res.data.assetResidual;  //资产残值(元)
                    this.innerCarInfo.viewCarLocate = res.data.viewCarLocate;  //看车地点
                    this.innerCarInfo.repairRecord = res.data.repairRecord;  //维修记录
                    this.innerCarInfo.bidSuccessfulTime = res.data.bidSuccessfulTime;  //成交时间
                    this.innerCarInfo.putawayTime = res.data.putawayTime;  //上架时间
                    this.innerCarInfo.isOperateCar = res.data.isOperateCar;  //是否营运车辆
                    this.innerCarInfo.status = res.data.status;  //车辆状态
                    this.innerCarInfo.plateNo = res.data.plateNo;  //车牌号
                    this.innerCarInfo.odMemter = res.data.odMemter;  //行驶里程
                    this.innerCarInfo.vehicleThreeGuarantee = res.data.vehicleThreeGuarantee;  //三包
                    this.innerCarInfo.sessionName = res.data.sessionName;  //场次信息
                    this.innerCarInfo.plateFlag = res.data.plateFlag;  //  是否带车牌
                    this.innerCarInfo.registerData = res.data.registerData;  //  注册时间
                    this.innerCarInfo.closingCost = res.data.closingCost;  //  注册时间
                    this.innerCarInfo.mobile = res.data.mobile;  //  注册时间
                    this.innerCarInfo.idNo = res.data.idNo;  //  身份证
                    this.innerCarInfo.userName = res.data.userName;  //  身份证

                    this.innerCarInfo.empNo = res.data.empNo;  //  注册时间
                    this.innerCarInfo.idNo = res.data.idNo;  //  身份证
                    this.innerCarInfo.userName = res.data.userName;  //  身份证
                }
            })
        },
    },
    mounted() {
        this.init();
        this.initHistoryInfo();
    },
    created() {
        this.applyNumber =
            this.applyNo != "" ? this.applyNo : this.detailParams.applyNo;
        //this.applyNumber = "DP-A002687";
        this.stageId = this.detailParams.stageId;
        this.remindApproveRecord.applyNo = this.applyNumber;
        this.remindApproveRecord.stageId = this.stageId;
        this.remindApproveRecord.approveRemark = this.approveRemark;
    }
};
</script>

<style scoped>
.recordHistoryRecord{
    float: right;width:94%;margin-left:10px;height:60px;display:flex;border-radius:10px;margin-top:6px;flex-direction: column;justify-content: center;
}
.scheduleTitle {
    padding-left: 30px;
    line-height: 20px;
    font-size: 14px;
}

.scheduleTitle span.curScheStyle {
    color: #0d8ef5;
}
.ivu-col-span-8 /deep/ {
    height: 40px;
}
.j-sbt{
    display: flex;
    justify-content: space-between;
}
.j-sb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 33px;
    padding-right: 10px;
}
.blue {
    background: #19be6b;
    color: #fff;
    margin-top: 2px;
}
.blues{
    background: #19be6b;
    margin: 5px 10px;
}
.ivu-form-item /deep/ {
    margin-bottom: 5px;
}
.iphone-invoice /deep/ .ivu-form-item-error-tip{
    padding-top:0px !important;
}
.flex-sb{
    display:flex;justify-content:space-between;align-items:center
}
.load_hong_beizhu{
    margin-top: 5px;
    margin-left: 10px;
    border-top: 1px solid #EEEEEE;
    padding-top: 5px;
}
.inputindex /deep/ input.ivu-input.ivu-input-small.ivu-input-with-suffix{
    color: red;
}
.inputindexs /deep/ input.ivu-input.ivu-input-small.ivu-input-with-suffix{
    color: black;
}
.insuranceStartTimeIndex /deep/ input.ivu-input.ivu-input-small.ivu-input-with-suffix{
    color: red;
}
.insuranceStartTimeIndexs /deep/ input.ivu-input.ivu-input-small.ivu-input-with-suffix{
    color: black;
}
</style>
