<template>
    <div class="search">
        <Row>          
            <Form ref="searchForm" :model="searchForm" inline :label-width="130" class="search-form rui-query-form" style="width: 100%">
                            <FormItem  label="渠道名称">
                            <div>                        
                                 <Select v-model="searchForm.dealerNo" placeholder="渠道名称" serch  clearable  filterable  style="width: 200px">
                                    <Option v-for="item in dealerData" :value="item.channelCode" :key="item.channelCode">{{ item.channelFullName }}
                                    </Option>
                                </Select>     
                            </div>
                        </FormItem>   
                        <FormItem  label="合同生成时间">
                            <div>                        
                                 <DatePicker v-model="searchForm.beginTime" type="daterange" format="yyyy-MM-dd" clearable
                                  placeholder="放款时间" style="width: 200px"></DatePicker>
                            </div>
                        </FormItem>                      
                        <FormItem label="申请编号">
                            <div>
                                  <Input placeholder="申请编号"    v-model="searchForm.applyNo" style="width: 200px"/>
                            </div>
                        </FormItem>
                        <FormItem label="客户姓名">
                            <div>                             
                               <Input placeholder="客户姓名"   v-model="searchForm.custName" style="width: 200px"/>
                            </div>
                        </FormItem>
                        <FormItem  label="进件类型">
                            <div>
                                <Select v-model="searchForm.inputType" placeholder="进件类型"     clearable style="width: 200px">
                                    <Option v-for="item in custTypeList" :value="item.value" :key="item.value">{{ item.title }}
                                    </Option>
                                </Select>                                  
                            </div>
                        </FormItem>
                        <FormItem  label="业务类型">
                            <div>
                                <Select v-model="searchForm.businessType" placeholder="业务类型"  clearable style="width: 200px">
                                    <Option v-for="item in businessTypeList" :value="item.value" :key="item.value">{{ item.title }}
                                    </Option>
                                </Select>                                  
                            </div>
                        </FormItem>
                        <FormItem label="业务主体">
                            <div>
                                <Select v-model="searchForm.subjectCode" placeholder="业务主体" clearable style="width: 200px">
                                    <Option v-for="item in dataDic.subjectCode" :value="item.value" :key="item.value">{{ item.title
                                        }}
                                    </Option>
                                </Select>
                            </div>
                        </FormItem>
                        <FormItem   label="">
                            <div style="text-align: center">
                                <Button @click="handleSearch" type="primary">查询</Button>
                                <Button @click="handleReset">重置</Button>

                                <Button @click="exportOrderEvent">导出</Button>
                                  
                            </div>
                        </FormItem>
            </Form>
        </Row>
            <Table  border :columns="columns" :data="formData" sortable="custom" ref="table" @on-selection-change="showSelect"></Table> 
            <Page :current="searchForm.pageNumber" :total="searchForm.total" :page-size="searchForm.pageSize"
                  @on-change="changePage" @on-page-size-change="changePageSize" :page-size-opts="[10,20,50]"
                  size="small" show-total show-elevator show-sizer></Page>
    </div>
</template>

<script>   
    import {dealOrderQuery,dealOrderExport,queryDealerList} from "./constant/paymentPlantApi";
    import {getByTypes} from "_p/basic/api/admin/datadic";
    export default {
        name: "orderReportExport",
         activated() {
            this.queryOrderList(); 

            this.queryDealerData();
        },
        data() {
            return {
               queryPageSize: 0,
                usernameCreateBy: '',
                userPower: {
                    userManage: '',
                    userId: '',
                    newApplyPower: '',
                    odlApplyPower: '',
                    newAccountPower: '',
                    oldAccountPower: ''
                },
                searchForm:{
                    "beginTime":"",
                    "applyNo":"",
                    "custName":"",
                    "inputType":"",
                    "businessType":"",
                    "endTime":"",
                    "pageNumber":1,
                    "pageSize":10,
                    "total":0,
                    "dealerNo":"",
                    "subjectCode":"",
                },
               
                 custTypeList:[
                        {value:"0",title:"个人"},
                     {value:"1",title:"企业"},
                 ],
                 businessTypeList:[
                     {value:"01",title:"新车"},
                     {value:"02",title:"二手车"},                      
                 ],
                 dealerData:[],
                createByAndName: '',
                applyStatus: '',
                isUrgent: false,
                isReconsider: false,
                dropDownContent: '更多筛选',
                drop: false,
                dropDownIcon: "ios-arrow-down",
                activeBtn: null, //订单状态按钮
                loanApplyBtns: [
                    {key: null, title: '全部'},
                    {key: '00', title: '草稿'},
                    {key: 'APPLY_STATUS_PRE_DEAL', title: '待处理'},
                    //     <!--修订暂停-->
                    //     <!--修订回复06-->
                    {key: 'APPLY_STATUS_PENDING', title: '待审核'},
                    {key: '08', title: '核准'},
                    //     <!--核准08,附条件核准09-->
                    {key: '10', title: '拒绝'},// <!--10拒绝-->
                ],
                operationLoading:false,
                formData: [],
                tableHead: [],
                businessType: '',
                urgentName: "加急",
                isHide: false,
                // 管理员不可以发起进件和预审批，不可以发起编辑、复议、撤回等功能，可实现转单、加急即可
               // transferTitle: '转单界面',
                // imgtype: "reconsiderApply",
                applyNo: '',
                deptId: '',//部门id
                transferDatas: {},//查询转单人员条件
                roleCode: [],//
                roleCodes: '',
                username: '',
                userRealName: '',
                disable: false,                
                reconsiderModalVisible: false,
                urgentModalVisible: false,
                backModalVisible: false, 
                approveDetailsForm:[],
                approveForm:[],//审批记录
                dataDic:{},
                //列表显示字段
                columns: [
                    {
                        title: "申请编号",
                        key: "applyNo",
                       align: 'center',       
                        minWidth: 100, 
                          fixed: "left"
                    },
                    {
                        title:'合同号',                     
                        key:"contractNo",
                        width: 120,
                        align: 'center',                       
                         fixed: "left" 
                    },
                    {
                        title:'业务主体',
                        key:"subjectName",
                        width: 120,
                        align: 'center',
                        fixed: "left"
                    },
                    {
                        title: "承租人",
                        key: "custName",
                       align: 'center',       
                        minWidth: 80, 
                    }, 
                    {
                        title: "部门",
                            key: "deptName",
                      align: 'center',       
                        minWidth: 80, 
                    },
                    {
                        title: "业务类型",
                        key: "businessType",
                        // align: "center",
                        align: 'center',       
                        minWidth: 80
                    },
                    {
                        title: "起租日",
                        key: "startDate", 
                        align: 'center',       
                        minWidth: 200
                    },
                    {
                        title: "到期日",
                        key: "endDate",
                        // align: "center",
                        align: 'center',       
                        minWidth: 100
                    },
                    {
                        title: "剩余期限区间",
                        key: "syqxqj",                  
                        align: 'center',       
                        minWidth: 80,
                        
                    },
                     {
                        title: "测算时点逾期天数",
                        key: "cssdyqts",
                       
                    align: 'center',       
                        minWidth: 80,
                         
                    }, 
                    {
                        title: "测算时点未还本金",
                        key: "cssjwhbj",
                       
                    align: 'center',       
                        minWidth: 80,
                         
                    }, 
                    {  title: "剩余租金",
                        key: "shzj",
                      align: 'center',       
                        minWidth: 80
                    },
                    {
                        title: "风险敞口",
                        key: "fxck",
                        align: 'center',       
                        minWidth: 80
                    },{
                        title: "逾期租金",
                        key: "yqzj",
                       align: 'center',       
                        minWidth: 80,
                       
                    }, {
                        title: "总融资额",
                        key: "zrze",
                       align: 'center',       
                        minWidth: 80,
                      
                    }, {
                        title: "租金总额",
                        key: "zjze",
                      align: 'center',       
                        minWidth: 80,
                       
                    },  
                ],
            };
        },
        mounted() {
            this.initDataDic()
        },
        methods: {
            initDataDic() {
                const dicKeys = ['subjectCode']
                getByTypes(dicKeys).then(res => {
                    if (res.code === "0000" && res.data) {
                        this.dataDic = res.data;
                    }
                });
            },
                 //查询按钮操作
            handleSearch() { 
                this.searchForm.pageNumber = 1;
                this.searchForm.pageSize = 10;
                this.queryOrderList(); 
            },
            
            handleReset(){
                this.searchForm.beginTime="";
                this.searchForm.applyNo="";
                this.searchForm.custName="";
                this.searchForm.inputType="";
                this.searchForm.businessType="";
                this.searchForm.endTime=""; 
                this.searchForm.subjectCode="";
                this.searchForm.pageNumber = 1;
                this.searchForm.pageSize = 10;
               
                // 重新加载数据
                this.queryOrderList();
                
            },
            //改变当前页码
            changePage(v) {
                this.searchForm.pageNumber = v;
                this.queryOrderList();
            },
            //改变页码数量
            changePageSize(v) {
                this.searchForm.pageSize = v;
                this.queryOrderList();
            },
                 showSelect(e) {
                this.selectList = e;
                this.selectCount = e.length;
            },      
          
            dropDown() {
                if (this.drop) {
                    this.dropDownContent = "更多筛选";
                    this.dropDownIcon = "ios-arrow-down";
                } else {
                    this.dropDownContent = "收起筛选";
                    this.dropDownIcon = "ios-arrow-up";
                }
                this.drop = !this.drop;
            }, 
            queryOrderList() {
                  let params = {
                      "beginTime":this.searchForm.beginTime[0],
                      "endTime":this.searchForm.beginTime[1],
                      "applyNo":this.searchForm.applyNo,
                      "custName":this.searchForm.custName,
                      "inputType":this.searchForm.inputType,
                      "businessType":this.searchForm.businessType,
                      "applyStatus":this.searchForm.applyStatus,
                      "page":this.searchForm.pageNumber,
                      "size":this.searchForm.pageSize,
                      "dealerNo":this.searchForm.dealerNo,
                      "subjectCode":this.searchForm.subjectCode
                  }
                   
                     try {
                            dealOrderQuery(params).then(res => {
                                    if(res.data){
                                        this.formData= res.data.list;
                                        this.searchForm.total = res.data.total;
                                    }
                                    
                            })
                     } catch (error) {
                        
                     }
               
            },
            queryDealerData(){
                    queryDealerList().then(res=>{
                        // console.log(res);
                        this.dealerData  = res.data.records;
                    })
            },
          
            exportOrderEvent() {
               
                  //no params        
                  let self = this; 
                  //导出所有的数据
                  let params = {
                      "beginTime":this.searchForm.beginTime[0],
                      "endTime":this.searchForm.beginTime[1],
                      "applyNo":this.searchForm.applyNo,
                      "custName":this.searchForm.custName,
                      "inputType":this.searchForm.inputType,
                      "businessType":this.searchForm.businessType,
                      "applyStatus":this.searchForm.applyStatus,
                      "dealerNo":this.searchForm.dealerNo,                  
                      "subjectCode":this.searchForm.subjectCode,
                      "size":-1
                  }
                  
                  try {
                  dealOrderExport(params).then(res => { 
                        if (res.status === 200) {
                            self.afs.downloadFile(res);
                        }
                    
                  })
                  } catch (error) {
                  }
                  
            },
        },
     
      
    };
</script>
