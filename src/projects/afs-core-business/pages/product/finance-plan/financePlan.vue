<template>
    <div>
        <rui-page :defines="defines" v-show="!isShow">
            <rui-query :label-width="150"
                       ref="searchFormPlan"
                       :query="queryData"
                       :query-rows="searchFormOptions"
            />
            <Row>
                <Alert show-icon>
                    已选择 <span class="select-count">{{selectCount}}</span> 项
                    <a class="select-clear" @click="clearSelectAll">清空</a>
                </Alert>
            </Row>
            <rui-table
                :defineId="'defineFinancePlan'"
                :fixedLeft="['action']"
                :showIndex="false"
                :columns="financePlanColumns"
                :slots="[{key:'action',slot:'action'},{key:'status',slot:'status'},{key:'name',slot:'name'}]"
                @loadDatas="queryData"
                @on-selection-change="changeSelect"
                ref-query="searchFormPlan"
                ref="planTable"
            >
                <template slot="toolBar">
                    <Button @click="add" icon="md-add" type="primary">新增</Button>
<!--
                    <Button @click="effective" type="primary">有效</Button>
-->
                    <Button @click="invalid" type="primary">无效</Button>
                    <Button @click="updatePlan" type="primary">修改申请</Button>
                </template>
                <template slot="action" slot-scope="{ row }">
                    <div>
                        <Button @click="edit(row)" size="small" v-if="row.status=='0' " type="primary">编辑</Button>
                        <Button type="error" size="small" v-if="row.status=='0'"  @click="remove(row)">删除</Button>
                        <Button @click="view(row)" size="small" v-if="row.status=='0' ||row.status=='1' ||row.status== '2'||row.status=='3' ||row.status=='4'||row.status=='5'||row.status=='6'" type="primary">查看</Button>
                         <Button @click="revertFlow(row) " size="small" v-if="row.status=='2'" type="primary">撤回</Button>

                        <Button @click="copy(row)" size="small"  type="primary">复制</Button>
                    </div>
                </template>
                <template slot-scope="{ row, index }" slot="status">
                    <div>
                        <tag v-if="row.status=='1'" color="success" size="default">有效</tag>
                        <tag v-if="row.status=='0'" color="error" size="default">草稿</tag>
                        <tag v-if="row.status=='2'" color="error" size="default">审核中</tag>
                        <tag v-if="row.status=='3'" color="error" size="default">被退回</tag>
                        <tag v-if="row.status=='4'" color="error" size="default">无效</tag>
                        <tag v-if="row.status=='5'" color="success" size="default">通过待生效</tag>
                        <tag v-if="row.status=='6'" color="error" size="default">被拒绝</tag>
                    </div>
                </template>
                <template slot-scope="{ row, index }" slot="name">
                    <a @click="view(row)">
                        {{row.name}}
                    </a>
                </template>
            </rui-table>

            <Modal
                v-model="isShowCopy"
                :title="copyTitle"
                :width="650"
                :closable="false"
                :mask-closable="false"
                @keydown.enter.native="saveCopy"
                @on-ok="saveCopy">
                <rui-form :form-options="formOptions"
                          :label-width="150"
                          :read-only="false"
                          ref="showForm">
                </rui-form>
                <div slot="footer">
                    <Button size="small" @click="isShowCopy = false">取消</Button>
                    <Button type="primary" size="small" @click="saveCopy">保存</Button>
                </div>
            </Modal>

        </rui-page>
        <FinancePlanRule @next="close()" ref="sonRefresh" v-if="isShow" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm" @backInfo="backInfo"/>
    </div>
</template>
<script>
    import FinancePlanDefine from "./finance-plan-define";
    import FinancePlanRule from "../finance-plan/financePlanRule.vue";
    import {deepClone} from "@/libs/utils/ObjectClone";
    import {
        getFinancePlanList,
        deletePlan,
        effective,
        invalid,
        copyPlan,
    } from "@/projects/afs-core-business/api/afs-product/product-plan-select/financePlan";
    import {
        deepCopyPlan,revertFlow
    } from "../../../api/afs-product/product/rulePlan";
    export default {
        components: {FinancePlanRule},
        data() {
            return {
                open: "financePlanForm",
                searchFormOptions: [
                    {defineId: "defineFinancePlan", span: 6, fields: ["planNo","name","statusQuery","createTimeQuery","createBy"]},
                ],
                financePlanColumns: [
                    'action', 'planNo','name', 'status',"createBy",'createTime'
                ],
                isShow: false,
                selectList: [],
                selectCount: 0,
                isShowCopy:false,
                copyTitle:"复制方案",
                formOptions: [
                    {
                        isGroup: false,
                        grids: [
                            {defineId: "defineFinancePlan", span: 24, fields: ["name"]},
                        ]
                    }
                ],
                submitLoading: false,

                //规则配置页面
                op:'',
                planId:'',
                fitScope:'006',
                schemeForm:{},
            };
        },
        computed: {
            defines() {
                return [
                    {
                        id: "defineFinancePlan",
                        fields: FinancePlanDefine
                    }
                ];
            }
        },
        methods: {
            queryData(queryData) {
                getFinancePlanList(queryData).then(res => {
                    if (res.code === "0000") {
                        let {records, total} = res.data;
                        this.$refs.planTable.updateTableData(records, total);
                        this.clearSelectAll();
                    }
                });
            },
            revertFlow(row) {
                this.$Modal.confirm({
                    title: "确认撤回流程",
                    content: "确认撤回流程?",
                    onOk: () => {
                        revertFlow(row.id,'006').then(res => {
                            if (res.code === "0000") {
                                this.refresh();
                                this.$Message.success(res.msg);
                            } else {
                                this.$Message.error(res.msg);
                            }
                        });
                    }
                });
            },
                updatePlan(){
                if (this.selectCount <= 0 || this.selectCount == undefined) {
                    this.$Message.warning("您还未选择数据");
                    return;
                }
                if (this.selectCount > 1 ) {
                    this.$Message.warning("请勿选中多条数据");
                    return;
                }
                let selRowData=this.selectList[0];
                if(selRowData.status!=1){
                    this.$Message.warning("当前选中数据非有效状态,不可修改");
                    return;
                }
                this.$Modal.confirm({
                    title: "确认修改申请吗",
                    content: "您确认要修改申请吗?",
                    onOk: () => {
                        //复制当前方案，新方案版本号+1
                        deepCopyPlan(selRowData.id,'006').then(res => {
                            if (res.code === "0000") {
                                this.isShow = !this.isShow;
                                // this.$refs.sonRefresh.refreshCom(row,"edit");
                                this.op='edit';
                                this.planId=res.data.id;
                                this.schemeForm=res.data;
                            }else{
                                this.$Message.warning(res.msg);
                            }
                        });
                    }
                });


            },
            refresh() {
                this.$refs['planTable'].reloadData();
                this.clearSelectAll();
            },
            clearSelectAll(){
                this.$refs.planTable.getTable().selectAll(false);
            },
            effective() {
                if (this.selectCount <= 0) {
                    this.$Message.warning("您还未选择要设置有效的数据");
                    return;
                }
                if(this.check("effective")){return;}
                this.$Modal.confirm({
                    title: "确认设置有效吗",
                    content: "您确认要设置有效所选的 " + this.selectCount + " 条数据?",
                    onOk: () => {
                        let ids = [];
                        this.selectList.forEach(function (e) {
                            ids += e.id + ",";
                        });
                        ids = ids.substring(0, ids.length - 1);
                        effective({ids: ids}).then(res => {
                            if (res.code === "0000") {
                                this.$Message.success("设置有效成功");
                                this.refresh();
                            }
                        });
                    }
                });
            },
            invalid() {
                if (this.selectCount <= 0) {
                    this.$Message.warning("您还未选择要设置无效的数据");
                    return;
                }
                if(this.check("invalid")){return;}
                this.$Modal.confirm({
                    title: "确认设置无效吗",
                    content: "您确认要设置无效所选的 " + this.selectCount + " 条数据?",
                    onOk: () => {
                        let ids = [];
                        this.selectList.forEach((e) => {
                            ids += e.id + ",";
                        });
                        ids = ids.substring(0, ids.length - 1);
                        invalid({ids: ids}).then(res => {
                            if (res.code === "0000") {
                                this.$Message.success("设置无效成功");
                                this.refresh();
                            }
                        });
                    }
                });
            },
            check(type){
                let invalidNum = 0;
                let effectiveNum = 0;
                this.selectList.forEach((e) => {
                    if(e.status!=1){//无效
                        invalidNum++;
                    }
                    if(e.status==1){//有效
                        effectiveNum++;
                    }
                });
                var checkResult = false;
                if("invalid"==type&&invalidNum>0){
                    this.$Message.warning("不可设置非有效数据为无效！");
                    checkResult = true;
                }
                if("effective"==type&&effectiveNum>0){
                    this.$Message.warning("不可设置有效数据为有效！");
                    checkResult = true;
                }
                return checkResult;
            },
            changeSelect(e) {
                this.selectList = e;
                this.selectCount = e.length;
            },
            close() {
                this.isShow = !this.isShow;
                this.refresh();
            },
            add() {
                this.isShow = !this.isShow;
                // this.$refs.sonRefresh.refreshCom(null,"add");
                this.op='add';
                this.planId=null;
                this.schemeForm={
                    planNo:null,
                    name:null,
                };
            },
            edit(row){
                this.isShow = !this.isShow;
                // this.$refs.sonRefresh.refreshCom(row,"edit");
                this.op='edit';
                this.planId=row.id;
                this.schemeForm=row;
            },
            view(row){
                this.isShow = !this.isShow;
                // this.$refs.sonRefresh.refreshCom(row,"view");
                this.op='view';
                this.planId=row.id;
                this.schemeForm=row;
            },
            remove(row){
                this.$Modal.confirm({
                    title: "确认删除",
                    content: "确认要删除方案，名称: " + row.name + " ?",
                    onOk: () => {
                        deletePlan(row.id).then(res => {
                            if (res.code === "0000") {
                                this.refresh();
                                this.$Message.success(res.msg);
                            }else{
                                this.$Message.error(res.msg);
                            }
                        });
                    }
                });
            },
            copy(row){
                this.isShowCopy = true;
                this.$refs.showForm.resetFrom();
                this.$refs.showForm.setFieldRequired('name', true);
                this.$refs.showForm.updateFormData(deepClone(row));
            },
            saveCopy(){
                this.submitLoading = true;
                this.$refs.showForm.getForm().validate((valid) => {
                    if (valid) {
                        copyPlan(this.$refs.showForm.getFormData()).then(res => {
                            this.submitLoading = false;
                            if (res.code === "0000") {
                                this.$Message.success("复制成功");
                                this.isShowCopy = false;
                                this.refresh();
                            }
                        }).catch(() => {
                            this.submitLoading = false;
                        });
                    }
                });
            },
            backInfo(val){
                console.info("回调值："+JSON.stringify(val));
                this.op='edit';
                this.planId=val.id;
                this.schemeForm=val;
            },
        },
        mounted() {
            this.refresh();
        }
    };
</script>
