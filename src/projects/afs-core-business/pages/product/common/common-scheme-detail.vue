<!--
    方案详情页面 公共插件
    参数：
    planId          方案ID
    fitScope        方案场景
 -->
<template>
    <div v-if="isShow">
        <riskPlanRule @next="close()" v-if="fitScope=='005'" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm"/>
        <launchPlanRule @next="close()" v-else-if="fitScope=='004'" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm" />
        <financePlanRule @next="close()" v-else-if="fitScope=='006'" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm"/>
        <extrasProgramConfig @next="close" v-else-if="fitScope=='003'" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm"/>
        <discountPlanRule @next="close()" v-else-if="fitScope=='002'" :op="op" :planId="planId" :fitScope="fitScope" :schemeForm="schemeForm" />
        <discountConfig :param="schemeForm" :op="op" v-else-if="fitScope=='discount'" @saveSuccess="close"/>
    </div>
</template>
<script>

    import {
        querySchemePlan,
    } from "@/projects/afs-core-business/api/afs-product/common/common";

    import riskPlanRule from "@/projects/afs-core-business/pages/product/risk-plan/riskPlanRule.vue";
    import launchPlanRule from "@/projects/afs-core-business/pages/product/launch-plan/launchPlanRule.vue";
    import financePlanRule from "@/projects/afs-core-business/pages/product/finance-plan/financePlanRule.vue";
    import extrasProgramConfig from "@/projects/afs-core-business/pages/product/extras-program/extrasProgramConfig.vue";
    import discountPlanRule from "@/projects/afs-core-business/pages/product/discount-plan/discountRulePlanDetail.vue";
    import discountConfig from "@/projects/afs-core-business/pages/product/discount-plan/discountPlanConfig.vue";

    export default {
        components: {
            riskPlanRule,
            launchPlanRule,
            financePlanRule,
            extrasProgramConfig,
            discountPlanRule,
            discountConfig,
        },
        data() {
            return {
                //方案ID
                planId:this.afs.getPageParams(this).planId,
                //方案场景
                fitScope:this.afs.getPageParams(this).fitScope,
                //表单
                schemeForm:{
                    planNo:null,
                    name:null,
                },
                //状态
                op:"view",
                //是否展示
                isShow:false,
            };
        },
        methods: {
            //刷新
            init(){
                // console.info(this.afs.getPageParams(this).planId)
                // console.info(this.afs.getPageParams(this).fitScope)
                querySchemePlan({planId:this.afs.getPageParams(this).planId,fitScope:this.afs.getPageParams(this).fitScope}).then(res => {
                    if (res.code === "0000") {
                        this.schemeForm = res.data;
                        this.isShow = true;
                    }
                });
            },
            //返回
            close(){
                this.afs.closeTab(this);
            },
        },
        mounted() {
            this.init();
        },
    };
</script>
