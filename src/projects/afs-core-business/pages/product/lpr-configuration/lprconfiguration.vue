<template>
    <rui-page :defines="defines">
        <rui-query :label-width="150"
                   ref="searchForm"
                   :query="queryData"
                   :query-rows="searchFormOptions"
        />
        <rui-table :defineId="'defineLprTable'"
                   :columns="['action','lprDate','lprRate','lprVariety','systemEffectiveTime','applicationPeriodStart','applicationPeriodEnd','status']"
                   :fixedLeft="['action']"
                   :showIndex="false"
                   :select="false"
                   :slots="[{key:'action',slot:'action'},{key:'status',slot:'status'}]"
                   @loadDatas="queryData"
                   ref-query="searchForm"
                   ref="lprTable">
            <template slot="toolBar">
                <Button @click="add" type="primary" icon="md-add">新增</Button>
            </template>
            <template slot="action" slot-scope="{ row }">
                <div>
                    <Button @click="edit(row)" size="small" v-if="row.status=='0'" type="primary">编辑</Button>
                    <Button type="error" size="small" v-if="row.status=='0'" @click="remove(row)">删除</Button>
                </div>
            </template>
            <template slot-scope="{ row, index }" slot="status">
                <div>
                    <tag v-if="row.status=='1'" color="success" size="default">有效</tag>
                    <tag v-if="row.status=='0'" color="error" size="default">无效</tag>
                </div>
            </template>
        </rui-table>
        <Modal v-model="lprModalVisible"
               :title="modalTitle"
               :width="900"
               :closable="false"
               :mask-closable="false"
               @on-ok="lprSubmit">
            <rui-form :form-options="lprFormOptions"
                      :read-only="false"
                      ref="lprForm">
            </rui-form>
            <div slot="footer">
                <Button size="small" @click="lprModalVisible = false">取消</Button>
                <Button type="primary" size="small"  :loading="submitLoading"  @click="lprSubmit">保存</Button>
            </div>
        </Modal>

        <!-- 当前生效的LPR -->
        <br/>
        <br/>
        <Row>
            <Alert>
                <h2>贷款市场报价利率(LPR)</h2>
            </Alert>
        </Row>
        <Table :columns="columns1" :data="data1"></Table>

    </rui-page>
</template>
<script>
    import defineLpr from "./lpr-define";
    import defineLprTable from "./lpr-table-define";
    import {
        getLprListData,
        save,
        deletelpr,
        getNewestLpr,
    } from "@/projects/afs-core-business/api/afs-product/product/lpr";
    import {
        deepClone
    } from "@/libs/utils/ObjectClone";

    export default {
        name: "lpr-configuration",
        data() {
            return {
                lprModalVisible: false,
                submitLoading:false,
                modalTitle: 'LPR配置信息新增',
                searchFormOptions: [
                    {defineId: 'defineLpr', span: 6, fields: ['lprDates', 'systemEffectiveTimes', 'lprVariety','status']},
                ],
                lprFormOptions: [
                    {
                        grids: [
                            {defineId: 'defineLpr', span: 12, fields: ['lprDate', 'lprRate', 'lprVariety']},
                            {defineId: 'defineLpr', span: 12, fields: ['systemEffectiveTime', 'applicationPeriodStart', 'applicationPeriodEnd']
                            },
                        ],
                    }
                ],
                //当前生效的LPR
                columns1: [
                    {
                        title: '期限',
                        key: 'lprVariety'
                    },
                    {
                        title: 'LPR(%)',
                        key: 'lprRate'
                    },
                    {
                        title: '时间',
                        key: 'lprDate'
                    }
                ],
                data1: [],

            }
        },
        computed: {
            defines() {
                return [
                    {
                        id: 'defineLpr',
                        fields: defineLpr
                    },
                    {
                        id: 'defineLprTable',
                        fields: defineLprTable
                    }
                ]
            }
        },
        methods: {
            queryData(queryData) {
                getLprListData(queryData).then(res => {
                    if (res.code === "0000") {
                        let {records, total} = res.data;
                        this.$refs.lprTable.updateTableData(records, total);
                    }
                });
                getNewestLpr().then(res => {
                    if (res.code === "0000") {

                        console.info(JSON.parse(JSON.stringify(res.data)))

                        this.data1 = res.data;
                    }
                });
            },
            refresh() {
                this.$refs['lprTable'].reloadData();
            },
            lprSubmit() {
                this.$refs.lprForm.getForm().validate((valid) => {
                    if (valid) {
                        if (this.modalType === 0) {
                            this.submitLoading = true;
                            save(this.$refs.lprForm.getFormData()).then(res => {
                                this.submitLoading = false;
                                if (res.code === "0000") {
                                    this.$Message.success("操作成功");
                                    this.refresh();
                                    this.lprModalVisible = false;
                                }
                            }).catch(() => {
                                this.submitLoading = false;
                                this.refresh();
                            });
                        } else {
                            // 编辑
                            this.submitLoading = true;
                            save(this.$refs.lprForm.getFormData()).then(res => {
                                this.submitLoading = false;
                                if (res.code === "0000") {
                                    this.$Message.success("操作成功");
                                    this.refresh();
                                    this.lprModalVisible = false;
                                }
                            }).catch(() => {
                                this.submitLoading = false;
                                this.refresh();
                            });
                        }
                    }
                });
            },
            remove(v) {
                this.$Modal.confirm({
                    title: "确认删除",
                    content: "是否确认删除？",
                    onOk: () => {
                        deletelpr(v.id).then(res => {
                            if (res.code === "0000") {
                                this.$Message.success("删除成功");
                                this.refresh();
                            }
                        });
                    }
                });
            },
            add() {
                this.modalType = 0;
                this.modalTitle = "新增";
                this.$refs.lprForm.setAllFieldRequired(true);
                let self = this;
                this.$nextTick(() => {
                    this.$refs.lprForm.resetFrom();
                    this.lprModalVisible = true;
                })
            },
            edit(row) {
                this.modalType = 1;
                this.modalTitle = "编辑";
                this.$refs.lprForm.resetFrom();
                this.$refs.lprForm.updateFormData(deepClone(row));
                this.$refs.lprForm.setFiledValue('lprDates', row.lprDate);
                this.$refs.lprForm.setFiledValue('systemEffectiveTimes', row.systemEffectiveTime);
                this.lprModalVisible = true;
                this.$refs.lprForm.setAllFieldRequired(true);
            },
        },
        mounted() {
            this.refresh();

        }
    };
</script>
