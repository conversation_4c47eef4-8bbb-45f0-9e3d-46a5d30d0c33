<template>
    <div>
        <div style="text-align:center;" class="common_br">
            <Button type="primary" style="width:100%;" @click="recordClick" ghost>
                <h3>审批进度（点击查看详情）</h3>
            </Button>
            <span>
                <span v-if="approveRecordData.length>0">
                    <Table :data="approveRecordData" :columns="approveRecordColumns" :show-header="false" size="small" stripe></Table>
                </span>
            </span>
        </div>
        <card>
            <div style="width:100%;overflow:hidden">
                <div style="display:inline-block;float:left;width:49%;">
                    <Button type="primary" @click="submitBtn">提交</Button>
                    <Button type="primary" @click="backBtn">返回</Button>&nbsp;
                    <Button type="primary" @click="backToAudit">退回初审</Button>
                </div>
                <div style="display:inline-block;float:left;width:49%;text-align:right">
                    <Button type="primary" @click="pauseBtn">暂停</Button>&nbsp;
                    <Button type="primary" @click="reviewBtn">已复核</Button>
                </div>
            </div>
        </card>
        <div>
            <card>
                <Tabs value="name1" type="card" :animated="true" @on-click="tabOnClick">
                    <TabPane label="审核页面" name="name1" :linkOption="{component:'_p/afs-core-business/afs-case/pages/approve/loan-audit/loanReviewDetail',isFull:true,params:{bb:'bb'},pageTitle:'审核页面'}">
                        <organ-loan-Audit-Detail ref="loanReviewDetail" :detailParams="detailParams"></organ-loan-Audit-Detail>
                    </TabPane>
                    <TabPane label="影像资料" name="name2" :linkOption="{component:'projects/basic/pages/image/file-audit/organCreateFileAudit',isFull:true,params:{auditParam:this.auditParam,operate:this.operate,businessDic:this.businessDic},pageTitle:this.detailParams.contractNo}">
                        <div style="height: calc(100vh - 250px);">
                            <organ-file-audit ref="fileAudit" :auditParam="auditParam" :operate="operate" :businessDic="businessDic" :detailParams="detailParams"></organ-file-audit>
                        </div>
                    </TabPane>
                    <TabPane label="VIN码解析" name="name3" v-if="detailParams.businessType!='02'" :linkOption="{component:'projects/afs-core-business/pages/case/approve/loan-audit/loanAuditVinLinkOption',isFull:true,params:this.detailParams}">
                        <div v-show="tabName === 'name3'" style="height: calc(100vh - 250px);">
                            <organ-loan-Audit-Vin ref="loanAuditVin" :detailParams="detailParams"></organ-loan-Audit-Vin>
                        </div>
                    </TabPane>
                </Tabs>
            </card>
        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>
        <Modal v-model="backToPartnersModalVisible" :title="modalTitle" :closable="false" :mask-closable='false' :width="1000" :styles="{top: '180px'}">
            <div slot="footer">
                <Button type="text" @click="backToPartnersModalVisible = false">取消</Button>
                <Button type="primary" @click="submitBackToPartners">提交</Button>
            </div>
            <div>
                <Form ref="formReview" :label-width="90" inline>
                    <FormItem label="退回原因:"><Input type="textarea" style="width:500px" v-model="backReason" /></FormItem>
                </Form>
            </div>
        </Modal>
        <Modal v-model="saveSubmitModalVisible" :closable="false" :mask-closable='false' :width="200" :styles="{top:'180px'}">
            <div>
                <span>{{this.saveSubmitData+"，是否通过？"}}</span>
            </div>
            <div slot="footer">
                <Button type="text" @click="saveSubmitModalVisible = false">否</Button>
                <Button type="primary" @click="getPassLastDate">是</Button>
            </div>
        </Modal>
        <Modal v-model="passLastDataModalVisible" :closable="false" :mask-closable='false' :width="200" :styles="{top:'180px'}">
            <div>
                <span>{{this.passLastData+"，是否通过？"}}</span>
            </div>
            <div slot="footer">
                <Button type="text" @click="passLastDataModalVisible = false">否</Button>
                <Button type="primary" @click="saveSubmit">是</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
    import organLoanAuditDetail from "./organLoanAuditDetail";
    import organLoanAuditVin from './organLoanAuditVin'
    import organFileAudit from "@/projects/basic/pages/image/file-audit/organFileAudit"
    import {
        getApproveRecord,
        reviewSubmit,
        backToAudit,
        getFileStatus,
        getSingledealer,
        getPassLastDate
    } from '_p/afs-core-business/api/organ/loan-approve/organLoanReview'
    export default {
        name: 'organLoanReviewView',
        components: {
            organLoanAuditDetail,organLoanAuditVin,organFileAudit
        },
        props: {
            detailParams: {},
        },
        data() {
            return {
                spinShow:false,
                operate:false,
                modalTitle: '',
                saveSubmitData:'',
                passLastData:'',
                backToPartnersModalVisible: false,
                saveSubmitModalVisible: false,
                passLastDataModalVisible: false,
                backReason: '',
                allRecordData:[],
                approveRecordData: [],
                auditParam:{busiNo:this.detailParams.contractNo,
                    belongNo:this.detailParams.contractNo,
                    busiNode:"loanOrgan"},
                businessDic:[{value:"loanOrgan",title:"放款审核"},
                ],
                approveRecordColumns: [
                    {
                        title: '流程节点',
                        key: 'disposeNodeName',
                        render: (h, params) => {
                            return h('span', '流程节点:' + (params.row.disposeNodeName || ''))
                        }
                    },
                    {
                        title: '处理人员',
                        key: 'disposeStaff',
                        render: (h, params) => {
                            return h('span', '处理人员:' + (params.row.disposeStaff || ''))
                        }
                    },
                    {
                        title: '处理命令',
                        key: 'approveSuggest',
                        render: (h, params) => {
                            return h('span', '处理命令:' + (params.row.approveSuggest || ''))
                        }
                    },
                    {
                        title: '处理时间',
                        key: 'approveEndTime',
                        render: (h, params) => {
                            return h('span', '处理时间:' + (params.row.approveEndTime || ''))
                        }
                    },
                    {
                        title: '处理意见',
                        key: 'approveMessage',
                        render: (h, params) => {
                            let remark=params.row.approveRemark===undefined?'':params.row.approveRemark;
                            let texts='处理意见:'+remark;
                            return h('div',[
                                h('Tooltip',{
                                    props: {
                                        placement: 'top-start',
                                        transfer: true
                                    }},[texts, h('span', {
                                    slot: 'content',
                                    style: {
                                        whiteSpace: 'normal'
                                    }
                                }, remark)
                                ])
                            ])
                        }
                    }
                ]
            }
        },
        mounted() {
            this.refresh()
        },
        methods: {
            refresh() {
                getApproveRecord(this.detailParams).then(res => {
                    if (res.code === '0000') {
                        this.approveRecordData = res.data;
                    }
                })
            },
            recordClick() {
                this.recordDrop = !this.recordDrop
                this.setApproveRecord(this.recordDrop);
            },
            setApproveRecord(val){
                if(!val){
                    let records=[];
                    for(let i=0;i<this.allRecordData.length;i++){
                        if(i==this.allRecordData.length-1 || i==this.allRecordData.length-2){
                            records.push(this.allRecordData[i]);
                        }
                    }
                    this.approveRecordData=records;
                }else{
                    this.approveRecordData=this.allRecordData;
                }
            },
            tabOnClick(name) {
                if (name && name === 'name3') {
                    this.$refs.loanAuditVin.summaryBtn();
                    //document.getElementsByTagName('DynamicLink')[0].click
                }
            },
            backToPartners() {
                this.modalTitle = '退回合作商'
                this.backToPartnersModalVisible = true
            },
            submitBackToPartners() {},
            reviewBtn() {},
            backBtn() {
                this.$emit('backGo')
            },
            pauseBtn() {},
            submitBtn() {
                if(this.detailParams.isLock ==='no'|| this.detailParams.isLock ===undefined || this.detailParams.isLock ===''){
                    this.detailParams.busiNo = this.detailParams.applyNo;
                    this.spinShow = true;
                    getFileStatus(this.detailParams).then(res =>{
                        if(res.data ===true){
                            console.log(this.detailParams)
                            getSingledealer(this.detailParams).then(res =>{
                                this.spinShow = false;
                                if(res.data ===true){
                                    this.getPassLastDate();
                                }else {
                                    this.saveSubmitData =res.data;
                                    this.saveSubmitModalVisible =true;
                                }
                            })
                        }else{
                            this.$Message.info("存在审核未通过的影像资料，不可提交！")
                        }
                    })
                }else {
                    this.$Message.info("该笔放款申请暂停放款，不可提交！")
                }
            },
            getPassLastDate(){
                getPassLastDate(this.detailParams).then(res =>{
                    if(res.data ===true){
                        this.saveSubmit();
                    }else{
                        this.passLastData =res.data;
                        this.passLastDataModalVisible =true;
                    }
                })
            },
            saveSubmit() {
                this.saveSubmitModalVisible =false;
                reviewSubmit(this.detailParams).then(res => {
                    if (res.code === '0000') {
                        this.$Message.success('提交成功')
                        this.backToPartnersModalVisible = false
                        this.$emit('backGo')
                    }
                })
            },
            backToAudit() {
                backToAudit(this.detailParams).then(res => {
                    if (res.code === '0000') {
                        this.$Message.success('提交成功')
                        this.backToPartnersModalVisible = false
                        this.$emit('backGo')
                    }
                })
            }
        }
    }
</script>
