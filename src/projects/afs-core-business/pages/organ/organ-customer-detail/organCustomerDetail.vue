<style lang="less">
    @import "organCustomerDetail.less";
</style>
<template>
    <div class="customer">
        <Card style="width:100%" >
            <Collapse v-model="values" >
                <Panel name="1">
                    主借人信息
                    <div slot="content">
                        <Form ref="customInfoForm" :model="customInfoForm" :label-width="130" :disabled="true">
                            <div>
                                <div style="padding:0 44px">
                                    <div>
                                        <h2 class="levellow_title">基本信息</h2>
                                        <div style="margin-top: -10px">
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="机构名称" prop="organName">
                                                        <Input v-model="customInfoForm.organName" :disabled="true" style="width: 250px"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="公司性质" prop="companyNature">
                                                        <Select v-model="customInfoForm.companyNature" :disabled="true">
                                                            <Option :value="item.value" v-for="(item,index) in dataDic.companyNature" :key="index">{{item.title}}</Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="行业性质" prop="industryType">
                                                        <Select v-model="customInfoForm.industryType" :disabled="true">
                                                            <Option :value="item.value" v-for="(item,index) in dataDic.industryType" :key="index">{{item.title}}</Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="社会统一信用代码" prop="socUniCrtCode">
                                                        <Input v-model="customInfoForm.socUniCrtCode" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="中征码" prop="middleSignCode">
                                                        <Input v-model="customInfoForm.middleSignCode" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="成立时间" prop="establishmentDate">
                                                        <Input v-model="customInfoForm.establishmentDate" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="营业执照到期时间" prop="expiringDate">
                                                        <DatePicker type="date" v-model="customInfoForm.expiringDate" disabled ></DatePicker>
                                                    </FormItem>
                                                    <FormItem prop="longTerm" :label-width="0" style="position: absolute;left:340px;width:60px;top:0px">
                                                        <Checkbox v-model="customInfoForm.longTerm" :disabled="true"> 长期</Checkbox>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="注册资本" prop="registeredCapital">
                                                        <Input v-model="customInfoForm.registeredCapital" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="营业范围" prop="businessScope">
                                                        <Input v-model="customInfoForm.businessScope" disabled/>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="年收入" prop="annualIncome">
                                                        <Input v-model="customInfoForm.annualIncome" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="员工人数" prop="employeesNumber">
                                                        <Input v-model="customInfoForm.employeesNumber" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="营业地址-详情" prop="businessAddress">
                                                        <Input v-model="customInfoForm.businessAddress" :disabled="true" style="width: 250px"/>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="levellow_title">高管信息</h2>
                                        <div style="margin-top: -10px">
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="高管姓名" prop="executivesName">
                                                        <Input v-model="customInfoForm.executivesName" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="身份证号" prop="executivesCertNo">
                                                        <Input v-model="customInfoForm.executivesCertNo" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="证件到期日" prop="executivesExpiringDate" class="inline-block">
                                                        <DatePicker type="date" v-model="customInfoForm.executivesExpiringDate" :disabled="true" format="yyyy-MM-dd">
                                                        </DatePicker>
                                                        <FormItem prop="isLongTerm" :label-width="0" style="position: absolute;left:210px;width:60px;top:0px">
                                                            <Checkbox v-model="customInfoForm.isLongTerm" :disabled="true"> 长期</Checkbox>
                                                        </FormItem>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col span="6">
                                                    <FormItem label="联系方式" prop="executivesContactNum">
                                                        <Input v-model="customInfoForm.executivesContactNum" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="公司职位" prop="executivesPosition">
                                                        <Select v-model="customInfoForm.executivesPosition" :disabled="true">
                                                            <Option :value="item.value" v-for="(item,index) in dataDic.companyPosition" :key="index">{{item.title}}</Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                                <Col span="6">
                                                    <FormItem label="家庭地址详情" prop="executivesHomeAddress">
                                                        <Input v-model="customInfoForm.executivesHomeAddress" style="width: 250px" :disabled="true"></Input>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Form>
                    </div>
                </Panel>
                <Panel name="2">
                    个人股东信息
                    <div slot="content">
                        <Row style="margin-top: -5px">
                            <Table border  :columns="personalStockColumns" :data="personalStockList">
                                <template slot-scope="{ row,index }" slot="certType">
                                    <span>{{setCertTypeTitle(row.certType)}}</span>
                                </template>
                                <template slot-scope="{ row,index}" slot="nationality">
                                    <span>{{setNationalityTitle(row.nationality)}}</span>
                                </template>
                                <template slot-scope="{ row,index}" slot="isLongTerm">
                                    <span>{{setIsLongTermTitle(row.isLongTerm)}}</span>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
                <Panel name="3">
                    法人股东信息
                    <div slot="content">
                        <Row style="margin-top: -5px">
                            <Table border  :columns="legalStockColumns" :data="legalStockList">
                                <template slot-scope="{ row,index }" slot="certType">
                                    <span>{{setCertTypeTitle(row.certType)}}</span>
                                </template>
                                <template slot-scope="{ row,index}" slot="isLongTerm">
                                    <span>{{setIsLongTermTitle(row.isLongTerm)}}</span>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
                <Panel name="4">
                    个人担保人
                    <div slot="content">
                        <Row style="margin-top: -5px">
                            <Table border  :columns="personalColumns" :data="personalGuarantorList">
                                <template slot-scope="{ row,index }" slot="sex">
                                    <span>{{setSexTitle(row.sex)}}</span>
                                </template>
                                <template slot-scope="{ row,index}" slot="certType">
                                    <span>{{setCertTypeTitle(row.certType)}}</span>
                                </template>
                                <template slot-scope="{ row,index }" slot="maritalStatus">
                                    <span>{{setMaritalStatusTitle(row.maritalStatus)}}</span>
                                </template>
                                <template slot-scope="{ row,index }" slot="association">
                                    <span>{{setAssociationTitle(row.association)}}</span>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
                <Panel name="5">
                    法人担保人
                    <div slot="content">
                        <Row style="margin-top: -5px">
                            <Table border  :columns="legalColumns" :data="legalCustInfoList">
                                <template slot-scope="{ row,index}" slot="industryType">
                                    <span>{{setIndustryTypeTitle(row.industryType)}}</span>
                                </template>
                                <template slot-scope="{ row, index }" slot="holderData">
                                    <Button type="default" size="default" @click.native="showHolderData(row)"
                                            style="color: #2d8cf0;border: none">
                                        详情
                                    </Button>
                                </template>
                            </Table>
                        </Row>
                    </div>
                </Panel>
                <Modal title="股东信息" v-model="viewHolder"
                       footer-hide
                       draggable
                       :width="620"
                       :styles="{top: '80px'}"
                >
                    <div style="max-height:500px;overflow-y:auto;overflow-x:hidden;">
                        <Form ref="legalHolderAndExecutives" :model="legalHolderAndExecutives" disabled :label-width="120">
                        </Form>
                    </div>
                </Modal>
                <Modal title="高管信息" v-model="viewExecutives"
                       footer-hide
                       draggable
                       :width="620"
                       :styles="{top: '80px'}"
                >
                    <div style="max-height:500px;overflow-y:auto;overflow-x:hidden;">
                        <Form ref="legalHolderAndExecutives" :model="legalHolderAndExecutives" disabled :label-width="120">
                            <Row>
                                <Col span="11">
                                    <FormItem label="高管名称" prop="executivesName">
                                        <Input style="width: 160px" v-model="legalHolderAndExecutives.executivesName" />
                                    </FormItem>
                                </Col>
                                <Col span="11">
                                    <FormItem label="证件号码" prop="executivesCertNo">
                                        <Input style="width: 160px" v-model="legalHolderAndExecutives.executivesCertNo" />
                                    </FormItem>
                                </Col>
                                <Col span="11">
                                    <FormItem label="证件到期日" prop="executivesExpiringDate">
                                        <DatePicker type="date"  v-model="legalHolderAndExecutives.executivesExpiringDate"disabled ></DatePicker>
                                    </FormItem>
                                </Col>
                                <Col span="11">
                                    <FormItem label="联系方式" prop="executivesContactNum">
                                        <Input style="width: 160px" v-model="legalHolderAndExecutives.executivesContactNum" />
                                    </FormItem>
                                </Col>
                                <Col span="11">
                                    <FormItem label="本公司从事职位" prop="executivesPosition">
                                        <Input style="width: 160px" v-model="legalHolderAndExecutives.executivesPosition" />
                                    </FormItem>
                                </Col>
                                <Col span="11">
                                    <FormItem label="家庭地址详细" prop="executivesHomeAddress">
                                        <Input style="width: 200px" v-model="legalHolderAndExecutives.executivesHomeAddress" />
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </div>
                </Modal>
            </Collapse>
            <div>
                <!--法人股东信息-->
                <guarantor-holder-info
                    :isShowHolder="showInfo"
                    :is-show-details="true"
                    ref="GuarantorHolder"
                    :dataList="dataList"
                    :custId="custId"
                    @close-model="closeModel"
                    :legalId="legalId"
                    :applyNo="applyNo"
                    :details="details"
                ></guarantor-holder-info>
            </div>
        </Card>

    </div>
</template>

<script>
    import {
        queryOrganCustBaseInfo,
        queryHolderOrExecutivesInfo
    } from "@/projects/afs-core-business/api/organ/order-mng/customInfo";
    import GuarantorHolderInfo from "../organ-customer-detail/holderInfo";
    import {
        getByTypes,
        getDictDataByType
    } from "_p/basic/api/admin/datadic";
    import {deepClone} from "@/libs/utils/ObjectClone";
    export default {
        name: "organ-customer-detail",
        components: {
            GuarantorHolderInfo
        },
        props: {
            applyNo: {
                required: false,
                default:()=>{
                    return '';
                }
            },
            isCheck: {
                type: Boolean
            },
            isAppear: {
                type: Boolean
            },
            tabName:{
                type:String
            },
        },
        watch:{
            tabName(val){
                if(val==='name2'){
                    this.init();
                }
            }
        },
        data: function () {
            return {
                values:["1","2","3","4","5","6","7","8"],
                viewExecutives:false,
                viewHolder:false,
                //法人担保人股东和高管
                legalHolderAndExecutives:{
                },
                stageId:"",
                dicKeys: ["houseType", "addressType", "companyPosition", "jobsType", "industryType", "unitNature", "drivingType",
                    "maritalStatus", "nationality", "highestEducation", "sex", "certType","companyNature","association","isLongTerm"],
                dataDic: {},
                // 主借人
                customInfoForm: {
                    isLongTerm: false,
                    longTerm: false
                },
                //法人担保人
                legalCustInfoList:[],
                //个人担保人
                personalGuarantorList:[],
                //个人股东
                personalStockList:[],
                //法人股东
                legalStockList:[],
                personalStockColumns: [
                    {
                        title:'股东名称',
                        key:'stockName',
                        align:"center",
                        minWidth: 180,
                        tooltip:true
                    },
                    {
                        title: '国籍',
                        key: 'nationality',
                        align:"center",
                        minWidth: 180,
                        slot: 'nationality'
                    },
                    {
                        title: '证件类型',
                        key: 'certType',
                        align:"center",
                        minWidth: 200,
                        tooltip:true,
                        slot: 'certType'
                    },
                    {
                        title: '证件号码',
                        key: 'certNo',
                        align:"center",
                        minWidth: 200
                    },
                    {
                        title: '证件到期日',
                        key: 'expiringDate',
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: '是否长期',
                        key: 'isLongTerm',
                        align:"center",
                        minWidth: 150,
                        slot: 'isLongTerm'
                    },
                    {
                        title: '家庭地址详情',
                        key: 'homeAddress',
                        align:"center",
                        minWidth: 150,
                        tooltip:true
                    },
                    {
                        title: "联系方式",
                        key: "contactNumber",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "投资金额(元)",
                        key: "investmentAmount",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "占股比例(%)",
                        key: "shareRatio",
                        align:"center",
                        minWidth: 150
                    }
                ],
                legalStockColumns: [
                    {
                        title:'股东名称',
                        key:'stockName',
                        align:"center",
                        minWidth: 180,
                        tooltip:true
                    },
                    {
                        title: '社会统一信用代码',
                        key: 'socUniCrtCode',
                        align:"center",
                        minWidth: 180
                    },
                    {
                        title: '证件类型',
                        key: 'certType',
                        align:"center",
                        minWidth: 200,
                        tooltip:true,
                        slot: 'certType'
                    },
                    {
                        title: '证件号码',
                        key: 'certNo',
                        align:"center",
                        minWidth: 200
                    },
                    {
                        title: '证件到期日',
                        key: 'expiringDate',
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: '是否长期',
                        key: 'isLongTerm',
                        align:"center",
                        minWidth: 150,
                        slot: 'isLongTerm'
                    },
                    {
                        title: '注册地址详情',
                        key: 'registeredAddress',
                        align:"center",
                        minWidth: 150,
                        tooltip:true
                    },
                    {
                        title: "联系方式",
                        key: "contactNumber",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "投资金额(元)",
                        key: "investmentAmount",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "占股比例(%)",
                        key: "shareRatio",
                        align:"center",
                        minWidth: 150
                    }
                ],
                personalColumns: [
                    {
                        title:'担保人名称',
                        key:'staffName',
                        align:"center",
                        minWidth: 180,
                        tooltip:true
                    },
                    {
                        title: '性别',
                        key: 'sex',
                        align:"center",
                        minWidth: 100,
                        slot: 'sex'
                    },
                    {
                        title: '出生日期',
                        key: 'birthDate',
                        align:"center",
                        minWidth: 180
                    },
                    {
                        title: '年龄',
                        key: 'personalAge',
                        align:"center",
                        minWidth: 100
                    },
                    {
                        title: '证件类型',
                        key: 'certType',
                        align:"center",
                        minWidth: 200,
                        tooltip:true,
                        slot: 'certType'
                    },
                    {
                        title: '证件号码',
                        key: 'certNo',
                        align:"center",
                        minWidth: 250
                    },
                    {
                        title: '婚姻状况',
                        key: 'maritalStatus',
                        align:"center",
                        minWidth: 150,
                        slot: 'maritalStatus'
                    },
                    {
                        title: '职业信息',
                        key: 'careerInformation',
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: '现地址详情',
                        key: 'livingAddress',
                        align:"center",
                        minWidth: 150,
                        tooltip:true
                    },
                    {
                        title: "联系方式",
                        key: "contactNumber",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "月收入",
                        key: "monthlyIncome",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "担保人个人资产情况",
                        key: "personalAssets",
                        align:"center",
                        minWidth: 150,
                        tooltip:true
                    },
                    {
                        title: "担保人和企业对关联关系",
                        key: "association",
                        align:"center",
                        minWidth: 150,
                        tooltip:true,
                        slot: 'association'
                    }
                ],
                legalColumns: [
                    {
                        title:'公司名称',
                        key:'companyName',
                        align:"center",
                        minWidth: 180,
                        tooltip:true
                    },
                    {
                        title:'股东信息',
                        key:'holderData',
                        align:"center",
                        minWidth: 180,
                        slot:'holderData'
                    },
                    {
                        title: '营业范围',
                        key: 'businessScope',
                        align:"center",
                        minWidth: 180
                    },
                    {
                        title: '行业类型',
                        key: 'industryType',
                        align:"center",
                        minWidth: 180,
                        slot: 'industryType'
                    },
                    {
                        title: '联系人',
                        key: 'contactName',
                        align:"center",
                        minWidth: 150
                    },

                    {
                        title: '联系方式',
                        key: 'contactNumber',
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "投资金额(元)",
                        key: "investmentAmount",
                        align:"center",
                        minWidth: 150
                    },
                    {
                        title: "占股比例(%)",
                        key: "shareRatio",
                        align:"center",
                        minWidth: 150
                    }
                ],
                showInfo:false,
                legalId:'',
                custId:'',
                details:'',
                dataList:{}

            }
        },
        methods: {
            init() {
                this.initDataDic();
                this.getCustBaseList();
            },
            getCustBaseList() {
                queryOrganCustBaseInfo(this.applyNo).then(res => {
                    if (res.code === "0000") {
                        this.customInfoForm = res.data.organCustInfo;
                        if (res.data.organCustInfo.isLongTerm == 'yes') {
                            this.customInfoForm.isLongTerm = true;
                        }
                        if (res.data.organCustInfo.longTerm == 'yes') {
                            this.customInfoForm.longTerm = true;
                        }
                        //法人担保人
                        this.legalCustInfoList=res.data.legalCustInfoList,
                        //个人担保人
                        this.personalGuarantorList=res.data.personalGuarantorList,
                        //个人股东
                        this.personalStockList=res.data.personalStockList,
                        //法人股东
                        this.legalStockList=res.data.legalStockList
                    }
                })
            },
            showHolderAndExecutives(v,flag) {
                queryHolderOrExecutivesInfo(this.applyNo,v.id).then(res => {
                    if (res.code === "0000") {
                        this.legalHolderAndExecutives = res.data;
                    }
                })
                if(flag==="holder"){
                    this.viewHolder=true;
                }
                else if(flag==="executives"){
                    this.viewExecutives=true;
                }

            },
            initDataDic() {
                let self = this;
                getByTypes(this.dicKeys).then(res => {
                    if (res.code === "0000" && res.data) {
                        self.dataDic = res.data;
                    }
                });
            },
            setSexTitle(val) {
                let dic = {};
                this.dataDic.sex.forEach(column => {
                    if (column.value === val) {
                        dic = column;
                    }
                });
                return dic.title;
            },
            setNationalityTitle(val) {
                let dic = {};
                this.dataDic.nationality.forEach( column => {
                    if (column.value === val) {
                        dic = column;
                    }
                });
                return dic.title;
            },
            setIsLongTermTitle(val) {
                let dic = {};
                this.dataDic.isLongTerm.forEach( column => {
                    if (column.value === val) {
                        dic = column;
                    }
                });
                return dic.title;
            },
            setCertTypeTitle(val) {
                let dic = {};
                this.dataDic.certType.forEach(column => {
                    if (column.value === val) {
                        dic = column;
                    }
                });
                return dic.title;
            },
            setMaritalStatusTitle(val) {
                let dic = {};
                this.dataDic.maritalStatus.forEach(column => {
                    if (column.value === val) {
                        dic = column
                    }
                });
                return dic.title;
            },
            setAssociationTitle(val) {
                let dic = {};
                this.dataDic.association.forEach(column => {
                    if (column.value === val) {
                        dic = column
                    }
                });
                return dic.title;
            },
            setIndustryTypeTitle(val) {
                let dic = {};
                this.dataDic.industryType.forEach(column => {
                    if (column.value === val) {
                        dic = column
                    }
                });
                return dic.title;
            },
            showHolderData(row){
                this.dataList = row;
                this.showInfo = true;
                this.legalId = row.id;
                this.custId = row.custId;
            },
            closeModel() {
                this.showInfo = false;

            },
        },

        mounted() {
        },
        created() {
            this.stageId = this.afs.getPageParams(this).stageId;
        }
    }
</script>
<style scoped>
</style>
