<style lang="less">
    @import 'organLoanContractTask.less';
    @import "./style/row-background.less";
</style>
<template>
    <div class="search">
        <Row v-if="isShowTaskList">
            <Col>
                <Card>
                    <Row class="operation">
                        <Button @click="setLock('yes')" type="primary" icon="md-lock">锁定</Button>
                        <Button @click="setLock('no')" type="primary" icon="ios-unlock">解锁</Button>
                    </Row>
                    <Row>
                        <Form ref="searchForm" :model="searchForm" inline :label-width="100" class="search-form">
                            <Form-item label="合同号码" prop="contractNo">
                                <Input type="text" v-model="searchForm.contractNo" clearable placeholder="请输入合同号码" style="width: 200px" />
                            </Form-item>
                            <Form-item label="客户姓名" prop="custName">
                                <Input type="text" v-model="searchForm.custName" clearable placeholder="请输入客户姓名" style="width: 200px" />
                            </Form-item>
                            <Form-item label="申请编号" prop="applyNo">
                                <Input type="text" v-model="searchForm.applyNo" clearable placeholder="请输入申请编号" style="width: 200px" />
                            </Form-item>
                            <span v-show="drop">
                                <Form-item label="合作商名称" prop="dealerName">
                                    <Select v-model="searchForm.dealerName" filterable clearable placeholder="请输入合作商名称" style="width:200px">
                                        <Option v-for="item in dealerNameDate" :label="item.channelFullName" :value="item.channelFullName" :key="item.channelFullName">{{item.channelFullName}}</Option>
                                    </Select>
                                </Form-item>
<!--                                <Form-item label="证件号码" prop="customerIdCard">-->
<!--                                    <Input type="text" v-model="searchForm.customerIdCard" clearable placeholder="请输入证件号码" style="width: 200px"/>-->
<!--                                </Form-item>-->
                                <Form-item label="案件状态" prop="applyStatus">
                                    <Select v-model="searchForm.applyStatus" clearable placeholder="请选择案件状态" style="width:200px">
                                        <Option v-for="item in applyStatusList" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>
                                    </Select>
                                </Form-item>
<!--                                <Form-item label="车辆属性" prop="carNature">-->
<!--                                    <Select v-model="searchForm.carNature" clearable placeholder="请选择车辆属性" style="width:200px">-->
<!--                                      <Option v-for="item in dataDic.carNature" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="车辆类型" prop="carType">-->
<!--                                    <Select v-model="searchForm.carType" clearable placeholder="请选择车辆类型" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.carType" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="业务分类" prop="businessType">-->
<!--                                    <Select v-model="searchForm.businessType" clearable placeholder="请选择业务分类" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.businessType" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="渠道类型" prop="channelBelong">-->
<!--                                    <Select v-model="searchForm.channelBelong" clearable placeholder="请选择渠道类型" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.channelBelong" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="运营方式" prop="operateWay">-->
<!--                                    <Select v-model="searchForm.operateWay" clearable placeholder="请选择运营方式" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.operateWay" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="挂靠方式" prop="affiliatedWay">-->
<!--                                    <Select v-model="searchForm.affiliatedWay" clearable placeholder="请选择挂靠方式" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.affiliatedWay" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="购车目的" prop="carPurpose">-->
<!--                                    <Select v-model="searchForm.carPurpose" clearable placeholder="请选择购车目的" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.carPurpose" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="VIN码" prop="carVin">-->
<!--                                    <Input type="text" v-model="searchForm.carVin" clearable placeholder="请输入VIN码" style="width: 200px" />-->
<!--                                </Form-item>-->
<!--                                <Form-item label="放款模式" prop="lendingMode">-->
<!--                                    <Select v-model="searchForm.lendingMode" clearable placeholder="请选择放款模式" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.lendingMode" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="放款申请时间"  prop="passFirstDate" >-->
<!--                                    <DatePicker v-model="selectDate" type="daterange" format="yyyy-MM-dd" clearable-->
<!--                                                @on-change="selectDateRange" placeholder="选择起始时间"-->
<!--                                                style="width: 200px"></DatePicker>-->
<!--                                </Form-item>-->
<!--                                <Form-item label="案件标记" prop="caseLabel">-->
<!--                                    <Select v-model="searchForm.caseLabel" filterable clearable placeholder="请选择案件标记" style="width:200px;">-->
<!--                                        <Option v-for="item in this.labelList" :label="item.labelName" :value="item.labelId" :key="item.value">{{item.labelName}}</Option>-->
<!--                                     </Select>                                </Form-item>-->
<!--                                 <Form-item label="车辆品牌" prop="carName">-->
<!--                                     <Select v-model="searchForm.brandCode" filterable clearable placeholder="请选择车辆品牌" style="width:200px;">-->
<!--                                        <Option v-for="item in this.brandNameList" :label="item.brandName" :value="item.brandCode" :key="item.value">{{item.brandName}}</Option>-->
<!--                                     </Select>-->
<!--                                 </Form-item>-->
<!--                                 <Form-item label="锁定标记" prop="remarks">-->
<!--                                      <Select v-model="searchForm.isLock" clearable placeholder="请选择锁定标记" style="width:200px">-->
<!--                                        <Option v-for="item in dataDic.isType" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>-->
<!--                                    </Select>-->
<!--                                </Form-item>-->
                            </span>
                            <Form-item style="margin-left: -35px;" v-if="dropDownContent=='展开'">
                                <Button @click="handleSearch" type="primary" icon="ios-search">查询</Button>
                                <Button @click="handleReset" type="primary" icon="ios-trash">重置</Button>
                                <a class="drop-down" @click="dropDown">{{dropDownContent}}
                                    <Icon :type="dropDownIcon"></Icon>
                                </a>
                            </Form-item>
                            <Form-item style="margin-left: -65px;width:100%;text-align: center;" v-if="dropDownContent=='收起'">
                                <Button @click="handleSearch" type="primary" icon="ios-search">查询</Button>
                                <Button @click="handleReset" type="primary" icon="ios-trash">重置</Button>
                                <a class="drop-down" @click="dropDown">{{dropDownContent}}
                                    <Icon :type="dropDownIcon"></Icon>
                                </a>
                            </Form-item>
                        </Form>
                    </Row>
                    <Row>
                        <Table v-bind:loading="loading"
                               border
                               v-bind:columns="columns"
                               v-bind:data="data"
                               sortable="custom"
                               @on-sort-change="changeSort"
                               @on-selection-change="showSelect"
                               :row-class-name="rowClassName"
                               ref="table">
                            <template slot-scope="{row,index}" slot="action">
                                <Button style="margin-right:5px" type="primary" size="small" @click="showLogs(row)">查看日志</Button>
                            </template>
                            <template slot-scope="{row , index}" slot="applyStatus">
                                <span v-if="(row.businessStateIn=='cancel'||row.businessStateIn=='cancelConditional')&&(row.contractNo === undefined||row.contractNo === null)">{{'合同取消'}}</span>
                                <span v-else-if="row.businessStateIn=='reject'">{{'审批拒绝'}}</span>
                                <span v-else-if="row.contractStatus=='normalSettle'||row.contractStatus=='advanceSettle'||row.contractStatus=='close'||row.contractStatus=='contractEffective'">{{setContractStatusTitle(row.contractStatus)}}</span>
                                <span v-else>{{setApplyStatusTitle(row.applyStatus)}}</span>
                            </template>
                            <template slot-scope="{row , index}" slot="businessType">
                                <span>{{setBusinessTypeTitle(row.businessType)}}</span>
                            </template>
                            <template slot-scope="{row , index}" slot="lendingMode">
                                <span>{{setLendingModeTitle(row.lendingMode)}}</span>
                            </template>
                            <template slot-scope="{row , index}" slot="isLock">
                                <span>{{setIsLock(row.isLock)}}</span>
                            </template>
                            <template slot-scope="{ row, index }" slot="tags">
                                <Tag v-for="item in row.labelList" :color="item.labelColor" >{{item.labelName}}</Tag>
                            </template>
                            <template slot-scope="{ row, index }" slot="contractNo">
                                <DynamicLink component="projects/afs-core-business/pages/organ/organ-loan-audit/organLoanAuditView"
                                             :is-full="true"
                                             :params="{busiNo:row.contractNo,taskId:row.id,applyNo:row.applyNo,contractNo:row.contractNo,businessType:row.businessType,carNature:row.carNature,applyStatus:row.applyStatus,lendingFirstDate:row.lendingFirstDate,isLock:row.isLock,reviewSign:row.reviewSign,pageReadOnly:true}"
                                             :name="setContractNo(row)"
                                             page-title="案件详情">
                                </DynamicLink>
                            </template>
                        </Table>
                    </Row>
                    <Row type="flex" justify="end" class="page">
                        <Page :current="searchForm.pageNumber" :total="total" :page-size="searchForm.pageSize"
                              @on-change="changePage" @on-page-size-change="changePageSize" :page-size-opts="[10,20,50]"
                              size="small" show-total show-elevator show-sizer></Page>                    </Row>
                </Card>
            </Col>
        </Row>
        <Modal v-model="makeLoanLabelVisible" ref="makeLoanlabelOptionModel" :label-width="80" :title="modalTitle" :closable="false" :mask-closable='false' :width="600" :styles="{top: '180px'}">
            <div slot="footer">
                <Button type="text" @click="cancelLabel">取消</Button>
                <Button type="primary" @click="submitLabel">提交</Button>
            </div>
            <Row>
                <Form ref="makeLoanLabelOptionForm" :model="makeLoanLabelOptionForm" inline :label-width="80" class="search-form" :rules="formValidate">
                    <Form-item  label="标签名称" prop="labelName">
                        <Input type="textarea" v-model="makeLoanLabelOptionForm.labelName" clearable placeholder="请输入标签" style="width: 450px;height: 50px;" />
                    </Form-item>
                </Form>
            </Row>
        </Modal>
        <loan-approve-record v-model="recordVisible" ref="approveRecord" :contractNo="contractNoParam" :applyNo="applyNoParam" />
        <modal v-model="lockFormVisible" ref="lockOptionModel" :label-width="80" :title="modalTitle" :closable="false" :mask-closable='false' :width="600" :styles="{top: '180px'}">
            <div slot="footer">
                <Button type="text" @click="cancelLock">取消</Button>
                <Button v-if="lockOptionStatus=='yes'" type="primary" @click="submitLock">提交</Button>
                <Button v-if="lockOptionStatus=='no'" type="primary" @click="setUnLock('no')">提交</Button>
            </div>
            <Row>
                <Form ref="lockOptionForm" :model="lockOptionForm" inline :label-width="80" class="search-form" :rules="formValidate">
                    <Form-item v-if="lockOptionStatus=='yes'" label="锁定原因" prop="processRemark">
                        <Input type="textarea" v-model="lockOptionForm.processRemark" clearable placeholder="请输入锁定原因" style="width: 450px;height: 50px;" />
                    </Form-item>
                    <Form-item v-if="lockOptionStatus=='no'" label="解锁原因" prop="processRemark">
                        <Input type="textarea" v-model="lockOptionForm.processRemark" clearable placeholder="解锁原因" style="width: 450px;height: 50px;" />
                    </Form-item>
                </Form>
            </Row>
        </modal>
        <!--显示案件审批详情页-->
        <LoanAuditView v-if="isShowLoanAudit" :detailParams="detailParams" @backGo="backGo" ></LoanAuditView>
        <LoanReviewView v-if="isShowLoanReview" :detailParams="detailParams" @backGo="backGo" ></LoanReviewView>
    </div>
</template>

<script>
    import LoanAuditView from '@/projects/afs-core-business/pages/organ/organ-loan-audit/organLoanAuditView.vue'
    import LoanReviewView from '@/projects/afs-core-business/pages/organ/organ-loan-audit/organLoanReviewView.vue'
    import { getByTypes } from '_p/basic/api/admin/datadic'
    import { reviewSubmit } from '_p/afs-core-business/api/organ/loan-approve/organLoanReview'
    import {getLoanAllTaskList, modifyLock, modifyLabel,getLabelList,getBrandNameList,getDealerNameList } from '@/projects/afs-core-business/api/organ/loan-approve/organLoanApproveTask'
    //根据申请编号获取一打标签
    import bubble from './style/bubble.vue';
    import loanApproveRecord from '../organ-loan-approve-record/organ-loan-approve-record'
    export default {
        name: 'caseContractQuery',
        components: {
            loanApproveRecord,
            LoanAuditView,
            LoanReviewView,
            bubble
        },
        /*mounted() {
            this.init()
        },*/
        activated() {
            this.init()
        },
        data() {
            return {
                visible:true,
                loading: true,
                operationLoading: false,
                importLoading: false,
                loadingExport: true,
                isShowTaskList: true,
                isShowLoanAudit: false,
                isShowLoanReview: false,
                recordVisible: false,
                makeLoanLabelVisible: false,
                modalTitle: '标签信息',
                tags:"",
                bubbleData:"",
                taskId: '',
                applyNo: '',
                detailParams:{
                    applyNo:'',
                    contractNo:'',
                    taskId:''
                },
                applyNoParam:'',
                contractNoParam: '',
                drop: false,
                dealerNameDate: [],
                dropDownContent: '展开',
                dropDownIcon: 'ios-arrow-down',
                dicKeys: [
                    'businessType',
                    'conApplyStatus',
                    'carType',
                    'carNature',
                    'operateWay',
                    'affiliatedWay',
                    'lendingMode',
                    'channelBelong',
                    'priority',
                    'carPurpose',
                    'businessStateIn',
                    'loanContractStatus',
                    'isType'
                ],
                businessTypeList: [],
                applyStatusList: [],
                businessStateInList: [],
                carType: [],
                carNature: [],
                operateWay: [],
                affiliatedWay: [],
                lendingModeList: [],
                channelBelong: [],
                labelList:[],
                brandNameList:[],
                carPurposeList:[],
                priority:[],
                loanContractStatusList:[],
                dataDic: {},
                selectCount: 0,
                selectList: [],
                interval: null,
                secondsAgo: 0,
                viewImage: false,
                deptId: [],
                selectDep: [],
                department: [],
                dataDep: [],
                searchKey: '',
                selectDate: null,
                searchForm: {
                    applyNo: '',
                    custName: '',
                    dealerName: '',
                    applyStatus: '',
                    carNature: '',
                    brundName: '',
                    carType: '',
                    operateWay: '',
                    affiliatedWay: '',
                    lendingMode: '',
                    isLock: '',
                    startDate: null,
                    endDate: null,
                    tag: '',
                    priority:"",
                    pageNumber: 1,
                    pageSize: 10
                },
                submitLoading: false,
                isLock: [],
                makeLoanLabelOptionForm:{
                    labelName: ''
                },
                lockFormVisible: false,
                lockOptionStatus: '',
                lockOptionForm: {
                    processRemark: ''
                },
                formValidate: {
                    processRemark: [
                        { required: true, message: '备注不能为空', trigger: 'blur' }
                    ],
                    labelName: [
                        { required: true, message: '备注不能为空', trigger: 'blur' }
                    ]
                },
                columns: [
                    {
                        type: 'selection',
                        minWidth: 60,
                        maxWidth: 60,
                        align: 'center',
                        fixed: 'left'
                    },
                    {
                        title: '操作',
                        key: 'action',
                        minWidth: 160,
                        align: 'center',
                        fixed: 'left',
                        slot: 'action'
                    },
                    {
                        title: '合同号码',
                        key: 'contractNo',
                        align: 'center',
                        minWidth: 165,
                        fixed: 'left',
                        tooltip: true, //显示提示
                        slot: 'contractNo'
                    },
                    {
                        title: '案件状态',
                        key: 'applyStatus',
                        align: 'center',
                        minWidth: 120,
                        slot:'applyStatus'
                    },
                    {
                        title: '客户姓名',
                        key: 'custName',
                        align: 'center',
                        minWidth: 120,
                        fixed: 'left',
                        tooltip: true //显示提示
                    },
                    {
                        title: '申请编号',
                        key: 'applyNo',
                        align: 'center',
                        minWidth: 165,
                        fixed: 'left',
                    },
                    {
                        title: '合作商名称',
                        align: 'center',
                        key: 'dealerName',
                        minWidth: 200
                    },
                    {
                        title: '案件标记',
                        align: 'center',
                        key: 'tags',
                        minWidth: 280,
                        slot: "tags"
                    },
                    {
                        title: '车辆品牌',
                        key: 'carName',
                        align: 'center',
                        minWidth: 280
                    },
                    {
                        title: '业务分类',
                        key: 'businessType',
                        align: 'center',
                        minWidth: 100,
                        slot:"businessType"
                    },
                    {
                        title: '放款模式',
                        key: 'lendingMode',
                        align: 'center',
                        minWidth: 120,
                        slot:'lendingMode'
                    },
                    // {
                    //     title: "流程节点",
                    //     key: "taskNodeName",
                    //     minWidth: 110,
                    //     sortable: true,
                    // },
                    {
                        title: '优先级',
                        align: 'center',
                        key: 'priority',
                        minWidth: 100,
                    },
                    {
                        title: '放款申请时间',
                        align: 'center',
                        key: 'lendingFirstDate',
                        minWidth: 150
                    },
                    {
                        title: '锁定标记',
                        align: 'center',
                        key: 'isLock',
                        slot: 'isLock',
                        minWidth: 150
                    }
                ],
                data: [],
                total: 0
            }
        },

        methods: {
            init() {
                this.searchForm.pageNumber = 1
                this.searchForm.pageSize = 10
                this.getTaskList()
                this.getDealerNameList()
                this.initDataDic()
                this.getLabelList()
                this.getBrandNameList()
            },
            getDealerNameList(){
                getDealerNameList().then(res=>{
                    if(res.code === '0000'){
                        this.dealerNameDate = res.data
                    }
                })
            },
            initDataDic() {
                let self = this
                getByTypes(this.dicKeys).then(res => {
                    if (res.code === '0000' && res.data) {
                        self.dataDic = res.data
                        this.businessTypeList.push(...res.data.businessType)
                        this.applyStatusList.push(...res.data.conApplyStatus)
                        this.carType.push(...res.data.carType)
                        this.carNature.push(...res.data.carNature)
                        this.operateWay.push(...res.data.operateWay)
                        this.affiliatedWay.push(...res.data.affiliatedWay)
                        this.lendingModeList.push(...res.data.lendingMode)
                        this.priority.push(...res.data.priority);
                        this.channelBelong.push(...res.data.channelBelong)
                        this.carPurposeList.push(...res.data.carPurpose)
                        this.businessStateInList.push(...res.data.businessStateIn)
                        this.loanContractStatusList.push(...res.data.loanContractStatus)
                        this.applyStatusList.push(...[{value:"reject",title:"审批拒绝"},{value:"contractEffective",title:"合同生效"},{value:"normalSettle",title:"正常结清"},{value:"advanceSettle",title:"提前结清"},{value:"close",title:"合同关闭"}])
                    }
                })
            },
            created(){
                this.afs.afsOn(this,"flushData",()=>{
                    this.getTaskList();
                })
            },
            beforeDestroy(){
                if (this.interval) {
                    clearInterval(this.interval);
                }
                this.afs.afsOff(this,"flushData");
            },
            getLabelList(){
                getLabelList().then(res=>{
                    this.labelList = res.data;
                    console.log(res.data)
                })
            },
            getBrandNameList(){
                getBrandNameList().then(res=>{
                    this.brandNameList = res.data;
                })
            },
            rowClassName (row, index) {
                //合同查询记录行不带背景色
                return '';
            },
            setApplyStatusTitle(val) {
                let dic = {}
                this.applyStatusList.forEach(colunm => {
                    if (colunm.value === val) {
                        dic = colunm
                    }
                })
                return dic.title
            },
            setContractStatusTitle(val) {
                let dic = {}
                this.loanContractStatusList.forEach(colunm => {
                    if (colunm.value === val) {
                        dic = colunm
                    }
                })
                return dic.title
            },
            setBusinessTypeTitle(val) {
                let dic = {}
                this.businessTypeList.forEach(colunm => {
                    if (colunm.value === val) {
                        dic = colunm
                    }
                })
                return dic.title
            },
            setBusinessStateIn(val){
                console.log(val)
                let dic = {}
                this.businessStateInList.forEach(colunm => {
                    if (colunm.value === val) {
                        dic = colunm
                    }
                })
                return dic.title
            },
            setLendingModeTitle(val) {
                let dic = {}
                this.lendingModeList.forEach(colunm => {
                    if (colunm.value === val) {
                        dic = colunm
                    }
                })
                return dic.title
            },
            selectDateRange(v) {
                if (v) {
                    this.searchForm.startDate = v[0]
                    this.searchForm.endDate = v[1]
                }
            },
            setIsLock(val){
                if(val === 'yes'){
                    return '是'
                } else {
                    return '否'
                }
            },
            handleSearch() {
                this.searchForm.pageNumber = 1
                this.searchForm.pageSize = 10
                this.getTaskList()
            },
            dropDown() {
                if (this.drop) {
                    this.dropDownContent = '展开'
                    this.dropDownIcon = 'ios-arrow-down'
                } else {
                    this.dropDownContent = '收起'
                    this.dropDownIcon = 'ios-arrow-up'
                }
                this.drop = !this.drop
            },
            handleReset() {
                this.$refs.searchForm.resetFields()
                this.searchForm.pageNumber = 1
                this.searchForm.pageSize = 10
                this.selectDate = null
                this.searchForm.brandCode =''
                this.searchForm.caseLabel =''
                this.searchForm.isLock = ''
                this.searchForm.startDate = null;
                this.searchForm.endDate = null;

                // 重新加载数据
                this.getTaskList()
            },
            seizeTask() {
                this.$refs.searchForm.resetFields()
                this.searchForm.pageNumber = 1
                this.searchForm.pageSize = 10
                this.selectDate = null
                this.searchForm.startDate = null;
                this.searchForm.endDate = null;
            },
            getTaskList() {
                this.loading = true
                getLoanAllTaskList(this.searchForm).then(res => {
                    this.loading = false
                    if (res.code === '0000') {
                        this.data = res.data.records
                        this.total = res.data.total
                        this.data.forEach(i=>{
                            if(i.lendingMode=='03'){
                                i.tags="(签约后放款)";
                            }else{
                                i.tags="";
                            }
                            if(i.remarks!=undefined&&i.remarks.length>0){
                                i.isShowBubble=true
                            }else{
                                i.isShowBubble=false
                            }
                        })
                    }
                    if(this.total > 0) {
                        this.startAddSeconds();
                    }
                })
                this.selectCount=0;
            },
            startAddSeconds() {
                if (this.interval) {
                    clearInterval(this.interval);
                }
                this.secondsAgo = 0;
                let _this = this;
                this.interval = setInterval(() => {
                    _this.secondsAgo++;
                }, 1000);
            },
            clearSelectAll() {
                this.$refs.table.selectAll(false)
            },
            changeSort(e) {
                this.searchForm.sort = e.key
                this.searchForm.order = e.order
                if (e.order === 'normal') {
                    this.searchForm.order = ''
                }
                this.getTaskList()
            },
            showSelect(e) {
                this.selectList = e
                this.selectCount = e.length
            },
            changePage(v) {
                this.searchForm.pageNumber = v
                this.getTaskList()
                this.clearSelectAll()
            },
            changePageSize(v) {
                this.searchForm.pageSize = v
                this.getTaskList()
            },
            showLoanDetail(param) {
                this.isShowTaskList = false
                this.isShowLoanAudit = true
                this.detailParams.taskId = param.id
                this.detailParams.applyNo = param.applyNo
                this.detailParams.contractNo = param.contractNo
                this.detailParams.applyStatus = param.applyStatus
                this.detailParams.lendingFirstDate = param.lendingFirstDate
                this.detailParams.isLock = param.isLock
            },
            hasSelected() {
                if (this.selectCount == 0) {
                    this.$Message.warning('需先勾选数据行!')
                    return false
                } else {
                    return true
                }
            },
            hasSelectedLabel() {
                if (this.selectCount == 0) {
                    this.$Message.warning('需先勾选数据行!')
                    return false
                } else if(this.selectCount != 1) {
                    this.$Message.warning('打标签只能勾选一行数据行!')
                    return false
                } else {
                    return true
                }
            },
            existsCurrentStatus(type, status) {
                let currentStatusNum = 0
                this.selectList.forEach(record => {
                    if (type == '1') {
                        if (record.isLock == status) {
                            currentStatusNum++
                        }
                    }
                })
                return currentStatusNum > 0
            },
            backGo() {
                this.isShowTaskList = true
                this.isShowLoanAudit = false
                this.isShowLoanReview = false
                this.getTaskList()
            },
            showLogs(v) {
                this.applyNoParam = v.applyNo
                this.contractNoParam = v.contractNo
                this.recordVisible = true
            },
            //锁定、解锁
            setLock(status) {
                if (this.hasSelected()) {
                    if (this.existsCurrentStatus('1', status)) {
                        this.$Message.warning(
                            '已选择数据存在' +
                            (status == 'yes' ? '已锁定' : '非锁定') +
                            '数据，请去除勾选后再操作'
                        )
                    } else {
                        if (status == 'yes') {
                            this.modalTitle = '锁定操作备注'
                            this.lockFormVisible = true
                        } else {
                            this.modalTitle = '解锁操作备注'
                            this.lockFormVisible = true
                        }
                        this.lockOptionStatus = status

                    }
                }
            },
            setContractNo(val){
                if(val.contractNo != undefined && val.contractNo!= null && val.contractNo !=''){
                    return val.contractNo + val.tags;
                } else {
                    return '';
                }
            },
            //锁定原因备注提交
            submitLock() {
                let validResult = true
                this.$refs.lockOptionForm.validate(valid => {
                    if (!valid) {
                        this.$Message.error('数据不合规，请检查数据项')
                        validResult = false
                    }
                })
                if (validResult) {
                    this.operationLoading = true
                    this.loading = true
                    let data = []
                    this.selectList.forEach(function(e) {
                        data += e.contractNo + ','
                    })
                    modifyLock(
                        data,
                        this.lockOptionForm.processRemark,
                        this.lockOptionStatus
                    ).then(res => {
                        if (res.code == '0000') {
                            this.$Message.success('操作成功')
                            this.getTaskList()
                        }
                    })
                    this.loading = false
                    this.cancelLock()
                    this.operationLoading = false
                }
            },
            //标签提交
            submitLabel() {
                let validResult = true
                this.$refs.makeLoanLabelOptionForm.validate(valid => {
                    if (!valid) {
                        this.$Message.error('数据不合规，请检查数据项')
                        validResult = false
                    }
                })
                if (validResult) {
                    this.operationLoading = true
                    this.loading = true
                    let data = []
                    this.selectList.forEach(function(e) {
                        data += e.contractNo + ','
                    })
                    modifyLabel(
                        data,
                        this.makeLoanLabelOptionForm.labelName,
                    ).then(res => {
                        if (res.code == '0000') {
                            this.$Message.success('操作成功')
                            this.getTaskList()
                        }
                    })
                    this.loading = false
                    this.cancelLabel()
                    this.operationLoading = false
                }
            },
            cancelLock() {
                this.lockFormVisible = false
                this.lockOptionForm.processRemark = ''
            },
            cancelLabel() {
                this.makeLoanLabelVisible = false
                this.makeLoanLabelOptionForm.labelName = ''
            },
            //解锁
            setUnLock(status) {
                if (this.hasSelected()) {
                    if (this.existsCurrentStatus('1', status)) {
                        this.$Message.warning(
                            '已选择数据存在' +
                            (status == 'yes' ? '已锁定' : '非锁定') +
                            '数据，请去除勾选后再操作'
                        )
                    }else{
                        this.lockOptionStatus=status
                        let validResult = true
                        this.$refs.lockOptionForm.validate(valid => {
                            if (!valid) {
                                this.$Message.error('数据不合规，请检查数据项')
                                validResult = false
                            }
                        })
                        if (validResult) {
                            this.operationLoading = true
                            this.loading = true
                            let data = []
                            this.selectList.forEach(function(e) {
                                data += e.contractNo + ','
                            })
                            modifyLock(
                                data,
                                this.lockOptionForm.processRemark,
                                this.lockOptionStatus
                            ).then(res => {
                                if (res.code == '0000') {
                                    this.$Message.success('操作成功')
                                    this.getTaskList()
                                }
                            })
                            this.loading = false
                            this.cancelLock()
                            this.operationLoading = false
                        }
                    }
                }
            },
            //取消
            cancel() {
                this.makeLoanLabelVisible = false
                this.getTaskList()
            },
            batchProcess() {
                let statusVisible = true
                let lockVisible =true
                this.selectList.forEach(function (e) {
                    if(e.applyStatus !='waitConfirm'){
                        statusVisible = false
                    }
                    if(e.isLock =='yes'){
                        lockVisible = false
                    }
                });
                if(statusVisible == true && lockVisible == true){
                    this.selectList.forEach(function(e){
                        e.taskId = e.id;
                        reviewSubmit(e).then(res => {
                            if (res.code === '0000') {
                                this.$Message.success('操作成功')
                            }
                        })
                    })
                }else if(statusVisible == false && lockVisible == true){
                    this.$Message.info("包含状态不为'放款待确认'的合同")
                }else if(lockVisible == false && statusVisible == true){
                    this.$Message.info("包含状态为'锁定'的合同")
                }else {
                    this.$Message.info("包含状态为'锁定'和不为'放款待确认'的合同")
                }
            },
            addLabelList() {
                if (this.hasSelectedLabel()) {
                    this.modalTitle = '案件打标签'
                    this.makeLoanLabelVisible = true
                }
            }
        }
    }
</script>

<style scoped>
</style>
