<template>
    <div class="totalInfo">
        <Card>
            <div>
                <h2>合计信息</h2>
                <Row style="padding-left: 20px;">
                    <Col span="10">
                        <ul>
                            <li><span>合同价格 : </span><b>{{totalInfo.totalcontAmt}}</b><span>元</span><input type="hidden" :value="totalcontAmt"/></li>
                            <li><span>首付总额 : </span><b>{{totalInfo.totalFirstAmt}}</b><span>元</span><input type="hidden" :value="totalFirstAmt"/></li>
                            <li><span>利息总额 : </span><b>{{totalInfo.totalInterest}}</b><span>元</span></li>
                        </ul>
                    </Col>
                    <Col span="10" offset="2">
                        <ul>
                            <li><span>贷款总额 : </span><b>{{totalInfo.totalLoanAmt}}</b><span>元</span><input type="hidden" :value="totalLoanAmt"/></li>
                            <li><span>贴息总额 : </span><b>{{totalInfo.totalDiscountAmt}}</b><span>元</span></li>
                        </ul>
                    </Col>
                </Row>
            </div>
        </Card>
    </div>
</template>
<script>
import vueEvent from "_p/basic/assets/js/vueEvent.js"
import * as utils from '_p/basic/assets/js/utils.js'
export default {
    name:"organTotalInfo",
    data(){
        return{
            proessModel:false,
            carPrice:"",
            additionalPrice:"",
            carDownPayAmt:"",
            additionalDownPayAmt:"",
            carLoanAmt:"",
            additionalLoanAmt:"",
            totalInfo:{
                totalcontAmt:"",
                totalLoanAmt:"",
                totalFirstAmt:"",
                totalDiscountAmt:"",
                totalInterest:"",
                totalRent:"",
            },
        }
    },
    props:{
        totalInfoData:{
            type:Object,
        },
        isAdditional:{
            type:String,
        },
        uuid:{
            type:String,
        },
        isSubProduct:{
            type:String,
        },
        saveStatus:{
            type:Object,
        }
    },
    watch:{
        totalInfoData(val){
            if(Object.keys(val).length>0){
                this.totalInfo.totalDiscountAmt=val.totalDiscountAmt;
                this.totalInfo.totalInterest=val.totalInterest;
                this.totalInfo.totalRent=val.totalRent;
                this.totalInfo.totalcontAmt=val.totalcontAmt;
                this.totalInfo.totalFirstAmt=val.totalFirstAmt;
                this.totalInfo.totalLoanAmt=val.totalLoanAmt;
            }
        },
        'totalInfoData.totalDiscountAmt':{
            handler: function(val) {
                this.totalInfo.totalDiscountAmt=val;
            },

        },
        'totalInfoData.totalInterest':{
            handler: function(val) {
                this.totalInfo.totalInterest=val;
            },
        },
        'totalInfoData.totalRent':{
            handler: function(val) {
                this.totalInfo.totalRent=val;
            },
        }
    },
    computed:{
        // 合同总金额
        totalcontAmt:function(){
            let totalcontAmt=0;
            if(this.isAdditional=='0'){
                totalcontAmt=this.carPrice;
            }else if(this.isAdditional=="1"){
                totalcontAmt=utils.add(this.carPrice,this.additionalPrice);
            }
            this.totalInfo.totalcontAmt=totalcontAmt||'';
            return totalcontAmt;
        },
        // 首付总金额
        totalFirstAmt:function(){
            let totalFirstAmt=0;
            if(this.isAdditional=='0'||this.isAdditional=="1"&&this.isSubProduct=="0"){
                totalFirstAmt=this.carDownPayAmt;
            }else if(this.isAdditional=="1"&&this.isSubProduct=="1"){
                totalFirstAmt=utils.add(this.carDownPayAmt,this.additionalDownPayAmt)
            }
            this.totalInfo.totalFirstAmt=totalFirstAmt||'';
            return totalFirstAmt;
        },
        // 总融资金额
        totalLoanAmt:function(){
            let totalLoanAmt=0;
            if(this.isAdditional=="0"||this.isAdditional=="1"&&this.isSubProduct=="0"){
                totalLoanAmt=this.carLoanAmt;
            }else if(this.isAdditional=="1"&&this.isSubProduct=="1"){
                totalLoanAmt=utils.add(this.carLoanAmt,this.additionalLoanAmt)
            }
            this.totalInfo.totalLoanAmt=totalLoanAmt||'';
            return totalLoanAmt;
        },
    },
    created(){

    },
    mounted(){
        console.log("融资租赁产品页面-总计贷款")
        let _this=this;
        // 车贷或者车贷与附加贷页面传递过来的计算数据
        vueEvent.$on('to-carPrice',function(data,id){
            if(id==_this.uuid){
                _this.carPrice=data;
            }
        })
        vueEvent.$on('to-carDownPayAmt',function(data,id){
            if(id==_this.uuid){
                _this.carDownPayAmt=data;
            }
        })
        vueEvent.$on('to-carLoanAmt',function(data,id){
            if(id==_this.uuid){
                _this.carLoanAmt=data;
            }
        })
        //附加贷传递过来的计算数据
        vueEvent.$on('to-additionalPrice',function(data,id){
            if(id==_this.uuid){
                _this.additionalPrice=data;
            }
        })
        vueEvent.$on('to-additionalDownPayAmt',function(data,id){
            if(id==_this.uuid){
                _this.additionalDownPayAmt=data;
            }
        })
        vueEvent.$on('to-additionalLoanAmt',function(data,id){
            if(id==_this.uuid){
                _this.additionalLoanAmt=data;
            }
        })
    },
    // 解决多次绑定,多次触发问题
    beforeDestroy () {
        vueEvent.$off("to-carPrice");
        vueEvent.$off("to-carDownPayAmt");
        vueEvent.$off("to-carLoanAmt");
        vueEvent.$off("to-additionalPrice");
        vueEvent.$off("to-additionalDownPayAmt");
        vueEvent.$off("to-additionalLoanAmt");
    },
    methods:{
       submitTotalInfo(){
           return this.totalInfo;
       },
       resetTotalInfo(){
            this.totalInfo={
                totalcontAmt:"",
                totalLoanAmt:"",
                totalFirstAmt:"",
                totalDiscountAmt:"",
                totalInterest:"",
                totalRent:"",
            };
            return this.totalInfo;
       }

    }
}
</script>
<style>
    .sliderList  .ivu-slider-disabled .ivu-slider-button:hover{
        cursor: pointer;
    }
    .sliderList .ivu-slider-disabled .ivu-slider-button:hover,
    .sliderList .ivu-slider-disabled .ivu-slider-button-dragging{
        border-color:#efab4d;
    }
    .sliderList .ivu-slider-disabled .ivu-slider-button{
       border-color:#efab4d;
    }
    .sliderList .ivu-slider-disabled .ivu-slider-bar{
        background-color: #efab4d;
    }
</style>
<style scoped>
.totalInfo h2{
    font-size: 18px;
}
.totalInfo ul>li{
    margin-top: 10px;
}
.totalInfo ul>li b{
    font-size: 18px;
    font-weight: normal;
}
</style>
