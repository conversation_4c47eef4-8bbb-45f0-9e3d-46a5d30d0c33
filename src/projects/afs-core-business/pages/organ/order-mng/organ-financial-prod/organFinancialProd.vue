<template>
    <div class="financialPro">
            <Form ref="financialProdForm" :model="financialProdForm" :rules="financialProdFormValiate" :label-width="90">
                <div class="common_br" style="border: none;background-color: white;">
                    <h2 class="extra_title" v-if="isStyle">金融产品</h2>
                    <div class="common_content">
                        <Row>
                            <Col span="11" style="padding-left:24px;">
                                <FormItem label="产品名称" prop="id" class="prodName">
                                    <Select v-model="financialProdForm.id"
                                    @on-change="selectProd"
                                    :label-in-value="true"
                                    :disabled="isShowDetails||diffType=='details'||isReconsider"
                                    class="wprecent100"
                                    @on-open-change="openProd"
                                    >
                                        <Input class="select-search wprecent100" search enter-button placeholder="请输入" @on-search="searchProd" />
                                        <Option :value="item.id" v-for="(item,index) in prodNameList" :key="index" >{{item.productName}}</Option>
                                        <Option :value="mainProductId"    style="display: none;">{{mainProductName}}</Option>
                                        <Page
                                        v-show="prodNameList.length>0"
                                        :page-size="pageData.pageSize"
                                        :total="pageData.total"
                                        @on-change="changePage"
                                        style="text-align: center;"
                                        simple />
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </div>
                </div>
            </Form>
            <div>
                <Row>
                    <Col span="11">
                            <Card style="margin-left: 40px;" class="mb10">
                                <div>
                                    <organ-car-loan
                                    :queryCarProductInfo="queryCarProductInfo"
                                    :selectCarProductInfo="selectCarProductInfo"
                                    :isCarTail="isCarTail"
                                    ref="carloan"
                                    :uuid="uuid"
                                    :carPrice="carPrice"
                                    :isShowDetails="isShowDetails"
                                    :isReconsider="isReconsider"
                                    :diffType="diffType"
                                    :mainProductId="mainProductId"
                                    :approveDisabled="approveDisabled"
                                    :applyNo="applyNo"
                                    v-if="isAdditional=='0'||(isAdditional=='1'&&isSubProduct=='1')"
                                    :saveStatus="saveStatus"
                                    :valiateFlag="valiateFlag"
                                    :salePrice="salePrice"
                                    :isAutoCount="isAutoCount"
                                    :pageIdentify="pageIdentify"
                                    @changeCarPrice="changeCarPrice"
                                    :queryCarLoanRebate="queryCarLoanRebate"
                                    >
                                    </organ-car-loan>
                                    <organ-car-additional-loan
                                    v-if="isAdditional=='1'&&isSubProduct=='0'"
                                    :isCarTail="isCarTail"
                                    :isShowDetails="isShowDetails"
                                    :isReconsider="isReconsider"
                                    :diffType="diffType"
                                    :uuid="uuid"
                                    :carPrice="carPrice"
                                    :isAdditional="isAdditional"
                                    :isSubProduct="isSubProduct"
                                    :approveDisabled="approveDisabled"
                                    :selectProductInfo="selectProductInfo"
                                    :selectCarProductInfo="selectCarProductInfo"
                                    :queryCarProductInfo="queryCarProductInfo"
                                    :queryCarLoanRebate="queryCarLoanRebate"
                                    ref="carAddLoan"
                                    :mainProductId="mainProductId"
                                    :applyNo="applyNo"
                                    :saveStatus="saveStatus"
                                    :valiateFlag="valiateFlag"
                                    :salePrice="salePrice"
                                    :isAutoCount="isAutoCount"
                                    :pageIdentify="pageIdentify"
                                    @changeCarPrice="changeCarPrice"
                                    :isInternet="isInternet"
                                    ></organ-car-additional-loan>
                                </div>
                            </Card>
                                <div v-if="isAdditional=='1'&&isSubProduct=='1'">
                                    <Card style="margin-left: 40px;">
                                        <organ-additional-loan
                                        :selectProductInfo="selectProductInfo"
                                        :selectCarProductInfo="selectCarProductInfo"
                                        :queryProductInfo="queryProductInfo"
                                        :uuid="uuid"
                                        :isShowDetails="isShowDetails"
                                        :isReconsider="isReconsider"
                                        :diffType="diffType"
                                        :isAdditional="isAdditional"
                                        :isSubProduct="isSubProduct"
                                        :addProductId="addProductId"
                                        :mainProductId="mainProductId"
                                        :applyNo="applyNo"
                                        :saveStatus="saveStatus"
                                        :valiateFlag="valiateFlag"
                                        :queryAddLoanRebate="queryAddLoanRebate"
                                        :isInternet="isInternet"
                                        ref="additionalLoan"></organ-additional-loan>
                                    </Card>
                                </div>
                    </Col>
                    <Col span="12" offset="1">
                            <organ-loan-info
                            :isAdditional="isAdditional"
                            :carDiscountOption="carDiscountOption"
                            :additionalDiscountOption="additionalDiscountOption"
                            :addMonthPay="addMonthPay"
                            :addMonthlyRate="addMonthlyRate"
                            :addMyriad="addMyriad"
                            :carMonthPay="carMonthPay"
                            :carMonthlyRate="carMonthlyRate"
                            :carMyriad="carMyriad"
                            :selectProductInfo="selectProductInfo"
                            :selectCarProductInfo="selectCarProductInfo"
                            :queryProductInfo="queryProductInfo"
                            :queryCarProductInfo="queryCarProductInfo"
                            :valiateCarLoan="valiateCarLoan"
                            :valiateAdditionalLoan="valiateAdditionalLoan"
                            :valiateCarAddLoan="valiateCarAddLoan"
                            :isSubProduct="isSubProduct"
                            :uuid="uuid"
                            ref="loaninfo"
                            :saveStatus="saveStatus"
                            :isShowDetails="isShowDetails"
                            :isReconsider="isReconsider"
                            :diffType="diffType"
                            ></organ-loan-info>
                            <div>
                                <organ-total-info
                                :totalInfoData="totalInfoData"
                                :isAdditional="isAdditional"
                                :isSubProduct="isSubProduct"
                                ref="totalInfo"
                                :saveStatus="saveStatus"
                                :uuid="uuid"
                                ></organ-total-info>
                                <slot name="afsApply"></slot>
                            </div>
                    </Col>
                </Row>
            </div>
        <slot name="afsProduct"></slot>
    </div>
</template>
<script>
import OrganCarLoan from "./organCarLoan/organCarLoan";
import organAdditionalLoan from "./organ-additional-loan/organAdditionalLoan";
import OrganCarAdditionalLoan from "./organCarAdditionalLoan/organCarAdditionalLoan"
import OrganLoanInfo from "./organ-loan-info/organLoanInfo"
import OrganTotalInfo from "./organTotalInfo/organTotalInfo"
import {
    queryProductSelectList,
    getProductDetails,
    getCostList,
    saveCostList,
    updateCostList,
    financeCalculation,
    getArbitraryRepaymentPlan,
    resetCostInfo
} from "_p/afs-core-business/api/organ/order-mng/organFianacialProd.js"
import {deepClone} from "@/libs/utils/ObjectClone";
import RuiSelectPage from "@/components/selectPage/RuiSelectPage"
import * as utils from '_p/basic/assets/js/utils.js'
export default {
    name:"organ-financial-prod",
    components:{
        OrganCarLoan,
        organAdditionalLoan,
        OrganLoanInfo,
        OrganTotalInfo,
        OrganCarAdditionalLoan,
        RuiSelectPage
    },
    data(){
        return {
            isInternet:"",
            tempRepaymentList:[],
            modifyRepayList:[],
            finalRentList:[],
            // isSaveDisabled:true,
            carPrice:"",
            isAdditional:"0",//附加贷
            isSubProduct:"0",//0没有子产品,是主产品跟子产品相同,1有子产品,是主产品跟子产品不相同
            carDiscountOption:"0",//车贷贴息方式
            discountAmt:0,
            additionalDiscountOption:"0",//附加贷贴息方式
            isCarTail:false, //车贷是否有尾款
            mainProductId:this.productId||'',
            mainProductName:this.productName||'',
            addProductId:"",
            addProductName:"",
            addAlgorithmType:"",
            carAlgorithmType:"",
            carRepaymentType:"",
            addRepaymentType:"",
            carMonthPay:0,
            addMonthPay:0,
            carMonthlyRate:"",
            addMonthlyRate:"",
            carMyriad:"",
            addMyriad:"",
            addFinancialId:"",
            carFinancialId:"",
            // 计算接口返回保存增加
            // 租金
            carTotalRent:"",
            addTotalRent:"",
            // 利息
            carTotalInterest:"",
            addTotalInterest:"",
            // 贴息金额
            addDiscount:"",
            carDiscount:"",

            pageData:{
                pageNumber:1,
                pageSize:10,
                productName:"",
                total:0
            },
            addRepaymentData:{},
            carRepaymentData:{},

            financialProdForm:{
                id:this.productId||"",
            },
            financialProdFormValiate:{
                id:[
                    {required: true, message: "产品名称不能为空", trigger: "change"}
                ]
            },
            prodNameList:[
            ],
            // 选择产品的附加贷产品的变量
            selectProductInfo:{},
            // 选择产品的产品详细信息的变量
            selectCarProductInfo:{},

            // 修改回显的附加贷产品的变量
            queryProductInfo:{},
            // 修改回显的车贷的信息
            queryCarProductInfo:{},
            // 修改回显合计信息
            totalInfoData:{
                totalInterest:"",
                totalDiscountAmt:"",
                totalRent:"",
            },
            // 选择产品的入参
            selectParam:{},

            formData:{
                processMode:"",
            }
        }
    },
    props:{
        applyNo:{
            type:String,
        },
        curTab:{
            type:Number
        },
        isShowDetails:{
            type:Boolean,
        },
        isStyle: {
            type: Boolean
        },
        approveDisabled: {
            type: Boolean
        },
        isReconsider:{
            type:Boolean,
        },
        uuid:{
            type:String,
        },
        intropath:{
            type:String,
        },
        saveStatus:{
            type:Object,
        },
        proessFormValidate:{
            type:String,
        },
        proessForm:{
            type:Object,
        },
        additionalProessForm:{
            type:Object,
        },
        carProessForm:{
            type:Object,
        },
        remarkForm:{
            type:Object,
        },
        queryFormEl:{
            type:Function,
        },
        valiateFlag:{
            type:Boolean
        },
        salePrice:{
            type:String
        },
        isAutoCount:{
            type:Boolean
        },
        pageIdentify:{
            type:String
        },
        diffType:{
            type:String
        },
        productId:{
            type:String
        },
        productName:{
            type:String
        }
    },
    watch:{
        "curTab":{
            immediate: true,
            handler(val){
              console.log("-------------",val)
                if(val===3){
                    if(this.applyNo){
                        this.saveStatus.disabled=true;
                        this.saveStatus.ismodify=false;
                        this.modfiyInitCostList(this.applyNo);
                    }else{
                        if(this.intropath=='afsProduct'){
                            if(this.productId!=null&&this.productId!==""){
                                this.initProductDetails(this.productId,"select");
                            }
                        }
                    }
                }

            }
        }
    },
    mounted(){

    },
    methods:{
        openProd(val){
           this.queryProductSelectList(val)
        },
        // 下拉框展开获取产品list
        queryProductSelectList(val){
            if(val){
                this.selectParam.applyNo=this.applyNo;
                this.selectParam.pageSize=this.pageData.pageSize;
                this.selectParam.pageNo=this.pageData.pageNumber;
                this.selectParam.productName=this.pageData.productName;
                queryProductSelectList(this.selectParam).then(res=>{
                    if(res.code=="0000"){
                        if(res.data){
                            this.prodNameList=res.data.records;
                            this.pageData.total=res.data.total;
                        }
                    }
                })
            }
        },
        changePage(value){
            this.pageData.pageNumber=value;
            this.queryProductSelectList(this.selectParam);
        },
        // 修改初始化查询贷款信息
        modfiyInitCostList(applyNo){
            let param={
                applyNo:applyNo,
                carId:"",
            }
            if(this.pageIdentify=="assertChange"){
                param.businessFlag="assetChange";
            }
            getCostList(param).then(res=>{
                if(res.code=="0000"){
                    if(res.data.salePrice){
                        this.carPrice=res.data.salePrice.toString();
                    }
                    if(res.data.carProductInfo||res.data.addProductInfo){
                        this.saveStatus.noData=false;
                    }
                    // 回显的时候产品的附加贷跟贴息方式
                    if(res.data.isAdditional){
                        this.isAdditional=res.data.isAdditional;
                    }
                    if(res.data.isSubProduct){
                        this.isSubProduct=res.data.isSubProduct;
                    }
                    if(res.data.isInternet){
                        this.isInternet=res.data.isInternet;
                    }
                    if(res.data.rentList&&res.data.rentList.length>0){
                        this.modifyRepayList=deepClone(res.data.rentList);
                    }
                    let _this=this;
                    this.$nextTick(()=>{
                        if(res.data.carProductInfo){
                            _this.mainProductId=res.data.carProductInfo.costInfo.productId;
                            _this.financialProdForm.id=res.data.carProductInfo.costInfo.productId;
                            _this.mainProductName=res.data.carProductInfo.costInfo.productName;
                            // 修改赋值产品那条数据的id
                            _this.carFinancialId=res.data.carProductInfo.costInfo.id;
                        }
                        if(_this.isAdditional=="1"){
                            if(res.data.addProductInfo){
                                _this.addFinancialId=res.data.addProductInfo.costInfo.id;
                            }
                        }
                        // 保证回显的值，覆盖产品详情值
                        if(res.data.carProductInfo){
                            _this.initProductDetails(res.data.carProductInfo.costInfo.productId).then((val)=>{
                                if(val.productInfo){
                                    if(res.data.carProductInfo){
                                        _this.queryCarProductInfo=deepClone(res.data.carProductInfo);
                                    }
                                }
                                if(val.mainProductInfo){
                                    _this.queryCarProductInfo=deepClone(res.data.carProductInfo);

                                }
                            })
                        }
                        // 合计金额
                        let tempTotalAmt={
                            totalcontAmt:res.data.totalContractAmt,
                            totalLoanAmt:res.data.totalLoanAmt,
                            totalFirstAmt:res.data.totalFirstAmt,
                            totalDiscountAmt:res.data.totalDiscountAmt,
                            totalInterest:res.data.totalInterest,
                            totalRent:res.data.totalRent,
                        }
                        _this.totalInfoData=tempTotalAmt;
                    })
                }
            })
        },
        // 重置
        resetCostInfo(applyNo) {
            let params = {
                applyNo: applyNo
            }
            resetCostInfo(params).then(res => {
                if(res.code==="0000"){
                    this.$Message.success("重置成功");
                }
                this.modfiyInitCostList(applyNo);
            })
        },
        // 选择产品
        selectProd(val){
            if(val){
                this.mainProductId=val.value;
                if(this.intropath=="afsProduct"){
                    this.$emit('queryProId',val.value)
                    this.valiteFailRes();
                }
                this.mainProductName=val.label;
                // this.saveStatus.disabled=true;
                this.saveStatus.noData=true;
                this.initProductDetails(val.value,"select");
                if(this.valiateFlag){
                    this.$emit('turnValiteFlag',false)
                }
            }else{
                // 清空产品
                this.$refs.loaninfo.resetCarLoanDis(this.carDiscountOption);
                this.modifyRepayList=[] //任意贷产品回显切换产品为任意贷的时候,清空此变量
                if(this.isAdditional=="1"&&this.isSubProduct=='1'){
                    this.$refs.loaninfo.resetTab();
                    this.$refs.carloan.resetCarLoan();
                    this.$refs.additionalLoan.resetAdditionalLoan();
                    this.$refs.loaninfo.resetAdditionalLoanDis(this.additionalDiscountOption);
                }else if(this.isAdditional=='1'&&this.isSubProduct=="0"){
                    this.$refs.carAddLoan.resetCarAddLoan()
                }else{
                    this.$refs.carloan.resetCarLoan();
                }
                this.$refs.totalInfo.resetTotalInfo();
            }
        },
        searchProd(val){
            this.pageData.productName=val;
            this.queryProductSelectList(true)
        },
        // 查询产品详情
        initProductDetails(val,type){
            let param='';
            param={
                id:val,
                applyNo:this.applyNo //此申请编号没有用到
            };
            return new Promise((resolve,reject)=>{
                // let res=queryProductDetail;
                getProductDetails(param).then(res=>{
                    if(res.code=="0000"){
                        if(res.data){
                            // // 0是无附加贷，1是有附加贷
                            this.isAdditional=res.data.isAdditional;
                            if(res.data.addProductInfo){
                                this.isSubProduct=res.data.addProductInfo[0].isPro;
                            }
                            // 0不贴息 1非灵活贴息 2灵活贴息
                            let _this=this;
                            this.$nextTick(()=>{
                                if(res.data.addProductInfo){
                                    _this.selectProductInfo=deepClone(res.data.addProductInfo);
                                    _this.additionalDiscountOption=res.data.addProductInfo.discountOption;
                                    _this.addProductId=res.data.productInfo.baseInfo.id;
                                    _this.addProductName=res.data.productInfo.baseInfo.productName;
                                    _this.addAlgorithmType=res.data.productInfo.algorithmType;
                                    _this.addRepaymentData=deepClone(res.data.productInfo.repaymentData);
                                    _this.addRepaymentType=res.data.productInfo.detailInfo.interestMode;
                                }
                                if(res.data.productInfo){
                                    _this.selectCarProductInfo=deepClone(res.data.productInfo);

                                    _this.carDiscountOption=res.data.productInfo.discountOption;
                                    _this.carAlgorithmType=res.data.productInfo.algorithmType;
                                    _this.carRepaymentData=deepClone(res.data.productInfo.repaymentData);
                                    _this.carRepaymentType=res.data.productInfo.detailInfo.interestMode;
                                    if(res.data.productInfo.discountInfo && res.data.productInfo.discountInfo.subsidyList){
                                        res.data.productInfo.discountInfo.subsidyList.forEach((item,index)=>{
                                            if(item.subsidyMoney){
                                                _this.discountAmt += parseFloat(item.subsidyMoney);

                                            }
                                        })
                                    }
                                    if(res.data.productInfo.isTail=="0"){
                                        _this.isCarTail=false;
                                    }else if(res.data.productInfo.isTail=="1"){
                                        _this.isCarTail=true;
                                    }
                                };
                                // 当重新选择产品的时候情况
                                if(type=="select"){
                                    _this.$refs.loaninfo.resetCarLoanDis(_this.carDiscountOption);
                                    _this.modifyRepayList=[] //任意贷产品回显切换产品为任意贷的时候,清空此变量
                                    if(_this.isAdditional=="1"&&_this.isSubProduct=='1'){
                                        _this.$refs.loaninfo.resetTab();
                                        _this.$refs.carloan.resetCarLoan();
                                        _this.$refs.loaninfo.resetAdditionalLoanDis(_this.additionalDiscountOption);
                                        _this.$refs.additionalLoan.resetLoanLimit();
                                        _this.$refs.carloan.resetLoanLimit();
                                    }else if(_this.isAdditional=='1'&&_this.isSubProduct=="0"){
                                        _this.$refs.carAddLoan.resetCarAddLoan();
                                        _this.$refs.carAddLoan.resetLoanLimit();
                                    }else{
                                        _this.$refs.carloan.resetCarLoan();
                                        _this.$refs.carloan.resetLoanLimit();
                                    }
                                    _this.$refs.totalInfo.resetTotalInfo();

                                }
                                resolve(res.data);
                            })
                        }
                    }
                })
            })
        },
        // 融资页面点击保存调接口
        financialSave(btnType){
            let param=this.querySaveData();
            if(!param){
                if(this.intropath=="afsApply"){
                    if(btnType=="nextBtn"){
                        this.$emit("valiateProRes",false)
                    }
                }
                return false;
            }
            if(this.carFinancialId){
                updateCostList(param).then(res=>{
                    if(res.code=="0000"){
                        if(this.intropath=="afsApply"){
                            if(btnType=="nextBtn"){
                                this.$emit("valiateProRes",true)
                            }
                            if(btnType=="cutTable"){
                                this.$emit("proCallSuccess",true)
                            }
                        }
                        this.$Message.success("更新金融产品成功");
                    }
                })
            }else{
                saveCostList(param).then(res=>{
                    if(res.code=="0000"){
                        if(this.intropath=="afsApply"){
                            if(btnType=="nextBtn"){
                                this.$emit("valiateProRes",true)
                            }
                            if(btnType=="cutTable"){
                                this.$emit("proCallSuccess",true)
                            }
                        }
                        res.data.forEach((item,index)=>{
                            if(item.costType=="01"){
                                this.carFinancialId=item.id;
                            }else if(item.costType=="02"){
                                this.addFinancialId=item.id;
                            }
                        })
                        this.$Message.success("保存金融产品成功");
                    }
                })
            }
        },
        // 构造保存的贴息入参
        constrDiscountData(discountOption,selectInfo,loanRebate,paramObj){
            // 车贷正常贴息
            if(discountOption==1){
                let discountList=this.$refs.loaninfo.backDiscountList();
                let subsidyList=[];
                selectInfo.discountInfo.subsidyList.forEach((item,index)=>{
                    let obj={
                        id:item.id,
                        discountId:item.subsidySide,
                        maxDiscountAmt:item.subsidyMoney,
                        discountScale:item.subsidyRate,
                        totalToTotal:item.totalToTotal,
                        discountParty:item.subsidyName,
                    }
                    // discountList.forEach((itemDis,indexDis)=>{
                    //       if(item.subsidySide==itemDis.value){
                    //         obj.discountParty=itemDis.title;
                    //       }
                    // })
                    subsidyList.push(obj);
                })
                paramObj.discountList=subsidyList;
                // paramObj.maxDiscountAmt=selectInfo.discountInfo.maxdiscountAmt;
                paramObj.maxDiscountAmt=loanRebate.maxDisCountAmt;
            }
            // 灵活贴息
            if(discountOption==2){
                for(let i=0;i<loanRebate.loadList.length;i++){
                    if(!loanRebate.loadList[i].discountId||loanRebate.loadList[i].discountAmt===""){
                        this.$Message.warning("贴息列表需填写完整")
                        return false;
                    }
                }
                let tempArr=[];
                for(let i=0;i<loanRebate.loadList.length;i++){
                    let obj={};
                    for(let j=0;j<selectInfo.discountInfo.subsidyList.length;j++){
                        // 匹配id校验
                        if(loanRebate.loadList[i].discountId==selectInfo.discountInfo.subsidyList[j].subsidySide){
                            if(selectInfo.discountInfo.subsidyList[j].singleSelect&&(selectInfo.discountInfo.subsidyList[j].singleSelect=='0'||selectInfo.discountInfo.subsidyList[j].singleSelect=='1')){
                                if(loanRebate.loadList[i].discountId!=0){
                                    if(loanRebate.loadList[i].discountAmt>selectInfo.discountInfo.subsidyList[j].subsidyMoney){
                                        this.$Message.warning("贴息额不能大于最高贴息额")
                                        return false;
                                    }
                                }else{
                                    if(loanRebate.loadList[i].discountAmt>loanRebate.maxDisCountAmt){
                                        this.$Message.warning('贴息额不能大于厂商最高贴息额');
                                        return false;
                                    }
                                }
                            }
                            obj.discountId=loanRebate.loadList[i].discountId;
                            obj.discountAmt=loanRebate.loadList[i].discountAmt;
                            // obj.discountParty=loanRebate.loadList[i].discountParty;
                            obj.id=selectInfo.discountInfo.subsidyList[j].id;
                            obj.discountScale=selectInfo.discountInfo.subsidyList[j].subsidyRate;
                            obj.maxDiscountAmt=selectInfo.discountInfo.subsidyList[j].subsidyMoney;
                            obj.discountParty=selectInfo.discountInfo.subsidyList[j].subsidyName;
                            obj.totalToTotal=selectInfo.discountInfo.subsidyList[j].totalToTotal;
                            tempArr.push(obj)
                        }
                    }
                }
                paramObj.discountList=tempArr;
                paramObj.maxDiscountAmt=loanRebate.maxDisCountAmt||0;
            }
            return true;
        },
        // 构造保存的融资列表入参
        constrCapitalData(additionalLoanInfo){
            let tempItemList=[];
            for(let i=0;i< additionalLoanInfo.additionalAmtList.length;i++){
                let obj={
                    financeItemCode:additionalLoanInfo.additionalAmtList[i].extrasProjectNo,
                    financeItemName:additionalLoanInfo.additionalAmtList[i].extrasProjectName,
                    financeItemAmt:additionalLoanInfo.additionalAmtList[i].extrasProjectAmt || '0',
                    financeItemTotal:1,
                    sonList:[],
                }
                if(additionalLoanInfo.additionalAmtList[i].itemList.length>0){
                    for(let j=0;j<additionalLoanInfo.additionalAmtList[i].itemList.length;j++){
                        let childObj={
                            financeItemCode:additionalLoanInfo.additionalAmtList[i].itemList[j].extrasProjectNo,
                            financeItemName:additionalLoanInfo.additionalAmtList[i].itemList[j].extrasProjectName,
                            financeItemAmt:additionalLoanInfo.additionalAmtList[i].itemList[j].extrasProjectAmt || '0',
                            financeItemTotal:1,
                        }
                        obj.financeItemTotal=additionalLoanInfo.additionalAmtList[i].itemList.length;
                        obj.sonList.push(childObj)
                    }
                }
                tempItemList.push(obj)
            }
            return tempItemList;
        },
        // 点击融资计算
        calculate(){
            let _this=this;
            setTimeout(function(){
                if(!_this.valiatePro()){
                    _this.valiteFailRes();
                    return false;
                }
                // 车贷信息
                let carLoanInfo='';
                if(_this.isAdditional=='0'||(_this.isAdditional=='1'&&_this.isSubProduct=='1')){
                    carLoanInfo=_this.$refs.carloan.submitCarLoan()
                }else if(_this.isAdditional=='1'&&_this.isSubProduct=='0'){
                    carLoanInfo=_this.$refs.carAddLoan.submitCarAddLoan();
                }
                if(!carLoanInfo){
                    _this.valiteFailRes();
                    return false;
                }
                // 车贷贴息相关信息
                let carLoanRebate=_this.$refs.loaninfo.submitCarLoanRebate();
                let params={
                }
                let tempCostList=[];
                let carObj={
                    costType:"mainPrd",
                    discountType:_this.carDiscountOption,
                    loanAmt:carLoanInfo.loanAmt,
                    loanTerm:carLoanInfo.loanLimit,
                    custRate:carLoanRebate.custRate,
                    settleRate:carLoanRebate.settleRate,
                    productId:_this.mainProductId,
                    algorithmType:_this.carAlgorithmType,
                    applyNo:_this.applyNo,
                    discountAmt:_this.discountAmt,

                }
                if(_this.isAdditional=='1'&&_this.isSubProduct=='0'){
                    carObj.itemList=_this.constrCapitalData(carLoanInfo);
                }
                let totalCarDisAmt=_this.constrTotalDiscountAmt(_this.carDiscountOption,carLoanRebate,carObj)
                if(!totalCarDisAmt){
                    _this.valiteFailRes();
                    return false;
                }
                // 当计算方式为分段时
                if(_this.carAlgorithmType=="ballonloan"){
                    carObj.ballonLoanType=this.carRepaymentData.repaymentMethods.finalPaymentCalculationLogic;
                    // 当有尾款
                    if(_this.isCarTail){
                        carObj.tailPayAmt=carLoanInfo.tailPayAmt;
                        carObj.tailPayScale=carLoanInfo.tailPayScale;
                        carObj.isTail="1";
                    }else{
                        // 当无尾款
                        carObj.isTail="0";
                        //当等于1时候直接赋值repaymentAmountStages
                        if(_this.carRepaymentData.data[0].paymentByStages=="1"){
                            let tempPeroids='';
                            let tempMoney='';
                            _this.carRepaymentData.data.forEach((item,index)=>{
                                let remainAmt="";
                                if(item.proportionRepaymentSections){
                                    // 比例先除100
                                    let remainScale=utils.divided(item.proportionRepaymentSections,100);
                                    // 车款价格乘以比例
                                    remainAmt=Math.round(utils.multiply(carLoanInfo.loanAmt,remainScale)*100)/100;
                                }else{
                                    remainAmt=item.repaymentAmountStages;
                                }
                                if(index==_this.carRepaymentData.data.length-1){
                                    tempPeroids+=item.numberInstallments;
                                    tempMoney+=remainAmt;
                                }else{
                                    tempPeroids+=item.numberInstallments+',';
                                    tempMoney+=remainAmt+',';
                                }
                            })
                            carObj.peroids=tempPeroids;
                            carObj.money=tempMoney;
                        }else{
                            _this.constrPeroidsMoney("car",_this.carRepaymentData,carObj,carLoanInfo);
                        }
                    }
                    // 等比累进
                }else if(_this.carAlgorithmType=="equalratioret"){
                    carObj.step=_this.carRepaymentData.repaymentMethods.numberStepsPerStep;
                    carObj.stepPercent=_this.carRepaymentData.repaymentMethods.floatingProportionStepLoan;
                    // 等额累进
                }else if(_this.carAlgorithmType=="equalquota"){
                    carObj.step=_this.addRepaymentData.repaymentMethods.numberStepsPerStep;
                    // 金额或许不为此后续字段
                    carObj.quota=_this.addRepaymentData.floatingProportionStepLoan;
                    // 任意贷计算传等额本息(车贷或者为同一个产品)
                }else if(_this.carAlgorithmType=="arbitraryloan"){
                    if(_this.modifyRepayList.length>0){
                        carObj.algorithmType=_this.carAlgorithmType;
                        let rentList=[];
                        _this.modifyRepayList.forEach((item,index)=>{
                            let obj={
                                period:item.period,
                                rent:item.rent
                            }
                            rentList.push(obj);
                        })
                        carObj.rentList=rentList;
                    }else{
                        carObj.algorithmType="equalrental"
                    }
                    // 结构化贷款
                }else if(_this.carAlgorithmType=="structuredloan"){
                    carObj.structuredMonth=_this.carRepaymentData.repaymentMethods.setAnyNumberCredits;
                    carObj.structuredType=_this.carRepaymentData.repaymentMethods.paymentType;
                    carObj.structuredMoney=_this.carRepaymentData.repaymentMethods.monthlySupplyAmount;
                }
                tempCostList[0]=carObj;
                // 附加贷
                if(_this.isAdditional=="1"&&_this.isSubProduct=="1"){
                    let additionalLoanInfo=_this.$refs.additionalLoan.submitAdditionalLoan();
                    if(!additionalLoanInfo){
                        _this.valiteFailRes();
                        return false;
                    }
                    if(additionalLoanInfo.loadAmtList&&additionalLoanInfo.loadAmtList.length<=0){
                        _this.$Message.warning("点击新增填写附加贷数据");
                        _this.valiteFailRes();
                        return false;
                    }
                    let additonalLoanRebate=_this.$refs.loaninfo.submitAdditonalLoanRebate();
                    let additionalObj={
                        costType:"02",
                        discountType:_this.additionalDiscountOption,
                        loanAmt:additionalLoanInfo.loadAmtList[0].loanAmt,
                        loanTerm:additionalLoanInfo.loadAmtList[0].loanLimit,
                        custRate:additonalLoanRebate.custRate,
                        settleRate:additonalLoanRebate.settleRate,
                        productId:_this.addProductId,
                        algorithmType:_this.addAlgorithmType,
                    }
                    //附加贷融资列表
                    additionalObj.itemsList=_this.constrCapitalData(additionalLoanInfo);
                    let totalAddDisAmt=_this.constrTotalDiscountAmt(_this.additionalDiscountOption,additonalLoanRebate,additionalObj)
                    if(!totalAddDisAmt){
                        _this.valiteFailRes();
                        return false;
                    }
                    // 当计算方式为分段时
                    if(_this.addAlgorithmType=="ballonloan"){
                        additionalObj.ballonLoanType=_this.addRepaymentData.repaymentMethods.finalPaymentCalculationLogic;
                        if(additionalLoanInfo.loadAmtList[0].isAdditionalTail){
                            additionalObj.tailPayAmt=additionalLoanInfo.loadAmtList[0].tailPayAmt;
                            additionalObj.tailPayScale=additionalLoanInfo.loadAmtList[0].tailPayScale;
                            additionalObj.isTail="1";
                        }else{
                            additionalObj.isTail="0";
                            //当等于1时候直接赋值repaymentAmountStages
                            if(_this.addRepaymentData.data[0].paymentByStages=="1"){
                                let tempPeroids=[];
                                let tempMoney=[];
                                _this.addRepaymentData.data.forEach((item,index)=>{
                                    let remainAmt="";
                                    if(item.proportionRepaymentSections){
                                        // 比例先除100
                                        let remainScale=utils.divided(item.proportionRepaymentSections,100);
                                        // 车款价格乘以比例
                                        remainAmt=Math.round(utils.multiply(additionalLoanInfo.loadAmtList[0].loanAmt,remainScale)*100)/100;
                                    }else{
                                        remainAmt=item.repaymentAmountStages;
                                    }
                                    if(index==_this.addRepaymentData.data.length-1){
                                        tempPeroids+=item.numberInstallments;
                                        tempMoney+=remainAmt;
                                    }else{
                                        tempPeroids+=item.numberInstallments+',';
                                        tempMoney+=remainAmt+',';
                                    }
                                })
                                additionalObj.peroids=tempPeroids;
                                additionalObj.money=tempMoney;
                            }else{
                                _this.constrPeroidsMoney('add',_this.addRepaymentData,additionalObj,additionalLoanInfo)
                            }
                        }
                        // 等比累进
                    }else if(_this.addAlgorithmType=="equalratioret"){
                        additionalObj.step=_this.addRepaymentData.repaymentMethods.numberStepsPerStep;
                        additionalObj.stepPercent=_this.addRepaymentData.floatingProportionStepLoan;
                        // 等额累进
                    }else if(_this.addAlgorithmType=="equalquota"){
                        additionalObj.step=_this.addRepaymentData.repaymentMethods.numberStepsPerStep;
                        // 金额或许不为后续字段
                        additionalObj.quota=_this.addRepaymentData.floatingProportionStepLoan;
                        // 结构化贷款
                    }else if(_this.addAlgorithmType=="structuredloan"){
                        // additionalObj.structuredMonth=utils.sub(additionalLoanInfo.loadAmtList[0].loanLimit,1);
                        additionalObj.structuredMonth=_this.addRepaymentData.repaymentMethods.setAnyNumberCredits;
                        additionalObj.structuredType=_this.addRepaymentData.repaymentMethods.paymentType;
                        additionalObj.structuredMoney=_this.addRepaymentData.repaymentMethods.monthlySupplyAmount;
                    }
                    tempCostList[1]=additionalObj;
                }
                let condition={
                    applyNo:_this.applyNo,
                    costList:tempCostList,
                }
                // params.costList=tempCostList;
                _this.valiteSuccessRes();
                financeCalculation(condition).then(res=>{
                    if(res.code=="0000"){
                        // 产品
                        if(_this.intropath=="afsProduct"){
                            if(_this.carAlgorithmType=="arbitraryloan"){
                                _this.$emit('isShowModel',true)
                            }
                            // 进件
                        }else if(_this.intropath=="afsApply"){
                            _this.$emit('isShowModel',true)
                            if(_this.carAlgorithmType=="arbitraryloan"){
                                _this.$emit('showCalculate',false,true)
                            }else{
                                _this.$emit('showCalculate',true,false)
                            }
                        }
                        _this.saveStatus.disabled=false;
                        _this.saveStatus.noData=false;
                        _this.showPayMoney(res.data);
                    }
                }).catch(error => {
                    _this.valiteFailRes();
                })
            },0)

        },
        valiteFailRes(){
            if(this.intropath=="afsProduct"){
                this.$emit("valiateProRes",false)
            }
        },
        valiteSuccessRes(){
            if(this.intropath=="afsProduct"){
                this.$emit("valiateProRes",true)
            }
        },
        // 构造计算的灵活贴息总额
        constrTotalDiscountAmt(discountOption,loanRebate,paramObj){
             if(discountOption==2){
                let totalAmt=0;
                for(let i=0;i<loanRebate.loadList.length;i++){
                    if(!loanRebate.loadList[i].discountId||loanRebate.loadList[i].discountAmt===""){
                        this.$Message.warning("贴息列表需填写完整")
                        return false;
                    }else{
                        totalAmt=utils.add(totalAmt,loanRebate.loadList[i].discountAmt)
                    }
                }
                paramObj.discountAmt=totalAmt;
            }
            return true;
        },
        // 构造peroids，money数据
        constrPeroidsMoney(type,repaymentData,obj,loanInfo) {
            let loanAmt="";
            let tempPeroids='';
            let tempMoney='';

            // 车贷或车和附加贷合并
            if(type=="car"){
                if(repaymentData.repaymentMethods.finalPaymentBenchmark=="1"){ //总融资额((车贷+附加贷)-首付金额)*比例
                    loanAmt=loanInfo.loanAmt;
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="0"){ //车价金额* 比例
                    loanAmt=loanInfo.carPrice;
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="2"){ //合同价格*比例
                    // 有附加贷同一个产品
                    if(this.isAdditional=="1"&&this.isSubProduct=='0'){
                        loanAmt=utils.add(loanInfo.carPrice,loanInfo.additionalTotalAmt);
                    // 有附加贷不是同一个产品或无附加贷
                    }else{
                        loanAmt=loanInfo.carPrice;
                    }
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="3"){//车辆融资额(车贷-首付金额)*比例
                    let scale=utils.sub(100,loanInfo.downPayScale);
                    let scalePrecent=utils.divided(scale,100);
                    loanAmt=utils.multiply(loanInfo.carPrice,scalePrecent)
                }
            // 附加贷
            }else if(type=="add"){
                if(repaymentData.repaymentMethods.finalPaymentBenchmark=="1"){ //总融资额((车贷+附加贷)-首付金额)*比例
                    loanAmt=loanInfo.loadAmtList[0].loanAmt;
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="0"){ //车价金额* 比例
                    loanAmt=loanInfo.additionalTotalAmt;
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="2"){//合同价格*比例
                    // 有附加贷且不为同一个产品
                   loanAmt=loanInfo.additionalTotalAmt;
                }else if(repaymentData.repaymentMethods.finalPaymentBenchmark=="3"){//车辆融资额(车贷-首付金额)*比例
                    let scale=utils.sub(100,loanInfo.downPayScale);
                    let scalePrecent=utils.divided(scale,100);
                    loanAmt=utils.multiply(loanInfo.additionalTotalAmt,scalePrecent)
                }
            }
            repaymentData.data.forEach((item,index)=>{
                let remainAmt="";
                if(item.proportionRepaymentSections){
                    // 比例先除100
                    let remainScale=utils.divided(item.proportionRepaymentSections,100);
                    // 车款价格乘以比例
                     remainAmt=Math.round(utils.multiply(loanAmt,remainScale)*100)/100;
                }else{
                    remainAmt=item.repaymentAmountStages;
                }

                if(index==repaymentData.data.length-1){
                    tempPeroids+=item.numberInstallments;
                    tempMoney+=remainAmt;
                }else{
                    tempPeroids+=item.numberInstallments+',';
                    tempMoney+=remainAmt+',';
                }
            })
            obj.peroids=tempPeroids;
            obj.money=tempMoney;
        },
        // 回显还款计划表
        showPayMoney(data){
            let titleList=[];
            this.$emit('caluMaxValue',data.maxRent);
            if(data.repaymentList.length>0){
                this.tempRepaymentList=deepClone(data.repaymentList);
            }
            this.proessForm.proessList=this.queryProessList(data.repaymentList);
            // if(data.rentList&&data.rentList.length>0){
            if(data.costList[0].rentList&&data.costList[0].rentList.length>0){
                this.modifyRepayList=data.costList[0].rentList;
            }else{
                this.modifyRepayList=[];
            }
            let costType=[];
            // 回显月供
            data.costList.forEach((item,index)=>{
                costType.push(item.costType);
                if(item.costType=="02"){
                    this.addMonthPay=item.firstRent;
                    // 等额本息
                    if(this.addAlgorithmType=='equalrental'){
                        this.addMonthlyRate=item.monthlyRate.toString();
                        this.addMyriad=item.myriadCoefficient.toString();
                    }
                    this.addTotalRent=item.totalRent;
                    this.addTotalInterest=item.totalInterest;
                    // 附加贷的贴息金额
                    this.addDiscount=item.discountAmt;
                    if(item.repaymentList){
                        this.additionalProessForm.proessList=this.queryProessList(item.repaymentList);
                    }
                }
                if(item.costType=="01"){
                    this.carMonthPay=item.firstRent;
                    if(this.carAlgorithmType=='equalrental'){
                        this.carMonthlyRate=item.monthlyRate.toString();
                        this.carMyriad=item.myriadCoefficient.toString();
                    }
                    this.carTotalRent=item.totalRent;
                    this.carTotalInterest=item.totalInterest;
                    // 车贷的贴息金额
                    this.carDiscount=item.discountAmt;
                    if(item.repaymentList){
                        this.carProessForm.proessList=this.queryProessList(item.repaymentList)
                    }
                }
            })
            // 无附加贷返回
            if(costType.indexOf('02')=="-1"){
                let titleList=[{value:"00",title:"还款计划表"}];
                this.$emit('scheduleTitle',titleList)
            }else{
                let titleList=[{value:"00",title:"还款计划表"},{value:"01",title:"车款还款计划表"},{value:"02",title:"附加贷还款计划表"}];
                this.$emit('scheduleTitle',titleList)
            }
            // 回显合计信息
            this.totalInfoData.totalcontAmt=this.carPrice;
            this.totalInfoData.totalInterest=data.totalInterest;
            this.totalInfoData.totalDiscountAmt=data.totalDiscountAmt;
            this.totalInfoData.totalRent=data.totalRent;
            this.$emit('passValiate')
        },
        // 获取车款，附加贷，总的还款计划表
        queryProessList(repaymentList){
            let tempArr=[];
             repaymentList.forEach((item,index)=>{
                let obj={
                    no:item.no,
                    inputValue:item.yueGong.toString(),
                    proessValue:[],
                    benJin:item.benJin,
                    compoundInterest:item.compoundInterest,
                    liXi:item.liXi,
                    monthlyInterest:item.monthlyInterest,
                    yuE:item.yuE,
                    yueGong: item.yueGong,
                    modifyAmount:0,
                    hirePurchaseAmt: item.hirePurchaseAmt
                }
                obj.proessValue[0]=0;
                obj.proessValue[1]=item.yueGong;
                tempArr.push(obj);
            })
            return tempArr;
        },
        // 还款计划表计算
        calculateIntrate(){
            // 对比当前修改的期数与金额
            let tempArr=[];
            this.proessForm.proessList.forEach((itemProess,indexProess)=>{
                this.tempRepaymentList.forEach((item,index)=>{
                    if(itemProess.no==item.no){
                        if(itemProess.inputValue!=item.yueGong){
                            let obj={
                                period:itemProess.no,
                                rent:itemProess.inputValue
                            }
                            tempArr.push(obj);
                        }
                    }
                })
            })
            if(tempArr.length>0){
                if(this.modifyRepayList.length>0){
                   let tempArrPeriod=[];
                   tempArr.forEach((item,index)=>{
                     tempArrPeriod.push(item.period);
                   })
                  //  判断上次修改的期数值modifyRepayList是否在这次修改期数tempArr中，不在的话push到tempArr,在的话不做任何处理
                   this.modifyRepayList.forEach((itemRepay,index)=>{
                       if(tempArrPeriod.indexOf(itemRepay.period)=="-1"){
                           let obj={
                                period:itemRepay.period,
                                rent:itemRepay.rent
                           }
                            tempArr.push(obj);
                       }
                   })
                }
                 // 车贷信息
                let carLoanInfo="";
                if(this.isAdditional=='0'||(this.isAdditional=='1'&&this.isSubProduct=='1')){
                    carLoanInfo=this.$refs.carloan.submitCarLoan()
                }else if(this.isAdditional=='1'&&this.isSubProduct=='0'){
                    carLoanInfo=this.$refs.carAddLoan.submitCarAddLoan();
                }
                if(!carLoanInfo){
                    return false;
                }
                let tempProessList=[];
                if(this.proessForm.proessList&&this.proessForm.proessList.length>0){
                    this.proessForm.proessList.forEach((item,index)=>{
                        let obj={
                            no:item.no,
                            yueGong:item.inputValue,
                        }
                        tempProessList.push(obj);
                    })
                }
                let queryFormEl=this.queryFormEl();
                queryFormEl.validate((valid) => {
                    if(valid){
                        // 车贷贴息相关信息
                        let carLoanRebate=this.$refs.loaninfo.submitCarLoanRebate();
                        let params={
                            costType:"01",
                            loanAmt:carLoanInfo.loanAmt,
                            loanTerm:carLoanInfo.loanLimit,
                            custRate:carLoanRebate.custRate,
                            settleRate:carLoanRebate.settleRate,
                            algorithmType:this.carAlgorithmType,
                            productId:this.mainProductId,
                            repaymentList:tempProessList,
                            discountAmt:this.addDiscount,
                        }
                        params.applyNo=this.applyNo;
                        params.rentList=tempArr;
                        this.finalRentList=tempArr;
                        getArbitraryRepaymentPlan(params).then(res=>{
                            if(res.code=="0000"){
                                this.showPayMoney(res.data)
                            }
                        })
                    }
                })
            }else{
                // this.$Message.warning("请修改租金后重新计算")
            }
        },
        valiatePro(){
            let proVailate=false;
            this.$refs.financialProdForm.validate((valid) => {
                if(valid){
                    proVailate=valid;
                }else{
                    proVailate=false;
                }
            })
            return proVailate;
        },
        valiateCarLoan(){
           return this.$refs.carloan.submitCarLoan()
        },
        valiateCarAddLoan(){
            return this.$refs.carAddLoan.submitCarAddLoan();
        },
        valiateAdditionalLoan(){
            return this.$refs.additionalLoan.submitAdditionalLoan()
        },
        queryCarLoanRebate(){
            return this.$refs.loaninfo.submitCarLoanRebate()
        },
        queryAddLoanRebate(){
            return this.$refs.loaninfo.submitAdditonalLoanRebate()
        },
        queryTotalLoanAmt(){
            let submitTotalInfo=this.$refs.totalInfo.submitTotalInfo();
            return submitTotalInfo.totalLoanAmt;
        },
        querySaveData(){
            // 保存获取合计信息
            let submitTotalInfo=this.$refs.totalInfo.submitTotalInfo();
            // 车贷贴息相关信息
            let carLoanRebate=this.$refs.loaninfo.submitCarLoanRebate();
            // 无附加贷或有附加贷不是一个产品
            let carLoanInfo="";
            let totalArr=[];
            let carObj={
                costType:"mainPrd",
                discountType:this.carDiscountOption,
                productId:this.mainProductId,
                productName:this.mainProductName,
                discountAmt:this.carDiscount,
                settleRate:carLoanRebate.settleRate,
                custRate:carLoanRebate.custRate,
                monthPayAmt:carLoanRebate.monthPayAmt,
                totalRent:this.carTotalRent,
                totalInterest:this.carTotalInterest,
                algorithmType:this.carAlgorithmType,
                repaymentMethod:this.carRepaymentType,
            }
            if(carLoanRebate.addPointValue){
                carObj.addPointValue=carLoanRebate.addPointValue;
            }
            if(this.isAdditional=='0'||(this.isAdditional=='1'&&this.isSubProduct=='1')){
                carLoanInfo=this.$refs.carloan.submitCarLoan();
                if(this.isAdditional=='1'&&this.isSubProduct=='1'){
                    carObj.isSubProduct=this.isSubProduct;
                }else if(this.isAdditional=="0"){
                    carObj.isSubProduct="";
                }
            }else if(this.isAdditional=='1'&&this.isSubProduct=='0'){
                carLoanInfo=this.$refs.carAddLoan.submitCarAddLoan();
                carObj.addAmt=carLoanInfo.additionalTotalAmt;
                carObj.itemsList=this.constrCapitalData(carLoanInfo);
                carObj.isSubProduct=this.isSubProduct;
            }
            carObj.contractAmt=carLoanInfo.carPrice;
            carObj.loanTerm=carLoanInfo.loanLimit;
            carObj.downPayScale=carLoanInfo.downPayScale;
            carObj.downPayAmt=carLoanInfo.downPayAmt;
            carObj.loanAmt=carLoanInfo.loanAmt;
            // 更新时候用的车贷id
            if(this.carFinancialId){
                carObj.id=this.carFinancialId;
            }
            // 有尾款
            if(this.isCarTail){
                carObj.tailPayAmt=carLoanInfo.tailPayAmt;
                carObj.tailPayScale=carLoanInfo.tailPayScale;
                carObj.isTail="1";
            }else{
                // 无尾款
                carObj.isTail="0";
            }
            let carDiscountResult=this.constrDiscountData(this.carDiscountOption,this.selectCarProductInfo,carLoanRebate,carObj);
            // 如果贴息校验不通过
            if(!carDiscountResult){
                return false;
            }
            if(this.finalRentList.length>0){
                carObj.rentList=this.finalRentList;
            }
            totalArr[0]=carObj;
            // 有附加贷不是同一个产品
            if(this.isAdditional=='1'&&this.isSubProduct=='1'){
                // 保存获取附加贷信息
                let additionalLoanInfo=this.$refs.additionalLoan.submitAdditionalLoan();
                //附加贷贷款信息
                let additonalLoanRebate=this.$refs.loaninfo.submitAdditonalLoanRebate();

                let additionalObj={
                    costType:"02",
                    discountType:this.additionalDiscountOption,
                    productId:this.addProductId,
                    isSubProduct:this.isSubProduct,
                    productName:this.addProductName,
                    contractAmt:additionalLoanInfo.additionalTotalAmt,
                    loanTerm:additionalLoanInfo.loadAmtList[0].loanLimit,
                    downPayScale:additionalLoanInfo.loadAmtList[0].downPayScale,
                    downPayAmt:additionalLoanInfo.loadAmtList[0].downPayAmt,
                    loanAmt:additionalLoanInfo.loadAmtList[0].loanAmt,
                    discountAmt:this.addDiscount,
                    settleRate:additonalLoanRebate.settleRate,
                    custRate:additonalLoanRebate.custRate,
                    monthPayAmt:additonalLoanRebate.monthPayAmt,
                    totalRent:this.addTotalRent,
                    totalInterest:this.addTotalInterest,
                    algorithmType:this.addAlgorithmType,
                    repaymentMethod:this.addRepaymentType,
                }
                if(additonalLoanRebate.addPointValue){
                    additionalObj.addPointValue=additonalLoanRebate.addPointValue;
                }
                // 更新时候用的附加贷id
                if(this.addFinancialId){
                    additionalObj.id=this.addFinancialId;
                }
                if(additionalLoanInfo.loadAmtList[0].isAdditionalTail){
                    additionalObj.tailPayAmt=additionalLoanInfo.loadAmtList[0].tailPayAmt;
                    additionalObj.tailPayScale=additionalLoanInfo.loadAmtList[0].tailPayScale;
                    additionalObj.isTail="1";
                }else{
                    additionalObj.isTail="0";
                }
                let addDiscountResult=this.constrDiscountData(this.additionalDiscountOption,this.selectProductInfo,additonalLoanRebate,additionalObj);
                // 如果贴息校验不通过
                if(!addDiscountResult){
                    return false;
                }
                //附加贷融资列表
                additionalObj.itemsList=this.constrCapitalData(additionalLoanInfo);
                totalArr[1]=additionalObj;
            }
            let param={
                applyNo:this.applyNo,
                costList:totalArr,
                totalContractAmt:submitTotalInfo.totalcontAmt,
                totalLoanAmt:submitTotalInfo.totalLoanAmt,
                totalFirstAmt:submitTotalInfo.totalFirstAmt,
                totalDiscountAmt:submitTotalInfo.totalDiscountAmt,
                totalInterest:submitTotalInfo.totalInterest,
                totalRent:submitTotalInfo.totalRent,
            }
            if(this.remarkForm){
                param.remarks=this.remarkForm.remark;
            }
            return param;
        },
        resetAll(){
            this.$refs.financialProdForm.resetFields();
        },
        changeCarPrice(val){
            this.carPrice=val;
        }
    }
}

</script>
<style scoped>
.financialPro>>>.ivu-select-dropdown-list{
    max-height: 180px;
}

/deep/ .ivu-input[disabled], fieldset[disabled] .ivu-input {
    background-color: #f3f3f3;
    opacity: 1;
    cursor: not-allowed;
    color: #515a6e;
}

.select-search {
    padding: 7px 16px;
}
/deep/ .ivu-select-small.ivu-select-single .ivu-select-selection {
    height: 24px;
    border-radius: 3px;
    width: 330px;
}
</style>
