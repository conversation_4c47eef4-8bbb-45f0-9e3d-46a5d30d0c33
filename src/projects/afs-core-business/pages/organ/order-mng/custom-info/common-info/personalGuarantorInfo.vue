<template>
    <div class="commoninfo">
        <Modal :width="1000" :title="titleName" v-model="isShow"
               @on-cancel="close"
               :mask-closable="false">
            <Form ref="formData" :model="formData" :rules="formDataValiate" :label-width="120"
                  :disabled="isShowDetails">
                <div >
                    <div>
                        <div>
                            <Row>
                                <Col span="6">
                                    <FormItem label="担保人姓名" prop="staffName">
                                        <Input v-model="formData.staffName" clearable maxlength="32"
                                               :disabled="isShowDetails"
                                               placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col span="6">
                                    <FormItem label="国籍" prop="nationality">
                                        <Select v-model="formData.nationality" clearable placeholder="请选择国籍"
                                                :disabled="isShowDetails"

                                        >
                                            <Option :value="item.value" v-for="(item,index) in dataDic.nationality"
                                                    :key="index">{{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="6">
                                    <FormItem label="性别" prop="sex">
                                        <Select v-model="formData.sex" clearable placeholder="请选择性别"
                                                :disabled="isShowDetails"
                                        >
                                            <Option
                                                :value="item.value"
                                                v-for="(item,index) in dataDic.sex"
                                                :key="index"
                                            >{{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="6">
                                    <FormItem label="年龄" prop="personalAge">
                                        <Input v-model="formData.personalAge"
                                               type="number"
                                               :disabled="isShowDetails"
                                               placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="证件类型" prop="certType">
                                        <Select v-model="formData.certType" clearable placeholder="请选择"
                                                :disabled="isShowDetails" @on-change="checkType(formData.certType)"
                                        >
                                            <Option :value="item.value" v-for="(item,index) in dataDic.certType"
                                                    :key="index">{{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="证件号码" prop="certNo">
                                        <Input v-model="formData.certNo" clearable maxlength="32"
                                               :disabled="isShowDetails"
                                               placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="出生日期"
                                              prop="birthDate"
                                              class="inline-block">
                                        <DatePicker type="date"
                                                    v-model="formData.birthDate"
                                                    format="yyyy-MM-dd"
                                                    placeholder="请选择证件到期日"
                                                    style="width:123%"
                                                    :disabled="formData.isLongTerm||isShowDetails">
                                        </DatePicker>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem label="婚姻状况" prop="maritalStatus">
                                        <Select
                                            v-model="formData.maritalStatus"
                                            clearable
                                            placeholder="请选择婚姻状况"
                                            :disabled="isShowDetails">
                                            <Option
                                                :value="item.value"
                                                v-for="(item,index) in dataDic.maritalStatus"
                                                :key="index"
                                            >{{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="职业信息" prop="careerInformation">
                                        <Input v-model="formData.careerInformation"
                                               clearable
                                               :disabled="isShowDetails"
                                               placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="联系方式" prop="contactNumber">
                                        <Input v-model="formData.contactNumber"
                                               clearable
                                               maxlength="11"
                                               :disabled="isShowDetails"
                                               placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem
                                        label="现所在省市" prop="livingLocation">
                                        <Cascader
                                            :data="location"
                                            v-model="formData.livingLocation"
                                            placeholder="下拉选择省市"
                                            :disabled="isShowDetails"
                                            style="margin-top: 5px;"
                                        ></Cascader>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem
                                        label="详细地址"
                                        :prop="'livingAddress'"
                                        clearable
                                        class="inline-block "
                                    >
                                        <Input
                                            v-model="formData.livingAddress"
                                            placeholder="请输入详细地址"
                                            clearable
                                            :disabled="isShowDetails"
                                            style="width: 240%"
                                            maxlength="128"
                                        />
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="8">
                                    <FormItem      label="月收入" prop="monthlyIncome">
                                        <Input  v-model="formData.monthlyIncome" clearable
                                                maxlength="11" :disabled="isShowDetails"
                                                placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem    label="个人资产情况" prop="personalAssets">
                                        <Input  v-model="formData.personalAssets" clearable
                                                maxlength="11" :disabled="isShowDetails"
                                                placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col span="8">
                                    <FormItem label="与企业对关联关系" prop="association">
                                        <Select
                                            v-model="formData.association"
                                            placement="top"
                                            clearable
                                            placeholder="请选择"
                                            :disabled="isShowDetails">
                                            <Option
                                                :value="item.value"
                                                v-for="(item,index) in dataDic.association"
                                                :key="index"
                                            >{{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                        </div>
                    </div>
                </div>
            </Form>
            <div style="margin-left:370px" slot="footer">
                <Button type="primary" @click="save('formData')"
                        :loading="loading">
                    保存
                </Button>
                <Button @click="close" >取消</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
    import {getAddressTree} from "@/projects/afs-core-business/api/organ/order-mng/businessOption";
    import {
        saveOrganPersonalGuarantor
    } from "@/projects/afs-core-business/api/organ/order-mng/customInfo";

    import * as utils from "../../../assets/js/utils";
    import {deepClone} from "../../../../../../../libs/utils/ObjectClone";
    import {getByTypes} from "@/projects/basic/api/admin/datadic";
    import {formatDate} from "../../../../../../../components/iview/components/date-picker/util";

    export default {
        name: "personalGuarantorInfo",
        data() {
            return {
                isShow:false,
                certTypeList: [],
                nationalityList: [],
                loading : false,
                titleName: '个人担保人信息',
                id: "",//编辑的时候修改id
                location: [],
                formData: {
                    id: "",
                    staffName: "",
                    nationality: "",
                    certType: "",
                    certNo: "",
                    sex: "",
                    birthDate: null,
                    personalAge: "",
                    maritalStatus: "",
                    careerInformation: "",
                    livingProvince: "",
                    livingCity: "",
                    livingLocation: [],
                    livingAddress: "",
                    contactNumber: "",
                    monthlyIncome: "",
                    personalAssets: "",
                    association: "",
                },
                formDataValiate: {
                    staffName: [
                        {required: true, message: "姓名不能为空", trigger: "blur"},
                        {
                            validator: (rule, value, callback) => {
                                utils.isNameValiate(rule, value, callback)
                            }, trigger: "change"
                        }
                    ],
                    sex: [{required: true, message: "性别不能为空", trigger: "change"}],
                    certType: [
                        {required: true, message: "证件类型不能为空", trigger: "change"}
                    ],
                    certNo: [
                        {required: true, message: '证件号码不能为空', trigger: 'change'},
                        {
                            validator: (rule, value, callback) => {
                                utils.doValidID(rule, value, callback)
                            }, trigger: "change"
                        }
                    ],
                    birthDate: [
                        {required: true, message: "出生日期不能为空", trigger: "blur", type: 'date'},
                    ],
                    personalAge: [
                        {required: true, message: "年龄不能为空", trigger: "blur"},
                    ],
                    nationality: [{required: true, message: "国籍不能为空", trigger: "change"}],
                    careerInformation: [
                        {required: true, message: "职业信息不能为空", trigger: "blur"},
                    ],
                    contactNumber: [
                        {required: true, message: "手机号不能为空", trigger: "blur"},
                        {
                            validator: (rule, value, callback) => {
                                utils.isMorePhoneValiate(rule, value, callback)
                            }, trigger: "blur"
                        }
                    ],
                    livingAddress: [
                        {required: true, message: "详情地址不能为空", trigger: "blur"}
                    ],
                    livingLocation: [
                        {required: true, message: "现所在地址-省市不能为空", trigger: "change", type: "array"}
                    ],
                    monthlyIncome: [
                        {required: true, message: "年收入不能为空", trigger: "blur"},
                        {
                            validator: (rule, value, callback) => {
                                utils.isMoneyValue(rule, value, callback)
                            }, trigger: "blur"
                        }
                    ],
                    personalAssets: [
                        {required: true, message: "年收入不能为空", trigger: "blur"},
                        {
                            validator: (rule, value, callback) => {
                                utils.isMoneyValue(rule, value, callback)
                            }, trigger: "blur"
                        }
                    ],
                    association: [{required: true, message: "与企业对关联关系不能为空", trigger: "change"}],
                },
                isCouple:false,
                //抛错信息
                commonErrorMessage:'',
                dataDic: {
                    //性别
                    sex: [],
                    //婚姻状况
                    maritalStatus: [],
                }
            }
        },

        props: {
            custId: {
                type: String
            },
            isShowModel: {
                type: Boolean,
                default: false
            },
            isShowDetails: {
                type: Boolean,
                required:false,
            },
            dataList: {},
            details: {},
            applyNo: {
                type: String,
            },
        },
        computed: {
        },
        watch:{
            isShowModel(val){
                if(val ){
                    this.isShow=true;
                }
            },
            dataList(val){
                if(val!=null){
                    this.queryPersonInfo();
                }
            }
        },
        created() {
            this.init()
        },
        mounted() {
        },
        methods: {
            init() {
                this.initGetAllDicData();
                this.getAddressTree();
            },
            checkType(val) {
                if (val == "00001") {
                    this.formDataValiate.certNo = [
                        {required: true, message: '证件号码不能为空', trigger: 'change'},
                        {
                            validator: (rule, value, callback) => {
                                utils.doValidID(rule, value, callback)
                            }, trigger: "change"
                        }
                    ]
                } else {
                    this.formDataValiate.certNo = [
                        {required: true, message: '证件号码不能为空', trigger: 'change'},
                    ]
                }
            },
            getAddressTree(){
                getAddressTree().then(res=>{
                    this.location = res.data[0].children;
                })
            },
            // 身份证验证
            validID(rule, value, callback) {
                // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
                let reg = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i;
                if (reg.test(value)) {
                    if (this.formData.certType == '00001') {
                        this.go(value.length);
                    }
                    callback()
                } else {
                    callback(new Error('身份证号码不正确'))
                }
            },
            //批量获取数据字典
            initGetAllDicData() {
                //数据字典
                let arr = ["certType","sex","nationality","maritalStatus","association",""];
                getByTypes(arr).then(res => {
                    if (res.code === "0000") {
                        this.dataDic = deepClone(res.data);
                    }
                });
            },
            selectCheckBox(value) {
                if (value === true) {
                    this.$refs["formData"].fields.forEach(function (e) {
                        if (e.prop === 'expiringDate') {
                            e.resetField()
                        }
                    })
                }
            },
            //编辑操作查询
            queryPersonInfo(){
                this.formData=this.dataList;
                let location = [];
                if (this.dataList.livingProvince !== "" && this.dataList.livingCity !== "") {
                    location[0] =this.dataList.livingProvince;
                    location[1] = this.dataList.livingCity;
                    this.formData.livingLocation = location;
                }
                if(this.formData.personalAge !== undefined && this.formData.personalAge !== null && this.formData.personalAge !== ''){
                    this.formData.personalAge = this.formData.personalAge.toString();
                    this.formData.monthlyIncome = this.formData.monthlyIncome.toString();
                    this.formData.personalAssets = this.formData.personalAssets.toString();
                }
            },
            upHidden() {
                this.$emit('hideForm', false)
            },
            // 点击保存数据
            save(name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        this.loading = true;
                        let s = '';
                        if (this.formData.birthDate != null && this.formData.birthDate !== '') {
                            let s1 = formatDate(new Date, "yyyy-MM-dd");
                            let s0 = formatDate(this.formData.birthDate, "yyyy-MM-dd");
                            if (s0 > s1) {
                                this.loading = false;
                                return this.$Message.error("出生日期必须小于当前时间");
                            }
                            s = formatDate(this.formData.birthDate, "yyyy-MM-dd");
                        }
                        let param = {
                            applyNo: this.applyNo,
                            //股东基础信息
                            organPersonalGuarantor: {
                                id: this.formData.id,
                                custId:this.custId,
                                applyNo:this.applyNo,//申请编号
                                staffName: this.formData.staffName,
                                nationality: this.formData.nationality,
                                certType: this.formData.certType,
                                certNo: this.formData.certNo,
                                sex: this.formData.sex,
                                birthDate: this.formData.birthDate,
                                personalAge: this.formData.personalAge,
                                maritalStatus: this.formData.maritalStatus,
                                careerInformation: this.formData.careerInformation,
                                livingProvince: this.formData.livingLocation[0],
                                livingCity: this.formData.livingLocation[1],
                                livingAddress: this.formData.livingAddress,
                                contactNumber: this.formData.contactNumber,
                                monthlyIncome: this.formData.monthlyIncome,
                                personalAssets: this.formData.personalAssets,
                                association: this.formData.association,
                            },
                        };
                        saveOrganPersonalGuarantor(param).then(res => {
                            if (res.code === "0000") {
                                this.loading = false;
                                this.$Message.success("操作成功");
                                //保存成功返回客户信息页面
                                this.cancel();
                            }
                        }).catch(() => {
                            this.loading = false;
                        });
                    }else {
                        //错误提示滚动
                        this.errorTipScorll('formData');
                        //抛出错误提示
                        this.$Message.warning(this.commonErrorMessage);
                    }
                })
            },
            // 错误信息提示滚动
            errorTipScorll(name) {
                for (let i in this.$refs[name].fields) {
                    if (this.$refs[name].fields[i].validateState === "error") {
                        this.$refs[name].fields[i].$el.scrollIntoView({
                            block: "center",
                            behavior: "smooth",
                            inline: "center"
                        });
                        this.commonErrorMessage=this.$refs[name].fields[i].validateMessage;
                        break;
                    }
                }
            },
            cancel() {
                this.isShow=false
                this.$emit("close-model", false);
            },
            close(){
                this.isShow=false
                this.$emit("close-model", "close");
            },
        }
    }
</script>
<style>
    .model .ivu-btn-success {
        background-color: green;
    }

    .model .ivu-modal {
        min-width: 1190px;
    }

    .commoninfo-model .ivu-modal-body {
        padding: 0 0 16px;
    }
    .ivu-btn > .ivu-icon + span {
        margin-left: -2px;
    }
</style>
<style scoped>
    .up {
        background-color: #fff;
    }

    .up > img {
        width: 16px;
        margin: 10px 20px 0 0;
        cursor: pointer;
    }

    .operBtn {
        text-align: center;
        margin-top: 10px;
    }

    .adresstype {
        border: 1px solid #DCDEE2;
        margin-bottom: 20px;
    }

    .adresstype h3 {
        background: #F6F6F6;
        border-bottom: 1px solid #DCDEE2;
        height: 40px;
        padding: 0 20px;
        margin-bottom: 10px;
        font-size: 13px;
        line-height: 40px;
    }

    .adresstype h3 > span {
        color: #EB9620;
        font-size: 12px;
        cursor: pointer;
        line-height: 20px;
        margin-top: 10px;
        float: right;
    }

    .adresstype h3 > span:hover {
        background: rgba(235, 150, 32, 0.25);
    }

    .adresstype h3 > span.del {
        color: #515a6e;
        margin-left: 20px;
    }

    .adresstype h3 > span.del:hover {
        color: #FF0010;
        background: transparent;
    }
</style>
<style lang="less">
    .QR-code-display {
        margin-top: 20px;
        display: flex;
        justify-content: flex-start;

        label {
            color: #333;
            width: 120px;
            text-align: right;
            padding-right: 12px;
        }

    }

    .QR-code-display-img {
        width: 195px;
        text-align: center;

        img {
            width: 195px;
        }
    }

    .model-box-max-height {
        max-height: calc(100vh - 232px);
        overflow: auto;
    }
    .authButtonClass {
        .ivu-btn-small {
            height: 24px;
            padding: 0 3px;
            font-size: 12px;
            border-radius: 3px;
        }
    }
</style>
