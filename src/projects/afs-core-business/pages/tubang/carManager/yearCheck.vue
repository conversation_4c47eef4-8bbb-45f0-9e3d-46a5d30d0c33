<template>
    <div>
        <Card>
            <Form v-show="showYear" :model="queryCriteria" :label-width="80" inline>
                <div class="search">
                    <FormItem label="车牌号码" prop="licensePlate">
                        <Input clearable type="text" v-model="queryCriteria.licensePlate" placeholder="请输入车牌号码"></Input>
                    </FormItem>
                    <Button type="primary" style="margin-top: 5px;" @click="singleQuery">查询</Button>
                </div>
            </Form>
            <div v-show="showNj">
                <Button v-if="$store.getters.permissions['action_group_yearcheck_add']" type="primary" @click="addClick" style="margin-bottom: 20px;">新增年检</Button>
            </div>
            <P v-show="showTitle">年检记录</P>
            <Table border :columns="columnsData" :data="tableData" :loading="loading" ref="myTable">
                <template slot-scope="{ row, index }" slot="operation">
                    <div style="display: flex;flex-direction: row;justify-content: space-around;">
                        <Button v-if="$store.getters.permissions['action_group_yearcheck_show']" type="primary"  @click="show(row)" style="margin-right: 10px;">查看</Button>
                        <Button v-if="$store.getters.permissions['action_group_yearcheck_edit']" type="primary"  @click="edit(row)" style="margin-right: 10px;">编辑</Button>
                        <Button v-if="$store.getters.permissions['action_group_yearcheck_continued']" type="primary"  @click="continued(row)" style="margin-right: 10px;">续检</Button>
                        <Button v-if="$store.getters.permissions['action_group_yearcheck_remove']" type="error"  @click="remove(row)">删除</Button>
                    </div>
                </template>
                <template slot-scope="{ row, index }" slot="payType">
                    <span>{{selectDictLabel(payTypeList,row.payType)}}</span>
                </template>
            </Table>
            <div style="margin-top: 20px;display: flex;justify-content: space-between;">
                <Page :current="pageData.pageNumber" :total="pageData.total"
                      :page-size="pageData.pageSize"
                      :page-size-opts="[10,20,50]"
                      @on-change="changePage" @on-page-size-change="changePageSize"
                      size="small" show-total show-elevator show-sizer>
                </Page>
                <Button type="primary" size="small" @click="exportDate"><Icon type="ios-download-outline"></Icon> 导出</Button>
            </div>
        </Card>
        <Modal width="800px" :mask-closable="false"
        v-model="addYearShowMoal"
        title="年检信息"
        @on-cancel="rightClose">
            <addYearCheck  ref="addYearCheck" @addCommit="addCommit" :operationType="operationType"></addYearCheck>
            <div slot="footer">
                <Button type="primary" :disabled="operationType=='look'" @click="confirm">保存</Button>
            </div>
        </Modal>
        <Modal v-model="modal" width="360">
            <p slot="header" style="color:#f60;text-align:center">
                <Icon type="information-circled"></Icon>
                <span>删除确认</span>
            </p>
            <div class="content" style="text-align:center">
                <p>请确认是否删除？</p>
            </div>
            <div slot="footer">
                <Button type="error" size="large" long :loading="modal_loading" @click="del">删除</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import {getInspectionInfoList,deleteInspectionInfo} from '@/projects/afs-core-business/api/tubang/carManager/yearCheck'
import {columnsData} from '@/projects/afs-core-business/api/tubang/carManager/yearCheckTableData'
import addYearCheck from "./components/addYearCheck"
import {deepClone} from "@/libs/utils/ObjectClone"
import {getDictDataByType} from "_p/basic/api/admin/datadic.js";

export default{
    data(){
        return{
            id:'',
            modal:false,
            modal_loading:false,
            operationType:'add', //add look edit continued
            addYearShowMoal:false,
            columnsData:columnsData,
            tableData:[],
            exportDates:[],
            defaultTableData:[],
            loading:false,
            pageData:{
                total:0,
                pageNumber:1,
                pageSize:10,
            },
            queryCriteria:{},
            waitRenewal:'',
            payTypeList:[]
        }
    },
    props:{
        showNj:{
            type: Boolean,
            default: true,
        },
        showTitle:{
            type: Boolean,
            default: false,
        },
        showYear:{
            type: Boolean,
            default: true,
        },
    },
    components:{
        addYearCheck
    },
    methods:{
        async exportDate(){
            await this.exportDateQueryCar();
            this.defaultTableData = deepClone(this.exportDates);
            this.defaultTableData.forEach(dataInfo =>{
                //添加制表符防止文本格式转换为数字格式
                dataInfo.applyNo = "\t" + dataInfo.applyNo;
                dataInfo.telephone = "\t" + dataInfo.telephone;
                dataInfo.engineNo = "\t" + dataInfo.engineNo;
            });
            this.$refs.myTable.exportCsv({
                filename: '年检记录',
                columns: this.columnsData.filter((col,index) => col.title != '操作'),
                data: this.defaultTableData.filter((col,index) => {
                    var payType = col.payType = this.selectDictLabel(this.payTypeList,col.payType);
                    return {payType}
                })
            })
        },
        initData(index,keyWord){
            this.loading = true;
            let parm ={
                'commonStatus':index,
                'keyWord':keyWord,
                'pageSize':this.pageData.pageSize,
                'pageNumber':this.pageData.pageNumber,
                'waitRenewal':this.waitRenewal
            }
            this.loading = true;
            getInspectionInfoList(parm).then((res)=>{
                this.loading = false;
                if(res.code==='0000'){
                    this.tableData = res.data.records;
                    this.pageData.total = res.data.total;
                }
            });
        },
        changePage(v){
            this.pageData.pageNumber = v;
            this.initData();
        },
        changePageSize(v){
            this.pageData.pageSize = v;
            this.pageData.pageNumber = 1;
            this.initData();
        },
        singleQuery(){
            this.loading = true;
            let parm ={
                'licensePlate':this.queryCriteria.licensePlate,
                'pageSize':this.pageData.pageSize,
                'pageNumber':1
            }
            this.loading = true;
            getInspectionInfoList(parm).then((res)=>{
                this.loading = false;
                if(res.code==='0000'){
                    this.tableData = res.data.records;
                    this.pageData.total = res.data.total;
                }
            });
        },
        async exportDateQueryCar(){
            this.loading = true;
            let parm ={
                'licensePlate':this.queryCriteria.licensePlate,
                'pageSize':-1,
                'pageNumber':1
            }
            this.loading = true;
            const res = await getInspectionInfoList(parm)
                this.loading = false;
                if(res.code==='0000'){
                    this.exportDates = res.data.records;

                }
        },
        addClick(){
            this.operationType = "add";
            this.addYearShowMoal = true;
            this.$refs.addYearCheck.initData();
            this.$refs.addYearCheck.defaultFirst();
            this.$refs.addYearCheck.random();
        },
        show(row){
            this.operationType = "look";
            this.addYearShowMoal = true;
            this.$refs.addYearCheck.initLook(row.id,row.carVin);
        },
        edit(row){
            this.operationType = "edit";
            this.addYearShowMoal = true;
            this.$refs.addYearCheck.initLookEdit(row.id);
        },
        continued(row){
            this.operationType = "continued";
            this.addYearShowMoal = true;
            this.$refs.addYearCheck.initContinued();
            this.$refs.addYearCheck.random();
        },
        del(){
            this.modal_loading = true;
            setTimeout(() => {
                deleteInspectionInfo(this.id).then((res)=>{
                    if(res.code==='0000'){
                        this.modal = false;
                        this.modal_loading = false;
                        this.$Message.success('删除成功');
                        this.pageData.pageSize = 10;
                        this.pageData.pageNumber =1;
                        this.initData();
                    }
                });
            }, 1000);
        },
        remove(row){
            this.modal = true;
            this.id = row.id;
        },
        addCommit(){
            this.addYearShowMoal = false;
            this.pageData.pageSize = 10;
            this.pageData.pageNumber = 1;
            this.initData();
        },
        confirm(){
            this.$refs.addYearCheck.confirm();
        },
        rightClose(){
            this.$refs.addYearCheck.myearlyIdInit();
        },
          //回旋数据字典
        selectDictLabel(datas, value) {
          if(Array.isArray(datas)){
            var actions = [];
            Object.keys(datas).some((key) => {
              if (datas[key].value == "" + value) {
                actions.push(datas[key].title);
                return true;
              }
            });
            return actions.join("");
          }
          return '';
        },
        initPayTypeArray(){
            getDictDataByType('payType').then((res)=>{
                debugger
                console.log("数据字典",res);
                if(res.code==="0000"){
                    this.payTypeList = res.data;
                }
            })
        },
    },
    created() {
        if (this.afs.getPageParams(this)) {
            this.waitRenewal = this.afs.getPageParams(this).waitRenewal ? this.afs.getPageParams(this).waitRenewal:'';
            console.log(this.waitRenewal,'this.waitRenewal')
        }
        this.initData();
        this.initPayTypeArray();
    }
}
</script>

<style>
</style>
