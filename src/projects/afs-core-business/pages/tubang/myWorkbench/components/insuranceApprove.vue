<template>
    <div>
        <Tabs type="card" style="margin: 0 auto;" @on-click="insuranceHistory" v-model="currentTab">
            <TabPane label="保单信息" name="name1">
                <div>
                    <Form  :disabled="opeartionType=='look'||opeartionType=='edit'||opeartionType=='renewal'" ref="headerForm" :model="headerForm" :rules="headerFormValidate" :label-width="100">
                        <Row>
                            <Col :span="12">
                                <FormItem label="车牌号" prop="licensePlate">
                                        <Input clearable v-model="headerForm.licensePlate" style="width: 200px;"></Input>
                                </FormItem>
                            </Col>
                            <Col :span="12">
                                <FormItem label="投保方式" prop="insuranceWay">
                                    <Select clearable v-model="headerForm.insuranceWay" style="width: 200px;">
                                        <Option v-for="item in insuranceWayArray" :value="item.value" :key="item.value">
                                            {{item.title}}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <Form :disabled="opeartionType=='look'" ref="commerInsuraneForm" :model="commerInsuraneForm"
                        :rules="commerInsuraneFormValidate" :label-width="100">
                        <Row>
                            <Col span="12">
                                <FormItem label="保单类型" prop="insuranceType">
                                    <Select clearable v-model="commerInsuraneForm.insuranceType" style="width: 200px;">
                                        <Option v-for="item in insuranceTypeList" :value="item.value" :key="item.value">
                                            {{item.title}}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span="12">
                                <FormItem label="车辆使用性质" prop="usePurpose">
                                    <Select clearable v-model="commerInsuraneForm.usePurpose" style="width: 200px;">
                                        <Option v-for="item in usePurposeArray" :value="item.value" :key="item.value">
                                            {{item.title}}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="保险公司" prop="insuranceCompany">
                                        <div class="tree-bar-box">
                                            <Input type="text"
                                                   v-model="commerInsuraneForm.insuranceCompany"
                                                   placeholder="请输入保险公司"
                                                   clearable
                                                   @on-change="search"
                                                   @on-focus="openTree" style="width: 200px;"
                                            />
                                            <div class="tree-mask" :class="aa === 1 ? 'open':'close'"
                                                 @click="closeTree"></div>
                                            <div class="tree-bar"
                                                 style="max-height: 99px;overflow: auto;width: 200px;z-index: 101"
                                                 v-show="this.aa===1">
                                                <Tree ref="tree" :data="treeData"
                                                      @on-select-change="selectTree"></Tree>
                                            </div>
                                        </div>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="承保分(支)公司" prop="branchCompany">
                                        <Input clearable v-model="commerInsuraneForm.branchCompany" style="width: 195px;"></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="保单号" prop="insuranceNo">
                                        <Input clearable v-model="commerInsuraneForm.insuranceNo" style="width: 200px;"></Input>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="保险金额" prop="insuranceAmt">
                                        <rui-number
                                            :formatter="value => currencyFormat(value,2)"
                                            :parser="value => value.replace(/\s?|(,*)/g, '')"
                                            v-model="commerInsuraneForm.insuranceAmt"
                                            style="width: 200px;">
                                            <span slot="append">元</span>
                                        </rui-number>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="保单生效日" prop="insuranceStartTime">
                                        <DatePicker clearable :transfer="true" type="date" v-model="commerInsuraneForm.insuranceStartTime"  :disabled="this.opeartionType == 'look'" :value="commerInsuraneForm.insuranceStartTime" style="width: 200px"
                                            @on-change="shangyeInsuranceTimeStart">

                                        </DatePicker>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="保单失效日" prop="insuranceEndTime">
                                        <DatePicker clearable type="date" v-model="commerInsuraneForm.insuranceEndTime" :disabled="this.opeartionType == 'look'" :value="commerInsuraneForm.insuranceEndTime" style="width: 200px"></DatePicker>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="续保支付方式" prop="payType">
                                        <Select clearable v-model="commerInsuraneForm.payType" style="width: 200px;">
                                            <Option v-for="item in payTypeArray" :value="item.value" :key="item.value">
                                                {{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                                <Col span="12">
                                    <FormItem label="续保年份" prop="renewalYear">
                                        <Select clearable v-model="commerInsuraneForm.renewalYear" style="width: 200px;">
                                            <Option v-for="item in RenewalYearArray" :value="item.value" :key="item.value">
                                                {{item.title}}
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                            <FormItem label="备注信息" prop="remark">
                                <Input type="textarea" v-model="commerInsuraneForm.remark" style="width: 585px"></Input>
                            </FormItem>
                    </Form>
                    <Table border :columns="columns" :data="dataList" :loading="loading" ref="approvalHistory">
                    </Table>
                </div>
            </TabPane>
            <TabPane label="影像资料" name="name2">
                <file-operation :key="insuranceId" ref="operation" :uploadParam="uploadParam" :isInt="canUpload" :path="path"
                    :read-only="readonly">
                </file-operation>
            </TabPane>
            <TabPane label="投保历史" name="name3" v-if="opeartionType=='look'">
                <Table border :columns="columnsData" :data="tableData" :loading="loading" ref="myTable">
                    <template slot-scope="{ row, index }" slot="insuranceType">
                        <span>{{selectDictLabel(insuranceTypeList,row.insuranceType)}}</span>
                    </template>
                </Table>
                <div style="margin-top: 20px;display: flex;justify-content: space-between;">
                    <Page :current="pageData.pageNumber" :total="pageData.total"
                          :page-size="pageData.pageSize"
                          :page-size-opts="[10,20,50]"
                          @on-change="changePage" @on-page-size-change="changePageSize"
                          size="small" show-total show-elevator show-sizer>
                    </Page>
                </div>
            </TabPane>
        </Tabs>
        <div style="width: 100%; height: 1px; background: #e8eaec;margin: 30px 0;"></div>
        <div >
            <Form :rules="submitTaskParamValidate" ref="submitTaskParam" :model="submitTaskParam">
                    <Row>
                        <FormItem label="审批选项" prop="operationType" :label-width="100" inline>
                            <Select clearable v-model="submitTaskParam.operationType" style="width: 200px">
                                <Option v-for="item in operationTypeArray" :value="item.value" :key="item.value">
                                    {{item.title}}
                                </Option>
                            </Select>
                        </FormItem>
                    </Row>
                    <Row>
                        <FormItem label="审批备注" prop="remark" :label-width="100" >
                            <Input type="textarea" style="width: 585px" v-model="submitTaskParam.remark"></Input>
                        </FormItem>
                    </Row>
            </Form>
        </div>
    </div>
</template>

<script>
import {
    queryInsuranceCompany,
    saveBatchInsuranceInfo,
    getCarInsuranceDetail,
    getInsuranceHistory,
    updateBatchInsuranceInfo,
    approveInsuranceInfo
} from '@/projects/afs-core-business/api/tubang/carManager/addPolicy'
import {  columnsData  } from '@/projects/afs-core-business/api/tubang/carManager/addPolicyTableData'
import {
    getDictDataByType
} from "_p/basic/api/admin/datadic.js"
import FileOperation from "_p/basic/pages/image/upload-file/fileOperation.vue"
import {deepClone} from "@/libs/utils/ObjectClone"
import { thinkLicensePlate,randomLongValue,getInsuranceGroupNo } from '@/projects/afs-core-business/api/tubang/common/common'
import {approvalHistory} from "@/projects/afs-core-business/api/tubang/carManager/approvalHistory.js"
export default{
    data() {
        return {
            currentInsuranceWay:'-1', //-1代表全部 0 商业险 1交强险
            currentTab:'name1',
            defaultHeaderForm:{
                licensePlate:'',
                insuranceWay:'',
            },
            headerForm:{},
            headerFormValidate:{
                licensePlate:[{ required: true, message: '请输入车牌号', trigger: 'blur' }],
                insuranceWay:[{ required: true, message: '请选择投保方式', trigger: 'change' }],
            },

            defaultCommerInsuraneForm:{
                insuranceCompany:'',
                branchCompany:'',
                insuranceNo:'',
                insuranceAmt:null,
                insuranceStartTime:'',
                insuranceEndTime:'',
                    remark:'',
                companyId:'',
                id:'',
            },
            commerInsuraneForm:{},//商业险
            submitTaskParam:{
                taskId:'',//任务id
                operationType:'',//操作动作
                remark:''//审批备注
            },//工作流任务提交参数
            submitTaskParamValidate:{
                operationType:[{ required: true, message: '请选择审批选项', trigger: 'change' }],
                remark:[{ required: true, message: '请输入审批备注', trigger: 'blur' }],
            },
            commerInsuraneFormValidate:{
                insuranceNo:[{ required: true, message: '请输入保单号', trigger: 'blur' }],
                    insuranceType:[{ required: true, message: '请选择保单类型', trigger: 'change' }],
                    insuranceCompany:[
                        { required: true, message: '请输入保险公司', trigger: 'blur' },
                        { required: true, message: '请输入保险公司', trigger: 'change' }
                    ],
                    branchCompany:[
                        { required: true, message: '请输入承保分(支)公司', trigger: 'blur' },
                        { required: true, message: '请输入承保分(支)公司', trigger: 'change' },
                    ],
                    insuranceAmt:[
                        { required: true, type:'number', message: '请输入保险金额', trigger: 'blur' },
                        { required: true, type:'number', message: '请输入保险金额', trigger: 'blur' }
                    ],
                    insuranceStartTime:[{ required: true,type:'date', message: '请输入保单生效日', trigger: 'blur' }],
                    payType:[
                        { required: true, message: '请选择续保支付方式', trigger: 'blur' },
                        { required: true, message: '请选择续保支付方式', trigger: 'change' }
                    ],
            },
            companyArray:[],//保险公司
            insuranceWayArray:[],//投保方式
            treeData:[],
            aa:0,
            othertreeData:[],
            bb:0,
            //影像资料
            uploadParam: {
                busiNo: "",
                belongNo: "",
                busiType: "carInsurance",
                busiData: {}
            },
            canUpload: true,
            path: "case",
            readonly: true,
            insuranceId:'',//保单id
            loading:false,
            options:[],
            licensePlateList:[],
            results: [],
            validNums: false,
            code:'',
            payTypeArray:[],
            RenewalYearArray:[],
            operationType:'',
            operationTypeArray:[],
            columnsData:columnsData,
            tableData:[],
            pageData:{
                total:0,
                pageNumber:1,
                pageSize:10,
            },
            carVin:'',
            insuranceTypeList:'',
            usePurposeArray:[],
                columns: [
                    {
                        title: "审批节点",
                        key: "disposeNodeName",
                        minWidth: 80,
                    },
                    {
                        title: "审批意见描述",
                        key: "approveSuggest",
                        minWidth: 100,
                    },
                    {
                        title: "审批人员",
                        key: "optUserName",
                        minWidth: 80,
                    },
                    {
                        title: "审批备注",
                        key: "approveRemark",
                        minWidth: 200,
                    },
                    {
                        title: "审批时间",
                        key: "approveEndTime",
                        type: 'datetime',
                        minWidth: 120,
                    }
                ],
                dataList: [],
        }
    },
    props: {
        opeartionType:{
            type:String,
            default:'look',
        }
    },
    components:{
    FileOperation
},
    computed: {

    },
    methods: {
        initInsuranceHistory(){
            let parm={
               'carVin' : this.carVin,
               'pageSize' : this.pageData.pageSize,
               'pageNumber' : this.pageData.pageNumber,
            }
            getInsuranceHistory(parm).then((res)=>{
                if(res.code==='0000'){
                    this.tableData = res.data.records;
                    this.pageData.total = res.data.total;
                }
            })
        },
        insuranceHistory(name){
            console.log(name,'initInsuranceHistory')
            if(name == 'name3'){
                this.initInsuranceHistory()
            }
        },
        changePage(v){
            this.pageData.pageNumber = v;
            this.initInsuranceHistory()
        },
        changePageSize(v){
            this.pageData.pageSize = v;
            this.pageData.pageNumber = 1;
            this.initInsuranceHistory()
        },
        initVerification(){
            this.validNums = false;
            this.results = this.$refs.operation.children;
            for(var i = 0; i < this.results.length; i++){
                console.log(this.results[i].needType,this.results[i].validNums,'....')
                if(this.results[i].needType == '1' && this.results[i].validNums == '0'){
                    this.validNums = true;
                }
            }
            console.log(this.results[1].needType,'needType')
        },
        random(){
            randomLongValue().then((res)=>{
                if(res.code==='0000'){
                    this.insuranceId = res.data;
                    this.initGetFilterParams();
                    console.log(this.insuranceId,res.data,'---->随机数');
                }
            })
        },
        defaultFirst(){
            this.currentTab = 'name1';
        },
        insuranceIdInit(){
            // this.insuranceId = '';
        },
        shangyeInsuranceTimeStart(val){
          this.commerInsuraneForm.insuranceStartTime = val;
          var date = new Date(val);
          console.log("val",date);
          var y = date.getFullYear() + 1;
          var m = (date.getMonth() + 1).toString().padStart(2, "0")
          var d = date.getDate().toString().padStart(2,"0");
          var time = y + '-' +m + '-' +d;
          this.commerInsuraneForm.insuranceEndTime = time;
        },
        handleSubmit() {
            let userRoles = this.$store.state.user.roles;
            //车管需要提交信息
            if(userRoles.includes('ROLE_CAR_MANAGER')){
                this.checkHeader();
            }
        },
        checkHeader(){
            this.$refs.headerForm.validate((valid) => {
                if(valid){
                    this.checkCommerIn();
                }
            });
        },
        checkCommerIn(){
            this.$refs.commerInsuraneForm.validate((valid) => {
                if(valid){
                //    this.checkcompulsoryIn();
                    this.postCommit();
                }
            });
        },
        approveInsuranceCheck(){
            if(this.submitTaskParam.operationType === ''){
                this.$Message.error("请选择审批选项");
                return false;
            }
            if((this.submitTaskParam.operationType === 'backToApply' || this.submitTaskParam.operationType === 'refuse') &&
                (this.submitTaskParam.remark === '')){
                this.$Message.error("退回或拒绝时，请输入审批备注");
                return false;
            }
            return true;
        },
        async approveInsurance(taskId){
            this.submitTaskParam.taskId = taskId;
            let parm = {
                'insuranceInfoList':[this.commerInsuraneForm],
                'submitTaskParam':this.submitTaskParam
            }
            const res = await approveInsuranceInfo(parm)
                if(res.code==="0000"){
                    this.$Message.success('审批完成');
                }

        },
        postCommit(){
            this.initVerification();
            this.commerInsuraneForm.licensePlate = this.headerForm.licensePlate;
            this.commerInsuraneForm.insuranceWay = this.headerForm.insuranceWay;
            this.commerInsuraneForm.id = this.insuranceId;
            this.commerInsuraneForm.companyId = this.getCompanyId(this.companyArray,this.commerInsuraneForm.insuranceCompany);
            let parm = {
                'insuranceInfoList':[this.commerInsuraneForm]
            }
            if(this.opeartionType=='edit'){
                if(this.validNums){
                    this.$Message.error('影像件资料不全');
                } else {
                    updateBatchInsuranceInfo(parm).then((res)=>{
                        if(res.code==="0000"){
                            this.code = res.code;
                        }
                    });
                }
            }
        },
        handleReset () {
            this.$refs.headerForm.resetFields();
            this.$refs.commerInsuraneForm.resetFields();
            this.headerForm = deepClone(this.defaultHeaderForm);
            this.commerInsuraneForm = deepClone(this.defaultCommerInsuraneForm);
            this.treeData = [];
            this.aa = 0;
            this.bb = 0;
            this.othertreeData = [];
            this.initQueryInsuranceCompany();
        },
        //新增初始化数据
        initData(){
            this.currentInsuranceWay = '-1';
            this.handleReset();
        },
        showDetail(id,   carVin){
            this.myInit(id);
            this.carVin = carVin;
            this.defaultFirst();
            //this.readonly = true;
            this.handleReset();
            this.insuranceId = id;
            this.initGetFilterParams();
            getCarInsuranceDetail(id).then((res)=>{
                if(res.code==="0000"){
                    this.commerInsuraneForm = res.data;
                    this.headerForm.licensePlate = res.data.licensePlate;
                    this.headerForm.insuranceWay = res.data.insuranceWay;
                }
            });
        },
        showDetailEdit(id){
            this.defaultFirst();
            //this.readonly = false;
            this.handleReset();
            this.insuranceId = id;
            this.initGetFilterParams();
            getCarInsuranceDetail(id).then((res)=>{
                if(res.code==="0000"){
                        this.commerInsuraneForm = res.data;
                        this.headerForm.licensePlate = res.data.licensePlate;
                        this.initGetFilterParams();
                    this.headerForm.insuranceWay = res.data.insuranceWay;
                }
            });
        },
        renewalClick(id){
            this.defaultFirst();
            //this.readonly = false;
            this.handleReset();
            this.headerForm.insuranceWay = this.insuranceWayArray[1].value;
            getCarInsuranceDetail(id).then((res)=>{
                if(res.code==="0000"){
                    console.log(res.data,'res.data')
                    this.headerForm.licensePlate = res.data.licensePlate;
                    this.currentInsuranceWay = "-1";
                }
            });
        },
        //回旋数据字典
        getCompanyId(datas, companyName) {
          if(Array.isArray(datas)){
            var actions = [];
            Object.keys(datas).some((key) => {
              if (datas[key].companyName == "" + companyName) {
                actions.push(datas[key].companyId);
                return true;
              }
            });
            return actions.join("");
          }
          return '';
        },
        getDictDataByType(){
            getDictDataByType('insuranceWay').then((res)=>{
                if(res.code==="0000"){
                    this.insuranceWayArray = res.data;
                }
            })
        },
        initPayType(){
            getDictDataByType('renewalPayType').then((res)=>{
                if(res.code==="0000"){
                    this.payTypeArray = res.data;
                }
            })
        },
        initRenewalYear(){
            getDictDataByType('renewalYear').then((res)=>{
                if(res.code==="0000"){
                    this.RenewalYearArray = res.data;
                }
            })
        },
        initgetDictDataByType(){
            getDictDataByType('usePurpose').then((res)=>{
                console.log("数据字典",res);
                if(res.code==="0000"){
                    this.usePurposeArray = res.data;
                }
            })
        },
        initGetDictDataByType(){
            getDictDataByType('insuranceType').then((res)=>{
                console.log("数据字典",res);
                if(res.code==="0000"){
                    this.insuranceTypeList = res.data;
                }
            })
        },
        initOperationTypeByType(){
            getDictDataByType('spType').then((res)=>{
                console.log("数据字典",res);
                if(res.code==="0000"){
                    this.operationTypeArray = res.data;
                }
            })
        },

        //回旋数据字典
        selectDictLabel(datas, value) {
          if(Array.isArray(datas)){
            var actions = [];
            Object.keys(datas).some((key) => {
              if (datas[key].value == "" + value) {
                actions.push(datas[key].title);
                return true;
              }
            });
            return actions.join("");
          }
          return '';
        },
        /*------商业险保险公司方法--------*/
        initQueryInsuranceCompany(){
            let parm = {
                companyName:'',
            }
            queryInsuranceCompany(parm).then((res)=>{
                if (res.data.length > 0) {
                    for (let i = 0; i < res.data.length; i++) {
                        let companyName = {"title": res.data[i].companyName};
                        this.treeData.push(companyName);
                        this.othertreeData.push(companyName);
                    }
                } else {
                    this.treeData = [];
                    this.othertreeData = [];
                }
            })
        },
        openTree() {
            console.log("openTreeopenTree");
            this.getTreeDate();
            this.aa = 1;
        },
        closeTree() {
            this.aa = 0;
            console.log("closeTreecloseTree");
        },
        search() {
            // 搜索树
            console.log("searchsearchsearch");
            this.treeData = [];
            this.getTreeDate();
        },
        selectTree(v) {
            console.log("selectTreeselectTree");
            if (v.length > 0) {
                // 转换null为""
                for (let attr in v[0]) {
                    if (v[0][attr] === null) {
                        v[0][attr] = "";
                    }
                }
                let str = JSON.stringify(v[0]);
                let data = JSON.parse(str);
                this.commerInsuraneForm.insuranceCompany = data.title;
                this.aa = 0;
            }
        },
        getTreeDate() {
            console.log("getTreeDategetTreeDate");
            let parm = {
                 companyName:this.commerInsuraneForm.insuranceCompany
            }
            queryInsuranceCompany(parm).then((res)=>{
                this.treeData = [];
                if (res.data.length > 0) {
                    for (let i = 0; i < res.data.length; i++) {
                        let companyName = {"title": res.data[i].companyName};
                        this.treeData.push(companyName);
                    }
                } else {
                    this.treeData = [];
                }
            })
        },
         //初始化附件上传参数
        initGetFilterParams(){
            this.uploadParam.busiNo = this.insuranceId
            this.uploadParam.belongNo = this.insuranceId
            this.isInt = true;
        },
        remoteMethod(query){
            if (query !== '') {
                this.loading = true;
                setTimeout(() => {
                    this.loading = false;
                    const list = this.licensePlateList.map(item => {
                        return {
                            value: item,
                            label: item
                        };
                    });
                    this.options = list.filter(item => item.label.toLowerCase().indexOf(query.toLowerCase()) > -1);
                }, 200);
            } else {
                    this.options = [];
            }
        },
        initThinkLicensePlate(){
            thinkLicensePlate(this.defaultHeaderForm.licensePlate).then((res)=>{
                let result = res.data;
                for(var i=0; i<result.length; i++){
                    this.licensePlateList.push(result[i].licensePlate)
                }
            })
        },
        accidentSubmit(){

        },
        // handleChange(value){
        //     let parm = {
        //         licensePlate:value,
        //     }
        //     getCarAndCustBaseInfo(parm).then((res)=>{
        //         if(res.code==="0000"){
        //             this.commerInsuraneForm.modelName = res.data.modelName;
        //             this.commerInsuraneForm.carVin = res.data.carVin;
        //             this.commerInsuraneForm.custName = res.data.custName;
        //             this.commerInsuraneForm.idNo = res.data.idNo;
        //             this.commerInsuraneForm.telephone = res.data.telephone;

        //             this.compulsoryInsuranceForm.modelName = res.data.modelName;
        //             this.compulsoryInsuranceForm.carVin = res.data.carVin;
        //             this.compulsoryInsuranceForm.custName = res.data.custName;
        //             this.compulsoryInsuranceForm.idNo = res.data.idNo;
        //             this.compulsoryInsuranceForm.telephone = res.data.telephone;
        //         }
        //     });
        // }
            //初始化审批记录列表
        async myInit(id) {
            let parm = {
                'businessNo':id
            }
            const res = await approvalHistory(parm)
            if (res.code == "0000") {
                this.dataList = res.data;
            }
        },
        initFormOperationType(){
            let userRoles = this.$store.state.user.roles;
            //车管可编辑审批页面信息
            if(userRoles.includes('ROLE_CAR_MANAGER')){
                this.opeartionType='edit';
                this.readonly = false;
            }
        }

    },
    created(){
        this.getDictDataByType();
        this.initPayType();
        this.initThinkLicensePlate();
        //初始化附件上传参数
        this.initGetFilterParams();
        this.initRenewalYear();
        this.initGetDictDataByType();
        this.initgetDictDataByType();
        this.initOperationTypeByType();
        this.initFormOperationType();
    }
}
</script>

<style lang="less" scoped>
    .commercial {
        background-color: #9CDBF2;
        padding: 5px;
        border-radius: 2px;
    }
    /* /deep/ .ivu-tabs .ivu-tabs-tabpane{
        width: 87%;
    } */
    .seat {
        margin-left: -85px;
    }
    .tag {
        margin-left: 14px;
    }
    .fileserver{
        display: flex;
        background-color: #1FB3F7;
        width: 218px;
        padding: 2px;
        color: #fff;

    }
    .fileserver1{
        display: flex;
        background-color: #1FB3F7;
        width: 218px;
        padding: 2px;
        color: #fff;
        margin-top: 10px;

    }
    .fileserver p {
        line-height: 24px;
        margin-right: 2px;
    }
    .fileserver1 p {
        line-height: 24px;
        margin-right: 14px;
    }
    .icon {
        color:#17948A;
        margin-right: 5px;
    }
    .imagedata{
        display: flex;
    }
    .carousel {
        width: 350px;
    }

    .tree-bar-box {
        position: relative;
        z-index: 100;

        .tree-bar {
            background-color: #fff;
            position: absolute;
            top: 30px;
            border: 1px solid rgb(220, 222, 226);
        }

        .tree-mask {
            display: none;
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 99;

            &.open {
                display: block;
            }
        }
    }
    /deep/ .ivu-select-disabled .ivu-select-selection{
        color: #6C7485;
    }
    /deep/ .ivu-input[disabled], fieldset[disabled] .ivu-input{
        color: #6C7485;
    }
    /deep/ .ivu-select-input[disabled]{
        color: #6C7485;
        -webkit-text-fill-color: #6C7485;
    }
    /deep/ .ivu-input-number-input[disabled]{
        color: #000;
    }
</style>
