<template>
    <div class="basis-info">
        <div class="basis_content">
            <Form ref="basicTypeForm" :model="basicTypeForm" :rules="basicTypeFormValidate" v-bind:disabled="this.typeCode == '0'"  :label-width="180">
                <!--基本信息   -->
                <Card>
                    <div class="common_br">
                        <h2 class="common_channel_title" >基本信息</h2>
                        <div class="common_content">
                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="合作商代码"  prop="channelCode" >
                                        <Input v-model="basicTypeForm.channelCode"  readonly="readonly"  class="w200" placeholder="系统自动生成"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="合作商名称" prop="channelFullName">
                                        <Input v-model.trim="basicTypeForm.channelFullName" disabled  class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="上线日期" prop="onlineDate" >
                                        <Date-picker type="date"  :value="basicTypeForm.onlineDate" @on-change="basicTypeForm.onlineDate=$event" class="w200"  placeholder="选择日期" ></Date-picker>

                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="统一社会信用代码" prop="socUniCrtCode">
                                        <Input v-model.trim="basicTypeForm.socUniCrtCode" disabled class="w200" @on-blur="checkBlackOrCode" placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="法人" prop="legalPerson">
                                        <Input v-model.trim="basicTypeForm.legalPerson"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="法人身份证号" prop="legalPersonIdCard">
                                        <Input v-model.trim="basicTypeForm.legalPersonIdCard"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="法人电话" prop="legalPersonTel">
                                        <Input v-model.trim="basicTypeForm.legalPersonTel"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="实际控制人" prop="actualController">
                                        <Input v-model.trim="basicTypeForm.actualController"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="实际控制人电话" prop="actualControllerTel">
                                        <Input v-model.trim="basicTypeForm.actualControllerTel"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="实际控制人身份证号" prop="actualControllerIdCard">
                                        <Input v-model.trim="basicTypeForm.actualControllerIdCard"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="管理员姓名" prop="channelAdmin">
                                        <Input v-model.trim="basicTypeForm.channelAdmin"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="管理员电话" prop="channelAdminTel">
                                        <Input v-model.trim="basicTypeForm.channelAdminTel"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="管理员邮箱" prop="channelAdminMail">
                                        <Input v-model.trim="basicTypeForm.channelAdminMail"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="管理员证件号码" prop="channelAdminIdCard">
                                        <Input v-model.trim="basicTypeForm.channelAdminIdCard"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="公司人数" prop="companiesNumber">
                                        <InputNumber :max="1000" :min="0" v-model="basicTypeForm.companiesNumber" class="w200" ></InputNumber>
                                    </FormItem>
                                </Col>

                                <Col :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="管理员省份" prop="channelAdminAddressValue">
                                        <Cascader :load-data="locationChange" :data="location" v-model="basicTypeForm.channelAdminAddressValue" class="w200" placeholder="下拉选择省市" >
                                        </Cascader>
                                    </FormItem>
                                </Col>
                                <Col :xs="24" :xl="18" :xxl="12">
                                    <FormItem label="管理员详细地址" prop="channelAdminAddressDetail" >
                                        <Input v-model.trim="basicTypeForm.channelAdminAddressDetail" placeholder="请输入详细地址" maxlength="50" class="w300"/>
                                    </FormItem>
                                </Col>

                                <Col :xs="24" :xl="12" :xxl="8">

                                    <FormItem label="注册省份" prop="channelAddressValue">
                                        <Cascader :load-data="locationChange" :data="location" v-model="basicTypeForm.channelAddressValue" class="w200" placeholder="下拉选择省市"  >
                                        </Cascader>
                                    </FormItem>

                                </Col>

                                <Col :xs="24" :xl="18" :xxl="12">
                                    <FormItem label="注册详细地址" prop="channelAddressDetail" >
                                        <Input v-model.trim="basicTypeForm.channelAddressDetail" placeholder="请输入详细地址" maxlength="50"  class="w300"/>
                                    </FormItem>
                                </Col>

                                <Col :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="办公地址" prop="officeAddressValue">
                                        <Cascader :load-data="locationChange" :data="location"  @on-change="onChangeLocation"  v-model="basicTypeForm.officeAddressValue" class="w200" placeholder="下拉选择省市" >
                                        </Cascader>
                                    </FormItem>
                                </Col>

                                <Col :xs="24" :xl="18" :xxl="10">
                                    <FormItem label="办公详细地址" prop="officeAddressDetail" >
                                        <Input v-model.trim="basicTypeForm.officeAddressDetail" @on-blur="onBlurLocation" placeholder="请输入详细地址" maxlength="50" class="w300"/>
                                    </FormItem>
                                </Col>

                                <Col span="5" offset="1">
                                    <Button type="primary" :disabled="!canOpenMapModal" @click="openMapModal">获取经纬度</Button>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="地址经度" prop="longitude">
                                        <Input v-model.trim="basicTypeForm.longitude"    class="w200"  placeholder="取办公地址"/>
                                    </FormItem>
                                </Col>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="地址纬度" prop="latitude">
                                        <Input v-model.trim="basicTypeForm.latitude"    class="w200"  placeholder="取办公地址"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="经纬度范围（km）" prop="longitudeLatitudeRange">
                                        <Input v-model.trim="basicTypeForm.longitudeLatitudeRange"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="经营场所面积(m²)" prop="businessArea">
                                        <Input v-model.trim="basicTypeForm.businessArea"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="汇款对象" prop="paymentObject">
                                        <RadioGroup v-model="basicTypeForm.paymentObject"  @on-change="checkPaymentObject"   >
                                            <Radio :false-value="''"  :key="radio.id" :label="radio.value" :true-value="radio.value" disabled  v-for="(radio) in paymentObjectList">{{radio.title}} </Radio>
                                        </RadioGroup>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="注册日期" prop="registrationDate" >
                                        <Date-picker type="date" :value="basicTypeForm.registrationDate" @on-change="basicTypeForm.registrationDate=$event"  class="w200"  placeholder="选择日期" ></Date-picker>
                                    </FormItem>
                                </Col>
                            </Row>
                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="注册资金(单位：元)" prop="registeredCapital">
                                           <rui-number
                                                                    :active-change="true"
                                                                    :formatter="value => currencyFormat(value,2)"
                                                                    :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                    v-model="basicTypeForm.registeredCapital"
                                                                    class="w200"
                                                                    placeholder="请输入"
                                                                >
                                                                </rui-number>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="实收资本(单位：元)" prop="subscribedCapital">
                                           <rui-number
                                                                    :active-change="true"
                                                                    :formatter="value => currencyFormat(value,2)"
                                                                    :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                    v-model="basicTypeForm.subscribedCapital"
                                                                    class="w200"
                                                                    placeholder="请输入"
                                                                >
                                                                </rui-number>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
<!--                                    <FormItem label="主营品牌" prop="mainBrand">-->
<!--                                        <Input @on-blur="search" clearable placeholder="输入搜索" class="w200" v-model="searchKey"/>-->
<!--                                        <div style="height:200px;overflow-y:auto">-->
<!--                                            <Tree :data="baseData"  ref="baseTree" show-checkbox v-model="basicTypeForm.mainBrand" @on-check-change="changeMainBrandSelect"	  multiple> </Tree>-->
<!--                                        </div>-->
<!--                                    </FormItem>-->
                                    <FormItem label="主营品牌" prop="mainBrand">
                                        <Input v-model="searchKey" @on-search="mainBrandSearch" search enter-button class="w200"
                                               placeholder="输入搜索"/>
                                        <div style="height:200px;overflow-y:auto">
                                            <SelectTree
                                                ref="mainBrand"
                                                v-model='basicTypeForm.mainBrand'
                                                :data='baseData'
                                                :isInit='mainBrandTreeInit'
                                                :isSearch="isMainBrandTreeSearch"
                                                :searchKey="searchKey"
                                                :disabled="typeCode === '0'"
                                                @closeSearch="closeMainBrandSearch"
                                            />
                                        </div>
                                    </FormItem>
                                </Col>

                            </Row>
                        </div>
                    </div>
                </Card>

                <Card>
                    <div class="common_br">
                        <h2 class="common_channel_title" >资质信息</h2>
                        <div class="common_content">
                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="渠道归属" prop="channelBelong" >
                                        <Select v-model="basicTypeForm.channelBelong" placeholder="请选择" disabled class="w200">
                                            <Option :value="item.value" v-for="(item,index) in channelBelongList" :key="index"> {{item.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="业务类型" prop="businessType" >
                                        <Checkbox-group  v-model="basicTypeForm.businessType" @on-change="checkBusinessType">
                                            <Checkbox label="01" disabled >新车</Checkbox>
                                            <Checkbox label="02" disabled>二手车</Checkbox>
                                        </Checkbox-group>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="层级最大数目" prop="hierarchy">
                                        <InputNumber :max="1000" :min="0" v-model="basicTypeForm.hierarchy"></InputNumber>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="资产净值(单位：元)" prop="netAssetValue">
                                    <rui-number
                                                                    :active-change="true"
                                                                    :formatter="value => currencyFormat(value,2)"
                                                                    :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                    v-model="basicTypeForm.netAssetValue"
                                                                    class="w200"
                                                                    placeholder="请输入"
                                                                >
                                                                </rui-number>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="经营年限(年)" prop="businessLife">
                                        <Input v-model.trim="basicTypeForm.businessLife"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="Gps厂商授权" prop="gpsVendorAuthor">
                                        <Select v-model="basicTypeForm.gpsVendorAuthor"  multiple allow-create @on-create="handgps_vendor_author">
                                            <Option :value="item.value" v-for="(item,index) in gpsvendorauthorList" :key="index"> {{item.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="资产负债率(%)" prop="assetLiabilityRatio">
                                        <Input v-model.trim="basicTypeForm.assetLiabilityRatio"   maxlength="2" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="上一年度经营利润(单位：元)" prop="operatingProfit">
                                       <rui-number
                                                                    :active-change="true"
                                                                    :formatter="value => currencyFormat(value,2)"
                                                                    :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                    v-model="basicTypeForm.operatingProfit"
                                                                    class="w200"
                                                                    placeholder="请输入"
                                                                >
                                                                </rui-number>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="Gps安装方式" prop="gpsInstalMode">
                                        <Select v-model="basicTypeForm.gpsInstalMode"  multiple allow-create @on-create="handgps_instal_mode">
                                            <Option :value="item.value" v-for="(item,index) in gpsinstalmodeList" :key="index"> {{item.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="业绩(单)" prop="achievement">
                                        <Input v-model.trim="basicTypeForm.achievement"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="利润率增长率(%)" prop="proGrowthRate">
                                        <Input v-model.trim="basicTypeForm.proGrowthRate"   maxlength="2" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="销售收入增长率(%)" prop="saleIncreaseRate">
                                        <Input v-model.trim="basicTypeForm.saleIncreaseRate"   maxlength="2" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col  :xs="24" :xl="12" :xxl="8">
                                    <FormItem label="流动比率(%)" prop="currentRatio">
                                        <Input v-model.trim="basicTypeForm.currentRatio"    maxlength="2" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                        </div>
                    </div>
                </Card>

                <div v-if="this.showGroupNewCar=='1'" v-bind:disabled="this.businessTypeDisabled == '0'" >
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >新车优质信息</h2>
                            <div class="common_content" style="">
                                <Row >
                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="优质等级" prop="qualityGrade"  >
                                            <Select v-model="basicTypeForm.qualityGrade"  placeholder="优质等级"  class="w200" @on-change="checkQualityGrade"  >
                                                <Option :value="item.value" v-for="(item,index) in qualityGradeList" :key="index"> {{item.title}}</Option>
                                            </Select>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <Form-item label="有效期" prop="validDate" :rules="Number(basicTypeForm.qualityGrade) != 5?validDate:notValidDate" >
                                            <DatePicker :value="basicTypeForm.validDate" @on-change="basicTypeForm.validDate=$event" type="daterange"  format="yyyy-MM-dd"  placeholder="选择起止时间"
                                                        style="width: 200px" >
                                            </DatePicker>
                                        </Form-item>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Card>
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >新车风控信息</h2>
                            <div class="common_content" style="">
                                <Row>
                                    <Col span="12">
                                        <FormItem label="车辆类型" prop="carType">
                                            <Checkbox-group  v-model="basicTypeForm.carType"  >
                                                <Checkbox label="00" >乘用车</Checkbox>
                                                <Checkbox label="01" >LCV</Checkbox>
                                                <Checkbox label="02" >中卡</Checkbox>
                                                <Checkbox label="03" >重卡</Checkbox>
                                            </Checkbox-group>
                                        </FormItem>
                                    </Col>

                                    <Col span="6">
                                        <FormItem label="账号最大数量" prop="accountMaxNum">
                                            <InputNumber :max="1000" :min="0" v-model="basicTypeForm.accountMaxNum"  style="width: 200px"  ></InputNumber>
                                        </FormItem>
                                    </Col>

                                    <Col span="6">
                                        <FormItem label="见证人最大数量" prop="witnessMaxNum">
                                            <InputNumber :max="10000" :min="0" v-model="basicTypeForm.witnessMaxNum"  style="width: 200px"  ></InputNumber>
                                        </FormItem>
                                    </Col>

                                </Row>
                                <Row>
                                    <Col span="12">
                                        <FormItem label="评级" prop="channelGrade" >
                                            <Select v-model="basicTypeForm.channelGrade" placeholder="评级" class="w200"  @on-change="checkChannelGrade" :label-in-value="true" >

                                                <Option :value="item.value" v-for="(item,index) in channelGradeList" :key="index"> {{item.title}}</Option>
                                            </Select>
                                        </FormItem>
                                    </Col>

                                    <Col span="12">
                                        <FormItem label="保证金(单位：元)" prop="channelDeposit">
                                           <rui-number
                                                                        :active-change="true"
                                                                        :disabled="this.businessTypeNewInfo == '0' "
                                                                        :formatter="value => currencyFormat(value,2)"
                                                                        :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                        v-model="basicTypeForm.channelDeposit"
                                                                        class="w200"
                                                                        placeholder="请输入"
                                                                    >
                                                                    </rui-number>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度判断 -->
                                    <Col span="12">
                                        <FormItem label="是否控制额度" prop="isLimitPut">
                                            <RadioGroup v-model="basicTypeForm.isLimitPut"  @on-change="checkLimitPut()" >
                                                <Radio label="1" v-bind:disabled="this.businessTypeNewInfo == '0'">是</Radio>
                                                <Radio label="0" v-bind:disabled="this.businessTypeNewInfo == '0'">否</Radio>
                                            </RadioGroup>
                                        </FormItem>`
                                    </Col>

                                    <!-- 先放后抵额度判断 -->
                                    <Col span="12">
                                        <FormItem label="是否控制额度" prop="isLimitPledge">
                                            <RadioGroup v-model="basicTypeForm.isLimitPledge" @on-change="checkLimitPut()"  >
                                                <Radio label="1" v-bind:disabled="this.businessTypeNewInfo == '0'">是</Radio>
                                                <Radio label="0" v-bind:disabled="this.businessTypeNewInfo == '0'">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <Col span="12">
                                        <FormItem label="签放额度(单位：元)" prop="limitPut" :rules="Number(basicTypeForm.isLimitPut)?limitPut:notLimitPut">
                                           <rui-number
                                                                        :active-change="true"
                                                                        :disabled="this.businessTypeNewInfo == '0'"
                                                                        :formatter="value => currencyFormat(value,2)"
                                                                        :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                        v-model="basicTypeForm.limitPut"
                                                                        class="w200"
                                                                        placeholder="请输入"
                                                                    >
                                                                    </rui-number>
                                            </FormItem>
                                    </Col>


                                    <Col span="12">
                                        <FormItem label="先放后抵额度(单位：元)" prop="limitPledge" :rules="Number(basicTypeForm.isLimitPledge)?limitPledge:notLimitPledge">
                                              <rui-number
                                                                           :active-change="true"
                                                                           :disabled="this.businessTypeNewInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPledge"
                                                                           class="w200"
                                                                           placeholder="请输入"
                                                                       >
                                                                       </rui-number>
                                         </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度 -->
                                    <Col span="12">
                                        <FormItem label="临时额度(单位：元)" prop="temporaryLimitPut">
                                             <rui-number
                                                                          :active-change="true"
                                                                          :disabled="this.businessTypeNewInfo == '0' "
                                                                          :formatter="value => currencyFormat(value,2)"
                                                                          :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                          v-model="basicTypeForm.temporaryLimitPut"
                                                                          class="w200"
                                                                          placeholder="请输入"
                                                                      >
                                                                      </rui-number>
                                        </FormItem>
                                    </Col>

                                    <!-- 先放后抵额度 -->
                                    <Col span="12">
                                        <FormItem label="临时额度(单位：元)" prop="temporaryLimitPledge">
                                              <rui-number
                                                                           :active-change="true"
                                                                           :disabled="this.businessTypeNewInfo == '0' "
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.temporaryLimitPledge"
                                                                           class="w200"
                                                                           placeholder="请输入"
                                                                       >
                                                                       </rui-number>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度有效期 -->
                                    <Col span="12">
                                        <Form-item label="有效期" prop="limitPutTime" >
                                            <Date-picker type="date"  :value="basicTypeForm.limitPutTime" @on-change="basicTypeForm.limitPutTime=$event" v-bind:disabled="this.businessTypeNewInfo == '0'"   class="w200"  placeholder="选择日期" ></Date-picker>
                                        </Form-item>
                                    </Col>

                                    <!-- 先放后抵额度有效期 -->
                                    <Col span="12">
                                        <Form-item label="有效期" prop="limitPledgeTime" >
                                            <Date-picker type="date"  :value="basicTypeForm.limitPledgeTime" @on-change="basicTypeForm.limitPledgeTime=$event" v-bind:disabled="this.businessTypeNewInfo == '0'"  class="w200"  placeholder="选择日期" ></Date-picker>
                                        </Form-item>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度占用 -->
                                    <Col span="12">
                                        <FormItem label="占用额度(单位：元)" prop="limitPutTake">
                                              <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeNewInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPutTake"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                          </FormItem>
                                    </Col>

                                    <!-- 先放后抵额度占用 -->
                                    <Col span="12">
                                        <FormItem label="占用额度(单位：元)" prop="limitPledgeTake">
                                              <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeNewInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPledgeTake"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                          </FormItem>
                                    </Col>

                                </Row>

                                <Row>
                                    <!-- 剩余签放额度 -->
                                    <Col span="12">
                                        <FormItem label="剩余额度(单位：元)" prop="limitPutResidue">
                                               <rui-number
                                                                            :active-change="true"
                                                                            readonly="readonly"
                                                                            :disabled="this.businessTypeNewInfo == '0' "
                                                                            :formatter="value => currencyFormat(value,2)"
                                                                            :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                            v-model="basicTypeForm.limitPutResidue"
                                                                            class="w200"
                                                                            placeholder="系统自动计算"
                                                                        >
                                                                        </rui-number>
                                           </FormItem>
                                    </Col>

                                    <!-- 剩余先放后抵额度 -->
                                    <Col span="12">
                                        <FormItem label="剩余额度(单位：元)" prop="limitPledgeResidue">
                                              <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeNewInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPledgeResidue"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                         </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <Col span="12">
                                        <FormItem label="区域经理" prop="customerManager">
                                            <Select v-model="basicTypeForm.customerManager"  placeholder="区域经理" class="w200">
                                                <Option v-for="(item,index) in regionalManager" :value="item.userName" :label="item.userRealName" :key="index" >
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Card>
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >新车授权区域/授权车型</h2>
                            <div class="common_content" style="">
                                <Row>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="区域是否生效" prop="authRegionSwitch">
                                                <RadioGroup v-model="basicTypeForm.authRegionSwitch"   >
                                                    <Radio label="1">是</Radio>
                                                    <Radio label="0">否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>

                                        <Col  :xs="24" :xl="12" :xxl="8">
                                            <FormItem label="车型是否生效" prop="authVehicleTypeSwitch">
                                                <RadioGroup v-model="basicTypeForm.authVehicleTypeSwitch"    >
                                                    <Radio label="1" >是</Radio>
                                                    <Radio label="0" >否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                    <Row>
                  <!--                        <Col span="12" style="padding-left: 180px;margin-bottom: 10px;">-->
<!--                                            <Input @on-blur="searchMainArea" clearable placeholder="输入搜索" class="w200" v-model="searchAreasDataKey"/>-->
                  <!--                            <Input v-model="searchAreasDataKey" @on-search="searchMainArea" search enter-button-->
                   <!--                                  class="w200"-->
                   <!--                                  placeholder="输入搜索"/>-->
                    <!--                      </Col>-->

                                        <Col span="12" style="padding-left: 180px;margin-bottom: 10px;">
<!--                                            <Input @on-blur="searchCarsData" clearable placeholder="输入搜索" class="w200" v-model="searchCarsDataKey"/>-->
                                            <Input v-model="searchCarsDataKey" @on-search="searchCarsData" search enter-button
                                                   class="w200"
                                                   placeholder="输入搜索"/>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="12" style="height:200px;overflow-y:auto" >
<!--                                            <FormItem label="" prop="mainArea" >-->
<!--                                                <Tree :data="areasData" ref="tree"  show-checkbox v-model="basicTypeForm.mainArea" @on-check-change="changeAreasDataSelect"  multiple > </Tree>-->
<!--                                            </FormItem>-->
                                            <FormItem label="" prop="mainArea">
                                                <SelectTree ref="mainArea" v-model='basicTypeForm.mainArea' :data='areasData'
                                                            :isInit='isAreasDataInit'
                                                            :disabled="typeCode === '0'"
                                                            :isSearch="isAreasDataSearch" :searchKey="searchAreasDataKey"
                                                            @closeSearch="closeSearch" :dataKeys="dataKeys" :setting="setting"/>
                                            </FormItem>
                                        </Col>

                                        <Col span="12" style="height:200px;overflow-y:auto" >
<!--                                            <FormItem label="" prop="mainCar" >-->
<!--                                                <Tree :data="carsData" ref="carstree" show-checkbox v-model="basicTypeForm.mainCar" @on-check-change="changeCarsDataSelect" multiple  > </Tree>-->
<!--                                            </FormItem>-->
                                            <FormItem label="" prop="mainCar">
                                                <SelectTree ref="mainCar" v-model='basicTypeForm.mainCar' :data='carsData'
                                                            :isInit='isCarsDataInit'
                                                            :disabled="typeCode === '0'"
                                                            :isSearch="isCarTreeSearch" :searchKey="searchCarsDataKey"
                                                            @closeSearch="closeSearch"/>
                                            </FormItem>
                                        </Col>

                                    </Row>
                                </Row>
                                <div>
                                    <a class="select-clear" @click="showAreasNewCar">选择渠道展业区域查看</a>
                                </div>
                            </div>
                        </div>
                    </Card>
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >新车开关控制</h2>
                            <div class="common_content" >
                                <Row>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="经纬度是否控制" prop="longitudeLatitudeSwitch">
                                            <RadioGroup v-model="basicTypeForm.longitudeLatitudeSwitch"     >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="业务人员关联车商" prop="personRelCardealerSwitch">
                                            <RadioGroup v-model="basicTypeForm.personRelCardealerSwitch"    >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="进件选择车商" prop="choiceCardealerSwitch">
                                            <RadioGroup v-model="basicTypeForm.choiceCardealerSwitch"     >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                </Row>
                            </div>
                        </div>
                    </Card>
                </div>

                <div v-if="this.showGroupOldCar=='1'" >
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >二手车优质信息</h2>
                            <div class="common_content" style="">
                                <Row>
                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="优质等级" prop="qualityGradeOld"   >
                                            <Select v-model="basicTypeForm.qualityGradeOld" placeholder="优质等级" @on-change="checkQualityGrade()" class="w200">
                                                <Option :value="item.value" v-for="(item,index) in qualityGradeList" :key="index"> {{item.title}}</Option>
                                            </Select>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <Form-item label="有效期" prop="validDateOld" :rules="Number(basicTypeForm.qualityGradeOld) != 5?validDateOld:notValidDateOld" >
                                            <DatePicker :value="basicTypeForm.validDateOld" @on-change="basicTypeForm.validDateOld=$event" type="daterange" format="yyyy-MM-dd"  placeholder="选择起止时间"
                                                        style="width: 200px">
                                            </DatePicker>
                                        </Form-item>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Card>
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >二手车风控信息</h2>
                            <div class="common_content" style="">
                                <Row>
                                    <Col span="12">
                                        <FormItem label="车辆类型" prop="carTypeOld">
                                            <Checkbox-group  v-model="basicTypeForm.carTypeOld" >
                                                <!-- <Checkbox label="00" >乘用车</Checkbox>
                                                <Checkbox label="01" >LCV</Checkbox>
                                                <Checkbox label="02" >中卡</Checkbox>
                                                <Checkbox label="03" >重卡</Checkbox> -->
                                                <Checkbox label="1" >商用车</Checkbox>
                                                <Checkbox label="2" >乘用车</Checkbox>
                                                <Checkbox label="02">中卡</Checkbox>
                                                <Checkbox label="03">重卡</Checkbox>
                                                <Checkbox label="4">工程机械</Checkbox>
                                            </Checkbox-group>
                                        </FormItem>
                                    </Col>

                                    <Col span="6">
                                        <FormItem label="账号最大数量" prop="accountMaxNumOld">
                                            <InputNumber :max="1000" :min="0" v-model="basicTypeForm.accountMaxNumOld" class="w200" ></InputNumber>
                                        </FormItem>
                                    </Col>
                                    <Col span="6">
                                        <FormItem label="见证人最大数量" prop="witnessMaxNumOld">
                                            <InputNumber :max="10000" :min="0" v-model="basicTypeForm.witnessMaxNumOld" class="w200" ></InputNumber>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <Col span="12">
                                        <FormItem label="评级" prop="channelGradeOld"  >
                                            <Select v-model="basicTypeForm.channelGradeOld" placeholder="评级" class="w200" @on-change="checkChannelGradeOld" :label-in-value="true"  >
                                                <Option :value="item.value"  v-for="(item,index) in channelGradeList" :key="index"> {{item.title}}</Option>
                                            </Select>
                                        </FormItem>
                                    </Col>

                                    <Col span="12">
                                        <FormItem label="保证金(单位：元)" prop="channelDepositOld">
                                                <rui-number
                                                                        :active-change="true"
                                                                        :disabled="this.businessTypeOldInfo == '0'"
                                                                        :formatter="value => currencyFormat(value,2)"
                                                                        :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                        v-model="basicTypeForm.channelDepositOld"
                                                                        class="w200"
                                                                        placeholder="请输入"
                                                                    >
                                                                    </rui-number>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度判断 -->
                                    <Col span="12">
                                        <FormItem label="是否控制额度" prop="isLimitPutOld">
                                            <RadioGroup v-model="basicTypeForm.isLimitPutOld" @on-change="checkLimitPut()" >
                                                <Radio label="1" v-bind:disabled="this.businessTypeOldInfo == '0'" >是</Radio>
                                                <Radio label="0" v-bind:disabled="this.businessTypeOldInfo == '0'" >否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                    <!-- 先放后抵额度判断 -->
                                    <Col span="12">
                                        <FormItem label="是否控制额度" prop="isLimitPledgeOld">
                                            <RadioGroup v-model="basicTypeForm.isLimitPledgeOld" @on-change="checkLimitPut()" >
                                                <Radio label="1" v-bind:disabled="this.businessTypeOldInfo == '0'" >是</Radio>
                                                <Radio label="0" v-bind:disabled="this.businessTypeOldInfo == '0'">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <Col span="12">
                                        <FormItem label="签放额度(单位：元)" prop="limitPutOld" :rules="Number(basicTypeForm.isLimitPutOld)?limitPutOld:notLimitPutOld" >
                                                <rui-number
                                                                            :active-change="true"
                                                                            :disabled="this.businessTypeOldInfo == '0' "
                                                                            :formatter="value => currencyFormat(value,2)"
                                                                            :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                            v-model="basicTypeForm.limitPutOld"
                                                                            class="w200"
                                                                            placeholder="请输入"
                                                                        >
                                                                        </rui-number>
                                             </FormItem>
                                    </Col>

                                    <Col span="12">
                                        <FormItem label="先放后抵额度(单位：元)" prop="limitPledgeOld" :rules="Number(basicTypeForm.isLimitPledgeOld)?limitPledgeOld:notLimitPledgeOld">
                                              <rui-number
                                                                          :active-change="true"
                                                                          :disabled="this.businessTypeOldInfo == '0' "
                                                                          :formatter="value => currencyFormat(value,2)"
                                                                          :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                          v-model="basicTypeForm.limitPledgeOld"
                                                                          class="w200"
                                                                          placeholder="请输入"
                                                                      >
                                                                      </rui-number>
                                             </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度 -->
                                    <Col span="12">
                                        <FormItem label="临时额度(单位：元)" prop="temporaryLimitPutOld">
                                               <rui-number
                                                                           :active-change="true"
                                                                           :disabled="this.businessTypeOldInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.temporaryLimitPutOld"
                                                                           class="w200"
                                                                           placeholder="请输入"
                                                                       >
                                                                       </rui-number>
                                             </FormItem>
                                    </Col>

                                    <!-- 先放后抵额度 -->
                                    <Col span="12">
                                        <FormItem label="临时额度(单位：元)" prop="temporaryLimitPledgeOld">
                                              <rui-number
                                                                          :active-change="true"
                                                                          :disabled="this.businessTypeOldInfo == '0' "
                                                                          :formatter="value => currencyFormat(value,2)"
                                                                          :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                          v-model="basicTypeForm.temporaryLimitPledgeOld"
                                                                          class="w200"
                                                                          placeholder="请输入"
                                                                      >
                                                                      </rui-number>
                                          </FormItem>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度有效期 -->
                                    <Col span="12">
                                        <Form-item label="有效期" prop="limitPutTimeOld" >
                                            <Date-picker type="date" :value="basicTypeForm.limitPutTimeOld" @on-change="basicTypeForm.limitPutTimeOld=$event" v-bind:disabled="this.businessTypeOldInfo == '0'" class="w200"  placeholder="选择日期" ></Date-picker>
                                        </Form-item>
                                    </Col>

                                    <!-- 先放后抵额度有效期 -->
                                    <Col span="12">
                                        <Form-item label="有效期" prop="limitPledgeTimeOld" >
                                            <Date-picker type="date" :value="basicTypeForm.limitPledgeTimeOld" @on-change="basicTypeForm.limitPledgeTimeOld=$event" v-bind:disabled="this.businessTypeOldInfo == '0'" class="w200"  placeholder="选择日期" ></Date-picker>
                                        </Form-item>
                                    </Col>
                                </Row>

                                <Row>
                                    <!-- 签放额度占用 -->
                                    <Col span="12">
                                        <FormItem label="占用额度(单位：元)" prop="limitPutTakeOld">
                                               <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeOldInfo == '0' "
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPutTakeOld"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                              </FormItem>
                                    </Col>

                                    <!-- 先放后抵额度占用 -->
                                    <Col span="12">
                                        <FormItem label="占用额度(单位：元)" prop="limitPledgeTakeOld">
                                               <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeOldInfo == '0' "
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPledgeTakeOld"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                              </FormItem>
                                    </Col>

                                </Row>

                                <Row>
                                    <!-- 剩余签放额度 -->
                                    <Col span="12">
                                        <FormItem label="剩余额度(单位：元)" prop="limitPutResidueOld">
                                               <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeOldInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPutResidueOld"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                         </FormItem>
                                    </Col>

                                    <!-- 剩余先放后抵额度 -->
                                    <Col span="12">
                                        <FormItem label="剩余额度(单位：元)" prop="limitPledgeResidueOld">
                                               <rui-number
                                                                           :active-change="true"
                                                                           readonly="readonly"
                                                                           :disabled="this.businessTypeOldInfo == '0'"
                                                                           :formatter="value => currencyFormat(value,2)"
                                                                           :parser="value => value.replace(/\s?|(,*)/g, '')"
                                                                           v-model="basicTypeForm.limitPledgeResidueOld"
                                                                           class="w200"
                                                                           placeholder="系统自动计算"
                                                                       >
                                                                       </rui-number>
                                        </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="12">
                                        <FormItem label="区域经理" prop="customerManagerOld">
                                            <Select v-model="basicTypeForm.customerManagerOld" placeholder="区域经理" class="w200">
                                                <Option v-for="(item,index) in regionalManagerOld" :value="item.userName" :label="item.userRealName" :key="index" >
                                                </Option>
                                            </Select>
                                        </FormItem>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Card>
                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >二手车授权区域/授权车型</h2>
                            <div class="common_content" style="">
                                <Row>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="区域是否生效" prop="authRegionSwitchOld">
                                                <RadioGroup v-model="basicTypeForm.authRegionSwitchOld" >
                                                    <Radio label="1" >是</Radio>
                                                    <Radio label="0" >否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>

                                        <Col  :xs="24" :xl="12" :xxl="8">
                                            <FormItem label="车型是否生效" prop="authVehicleTypeSwitchOld">
                                                <RadioGroup v-model="basicTypeForm.authVehicleTypeSwitchOld" >
                                                    <Radio label="1" >是</Radio>
                                                    <Radio label="0" >否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>

                                        <Col span="12" style="padding-left: 180px;">
<!--                                            <Input @on-blur="searchMainCarOld" clearable placeholder="输入搜索" class="w200" v-model="searchMainCarOldKey"/>-->
                                            <Input v-model="searchCarOldKey" @on-search="searchMainCarOld" search enter-button
                                                   class="w200"
                                                   placeholder="输入搜索"/>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col span="12">
                                            <FormItem label="" prop="mainAreaOld" v-bind:disabled=" this.businessTypeOldInfo == '0'">
<!--                                                <Tree :data="areasDataOld" ref="treeOld" show-checkbox v-model="basicTypeForm.mainAreaOld" > </Tree>-->
                                                <div style="height:200px;margin-top: 10px; overflow-y:auto">
                                                    <SelectTree ref="mainAreaOld" v-model='basicTypeForm.mainAreaOld'
                                                                :data='areasDataOld'
                                                                :disabled="typeCode === '0'"
                                                                :isInit='isAreasDataOldInit'
                                                                :isSearch="isAreasDataOldSearch" :searchKey="searchAreaOldKey"
                                                                @closeSearch="closeSearch" :dataKeys="dataKeys" :setting="setting"/>
                                                </div>
                                            </FormItem>
                                        </Col>

                                        <Col span="12">
                                            <FormItem label="" prop="mainCarOld" v-bind:disabled=" this.businessTypeOldInfo == '0'">
<!--                                                <Tree :data="carsDataOld" ref="carstreeOld" show-checkbox v-model="basicTypeForm.mainCarOld" @on-check-change="changeCarsDataOldSelect" > </Tree>-->
                                                <div style="height:200px;margin-top: 10px;overflow-y:auto">
                                                    <SelectTree ref="mainCarOld" v-model='basicTypeForm.mainCarOld' :data='carsDataOld'
                                                                :isInit='isCarsDataOldInit'
                                                                :disabled="typeCode === '0'"
                                                                :isSearch="isCarsDataOldSearch" :searchKey="searchCarOldKey"
                                                                @closeSearch="closeSearch"/>
                                                </div>
                                            </FormItem>
                                        </Col>
                                    </Row>
                                </Row>
                                <div>
                                    <a class="select-clear" @click="showAreasOldCar">选择渠道展业区域查看</a>
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card>
                        <div class="common_br">
                            <h2 class="common_channel_title" >二手车开关控制</h2>
                            <div class="common_content" >
                                <Row>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="经纬度是否控制" prop="longitudeLatitudeSwitchOld">
                                            <RadioGroup v-model="basicTypeForm.longitudeLatitudeSwitchOld"     >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="业务人员关联车商" prop="personRelCardealerSwitchOld">
                                            <RadioGroup v-model="basicTypeForm.personRelCardealerSwitchOld"    >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                    <Col  :xs="24" :xl="12" :xxl="8">
                                        <FormItem label="进件选择车商" prop="choiceCardealerSwitchOld">
                                            <RadioGroup v-model="basicTypeForm.choiceCardealerSwitchOld"     >
                                                <Radio label="1">是</Radio>
                                                <Radio label="0">否</Radio>
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>

                                </Row>
                            </div>
                        </div>
                    </Card>
                </div>
            </Form>
        </div>
        <Modal :title="modalTitle" v-model="blackApplyVisible" :mask-closable='false' :width="500" :styles="{top: '100px'}">
            <Form ref="blackApplyForm" :model="blackApplyForm" :label-width="70" :rules="blackApplyFormValidate">
                <FormItem label="申请原因" prop="blacklistReason">
                    <Input v-model="blackApplyForm.blacklistReason" class="w400"  placeholder="请输入黑名单申请原因" type="textarea" :rows="6"/>
                </FormItem>
            </Form>
            <div slot="footer" style="text-align:center;" >
                <Button type="text" @click="cancel">取消</Button>
                <Button type="primary" @click="join">提交</Button>
            </div>
        </Modal>

        <Modal :title="areaTitle" v-model="areaVisible" :mask-closable='false' :width="1000" :styles="{top: '300px'}">
            <Row>
                <Table :loading="loading" border :columns="columns" :data="dataArea" sortable="custom"
                       @on-sort-change="changeSort" @on-selection-change="showSelect" ref="table">
                </Table>
            </Row>
            <Row type="flex" justify="end" class="page">
                <Page :current="searchForm.pageNumber" :total="totalArea" :page-size="searchForm.pageSize"
                      @on-change="changePage" @on-page-size-change="changePageSize" :page-size-opts="[10,20,50]"
                      size="small" show-total show-elevator show-sizer></Page>
            </Row>
        </Modal>
        <MapModal :mapModal="mapModal" :mapLocation="mapLocation" :mapAddress="mapAddress" @confirmLngLat="confirmLngLat" @closeMapModel="closeMapModel" />
    </div>
</template>

<script>
    import PlaceCascade from "@/components/place-cascade"
    import vueEvent from "_p/basic/assets/js/vueEvent.js"
    import {
        getAccountInfoByChannelId
    } from "@/projects/afs-channel/api/car-dealer/dealer";
    import {
        //根据渠道代码查询渠道信息
        getChannelBasicInfoById,
        //编辑渠道保存
        modifyChannel,
        //多条件查询渠道基本信息
        getChannelListData,
        //获取渠道风控信息
        getChannelRiskInfoById,
        //获取渠道保证金信息
        getChannelQuotaInfoById,
        //保存黑名单
        saveBlack,
        //验证黑名单
        blackCleack,
        //查询授权区域信息
        getMainAreaById,
        //查询主营品牌信息
        getCarBrandById,
        //授权车型
        getVehicleById,
        //获取渠道展业区域
        getAreaListData,
        //统一社会信用代码重复验证
        socUniCrtCodeCheck,
        getNewVehicleById,
        getOldVehicleById,
        getNewMainAreaById,
        getOldMainAreaById,
        checkAccountInfo,
    } from  "@/projects/afs-channel/api/dealer/dealer";

    import utils from "@/libs/util.js";
    import {
        getDictDataByType
    } from "_p/basic/api/admin/datadic.js";

    import {
        getAllAddressList,
        getAllCarList,
        getLocation,
        //区域经理查询
        getAllRegionalManager,
        getBrandTree
    } from "_p/afs-channel/api/common/common.js"

    import MapModal from '_p/afs-channel/pages/common/map/MapModal';

    import {deepClone} from "@/libs/utils/ObjectClone";
    import SelectTree from '_p/afs-channel/pages/dealer-apply/dealer-online/basic-info/component/selectTree'
    import {currencyFormat, numberFormat,percentageParse} from "@/libs/tools";
    export default {
        name:"basic_info",
        data(){
            let validate = function(rule, value, callback){
                if(Array.isArray(value)){//格式为：daterange、datetimerange检测
                    if(value.length==0){
                        return callback("日期不能为空");
                    }
                    value.map(function(item){
                        if(item === '' ||item == undefined){
                            return callback("日期不能为空")
                        }
                    })
                }else{ //格式为：date、datetime、year、month 检测
                    if(value === ''){
                        return callback("日期不能为空")
                    }
                }
                return callback()
            };
            let notValidate = function(rule, value, callback){
                if(Array.isArray(value)){//格式为：daterange、datetimerange检测
                    value.map(function(item){
                        if(item === ""){
                            //return callback("日期不能为空")
                        }
                    })
                }else{ //格式为：date、datetime、year、month 检测
                    if(value === ''){
                        //return callback("日期不能为空")
                    }
                }
                return callback()
            };
            const validatePassword  = (rule, code, callback) => {
                var city = {
                    11: "北京",
                    12: "天津",
                    13: "河北",
                    14: "山西",
                    15: "内蒙古",
                    21: "辽宁",
                    22: "吉林",
                    23: "黑龙江 ",
                    31: "上海",
                    32: "江苏",
                    33: "浙江",
                    34: "安徽",
                    35: "福建",
                    36: "江西",
                    37: "山东",
                    41: "河南",
                    42: "湖北 ",
                    43: "湖南",
                    44: "广东",
                    45: "广西",
                    46: "海南",
                    50: "重庆",
                    51: "四川",
                    52: "贵州",
                    53: "云南",
                    54: "西藏 ",
                    61: "陕西",
                    62: "甘肃",
                    63: "青海",
                    64: "宁夏",
                    65: "新疆",
                    71: "台湾",
                    81: "香港",
                    82: "澳门",
                    91: "国外 "
                };
                var tip = ""
                var pass = true

                if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
                    tip = "身份证号格式错误"
                    pass = false;
                } else if (!city[code.substr(0, 2)]) {
                    tip = "身份证号格式错误"
                    pass = false
                } else {
                    // 18位身份证需要验证最后一位校验位
                    if (code.length === 18) {
                        code = code.split('')
                        // ∑(ai×Wi)(mod 11)
                        // 加权因子
                        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
                        // 校验位
                        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
                        var sum = 0
                        var ai = 0
                        var wi = 0
                        for (var i = 0; i < 17; i++) {
                            ai = code[i]
                            wi = factor[i]
                            sum += ai * wi
                        }
                        var last = parity[sum % 11];
                        if (parity[sum % 11] != code[17]) {
                            tip = "身份证号格式错误"
                            pass = false
                        }
                    }
                }
                if (!pass) {
                    callback(new Error(tip))
                } else {
                    callback()
                }
            };
            const validateMobile = (rule, value, callback) => {
                let regPone = null;
                let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/; //最新16手机正则
                let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/; //座机
                if (value.charAt(0) == 0) {    // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
                    regPone = tel;
                } else {
                    regPone = mobile;
                }
                if (!regPone.test(value)) {
                    return callback(
                        new Error("联系电话格式有误(座机格式'区号-座机号码')")
                    );
                }
                callback();
            };
            const validateAmount = (rule, value, callback) => {
                var reg = /^\d+(\.\d+)?$/;
                if(!value){
                    callback();
                    return;
                }
                if (!reg.test(value)) {
                    callback(new Error("数值类型格式错误"));
                } else {
                    callback();
                }

            };
            return{
                allBrandTree: null,
                allAreaList: null,
                isMainBrandTreeSearch: false,
                mainBrandTreeInit: false,
                isCarsDataInit: false,
                isCarsDataOldInit: false,
                isAreasDataOldInit: false,
                isAreasDataInit: false,
                searchCarsDataKey: '',
                isCarTreeSearch: false,
                searchAreasDataKey: '',
                isAreasDataSearch: false,
                dataKeys: ["addrLevel", "expand", "delFlag", "id", "label", "postCode", "spellCode", "title", "upperCode", "value"],
                setting: {
                    check: {enable: true, chkDisabledInherit: true},
                    data: {
                        key: {name: "title"},
                        simpleData: {enable: true, pIdKey: "upperCode", idKey: 'value'}
                    },
                    view: {showIcon: false,}
                },
                searchCarOldKey: '',
                isCarsDataOldSearch: false,

                searchAreaOldKey: '',
                isAreasDataOldSearch: false,
                pageParams: null,
                //注音品牌查询
                searchKey:"",
                //注音品牌查询全局变量
                mainCarSearch:[],
                areasDataSearch:[],
                //二手车授权区域查询
                searchAreasDataOldKey:"",
                areasDataOldSearch:[],
                carsDataSearch:[],
                //二手车授权车新查询
                searchMainCarOldKey:"",
                carsDataOldSearch:[],



                loading:false,
                submitLoading: false,
                paymentObjectDisabled:false,
                //禁用新车复选框
                businessTypeDisabled:"0",
                //禁用二手车复选框
                businessTypeOldDisabled:"0",
                //禁用新车表单
                businessTypeNewInfo:"0",
                //禁用二手车表单
                businessTypeOldInfo:"0",
                //优质等级Title
                qualityGradeTitle:"",
                //二手车优质等级Title
                qualityGradeOldTitle:"",
                disabledPaymentObject:"0",
                readOnly:false,
                isShowModel:false,
                expandLevel: 0,
                carLevel: 0,
                //黑名单
                modalTitle: "",
                //展业区域
                areaTitle:"",
                //黑明单申请弹窗
                blackApplyVisible: false,
                //展业区域弹窗
                areaVisible:false,
                typeCode:"1",
                mainCar:[],
                mainArea:[],
                //渠道归属数据字典
                channelBelongList:[],
                //汇款对象
                paymentObjectList:[],
                location:[],
                //业务类型字典
                businessTypeList:[],

                //优质等级字典处理
                qualityGradeList:[],

                //渠道评级
                channelGradeList:[],
                //区域经理
                regionalManager:[],
                //二手车区域经理
                regionalManagerOld:[],
                //gps厂商字典
                gpsvendorauthorList: [],
                //Gps安装方式字典
                gpsinstalmodeList: [],
                // 运营公司
                companyList:[
                    {
                        value:"00",
                        label:"内部车型库",
                    },
                    {
                        value:"01",
                        label:"外部车型库",
                    },
                    {
                        value:"02",
                        label:"厂商认定",
                    },
                    {
                        value:"02",
                        label:"第三方评估",
                    }
                ],
                searchForm: {
                    pageNumber: 1,
                    pageSize: 10,
                    areaId:[],
                },
                columns: [
                    {
                        type: 'index',
                        title:"序号",
                        minWidth: 80,
                        align: "center",
                        fixed: "left"

                    },
                    {
                        title: "合作商名称",
                        key: "channelFullName",
                        minWidth: 120,
                    },
                    {
                        title: "地区",
                        key: "area",
                        minWidth: 145
                    },
                ],
                dataArea: [],
                totalArea: 0,
                basicTypeForm:{
                    mainBrand:[],
                    mainCar:[],
                    mainArea:[],
                    mainAreaOld:[],
                    mainCarOld:[],
                    //admin地址省市区
                    channelAdminAddressValue:[],
                    //注册地址省市区
                    channelAddressValue:[],
                    //办公地址省市区
                    officeAddressValue:[],
                    newCarRole:"",
                    oldCarRole:"",
                    pageNumber: 1,
                    pageSize: 10,
                    //渠道名称
                    channelFullName:"",
                    //上线日期
                    onlineDate:"",
                    //统一社会信用代码
                    socUniCrtCode:"",
                    //法人
                    legalPerson:"",
                    //法人身份证号
                    legalPersonIdCard:"",
                    //法人电话
                    legalPersonTel:"",
                    //实际控制人
                    actualController:"",
                    //实际控制人电话
                    actualControllerTel:"",
                    //实际控制人身份证号
                    actualControllerIdCard:"",
                    //管理员姓名
                    channelAdmin:"",
                    //管理员电话
                    channelAdminTel:"",
                    //管理员证件号码
                    channelAdminIdCard:"",
                    //管理员邮箱
                    channelAdminMail:"",
                    channelAdminAddressDetail:"",
                    channelAddressDetail:"",
                    officeAddressDetail:"",
                    //地址经纬度
                    longitude:"",
                    latitude:"",
                    //经纬度范围（km）
                    longitudeLatitudeRange:"",
                    //公司人数
                    companiesNumber:1,
                    //经营场所面积
                    businessArea:"",
                    //汇款对象
                    paymentObject:"",
                    //注册日期
                    registrationDate:"",
                    //注册资金(单位：万元)
                    registeredCapital:"",
                    //实收资本(单位：万元)
                    subscribedCapital:"",
                    //业务类型
                    businessType:[],
                    //资产净值
                    netAssetValue:"",
                    //经营年限
                    businessLife:"",
                    //Gps厂商授权
                    gpsVendorAuthor:[],
                    //资产负债率
                    assetLiabilityRatio:"",
                    //上一年度经营利润
                    operatingProfit:"",
                    //Gps安装方式
                    gpsInstalMode:[],
                    //业绩
                    achievement:"",
                    //利润率增长率
                    proGrowthRate:"",
                    //销售收入增长率
                    saleIncreaseRate:"",
                    //流动比率
                    currentRatio:"",
                    //优质等级
                    qualityGrade:"",
                    //二手车优质等级
                    qualityGradeOld:"",
                    //有效期
                    validDate:[],
                    //二手车有效期
                    validDateOld:[],
                    //车辆类型
                    carType:[],
                    //二手车车辆类型
                    carTypeOld:[],
                    //区域经理
                    customerManager:"",
                    //二手车区域经理
                    customerManagerOld:"",
                    //二手车账号最大数量
                    accountMaxNumOld:1,
                    //新车账号最大数量
                    accountMaxNum:1,
                    // 见证人最大数量
                    witnessMaxNum:1,
                    // 二手车见证人最大数量
                    witnessMaxNumOld:1,
                    //层级最大数量
                    hierarchy:1,
                    //评级
                    channelGrade:"",
                    //二手车评级
                    channelGradeOld:"",
                    //保证金
                    channelDeposit:"",
                    //二手车保证金
                    channelDepositOld:"",
                    //是否控制额度
                    isLimitPut:"",
                    //二手车是否控制额度
                    isLimitPutOld:"",
                    //是否控制额度
                    isLimitPledge:"",
                    //二手车是否控制额度
                    isLimitPledgeOld:"",
                    //签放额度
                    limitPut:"",
                    //签放额度
                    limitPutOld:"",
                    //先放后抵额度
                    limitPledge:"",
                    //二手车先放后抵额度
                    limitPledgeOld:"",
                    //签放额度临时额度
                    temporaryLimitPut:"",
                    //二手车签放额度临时额度
                    temporaryLimitPutOld:"",
                    //先放后抵额度临时额度
                    temporaryLimitPledge:"",
                    //二手车先放后抵额度临时额度
                    temporaryLimitPledgeOld:"",
                    //签放额度有效期
                    limitPutTime:"",
                    //二手车签放额度有效期
                    limitPutTimeOld:"",
                    //先放后抵额度有效期
                    limitPledgeTime:"",
                    //二手车先放后抵额度有效期
                    limitPledgeTimeOld:"",
                    //签放额度占用
                    limitPutTake:"",
                    //二手车签放额度占用
                    limitPutTakeOld:"",
                    //先放后抵额度占用
                    limitPledgeTake:"",
                    //二手车先放后抵额度占用
                    limitPledgeTakeOld:"",
                    //剩余签放额度
                    limitPutResidue:"",
                    //二手车剩余签放额度
                    limitPutResidueOld:"",
                    //剩余先放后抵额度
                    limitPledgeResidue:"",
                    //二手车剩余先放后抵额度
                    limitPledgeResidueOld:"",
                    //区域是否生效
                    authRegionSwitch:"",
                    //二手车区域是否生效
                    authRegionSwitchOld:"",
                    //车型是否生效
                    authVehicleTypeSwitch:"",
                    //二手车是否生效
                    authVehicleTypeSwitchOld:"",
                    //经纬度是否控制
                    longitudeLatitudeSwitch:"",
                    //业务人员关联车商
                    personRelCardealerSwitch:"",
                    //进件选择车商
                    choiceCardealerSwitch:"",

                    //经纬度是否控制
                    longitudeLatitudeSwitchOld:"",
                    //业务人员关联车商
                    personRelCardealerSwitchOld:"",
                    //进件选择车商
                    choiceCardealerSwitchOld:"",
                },
                basicTypeFormValidate:{
                    channelFullName: [
                        { required: true, message: "合作商名称不能为空", trigger: "blur"}
                    ],
                    onlineDate: [
                        { required: true, validator: validate, trigger: "change"}
                    ],
                    socUniCrtCode: [
                        { required: true, message: "统一社会信用代码不能为空", trigger: "blur"}
                    ],
                    legalPerson:[
                        { required: true, message: "法人不能为空", trigger: "blur"}
                    ],
                    legalPersonIdCard:[
                        { required: true, message: "法人身份证号不能为空", trigger: "blur"},
                        { validator: validatePassword, trigger: "blur" }

                    ],
                    legalPersonTel:[
                        { required: true, message: "法人电话不能为空", trigger: "blur"},
                        {validator: validateMobile, trigger: "blur"}
                    ],
                    actualController:[
                        { required: true, message: "实际控制人不能为空", trigger: "blur"}
                    ],
                    actualControllerTel:[
                        { required: true, message: "实际控制人电话不能为空", trigger: "blur"},
                        {validator: validateMobile, trigger: "blur"}
                    ],
                    actualControllerIdCard:[
                        { required: true, message: "实际控制人身份证号不能为空", trigger: "blur"},
                        { validator: validatePassword, trigger: "blur" }
                    ],
                    channelAdmin:[
                        { required: true, message: "管理员姓名不能为空", trigger: "blur"}
                    ],
                    channelAdminTel:[
                        { required: true, message: "管理员电话不能为空", trigger: "blur"},
                        {validator: validateMobile, trigger: "blur"}
                    ],
                    channelAdminMail:[
                        {required: true, message: "管理员邮箱不能为空"},
                        {type: "email", message: "邮箱格式不正确"}
                    ],
                    channelAdminIdCard:[
                        { required: true, message: "管理员证件号码不能为空", trigger: "blur"},
                        { validator: validatePassword, trigger: "blur" }
                    ],
                    channelAdminAddressValue:[
                        { required: true, type: 'array', message: '管理员地址不能为空', trigger: 'change' },
                    ],

                    channelAdminAddressDetail:[
                        { required: true, message: "管理员详细地址不能为空", trigger: "blur"}
                    ],

                    channelAddressValue:[
                        { required: true, type: 'array', message: '注册地址不能为空', trigger: 'change' },
                    ],

                    channelAddressDetail:[
                        { required: true, message: "注册地详细地址不能为空", trigger: "blur"}
                    ],

                    officeAddressValue:[
                        { required: true, type: 'array', message: '办公地址不能为空', trigger: 'change' },
                    ],

                    officeAddressDetail:[
                        { required: true,  message: '办公地详细址不能为空', trigger: 'blur' },
                    ],

                    longitude:[
                        { required: true, message: "地址经度不能为空", trigger: "change"},
                    ],
                    latitude:[
                        { required: true, message: "地址纬度不能为空", trigger: "change"},
                    ],
                    companiesNumber:[
                        { required: true,type: 'number',message: "公司人数", trigger: "blur"}
                    ],
                    accountMaxNum:[
                        { required: true,type: 'number',message: "账号最大数量", trigger: "blur"}
                    ],
                    accountMaxNumOld:[
                        { required: true,type: 'number',message: "账号最大数量", trigger: "blur"}
                    ],
                    witnessMaxNum: [
                        {required: true, type: 'number', message: "见证人最大数量", trigger: "blur"}
                    ],
                    witnessMaxNumOld: [
                        {required: true, type: 'number', message: "见证人最大数量", trigger: "blur"}
                    ],
                    hierarchy:[
                        { required: true,type: 'number',message: "层级最大数量", trigger: "blur"}
                    ],
                    longitudeLatitudeRange:[
                        { required: true, message: "经纬度范围不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    businessArea:[
                        { required: true, message: "经营场所面积不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    paymentObject:[
                        { required: true, message: "汇款对象不能为空", trigger: "change"}
                    ],
                    registrationDate:[
                        { required: true, validator: validate, trigger: "change"}
                    ],
                    registeredCapital:[
                        { required: true,type:"number", message: "注册资金不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    subscribedCapital:[
                        { required: true,type:"number", message: "实收资本不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    businessType: [
                        { required: true, type: 'array', min: 1, message: '业务类型不能为空', trigger: 'change' },
                    ],
                    netAssetValue:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    businessLife:[
                        { required: true, message: "经营年限不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    gpsVendorAuthor:[
                        { required: true, type: 'array', min: 1, message: 'Gps厂商授权不能为空', trigger: 'change' },
                    ],
                    assetLiabilityRatio:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    operatingProfit:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    gpsInstalMode:[
                        { required: true, type: 'array', min: 1, message: 'Gps安装方式不能为空', trigger: 'change' },
                    ],
                    achievement:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    proGrowthRate:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    saleIncreaseRate:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    currentRatio:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    qualityGrade:[
                        { required: true, message: "优质等级不能为空", trigger: "change"}
                    ],
                    qualityGradeOld:[
                        { required: true, message: "优质等级不能为空", trigger: "change"}
                    ],
                    carType:[
                        { required: true, type: 'array', message: '车辆类型不能为空', trigger: 'change' },
                    ],
                    carTypeOld:[
                        { required: true, type: 'array', message: '车辆类型不能为空', trigger: 'change' },
                    ],
                    customerManager:[
                        { required: true, message: "区域经理不能为空", trigger: "change"}
                    ],
                    customerManagerOld:[
                        { required: true, message: "区域经理不能为空", trigger: "change"}
                    ],
                    channelGrade:[
                        { required: true, message: "评级不能为空", trigger: "change"}
                    ],
                    channelGradeOld:[
                        { required: true, message: "评级不能为空", trigger: "change"}
                    ],
                    channelDeposit:[
                        { required: true,type:"number", message: "保证金不能为空", trigger: "blur" },
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    channelDepositOld:[
                        { required: true,type:"number", message: "保证金不能为空", trigger: "blur" },
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    isLimitPut:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPutOld:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPledge:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPledgeOld:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],

                    temporaryLimitPut:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPutOld:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPledge:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPledgeOld:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    authRegionSwitch:[
                        { required: true, message: "区域是否生效不能为空", trigger: "change"}
                    ],
                    authRegionSwitchOld:[
                        { required: true, message: "区域是否生效不能为空", trigger: "change"}
                    ],
                    authVehicleTypeSwitch:[
                        { required: true, message: "车型是否生效不能为空", trigger: "change"}
                    ],
                    authVehicleTypeSwitchOld:[
                        { required: true, message: "车型是否生效不能为空", trigger: "change"}
                    ],
                    longitudeLatitudeSwitch:[
                        { required: true, message: "经纬度是否控制不能为空", trigger: "change"}
                    ],
                    personRelCardealerSwitch:[
                        { required: true, message: "业务人员关联车不能为空", trigger: "change"}
                    ],
                    choiceCardealerSwitch:[
                        { required: true, message: "进件选择车商不能为空", trigger: "change"}
                    ],
                    longitudeLatitudeSwitchOld:[
                        { required: true, message: "经纬度是否控制不能为空", trigger: "change"}
                    ],
                    personRelCardealerSwitchOld:[
                        { required: true, message: "业务人员关联车不能为空", trigger: "change"}
                    ],
                    choiceCardealerSwitchOld:[
                        { required: true, message: "进件选择车商不能为空", trigger: "change"}
                    ]
                },
                limitPut:[
                    { required: true,type:"number", message: "签放额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPut:[
                    {validator: validateAmount, trigger: "blur"},
                ],
                limitPledge:[
                    { required: true,type:"number", message: "先放后抵额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],

                notLimitPledge:[
                    {validator: validateAmount, trigger: "blur"}
                ],
                limitPutOld:[
                    { required: true,type:"number", message: "签放额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPutOld:[
                    {validator: validateAmount, trigger: "blur"}
                ],
                limitPledgeOld:[
                    { required: true, type:"number", message: "先放后抵额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPledgeOld:[
                    {validator: validateAmount, trigger: "blur"}
                ],
                validDate:[
                    { required: true, validator: validate, trigger: "change"}
                ],
                notValidDate:[
                    { required: false, validator: notValidate, trigger: "change"}
                ],
                validDateOld:[
                    { required: true, validator: validate, trigger: "change"}
                ],
                notValidDateOld:[
                    { required: false, validator: notValidate, trigger: "change"}
                ],

                //黑名单
                blackApplyForm:{

                },
                //黑名单
                blackApplyFormValidate:{

                },
                baseData: [],
                areasData:[],
                carsData: [],
                carsDataOld:[],
                areasDataOld:[],
                showGroupNewCar:"0",//默认隐藏新车
                showGroupOldCar:"0",//默认隐藏二手车
                mapModal: false, // 地图弹窗
                mapLocation:'', // 地图定位
                mapAddress:'', // 详细地址
                canOpenMapModal: false, // 是否可以打开地图弹窗
            }
        },
        components:{
            SelectTree,
            PlaceCascade,
            MapModal
        },
        props:{
            channelType:{
                type:String,
            },

            newCarRole:{
                type:String,
            },
            oldCarRole:{
                type:String,
            },
            channelStatus:{
                type:String,
            },
            channelStatusOldCar:{
                type:String,
            },
             formatter: {
                         type: Object,
                         default: () => {
                             return {
                                 dataFormatter: numberFormat,
                                 percentageParse: percentageParse,
                                 currencyFormat: currencyFormat,
                             };
                         }
                     }
        },
        watch:{

            channelType:function(val){

                if(this.basicTypeForm.channelBelong ==null || this.basicTypeForm.channelBelong =="" || this.basicTypeForm.channelBelong ==undefined){
                    //首次进入
                    this.basicTypeForm.channelBelong=this.channelType
                    if(this.basicTypeForm.channelBelong=="00"||this.basicTypeForm.channelBelong=="02"){
                        //如果渠道归属为sp或者总对总：则汇款对象只能为主体
                        this.basicTypeForm.paymentObject="0";
                        this.paymentObjectDisabled=true;
                    }
                    if(this.basicTypeForm.channelBelong=="01"){
                        //如果渠道归属为直营：则汇款对象默认主体
                        this.basicTypeForm.paymentObject="0";
                    }
                }else{
                    //修改进入
                    if(this.basicTypeForm.channelBelong=="00"||this.basicTypeForm.channelBelong=="02"){
                        //sq、总对总默认只读
                        this.paymentObjectDisabled=true;
                    }
                }
            },
        },
        methods:{
            init() {
                this.queryAllBrandTree();// 初始请求品牌名称树
                this.queryAllAreaList();// 初始请求地址树
                this.getAllRegionalManager();
                this.getAllRegionalManagerOld();
            },
            checkQualityGrade(){
                let qualityGrade = this.basicTypeForm.qualityGrade;
                let validDate = this.basicTypeForm.validDate;
                if(qualityGrade=="5"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'validDate') {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };

                let qualityGradeOld = this.basicTypeForm.qualityGradeOld;
                let validDateOld = this.basicTypeForm.validDateOld;
                if(qualityGradeOld=="5"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'validDateOld') {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };
            },

            checkChannelGrade(val){
                this.qualityGradeTitle = val.label;
            },
            checkChannelGradeOld(val){
                this.qualityGradeOldTitle = val.label;
            },
            checkLimitPut(){
                var reg = /^\d+(\.\d+)?$/;
                let isLimitPut = this.basicTypeForm.isLimitPut;
                let limitPut = this.basicTypeForm.limitPut;
                if(isLimitPut=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPut' && (limitPut==""||limitPut==null||limitPut==undefined)) {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };

                let isLimitPledge = this.basicTypeForm.isLimitPledge;
                let limitPledge = this.basicTypeForm.limitPledge;
                if(isLimitPledge=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPledge'&& (limitPledge==""||limitPledge==null||limitPledge==undefined)) {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                }

                let isLimitPutOld = this.basicTypeForm.isLimitPutOld;
                let limitPutOld = this.basicTypeForm.limitPutOld;
                if(isLimitPutOld=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPutOld' && (limitPutOld==""||limitPutOld==null||limitPutOld==undefined)) {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };

                let isLimitPledgeOld = this.basicTypeForm.isLimitPledgeOld;
                let limitPledgeOld = this.basicTypeForm.limitPledgeOld;
                if(isLimitPledgeOld=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPledgeOld'&& (limitPledgeOld==""||limitPledgeOld==null||limitPledgeOld==undefined)) {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                }
            },

            checkBusinessType(){
                let businessType =this.basicTypeForm.businessType;
                let channelId = this.basicTypeForm.id;
                if(businessType !=null && businessType.length>0){
                    if(businessType.length===1 && businessType[0]=="01"){
                        //新车区域展示
                        this.showGroupNewCar="1";
                        //二手车区域隐藏
                        this.showGroupOldCar="0";
                        //新车角色添加
                        this.isNewCarRole="1";
                        //二手车角色删除
                        this.isOldCarRole="0";
                    }else if(businessType.length===1 && businessType[0]=="02"){
                        this.showGroupOldCar="1";
                        this.showGroupNewCar="0";
                        this.isNewCarRole="0";
                        this.isOldCarRole="1";
                    }else if(businessType.length===2){
                        this.showGroupNewCar="1";
                        this.showGroupOldCar="1";
                        this.isNewCarRole="1";
                        this.isOldCarRole="1";
                    }else{
                        this.showGroupNewCar="0";
                        this.showGroupOldCar="0";
                        this.isNewCarRole="0";
                        this.isOldCarRole="0";
                    }
                };
                let param ={
                    newCarRole:this.isNewCarRole,
                    oldCarRole:this.isOldCarRole
                }
                vueEvent.$emit('to-accountInfo',param);
                this.$parent.setBusinessType(businessType);
            },

            carRole(){
                console.log(this.newCarRole,"新车-------",this.oldCarRole,"二手车--------");
                if(this.newCarRole=="1" && this.oldCarRole=="1"){
                    this.businessTypeDisabled="0";
                    this.businessTypeNewInfo="0";
                    this.businessTypeOldDisabled="0";
                    this.showGroupNewCar="1";
                    this.showGroupOldCar="1";
                    this.isNewCarRole='1';
                    this.isOldCarRole='1';

                }
                //新车角色
                if(this.newCarRole=="1" && this.oldCarRole=="0"){
                    //当前业务类型只有一个且已存的业务类型为新车
                    if(this.basicTypeForm.businessType.length===1 && this.basicTypeForm.businessType[0]=="01" ){
                        this.basicTypeForm.businessType=["01"];
                        this.showGroupNewCar="1";
                        this.showGroupOldCar="0";
                        //赋值新车权限
                        this.isNewCarRole='1';
                        //赋值二手车权限
                        this.isOldCarRole='0';
                        //新车复选框解除禁用
                        this.businessTypeDisabled="0";
                        //新车表单解除禁用
                        this.businessTypeNewInfo="0";
                    }else{
                        //业务类型赋值新车、二手车
                        this.basicTypeForm.businessType=["01","02"];
                        //展示新车表单
                        this.showGroupNewCar="1";
                        //隐藏二手车表单（因为没有二手车没有上线，维护地方只更新已上线的信息和基础信息）
                        this.showGroupOldCar="0";
                        //赋值新车权限
                        this.isNewCarRole='1';
                        //赋值二手车权限
                        this.isOldCarRole='0';
                        //新车复选框解除禁用
                        this.businessTypeDisabled="1";
                        //二手车复选框禁用
                        this.businessTypeOldDisabled="0";
                        //新车表单解除禁用
                        this.businessTypeNewInfo="0";
                        //二手车表单禁用
                        this.businessTypeOldInfo="0";
                    }
                }
                //二手车角色
                if(this.newCarRole=="0" && this.oldCarRole=="1"){
                    //当前业务类型只有一个且已存的业务类型为二手车
                    if(this.basicTypeForm.businessType.length===1 && this.basicTypeForm.businessType[0]=="02" ){
                        //业务类型赋值为：二手车
                        this.basicTypeForm.businessType=["02"];
                        //展示二手车区域
                        this.showGroupOldCar="1";
                        //隐藏新车区域
                        this.showGroupNewCar="0";
                        //赋值新车权限
                        this.isNewCarRole='0';
                        //赋值二手车权限
                        this.isOldCarRole='1';
                        //二手车复选框禁用
                        this.businessTypeOldDisabled="0";
                        //二手车表单解用
                        this.businessTypeOldInfo="0";

                    }else{
                        //业务类型赋值为：新车、二手车
                        this.basicTypeForm.businessType=["01","02"];
                        //展示二手车区域
                        this.showGroupOldCar="1";
                        //隐藏新车区域（因为新车没有角色权限）
                        this.showGroupNewCar="0";
                        //赋值新车权限
                        this.isNewCarRole='0';
                        //赋值二手车权限
                        this.isOldCarRole='1';
                        //二手车复选框禁用
                        this.businessTypeOldDisabled="1";
                        //二手车表单禁用
                        this.businessTypeOldInfo="1";
                    }
                }

                let param ={
                    newCarRole:this.isNewCarRole,
                    oldCarRole:this.isOldCarRole
                }
                vueEvent.$emit('to-accountInfo',param)
            },

            addCarRole(v,n){
                console.log(v,"new");
                console.log(n,"old");
                if(v=="1" && n=="1"){
                    //如果当前用户同时拥有新车、二手车权限
                    //新车复选框解除禁用
                    this.businessTypeDisabled="1";
                    //二手车复选框解除禁用
                    this.businessTypeOldDisabled="1";
                    //新车表单解除禁用
                    this.businessTypeNewInfo="1";
                    //二手车表单解除禁用
                    this.businessTypeOldInfo="1";
                    this.basicTypeForm.businessType=["01","02"];
                    this.isNewCarRole=v;
                    this.isOldCarRole=n;
                    this.showGroupNewCar="1";
                    this.showGroupOldCar="1";
                    this.businessTypeDisabled="1";
                    this.businessTypeOldDisabled="1";
                }
                if(v=="1" && n=="0"){
                    this.basicTypeForm.businessType=["01"];
                    //新车表单解除禁用
                    this.businessTypeNewInfo="1";
                    this.isNewCarRole=v;
                    this.isOldCarRole=n;
                    this.showGroupNewCar="1";
                    this.showGroupOldCar="0";
                }
                if(v=="0" && n=="1"){
                    this.basicTypeForm.businessType=["02"];
                    //二手车表单解除禁用
                    this.businessTypeOldInfo="1";
                    this.isNewCarRole=v;
                    this.isOldCarRole=n;
                    this.showGroupOldCar="1";
                    this.showGroupNewCar="0";
                }
                let param ={
                    newCarRole:this.isNewCarRole,
                    oldCarRole:this.isOldCarRole
                }
                vueEvent.$emit('to-accountInfo',param)
            },

            getChannelInfo(v) {
                //根据id查询渠道基本信息
                this.loading = true;
                getChannelBasicInfoById(v).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.basicTypeForm.id=res.data[0].id;
                        this.basicTypeForm.channelCode=res.data[0].channelCode;
                        this.basicTypeForm.channelFullName=res.data[0].channelFullName;
                        this.basicTypeForm.onlineDate=res.data[0].onlineDate;
                        this.basicTypeForm.socUniCrtCode=res.data[0].socUniCrtCode;
                        this.basicTypeForm.legalPerson=res.data[0].legalPerson;
                        this.basicTypeForm.legalPersonIdCard=res.data[0].legalPersonIdCard;
                        this.basicTypeForm.legalPersonTel=res.data[0].legalPersonTel;
                        this.basicTypeForm.actualController=res.data[0].actualController;
                        this.basicTypeForm.actualControllerTel=res.data[0].actualControllerTel;
                        this.basicTypeForm.actualControllerIdCard=res.data[0].actualControllerIdCard;
                        this.basicTypeForm.channelAdmin=res.data[0].channelAdmin;
                        this.basicTypeForm.channelAdminTel=res.data[0].channelAdminTel;
                        this.basicTypeForm.channelAdminMail=res.data[0].channelAdminMail;
                        this.basicTypeForm.longitude=res.data[0].longitude;
                        this.basicTypeForm.latitude=res.data[0].latitude;
                        this.basicTypeForm.longitudeLatitudeRange=res.data[0].longitudeLatitudeRange;
                        this.basicTypeForm.businessArea=res.data[0].businessArea;
                        this.basicTypeForm.paymentObject=res.data[0].paymentObject;
                        this.basicTypeForm.registrationDate=res.data[0].registrationDate;
                        this.basicTypeForm.channelBelong=res.data[0].channelBelong;
                        this.basicTypeForm.hierarchy=res.data[0].hierarchy;
                        this.basicTypeForm.companiesNumber=res.data[0].companiesNumber;
                        this.basicTypeForm.channelAdminIdCard =res.data[0].channelAdminIdCard;
                        if(res.data[0].registeredCapital){
                            this.basicTypeForm.registeredCapital=res.data[0].registeredCapital;
                        }else{
                            this.basicTypeForm.registeredCapital=0
                        }
                        if(res.data[0].assetLiabilityRatio||res.data[0].assetLiabilityRatio=='0'){
                            this.basicTypeForm.assetLiabilityRatio=res.data[0].assetLiabilityRatio+"";
                        }
                        if(res.data[0].businessLife||res.data[0].businessLife=='0'){
                            this.basicTypeForm.businessLife=res.data[0].businessLife+"";
                        }
                        if(res.data[0].operatingProfit||res.data[0].operatingProfit=='0'){
                            this.basicTypeForm.operatingProfit=res.data[0].operatingProfit;
                        }
                        if(res.data[0].achievement||res.data[0].achievement=='0'){
                            this.basicTypeForm.achievement=res.data[0].achievement+"";
                        }
                        if(res.data[0].subscribedCapital){
                            if(res.data[0].subscribedCapital=='0'){
                                this.basicTypeForm.subscribedCapital=0;
                            }else{
                                this.basicTypeForm.subscribedCapital=res.data[0].subscribedCapital;
                            }
                        }else{
                            this.basicTypeForm.subscribedCapital=0;
                        }
                        if(res.data[0].netAssetValue||res.data[0].netAssetValue=='0'){
                            this.basicTypeForm.netAssetValue=res.data[0].netAssetValue;
                        }
                        if(res.data[0].proGrowthRate||res.data[0].proGrowthRate=='0'){
                            this.basicTypeForm.proGrowthRate=res.data[0].proGrowthRate+"";
                        }
                        if(res.data[0].saleIncreaseRate||res.data[0].saleIncreaseRate=='0'){
                            this.basicTypeForm.saleIncreaseRate=res.data[0].saleIncreaseRate+"";
                        }
                        if(res.data[0].currentRatio||res.data[0].currentRatio=='0'){
                            this.basicTypeForm.currentRatio=res.data[0].currentRatio+"";
                        }
                        this.basicTypeForm.longitudeLatitudeSwitch=res.data[0].longitudeLatitudeSwitch;
                        this.basicTypeForm.personRelCardealerSwitch=res.data[0].personRelCardealerSwitch;
                        this.basicTypeForm.choiceCardealerSwitch=res.data[0].choiceCardealerSwitch;

                        this.basicTypeForm.longitudeLatitudeSwitchOld=res.data[0].longitudeLatitudeSwitchOld;
                        this.basicTypeForm.personRelCardealerSwitchOld=res.data[0].personRelCardealerSwitchOld;
                        this.basicTypeForm.choiceCardealerSwitchOld=res.data[0].choiceCardealerSwitchOld;

                        this.basicTypeForm.businessType=res.data[0].businessType.split(',');
                        if(res.data[0].gpsVendorAuthor){
                            this.basicTypeForm.gpsVendorAuthor=res.data[0].gpsVendorAuthor.split(',');
                        }else{
                            this.basicTypeForm.gpsVendorAuthor=[];
                        }
                        if(res.data[0].gpsInstalMode){
                            this.basicTypeForm.gpsInstalMode=res.data[0].gpsInstalMode.split(',');
                        }else{
                            this.basicTypeForm.gpsInstalMode=[];
                        }

                        let channelAdminAddressValue = [];
                        channelAdminAddressValue[0]=res.data[0].channelAdminProvince;
                        channelAdminAddressValue[1]=res.data[0].channelAdminCity;
                        this.basicTypeForm.channelAdminAddressValue=channelAdminAddressValue;
                        this.basicTypeForm.channelAdminAddressDetail=res.data[0].channelAdminAddress;

                        let channelAddressValue = [];
                        channelAddressValue[0]=res.data[0].channelProvince;
                        channelAddressValue[1]=res.data[0].channelCity;
                        this.basicTypeForm.channelAddressValue=channelAddressValue;
                        this.basicTypeForm.channelAddressDetail=res.data[0].channelAddress;

                        let officeAddressValue = [];
                        officeAddressValue[0]=res.data[0].officeProvince;
                        officeAddressValue[1]=res.data[0].officeCity;
                        this.basicTypeForm.officeAddressValue=officeAddressValue;
                        this.basicTypeForm.officeAddressDetail=res.data[0].officeAddress;

                        let channelInfo = this.basicTypeForm;
                        this.$emit('passmsg',channelInfo)
                        this.carRole();
                    }
                });
                //根据渠道id查询风控信息
                getChannelRiskInfoById(v).then(resRisk => {
                    this.loading = false;
                    if (resRisk.code === "0000") {
                        let str = JSON.stringify(resRisk.data);
                        let channelRiskInfoTemp = JSON.parse(str);
                        channelRiskInfoTemp.forEach((item, i) => {
                            //新车
                            if(item.businessType=="01"){
                                let validDate = [];
                                validDate[0]=item.qualityStartDate;
                                validDate[1]=item.qualityEndDate;
                                this.basicTypeForm.validDate=validDate;
                                if(item.carType){
                                    this.basicTypeForm.carType=item.carType.split(',');
                                }else{
                                    this.basicTypeForm.carType=[];
                                }
                                this.basicTypeForm.qualityGrade =item.qualityGrade;
                                this.basicTypeForm.customerManager =item.customerManager;
                                this.basicTypeForm.channelGrade =item.channelGrade;
                                if(item.channelDeposit ||item.channelDeposit=="0"){
                                    if(item.channelDeposit=="0"){
                                        this.basicTypeForm.channelDeposit =0;
                                    }else{
                                        this.basicTypeForm.channelDeposit =item.channelDeposit;
                                    }
                                }
                                this.basicTypeForm.authRegionSwitch =item.authRegionSwitch;
                                this.basicTypeForm.authVehicleTypeSwitch =item.authVehicleTypeSwitch;
                                this.basicTypeForm.accountMaxNum = item.accountMaxNum;
                                this.basicTypeForm.witnessMaxNum = item.witnessMaxNum;
                                //编辑或回显数据时候根据车辆类型重新获取对应车型
                            }else if(item.businessType=="02"){
                                //二手车
                                let validDate = [];
                                validDate[0]=item.qualityStartDate;
                                validDate[1]=item.qualityEndDate;
                                this.basicTypeForm.validDateOld=validDate;
                                if(item.carType){
                                    this.basicTypeForm.carTypeOld=item.carType.split(',');
                                }else{
                                    this.basicTypeForm.carTypeOld=[];
                                }
                                this.basicTypeForm.qualityGradeOld =item.qualityGrade;
                                this.basicTypeForm.customerManagerOld =item.customerManager;
                                this.basicTypeForm.channelGradeOld =item.channelGrade;
                                if(item.channelDeposit || item.channelDeposit=="0"){
                                    if(item.channelDeposit=="0"){
                                        this.basicTypeForm.channelDepositOld =0;
                                    }else{
                                        this.basicTypeForm.channelDepositOld =item.channelDeposit;
                                    }
                                }
                                this.basicTypeForm.authRegionSwitchOld =item.authRegionSwitch;
                                this.basicTypeForm.authVehicleTypeSwitchOld =item.authVehicleTypeSwitch;
                                this.basicTypeForm.accountMaxNumOld = item.accountMaxNum;
                                this.basicTypeForm.witnessMaxNumOld = item.witnessMaxNum;
                                //编辑或回显数据时候根据车辆类型重新获取对应车型

                            }
                        });
                    }
                });

                //根据渠道id查询保证金信息
                getChannelQuotaInfoById(v).then(resQuota => {
                    this.loading = false;
                    if (resQuota.code === "0000") {

                        let str = JSON.stringify(resQuota.data);
                        let ChannelQuotaInfoTemp = JSON.parse(str);
                        ChannelQuotaInfoTemp.forEach((item, i) => {
                            //新车
                            if(item.businessType=="01"){
                                //签放额度
                                if (item.quotaType == "1") {
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPut = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount||item.quotaAmount=='0'){
                                        this.basicTypeForm.limitPut = item.quotaAmount;
                                    }
                                    if(item.tempQuota||item.tempQuota=='0'){
                                        this.basicTypeForm.temporaryLimitPut=item.tempQuota;
                                    }
                                    this.basicTypeForm.limitPutTake=item.occupiedQuota;

                                    this.basicTypeForm.limitPutTime=item.validityTermEnd;
                                    if(item.surplusQuota||item.surplusQuota=='0'){
                                        this.basicTypeForm.limitPutResidue=item.surplusQuota;
                                    }
                                } else {
                                    //先放后抵
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPledge = item.quotaControlSwitch+"";
                                    }
                                    console.log("先放后抵"+item.quotaAmount)
                                    if((item.quotaAmount!=""&&item.quotaAmount!=null&&item.quotaAmount!=undefined)||(item.quotaAmount=='0')){
                                        console.log("我进来了")
                                        this.basicTypeForm.limitPledge = item.quotaAmount;

                                    }
                                    if(item.tempQuota||item.tempQuota=='0'){
                                        this.basicTypeForm.temporaryLimitPledge=item.tempQuota;
                                    }

                                    this.basicTypeForm.limitPledgeTake=item.occupiedQuota;

                                    this.basicTypeForm.limitPledgeTime=item.validityTermEnd;
                                    if(item.surplusQuota||item.surplusQuota=='0'){
                                        this.basicTypeForm.limitPledgeResidue=item.surplusQuota;
                                    }
                                }
                            }else{
                                //二手车
                                //签放额度
                                if (item.quotaType == "1") {
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPutOld = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount||item.quotaAmount=='0'){
                                        this.basicTypeForm.limitPutOld = item.quotaAmount;
                                    }
                                    if(item.tempQuota||item.tempQuota=='0'){
                                        this.basicTypeForm.temporaryLimitPutOld=item.tempQuota;
                                    }

                                    this.basicTypeForm.limitPutTakeOld=item.occupiedQuota;

                                    this.basicTypeForm.limitPutTimeOld=item.validityTermEnd;
                                    if(item.surplusQuota||item.surplusQuota=='0'){
                                        this.basicTypeForm.limitPutResidueOld=item.surplusQuota;
                                    }
                                } else {
                                    //先抵后放
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPledgeOld = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount||(item.quotaAmount=='0')){
                                        this.basicTypeForm.limitPledgeOld = item.quotaAmount;

                                    }
                                    if(item.tempQuota||item.tempQuota=='0'){
                                        this.basicTypeForm.temporaryLimitPledgeOld=item.tempQuota;
                                    }

                                    this.basicTypeForm.limitPledgeTakeOld=item.occupiedQuota;

                                    this.basicTypeForm.limitPledgeTimeOld=item.validityTermEnd;
                                    if(item.surplusQuota||item.surplusQuota=='0'){
                                        this.basicTypeForm.limitPledgeResidueOld=item.surplusQuota;
                                    }
                                }
                            }

                        })
                    }
                })


            },
            getChannelList(v) {
                // 多件基本信息
                this.loading = true;
                getChannelListData(this.basicTypeForm).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.data = res.data.records;
                        this.total = res.data.total;
                    }
                });
            },
            //地址公共信息调用
            locationChange(item, callback) {
                item.loading = true;
                getLocation({upperCode: item.value}).then(res => {
                    if (res.code === "0000") {
                        res.data.forEach(function (item) {
                            if (item.isParent) {
                                item.loading = false;
                                item.children = [];
                            }
                        });
                        item.children = res.data;
                        item.loading = false;
                        callback();
                    }
                })
            },
            onChangeLocation(value, data){
                console.log(value, data);
                let str='';
                if(Array.isArray(data) && data.length){
                    data.forEach(ele =>{
                        str += (ele.label ? ele.label : '');
                    })
                    this.mapLocation = str;
                    if(this.mapAddress) {
                        this.canOpenMapModal = true;
                    }
                }
            },
            onBlurLocation(){
                this.$refs.basicTypeForm.validateField('officeAddressDetail',(e) => {
                    console.log('officeAddressDetail',e,typeof e);
                    if(!e) {
                        this.mapAddress = this.basicTypeForm.officeAddressDetail;
                        if(this.mapLocation) {
                            this.canOpenMapModal = true;
                        }
                    }
                })
            },

            getLocationData() {
                let param = ""
                param = {
                    level: '1',
                    upperCode: '1'
                };
                getLocation(param).then((res) => {
                    if (res.code == "0000") {
                        res.data.forEach(item => {
                            // 表明是否是父节点
                            if (item.isParent) {
                                item.loading = false;
                                item.children = [];
                            }
                        })
                    }
                    this.location.push(...res.data);
                })
            },

            closeModel(value){
                this.isShowModel=value;
            },
            //Gps授权厂商
            handgps_vendor_author (val) {
                this.gpsvendorauthor.push({
                    value: val,
                    label: val
                });
            },
            //Gps安装方式
            handgps_instal_mode(val) {
                this.gpsinstalmode.push({
                    value: val,
                    label: val
                });
            },
            //新车区域展示
            addNewCar(){
                //展示新车
                this.showGroupNewCar="1";
                //隐藏二手车
                this.showGroupOldCar="0";
            },
            //二手车展示
            addOldCar(){
                //展示二手车按钮
                this.showGroupNewCar="0";
                //展示二手车
                this.showGroupOldCar="1";
            },
            showAreasNewCar(){
                //新车展业区域查看
                let areaId = [];
                let selectedAreaNodes = this.$refs.tree.getSelectedNodes();
                selectedAreaNodes.forEach(function (e) {
                    areaId.push(e.value);
                });
                if(areaId.length==0){
                    this.$Message.error("请至少选中一个区域!");
                    return;
                }
                this.areaVisible=true;
                this.searchForm.areaId=areaId;
                this.getAreaList();
            },
            showAreasOldCar(){
                //二手车展业区域
                let areaId = [];
                let selectedAreaNodes = this.$refs.treeOld.getSelectedNodes();
                selectedAreaNodes.forEach(function (e) {
                    areaId.push(e.value);
                });
                if(areaId.length==0){
                    this.$Message.error("请至少选中一个区域!");
                    return;
                }
                this.areaVisible=true;
                this.searchForm.areaId=areaId;
                this.getAreaList();
            },
            selectDateRange(v) {
                if (v) {
                    this.basicTypeForm.startDate = v[0];
                    this.basicTypeForm.endDate = v[1];
                }
            },
            getAreaList() {
                // 多条件搜索已上线的渠道展业区域
                this.loading = true;
                getAreaListData(this.searchForm).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.dataArea = res.data.records;
                        this.totalArea = res.data.total;
                    }
                });
            },
            refresh() {
                this.$refs['basicTypeForm'].reloadData();
            },

            basicSave(){
                console.log("渠道基本信息保存开始！");
                this.$refs.basicTypeForm.validate((valid) => {
                    console.log(valid,"必输验证------");
                    if (valid) {
                        this.$parent.spinShow=true;
                        let brandIds = this.getSelectedBrandNodes(this.$refs.mainBrand.getCheckedNodes())

                        if(brandIds.length==0){
                            this.$Message.error("请勾主营品牌");
                            this.$parent.spinShow=false;
                            return false;
                        }
                        this.basicTypeForm.mainBrand=brandIds;

                        let areaIds = [];
                        let carIds = [];
                        //新车角色权限 && 而且当前页面存在新车表单
                        if(this.isNewCarRole=="1"){
                            if(this.basicTypeForm.authRegionSwitch=="1"){
                                areaIds = this.getSelectedAreaNodes(this.$refs.mainArea.getCheckedNodes());

                                if(areaIds.length===0 && this.basicTypeForm.authRegionSwitch=="1"){
                                    this.$Message.error("请勾选新车授权区域");
                                    this.$parent.spinShow=false;
                                    return false;
                                }
                            }
                            if(this.basicTypeForm.authVehicleTypeSwitch=="1"){
                                carIds = this.getSelectedCarNodes(this.$refs.mainCar.getCheckedNodes())

                                if(carIds.length===0 && this.basicTypeForm.authVehicleTypeSwitch=="1"){
                                    this.$Message.error("请勾新车授权车型");
                                    this.$parent.spinShow=false;
                                    return false;
                                }
                            }

                        }
                        this.basicTypeForm.mainArea=areaIds;
                        this.basicTypeForm.mainCar=carIds;

                        let carOldIds = [];
                        let areaOldIds = [];
                        //二手车角色权限 && 而且当前页面存在二手车表单
                        if(this.isOldCarRole=="1"){
                            if(this.basicTypeForm.authVehicleTypeSwitchOld=="1"){
                                carOldIds = this.getSelectedCarNodes(this.$refs.mainCarOld.getCheckedNodes())

                                if(carOldIds.length==0 && this.basicTypeForm.authVehicleTypeSwitchOld=="1"){
                                    this.$Message.error("请勾二手车授权车型");
                                    this.$parent.spinShow=false;
                                    return false;
                                }
                            }
                            if(this.basicTypeForm.authRegionSwitchOld=="1"){
                                areaOldIds = this.getSelectedAreaNodes(this.$refs.mainAreaOld.getCheckedNodes());

                                if(areaOldIds.length==0 && this.basicTypeForm.authRegionSwitchOld=="1"){
                                    this.$Message.error("请勾选二手车授权区域");
                                    this.$parent.spinShow=false;
                                    return false;
                                }
                            }
                        }
                        this.basicTypeForm.mainCarOld=carOldIds;
                        this.basicTypeForm.mainAreaOld=areaOldIds;

                        let param = {
                            //当前登陆者是否拥用新车角色权限:0否，1是
                            isNewCarRole:this.isNewCarRole,
                            //当前登陆者是否拥用二手车角色权限:0否，1是
                            isOldCarRole:this.isOldCarRole,
                            //渠道基本信息
                            channelBaseInfoTemp: {
                                //渠道类型:合作商
                                channelType:"01",
                                //主键
                                id:this.basicTypeForm.id,
                                //渠道代码
                                channelCode: this.basicTypeForm.channelCode,
                                //合作商全称
                                channelFullName:this.basicTypeForm.channelFullName.replace(/\s*/g,""),
                                //上线日期
                                onlineDate:this.basicTypeForm.onlineDate,
                                //统一社会信用代码
                                socUniCrtCode:this.basicTypeForm.socUniCrtCode.replace(/\s*/g,""),
                                //法人
                                legalPerson:this.basicTypeForm.legalPerson.replace(/\s*/g,""),
                                //法人身份证号
                                legalPersonIdCard:this.basicTypeForm.legalPersonIdCard.replace(/\s*/g,""),
                                //法人电话
                                legalPersonTel:this.basicTypeForm.legalPersonTel,
                                //实际控制人
                                actualController:this.basicTypeForm.actualController.replace(/\s*/g,""),
                                //实际控制人身份证号
                                actualControllerIdCard:this.basicTypeForm.actualControllerIdCard,
                                //实际控制人电话
                                actualControllerTel:this.basicTypeForm.actualControllerTel,
                                //管理员姓名
                                channelAdmin:this.basicTypeForm.channelAdmin.replace(/\s*/g,""),
                                //管理员电话
                                channelAdminTel:this.basicTypeForm.channelAdminTel,
                                //管理员邮箱
                                channelAdminMail:this.basicTypeForm.channelAdminMail,
                                //管理员证件号码
                                channelAdminIdCard:this.basicTypeForm.channelAdminIdCard,
                                //渠道管理员详细地址
                                channelAdminAddress:this.basicTypeForm.channelAdminAddressDetail,
                                //注册地址详细地址
                                channelAddress:this.basicTypeForm.channelAddressDetail,
                                //办公地址详细地址
                                officeAddress:this.basicTypeForm.officeAddressDetail,
                                longitude:this.basicTypeForm.longitude,
                                //地址纬度
                                latitude:this.basicTypeForm.latitude,
                                //经纬度范围
                                longitudeLatitudeRange:this.basicTypeForm.longitudeLatitudeRange,
                                //公司人数
                                companiesNumber:this.basicTypeForm.companiesNumber,
                                //经营场所面积
                                businessArea:this.basicTypeForm.businessArea,
                                //汇款对象
                                paymentObject:this.basicTypeForm.paymentObject,
                                //注册日期
                                registrationDate:this.basicTypeForm.registrationDate,
                                //主营品牌
                                mainBrand:this.basicTypeForm.mainBrand,
                                //注册资金
                                registeredCapital:this.basicTypeForm.registeredCapital,
                                //实收资本
                                subscribedCapital:this.basicTypeForm.subscribedCapital,
                                //渠道归属
                                channelBelong:this.basicTypeForm.channelBelong,
                                //业务类型;新车/二手车
                                delete:this.basicTypeForm.businessType,
                                businessType:this.basicTypeForm.businessType.join(','),

                                //资产净值
                                netAssetValue:this.basicTypeForm.netAssetValue,
                                //经营年限
                                businessLife:this.basicTypeForm.businessLife,
                                //GPS厂商授权
                                delete:this.basicTypeForm.gpsVendorAuthor,
                                gpsVendorAuthor:this.basicTypeForm.gpsVendorAuthor.join(','),

                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //上一年度经营利润
                                operatingProfit:this.basicTypeForm.operatingProfit,
                                //GPS安装方式
                                delete:this.basicTypeForm.gpsInstalMode,
                                gpsInstalMode:this.basicTypeForm.gpsInstalMode.join(','),

                                //业绩
                                achievement:this.basicTypeForm.achievement,
                                //利润率增长率
                                proGrowthRate:this.basicTypeForm.proGrowthRate,
                                //销售收入增长率
                                saleIncreaseRate:this.basicTypeForm.saleIncreaseRate,
                                //流动比率
                                currentRatio:this.basicTypeForm.currentRatio,
                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //经纬度是否控制;是/否
                                longitudeLatitudeSwitch:this.basicTypeForm.longitudeLatitudeSwitch,
                                //业务人员关联车商;是/否
                                personRelCardealerSwitch:this.basicTypeForm.personRelCardealerSwitch,
                                //进件选择车商;是/否
                                choiceCardealerSwitch:this.basicTypeForm.choiceCardealerSwitch,

                                //经纬度是否控制;是/否
                                longitudeLatitudeSwitchOld:this.basicTypeForm.longitudeLatitudeSwitchOld,
                                //业务人员关联车商;是/否
                                personRelCardealerSwitchOld:this.basicTypeForm.personRelCardealerSwitchOld,
                                //进件选择车商;是/否
                                choiceCardealerSwitchOld:this.basicTypeForm.choiceCardealerSwitchOld,

                                //层级最大数目
                                hierarchy:this.basicTypeForm.hierarchy

                            },
                            //管理员省市区
                            channelAdminAddressValue:this.basicTypeForm.channelAdminAddressValue,
                            //注册地省市区
                            channelAddressValue:this.basicTypeForm.channelAddressValue,
                            //办公室省市区
                            officeAddressValue:this.basicTypeForm.officeAddressValue,
                            //新车授权区域
                            mainArea:this.basicTypeForm.mainArea,
                            //新车主营品牌
                            mainBrand:this.basicTypeForm.mainBrand,
                            //新车授权车型
                            mainCar:this.basicTypeForm.mainCar,

                            //新车授权区域
                            mainAreaOld:this.basicTypeForm.mainAreaOld,
                            //二手车授权车型
                            mainCarOld:this.basicTypeForm.mainCarOld,

                            //新车渠道风控信息
                            channelRiskInfoTemp: {
                                //业务类型：新车
                                businessType:"01",
                                qualityGrade:this.basicTypeForm.qualityGrade,
                                qualityStartDate:this.basicTypeForm.validDate[0],
                                qualityEndDate:this.basicTypeForm.validDate[1],

                                delete:this.basicTypeForm.carType,
                                carType:this.basicTypeForm.carType.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitch,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitch,
                                channelGrade:this.basicTypeForm.channelGrade,
                                channelDeposit:this.basicTypeForm.channelDeposit,
                                customerManager:this.basicTypeForm.customerManager,
                                accountMaxNum:this.basicTypeForm.accountMaxNum,
                                witnessMaxNum:this.basicTypeForm.witnessMaxNum,
                            },
                            //二手车渠道风控信息
                            channelRiskInfoTempOld: {
                                //业务类型：二手车
                                businessType:"02",
                                qualityGrade:this.basicTypeForm.qualityGradeOld,
                                qualityStartDate:this.basicTypeForm.validDateOld[0],
                                qualityEndDate:this.basicTypeForm.validDateOld[1],

                                delete:this.basicTypeForm.carTypeOld,
                                carType:this.basicTypeForm.carTypeOld.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitchOld,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitchOld,
                                channelGrade:this.basicTypeForm.channelGradeOld,
                                channelDeposit:this.basicTypeForm.channelDepositOld,
                                customerManager:this.basicTypeForm.customerManagerOld,
                                accountMaxNum:this.basicTypeForm.accountMaxNumOld,
                                witnessMaxNum:this.basicTypeForm.witnessMaxNumOld,
                            },
                            //新车渠道保证金信息
                            channelQuotaInfoTempList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPut,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPut,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPut,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidue,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledge,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledge,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledge,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidue,
                                }
                            ],

                            //二手车保证金信息
                            channelQuotaInfoTempOldList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPutOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPutOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPutOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidueOld,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledgeOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledgeOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledgeOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidueOld,
                                }
                            ],
                        }

                        modifyChannel(param).then(res => {
                            if (res.code === "0000") {
                                this.basicTypeForm.id=res.data[0].id;
                                this.getChannelInfo(res.data[0].id);
                                this.$parent.setchannelId(res.data[0].id);
                                this.getMainAreaList(res.data[0].id);
                                this.getBaseTreeList(res.data[0].id);
                                this.$Message.success("保存成功");
                                this.$parent.spinShow=false;
                                //基本信息处理
                            }
                        }).catch(() => {
                            this.$Message.error("保存失败");
                            this.$parent.spinShow=false;
                        });
                    }else{
                        for (let i in this.$refs.basicTypeForm.fields) {
                            if (this.$refs.basicTypeForm.fields[i].validateState === "error") {
                                this.$refs.basicTypeForm.fields[i].$el.scrollIntoView({
                                    block: "center",
                                    behavior: "smooth",
                                    inline: "center"
                                });
                                break;
                            }
                        }
                    }
                });
            },

            //验证黑名单或者统一社会信用代码是否重复
            checkBlackOrCode(){
                if(this.basicTypeForm.socUniCrtCode==null||this.basicTypeForm.socUniCrtCode==""||this.basicTypeForm.socUniCrtCode==undefined){
                    return;
                }
                //黑名单保存验证改渠道是否在黑名单信息表中
                let blackParams={
                    //黑名单类型：合作商
                    blacklistType:'01',
                    //黑名单名称
                    blackName:this.basicTypeForm.channelFullName,
                    //证件类型：统一社会信息代码
                    idType:'02',
                    //统一社会信用代码
                    idCode:this.basicTypeForm.socUniCrtCode,
                }
                blackCleack(blackParams).then(res => {
                    if (res.code === "0000") {
                        //黑名单判断字段:是
                        if(res.weatherBlackList === "1"){
                            this.$Modal.confirm({
                                content: "该渠道已存在黑名单信息库中!",
                                onOk: () => {
                                    //this.$parent.back();
                                },
                                onCancel:() => {
                                    //this.$parent.back();
                                },
                            });
                        }
                    }
                });

                let codeParams={
                    id:this.basicTypeForm.id,
                    socUniCrtCode:this.basicTypeForm.socUniCrtCode,
                }
                socUniCrtCodeCheck(codeParams).then(res => {
                    if (res.code === "0000") {
                        //统一社会信用代码验重
                        //res.data[0]
                        if(res.yes === "1"){
                            this.$Modal.confirm({
                                content: "该统一社会信用代码已存在!",
                                onOk: () => {
                                    //this.$parent.back();
                                },
                                onCancel:() => {
                                    //this.$parent.back();
                                },
                            });
                        }
                    }
                });
            },
            blackChannel(){
                console.log("黑名单申请开始！");
                this.modalTitle="确认将合作商加入黑名单吗？";
                this.blackApplyVisible=true;
            },
            cancel() {
                //关闭黑名单窗口
                this.blackApplyVisible = false;
            },
            //申请加入黑名单
            join(){
                console.log("渠道黑名单保存开始！");
                let params={
                    //黑名单类型：合作商
                    blacklistType:'01',
                    //黑名单名称
                    blackName:this.basicTypeForm.channelFullName,
                    //证件类型：组织机构代码
                    idType:'02',
                    idCode:this.basicTypeForm.socUniCrtCode,
                    //黑名单原因
                    blacklistReason:this.blackApplyForm.blacklistReason
                }
                saveBlack(params).then(res => {
                    if (res.code === "0000") {
                        this.$Message.success("申请成功！");
                        this.cancel();
                    }
                }).catch(() => {
                    this.$Message.success("申请失败！");
                });
            },
            changeSelect() {

            },
            selectChange(){

            },
            readOnlyInfo(b) {
                let _this=this;
                this.$nextTick(()=>{
                    _this.typeCode=b;
                })
            },

            disabledTree(treeType){
                let bool;
                switch (treeType) {
                    case 'oldCar':
                        bool = !this.pageParams.infoView || this.isDisableOldCarForm;
                        break;
                    case 'newCar':
                        bool = !this.pageParams.infoView || this.isDisableNewCarForm
                        break;
                    default:
                        bool = !this.pageParams.infoView;
                }
                return bool;
            },

            //主营品牌
            getBaseTreeList(v) {
                if (v) {
                    getCarBrandById(v).then(res => {
                        this.loading = false;
                        if (res.code === "0000") {
                            console.time('getCarBrandById')
                            let treeData = deepClone(this.allBrandTree);
                            console.log('getCarBrandByIds',res.data)
                            if (res.data.length > 0) {
                                let obj = this.arrToObj(res.data, 'code');
                                this.setMainBrandCheckTree(treeData, obj, 'code')
                            }
                            treeData[0].chkDisabled = this.disabledTree();
                            this.baseData = treeData;

                            console.log('baseData',treeData)
                            console.timeEnd('getCarBrandById')
                            this.mainBrandTreeInit = true
                        }
                    })
                }else {
                    this.baseData = deepClone(this.allBrandTree);
                    this.mainBrandTreeInit = true;
                }
            },
            arrToObj(arr, key) {
                let obj = {};
                arr.forEach(ele => {
                    obj[ele[key]] = ele
                });
                return obj;
            },
            setCheckTree(data, obj, key) {
                data.forEach(e => {
                    if (obj[e[key]]) {
                        e.checked = true;
                    }
                    e.children && e.children.length > 0 && this.setCheckTree(e.children, obj, key)
                })
            },
            setMainBrandCheckTree(data, obj, key){
                data.forEach(e => {
                    let v = obj[e[key]]
                    if (v) {
                        e.checked = true;
                        if (v.indeterminate === "false") {
                            Array.isArray(e.children) && e.children.forEach(i => {
                                i.checked = true;
                                Array.isArray(i.children) && i.children.forEach(j => { j.checked = true })
                            })
                            return ;
                        }
                    }
                    Array.isArray(e.children) && this.setMainBrandCheckTree(e.children, obj, key)
                })
            },

            //授权区域
            getMainAreaList(v) {
                if (!v) return;
                getNewMainAreaById(v).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        console.time('授权区域')
                        let oldArea = [],
                            newArea = [], oldObj, newObj,
                            oldTreeData = deepClone(this.allAreaList),
                            newTreeData = deepClone(this.allAreaList);
                        console.log('getNewMainAreaById',res.data)
                        if (Array.isArray(res.data) && res.data.length > 0) {
                            res.data.forEach(e => {
                                e.businessType === "01" ? newArea.push(e) : oldArea.push(e);
                            })
                            this.setCheckTree(oldTreeData, this.arrToObj(oldArea, 'code'), 'value')

                            this.setCheckTree(newTreeData, this.arrToObj(newArea, 'code'), 'value')
                        }
                        newTreeData[0].chkDisabled = this.disabledTree();
                        oldTreeData[0].chkDisabled = this.disabledTree();
                        this.areasData = newTreeData;
                        this.areasDataOld = oldTreeData;
                        this.isAreasDataInit = true;
                        this.isAreasDataOldInit = true;
                        console.timeEnd('授权区域')
                    }
                })
            },

            checkAllNewCar(v) {
                if (v != null && v !== "" && v !== undefined) {
                    getNewVehicleById(v).then(res => {
                        this.loading = false;
                        if (res.code === "0000") {
                            let treeData = deepClone(this.allBrandTree);
                            if (res.data.length > 0) {
                                let obj = this.arrToObj(res.data, 'code');
                                this.setMainBrandCheckTree(treeData, obj, 'code')
                            }
                            treeData[0].chkDisabled = this.disabledTree();
                            this.carsData = treeData;
                            // console.log(res.data, '新车----------', this.carsData)
                            this.isCarsDataInit = true;
                        }
                    })
                } else {
                    this.carsData = deepClone(this.allBrandTree);
                    this.isCarsDataInit = true;
                }
            },

            checkAllOldCar(v) {
                if (v) {
                    getOldVehicleById(v).then(res => {
                        this.loading = false;
                        if (res.code === "0000") {
                            let treeData = deepClone(this.allBrandTree);
                            if (res.data.length > 0) {
                                let obj = this.arrToObj(res.data, 'code');
                                this.setMainBrandCheckTree(treeData, obj, 'code')
                            }
                            treeData[0].chkDisabled = this.disabledTree();
                            this.carsDataOld = treeData;
                            this.isCarsDataOldInit = true;
                        }
                    })
                }else {
                    this.carsDataOld = deepClone(this.allBrandTree);
                    this.isCarsDataOldInit = true;
                }
            },

            changeSort(e) {
                this.searchForm.sort = e.key;
                this.searchForm.order = e.order;
                if (e.order === "normal") {
                    this.searchForm.order = "";
                }
                this.getAreaList();
            },

            showSelect(e) {
                this.selectList = e;
                this.selectCount = e.length;
            },

            changePage(v) {
                this.searchForm.pageNumber = v;
                this.getAreaList();
                this.clearSelectAll();
            },
            changePageSize(v) {
                this.searchForm.pageSize = v;
                this.getAreaList();
            },
            clearSelectAll() {
                this.$refs.table.selectAll(false);
            },
            //新车区域经理
            getAllRegionalManager(){
                //获取当前区域经理下面所有的业务人员
                getAllRegionalManager("ROLE_NEW_REG_MANAGER").then(res => {
                    if (res.code === "0000") {
                        this.regionalManager=res.data.data;
                    }
                });
            },
            //二手车区域经理
            getAllRegionalManagerOld(){
                //获取当前二手车区域经理下面所有的业务人员
                getAllRegionalManager("ROLE_OLD_REG_MANAGER").then(res => {
                    if (res.code === "0000") {
                        this.regionalManagerOld=res.data.data;
                    }
                });
            },
            //主营品牌模糊查询
            search() {
                let newBaseDataTemp = deepClone(this.mainCarSearch);
                let newBaseData = [];
                let _this = this;
                newBaseDataTemp.forEach(function (e,index) {
                    let obj = {title:e.title,id:e.id,children:[],expand:true};
                    if (e.children && e.children.length >= 0) {
                        for (let i=0;i<e.children.length;i++) {
                            if(e.children[i].title.indexOf(_this.searchKey) >= 0){
                                obj.children.push(e.children[i]);
                            }
                        }
                    }
                    newBaseData.push(obj)
                });
                this.baseData=newBaseData;
            },
            //获取勾选变量
            changeMainBrandSelect(all,current){
                let data = deepClone(this.mainCarSearch);
                if(Array.isArray(this.mainCarSearch)){
                    this.mainCarSearch.forEach((item,index)=>{
                        if(item.id === current.id){
                            data[index].checked = current.checked
                        }else if(Array.isArray(item.children)){
                            item.children.forEach((itm,idx)=> {
                                if(itm.id === current.id){
                                    data[index].children[idx].checked = current.checked
                                }else if(Array.isArray(itm.children)){
                                    itm.children.forEach((i,d)=>{
                                        if(i.id === current.id) {
                                            data[index].children[idx].children[d].checked = current.checked
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
                this.mainCarSearch = data;
            },
            //新车授权区域勾选判断
            changeAreasDataSelect(all,current){
                let data = deepClone(this.areasDataSearch);
                if(Array.isArray(this.areasDataSearch)){
                    this.areasDataSearch.forEach((item,index)=>{
                        if(item.id === current.id){
                            data[index].checked = current.checked
                        }else if(Array.isArray(item.children)){
                            item.children.forEach((itm,idx)=> {
                                if(itm.id === current.id){
                                    data[index].children[idx].checked = current.checked
                                }else if(Array.isArray(itm.children)){
                                    itm.children.forEach((i,d)=>{
                                        if(i.id === current.id) {
                                            data[index].children[idx].children[d].checked = current.checked
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
                this.areasDataSearch = data;
            },

            changeCarsDataSelect(all,current){
                let data = deepClone(this.carsDataSearch);
                if(Array.isArray(this.carsDataSearch)){
                    this.carsDataSearch.forEach((item,index)=>{
                        if(item.id === current.id){
                            data[index].checked = current.checked
                        }else if(Array.isArray(item.children)){
                            item.children.forEach((itm,idx)=> {
                                if(itm.id === current.id){
                                    data[index].children[idx].checked = current.checked
                                }else if(Array.isArray(itm.children)){
                                    itm.children.forEach((i,d)=>{
                                        if(i.id === current.id) {
                                            data[index].children[idx].children[d].checked = current.checked
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
                this.carsDataSearch = data;
            },
            //二手车授权车型查询
            changeCarsDataOldSelect(all,current){
                let data = deepClone(this.carsDataOldSearch);
                if(Array.isArray(this.carsDataOldSearch)){
                    this.carsDataOldSearch.forEach((item,index)=>{
                        if(item.id === current.id){
                            data[index].checked = current.checked
                        }else if(Array.isArray(item.children)){
                            item.children.forEach((itm,idx)=> {
                                if(itm.id === current.id){
                                    data[index].children[idx].checked = current.checked
                                }else if(Array.isArray(itm.children)){
                                    itm.children.forEach((i,d)=>{
                                        if(i.id === current.id) {
                                            data[index].children[idx].children[d].checked = current.checked
                                        }
                                    })
                                }
                            })
                        }
                    })
                }
                this.carsDataOldSearch = data;
            },
            // 查询数据字典
            initDataDic() {
                this.channelBelongType();
                this.paymentObjectType();
                this.qualityGradeType();
                this.channelGradeType();
                this.gpsSupplier();
                this.gpsinstalmode();
            },
            //渠道归属数据字典
            channelBelongType() {
                let param = {
                    type: "channelBelong"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.channelBelongList = res.data;
                    }
                });
            },
            //汇款对象数据字典
            paymentObjectType() {
                let param = {
                    type: "paymentObject"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.paymentObjectList = res.data;
                    }
                });
            },
            //优质等级
            qualityGradeType() {
                let param = {
                    type: "qualityGrade"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.qualityGradeList = res.data;
                    }
                });
            },
            //渠道评级
            channelGradeType() {
                let param = {
                    type: "channelGrade"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.channelGradeList = res.data;
                    }
                });
            },
            //GPS厂商字典处理
            gpsSupplier() {
                let param = {
                    type: "gpsSupplier"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.gpsvendorauthorList = res.data;
                    }
                });
            },
            //GPS安装方式字典处理
            gpsinstalmode() {
                let param = {
                    type: "gpsinstalmode"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.gpsinstalmodeList = res.data;
                    }
                });
            },
            // 地图弹窗
            openMapModal(){
                let address = this.mapLocation
                let detial = this.basicTypeForm.officeAddressDetail
                console.log(address,detial);
                this.mapModal = true;
            },
            // 确认经纬度
            confirmLngLat(data){
                console.log('选择的经纬度为：',data);
                this.mapModal = false;
                if(data && data.latitude && data.longitude){
                    this.basicTypeForm.latitude=data.latitude.toString();
                    this.basicTypeForm.longitude=data.longitude.toString();
                }
            },
            closeMapModel(){
                this.mapModal = false;
            },
            getDetailAddress(officeProvince,officeAddress){
                this.mapLocation=officeProvince;
                this.mapAddress = officeAddress;
                this.canOpenMapModal = true;
            },

            //汇款对象修改调用检验方法
            checkPaymentObject(){
                //0:主体 1:车商
                if(this.basicTypeForm.paymentObject=='1'){
                //主体变车商,提示一下
                    this.$Modal.warning({
                        title: "警告",
                        content: "请确认所有与此合作商关联的车商是否有收款账号!",
                    });
                }else{
                    //车商变主体,调用方法校验
                    this.$parent.spinShow=true;
                    getAccountInfoByChannelId(this.basicTypeForm.id,this.basicTypeForm.id).then(res => {
                        if (res.code === "0000") {
                            this.$parent.spinShow=false;
                            if(res.data=='请录入新车车款账户!'){
                                this.basicTypeForm.paymentObject='1'
                                setTimeout(() => {
                                    this.$Modal.warning({
                                        title: "警告",
                                        content: "缺少已启用新车车款账户!",
                                    });
                                },300)
                            }else if(res.data=='请录入新车佣金账户!'){
                                this.basicTypeForm.paymentObject='1'
                                setTimeout(() => {
                                    this.$Modal.warning({
                                        title: "警告",
                                        content: "缺少已启用新车佣金账户!",
                                    });
                                },300)
                            }else if(res.data=='请录入二手车车款账户!'){
                                this.basicTypeForm.paymentObject='1'
                                setTimeout(() => {
                                    this.$Modal.warning({
                                        title: "警告",
                                        content: "缺少已启用二手车车款账户!",
                                    });
                                },300)

                            }else if(res.data=='请录入二手车佣金账户!'){
                                this.basicTypeForm.paymentObject='1'
                                setTimeout(() => {
                                    this.$Modal.warning({
                                        title: "警告",
                                        content: "缺少已启用二手车佣金账户!",
                                    });
                                },300)
                            }
                        }
                    }).catch(() => {
                        this.$parent.spinShow=false;
                    });
                }
            },
            queryAllAreaList(v) {
                getAllAddressList().then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.allAreaList = res.data;
                        this.$nextTick(() => {
                            this.areaListInit();
                        })
                    }
                })
            },
            areaListInit() {
                const {operationType, channelId} = this.pageParams;
                this.getMainAreaList(channelId);
            },

            queryAllBrandTree() {
                let param = {type: []};

                getBrandTree(param).then(res => {
                    this.loading = false;
                    if (res.code === "0000" && res.data) {
                        this.allBrandTree = res.data;
                        this.brandTreeInit();
                    }
                });
            },
            brandTreeInit() {
                const {operationType, channelId} = this.pageParams;

                this.getBaseTreeList(channelId);
                this.checkAllNewCar(channelId);
                this.checkAllOldCar(channelId);
            },
            mainBrandSearch(){
                this.isMainBrandTreeSearch = true
            },
            closeMainBrandSearch() {
                this.isMainBrandTreeSearch = false;
            },
            closeSearch() {
                this.isCarTreeSearch = false;
                this.isAreasDataSearch = false;
                this.isCarsDataOldSearch = false;
                this.isAreasDataOldSearch = false;
            },
            searchCarsData() {
                this.isCarTreeSearch = true;
            },
            searchMainArea() {
                this.isAreasDataSearch = true;
            },

            searchMainCarOld() {
                this.isCarsDataOldSearch = true;
            },
            searchAreasOld() {
                this.isAreasDataOldSearch = true;
            },
            getSelectedAreaNodes(node) {
                let arr = [];
                Array.isArray(node) && node.forEach(e => {
                    let isParent = e.isParent && e.children && e.children.length > 0 ? "1" : "0";
                    let halfCheck = e.check_Child_State === 1;
                    arr.push(e.value + "," + e.title + "," + isParent + "," + e.upperCode + "," + halfCheck);
                });
                return arr;
            },
            getSelectedBrandNodes(node) {
                let arr = [];
                Array.isArray(node) && node.forEach(e => {
                    let halfCheck = e.check_Child_State === 1;

                    if (e.spellCode === undefined) {
                        e.spellCode = " ";
                    }
                    arr.push(e.code + "," + e.title + "," + halfCheck + "," + e.carLevel + "," + e.spellCode);
                })
                return arr;
            },
            getSelectedCarNodes(node) {
                let arr = [];
                Array.isArray(node) && node.forEach(e => {
                    let halfCheck = e.check_Child_State === 1;

                    if (e.spellCode === undefined) {
                        e.spellCode = " ";
                    }
                    arr.push(e.code + "," + e.title + "," + e.upperCode + "," + halfCheck + "," + e.carLevel + "," + e.spellCode + "," + e.carType);
                });
                return arr;
            },
        },
        created() {
            this.pageParams = this.afs.getPageParams(this);
            const {channelStatus,channelStatusOldCar} = this.pageParams;
            this.isDisableNewCarForm =  !!channelStatus && channelStatus !== "02" && channelStatus !== "03" && channelStatus !== "04";
            this.isDisableOldCarForm = !!channelStatusOldCar && channelStatusOldCar !== "02" && channelStatusOldCar !== "03" && channelStatusOldCar !== "04";

            this.initDataDic();
        },
        mounted() {
            this.getLocationData();
            this.init();
        },
    }
</script>

<style>

    .basis-info .ivu-input[disabled]{
        color: black;
    }
    .basis-info span.ivu-select-selected-value{
        color: black;
    }
    .basis-info .ivu-input-number-input[disabled]{
        color:black;
    }
    .addressDetail .ivu-form-item-content{
        margin-left: 0 !important;
    }

</style>
