<template>
    <div class="basis-info">
        <div class="basis_content">
            <Form ref="basicTypeForm" :model="basicTypeForm" :rules="basicTypeFormValidate" v-bind:disabled="this.typeCode == '0'"  :label-width="150">
                <!--基本信息   -->
                <Card>


                <div class="common_br">
                    <h2 class="common_channel_title" >基本信息</h2>
                    <div class="common_content">
                        <Row>
                            <Col span="8">
                                <FormItem label="车商代码"  prop="channelCode" >
                                    <Input v-model.trim="basicTypeForm.channelCode"  readonly="readonly"  class="w200" placeholder="系统自动生成"/>
                                </FormItem>
                            </Col>

                            <Col span="8">

                                <FormItem label="车商名称" prop="channelFullName" >
                                    <Select
                                        v-model="basicTypeForm.channelFullName"
                                        clearable
                                        remote
                                        loadingText="查询中"
                                        :loading="loadModel"
                                        @on-change="searchSelectModel"
                                        style="width: 200px"
                                    >
                                        <Input  v-model="searchName"  placeholder="请输入" @on-change="remoteModel()">
                                            <template #suffix>
                                                <Icon type="ios-search" class="search"/>
                                            </template>
                                        </Input>>
                                        <Option :value="item.entName+item.frName" v-for="(item,index) in searchModelList" :key="index">{{item.entName}}</Option>
                                        <Option :value="basicTypeForm.channelFullName"  style="display: none;">{{basicTypeForm.channelFullName}}</Option>
                                    </Select>
                                </FormItem>
                            </Col>


                            <Col span="8">
                                <FormItem label="合作商名称" prop="channelId" style="width: 350px"  v-show="!this.basicTypeForm.id">
                                    <Select v-model="basicTypeForm.channelId" filterable  placeholder="请选择" style="width: 200px" class="s1"  @on-change="getChannelInfoById" v-bind:disabled="this.typeCode2 == '2' && this.OldStatus !='06'" clearable>
                                        <Option v-for="(item,index) in channelData" :value="item.id" :label="item.channelFullName"  :key="index" ></Option>
                                    </Select>
                                </FormItem>
                            </Col>

                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="统一社会信用代码" prop="socUniCrtCode" >
                                    <Input v-model.trim="basicTypeForm.socUniCrtCode" class="w200" @on-blur="checkBlack" placeholder="请输入" v-bind:disabled="this.typeCode2 == '2' && this.OldStatus !='06'"/>
                                </FormItem>
                            </Col>
                            <Col span="8">
                                <FormItem label="法人" prop="legalPerson">
                                    <Input v-model.trim="basicTypeForm.legalPerson"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>
                            <Col span="8">
                                <FormItem label="法人身份证号" prop="legalPersonIdCard">
                                    <Input v-model.trim="basicTypeForm.legalPersonIdCard"    class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>
                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="法人电话" prop="legalPersonTel">
                                    <Input v-model.trim="basicTypeForm.legalPersonTel"    class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="实际控制人" prop="actualController">
                                    <Input v-model.trim="basicTypeForm.actualController"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="实际控制人电话" prop="actualControllerTel">
                                    <Input v-model.trim="basicTypeForm.actualControllerTel"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="实际控制人身份证号" prop="actualControllerIdCard">
                                    <Input v-model.trim="basicTypeForm.actualControllerIdCard"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="公司人数" prop="companiesNumber">
                                    <InputNumber :max="1000" :min="0" v-model="basicTypeForm.companiesNumber" class="w200" ></InputNumber>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="经营场所面积(平方米)" prop="businessArea">
                                    <Input v-model.trim="basicTypeForm.businessArea"    class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>


                        </Row>

                        <Row>

                            <Col span="8">
                                <FormItem label="注册地址" prop="channelAddressValue">
                                    <Cascader :load-data="locationChange" :data="location" v-model="basicTypeForm.channelAddressValue" placeholder="下拉选择省市"  class="w200" @on-change="onChangeLocation"  :clearable="false" >
                                    </Cascader>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="请输入详细地址" prop="channelAddressDetail">
                                    <Input v-model.trim="basicTypeForm.channelAddressDetail" placeholder="请输入" class="w200"  @on-blur="onBlurLocation"/>
                                </FormItem>
                            </Col>


                            <Col span="8">
                                <FormItem label="上线日期" prop="onlineDate" >
                                    <Date-picker type="date" v-model="basicTypeForm.onlineDate" class="w200"  placeholder="选择日期" ></Date-picker>
                                </FormItem>
                            </Col>

                        </Row>

                        <Row>

                            <Col span="8">
                                <FormItem label="地址经度" prop="longitude">
                                    <Input v-model.trim="basicTypeForm.longitude"    class="w200"  placeholder="输入地址后自动带入"/>
                                </FormItem>
                            </Col>
                            <Col span="8">
                                <FormItem label="地址纬度" prop="latitude">
                                    <Input v-model.trim="basicTypeForm.latitude"    class="w200"  placeholder="输入地址后自动带入"/>
                                </FormItem>
                            </Col>



                            <Col span="8">
                                <FormItem label="经纬度范围（km）" prop="longitudeLatitudeRange" >
                                    <Input v-model.trim="basicTypeForm.longitudeLatitudeRange"    class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>




                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="车商类型" prop="cardealType">
                                    <RadioGroup v-model="basicTypeForm.cardealType" >
                                        <Radio label="1">4S店</Radio>
                                        <Radio label="0">综合汽贸</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>


                            <Col span="8">
                                <FormItem label="账户信息" prop="accountInfo" >
                                    <RadioGroup v-model="basicTypeForm.accountInfo" >
                                        <Radio label="1" v-bind:disabled="this.accountSwitch == 1">有</Radio>
                                        <Radio label="0" v-bind:disabled="this.accountSwitch == 1">无</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>


                        </Row>
                        <Row>

                            <Col span="12">
                                <FormItem label="主营品牌" prop="mainBrand">
                                    <Input v-model="searchKey" @on-search="search" search enter-button class="w200"
                                           placeholder="输入搜索"/>
                                    <div style="height:200px;overflow-y:auto">
                                        <SelectTree ref="mainBrand" v-model='basicTypeForm.mainBrand' :data='treeData'
                                                    :isInit='isInit'
                                                    :isSearch="isSearch" :searchKey="searchKey"
                                                    @closeSearch="closeSearch"/>
                                    </div>
                                </FormItem>
                            </Col>

                        </Row>

                    </div>
                </div>
                </Card>

                <Card>



                <div class="common_br">
                    <h2 class="common_channel_title" >资质信息</h2>
                    <div class="common_content">
                        <Row>

                            <Col span="8">
                                <FormItem label="业务类型" prop="businessType">
                                    <Checkbox-group  v-model="basicTypeForm.businessType"  @on-change="carBusinessType" >
                                        <Checkbox label="01" v-bind:disabled="oldCar || disableBusinessType || this.businessTypeControlValue=='01'">新车</Checkbox>
                                        <Checkbox label="02" v-bind:disabled="newCar || disableBusinessType || this.businessTypeControlValue=='02'">二手车</Checkbox>
                                    </Checkbox-group>
                                </FormItem>
                            </Col>

                            <Col span="8" v-if="this.showGroupNewCar=='1' && this.newCarRole!='0'">
                                <FormItem label="新车经纬度是否控制" prop="longitudeLatitudeSwitch">
                                    <RadioGroup v-model="basicTypeForm.longitudeLatitudeSwitch"     >
                                        <Radio label="1">是</Radio>
                                        <Radio label="0">否</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>

                            <Col span="8" v-if="this.showGroupOldCar=='1' && this.oldCarRole!='0'">
                                <FormItem label="二手车经纬度是否控制" prop="longitudeLatitudeSwitchOld">
                                    <RadioGroup v-model="basicTypeForm.longitudeLatitudeSwitchOld">
                                        <Radio label="1">是</Radio>
                                        <Radio label="0">否</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>

                        </Row>

                        <Row>

                        <Col span="8">
                            <FormItem label="实收资本(万元)" prop="subscribedCapital">
                                <Input v-model.trim="basicTypeForm.subscribedCapital"   class="w200"  placeholder="请输入"/>
                            </FormItem>
                        </Col>


                            <Col span="8">
                                <FormItem label="注册资金(万元)" prop="registeredCapital">
                                    <Input v-model.trim="basicTypeForm.registeredCapital"    class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                        <Col span="8">
                            <FormItem label="GPS厂商授权" prop="gpsVendorAuthor">
                                <Select v-model="basicTypeForm.gpsVendorAuthor"     multiple allow-create @on-create="handgps_vendor_author" style="width: 200px"  @on-change="changeGPS">
                                    <Option :value="item.value" v-for="(item,index) in gpsvendorauthorList" :key="index"> {{item.title}}</Option>
                                </Select>
                            </FormItem>
                        </Col>

                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="资产净值(元)" prop="netAssetValue">
                                    <Input v-model.trim="basicTypeForm.netAssetValue"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="经营年限(年)" prop="businessLife">
                                    <Input v-model.trim="basicTypeForm.businessLife"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="GPS安装方式" prop="gpsInstalMode">

                                    <Select v-model="basicTypeForm.gpsInstalMode"  style="width: 200px" >
                                        <Option :value="item.value" v-for="(item,index) in gpsinstalmodeList" :key="index"> {{item.title}}</Option>
                                    </Select>
                                </FormItem>
                            </Col>


                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="资产负债率(%)" prop="assetLiabilityRatio">
                                    <Input v-model.trim="basicTypeForm.assetLiabilityRatio"   maxlength="2" class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="注册日期" prop="registrationDate" >
                                    <Date-picker type="date" v-model="basicTypeForm.registrationDate"    class="w200"  placeholder="选择日期" ></Date-picker>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="上一年度经营利润(元)" prop="operatingProfit">
                                    <Input v-model.trim="basicTypeForm.operatingProfit"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>


                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="业绩(单)" prop="achievement">
                                    <Input v-model.trim="basicTypeForm.achievement"   class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="利润率增长率(%)" prop="proGrowthRate">
                                    <Input v-model.trim="basicTypeForm.proGrowthRate"   maxlength="2" class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>

                            <Col span="8">
                                <FormItem label="销售收入增长率(%)" prop="saleIncreaseRate">
                                    <Input v-model.trim="basicTypeForm.saleIncreaseRate"   maxlength="2" class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>
                        </Row>

                        <Row>
                            <Col span="8">
                                <FormItem label="流动比率(%)" prop="currentRatio">
                                    <Input v-model.trim="basicTypeForm.currentRatio"    maxlength="2" class="w200"  placeholder="请输入"/>
                                </FormItem>
                            </Col>
                        </Row>

                    </div>
                </div>

                </Card>

                 <div v-if="this.showGroupNewCar=='1' && this.newCarRole!='0'">
                    <Card>
                    <div class="common_br">
                        <h2 class="common_channel_title" >新车优质信息</h2>
                        <div class="common_content" style="padding-left: 16%;">
                            <Row>
                                <Col span="12">
                                    <FormItem label="优质等级" prop="qualityGrade"  >
                                        <Select v-model="basicTypeForm.qualityGrade"  placeholder="优质等级" class="w200" @on-change="checkQualityGrade"  >
                                            <Option :value="item.value" v-for="(item,index) in qualityGradeList" :key="index"> {{item.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <Form-item label="有效期" prop="validDate" :rules="Number(basicTypeForm.qualityGrade) != 5?validDate:notValidDate">
                                        <DatePicker v-model="basicTypeForm.validDate" type="daterange" format="yyyy-MM-dd"  placeholder="选择起止时间"
                                                    style="width: 200px">
                                        </DatePicker>
                                    </Form-item>
                                </Col>
                            </Row>
                        </div>
                    </div>
                    <div class="common_br">
                        <h2 class="common_channel_title" >新车风控信息</h2>
                        <div class="common_content" style="padding-left: 16%;">
                            <Row>
                                <Col span="12">
                                    <FormItem label="车辆类型" prop="carType">
                                        <Checkbox-group  v-model="basicTypeForm.carType"   ><!--@on-change="checkAllNewCar"-->
                                            <!-- <Checkbox label="00" v-bind:disabled="this.businessTypeSwitch0 == '0'">乘用车</Checkbox>
                                            <Checkbox label="01" v-bind:disabled="this.businessTypeSwitch1 == '1'">LCV</Checkbox>
                                            <Checkbox label="02" v-bind:disabled="this.businessTypeSwitch2 == '2'">中卡</Checkbox>
                                            <Checkbox label="03" v-bind:disabled="this.businessTypeSwitch3 == '3'">重卡</Checkbox> -->
                                            <Checkbox label="1" v-bind:disabled="this.businessTypeSwitch0 == '0'">商用车</Checkbox>
                                            <Checkbox label="2" v-bind:disabled="this.businessTypeSwitch1 == '1'">乘用车</Checkbox>
                                            <Checkbox label="3" v-bind:disabled="this.businessTypeSwitch2 == '2'">设备</Checkbox>
                                            <Checkbox label="4" v-bind:disabled="this.businessTypeSwitch3 == '3'">工程机械</Checkbox>
                                        </Checkbox-group>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="城市经理" prop="customerManager" v-if="!basicTypeForm.id || showSaleMans">
                                        <Select v-model="basicTypeForm.customerManager"    placeholder="城市经理" class="w200">
                                            <Option v-for="(item,index) in salesManData" :value="item.username" :label="item.userRealName" :key="index" >
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>


                            </Row>
                            <Row>
                                <Col span="12">
                                    <FormItem label="评级" prop="channelGrade" >
                                        <Select v-model="basicTypeForm.channelGrade"    placeholder="评级" class="w200">
                                            <Option value="1">A</Option>
                                            <Option value="2">B</Option>
                                            <Option value="3">C</Option>
                                            <Option value="4">D</Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="保证金(元)" prop="channelDeposit">
                                        <Input v-model.trim="basicTypeForm.channelDeposit"    class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度判断 -->
                                <Col span="12">
                                    <FormItem label="是否控制额度" prop="isLimitPut">
                                        <RadioGroup v-model="basicTypeForm.isLimitPut"  @on-change="checkLimitPut()"  >
                                            <Radio label="1">是</Radio>
                                            <Radio label="0">否</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度判断 -->
                                <Col span="12">
                                    <FormItem label="是否控制额度" prop="isLimitPledge">
                                        <RadioGroup v-model="basicTypeForm.isLimitPledge"  @on-change="checkLimitPut()" >
                                            <Radio label="1">是</Radio>
                                            <Radio label="0">否</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col span="12">
                                    <FormItem label="签放额度(元)" prop="limitPut" :rules="Number(basicTypeForm.isLimitPut)?limitPut:notLimitPut">
                                        <Input v-model.trim="basicTypeForm.limitPut"  class="w200"   placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="先放后抵额度(元)" prop="limitPledge" :rules="Number(basicTypeForm.isLimitPledge)?limitPledge:notLimitPledge">
                                        <Input v-model.trim="basicTypeForm.limitPledge"   class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度 -->
                                <Col span="12">
                                    <FormItem label="临时额度(元)" prop="temporaryLimitPut" >
                                        <Input v-model.trim="basicTypeForm.temporaryLimitPut"    class="w200"  placeholder="请输入" @on-change="checkdate()"/>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度 -->
                                <Col span="12">
                                    <FormItem label="临时额度(元)" prop="temporaryLimitPledge"  >
                                        <Input v-model.trim="basicTypeForm.temporaryLimitPledge"   class="w200"  placeholder="请输入" @on-change="checkdate()"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度有效期 -->
                                <Col span="12">
                                    <Form-item label="有效期" prop="limitPutTime" :rules="Number(newCarDate1) != 1?notNewCarLimitPutTime:newCarLimitPutTime">
                                        <Date-picker type="date" v-model="basicTypeForm.limitPutTime"    class="w200"  placeholder="选择日期" ></Date-picker>
                                    </Form-item>
                                </Col>

                                <!-- 先放后抵额度有效期 -->
                                <Col span="12">
                                    <Form-item label="有效期" prop="limitPledgeTime" :rules="Number(newCarDate2) != 1?notNewCarLimitPutTime:newCarLimitPutTime">
                                        <Date-picker type="date" v-model="basicTypeForm.limitPledgeTime"   class="w200"  placeholder="选择日期" ></Date-picker>
                                    </Form-item>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度占用 -->
                                <Col span="12">
                                    <FormItem label="占用额度(元)" prop="limitPutTake">
                                        <Input v-model.trim="basicTypeForm.limitPutTake" readonly="readonly"   class="w200"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度占用 -->
                                <Col span="12">
                                    <FormItem label="占用额度(元)" prop="limitPledgeTake">
                                        <Input v-model.trim="basicTypeForm.limitPledgeTake"  readonly="readonly"  class="w200"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                            </Row>

                            <Row>
                                <!-- 剩余签放额度 -->
                                <Col span="12">
                                    <FormItem label="剩余额度(元)" prop="limitPutResidue">
                                        <Input v-model.trim="basicTypeForm.limitPutResidue"  readonly="readonly"  class="w200"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                                <!-- 剩余先放后抵额度 -->
                                <Col span="12">
                                    <FormItem label="剩余额度(元)" prop="limitPledgeResidue">
                                        <Input v-model.trim="basicTypeForm.limitPledgeResidue"  readonly="readonly"  class="w200"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>
                            </Row>
                        </div>

                        <div class="common_br">
                            <h2 class="common_channel_title" >新车授权区域/授权车型</h2>
                            <div class="common_content" style="padding-left: 16%;">
                                <Row>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="区域是否生效" prop="authRegionSwitch">
                                                <RadioGroup v-model="basicTypeForm.authRegionSwitch"   >
                                                    <Radio label="1">是</Radio>
                                                    <Radio label="0">否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>

                                        <Col span="12">
                                            <FormItem label="车型是否生效" prop="authVehicleTypeSwitch">
                                                <RadioGroup v-model="basicTypeForm.authVehicleTypeSwitch"    >
                                                    <Radio label="1">是</Radio>
                                                    <Radio label="0">否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>
                                    </Row>


                                    <Row>
                                        <Col span="12" style="padding-left: 180px;">
                                            <Input v-model="searchAreasDataKey" @on-search="searchMainArea" search
                                                   enter-button
                                                   class="w200"
                                                   placeholder="输入搜索"/>
                                        </Col>

                                        <Col span="12" style="padding-left: 180px;">
                                            <Input v-model="searchCarsDataKey" @on-search="searchCarsData" search
                                                   enter-button
                                                   class="w200"
                                                   placeholder="输入搜索"/>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="" prop="mainArea">
                                                <div style="height:200px;margin-top: 10px;overflow-y:auto">
                                                    <SelectTree ref="mainArea" v-model='basicTypeForm.mainArea'
                                                                :data='areasData'
                                                                :isInit='isAreasDataInit'
                                                                :isSearch="isAreasDataSearch"
                                                                :searchKey="searchAreasDataKey"
                                                                :isDisableNewCarForm="isDisableNewCarForm"
                                                                @closeSearch="closeSearchNewCar" :dataKeys="dataKeys"
                                                                :setting="setting"/>
                                                </div>
                                            </FormItem>
                                        </Col>

                                        <Col span="12">
                                            <FormItem label="" prop="mainCar">
                                                <div style="height:200px;margin-top: 10px;overflow-y:auto">
                                                    <SelectTree ref="mainCar" v-model='basicTypeForm.mainCar'
                                                                :data='carsData'
                                                                :isInit='isCarsDataInit'
                                                                :isSearch="isCarTreeSearch"
                                                                :isDisableNewCarForm="isDisableNewCarForm"
                                                                :searchKey="searchCarsDataKey"
                                                                @closeSearch="closeSearchNewCar"/>
                                                </div>
                                            </FormItem>
                                        </Col>
                                    </Row>



                                </Row>
                                <div>
                                    <a class="select-clear" @click="showAreasNewCar">选择渠道展业区域查看</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    </Card>
                </div>



                 <div v-if="this.showGroupOldCar=='1' && this.oldCarRole!='0'">
                    <Card>
                    <div class="common_br">
                        <h2 class="common_channel_title" >二手车优质信息</h2>
                        <div class="common_content" style="padding-left: 16%;">
                            <Row>
                                <Col span="12">
                                    <FormItem label="优质等级" prop="qualityGradeOld" >
                                        <Select v-model="basicTypeForm.qualityGradeOld" placeholder="优质等级" class="w200" @on-change="checkQualityGrade()">
                                            <Option :value="item.value" v-for="(item,index) in qualityGradeList" :key="index"> {{item.title}}</Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <Form-item label="有效期" prop="validDateOld" :rules="Number(basicTypeForm.qualityGradeOld) != 5?validDateOld:notValidDateOld">
                                        <DatePicker v-model="basicTypeForm.validDateOld" type="daterange" format="yyyy-MM-dd"  placeholder="选择起止时间"
                                                    style="width: 200px">
                                        </DatePicker>
                                    </Form-item>
                                </Col>
                            </Row>
                        </div>
                    </div>
                    <div class="common_br">
                        <h2 class="common_channel_title" >二手车风控信息</h2>
                        <div class="common_content" style="padding-left: 16%;">
                            <Row>
                                <Col span="12">
                                    <FormItem label="车辆类型" prop="carTypeOld">
                                        <Checkbox-group  v-model="basicTypeForm.carTypeOld">
                                            <!-- <Checkbox label="00" v-bind:disabled="this.carTypeSwitch0 == '0'">乘用车</Checkbox>
                                            <Checkbox label="01" v-bind:disabled="this.carTypeSwitch1 == '1'">LCV</Checkbox>
                                            <Checkbox label="02" v-bind:disabled="this.carTypeSwitch2 == '2'">中卡</Checkbox>
                                            <Checkbox label="03" v-bind:disabled="this.carTypeSwitch3 == '3'">重卡</Checkbox> -->
                                            <Checkbox label="1" v-bind:disabled="this.carTypeSwitch0 == '0'">商用车</Checkbox>
                                            <Checkbox label="2" v-bind:disabled="this.carTypeSwitch1 == '1'">乘用车</Checkbox>
                                            <Checkbox label="3" v-bind:disabled="this.carTypeSwitch2 == '2'">设备</Checkbox>
                                            <Checkbox label="4" v-bind:disabled="this.carTypeSwitch3 == '3'">工程机械</Checkbox>
                                        </Checkbox-group>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="城市经理" prop="customerManagerOld" v-if="!basicTypeForm.id || showSaleMans">
                                        <Select v-model="basicTypeForm.customerManagerOld" placeholder="城市经理" class="w200">
                                            <Option v-for="(item,index) in salesManDataOld" :value="item.username" :label="item.userRealName" :key="index" >
                                            </Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                            </Row>

                            <Row>
                                <Col span="12">
                                    <FormItem label="评级" prop="channelGradeOld" >
                                        <Select v-model="basicTypeForm.channelGradeOld" placeholder="评级" class="w200">
                                            <Option value="1">A</Option>
                                            <Option value="2">B</Option>
                                            <Option value="3">C</Option>
                                            <Option value="4">D</Option>
                                        </Select>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="保证金(元)" prop="channelDepositOld">
                                        <Input v-model.trim="basicTypeForm.channelDepositOld" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度判断 -->
                                <Col span="12">
                                    <FormItem label="是否控制额度" prop="isLimitPutOld">
                                        <RadioGroup v-model="basicTypeForm.isLimitPutOld" @on-change="checkLimitPut()">
                                            <Radio label="1">是</Radio>
                                            <Radio label="0">否</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度判断 -->
                                <Col span="12">
                                    <FormItem label="是否控制额度" prop="isLimitPledgeOld" >
                                        <RadioGroup v-model="basicTypeForm.isLimitPledgeOld" @on-change="checkLimitPut()">
                                            <Radio label="1">是</Radio>
                                            <Radio label="0">否</Radio>
                                        </RadioGroup>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <Col span="12">
                                    <FormItem label="签放额度(元)" prop="limitPutOld" :rules="Number(basicTypeForm.isLimitPutOld)?limitPutOld:notLimitPutOld">
                                        <Input v-model.trim="basicTypeForm.limitPutOld" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>

                                <Col span="12">
                                    <FormItem label="先放后抵额度(元)" prop="limitPledgeOld" :rules="Number(basicTypeForm.isLimitPledgeOld)?limitPledgeOld:notLimitPledgeOld">
                                        <Input v-model.trim="basicTypeForm.limitPledgeOld" class="w200"  placeholder="请输入"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度 -->
                                <Col span="12">
                                    <FormItem label="临时额度(元)" prop="temporaryLimitPutOld" >
                                        <Input v-model.trim="basicTypeForm.temporaryLimitPutOld" class="w200"  placeholder="请输入" @on-change="checkdate()"/>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度 -->
                                <Col span="12">
                                    <FormItem label="临时额度(元)" prop="temporaryLimitPledgeOld"  >
                                        <Input v-model.trim="basicTypeForm.temporaryLimitPledgeOld" class="w200"  placeholder="请输入" @on-change="checkdate()"/>
                                    </FormItem>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度有效期 -->
                                <Col span="12">
                                    <Form-item label="有效期" prop="limitPutTimeOld"  :rules="Number(oldCarDate1) != 1?notNewCarLimitPutTime:newCarLimitPutTime">
                                        <Date-picker type="date" v-model="basicTypeForm.limitPutTimeOld" class="w200"  placeholder="选择日期" ></Date-picker>
                                    </Form-item>
                                </Col>

                                <!-- 先放后抵额度有效期 -->
                                <Col span="12">
                                    <Form-item label="有效期" prop="limitPledgeTimeOld"  :rules="Number(oldCarDate2) != 1?notNewCarLimitPutTime:newCarLimitPutTime">
                                        <Date-picker type="date" v-model="basicTypeForm.limitPledgeTimeOld" class="w200"  placeholder="选择日期" ></Date-picker>
                                    </Form-item>
                                </Col>
                            </Row>

                            <Row>
                                <!-- 签放额度占用 -->
                                <Col span="12">
                                    <FormItem label="占用额度(元)" prop="limitPutTakeOld">
                                        <Input v-model.trim="basicTypeForm.limitPutTakeOld" class="w200"  readonly="readonly"   placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                                <!-- 先放后抵额度占用 -->
                                <Col span="12">
                                    <FormItem label="占用额度(元)" prop="limitPledgeTakeOld">
                                        <Input v-model.trim="basicTypeForm.limitPledgeTakeOld" class="w200"  readonly="readonly"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                            </Row>

                            <Row>
                                <!-- 剩余签放额度 -->
                                <Col span="12">
                                    <FormItem label="剩余额度(元)" prop="limitPutResidueOld">
                                        <Input v-model.trim="basicTypeForm.limitPutResidueOld" class="w200"  readonly="readonly"   placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>

                                <!-- 剩余先放后抵额度 -->
                                <Col span="12">
                                    <FormItem label="剩余额度(元)" prop="limitPledgeResidueOld">
                                        <Input v-model.trim="basicTypeForm.limitPledgeResidueOld" class="w200"   readonly="readonly"  placeholder="系统自动计算"/>
                                    </FormItem>
                                </Col>
                            </Row>
                        </div>

                        <div class="common_br">
                            <h2 class="common_channel_title" >二手车授权区域/授权车型</h2>
                            <div class="common_content" style="padding-left: 16%;">
                                <Row>
                                    <Row>
                                        <Col span="12">
                                            <FormItem label="区域是否生效" prop="authRegionSwitchOld">
                                                <RadioGroup v-model="basicTypeForm.authRegionSwitchOld" >
                                                    <Radio label="1">是</Radio>
                                                    <Radio label="0">否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>

                                        <Col span="12">
                                            <FormItem label="车型是否生效" prop="authVehicleTypeSwitchOld">
                                                <RadioGroup v-model="basicTypeForm.authVehicleTypeSwitchOld" >
                                                    <Radio label="1">是</Radio>
                                                    <Radio label="0">否</Radio>
                                                </RadioGroup>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col span="12" style="padding-left: 180px;">
                                            <Input v-model="searchAreaOldKey" @on-search="searchAreasOld" search
                                                   enter-button
                                                   class="w200"

                                                   placeholder="输入搜索"/>
                                        </Col>

                                        <Col span="12" style="padding-left: 180px;">
                                            <Input v-model="searchCarOldKey" @on-search="searchMainCarOld" search
                                                   enter-button
                                                   class="w200"

                                                   placeholder="输入搜索"/>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col span="12">
                                            <FormItem label="" prop="mainAreaOld" >
                                                <div style="height:200px;margin-top: 10px; overflow-y:auto">
                                                    <!-- <Tree :data="areasDataOld" ref="treeOld" show-checkbox v-model="basicTypeForm.mainAreaOld" > </Tree>-->
                                                    <SelectTree ref="mainAreaOld" v-model='basicTypeForm.mainAreaOld'
                                                                :data='areasDataOld'
                                                                :isInit='isAreasDataOldInit'
                                                                :isSearch="isAreasDataOldSearch"
                                                                :searchKey="searchAreaOldKey"
                                                                :isDisableOldCarForm="isDisableOldCarForm"
                                                                @closeSearch="closeSearch" :dataKeys="dataKeys"
                                                                :setting="setting"/>
                                                </div>
                                            </FormItem>
                                        </Col>

                                        <Col span="12">
                                            <FormItem label="" prop="mainCarOld" >
                                                <div style="height:200px;margin-top: 10px;overflow-y:auto">
                                                    <SelectTree ref="mainCarOld" v-model='basicTypeForm.mainCarOld'
                                                                :data='carsDataOld'
                                                                :isInit='isCarsDataOldInit'
                                                                :isSearch="isCarsDataOldSearch"
                                                                :searchKey="searchCarOldKey"
                                                                :isDisableOldCarForm="isDisableOldCarForm"
                                                                @closeSearch="closeSearch"/>
                                                </div>
                                            </FormItem>
                                        </Col>
                                    </Row>

                                </Row>
                                <div>
                                    <a class="select-clear" @click="showAreasOldCar">选择渠道展业区域查看</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    </Card>
                </div>


            </Form>
        </div>
        <Modal :title="modalTitle" v-model="blackApplyVisible" :mask-closable='false' :width="500" :styles="{top: '100px'}">
            <Form ref="blackApplyForm" :model="blackApplyForm" :label-width="70" :rules="blackApplyFormValidate">
                <FormItem label="申请原因" prop="blacklistReason">
                    <Input v-model.trim="blackApplyForm.blacklistReason" class="w400"  placeholder="请输入黑名单申请原因" type="textarea" :rows="6"/>
                </FormItem>
            </Form>
            <div slot="footer" style="text-align:center;" >
                <Button type="text" @click="cancel">取消</Button>
                <Button type="primary" @click="join">提交</Button>
            </div>
        </Modal>

        <Modal :title="areaTitle" v-model="areaVisible" :mask-closable='false' :width="1000" :styles="{top: '300px'}">
            <Row>
                <Table :loading="loading" border :columns="columns" :data="dataArea" sortable="custom"
                       @on-sort-change="changeSort" @on-selection-change="showSelect" ref="table">
                </Table>
            </Row>
            <Row type="flex" justify="end" class="page">
                <Page :current="searchForm.pageNumber" :total="totalArea" :page-size="searchForm.pageSize"
                      @on-change="changePage" @on-page-size-change="changePageSize" :page-size-opts="[10,20,50]"
                      size="small" show-total show-elevator show-sizer></Page>
            </Row>
        </Modal>
        <MapModal :mapModal="mapModal" :mapLocation="mapLocation" :mapAddress="mapAddress" @confirmLngLat="confirmLngLat" @closeMapModel="closeMapModel" />
    </div>
</template>

<script>
    import {formatDate} from "_c/iview/components/date-picker/util";
    import MapModal from '_p/afs-channel/pages/common/map/MapModal';
    import vueEvent from "_p/basic/assets/js/vueEvent.js"
    import PlaceCascade from "@/components/place-cascade";
    import SelectTree from '_p/afs-channel/pages/dealer-apply/dealer-online/basic-info/component/selectTree'
    import {searchEnterpriseName} from "_p/afs-channel/api/search-enterprise-name/searchEnterpriseName";
    import Source from "_p/afs-channel/api/search-enterprise-name/canale";
    import {
        //保存直营车商
        addChannel,
        //更新直营车商
        updateChannel,
        //保存黑名单
        saveBlack,
        //黑名单检查
        blackCleack,
        //获取所有的直营合作商
        getAllDirectChannels,
        //同步车商信息到案件服务
        saveDirectCarDealerDataToCase,
        //暂存
        stagingSaveDirectCarDealer,
        //获取直营车商展业区域
        getDirectCardealerAreaListData,
        //根据车商id获取收款账户信息
        getAccountInfoByChannelId,
        getAllSaleManByChannelId,
        checkCooper,
        getAreaByChannelId,

        //获取渠道风控信息
        getDirectCarDealerRiskInfoById,
        //获取渠道保证金信息
        getChannelQuotaInfoById,
        //查询授权区域信息
        getDirectCarDealerMainAreaById,
        //授权车型
        getNewVehicleById,
        getOldVehicleById,
        //查询合作商剩余额度
        getSurplusChannelQuotaInfo,


    } from "@/projects/afs-channel/api/car-dealer/dealer";
    import {
        //获取渠道风控信息
        getChannelRiskInfoById,
        //根据渠道代码查询渠道信息
        getChannelBasicInfoById,
        //多条件查询渠道基本信息
        getChannelListData,
        //查询主营品牌信息
        getCarBrandById,
        //提交审批
        channelSubmit,
        //查询授权区域信息
        getMainAreaById,
    } from  "@/projects/afs-channel/api/dealer/dealer";

    import {
        getDictDataByType
    } from "_p/basic/api/admin/datadic.js";

    import {
        getAllAddressList,
        getAllCarList,
        getLocation,
        getBrandTree,
        syncFileToApply
    } from "_p/afs-channel/api/common/common.js"

    import {deepClone} from "@/libs/utils/ObjectClone";
    export default {
        name:"direct_cardealer_basic_info",
        mixins: [Source],
        data() {
            let validate = function(rule, value, callback){
                if(!value){
                    return callback("日期不能为空");
                }
                if(Array.isArray(value)){//格式为：daterange、datetimerange检测
                    if(value.length==0){
                        return callback("日期不能为空");
                    }
                    value.map(function(item){
                        console.log(item,"--------")
                        if(item === '' ||item == undefined){
                            return callback("日期不能为空");
                        }
                    })
                }else{ //格式为：date、datetime、year、month 检测
                    if(value === ''){
                        return callback("日期不能为空")
                    }
                }
                return callback()
            };

            let notValidate = function (rule, value, callback) {
                if (Array.isArray(value)) {//格式为：daterange、datetimerange检测
                    value.map(function (item) {
                        if (item === "") {
                            //return callback("日期不能为空")
                        }
                    })
                } else { //格式为：date、datetime、year、month 检测
                    if (value === '') {
                        //return callback("日期不能为空")
                    }
                }
                return callback()
            };
            const validatesocUniCrtCode = (rule, value, callback) => {
                var reg = /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/;
                if (!reg.test(value)) {
                    callback(new Error("统一社会信用码格式错误"));
                } else {
                    callback();
                }
            };
            const validatePassword  = (rule, code, callback) => {
                var city = {
                    11: "北京",
                    12: "天津",
                    13: "河北",
                    14: "山西",
                    15: "内蒙古",
                    21: "辽宁",
                    22: "吉林",
                    23: "黑龙江 ",
                    31: "上海",
                    32: "江苏",
                    33: "浙江",
                    34: "安徽",
                    35: "福建",
                    36: "江西",
                    37: "山东",
                    41: "河南",
                    42: "湖北 ",
                    43: "湖南",
                    44: "广东",
                    45: "广西",
                    46: "海南",
                    50: "重庆",
                    51: "四川",
                    52: "贵州",
                    53: "云南",
                    54: "西藏 ",
                    61: "陕西",
                    62: "甘肃",
                    63: "青海",
                    64: "宁夏",
                    65: "新疆",
                    71: "台湾",
                    81: "香港",
                    82: "澳门",
                    91: "国外 "
                };
                var tip = ""
                var pass = true

                if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
                    tip = "身份证号格式错误"
                    pass = false;
                } else if (!city[code.substr(0, 2)]) {
                    tip = "身份证号格式错误"
                    pass = false
                } else {
                    // 18位身份证需要验证最后一位校验位
                    if (code.length === 18) {
                        code = code.split('')
                        // ∑(ai×Wi)(mod 11)
                        // 加权因子
                        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
                        // 校验位
                        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
                        var sum = 0
                        var ai = 0
                        var wi = 0
                        for (var i = 0; i < 17; i++) {
                            ai = code[i]
                            wi = factor[i]
                            sum += ai * wi
                        }
                        var last = parity[sum % 11];
                        if (parity[sum % 11] != code[17]) {
                            tip = "身份证号格式错误"
                            pass = false
                        }
                    }
                }
                if (!pass) {
                    callback(new Error(tip))
                } else {
                    callback()
                }
                // if (!pass) alert(tip)
                // return pass
            };


            const validateMobile = (rule, value, callback) => {
                let regPone = null;
                let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/; //最新16手机正则
                let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/; //座机
                if (value.charAt(0) == 0) {    // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
                    regPone = tel;
                } else {
                    regPone = mobile;
                }
                if (!regPone.test(value)) {
                    return callback(
                        new Error("联系电话格式有误(座机格式'区号-座机号码')")
                    );
                }
                callback();
            };
            const validateAmount = (rule, value, callback) => {
                var reg = /^\d+(\.\d+)?$/;
                if (!reg.test(value) && value != "") {
                    callback(new Error("数值类型格式错误"));
                } else{
                    callback();
                }
            };
            return{
                source: null,
                searchName:"",
                searchModelList:[],
                loadModel:false,
                //主营品牌搜索
                searchKey: "",
                isSearch: false,

                //授权区域,授权车型搜索
                searchCarsDataKey: '',
                isCarTreeSearch: false,
                searchAreasDataKey: '',
                isAreasDataSearch: false,
                dataKeys: ["addrLevel", "expand", "delFlag", "id", "label", "postCode", "spellCode", "title", "upperCode", "value"],
                setting: {
                    check: {enable: true, chkDisabledInherit: true},
                    data: {
                        key: {name: "title"},
                        simpleData: {enable: true, pIdKey: "upperCode", idKey: 'value'}
                    },
                    view: {showIcon: false,}
                },

                searchCarOldKey: '',
                isCarsDataOldSearch: false,

                searchAreaOldKey: '',
                isAreasDataOldSearch: false,


                businessTypeControlValue:"",
                disableBusinessType:false,
                newCarRole:"1",
                oldCarRole:"1",
                //有效期表单验证
                newCarDate1:"",
                newCarDate2:"",
                oldCarDate1:"",
                oldCarDate2:"",

                inBlackList: true,

                showGroupNewCar:"",
                showGroupOldCar:"",
                showOldCar:"",
                showNewCar:"",
                //是否禁用新车按钮：默认否
                viewNewCar:"0",
                //是否禁用二手车按钮：默认否
                viewOldCar:"0",
                isNewCarRole:"",
                isOldCarRole:"",
                //控制是否更新
                updateswitch:false,
                //控制经纬度范围是否显示
                showRangeSwitch:false,
                //默认是非管理员账号,后面也要改
                isEmployee:false,
                associAccount:"",
               // typeId:"",
                //汇款对象可以选择开关
                accountSwitch:"1",

                //车商状态默认为草稿
                channelStatus:"",
                //二手车状态
                channelStatusOldCar:"",
                channelStatusNewCar:"",

                loading:false,
                submitLoading: false,
                disabledPaymentObject:"0",
                readOnly:false,
                isShowModel:false,
                expandLevel: 0,
                carLevel: 0,
                //黑名单
                modalTitle: "",
                //展业区域
                areaTitle:"",
                //黑明单申请弹窗
                blackApplyVisible: false,
                //展业区域弹窗
                areaVisible:false,
                typeCode:1,
                typeCode2:0,
                mainCar:[],
                mainArea:[],
                //渠道归属数据字典
                channelBelongList:[],
                //汇款对象
                paymentObjectList:[],
                location:[],
                //gps厂商字典
                gpsvendorauthorList: [],
                //Gps安装方式字典
                gpsinstalmodeList: [
                ],
                // 运营公司
                companyList:[
                    {
                        value:"00",
                        label:"内部车型库",
                    },
                    {
                        value:"01",
                        label:"外部车型库",
                    },
                    {
                        value:"02",
                        label:"厂商认定",
                    },
                    {
                        value:"02",
                        label:"第三方评估",
                    }
                ],
                searchForm: {
                    pageNumber: 1,
                    pageSize: 10,
                    areaId:[],
                    businessType:"",
                },
                columns: [
                    {
                        type: 'index',
                        title:"序号",
                        minWidth: 80,
                        align: "center",
                        fixed: "left"

                    },
                    {
                        title: "直营车商名称",
                        key: "channelFullName",
                        minWidth: 120,
                    },
                    {
                        title: "地区",
                        key: "areaTitle",
                        minWidth: 145
                    },
                ],
                dataArea: [],
                totalArea: 0,
                //所有渠道信息
                channelData:[],

                //旧的状态
                OldStatus:"",
                newCar:true,
                oldCar:true,
                //开票信息
                invoiceForm:{
                    id:"",
                    taxpayerType:"",
                    taxpayerIdNumber:"",
                    invoiceTelNumber:"",
                    invoiceAddress:"",
                    openingBank:"",
                    bankAccount:"",
                    channelStatus:"",
                    taxRate:"",
                },

                //合作商下所有业务员集合
                salesManData:[],
                salesManDataOld:[],

                mapModal: false, // 地图弹窗
                mapLocation:'', // 地图定位
                mapAddress:'', // 详细地址
                canOpenMapModal: false, // 是否可以打开地图弹窗

                //优质等级字典处理
                qualityGradeList:[],
                saleMan:"",
                saleManOld:"",
                businessTypeSwitch0:"-1",
                businessTypeSwitch1:"-1",
                businessTypeSwitch2:"-1",
                businessTypeSwitch3:"-1",

                carTypeSwitch0:"-1",
                carTypeSwitch1:"-1",
                carTypeSwitch2:"-1",
                carTypeSwitch3:"-1",

                showSaleMans: false,
                isEnable: "",
                basicTypeForm:{
                    oldChannelId:"",
                    channelId:"",
                    id:"",
                    //车商类型 4s 汽贸
                    cardealType:"",
                    //账户信息
                    accountInfo:"1",
                    mainBrand:[],
                    channelAddressDetail:"",

                    //admin地址省市区
                    channelAdminAddressValue:[],
                    //注册地址省市区
                    channelAddressValue:[],
                    //办公地址省市区
                    officeAddressValue:[],
                    pageNumber: 1,
                    pageSize: 10,
                    //渠道名称
                    channelFullName:"",
                    //上线日期
                    onlineDate:"",
                    //统一社会信用代码
                    socUniCrtCode:"",
                    //法人
                    legalPerson:"",
                    //法人身份证号
                    legalPersonIdCard:"",
                    //法人电话
                    legalPersonTel:"",
                    //实际控制人
                    actualController:"",
                    //实际控制人电话
                    actualControllerTel:"",
                    //实际控制人身份证号
                    actualControllerIdCard:"",
                    //管理员姓名
                    channelAdmin:"",
                    //管理员电话
                    channelAdminTel:"",
                    //管理员证件号码
                    channelAdminIdCard:"",
                    //管理员邮箱
                    channelAdminMail:"",
                    //地址经纬度
                    longitude:"",
                    latitude:"",
                    //经纬度范围（km）
                    longitudeLatitudeRange:"",
                    //公司人数
                    companiesNumber:1,
                    //经营场所面积
                    businessArea:"",
                    //汇款对象
                    paymentObject:"",
                    //注册日期
                    registrationDate:"",
                    //注册资金(万元)
                    registeredCapital:"",
                    //实收资本(万元)
                    subscribedCapital:"",
                    //业务类型
                    businessType:[],
                    //资产净值
                    netAssetValue:"",
                    //经营年限
                    businessLife:"",
                    //Gps厂商授权
                    gpsVendorAuthor:[],
                    //资产负债率
                    assetLiabilityRatio:"",
                    //上一年度经营利润
                    operatingProfit:"",
                    //Gps安装方式
                    gpsInstalMode:"",
                    //业绩
                    achievement:"",
                    //利润率增长率
                    proGrowthRate:"",
                    //销售收入增长率
                    saleIncreaseRate:"",
                    //流动比例
                    currentRatio:"",
                    //优质等级
                    qualityGrade:"",
                    //二手车优质等级
                    qualityGradeOld:"",
                    //有效期
                    validDate:[],
                    //二手车有效期
                    validDateOld:[],
                    //车辆类型
                    carType:[],
                    //二手车车辆类型
                    carTypeOld:[],
                    //区域经理
                    customerManager:"",
                    //二手车区域经理
                    customerManagerOld:"",
                    //二手车账号最大数量
                    accountMaxNumOld:"",
                    //新车账号最大数量
                    accountMaxNum:"",
                    //层级最大数量
                    hierarchy:1,
                    //评级
                    channelGrade:"",
                    //二手车评级
                    channelGradeOld:"",
                    //保证金
                    channelDeposit:"",
                    //二手车保证金
                    channelDepositOld:"",
                    //是否控制额度
                    isLimitPut:"",
                    //二手车是否控制额度
                    isLimitPutOld:"",
                    //是否控制额度
                    isLimitPledge:"",
                    //二手车是否控制额度
                    isLimitPledgeOld:"",
                    //签放额度
                    limitPut:"",
                    //签放额度
                    limitPutOld:"",
                    //先放后抵额度
                    limitPledge:"",
                    //二手车先放后抵额度
                    limitPledgeOld:"",
                    //签放额度临时额度
                    temporaryLimitPut:"",
                    //二手车签放额度临时额度
                    temporaryLimitPutOld:"",
                    //先放后抵额度临时额度
                    temporaryLimitPledge:"",
                    //二手车先放后抵额度临时额度
                    temporaryLimitPledgeOld:"",
                    //签放额度有效期
                    limitPutTime:"",
                    //二手车签放额度有效期
                    limitPutTimeOld:"",
                    //先放后抵额度有效期
                    limitPledgeTime:"",
                    //二手车先放后抵额度有效期
                    limitPledgeTimeOld:"",
                    //签放额度占用
                    limitPutTake:"",
                    //二手车签放额度占用
                    limitPutTakeOld:"",
                    //先放后抵额度占用
                    limitPledgeTake:"",
                    //二手车先放后抵额度占用
                    limitPledgeTakeOld:"",
                    //剩余签放额度
                    limitPutResidue:"",
                    //二手车剩余签放额度
                    limitPutResidueOld:"",
                    //剩余先放后抵额度
                    limitPledgeResidue:"",
                    //二手车剩余先放后抵额度
                    limitPledgeResidueOld:"",
                    //区域是否生效
                    authRegionSwitch:"",
                    //二手车区域是否生效
                    authRegionSwitchOld:"",
                    //车型是否生效
                    authVehicleTypeSwitch:"",
                    //二手车是否生效
                    authVehicleTypeSwitchOld:"",
                    //新车经纬度是否控制
                    longitudeLatitudeSwitch:"",
                    //二手车经纬度是否控制
                    longitudeLatitudeSwitchOld:"",
                    //业务人员关联车商
                    personRelCardealerSwitch:"",
                    //进件选择车商
                    choiceCardealerSwitch:"",
                },

                //渠道的授权区域
                channelAreas:"",
                channelAreasOld:"",
                basicTypeFormValidate:{
                    channelId: [
                        { required: true, message: "合作商名称不能为空", trigger: "change"}
                    ],

                    channelFullName: [
                        { required: true, message: "车商名称不能为空", trigger: "change"}
                    ],
                    onlineDate: [
                        { required: true, validator: validate, trigger: "change"}
                    ],
                   /* onlineDate: [
                        { required: true, message: "上线日期不能为空", trigger: "blur",type:"date"}
                    ],*/
                    socUniCrtCode: [
                        { required: true, message: "统一社会信用代码不能为空", trigger: "blur"},
                        {validator: validatesocUniCrtCode, trigger: "blur"}
                    ],
                    legalPerson:[
                        { required: true, message: "法人不能为空", trigger: "blur"}
                    ],
                    legalPersonIdCard:[
                        { required: true, message: "法人身份证号不能为空", trigger: "blur"},
                        { validator: validatePassword, trigger: "blur" }
                    ],
                    legalPersonTel:[
                        { required: true, message: "法人电话不能为空", trigger: "blur"},
                        {validator: validateMobile, trigger: "blur"}
                    ],
                    actualController:[
                        { required: true, message: "实际控制人不能为空", trigger: "blur"}
                    ],
                    actualControllerTel:[
                        { required: true, message: "实际控制人电话不能为空", trigger: "blur"},
                        {validator: validateMobile, trigger: "blur"}
                    ],
                    actualControllerIdCard:[
                        { required: true, message: "实际控制人身份证号不能为空", trigger: "blur"},
                        { validator: validatePassword, trigger: "blur" }
                    ],

                    longitude:[
                        { required: true, message: "地址经度不能为空", trigger: "change"}
                    ],
                    latitude:[
                        { required: true, message: "地址纬度不能为空", trigger: "change"}
                    ],
                    companiesNumber:[
                        { required: true,type: 'number',message: "公司人数", trigger: "blur"}
                    ],
                    accountMaxNum:[
                        { required: true,type: 'number',message: "账号最大数量", trigger: "blur"}
                    ],
                    hierarchy:[
                        { required: true,type: 'number',message: "层级最大数量", trigger: "blur"}
                    ],
                    longitudeLatitudeRange:[
                        { required: true, message: "经纬度范围不能为空", trigger: "blur"}
                    ],
                    businessArea:[
                        { required: true, message: "经营场所面积不能为空", trigger: "blur"}
                    ],
                    paymentObject:[
                        { required: true, message: "汇款对象不能为空", trigger: "change"}
                    ],
                    registrationDate:[
                        { required: true, validator: validate, trigger: "change"}
                    ],
                    registeredCapital:[
                        { required: true, message: "注册资金不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],

                    channelAddressValue:[
                        { required: true, message: "注册地址不能为空", trigger: "blur",type:"array"}
                    ],
                    channelAddressDetail:[
                        { required: true, message: "详细地址不能为空", trigger: "blur"}
                    ],
                    /*subscribedCapital:[
                        { required: true, message: "实收资本不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    businessType: [
                        { required: true, type: 'array', min: 1, message: '业务类型不能为空', trigger: 'blur' },
                    ],
                    /*netAssetValue:[
                        { required: true, message: "资产净值不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    businessLife:[
                        { required: true, message: "经营年限不能为空", trigger: "blur"}
                    ],
                    gpsVendorAuthor:[
                        { required: true, type: 'array', min: 1, message: 'Gps厂商授权不能为空', trigger: 'change' },
                    ],
                    /*assetLiabilityRatio:[
                        { required: true ,message: "资产负债率不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    /*operatingProfit:[
                        { required: true, message: "上一年度经营利润不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    gpsInstalMode:[
                        { required: true, message: 'Gps安装方式不能为空', trigger: 'blur' },
                    ],
                    /*achievement:[
                        { required: true, message: "业绩不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    /*proGrowthRate:[
                        { required: true, message: "利润率增长率不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    /*saleIncreaseRate:[
                        { required: true, message: "销售收入增长率不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    currentRatio:[
                        { required: true, message: "流动比例不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    qualityGrade:[
                        { required: true, message: "优质等级不能为空", trigger: "change"}
                    ],
                    qualityGradeOld:[
                        { required: true, message: "优质等级不能为空", trigger: "change"}
                    ],
                    /*validDate:[
                        { required: true, validator: validate, trigger: "change"}
                    ],
                    validDateOld:[
                        { required: true, validator: validate, trigger: "change"}
                    ],*/
                    carType:[
                        { required: true, type: 'array', message: '车辆类型不能为空', trigger: 'change' },
                    ],
                    carTypeOld:[
                        { required: true, type: 'array', message: '车辆类型不能为空', trigger: 'change' },
                    ],
                    customerManager:[
                        { required: true, message: "城市经理不能为空", trigger: "change"}
                    ],
                    customerManagerOld:[
                        { required: true, message: "城市经理不能为空", trigger: "change"}
                    ],
                    channelGrade:[
                        { required: true, message: "评级不能为空", trigger: "change"}
                    ],
                    channelGradeOld:[
                        { required: true, message: "评级不能为空", trigger: "change"}
                    ],
                    channelDeposit:[
                      /*  { required: true, message: "保证金不能为空", trigger: "blur" },*/
                        {validator: validateAmount, trigger: "change"}
                    ],
                    channelDepositOld:[
                      //  { required: true, message: "保证金不能为空", trigger: "blur" },
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    isLimitPut:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPutOld:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPledge:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],
                    isLimitPledgeOld:[
                        { required: true, message: "是否控制额度不能为空", trigger: "change"}
                    ],

                    temporaryLimitPut:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPutOld:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPledge:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPledgeOld:[
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    /*temporaryLimitPut:[
                        { required: true, message: "临时额度不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],
                    temporaryLimitPledge:[
                        { required: true, message: "临时额度不能为空", trigger: "blur"},
                        {validator: validateAmount, trigger: "blur"}
                    ],*/
                    limitPutTime:[
                    ],
                    limitPledgeTime:[
                      //  { required: true, validator: validate, trigger: "change"}
                    ],

                    limitPutTimeOld:[
                      //  { required: true, validator: validate, trigger: "change"}
                    ],
                    limitPledgeTimeOld:[
                       // { required: true, validator: validate, trigger: "change"}
                    ],

                    authRegionSwitch:[
                        { required: true, message: "区域是否生效不能为空", trigger: "change"}
                    ],
                    authRegionSwitchOld:[
                        { required: true, message: "区域是否生效不能为空", trigger: "change"}
                    ],
                    authVehicleTypeSwitch:[
                        { required: true, message: "车型是否生效不能为空", trigger: "change"}
                    ],
                    authVehicleTypeSwitchOld:[
                        { required: true, message: "车型是否生效不能为空", trigger: "change"}
                    ],
                    longitudeLatitudeSwitch:[
                        { required: true, message: "新车经纬度是否控制不能为空", trigger: "change"}
                    ],
                    longitudeLatitudeSwitchOld:[
                        { required: true, message: "二手车经纬度是否控制不能为空", trigger: "change"}
                    ],
                    accountInfo:[
                        { required: true, message: "账户信息不能为空", trigger: "change"}
                    ],
                    cardealType:[
                        { required: true, message: "车商类型不能为空", trigger: "change"}
                    ],
                    personRelCardealerSwitch:[
                        { required: true, message: "业务人员关联车不能为空", trigger: "change"}
                    ],
                    choiceCardealerSwitch:[
                        { required: true, message: "进件选择车商不能为空", trigger: "change"}
                    ],
                },
                limitPut:[
                    { required: true, message: "签放额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPut:[
                    {validator: validateAmount, trigger: "blur"},
                    //{validator: validateAmount, trigger: "blur"}
                ],
                limitPutOld:[
                    { required: true, message: "签放额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPutOld:[
                    {validator: validateAmount, trigger: "blur"}
                ],
                limitPledge:[
                    { required: true, message: "先放后抵额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPledge:[
                    {validator: validateAmount, trigger: "blur"}
                ],
                limitPledgeOld:[
                    { required: true, message: "先放后抵额度不能为空", trigger: "blur"},
                    {validator: validateAmount, trigger: "blur"}
                ],
                notLimitPledgeOld:[
                    {validator: validateAmount, trigger: "blur"}
                ],

                validDate:[
                    { required: true, validator: validate, trigger: "change"}
                ],
                notValidDate:[
                    { required: false, validator: notValidate, trigger: "change"}
                ],
                validDateOld:[
                    { required: true, validator: validate, trigger: "change"}
                ],
                notValidDateOld:[
                    { required: false, validator: notValidate, trigger: "change"}
                ],

                notNewCarLimitPutTime:[
                    { required: false, validator: notValidate,trigger: "change"}
                ],
                newCarLimitPutTime:[
                    { required: true, validator: validate, trigger: "change"}
                ],

                //黑名单
                blackApplyForm:{
                    blacklistReason:"",

                },
                //黑名单
                blackApplyFormValidate:{
                },
                baseData: [],

                showGroupNewCar:"0",//默认隐藏新车
                showGroupOldCar:"0",//默认隐藏二手车
                showOldCar:"0",//默认隐藏二手车
                showNewCar:"0",//默认隐藏二手车
            }
        },
        components:{
            PlaceCascade,
            MapModal,
            SelectTree
        },
        props: {
            channelType: {
                type: String,
            },
            isInit: {
                type: Boolean,
                default: false,
                required: true
            },
            treeData: {
                type: Array,
                default: null,
            },

            carsData: {
                type: Array,
                default: () => [],
            },
            isCarsDataInit: {
                type: Boolean,
                default: false,
            },
            areasData: {
                type: Array,
                default: () => [],
            },
            isAreasDataInit: {
                type: Boolean,
                default: false,
            },

            carsDataOld: {
                type: Array,
                default: () => [],
            },
            areasDataOld: {
                type: Array,
                default: () => [],
            },
            isCarsDataOldInit: {
                type: Boolean,
                default: false,
            },
            isAreasDataOldInit: {
                type: Boolean,
                default: false,
            },
            isDisableNewCarForm: {
                type: Boolean,
            },
            isDisableOldCarForm: {
                type: Boolean,
            },

        },
        watch:{
        },
        methods:{
            init() {
                this.getAllChannelInfo();
                this.initDataDic();
                this.getLocationData();
                console.log("合作商id" + this.basicTypeForm.channelId)
            },

            //查所有渠道
            getAllChannelInfo(){
                console.log("查所有渠道")
                getAllDirectChannels().then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.channelData= res.data;
                    }
                })
            },

            getChannelInfo(v,associatedChannelId) {
                //根据id查询直营车商基本信息
                this.loading = true;
                getChannelBasicInfoById(v).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.basicTypeForm.id=res.data[0].id;
                        this.basicTypeForm.channelCode=res.data[0].channelCode;
                        this.basicTypeForm.channelFullName=res.data[0].channelFullName;
                        this.basicTypeForm.onlineDate=res.data[0].onlineDate;
                        this.basicTypeForm.socUniCrtCode=res.data[0].socUniCrtCode;
                        this.basicTypeForm.legalPerson=res.data[0].legalPerson;
                        this.basicTypeForm.legalPersonIdCard=res.data[0].legalPersonIdCard;
                        this.basicTypeForm.legalPersonTel=res.data[0].legalPersonTel;
                        this.basicTypeForm.cardealType=res.data[0].carDealType;
                        this.basicTypeForm.accountInfo=res.data[0].accountInfo;
                        this.basicTypeForm.actualController=res.data[0].actualController;
                        this.basicTypeForm.actualControllerTel=res.data[0].actualControllerTel;
                        this.basicTypeForm.actualControllerIdCard=res.data[0].actualControllerIdCard;
                        this.basicTypeForm.channelAdmin=res.data[0].channelAdmin;
                        this.basicTypeForm.channelAdminTel=res.data[0].channelAdminTel;
                        this.basicTypeForm.channelAdminMail=res.data[0].channelAdminMail;
                        this.basicTypeForm.longitude=res.data[0].longitude;
                        this.basicTypeForm.latitude=res.data[0].latitude;
                        this.basicTypeForm.longitudeLatitudeRange=res.data[0].longitudeLatitudeRange;
                        this.basicTypeForm.businessArea=res.data[0].businessArea;
                        this.basicTypeForm.paymentObject=res.data[0].paymentObject;
                        this.basicTypeForm.registrationDate=res.data[0].registrationDate;
                        this.basicTypeForm.channelBelong=res.data[0].channelBelong;
                        this.basicTypeForm.hierarchy=res.data[0].hierarchy;
                        this.basicTypeForm.channelAdminIdCard =res.data[0].channelAdminIdCard;
                        this.basicTypeForm.companiesNumber =res.data[0].companiesNumber;
                        this.OldStatus =res.data[0].channelStatus;

                        if(res.data[0].channelStatus == "02" || res.data[0].channelStatusOldCar=="02"){
                            this.showSaleMans = true;
                        }

                        if(res.data[0].registeredCapital || res.data[0].registeredCapital== 0 ){
                            this.basicTypeForm.registeredCapital=res.data[0].registeredCapital+"";
                        }
                        if(res.data[0].assetLiabilityRatio || res.data[0].assetLiabilityRatio==0){
                            this.basicTypeForm.assetLiabilityRatio=res.data[0].assetLiabilityRatio+"";
                        }
                        if(res.data[0].businessLife || res.data[0].businessLife==0){
                            this.basicTypeForm.businessLife=res.data[0].businessLife+"";
                        }
                        if(res.data[0].operatingProfit||res.data[0].operatingProfit==0){
                            this.basicTypeForm.operatingProfit=res.data[0].operatingProfit+"";
                        }
                        if(res.data[0].achievement||res.data[0].achievement==0){
                            this.basicTypeForm.achievement=res.data[0].achievement+"";
                        }
                        if(res.data[0].subscribedCapital||res.data[0].subscribedCapital==0){
                            this.basicTypeForm.subscribedCapital=res.data[0].subscribedCapital+"";
                        }
                        if(res.data[0].netAssetValue||res.data[0].netAssetValue==0){
                            this.basicTypeForm.netAssetValue=res.data[0].netAssetValue+"";
                        }
                        if(res.data[0].proGrowthRate||res.data[0].proGrowthRate==0){
                            this.basicTypeForm.proGrowthRate=res.data[0].proGrowthRate+"";
                        }
                        if(res.data[0].saleIncreaseRate||res.data[0].saleIncreaseRate==0){
                            this.basicTypeForm.saleIncreaseRate=res.data[0].saleIncreaseRate+"";
                        }
                        if(res.data[0].currentRatio||res.data[0].currentRatio==0){
                            this.basicTypeForm.currentRatio=res.data[0].currentRatio+"";
                        }
                        this.basicTypeForm.longitudeLatitudeSwitch=res.data[0].longitudeLatitudeSwitch;
                        this.basicTypeForm.longitudeLatitudeSwitchOld=res.data[0].longitudeLatitudeSwitchOld;
                        this.basicTypeForm.personRelCardealerSwitch=res.data[0].personRelCardealerSwitch;
                        this.basicTypeForm.choiceCardealerSwitch=res.data[0].choiceCardealerSwitch;
                        this.basicTypeForm.businessType=res.data[0].businessType.split(',');
                        this.basicTypeForm.gpsVendorAuthor=res.data[0].gpsVendorAuthor.split(',');
                        this.basicTypeForm.gpsInstalMode=res.data[0].gpsInstalMode;

                        let channelAddressValue = [];
                        channelAddressValue[0]=res.data[0].channelProvince;
                        channelAddressValue[1]=res.data[0].channelCity;
                        this.basicTypeForm.channelAddressValue=channelAddressValue;
                        this.basicTypeForm.channelAddressDetail=res.data[0].channelAddress;

                        //开票信息
                        this.invoiceForm.taxpayerType=res.data[0].taxpayerType;
                        this.invoiceForm.taxpayerIdNumber=res.data[0].taxpayerIdNumber;
                        this.invoiceForm.invoiceTelNumber=res.data[0].invoiceTelNumber;
                        this.invoiceForm.invoiceAddress=res.data[0].invoiceAddress;
                        this.invoiceForm.openingBank=res.data[0].openingBank;
                        this.invoiceForm.bankAccount=res.data[0].bankAccount;
                        this.invoiceForm.taxRate=res.data[0].taxRate;

                        /*let channelInfo = this.basicTypeForm;
                        this.$emit('passmsg',channelInfo)*/

                    //    this.getAllSaleManByChannelId();
                        this.getChannelInfoById(-1);


                        setTimeout(() => {
                            this.getDetailAddress1(res.data[0].channelProvince,res.data[0].channelCity);
                            this.checkdate();
                        },1000)


                        if(this.basicTypeForm.businessType.indexOf('01') == -1 && this.newCarRole=='1' && this.disableBusinessType){
                            this.basicTypeForm.businessType.push('01')
                        }
                         if(this.basicTypeForm.businessType.indexOf('02') == -1 && this.oldCarRole=='1' && this.disableBusinessType){
                            this.basicTypeForm.businessType.push('02')
                        }


                    }
                });
                //根据车商id查询风控信息
                getDirectCarDealerRiskInfoById(v,associatedChannelId).then(resRisk => {
                    this.loading = false;
                    if (resRisk.code === "0000") {
                        let str = JSON.stringify(resRisk.data);
                        let channelRiskInfoTemp = JSON.parse(str);
                        channelRiskInfoTemp.forEach((item, i) => {
                            //新车
                            if(item.businessType=="01"){
                                let validDate = [];
                                validDate[0]=item.qualityStartDate;
                                validDate[1]=item.qualityEndDate;
                                this.basicTypeForm.validDate=validDate;
                                this.basicTypeForm.carType=item.carType.split(',');
                                this.basicTypeForm.qualityGrade =item.qualityGrade;
                                this.basicTypeForm.customerManager=item.customerManager;

                                //this.saleMan =item.customerManager;
                                this.basicTypeForm.channelGrade =item.channelGrade;
                                if(item.channelDeposit || item.channelDeposit=="0"){
                                    if(item.channelDeposit=="0"){
                                        this.basicTypeForm.channelDeposit ="0";
                                    }else{
                                        this.basicTypeForm.channelDeposit =item.channelDeposit+"";
                                    }
                                }
                                this.basicTypeForm.authRegionSwitch =item.authRegionSwitch;
                                this.basicTypeForm.authVehicleTypeSwitch =item.authVehicleTypeSwitch;
                                this.basicTypeForm.accountMaxNum = item.accountMaxNum;
                           //    this.checkAllNewCar(v,associatedChannelId);
                            }else{
                                //二手车
                                let validDate = [];
                                validDate[0]=item.qualityStartDate;
                                validDate[1]=item.qualityEndDate;
                                this.basicTypeForm.validDateOld=validDate;
                                this.basicTypeForm.carTypeOld=item.carType.split(',');
                                this.basicTypeForm.qualityGradeOld =item.qualityGrade;
                                this.basicTypeForm.customerManagerOld=item.customerManager;
                                this.basicTypeForm.channelGradeOld =item.channelGrade;
                                if(item.channelDeposit || item.channelDeposit=="0"){
                                    if(item.channelDeposit=="0"){
                                        this.basicTypeForm.channelDepositOld ="0";
                                    }else{
                                        this.basicTypeForm.channelDepositOld =item.channelDeposit+"";
                                    }
                                }
                                this.basicTypeForm.authRegionSwitchOld =item.authRegionSwitch;
                                this.basicTypeForm.authVehicleTypeSwitchOld =item.authVehicleTypeSwitch;
                                this.basicTypeForm.accountMaxNumOld = item.accountMaxNum;
                            }
                        })
                        this.carBusinessType();
                    }
                });

                //根据渠道id查询保证金信息
                getChannelQuotaInfoById(v,associatedChannelId).then(resQuota => {
                    this.loading = false;
                    if (resQuota.code === "0000") {

                        let str = JSON.stringify(resQuota.data);
                        let ChannelQuotaInfoTemp = JSON.parse(str);
                        ChannelQuotaInfoTemp.forEach((item, i) => {
                            //新车
                            if(item.businessType=="01"){
                                //签放额度
                                if (item.quotaType == "1") {
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPut = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount || item.quotaAmount=="0"){
                                        this.basicTypeForm.limitPut = item.quotaAmount+"";
                                    }
                                    if(item.tempQuota || item.tempQuota=="0"){
                                        this.basicTypeForm.temporaryLimitPut=item.tempQuota+"";
                                    }
                                    if(item.occupiedQuota || item.occupiedQuota=="0"){
                                        this.basicTypeForm.limitPutTake=item.occupiedQuota+"";
                                    }
                                    this.basicTypeForm.limitPutTime=item.validityTermEnd;
                                    if(item.surplusQuota || item.surplusQuota=="0"){
                                        this.basicTypeForm.limitPutResidue=item.surplusQuota+"";
                                    }
                                } else {
                                    //先放后抵
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPledge = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount || item.quotaAmount=="0"){
                                        this.basicTypeForm.limitPledge = item.quotaAmount+"";

                                    }
                                    if(item.tempQuota || item.tempQuota=="0"){
                                        this.basicTypeForm.temporaryLimitPledge=item.tempQuota+"";
                                    }
                                    if(item.occupiedQuota || item.occupiedQuota=="0"){
                                        this.basicTypeForm.limitPledgeTake=item.occupiedQuota+"";
                                    }
                                    this.basicTypeForm.limitPledgeTime=item.validityTermEnd;
                                    if(item.surplusQuota || item.surplusQuota=="0"){
                                        this.basicTypeForm.limitPledgeResidue=item.surplusQuota+"";
                                    }
                                }
                            }else{
                                //二手车
                                //签放额度
                                if (item.quotaType == "1") {
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPutOld = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount || item.quotaAmount=="0"){
                                        this.basicTypeForm.limitPutOld = item.quotaAmount+"";
                                    }
                                    if(item.tempQuota || item.tempQuota=="0"){
                                        this.basicTypeForm.temporaryLimitPutOld=item.tempQuota+"";
                                    }
                                    if(item.occupiedQuota || item.occupiedQuota=="0"){
                                        this.basicTypeForm.limitPutTakeOld=item.occupiedQuota+"";
                                    }
                                    this.basicTypeForm.limitPutTimeOld=item.validityTermEnd;
                                    if(item.surplusQuota || item.surplusQuota=="0"){
                                        this.basicTypeForm.limitPutResidueOld=item.surplusQuota+"";
                                    }
                                } else {
                                    //先放后抵
                                    if(item.quotaControlSwitch){
                                        this.basicTypeForm.isLimitPledgeOld = item.quotaControlSwitch+"";
                                    }
                                    if(item.quotaAmount || item.quotaAmount=="0"){
                                        this.basicTypeForm.limitPledgeOld = item.quotaAmount+"";

                                    }
                                    if(item.tempQuota || item.tempQuota=="0"){
                                        this.basicTypeForm.temporaryLimitPledgeOld=item.tempQuota+"";
                                    }
                                    if(item.occupiedQuota || item.occupiedQuota=="0"){
                                        this.basicTypeForm.limitPledgeTakeOld=item.occupiedQuota+"";
                                    }
                                    this.basicTypeForm.limitPledgeTimeOld=item.validityTermEnd;
                                    if(item.surplusQuota || item.surplusQuota=="0"){
                                        this.basicTypeForm.limitPledgeResidueOld=item.surplusQuota+"";
                                    }
                                }
                            }

                        })
                    }
                })
            },
            getChannelList(v) {
                // 多件基本信息
                this.loading = true;
                getChannelListData(this.basicTypeForm).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.data = res.data.records;
                        this.total = res.data.total;
                    }
                });
            },
            //地址公共信息调用
            locationChange(item, callback) {
                item.loading = true;
                getLocation({upperCode: item.value}).then(res => {
                    if (res.code === "0000") {
                        res.data.forEach(function (item) {
                            if (item.isParent) {
                                item.loading = false;
                                item.children = [];
                            }
                        });
                        item.children = res.data;
                        item.loading = false;
                        callback();
                    }
                })
            },

            getLocationData() {
                let param = ""
                param = {
                    level: '1',
                    upperCode: '1'
                };
                getLocation(param).then((res) => {
                    if (res.code == "0000") {
                        res.data.forEach(item => {
                            // 表明是否是父节点
                            if (item.isParent) {
                                item.loading = false;
                                item.children = [];
                            }
                        })
                        this.province=res.data;
                    }
                    this.location.push(...res.data);
                })
                let param2 = {level:2,upperCode:''};
                getLocation(param2).then(res => {
                    if (res.code == "0000") {
                        this.city=res.data;
                    }
                });
            },

            closeModel(value){
                this.isShowModel=value;
            },
            //Gps授权厂商
            handgps_vendor_author (val) {
                this.gpsvendorauthor.push({
                    value: val,
                    label: val
                });
            },
            //Gps安装方式
            handgps_instal_mode(val) {
                this.gpsinstalmode.push({
                    value: val,
                    label: val
                });
            },

            showAreasNewCar(){
                //新车展业区域查看
                let areaId = [];
                let selectedAreaNodes = this.$refs.mainArea.getSelectedNodes();
                selectedAreaNodes.forEach(function (e) {
                    areaId.push(e.id);
                });
                if(areaId.length==0){
                    this.$Message.error("请至少选中一个区域!");
                    return;
                }
                this.areaVisible=true;
                this.searchForm.areaId=areaId;
                this.searchForm.businessType="01";
                this.getAreaList();
            },
            showAreasOldCar(){
                //二手车展业区域
                let areaId = [];
                let selectedAreaNodes = this.$refs.mainAreaOld.getSelectedNodes();
                selectedAreaNodes.forEach(function (e) {
                    areaId.push(e.id);
                });
                if(areaId.length==0){
                    this.$Message.error("请至少选中一个区域!");
                    return;
                }
                this.areaVisible=true;
                this.searchForm.areaId=areaId;
                this.searchForm.businessType="02";
                this.getAreaList();
            },
            selectDateRange(v) {
                if (v) {
                    this.basicTypeForm.startDate = v[0];
                    this.basicTypeForm.endDate = v[1];
                }
            },
            getAreaList() {
                // 多条件搜索已上线的直营车商展业区域
                this.loading = true;
                getDirectCardealerAreaListData(this.searchForm).then(res => {
                    this.loading = false;
                    if (res.code === "0000") {
                        this.dataArea = res.data.records;
                        this.totalArea = res.data.total;
                    }
                });
            },
            refresh() {
                this.$refs['basicTypeForm'].reloadData();
            },


            //直营车商车商保存为草稿
            basicSave(){
                console.log("渠道基本信息保存开始！");
                //有效期表单验证
                //this.verifyLimitDate();
                this.channelStatus="02";
                this.channelStatusNewCar="";
                this.channelStatusOldCar="";
                this.$refs.basicTypeForm.validate(valid => {
                    console.log(valid);
                    if (valid) {

                        this.$parent.spinShow=true;
                        let brandIds = this.getSelectedBrandNodes(this.getCheckedNodes());
                        brandIds = this.handelBrands(brandIds)
                        this.basicTypeForm.mainBrand = brandIds;
                        let areaIds = [];
                        let carIds = [];
                        //新车角色权限 && 而且当前页面存在新车表单
                        if(this.isNewCarRole=="1"){

                            areaIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainArea'));
                            carIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCar'));
                            carIds =  this.handelMainCar(carIds);
                            if(areaIds.length===0 && this.basicTypeForm.authRegionSwitch=="1"){
                                this.$Message.error("请勾选新车授权区域");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            if(carIds.length===0 && this.basicTypeForm.authVehicleTypeSwitch=="1"){
                                this.$Message.error("请勾新车授权车型");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            this.channelStatusNewCar=this.channelStatus;
                        }
                        this.basicTypeForm.mainArea=areaIds;
                        this.basicTypeForm.mainCar=carIds;

                        let carOldIds = [];
                        let areaOldIds = [];
                        //二手车角色权限 && 而且当前页面存在二手车表单
                        if(this.isOldCarRole=="1"){
                            carOldIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCarOld'));
                            carOldIds =  this.handelMainCar(carOldIds);
                            areaOldIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainAreaOld'));
                            if(carOldIds.length==0 && this.basicTypeForm.authVehicleTypeSwitchOld=="1"){
                                this.$Message.error("请勾二手车授权车型");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            if(areaOldIds.length==0 && this.basicTypeForm.authRegionSwitchOld=="1"){
                                this.$Message.error("请勾选二手车授权区域");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            this.channelStatusOldCar=this.channelStatus;
                        }
                        this.basicTypeForm.mainCarOld=carOldIds;
                        this.basicTypeForm.mainAreaOld=areaOldIds;

                        console.log("渠道基本信息");
                        if(this.basicTypeForm.channelId==undefined){
                            this.basicTypeForm.channelId=="";
                        }

                        //上线日期
                        if(this.basicTypeForm.onlineDate != null || this.basicTypeForm.onlineDate !=""){
                            this.basicTypeForm.onlineDate =formatDate(this.basicTypeForm.onlineDate,"yyyy-MM-dd")
                        }
                        //新车有效期
                        if(this.basicTypeForm.validDate != null || this.basicTypeForm.validDate !=""){
                            this.basicTypeForm.validDate[0] =formatDate(this.basicTypeForm.validDate[0],"yyyy-MM-dd")
                            this.basicTypeForm.validDate[1] =formatDate(this.basicTypeForm.validDate[1],"yyyy-MM-dd")
                        }
                        //二手车有效期
                        if(this.basicTypeForm.validDateOld != null || this.basicTypeForm.validDateOld !=""){
                            this.basicTypeForm.validDateOld[0] =formatDate(this.basicTypeForm.validDateOld[0],"yyyy-MM-dd")
                            this.basicTypeForm.validDateOld[1] =formatDate(this.basicTypeForm.validDateOld[1],"yyyy-MM-dd")
                        }
                        //注册日期
                        if(this.basicTypeForm.registrationDate != null || this.basicTypeForm.registrationDate !=""){
                            this.basicTypeForm.registrationDate =formatDate(this.basicTypeForm.registrationDate,"yyyy-MM-dd")
                        }
                        //新车先放有效期
                        if(this.basicTypeForm.limitPutTime != null || this.basicTypeForm.limitPutTime !=""){
                            this.basicTypeForm.limitPutTime =formatDate(this.basicTypeForm.limitPutTime,"yyyy-MM-dd")
                        }
                        //新车后放有效期
                        if(this.basicTypeForm.limitPledgeTime != null || this.basicTypeForm.limitPledgeTime !=""){
                            this.basicTypeForm.limitPledgeTime =formatDate(this.basicTypeForm.limitPledgeTime,"yyyy-MM-dd")
                        }
                        //二手车先放有效期
                        if(this.basicTypeForm.limitPutTimeOld != null || this.basicTypeForm.limitPutTimeOld !=""){
                            this.basicTypeForm.limitPutTimeOld =formatDate(this.basicTypeForm.limitPutTimeOld,"yyyy-MM-dd")
                        }
                        //二手车后放有效期
                        if(this.basicTypeForm.limitPledgeTimeOld != null || this.basicTypeForm.limitPledgeTimeOld !=""){
                            this.basicTypeForm.limitPledgeTimeOld =formatDate(this.basicTypeForm.limitPledgeTimeOld,"yyyy-MM-dd")
                        }

                        let param = {
                            //当前登陆者是否拥用新车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                            isNewCarRole:this.isNewCarRole,
                            //当前登陆者是否拥用二手车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                            isOldCarRole:this.isOldCarRole,
                            isEnable: this.isEnable,

                            oldChannelId:this.basicTypeForm.oldChannelId,
                            channelCoopeCardealerTemp: {
                                channelId: this.basicTypeForm.channelId,
                                associAccount: this.basicTypeForm.customerManager,
                                associAccountOld: this.basicTypeForm.customerManagerOld,
                                cardealerType: "02",
                                status: this.channelStatus,
                                oldCarStatus: this.channelStatus,
                                createBy:"channelAdmin",
                            },
                            //渠道基本信息f
                            channelBaseInfoTemp: {
                                //渠道类型：直营车商
                                channelType:"02",
                                //主键
                                id:this.basicTypeForm.id,
                                //渠道代码
                                channelCode: this.basicTypeForm.channelCode,
                                //合作商全称
                                channelFullName:this.basicTypeForm.channelFullName.replace(/\s*/g,""),
                                //上线日期
                                onlineDate:this.basicTypeForm.onlineDate,
                                //统一社会信用代码
                                socUniCrtCode:this.basicTypeForm.socUniCrtCode.replace(/\s*/g,""),
                                //法人
                                legalPerson:this.basicTypeForm.legalPerson.replace(/\s*/g,""),
                                //法人身份证号
                                legalPersonIdCard:this.basicTypeForm.legalPersonIdCard,
                                //法人电话
                                legalPersonTel:this.basicTypeForm.legalPersonTel,
                                //实际控制人
                                actualController:this.basicTypeForm.actualController.replace(/\s*/g,""),
                                //实际控制人身份证号
                                actualControllerIdCard:this.basicTypeForm.actualControllerIdCard,
                                //实际控制人电话
                                actualControllerTel:this.basicTypeForm.actualControllerTel,
                                //管理员姓名
                                channelAdmin:this.basicTypeForm.channelAdmin.replace(/\s*/g,""),
                                //管理员电话
                                channelAdminTel:this.basicTypeForm.channelAdminTel,
                                //管理员邮箱
                                channelAdminMail:this.basicTypeForm.channelAdminMail,
                                //管理员证件号码
                                channelAdminIdCard:this.basicTypeForm.channelAdminIdCard,
                                //渠道管理员详细地址
                                channelAdminAddress:this.basicTypeForm.channelAdminAddressDetail,
                                //注册地址详细地址
                                channelAddress:this.basicTypeForm.channelAddressDetail,
                                //办公地址详细地址
                                officeAddress:this.basicTypeForm.officeAddressDetail,
                                //地址经纬度
                                longitude:this.basicTypeForm.longitude,
                                latitude:this.basicTypeForm.latitude,
                                //经纬度范围
                                longitudeLatitudeRange:this.basicTypeForm.longitudeLatitudeRange.replace(/\s*/g,""),
                                //公司人数
                                companiesNumber:this.basicTypeForm.companiesNumber,
                                //车商性质
                                carDealType:this.basicTypeForm.cardealType,
                                //经营场所面积
                                businessArea:this.basicTypeForm.businessArea,
                                //汇款对象
                                paymentObject:this.basicTypeForm.paymentObject,
                                //注册日期
                                registrationDate:this.basicTypeForm.registrationDate,
                                //主营品牌
                                mainBrand:this.basicTypeForm.mainBrand,
                                //注册资金
                                registeredCapital:this.basicTypeForm.registeredCapital,
                                //实收资本
                                subscribedCapital:this.basicTypeForm.subscribedCapital,
                                //渠道归属
                                channelBelong:this.basicTypeForm.channelBelong,
                                //业务类型;新车/二手车
                                delete:this.basicTypeForm.businessType,
                                businessType:this.basicTypeForm.businessType.join(','),

                                //资产净值
                                netAssetValue:this.basicTypeForm.netAssetValue,
                                //经营年限
                                businessLife:this.basicTypeForm.businessLife,
                                //GPS厂商授权
                                delete:this.basicTypeForm.gpsVendorAuthor,
                                gpsVendorAuthor:this.basicTypeForm.gpsVendorAuthor.join(','),

                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //上一年度经营利润
                                operatingProfit:this.basicTypeForm.operatingProfit,
                                //GPS安装方式
                              //  //delete:this.basicTypeForm.gpsInstalMode,
                                gpsInstalMode:this.basicTypeForm.gpsInstalMode,

                                //业绩
                                achievement:this.basicTypeForm.achievement,
                                //利润率增长率
                                proGrowthRate:this.basicTypeForm.proGrowthRate,
                                //销售收入增长率
                                saleIncreaseRate:this.basicTypeForm.saleIncreaseRate,
                                //流动比率
                                currentRatio:this.basicTypeForm.currentRatio,
                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //车辆类型;新车;二手车
                                //delete:this.basicTypeForm.carType,
                                //carType:this.basicTypeForm.carType.join(','),
                                //经纬度是否控制;是/否
                                longitudeLatitudeSwitch:this.basicTypeForm.longitudeLatitudeSwitch,
                                longitudeLatitudeSwitchOld:this.basicTypeForm.longitudeLatitudeSwitchOld,
                                //业务人员关联车商;是/否
                                personRelCardealerSwitch:this.basicTypeForm.personRelCardealerSwitch,
                                //进件选择车商;是/否
                                choiceCardealerSwitch:this.basicTypeForm.choiceCardealerSwitch,
                                //层级最大数目
                                hierarchy:this.basicTypeForm.hierarchy,

                                channelStatus:this.channelStatusNewCar,
                                channelStatusOldCar:this.channelStatusOldCar,
                                accountInfo:this.basicTypeForm.accountInfo,

                            },
                            //管理员省市区
                           // channelAdminAddressValue:this.basicTypeForm.channelAdminAddressValue,
                            //注册地省市区
                            channelAddressValue:this.basicTypeForm.channelAddressValue,
                            //办公室省市区
                         //   officeAddressValue:this.basicTypeForm.officeAddressValue,
                            //新车授权区域
                            mainArea:this.basicTypeForm.mainArea,
                            //新车主营品牌
                            mainBrand:this.basicTypeForm.mainBrand,
                            //新车授权车型
                            mainCar:this.basicTypeForm.mainCar,

                            //新车授权区域
                            mainAreaOld:this.basicTypeForm.mainAreaOld,
                            //二手车授权车型
                            mainCarOld:this.basicTypeForm.mainCarOld,

                            //新车渠道风控信息
                            channelRiskInfoTemp: {
                                //业务类型：新车
                                businessType:"01",
                                qualityGrade:this.basicTypeForm.qualityGrade,
                                qualityStartDate:this.basicTypeForm.validDate[0],
                                qualityEndDate:this.basicTypeForm.validDate[1],

                                delete:this.basicTypeForm.carType,
                                carType:this.basicTypeForm.carType.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitch,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitch,
                                channelGrade:this.basicTypeForm.channelGrade,
                                channelDeposit:this.basicTypeForm.channelDeposit,
                                customerManager:this.basicTypeForm.customerManager,
                                accountMaxNum:this.basicTypeForm.accountMaxNum,
                            },
                            //二手车渠道风控信息
                            channelRiskInfoTempOld: {
                                //业务类型：二手车
                                businessType:"02",
                                qualityGrade:this.basicTypeForm.qualityGradeOld,
                                qualityStartDate:this.basicTypeForm.validDateOld[0],
                                qualityEndDate:this.basicTypeForm.validDateOld[1],

                                delete:this.basicTypeForm.carTypeOld,
                                carType:this.basicTypeForm.carTypeOld.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitchOld,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitchOld,
                                channelGrade:this.basicTypeForm.channelGradeOld,
                                channelDeposit:this.basicTypeForm.channelDepositOld,
                                customerManager:this.basicTypeForm.customerManagerOld,
                                accountMaxNum:this.basicTypeForm.accountMaxNumOld,
                            },
                            //新车渠道保证金信息
                            channelQuotaInfoTempList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPut,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPut,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPut,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidue,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledge,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledge,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledge,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidue,
                                }
                            ],

                            //二手车保证金信息
                            channelQuotaInfoTempOldList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPutOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPutOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPutOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidueOld,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledgeOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledgeOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledgeOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidueOld,
                                }
                            ],
                        }

                        let blackParams={
                            //黑名单类型：直营车商
                            blacklistType:'05',
                            //黑名单名称
                            blackName:this.basicTypeForm.channelFullName,
                            //证件类型：统一社会信息代码
                            idType:'02',
                            //统一社会信用代码
                            idCode:this.basicTypeForm.socUniCrtCode,
                        }
                        blackCleack(blackParams).then(res => {
                            if (res.code === "0000") {
                                //黑名单判断字段:是
                                if(res.weatherBlackList === "1"){
                                    this.$Message.error("该车商已存在黑名单信息库中！");
                                    this.$parent.spinShow=false;
                                }else {
                                    if(this.basicTypeForm.id !=null && this.basicTypeForm.id !="" && this.basicTypeForm.id != undefined && this.OldStatus !="06"){
                                        updateChannel(param).then(res => {
                                            if (res.code === "0000") {
                                                this.$Message.success("保存成功");
                                                this.basicTypeForm.id=res.data[0].id;
                                                this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                                //账户页面信息更新
                                                this.$parent.getAccountInfo(this.basicTypeForm.id);
                                                //回显业务员
                                                this.getAllSaleManByChannelId();
                                                //基本信息处理
                                                this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                                this.$parent.setchannelId(res.data[0].id);
                                              //  this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                             //   this.getBaseTreeList(res.data[0].id);
                                                //this.getCarsTreeList(res.data[0].id);
                                                this.$parent.spinShow=false;

                                                //同步影像件到进件服务
                                                let fileId = this.basicTypeForm.id;
                                                let belongNo = this.basicTypeForm.channelId;
                                                this.syncFileToApply(fileId,belongNo)

                                            }
                                        }).catch(() => {
                                            this.$Message.error("操作失败");
                                            this.$parent.spinShow=false;
                                        });
                                    }else {
                                        addChannel(param).then(res => {
                                            if (res.code === "0000") {
                                                this.$Message.success("保存成功");

                                                //基本信息处理
                                                this.basicTypeForm.id=res.data[0].id;
                                                this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                                //账户页面信息更新
                                                this.$parent.getAccountInfo(this.basicTypeForm.id);
                                                //回显业务员
                                                this.getAllSaleManByChannelId();

                                                this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                                this.$parent.setchannelId(res.data[0].id);
                                              //  this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                              //  this.getBaseTreeList(res.data[0].id);
                                                //this.getCarsTreeList(res.data[0].id);
                                                this.$parent.spinShow=false;
                                            }
                                        }).catch(() => {
                                            this.$Message.error("操作失败");
                                            this.$parent.spinShow=false;
                                        });
                                    }
                                }
                            }
                        }).catch(() => {
                            this.$Message.error("未知错误,请联系管理员！");
                            this.$parent.spinShow=false;
                        });

                    }else{
                        this.$Message.error('请补全基本信息');
                        this.$parent.spinShow=false;
                        for (let i in this.$refs.basicTypeForm.fields) {
                            if (this.$refs.basicTypeForm.fields[i].validateState === "error") {
                                this.$refs.basicTypeForm.fields[i].$el.scrollIntoView({
                                    block: "center",
                                    behavior: "smooth",
                                    inline: "center"
                                });
                                break;
                            }
                        }
                    }
                });
            },

            //暂存
            stagingSave(){
               if(this.basicTypeForm.channelId==null || this.basicTypeForm.channelId=="" || this.basicTypeForm.channelId==undefined){
                   this.$Message.error("请选择合作商!");
                   return false;
               }
                if(this.basicTypeForm.socUniCrtCode==null || this.basicTypeForm.socUniCrtCode=="" || this.basicTypeForm.socUniCrtCode==undefined){
                    this.$Message.error("请先输入统一社会信用代码!");
                    return false;
                }else{
                    var reg = /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/;
                    if (!reg.test(this.basicTypeForm.socUniCrtCode)) {
                        return false;
                    }
                }
                if(this.basicTypeForm.businessType.length==0){
                   this.$Message.error("请选择业务类型!");
                   return false;
               }

                this.$parent.spinShow=true;

              //  this.channelStatusOldCar='02';
                console.log("渠道基本信息暂存开始！");
                let brandIds = this.getSelectedBrandNodes(this.getCheckedNodes());
                brandIds = this.handelBrands(brandIds)
                this.basicTypeForm.mainBrand = brandIds;
                let areaIds = [];
                let carIds = [];
                //新车角色权限 && 而且当前页面存在新车表单
                if(this.isNewCarRole=="1"){
                    this.channelStatusNewCar='02';
                    areaIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainArea'));
                    carIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCar'));
                    carIds =  this.handelMainCar(carIds);
                }
                this.basicTypeForm.mainArea=areaIds;
                this.basicTypeForm.mainCar=carIds;

                let carOldIds = [];
                let areaOldIds = [];
                //二手车角色权限 && 而且当前页面存在二手车表单
                if(this.isOldCarRole=="1"){
                    this.channelStatusOldCar='02';
                    carOldIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCarOld'));
                    carOldIds =  this.handelMainCar(carOldIds);
                    areaOldIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainAreaOld'));
                }
                this.basicTypeForm.mainCarOld=carOldIds;
                this.basicTypeForm.mainAreaOld=areaOldIds;

                console.log("渠道基本信息");
                if(this.basicTypeForm.channelId==undefined){
                    this.basicTypeForm.channelId=="";
                }
                //上线日期
                if(this.basicTypeForm.onlineDate != null || this.basicTypeForm.onlineDate !=""){
                    this.basicTypeForm.onlineDate =formatDate(this.basicTypeForm.onlineDate,"yyyy-MM-dd")
                }
                //新车有效期
                if(this.basicTypeForm.validDate != null || this.basicTypeForm.validDate !=""){
                    this.basicTypeForm.validDate[0] =formatDate(this.basicTypeForm.validDate[0],"yyyy-MM-dd")
                    this.basicTypeForm.validDate[1] =formatDate(this.basicTypeForm.validDate[1],"yyyy-MM-dd")
                }
                //二手车有效期
                if(this.basicTypeForm.validDateOld != null || this.basicTypeForm.validDateOld !=""){
                    this.basicTypeForm.validDateOld[0] =formatDate(this.basicTypeForm.validDateOld[0],"yyyy-MM-dd")
                    this.basicTypeForm.validDateOld[1] =formatDate(this.basicTypeForm.validDateOld[1],"yyyy-MM-dd")
                }
                //注册日期
                if(this.basicTypeForm.registrationDate != null || this.basicTypeForm.registrationDate !=""){
                    this.basicTypeForm.registrationDate =formatDate(this.basicTypeForm.registrationDate,"yyyy-MM-dd")
                }
                //新车先放有效期
                if(this.basicTypeForm.limitPutTime != null || this.basicTypeForm.limitPutTime !=""){
                    this.basicTypeForm.limitPutTime =formatDate(this.basicTypeForm.limitPutTime,"yyyy-MM-dd")
                }
                //新车后放有效期
                if(this.basicTypeForm.limitPledgeTime != null || this.basicTypeForm.limitPledgeTime !=""){
                    this.basicTypeForm.limitPledgeTime =formatDate(this.basicTypeForm.limitPledgeTime,"yyyy-MM-dd")
                }
                //二手车先放有效期
                if(this.basicTypeForm.limitPutTimeOld != null || this.basicTypeForm.limitPutTimeOld !=""){
                    this.basicTypeForm.limitPutTimeOld =formatDate(this.basicTypeForm.limitPutTimeOld,"yyyy-MM-dd")
                }
                //二手车后放有效期
                if(this.basicTypeForm.limitPledgeTimeOld != null || this.basicTypeForm.limitPledgeTimeOld !=""){
                    this.basicTypeForm.limitPledgeTimeOld =formatDate(this.basicTypeForm.limitPledgeTimeOld,"yyyy-MM-dd")
                }

                let param = {
                    //当前登陆者是否拥用新车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                    isNewCarRole:this.isNewCarRole,
                    //当前登陆者是否拥用二手车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                    isOldCarRole:this.isOldCarRole,
                    isEnable: this.isEnable,

                    oldChannelId:this.basicTypeForm.oldChannelId,
                    channelCoopeCardealerTemp: {
                        channelId: this.basicTypeForm.channelId,
                        associAccount: this.basicTypeForm.customerManager,
                        associAccountOld: this.basicTypeForm.customerManagerOld,
                        cardealerType: "02",
                        status: '02',
                        oldCarStatus: '02',
                        createBy:"channelAdmin",
                    },
                    //渠道基本信息
                    channelBaseInfoTemp: {
                        //渠道类型：直营车商
                        channelType:"02",
                        //主键
                        id:this.basicTypeForm.id,
                        //渠道代码
                        channelCode: this.basicTypeForm.channelCode,
                        //合作商全称
                        channelFullName:this.basicTypeForm.channelFullName.replace(/\s*/g,""),
                        //上线日期
                        onlineDate:this.basicTypeForm.onlineDate,
                        //统一社会信用代码
                        socUniCrtCode:this.basicTypeForm.socUniCrtCode.replace(/\s*/g,""),
                        //法人
                        legalPerson:this.basicTypeForm.legalPerson.replace(/\s*/g,""),
                        //法人身份证号
                        legalPersonIdCard:this.basicTypeForm.legalPersonIdCard,
                        //法人电话
                        legalPersonTel:this.basicTypeForm.legalPersonTel,
                        //实际控制人
                        actualController:this.basicTypeForm.actualController.replace(/\s*/g,""),
                        //实际控制人身份证号
                        actualControllerIdCard:this.basicTypeForm.actualControllerIdCard,
                        //实际控制人电话
                        actualControllerTel:this.basicTypeForm.actualControllerTel,
                        //管理员姓名
                        channelAdmin:this.basicTypeForm.channelAdmin.replace(/\s*/g,""),
                        //管理员电话
                        channelAdminTel:this.basicTypeForm.channelAdminTel,
                        //管理员邮箱
                        channelAdminMail:this.basicTypeForm.channelAdminMail,
                        //管理员证件号码
                        channelAdminIdCard:this.basicTypeForm.channelAdminIdCard,
                        //渠道管理员详细地址
                        channelAdminAddress:this.basicTypeForm.channelAdminAddressDetail,
                        //注册地址详细地址
                        channelAddress:this.basicTypeForm.channelAddressDetail,
                        //办公地址详细地址
                        officeAddress:this.basicTypeForm.officeAddressDetail,
                        //地址经纬度
                        longitude:this.basicTypeForm.longitude,
                        latitude:this.basicTypeForm.latitude,
                        //经纬度范围
                        longitudeLatitudeRange:this.basicTypeForm.longitudeLatitudeRange.replace(/\s*/g,""),
                        //公司人数
                        companiesNumber:this.basicTypeForm.companiesNumber,
                        //车商性质
                        carDealType:this.basicTypeForm.cardealType,
                        //经营场所面积
                        businessArea:this.basicTypeForm.businessArea,
                        //汇款对象
                        paymentObject:this.basicTypeForm.paymentObject,
                        //注册日期
                        registrationDate:this.basicTypeForm.registrationDate,
                        //主营品牌
                        mainBrand:this.basicTypeForm.mainBrand,
                        //注册资金
                        registeredCapital:this.basicTypeForm.registeredCapital,
                        //实收资本
                        subscribedCapital:this.basicTypeForm.subscribedCapital,
                        //渠道归属
                        channelBelong:this.basicTypeForm.channelBelong,
                        //业务类型;新车/二手车
                        delete:this.basicTypeForm.businessType,
                        businessType:this.basicTypeForm.businessType.join(','),

                        //资产净值
                        netAssetValue:this.basicTypeForm.netAssetValue,
                        //经营年限
                        businessLife:this.basicTypeForm.businessLife,
                        //GPS厂商授权
                        delete:this.basicTypeForm.gpsVendorAuthor,
                        gpsVendorAuthor:this.basicTypeForm.gpsVendorAuthor.join(','),

                        //资产负债率
                        assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                        //上一年度经营利润
                        operatingProfit:this.basicTypeForm.operatingProfit,
                        //GPS安装方式
                        ////delete:this.basicTypeForm.gpsInstalMode,
                        gpsInstalMode:this.basicTypeForm.gpsInstalMode,

                        //业绩
                        achievement:this.basicTypeForm.achievement,
                        //利润率增长率
                        proGrowthRate:this.basicTypeForm.proGrowthRate,
                        //销售收入增长率
                        saleIncreaseRate:this.basicTypeForm.saleIncreaseRate,
                        //流动比率
                        currentRatio:this.basicTypeForm.currentRatio,
                        //资产负债率
                        assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                        //车辆类型;新车;二手车
                        //delete:this.basicTypeForm.carType,
                        //carType:this.basicTypeForm.carType.join(','),
                        //经纬度是否控制;是/否
                        longitudeLatitudeSwitch:this.basicTypeForm.longitudeLatitudeSwitch,
                        longitudeLatitudeSwitchOld:this.basicTypeForm.longitudeLatitudeSwitchOld,
                        //业务人员关联车商;是/否
                        personRelCardealerSwitch:this.basicTypeForm.personRelCardealerSwitch,
                        //进件选择车商;是/否
                        choiceCardealerSwitch:this.basicTypeForm.choiceCardealerSwitch,
                        //层级最大数目
                        hierarchy:this.basicTypeForm.hierarchy,

                        channelStatus:this.channelStatusNewCar,
                        channelStatusOldCar:this.channelStatusOldCar,
                        accountInfo:this.basicTypeForm.accountInfo,

                    },
                    //管理员省市区
                    // channelAdminAddressValue:this.basicTypeForm.channelAdminAddressValue,
                    //注册地省市区
                    channelAddressValue:this.basicTypeForm.channelAddressValue,
                    //办公室省市区
                    //   officeAddressValue:this.basicTypeForm.officeAddressValue,
                    //新车授权区域
                    mainArea:this.basicTypeForm.mainArea,
                    //新车主营品牌
                    mainBrand:this.basicTypeForm.mainBrand,
                    //新车授权车型
                    mainCar:this.basicTypeForm.mainCar,

                    //新车授权区域
                    mainAreaOld:this.basicTypeForm.mainAreaOld,
                    //二手车授权车型
                    mainCarOld:this.basicTypeForm.mainCarOld,

                    //新车渠道风控信息
                    channelRiskInfoTemp: {
                        //业务类型：新车
                        businessType:"01",
                        qualityGrade:this.basicTypeForm.qualityGrade,
                        qualityStartDate:this.basicTypeForm.validDate[0],
                        qualityEndDate:this.basicTypeForm.validDate[1],

                        delete:this.basicTypeForm.carType,
                        carType:this.basicTypeForm.carType.join(','),
                        authRegionSwitch:this.basicTypeForm.authRegionSwitch,
                        authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitch,
                        channelGrade:this.basicTypeForm.channelGrade,
                        channelDeposit:this.basicTypeForm.channelDeposit,
                        customerManager:this.basicTypeForm.customerManager,
                        accountMaxNum:this.basicTypeForm.accountMaxNum,
                    },
                    //二手车渠道风控信息
                    channelRiskInfoTempOld: {
                        //业务类型：二手车
                        businessType:"02",
                        qualityGrade:this.basicTypeForm.qualityGradeOld,
                        qualityStartDate:this.basicTypeForm.validDateOld[0],
                        qualityEndDate:this.basicTypeForm.validDateOld[1],

                        delete:this.basicTypeForm.carTypeOld,
                        carType:this.basicTypeForm.carTypeOld.join(','),
                        authRegionSwitch:this.basicTypeForm.authRegionSwitchOld,
                        authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitchOld,
                        channelGrade:this.basicTypeForm.channelGradeOld,
                        channelDeposit:this.basicTypeForm.channelDepositOld,
                        customerManager:this.basicTypeForm.customerManagerOld,
                        accountMaxNum:this.basicTypeForm.accountMaxNumOld,
                    },
                    //新车渠道保证金信息
                    channelQuotaInfoTempList: [
                        //签放额度
                        {
                            //额度类型：签放额度
                            quotaType:"1",
                            //业务类型:新车
                            businessType:"01",
                            //是否控制额度
                            quotaControlSwitch:this.basicTypeForm.isLimitPut,
                            //额度金额
                            quotaAmount:this.basicTypeForm.limitPut,
                            //临时额度
                            tempQuota:this.basicTypeForm.temporaryLimitPut,
                            //有效期
                            validityTermEnd:this.basicTypeForm.limitPutTime,
                            //占用额度
                            occupiedQuota:this.basicTypeForm.limitPutTake,
                            //剩余额度
                            surplusQuota:this.basicTypeForm.limitPutResidue,

                        },
                        //先放后抵押额度
                        {
                            //额度类型：先放后抵押额度
                            quotaType:"2",
                            //业务类型
                            businessType:"01",
                            //是否控制额度
                            quotaControlSwitch:this.basicTypeForm.isLimitPledge,
                            //额度金额
                            quotaAmount:this.basicTypeForm.limitPledge,
                            //临时额度
                            tempQuota:this.basicTypeForm.temporaryLimitPledge,
                            //有效期
                            validityTermEnd:this.basicTypeForm.limitPledgeTime,
                            //占用额度
                            occupiedQuota:this.basicTypeForm.limitPledgeTake,
                            //剩余额度
                            surplusQuota:this.basicTypeForm.limitPledgeResidue,
                        }
                    ],

                    //二手车保证金信息
                    channelQuotaInfoTempOldList: [
                        //签放额度
                        {
                            //额度类型：签放额度
                            quotaType:"1",
                            //业务类型:新车
                            businessType:"02",
                            //是否控制额度
                            quotaControlSwitch:this.basicTypeForm.isLimitPutOld,
                            //额度金额
                            quotaAmount:this.basicTypeForm.limitPutOld,
                            //临时额度
                            tempQuota:this.basicTypeForm.temporaryLimitPutOld,
                            //有效期
                            validityTermEnd:this.basicTypeForm.limitPutTimeOld,
                            //占用额度
                            occupiedQuota:this.basicTypeForm.limitPutTakeOld,
                            //剩余额度
                            surplusQuota:this.basicTypeForm.limitPutResidueOld,

                        },
                        //先放后抵押额度
                        {
                            //额度类型：先放后抵押额度
                            quotaType:"2",
                            //业务类型
                            businessType:"02",
                            //是否控制额度
                            quotaControlSwitch:this.basicTypeForm.isLimitPledgeOld,
                            //额度金额
                            quotaAmount:this.basicTypeForm.limitPledgeOld,
                            //临时额度
                            tempQuota:this.basicTypeForm.temporaryLimitPledgeOld,
                            //有效期
                            validityTermEnd:this.basicTypeForm.limitPledgeTimeOld,
                            //占用额度
                            occupiedQuota:this.basicTypeForm.limitPledgeTakeOld,
                            //剩余额度
                            surplusQuota:this.basicTypeForm.limitPledgeResidueOld,
                        }
                    ],
                }

                let blackParams={
                    //黑名单类型：直营车商
                    blacklistType:'05',
                    //黑名单名称
                    blackName:this.basicTypeForm.channelFullName,
                    //证件类型：统一社会信息代码
                    idType:'02',
                    //统一社会信用代码
                    idCode:this.basicTypeForm.socUniCrtCode,
                }
                blackCleack(blackParams).then(res => {
                    if (res.code === "0000") {
                        //黑名单判断字段:是
                        if(res.weatherBlackList === "1"){
                            this.$Message.error("该车商已存在黑名单信息库中！");
                            this.$parent.spinShow=false;
                        }else {
                            if(this.basicTypeForm.id !=null && this.basicTypeForm.id !="" && this.basicTypeForm.id != undefined && this.OldStatus !="06"){
                                updateChannel(param).then(res => {
                                    if (res.code === "0000") {
                                        this.$Message.success("保存成功");
                                        this.$parent.spinShow=false;
                                        this.basicTypeForm.id=res.data[0].id;
                                        this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                        //账户页面信息更新
                                        this.$parent.getAccountInfo(this.basicTypeForm.id);
                                        //回显业务员
                                        this.getAllSaleManByChannelId();
                                        //基本信息处理
                                        this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                        this.$parent.setchannelId(res.data[0].id);

                                      //  this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                     //   this.getBaseTreeList(res.data[0].id);
                                        //this.getCarsTreeList(res.data[0].id);

                                        //同步影像件到进件服务
                                        let fileId = this.basicTypeForm.id;
                                        let belongNo = this.basicTypeForm.channelId;
                                        this.syncFileToApply(fileId,belongNo)

                                    }
                                }).catch(() => {
                                    this.$Message.error("操作失败");
                                    this.$parent.spinShow=false;
                                });
                            }else {
                                addChannel(param).then(res => {
                                    if (res.code === "0000") {
                                        this.$Message.success("保存成功");
                                        this.$parent.spinShow=false;

                                        //基本信息处理
                                        this.basicTypeForm.id=res.data[0].id;
                                        this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                        //账户页面信息更新
                                        this.$parent.getAccountInfo(this.basicTypeForm.id);
                                        //回显业务员
                                        this.getAllSaleManByChannelId();

                                        this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                        this.$parent.setchannelId(res.data[0].id);

                                    //    this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                      //  this.getBaseTreeList(res.data[0].id);
                                        //this.getCarsTreeList(res.data[0].id);
                                    }
                                }).catch(() => {
                                    this.$Message.error("操作失败");
                                    this.$parent.spinShow=false;
                                });
                            }
                        }
                    }
                }).catch(() => {
                    this.$Message.error("未知错误,请联系管理员！");
                    this.$parent.spinShow=false;
                });

            },

            //直营车商提交审核
            basicSubmit(){
                //提交的时候校验额度信息
                //提交之前先查一下汇款账号信息 必须要有汇款账号信息
                this.channelStatus="00";
                this.channelStatusNewCar="";
                this.channelStatusOldCar="";
                this.checkNewCarQuotaInfo();
                console.log("渠道基本信息提交开始！");
            },

            checkNewCarQuotaInfo(){
                if(this.isNewCarRole=="1"){
                    getSurplusChannelQuotaInfo(this.basicTypeForm.channelId,"01",this.basicTypeForm.id).then(res => {
                        let result1 = false;
                        let result2 = false;
                       if (res.code === "0000") {
                            if(res.data.signQuotaSwitch == "1"){
                                if(this.basicTypeForm.isLimitPut =="0"){
                                    //弹框提示不能为否
                                    this.$Modal.warning({
                                    title: "警告",
                                    content: "此合作商的新车签放额度为控制,与其关联的车商新车签放额度只能为控制！"
                                });
                                    return false;
                                }else {
                                    if(res.data.surplusSignQuota < (parseFloat(this.basicTypeForm.limitPut) + parseFloat(this.basicTypeForm.temporaryLimitPut ? this.basicTypeForm.temporaryLimitPut : 0))){
                                        this.$Modal.warning({
                                        title: "警告",
                                        content: "当前车商新车签放额度大于合作商剩余额度！合作商剩余新车签放额度为"+res.data.surplusSignQuota+"元",
                                        })
                                        return false;
                                    }else{
                                        result1 = true;
                                    }
                                }
                            }else{
                                result1 = true;
                            }
                             if(res.data.beforeQuotaSwitch == "1"){
                                if(this.basicTypeForm.isLimitPledge =="0"){
                                    //弹框提示不能为否
                                    this.$Modal.warning({
                                    title: "警告",
                                    content: "此合作商的新车先放后抵额度为控制,与其关联的车商新车先放后抵额度只能为控制！"
                                });
                                    return false;
                                }else {
                                    if(res.data.surplusBeforeQuota < (parseFloat(this.basicTypeForm.limitPledge) + parseFloat(this.basicTypeForm.temporaryLimitPledge ? this.basicTypeForm.temporaryLimitPledge : 0))){
                                        this.$Modal.warning({
                                        title: "警告",
                                        content: "当前车商新车先放后抵额度大于合作商剩余额度！合作商剩余新车先放后抵额度为"+res.data.surplusBeforeQuota+"元",
                                        })
                                        return false;
                                    }else{
                                        result2 = true;
                                    }
                                }
                            }else{
                                 result2 = true;
                             }
                             if(result1 && result2){
                                 this.checkOldCarQuotaInfo();
                             }
                        }
                    })
                }else{
                    this.checkOldCarQuotaInfo();
                }
            },
            checkOldCarQuotaInfo(){
                if(this.isOldCarRole=="1"){
                    getSurplusChannelQuotaInfo(this.basicTypeForm.channelId,"02",this.basicTypeForm.id).then(res => {
                        let result1 = false;
                        let result2 = false;
                       if (res.code === "0000") {
                            if(res.data.signQuotaSwitch == "1"){
                                if(this.basicTypeForm.isLimitPutOld =="0"){
                                    //弹框提示不能为否
                                    this.$Modal.warning({
                                    title: "警告",
                                    content: "此合作商的二手车签放额度为控制,与其关联的车商二手车签放额度只能为控制！"
                                });
                                    return false;
                                }else {
                                    if(res.data.surplusSignQuota < (parseFloat(this.basicTypeForm.limitPutOld) + parseFloat(this.basicTypeForm.temporaryLimitPutOld ? this.basicTypeForm.temporaryLimitPutOld : 0))){
                                        this.$Modal.warning({
                                        title: "警告",
                                        content: "当前车商二手车签放额度大于合作商剩余额度！合作商剩余二手车签放额度为"+res.data.surplusSignQuota+"元",
                                        })
                                        return false;
                                    }else{
                                        result1 = true;
                                    }
                                }
                            }else{
                                result1 = true;
                            }

                             if(res.data.beforeQuotaSwitch == "1"){
                                if(this.basicTypeForm.isLimitPledgeOld =="0"){
                                    //弹框提示不能为否
                                    this.$Modal.warning({
                                    title: "警告",
                                    content: "此合作商的二手车先放后抵额度为控制,与其关联的车商二手车先放后抵额度只能为控制！"
                                });
                                    return false;
                                }else {
                                    if(res.data.surplusBeforeQuota < (parseFloat(this.basicTypeForm.limitPledgeOld) + parseFloat(this.basicTypeForm.temporaryLimitPledgeOld ? this.basicTypeForm.temporaryLimitPledgeOld : 0))){
                                        this.$Modal.warning({
                                        title: "警告",
                                        content: "当前车商二手车先放后抵额度大于合作商剩余额度！合作商剩余二手车先放后抵额度为"+res.data.surplusBeforeQuota+"元",
                                        })
                                        return false;
                                    }else{
                                        result2 = true;
                                    }
                                }
                            }else{
                                 result2 = true;
                             }
                             if(result1 && result2){
                                 this.checkAccountInfo();
                             }

                        }

                    })
                }else{
                    this.checkAccountInfo()
                }
            },

            checkAccountInfo(){
                 let channelId = this.basicTypeForm.id;
                console.log(channelId)
                if(this.basicTypeForm.accountInfo != "0"){
                    //收款账号选择有 ,验证收款账号信息
                    if(channelId ==null || channelId=="" || channelId==undefined){
                        this.$Message.error("请先保存基本信息");
                        console.log("请先保存基本信息")
                        return false;
                    }
                    let businessType = null;
                    if(this.newCarRole=='1' && this.oldCarRole != '1'){
                        businessType = '01'
                    }else if(this.newCarRole !='1' && this.oldCarRole == '1'){
                        businessType = '02'
                    }
                    getAccountInfoByChannelId(channelId,this.basicTypeForm.channelId,businessType).then(res => {
                        if (res.code === "0000") {
                            if(res.data == "01" && this.accountSwitch=='1'){
                                this.channelStatus="01";
                            }else{
                                this.channelStatus="00";
                            }
                            this.submitBasicInfo();
                        }
                    })
                }else {
                    this.submitBasicInfo();
                }
            },

            submitBasicInfo(){
                this.channelStatusNewCar="";
                this.channelStatusOldCar="";
                //有效期表单验证
                this.$refs.basicTypeForm.validate(valid => {
                    console.log(valid);
                    if (valid) {
                        this.$parent.spinShow=true;
                        let brandIds = this.getSelectedBrandNodes(this.getCheckedNodes());
                        brandIds = this.handelBrands(brandIds)
                        /*if(brandIds.length==0){
                            this.$Message.error("请勾主营品牌");
                            return false;
                        }*/
                        this.basicTypeForm.mainBrand=brandIds;
                        let areaIds = [];
                        let carIds = [];
                        //新车角色权限 && 而且当前页面存在新车表单
                        if(this.isNewCarRole=="1"){
                              areaIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainArea'));
                              carIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCar'));
                              carIds =  this.handelMainCar(carIds);
                            if(areaIds.length===0 && this.basicTypeForm.authRegionSwitch=="1"){
                                this.$Message.error("请勾选新车授权区域");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            if(carIds.length===0 && this.basicTypeForm.authVehicleTypeSwitch=="1"){
                                this.$Message.error("请勾新车授权车型");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            this.channelStatusNewCar=this.channelStatus;
                        }
                        this.basicTypeForm.mainArea=areaIds;
                        this.basicTypeForm.mainCar=carIds;

                        let carOldIds = [];
                        let areaOldIds = [];
                        //二手车角色权限 && 而且当前页面存在二手车表单

                        if(this.isOldCarRole=="1"){
                            carOldIds = this.getSelectedCarNodes(this.getCheckedNodes('mainCarOld'));
                            carOldIds =  this.handelMainCar(carOldIds);
                            areaOldIds = this.getSelectedAreaNodes(this.getCheckedNodes('mainAreaOld'));
                            if(carOldIds.length==0 && this.basicTypeForm.authVehicleTypeSwitchOld=="1"){
                                this.$Message.error("请勾二手车授权车型");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            if(areaOldIds.length==0 && this.basicTypeForm.authRegionSwitchOld=="1"){
                                this.$Message.error("请勾选二手车授权区域");
                                this.$parent.loadingSave=false;
                                this.$parent.spinShow=false;
                                return false;
                            }
                            this.channelStatusOldCar=this.channelStatus;
                        }
                        this.basicTypeForm.mainCarOld=carOldIds;
                        this.basicTypeForm.mainAreaOld=areaOldIds;

                        console.log("渠道基本信息");
                        if(this.basicTypeForm.channelId==undefined){
                            this.basicTypeForm.channelId=="";
                        }

                        //上线日期
                        if(this.basicTypeForm.onlineDate != null || this.basicTypeForm.onlineDate !=""){
                            this.basicTypeForm.onlineDate =formatDate(this.basicTypeForm.onlineDate,"yyyy-MM-dd")
                        }
                        //新车有效期
                        if(this.basicTypeForm.validDate != null || this.basicTypeForm.validDate !=""){
                            this.basicTypeForm.validDate[0] =formatDate(this.basicTypeForm.validDate[0],"yyyy-MM-dd")
                            this.basicTypeForm.validDate[1] =formatDate(this.basicTypeForm.validDate[1],"yyyy-MM-dd")
                        }
                        //二手车有效期
                        if(this.basicTypeForm.validDateOld != null || this.basicTypeForm.validDateOld !=""){
                            this.basicTypeForm.validDateOld[0] =formatDate(this.basicTypeForm.validDateOld[0],"yyyy-MM-dd")
                            this.basicTypeForm.validDateOld[1] =formatDate(this.basicTypeForm.validDateOld[1],"yyyy-MM-dd")
                        }
                        //注册日期
                        if(this.basicTypeForm.registrationDate != null || this.basicTypeForm.registrationDate !=""){
                            this.basicTypeForm.registrationDate =formatDate(this.basicTypeForm.registrationDate,"yyyy-MM-dd")
                        }
                        //新车先放有效期
                        if(this.basicTypeForm.limitPutTime != null || this.basicTypeForm.limitPutTime !=""){
                            this.basicTypeForm.limitPutTime =formatDate(this.basicTypeForm.limitPutTime,"yyyy-MM-dd")
                        }
                        //新车后放有效期
                        if(this.basicTypeForm.limitPledgeTime != null || this.basicTypeForm.limitPledgeTime !=""){
                            this.basicTypeForm.limitPledgeTime =formatDate(this.basicTypeForm.limitPledgeTime,"yyyy-MM-dd")
                        }
                        //二手车先放有效期
                        if(this.basicTypeForm.limitPutTimeOld != null || this.basicTypeForm.limitPutTimeOld !=""){
                            this.basicTypeForm.limitPutTimeOld =formatDate(this.basicTypeForm.limitPutTimeOld,"yyyy-MM-dd")
                        }
                        //二手车后放有效期
                        if(this.basicTypeForm.limitPledgeTimeOld != null || this.basicTypeForm.limitPledgeTimeOld !=""){
                            this.basicTypeForm.limitPledgeTimeOld =formatDate(this.basicTypeForm.limitPledgeTimeOld,"yyyy-MM-dd")
                        }

                        let param = {
                            //当前登陆者是否拥用新车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                            isNewCarRole:this.isNewCarRole,
                            //当前登陆者是否拥用二手车角色权限:0否，1是(暂时测试给1、后续调整根据账户体系自动判断取值)
                            isOldCarRole:this.isOldCarRole,
                            isEnable: this.isEnable,

                            oldChannelId:this.basicTypeForm.oldChannelId,
                            channelCoopeCardealerTemp: {
                                channelId: this.basicTypeForm.channelId,
                                associAccount: this.basicTypeForm.customerManager,
                                associAccountOld: this.basicTypeForm.customerManagerOld,
                                cardealerType: "02",
                                status: this.channelStatus,
                                oldCarStatus: this.channelStatus,
                                createBy:"channelAdmin",
                            },
                            //渠道基本信息f
                            channelBaseInfoTemp: {
                                //渠道类型：直营车商
                                channelType:"02",
                                //主键
                                id:this.basicTypeForm.id,
                                //渠道代码
                                channelCode: this.basicTypeForm.channelCode,
                                //合作商全称
                                channelFullName:this.basicTypeForm.channelFullName.replace(/\s*!/g,""),
                                //上线日期
                                onlineDate:this.basicTypeForm.onlineDate,
                                //统一社会信用代码
                                socUniCrtCode:this.basicTypeForm.socUniCrtCode.replace(/\s*!/g,""),
                                //法人
                                legalPerson:this.basicTypeForm.legalPerson.replace(/\s*!/g,""),
                                //法人身份证号
                                legalPersonIdCard:this.basicTypeForm.legalPersonIdCard,
                                //法人电话
                                legalPersonTel:this.basicTypeForm.legalPersonTel,
                                //实际控制人
                                actualController:this.basicTypeForm.actualController.replace(/\s*!/g,""),
                                //实际控制人身份证号
                                actualControllerIdCard:this.basicTypeForm.actualControllerIdCard,
                                //实际控制人电话
                                actualControllerTel:this.basicTypeForm.actualControllerTel,
                                //管理员姓名
                                channelAdmin:this.basicTypeForm.channelAdmin.replace(/\s*!/g,""),
                                //管理员电话
                                channelAdminTel:this.basicTypeForm.channelAdminTel,
                                //管理员邮箱
                                channelAdminMail:this.basicTypeForm.channelAdminMail,
                                //管理员证件号码
                                channelAdminIdCard:this.basicTypeForm.channelAdminIdCard,
                                //渠道管理员详细地址
                                channelAdminAddress:this.basicTypeForm.channelAdminAddressDetail,
                                //注册地址详细地址
                                channelAddress:this.basicTypeForm.channelAddressDetail,
                                //办公地址详细地址
                                officeAddress:this.basicTypeForm.officeAddressDetail,
                                //地址经纬度
                                longitude:this.basicTypeForm.longitude,
                                latitude:this.basicTypeForm.latitude,
                                //经纬度范围
                                longitudeLatitudeRange:this.basicTypeForm.longitudeLatitudeRange.replace(/\s*/g,""),
                                //公司人数
                                companiesNumber:this.basicTypeForm.companiesNumber,
                                //车商性质
                                carDealType:this.basicTypeForm.cardealType,
                                //经营场所面积
                                businessArea:this.basicTypeForm.businessArea,
                                //汇款对象
                                paymentObject:this.basicTypeForm.paymentObject,
                                //注册日期
                                registrationDate:this.basicTypeForm.registrationDate,
                                //主营品牌
                                mainBrand:this.basicTypeForm.mainBrand,
                                //注册资金
                                registeredCapital:this.basicTypeForm.registeredCapital,
                                //实收资本
                                subscribedCapital:this.basicTypeForm.subscribedCapital,
                                //渠道归属
                                channelBelong:this.basicTypeForm.channelBelong,
                                //业务类型;新车/二手车
                                delete:this.basicTypeForm.businessType,
                                businessType:this.basicTypeForm.businessType.join(','),

                                //资产净值
                                netAssetValue:this.basicTypeForm.netAssetValue,
                                //经营年限
                                businessLife:this.basicTypeForm.businessLife,
                                //GPS厂商授权
                                delete:this.basicTypeForm.gpsVendorAuthor,
                                gpsVendorAuthor:this.basicTypeForm.gpsVendorAuthor.join(','),

                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //上一年度经营利润
                                operatingProfit:this.basicTypeForm.operatingProfit,
                                //GPS安装方式
                                //delete:this.basicTypeForm.gpsInstalMode,
                                gpsInstalMode:this.basicTypeForm.gpsInstalMode,

                                //业绩
                                achievement:this.basicTypeForm.achievement,
                                //利润率增长率
                                proGrowthRate:this.basicTypeForm.proGrowthRate,
                                //销售收入增长率
                                saleIncreaseRate:this.basicTypeForm.saleIncreaseRate,
                                //流动比率
                                currentRatio:this.basicTypeForm.currentRatio,
                                //资产负债率
                                assetLiabilityRatio:this.basicTypeForm.assetLiabilityRatio,
                                //车辆类型;新车;二手车
                                //delete:this.basicTypeForm.carType,
                                //carType:this.basicTypeForm.carType.join(','),
                                //经纬度是否控制;是/否
                                longitudeLatitudeSwitch:this.basicTypeForm.longitudeLatitudeSwitch,
                                longitudeLatitudeSwitchOld:this.basicTypeForm.longitudeLatitudeSwitchOld,
                                //业务人员关联车商;是/否
                                personRelCardealerSwitch:this.basicTypeForm.personRelCardealerSwitch,
                                //进件选择车商;是/否
                                choiceCardealerSwitch:this.basicTypeForm.choiceCardealerSwitch,
                                //层级最大数目
                                hierarchy:this.basicTypeForm.hierarchy,

                                channelStatus:this.channelStatusNewCar,
                                channelStatusOldCar:this.channelStatusOldCar,
                                accountInfo:this.basicTypeForm.accountInfo,

                            },
                            //管理员省市区
                            // channelAdminAddressValue:this.basicTypeForm.channelAdminAddressValue,
                            //注册地省市区
                            channelAddressValue:this.basicTypeForm.channelAddressValue,
                            //办公室省市区
                            //   officeAddressValue:this.basicTypeForm.officeAddressValue,
                            //新车授权区域
                            mainArea:this.basicTypeForm.mainArea,
                            //新车主营品牌
                            mainBrand:this.basicTypeForm.mainBrand,
                            //新车授权车型
                            mainCar:this.basicTypeForm.mainCar,

                            //新车授权区域
                            mainAreaOld:this.basicTypeForm.mainAreaOld,
                            //二手车授权车型
                            mainCarOld:this.basicTypeForm.mainCarOld,

                            //新车渠道风控信息
                            channelRiskInfoTemp: {
                                //业务类型：新车
                                businessType:"01",
                                qualityGrade:this.basicTypeForm.qualityGrade,
                                qualityStartDate:this.basicTypeForm.validDate[0],
                                qualityEndDate:this.basicTypeForm.validDate[1],

                                delete:this.basicTypeForm.carType,
                                carType:this.basicTypeForm.carType.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitch,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitch,
                                channelGrade:this.basicTypeForm.channelGrade,
                                channelDeposit:this.basicTypeForm.channelDeposit,
                                customerManager:this.basicTypeForm.customerManager,
                                accountMaxNum:this.basicTypeForm.accountMaxNum,
                            },
                            //二手车渠道风控信息
                            channelRiskInfoTempOld: {
                                //业务类型：二手车
                                businessType:"02",
                                qualityGrade:this.basicTypeForm.qualityGradeOld,
                                qualityStartDate:this.basicTypeForm.validDateOld[0],
                                qualityEndDate:this.basicTypeForm.validDateOld[1],

                                delete:this.basicTypeForm.carTypeOld,
                                carType:this.basicTypeForm.carTypeOld.join(','),
                                authRegionSwitch:this.basicTypeForm.authRegionSwitchOld,
                                authVehicleTypeSwitch:this.basicTypeForm.authVehicleTypeSwitchOld,
                                channelGrade:this.basicTypeForm.channelGradeOld,
                                channelDeposit:this.basicTypeForm.channelDepositOld,
                                customerManager:this.basicTypeForm.customerManagerOld,
                                accountMaxNum:this.basicTypeForm.accountMaxNumOld,
                            },
                            //新车渠道保证金信息
                            channelQuotaInfoTempList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPut,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPut,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPut,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidue,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"01",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledge,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledge,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledge,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTime,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTake,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidue,
                                }
                            ],

                            //二手车保证金信息
                            channelQuotaInfoTempOldList: [
                                //签放额度
                                {
                                    //额度类型：签放额度
                                    quotaType:"1",
                                    //业务类型:新车
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPutOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPutOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPutOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPutTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPutTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPutResidueOld,

                                },
                                //先放后抵押额度
                                {
                                    //额度类型：先放后抵押额度
                                    quotaType:"2",
                                    //业务类型
                                    businessType:"02",
                                    //是否控制额度
                                    quotaControlSwitch:this.basicTypeForm.isLimitPledgeOld,
                                    //额度金额
                                    quotaAmount:this.basicTypeForm.limitPledgeOld,
                                    //临时额度
                                    tempQuota:this.basicTypeForm.temporaryLimitPledgeOld,
                                    //有效期
                                    validityTermEnd:this.basicTypeForm.limitPledgeTimeOld,
                                    //占用额度
                                    occupiedQuota:this.basicTypeForm.limitPledgeTakeOld,
                                    //剩余额度
                                    surplusQuota:this.basicTypeForm.limitPledgeResidueOld,
                                }
                            ],
                        }
                        let blackParams={
                            //黑名单类型：直营车商
                            blacklistType:'05',
                            //黑名单名称
                            blackName:this.basicTypeForm.channelFullName,
                            //证件类型：统一社会信息代码
                            idType:'02',
                            //统一社会信用代码
                            idCode:this.basicTypeForm.socUniCrtCode,
                        }

                        blackCleack(blackParams).then(res => {
                            if (res.code === "0000") {
                                //黑名单判断字段:是
                                if(res.weatherBlackList === "1"){
                                    this.$Message.error("该车商已存在黑名单信息库中！");
                                    this.$parent.spinShow=false;
                                }else {
                                    if(this.basicTypeForm.id !=null && this.basicTypeForm.id !="" && this.basicTypeForm.id != undefined && this.OldStatus !="06"){
                                        updateChannel(param).then(res => {
                                            if (res.code === "0000") {
                                                this.basicTypeForm.id=res.data[0].id;
                                                this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                                //账户页面信息更新
                                                this.$parent.getAccountInfo(this.basicTypeForm.id);
                                                //回显业务员
                                                this.getAllSaleManByChannelId();

                                                this.$Message.success("提交成功");
                                                this.afs.closeTab(this);
                                                this.$router.push({path: '/directCarDealerList'});

                                                //基本信息处理
                                                this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                                this.$parent.setchannelId(res.data[0].id);
                                            //    this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                             //   this.getBaseTreeList(res.data[0].id);
                                                //this.getCarsTreeList(res.data[0].id);
                                                this.$parent.spinShow=false;
                                                //同步影像件到进件服务
                                                let fileId = this.basicTypeForm.id;
                                                let belongNo = this.basicTypeForm.channelId;
                                                this.syncFileToApply(fileId,belongNo)

                                            }
                                        }).catch(() => {
                                            this.$Message.error("操作失败");
                                            this.$parent.spinShow=false;
                                        });
                                    }else {
                                        addChannel(param).then(res => {
                                            if (res.code === "0000") {

                                                //基本信息处理
                                                this.basicTypeForm.id=res.data[0].id;
                                                this.basicTypeForm.oldChannelId=res.data[1].channelId;
                                                //账户页面信息更新
                                                this.$parent.getAccountInfo(this.basicTypeForm.id);
                                                //回显业务员
                                                this.getAllSaleManByChannelId();

                                                this.$Message.success("提交成功");
                                                this.afs.closeTab(this);
                                                this.$router.push({path: '/directCarDealerList'});

                                                this.getChannelInfo(res.data[0].id,this.$parent.belongChannelId);
                                                this.$parent.setchannelId(res.data[0].id);
                                             //   this.getMainAreaList(res.data[0].id,this.$parent.belongChannelId);
                                              //  this.getBaseTreeList(res.data[0].id);
                                                //this.getCarsTreeList(res.data[0].id);
                                                this.$parent.spinShow=false;
                                            }
                                        }).catch(() => {
                                            this.$Message.error("操作失败");
                                            this.$parent.spinShow=false;
                                        });
                                    }
                                }
                            }
                        }).catch(() => {
                            this.$Message.error("未知错误,请联系管理员！");
                            this.$parent.spinShow=false;
                        });
                    }else{
                        this.$parent.spinShow=false;
                        this.$Message.error('请补全基本信息');
                        for (let i in this.$refs.basicTypeForm.fields) {
                            if (this.$refs.basicTypeForm.fields[i].validateState === "error") {
                                this.$refs.basicTypeForm.fields[i].$el.scrollIntoView({
                                    block: "center",
                                    behavior: "smooth",
                                    inline: "center"
                                });
                                break;
                            }
                        }
                    }
                });
                console.log("submitBasicInfo");
            },

            OnlyCheckBlack(){
                //黑名单保存验证改渠道是否在黑名单信息表中
                if(this.basicTypeForm.socUniCrtCode == null || this.basicTypeForm.socUniCrtCode == undefined || this.basicTypeForm.socUniCrtCode ==""){
                    return false;
                }
                let blackParams={
                    //黑名单类型：直营车商
                    blacklistType:'05',
                    //黑名单名称
                    blackName:this.basicTypeForm.channelFullName,
                    //证件类型：统一社会信息代码
                    idType:'02',
                    //统一社会信用代码
                    idCode:this.basicTypeForm.socUniCrtCode,
                }
                blackCleack(blackParams).then(res => {
                    if (res.code === "0000") {
                        //黑名单判断字段:是
                        if(res.weatherBlackList === "1"){
                            this.$Message.error("该车商已存在黑名单信息库中！");
                            this.$parent.spinShow=false;
                        }else {
                            this.inBlackList=false;
                        }
                    }
                }).catch(() => {
                    this.$Message.error("未知错误,请联系管理员！");
                });
            },

            //验证黑名单
            checkBlack(){
                //黑名单保存验证改渠道是否在黑名单信息表中
                if(this.basicTypeForm.socUniCrtCode == null || this.basicTypeForm.socUniCrtCode == undefined || this.basicTypeForm.socUniCrtCode ==""){
                    return false;
                }
                let blackParams={
                    //黑名单类型：直营车商
                    blacklistType:'05',
                    //黑名单名称
                    blackName:this.basicTypeForm.channelFullName,
                    //证件类型：统一社会信息代码
                    idType:'02',
                    //统一社会信用代码
                    idCode:this.basicTypeForm.socUniCrtCode,
                }

                let param={
                    //社会统一信用代码
                    id:this.basicTypeForm.id,
                    socUniCrtCode:this.basicTypeForm.socUniCrtCode,
                    channelId:this.basicTypeForm.channelId,
                }

                blackCleack(blackParams).then(res => {
                    if (res.code === "0000") {
                        //黑名单判断字段:是
                        if(res.weatherBlackList === "1"){
                            this.$Message.error("该车商已存在黑名单信息库中！");
                            this.$parent.spinShow=false;
                        }else if(this.basicTypeForm.channelId != null && this.basicTypeForm.channelId != "" && this.basicTypeForm.channelId != undefined && this.typeCode== "01" ){
                            //不在黑名单中
                            this.inBlackList=false;
                            //查重
                            checkCooper(param).then(res => {
                                if (res.code === "0000") {
                                    if(res.data == "00" && !this.basicTypeForm.channelId){
                                        this.$Message.error("合作商与此车商已存在合作关系！");
                                    }
                                }
                            })
                        }
                    }
                }).catch(() => {
                    this.$Message.error("未知错误,请联系管理员！");
                });
            },
            blackChannel(){
                console.log("黑名单申请开始！");
                this.modalTitle="确认将合作商加入黑名单吗？";
                this.blackApplyVisible=true;
            },
            cancel() {
                //关闭黑名单窗口
                this.blackApplyVisible = false;
            },
            //申请加入黑名单
            join(){
                console.log("渠道黑名单保存开始！");
                console.log(this.blackApplyForm.blacklistReason);
                if (this.blackApplyForm.blacklistReason !="" && this.blackApplyForm.blacklistReason != null && this.blackApplyForm.blacklistReason != undefined) {
                    let params={
                        //黑名单类型：直营车商
                        blacklistType:'05',
                        //黑名单名称
                        blackName:this.basicTypeForm.channelFullName,
                        //证件类型：组织机构代码
                        idType:'02',
                        idCode:this.basicTypeForm.socUniCrtCode,
                        //黑名单原因
                        blacklistReason:this.blackApplyForm.blacklistReason,
                        carDealers: "02"
                    }
                    saveBlack(params).then(res => {
                        if (res.code === "0000") {
                            this.$Message.success("申请成功！");
                            this.cancel();
                        }
                    }).catch(() => {
                        this.$Message.error("申请失败！");
                    });
                }else {
                    this.$Message.error("请输入加入黑名单原因！");
                    return false;
                }

            },
            changeSelect() {

            },
            selectChange(){

            },
            readOnlyInfo(b) {
                let _this=this;
                this.$nextTick(()=>{
                    _this.typeCode=b;
                })
            },
            changeTypeCode(v){
                this.typeCode2=v;
            },

            changeSort(e) {
                this.searchForm.sort = e.key;
                this.searchForm.order = e.order;
                if (e.order === "normal") {
                    this.searchForm.order = "";
                }
                this.getAreaList();
            },

            showSelect(e) {
                this.selectList = e;
                this.selectCount = e.length;
            },

            changePage(v) {
                this.searchForm.pageNumber = v;
                this.getAreaList();
                this.clearSelectAll();
            },
            changePageSize(v) {
                this.searchForm.pageSize = v;
                this.getAreaList();
            },
            clearSelectAll() {
                this.$refs.table.selectAll(false);
            },


            //汇款对象数据字典
            paymentObjectType() {
                let param = {
                    type: "paymentObject"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.paymentObjectList = res.data;
                    }
                });
            },

            //从父页面获取合作商id
            getOldChannelId2(v){
                this.basicTypeForm.oldChannelId=v;
                this.basicTypeForm.channelId=v;
            },

            ShowSaleMan(){
               this.showSaleMans=true;
            },

            //审核页面控制经纬度显示开关
            showRange(){
                this.showRangeSwitch=true;
            },

            //控制二手车是否禁用
            carBusinessType(){
                console.log("选择车辆类型")
                console.log(this.basicTypeForm.businessType)
                console.log("合作商信息"+this.basicTypeForm)
              //  this.$parent.queryAllAreaList(this.basicTypeForm.channelId);
               let type = this.basicTypeForm.businessType;
                if(type.indexOf("01") > -1 && type.indexOf("02") > -1){
                    //新车二手车全部打开
                    this.showGroupNewCar="1";
                    this.showGroupOldCar="1";
                    //新车权限
                    this.isNewCarRole="1"
                    //二手车权限
                    this.isOldCarRole="1"

                }else if(type.indexOf("01") > -1 && type.indexOf("02") == -1){
                        //新车打开
                        this.showGroupNewCar="1";
                        this.showGroupOldCar="0";
                        //新车权限
                        this.isNewCarRole="1"
                        //二手车权限
                        this.isOldCarRole="0"
                }else if(type.indexOf("01") == -1 && type.indexOf("02") > -1){
                        //二手车打开
                        this.showGroupOldCar="1";
                        this.showGroupNewCar="0";
                        //新车权限
                        this.isNewCarRole="0"
                        //二手车权限
                        this.isOldCarRole="1"

                }else{
                    this.showGroupNewCar="0";
                    this.showGroupOldCar="0";
                    //新车权限
                    this.isNewCarRole="0"
                    //二手车权限
                    this.isOldCarRole="0"
                }

                if(this.newCarRole == '0'){
                    this.isNewCarRole='0'
                }
                if(this.oldCarRole=='0'){
                    this.isOldCarRole='0'
                }

                let param ={
                    newCarRole:this.isNewCarRole,
                    oldCarRole:this.isOldCarRole
                }
                vueEvent.$emit('to-directCardealerAccountInfo',param)
            },

            //审核用的方法
            setChannlId(v){
                this.basicTypeForm.channelId=v;
                /*this.getAreaByChannelId(v,"01");
                this.getAreaByChannelId(v,"02");*/
                this.getChannelInfoById();
            },

            //根据合作商id查询相关的信息
            getChannelInfoById(v){
                let id = this.basicTypeForm.channelId;
                console.log(id,"所选渠道id")
                this.$parent.belongChannelId=id;
                console.log(this.basicTypeForm.carType)
                console.log(this.basicTypeForm.carTypeOld)
                if(id=="" || id==undefined || id== null){
                    /*this.formValidate.salesMan=[];
                    this.personRelCardealerSwitch="0";*/
                    return;
                };
                //获取当前合作商下面的所有业务员
                if(v != -1){
                    this.basicTypeForm.customerManagerOld="";
                    this.basicTypeForm.customerManager="";
                    this.basicTypeForm.carType=[];
                    this.basicTypeForm.carTypeOld=[];
                }
                console.log(this.basicTypeForm.carType)
                console.log(this.basicTypeForm.carTypeOld)
                console.log("-----")
                let newCar ="01";
                let oldCar ="02";
                getAllSaleManByChannelId(id,newCar).then(res => {
                    if (res.code === "0000") {
                        this.salesManData=res.data;
                    }
                });
                getAllSaleManByChannelId(id,oldCar).then(res => {
                    if (res.code === "0000") {
                        this.salesManDataOld=res.data;
                    }
                });
                getChannelBasicInfoById(id).then(res => {
                    if (res.code === "0000") {
                        let paymentObject = res.data[0].paymentObject;
                        if(paymentObject == "0"){
                            //收款对象为主体,账号信息可选
                            this.accountSwitch="0";
                        }else{
                            //收款账号为车商,账号信息默认为有并且不可变
                            this.accountSwitch="1";
                            this.basicTypeForm.accountInfo = "1"
                        }
                        let type =  res.data[0].businessType;
                        console.log(type,"业务类型")
                        console.log(res.data[0])
                        if(type.indexOf("01") > -1 && type.indexOf("02") > -1){
                            //新车二手车都可选
                            if(res.data[0].channelStatus=="00"&&res.data[0].channelStatusOldCar=="00"){
                                this.oldCar=false;
                                this.newCar=false;
                            }else if(res.data[0].channelStatus=="00"&&res.data[0].channelStatusOldCar!="00"){
                                this.newCar=false;
                                this.oldCar=true;
                            }else if(res.data[0].channelStatus!="00"&&res.data[0].channelStatusOldCar=="00"){
                                this.oldCar=false;
                                this.newCar=true;
                            }

                        }else if(type.indexOf("01") > -1 && type.indexOf("02") == -1){
                                //新车打开
                                this.oldCar=true;
                                this.newCar=true;
                                this.basicTypeForm.businessType=["01"];

                                //新车打开
                                this.showGroupNewCar="1";
                                this.showGroupOldCar="0";
                                //新车权限
                                this.isNewCarRole="1"
                                //二手车权限
                                this.isOldCarRole="0"
                        }else if(type.indexOf("02") > -1 && type.indexOf("01") == -1){
                                //二手车打开
                                this.oldCar=true;
                                this.newCar=true;
                                this.basicTypeForm.businessType=["02"];
                                //二手车打开
                                this.showGroupOldCar="1";
                                this.showGroupNewCar="0";
                                //新车权限
                                this.isNewCarRole="0"
                                //二手车权限
                                this.isOldCarRole="1"
                            }
                    }
                    this.carBusinessType();
                });

                //获取所选的渠道车辆类型
                getChannelRiskInfoById(id).then(resRisk => {
                    if (resRisk.code === "0000") {
                        let str = JSON.stringify(resRisk.data);
                        let channelRiskInfoTemp = JSON.parse(str);
                        channelRiskInfoTemp.forEach((item, i) => {
                            //新车
                            if (item.businessType == "01") {
                                let carType1 = item.carType.split(',');
                                console.log(carType1)
                                console.log("-------------------")
                                if (carType1.indexOf("1") == -1) {
                                    this.businessTypeSwitch0 = "0"
                                }
                                if (carType1.indexOf("2") == -1) {
                                    this.businessTypeSwitch1 = "1"
                                }
                                if (carType1.indexOf("3") == -1) {
                                    this.businessTypeSwitch2 = "2"
                                }
                                if (carType1.indexOf("4") == -1) {
                                    this.businessTypeSwitch3 = "3"
                                }
                            } else {
                                let carType2 = item.carType.split(',');
                                if (carType2.indexOf("1") == -1) {
                                    this.carTypeSwitch0 = "0"
                                }
                                if (carType2.indexOf("2") == -1) {
                                    this.carTypeSwitch1 = "1"
                                }
                                if (carType2.indexOf("3") == -1) {
                                    this.carTypeSwitch2 = "2"
                                }
                                if (carType2.indexOf("4") == -1) {
                                    this.carTypeSwitch3 = "3"
                                }
                            }
                        })

                    }
                })
                //获取所选合作商的授权区域
                this.$parent.queryAllAreaList(id);
            },

            //账户页面的提交
            submitToCase(){
                let channelId = this.basicTypeForm.id;
                getChannelBasicInfoById(channelId).then(res => {
                    if (res.code === "0000") {
                        this.basicTypeForm.id = res.data[0].id;
                        this.basicTypeForm.channelCode = res.data[0].channelCode;
                        this.basicTypeForm.channelFullName = res.data[0].channelFullName;
                        this.basicTypeForm.onlineDate = res.data[0].onlineDate;
                        this.basicTypeForm.socUniCrtCode = res.data[0].socUniCrtCode;
                        this.basicTypeForm.legalPerson = res.data[0].legalPerson;
                        this.basicTypeForm.legalPersonIdCard = res.data[0].legalPersonIdCard;
                        this.basicTypeForm.legalPersonTel = res.data[0].legalPersonTel;
                        this.basicTypeForm.cardealType = res.data[0].carDealType;
                        this.basicTypeForm.accountInfo = res.data[0].accountInfo;
                        this.basicTypeForm.actualController = res.data[0].actualController;
                        this.basicTypeForm.actualControllerTel = res.data[0].actualControllerTel;
                        this.basicTypeForm.actualControllerIdCard = res.data[0].actualControllerIdCard;
                        this.basicTypeForm.channelAdmin = res.data[0].channelAdmin;
                        this.basicTypeForm.channelAdminTel = res.data[0].channelAdminTel;
                        this.basicTypeForm.channelAdminMail = res.data[0].channelAdminMail;
                        this.basicTypeForm.longitude = res.data[0].longitude;
                        this.basicTypeForm.latitude = res.data[0].latitude;
                        this.basicTypeForm.longitudeLatitudeRange = res.data[0].longitudeLatitudeRange;
                        this.basicTypeForm.businessArea = res.data[0].businessArea;
                        this.basicTypeForm.paymentObject = res.data[0].paymentObject;
                        this.basicTypeForm.registrationDate = res.data[0].registrationDate;
                        this.basicTypeForm.channelBelong = res.data[0].channelBelong;
                        this.basicTypeForm.hierarchy = res.data[0].hierarchy;
                        this.basicTypeForm.channelAdminIdCard = res.data[0].channelAdminIdCard;
                        this.OldStatus = res.data[0].channelStatus;
                        if (res.data[0].registeredCapital) {
                            this.basicTypeForm.registeredCapital = res.data[0].registeredCapital + "";
                        }
                        if (res.data[0].assetLiabilityRatio) {
                            this.basicTypeForm.assetLiabilityRatio = res.data[0].assetLiabilityRatio + "";
                        }
                        if (res.data[0].businessLife) {
                            this.basicTypeForm.businessLife = res.data[0].businessLife + "";
                        }
                        if (res.data[0].operatingProfit) {
                            this.basicTypeForm.operatingProfit = res.data[0].operatingProfit + "";
                        }
                        if (res.data[0].achievement) {
                            this.basicTypeForm.achievement = res.data[0].achievement + "";
                        }
                        if (res.data[0].subscribedCapital) {
                            this.basicTypeForm.subscribedCapital = res.data[0].subscribedCapital + "";
                        }
                        if (res.data[0].netAssetValue) {
                            this.basicTypeForm.netAssetValue = res.data[0].netAssetValue + "";
                        }
                        if (res.data[0].proGrowthRate) {
                            this.basicTypeForm.proGrowthRate = res.data[0].proGrowthRate + "";
                        }
                        if (res.data[0].saleIncreaseRate) {
                            this.basicTypeForm.saleIncreaseRate = res.data[0].saleIncreaseRate + "";
                        }
                        if (res.data[0].currentRatio) {
                            this.basicTypeForm.currentRatio = res.data[0].currentRatio + "";
                        }
                        this.basicTypeForm.longitudeLatitudeSwitch = res.data[0].longitudeLatitudeSwitch;
                        this.basicTypeForm.longitudeLatitudeSwitchOld = res.data[0].longitudeLatitudeSwitchOld;
                        this.basicTypeForm.personRelCardealerSwitch = res.data[0].personRelCardealerSwitch;
                        this.basicTypeForm.choiceCardealerSwitch = res.data[0].choiceCardealerSwitch;
                        this.basicTypeForm.businessType = res.data[0].businessType.split(',');
                        this.basicTypeForm.gpsVendorAuthor = res.data[0].gpsVendorAuthor.split(',');
                        this.basicTypeForm.gpsInstalMode = res.data[0].gpsInstalMode;

                        let channelAddressValue = [];
                        channelAddressValue[0] = res.data[0].channelProvince;
                        channelAddressValue[1] = res.data[0].channelCity;
                        this.basicTypeForm.channelAddressValue = channelAddressValue;
                        this.basicTypeForm.channelAddressDetail = res.data[0].channelAddress;

                        //开票信息
                        this.invoiceForm.taxpayerType = res.data[0].taxpayerType;
                        this.invoiceForm.taxpayerIdNumber = res.data[0].taxpayerIdNumber;
                        this.invoiceForm.invoiceTelNumber = res.data[0].invoiceTelNumber;
                        this.invoiceForm.invoiceAddress = res.data[0].invoiceAddress;
                        this.invoiceForm.openingBank = res.data[0].openingBank;
                        this.invoiceForm.bankAccount = res.data[0].bankAccount;
                        this.invoiceForm.taxRate = res.data[0].taxRate;

                        this.basicSubmit();
                        console.log("**")
                       /* let channelInfo = this.basicTypeForm;
                        this.$emit('passmsg', channelInfo)*/
                    }
                });


            },

            //控制签放额度是否必输
            checkLimitPut(){
                var reg = /^\d+(\.\d+)?$/;
                let isLimitPut = this.basicTypeForm.isLimitPut;
                let limitPut = this.basicTypeForm.limitPut;
                if(isLimitPut=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPut' && (limitPut==""||limitPut==null||limitPut==undefined)) {
                            e.validateMessage="";
                        }
                    })
                };

                let isLimitPledge = this.basicTypeForm.isLimitPledge;
                let limitPledge = this.basicTypeForm.limitPledge;
                if(isLimitPledge=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPledge'&& (limitPledge==""||limitPledge==null||limitPledge==undefined)) {
                            e.validateMessage="";
                        }
                    })
                }

                let isLimitPutOld = this.basicTypeForm.isLimitPutOld;
                let limitPutOld = this.basicTypeForm.limitPutOld;
                if(isLimitPutOld=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPutOld' && (limitPutOld==""||limitPutOld==null||limitPutOld==undefined)) {
                            e.validateMessage="";
                        }
                    })
                };

                let isLimitPledgeOld = this.basicTypeForm.isLimitPledgeOld;
                let limitPledgeOld = this.basicTypeForm.limitPledgeOld;
                if(isLimitPledgeOld=="0"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'limitPledgeOld'&& (limitPledgeOld==""||limitPledgeOld==null||limitPledgeOld==undefined)) {
                            e.validateMessage="";
                        }
                    })
                }
            },

            getAllSaleManByChannelId(){
                //获取当前合作商下面的所有业务员
                let newCar ="01";
                let oldCar ="02";
                getAllSaleManByChannelId(this.basicTypeForm.channelId,newCar).then(res => {
                    if (res.code === "0000") {
                        this.salesManData=res.data;
                    }
                });
                getAllSaleManByChannelId(this.basicTypeForm.channelId,oldCar).then(res => {
                    if (res.code === "0000") {
                        this.salesManDataOld=res.data;
                    }
                });
            },

            //优质等级控制有效期
            checkQualityGrade(){
                let qualityGrade = this.basicTypeForm.qualityGrade;
                let validDate = this.basicTypeForm.validDate;
                if(qualityGrade=="5"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'validDate') {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };

                let qualityGradeOld = this.basicTypeForm.qualityGradeOld;
                let validDateOld = this.basicTypeForm.validDateOld;
                if(qualityGradeOld=="5"){
                    this.$refs.basicTypeForm.fields.forEach(function (e) {
                        if (e.prop == 'validDateOld') {
                            e.validateMessage="";
                            e.validateState="";
                        }
                    })
                };
            },

            //优质等级字典处理
            qualityGradeType() {
                let param = {
                    type: "qualityGrade"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.qualityGradeList = res.data;
                    }
                });
            },
            //GPS厂商字典处理
            gpsSupplier() {
                let param = {
                    type: "gpsSupplier"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.gpsvendorauthorList = res.data;
                    }
                });
            },
            //GPS安装方式字典处理
            gpsinstalmode() {
                let param = {
                    type: "gpsinstalmode"
                }
                getDictDataByType(param.type).then(res => {
                    if (res.code === "0000") {
                        this.gpsinstalmodeList = res.data;
                    }
                });
            },
            // 查询数据字典
            initDataDic() {
                this.paymentObjectType();
                this.qualityGradeType();
                this.gpsSupplier();
                this.gpsinstalmode();
            },

            // 地图弹窗
            openMapModal(){
                let address = this.mapLocation
                let detial = this.basicTypeForm.channelAddressDetail
                console.log(address,detial);
                this.mapModal = true;
            },
            // 确认经纬度
            confirmLngLat(data){
                console.log('选择的经纬度为：',data);
                this.mapModal = false;
                if(data && data.latitude && data.longitude){
                    this.basicTypeForm.latitude=data.latitude.toString();
                    this.basicTypeForm.longitude=data.longitude.toString();
                }
            },
            closeMapModel(){
                this.mapModal = false;
            },
            onBlurLocation(){
                this.$refs.basicTypeForm.validateField('channelAddressDetail',(e) => {
                    console.log('channelAddressDetail',e,typeof e);
                    if(!e) {
                        this.mapAddress = this.basicTypeForm.channelAddressDetail;
                        if(this.mapLocation) {
                            this.canOpenMapModal = true;
                            this.openMapModal();
                        }
                    }

                })

            },
            onChangeLocation(value, data){
                console.log(value, data);
                let str='';
                if(Array.isArray(data) && data.length){
                    data.forEach(ele =>{
                        str += (ele.label ? ele.label : '');
                    })
                    this.mapLocation = str;
                    if(this.mapAddress) {
                        this.canOpenMapModal = true;
                    }
                }
            },

            getChannelId(v){
                this.basicTypeForm.channelId=v;
                this.getChannelInfoById;
            },

            getDetailAddress1(registeredProvince,registeredCity){
                if(registeredProvince!="" && registeredCity!=""){
                    let province="";
                    let city="";
                    for(let i=0;i<this.province.length;i++){
                        if(this.province[i].value==registeredProvince){
                            province=this.province[i].label;
                        }
                    };
                    for(let i=0;i<this.city.length;i++){
                        if(this.city[i].value==registeredCity){
                            city=this.city[i].label;
                        }
                    };
                    this.mapLocation=province+city;
                }
            },

            checkdate(){
                if(this.basicTypeForm.temporaryLimitPut && this.basicTypeForm.temporaryLimitPut !=0){
                    this.newCarDate1 = '1';
                }else{
                    this.newCarDate1 = '0';
                }
                if(this.basicTypeForm.temporaryLimitPledge && this.basicTypeForm.temporaryLimitPledge !=0){
                    this.newCarDate2 = '1';
                }else{
                    this.newCarDate2 = '0';
                }
                if(this.basicTypeForm.temporaryLimitPutOld && this.basicTypeForm.temporaryLimitPutOld !=0){
                    this.oldCarDate1 = '1';
                }else{
                    this.oldCarDate1 = '0';
                }
                if(this.basicTypeForm.temporaryLimitPledgeOld && this.basicTypeForm.temporaryLimitPledgeOld !=0){
                    this.oldCarDate2 = '1';
                }else{
                    this.oldCarDate2 = '0';
                }
            },
            changeGPS(){
                if(this.basicTypeForm.gpsVendorAuthor.indexOf("all") > -1){
                    this.basicTypeForm.gpsVendorAuthor=["all"]
                }
            },
            //同步影像件到进件服务
            syncFileToApply(fileId,belongNo){
                syncFileToApply(fileId,belongNo).then(result => {
                })
            },
             getBusinessTypeRole(newCar,oldCar){
                this.disableBusinessType = true;
                this.newCarRole = newCar;
                this.oldCarRole = oldCar;
            },
            getBusinessTypeRoleForCheck(newCar,oldCar){
                this.newCarRole = newCar;
                this.oldCarRole = oldCar;
            },
            businessTypeControl(role) {
                if ('newCarRole' == role) {
                    //置灰二手车
                    this.businessTypeControlValue='02'
                }else if('oldCarRole' == role){
                    //置灰新车
                    this.businessTypeControlValue='01'
                }
            },


            //主营品牌模糊查询
            search() {
                this.isSearch = true;
            },
            closeSearch() {
                this.isSearch = false;
            },

            getSelectedAreaNodes(node) {
                let arr = [];
                Array.isArray(node) && node.forEach(e => {
                    let isParent = e.isParent && e.children && e.children.length > 0 ? "1" : "0";
                    let halfCheck = e.check_Child_State === 1;
                    arr.push(e.value + "," + e.title + "," + isParent + "," + e.upperCode + "," + halfCheck);
                });
                return arr;
            },
            getSelectedBrandNodes(node) {
                let arr = [], allSelectChildNode = {};
                Array.isArray(node) && node.forEach(e => {
                    let halfCheck = e.check_Child_State === 1;

                    if (e.spellCode === undefined) {
                        e.spellCode = " ";
                    }
                    arr.push(e.code + "," + e.title + "," + halfCheck + "," + e.carLevel + "," + e.spellCode);
                })
                return arr;
            },
            getSelectedCarNodes(node) {
                let arr = [], allSelectChildNode = {};
                Array.isArray(node) && node.forEach(e => {
                    let halfCheck = e.check_Child_State === 1;

                    if (e.spellCode === undefined) {
                        e.spellCode = " ";
                    }
                    arr.push(e.code + "," + e.title + "," + e.upperCode + "," + halfCheck + "," + e.carLevel + "," + e.spellCode + "," + e.carType);
                });
                return arr;
            },

            getCheckedNodes(ref) {
                if (ref) {
                    return this.$refs[ref].getCheckedNodes()
                } else {
                    return this.$refs.mainBrand.getCheckedNodes()
                }
            },

            //授权区域授权车型
            closeSearchNewCar() {
                this.isCarTreeSearch = false;
                this.isAreasDataSearch = false;
            },
            searchCarsData() {
                this.isCarTreeSearch = true;
            },
            searchMainArea() {
                this.isAreasDataSearch = true;
            },

            closeSearchOldCae() {
                this.isCarsDataOldSearch = false;
                this.isAreasDataOldSearch = false;
            },

            searchMainCarOld() {
                this.isCarsDataOldSearch = true;
            },
            searchAreasOld() {
                this.isAreasDataOldSearch = true;
            },

            handelBrands(selectedBrandNodes){
                let brandIds =[];
                try {
                    selectedBrandNodes.forEach(e => {
                        let brandDetail = e.split(',');
                        if (brandDetail[3] == '0' && brandDetail[2] == 'false') {
                            brandIds.push(e);
                            throw Error();
                        } else if (brandDetail[3] == '0' && brandDetail[2] == 'true') {
                            brandIds.push(e);
                        } else if (brandDetail[3] == '1' && brandDetail[2] == 'false') {
                            brandIds.push(e);
                        } else if (brandDetail[3] == '1' && brandDetail[2] == 'true') {
                            brandIds.push(e)
                            let upCode = e.split(',')[0];
                            selectedBrandNodes.forEach(m => {
                                let brandDetail2 = m.split(',');
                                let downCode = brandDetail2[0];
                                if (brandDetail2[3] == '2' && downCode.substring(0, downCode.length - 2) == upCode) {
                                    brandIds.push(m);
                                }
                            })
                        }
                    })
                } catch (e) {
                }
                return brandIds;
            },

            handelMainCar(selectedBrandNodes){
                let brandIds =[];
                try {
                    selectedBrandNodes.forEach(e => {
                        let brandDetail = e.split(',');
                        if (brandDetail[4] == '0' && brandDetail[3] == 'false') {
                            brandIds.push(e);
                            throw Error();
                        } else if (brandDetail[4] == '0' && brandDetail[3] == 'true') {
                            brandIds.push(e);
                        } else if (brandDetail[4] == '1' && brandDetail[3] == 'false') {
                            brandIds.push(e);
                        } else if (brandDetail[4] == '1' && brandDetail[3] == 'true') {
                            brandIds.push(e)
                            let upCode = e.split(',')[0];
                            selectedBrandNodes.forEach(m => {
                                let brandDetail2 = m.split(',');
                                let downCode = brandDetail2[2];
                                if (brandDetail2[4] == '2' && downCode == upCode) {
                                    brandIds.push(m);
                                }
                            })
                        }
                    })
                } catch (e) {
                }
                return brandIds;
            },
            cancelSearch(){
                // 取消请求
                this.source.cancel("取消重复请求！");
            },
            remoteModel(){
                if(this.source){
                    this.cancelSearch();
                }
                if(this.searchName&&this.searchName!='') {
                    let params = {entInfo: this.searchName, type: "companyFuzzy"};
                    this.loadModel = true;
                    this.loadModel = true;
                    this.searchModelList = [];
                    this.source = this.getSource();
                    searchEnterpriseName(params,this.source).then(res => {
                        let isExit = false;
                        if (res.code == "0000"&&res.data) {
                            this.searchModelList = res.data;
                            this.searchModelList.forEach(item => {
                                if (item.entName == this.searchName) {
                                    isExit = true;
                                }
                            })
                        }
                        if (!isExit) {
                            this.searchModelList.push({
                                entName: this.searchName,
                            });
                        }
                        this.loadModel = false;
                    }).catch(() => {
                        this.searchModelList.push({
                            entName: this.searchName,
                        });
                        this.loadModel = false;
                    });
                }
            },
            searchSelectModel(value){
                if(value){
                    this.basicTypeForm.socUniCrtCode="";
                    this.basicTypeForm.channelFullName="";
                    this.basicTypeForm.legalPerson="";
                    this.searchModelList.forEach(item => {
                        if (item.entName+item.frName == value) {
                            this.basicTypeForm.channelFullName=item.entName;
                            this.basicTypeForm.socUniCrtCode = item.ucCode;
                            this.basicTypeForm.legalPerson = item.frName;
                        }
                    })
                }else {
                    this.searchModelList=[];
                }
            },
        },

        created() {
        },
        mounted() {
            this.init();
        },
    }
</script>

<style>
    .basis-info .ivu-icon-ios-search:before{
        font-size: 18px;
        /*position: absolute;*/
        right: 5px;
    }
    .basis-info .ivu-input[disabled]{
        color: black;
    }
    .basis-info span.ivu-select-selected-value{
        color: black;
    }
    .basis-info .ivu-input-number-input[disabled]{
        color:black;
    }

    .s1 .ivu-select-dropdown-list {
            height:200px;
        }


</style>

