@font-face {font-family: "iconfont";
  src: url('iconfont.eot?t=1588001957661'); /* IE9 */
  src: url('iconfont.eot?t=1588001957661#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAJ0AAsAAAAABjAAAAIoAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcAowSgE2AiQDCAsGAAQgBYRtBzkbeQURBfTIfiRkZzTX+fxqeC0RtSk2jY0z8fA0Vnt/ZlY1nRh4OojiTVSjhkqClC8UStconsi7tWmHe6MiFEtyaPBd/kcedrgQOtx2ukpVV1XIZuksswHX1E+gB1kd0FwigqKSIwEHNh1YJz6NbBOa8YaxC1zCcwJNC3aKI/mxc+oKa10gntUPb6vnIorC8nWhWnO0iHcq6ulNGm/D78cv61EnqWRW06kHR+858NXYRfk6u7rmE6IEdLqCjJ0oxLnazAlZMC5rZooV9lUffDWzme8We3UI9tdZtW0wB7PBV+OeECCBEk/W0BnFg1SNp9n9e3sfPni+cX7cGbfPZ+t7djmAf5Y2UgLBv++Hqzd72f96q4APn3mhvejno76iN/gBqp4DRddYrlRUVa0vqY1am9DUxAwODH0Za52crRDqJueUQfN8iqxujSzsTlS07EZVHbofTTsUp1tG2FqUBrZ7AYS+N5Ku98j6PmRhf6iY+qOqHxlNF2J0ZcvmeKwOM2pBB+YCtaXBOx5RUf0Gy7nTnNWEggPyVMchDqJivsABeY4d07FMRDx4ph5y8Bp2HcHI1KCVoBIZ0zD0dW8KLPUL5TCGNIEcYFyAmkUG3h8vVOnzG6g062jc0lKTPkBsUk8PYoGoB1kYh14t98JnclRKCOEBj5EeyMEi1OkQMNYvaiBLBKoRqVEqtEf5vsZgfXP/ARWsA8uWwp6Z9V7XeigvNDehTohjsQAAAA==') format('woff2'),
  url('iconfont.woff?t=1588001957661') format('woff'),
  url('iconfont.ttf?t=1588001957661') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  url('iconfont.svg?t=1588001957661#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconxiangyoujiantou:before {
  content: "\e662";
}

