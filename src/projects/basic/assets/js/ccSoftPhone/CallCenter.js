/**
 * ---------------------- SDK VERSION
 * */
var ccsdk = "2022.1.0.4";

import {UIT} from "./language";


import * as $ from "jquery";
if(!window.jQuery){
    window.jQuery = $;
}

// 状态管理
export var CallCenterStatus;
let SDK_action, SDK_state, SDK_events;
/**
 * ---------------------- STATUS EXECUTE
 */
(function () {
  var a = {
    Result: { SUCCEEDED: 1, NOTRANSITION: 2, CANCELLED: 3, PENDING: 4 },
    Error: { INVALID_TRANSITION: 100, PENDING_TRANSITION: 200, INVALID_CALLBACK: 300 },
    WILDCARD: "*",
    ASYNC: "async",
    create: function (g, h) {
      var j = (typeof g.initial == "string") ? { state: g.initial } : g.initial;
      var f = g.terminal || g["final"];
      var e = h || g.target || {};
      var m = g.events || [];
      var i = g.callbacks || {};
      var c = {};
      var k = {};
      var l = function (o) {
        var q = (o.from instanceof Array) ? o.from : (o.from ? [o.from] : [a.WILDCARD]);
        c[o.name] = c[o.name] || {};
        for (var p = 0; p < q.length; p++) {
          k[q[p]] = k[q[p]] || [];
          k[q[p]].push(o.name);
          c[o.name][q[p]] = o.to || q[p];
        }
      };
      if (j) {
        j.event = j.event || "startup";
        l({ name: j.event, from: "none", to: j.state });
      }
      for (var d = 0; d < m.length; d++) {
        l(m[d]);
      }
      for (var b in c) {
        if (c.hasOwnProperty(b)) {
          e[b] = a.buildEvent(b, c[b]);
        }
      }
      for (var b in i) {
        if (i.hasOwnProperty(b)) {
          e[b] = i[b];
        }
      }
      e.current = "none";
      e.is = function (n) {
        return (n instanceof Array) ? (n.indexOf(this.current) >= 0) : (this.current === n);
      };
      e.can = function (n) {
        return !this.transition && (c[n].hasOwnProperty(this.current) || c[n].hasOwnProperty(a.WILDCARD));
      };
      e.cannot = function (n) {
        return !this.can(n);
      };
      e.transitions = function () {
        return k[this.current];
      };
      e.isFinished = function () {
        return this.is(f);
      };
      e.error = g.error || function (p, t, s, o, n, r, q) {
        throw q || r;
      };
      if (j && !j.defer) {
        e[j.event]();
      }
      return e;
    },
    firstToUpper: function (b) {
      return b.charAt(0).toUpperCase() + b.substr(1);
    },
    doCallback: function (g, d, c, i, h, b) {
      if (d) {
        try {
          return d.apply(g, [c, i, h].concat(b));
        } catch (f) {
          return g.error(c, i, h, b, a.Error.INVALID_CALLBACK, "an exception occurred in a caller-provided callback function", f);
        }
      }
    },
    beforeAnyEvent: function (d, c, f, e, b) {
      return a.doCallback(d, d["onbeforeevent"], c, f, e, b);
    },
    afterAnyEvent: function (d, c, f, e, b) {
      return a.doCallback(d, d["onafterevent"] || d["onevent"], c, f, e, b);
    },
    leaveAnyState: function (d, c, f, e, b) {
      return a.doCallback(d, d["onleavestate"], c, f, e, b);
    },
    enterAnyState: function (d, c, f, e, b) {
      return a.doCallback(d, d["onenterstate"] || d["onstate"], c, f, e, b);
    },
    changeState: function (d, c, f, e, b) {
      return a.doCallback(d, d["onchangestate"], c, f, e, b);
    },
    beforeThisEvent: function (d, c, f, e, b) {
      return a.doCallback(d, d["onbefore" + this.firstToUpper(c)], c, f, e, b);
    },
    afterThisEvent: function (d, c, f, e, b) {
      return a.doCallback(d, d["onafter" + this.firstToUpper(c)] || d["on" + this.firstToUpper(c)], c, f, e, b);
    },
    leaveThisState: function (d, c, f, e, b) {
      return a.doCallback(d, d["onleave" + this.firstToUpper(f)], c, f, e, b);
    },
    enterThisState: function (d, c, f, e, b) {
      return a.doCallback(d, d["onenter" + this.firstToUpper(e)] || d["on" + this.firstToUpper(e)], c, f, e, b);
    },
    beforeEvent: function (d, c, f, e, b) {
      if ((false === a.beforeThisEvent(d, c, f, e, b)) || (false === a.beforeAnyEvent(d, c, f, e, b))) {
        return false;
      }
    },
    afterEvent: function (d, c, f, e, b) {
      a.afterThisEvent(d, c, f, e, b);
      a.afterAnyEvent(d, c, f, e, b);
    },
    leaveState: function (f, e, h, g, d) {
      var c = a.leaveThisState(f, e, h, g, d), b = a.leaveAnyState(f, e, h, g, d);
      if ((false === c) || (false === b)) {
        return false;
      } else {
        if ((a.ASYNC === c) || (a.ASYNC === b)) {
          return a.ASYNC;
        }
      }
    },
    enterState: function (d, c, f, e, b) {
      a.enterThisState(d, c, f, e, b);
      a.enterAnyState(d, c, f, e, b);
    },
    buildEvent: function (b, c) {
      return function () {
        var h = this.current;
        var g = c[h] || c[a.WILDCARD] || h;
        var e = Array.prototype.slice.call(arguments);
        if (this.transition) {
          return this.error(b, h, g, e, a.Error.PENDING_TRANSITION, "event " + b + " inappropriate because previous transition did not complete");
        }
        if (this.cannot(b)) {
          return this.error(b, h, g, e, a.Error.INVALID_TRANSITION, "event " + b + " inappropriate in current state " + this.current);
        }
        if (false === a.beforeEvent(this, b, h, g, e)) {
          return { result: a.Result.CANCELLED, reason: "cancelled" };
        }
        if (h === g) {
          a.afterEvent(this, b, h, g, e);
          return { result: a.Result.NOTRANSITION, reason: "notransition" };
        }
        var f = this;
        this.transition = function () {
          f.transition = null;
          f.current = g;
          a.enterState(f, b, h, g, e);
          a.changeState(f, b, h, g, e);
          a.afterEvent(f, b, h, g, e);
          return { result: a.Result.SUCCEEDED, reason: "succeeded" };
        };
        this.transition.cancel = function () {
          f.transition = null;
          a.afterEvent(f, b, h, g, e);
        };
        var d = a.leaveState(this, b, h, g, e);
        if (false === d) {
          this.transition = null;
          return a.Result.CANCELLED;
        } else {
          if (a.ASYNC === d) {
            return { result: a.Result.PENDING, reason: "pending" };
          } else {
            if (this.transition) {
              return this.transition();
            }
          }
        }
      };
    }
  };
  if (typeof exports !== "undefined") {
    if (typeof module !== "undefined" && module.exports) {
      exports = module.exports = a;
    }
    exports.CallCenterStateMachine = a;
  } else {
    if (typeof define === "function" && define.amd) {
      define(function (b) {
        return a;
      });
    } else {
      if (typeof window !== "undefined") {
        window.CallCenterStateMachine = a;
      } else {
        if (typeof self !== "undefined") {
          self.CallCenterStateMachine = a;
        }
      }
    }
  }
}());

/**
 * ----------------------- SDK START
 */
(function () {
  if ($ && $ && $ !== $) {
    $.noConflict();
  }
  /**
   * ---------------------- PROTECTED START
   **/
  var Utils = {
    initConfig: function (defaultConfig) {
      return $.extend(true, {}, defaultConfig);
    },
    afterCauseEnum: {
      0: "呼叫正常结束", 1: "关机", 2: "停机", 3: "暂时无法接通", 4: "用户忙", 5: "久叫不应",
      6: "呼叫限制", 7: "网络忙", 8: "用户忙或拒绝", 9: "呼叫被转移", 10: "传真机接听",
      11: "自动应答机接听", 12: "主叫用户早释", 13: "系统异常", 14: "系统路由错误", 15: "应用服务器无应答",
      16: "用户未注册", 17: "用户无响应", 18: "空号", 19: "一般错误", 20: "目的号码错误",
      21: "黑名单号码", 22: "用户不在", 23: "用户未登录", 24: "用户不在服务区", 25: "认证失败",
      26: "应答但无声音"
    },
    // 话后原因处理
    addAfterCauseMsg: function (json) {
      if (!json.causeMsg) {
        json.causeMsg = Utils.afterCauseEnum[json.cause] || "";
      }
      return json;
    },
    // 接收数据脱敏打印
    printMessageData: function (json) {
      var logJson = $.extend({}, json);
      logJson.caller && (logJson.caller = "*");
      logJson.called && (logJson.called = "*");
      logJson.origcaller && (logJson.origcaller = "*");
      logJson.origcalled && (logJson.origcalled = "*");
      logJson.number && (logJson.number = "*");
      logJson.userdata && (logJson.userdata = "*");
      logJson.userData && (logJson.userData = "*");
      logJson.custId && (logJson.custId = "*");
      logJson.preview && (logJson.preview = "*");
      logJson.recordcode && (logJson.recordcode = "*");
      logJson.filename && (logJson.filename = "*");
      logJson.followData && (logJson.followData = "*");
      logJson.uuiData && (logJson.uuiData = "*");
      CallCenter.log(logJson);
    },
    // 发送数据脱敏打印
    printSendData: function (sendObj) {
      sendObj.operatorid && (sendObj.operatorid = "*");
      sendObj.password && (sendObj.password = "*");
      sendObj.called && (sendObj.called = "*");
      sendObj.origcaller && (sendObj.origcaller = "*");
      sendObj.origcalled && (sendObj.origcalled = "*");
      sendObj.caller && (sendObj.caller = "*");
      sendObj.userdata && (sendObj.userdata = "*");
      sendObj.userData && (sendObj.userData = "*");
      sendObj.uuiData && (sendObj.uuiData = "*");
      CallCenter.log("CallCenter发送消息:" + JSON.stringify(sendObj));
    }
  };

  /**
   * ---------------------- SDKConfig START
   */
  var Configs = {
    // 签入/签出配置
    register: {
      cccsLoginUrl: null,  // 3CS登录地址
      enableRefreshConfirm: 1  // 启用SDK浏览器刷新保护
    },
    // 重连配置
    reconnection: {
      timeout: 1000 * 15, // 超时20秒重连
      lastMsgTime: 0,  // 最后一次消息时间
      hasDisconnect: false,
      isLastReconnection: false,  // 是否收到重连命令
      reconnectMax: 0  // 是否收到重连命令
    },
    customize: null  // 客户自定义事件数据
  };
  var CCProps = Utils.initConfig(Configs);

  /* SDK语言部署 */
  Utils.language = {
    updateDefaultText: function () {
      CallCenter._defaultBusyText = UIT("agentbusy");
      CallCenter._defaultIdleText = UIT("agentidle");
      CallCenter._statusText = UIT("waitConnection");
    },
    updateDefaultState: function () {
      SDK_action = CallCenter.actions();
      SDK_state = CallCenter.states();
      SDK_action = $.extend(CallCenter.agentCoachActions(), SDK_action);
      SDK_state = $.extend(CallCenter.agentCoachStates(), SDK_state);
    },
    toggleLanguage: function (type) {
      langDefine.setLanguageType(type);
      Utils.language.updateDefaultText();
      Utils.language.updateDefaultState();
    },
    toggleMainClass: function () {
      $("#CallCenter_main").removeClass(langDefine.config.historyType).addClass(langDefine.config.type);
    }
  };

  /* SDK-UI部署 */
  Utils.ui = {
    // 拨号盘
    dialPlate: function () {
      return "<div id=\"CallCenter_jianpan\" class=\"jianpan\" style=\"display:none;\">\
                        <ul class=\"jianpan_ul\">\
                            <li class=\"jianpan_icon jianpan_1\"></li>\
                            <li class=\"jianpan_icon jianpan_2\"></li>\
                            <li class=\"jianpan_icon jianpan_3\"></li>\
                            <li class=\"jianpan_icon jianpan_4\"></li>\
                            <li class=\"jianpan_icon jianpan_5\"></li>\
                            <li class=\"jianpan_icon jianpan_6\"></li>\
                            <li class=\"jianpan_icon jianpan_7\"></li>\
                            <li class=\"jianpan_icon jianpan_8\"></li>\
                            <li class=\"jianpan_icon jianpan_9\"></li>\
                            <li class=\"jianpan_icon jianpan_x\"></li>\
                            <li class=\"jianpan_icon jianpan_0\"></li>\
                            <li class=\"jianpan_icon jianpan_h\"></li>\
                        </ul>\
                    </div>";
    },
    // 外呼号码框 oldName:getMakeCallUI
    makeCall: function () {
      return "<div id='CallCenter_call_div' class='enternum'><div class='arrow'></div><div class='popover_content'><input type='text' placeholder='" + UIT("InputNumberClickEnterKeytoInitiateCall") + "' class='numtxt' /><input type='button' value='" + UIT("call") + "' class='btn-makecall'></div></div>";
    }
  };

  /* SDK-登录部署 */
  Utils.signHandler = {
    /**
     * @private 设置服务类型
     * @server_type 登录的服务类型，1 CCS（默认），2 CTI
     */
    setServerType: function (server_type) {
      if (server_type == CallCenter._serverType_ccs || server_type == CallCenter._serverType_cti) {
        CallCenter._serverType = server_type;
      } else {
        CallCenter._serverType = CallCenter._serverType_ccs;  // 默认服务类型为CCS
      }
    },
    /**
     * @private 是否预测外呼登录
     * @return 0 | 1
     * @invoke relogin
     **/
    getAuto: function () {
      return CallCenter._auto;
    },
    /**
     * @private 是否允许调用登录
     * @invoke getLoginInfo
     **/
    allowLogin: function () {
      //未登录、退出、验证失败、被踢出、切换主从，可重复登录
      return CallCenterStatus.is(SDK_state.s_nologin.name) || CallCenterStatus.is(SDK_state.s_logout.name) ||
        CallCenterStatus.is(SDK_state.s_authfail.name) || CallCenterStatus.is(SDK_state.s_kick.name) ||
        CallCenterStatus.is(SDK_state.s_reconnection_fail.name)
    },
    /**
     * @private 自定义忙碌类型
     * @invoke getLoginInfo
     **/
    customizeBusyType: function (busyList) {
      $(".CallCenter_busy").not("#CallCenter_busy").each(function (e) {
        $(this).remove();
      });
      CallCenter._busyTypeMap.clear();
      CallCenter._busyTypeMap.put(0, CallCenter._defaultBusyText);
      for (var i = 0; i < busyList.length; i++) {
        if (busyList[i]["showText"]) {
          CallCenter._busyTypeMap.put(busyList[i]["typeId"], busyList[i]["showText"]);
        } else {
          CallCenter._busyTypeMap.put(busyList[i]["busy_descr"], busyList[i]["busy_name"]);
        }
      }
      CallCenter.addBusyButton($("#CallCenter_main"));
    },
    /**
     * @private 初始化客户端类型【20181126_3.0.0.0】
     * @clientType 登录接口配置的客户端类型
     * @useSipPhone 3CS接口返回是否使用SIP话机 1 webcall 2 sip&rtc
     * @invoke getLoginInfo
     */
    initClientType: function (clientType, useSipPhone) {
      if (clientType) {
        if (Number(clientType) === 2) {
          if (Number(useSipPhone) === 2) {
            CallCenter._clientType = clientType;
          } else {
            CallCenter.eventAlert("当前企业未开通网页电话模式，请联系管理员。");
          }
        } else {
          CallCenter._clientType = clientType;
        }
      } else {
        if (typeof useSipPhone === "undefined") {
          CallCenter.log("企业没有设置客户端软话机类型，默认使用WebCall");
          CallCenter._clientType = CallCenter._clientType_sipphone;
        } else {
          if (Number(useSipPhone) === 2) {
            CallCenter.log("企业设置客户端软话机类型为：WebRTC");
            CallCenter._clientType = CallCenter._clientType_websip;
          } else {
            CallCenter.log("企业设置客户端软话机类型为：WebCall");
            CallCenter._clientType = CallCenter._clientType_sipphone;
          }
        }
      }
    },
    /**
     * @private 初始化SoftPhone 【20181126_3.0.0.0】
     * @invoke getLoginInfo
     */
    initSoftPhone: function () {
      var clientType = CallCenter.getClientType();
      if (clientType == CallCenter._clientType_websip) {
        CallCenter.log("使用WebRTC");
        window.SoftPhone = window.CC_WebrtcPhone;
      } else if (clientType == CallCenter._clientType_sipphone) {
        CallCenter.log("使用WebCall");
        window.SoftPhone = window.CC_SoftPhone;
      }
    },
    /**
     * @private 获取登录信息并且初始化
     * @url 3CS连接地址
     * @logintype 登录方式，0手机，1硬话机，2软话机
     * @auto 是否智能外呼,0否1是
     * @logingroups 智能外呼时，登录到的技能组id，非预测外呼时不需要传
     * @client_type  客户端类型，1 WebCall,2 WebRTC
     */
    getLoginInfo: function (url, logintype, auto, logingroups, client_type) {
      CCProps.register.cccsLoginUrl = url;
      CallCenter.log("logintype:" + logintype + ", auto:" + auto + ", logingroups:" + logingroups + ", client_type:" + client_type + ", logingroups:" + logingroups);

      // 初始化登录
      function apply3CSData(json) {
          debugger;
        if (json.code === "0000" && json.info) {
          var printJson = $.extend({}, json.info);
          printJson.operatorid = "*";
          printJson.password = "*";
          printJson.sip_pwd = "*";
          CallCenter.log("CallCenter接口消息：收到3CS数据" + JSON.stringify(printJson));

          CallCenter._agentType = json.info.agenttype || (CallCenter.isAuto() ? 2 : 1);
          var operatorid = json.info.operatorid;
          var password = json.info.password;
          var abbreviate = json.info.abbreviate;
          var companyid = json.info.companyid;
          var sip_id = json.info.sip_id;
          var sip_pwd = json.info.sip_pwd;
          var stun_servers = json.info.stunServers;
          var rtc_server = "wss://" + json.info.wertc_ip + ":" + json.info.webrtc_port;


          CallCenter._wsurl = json.info.ws_url;             //ccs地址
          CallCenter._media_ip = json.info.media_ip;        //媒体地址
          CallCenter._media_port = json.info.media_port;    //媒体端口

          CallCenter._logintype = logintype;      //登录类型,0 手机,1 sip话机,2 软话机
          CallCenter._operatorid = operatorid;    //工号
          CallCenter._password = password;        //密码
          CallCenter._abbreviate = abbreviate;    //公司简称
          CallCenter._companyid = companyid;      //公司编号
          CallCenter._logingroups = logingroups;  //登录到的技能组
          CallCenter._auto = auto;                //是否预测外呼
          CallCenter._sip_id = sip_id;            //SIP账号
          CallCenter._sip_pwd = sip_pwd;          //SIP密码
          CallCenter._stun_servers = stun_servers;//rtc连接stunServers
          CallCenter._rtc_server = rtc_server;    //rtc连接socket地址
          CallCenter._ws_msg_type = json.info.ws_msg_type || 0;    //是否加密

          CallCenter._busyTypeMap.put(0, CallCenter._defaultBusyText);
          if (CallCenter.getLoginType() === CallCenter._loginType_web) {
            // Utils.signHandler.initClientType(client_type, json.info.use_sipphone);  // 初始化客户端类型
            if (client_type) {
              if (Number(client_type) === 2) {
                if (Number(json.info.use_sipphone) === 2) {
                  CallCenter._clientType = client_type;
                } else {
                  CallCenter.eventAlert("当前企业未开通webRTC，请联系管理员。");
                  return;
                }
              } else {
                CallCenter._clientType = client_type;
              }
            } else {
              if (typeof json.info.use_sipphone === "undefined") {
                CallCenter.log("企业没有设置客户端软话机类型，默认使用WebCall");
                CallCenter._clientType = CallCenter._clientType_sipphone;
              } else {
                if (Number(json.info.use_sipphone) === 2) {
                  CallCenter.log("企业设置客户端软话机类型为：WebRTC");
                  CallCenter._clientType = CallCenter._clientType_websip;
                } else {
                  CallCenter.log("企业设置客户端软话机类型为：WebCall");
                  CallCenter._clientType = CallCenter._clientType_sipphone;
                }
              }
            }
            // 并监听就绪事件
            Utils.signHandler.initSoftPhone();
          }

          if (json.info.sendlog === 1) {
            CallCenter.log("发送日志到服务端状态：启用");
            CallCenter._sendlog = true;
          } else {
            CallCenter.log("发送日志到服务端状态：禁用");
          }

          //自定义忙碌类型
          if (json.info.busyList) {
            Utils.signHandler.customizeBusyType(json.info.busyList);
          }

          //登录时选择技能组
          if (CallCenter._selectionGroup) {
            $(".CallCenter_login_group,#CallCenter_login_group_pannel").remove();
            CallCenter.initControl().setStatusAndPhoneText(UIT("selectgroup"))
            .showControl("#CallCenter_status_buts,.CallCenter_login_group");
            if (json.info.groupList) {
              for (var i = 0; i < json.info.groupList.length; i++) {
                var ws_url = json.info.groupList[i].ws_url;
                var groupid = json.info.groupList[i].groupid;
                var groupname = json.info.groupList[i].groupname;
                var media_ip = json.info.groupList[i].media_ip;
                var media_port = json.info.groupList[i].media_port;
                CallCenter.addLoginGroup(ws_url, media_ip, media_port, groupname, groupid);
              }
            } else {
              CallCenter.setStatusAndPhoneText(UIT("noavailablegroup")).eventAlert(UIT("noavailablegroup"));
            }
          } else {
            CallCenter.init();
          }
        } else {
          if (typeof (CallCenter.opLogin_callback) == "function") {
            CallCenter.opLogin_callback(json);
          }
          if (typeof (CallCenter.sipLogin_callback) == "function") {
            CallCenter.sipLogin_callback(json);
          }
          CallCenter.setStatusAndPhoneText("登录失败，错误：" + json.code);
        }
        if (json.flowList && json.flowList.length > 0) {
          CallCenter.log("收到IVR流程数据");
          CallCenter._ivrFlowList = json.flowList;
        } else {
          CallCenter.log("没有收到IVR流程数据");
        }
      }

      //未登录、退出、验证失败、被踢出，可重复登录
      if (Utils.signHandler.allowLogin()) {
        $.ajax({
          url: url,
          headers: {
            "CSRF-TOKEN": CallCenter.getUUID()
          },
          dataType: "jsonp",
          success: apply3CSData
        });
      } else {
        CallCenter.eventAlert(UIT("nologon"));
      }
    }
  };

  /* 重连逻辑部署 */
  Utils.reconnection = {
    relogin: function () {
      var url = CCProps.register.cccsLoginUrl;
      var loginType = CallCenter.getLoginType();
      var auto = Utils.signHandler.getAuto();
      var loginGroups = CallCenter.getLoginGroups();
      var clientType = CallCenter.getClientType();
      Utils.signHandler.getLoginInfo(url, loginType, auto, loginGroups, clientType);
    },
    // 是否为通话中保持状态
    isCallingKeepStatus: function () {
      return CCProps.reconnection.isCallingDisconnect && CallCenter.getNowStatus() !== "after";
    },
    //
    reconnection: function () {
      window.clearInterval(CCProps.reconnectionTime);
      CCProps.reconnectionTime = window.setInterval(function () {
        CallCenter.log("超时重连，超时时长：" + CCProps.reconnection.timeout + "毫秒");
        if (CallCenter._websocket) {
          CallCenter._websocket.close();
          SoftPhone.UnInitialize();
        }
      }, CCProps.reconnection.timeout);
    },
    /**
     * 重连后保持时间状态
     * 切换CCS后，重连成功，CCS推送恢复切换前状态，保持当前状态时间不进行重置
     **/
    preserveTimerWhenReconnection: function () {
      if (!CCProps.reconnection.isLastReconnection) {
        CallCenter._callingtimer = 0;
      }
      CCProps.reconnection.isLastReconnection = false;
    }
  };

  /* 号码部署 */
  Utils.phoneHandler = {
    /* 号码脱敏 */
    // 隐藏号码段
    hidePhone: function () {
      CallCenter._hidePhone = true;
      return "success";
    },
    // 显示完整号码
    showPhone: function () {
      CallCenter._hidePhone = false;
      return "success";
    },
    /* 号码透传 */
    /**
     * 设置透传号码
     * @param number
     */
    setTransmissionNumber: function (number) {
      CallCenter._transmission_number = number;
      return "success";
    },
    /**
     * 获取透传号码
     * @returns {string}
     */
    getTransmissionNumber: function () {
      return CallCenter._transmission_number;
    }
  };

  /* 通话部署 */
  Utils.callHandler = {
    /**
     * 显示接通按键
     */
    showAnswerButton: function () {
      // CCS模式且不是手机签入（SIP话机或WebCall）或CTI模式签入坐席执行正常摘机
      if (
        CallCenter._logintype != CallCenter._loginType_mobile ||
        CallCenter._serverType == CallCenter._serverType_cti
      ) {
        CallCenter.showControl("#CallCenter_answer");
      }
    }
  };

  /* 事件部署 */
  Utils.EventHandler = {
    /**
     * 添加事件到监听
     * @param event
     * @param fun
     * @returns {*}
     */
    addEventListener: function (event, fun) {
      if (typeof (fun) != "function") {
        return "";
      }
      if (typeof (CallCenter._events[event]) == "undefined") {
        CallCenter._events[event] = [];
      }
      var uuid = CallCenter.getUUID();
      CallCenter._events[event][uuid] = fun;
      CallCenter._events[event].length++;
      return uuid;
    },
    /**
     * 根据事件id移除事件
     * @param event
     * @param uuid
     * @returns {number}
     */
    removeEventListener: function (event, uuid) {
      if (typeof (CallCenter._events[event]) == "undefined") {
        return 0;
      } else {
        delete CallCenter._events[event][uuid];
        CallCenter._events[event].length--;
        return { result: 1, reason: "succeeded" };
      }
    },
    /**
     * 获取事件函数当前个数
     * @param event
     * @returns {number}
     */
    getEventListenerCount: function (event) {
      if (typeof (CallCenter._events[event]) == "undefined") {
        return 0;
      } else {
        return CallCenter._events[event].length;
      }
    },
    /**
     * @Public 发送数据
     */
    sendcmd: function (cmd) {
      this.cmd = cmd || "ping";
      var customizeData = CallCenter.getTransactionCmd();
      if (typeof customizeData === "object") {
        this.businessData = JSON.stringify(customizeData);
      } else if (/string|number|boolean/.test(typeof (customizeData))) {
        this.businessData = customizeData;
      }
      CallCenter.setTransactionCmd(null);
    },
    /**
     * @Public 获取客户自定义发送数据
     */
    getTransactionCmd: function () {
      return CCProps.customize || null;
    },
    /**
     * @Public 设置客户自定义发送数据
     * @param customize 自定义数据对象
     */
    setTransactionCmd: function (customize) {
      CCProps.customize = customize;
    },
    /**
     * @Public 启用或禁用浏览器刷新保护
     * @param enable 1 启用，0 禁用
     */
    enableSDKRefreshConfirm: function (enable) {
      CCProps.register.enableRefreshConfirm = enable === 1;
    },
    /**
     * @Public 是否启用内置浏览器刷新保护
     */
    innerRefreshConfirmEnabled: function () {
      return CCProps.register.enableRefreshConfirm;
    },
    /**
     * @Public 浏览器刷新提示
     */
    browserRefreshConfirm: function () {
      return
    }
  };

  /* 加密 */
  Utils.encryption = function () {
    // private property
    const _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    // public method for encoding
    this.encode = function (input) {
      var output = "";
      var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
      var i = 0;
      input = _utf8_encode(input);
      while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
          enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
          enc4 = 64;
        }
        output = output +
          _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
          _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
      }
      return output;
    }
    // public method for decoding
    this.decode = function (input) {
      var output = "";
      var chr1, chr2, chr3;
      var enc1, enc2, enc3, enc4;
      var i = 0;
      input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
      while (i < input.length) {
        enc1 = _keyStr.indexOf(input.charAt(i++));
        enc2 = _keyStr.indexOf(input.charAt(i++));
        enc3 = _keyStr.indexOf(input.charAt(i++));
        enc4 = _keyStr.indexOf(input.charAt(i++));
        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;
        output = output + String.fromCharCode(chr1);
        if (enc3 != 64) {
          output = output + String.fromCharCode(chr2);
        }
        if (enc4 != 64) {
          output = output + String.fromCharCode(chr3);
        }
      }
      output = _utf8_decode(output);
      return output;
    }
    // private method for UTF-8 encoding
      let _utf8_encode = function (string) {
      string = string.replace(/\r\n/g, "\n");
      var utftext = "";
      for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
          utftext += String.fromCharCode(c);
        } else if ((c > 127) && (c < 2048)) {
          utftext += String.fromCharCode((c >> 6) | 192);
          utftext += String.fromCharCode((c & 63) | 128);
        } else {
          utftext += String.fromCharCode((c >> 12) | 224);
          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
          utftext += String.fromCharCode((c & 63) | 128);
        }
      }
      return utftext;
    }
    // private method for UTF-8 decoding
    let _utf8_decode = function (utftext) {
      var string = "";
      var i = 0;
      let c1,c2;
      var c = c1 = c2 = 0;
      while (i < utftext.length) {
        c = utftext.charCodeAt(i);
        if (c < 128) {
          string += String.fromCharCode(c);
          i++;
        } else if ((c > 191) && (c < 224)) {
          c2 = utftext.charCodeAt(i + 1);
          string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
          i += 2;
        } else {
          c2 = utftext.charCodeAt(i + 1);
          c3 = utftext.charCodeAt(i + 2);
          string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
          i += 3;
        }
      }
      return string;
    }
  }

  /**
   * ---------------------- PUBLIC START
   **/
  window.CallCenter = window.CallCenter || {
    version: ccsdk,
    _nolog: false,                  //关闭日志
    _thisPath: null,                //当前文件路径

    _wsurl: null,                   //CCSWebSocket地址
    _companyid: null,               //企业编号
    _abbreviate: null,              //企业简写
    _ws_msg_type: 0,                 //是否加密
    _operatorid: null,              //工号 getOperatorid()
    _password: null,                //密码 getPassword()
    _media_ip: null,                //媒体服务器IP
    _media_port: null,              //媒体服务器port
    _sip_id: null,                  //SIP账号
    _sip_pwd: null,                 //SIP密码
    _auto: 0,                       //是否预测外呼，0否1是
    _logingroups: "",               //登录到的技能组
    _agentType: null,               //预测坐席签入方式:1 短签，2 长签[20180724]

    _url_3cs: null,                 //3CS的地址，不含协议
    _url_3cs_ssl: false,            //是否启用SSL连接3CS

    _sendlog: false,                //是否启用远端写日志
    _pingInterval: 5000,            //ping的间隔时长

    _defaultBusyText: UIT("agentbusy"),       //默认忙碌显示文字
    _defaultIdleText: UIT("agentidle"),       //默认空闲显示文字

    _islogin: false,                //是否已经登录
    _calling: false,                //是否在通话中
    _isCallout: false,              //通话中是否为外呼触发

    _preview: null,                 //预测外呼用
    _nowStatus: null,               //当前坐席状态，logon/offwork/agentidle/agentbusy/after
    _busyType: 0,                   //当前坐席忙碌类型
    _hidePhone: false,              //是否隐藏号码不显示到界面
    _transmission_number: "",       //透传的号码(外呼时的主叫)
    _isMeeting: false,              //是否会议模式，会议模式没有咨询转接转IVR
    _isInnercall: false,            //是否内呼

    _callId: "",                    //通话ID
    _timestamp: "",                 //创建通话的时间戳
    _caller: "",                    //主叫号码
    _called: "",                    //被叫号码
    _desNo: "",                    //脱敏号码
    _orgCaller: "",                  // 原始主叫
    _orgCalled: "",                  // 原始被叫
    _calling_from: "",              //通话中的状态来源
    _agentStandby: "",              //是否为预测外呼
    _be_operator: "",               //被操作人

    _lastStatus: "",                     //上一次CCS返回状态
    _isconsultCall: 0,                     //是否正在咨询
    _isCallmute: false,                     //是否静音
    _status: "",                     // 当前CCS返回状态
    _mainStatus: "",                 // 当前状态
    _meetingSeats: [],               // 是否存在会议
    _consultSeat: "",
    _isSanfangCall: "",
    _meetingLimit: 10,
    _statusText: UIT("waitConnection"),        //当前状态文字
    _callingtimer: 0,               //通话时长
    _timerspan: 0,                  //状态栏计时秒数
    _timerId: 0,                    //状态栏时间控件编号
    _pingId: 0,                     //ping计时器编号
    _eventAlertTimeoutId: 0,        //事件提醒计时器编号

    _websocket: null,               //ws对象
    _websocket_ocx: null,           //ocx的ws对象
    _lastmsg: null,                 //最后一次发送的消息
    _useOcx: false,                 //是否使用了OCX

    _drawType: 0,                   //使用的全图标布局还是简版，1简版2全图标
    _calloutHideTCButton: false,    //外呼是否隐藏转接咨询按钮
    _openOnlyMuteCustomer: false,   //保持时是否静音两端
    _getIdleAgentFromTC: 0,         //获取空闲来源，0无，1转接，2咨询
    _events: [],                    //事件列表
    _availablegroup: false,         //是否登录时先获取可用任务
    _selectionGroup: false,         //手动选择技能组登录
    _refreshReconnection: false,    //刷新后重新连接

    _busyTypeMap: null,             //忙碌类型map

    _serverType: 1,                 //登录的服务类型
    _serverType_ccs: 1,             //服务类型常量,CCS
    _serverType_cti: 2,             //服务类型常量,CTI

    _clientType: 1,                 //客户端软话机类型
    _clientType_sipphone: 1,        //客户端类型WebCall
    _clientType_ocx: 2,             //客户端类型OCX
    _clientType_websip: 2,          //客户端类型WebRTC

    _logintype: null,               //登录方式,0手机,1SIP话机,2软话机
    _loginType_mobile: 0,           //手机方式登录
    _loginType_sip: 1,              //sip话机
    _loginType_web: 2,              //软话机

    _ivrFlowList: null,              //IVR流程列表

    _enableAgentBusyCallout: false,    //是否启用忙碌时外呼
    _isCoashCall: 0,                  // 是允进入班长辅导状态
    _leaveconf: false,               //是否三方有人挂机

    _ccsCancelMakeCallException: false,  // CCS挂机异常
    _ccsAgentConsultBackException: false, // CCS结束咨询异常
    _sdk_login: false,                    // ccs登录/重连成功
    _save_message: [],                    // 软化机登录的时候存储的消息
    _nowebcall: false,                    // 登录未打开webcall
    taskClearTimeout: null,
    _isConsultingGetFrreAgent: false, //是否是咨询时获取空闲坐席


    /**
     * 清除SDK登录信息
     */
    clearConnection: function () {
      if (CallCenter._logintype == CallCenter._loginType_web) {//软话机方式登录
        try {
          SoftPhone.Logout();
          SoftPhone.UnInitialize();
        } catch (e) {
          CallCenter.log(e);
        }
      }
      if (CallCenter.isWebRtc()) {//rtc方式登录
        try {
          SoftPhone.UnInitialize();
        } catch (e) {
          CallCenter.log(e);
        }
      }

      CallCenter._islogin = false;
      CallCenter._nowStatus = "offwork";
      CallCenter._wsurl = null;
      CallCenter._companyid = null;               //企业编号
      CallCenter._abbreviate = null;              //企业简写
      CallCenter._ws_msg_type = 0;                //是否加密
      CallCenter._operatorid = null;              //工号
      CallCenter._password = null;                //密码
      CallCenter._media_ip = null;                //媒体服务器IP
      CallCenter._media_port = null;              //媒体服务器port
      CallCenter._sip_id = null;                  //SIP账号
      CallCenter._sip_pwd = null;                 //SIP密码
      CallCenter._logintype = null;               //登录方式,0手机,1SIP话机,2软话机
      CallCenter._auto = 0;                       //是否预测外呼，0否1是
      CallCenter._logingroups = "";               //登录到的技能组
      CallCenter._url_3cs = null;                 //3cs的地址
      CallCenter._url_3cs_ssl = false;            //3cs前缀，是否为ssl
      CallCenter._callId = "";                    //callid
      CallCenter._timestamp = "";                 //callid匹配的timestamp
      CallCenter._caller = "";                    //主叫号码
      CallCenter._called = "";                    //被叫号码
      CallCenter._calling_from = "";              //通话中的状态来源
      CallCenter._agentStandby = "";              //通话中的状态来源
      CallCenter._be_operator = "";               //被操作人
      CallCenter._status = "";                    //当前CCS返回状态
      CallCenter._isCallmute = false;                //是否正在静音
      CallCenter._lastStatus = "";                //上一次CCS返回状态
      CallCenter._isSanfangCall = "";            //是否正在三方
      CallCenter._isconsultCall = 0;             //是否正在咨询 1=咨询方 2=被咨询方
      CallCenter._statusText = "等待连接";        //当前状态文字

      CallCenter._callingtimer = 0;               //通话时长
      CallCenter._timerspan = 0;                  //状态栏计时秒数
      CallCenter.timer();
      window.clearInterval(CallCenter._timerId);  //清除计时器
      CallCenter._timerId = 0;                    //状态栏时间控件编号

      CallCenter._isCallout = false;              //是否外呼
      CallCenter._calling = false;                //是否在通话中
      CallCenter._isMeeting = false;              //是否会议模式，会议模式没有咨询转接转IVR
      CallCenter._isInnercall = false;            //是否内呼
      CallCenter._enableAgentBusyCallout = false; //是否启用忙碌外呼
      CallCenter._isCoashCall = 0;
      CallCenter._leaveconf = false;

      //清除计时器
      window.clearInterval(CCProps.reconnectionTime);

      if (CallCenter._websocket) {
        try {
          CallCenter._websocket.close();
          CallCenter._websocket = null;
        } catch (ex) {
          CallCenter.log(ex);
        }
      }
      if (CallCenter._websocket_ocx) {
        try {
          CallCenter._websocket_ocx.close();
          CallCenter._websocket_ocx = null;
        } catch (ex) {
          CallCenter.log(ex);
        }
      }
      CCProps = Utils.initConfig(Configs);
      return this;
    },

    // 主动请求命令类
    _sendcmd: Utils.EventHandler.sendcmd,
    getTransactionCmd: Utils.EventHandler.getTransactionCmd,
    setTransactionCmd: Utils.EventHandler.setTransactionCmd,
    isCallingKeepStatus: Utils.reconnection.isCallingKeepStatus,  // 是否通话中保持状态

    // 下面为功能性函数
    toggleLanguage: Utils.language.toggleLanguage,  // 切换语言
    toggleMainClass: Utils.language.toggleMainClass,  // 切换样式主类
    base64: new Utils.encryption(),  // 加密初始化

    // 更改忙碌显示文字
    setBusyText: function (showText) {
      CallCenter._defaultBusyText = showText;
      $(".CallCenter_defaultBusyText").text(showText);
      CallCenter._busyTypeMap.put(0, showText);
      return "success";
    },
    // 更改空闲显示文字
    setIdleText: function (showText) {
      CallCenter._defaultIdleText = showText;
      $(".CallCenter_defaultIdleText").html(showText);
      return "success";
    },
    // 启用日志发送到服务器
    openClientLog: function () {
      CallCenter._sendlog = true;
      CallCenter.log("开启发送日志到服务端", true);
      return "success";
    },
    // 是否是webrtc登录
    isWebRtc: function () {
      var clientType = CallCenter.getClientType();
      if (clientType == CallCenter._clientType_websip && CallCenter._logintype == CallCenter._loginType_web) {
        return true;
      }
      return false;
    },
    // 是否是WebCall登录
    isWebCall: function () {
      var clientType = CallCenter.getClientType();
      if (clientType == CallCenter._clientType_sipphone && CallCenter._logintype == CallCenter._loginType_web) {
        return true;
      }
      return false;
    },
    // webrtc登录
    loginWebRtc: function (json, type) {
        debugger;
      var stunServers = [];
      var data = CallCenter._stun_servers;
      for (var i = 0; i < data.length; i++) {
        var item = data[i];
        stunServers.push({ urls: item });
      }
      CallCenter.log("janus准备连接");
      type === "reconnection" && SoftPhone.UnInitialize();
      SoftPhone.init({
        stunServers: stunServers,
        server: CallCenter._rtc_server,
        sip_id: CallCenter._sip_id,
        sip_pwd: CallCenter._sip_pwd,
        reconnection: CCProps.reconnection,
        eventJson: json,
        initType: type
      });
    },
    // webCall登录
    loginWebCall: function (json, type) {
      CallCenter.log("webcall准备连接");
      type === "reconnection" && SoftPhone.UnInitialize();
      SoftPhone.init(CallCenter._media_ip, CallCenter._media_port, CallCenter._sip_id, CallCenter._sip_pwd, json, type);
    },
    // 执行缓存状态
    executeMessage: function () {
      if (CallCenter._save_message.length > 0) {
        CallCenter.log("执行缓存状态");
        for (var i = 0; i < CallCenter._save_message.length; i++) {
          CallCenter.onmessage(CallCenter._save_message[i]);
        }
        CallCenter._save_message = [];
      }
      if (CallCenter._agentType == 2 && CallCenter._logintype == CallCenter._loginType_web && CallCenter.isAuto()) {
        CallCenter.handle_automakecall();
      }
    },
    // 关闭日志发送到服务器
    closeClientLog: function () {
      CallCenter._sendlog = false;
      CallCenter.log("关闭发送日志到服务端", false);
      return "success";
    },
    // 开启外呼后显示咨询转接
    openCalloutTC: function () {
      CallCenter._calloutHideTCButton = false;
      return "success";
    },
    // 关闭外呼后显示咨询转接
    closeCalloutTC: function () {
      CallCenter._calloutHideTCButton = true;
      return "success";
    },

    // 号码脱敏
    hidePhone: Utils.phoneHandler.hidePhone,  // 隐藏号码段
    showPhone: Utils.phoneHandler.showPhone,  // 显示完整号码
    // 号码透传
    setTransmissionNumber: Utils.phoneHandler.setTransmissionNumber,  // 设置透传号码
    getTransmissionNumber: Utils.phoneHandler.getTransmissionNumber,  // 获取透传号码
    // 事件注册
    // 添加事件到监听
    addEventListener: Utils.EventHandler.addEventListener,
    // 根据事件id移除事件
    removeEventListener: Utils.EventHandler.removeEventListener,
    // 获取事件函数当前个数
    getEventListenerCount: Utils.EventHandler.getEventListenerCount,
    // 启用或禁用内置浏览器刷新保护
    enableSDKRefreshConfirm: Utils.EventHandler.enableSDKRefreshConfirm,
    // 是否启用内置浏览器刷新保护
    innerRefreshConfirmEnabled: Utils.EventHandler.innerRefreshConfirmEnabled,
    // 浏览器刷新提示
    browserRefreshConfirm: Utils.EventHandler.browserRefreshConfirm,
    // 开启连接后获取预测外呼技能组
    openAvailablegroup: function () {
      CallCenter._availablegroup = true;
      return "success";
    },
    // 关闭连接后获取预测外呼技能组
    closeAvailablegroup: function () {
      CallCenter._availablegroup = false;
      return "success";
    },
    // 启用登录后选择技能组
    openSelectionGroup: function () {
      CallCenter._selectionGroup = true;
      return "success";
    },
    // 关闭登录后选择技能组
    closeSelectionGroup: function () {
      CallCenter._selectionGroup = false;
      return "success";
    },
    // 开启只静音客户
    openOnlyMuteCustomer: function () {
      CallCenter._openOnlyMuteCustomer = true;
      return "success";
    },
    // 关闭只静音客户
    closeOnlyMuteCustomer: function () {
      CallCenter._openOnlyMuteCustomer = false;
      return "success";
    },
    // 开启刷新后重新连接
    openRefreshReconnection: function () {
      CallCenter._refreshReconnection = true;
      CallCenter.setlocalstorage("refreshReconnection", 1);
      return "success";
    },
    // 关闭刷新后重新连接
    closeRefreshReconnection: function () {
      CallCenter._refreshReconnection = false;
      CallCenter.setlocalstorage("refreshReconnection", 0);
      return "success";
    },
    // 设置登录的技能组
    setLoginGroups: function (ids) {
      CallCenter._logingroups = ids;
      CallCenter._sendcmd.prototype.logingroups = ids;
    },
    // 获取登录的技能组
    getLoginGroups: function () {
      return CallCenter._logingroups;
    },
    // 销毁布局
    destory: function () {
      $("#CallCenter_main").remove();
    },
    // 设置状态变化回调
    setStatusAndPhoneTextEvent: function (fun) {
      CallCenter.setStatusAndPhoneText_event = fun;
    },
    // 设置事件提醒回调
    setEventAlertEvent: function (fun) {
      CallCenter.eventAlert_event = fun;
    },

    // 设置重连时间Max
    setReconnectMax: function (num) {
      num = Number(num) || 0;
      CCProps.reconnection.reconnectMax = ((num <= 30 && num !== 0) ? 30 : parseInt(num));
      return CCProps.reconnection.reconnectMax;
    },
    // 开启日志输出
    openLog: function () {
      CallCenter._nolog = false;
      return "success";
    },
    // 关闭日志输出
    closeLog: function () {
      CallCenter._nolog = true;
      return "success";
    },
    // 开启忙碌时外呼
    openAgentBusyCallout: function () {
      if (!CallCenter.isAuto() && !CallCenter.isCalling()) {
        CallCenter._enableAgentBusyCallout = true;
        return "success";
      } else {
        return "failure,预测外呼或通话中无法开启";
      }
    },
    // 关闭忙碌时外呼
    closeAgentBusyCallout: function () {
      CallCenter._enableAgentBusyCallout = false;
      return "success";
    },
    // 设置随路数据
    setFollowData: function (userData) {
      CallCenter.log("设置随路数据：" + userData);
      var sendObj = new CallCenter._sendcmd("saveOrUpdataFollowData");
      sendObj.userData = userData;
      CallCenter.send(sendObj);
    },
    // 设置UUI数据
    setUUIData: function (uuiData) {
      CallCenter.log("设置UUI数据：" + uuiData);
      var sendObj = new CallCenter._sendcmd("saveOrUpdataUUIData");
      sendObj.uuiData = uuiData;
      CallCenter.send(sendObj);
    },
    /* 上面为功能性函数 */

    /* 下面为获取部分参数 */
    // 获取是否CCS挂机异常
    isCcsCancelMakeCallException: function () {
      return CallCenter._ccsCancelMakeCallException;
    },
    // 获取是否CCS结束咨询异常
    isCcsAgentConsultBackException: function () {
      return CallCenter._ccsAgentConsultBackException;
    },
    // 获取版本信息
    getVersion: function () {
      return this.version;
    },
    // 获取Callid
    getCallid: function () {
      return CallCenter._callId;
    },
    // 获取Callid匹配的timestamp
    getTimestamp: function () {
      return CallCenter._timestamp;
    },
    // 获取主叫号码
    getCaller: function () {
      return CallCenter._caller;
    },
    // 获取被叫号码
    getCalled: function () {
      return CallCenter._called;
    },
    // 是否智能外呼
    isAuto: function () {
      return CallCenter._auto != 0;
    },
    // 获取WebSocket的连接地址
    getWsurl: function () {
      return CallCenter._wsurl;
    },
    // 获取企业ID
    getCompanyid: function () {
      return CallCenter._companyid;
    },
    // 获取企业简拼
    getAbbreviate: function () {
      return CallCenter._abbreviate;
    },
    // 获取工号
    getOperatorid: function () {
      return CallCenter._operatorid;
    },
    // 获取密码
    getPassword: function () {
      return CallCenter._password;
    },
    // 获取媒体IP
    getMediaip: function () {
      return CallCenter._media_ip;
    },
    // 获取媒体端口
    getMediaport: function () {
      return CallCenter._media_port;
    },
    // 获取SIP编号
    getSipid: function () {
      return CallCenter._sip_id;
    },
    // 获取SIP密码
    getSippwd: function () {
      return CallCenter._sip_pwd;
    },
    // 获取登录方式0手机,1SIP话机,2软话机
    getLoginType: function () {
      return Number(CallCenter._logintype);
    },
    // 是否已经登录
    isLogin: function () {
      return CallCenter._islogin;
    },
    // 是否通话中
    isCalling: function () {
      return CallCenter._calling;
    },
    // 是否外呼
    isOutbound: function () {
      return CallCenter._isCallout;
    },
    //获取当前 logon/offwork/agentidle/agentbusy/after
    getNowStatus: function () {
      return CallCenter._nowStatus;
    },
    // 获取当前状态
    getStatus: function () {
      return CallCenter._status;
    },
    // 获取当前状态文本
    getNowStatusText: function () {
      return CallCenter._statusText;
    },
    // 获取忙碌类型
    getBusyType: function () {
      return CallCenter._busyType;
    },
    // 获取忙碌类型Map
    getBusyTypeMap: function () {
      return CallCenter._busyTypeMap;
    },
    // 获取当前呼叫时长
    getCallDuration: function () {
      return CallCenter._callingtimer;
    },
    // 获取当前状态持续时长
    getStatusDuration: function () {
      if (CallCenter._calling) {
        return CallCenter._callingtimer;
      } else {
        return CallCenter._timerspan;
      }
    },
    // 获取使用的软话机类型
    getClientType: function () {
      return Number(CallCenter._clientType);
    },
    // 获取服务端类型
    getServerType: function () {
      return Number(CallCenter._serverType);
    },
    // 获取是否会议模式
    isMeeting: function () {
      return CallCenter._isMeeting;
    },
    // 获取通话来源,inringing/makecall/monitor/interceptcall/agentinsert
    getCallFrom: function () {
      return CallCenter._calling_from;
    },
    // 获取是否登录前获取技能组
    getavailablegroup: function () {
      return CallCenter._availablegroup;
    },
    // 是否登录前选择技能组
    getSelectionGroup: function () {
      return CallCenter._selectionGroup;
    },
    // 获取被操作的坐席
    getBeOperator: function () {
      return CallCenter._be_operator;
    },
    // 获取是否允许忙碌时外呼
    isAgentBusyCallout: function () {
      return CallCenter._enableAgentBusyCallout;
    },
    // 是否三方会话被咨询侧挂机
    isLeaveConf: function () {
      return CallCenter._leaveconf;
    },
    // 获取IVR流程列表
    getIVRFlowList: function () {
      return CallCenter._ivrFlowList;
    },
    // 获取是否为长签
    isLongSigned: function () {
      return Number(CallCenter._agentType) === 2;  // 预测坐席签入方式:1 短签，2 长签
    },
    // 获取重连时间最大值
    getReconnectMax: function () {
      return CCProps.reconnection.reconnectMax;
    },
    // 获取是否在重连
    getReconnect: function () {
      return CCProps.reconnection.hasDisconnect;
    },
    /* 上面为获取部分参数 */

    /* 下面为对外公布的功能性函数 */
    // 生成简版布局
    draw: function () {
      CallCenter._drawType = 1;
      $("#CallCenter_css_drawAllIcon").remove();
      CallCenter.createCss(CallCenter._thisPath + "CallCenter.css", "CallCenter_css_draw");
      $("#CallCenter_main").remove();
      var e1 = $("<div id=\"CallCenter_main\" class=\"zh\"></div>");
      var e1_1 = $("<div id=\"CallCenter_status_bar\" class=\"agent\"></div>");
      e1_1.bind("click", function (e) {
        $("#CallCenter_status_buts").children().each(function () {
          if ($(this).css("display") != "none") {
            CallCenter.showControl("#CallCenter_status_buts");
            return false;
          }
        });
      });

      // 状态条
      var e1_1_1 = $("<span id=\"CallCenter_status_tiao\" class=\"telbtn green\"></span>");
      var e1_1_1_1 = $("<div class=\"con\"></div>");
      var e1_1_1_1_1 = $("<div class=\"time\" id=\"CallCenter_status_time\">00:00:00</div>");
      var e1_1_1_1_2 = $("<img src=\"" + CallCenter._thisPath + "images/line.png\" class=\"line\"/>");
      var e1_1_1_1_3 = $("<div id=\"CallCenter_status\" class=\"status\">" + UIT("waitConnection") + "</div>");
      var e1_1_1_1_4 = $("<div id=\"CallCenter_phonenum\" class=\"num\"></div>");
      e1_1_1_1.append(e1_1_1_1_1);
      e1_1_1_1.append(e1_1_1_1_2);
      e1_1_1_1.append(e1_1_1_1_3);
      e1_1_1_1.append(e1_1_1_1_4);
      e1_1_1.append(e1_1_1_1);
      var e1_1_1_2 = $("<span id=\"CallCenter_trig\" class=\"trig\"></span>");
      e1_1_1.append(e1_1_1_2);
      var e1_1_1_3 = $("<div class=\"dialog\">" + UIT("warn") + "</div>");
      e1_1_1.append(e1_1_1_3);
      e1_1.append(e1_1_1);

      // 状态按钮
      var e1_1_2 = $("<ul id=\"CallCenter_status_buts\" class=\"softphone\" style=\"display:none;\"></ul>");

      // 空闲
      var e1_1_2_1 = $("<li id=\"CallCenter_free\" style=\"display:none;\"></li>");
      var e1_1_2_1_1 = $("<span class=\"CallCenter_defaultIdleText\" style=\"color:#468847;\">" + CallCenter._defaultIdleText + "</span>");
      e1_1_2_1_1.bind("click", function (e) {
        CallCenter.free();
        $("#CallCenter_status_buts").hide();
        e.stopPropagation();
        return false;
      });
      e1_1_2_1.append(e1_1_2_1_1);
      e1_1_2.append(e1_1_2_1);

      // 忙碌
      var e1_1_2_2 = $("<li class=\"CallCenter_busy\" id=\"CallCenter_busy\" style=\"display:none;\"></li>");
      var e1_1_2_2_1 = $("<span class=\"CallCenter_defaultBusyText\">" + CallCenter._defaultBusyText + "</span>");
      var e1_1_3 = $("<ul id=\"CallCenter_seat_list\" style=\"display:none;\"></ul>");
      e1_1_2_2_1.bind("click", function (e) {
        CallCenter.busy();
        $("#CallCenter_status_buts").hide();
        e.stopPropagation();
        return false;
      });
      e1_1_2_2.append(e1_1_2_2_1);
      e1_1_2.append(e1_1_2_2);
      e1_1.append(e1_1_2);
      e1_1.append(e1_1_3);
      e1.append(e1_1);
      $(document).bind("click", function (e) {
        if ($(e.target).closest("#CallCenter_main").length == 0) {
          $("#CallCenter_status_buts").hide();
          $("#CallCenter_main .enternum").hide();
        }
      });


      // 各显示按钮容器
      var e1_2 = $("<div class=\"telbtns\"></div>");

      // 外呼
      var e1_2_1 = $("<span id=\"CallCenter_calloutbut\" class=\"outphone\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/phone.png\"/>\
                                    <div class=\"dialog\">" + UIT("callout") + "<i class=\"pointer\"></i></div>" + Utils.ui.makeCall() + "\
                                 </span>");
      e1_2_1.bind("click", function () {
        $("#CallCenter_main .enternum").fadeIn();
        $("#CallCenter_main .enternum .numtxt").focus();
      });
      e1_2_1.find(".numtxt").bind(CallCenter.callOutKeypress);
      e1_2_1.find(".btn-makecall").bind("click", function () {
        CallCenter.callout($(this).prev().val());
      });
      e1_2.append(e1_2_1);

      //挂机
      var e1_2_2 = $("<span id=\"CallCenter_hangupbut\" class=\"hang_up\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/hangup.png\"/><div class=\"dialog\">" + UIT("hangup") + "<i class=\"pointer\"></i></div></span>");
      e1_2_2.bind("click", function () {
        CallCenter.cancelmakecall();
      });

      // 静音用户
      var $silence = $("<span id=\"CallCenter_silence\" class=\"hang_up\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/jy1.png\"/><div class=\"dialog\">" + UIT("silence") + "<i class=\"pointer\"></i></div></span>");
      $silence.bind("click", function () {
        CallCenter.silence();
      });
      // 取消静音用户
      var $unsilence = $("<span id=\"CallCenter_unsilence\" class=\"hang_up\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/jy2.png\"/><div class=\"dialog\">" + UIT("unsilence") + "<i class=\"pointer\"></i></div></span>");
      $unsilence.bind("click", function () {
        CallCenter.unsilence();
      });

      //保持
      var e1_2_3 = $("<span id=\"CallCenter_mutebut\" class=\"holding\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/bc.png\"/><div class=\"dialog\">" + UIT("hold") + "<i class=\"pointer\"></i></div></span>");
      e1_2_3.bind("click", function () {
        CallCenter.mute();
      });
      //恢复
      var e1_2_4 = $("<span id=\"CallCenter_unmutebut\" class=\"recovery\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/hf.png\"/><div class=\"dialog\">" + UIT("recovery") + "<i class=\"pointer\"></i></div></span>");
      e1_2_4.bind("click", function () {
        CallCenter.unmute();
      });

      //静音坐席
      var e1_2_22 = $("<span id=\"CallCenter_callmutebut\" class=\"holding\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/jy.png\"/><div class=\"dialog\">" + UIT("callmute") + "<i class=\"pointer\"></i></div></span>");
      e1_2_22.bind("click", function () {
        CallCenter.callmute();
      });
      //取消静音坐席
      var e1_2_23 = $("<span id=\"CallCenter_callunmutebut\" class=\"recovery\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/qxjy.png\"/><div class=\"dialog\">" + UIT("callunmute") + "<i class=\"pointer\"></i></div></span>");
      e1_2_23.bind("click", function () {
        CallCenter.callunmute();
      });

      //转接
      var e1_2_6 = $("<span id=\"CallCenter_transfercallbut\" class=\"switch\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/zj.png\"/><div class=\"dialog\">" + UIT("transfercall") + "<i class=\"pointer\"></i></div></span>");
      e1_2_6.bind("click", function () {
        CallCenter._getIdleAgentFromTC = 1;
        CallCenter.idleagents();
      });
      //结束转接
      var e1_2_61 = $("<span id=\"CallCenter_canceltransfercallbut\" class=\"canceltransfercall\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/jszx.png\"/><div class=\"dialog\">" + UIT("endTransfer") + "<i class=\"pointer\"></i></div></span>");
      e1_2_61.bind("click", function () {
        CallCenter.canceltransfercall();
      });
      //咨询
      var e1_2_7 = $("<span id=\"CallCenter_consultbut\" class=\"consult\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/zx.png\"/><div class=\"dialog\">" + UIT("cousult") + "<i class=\"pointer\"></i></div></span>");
      e1_2_7.bind("click", function () {
        CallCenter._getIdleAgentFromTC = 2;
        CallCenter.idleagents();
      });
      //结束咨询
      var e1_2_8 = $("<span id=\"CallCenter_consultbackbut\" class=\"over_consult\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/jszx.png\"/><div class=\"dialog\">" + UIT("endingConsultation") + "<i class=\"pointer\"></i></div></span>");
      e1_2_8.bind("click", function () {
        CallCenter.agentconsultback();
      });
      //转IVR菜单
      var e1_2_9 = $("<span id=\"CallCenter_ivrbut\" class=\"ivr\" style=\"display:none\"><img src=\"" + CallCenter._thisPath + "images/zcd.png\"/><div class=\"dialog\">" + UIT("ivrmenu") + "<i class=\"pointer\"></i></div></span>");
      e1_2_9.bind("click", function () {
        // *******
        CallCenter.showIVRFlowMenu();
      });
      //三方通话
      var e1_2_11 = $("<span id=\"CallCenter_tripartitetalkbut\" class=\"sf\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/threeCall.png\"/><div class=\"dialog\">" + UIT("transferTripartiteCalls") + "<i class=\"pointer\"></i></div></span>");
      e1_2_11.bind("click", function () {
        CallCenter.tripartitetalk();
      });
      //咨询转接
      var e1_2_12 = $("<span id=\"CallCenter_shiftbut\" class=\"zxzj\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/zj.png\"/><div class=\"dialog\">" + UIT("agentshift") + "<i class=\"pointer\"></i></div></span>");
      e1_2_12.bind("click", function () {
        CallCenter.agentshift();
      });
      //摘机
      var e1_2_19 = $("<span id=\"CallCenter_answer\" class=\"zxzj\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/phone.png\"/><div class=\"dialog\">" + UIT("offHook") + "<i class=\"pointer\"></i></div></span>");
      e1_2_19.bind("click", function () {
        CallCenter.acceptcall();
      });
      //拨号
      var e1_2_20 = $("<span id=\"CallCenter_bohao\" class=\"hang_up\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/bohao.png\"/><div class=\"dialog\">" + UIT("dial") + "<i class=\"pointer\"></i></div></span>");
      e1_2_20.find("img").bind("click", function () {
        if ($("#CallCenter_jianpan").is(":hidden")) {
          $("#CallCenter_jianpan").show();
        } else {
          $("#CallCenter_jianpan").hide();
        }
      });
      e1_2_20.append(CallCenter.dialPlate());
      //刷新
      var e1_2_21 = $("<span id=\"CallCenter_refresh\" style=\"display:none;\"><img src=\"" + CallCenter._thisPath + "images/threeCall.png\"/><div class=\"dialog\">" + UIT("Refresh") + "<i class=\"pointer\"></i></div></span>");
      e1_2_21.bind("click", function () {
        CallCenter.availablegroup();
      });

      e1_2.append(e1_2_2);
      e1_2.append($silence);
      e1_2.append($unsilence);
      e1_2.append(e1_2_3);
      e1_2.append(e1_2_4);
      e1_2.append(e1_2_22);
      e1_2.append(e1_2_23);
      //e1_2.append(e1_2_5);
      e1_2.append(e1_2_6);
      e1_2.append(e1_2_61);
      e1_2.append(e1_2_7);
      e1_2.append(e1_2_8);
      e1_2.append(e1_2_9);
      //e1_2.append(e1_2_10);
      e1_2.append(e1_2_11);
      e1_2.append(e1_2_12);
      e1_2.append(e1_2_19);
      e1_2.append(e1_2_20);
      e1_2.append(e1_2_21);

      e1_2.find("span").hover(function () {
        var obj = $(this);
        obj.find(".dialog").filter(":not(:animated)").fadeIn("fast");
        CallCenter._eventAlertTimeoutId = setTimeout(function () {
          obj.find(".dialog").fadeOut("fast");
        }, 1000);
      }, function () {
        $(this).find(".dialog").fadeOut("fast");
      });
      e1.append(e1_2);
      CallCenter.addBusyButton(e1);
      return e1;
    },
    // 生成全按钮的布局
    drawAllIcon: function () {
      $("#CallCenter_css_draw").remove();
      CallCenter._drawType = 2;
      CallCenter.createCss(CallCenter._thisPath + "CallCenterAllIcon.css", "CallCenter_css_drawAllIcon");
      var e_1 = $("<div id=\"CallCenter_main\" class=\"zh\"></div>");
      var e_a = $("<span class=\"CallCenter_button\"></span>");
      var e_icon = $("<div class=\"CallCenter_icon\"></div>");
      var e_text = $("<div class=\"CallCenter_text\"></div>");

      var e_1_1_a = e_a.clone();
      var e_1_1_icon = e_icon.clone();
      var e_1_1_text = e_text.clone();
      e_1_1_icon.attr("id", "CallCenter_login_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/active/changtai-1.png\"/>");
      e_1_1_text.attr("id", "CallCenter_login_text").html(UIT("login"));
      e_1_1_a.attr("id", "CallCenter_login").append(e_1_1_icon).append(e_1_1_text);
      e_1_1_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.init();
        }
      });
      e_1.append(e_1_1_a);

      var e_1_2_a = e_a.clone();
      var e_1_2_icon = e_icon.clone();
      var e_1_2_text = e_text.clone();
      e_1_2_icon.attr("id", "CallCenter_free_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-2.png\"/>");
      e_1_2_text.attr("id", "CallCenter_free_text").addClass("CallCenter_defaultIdleText").html(CallCenter._defaultIdleText);
      e_1_2_a.attr("id", "CallCenter_free").append(e_1_2_icon).append(e_1_2_text);
      e_1_2_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.free();
        }
      });
      e_1.append(e_1_2_a);

      var e_1_3_a = e_a.clone();
      var e_1_3_icon = e_icon.clone();
      var e_1_3_text = e_text.clone();
      var e_1_3_ul = $("<ul id=\"CallCenter_busyList\"></ul>");
      e_1_3_icon.attr("id", "CallCenter_busy_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-3.png\"/>");
      e_1_3_text.attr("id", "CallCenter_busy_text").addClass("CallCenter_defaultBusyText").html(UIT("busyType"));
      e_1_3_a.attr("id", "CallCenter_busy").addClass("CallCenter_busy").append(e_1_3_icon).append(e_1_3_text).append(e_1_3_ul);
      e_1_3_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          $("#CallCenter_busyList").show();
        }
      });
      e_1.append(e_1_3_a);
      $(document).bind("click", function (e) {
        if ($(e.target).closest("#CallCenter_busy").length == 0) {
          $("#CallCenter_busyList").hide();
        }
      });

      // 静音用户
      var e_1_silence_a = e_a.clone();
      var e_1_silence_icon = e_icon.clone();
      e_1_silence_icon.attr("id", "CallCenter_slience_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/jy1.png\"/>");
      var e_1_silence_text = e_text.clone();
      e_1_silence_text.attr("id", "CallCenter_silence_text").html("座席辅导静音");
      e_1_silence_a.attr("id", "CallCenter_silence").append(e_1_silence_icon).append(e_1_silence_text);
      e_1_silence_a.bind("click", function () {
        if ($(this).find("img").data("status") === "active") {
          // 执行静音用户
          CallCenter.silence();
        }
      });
      e_1.append(e_1_silence_a);
      CallCenter.bindHover(e_1_silence_a);

      // 取消静音用户
      var e_1_unsilence_a = e_a.clone();
      var e_1_unsilence_icon = e_icon.clone();
      var e_1_unsilence_text = e_text.clone();
      e_1_unsilence_icon.attr("id", "CallCenter_unslience_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/jy2.png\"/>");
      e_1_unsilence_text.attr("id", "CallCenter_unsilence_text").html("座席辅导取消静音");
      e_1_unsilence_a.attr("id", "CallCenter_unsilence").append(e_1_unsilence_icon).append(e_1_unsilence_text);
      e_1_unsilence_a.bind("click", function () {
        if ($(this).find("img").data("status") === "active") {
          // 执行取消静音用户
          CallCenter.unsilence();
        }
      });
      e_1.append(e_1_unsilence_a);
      CallCenter.bindHover(e_1_unsilence_a);

      // 保持
      var e_1_4_a = e_a.clone();
      var e_1_4_icon = e_icon.clone();
      var e_1_4_text = e_text.clone();
      e_1_4_icon.attr("id", "CallCenter_mutebut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-20.png\"/>");
      e_1_4_text.attr("id", "CallCenter_mutebut_text").html(UIT("hold"));
      e_1_4_a.attr("id", "CallCenter_mutebut").append(e_1_4_icon).append(e_1_4_text);
      e_1_4_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.mute();
        }
      });
      e_1.append(e_1_4_a);

      // 取消保持
      var e_1_4i_a = e_a.clone();
      var e_1_4i_icon = e_icon.clone();
      var e_1_4i_text = e_text.clone();
      e_1_4i_icon.attr("id", "CallCenter_unmutebut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-4.png\"/>");
      e_1_4i_text.attr("id", "CallCenter_unmutebut_text").html(UIT("unhold"));
      e_1_4i_a.attr("id", "CallCenter_unmutebut").append(e_1_4i_icon).append(e_1_4i_text);
      e_1_4i_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.unmute();
        }
      });
      e_1.append(e_1_4i_a);

      // 坐席静音
      var e_1_19_a = e_a.clone();
      var e_1_19_icon = e_icon.clone();
      var e_1_19_text = e_text.clone();
      e_1_19_icon.attr("id", "CallCenter_callmutebut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-22.png\"/>");
      e_1_19_text.attr("id", "CallCenter_callmutebut_text").html(UIT("callmute"));
      e_1_19_a.attr("id", "CallCenter_callmutebut").append(e_1_19_icon).append(e_1_19_text);
      e_1_19_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.callmute();
        }
      });
      e_1.append(e_1_19_a);

      // 取消坐席静音
      var e_1_20_a = e_a.clone();
      var e_1_20_icon = e_icon.clone();
      var e_1_20_text = e_text.clone();
      e_1_20_icon.attr("id", "CallCenter_callunmutebut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-21.png\"/>");
      e_1_20_text.attr("id", "CallCenter_callunmutebut_text").html(UIT("callunmute"));
      e_1_20_a.attr("id", "CallCenter_callunmutebut").append(e_1_20_icon).append(e_1_20_text);
      e_1_20_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.callunmute();
        }
      });
      e_1.append(e_1_20_a);

      // 咨询
      var e_1_5_a = e_a.clone();
      var e_1_5_icon = e_icon.clone();
      var e_1_5_text = e_text.clone();
      e_1_5_icon.attr("id", "CallCenter_consultbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-5.png\"/>");
      e_1_5_text.attr("id", "CallCenter_consultbut_text").html(UIT("cousult"));
      e_1_5_a.attr("id", "CallCenter_consultbut").append(e_1_5_icon).append(e_1_5_text);
      e_1_5_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter._getIdleAgentFromTC = 2;
          CallCenter.idleagents();
        }
      });
      e_1.append(e_1_5_a);

      // 咨询接回
      var e_1_6_a = e_a.clone();
      var e_1_6_icon = e_icon.clone();
      var e_1_6_text = e_text.clone();
      e_1_6_icon.attr("id", "CallCenter_consultbackbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-6.png\"/>");
      e_1_6_text.attr("id", "CallCenter_consultbackbut_text").html(UIT("agentconsultback"));
      e_1_6_a.attr("id", "CallCenter_consultbackbut").append(e_1_6_icon).append(e_1_6_text);
      e_1_6_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.agentconsultback();
        }
      });
      e_1.append(e_1_6_a);

      // 咨询转接
      var e_1_21_a = e_a.clone();
      var e_1_21_icon = e_icon.clone();
      var e_1_21_text = e_text.clone();
      e_1_21_icon.attr("id", "CallCenter_consultbackbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/zxzj.png\"/>");
      e_1_21_text.attr("id", "CallCenter_consultbackbut_text").html(UIT("agentshift"));
      e_1_21_a.attr("id", "CallCenter_consultbackbut").append(e_1_21_icon).append(e_1_21_text);
      e_1_21_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.agentshift();
        }
      });
      e_1.append(e_1_21_a);

      // 转接
      var e_1_7_a = e_a.clone();
      var e_1_7_icon = e_icon.clone();
      var e_1_7_text = e_text.clone();
      e_1_7_icon.attr("id", "CallCenter_transfercallbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-7.png\"/>");
      e_1_7_text.attr("id", "CallCenter_transfercallbut_text").html(UIT("transfercall"));
      e_1_7_a.attr("id", "CallCenter_transfercallbut").append(e_1_7_icon).append(e_1_7_text);
      e_1_7_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter._getIdleAgentFromTC = 1;
          CallCenter.idleagents();
        }
      });
      e_1.append(e_1_7_a);

      // 会议
      var e_1_8_a = e_a.clone();
      var e_1_8_icon = e_icon.clone();
      var e_1_8_text = e_text.clone();
      e_1_8_icon.attr("id", "CallCenter_tripartitetalkbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-8.png\"/>");
      e_1_8_text.attr("id", "CallCenter_tripartitetalkbut_text").html(UIT("tripartitetalk"));
      e_1_8_a.attr("id", "CallCenter_tripartitetalkbut").append(e_1_8_icon).append(e_1_8_text);
      e_1_8_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.tripartitetalk();
        }
      });
      e_1.append(e_1_8_a);

      // 外呼
      var e_1_12_a = e_a.clone();
      var e_1_12_icon = e_icon.clone();
      var e_1_12_text = e_text.clone();
      e_1_12_icon.attr("id", "CallCenter_calloutbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-12.png\"/>");
      e_1_12_text.attr("id", "CallCenter_calloutbut_text").html(UIT("callout"));
      e_1_12_a.attr("id", "CallCenter_calloutbut").append(e_1_12_icon).append(e_1_12_text);
      e_1_12_a.bind("click", function (e) {
        if ($(this).find("img").data("status") == "active") {
          if ($(this).find("#CallCenter_call_div").length == 0) {
            $(this).append($("#CallCenter_call_div"));
          }
          if ($("#CallCenter_call_div").is(":hidden")) {
            $("#CallCenter_call_div").show().find("input").focus();
          }
        }
      });
      e_1.append(e_1_12_a);

      var call_div = $(Utils.ui.makeCall());
      call_div.find(".numtxt").bind(CallCenter.callOutKeypress);
      call_div.find(".btn-makecall").bind("click", function () {
        CallCenter.callout($(this).prev().val());
        call_div.hide();
      });
      $(document).bind("click", function (e) {
        if ($(e.target).closest("#CallCenter_calloutbut").length == 0 && $(e.target).closest("#CallCenter_innercall").length == 0) {
          call_div.hide();
        }
      });
      e_1_12_a.append(call_div);

      // 内呼
      var e_1_13_a = e_a.clone();
      var e_1_13_icon = e_icon.clone();
      var e_1_13_text = e_text.clone();
      e_1_13_icon.attr("id", "CallCenter_innercall_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-13.png\"/>");
      e_1_13_text.attr("id", "CallCenter_innercall_text").html(UIT("innercall"));
      e_1_13_a.attr("id", "CallCenter_innercall").append(e_1_13_icon).append(e_1_13_text);
      e_1_13_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          if ($(this).find("#CallCenter_call_div").length == 0 || $("#CallCenter_call_div").is(":hidden")) {
            $(this).append($("#CallCenter_call_div"));
            $("#CallCenter_call_div").show().find("input").focus();
          }
        }
      });
      e_1.append(e_1_13_a);

      // IVR
      var e_1_14_a = e_a.clone();
      var e_1_14_icon = e_icon.clone();
      var e_1_14_text = e_text.clone();
      e_1_14_icon.attr("id", "CallCenter_ivrbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-14.png\"/>");
      e_1_14_text.attr("id", "CallCenter_ivrbut_text").html(UIT("ivrmenu"));
      e_1_14_a.attr("id", "CallCenter_ivrbut").append(e_1_14_icon).append(e_1_14_text);
      // *******
      e_1_14_a.bind("click", function () {
        if ($(this).find("img").data("status") === "active") {
          CallCenter.showIVRFlowMenu();
        }
      });
      e_1.append(e_1_14_a);

      // 挂机
      var e_1_15_a = e_a.clone();
      var e_1_15_icon = e_icon.clone();
      var e_1_15_text = e_text.clone();
      e_1_15_icon.attr("id", "CallCenter_hangupbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-15.png\"/>");
      e_1_15_text.attr("id", "CallCenter_hangupbut_text").html(UIT("hangup"));
      e_1_15_a.attr("id", "CallCenter_hangupbut").append(e_1_15_icon).append(e_1_15_text);
      e_1_15_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.cancelmakecall();
        }
      });
      e_1.append(e_1_15_a);

      // 接听
      var e_1_15i_a = e_a.clone();
      var e_1_15i_icon = e_icon.clone();
      var e_1_15i_text = e_text.clone();
      e_1_15i_icon.attr("id", "CallCenter_answer_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-19.png\"/>");
      e_1_15i_text.attr("id", "CallCenter_answer_text").html(UIT("acceptcall"));
      e_1_15i_a.attr("id", "CallCenter_answer").append(e_1_15i_icon).append(e_1_15i_text);
      e_1_15i_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.acceptcall();
        }
      });
      e_1.append(e_1_15i_a);

      // 按键
      var e_1_16_a = e_a.clone();
      var e_1_16_icon = e_icon.clone();
      var e_1_16_text = e_text.clone();
      e_1_16_icon.attr("id", "CallCenter_bohao_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-16.png\"/>");
      e_1_16_text.attr("id", "CallCenter_bohao_text").html(UIT("keypad"));
      e_1_16_a.attr("id", "CallCenter_bohao").append(e_1_16_icon).append(e_1_16_text);
      e_1_16_icon.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          if ($("#CallCenter_jianpan").is(":hidden")) {
            $("#CallCenter_jianpan").show();
          } else {
            $("#CallCenter_jianpan").hide();
          }
        }
      });
      e_1_16_a.append(CallCenter.dialPlate());
      e_1.append(e_1_16_a);

      // 重置
      var e_1_17_a = e_a.clone();
      var e_1_17_icon = e_icon.clone();
      var e_1_17_text = e_text.clone();
      e_1_17_icon.attr("id", "CallCenter_reset_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-17.png\"/>");
      e_1_17_text.attr("id", "CallCenter_reset_text").html(UIT("reset"));
      e_1_17_a.attr("id", "CallCenter_reset").append(e_1_17_icon).append(e_1_17_text);
      e_1_17_a.bind("click", function () {
        CallCenter.initControl();
      });
      e_1.append(e_1_17_a);

      // 退出
      var e_1_18_a = e_a.clone();
      var e_1_18_icon = e_icon.clone();
      var e_1_18_text = e_text.clone();
      e_1_18_icon.attr("id", "CallCenter_logoutbut_icon").append("<img src=\"" + CallCenter._thisPath + "images/all/static/changtai-18.png\"/>");
      e_1_18_text.attr("id", "CallCenter_logoutbut_text").html(UIT("signOut"));
      e_1_18_a.attr("id", "CallCenter_logoutbut").append(e_1_18_icon).append(e_1_18_text);
      e_1_18_a.bind("click", function () {
        if ($(this).find("img").data("status") == "active") {
          CallCenter.logout();
        }
      });
      e_1.append(e_1_18_a);
      e_1.append("<div style=\"clear:both;\"></div>");

      CallCenter.bindHover(e_1_1_a);
      CallCenter.bindHover(e_1_2_a);
      CallCenter.bindHover(e_1_3_a);
      CallCenter.bindHover(e_1_4_a);
      CallCenter.bindHover(e_1_4i_a);
      CallCenter.bindHover(e_1_5_a);
      CallCenter.bindHover(e_1_6_a);
      CallCenter.bindHover(e_1_7_a);
      CallCenter.bindHover(e_1_8_a);
      //CallCenter.bindHover(e_1_9_a);
      //CallCenter.bindHover(e_1_10_a);
      //CallCenter.bindHover(e_1_11_a);
      CallCenter.bindHover(e_1_12_a);
      CallCenter.bindHover(e_1_13_a);
      CallCenter.bindHover(e_1_14_a);
      CallCenter.bindHover(e_1_15_a);
      CallCenter.bindHover(e_1_15i_a);
      CallCenter.bindHover(e_1_16_a);
      CallCenter.bindHover(e_1_17_a);
      CallCenter.bindHover(e_1_18_a);
      CallCenter.bindHover(e_1_19_a);
      CallCenter.bindHover(e_1_20_a);
      CallCenter.bindHover(e_1_21_a);
      CallCenter.addBusyButton(e_1);
      return e_1;
    },
    // 外呼事件绑定
    callOutKeypress: {
      keypress: function (e) {
        e = (e) ? e : ((window.event) ? window.event : "");
        //兼容IE和Firefox获得keyBoardEvent对象的键值
        var key = e.keyCode ? e.keyCode : e.which;
        if (CallCenter._ws_msg_type !== 2) {
          if (key < 48 && key != 8 && key != 13 && key != 46) {
            if (e && e.stopPropagation) {
              e.stopPropagation();
            } else {
              window.event.cancelBubble = true;
            }
            return false;
          }
          if ((key > 57 && key < 64) || (key > 90 && key < 97) || (key > 122)) {
            if (e && e.stopPropagation) {
              e.stopPropagation();
            } else {
              window.event.cancelBubble = true;
            }
            return false;
          }
        }
        if (key == 13) {
          CallCenter.callout(this, e);
          $("#CallCenter_call_div").hide();
        }
      },
      click: function (e) {
        e.preventDefault();
        return false;
      }
    },
    // 使控件恢复最后一次样式
    applyLastStyle: function () {
      var nowStatus = CallCenterStatus.current;
      var statusName = nowStatus.charAt(0).toUpperCase() + nowStatus.substr(1);
      var fun = CallCenterStatus["onenter" + statusName];
      if (typeof (fun) == "function") {
        CallCenter.log("应用样式[" + "onenter" + statusName + "]");
        fun();
      } else {
        CallCenter.log("当前状态无法应用样式[" + statusName + "]");
      }
    },
    // 坐席签入
    ccsLogin: function (operator, password, companyid, logintype, url3cs, client_type, sip) {
      CallCenter.opLogin(operator, password, companyid, logintype, 0, "", url3cs, client_type, 1, sip);
    },
    // 工号密码方式登录
    opLogin: function (operator, pwd, companyid, logintype, auto, logingroups, url_3cs, client_type, server_type, sip_id) {
      // 设置CallCenter._url_3cs
      if (url_3cs) {
        CallCenter.set3CS_url(url_3cs);
      }
      CallCenter.log("companyid:" + companyid + ", logintype:" + logintype + ", auto:" + auto + ", logingroups:" + logingroups + ", url_3cs:" + url_3cs + ", client_type:" + client_type + ", server_type:" + server_type + ", sip_id:" + sip_id);
      if (CallCenter._url_3cs == null || CallCenter._url_3cs == "") {
        CallCenter.log("没有设置服务器连接地址");
        alert("请先设置服务器连接地址");
      } else {
        // 设置服务类型
        Utils.signHandler.setServerType(server_type);

        // 设置SIP号码
        if (!sip_id) {
          sip_id = "";
        }
        var url = CallCenter._url_3cs + "/Api/get_login_info4operator";
        url = CallCenter._url_3cs_ssl ? "https://" + url : "http://" + url;
        url = url + "?operator=" + operator + "&pwd=" + pwd + "&companyid=" + companyid + "&logintype=" + logintype + "&server_type=" + CallCenter._serverType + "&callback=?";
        if (Number(logintype) === 0) {
          url = url + "&phone=" + sip_id;
        } else {
          url = url + "&sip_id=" + sip_id;
        }
        Utils.signHandler.getLoginInfo(url, logintype, auto, logingroups, client_type);
      }
    },
    // sip和密码方式登录
    sipLogin: function (sip_id, sip_pwd, companyid, logintype, auto, logingroups, url_3cs, client_type, server_type) {
      if (url_3cs) {
        CallCenter.set3CS_url(url_3cs);
      }
      CallCenter.log("companyid:" + companyid + ", logintype:" + logintype + ", auto:" + auto + ", logingroups:" + logingroups + ", url_3cs:" + url_3cs + ", client_type:" + client_type + ", server_type:" + server_type);
      if (CallCenter._url_3cs == null || CallCenter._url_3cs == "") {
        CallCenter.log("没有设置服务器连接地址");
        alert("请先设置服务器连接地址");
      } else {
        // 设置服务类型
        Utils.signHandler.setServerType(server_type);

        var url = CallCenter._url_3cs + "/Api/get_login_info4sip";
        url = CallCenter._url_3cs_ssl ? "https://" + url : "http://" + url;
        url = url + "?sip_id=" + sip_id + "&sip_pwd=" + sip_pwd + "&companyid=" + companyid + "&server_type=" + CallCenter._serverType + "&callback=?";
        if (!CallCenter.isLogin()) {
          //CallCenter._loginselect = 2;
          Utils.signHandler.getLoginInfo(url, logintype, auto, logingroups, client_type);
        } else {
          CallCenter.log("已经登录");
        }
      }
    },
    // 登录
    login: function () {
      return CallCenterStatus.login_handle();
    },
    // 登出
    logout: function () {
      CallCenter.setlocalstorage("last_msg", "logout");
      return CallCenterStatus.logout_handle();
    },
    // 示闲
    free: function () {
      if (CallCenterStatus.current === "answer") {
        if (CallCenter._calling_from === "incall" && CallCenter._agentStandby) {
          return CallCenterStatus.agentidle_handle();
        } else {
          return "";
        }
      } else {
        return CallCenterStatus.agentidle_handle();
      }
    },
    // 示忙
    busy: function (busydescr) {
      return CallCenterStatus.agentbusy_handle(busydescr);
    },
    // 接听
    acceptcall: function () {
      return CallCenterStatus.acceptcall_handle();
    },
    // 外呼
    callout: function (obj, e, preview, transmission_number, caseid, userData) {
      var called = "";
      var caller = CallCenter._transmission_number;  // 外呼主叫
      if ("string" === typeof (obj)) {
        called = obj;
      } else if (e.keyCode == 13) {
        called = obj.value;
      }
      if (CallCenter._ws_msg_type === 2) {
        called = called.trim();
      } else {
        called = called.replace(/[^0-9a-zA-Z@]/ig, "");
      }
      if (called == "") {
        return { result: 0, reason: "外呼的号码不能为空" };
      } else {
        if (called.length > 32) {
          called = called.substr(0, 32);
        }
        if (typeof (transmission_number) !== "undefined" && transmission_number != null) {
          caller = transmission_number;
        }
        if (typeof (caseid) === "undefined" || caseid == null) {
          caseid = "";
        }
        return CallCenterStatus.makecall_handle({
          called: called,
          caller: caller,
          preview: preview, caseid: caseid,
          userData: userData
        });
      }
    },
    // 挂断呼叫
    cancelmakecall: function () {
      return CallCenterStatus.cancelmakecall_handle();
    },
    // 按键
    sendDTMF: function (key, role) {
      var sendobj = new CallCenter._sendcmd("senddtmf");
      if (CallCenter._serverType == CallCenter._serverType_ccs && typeof role !== "undefined" && role !== null && role !== "") {
        sendobj.role = role;
      }
      sendobj.num = key;
      CallCenter.send(sendobj);
      return { result: 1, reason: "succeeded" };
    },
    // 保持
    mute: function () {
      return CallCenterStatus.mute_handle();
    },
    // 取消保持
    unmute: function () {
      return CallCenterStatus.unmute_handle();
    },
    // 静音坐席
    callmute: function () {
      CallCenter.handle_callmute()
    },
    // 取消静音坐席
    callunmute: function () {
      CallCenter.handle_callunmute();
    },
    // 静音用户
    silence: function () {
      CallCenterStatus.silence_handle();
    },
    // 取消静音用户
    unsilence: function () {
      CallCenterStatus.unsilence_handle();
    },
    // 咨询
    agentconsult: function (agentid, groupid, userdata) {
      if (agentid) {
        CallCenter._isConsultingGetFrreAgent = false;
        CallCenter._consultSeat = agentid;
        if (CallCenter._isSanfangCall && CallCenter._meetingSeats && CallCenter._meetingSeats.length >= (CallCenter._meetingLimit - 1)) {
          CallCenter.eventAlert(UIT("meeting_limit"));
          return;
        }
        if (CallCenter._leaveconf) {
          CallCenter.handle_agentconsult({
            agentid: agentid,
            groupid: groupid,
            userdata: userdata
          });
        } else {
          return CallCenterStatus.agentconsult_handle({
            agentid: agentid,
            groupid: groupid,
            userdata: userdata
          });
        }
      } else {
        CallCenter._isConsultingGetFrreAgent = true;
        CallCenter.agentFreeData = {};
        CallCenter.agentFreeData.groupid = groupid;
        CallCenter.agentFreeData.userdata = userdata;
        // 获取空闲坐席
        CallCenter.idleagents();
      }
    },
    // 取消咨询
    agentconsultback: function () {
      if (CallCenter._leaveconf) {
        CallCenter.handle_agentconsultabck();
      } else {
        return CallCenterStatus.agentconsultback_handle();
      }
    },
    // 咨询转接
    agentshift: function () {
      return CallCenterStatus.agentshift_handle();
    },
    // 咨询服务
    consulationservice: function (filename, groupid, userdata, num, isFlowname) {
      return CallCenterStatus.consulationservice_handle({
        filename: filename,
        groupid: groupid,
        userdata: userdata,
        num: num,
        isFlowname: isFlowname
      });
    },
    // 三方
    tripartitetalk: function (agentids) {
      if (CallCenter._isSanfangCall) {
        CallCenter.handle_tripartitetalk(agentids);
      } else {
        return CallCenterStatus.tripartitetalk_handle();
      }
    },
    // 转移会议主持人
    transferModerator: function (agentids) {
      return CallCenterStatus.transferModerator_handle(agentids);
    },
    // 转接
    transfercall: function (number, groupid, userdata) {
      return CallCenterStatus.transfercall_handle({
        number: number,
        groupid: groupid,
        userdata: userdata
      });
    },
    // 转接到技能组
    transfergroup: function (groupid, userdata) {
      return CallCenterStatus.transfergroup_handle({
        groupid: groupid,
        userdata: userdata
      });
    },
    // 取消转接
    canceltransfercall: function () {
      return CallCenterStatus.canceltransfercall_handle();
    },
    // 转接IVR服务
    transferservice: function (filename, num, hangupaftertransfer, isFlowname) {
      return CallCenterStatus.transferservice_handle({
        filename: filename,
        num: num,
        hangupaftertransfer: hangupaftertransfer,
        isFlowname: isFlowname
      });
    },
    // 监控坐席
    monitoragent: function (agentids) {
      CallCenter.log("启动监控坐席");
      var sendobj = new CallCenter._sendcmd("monitoragent");
      sendobj.agentids = agentids;
      CallCenter.send(sendobj);
      return { result: 1, reason: "succeeded" };
    },
    // 结束坐席监控
    closeMonitorAgent: function () {
      CallCenter.log("结束坐席监控");
      var sendobj = new CallCenter._sendcmd("unmonitoragent");
      CallCenter.send(sendobj);
      return { result: 1, reason: "succeeded" };
    },
    // 坐席辅导
    instructAgent: function (agentid) {
      if (CallCenter._auto == 0) {
        return CallCenterStatus.instruct_handle(agentid);
      } else {
        var reason = "预测坐席不允许进行坐席辅导";
        CallCenter.eventAlert(reason);
        return { result: 0, reason: reason };
      }
    },
    // 耳语
    whisper: function (agentid) {
      if (CallCenter._auto == 0) {
        return CallCenterStatus.monitor_handle({
          agentid: agentid,
          type: 1
        });
      } else {
        var reason = "预测外呼下不允许进行耳语";
        CallCenter.eventAlert(reason);
        return { result: 0, reason: reason };
      }
    },
    // 监听
    monitor: function (agentid) {
      if (CallCenter._auto == 0) {
        return CallCenterStatus.monitor_handle({
          agentid: agentid
        });
      } else {
        var reason = UIT("nomonitor");
        CallCenter.eventAlert(reason);
        return { result: 0, reason: reason };
      }
    },
    // 强插
    agentinsert: function (agentid) {
      if (CallCenter._auto == 0) {
        return CallCenterStatus.agentinsert_handle(agentid);
      } else {
        var reason = UIT("noainsert");
        CallCenter.eventAlert(reason);
        return { result: 0, reason: reason };
      }
    },
    // 拦截
    agentinterceptcall: function (agentid) {
      if (CallCenter._auto == 0) {
        return CallCenterStatus.agentinterceptcall_handle(agentid);
      } else {
        var reason = UIT("noaintercept");
        CallCenter.eventAlert(reason);
        return { result: 0, reason: reason };
      }
    },
    // 强拆
    agentbreak: function (agentid) {
      var sendobj = new CallCenter._sendcmd("agentbreak");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
      if (typeof (CallCenter.agentbreak_callback) == "function") {
        CallCenter.agentbreak_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 强制示忙
    forcebusy: function (agentid) {
      var sendobj = new CallCenter._sendcmd("forcebusy");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
      return { result: 1, reason: "succeeded" };
    },
    // 强制示闲
    forceidle: function (agentid) {
      var sendobj = new CallCenter._sendcmd("forceidle");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
      if (typeof (CallCenter.forceidle_callback) == "function") {
        CallCenter.forceidle_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 强制签出
    forcelogout: function (agentid) {
      var sendobj = new CallCenter._sendcmd("forcelogout");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
      if (typeof (CallCenter.forcelogout_callback) == "function") {
        CallCenter.forcelogout_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 获取空闲坐席
    idleagents: function (type) {
      CallCenter.log("获取空闲坐席");
      var sendobj = new CallCenter._sendcmd("idleagents");
      type = type || 0;
      sendobj.type = type;
      CallCenter.send(sendobj);

      if (typeof (CallCenter.idleagents_callback) == "function") {
        CallCenter.idleagents_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 获取坐席所在技能组
    agentgroups: function () {
      var sendobj = new CallCenter._sendcmd("agentgroups");
      CallCenter.send(sendobj);
      if (typeof (CallCenter.agentgroups_callback) == "function") {
        CallCenter.agentgroups_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 获取预测外呼可用技能组
    availablegroup: function () {
      var sendobj = new CallCenter._sendcmd("availablegroup");
      sendobj.companyid = CallCenter._companyid;
      sendobj.operatorid = CallCenter._operatorid;
      sendobj.abbreviate = CallCenter._abbreviate;
      CallCenter.send(sendobj);
      if (typeof (CallCenter.availablegroup_callback) == "function") {
        CallCenter.availablegroup_callback();
      }
      return { result: 1, reason: "succeeded" };
    },
    // 重新连接
    reconnection: function () {
      return CallCenterStatus.reconnection();
    },
    /* 以上为对外公布的功能性函数 */

    /* 下面为初始化调用 */
    // 拨号盘
    dialPlate: function () {
      var plate = $(Utils.ui.dialPlate());
      plate.find(".jianpan_1").bind("click", function () {
        //SoftPhone.SendDTMF('1');
        CallCenter.sendDTMF("1");
      });
      plate.find(".jianpan_2").bind("click", function () {
        //SoftPhone.SendDTMF('2');
        CallCenter.sendDTMF("2");
      });
      plate.find(".jianpan_3").bind("click", function () {
        //SoftPhone.SendDTMF('3');
        CallCenter.sendDTMF("3");
      });
      plate.find(".jianpan_4").bind("click", function () {
        //SoftPhone.SendDTMF('4');
        CallCenter.sendDTMF("4");
      });
      plate.find(".jianpan_5").bind("click", function () {
        //SoftPhone.SendDTMF('5');
        CallCenter.sendDTMF("5");
      });
      plate.find(".jianpan_6").bind("click", function () {
        //SoftPhone.SendDTMF('6');
        CallCenter.sendDTMF("6");
      });
      plate.find(".jianpan_7").bind("click", function () {
        //SoftPhone.SendDTMF('7');
        CallCenter.sendDTMF("7");
      });
      plate.find(".jianpan_8").bind("click", function () {
        //SoftPhone.SendDTMF('8');
        CallCenter.sendDTMF("8");
      });
      plate.find(".jianpan_9").bind("click", function () {
        //SoftPhone.SendDTMF('9');
        CallCenter.sendDTMF("9");
      });
      plate.find(".jianpan_0").bind("click", function () {
        //SoftPhone.SendDTMF('0');
        CallCenter.sendDTMF("0");
      });
      plate.find(".jianpan_x").bind("click", function () {
        //SoftPhone.SendDTMF('*');
        CallCenter.sendDTMF("*");
      });
      plate.find(".jianpan_h").bind("click", function () {
        //SoftPhone.SendDTMF('#');
        CallCenter.sendDTMF("#");
      });

      $(document).bind("click", function (e) {
        if ($(e.target).closest("#CallCenter_main").length == 0) {
          $("#CallCenter_jianpan").hide();
        }
      });
      return plate;
    },
    // 获取状态文本
    getStatusText: function (stausKey, busyKey) {
      switch (stausKey) {
        case "free":
          return "空闲";
        case "onwork":
          return "上班";
        case "busy":
          var mapInstance = CallCenter.getBusyTypeMap();
          var busyMap = mapInstance.map();
          return busyMap[busyKey];
        default:
          return "其它";
      }
    },
    // 获取非通话签入坐席
    drawTCBox: function (json) {
      var tcbox = $("<div id=\"CallCenter_TCBox\" class=\"CallCenter_TCBox\"></div>");
      for (var i = 0; i < json.data.length; i++) {
        var group = json.data[i], groupid = group.groupid;
        var groupDiv = $("<div class=\"CallCenter_group\" data-groupid=\"" + groupid + "\">[" + group.groupname + "]</div>");  // 技能组
        var groupUL = $("<ul class=\"CallCenter_ul\"></ul>");  // 坐席
        if (group.agentInfo) {
          for (var k = 0; k < group.agentInfo.length; k++) {
            var agentid = group.agentInfo[k].agentKey;
            var status = group.agentInfo[k].status;
            var busyKey = group.agentInfo[k].busyDesc;
            if (true || agentid != CallCenter.getOperatorid() + "@" + CallCenter.getAbbreviate()) {
              var li = $("<li></li>");
              li.html("<div class=\"CallCenter_agent\">" + agentid + "（" + CallCenter.getStatusText(status, busyKey) + "）</div>");
              var qd = $("<div class=\"CallCenter_agentSelected\">确定</div>");
              qd.click({ agentid: agentid, groupid: groupid }, function (e) {
                if (CallCenter._getIdleAgentFromTC == 1) {
                  // 转接，agentid格式：工号@企业缩写
                  CallCenter.transfercall(e.data.agentid, e.data.groupid);
                } else if (CallCenter._getIdleAgentFromTC == 2) {
                  // 咨询，agentid格式：工号@企业缩写
                  CallCenter.agentconsult(e.data.agentid, e.data.groupid);
                }
                $("#CallCenter_TCBox").remove();
                CallCenter._getIdleAgentFromTC = 0;
              });
              li.append(qd);
              li.append("<div style=\"clear:both;\"></div>");
              groupUL.append(li);
            } else {
              CallCenter.log("坐席帐号必须使用'工号@企业缩写'的标准格式");
            }
          }
        } else {
          for (var k = 0; k < group.agents.length; k++) {
            var agentid = group.agents[k];
            if (agentid != CallCenter.getOperatorid() + "@" + CallCenter.getAbbreviate()) {
              var li = $("<li></li>");
              li.html("<div class=\"CallCenter_agent\">" + agentid + "</div>");
              var qd = $("<div class=\"CallCenter_agentSelected\">确定</div>");
              qd.click({ agentid: agentid, groupid: groupid }, function (e) {
                if (CallCenter._getIdleAgentFromTC == 1) {
                  // 转接，agentid格式：工号@企业缩写
                  CallCenter.transfercall(e.data.agentid, e.data.groupid);
                } else if (CallCenter._getIdleAgentFromTC == 2) {
                  // 咨询，agentid格式：工号@企业缩写
                  CallCenter.agentconsult(e.data.agentid, e.data.groupid);
                }
                $("#CallCenter_TCBox").remove();
                CallCenter._getIdleAgentFromTC = 0;
              });
              li.append(qd);
              li.append("<div style=\"clear:both;\"></div>");
              groupUL.append(li);
            } else {
              CallCenter.log("坐席帐号必须使用'工号@企业缩写'的标准格式");
            }
          }
        }
        tcbox.append(groupDiv).append(groupUL);
      }
      return tcbox;
    },
    // 生成IVR列表
    showIVRFlowMenu: function () {
      if (typeof CallCenter.showIVRFlowMenu_event != "function") {
        var ivrFlowList = CallCenter.getIVRFlowList();
        if (ivrFlowList !== null) {
          CallCenter.log("生成IVR列表");
          var ivrFlowMenu = $("<div id=\"ivr-list\"><div class=\"title_div\"><p>转IVR菜单</p><button class=\"close\" id=\"close-div\">×</button></div><div class=\"list_div\"><ul></ul></div></div>");
          var ivrFlowMenuWrap = ivrFlowMenu.find(".list_div").children();
          var ivrFlowListHTML = "";
          for (var i = 0, len = ivrFlowList.length; i < len; i++) {
            /* @var flowName 流程名称 @var fileName 流程文件名 @var flowType 0用户流程，1系统流程，2总机业务 @var type 流程类型 1转接 2咨询 */
            var flowName = ivrFlowList[i].flowname, fileName = ivrFlowList[i].filename,
              flowType = ivrFlowList[i].flowtype, type = ivrFlowList[i].type;
            ivrFlowListHTML += "<li data-filename=\"" + fileName + "\" data-flowtype=\"" + flowType + "\" data-type=\"" + type + "\"><span>" + flowName + "</span><span>" + (type === 1 ? UIT("transfercall") : UIT("cousult")) + "</span></li>";
          }
          ivrFlowMenuWrap.html(ivrFlowListHTML);
          ivrFlowMenuWrap.children().bind("click", function (e) {
            e.stopPropagation();
            var type = $(this).data("type");
            if (type === 0 || type === 1) {
              CallCenter.log("执行转接IVR满意度");
              CallCenter.transferservice($(this).data("filename"), $(this).data("flowtype"), true);
            }
            if (type === 2) {
              CallCenter.log("执行转接IVR服务");
              CallCenter.consulationservice($(this).data("filename"));
            }
            if (type !== 0 && type !== 1 && type !== 2) {
              CallCenter.log("没有有效的IVR流程");
            }
            ivrFlowMenu.remove();
          });
          ivrFlowMenu.find("#close-div").bind("click", function (e) {
            e.stopPropagation();
            ivrFlowMenu.remove();
          });
          if ($("#CallCenter_ivrbut").find("#ivr-list").length === 0) {
            $("#CallCenter_ivrbut").append(ivrFlowMenu);
          } else {
            ivrFlowMenu.show();
          }
        } else {
          CallCenter.log("CallCenter消息：没有可使用的IVR流程");
        }
      } else {

        CallCenter.showIVRFlowMenu_event();
      }
    },
    // 初始化基本参数，创建CCSWebsocket连接
    init: function (obj) {
      CallCenter.log("初始化");
      if (obj) {//初始化传参数了
        CallCenter.log(obj);
        CallCenter._wsurl = obj.wsurl || CallCenter._wsurl;   //CCS的WebSocket连接地址
        CallCenter._logintype = obj.logintype || CallCenter._logintype;//登录类型,0手机,1sip话机,2软话机
        CallCenter._operatorid = obj.operator_id || CallCenter._operatorid;
        CallCenter._password = obj.password || CallCenter._password;
        CallCenter._abbreviate = obj.abbreviate || CallCenter._abbreviate;
        CallCenter._companyid = obj.company_id || CallCenter._companyid;
        CallCenter._logingroups = obj.logingroups || CallCenter._logingroups;
        CallCenter._auto = obj.auto || CallCenter._auto;
        CallCenter._media_ip = obj.media_ip || CallCenter._media_ip;
        CallCenter._media_port = obj.media_port || CallCenter._media_port;
        CallCenter._sip_id = obj.sip_id || CallCenter._sip_id;
        CallCenter._sip_pwd = obj.sip_pwd || CallCenter._sip_pwd;
        if (obj.url_3cs) {
          CallCenter.set3CS_url(obj.url_3cs);
        }
      }
      // 连接失败或关闭
      if (CallCenter._islogin == false && CallCenter._websocket == null && CallCenter._wsurl != null && CallCenter._wsurl != "") {
        if ("WebSocket" in window) {//支持原生WebSocket
          CallCenter._websocket = new WebSocket(CallCenter._wsurl);
          CallCenter._websocket.onopen = CallCenter.onopen;
          CallCenter._websocket.onmessage = CallCenter.onmessage;
          CallCenter._websocket.onclose = CallCenter.onclose;
          CallCenter._useOcx = false;
        } else {
          //不支持原生WebSocket，尝试使用OCX
          alert("您的浏览器不支持WebSocket！无法进行连接！");
          CallCenter.eventAlert(UIT("browsernotsupport"));
        }
      }
      if (!CallCenter._wsurl) {
        CallCenter.setStatusAndPhoneText(UIT("noccsurl"));
      }
      if (CallCenter._timerId == 0) {//ping线程
        CallCenter._timerId = window.setInterval(CallCenter.timer, 1000);
        CallCenter.ping();
      }
      return this;
    },
    /* 上面为初始化调用 */

    /* 以下为WebSocket功能 */
    // 连接建立
    onopen: function () {
      CallCenter.log("CallCenter消息：建立连接");
      // 刷新后重新连接
      if (CallCenter._refreshReconnection) {
        CallCenter.log("开启了刷新后重连");
        var last_msg_time = CallCenter.getlocalstorage("last_msg_time");
        var nowtime = new Date().getTime();
        if (last_msg_time) {
          var last_msg_interval = nowtime - last_msg_time;
          CallCenter.log("最后一次发消息距离当前时间:" + last_msg_interval + "毫秒");
        } else {
          CallCenter.log("没有最后一次发送消息记录");
        }
      }
      var last_msg = CallCenter.getlocalstorage("last_msg");
      CallCenter.log("最后一次消息" + last_msg);

      // 掉线重连
      if (CallCenterStatus.is(SDK_state.s_disconnect.name)) {
        CallCenter.log("断网重连");
        CallCenterStatus.reconnection_handle();
      }
      // 刷新重连
      else if (CallCenter._refreshReconnection && last_msg_time && //启用刷新后重连
        (nowtime - last_msg_time) < (60 * 1000) && //并且没有超出1分钟
        last_msg != "logout" && //上次消息不是退出
        !CallCenterStatus.is(SDK_state.s_reconnection_fail.name)  // 不是重连失败
      ) {
        CallCenter.log("刷新重连");
        CallCenterStatus.reconnection_handle();  // 执行重连
      } else {
        if (CallCenter.isAuto() && CallCenter.getavailablegroup()) {//是预测外呼并且启用了登录后获取技能组
          CallCenter.setStatusAndPhoneText(UIT("getavailablegroup")).availablegroup();
        } else {
          CallCenterStatus.login_handle();//发送登录
        }
      }
      if (typeof (CallCenter.onopen_callback) == "function") {
        CallCenter.onopen_callback();
      }
      return this;
    },
    // 连接关闭
    onclose: function () {
      CallCenter.log("websocket connect closed");
      CallCenter._websocket = null;
      CallCenter._islogin = false;
      CallCenter._sdk_login = false;
      if (CallCenterStatus.is(SDK_state.s_logout_sending.name)) {//如果为登出操作
        CallCenterStatus.logout();
      } else if (CallCenterStatus.is(SDK_state.s_kick.name)) {//被踢出

      } else if (CallCenterStatus.is(SDK_state.s_authfail.name)) {//验证失败

      } else if (CallCenterStatus.is(SDK_state.s_reconnection_fail.name)) {//重连失败

      } else if (CallCenterStatus.is(SDK_state.s_logout.name)) {//已退出

      } else {
        // 断网重连第一次-初始化计时时间
        if (CallCenterStatus.current !== "disconnect") {
          CallCenter._timerspan = 0;
          CallCenter._callingtimer = 0;
          $("#CallCenter_phonenum").html("");
          $("#CallCenter_status_time").html(CallCenter.secondsToHours(0));
        }

        if (CCProps.reconnection.reconnectMax === 0 || (CallCenter._timerspan <= CCProps.reconnection.reconnectMax)) {
          CCProps.reconnection.hasDisconnect = true;  // 掉线了
          CallCenter._save_message = [];
          Utils.reconnection.reconnection();
          CallCenterStatus.disconnect();
        } else {
          CCProps.reconnection.hasDisconnect = false;
          CallCenter._timerspan = 0;
          CallCenter._callingtimer = 0;
          $("#CallCenter_phonenum").html("");
          $("#CallCenter_status_time").html(CallCenter.secondsToHours(0));
          window.clearInterval(CCProps.reconnectionTime);
          CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("reconnectfail")).log("重连失败");
          CallCenterStatus.current = "nologin";
        }
      }
      if (typeof (CallCenter.onclose_callback) === "function") {
        CallCenter.onclose_callback();
      }
      if (typeof (CallCenter.socket_close_transaction) === "function") {
        CallCenter.socket_close_transaction();
      }
      return this;
    },
    // 连接错误
    onerror: function () {
      CallCenter.log("websocket connect error");
      CallCenter._websocket = null;
      CallCenter._islogin = false;
      if (CallCenterStatus.is(SDK_state.s_logout_sending.name)) {//如果为登出操作
        CallCenterStatus.logout();
      } else if (CallCenterStatus.is(SDK_state.s_kick.name)) {//被踢出

      } else if (CallCenterStatus.is(SDK_state.s_authfail.name)) {//验证失败

      } else if (CallCenterStatus.is(SDK_state.s_reconnection_fail.name)) {//重连失败

      } else if (CallCenterStatus.is(SDK_state.s_logout.name)) {//已退出

      } else {
        CCProps.reconnection.hasDisconnect = true;  // 掉线了
        CallCenterStatus.disconnect();
      }
      if (typeof (CallCenter.onerror_callback) === "function") {
        CallCenter.onerror_callback();
      }
      if (typeof (CallCenter.socket_error_transaction) === "function") {
        CallCenter.socket_error_trasaction();
      }
      return this;
    },
    // 发送消息到ws服务器
    send: function (sendObj) {
      try {
        if (CallCenter._websocket != null) {
          var readyState = ("m_readyState" in CallCenter._websocket ? CallCenter._websocket.m_readyState : CallCenter._websocket.readyState);
          if (readyState == 1) {
            if (!sendObj) {
              sendObj = new CallCenter._sendcmd();
            }
            if (!sendObj.sequence) {
              sendObj.sequence = new Date().getTime();
            }
            if (typeof (onmessage_event) == "function") {
              onmessage_event(sendObj, "send");
            }
            if (CallCenter._ws_msg_type === 1 && sendObj.cmd !== "logon" && sendObj.cmd !== "reconnection") {
              var sendStr = CallCenter.base64.encode(JSON.stringify(sendObj));
            } else {
              var sendStr = JSON.stringify(sendObj);
            }
            CallCenter._websocket.send(sendStr);
            if (sendObj.cmd != "ping" && sendObj.cmd != "pingack") {
              Utils.printSendData(sendObj);
            }
          } else {
            switch (readyState) {
              case 0:
                CallCenter.log("CallCenter:连接状态[连接尚未建立]");
                break;
              case 1:
                CallCenter.log("CallCenter:连接状态[WebSocket的链接已经建立]");
                break;
              case 2:
                CallCenter.log("CallCenter:连接状态[连接正在关闭]");
                break;
              case 3:
                CallCenter.log("CallCenter:连接状态[连接已经关闭或不可用]");
                break;
              default:
                CallCenter.log("CallCenter:连接状态[" + readyState + "]");
            }
          }
        } else {
          CallCenter.log("CallCenter:连接为null");
            let events = CallCenter._events['ws_null'];
            if (typeof (events) != "undefined") {
                for (var key in events) {
                    var fun = events[key];
                    if (typeof (fun) == "function") {
                        try {
                            fun();
                        } catch (ex) {
                            CallCenter.log("调用外部注册事件异常，查看详情需要开启调试模式");
                            CallCenter.log(ex);
                        }
                    }
                }
            }
        }
      } catch (ex) {
        CallCenter.log("CallCenter:发送消息异常");
        for (x in ex) {
          CallCenter.log(x + ":" + ex[x]);
        }
        CallCenter.log(ex);
      }
    },
    // 收到的消息
    onmessage: function (data) {
      try {
        var backData = data.data;
        // 判断是否解密
        if (CallCenter._ws_msg_type === 1) {
          try {
            backData = JSON.stringify(JSON.parse(backData));
          } catch (err) {
            backData = CallCenter.base64.decode(backData);
          }
        }
        var json = eval("(" + backData + ")");
        var type = json.type;// 命令
        var status = parseInt(json.status);//状态 0成功 1失败
        if ((CallCenter.isWebRtc() && SoftPhone.config && !SoftPhone.config.registered) || (CallCenter.isWebCall() && !SoftPhone._registered)) {
          if (!((type === "logon" || type === "reconnection") && status === 0) && CallCenter._sdk_login) {
            CallCenter._save_message.push(data);
            return;
          }
        }
        if (typeof (onmessage_event) == "function") {
          try {
            onmessage_event(json, "recive");
          } catch (e) {
            CallCenter.log("触发onmessage_event异常");
          }
        }
        var cmdtype = json.cmdtype;  // 命令类型，1 非坐席状态，0或不存在 坐席状态
        var reason = "";
        if (typeof (json.reason) != "undefined" && json.reason != null && json.reason != "") {
          reason = json.reason;//信息
        }
        if (type === "after") {
          CallCenter._isCallmute = false;
          json = Utils.addAfterCauseMsg(json);  // 增加话后原因说明
          json.custId && CallCenter.setCastTime(json.custId);
        }
        if (type != "ping" && type != "queuenum") {//不是ping消息打印消息内容
          if (type != "monitorqueue" && type != "monitoragent" && type != "callinfo") {
            if (!cmdtype) {
              CallCenter._lastStatus = CallCenter._status;
              CallCenter._status = type;
            }
            CallCenter._lastmsg = data;

            // 掉线，预测坐席签入退出技能组不更新状态
            if (!CCProps.reconnection.hasDisconnect && !cmdtype) {
              CallCenter._timerspan = 0;
            }

            if (CCProps.reconnection.hasDisconnect && type === "agentbusy") {
              CallCenter._timerspan = 0;
            }

            CallCenter.log("CallCenter接收CCS命令：" + type);
            Utils.printMessageData(json);
          }
        } else {
          CallCenter.setlocalstorage("last_msg", "");
          CallCenter.setlocalstorage("last_msg_time", new Date().getTime());
        }

        switch (type) {
          case "logon"://登录
            switch (status) {
              case 0://登录成功
                if (CallCenter.isWebRtc() || CallCenter.isWebCall()) {
                  CallCenter._sdk_login = true;
                  CallCenter.isWebRtc() ? CallCenter.loginWebRtc(json, "logon") :
                    CallCenter.loginWebCall(json, "logon");
                  return;
                } else {
                  CallCenterStatus.login();
                  if (CallCenter._agentType == 2 && CallCenter.isAuto()) {
                    CallCenter.handle_automakecall();
                  }
                }
                break;
              case 1://验证失败
                CallCenterStatus.authfail();
                CallCenter.eventAlert(reason);
                break;
              case 2://重复登录
                CallCenterStatus.kick();
                break;
              case 3://强制签出
                CallCenterStatus.forcelogout();
                break;
              default:
                CallCenter.setStatusAndPhoneText(UIT("logoncode") + status);
            }
            break;
          case "logout"://登出
            CallCenter.setlocalstorage("last_msg", "logout");
            break;
          case "reconnection"://重连
            switch (status) {
              case 0:
                if (CallCenter.isWebRtc() || CallCenter.isWebCall()) {
                  CallCenter._sdk_login = true;
                  CallCenter.isWebRtc() ? CallCenter.loginWebRtc(json, "reconnection") :
                    CallCenter.loginWebCall(json, "reconnection");
                  return;
                } else {
                  CallCenterStatus.reconnection();
                  if (CallCenter._agentType == 2 && CallCenter.isAuto()) {
                    CallCenter.handle_automakecall();
                  }
                }
                break;
              default:
                CallCenterStatus.reconnection_fail();
                break;
            }
            break;
          case "agentidle"://示闲
            switch (status) {
              case 0:
                CallCenter._mainStatus = "agentidle";
                CallCenterStatus.agentidle();
                CallCenter.taskClearTimeout && clearInterval(CallCenter.taskClearTimeout);
                CallCenter.taskCountdown();
                break;
              default:
                CallCenterStatus.agentidle_fail();
                CallCenter.eventAlert(reason);
            }
            break;
          case "agentbusy"://示忙
            switch (status) {
              case 0:
                CallCenter._mainStatus = "agentidle";
                CallCenterStatus.agentbusy();
                CallCenter.taskClearTimeout && clearInterval(CallCenter.taskClearTimeout);
                CallCenter.taskCountdown();
                break;
              default:
                CallCenterStatus.agentbusy_fail();
                CallCenter.eventAlert(reason);
            }
            break;
          case "playtts"://播放tts
            CallCenterStatus.playtts();
            break;
          case "makecall"://外呼呼叫中
            switch (status) {
              case 0:
                CallCenterStatus.makecall({
                  callid: json.callid,
                  timestamp: json.timestamp
                });
                break;
              default:
                CallCenter.eventAlert(reason);
                //20190925外呼失败直接挂机
                CallCenterStatus.makecall_fail();
            }
            break;
          case "cancelmakecall"://挂断呼叫
            switch (status) {
              case 0:
                CallCenter._ccsCancelMakeCallException = false;
                CallCenterStatus.cancelmakecall();
                break;
              case 2:
                /**
                 * 20190225 挂机失败逻辑处理
                 * webcall登录延迟挂机，CCS自动转话后
                 * 非webcall 自动转入话后
                 **/
                CallCenter._ccsCancelMakeCallException = true;
                if (CallCenter.getLoginType() === 2) {
                  CallCenter.log("ccs挂机处理异常，webcall登录，稍候执行webcall挂机");
                  CallCenter.eventAlert(reason);
                  window.setTimeout(function () {
                    CallCenter.log("执行webcall挂机");
                    window.SoftPhone.ReleaseCall();
                  }, 2000);
                } else {
                  CallCenter.log("ccs挂机处理异常，非webcall登录，执行话后");
                  CallCenterStatus.after();
                }
                break;
              default:
                CallCenter._ccsCancelMakeCallException = false;
                CallCenter.eventAlert(reason);
            }
            break;
          case "inringing"://呼入振铃
            CallCenterStatus.inringing(json);
            break;
          case "innerringing"://内呼来电振铃
            CallCenterStatus.innerringing(json);
            break;
          case "incall"://呼入坐席接听
            CallCenter._mainStatus = "answer";
            CallCenterStatus.incall(json);
            break;
          case "outringing"://外呼时坐席端振铃
            CallCenterStatus.outringing(json);
            break;
          case "outcall"://外呼坐席摘机
            CallCenter._mainStatus = "answer";
            CallCenterStatus.outcall(json);
            break;
          case "calledringing"://被叫振铃
            CallCenterStatus.calledringing(json);
            break;
          case "outboundcall"://预测外呼接通被叫
            CallCenter._mainStatus = "answer";
            console.log(CallCenterStatus.current);
            CallCenterStatus.outboundcall(json);
            break;
          case "answer"://外呼接通被叫
            CallCenter._mainStatus = "answer";
            CallCenterStatus.answer(json);
            break;
          case "consultationcalls":// 咨询通话中-咨询方
            CallCenter._isconsultCall = 1;
            CallCenter._isSanfangCall ? CallCenter._mainStatus = "meetingConsult" : CallCenter._mainStatus = "consult";
            CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
            CallCenterStatus.consultationcalls();
            break;
          case "consultinringing"://咨询来电振铃
            CallCenterStatus.consultinringing(json);
            break;
          case "consultincall":// 咨询来电通话中-被咨询方
            CallCenter._isconsultCall = 2;
            CallCenter._mainStatus = "consulted";
            CallCenterStatus.consultincall();
            break;
          case "transferinringing"://转接来电振铃
            CallCenterStatus.transferinringing(json);
            break;
          case "transferincall"://转接来电通话中
            CallCenter._mainStatus = "answer";
            CallCenterStatus.transferincall(json);
            break;
          case "innercall"://内呼来电通话中
            CallCenter._mainStatus = "innercall";
            CallCenterStatus.innercall();
            break;
          case "innercallout"://内呼失败
            CallCenterStatus.innercall_fail();
            CallCenter._isInnercall = false;  // 内呼失败恢复非内呼标识
            CallCenter._calling = false;      // 内呼失败进入非通话状态
            CallCenter.eventAlert(reason);
            break;
          case "sanfangcall": // 三方通话中
            (CallCenter._isconsultCall === 1 || CallCenter._mainStatus === "meetingConsult" || CallCenter._mainStatus === "meetingMaster") ? CallCenter._mainStatus = "meetingMaster" : CallCenter._mainStatus = "meetingMember";
            CallCenter._isSanfangCall = true;
            CallCenter._isconsultCall = 0;
            CallCenter._leaveconf = false;
            CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
            CallCenterStatus.sanfangcall();
            break;
          case "mute"://保持
            switch (status) {
              case 0://成功
                CallCenter._mainStatus = "mute";
                CallCenterStatus.mute();
                break;
              default://失败
                if (CallCenter._calling_from === "caochCalled") {
                  CallCenterStatus.agent_coach_call_mute_fail();
                } else {
                  CallCenterStatus.mute_fail();
                }
            }
            break;
          case "unmute"://取消保持
            switch (status) {
              case 0://成功
                if (CallCenter._calling_from === "caochCalled") {
                  CallCenterStatus.agent_coach_call_unmute();
                } else {
                  CallCenterStatus.unmute(json);
                }
                break;
              default://失败
                CallCenter.eventAlert(reason);
                CallCenterStatus.unmute_fail();
            }
            break;
          case "callmute"://坐席静音
            switch (status) {
              case 0: // 成功
                CallCenter._isCallmute = true;
                CallCenter.event_callmute();
                break;
              default: // 失败
                CallCenter.eventAlert(reason);
            }
            break;
          case "callunmute"://取消坐席静音
            switch (status) {
              case 0: // 成功
                CallCenter._isCallmute = false;
                CallCenter.toggleMuteIcon();
                break;
              default: // 失败
                CallCenter.eventAlert(reason);
            }
            break;
          case "agentconsult": // 咨询待接通中-咨询方
            switch (status) {
              case 0:
                CallCenter._isconsultCall = 1;
                CallCenterStatus.agentconsult();
                break;
              default:
                CallCenter.eventAlert(reason);
                if (CallCenter._isSanfangCall) {
                  CallCenterStatus.sanfang_agentconsult_fail();
                } else {
                  CallCenterStatus.agentconsult_fail();
                }
            }
            break;
          case "agentconsultback"://取消咨询
            switch (status) {
              case 0:
                CallCenter._ccsAgentConsultBackException = false;
                CallCenter._isconsultCall = 0;
                CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
                if (CallCenter._isSanfangCall) {
                  // 会议中
                  CallCenterStatus.current = "consultationcalls";
                  CallCenter._mainStatus === "meetingConsult" && CallCenter.setMeeting();
                  CallCenter.tripartitetalk(CallCenter._operatorid + "@" + CallCenter._abbreviate);
                } else {
                  CallCenter._mainStatus = "answer";
                  CallCenterStatus.agentconsultback(json);
                }
                break;
              case 2:
                /**
                 * 20190226 取消咨询失败逻辑处理,CCManager故障
                 * webcall登录延迟挂机，CCS自动转话后
                 * 非webcall 自动转入话后
                 **/
                CallCenter._ccsAgentConsultBackException = true;
                CallCenter._isconsultCall = 0;
                if (CallCenter.getLoginType() === 2) {
                  CallCenter.log("ccs取消咨询处理异常，webcall登录，稍候执行webcall挂机");
                  CallCenter.eventAlert(reason);
                  window.setTimeout(function () {
                    CallCenter.log("执行webcall挂机");
                    window.SoftPhone.ReleaseCall();
                  }, 2000);
                } else {
                  CallCenter.log("ccs取消咨询处理异常，非webcall登录，执行话后");
                  CallCenterStatus.after();
                }
                break;
              default:
                CallCenterStatus.agentconsult_fail();
                CallCenter._ccsAgentConsultBackException = false;
            }
            break;
          case "agentshift"://咨询转接
            switch (status) {
              case 0:
                CallCenter._isconsultCall = 0;
                CallCenter._mainStatus = "answer";
                CallCenterStatus.agentshift();
                break;
              case 2:
                /**
                 * 20190226 咨询转接失败逻辑处理，CCManager故障
                 * 提示错误信息
                 */
                CallCenter._isconsultCall = 0;
                CallCenter.eventAlert(reason);
                break;
              default:
                CallCenterStatus.agentshift_fail();
            }
            break;
          case "deviceChanged":// 咨询转接被转接方接收，20181030新增
            /*CallCenterStatus.current = "answer";
            CallCenter.event_deviceChanged();*/
            CallCenter._isconsultCall = 0;
            CallCenter._mainStatus = "answer";
            CallCenterStatus.answer(json);
            break;
          case "transfering"://转接中
            if (CallCenter._calling_from == "transfer") {
              CallCenterStatus.transfering(json);
            } else {
              CallCenterStatus.transfering();
            }
            break;
          case "transfercall"://转接
            switch (status) {
              case 0:
                CallCenterStatus.transfercall();
                break;
              default:
                CallCenterStatus.transfercall_fail(json);
            }
            break;
          case "tripartitetalk": //三方
            switch (status) {
              case 0: //成功
                CallCenterStatus.tripartitetalk();
                break;
              case 2:
                /**
                 * 20190226 咨询转接失败逻辑处理，CCManager故障
                 * 提示错误信息
                 */
                CallCenter.eventAlert(reason);
                break;
              default://失败
                CallCenterStatus.tripartitetalk_fail();
            }
            break;
          case "after"://话后
            CallCenter._mainStatus = "";
            CallCenterStatus.after();
            break;
          case "monitor"://监听
            switch (status) {
              case 0:
                if (json.monitor == 1) {//操作人
                  CallCenterStatus.monitor();
                } else {
                }
                break;
              default:
            }
            break;
          case "monitorringing"://监听来电振铃
            CallCenterStatus.monitorringing();
            break;
          case "monitorincall"://监听通话中
            CallCenterStatus.monitorincall();
            break;
          case "agentinterceptcall"://拦截操作
            CallCenter.eventAlert(reason);
            switch (status) {
              case 0:
                if (json.monitor == 1) {//操作人
                  CallCenterStatus.agentinterceptcall();
                } else {//被操作人
                }
                break;
              default:
                CallCenterStatus.agentinterceptcall_fail();
            }
            break;
          case "intercept"://拦截中
            CallCenterStatus.intercept();
            break;
          case "interceptaltering"://拦截振铃
            CallCenterStatus.interceptaltering();
            break;
          case "interceptcall"://拦截通话中
            CallCenterStatus.interceptcall();
            break;
          case "agentinsert"://强插
            switch (status) {
              case 0:
                if (json.monitor == 1) {//操作人
                  CallCenterStatus.agentinsert();
                } else {
                }
                break;
              default:
            }
            break;
          case "agentinsertringing"://强插振铃
            CallCenterStatus.agentinsertringing();
            break;
          case "agentinsertincall"://强插通话中
            CallCenterStatus.agentinsertincall();
            break;
          case "agentbreak"://强拆
            CallCenter.eventAlert(reason);
            break;
          case "forceidle"://强制示闲
            CallCenter.eventAlert(reason);
            switch (status) {
              case 0:
                if (json.monitor == 1) {//操作人
                } else {//被操作人
                  CallCenterStatus.forceidle();
                }
                break;
              default:
            }
            break;
          case "forcebusy"://强制示忙
            CallCenter.eventAlert(reason);
            switch (status) {
              case 0:
                if (json.monitor == 1) {//操作人
                } else {//被操作人
                  CallCenterStatus.forcebusy();
                }
                break;
              default:
            }
            break;
          case "forcelogout":// 强制签出 20191212
            break;
          case "consulationservice"://咨询服务
            switch (status) {
              case 0:
                CallCenterStatus.consulationservice();
                break;
              case 2:
                /**
                 * 20190226 咨询转接失败逻辑处理，CCManager故障
                 * 提示错误信息
                 */
                CallCenter.eventAlert(reason);
                break;
              default:
                CallCenter.eventAlert(reason);
            }
            break;
          case "transferservice":
            switch (status) {
              case 2:
                /**
                 * 20190226 咨询转接失败逻辑处理，CCManager故障
                 * 提示错误信息
                 */
                CallCenter.eventAlert(reason);
                break;
              default:
                ;
            }
            break;
          case "queuenum"://当前坐席所在技能组队列
            //CallCenter.log("CallCenter：队列等待数：" + json.num);
            break;
          case "siperror"://话机异常
            CallCenterStatus.siperror();
            break;
          case "idleagents"://获取空闲坐席
            if (CallCenter._isConsultingGetFrreAgent == false) {
              if (typeof (CallCenter.idleagents_event) != "function") {
                $("#CallCenter_TCBox").remove();
                if (CallCenter._getIdleAgentFromTC == 1 || CallCenter._getIdleAgentFromTC == 2) {
                  var tcbox = CallCenter.drawTCBox(json);
                  $("#CallCenter_main").append(tcbox);
                  $(document).bind("click", function (e) {
                    if ($(e.target).closest("#CallCenter_main").length == 0) {
                      $("#CallCenter_TCBox").remove();
                      CallCenter._getIdleAgentFromTC = 0;
                    }
                  });
                }
              }
            } else {
              if (CallCenter.agentFreeData) {
                var groupid = CallCenter.agentFreeData.groupid;
                var userData = CallCenter.agentFreeData.userData;
                var data = json.data;
                if (data.length > 0) {
                  var agentsList = [];
                  for (let i = 0; i < data.length; i++) {
                    if (data[i]["groupid"] == groupid) {
                      var agentInfo = data[i].agentInfo;
                      if (agentInfo.length > 0) {
                        for (let j = 0; j < agentInfo.length; j++) {
                          if (agentInfo[j]["status"] == "free") {
                            agentsList.push(agentInfo[j]);
                          }
                        }
                      }
                      // 取随机agentid  (只有空闲坐席的坐席)
                      if (agentsList.length > 0) {
                        var randomAgent = Math.floor(Math.random() * agentsList.length);
                        var agentid = agentsList[randomAgent].agentKey;
                        CallCenter.agentFreeData = {};
                        CallCenter.agentconsult(agentid, groupid, userData);
                      }
                    }
                  }
                }
              }
            }
            break;
          case "ping":
            json.delay = new Date().getTime() - json.sequence;
            // CallCenter.pingack(json.sequence);
            break;
          case "sendmsg"://收到的消息
            break;
          case "monitoragent"://监控坐席
            break;
          case "monitorqueue"://所有技能组队列状态
            break;
          case "agentbegingroup"://进入预测外呼技能组
            break;
          case "agentstopgroup"://退出预测外呼技能组
            break;
          case "getagentstate"://获取指定坐席状态，字符串值，包含 logout:未登陆, idle：空闲,busy：忙碌, after:话后，四个状态
            break;
          case "callinfo"://推送坐席呼叫信息
            break;
          case "availablegroup"://获取坐席所在技能组
            if (json.data && json.data.length > 0) {
              $(".CallCenter_login_group,#CallCenter_login_group_pannel").remove();
              var gids = [];
              for (var i = 0; i < json.data.length; i++) {
                var item = json.data[i];
                gids.push(item.id);
              }
              gids = gids.join(",");//技能组编号
              CallCenter.getTaskName(gids, json.data);
              CallCenter.initControl().setStatusAndPhoneText(UIT("selecttask")).showControl("#CallCenter_status_buts,.CallCenter_login_group");
            } else {
              CallCenter.setStatusAndPhoneText(UIT("noavailabletask")).eventAlert(UIT("noavailabletask"));
            }
            CallCenter.showControl("#CallCenter_refresh");
            break;
          case "agentgroups"://获取坐席可用技能组
            break;
          case "agent2minutelogout"://坐席由于任务完成的签出
            break;
          case "leaveconf":  // 20180921新增事件 三方被咨询方挂断
            CallCenter._leaveconf = true;
            CallCenter._isconsultCall = 0;
            CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
            CallCenter.setMeeting();
            break;
          case "leaveuser":  // 用户退出坐席辅导
            switch (status) {
              case 0:
                CallCenterStatus.current = "leaveuser";
                CallCenter._calling_from = "leaveuser";
                CallCenter.enterGuidance();
                break;
              case 1:
                CallCenter._leaveuserByTransferService = true;
                CallCenter.enterGuidance();
                break;
              default:
                ;
            }
            break;
          case "coach":  // 20190408班长辅导事件
            switch (status) {
              case 0:
                if (Number(json.monitor) === 1) {  // 班长
                  CallCenterStatus.instruct();
                }
                break;
              case 1:
              default:
                // 班长坐席失败处理
                CallCenter.eventAlert(reason);
                CallCenterStatus.instruct_fail();
                break;
            }
            break;
          case "coachringing":  // 20190408班长辅导振铃事件
            CallCenterStatus.instructringing();
            break;
          case "coachcall":  // 20190408班长辅导接听事件
            switch (status) {
              case 0:
                if (Number(json.monitor) === 1) {  // 班长
                  CallCenterStatus.instructincall();
                } else {
                  CallCenterStatus.agentCoachCall();
                }
                break;
              default:
                if (Number(json.monitor) === 1) {  // 班长
                  CallCenterStatus.monitorCoachCallFail(reason);
                }
            }
            break;
          case "silence":
            CallCenterStatus.silence();
            break;
          case "unsilence":
            CallCenterStatus.unsilence();
            break;
          case "followDataUpdated":  // 随路数据变更
            break;
          case "UUIUpdated":  // 用户数据变更
            break;
          case "saveOrUpdataFollowData":  // 随路数据保存成功
            break;
          case "saveOrUpdataUUIData":  // 用户数据保存成功
            break;
          case "monitorcaller":
            break;
          case "memberEnqueue": // 20191012 固定坐席记忆-用户振铃
            break;
          case "memberDequeue":  // 20191012 固定坐席记忆-用户挂机
            break;
          case "callmodelchange":  // 20191029 拨打模式切换通知
            break;
          case "senddtmf":  // 20191118 增加CCS DTMF收号事件
            break;
          case "answercall":  // 20191122 易联话机摘机
            break;
          case "endtask":  // 20191207 任务结束通知
            break;
          case "pullInSanfang": // 被咨询者进入多方-主持人收到事件
            CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
            CallCenter._mainStatus === "meetingMaster" && CallCenter.setMeeting();
            break;
          case "becomeModerator": // 会议中被转为主持人事件
            CallCenter._mainStatus = "meetingMaster";
            CallCenter.showControl("#CallCenter_consultbut");
            CallCenter._meetingSeats = json.inMeetingAgentKeys || [];
            CallCenter.setMeeting();
            break;
          case "transferModerator": // 会议中被转为主持人事件
            CallCenter._mainStatus = "meetingMember";
            CallCenterStatus.sanfangcall();
            break;
          default:
            if (json && json.cmd === "sendAgentMessage") {
              var sendAgentMessage = CallCenter._events["sendAgentMessage"];
              if (typeof (sendAgentMessage) != "undefined") {
                for (var key in sendAgentMessage) {
                  var fun = sendAgentMessage[key];
                  if (typeof (fun) == "function") {
                    try {
                      fun(json);
                    } catch (ex) {
                      CallCenter.log("调用外部注册事件异常，查看详情需要开启调试模式");
                      CallCenter.log(ex);
                    }
                  }
                }
              }
            } else {
              CallCenter.log("CallCenter：未知的命令，" + type);
            }
        }
        if (CallCenter._isConsultingGetFrreAgent && type === "idleagents") {
          // 咨询技能组，获取空闲坐席，不处理回调
          CallCenter._isConsultingGetFrreAgent = false;
        } else {
          var eventFun = window.CallCenter[type + "_event"];
          if (typeof (eventFun) == "function") {//是否有外部注册回调函数
            try {
              eventFun(json);
            } catch (ex) {
              CallCenter.log("调用外部注册事件异常，查看详情需要开启调试模式");
              CallCenter.log(ex);
            }
          }
          var events = CallCenter._events[type];//是否有注册事件
          if (typeof (events) != "undefined") {
            for (var key in events) {
              var fun = events[key];
              if (typeof (fun) == "function") {
                try {
                  fun(json);
                } catch (ex) {
                  CallCenter.log("调用外部注册事件异常，查看详情需要开启调试模式");
                  CallCenter.log(ex);
                }
              }
            }
          }
          var transactionListener = window.CallCenter[type + "_transaction"];
          if (typeof (transactionListener) === "function") {
            try {
              transactionListener(json);
            } catch (e) {
              CallCenter.log("内部注册事件执行失败", e);
            }
          }
          // 超时重连
          Utils.reconnection.reconnection();
        }
      } catch (ex) {
        console.error(ex);
        //alert(data.data);
      }
    },
    reconnections: function () {
      CCProps.reconnection.hasDisconnect = true;  // 掉线了
      CallCenter._save_message = [];
      Utils.reconnection.reconnection();
      CallCenterStatus.disconnect();
    },
    /* 以上为WebSocket功能 */

    /* 以下为SDK内部调用功能 */
    // 获取任务名称
    getTaskName: function (groupids, data) {
      var url = CallCenter._url_3cs + "/Api/grouptasklist";
      if (CallCenter._url_3cs_ssl) {
        url = "https://" + url;
      } else {
        url = "http://" + url;
      }
      //根据技能组编号查询技能组信息
      url = url + "?companyid=" + CallCenter._companyid + "&groupids=" + groupids + "&callback=?";
      $.ajax({
        url: url,
        headers: {
          "CSRF-TOKEN": CallCenter.getUUID()
        },
        dataType: "jsonp",
        success: function (json) {
          if (json.code == "0000") {
            if (json.data) {
              var taskData = json.data;
              for (var i = 0; i < data.length; i++) {
                var item = data[i];
                var taskName = item.name + "(技能组)";//默认显示技能组名称
                if (taskData) {
                  for (var k = 0; k < taskData.length; k++) {
                    if (item.id == taskData[k].groupid) {//找到技能组id与任务所属技能组id相同
                      taskName = taskData[k].taskname;//显示任务名称
                      break;
                    }
                  }
                  CallCenter.addLoginTask(taskName, item.id, item.status);
                }
              }
            } else {
              alert("没有查到任何数据");
            }
          } else {
            alert("获取任务名称失败，错误代码：" + json.code);
          }
        }
      });
    },
    // ping
    ping: function () {
      if (CallCenter._islogin) {
        var sendobj = new CallCenter._sendcmd();
        CallCenter.send(sendobj);
      }
      window.clearTimeout(CallCenter._pingId);
      CallCenter._pingId = window.setTimeout(CallCenter.ping, CallCenter._pingInterval);//循环发送ping包
    },
    // PING-ACK
    pingack: function (sequence) {
      CallCenter.log("sequence:" + sequence);
      var sendobj = new CallCenter._sendcmd("pingack");
      sendobj.sequence = sequence;
      CallCenter.send(sendobj);
      return this;
    },
    // 界面计时器
    timer: function () {
      if (CallCenter._calling) {
        $("#CallCenter_status_time").html(CallCenter.secondsToHours(CallCenter._callingtimer++));
      } else {
        $("#CallCenter_status_time").html(CallCenter.secondsToHours(CallCenter._timerspan++));
      }
    },
    // 秒转时间(HH:mm:ss)
    secondsToHours: function (sec) {
      var hrs = Math.floor(sec / 3600);
      var min = Math.floor((sec % 3600) / 60);
      sec = sec % 60;
      sec = sec < 10 ? "0" + sec : sec;
      min = min < 10 ? "0" + min : min;
      hrs = hrs < 10 ? "0" + hrs : hrs;
      return hrs + ":" + min + ":" + sec;
    },
    // 初始化控件(隐藏所有控件)
    initControl: function () {
      if (!CallCenterStatus.is(SDK_state.s_disconnect.name)) {
        CallCenter.hideControl("#CallCenter_canceltransfercallbut,#CallCenter_refresh,#CallCenter_status_buts,#CallCenter_login,#CallCenter_jianpan,#CallCenter_bohao,#CallCenter_answer,#CallCenter_calloutbut,#CallCenter_hangupbut,#CallCenter_silence,#CallCenter_unsilence,#CallCenter_mutebut,#CallCenter_unmutebut,#CallCenter_callmutebut,#CallCenter_callunmutebut,#CallCenter_logoutbut,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_consultbackbut,#CallCenter_ivrbut,#CallCenter_tripartitetalkbut,#CallCenter_shiftbut,.CallCenter_login_group,.CallCenter_busy,#CallCenter_free,#CallCenter_phonenum,#CallCenter_monitor,#CallCenter_agentinsert,#CallCenter_agentbreak,#CallCenter_innercall,#CallCenter_reset");
        if (!CCProps.reconnection.hasDisconnect) {
          $("#CallCenter_status").html(CallCenter._defaultBusyText);
          $("#CallCenter_status_tiao").removeClass("green").addClass("org");
          $("#CallCenter_phonenum").html("");
          CallCenter._timerspan = 0;
          $("#CallCenter_status_time").html(CallCenter.secondsToHours(0));
        }
        if (CCProps.reconnection.hasDisconnect && CallCenter._islogin) {
          CCProps.reconnection.hasDisconnect = false;
        }
      }
      return this;
    },
    // 显示某些控件
    showControl: function (ctrlId) {
      if (CallCenter._drawType == 1) {//布局为简版布局
        if (ctrlId.indexOf("CallCenter_busy") != -1 || ctrlId.indexOf("CallCenter_free") != -1) {
          $("#CallCenter_status_buts,#CallCenter_trig").show();
        }
        $(ctrlId).show();
      } else {//全按钮布局
        if (CallCenter._islogin) {
          ctrlId += ",#CallCenter_logoutbut";
        }
        var src = $(ctrlId).each(function (i, e) {
          if (typeof (e) != "undefined") {
            var src = $(e).find(".CallCenter_icon img").attr("src");
            if (src) {
              src = src.replace("static", "active");
              src = src.replace("hover", "active");
              $(e).find(".CallCenter_icon img").attr("src", src).data("status", "active");
            }
          }
        });
      }
      return this;
    },
    // 隐藏某些控件
    hideControl: function (ctrlId) {
      if (CallCenter._drawType == 1) {//布局为简版布局
        $(ctrlId).hide();
      } else {//全按钮布局
        if (CallCenter._islogin) {
          ctrlId += ",#CallCenter_login";
        }
        var src = $(ctrlId).each(function (i, e) {
          if (typeof (e) != "undefined") {
            var src = $(e).find(".CallCenter_icon img").attr("src");
            if (src) {
              src = src.replace("active", "static");
              src = src.replace("hover", "static");
              $(e).find(".CallCenter_icon img").attr("src", src).data("status", "static");
            }
          }
        });
      }
      if (CallCenter._islogin) {
        CallCenter.showControl("#CallCenter_logoutbut");
      } else {
        CallCenter.showControl("#CallCenter_login");
      }
      return this;
    },
    // 显示呼叫过程中的控件
    showCallingControl: function () {
      CallCenter.showControl("#CallCenter_hangupbut,#CallCenter_mutebut,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut,#CallCenter_phonenum");
      if (CallCenter._isCallout && CallCenter._logintype == CallCenter._loginType_web || CallCenter._logintype == CallCenter._loginType_sip) {//软话机或SIP话机方式登录
        CallCenter.showControl("#CallCenter_bohao");
      }
      if (CallCenter._isCallout && CallCenter._calloutHideTCButton) {//外呼，并且要求隐藏咨询转接
        CallCenter.hideControl("#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut");
      }
      if (CallCenter._isMeeting) {//已开启会议模式，隐藏
        CallCenter.hideControl("#CallCenter_mutebut,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut");
      }
      CallCenter.toggleMuteIcon();
      return this;
    },
    // 判断静音按钮
    toggleMuteIcon: function () {
      if (CallCenter._isCallmute) { // 是否静音中
        CallCenter.hideControl("#CallCenter_callmutebut").showControl("#CallCenter_callunmutebut");
      } else {
        CallCenter.hideControl("#CallCenter_callunmutebut").showControl("#CallCenter_callmutebut");
      }
    },
    /**----------------------控件部分结束-------------------------*/

    // 设置状态文字和号码
    setStatusAndPhoneText: function (text) {
      CallCenter._statusText = text;
      $("#CallCenter_status").text(text);
      var phonenum = "";
      if (CallCenter._isCallout) {
        phonenum = CallCenter.getCalled();
      } else {
        phonenum = CallCenter.getCaller();
      }
      CallCenter._desNo ? phonenum = CallCenter._desNo : "";
      $("#CallCenter_phonenum").html(CallCenter.filterPhone(phonenum));
      if (typeof (CallCenter.setStatusAndPhoneText_event) == "function") {
        CallCenter.setStatusAndPhoneText_event(text);
      }
      return this;
    },
    // 事件提醒内容
    eventAlert: function (text) {
      if (CallCenter._eventAlertTimeoutId != 0) {
        clearTimeout(CallCenter._eventAlertTimeoutId);  // 停止计时
      }
      if (text) {
        $("#CallCenter_status_tiao .dialog").html(text + "<i class=\"pointer\"></i>").filter(":not(:animated)").fadeIn("fast");
        CallCenter._eventAlertTimeoutId = setTimeout(function () {
          $("#CallCenter_status_tiao .dialog").fadeOut("fast");
        }, 2000);
        if (typeof (CallCenter.eventAlert_event) == "function") {
          CallCenter.eventAlert_event(text);
        }
      }
      return this;
    },
    // 设置状态条为绿色
    setGreenClass: function () {
      $("#CallCenter_status_tiao").removeClass("org").addClass("green");
      return this;
    },
    // 设置状态条为橙色
    setOrgClass: function () {
      $("#CallCenter_status_tiao").removeClass("green").addClass("org");
      return this;
    },
    // 返回当前日期+时间
    dateNow: function () {
      var date = new Date();
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      var h = date.getHours();
      var mm = date.getMinutes();
      var s = date.getSeconds();
      var sss = date.getMilliseconds();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (mm < 10) {
        mm = "0" + mm;
      }
      if (s < 10) {
        s = "0" + s;
      }
      if (sss < 10) {
        sss = sss + "00";
      } else if (sss < 100) {
        sss = sss + "0";
      }
      return y + "-" + m + "-" + d + " " + h + ":" + mm + ":" + s + "." + sss;
    },
    // 生成uuid
    getUUID: function () {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == "x" ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    // 过滤号码
    filterPhone: function (phone) {
      if (!CallCenter._hidePhone) {
        return phone;
      }
      if (phone.length > 7) {
        var s = (phone.indexOf("0") == 0) ? phone.substr(1) : phone;//如果首位为0，去掉0
        var s2 = parseInt(s.substr(0, 2));//截取前两位
        if (s2 > 10 && s2 < 20) {//如果在10和20区间，判断为手机号
          return s.substr(0, 3) + "****" + s.substr(7);
        } else {//判断为固话，如果号段在30以下，判断为3位号段，010-029，否则为4位号段
          if (s2 < 30) {
            if (phone.length > 8) {
              return phone.substr(0, 3) + "****" + phone.substr(7);
            } else {
              return "****" + phone.substr(4);
            }
          } else {
            if (phone.length > 10) {
              return phone.substr(0, 4) + "****" + phone.substr(8);
            } else {
              return "****" + phone.substr(4);
            }
          }
        }
      } else {
        return phone;
      }
    },
    // 添加忙碌按钮
    addBusyButton: function (CallCenter_main) {
      if (CallCenter._busyTypeMap.size() > 0) {
        var keyList = CallCenter._busyTypeMap.keySet();
        for (var i = 0; i < keyList.length; i++) {
          var typeId = keyList[i];
          var showText = CallCenter._busyTypeMap.get(typeId);
          var exist = CallCenter_main.find("#CallCenter_busy" + typeId).length > 0;
          if (CallCenter._drawType == 1) {//简版布局
            if (typeId == 0) {
              continue;
            }
            var li = $("<li class=\"CallCenter_busy\" id=\"CallCenter_busy" + typeId + "\"></li>");
            // var li_a = $('<span>' + showText + '</span>');
            var li_a = $("<span></span>");
            li_a.text(showText);
            li_a.bind("click", { type: typeId }, function (e) {
              CallCenter.setlocalstorage("busytype", e.data.type);// 记录忙碌类型
              CallCenter.busy(e.data.type);
              $("#CallCenter_status_buts").hide();
              e.stopPropagation();
              return false;
            });
            li.append(li_a);
            if (exist) {
              CallCenter_main.find("#CallCenter_busy" + typeId).replaceWith(li);
            } else {
              CallCenter_main.find("#CallCenter_status_buts").append(li);
            }
          } else if (CallCenter._drawType == 2) {//全按钮布局
            var e_1_3_a = $("<li class=\"CallCenter_busyList_li\"></li>");
            var e_1_3_icon = $("<div class=\"CallCenter_icon\"></div>");
            var e_1_3_text = $("<div class=\"CallCenter_text\"></div>");
            e_1_3_icon.attr("id", "CallCenter_busy_icon" + typeId).append("<img src=\"images/all/static/changtai-3.png\"/>");
            e_1_3_text.attr("id", "CallCenter_busy_text" + typeId).text(showText);
            e_1_3_a.attr("id", "CallCenter_busy" + typeId).addClass("CallCenter_busy").append(e_1_3_icon).append(e_1_3_text);
            e_1_3_a.bind("click", { type: typeId }, function (e) {
              if ($(this).find("img").data("status") == "active") {
                CallCenter.busy(e.data.type);
                $("#CallCenter_busyList").hide();
                e.preventDefault();
                return false;
              }
            });
            if (exist) {
              CallCenter_main.find("#CallCenter_busy" + typeId).replaceWith(e_1_3_a);
            } else {
              CallCenter_main.find("#CallCenter_busyList").append(e_1_3_a);
            }
          }
        }
        if (CallCenter._drawType == 2) {
          CallCenter_main.find("#CallCenter_busyList").width(CallCenter_main.find(".CallCenter_busyList_li").length * 60);
        }
      }
      return this;
    },
    // 移除忙碌按钮
    removeBusyButton: function (typeId) {
      $("#CallCenter_busy" + typeId).remove();
      CallCenter._busyTypeMap.remove(typeId);
      return this;
    },
    // 生成会议下拉
    setMeeting: function () {
      $("#CallCenter_seat_list").html("");
      var list = [];
      if (CallCenter._mainStatus === "meetingConsult") {
        list.push({
          operator: CallCenter._consultSeat,
          status: "consulted"
        });
      }
      if (CallCenter._meetingSeats && CallCenter._meetingSeats.length > 0) {
        for (var i = 0; i < CallCenter._meetingSeats.length; i++) {
          if (CallCenter._meetingSeats[i] !== (CallCenter._operatorid + "@" + CallCenter._abbreviate)) {
            list.push({
              operator: CallCenter._meetingSeats[i],
              status: "meetingMember"
            });
          }
        }
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].status !== "meetingMaster") {
          var li = $("<li><span>" + list[i].operator + "</span></li>");
          if (list[i].status === "meetingMember") {
            if (CallCenter._mainStatus === "meetingMaster") {
              var overMeet = $("<span ids=" + list[i].operator + ">" + UIT("end_meeting") + "</span>");
              overMeet.unbind("click").bind("click", function () {
                CallCenter.handle_leavemeeting($(this).attr("ids"));
              });
              li.append(overMeet);
            }
          } else {
            var overConsult = $("<span>" + UIT("end_consult") + "</span>");
            overConsult.unbind("click").bind("click", function () {
              CallCenter.agentconsultback();
            });
            var enterMeet = $("<span>" + UIT("start_meeting") + "</span>");
            enterMeet.unbind("click").bind("click", function () {
              CallCenter.tripartitetalk();
            });
            li.append(overConsult);
            li.append(enterMeet);
          }
          $("#CallCenter_seat_list").append(li);
        }
      }
      list.length > 0 ? $("#CallCenter_seat_list").show() : $("#CallCenter_seat_list").hide();
    },
    // 设置3CS地址
    set3CS_url: function (url) {
      if (typeof (url) != "undefined" && url != null && url != "" && url.length > 8 && CallCenter._url_3cs != url) {
        CallCenter._url_3cs = url;
        if (CallCenter._url_3cs.indexOf("http://") == 0) {
          CallCenter._url_3cs = CallCenter._url_3cs.substr(7, CallCenter._url_3cs.length);
        } else if (CallCenter._url_3cs.indexOf("https://") == 0) {
          CallCenter._url_3cs = CallCenter._url_3cs.substr(8, CallCenter._url_3cs.length);
          CallCenter._url_3cs_ssl = true;
        }
        if (CallCenter._url_3cs.indexOf("ws://") == 0) {
          CallCenter._url_3cs = CallCenter._url_3cs.substr(5, CallCenter._url_3cs.length);
        } else if (CallCenter._url_3cs.indexOf("wss://") == 0) {
          CallCenter._url_3cs = CallCenter._url_3cs.substr(6, CallCenter._url_3cs.length);
          CallCenter._url_3cs_ssl = true;
        }
        if (CallCenter._url_3cs != null && CallCenter._url_3cs != "" && CallCenter._url_3cs.lastIndexOf("/") == (CallCenter._url_3cs.length - 1)) {
          CallCenter._url_3cs = CallCenter._url_3cs.substr(0, CallCenter._url_3cs.length - 1);
        }
        CallCenter.log("设置3CS_url为：" + CallCenter._url_3cs);
      }
    },
    // 获取3CS地址
    get3CS_url: function () {
      return (CallCenter._url_3cs_ssl ? "https://" : "http://") + CallCenter._url_3cs;
    },
    // 添加登录的任务
    addLoginTask: function (showText, groupId, status) {
      if (CallCenter._drawType == 1) {
        var li = $("<li class=\"CallCenter_login_group\" id=\"CallCenter_group_" + groupId + "\"></li>");
        var li_span = $("<span></span>");
        if (status == 2) {
          li_span.html(showText + "(暂停中)");
          li_span.css("color", "#CCCCCC");
        } else {
          li_span.html(showText + "(运行中)");
          li_span.bind("click", { groupId: groupId }, function (e) {
            CallCenter.setLoginGroups(e.data.groupId);
            CallCenter.login();
            $("#CallCenter_status_buts").hide();
          });
        }
        li.append(li_span);
        $("#CallCenter_status_buts").append(li);
      } else {
        var pannel;
        if ($("#CallCenter_login_group_pannel").length == 0) {
          pannel = $("<div id=\"CallCenter_login_group_pannel\"></div>");
          var offset = $("#CallCenter_login").offset();
          var left = offset.left || 0;
          var top = offset.top || 0;
          pannel.css({
            "postion": "absolute", "left": left, "top": top + $("#CallCenter_login").height()
          });
          $("#CallCenter_main").prepend(pannel);
        }
        pannel = $("#CallCenter_login_group_pannel");
        var div = $("<div class=\"CallCenter_login_group\" id=\"CallCenter_group_" + groupId + "\"></div>");

        if (status == 2) {
          div.html(showText + "(暂停中)");
          div.css("color", "#CCCCCC");
        } else {
          div.html(showText + "(运行中)");
          div.bind("click", { groupId: groupId }, function (e) {
            CallCenter.setLoginGroups(e.data.groupId);
            CallCenter.login();
            pannel.hide();
          });
        }
        pannel.append(div);
      }
    },
    // 添加可登录技能组
    addLoginGroup: function (ws_url, media_ip, media_port, showText, groupId) {
      if (CallCenter._drawType == 1) {
        var li = $("<li class=\"CallCenter_login_group\" id=\"CallCenter_group_" + groupId + "\"></li>");
        var li_span = $("<span></span>");
        li_span.html(showText);
        li_span.bind("click", {
          ws_url: ws_url, media_ip: media_ip, media_port: media_port, groupId: groupId
        }, function (e) {
          CallCenter._wsurl = e.data.ws_url;           //ccs地址
          CallCenter._media_ip = e.data.media_ip;      //媒体地址
          CallCenter._media_port = e.data.media_port;  //媒体端口
          CallCenter._logingroups = e.data.groupId;    //登录到的技能组
          CallCenter.init();
          $("#CallCenter_status_buts").hide();
          e.stopPropagation();
          return false;
        });
        li.append(li_span);
        $("#CallCenter_status_buts").append(li);
      } else {
        var pannel;
        if ($("#CallCenter_login_group_pannel").length == 0) {
          pannel = $("<div id=\"CallCenter_login_group_pannel\"></div>");
          var offset = $("#CallCenter_login").offset();
          var left = offset.left || 0;
          var top = offset.top || 0;
          pannel.css({
            "postion": "absolute", "left": left, "top": top + $("#CallCenter_login").height()
          });
          $("#CallCenter_main").prepend(pannel);
        }
        pannel = $("#CallCenter_login_group_pannel");
        var div = $("<div class=\"CallCenter_login_group\" id=\"CallCenter_group_" + groupId + "\"></div>");

        div.html(showText);
        div.bind("click", {
          ws_url: ws_url, media_ip: media_ip, media_port: media_port, groupId: groupId
        }, function (e) {
          CallCenter._wsurl = e.data.ws_url;           //ccs地址
          CallCenter._media_ip = e.data.media_ip;      //媒体地址
          CallCenter._media_port = e.data.media_port;  //媒体端口
          CallCenter._logingroups = e.data.groupId;    //登录到的技能组
          CallCenter.init();
          pannel.hide();
        });
        pannel.append(div);
      }
    },
    // 打印日志
    log: function (c, source, send3cs) {
      if (CallCenter._nolog) {
        return this;
      }
      var src = source ? source : "CallCenter";
      var send = send3cs ? send3cs : true;

      if (window.console && window.console.log) {
        if (typeof (c) === "string") {
          if (c === "WebRTC") {
            c = "[" + CallCenter.dateNow() + "] " + "WebRTC：" + "消息：" + (typeof source === "string" ? source : JSON.stringify(source));
          } else {
            c = "[" + CallCenter.dateNow() + "] " + src + "消息：" + c;
          }
        } else {
          c = "[" + CallCenter.dateNow() + "] " + src + "消息：" + JSON.stringify(c);
        }
        window.console.log(c);
      }

      return this;
    },
    // 发送消息到3CS
    send_3cs: function (sendObj) {
    },
    // IE9以下创建WS连接
    newWebSocket: function (url) {
      CallCenter._websocket_ocx = document.createElement("object");
      if (window.ActiveXObject || "ActiveXObject" in window) {
        CallCenter._websocket_ocx.classid = "CLSID:4B99B6A3-777E-4DB9-87A9-A0AE3E13F6BC";
        CallCenter._websocket_ocx.width = 1;
        CallCenter._websocket_ocx.height = 1;
        CallCenter._websocket_ocx.style.position = "fixed";
        CallCenter._websocket_ocx.style.bottom = "0px";
        CallCenter._websocket_ocx.style.left = "0px";
        document.body.appendChild(CallCenter._websocket_ocx);
        CallCenter._websocket_ocx.setwsurl(url);
      }
      return CallCenter._websocket_ocx;
    },
    // 全按钮布局绑定事件
    bindHover: function (el) {
      el.bind("mouseover", function () {
        var src = $(this).find(".CallCenter_icon img").attr("src");
        $(this).find(".CallCenter_icon img").attr("src", src.replace("static", "hover"));
      });
      el.bind("mouseout", function () {
        var src = $(this).find(".CallCenter_icon img").attr("src");
        $(this).find(".CallCenter_icon img").attr("src", src.replace("hover", "static"));
      });
    },
    // 获取当前文件路径
    getPath: function () {
      if (!CallCenter._thisPath) {
        var js = document.scripts;
        for (var i = 0; i < js.length; i++) {
          var script = js[i];
          var jsPath = script.src;
          if (jsPath.indexOf("CallCenter.js") != -1) {
            CallCenter._thisPath = jsPath.substring(0, jsPath.lastIndexOf("/") + 1);
          }
        }
      }
      if (!CallCenter._thisPath) {
        CallCenter._thisPath = "";
      }
      return CallCenter._thisPath;
    },
    // 创建CSS元素
    createCss: function (filePath, id) {
      var styleTag = document.createElement("link");
      styleTag.setAttribute("type", "text/css");
      styleTag.setAttribute("rel", "stylesheet");
      styleTag.setAttribute("href", filePath + "?V=" + ccsdk);
      styleTag.setAttribute("id", id);
      $("head")[0].appendChild(styleTag);
    },
    // 创建script元素
    createScript: function (filePath) {
      var tag = document.createElement("script");
      tag.setAttribute("type", "text/javascript");
        tag.setAttribute("src", filePath + "?V=" + ccsdk);
      $("head")[0].appendChild(tag);
      return this;
    },
    // 内部使用Map
    HashMap: function () {
      var length = 0;
      var obj = new Object();
      this.isEmpty = function () {
        return length == 0;
      };
      this.containsKey = function (key) {
        return (key in obj);
      };
      this.containsValue = function (value) {
        for (var key in obj) {
          if (obj[key] == value) {
            return true;
          }
        }
        return false;
      };
      this.put = function (key, value) {
        if (!this.containsKey(key)) {
          length++;
        }
        obj[key] = value;
      };
      this.get = function (key) {
        return this.containsKey(key) ? obj[key] : null;
      };
      this.remove = function (key) {
        if (this.containsKey(key) && (delete obj[key])) {
          length--;
        }
      };
      this.values = function () {
        var _values = new Array();
        for (var key in obj) {
          _values.push(obj[key]);
        }
        return _values;
      };
      this.keySet = function () {
        var _keys = new Array();
        for (var key in obj) {
          _keys.push(key);
        }
        return _keys;
      };
      this.size = function () {
        return length;
      };
      this.clear = function () {
        length = 0;
        obj = new Object();
      };
      // 2.1.1.24_30
      this.map = function () {
        return obj;
      };
    },
    // 绑定快捷键
    hotkey: function (evt) {
      //兼容IE和Firefox获得keyBoardEvent对象
      evt = (evt) ? evt : ((window.event) ? window.event : "");
      if (evt !== "") {
        var key = evt.keyCode ? evt.keyCode : evt.which;//兼容IE和Firefox获得keyBoardEvent对象的键值
        if ((key == 79) && evt.ctrlKey && evt.altKey) {
          if (CallCenter._sendlog) {
            CallCenter.closeClientLog();
            alert("发送日志到服务端状态：禁用");
          } else {
            CallCenter.openClientLog();
            alert("发送日志到服务端状态：启用");
          }
        }
      }
    },
    // 设置cookie
    getcookie: function (name) {
      var cookie_start = document.cookie.indexOf(name + "=");
      var cookie_end = document.cookie.indexOf(";", cookie_start);
      return cookie_start == -1 ? "" : decodeURIComponent(document.cookie.substring(cookie_start + name.length + 1, (cookie_end > cookie_start ? cookie_end : document.cookie.length)));
    },
    // 获取cookie
    setcookie: function (name, val, seconds, path, domain, secure) {
      var expires = new Date();
      expires.setTime(expires.getTime() + seconds * 1000);
      document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(val) + "; expires=" + expires.toGMTString() + (path ? "; path=" + path : "/") + (domain ? "; domain=" + domain : "") + (secure ? "; secure" : "");
    },
    // 获取本地存储
    getlocalstorage: function (name) {
      return window.localStorage.getItem("com.CallCenter." + name);
    },
    // 设置本地存储
    setlocalstorage: function (name, val) {
      window.localStorage.setItem("com.CallCenter." + name, val);
    },
    // 设置预测话后计时
    setCastTime: function (custId) {
      custId = JSON.parse(custId);
      var afterTime = custId.afterTime;
      if (afterTime && afterTime > 0) {
        taskSetTimeout();
      }
      function taskSetTimeout() {
        $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.time").hide();
        if ($("#CallCenter_status_bar span#CallCenter_status_tiao .con div.taskCountdown").length > 0) {
          $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.taskCountdown").html(CallCenter.secondsToHours(afterTime));
        } else {
          $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.time").after("<div class='taskCountdown' style='color: #fff;float: right;font-size: 12px;font-weight: bold;line-height: 32px;padding-right:10px;'>" + CallCenter.secondsToHours(afterTime) + "</div>");
        }
        if (afterTime >= 1) {
          afterTime--;
          CallCenter.taskClearTimeout = setTimeout(function () {
            taskSetTimeout();
          }, 1000);

        } else {
          CallCenter.taskCountdown();
          CallCenter.free();
        }
      }
    },
    // 重新计时
    taskCountdown: function () {
      $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.taskCountdown").remove();
      $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.time").show();
      $("#CallCenter_status_bar span#CallCenter_status_tiao .con div.time").text("00:00:00");
    },

    /* 基础操作begin */
    // 请求登录
    handle_login: function (cmd) {
      var sendobj = new CallCenter._sendcmd(cmd);
      sendobj.agentkey = CallCenter._operatorid + "@" + CallCenter._abbreviate;
      sendobj.operatorid = CallCenter._operatorid;             //工号
      sendobj.password = CallCenter._password;                 //密码
      sendobj.abbreviate = CallCenter._abbreviate;             //公司简称
      sendobj.ws_msg_type = CallCenter._ws_msg_type;             //是否加密
      sendobj.worktype = CallCenter._logintype;                //登录类型,0手机,1sip话机,2软话机
      sendobj.companyid = CallCenter._companyid;              //公司编号
      sendobj.auto = CallCenter._auto;                         //登录方式，0普通1预测
      sendobj.logingroups = CallCenter._logingroups;          //登录到的技能组。登录方式为预测式生效
      sendobj.agentType = CallCenter._agentType;            //预测坐席签入方式:1 短签，2 长签
      CallCenter._callId && (sendobj.callid = CallCenter._callId);  // 通话中的_callId
      sendobj.sdkVersion = CallCenter.getVersion();
      CallCenter.send(sendobj);
    },
    // 预测外呼长签时在sip连接成功时发送automakecall命令
    handle_automakecall: function () {
      var sendobj = new CallCenter._sendcmd("automakecall");
      sendobj.operatorid = CallCenter._operatorid;             //工号
      sendobj.abbreviate = CallCenter._abbreviate;             //公司简称
      sendobj.companyid = CallCenter._companyid;              //公司编号
      CallCenter.send(sendobj);
    },
    // 请求接听
    handle_acceptcall: function () {
      if (
        (CallCenter._serverType == CallCenter._serverType_ccs && CallCenter._logintype == 1) ||
        CallCenter._serverType == CallCenter._serverType_cti
      ) {
        var sendobj = new CallCenter._sendcmd("answercall");
        CallCenter.send(sendobj);
      } else {
        SoftPhone.AcceptCall();
      }
    },
    // 请求外呼
    handle_makecall: function (arg) {
      // 2019-09-25调整为非通话中可以外呼
      if (CallCenter.isCalling()) {
        CallCenter.eventAlert(UIT("cannotmakecall"));
      } else {
        CallCenter.initControl().setStatusAndPhoneText(UIT("requestcallout"));
        CallCenter._calling = true;  // 通话中
        CallCenter._isCallout = true;  // 是否外呼通话
        CallCenter._callingtimer = 0;  // 通话时长
        CallCenter._preview = arg.preview;

        // 内呼
        if (arg.called && arg.called.indexOf("@") != -1) {
          CallCenter._isInnercall = true;
        }

        var sendobj = new CallCenter._sendcmd("makecall");
        sendobj.preview = arg.preview || "";
        sendobj.caller = arg.caller;
        sendobj.called = arg.called;
        sendobj.caseid = arg.caseid;
        if (arg.userData && typeof arg.userData === "object") {
          if (arg.userData.userData) {
            sendobj.userData = arg.userData.userData;
          }
          if (arg.userData.uuiData) {
            sendobj.uuiData = arg.userData.uuiData;
          }
          if (arg.userData.autoAnswer) {
            sendobj.autoAnswer = arg.userData.autoAnswer;
          }
        }
        CallCenter.send(sendobj);
      }
    },
    // 执行保持
    handle_mute: function () {
      CallCenter.setStatusAndPhoneText(UIT("requesthold"));
      if (CallCenter._openOnlyMuteCustomer && CallCenter._logintype == CallCenter._loginType_web) {//如果是软话机方式并且开启了只静音客户
        SoftPhone.Mute();
      } else {
        var sendobj = new CallCenter._sendcmd("mute");
        CallCenter.send(sendobj);
      }
    },
    // 执行静音
    handle_callmute: function () {
      var sendobj = new CallCenter._sendcmd("callmute");
      CallCenter.send(sendobj);
    },
    // 请求执行三方
    handle_tripartitetalk: function (agentids) {
      if (!CallCenter._leaveconf) {
        CallCenter.setStatusAndPhoneText(UIT("requestthreewaycalling"));
      }
      var sendobj = new CallCenter._sendcmd("tripartitetalk");
      agentids ? sendobj.controlAgentKey = agentids : "";
      CallCenter.send(sendobj);
    },
    // 三方连接中
    event_tripartitetalk: function (event, from, to) {
      CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("tripartitetalkconnecting")).showControl("#CallCenter_hangupbut");
    },
    // 执行三方通话
    event_sanfangcall: function (event, from, to) {
      CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("threewaycalling")).showControl("#CallCenter_hangupbut,#CallCenter_bohao");
      if (CallCenter._mainStatus === "meetingMaster") {
        CallCenter.showControl("#CallCenter_consultbut");
      }
      CallCenter._mainStatus === "meetingMaster" ? CallCenter.setMeeting() : $("#CallCenter_seat_list").hide();
      CallCenter.toggleMuteIcon();
    },
    // 保持成功
    event_mute: function () {
      Utils.reconnection.preserveTimerWhenReconnection();
      CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("hold")).showControl("#CallCenter_unmutebut,#CallCenter_phonenum");
    },
    // 取消保持
    handle_unmute: function () {
      CallCenter.setStatusAndPhoneText(UIT("requestunhold"));
      if (CallCenter._openOnlyMuteCustomer && CallCenter._logintype == CallCenter._loginType_web) {//如果是软话机方式并且开启了只静音客户
        SoftPhone.UnMute();
      } else {
        var sendobj = new CallCenter._sendcmd("unmute");
        CallCenter.send(sendobj);
      }
    },
    // 静音坐席成功
    event_callmute: function () {
      Utils.reconnection.preserveTimerWhenReconnection();
      CallCenter.hideControl("#CallCenter_callmutebut").showControl("#CallCenter_callunmutebut");
    },
    // 取消静音坐席
    handle_callunmute: function () {
      var sendobj = new CallCenter._sendcmd("callunmute");
      CallCenter.send(sendobj);
    },
    // 转移会议主持人
    transferModerator_handle: function (agentid) {
      CallCenter.setStatusAndPhoneText(UIT("transfer_meeting_sending"));
      var sendobj = new CallCenter._sendcmd("transferModerator");
      sendobj.controlAgentKey = agentid;
      CallCenter.send(sendobj);
    },
    // 请求咨询
    handle_agentconsult: function (arg) {
      // CallCenter._leaveconf：true 表示三方有人挂机，执行邀请，此时保持状态不变更
      if (!CallCenter._leaveconf) {
        CallCenter.setStatusAndPhoneText(UIT("requestconsult"));
      }
      var sendobj = new CallCenter._sendcmd("agentconsult");
      sendobj.num = arg.agentid;
      sendobj.groupid = arg.groupid;
      if (arg.userdata && typeof arg.userdata === "object") {
        if (arg.userdata.userData) {
          sendobj.userData = userdata.userData;
        }
        if (arg.userdata.uuiData) {
          sendobj.uuiData = userdata.uuiData;
        }
        if (arg.userdata.type) {
          sendobj.type = arg.userdata.type;
        } else {
          sendobj.type = "0"
        }
        if (arg.userdata.called) {
          sendobj.called = arg.userdata.called;
        }
      }
      CallCenter.send(sendobj);
    },
    // 咨询中
    event_agentconsult: function () {
      // CallCenter._leaveconf：true 表示三方有人挂机，执行邀请，此时保持状态不变更
      if (!CallCenter._leaveconf) {
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("consulting")).showControl("#CallCenter_consultbackbut,#CallCenter_phonenum");
      }
    },
    // 咨询通话中
    event_consultationcalls: function () {
      CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("consultationcalling"));
      CallCenter.showControl("#CallCenter_consultbackbut,#CallCenter_tripartitetalkbut");
      if (CallCenter._mainStatus === "meetingConsult") {
        CallCenter.setMeeting();
      } else if (CallCenter._mainStatus === "consult") {
        CallCenter.showControl("#CallCenter_shiftbut");
      }
      CallCenter.toggleMuteIcon();
    },
    // 请求取消咨询
    handle_agentconsultabck: function () {
      if (!CallCenter._leaveconf) {
        CallCenter.setStatusAndPhoneText(UIT("requestconsultback"));
      }
      var sendobj = new CallCenter._sendcmd("agentconsultback");
      CallCenter.send(sendobj);
    },
    // 结束会议中坐席通话
    handle_leavemeeting: function (controlAgentKey) {
      var sendobj = new CallCenter._sendcmd("leavemeeting");
      sendobj.controlAgentKey = controlAgentKey;
      CallCenter.send(sendobj);
    },
    // 请求咨询服务
    handle_consulationservice: function (arg) {
      CallCenter.setStatusAndPhoneText(UIT("requestconsulationservice"));
      var sendobj = new CallCenter._sendcmd("consulationservice");
      sendobj.num = arg.num;
      sendobj.groupid = arg.groupid;
      sendobj.userdata = arg.userdata;
      if (arg.userdata && typeof arg.userdata === "object") {
        if (arg.userdata.userData) {
          sendobj.userData = arg.userdata.userData;
        }
        if (arg.userdata.uuiData) {
          sendobj.uuiData = arg.userdata.uuiData;
        }
      }
      if (arg.isFlowname) {
        var item = null;
        for (var i = 0; i < CallCenter._ivrFlowList.length; i++) {
          if (CallCenter._ivrFlowList[i].flowname === arg.filename) {
            item = CallCenter._ivrFlowList[i];
            break;
          }
        }
        item ? sendobj.filename = item.filename : sendobj.filename = arg.filename;
      } else {
        sendobj.filename = arg.filename;
      }
      CallCenter.send(sendobj);
    },
    // 请求咨询转接
    handle_agentshift: function () {
      CallCenter.setStatusAndPhoneText(UIT("requestagentshift"));
      var sendobj = new CallCenter._sendcmd("agentshift");
      CallCenter.send(sendobj);
    },
    // 执行转接
    handle_transfercall: function (arg) {
      CallCenter.setStatusAndPhoneText(UIT("requesttransfercall"));
      var sendobj = new CallCenter._sendcmd("transfercall");
      sendobj.num = arg.number;
      sendobj.groupid = arg.groupid;
      if (arg.userdata) {
        if (arg.userdata.userData) {
          sendobj.userData = arg.userdata.userData;
        }
        if (arg.userdata.uuiData) {
          sendobj.uuiData = arg.userdata.uuiData;
        }
        if (arg.userdata.outCall) {
          sendobj.outCall = arg.userdata.outCall;  // 盲转
        } else {
          sendobj.outCall = false;
        }
        if (arg.userdata.type) {
          sendobj.type = arg.userdata.type;
        } else {
          sendobj.type = "0"
        }
        if (arg.userdata.called) {
          sendobj.called = arg.userdata.called;
        }
      }
      CallCenter.send(sendobj);
    },
    // 执行转接技能组
    handle_transfergroup: function (arg) {
      CallCenter.setStatusAndPhoneText(UIT("requesttransfergroup"));
      var sendobj = new CallCenter._sendcmd("transfergroup");
      sendobj.agentkey = CallCenter._operatorid + "@" + CallCenter._abbreviate;
      sendobj.companyid = CallCenter._companyid;
      sendobj.groupid = arg.groupid;
      sendobj.userdata = arg.userdata;
      CallCenter.send(sendobj);
    },
    // 请求取消转接
    handle_canceltransfercall: function () {
      CallCenter.setStatusAndPhoneText(UIT("requestcanceltransfercall"));
      var sendobj = new CallCenter._sendcmd("canceltransfercall");
      CallCenter.send(sendobj);
    },
    // 请求转接服务
    handle_transferservice: function (arg) {
      CallCenter.setStatusAndPhoneText(UIT("requesttransferservice"));
      var sendobj = new CallCenter._sendcmd("transferservice");
      sendobj.num = arg.num;
      sendobj.hangupaftertransfer = arg.hangupaftertransfer;
      if (arg.isFlowname) {
        var item = null;
        for (var i = 0; i < CallCenter._ivrFlowList.length; i++) {
          if (CallCenter._ivrFlowList[i].flowname === arg.filename) {
            item = CallCenter._ivrFlowList[i];
            break;
          }
        }
        item ? sendobj.filename = item.filename : sendobj.filename = arg.filename;
      } else {
        sendobj.filename = arg.filename;
      }
      CallCenter.send(sendobj);
    },
    // 请求监听
    handle_monitor: function (arg) {
      CallCenter.initControl().setStatusAndPhoneText(UIT("requestmonitor"));
      CallCenter._be_operator = arg.agentid;
      var sendobj = new CallCenter._sendcmd("monitor");
      sendobj.agentoperatorid = arg.agentid;
      if (arg.type) {
        sendobj.type = arg.type;
      }
      CallCenter.send(sendobj);
    },
    // 请求拦截
    handle_intercept: function (agentid) {
      CallCenter.initControl().setStatusAndPhoneText(UIT("requestintercept"));
      CallCenter._be_operator = agentid;
      var sendobj = new CallCenter._sendcmd("agentinterceptcall");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
    },
    // 请求强插
    handle_agentinsert: function (agentid) {
      CallCenter.initControl().setStatusAndPhoneText(UIT("requestagentinsert"));
      CallCenter._be_operator = agentid;
      var sendobj = new CallCenter._sendcmd("agentinsert");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
    },
    // 请求辅导
    handle_instruct: function (agentid) {
      CallCenter.initControl().setStatusAndPhoneText("请求辅导");
      var sendobj = new CallCenter._sendcmd("coach");
      sendobj.agentoperatorid = agentid;
      CallCenter.send(sendobj);
    },
    // 坐席指导
    enterGuidance: function () {
      CallCenter.setStatusAndPhoneText(UIT("leaveuser")).setGreenClass();
      CallCenter.hideControl("#CallCenter_silence,#CallCenter_unsilence,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut,#CallCenter_bohao,#CallCenter_mutebut,#CallCenter_unmutebut,#CallCenter_callunmutebut");
      CallCenter.showControl("#CallCenter_hangupbut");
      CallCenter.toggleMuteIcon();
    },
    /* 基础操作end */

    // 加载后自动调用
    loading: function () {
      CallCenter.log("CallCenter消息：YOU ARE USING V_" + ccsdk + " CCSDK");
      CallCenter.getPath();
      CallCenter.createCss(CallCenter._thisPath + "CallCenterCommon.css", "CallCenter_css_common");
      // CallCenter.createScript(CallCenter._thisPath + "SoftPhone.js");
      CallCenter._busyTypeMap = new CallCenter.HashMap();
      document.onkeydown = CallCenter.hotkey;
      window.SoftPhone = new Object();
    },
    // SDK自检
    selfChecking: function (data) {
      var sendobj = new CallCenter._sendcmd("ccsdk");
      sendobj.sequence = new Date().getTime();
      sendobj = $.extend(data, sendobj);
      if (CallCenter._islogin) {
        CallCenter.send(sendobj);
      }
    }
    /* 以上为SDK内部调用功能 */
  };
  CallCenter.loading();

  CallCenter.actions = function () {
    return {
      // client request
      login_handle: { type: "handle", name: "login_handle", text: UIT("login") },//登录
      reconnection_handle: { type: "handle", name: "reconnection_handle", text: UIT("reconnect") },//重新连接
      logout_handle: { type: "handle", name: "logout_handle", text: UIT("requestlogout") },//发送登出
      agentidle_handle: { type: "handle", name: "agentidle_handle", text: UIT("agentidle") },//示闲
      agentbusy_handle: { type: "handle", name: "agentbusy_handle", text: UIT("agentbusy") },//示忙
      acceptcall_handle: { type: "handle", name: "acceptcall_handle", text: UIT("acceptcall") },//接听
      makecall_handle: { type: "handle", name: "makecall_handle", text: UIT("callout") },//外呼
      cancelmakecall_handle: { type: "handle", name: "cancelmakecall_handle", text: UIT("cancelmakecall") },//挂断呼叫
      mute_handle: { type: "handle", name: "mute_handle", text: UIT("hold") },//保持
      unmute_handle: { type: "handle", name: "unmute_handle", text: UIT("unhold") },//取消保持
      transfercall_handle: { type: "handle", name: "transfercall_handle", text: UIT("transfercall") },//转接
      transfergroup_handle: { type: "handle", name: "transfergroup_handle", text: UIT("requesttransfergroup") },//转接技能组
      canceltransfercall_handle: { type: "handle", name: "canceltransfercall_handle", text: UIT("canceltransfercall") },//取消转接
      agentconsult_handle: { type: "handle", name: "agentconsult_handle", text: UIT("cousult") },//咨询
      agentconsultback_handle: { type: "handle", name: "agentconsultback_handle", text: UIT("cancelconsult") },//取消咨询
      agentshift_handle: { type: "handle", name: "agentshift_handle", text: UIT("agentshift") },//咨询转接
      consulationservice_handle: { type: "handle", name: "consulationservice_handle", text: UIT("consulationservice") },//转IVR咨询服务
      tripartitetalk_handle: { type: "handle", name: "tripartitetalk_handle", text: UIT("threewaycalling") },//三方
      transferModerator_handle: { type: "handle", name: "transferModerator_handle", text: UIT("transfer_meeting") },//多方会议转接
      transferservice_handle: { type: "handle", name: "transferservice_handle", text: UIT("transferservice") },//转接满意度服务
      agentinsert_handle: { type: "handle", name: "agentinsert_handle", text: UIT("agentinsert") },//强插
      instruct_handle: { type: "handle", name: "instruct_handle", text: "耳语" },//耳语
      monitor_handle: { type: "handle", name: "monitor_handle", text: UIT("monitor") },//监听
      agentinterceptcall_handle: { type: "handle", name: "agentinterceptcall_handle", text: UIT("intercept") },	//拦截

      login: { type: "event", name: "login", text: UIT("login") },   //登录
      authfail: { type: "event", name: "authfail", text: UIT("loginfail") }, //登录失败
      kick: { type: "event", name: "kick", text: UIT("kick") },	   //另一设备登录
      logout: { type: "event", name: "logout", text: UIT("logout") },		   //登出
      disconnect: { type: "event", name: "disconnect", text: UIT("netdisconnect") },     //网络连接断开
      reconnection: { type: "event", name: "reconnection", text: UIT("reconnect") },    //重新连接
      reconnection_fail: { type: "event", name: "reconnection_fail", text: UIT("reconnectionFail") }, //重新连接失败
      agentidle: { type: "event", name: "agentidle", text: UIT("agentidle") },      //示闲
      agentidle_fail: { type: "event", name: "agentidle_fail", text: UIT("idlefail") },	          //示闲
      agentbusy: { type: "event", name: "agentbusy", text: UIT("agentbusy") },	                //忙碌
      agentbusy_fail: { type: "event", name: "agentbusy_fail", text: UIT("busyfail") },
      acceptcall_fail: { type: "event", name: "acceptcall_fail", text: UIT("acceptfail") },   //接听失败
      makecall: { type: "event", name: "makecall", text: UIT("callout") },           //外呼
      makecall_fail: { type: "event", name: "makecall_fail", text: UIT("calloutfail") },   //外呼失败
      outringing: { type: "event", name: "outringing", text: UIT("agentringing") },	  //外呼坐席振铃
      outcall: { type: "event", name: "outcall", text: UIT("outcall") },      //外呼坐席摘机
      calledringing: { type: "event", name: "calledringing", text: UIT("calledring") },	 //外呼被叫振铃
      outboundcall: { type: "event", name: "outboundcall", text: UIT("outboundcall") },	  //预测外呼被叫接通
      answer: { type: "event", name: "answer", text: UIT("outboundcall") },	       //外呼被叫接通
      cancelmakecall: { type: "event", name: "cancelmakecall", text: UIT("cancelmakecall") },	   //挂断呼叫
      inringing: { type: "event", name: "inringing", text: UIT("inringing") },         //来电振铃
      playtts: { type: "event", name: "playtts", text: UIT("palytts") },             //播放TTS
      incall: { type: "event", name: "incall", text: UIT("Seatreception") },             //来电坐席接听
      consultinringing: { type: "event", name: "consultinringing", text: UIT("consultinringing") },   //被咨询方来电振铃
      consultincall: { type: "event", name: "consultincall", text: UIT("consultincall") }, //被咨询方接通
      transfering: { type: "event", name: "transfering", text: UIT("transfering") },	 //转接中
      transfercall_fail: { type: "event", name: "transfercall_fail", text: UIT("transferfail") },	//转接失败
      transferinringing: { type: "event", name: "transferinringing", text: UIT("transferinringing") },//被转接来电振铃
      transferincall: { type: "event", name: "transferincall", text: UIT("transferincall") },	//被转接来电通话中
      innerringing: { type: "event", name: "innerringing", text: UIT("internalcallringing") },//内呼振铃
      innercall: { type: "event", name: "innercall", text: UIT("internalcallconnect") },//内呼接通
      innercall_fail: { type: "event", name: "innercall_fail", text: UIT("innercallfail") },  //内呼失败
      mute: { type: "event", name: "mute", text: UIT("hold") },  //保持
      mute_fail: { type: "event", name: "mute_fail", text: UIT("holdfail") },  //保持失败
      agent_coach_call_mute_fail: { type: "event", name: "agent_coach_call_mute_fail", text: UIT("holdfail") },  //坐席辅导下保持失败
      unmute: { type: "event", name: "unmute", text: UIT("unhold") },//取消保持
      agent_coach_call_unmute: { type: "event", name: "agent_coach_call_unmute", text: UIT("unhold") },//坐席辅导下取消保持
      unmute_fail: { type: "event", name: "unmute_fail", text: UIT("unhold") },   //取消保持失败
      transfercall: { type: "event", name: "transfercall", text: UIT("transfercall") },	 //转接
      canceltransfercall: { type: "event", name: "canceltransfercall", text: UIT("canceltransfercall") },	//取消转接
      agentconsult: { type: "event", name: "agentconsult", text: UIT("cousult") },	//咨询
      agentconsult_fail: { type: "event", name: "agentconsult_fail", text: UIT("consultfail") },    //咨询失败
      sanfang_agentconsult_fail: { type: "event", name: "sanfang_agentconsult_fail", text: UIT("consultfail") },    //多方咨询失败
      consultationcalls: { type: "event", name: "consultationcalls", text: UIT("consultationcalling") },//咨询通话中
      agentconsultback: { type: "event", name: "agentconsultback", text: UIT("cancelconsult") },//取消咨询
      agentshift: { type: "event", name: "agentshift", text: UIT("agentshift") },	//咨询转接
      agentshift_fail: { type: "event", name: "agentshift_fail", text: UIT("agentshiftfail") }, //咨询转接
      consulationservice: { type: "event", name: "consulationservice", text: UIT("consulationservice") },	//咨询服务
      consulationservice_fail: { type: "event", name: "consulationservice_fail", text: UIT("consulationservicefail") },//咨询服务失败
      tripartitetalk: { type: "event", name: "tripartitetalk", text: UIT("threewaycalling") },//三方
      tripartitetalk_fail: { type: "event", name: "tripartitetalk_fail", text: UIT("tripartitetalkfail") },//三方通话失败
      sanfangcall: { type: "event", name: "sanfangcall", text: UIT("tripartitetalkcall") },//三方通话接通咨询方
      transferservice: { type: "event", name: "transferservice", text: UIT("transferservice") },//转接服务
      transferservice_fail: { type: "event", name: "transferservice_fail", text: UIT("transferservicefail") },//转接服务失败
      forceidle: { type: "event", name: "forceidle", text: UIT("forceidle") },//强制示闲
      forcebusy: { type: "event", name: "forcebusy", text: UIT("forcebusy") },//强制示忙
      agentbreak: { type: "event", name: "agentbreak", text: UIT("agentbreak") },	//强拆
      forcelogout: { type: "event", name: "forcelogout", text: UIT("forcelogout") },//强制签出
      monitor: { type: "event", name: "monitor", text: UIT("monitor") },//监听
      monitor_fail: { type: "event", name: "monitor_fail", text: UIT("monitorfail") },//监听
      monitorringing: { type: "event", name: "monitorringing", text: UIT("monitorringing") },	//监听来电振铃
      monitorincall: { type: "event", name: "monitorincall", text: UIT("monitorincall") },//监听接通
      agentinterceptcall: { type: "event", name: "agentinterceptcall", text: UIT("intercept") },//拦截
      agentinterceptcall_fail: { type: "event", name: "agentinterceptcall_fail", text: UIT("interceptfail") },//拦截失败
      interceptaltering: { type: "event", name: "interceptaltering", text: UIT("interceptaltering") },//拦截来电振铃
      intercept: { type: "event", name: "intercept", text: UIT("intercepting") },//拦截中
      interceptcall: { type: "event", name: "interceptcall", text: UIT("interceptcall") },//拦截接通
      agentinsert: { type: "event", name: "agentinsert", text: UIT("agentinsert") },				//强插
      agentinsert_fail: { type: "event", name: "agentinsert_fail", text: UIT("insertfail") },//强插失败
      agentinsertringing: { type: "event", name: "agentinsertringing", text: UIT("insertringing") },//强插振铃
      agentinsertincall: { type: "event", name: "agentinsertincall", text: UIT("insertcall") },//强插通话中
      after: { type: "event", name: "after", text: UIT("after") },//话后
      siperror: { type: "event", name: "siperror", text: UIT("sipError") }//话机状态异常
    };
  };
  CallCenter.agentCoachActions = function () {
    return {
      // 坐席辅导
      instruct: { type: "event", name: "instruct", text: UIT("instruct") },   // 坐席辅导
      instruct_fail: { type: "event", name: "instruct_fail", text: UIT("instruct_fail") },  // 坐席辅导失败
      instructringing: { type: "event", name: "instructringing", text: UIT("instructringing") },// 坐席辅导来电振铃
      instructincall: { type: "event", name: "instructincall", text: UIT("instructincall") },// 坐席辅导接通（班长侧）
      agentCoachCall: { type: "event", name: "agentCoachCall", text: UIT("agentCoachCall") },// 坐席辅导接通（坐席侧）
      monitorCoachCallFail: { type: "event", name: "monitorCoachCallFail", text: UIT("monitorCoachCallFail") },// 坐席辅导失败（班长侧）
      // 坐席辅导（坐席静音用户）
      silence_handle: { type: "handle", name: "silence_handle", text: UIT("requestSilence") }, // 请求静音
      silence: { type: "event", name: "silence", text: UIT("silence") },// 静音中
      unsilence_handle: { type: "handle", name: "unsilence_handle", text: UIT("requestUnsilence") },// 请求取消静音
      unsilence: { type: "event", name: "unsilence", text: UIT("unsilence") }// 取消静音（回到辅导中）
    };
  };  // 坐席辅导
  CallCenter.states = function () {
    return {
      s_nologin: { name: "nologin", text: UIT("nologin") },															     //**未登录
      s_login_sending: { name: "login_sending", text: UIT("requestlogin") },											 //**登录发送
      s_login: { name: "login", text: UIT("login") },																	 //**登录
      s_kick: { name: "kick", text: UIT("kick") },                                                                       //**另一设备登录
      s_authfail: { name: "authfail", text: UIT("authfail") },                                                           //验证失败
      s_logout_sending: { name: "logout_sending", text: UIT("requestlogout") },                                          //**登出发送
      s_logout: { name: "logout", text: UIT("logout") },																 //**登出
      s_disconnect: { name: "disconnect", text: UIT("disconnect") },													 //**断开连接
      s_reconnection_sending: { name: "reconnection_sending", text: UIT("requestreconnection") },						 //**重连发送
      s_reconnection: { name: "reconnection", text: UIT("reconnection") },												 //**重连
      s_reconnection_fail: { name: "reconnection_fail", text: UIT("reconnectionFail") },								 //**重连失败

      s_idle_sending: { name: "idle_sending", text: UIT("requestidle") },												 //**空闲发送
      s_idle: { name: "idle", text: UIT("agentidle") },																	 //**空闲
      s_idle_fail: { name: "idle_fail", text: UIT("idlefail") },														 //**空闲
      s_busy_sending: { name: "busy_sending", text: UIT("requestbusy") },												 //**忙碌发送
      s_busy: { name: "busy", text: UIT("agentbusy") },																	 //**忙碌
      s_busy_fail: { name: "busy_fail", text: UIT("busyfail") },

      s_out_acceptcall_sending: { name: "out_acceptcall_sending", text: UIT("requestacceptcall") },						 //**外呼请求接听
      s_in_acceptcall_sending: { name: "in_acceptcall_sending", text: UIT("requestacceptcall") },						 //**呼入请求接听
      s_consult_acceptcall_sending: { name: "consult_acceptcall_sending", text: UIT("requestacceptcall") },				 //**咨询请求接听
      s_transfer_acceptcall_sending: { name: "transfer_acceptcall_sending", text: UIT("requestacceptcall") },			 //**转接请求接听
      s_monitor_acceptcall_sending: { name: "monitor_acceptcall_sending", text: UIT("requestacceptcall") },				 //**监听请求接听
      s_intercept_acceptcall_sending: { name: "intercept_acceptcall_sending", text: UIT("requestacceptcall") },			 //**拦截请求接听
      s_insert_acceptcall_sending: { name: "insert_acceptcall_sending", text: UIT("requestacceptcall") },				 //**强插请求接听

      // 外呼
      s_makecall_sending: { name: "makecall_sending", text: UIT("requestcallout") },                                        //**外呼发送
      s_makecall: { name: "makecall", text: UIT("callout") },															    //**外呼中
      s_cancelmakecall_sending: { name: "cancelmakecall_sending", text: UIT("requesthangup") },						        //**请求挂断呼叫
      s_cancelmakecall: { name: "cancelmakecall", text: UIT("cancelmakecall") },											//**挂断呼叫
      s_outringing: { name: "outringing", text: UIT("agentringing") },													    //**外呼坐席振铃
      s_outcall: { name: "outcall", text: UIT("outcall") },															        //**外呼坐席摘机
      s_calledringing: { name: "calledringing", text: UIT("calledring") },												    //**外呼被叫振铃
      s_outboundcall: { name: "outboundcall", text: UIT("outboundcall") },												    //预测外呼被叫接通
      s_answer: { name: "answer", text: UIT("outboundcall") },															    //**外呼被叫接通

      // 请求保持状态
      s_mute_sending: { name: "mute_sending", text: UIT("requesthold") },										  //**外呼保持发送
      // 保持状态
      s_mute: { name: "mute", text: UIT("hold") },															      //**外呼保持
      // 请求取消保持
      s_unmute_sending: { name: "unmute_sending", text: UIT("requestunhold") },								      //**外呼取消保持发送

      // 咨询坐席/技能组
      s_agentconsult_sending: { name: "agentconsult_sending", text: UIT("requestconsult") },					                //**外呼咨询发送
      s_agentconsult: { name: "agentconsult", text: UIT("cousult") },											                //**外呼咨询
      s_agentconsultback_sending: { name: "agentconsultback_sending", text: UIT("requestconsultback") },			            //**外呼取消咨询发送
      s_consultationcalls: { name: "consultationcalls", text: UIT("consultationcalling") },							            //**外呼咨询通话中

      // 咨询IVR
      s_consulationservice_sending: { name: "consulationservice_sending", text: UIT("requestconsulationservice") },              //外呼咨询服务发送
      s_consulationservice: { name: "consulationservice", text: UIT("consulationservice") },						             //外呼咨询服务
      // 咨询转接
      s_agentshift_sending: { name: "agentshift_sending", text: UIT("requestagentshift") },					            //**外呼咨询转接发送

      // 多方
      s_tripartitetalk_sending: { name: "tripartitetalk_sending", text: UIT("threewaycalling") },					    //**外呼三方通话发送
      s_tripartitetalk: { name: "tripartitetalk", text: UIT("threewaycalling") },									    //外呼三方通话
      s_sanfangcall: { name: "sanfangcall", text: UIT("threewaycalling") },	//**呼入三方通话
      s_transferModerator_sending: { name: "transferModerator_sending", text: UIT("transfer_meeting") },	// 会议中转接主持人

      // 转接
      s_transfercall_sending: { name: "transfercall_sending", text: UIT("requesttransfercall") },	//请求转接
      s_transfercall: { name: "transfercall", text: UIT("requesttransfercall") },		//请求转接
      s_transfergroup_sending: { name: "transfergroup_sending", text: UIT("requesttransfergroup") },						               //请求转接技能组
      s_transfering: { name: "transfering", text: UIT("transfering") },											                           //转接中
      s_canceltransfercall_sending: { name: "canceltransfercall_sending", text: UIT("requestcanceltransfercall") },

      // 被转接咨询坐席
      s_consultinringing: { name: "consultinringing", text: UIT("consultinringing") }, //**被咨询方来电振铃
      s_consultincall: { name: "consultincall", text: UIT("consultincall") },	//**被咨询方接通
      s_transferinringing: { name: "transferinringing", text: UIT("transferinringing") },//**被转接来电振铃
      s_transferincall: { name: "transferincall", text: UIT("transferincall") }, //**被转接来电通话中

      // 转接IVR
      s_transferservice_sending: { name: "transferservice_sending", text: UIT("requesttransferservice") },//外呼转接服务

      // 呼入
      s_inringing: { name: "inringing", text: UIT("inringing") },  //**来电振铃
      s_playtts: { name: "playtts", text: UIT("palytts") },  //**播放TTS
      s_incall: { name: "incall", text: UIT("Seatreception") },  //**呼入坐席接听

      // 内呼
      s_innerringing: { name: "innerringing", text: UIT("internalcallringing") },		//**内呼振铃
      s_innercall: { name: "innercall", text: UIT("internalcallconnect") },				//**内呼接通

      s_monitor_idle_sending: { name: "monitor_idle_sending", text: UIT("requestmonitor") }, //**监听发送
      s_monitor_busy_sending: { name: "monitor_busy_sending", text: UIT("requestmonitor") },  //**监听发送
      s_monitor: { name: "monitor", text: UIT("monitor") },						//**监听
      s_monitorringing: { name: "monitorringing", text: UIT("monitorringing") },  //**监听来电振铃
      s_monitorincall: { name: "monitorincall", text: UIT("MonitorInthecall") },												                    //**监听接通
      s_agentinterceptcall_idle_sending: { name: "agentinterceptcall_idle_sending", text: UIT("requestintercept") },  //请求拦截
      s_agentinterceptcall_busy_sending: { name: "agentinterceptcall_busy_sending", text: UIT("requestintercept") },  //请求拦截
      s_agentinterceptcall_agentinsertincall_sending: { name: "agentinterceptcall_agentinsertincall_sending", text: UIT("requestintercept") }, //请求拦截
      s_agentinterceptcall: { name: "agentinterceptcall", text: UIT("intercept") },					//**拦截
      s_interceptaltering: { name: "interceptaltering", text: UIT("interceptaltering") },  //**拦截来电振铃
      s_intercept: { name: "intercept", text: UIT("intercepting") },												 //**拦截中
      s_interceptcall: { name: "interceptcall", text: UIT("interceptcall") },								//**拦截接通
      s_agentinsert_idle_sending: { name: "agentinsert_idle_sending", text: UIT("requestagentinsert") }, //**强插发送
      s_agentinsert_busy_sending: { name: "agentinsert_busy_sending", text: UIT("requestagentinsert") }, //**强插发送
      s_agentinsert_monitorincall_sending: { name: "agentinsert_monitorincall_sending", text: UIT("requestagentinsert") }, //**强插发送
      s_agentinsert: { name: "agentinsert", text: UIT("agentinsert") },					//**强插
      s_agentinsertringing: { name: "agentinsertringing", text: UIT("insertringing") },	//**强插振铃
      s_agentinsertincall: { name: "agentinsertincall", text: UIT("insertcall") },	//**强插通话中
      s_after: { name: "after", text: UIT("after") },		 //**话后
      s_siperror: { name: "siperror", text: UIT("sipError") }	 //话机状态异常
    };
  };
  CallCenter.agentCoachStates = function () {
    return {
      // 坐席辅导
      s_instruct_idle_sending: { name: "instruct_idle_sending", text: UIT("requestAgentCoach") },                                                 /*请求坐席辅导*/
      s_instruct_busy_sending: { name: "instruct_busy_sending", text: UIT("requestAgentCoach") },                                                 /*请求坐席辅导*/
      s_instruct: { name: "instruct", text: UIT("instructing") },                                                                                 /*坐席辅导*/
      s_instructringing: { name: "instructringing", text: UIT("instructringing") },                                                               /*坐席辅导来电振铃*/
      s_instructincall: { name: "instructincall", text: UIT("instructing") },                                                                     /*坐席辅导中（班长侧）*/
      s_monitorCoachCallFail: { name: "monitorCoachCallFail", text: UIT("instruct_fail") },                                                       /*坐席辅导失败（班长侧）*/
      s_agent_coach_call: { name: "agent_coach_call", text: UIT("instructing") },                                                                 /*坐席辅导中（坐席侧）*/
      s_instruct_acceptcall_sending: { name: "instruct_acceptcall_sending", text: UIT("instructRequestAcceptCall") },                             /*坐席辅导接听（班长侧）*/

      // 静音用户
      s_silence_sending: { name: "silence_sending", text: UIT("requestSilence") }, /*请求静音*/
      s_silence: { name: "silence", text: UIT("silence") },/*静音*/
      s_silence_fail: { name: "silence_fail", text: UIT("silenceFail") },/*静音失败*/
      s_unsilence_sending: { name: "unsilence_sending", text: UIT("requestUnsilence") },/*请求取消静音*/
      s_unsilence_fail: { name: "unsilence_fail", text: UIT("unsilenceFail") }, /*取消静音失败*/
      s_leaveuser: { name: "leaveuser", text: UIT("leaveuser") },
      /*坐席辅导时请求转接*/
      s_agentcoach_transfercall_sending: { name: "agentcoach_transfercall_sending", text: UIT("requesttransfercall") },                             /*请求转接*/
      s_agentcoach_transfercall: { name: "agentcoach_transfercall", text: UIT("transfering") }                                                     /*转接中*/
    };
  };  // 坐席辅导

  // 所有的动作
  SDK_action = CallCenter.actions();
  SDK_state = CallCenter.states();

  SDK_action = $.extend(CallCenter.agentCoachActions(), SDK_action);
  SDK_state = $.extend(CallCenter.agentCoachStates(), SDK_state);

  SDK_events = [
    // 登录
    { name: SDK_action.login_handle.name, from: SDK_state.s_nologin.name, to: SDK_state.s_login_sending.name },
    { name: SDK_action.login_handle.name, from: SDK_state.s_logout.name, to: SDK_state.s_login_sending.name },
    { name: SDK_action.login_handle.name, from: SDK_state.s_reconnection_fail.name, to: SDK_state.s_login_sending.name },
    { name: SDK_action.login_handle.name, from: SDK_state.s_kick.name, to: SDK_state.s_login_sending.name },
    { name: SDK_action.login.name, from: SDK_state.s_login_sending.name, to: SDK_state.s_login.name },
    { name: SDK_action.login.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_login.name },
    { name: SDK_action.authfail.name, from: SDK_state.s_login_sending.name, to: SDK_state.s_authfail.name },
    { name: SDK_action.kick.name, from: "*", to: SDK_state.s_kick.name },

    // 登出
    { name: SDK_action.logout_handle.name, from: SDK_state.s_login.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_login_sending.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_idle_sending.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_busy_sending.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_siperror.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_after.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout_handle.name, from: SDK_state.s_idle_fail.name, to: SDK_state.s_logout_sending.name },
    { name: SDK_action.logout.name, from: SDK_state.s_logout_sending.name, to: SDK_state.s_logout.name },

    // 空闲
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_login.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_incall.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_after.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_idle_fail.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentidle.name, from: SDK_state.s_after.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentidle.name, from: SDK_state.s_idle_sending.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentidle.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentidle_fail.name, from: SDK_state.s_idle_sending.name, to: SDK_state.s_idle_fail.name },

    // 忙碌
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_login.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_incall.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_siperror.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_after.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_idle_fail.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentbusy.name, from: SDK_state.s_busy_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.agentbusy.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_busy.name },
    { name: SDK_action.agentbusy_fail.name, from: SDK_state.s_busy_sending.name, to: SDK_state.s_busy_fail.name },

    // 请求接听
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_outringing.name, to: SDK_state.s_out_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_inringing.name, to: SDK_state.s_in_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_consultinringing.name, to: SDK_state.s_consult_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_transferinringing.name, to: SDK_state.s_transfer_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_monitorringing.name, to: SDK_state.s_monitor_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_interceptaltering.name, to: SDK_state.s_intercept_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_agentinsertringing.name, to: SDK_state.s_insert_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_innerringing.name, to: SDK_state.s_in_acceptcall_sending.name },
    { name: SDK_action.acceptcall_handle.name, from: SDK_state.s_instructringing.name, to: SDK_state.s_instruct_acceptcall_sending.name },
    // 班长辅导（班长侧接听）

    // 接听失败
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_out_acceptcall_sending.name, to: SDK_state.s_outringing.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_in_acceptcall_sending.name, to: SDK_state.s_inringing.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_consult_acceptcall_sending.name, to: SDK_state.s_consultinringing.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_transfer_acceptcall_sending.name, to: SDK_state.s_transferinringing.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_monitor_acceptcall_sending.name, to: SDK_state.s_monitorringing.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_intercept_acceptcall_sending.name, to: SDK_state.s_interceptaltering.name },
    { name: SDK_action.acceptcall_fail.name, from: SDK_state.s_insert_acceptcall_sending.name, to: SDK_state.s_agentinsertringing.name },

    // 请求外呼
    { name: SDK_action.makecall_handle.name, from: "*", to: SDK_state.s_makecall_sending.name },

    // 外呼
    { name: SDK_action.makecall.name, from: SDK_state.s_makecall_sending.name, to: SDK_state.s_makecall.name },
    // 20181115-允许忙碌时CCS调用接口执行外呼
    { name: SDK_action.makecall.name, from: SDK_state.s_busy.name, to: SDK_state.s_makecall.name },

    // 外呼失败
    { name: SDK_action.makecall_fail.name, from: SDK_state.s_makecall_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.makecall_fail.name, from: SDK_state.s_makecall_sending.name, to: SDK_state.s_idle.name },

    // 外呼时坐席状态与被叫状态变更
    { name: SDK_action.outringing.name, from: SDK_state.s_makecall.name, to: SDK_state.s_outringing.name },
    { name: SDK_action.outringing.name, from: SDK_state.s_idle.name, to: SDK_state.s_outringing.name },
    { name: SDK_action.outringing.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_outringing.name },  // 允许忙碌失败接预测来电

    // 外呼坐席摘机
    { name: SDK_action.outcall.name, from: SDK_state.s_out_acceptcall_sending.name, to: SDK_state.s_outcall.name },
    { name: SDK_action.outcall.name, from: SDK_state.s_makecall.name, to: SDK_state.s_outcall.name },
    { name: SDK_action.outcall.name, from: SDK_state.s_outringing.name, to: SDK_state.s_outcall.name },

    // 被叫振铃
    { name: SDK_action.calledringing.name, from: SDK_state.s_outcall.name, to: SDK_state.s_calledringing.name },

    // 预测外呼被叫接通
    { name: SDK_action.outboundcall.name, from: SDK_state.s_outringing.name, to: SDK_state.s_outboundcall.name },
    { name: SDK_action.outboundcall.name, from: SDK_state.s_busy_fail.name, to: SDK_state.s_outboundcall.name },  // 允许忙碌失败接预测来电

    // 外呼被叫接通
    { name: SDK_action.answer.name, from: SDK_state.s_calledringing.name, to: SDK_state.s_answer.name },
    { name: SDK_action.answer.name, from: SDK_state.s_consulationservice.name, to: SDK_state.s_answer.name },
    { name: SDK_action.answer.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_answer.name },
    { name: SDK_action.answer.name, from: SDK_state.s_silence.name, to: SDK_state.s_answer.name },  // 坐席辅导静音恢复通话状态
    { name: SDK_action.answer.name, from: SDK_state.s_agent_coach_call.name, to: SDK_state.s_answer.name },  // 坐席辅导恢复通话状态

    /*呼入*/
    // 呼入来电振铃
    { name: SDK_action.inringing.name, from: SDK_state.s_idle.name, to: SDK_state.s_inringing.name },
    { name: SDK_action.inringing.name, from: SDK_state.s_login.name, to: SDK_state.s_inringing.name },
    { name: SDK_action.inringing.name, from: SDK_state.s_after.name, to: SDK_state.s_inringing.name },   // 预测长签话机导常后接收再呼通坐席事件

    /*预测外呼登录时*/
    // 播放TTS
    { name: SDK_action.playtts.name, from: SDK_state.s_in_acceptcall_sending.name, to: SDK_state.s_playtts.name },
    { name: SDK_action.playtts.name, from: SDK_state.s_inringing.name, to: SDK_state.s_playtts.name },
    { name: SDK_action.playtts.name, from: SDK_state.s_idle.name, to: SDK_state.s_playtts.name },  // 空闲后可播放TTS
    { name: SDK_action.playtts.name, from: SDK_state.s_playtts.name, to: SDK_state.s_playtts.name },  // 连接播放多段语音时
    { name: SDK_action.playtts.name, from: SDK_state.s_transferinringing.name, to: SDK_state.s_playtts.name },  // 转接振铃播放TTS
    { name: SDK_action.playtts.name, from: SDK_state.s_transfer_acceptcall_sending.name, to: SDK_state.s_playtts.name },  // 转接接听播放TTS

    // 来电坐席接听
    { name: SDK_action.incall.name, from: SDK_state.s_login.name, to: SDK_state.s_incall.name },
    { name: SDK_action.incall.name, from: SDK_state.s_inringing.name, to: SDK_state.s_incall.name },
    { name: SDK_action.incall.name, from: SDK_state.s_idle.name, to: SDK_state.s_incall.name },  // 空闲后可接听来电
    { name: SDK_action.incall.name, from: SDK_state.s_in_acceptcall_sending.name, to: SDK_state.s_incall.name },
    { name: SDK_action.incall.name, from: SDK_state.s_playtts.name, to: SDK_state.s_incall.name },
    { name: SDK_action.incall.name, from: SDK_state.s_consulationservice.name, to: SDK_state.s_answer.name },
    { name: SDK_action.incall.name, from: SDK_state.s_silence.name, to: SDK_state.s_incall.name },  // 坐席辅导静音恢复通话状态
    { name: SDK_action.incall.name, from: SDK_state.s_agent_coach_call.name, to: SDK_state.s_incall.name },  // 坐席辅导恢复通话状态

    // 内呼坐席振铃
    { name: SDK_action.innerringing.name, from: SDK_state.s_idle.name, to: SDK_state.s_innerringing.name },
    { name: SDK_action.innerringing.name, from: SDK_state.s_busy.name, to: SDK_state.s_innerringing.name },  // 允许忙碌接内呼
    { name: SDK_action.innerringing.name, from: SDK_state.s_after.name, to: SDK_state.s_innerringing.name },  // 允许忙碌接内呼
    { name: SDK_action.innerringing.name, from: SDK_state.s_login.name, to: SDK_state.s_innerringing.name },  // 允许忙碌接内呼

    // 内呼坐席接通
    { name: SDK_action.innercall.name, from: SDK_state.s_in_acceptcall_sending.name, to: SDK_state.s_innercall.name },
    { name: SDK_action.innercall.name, from: SDK_state.s_innerringing.name, to: SDK_state.s_innercall.name },

    // 内呼失败
    { name: SDK_action.innercall_fail.name, from: SDK_state.s_makecall_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.innercall_fail.name, from: SDK_state.s_makecall_sending.name, to: SDK_state.s_idle.name },

    /*被咨询来电*/
    // 被咨询方来电振铃
    { name: SDK_action.consultinringing.name, from: SDK_state.s_login.name, to: SDK_state.s_consultinringing.name },
    { name: SDK_action.consultinringing.name, from: SDK_state.s_idle.name, to: SDK_state.s_consultinringing.name },
    { name: SDK_action.consultinringing.name, from: SDK_state.s_busy.name, to: SDK_state.s_consultinringing.name },
    { name: SDK_action.consultinringing.name, from: SDK_state.s_after.name, to: SDK_state.s_consultinringing.name },

    //被咨询方来电接通
    { name: SDK_action.consultincall.name, from: SDK_state.s_consult_acceptcall_sending.name, to: SDK_state.s_consultincall.name },
    { name: SDK_action.consultincall.name, from: SDK_state.s_consultinringing.name, to: SDK_state.s_consultincall.name },
    { name: SDK_action.answer.name, from: SDK_state.s_consultincall.name, to: SDK_state.s_answer.name },

    /* 被转接来电 */
    //被转接来电振铃
    { name: SDK_action.transferinringing.name, from: SDK_state.s_login.name, to: SDK_state.s_transferinringing.name },
    { name: SDK_action.transferinringing.name, from: SDK_state.s_idle.name, to: SDK_state.s_transferinringing.name },
    { name: SDK_action.transferinringing.name, from: SDK_state.s_busy.name, to: SDK_state.s_transferinringing.name },
    { name: SDK_action.transferinringing.name, from: SDK_state.s_after.name, to: SDK_state.s_transferinringing.name },

    //被转接来电接通
    { name: SDK_action.transferincall.name, from: SDK_state.s_transfer_acceptcall_sending.name, to: SDK_state.s_transferincall.name },
    { name: SDK_action.transferincall.name, from: SDK_state.s_transferinringing.name, to: SDK_state.s_transferincall.name },
    { name: SDK_action.transferincall.name, from: SDK_state.s_playtts.name, to: SDK_state.s_transferincall.name },   // 播放完TTS接通坐席
    { name: SDK_action.transferincall.name, from: SDK_state.s_consulationservice.name, to: SDK_state.s_transferincall.name },

    //请求挂断呼叫
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_makecall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_outringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_outcall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_calledringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_outboundcall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_inringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_playtts.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_incall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_transferinringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_consultinringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_consultincall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_sanfangcall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_transferincall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_monitorincall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_agentinsertincall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_interceptcall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_innerringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_innercall.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_monitorringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_interceptaltering.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_agentinsertringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_transferservice_sending.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_consulationservice.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_consulationservice_sending.name, to: SDK_state.s_cancelmakecall_sending.name },

    // 坐席辅导请求挂机
    // 坐席辅导（班长侧振铃）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_instructringing.name, to: SDK_state.s_cancelmakecall_sending.name },
    // 坐席辅导（班长侧接通）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_instructincall.name, to: SDK_state.s_cancelmakecall_sending.name },
    //  坐席辅导（坐席侧接通）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_agent_coach_call.name, to: SDK_state.s_cancelmakecall_sending.name },
    // 坐席辅导（坐席侧请求静音）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_silence_sending.name, to: SDK_state.s_cancelmakecall_sending.name },
    // 坐席辅导（坐席侧静音）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_silence.name, to: SDK_state.s_cancelmakecall_sending.name },
    // 坐席辅导（坐席侧请求取消静音）挂机
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_unsilence_sending.name, to: SDK_state.s_cancelmakecall_sending.name },
    { name: SDK_action.cancelmakecall_handle.name, from: SDK_state.s_leaveuser.name, to: SDK_state.s_cancelmakecall_sending.name },

    // 挂断呼叫
    { name: SDK_action.cancelmakecall.name, from: "*", to: SDK_state.s_cancelmakecall.name },

    // 请求保持
    { name: SDK_action.mute_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_mute_sending.name },
    { name: SDK_action.mute_handle.name, from: SDK_state.s_agent_coach_call.name, to: SDK_state.s_mute_sending.name },
    // 保持成功
    { name: SDK_action.mute.name, from: SDK_state.s_mute_sending.name, to: SDK_state.s_mute.name },
    // 保持失败
    { name: SDK_action.mute_fail.name, from: SDK_state.s_mute_sending.name, to: SDK_state.s_answer.name },
    { name: SDK_action.agent_coach_call_mute_fail.name, from: SDK_state.s_mute_sending.name, to: SDK_state.s_agent_coach_call.name },
    // 取消保持
    { name: SDK_action.unmute_handle.name, from: SDK_state.s_mute.name, to: SDK_state.s_unmute_sending.name },
    // 取消保持成功
    { name: SDK_action.unmute.name, from: SDK_state.s_unmute_sending.name, to: SDK_state.s_answer.name },
    { name: SDK_action.agent_coach_call_unmute.name, from: SDK_state.s_unmute_sending.name, to: SDK_state.s_agent_coach_call.name },
    // 取消保持失败
    { name: SDK_action.unmute_fail.name, from: SDK_state.s_unmute_sending.name, to: SDK_state.s_mute.name },

    // 请求咨询
    { name: SDK_action.agentconsult_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_agentconsult_sending.name },
    { name: SDK_action.agentconsult_handle.name, from: SDK_state.s_sanfangcall.name, to: SDK_state.s_agentconsult_sending.name },

    // 执行咨询
    { name: SDK_action.agentconsult.name, from: SDK_state.s_agentconsult_sending.name, to: SDK_state.s_agentconsult.name },
    { name: SDK_action.agentconsult.name, from: SDK_state.s_sanfangcall.name, to: SDK_state.s_agentconsult.name },
    { name: SDK_action.consultationcalls.name, from: SDK_state.s_agentconsult.name, to: SDK_state.s_consultationcalls.name },
    { name: SDK_action.sanfang_agentconsult_fail.name, from: SDK_state.s_agentconsult_sending.name, to: SDK_state.s_sanfangcall.name },
    { name: SDK_action.agentconsult_fail.name, from: SDK_state.s_agentconsult_sending.name, to: SDK_state.s_answer.name },

    // 取消咨询
    { name: SDK_action.agentconsultback_handle.name, from: SDK_state.s_agentconsult.name, to: SDK_state.s_agentconsultback_sending.name },
    { name: SDK_action.agentconsultback_handle.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_agentconsultback_sending.name },
    { name: SDK_action.agentconsultback_handle.name, from: SDK_state.s_tripartitetalk_sending.name, to: SDK_state.s_agentconsultback_sending.name },
    { name: SDK_action.agentconsultback_handle.name, from: SDK_state.s_agentshift_sending.name, to: SDK_state.s_agentconsultback_sending.name },
    { name: SDK_action.agentconsultback.name, from: SDK_state.s_agentconsultback_sending.name, to: SDK_state.s_answer.name },
    { name: SDK_action.agentconsultback.name, from: SDK_state.s_agentconsult.name, to: SDK_state.s_answer.name },
    { name: SDK_action.agentconsultback.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_answer.name },

    // 咨询转接
    { name: SDK_action.agentshift_handle.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_agentshift_sending.name },
    { name: SDK_action.agentshift_fail.name, from: SDK_state.s_agentshift_sending.name, to: SDK_state.s_consultationcalls.name },

    // 咨询IVR服务
    { name: SDK_action.consulationservice_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_consulationservice_sending.name },
    { name: SDK_action.consulationservice.name, from: SDK_state.s_consulationservice_sending.name, to: SDK_state.s_consulationservice.name },
    { name: SDK_action.consulationservice_fail.name, from: SDK_state.s_consulationservice_sending.name, to: SDK_state.s_answer.name },

    // 请求三方
    { name: SDK_action.tripartitetalk_handle.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_tripartitetalk_sending.name },
    { name: SDK_action.tripartitetalk_handle.name, from: SDK_state.s_agentconsultback_sending.name, to: SDK_state.s_tripartitetalk_sending.name },

    // 建立三方
    { name: SDK_action.tripartitetalk.name, from: SDK_state.s_tripartitetalk_sending.name, to: SDK_state.s_tripartitetalk.name },
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_tripartitetalk_sending.name, to: SDK_state.s_tripartitetalk.name },
    { name: SDK_action.tripartitetalk.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_tripartitetalk.name },

    // 咨询来电接通转为三方
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_consultincall.name, to: SDK_state.s_sanfangcall.name },
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_tripartitetalk.name, to: SDK_state.s_sanfangcall.name },
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_consultationcalls.name, to: SDK_state.s_sanfangcall.name },
    { name: SDK_action.tripartitetalk_fail.name, from: SDK_state.s_tripartitetalk_sending.name, to: SDK_state.s_consultationcalls.name },

    // 会议中请求转接主持人
    { name: SDK_action.transferModerator_handle.name, from: SDK_state.s_sanfangcall.name, to: SDK_state.s_transferModerator_sending.name },
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_transferModerator_sending.name, to: SDK_state.s_sanfangcall.name },

    // 转接
    { name: SDK_action.transfercall_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_transfercall_sending.name },
    // 转接通话到请求转接
    { name: SDK_action.transfercall_handle.name, from: SDK_state.s_consultincall.name, to: SDK_state.s_transfercall_sending.name },
    { name: SDK_action.transfergroup_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_transfergroup_sending.name },

    // 转接通话到请求转接
    { name: SDK_action.transfercall.name, from: SDK_state.s_transfercall_sending.name, to: SDK_state.s_transfercall.name },
    { name: SDK_action.transfering.name, from: SDK_state.s_transfercall_sending.name, to: SDK_state.s_transfering.name },
    { name: SDK_action.transfering.name, from: SDK_state.s_transfercall.name, to: SDK_state.s_transfering.name },
    { name: SDK_action.transfering.name, from: "*", to: SDK_state.s_transfering.name },
    { name: SDK_action.transfercall_fail.name, from: SDK_state.s_transfercall_sending.name, to: SDK_state.s_answer.name },
    { name: SDK_action.transfercall_fail.name, from: SDK_state.s_transfering.name, to: SDK_state.s_answer.name },
    { name: SDK_action.transfercall_fail.name, from: SDK_state.s_transfergroup_sending.name, to: SDK_state.s_answer.name },
    { name: SDK_action.transfercall_fail.name, from: SDK_state.s_transfering.name, to: SDK_state.s_answer.name },

    // 转接IVR服务
    { name: SDK_action.transferservice_handle.name, from: SDK_state.s_answer.name, to: SDK_state.s_transferservice_sending.name },
    { name: SDK_action.transferservice_fail.name, from: SDK_state.s_transferservice_sending.name, to: SDK_state.s_answer.name },

    // 取消转接
    { name: SDK_action.canceltransfercall_handle.name, from: SDK_state.s_transfering.name, to: SDK_state.s_canceltransfercall_sending.name },
    { name: SDK_action.canceltransfercall.name, from: SDK_state.s_canceltransfercall_sending.name, to: SDK_state.s_answer.name },

    // 强制示闲
    { name: SDK_action.forceidle.name, from: SDK_state.s_login.name, to: SDK_state.s_idle.name },
    { name: SDK_action.forceidle.name, from: SDK_state.s_busy.name, to: SDK_state.s_idle.name },
    { name: SDK_action.forceidle.name, from: SDK_state.s_after.name, to: SDK_state.s_idle.name },

    // 强制示忙
    { name: SDK_action.forcebusy.name, from: SDK_state.s_login.name, to: SDK_state.s_busy.name },
    { name: SDK_action.forcebusy.name, from: SDK_state.s_idle.name, to: SDK_state.s_busy.name },
    { name: SDK_action.forcebusy.name, from: SDK_state.s_after.name, to: SDK_state.s_busy.name },

    // 强拆
    { name: SDK_action.agentbreak.name, from: SDK_state.s_answer.name, to: SDK_state.s_after.name },
    { name: SDK_action.agentbreak.name, from: SDK_state.s_agentconsult.name, to: SDK_state.s_after.name },
    { name: SDK_action.agentbreak.name, from: SDK_state.s_sanfangcall.name, to: SDK_state.s_after.name },
    // 强制签出
    { name: SDK_action.forcelogout.name, from: "*", to: SDK_state.s_logout.name },
    // 监听、耳语
    { name: SDK_action.monitor_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_monitor_idle_sending.name },
    { name: SDK_action.monitor_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_monitor_busy_sending.name },
    { name: SDK_action.monitor_fail.name, from: SDK_state.s_monitor_idle_sending.name, to: SDK_state.s_idle.name },
    { name: SDK_action.monitor_fail.name, from: SDK_state.s_monitor_busy_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.monitor.name, from: SDK_state.s_monitor_idle_sending.name, to: SDK_state.s_monitor.name },
    { name: SDK_action.monitor.name, from: SDK_state.s_monitor_busy_sending.name, to: SDK_state.s_monitor.name },
    { name: SDK_action.monitorringing.name, from: SDK_state.s_monitor.name, to: SDK_state.s_monitorringing.name },
    { name: SDK_action.monitorincall.name, from: SDK_state.s_monitor_acceptcall_sending.name, to: SDK_state.s_monitorincall.name },
    { name: SDK_action.monitorincall.name, from: SDK_state.s_monitorringing.name, to: SDK_state.s_monitorincall.name },

    //强插
    { name: SDK_action.agentinsert_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_agentinsert_idle_sending.name },
    { name: SDK_action.agentinsert_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_agentinsert_busy_sending.name },
    { name: SDK_action.agentinsert_fail.name, from: SDK_state.s_agentinsert_idle_sending.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentinsert_fail.name, from: SDK_state.s_agentinsert_busy_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.agentinsert_fail.name, from: SDK_state.s_agentinsert_monitorincall_sending.name, to: SDK_state.s_monitorincall.name },
    { name: SDK_action.agentinsert.name, from: SDK_state.s_agentinsert_idle_sending.name, to: SDK_state.s_agentinsert.name },
    { name: SDK_action.agentinsert.name, from: SDK_state.s_agentinsert_busy_sending.name, to: SDK_state.s_agentinsert.name },
    { name: SDK_action.agentinsert.name, from: SDK_state.s_agentinsert_monitorincall_sending.name, to: SDK_state.s_agentinsert.name },
    { name: SDK_action.agentinsertringing.name, from: SDK_state.s_agentinsert.name, to: SDK_state.s_agentinsertringing.name },

    // 强插接听
    { name: SDK_action.agentinsertincall.name, from: SDK_state.s_insert_acceptcall_sending.name, to: SDK_state.s_agentinsertincall.name },
    { name: SDK_action.agentinsertincall.name, from: SDK_state.s_agentinsertringing.name, to: SDK_state.s_agentinsertincall.name },

    // 拦截
    { name: SDK_action.agentinterceptcall_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_agentinterceptcall_idle_sending.name },
    { name: SDK_action.agentinterceptcall_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_agentinterceptcall_busy_sending.name },
    { name: SDK_action.agentinterceptcall_handle.name, from: SDK_state.s_agentinsertincall.name, to: SDK_state.s_agentinterceptcall_agentinsertincall_sending.name },
    { name: SDK_action.agentinterceptcall_fail.name, from: SDK_state.s_agentinterceptcall_idle_sending.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentinterceptcall_fail.name, from: SDK_state.s_agentinterceptcall_busy_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.agentinterceptcall_fail.name, from: SDK_state.s_agentinterceptcall_agentinsertincall_sending.name, to: SDK_state.s_agentinsertincall.name },
    { name: SDK_action.agentinterceptcall.name, from: SDK_state.s_agentinterceptcall_idle_sending.name, to: SDK_state.s_agentinterceptcall.name },
    { name: SDK_action.agentinterceptcall.name, from: SDK_state.s_agentinterceptcall_busy_sending.name, to: SDK_state.s_agentinterceptcall.name },
    { name: SDK_action.agentinterceptcall.name, from: SDK_state.s_agentinterceptcall_agentinsertincall_sending.name, to: SDK_state.s_agentinterceptcall.name },
    { name: SDK_action.intercept.name, from: SDK_state.s_agentinterceptcall_idle_sending.name, to: SDK_state.s_intercept.name },
    { name: SDK_action.intercept.name, from: SDK_state.s_agentinterceptcall_busy_sending.name, to: SDK_state.s_intercept.name },
    { name: SDK_action.intercept.name, from: SDK_state.s_agentinterceptcall_agentinsertincall_sending.name, to: SDK_state.s_intercept.name },
    { name: SDK_action.interceptaltering.name, from: SDK_state.s_agentinterceptcall.name, to: SDK_state.s_interceptaltering.name },

    // 拦截接听
    { name: SDK_action.interceptcall.name, from: SDK_state.s_interceptaltering.name, to: SDK_state.s_intercept_acceptcall_sending.name },
    { name: SDK_action.interceptcall.name, from: SDK_state.s_intercept_acceptcall_sending.name, to: SDK_state.s_interceptcall.name },
    { name: SDK_action.interceptcall.name, from: SDK_state.s_interceptaltering.name, to: SDK_state.s_interceptcall.name },

    // siperror
    { name: SDK_action.siperror.name, from: SDK_state.s_login.name, to: SDK_state.s_siperror.name },
    { name: SDK_action.siperror.name, from: SDK_state.s_idle.name, to: SDK_state.s_siperror.name },
    { name: SDK_action.siperror.name, from: SDK_state.s_busy.name, to: SDK_state.s_siperror.name },

    // 话后
    { name: SDK_action.after.name, from: "*", to: SDK_state.s_after.name },

    // 断开连接
    { name: SDK_action.disconnect.name, from: "*", to: SDK_state.s_disconnect.name },

    // 重新连接
    { name: SDK_action.reconnection_handle.name, from: SDK_state.s_nologin.name, to: SDK_state.s_reconnection_sending.name },
    { name: SDK_action.reconnection_handle.name, from: SDK_state.s_disconnect.name, to: SDK_state.s_reconnection_sending.name },
    { name: SDK_action.reconnection.name, from: SDK_state.s_disconnect.name, to: SDK_state.s_reconnection.name },
    { name: SDK_action.reconnection.name, from: SDK_state.s_reconnection_sending.name, to: SDK_state.s_reconnection.name },
    { name: SDK_action.reconnection_fail.name, from: SDK_state.s_reconnection_sending.name, to: SDK_state.s_reconnection_fail.name },

    // 各种事件的重新连接
    { name: SDK_action.agentidle_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_idle_sending.name },
    { name: SDK_action.agentbusy_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_busy_sending.name },
    { name: SDK_action.agentidle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_idle.name },
    { name: SDK_action.agentbusy.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_busy.name },
    { name: SDK_action.makecall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_makecall.name },
    { name: SDK_action.outringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_outringing.name },
    { name: SDK_action.outcall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_outcall.name },
    { name: SDK_action.calledringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_calledringing.name },
    { name: SDK_action.outboundcall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_outboundcall.name },
    { name: SDK_action.answer.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_answer.name },
    { name: SDK_action.inringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_inringing.name },
    { name: SDK_action.playtts.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_playtts.name },
    { name: SDK_action.incall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_incall.name },
    { name: SDK_action.innerringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_innerringing.name },
    { name: SDK_action.innercall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_innercall.name },
    { name: SDK_action.consultinringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_consultinringing.name },
    { name: SDK_action.consultincall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_consultincall.name },
    { name: SDK_action.transferinringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_transferinringing.name },
    { name: SDK_action.transferincall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_transferincall.name },
    { name: SDK_action.cancelmakecall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_cancelmakecall.name },
    { name: SDK_action.mute.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_mute.name },
    { name: SDK_action.agentconsult.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_agentconsult.name },
    { name: SDK_action.sanfangcall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_sanfangcall.name },
    { name: SDK_action.transfering.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_transfering.name },
    { name: SDK_action.monitor.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_monitor.name },
    { name: SDK_action.monitorincall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_monitorincall.name },
    { name: SDK_action.agentinsert.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_agentinsert.name },
    { name: SDK_action.agentinsertincall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_agentinsertincall.name },
    { name: SDK_action.agentinterceptcall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_intercept.name },
    { name: SDK_action.interceptcall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_interceptcall.name },
    { name: SDK_action.interceptaltering.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_interceptaltering.name },
    { name: SDK_action.siperror.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_siperror.name },
    { name: SDK_action.siperror.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_sanfangcall.name },

    // 坐席辅导
    { name: SDK_action.instruct_handle.name, from: SDK_state.s_idle.name, to: SDK_state.s_instruct_idle_sending.name },  // 空闲时请求坐席辅导（班长）
    { name: SDK_action.instruct_handle.name, from: SDK_state.s_busy.name, to: SDK_state.s_instruct_busy_sending.name },  // 忙碌时请求坐席辅导（班长）
    { name: SDK_action.instruct_fail.name, from: SDK_state.s_instruct_idle_sending.name, to: SDK_state.s_idle.name },
    { name: SDK_action.instruct_fail.name, from: SDK_state.s_instruct_busy_sending.name, to: SDK_state.s_busy.name },
    { name: SDK_action.instruct.name, from: SDK_state.s_instruct_idle_sending.name, to: SDK_state.s_instruct.name },
    { name: SDK_action.instruct.name, from: SDK_state.s_instruct_busy_sending.name, to: SDK_state.s_instruct.name },
    { name: SDK_action.instructringing.name, from: SDK_state.s_instruct.name, to: SDK_state.s_instructringing.name },
    { name: SDK_action.instructringing.name, from: SDK_state.s_instruct_acceptcall_sending.name, to: SDK_state.s_instructringing.name },
    { name: SDK_action.instructincall.name, from: SDK_state.s_instructringing.name, to: SDK_state.s_instructincall.name },
    { name: SDK_action.instructincall.name, from: SDK_state.s_instruct_acceptcall_sending.name, to: SDK_state.s_instructincall.name },
    { name: SDK_action.instructincall.name, from: SDK_state.s_incall.name, to: SDK_state.s_instructincall.name },
    { name: SDK_action.monitorCoachCallFail.name, from: SDK_state.s_instruct_acceptcall_sending.name, to: SDK_state.s_monitorCoachCallFail.name },
    { name: SDK_action.agentCoachCall.name, from: SDK_state.s_answer.name, to: SDK_state.s_agent_coach_call.name },  // 呼出坐席辅导接通
    { name: SDK_action.agentCoachCall.name, from: SDK_state.s_silence.name, to: SDK_state.s_agent_coach_call.name },  // 静音时班长挂机坐席恢复辅导接通

    // 坐席辅导用户静音
    { name: SDK_action.silence_handle.name, from: SDK_state.s_agent_coach_call.name, to: SDK_state.s_silence_sending.name },
    { name: SDK_action.silence.name, from: SDK_state.s_silence_sending.name, to: SDK_state.s_silence.name },
    { name: SDK_action.unsilence_handle.name, from: SDK_state.s_silence.name, to: SDK_state.s_unsilence_sending.name },
    { name: SDK_action.unsilence.name, from: SDK_state.s_unsilence_sending.name, to: SDK_state.s_agent_coach_call.name },

    // 坐席辅导重连
    { name: SDK_action.instruct_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_instruct_idle_sending.name },
    { name: SDK_action.instruct_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_instruct_busy_sending.name },
    { name: SDK_action.instruct.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_instruct.name },
    { name: SDK_action.instructringing.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_instructringing.name },
    { name: SDK_action.instructincall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_instructincall.name },
    { name: SDK_action.agentCoachCall.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_agent_coach_call.name },  // 呼出坐席辅导接通
    { name: SDK_action.silence_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_silence_sending.name },
    { name: SDK_action.silence.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_silence.name },
    { name: SDK_action.unsilence_handle.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_unsilence_sending.name },
    { name: SDK_action.unsilence.name, from: SDK_state.s_reconnection.name, to: SDK_state.s_agent_coach_call.name }
  ];

  CallCenterStatus = CallCenterStateMachine.create({
    initial: SDK_state.s_nologin.name,
    events: SDK_events,
    callbacks: {
      onleavestate: function (name, from, to, args) {//在要改变对象状态时触发
        CallCenter.log("leave from:" + from + " to:" + to);
      },
      // 验证失败
      onbeforeAuthfail: function (event, from, to) {
        CallCenter.setOrgClass().setStatusAndPhoneText(UIT("loginfail")).clearConnection();
      },
      // 掉线
      onbeforeDisconnect: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText("重连中...").log("连接断开，准备重连");
        setTimeout(function () {
          CallCenter.init();
        }, 1000);//断线重连
      },
      // 退出登录
      onbeforeForcelogout: function (event, from, to) {
        CallCenter._islogin = false;
        CallCenter.setOrgClass().setStatusAndPhoneText(UIT("forcedCheckout")).clearConnection();
      },
      // 咨询失败
      onbeforeAgentconsult_fail: function (event, from, to) {
        CallCenter.eventAlert(UIT("consultfail"));
      },
      // 转接失败
      onbeforeTransfercall_fail: function (event, from, to) {
        CallCenter.eventAlert(UIT("transferfail"));
      },
      // 进入未登录状态
      onenterNologin: function (event, from, to) {
        CallCenter.clearConnection();
      },
      // 重复登录
      onenterKick: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("kick")).clearConnection();
      },
      // 请求登录
      onenterLogin_sending: function (event, from, to) {
        CallCenter.handle_login("logon");
      },
      // 登录成功
      onenterLogin: function (event, from, to) {
        CallCenter._islogin = true;
        CallCenter._nowStatus = "logon";
        if (CallCenter._auto == 1) {
          // 预测外呼
          CallCenter.setStatusAndPhoneText(UIT("waitingPhoneConnect"));
        } else {
          CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("loginsuccess"));

          $("#CallCenter_status_tiao").unbind("click").bind("click", function () {
            /*空闲，忙碌，切换*/
            if ($("#CallCenter_status_buts").css("display") == "none") {
              if ($("#CallCenter_free").css("display") == "list-item" || $("#CallCenter_busy").css("display") == "list-item") {
                CallCenter.showControl("#CallCenter_status_buts");
              }
            } else {
              CallCenter.hideControl("#CallCenter_status_buts");
            }
            if (CallCenter._mainStatus === "meetingMaster" || CallCenter._mainStatus === "meetingConsult") {
              $("#CallCenter_seat_list li").length > 0 && $("#CallCenter_seat_list").toggle();
            } else {
              $("#CallCenter_seat_list").hide();
            }
          });
          CallCenter.showControl("#CallCenter_status_buts,#CallCenter_free,.CallCenter_busy");
          CallCenter.showControl("#CallCenter_calloutbut");  // 显示外呼按键
          CallCenter._meetingSeats = [
            {
              operator: CallCenter._operatorid,
              status: "meetingMaster"
            }
          ];
        }
        CallCenter.browserRefreshConfirm();
      },
      // 发送退出
      onenterLogout_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("requestlogout"));
        CallCenter.log("请求登出");
        var sendobj = new CallCenter._sendcmd("logout");
        CallCenter.send(sendobj);
      },
      // 退出成功
      onenterLogout: function (event, from, to) {
        CallCenter.setlocalstorage("refreshReconnection", 0);
        CallCenter.clearConnection();
        if (CallCenter._nowebcall) {
          CallCenter._nowebcall = false;
          CallCenter.initControl().setStatusAndPhoneText(UIT("waitConnection"));
        } else {
          CallCenter.initControl().setStatusAndPhoneText(UIT("alreadyLogout"));
        }
      },
      // 发送重新连接
      onenterReconnection_sending: function (event, from, to) {
        CallCenter.handle_login("reconnection");
      },
      // 发送空闲
      onenterIdle_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("requestidle"));
        var sendobj = new CallCenter._sendcmd("agentidle");
        CallCenter.send(sendobj);
      },
      // 空闲
      onenterIdle: function (event, from, to) {
        CallCenter._nowStatus = "agentidle";
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(CallCenter._defaultIdleText);
        if (CallCenter._auto == 1) {//预测外呼
          CallCenter.showControl(".CallCenter_busy");
        } else {
          // 启用忙碌、外呼、内呼
          CallCenter.showControl(".CallCenter_busy,#CallCenter_calloutbut,#CallCenter_innercall");
        }
        CallCenter.hideControl("#CallCenter_status_buts");
      },
      // 空闲失败
      onenterIdle_fail: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(CallCenter._defaultIdleText + "失败");
        if (CallCenter._auto == 1) {//预测外呼
          CallCenter.showControl("#CallCenter_free");
        } else {
          // 启用空闲、外呼
          CallCenter.showControl("#CallCenter_free,#CallCenter_calloutbut");
        }
        CallCenter.hideControl("#CallCenter_status_buts");
      },
      // 发送忙碌
      onenterBusy_sending: function (event, from, to, busydescr) {
        CallCenter.setStatusAndPhoneText(UIT("requestbusy"));
        if (!busydescr) {
          busydescr = 0;
        }
        CallCenter._busyType = busydescr;
        var sendobj = new CallCenter._sendcmd("agentbusy");
        sendobj.busydescr = busydescr;
        CallCenter.send(sendobj);
      },
      // 忙碌
      onenterBusy: function (event, from, to) {
        CallCenter._nowStatus = "agentbusy";
        var showText = CallCenter._busyTypeMap.get(CallCenter._busyType);
        if (!showText || CallCenter.getBusyType() === 0) {
          showText = CallCenter._defaultBusyText;
        }
        CCProps.reconnection.hasDisconnect = false; //断网重连状态设置
        CallCenter._callId = "";
        CallCenter._calling = false;
        CallCenter.initControl().setOrgClass()
        .showControl("#CallCenter_free,.CallCenter_busy")
        .setStatusAndPhoneText(showText)
        .hideControl("#CallCenter_status_buts,#CallCenter_calloutbut")
        .hideControl("#CallCenter_busy" + (Number(CallCenter._busyType) === 0 ? "" : CallCenter._busyType));
        if (!CallCenter.isAuto()) {
          CallCenter.showControl("#CallCenter_calloutbut");
        }
      },
      // 忙碌失败
      onenterBusy_fail: function (event, from, to) {
        CallCenter.log("忙碌失败以前：" + CallCenter._nowStatus);
        switch (CallCenter._nowStatus) {
          case "agentbusy":
            CallCenterStatus.agentbusy();
            break;
          case "agentidle":
            CallCenterStatus.agentidle();
            break;
          case "logon":
            CallCenterStatus.login();
            break;
          case "after":
            CallCenterStatus.after();
            break;
          default:
            CallCenter.log("未处理的：" + CallCenter._nowStatus);
        }
      },
      // 来电接听
      onenterIn_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_in_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 外呼接听
      onenterOut_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_out_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 咨询接听
      onenterConsult_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_consult_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 转接接听
      onenterTransfer_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_transfer_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 监听接听
      onenterMonitor_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_monitor_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 拦截接听
      onenterIntercept_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_intercept_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 强插接听
      onenterInsert_acceptcall_sending: function (event, from, to) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requestacceptcall")).handle_acceptcall();
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_insert_acceptcall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("acceptfail"));
            CallCenterStatus.acceptcall_fail();
          }
        }, 5000);
      },
      // 请求外呼
      onenterMakecall_sending: function (event, from, to, arg) {
        CallCenter.handle_makecall(arg);
        // 6秒后状态没有变化，监听失败
        var timer = setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_makecall_sending.name)) {
            CallCenter.eventAlert(UIT("calltimeout"));
            CallCenterStatus.makecall_fail();
          }
          clearTimeout(timer);
        }, 6 * 1000);
      },
      // 外呼成功
      onenterMakecall: function (event, from, to) {
        CallCenter.initControl().setStatusAndPhoneText(UIT("incall")).setOrgClass().showControl("#CallCenter_phonenum,#CallCenter_hangupbut");
        CallCenter._isCallout = true;
        CallCenter._calling = true;
        CallCenter._calling_from = "makecall";
      },
      // 外呼失败
      onbeforeMakecall_fail: function (event, from, to) {
        CallCenter.initControl().setStatusAndPhoneText(UIT("calloutfail")).showControl(".CallCenter_busy");
        // 非预测外呼且坐席空闲
        if (!CallCenter.isAuto() && (CallCenter.getNowStatus() === "agentidle" || CallCenter.getNowStatus() === "agentbusy")) {
          CallCenter.showControl("#CallCenter_calloutbut");  // 显示外呼按键
        }
        CallCenter._isCallout = false;
        CallCenter._calling = false;
      },
      // 发送取消外呼
      onenterCancelmakecall_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("requestCancelCall"));
        var sendobj = new CallCenter._sendcmd("cancelmakecall");
        sendobj.agentoperatorid = CallCenter._operatorid;
        CallCenter.send(sendobj);
      },
      // 外呼坐席振铃
      onenterOutringing: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
        if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
          CallCenter._isCallout = false;
        } else {
          CallCenter._isCallout = true;
        }
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("agentringing")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 外呼接通坐席
      onenterOutcall: function (event, from, to, arg) {
        CallCenter._called = arg.number || CallCenter._called;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("connectingAgent")).showControl("#CallCenter_hangupbut");
      },
      // 外呼被叫振铃
      onenterCalledringing: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("calledring")).showControl("#CallCenter_hangupbut,#CallCenter_phonenum");
      },
      // 预测外呼接通被叫
      onenterOutboundcall: function (event, from, to, arg) {
        CallCenterStatus.current = "answer"; // 统一状态
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._isCallout = true;
        CallCenter._calling = true;
        CallCenter._calling_from = "outboundcall";
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("inThePhone")).showCallingControl();
      },
      // 外呼接通被叫
      onenterAnswer: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._calling = true;
        if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
          CallCenter._isCallout = false;
        } else {
          CallCenter._isCallout = true;
        }
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("inThePhone")).showCallingControl();
        CallCenter._calling_from = "answer";
        if (CallCenter._isInnercall) {
          CallCenter.hideControl("#CallCenter_mutebut,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut");
        }
        if (!CallCenter._isSanfangCall) {
          $("#CallCenter_seat_list").hide();
        }
      },
      // 保持静音
      onenterMute_sending: function (event, from, to) {
        CallCenter.handle_mute();
      },
      // 外呼接通保持
      onenterMute: function (event, from, to) {
        CallCenter.event_mute();
      },
      // 取消保持静音
      onenterUnmute_sending: function (event, from, to) {
        CallCenter.handle_unmute();
      },
      // 外呼接通发送咨询
      onenterAgentconsult_sending: function (event, from, to, arg) {
        CallCenter.handle_agentconsult(arg);
      },
      // 外呼时咨询中
      onenterAgentconsult: function (event, from, to) {
        CallCenter.event_agentconsult();
      },
      // 外呼通话时咨询接通
      onenterConsultationcalls: function (event, from, to) {
        CallCenter.event_consultationcalls();
      },
      // 外呼时发送取消咨询
      onenterAgentconsultback_sending: function (event, from, to) {
        CallCenter.handle_agentconsultabck();
      },
      // 外呼时发送咨询服务data
      onenterConsulationservice_sending: function (event, from, to, arg) {
        CallCenter.initControl().showControl("#CallCenter_phonenum,#CallCenter_hangupbut").setStatusAndPhoneText(UIT("consultingService"))
        .handle_consulationservice(arg);
      },
      // 外呼时咨询服务
      onenterConsulationservice: function (event, from, to) {
        CallCenter.initControl().setOrgClass().showControl("#CallCenter_hangupbut").setStatusAndPhoneText(UIT("advisoryService"));
      },
      // 外呼时咨询转接
      onenterAgentshift_sending: function (event, from, to) {
        CallCenter.handle_agentshift();
      },
      // 外呼时发送三方
      onenterTripartitetalk_sending: function (event, from, to) {
        CallCenter.handle_tripartitetalk();
      },
      /// 外呼三方建立中
      onenterTripartitetalk: function (event, from, to) {
        CallCenter.event_tripartitetalk(event, from, to);
      },
      // 三方建立成功
      onenterSanfangcall: function (event, from, to) {
        CallCenter.event_sanfangcall(event, from, to);
      },
      // 外呼后发送转接
      onenterTransfercall_sending: function (event, from, to, arg) {
        CallCenter.handle_transfercall(arg);
      },
      // 转接
      onenterAgentcoach_transfercall_sending: function (event, from, to, arg) {
        CallCenter.handle_transfercall(arg);
      },
      // 外呼后发送转接技能组
      onenterTransfergroup_sending: function (event, from, to, arg) {
        CallCenter.initControl().handle_transfergroup(arg);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_transfergroup_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("transfergroupfail"));
            CallCenterStatus.transfercall_fail();
          }
        }, 5000);
      },
      // 通话时被转接振铃
      onenterTransfering: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("transfering"));
      },
      // 通话发送取消转接
      onenterCanceltransfercall_sending: function (event, from, to) {
        CallCenter.handle_canceltransfercall();
      },
      // 通话发送转接服务
      onenterTransferservice_sending: function (event, from, to, arg) {
        CallCenter.initControl().showControl("#CallCenter_phonenum").setStatusAndPhoneText(UIT("requesttransferservice")).handle_transferservice(arg);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_transferservice_sending.name) && !CallCenter._leaveuserByTransferService) {
            CallCenter._leaveuserByTransferService = false;
            CallCenterStatus.transferservice_fail();
            CallCenter.eventAlert(UIT("transferservicefail"));
          }
        }, 5000);
      },
      // 呼入振铃
      onenterInringing: function (event, from, to, arg) {
        Utils.reconnection.preserveTimerWhenReconnection();
        if (!arg.agentStandby) {
          CallCenter._callId = arg.callid || CallCenter._callId;
          CallCenter._timestamp = arg.timestamp || CallCenter._callId;
          CallCenter._caller = arg.caller || CallCenter._caller;
          CallCenter._called = arg.called || CallCenter._called;
          CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
          CallCenter._calling = true;
          if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
            CallCenter._isCallout = false;
          } else {
            CallCenter._isCallout = true;
          }
          CallCenter._calling_from = "inringing";
          CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("inringing")).showControl("#CallCenter_hangupbut,#CallCenter_phonenum");
          Utils.callHandler.showAnswerButton();
        } else {
          CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("inringing"));
        }
      },
      // 播放TTS文件
      onenterPlaytts: function (event, from, to) {
        CallCenter._callingtimer = 0;
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("playingOperator"));
      },
      // 呼入接通
      onenterIncall: function (event, from, to, arg) {
        CallCenterStatus.current = "answer"; // 统一状态
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._calling_from = "incall";
        CallCenter._agentStandby = arg.agentStandby;
        if (!arg.agentStandby) {
          CallCenter.initControl().setGreenClass().showCallingControl().setStatusAndPhoneText(UIT("inThePhone"));
        } else {
          CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("telephoneConnection"));
          $("#CallCenter_status_tiao").unbind("click").bind("click", function () {
            //空闲，忙碌，切换
            if ($("#CallCenter_status_buts").css("display") == "none") {
              if ($("#CallCenter_free").css("display") == "list-item" || $("#CallCenter_busy").css("display") == "list-item") {
                CallCenter.showControl("#CallCenter_status_buts");
              }
            } else {
              CallCenter.hideControl("#CallCenter_status_buts");
            }
          });
          if (CallCenter._nowStatus == "agentbusy") {
            CallCenter.initControl().setOrgClass().setStatusAndPhoneText(CallCenter._defaultBusyText).showControl("#CallCenter_free");
          } else {
            CallCenter.showControl("#CallCenter_status_buts,#CallCenter_free,.CallCenter_busy");
          }
        }
      },
      // 咨询来电振铃
      onenterConsultinringing: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
        Utils.reconnection.preserveTimerWhenReconnection();
        if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
          CallCenter._isCallout = false;
        } else {
          CallCenter._isCallout = true;
        }
        CallCenter._calling = true;
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("consultinringing")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 被咨询方接通
      onenterConsultincall: function (event, from, to) {
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._calling_from = "consult";
        CallCenter._calling = true;
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("consultingCallers")).showControl("#CallCenter_hangupbut");
        CallCenter.toggleMuteIcon();
      },
      // 转接来电振铃
      onenterTransferinringing: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
        CallCenter._calling = true;
        if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
          CallCenter._isCallout = false;
        } else {
          CallCenter._isCallout = true;
        }
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("transferinringing")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 转接通话中
      onenterTransferincall: function (event, from, to, arg) {
        CallCenterStatus.current = "answer"; // 统一状态
        CallCenter.log("当前状态-current：" + CallCenterStatus.current);
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        CallCenter._desNo = arg.desensitizationNo || CallCenter._desNo;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._calling_from = "transfer";
        CallCenter._calling = true;
        if (arg.dir === 0 || (arg.userdata && arg.userdata.dir === 0)) {
          CallCenter._isCallout = false;
        } else {
          CallCenter._isCallout = true;
        }
        CallCenter.initControl().setGreenClass().showCallingControl().setStatusAndPhoneText(UIT("transferincall"));
      },
      // 内呼来电振铃
      onenterInnerringing: function (event, from, to, arg) {
        CallCenter._callId = arg.callid || CallCenter._callId;
        CallCenter._timestamp = arg.timestamp || CallCenter._callId;
        CallCenter._caller = arg.caller || CallCenter._caller;
        CallCenter._called = arg.called || CallCenter._called;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._isCallout = false;
        CallCenter._calling = true;
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("Innercallringing")).showControl("#CallCenter_hangupbut,#CallCenter_phonenum");
        Utils.callHandler.showAnswerButton();
      },
      // 内呼接通
      onenterInnercall: function (event, from, to) {
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._calling = true;
        CallCenter.initControl().setStatusAndPhoneText(UIT("Incallcalls")).setGreenClass().showCallingControl()
        .hideControl("#CallCenter_mutebut,#CallCenter_transfercallbut,#CallCenter_consultbut,#CallCenter_ivrbut");
      },
      // 空闲时发送监听
      onenterMonitor_idle_sending: function (event, from, to, arg) {
        CallCenter.handle_monitor(arg);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_monitor_idle_sending.name)) {//5秒后状态没有变化，监听失败
            CallCenter.eventAlert(UIT("monitorfail"));
            CallCenterStatus.monitor_fail();
          }
        }, 5000);
      },
      // 忙碌时发送监听
      onenterMonitor_busy_sending: function (event, from, to, arg) {
        CallCenter.handle_monitor(arg);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_monitor_busy_sending.name)) {//5秒后状态没有变化，监听失败
            CallCenter.eventAlert(UIT("monitorfail"));
            CallCenterStatus.monitor_fail();
          }
        }, 5000);
      },
      // 监听回执
      onenterMonitor: function (event, from, to) {
        CallCenter._calling_from = "monitor";
        CallCenter._calling = true;
      },
      // 监听振铃
      onenterMonitorringing: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("monitorringing")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 监听接通
      onenterMonitorincall: function (event, from, to) {
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("MonitorInthecall")).showControl("#CallCenter_hangupbut");
        CallCenter.toggleMuteIcon();
      },
      // 空闲时发送拦截
      onenterAgentinterceptcall_idle_sending: function (event, from, to, agentid) {
        CallCenter.handle_intercept(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_agentinterceptcall_idle_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("interceptfail"));
            CallCenterStatus.agentinterceptcall_fail();
          }
        }, 5000);
      },
      // 忙碌时发送拦截
      onenterAgentinterceptcall_busy_sending: function (event, from, to, agentid) {
        CallCenter.handle_intercept(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_agentinterceptcall_busy_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("interceptfail"));
            CallCenterStatus.agentinterceptcall_fail();
          }
        }, 5000);
      },
      // 强插通话中发送拦截
      onenterAgentinterceptcall_agentinsertincall_sending: function (event, from, to, agentid) {
        CallCenter.handle_intercept(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_agentinterceptcall_agentinsertincall_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("interceptfail"));
            CallCenterStatus.agentinterceptcall_fail();
          }
        }, 5000);
      },
      // 拦截回执
      onenterAgentinterceptcall: function (event, from, to) {
        CallCenter._calling_from = "interceptcall";
        CallCenter._calling = true;
      },
      // 拦截中
      onenterIntercept: function (event, from, to) {
        CallCenter._calling = true;
      },
      // 拦截振铃
      onenterInterceptaltering: function (event, from, to) {
        CallCenter._calling = true;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("interceptaltering")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 拦截通话中
      onenterInterceptcall: function (event, from, to) {
        CallCenter._calling = true;
        Utils.reconnection.preserveTimerWhenReconnection();
        CallCenter._calling_from = "interceptcall";
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("interceptcall")).showControl("#CallCenter_hangupbut");
        CallCenter.toggleMuteIcon();
      },
      // 空闲时发送强插
      onenterAgentinsert_idle_sending: function (event, from, to, agentid) {
        CallCenter.handle_agentinsert(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_agentinsert_idle_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("insertfail"));
            CallCenterStatus.agentinsert_fail();
          }
        }, 5000);
      },
      // 忙碌时发送强插
      onenterAgentinsert_busy_sending: function (event, from, to, agentid) {
        CallCenter.handle_agentinsert(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_agentinsert_busy_sending.name)) {//5秒后状态没有变化，失败
            CallCenter.eventAlert(UIT("insertfail"));
            CallCenterStatus.agentinsert_fail();
          }
        }, 5000);
      },
      // 强插操作成功
      onenterAgentinsert: function (event, from, to) {
        CallCenter._isMeeting = true;
        CallCenter._calling_from = "agentinsert";
        CallCenter._calling = true;
      },
      // 强插振铃
      onenterAgentinsertringing: function (event, from, to) {
        CallCenter._calling = true;
        CallCenter.initControl().setStatusAndPhoneText(UIT("insertringing"));
        if (CallCenter._logintype == CallCenter._loginType_web || CallCenter._serverType == CallCenter._serverType_cti) {
          CallCenter.showControl("#CallCenter_answer,#CallCenter_hangupbut");
        }
      },
      // 强插通话中
      onenterAgentinsertincall: function (event, from, to) {
        CallCenter._calling = true;
        CallCenter.initControl().setStatusAndPhoneText(UIT("insertcall")).setGreenClass().showControl("#CallCenter_hangupbut");
        CallCenter.toggleMuteIcon();
      },
      // 会议中转接主持人
      onenterTransferModerator_sending: function (event, from, to, agentid) {
        CallCenter.transferModerator_handle(agentid);
      },
      // 话后
      onenterAfter: function (event, from, to) {
        CCProps.reconnection.hasDisconnect = false; //断网重连设置状态
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("after")).showControl("#CallCenter_free,.CallCenter_busy");
        if (!CallCenter.isAuto()) {
          CallCenter.showControl("#CallCenter_calloutbut");
        }
        CallCenter._calling = false;
        CallCenter._isCallout = false;
        CallCenter._nowStatus = "after";
        CallCenter._callId = "";
        CallCenter._timestamp = "";
        CallCenter._caller = "";
        CallCenter._called = "";
        CallCenter._calling_from = "";
        CallCenter._be_operator = "";
        CallCenter._isMeeting = false;
        CallCenter._isInnercall = false;
        CallCenter._leaveconf = false;
        CallCenter._isSanfangCall = false;
        CallCenter._isconsultCall = 0;
        $("#CallCenter_seat_list").html("");
        $("#CallCenter_seat_list").hide();
      },
      // 话机异常
      onenterSiperror: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("sipError")).showControl(".CallCenter_busy,#CallCenter_free");
        if (CallCenter._logintype == CallCenter._loginType_web) {
          SoftPhone.Login(CallCenter._media_ip, CallCenter._media_port, CallCenter._sip_id, CallCenter._sip_pwd);
        }
      },
      // 重连成功
      onenterReconnection: function (event, from, to) {
        CallCenter.log("event:" + event + " from:" + from + " to:" + to);
        //CallCenter.setOrgClass().setStatusAndPhoneText("重连成功");
        CCProps.reconnection.isLastReconnection = true;
        CallCenter._islogin = true;
      },
      // 重连失败
      onenterReconnection_fail: function (event, from, to) {
        CallCenter.log("event:" + event + " from:" + from + " to:" + to);
        CallCenter.setOrgClass().setStatusAndPhoneText(UIT("reconnectionFail"));
        CallCenter.setlocalstorage("refreshReconnection", 0);
      },

      /*------------------------------- 坐席辅导 --------------------------*/
      // 空闲时发送坐席辅导 @agentid 要辅导坐席的工号
      onenterInstruct_idle_sending: function (event, from, to, agentid) {
        CallCenter.handle_instruct(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_instruct_idle_sending.name)) {//5秒后状态没有变化，监听失败
            CallCenter.eventAlert(UIT("monitorbCoachCallFail"));
            CallCenterStatus.instruct_fail();
          }
        }, 5000);
      },
      // 忙碌时发送坐席辅导 @agentid 要辅导坐席的工号
      onenterInstruct_busy_sending: function (event, from, to, agentid) {
        CallCenter.handle_instruct(agentid);
        setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_instruct_busy_sending.name)) {//5秒后状态没有变化，监听失败
            CallCenter.eventAlert(UIT("monitorbCoachCallFail"));
            CallCenterStatus.instruct_fail();
          }
        }, 5000);
      },
      // 坐席辅导
      onenterInstruct: function (event, from, to) {
        CallCenter._calling_from = "instruct";
        CallCenter._calling = true;
      },
      // 坐席辅导振铃
      onenterInstructringing: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("Tutoringb_ringing")).showControl("#CallCenter_hangupbut");
        Utils.callHandler.showAnswerButton();
      },
      // 坐席辅导接听
      onenterInstruct_acceptcall_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("Tutoringb_calling")).handle_acceptcall();
        window.setTimeout(function () {
          if (CallCenterStatus.is(SDK_state.s_instruct_acceptcall_sending.name)) {//10秒状态没有变化，回到振铃
            CallCenter.eventAlert(UIT("Tutoringb_callfail"));
            CallCenterStatus.instructringing();
          }
        }, 10000);
      },
      // 坐席辅导中（班长）
      onenterInstructincall: function (event, from, to) {
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("Monitor_tutoring")).showControl("#CallCenter_hangupbut");
        CallCenter.toggleMuteIcon();
      },
      // 坐席辅导失败（班长）
      onenterMonitorCoachCallFail: function (event, from, to, reason) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(reason || UIT("instruct_fail"));
      },
      // 坐席辅导中（坐席）
      onenterAgent_coach_call: function (event, from, to) {
        CallCenter._isCoashCall = 1;  // 进入坐席辅导状态
        CallCenter._calling_from = "caochCalled";
        CallCenter.initControl().setGreenClass().setStatusAndPhoneText(UIT("inThePhone")).hideControl("#CallCenter_mutebut,#CallCenter_unsilence");
        CallCenter.showControl("#CallCenter_silence,#CallCenter_hangupbut,#CallCenter_mutebut");
        CallCenter.toggleMuteIcon();
      },
      // 辅导后转接中
      onenterOut_agentcoach_transfering: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("transfering"));
      },
      // 辅导后转接中
      onenterIn_agentcoach_transfering: function (event, from, to) {
        CallCenter.initControl().setOrgClass().setStatusAndPhoneText(UIT("transfering"));
      },
      /*------------------------------- 坐席辅导静音用户 --------------------------*/
      // 请求静音
      onenterSilence_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("requestSilence"));
        var sendobj = new CallCenter._sendcmd("silence");
        CallCenter.send(sendobj);
      },
      // 静音
      onenterSilence: function (event, from, to) {
        CallCenter.initControl().hideControl("#CallCenter_silence").showControl("#CallCenter_unsilence").setStatusAndPhoneText(UIT("silence"));
      },
      // 请求取消静音
      onenterUnsilence_sending: function (event, from, to) {
        CallCenter.setStatusAndPhoneText(UIT("unsilence"));
        var sendobj = new CallCenter._sendcmd("unsilence");
        CallCenter.send(sendobj);
      },
      // 取消静音
      onenterUnsilence: function (event, from, to) {
        CallCenter.showCallingControl().hideControl("#CallCenter_unsilence").showControl("#CallCenter_silence").setStatusAndPhoneText(UIT("Monitor_tutoring"));
      }
    },
    error: function (eventName, from, to, args, errorCode, errorMessage, e) {
      CallCenter.log("ERROR: eventName:" + eventName + " from:" + from + " to:" + to + " args:" + args + " errorCode:" + errorCode + " errorMessage:" + errorMessage);
      if (errorCode == 300) {
        CallCenter.log(e);
      }
      var state_text = UIT("unknown");
      var from = SDK_state["s_" + from];
      if (from) {
        state_text = from.text;
      }
      var action_text = UIT("unknown");
      var action = SDK_action[eventName];
      if (action) {
        action_text = action.text;
        if (action.type == "handle") {
          action_text += UIT("manipulateion");
        }
      }
      var msg = UIT("currentState") + state_text + "，" + UIT("unenforceable") + action_text;
      CallCenter.eventAlert(msg).log(msg);
      return { result: 0, reason: msg };
    }
  });
})();
