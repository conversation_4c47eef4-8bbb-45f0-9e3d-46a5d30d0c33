<template>
    <rui-page :defines="defines">
        <Row>
            <rui-query :label-width="120"
                       :query="queryData"
                       :query-rows="queryRows"
                       ref="dataQuery"
            />
        </Row>
        <rui-table :defineId="'flowInstance'"
                   :fixedLeft="['id','templateName']"
                   :fixedRight="['action']"
                   :showIndex="false"
                   :select="false"
                   :slots="[{key:'action',slot:'action'}]"
                   @loadDatas="queryData"
                   ref="dataTable"
                   ref-query="dataQuery">
            <template slot="action" slot-scope="{ row }">
                <div>
                    <DynamicLink
                        component="projects/basic/pages/workflow/flow-manage/workflow"
                        :is-full="true"
                        :params="{mode:'history',templateVersion:row.templateVersion,packageId:row.packageId,templateId:row.templateId,processId:row.id}"
                        name="实例轨迹"
                        :page-title="'流程 ' + row.subject + ' 轨迹'"
                        :type-define="{button:true,buttonSize:'small',buttonType:'primary'}"
                    />
                    <Button v-show="row.status!==16" @click="variableModify(row)" type="warning">变量修改</Button>
                    <Button v-show="row.status===512" @click="processFlowError(row)" type="error">处理异常</Button>
                    <Poptip trigger="click" @on-popper-hide="clearVariables" @on-popper-show="variableLoad(row.id,'var')"  width="400" transfer transfer-class-name="afs-header-transfer">
                        <Button  type="primary">变量</Button>
                        <div slot="content">
                            <Table :loading="variableLoading" :max-height="400" :border="true" :columns="[{title: '变量名',key: 'name'},{title: '变量值',key: 'value'}]" :data="variables"></Table>
                        </div>
                    </Poptip>
                    <Poptip v-show="row.status!==16" trigger="click" @on-popper-hide="clearVariables" @on-popper-show="variableLoad(row.id,'task')" placement="right" width="400" transfer transfer-class-name="afs-header-transfer">
                        <Button  type="success">任务</Button>
                        <div slot="content">
                            <Table :loading="taskLoading" :max-height="400" :border="true" :columns="[{title: '任务名称',key: 'taskName'},{title: '用户',key: 'taskOwner'},{title: '中文名',key: 'taskOwnerName'}]" :data="taskList"></Table>
                        </div>
                    </Poptip>
                </div>
            </template>
        </rui-table>
        <Modal
            :closable="false"
            :mask-closable="false"
            :title="exceptionModelTitle"
            :width="850"
            v-model="exceptionModel">
            <rui-table  :defineId="'flowException'"
                       :fixedRight="['action']"
                       :showIndex="false"
                       :select="false"
                       :slots="[{key:'action',slot:'action'}]"
                       ref="exceptionTable">
                <template slot="action" slot-scope="{ row }">
                    <div>
                        <Button v-show="row.status===0" @click="exceptionSubmit(row)" size="small" type="primary">提交解决</Button>
                    </div>
                </template>
            </rui-table>
            <div slot="footer">
                <Button @click="exceptionModel = false" size="small">取消</Button>
            </div>
        </Modal>
        <Modal
            :closable="false"
            :mask-closable="false"
            :title="variableModifyModelTitle"
            :width="850"
            v-model="variableModel">
            <Form :label-width="100" :model="variableForm" :rules="varValidate"
                  ref="variableForm">
                <FormItem label="变量名" prop="name" >
                    <Input placeholder="变量名" type="text" v-model="variableForm.name">
                    </Input>
                </FormItem>

                <FormItem label="变量值" prop="value" >
                    <Input placeholder="变量值" type="text" v-model="variableForm.value">
                    </Input>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button size="small" @click="closeModifyModel">取消</Button>
                <Button size="small" type="primary" @click="submitVarModify">确定</Button>
            </div>
        </Modal>
    </rui-page>
</template>

<script>
import flowInstance from "./define/flow-instance-define";
import flowException from "./define/flow-instance-exception";
import {
    listPageInstance,
    listFlowInstanceException,
    exceptionRetry,
    setFlowVariableByFlowInstance,
    listVariables,
    listFlowTask
} from "@/projects/basic/api/workflow/flow-instanceapi"
export default {
    name: "flow-instance-info",
    components: {
    },
    data() {
        return {
            defines:[],
            queryRows: [
                {
                    defineId: 'flowInstance',
                    fields: ['id','subject']
                }
            ],
            exceptionModel:false,
            variableModel:false,
            exceptionModelTitle:'',
            variableModifyModelTitle:'',
            varValidate: {
                name: [
                    {required: true, message: '不能为空', trigger: 'blur'}
                ],
                value: [
                    {required: true, message: '不能为空', trigger: 'blur'},
                ],
            },
            variableForm:{
                flowInstanceId:'',
                name:'',
                value:''
            },
            variableLoading:true,
            variables:[],
            taskLoading:true,
            taskList:[],
        };
    },
    methods: {
        queryData(data) {
            listPageInstance(data).then(res => {
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    this.$refs.dataTable.updateTableData(records, total);
                }
            });
        },
        processFlowError(row){
            this.exceptionModelTitle = row.subject+'异常列表';
            listFlowInstanceException(row.id).then(res=>{
                if(res.code==='0000'){
                    this.$refs.exceptionTable.updateTableData(res.data,res.data.length)
                    this.$nextTick(()=>{
                        this.exceptionModel = true;
                    })
                }
            })
        },
        exceptionSubmit(row){
            exceptionRetry(row.id).then(res=>{
                this.$Message.info("异常提交成功");
                this.$nextTick(()=>{
                    this.exceptionModel = false;
                    this.$refs.dataTable.reloadData();
                })
            })
        },
        variableModify(row){
            this.variableForm.flowInstanceId = row.id;
            this.variableModifyModelTitle = row.subject+'变量修改';
            this.variableModel = true;
        },
        closeModifyModel(){
            this.variableModel = false;
            this.$nextTick(()=>{
                this.$refs.variableForm.resetFields();
            })
        },
        submitVarModify(){
            this.$refs.variableForm.validate((result)=>{
                if(result){
                    setFlowVariableByFlowInstance(this.variableForm).then((res)=>{
                        if(res.code==='0000'){
                            this.$Message.success("操作成功！");
                            this.closeModifyModel();
                        }
                    })
                }
            })
        },
        variableLoad(flowInstanceId,type='var'){
            if(type==='var') {
                listVariables(flowInstanceId).then((res) => {
                    this.variableLoading = false;
                    if (res.code === '0000') {
                        this.variables.push(...res.data)
                    }
                })
            }else if(type==='task'){
                listFlowTask(flowInstanceId).then((res) => {
                    this.taskLoading = false;
                    if (res.code === '0000') {
                        this.taskList.push(...res.data)
                    }
                })
            }
        },
        clearVariables(){
            this.variables.splice(0,this.variables.length);
            this.taskList.splice(0,this.taskList.length);
            this.$nextTick(()=>{
                this.variableLoading = true;
                this.taskLoading = true;
            })
        }
    },
    computed: {

    },
    created(){
        this.defines.push(
            {
            id: 'flowInstance',
            fields: flowInstance
            },
            {
                id: 'flowException',
                fields: flowException
            }
        )
    },
    mounted() {
        this.$refs.dataTable.reloadData();
    }
};
</script>
