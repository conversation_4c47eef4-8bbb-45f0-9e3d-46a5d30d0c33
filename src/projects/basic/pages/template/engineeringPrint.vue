<style lang="less">
@import "print.less";
</style>
<template>
    <rui-page :defines="defines">
        <Card>
            <Row>
                <Col span="5" style="margin-right:10px;" >
                    <Card>
                        <Row style="margin-bottom: 2vh;">
                            <Button @click="addClass" type="primary" icon="md-add">添加类别</Button>
                            <Dropdown @on-click="handleDropdown">
                                <Button>更多操作
                                    <Icon type="md-arrow-dropdown"/>
                                </Button>
                                <DropdownMenu slot="list">
                                    <DropdownItem name="editClass">编辑类别</DropdownItem>
                                    <DropdownItem name="delClass">删除类别</DropdownItem>
                                    <DropdownItem name="refreshClass">刷新</DropdownItem>
                                </DropdownMenu>
                            </Dropdown>
                        </Row>
                        <Tree :data="data"
                              :render="renderContent"
                              @on-select-change="selectTree"
                              class="tree-bar">
                        </Tree>
                        <Spin fix size="large" v-if="treeLoading"></Spin>
                    </Card>
                </Col>
                <Col span="18">
                    <Card>
                        <Row class="operation">
                            <Button @click="addTemplate" type="primary" icon="md-add">新增模板</Button>
                            <circleLoading v-if="operationLoading"/>
                            <Form ref="searchForm" :model="searchForm" inline :label-width="100" class="search-form">
                                <Form-item label="模版名称" prop="printFormName">
                                    <Input type="text" v-model="searchForm.condition.printFormName" clearable placeholder="请输入模版名称" style="width: 200px" />
                                </Form-item>
                                <Form-item>
                                    <Button @click="handleSearch" type="primary" icon="ios-search">查询</Button>
                                    <Button @click="handleReset" type="primary" icon="ios-trash">重置</Button>
                                </Form-item>
                            </Form>
                        </Row>
                        <Row>
                            <rui-table
                                :defineId="'defineTemplate'"
                                :loading="loading"
                                :fixedLeft="['currency','number']"
                                :fixedRight="['action']"
                                :showIndex="false"
                                :slots="[{key:'action',slot:'action'},{key:'isEnableHandle',slot:'lockFlag'}]"
                                @loadDatas="remoteTableDatas"
                                @on-selection-change="changeSelect"
                                ref="templateTable">

                                <template slot-scope="{ row, index }" slot="lockFlag">
                                    <Button v-if="row.isEnable==='1'" style="margin-right:5px" type="error" size="small"
                                            @click="disable(row)">禁用
                                    </Button>
                                    <Button v-if="row.isEnable==='0'" style="margin-right:5px" type="success" size="small"
                                            @click="enable(row)">启用
                                    </Button>
                                </template>
                                <template slot-scope="{ row, index }" slot="action">
                                    <Dropdown :transfer="true" class="btn-no-border"
                                              transfer-class-name="btn-no-border btn-hover-bg afs-header-transfer">
                                        <Button type="primary" ghost>
                                            更多
                                        </Button>
                                        <ButtonGroup slot="list" size="default" vertical style="min-width: 80px;">
                                            <Button type="primary" size="small" @click="editTemplate(row)">编辑
                                            </Button>
                                            <Button type="success" size="small" @click="view(row)">查看
                                            </Button>
                                            <Button type="info" size="small" @click="preview(row)">预览
                                            </Button>
                                            <Button type="error" size="small" @click="uploadTemplate(row)">维护模板</Button>
                                            <Button type="info" size="small" @click="dataUpdate(row)">同步数据</Button>
                                            <Button type="warning" size="small" @click="copyData(row)">复制</Button>
                                        </ButtonGroup>
                                    </Dropdown>
                                </template>
                            </rui-table>
                        </Row>
                    </Card>
                </Col>
            </Row>

            <!--类别新增或修改-->
            <Modal v-model="classModalVisible"
                   :title="classTitle"
                   :width="600"
                   :mask-closable="false"
                   @on-ok="submitClass">
                <rui-form :form-options="classFormOptions"
                          :read-only="false"
                          ref="classForm">
                </rui-form>
            </Modal>

            <!--模板修改或新增-->
            <Modal v-model="templateModalVisible"
                   :title="templateTitle"
                   :width="1100"
                   :mask-closable="false"
                   @on-ok="submitTemplate"
                   @on-visible-change="visibleTemplate">
                <Tabs v-model="tabName" type="card">
                    <TabPane label="模板参数" name='paramTab'>
                        <rui-form :form-options="templateFormOptions"
                                  :read-only="false"
                                  :after-change="afterChange"
                                  ref="templateForm">
                            <template  slot="slotAttachmentClass">
                                <Cascader :disabled="!isEnable" :data="locationData" transfer filterable
                                          v-model="defualtAttachmentClass" @on-change="handleAttachmentChange"></Cascader>
                            </template>
                            <template  slot="slotAscriptionClass">
                                <Cascader :disabled="!isEnable" :data="ascriptionData" transfer filterable
                                          v-model="defualtAscriptionClass" @on-change="handleAscriptionChange"></Cascader>
                            </template>
                        </rui-form>
                    </TabPane>
                    <TabPane label="模板规则" name='ruleTab'>
                        <rule-tree
                            v-if="dataLoad"
                            @saved="ruleSaved"
                            :rule-data="ruleData"
                            :ruleAtoms="ruleAtoms"
                            :rule-id="ruleId"
                            :ruleName="ruleName"
                            :ruleGroup="ruleGroup"
                            :ruleOut="ruleOut"
                            :businessScenario="businessScenario"
                            :readOnly="ruleReadOnly"
                        />
                    </TabPane>
                    <TabPane label="模板映射" name='mappingTab'>
                        <Row class="operation" v-if="isEnable">
                            <Button @click="addMapping" type="primary" icon="md-add">新增</Button>
                        </Row>
                        <rui-table
                            :defineId="'defineMapping'"
                            :loading="mappingTableloading"
                            :fixedRight="['action']"
                            :showIndex="false"
                            :slots="[{key:'action',slot:'action'},{key:'originalFieldId',slot:'originalFieldId'},{key:'originalFieldName',slot:'originalFieldName'},{key:'mappingFieldId',slot:'mappingFieldId'},{key:'mappingFieldName',slot:'mappingFieldName'},{key:'isRebuild',slot:'isRebuild'},{key:'isReset',slot:'isReset'}]"
                            @loadDatas="loadMappingTableData"
                            ref="mappingTable">
                            <template slot-scope="{ row, index }" slot="action" v-if="isEnable">
                                <Button type="primary" size="small" @click="saveMapping(row)">保存</Button>
                                <Button type="error" size="small" @click="delMapping(row, index)">删除</Button>
                            </template>
                            <template slot-scope="{ row, index }" slot="originalFieldId">
                                <Input type="text" @on-change="(e)=>{updateTableValue(e,'mappingTable',index,'originalFieldId')}" :value="row.originalFieldId" placeholder="原字段名"></Input>
                            </template>
                            <template slot-scope="{ row, index }" slot="originalFieldName">
                                <Input type="text" @on-change="(e)=>{updateTableValue(e,'mappingTable',index,'originalFieldName')}" :value="row.originalFieldName" placeholder="原字段描述"></Input>
                            </template>
                            <template slot-scope="{ row, index }" slot="mappingFieldId">
                                <Input type="text" @on-change="(e)=>{updateTableValue(e,'mappingTable',index,'mappingFieldId')}" :value="row.mappingFieldId" placeholder="映射字段名"></Input>
                            </template>

                            <template slot-scope="{ row, index }" slot="mappingFieldName">
                                <Input type="text" @on-change="(e)=>{updateTableValue(e,'mappingTable',index,'mappingFieldName')}" :value="row.mappingFieldName" placeholder="映射字段描述"></Input>
                            </template>
                            <template slot-scope="{ row, index }" slot="isRebuild">
                                <Select @on-change="(e)=>{updateTableSelectValue(e,'mappingTable',index,'isRebuild')}"
                                        :value="row.isRebuild"
                                        :label="setIsTypeListTitle(row.isRebuild)"
                                        transfer
                                        placeholder="是/否">
                                    <Option v-for="(item,index) in row.isRebuildList" :value="item.value" :key="item.value">{{ item.title }}</Option>
                                </Select>
                            </template>
                            <template slot-scope="{ row, index }" slot="isReset">
                                <Select @on-change="(e)=>{updateTableSelectValue(e,'mappingTable',index,'isReset')}"
                                        :value="row.isReset"
                                        :label="setIsTypeListTitle(row.isReset)"
                                        transfer
                                        placeholder="是/否">
                                    <Option v-for="(item,index) in row.isResetList" :value="item.value" :key="item.value">{{ item.title }}</Option>
                                </Select>
                            </template>
                        </rui-table>
                    </TabPane>
                    <TabPane label="模板印章" name='sealTab'>
                        <Row class="operation" v-if="isEnable">
                            <Button @click="addSeal" type="primary" icon="md-add">新增</Button>
                        </Row>
                        <rui-table
                            :defineId="'defineSeal'"
                            :loading="mappingTableloading"
                            :fixedRight="['action']"
                            :showIndex="false"
                            :slots="[{key:'action',slot:'action'},{key:'locationType',slot:'locationType'}]"
                            @loadDatas="loadSealTableData"
                            ref="sealTable">
                            <template slot-scope="{ row, index }" slot="locationType">
                                {{setLocationTypeTitle(row.locationType)}}
                            </template>
                            <template slot-scope="{ row, index }" slot="action" v-if="isEnable">
                                <Button type="primary" size="small" @click="editSeal(row)">修改</Button>
                                <Button type="error" size="small" @click="delSeal(row)">删除</Button>
                            </template>
                        </rui-table>
                    </TabPane>
                    <TabPane label="模板签名" name='signTab'>
                        <Row class="operation" v-if="isEnable">
                            <Button @click="addSign" type="primary" icon="md-add">新增</Button>
                        </Row>
                        <rui-table
                            :defineId="'defineSign'"
                            :loading="mappingTableloading"
                            :fixedRight="['action']"
                            :showIndex="false"
                            :slots="[{key:'action',slot:'action'},{key:'custRole',slot:'custRole'},{key:'locationType',slot:'locationType'}]"
                            @loadDatas="loadSignTableData"
                            ref="signTable">
                            <template slot-scope="{ row, index }" slot="custRole">
                                <span>{{setcustRoleTitle(row.custRole)}}</span>
                            </template>
                            <template slot-scope="{ row, index }" slot="locationType">
                                {{setLocationTypeTitle(row.locationType)}}
                            </template>
                            <template slot-scope="{ row, index }" slot="action" v-if="isEnable">
                                <Button type="primary" size="small" @click="editSign(row)">修改</Button>
                                <Button type="error" size="small" @click="delSign(row)">删除</Button>
                            </template>
                        </rui-table>
                    </TabPane>
                </Tabs>
                <div slot="footer">
                    <Button type="text" size="small" @click="cancelTemplate">取消</Button>
                    <Button type="primary" size="small" @click="submitTemplate">确定</Button>
                </div>
            </Modal>
            <Modal :width="1100" v-model="showFileUpload" :footer-hide="true" @on-cancel="closeModal">
                <fileOperation :uploadParam="uploadParam" :isInt="canUpload" :path="requestPath" ref="fileOperation"></fileOperation>
            </Modal>
            <Modal v-model="sealModalVisible" :title="sealTitle" :closable="true" :mask-closable='true' :width="600" :styles="{top: '180px'}">
                <Row>
                    <Form ref="sealModalSearchForm" :model="sealModalSearchForm" inline :label-width="100" class="search-form" :rules="sealModalRules">
                        <Col>
                            <Form-item label="印章名称" prop="sealNo">
                                <Select v-model="sealModalSearchForm.sealNo" clearable placeholder="请选择" style="width:200px">
                                    <Option v-for="item in sealModalData" :label="item.sealName" :value="item.sealNo" :key="item.attachmentName">{{item.sealName}}</Option>
                                </Select>
                            </Form-item>
                        </Col>
                        <Col>
                            <FormItem label="定位模式" prop="locationType">
                                <RadioGroup v-model="sealModalSearchForm.locationType" >
                                    <Radio :false-value="''"  :key="radio.id" :label="radio.value" :true-value="radio.value"  v-for="(radio) in locationTypeList">{{radio.title}} </Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                        <div v-if="this.sealModalSearchForm.locationType==='01'">
                            <Col>
                                <Form-item label="位置坐标X轴" prop="coordinateX">
                                    <Input type="number" v-model="sealModalSearchForm.coordinateX" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                            <Col>
                                <Form-item label="位置坐标Y轴" prop="coordinateY">
                                    <Input type="number" v-model="sealModalSearchForm.coordinateY" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                        </div>
                        <div v-if="this.sealModalSearchForm.locationType==='02'">
                            <Col>
                                <Form-item label="关键字" prop="keyword">
                                    <Input type="text" v-model="sealModalSearchForm.keyword" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                        </div>
                    </Form>
                </Row>
                <div slot="footer">
                    <Button size="small" @click="backSealList">取消</Button>
                    <Button size="small" @click="saveSeal">保存</Button>
                </div>
            </Modal>
            <Modal v-model="signModalVisible" :title="signTitle" :closable="true" :mask-closable='true' :width="1000" :styles="{top: '180px'}">
                <Row>
                    <Form ref="signModalSearchForm" :model="signModalSearchForm" inline :label-width="100" class="search-form" :rules="signModalRules">
                        <Col>
                            <Form-item label="案件角色" prop="custRole">
                                <Select v-model="signModalSearchForm.custRole" clearable placeholder="请选择" style="width:200px" >
                                    <Option v-for="item in custRoleList" :label="item.title" :value="item.value" :key="item.value">{{item.title}}</Option>
                                </Select>
                            </Form-item>
                        </Col>
                        <Col>
                            <FormItem label="定位模式" prop="locationType">
                                <RadioGroup v-model="signModalSearchForm.locationType">
                                    <Radio :false-value="''"  :key="radio.id" :label="radio.value" :true-value="radio.value"  v-for="(radio) in locationTypeList">{{radio.title}} </Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                        <div v-if="this.signModalSearchForm.locationType=='01'">
                            <Col>
                                <Form-item label="位置坐标X轴" prop="coordinateX">
                                    <Input type="number" v-model="signModalSearchForm.coordinateX" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                            <Col>
                                <Form-item label="位置坐标Y轴" prop="coordinateY">
                                    <Input type="number"  v-model="signModalSearchForm.coordinateY" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                        </div>
                        <div v-if="this.signModalSearchForm.locationType=='02'">
                            <Col>
                                <Form-item label="关键字" prop="keyword">
                                    <Input type="text" v-model="signModalSearchForm.keyword" placeholder="请输入" style="width: 200px"/>
                                </Form-item>
                            </Col>
                        </div>
                    </Form>
                </Row>
                <div slot="footer">
                    <Button size="small" @click="backSignList">取消</Button>
                    <Button size="small" @click="saveSign">保存</Button>
                </div>
            </Modal>
            <Modal v-model="classCopyModalVisible" :title="classCopyTitle" :width="380" @on-ok="copyTemplate" @on-cancel="cancelCopy">
                <div>
                    该复制功能无法复制规则和word模版文件，请复制完成单独配置。
                </div>
                <Row>
                    <Form ref="copyForm" :model="copyForm" inline :label-width="100">
                        <Col>
                            <Form-item label="类别">
                                <Select v-model="copyForm.classId" style="width:200px" >
                                    <Option v-for="item in classData" :label="item.className" :value="item.classId" :key="item.classId">{{item.className}}</Option>
                                </Select>
                            </Form-item>
                        </Col>
                    </Form>
                </Row>
            </Modal>
        </Card>
        <Modal  width="60%" :title="previewName" :scrollable="true" fullscreen transfer v-model="previewMode" :footer-hide="true" >
            <div style="padding: 0px">
                <pdfJs v-if="previewMode" :requestPath="pdfViewRequestPath" request-param=""></pdfJs>
            </div>
        </Modal>
    </rui-page>
</template>
<script>
import { getByTypes } from '_p/basic/api/admin/datadic'
import fileOperation from "@/projects/basic/pages/image/upload-file/fileOperation.vue";
import defineTemplate from "./defines/template-define";
import defineClass from "./defines/class-define";
import defineMapping from "./defines/mapping-define";
import defineSeal from "./defines/seal-define";
import defineSign from "./defines/sign-define";
import ruiRules from '_c/rui-auto/rui-rules/index';
import {queryAtomsByBizType} from '_p/basic/api/rule/ruleAtom';
import {getComAttachmentCascader}from "_p/basic/api/image/image-manage";
import {
    addClass,// 新增类别
    editClass,// 编辑类别
    delClass,// 删除类别，类别下已有模板则不允许删除
    addTemplate,// 新增打印模板
    editTemplate,// 编辑打印模板
    enableTemplate,// 启用模板
    disableTemplate,// 禁用模板
    getAllTemplateList,// 默认查询全部
    queryTemplateList,// 查询模板列表，焦点在类别上则查询所有类别下的全部模板
    loadTemplateRuleTreeById,
    deActiveTemplateRuleById,
    queryMappingList,// 查询模板下的模板字段映射
    querySealList,
    querySealMappingList,
    addMapping,// 增加映射
    editMapping,// 编辑映射
    delMapping,// 删除映射
    syncData,//同步数据
    addSeal,//增加印章映射
    editSeal,//修改印章映射
    delSeal,//删除印章映射
    querySignMappingList,
    addSign,//增加签名
    editSign,//修改签名
    delSign,//删除签名
    getAllClass,//获取所有类别
    copyTemplate,//复制模版
    copyPrintData,//复制模版数据
    previewPdfPath
} from '@/projects/basic/api/template/template';
import pdfJs from '@/components/pdf/pdf-js'
import getUuid from "@/libs/afs-uuid";
export default {
    components: {
        ruleTree:ruiRules,
        fileOperation,
        pdfJs
    },
    data () {
        return {
            loading:false,// 表格状态
            treeLoading: false, // 树加载状态
            mappingTableloading:false,
            operationLoading: false, // 操作加载状态
            showFileUpload:false,
            sealModalVisible:false,
            signModalVisible:false,
            canUpload:false,
            ruleReadOnly:false,
            requestPath:"",
            activateTitle:'',
            searchKey: "", // 搜索树
            classTitle:"",// 类别标题
            classModalVisible:false,// 控制类别弹窗显示
            classCopyModalVisible:false,
            classModalType: null,// 类别编辑状态 0 新增 1 修改
            templateTitle:"",// 模板标题
            classCopyTitle:"请选择复制到以下类别",
            sealTitle:"",
            signTitle:"",
            dicKeys: [
                'custRole',
                'locationType',
                'isType'
            ],
            sealModalData:[],
            signModalData:[],
            custRoleList:[],
            isTypeList:[],
            templateId:"",
            locationTypeList:[],
            templateModalVisible:false,// 控制模板标题
            templateFormModalType:null,// 模板编辑状态 0 新增 1 修改
            printFormId:'',// 模板ID
            versionId:'',//模版版本号
            tabName:'paramTab',// 模板详情窗口默认tab
            isEnable:'true',
            classData:[],
            uploadParam:{
                busiNo:"",
                belongNo:"",
                busiType:"",
                busiData:{}},
            //--------规则相关----------
            ruleAtoms:[],// 模板原子集合，需要各子系统自行配置
            ruleName:'',// 规则名称
            ruleGroup:'',// 规则组，打印模板规则默认且不允许修改
            businessScenario:'',// 规则编号ruleNo，这里用的模板ID作为规则编号
            ruleOut:'',// 规则通过后返回，这里设置成模板ID
            ruleData:[],// 规则树数据
            ruleId:'',// 规则ID
            dataLoad:false,// 是否加载
            bizType:'templatePrint',// 业务场景
            //--------规则相关----------
            selectNode:{},// 选中的类别树
            defines:[
                {
                    id: 'defineTemplate',// 模板表头
                    fields: defineTemplate
                },
                {
                    id: 'defineClass',// 类别表头
                    fields: defineClass
                },
                {
                    id: 'defineMapping',// 模板映射表头
                    fields: defineMapping
                },
                {
                    id: 'defineSeal',// 模板印章表头
                    fields: defineSeal
                },
                {
                    id: 'defineSign',// 模板签名表头
                    fields: defineSign
                }
            ],
            data: [// 类别树
                {
                    "title": "多品核心模板管理",
                    "expand": "true",
                    "children":[]
                }
            ],
            locationData: [],// 附件类别级联
            ascriptionData: [],//归属文件类别级联
            defualtAttachmentClass:[],// 附件类别初始值
            defualtAscriptionClass:[],// 文件签约归属文档初始值
            classFormOptions:[// 类别编辑form字段
                {
                    grids:[
                        {defineId: "defineClass", fields: ["classId", "className","id"]}
                    ],
                    hideKey: ['id']// 隐藏主键
                }
            ],
            templateFormOptions:[// 模板编辑form
                {
                    grids:[
                        {defineId: "defineTemplate", span:36, fields: ["printFormId", "effectTime", "formTpye"]},
                        {defineId: "defineTemplate", span:36, fields: ["versionId","failureTime","printNode"]},
                        {defineId: "defineTemplate", span:36, fields: ["printFormName","ascriptionSubClass","isElectronic","id"],
                            slots: [{key:'ascriptionSubClass',slotName: 'slotAscriptionClass'}]},
                        {defineId: "defineTemplate", span:13, fields: ["attachmentSubClass"],
                            slots: [{key: 'attachmentSubClass', slotName: 'slotAttachmentClass'}]},
                    ],
                    hideKey: ['id']
                }
            ],
            sealModalSearchForm:{
                coordinateX:null,
                coordinateY:null
            },
            signModalSearchForm:{
                coordinateX:null,
                coordinateY:null
            },
            searchForm: {
                // 搜索框对应data对象
                condition:{classNo: "",templateId: "",printFormName:""},
                pageNumber: 1, // 当前页数
                pageSize: 10, // 页面大小
                sort: "sortOrder", // 默认排序字段
                order: "asc", // 默认排序方式
            },
            copyForm:{
                classId:"",
            },
            searchMappingForm: {
                // 搜索框对应data对象
                condition: {printFormId: "",templateId:""},
                pageNumber: 1, // 当前页数
                pageSize: 10, // 页面大小
                sort: "sortOrder", // 默认排序字段
                order: "asc" // 默认排序方式
            },
            searchSealForm: {
                // 搜索框对应data对象
                condition: {printFormId: "",versionId:"",templateId:""},
                pageNumber: 1, // 当前页数
                pageSize: 10, // 页面大小
                sort: "sortOrder", // 默认排序字段
                order: "asc" // 默认排序方式
            },
            searchSignForm: {
                // 搜索框对应data对象
                condition: {printFormId: "",versionId:"",templateId:""},
                pageNumber: 1, // 当前页数
                pageSize: 10, // 页面大小
                sort: "sortOrder", // 默认排序字段
                order: "asc" // 默认排序方式
            },
            //规则
            sealModalRules:{
                custRole: [{ required: true, message: "案件角色不能为空", trigger: "change" }],
                sealNo: [{ required: true, message: "印章名称不能为空", trigger: "change" }],
                locationType: [{ required: true, message: '请选择', trigger: 'change' }],
                keyword: [{ required: true, message: '关键字不能为空', trigger: 'blur' }],
                coordinateX:[
                    {required: true, message: '坐标不能为空',trigger: 'blur',type:'number'},
                ],
                coordinateY:[
                    {required: true, message: '坐标不能为空',trigger: 'blur',type:'number'},
                ],
            },
            signModalRules:{
                custRole: [{ required: true, message: "案件角色不能为空", trigger: "change" }],
                sealNo: [{ required: true, message: "印章名称不能为空", trigger: "change" }],
                locationType: [{ required: true, message: '请选择', trigger: 'change' }],
                keyword: [{ required: true, message: '关键字不能为空', trigger: 'blur' }],
                coordinateX:[
                    {required: true, message: '坐标不能为空',trigger: 'blur',type:'number'},
                ],
                coordinateY:[
                    {required: true, message: '坐标不能为空',trigger: 'blur',type:'number'},
                ],
            },
            previewMode:false,
            previewName:'',
            pdfViewRequestPath:''
        }
    },
    methods: {
        init(dataFromFirstIndex=true) {
            this.getAllList();// 查询所有类别初始化成类别树
            this.getAllClass();
            if(dataFromFirstIndex) {
                this.remoteTableDatas(this.searchForm.pageNumber, this.searchForm.pageSize);// 初始化模板列表表格
            }else {
                this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize)
            }
            this.initAttachmentCascader();// 初始化附件类别级联
            this.initDataDic();//获取数据字典
        },

        initDataDic() {
            let self = this
            getByTypes(this.dicKeys).then(res => {
                if (res.code === '0000' && res.data) {
                    self.dataDic = res.data
                    this.custRoleList.push(...res.data.custRole)
                    this.locationTypeList.push(...res.data.locationType)
                    this.isTypeList.push(...res.data.isType)
                }
            })
        },
        setIsTypeListTitle(val) {
            let dic = {}
            this.isTypeList.forEach(colunm => {
                if (colunm.value === val) {
                    dic = colunm
                }
            })
            return dic.title
        },
        setcustRoleTitle(val) {
            let dic = {}
            this.custRoleList.forEach(colunm => {
                if (colunm.value === val) {
                    dic = colunm
                }
            })
            return dic.title
        },
        setLocationTypeTitle(val) {
            let dic = {}
            this.locationTypeList.forEach(colunm => {
                if (colunm.value === val) {
                    dic = colunm
                }
            })
            return dic.title
        },
        remoteTableDatas(pageNumber,pageSize) {
            this.loading = true;
            this.searchForm.pageNumber = pageNumber;
            this.searchForm.pageSize = pageSize;
            queryTemplateList(this.searchForm, this.requestPath).then(res => {
                this.loading = false;
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    this.$refs.templateTable.updateTableData(records, total);
                    this.$refs.templateTable.resetPageInfo(this.searchForm.pageNumber,this.searchForm.pageSize);
                }
            });
        },
        initAttachmentCascader() {
            getComAttachmentCascader("template", this.requestPath).then(res => {
                if (res.code === "0000") {
                    this.locationData = res.data;
                }
            });
            getComAttachmentCascader("", this.requestPath).then(res => {
                if (res.code === "0000") {
                    this.ascriptionData = res.data;
                }
            });
        },
        loadMappingTableData(pageNumber, pageSize) {
            this.mappingTableloading = true;
            this.searchMappingForm.pageNumber = pageNumber;
            this.searchMappingForm.pageSize = pageSize;
            queryMappingList(this.searchMappingForm,this.requestPath).then(res => {
                this.mappingTableloading = false;
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    records.forEach(r=>{
                        r.isResetList=this.isTypeList;
                        r.isRebuildList=this.isTypeList;
                    })
                    this.$refs.mappingTable.updateTableData(records, total);
                    this.$refs.mappingTable.resetPageInfo(pageNumber,pageSize);
                }
            });
        },
        loadSealTableData(pageNumber, pageSize) {
            this.mappingTableloading = true;
            this.searchSealForm.pageNumber = pageNumber;
            this.searchSealForm.pageSize = pageSize;
            querySealMappingList(this.searchSealForm, this.requestPath).then(res => {
                this.mappingTableloading = false;
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    this.$refs.sealTable.updateTableData(records, total);
                }
            });
        },
        loadSignTableData(pageNumber, pageSize) {
            this.mappingTableloading = true;
            this.searchSignForm.pageNumber = pageNumber;
            this.searchSignForm.pageSize = pageSize;
            querySignMappingList(this.searchSignForm, this.requestPath).then(res => {
                this.mappingTableloading = false;
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    this.$refs.signTable.updateTableData(records, total);
                }
            });
        },
        loadRuleInfo() {
            this.ruleData = [];
            this.dataLoad = false;
            if(this.$refs.templateForm.getFormData().ruleNo!=null&&this.$refs.templateForm.getFormData().ruleNo!=undefined){
                this.ruleId=this.$refs.templateForm.getFormData().ruleNo;
            }
            console.log("编辑的ruleNo:"+this.$refs.templateForm.getFormData().ruleNo);
            loadTemplateRuleTreeById(this.ruleId,this.requestPath).then(res => {
                if (res.code === '0000') {
                    if (res.data != '' && res.data != null && res.data != undefined) {
                        if (res.data.data != '' && res.data.data != null && res.data.data != undefined) {
                            this.ruleData.push(...res.data.data);
                            this.ruleId = res.msg;
                        }else {
                            this.ruleId="";
                        }
                    }
                    this.dataLoad = true;
                }
            });
        },
        queryRuleAtomInfo() { // 查询原子
            queryAtomsByBizType(this.bizType).then(res => {
                if (res.code == "0000") {
                    this.ruleAtoms = res.data;
                }
            })
        },
        ruleSaved(ruleId) {
            console.log("保存规则之后回传ruleId:"+ruleId);
            let oldRuleId="";
            if(this.$refs.templateForm.getFormData().ruleNo!=null&&this.$refs.templateForm.getFormData().ruleNo!=undefined){
                oldRuleId=this.$refs.templateForm.getFormData().ruleNo;
            }
            if(ruleId!=oldRuleId&&oldRuleId!=""){
                //如果两个值不一样先失效旧规则
                deActiveTemplateRuleById(oldRuleId,this.requestPath).then(res=>{
                    this.loading = false;
                    if (res.code === "0000") {
                        console.log("保存成功");
                    }
                });
            }
            this.$refs.templateForm.setFiledValue('ruleNo', ruleId);
            editTemplate(this.$refs.templateForm.getFormData(),this.requestPath).then(res => {
                this.loading = false;
                if (res.code === "0000") {
                    console.log("保存成功");
                }
            });
            console.log("保存成功之后的ruleNo:"+this.$refs.templateForm.getFormData().ruleNo);
        },
        afterChange(k, v) {
            // 如果是电子签，电子签编号必填
            if (k == 'isElectronic') {
                this.$refs.templateForm.setFieldRequired('electronicNo', v == 1);
                if (v == 0) {
                    this.$refs.templateForm.setFiledValue('electronicNo', '');
                }
            }
        },
        changeSelect(e) {
        },
        getAllList() {
            this.treeLoading = true;
            getAllTemplateList(this.requestPath).then(res => {
                this.treeLoading = false;
                if (res.code === "0000") {
                    if(res.data.length>0){
                        const firstData=res.data[0];
                        if(firstData&&firstData!=undefined){
                            const children=firstData.children;
                            if(children!==undefined&&children.length>0){
                                res.data[0].children[0].expand = true;
                            }
                            this.data[0].children = res.data;
                        }
                    }
                }
            });
        },
        selectTree(v) {
            if (v.length > 0) {
                console.log(v);
                // 转换null为""
                for (let attr in v[0]) {
                    if (v[0][attr] === null) {
                        v[0][attr] = "";
                    }
                }
                let str = JSON.stringify(v[0]);
                let data = JSON.parse(str);
                this.selectNode = data;
                // 编辑框所需数据
                this.$refs.classForm.setFiledValue('id', this.selectNode.classId);
                this.$refs.classForm.setFiledValue('classId', this.selectNode.classNo);
                this.$refs.classForm.setFiledValue('className', this.selectNode.title);
                // 查询类别下的模板
                this.searchForm.condition.classNo = this.selectNode.classNo;
                this.searchForm.condition.templateId = this.selectNode.templateId;
                this.searchForm.pageNumber = 1;
                this.searchForm.pageSize = 10;
                this.remoteTableDatas(1,10);
            }
        },
        handleAttachmentChange(value, selectedData) { // 大类小类选择
            this.$refs.templateForm.setFiledValue('attachmentClass', value[0]);
            this.$refs.templateForm.setFiledValue('attachmentSubClass', value[1]);
        },
        handleAscriptionChange(value, selectedData) { // 大类小类选择
            this.$refs.templateForm.setFiledValue('ascriptionClass', value[0]);
            this.$refs.templateForm.setFiledValue('ascriptionSubClass', value[1]);
        },
        handleDropdown(name) {
            if (name == "editClass") {
                if (!this.selectNode.classId) {
                    this.$Message.warning("您还未选择要编辑的类别");
                    return;
                }
                this.editClass();
            } else if (name == "delClass") {
                this.delClass();
            } else if (name == "refreshClass") {
                this.init();
            }
        },
        addClass() {
            this.$refs.classForm.resetFrom();
            this.classModalType = 0;
            this.classTitle = "类别新增";
            this.$refs.classForm.setAllFieldRequired(true);
            this.classModalVisible = true;
        },
        editClass() {
            this.classModalType = 1;
            this.classTitle = "类别编辑";
            this.$refs.classForm.setAllFieldRequired(true);
            this.classModalVisible = true;
        },
        delClass() {
            if (!this.selectNode.classId) {
                this.$Message.warning("只能删除模板类别");
                return;
            }

            this.$Modal.confirm({
                title: "确认删除",
                loading: true,
                content: "您确认要删除类别 " + this.selectNode.title + " ?",
                onOk: () => {
                    // 删除
                    delClass(this.selectNode.classNo,'',this.requestPath).then(res => {
                        this.$Modal.remove();
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.init();
                        }
                    });
                }
            });
        },
        addTemplate() {
            if (!this.selectNode.classNo) {
                this.$Message.warning("请在对应的类别下新增模板");
                return;
            }
            this.$refs.templateForm.resetFrom();
            this.$refs.templateForm.setFiledValue("attachmentSubClass","");
            this.$refs.templateForm.setFiledValue("ascriptionSubClass","");
            this.$refs.templateForm.setFiledValue('classNo', this.selectNode.classNo);
            this.templateFormModalType = 0;
            this.templateTitle = "模板新增";
            this.templateModalVisible = true;
            this.ruleData = [];
            this.dataLoad = true;
            this.loadMappingTableData(1,10);
            this.isEnable = true;
        },
        editTemplate(v) {
            this.templateFormModalType = 1;
            this.templateTitle = v.printFormName + "->版本号【" + v.versionId + "】模板编辑";
            this.setTemplateFormData(v);
            this.$refs.templateForm.setAllFieldReadOnly(false);
            this.templateModalVisible = true;
            this.isEnable = true;
            if(v.isEnable==='1'){
                this.ruleReadOnly=true;
            }else {
                this.ruleReadOnly=false;
            }

        },
        view(v) {
            this.setTemplateFormData(v);
            this.$refs.templateForm.setAllFieldReadOnly(true);
            this.$refs.templateForm.setFieldReadOnly('attachmentSubClass', false);
            this.templateTitle = "模板查看";
            this.templateFormModalType = 2;
            this.templateModalVisible = true;
            this.isEnable = false;
        },
        preview(v) {
            //modal 打开代码
            // this.pdfViewRequestPath=previewPdfPath(v.id);
            // this.previewName = `模板${v.printFormName}预览`;
            // this.previewMode = true;

            // 浏览器新tab打开代码
            const token = this.getToken();
            console.log(token)
            let _afsDynamicLinkKey = md5(this.$route.name + this.$store.state.app.sessionKey)
            let data = {
                token: token,
                sessionKey:this.$store.state.app.sessionKey,
                pageData: {
                    params: Object.assign({_afsDynamicLinkKey:'key_'+getUuid()},{requestPath:previewPdfPath(v.id),requestParam:''}),
                    component: 'components/pdf/pdf-js',
                    isFull: true,
                    pageTitle: `模板${v.printFormName}预览`,
                    eventHashKey: _afsDynamicLinkKey
                }
            };
            console.log("aaa2"+Base64.encode(JSON.stringify(data), true));

            window.open(this.$router.resolve('/d/afsLink').href + '?_link=' + Base64.encode(JSON.stringify(data), true),'_blank');

        },
        copy(v) {
            this.setTemplateFormData(v);
            this.$refs.templateForm.setFiledValue('id', '');
            this.templateFormModalType = 0;
            this.templateTitle = "模板复制";
            this.templateModalVisible = true;
            this.isEnable = true;
        },
        uploadTemplate(v) { // 模板上传
            this.uploadParam.busiNo = v.id;
            this.uploadParam.belongNo = v.id;
            this.uploadParam.attachmentId = v.attachmentSubClass;
            this.uploadParam.busiType = "template";
            this.canUpload = true;
            this.showFileUpload = true;
        },
        addMapping() {
            let obj = {
                "id": '', "printFormId": this.printFormId, "originalFieldId": '',
                "originalFieldName": '', "mappingFieldId": '', 'mappingFieldName': '',
                "isRebuild":'no',"isReset":'no',"isRebuildList":this.isTypeList,"isResetList":this.isTypeList,
            };
            this.$refs.mappingTable.tableDatas.push(obj);
        },
        addSeal() {
            this.sealModalSearchForm.printFormId = this.printFormId;
            this.sealModalSearchForm.versionId = this.versionId;
            console.info("templateId：" + this.$refs.templateForm.getFormData().id);
            this.sealModalSearchForm.templateId = this.$refs.templateForm.getFormData().id;
            this.sealTitle = "新增印章";
            querySealList(this.requestPath).then(res => {
                if (res.code === '0000') {
                    this.sealModalData = res.data;
                }
            })
            this.sealModalVisible = true;
        },
        editSeal(val) {
            this.sealModalSearchForm = val;
            console.info("-------" + this.sealModalSearchForm);
            this.sealModalSearchForm.printFormId = this.printFormId;
            this.sealModalSearchForm.versionId = this.versionId;
            this.sealTitle = "修改印章";
            querySealList(this.requestPath).then(res => {
                if (res.code === '0000') {
                    this.sealModalData = res.data;
                }
            })
            this.sealModalVisible = true;
        },
        editSign(val) {
            this.signModalSearchForm = val;
            this.signModalSearchForm.printFormId = this.printFormId;
            this.signModalSearchForm.versionId = this.versionId;
            this.signTitle = "修改签名";
            this.signModalVisible = true;
        },
        delSeal(val) {
            this.sealModalSearchForm.id = val.id;
            delSeal(this.sealModalSearchForm,this.requestPath).then(res => {
                if (res.code === '0000') {
                    this.$Message.success("删除成功");
                    this.loadSealTableData(1, 10);
                }

            })
        },
        addSign() {
            this.signModalSearchForm.printFormId = this.printFormId;
            this.signModalSearchForm.versionId = this.versionId;
            console.info("templateId：" + this.$refs.templateForm.getFormData().id);
            this.signModalSearchForm.templateId = this.$refs.templateForm.getFormData().id;
            this.signTitle = "新增签名";
            this.signModalVisible = true;
        },
        delSign(val) {
            this.signModalSearchForm.id = val.id;
            delSign(this.signModalSearchForm, this.requestPath).then(res => {
                if (res.code === '0000') {
                    this.$Message.success("删除成功");
                    this.loadSignTableData(1, 10);
                }

            })
        },
        submitClass() {
            this.$refs.classForm.getForm().validate(valid => {
                if (valid) {
                    this.treeLoading = true;
                    if (this.classModalType === 0) {
                        // 添加 避免编辑后传入id等数据 记得删除
                        this.$refs.classForm.setFiledValue('id', '');
                        addClass(this.$refs.classForm.getFormData(),this.requestPath).then(res => {
                            this.treeLoading = false;
                            if (res.code === "0000") {
                                this.$Message.success("操作成功");
                                this.init();
                                this.classModalVisible = false;
                            }
                        });
                    } else if (this.classModalType === 1) {
                        // 编辑
                        editClass(this.$refs.classForm.getFormData(),this.requestPath).then(res => {
                            this.treeLoading = false;
                            if (res.code === "0000") {
                                this.$Message.success("操作成功");
                                //boolean 变量用来标记是否从第一页加载table
                                this.init();
                                this.classModalVisible = false;
                            }
                        });
                    }
                }
            });
        },
        submitTemplate() {
            if (this.tabName != 'paramTab') {
                this.ruleId="";
                this.templateId="";
                this.printFormId='';
                this.templateModalVisible = false;
                this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize);
                return;
            }
            this.$refs.templateForm.getForm().validate(valid => {
                if (valid) {
                    this.loading = true;
                    if (this.templateFormModalType === 0) {
                        // 添加 避免编辑后传入id等数据 记得删除
                        this.$refs.templateForm.setFiledValue('id', '');
                        addTemplate(this.$refs.templateForm.getFormData(),this.requestPath).then(res => {
                            this.loading = false;
                            if (res.code === "0000") {
                                this.$Message.success("操作成功");
                                this.init();
                                this.templateModalVisible = false;
                                this.ruleId="";
                                this.templateId="";
                                this.printFormId='';
                                this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize);
                            }
                        });
                    } else if (this.templateFormModalType === 1) {
                        // 编辑
                        editTemplate(this.$refs.templateForm.getFormData(),this.requestPath).then(res => {
                            this.loading = false;
                            if (res.code === "0000") {
                                this.$Message.success("操作成功");
                                this.init(false);
                                this.templateModalVisible = false;
                                this.ruleId="";
                                this.templateId="";
                                this.printFormId='';
                                this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize);
                            }
                        });
                    } else {
                        this.templateModalVisible = false;
                        this.ruleId="";
                        this.templateId="";
                        this.printFormId='';
                    }
                } else {
                    return false;
                }
            });
        },
        saveMapping(v) {
            this.mappingTableloading = true;
            if (this.$refs.templateForm.getFormData().id != null || this.$refs.templateForm.getFormData().id != "") {
                v.templateId = this.$refs.templateForm.getFormData().id;
                console.info("v.templateId：" + v.templateId);
                if (!v.id) {
                    addMapping(v,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            v.id = res.data;
                        }
                    });
                } else {
                    editMapping(v,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                        }
                    });
                }
            } else {
                this.$Message.fail("请先保存模板参数数据！");
            }

        },
        saveSeal() {
            if (this.sealModalSearchForm) {
                this.mappingTableloading = true;
                if (!this.sealModalSearchForm.id) {
                    addSeal(this.sealModalSearchForm,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.sealModalSearchForm = {}
                            this.sealModalVisible = false;
                            this.sealModalData=[];
                            this.loadSealTableData(1, 10);
                        }
                    });
                } else {
                    editSeal(this.sealModalSearchForm,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.sealModalSearchForm = {}
                            this.sealModalData=[];
                            this.sealModalVisible = false;
                            this.loadSealTableData(1, 10);
                        }
                    });
                }
            }
        },
        saveSign() {
            if (this.sealModalSearchForm) {
                this.mappingTableloading = true;
                if (!this.signModalSearchForm.id) {
                    addSign(this.signModalSearchForm,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.signModalSearchForm = {}
                            this.signModalVisible = false;
                            this.signModalData=[];
                            this.loadSignTableData(1, 10);
                        }
                    });
                } else {
                    editSign(this.signModalSearchForm,this.requestPath).then(res => {
                        this.mappingTableloading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.signModalSearchForm = {}
                            this.signModalVisible = false;
                            this.signModalData=[];
                            this.loadSignTableData(1, 10);
                        }
                    });
                }
            }
        },
        delMapping(v, index) {
            this.$Modal.confirm({
                title: "确认删除",
                loading: true,
                content: "您确认要删除映射 " + v.mappingFieldId + '[' + v.mappingFieldName + ']' + " ?",
                onOk: () => {
                    // 删除
                    if (v.id) {
                        let mappingId = v.id;
                        delMapping(mappingId,this.requestPath).then(res => {
                            if (res.code === "0000") {
                                this.$Message.success("操作成功");
                            }
                            this.$refs.mappingTable.getTable().data.splice(index, 1);
                        });
                    } else {
                        this.$refs.mappingTable.getTable().data.splice(index, 1);
                    }
                    this.$Modal.remove();
                }
            });
        },
        cancelTemplate() {
            this.ruleId="";
            this.templateId="";
            this.printFormId='';
            this.templateModalVisible = false;
        },
        visibleTemplate(v) {
            if (!v) {
                this.tabName = 'paramTab';
                this.businessScenario = '';
                this.ruleOut = '';
                this.printFormId = '';
                this.searchMappingForm.condition.printFormId = '';
            }
        },
        setTemplateFormData(v) {
            // 转换null为""
            for (let attr in v) {
                if (v[attr] === null) {
                    v[attr] = "";
                }
            }
            let str = JSON.stringify(v);
            let templateInfo = JSON.parse(str);
            this.$refs.templateForm.updateFormData(templateInfo);
            // 大小类默认值
            if (v.attachmentClass && v.attachmentSubClass) {
                this.defualtAttachmentClass = [v.attachmentClass, v.attachmentSubClass];
            } else {
                this.defualtAttachmentClass = [];
            }
            // 大小类默认值
            if (v.ascriptionClass && v.ascriptionSubClass) {
                this.defualtAscriptionClass = [v.ascriptionClass, v.ascriptionSubClass];
            } else {
                this.defualtAscriptionClass = [];
            }
            // 映射列表
            this.searchMappingForm.condition.templateId = v.id;
            this.searchSealForm.condition.templateId = v.id;
            this.searchSignForm.condition.templateId = v.id;
            this.printFormId = v.printFormId;
            this.versionId = v.versionId;
            // 规则参数
            this.businessScenario = v.id;
            this.ruleOut = v.id;
            this.ruleName = v.printFormName + '规则';
            this.ruleGroup = v.printNode;
            this.loadRuleInfo();
            this.loadMappingTableData(1,10);
            this.loadSealTableData(1, 10);
            this.loadSignTableData(1, 10);
        },
        disable(v) {
            this.$Modal.confirm({
                title: "确认禁用",
                content: "您确认要禁用模板 " + v.printFormName + " ?",
                onOk: () => {
                    this.operationLoading = true;
                    let id = v.id;
                    // 编辑
                    disableTemplate(id,this.requestPath).then(res => {
                        this.treeLoading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize);
                            this.templateModalVisible = false;
                        }
                    });
                }
            });
        },
        enable(v) {
            this.$Modal.confirm({
                title: "确认启用",
                content: "您确认要启用模板 " + v.printFormName + " ?",
                onOk: () => {
                    this.operationLoading = true;
                    let id = v.id;
                    // 编辑
                    enableTemplate(id,this.requestPath).then(res => {
                        this.treeLoading = false;
                        if (res.code === "0000") {
                            this.$Message.success("操作成功");
                            this.remoteTableDatas(this.searchForm.pageNumber,this.searchForm.pageSize);
                            this.templateModalVisible = false;
                        }
                    });
                }
            });
        },
        renderContent(h, {root, node, data}) {
            return h('span', [
                h('Icon', {
                    props: {
                        type: 'md-folder'
                    },
                    style: {
                        marginRight: '8px'
                    }
                }),
                h('span', data.title)
            ])
            // h('span', {
            //     style: {
            //         display: 'inline-block',
            //         float: 'right',
            //         marginRight: '32px'
            //     }
            // })
            // );
        },

        //数据同步
        dataUpdate(row) {
            syncData(row, this.requestPath).then(res => {
                if (res.code === "0000") {
                    this.$Message.success("操作成功");
                    this.getList();
                }
            })
        },
        backSealList() {
            this.sealModalVisible = false;
            this.sealModalSearchForm = {}
            this.loadSealTableData(1, 10);
            this.sealModalData=[];
        },
        backSignList() {
            this.signModalVisible = false;
            this.signModalSearchForm = {}
            this.signModalData=[];
            this.loadSignTableData(1, 10);
        },
        updateTableValue(event,refName,index,attrKey){
            (this.$refs[refName].tableDatas[index])[attrKey] = event.target.value;
        },
        updateTableSelectValue(event,refName,index,attrKey){
            (this.$refs[refName].tableDatas[index])[attrKey] = event;
        },
        copyData(row){
            this.templateId = row.id;
            this.classCopyModalVisible=true;
        },
        getAllClass(){
            getAllClass({},this.requestPath).then(res=>{
                if(res.code==="0000"){
                    this.classData=res.data;
                }
            });
        },
        copyTemplate(){
            copyTemplate(this.templateId,this.copyForm.classId,this.requestPath).then(res=>{
                if(res.code==="0000"){
                    let printFormId=res.data;
                    copyPrintData(this.templateId,printFormId,this.requestPath).then(res=>{
                        if(res.code==="0000"){
                            this.templateId = "";
                            this.copyForm.classId="";
                            this.init();
                            this.classCopyModalVisible=false;
                            this.$Message.success("复制成功");
                        }else {
                            this.$Message.fail("复制失败：",res.data);
                            this.classCopyModalVisible=false;
                            this.templateId = "";
                            this.copyForm.classId="";
                        }
                    });
                }else{
                    this.$Message.fail("复制失败：",res.data);
                    this.classCopyModalVisible=false;
                    this.templateId = "";
                    this.copyForm.classId="";
                }
            });
        },
        cancelCopy(){
            this.templateId = "";
            this.copyForm.classId="";
            this.classCopyModalVisible=false;
        },
        closeModal(){
            this.canUpload = false;
            this.uploadParam={};
            this.showFileUpload = false;

        },
        setValue(row){
            console.log(row.isRebuildList[index],"1111-----ss")
        },
        handleSearch() {
            this.remoteTableDatas(1,10);
        },
        handleReset(){
            this.searchForm.attachmentName="";
            this.remoteTableDatas(1,10);
        },
        getToken(){
            return  {
                accessToken:this.$store.getters.access_token,
                refreshToken:this.$store.getters.refresh_token,
                expiresIn:(this.$store.getters.expires_in-Date.now())/1000
            }
        }
    },
    mounted() {
        if(this.$route.meta.params&&this.$route.meta.params.requestPath&&this.$route.meta.params.requestPath!==''){
            this.requestPath = this.$route.meta.params.requestPath;
        }
        this.init();
        this.queryRuleAtomInfo();
    }
}
</script>
