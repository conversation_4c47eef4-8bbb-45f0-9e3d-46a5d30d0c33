
<template>
    <div class="contract-list-page change-contact-list">
        <Card>
            <ChangeForm
                :isSP="isSP"
                :formValidate="formValidate"
                :formValidateRule="formValidateRule"
                :dataDict="dataDict"
                :businessSource="businessSource"
                :loading="loading"
                @showModal="showModal"
                @exportDataClick="exportDataClick"
                @getList="getList"
            />
            <ChangeTable
                ref="child"
                :isSP="isSP"
                :formValidate="formValidate"
                :dataDict="dataDict"
                :contractTable="contractTable"
                :businessSource="businessSource"
                :loading="loading"
                @getList="getList"
                @showContractDetial="showContractDetial"
            />
        </Card>
        <ActivatedCase
            :isSP="isSP"
            :modal="modal"
            modalTitle="已激活案件列表"
            :dataDict="dataDict"
            :handleBusiness="handleBusiness"
            @showContractDetial="showContractDetial"
            @closeModal="closeModal"
            :contractBusiness="contractBusiness"
        />
        <!-- <button @click="showContractDetial({contractNo:'N888888'})">联系方式变更详情</button> -->
    </div>
</template>
<script>
import { mapGetters } from "vuex";
// api
import {
    getManageContactChangeList,
} from "_p/basic/api/contract/post-loan-info-change";

import { channelType, businessSource } from "_p/basic/api/contract/channel-type/channel-type";

import { isPhoneValiate, getDictDataList } from "_p/basic/assets/contract/js/utils";

import ChangeForm from "./ChangeForm";
import ChangeTable from "./ChangeTable";
import ActivatedCase from "_p/basic/pages/contract/common/activated-case/index";

export default {
    name: "ChangeContactList",
    components: {
        ChangeForm,
        ChangeTable,
        ActivatedCase
    },
    props: {},
    data() {
        return {
            contractBusiness:["contactInfoChange","closeContract"],
            pageNo: 1,
            // 是否是合作商
            isSP: false,
            loading: false,
            // 日期区间
            isDateErr: false,
            formValidate: {
                caseAll:false,
                applyNo: "", // 申请编号
                contractNo: "", // 合同号
                custName: "", // 客户名称
                pageNumber: 1, // integer($int32) // 当前页
                pageSize: 10, //integer($int32) // 多少条,
                processStatus: null //'draft'// "refuse"
            },
            handleBusiness: "contactInfoChange",
            formValidateRule: {
                phone: [
                    {
                        validator: (rule, value, callback) => {
                            isPhoneValiate(rule, value, callback);
                        },
                        trigger: "blur"
                    }
                ]
            },
            contractTable: {
                current: 1, // 表格当前页
                pages: 0,
                searchCount: false,
                size: 10,
                total: 0,
                orders: [],
                records: []
            },
            pageSizeOpts: [10, 20, 50, 100],
            // 处理状态(processStatus) 来源(businessSource) 变更主体(PersonRoleType) 合同状态/合同申请状态(contractStatus) 渠道类型(channelType)
            // 已激活案件列表: contractStatus 合同状态 channelType 渠道类型,contactProcessStatus 联系方式处理状态
            dicttArr: [
                "dataSource",
                "processStatus",
                "businessSource",
                "PersonRoleType",
                "contractStatus",
                "ContactChangeType",
                "channelType",
                "exemptProcessStatus",
                "contactProcessStatus",
                "orderSourceSystemType",
                'belongingCapital',
            ],
            dataDict: {}, // 数据字典
            modalTitle: "已激活案件列表",
            modal: false
        };
    },
    created() {
            this.isSP = channelType(this.$route.meta.params);
            // businessSource 通过 roleType 获取 值: BUSINESS_PEOPLE / SP
            this.businessSource = businessSource(this.isSP);
    },
    mounted() {
    },
    activated() {
        this.init();
    },
    methods: {
        init() {
            this.getList(this.formValidate);
            this.getDictDataList();
        },
        getDictDataList() {
            getDictDataList(this.dicttArr,this.afs.getMenuParams(this).dicSource).then(res => {
                this.dataDict = res;
                console.log("this.dataDict520",this.dataDict)
            });
        },
        getList(data) {
            if(this.isSP){
                data.caseAll = true;
            }
            this.loading = true;
            getManageContactChangeList(data,channelType(this.$route.meta.menusParams)).then(res => {
                this.loading = false;
                if (res.code === "0000") {
                    this.contractTable = res.data;
                } else {
                    this.$Message.error("Fail!");
                }
            });
        },
        // 分页 Fn
        changePageSize(num) {
            this.getList(
                Object.assign({}, this.formValidate, { pageSize: num })
            );
            this.formValidate.pageSize = num;
        },
        changePage(num) {
            this.getList(
                Object.assign({}, this.formValidate, { pageNumber: num })
            );
            this.formValidate.pageNumber = num;
        },

        showContractDetial(params, isReadOnly) { //isReadOnly: 查看
            if (!params && !params.contractNo) return;
            if (isReadOnly) params.isReadOnly = true;
            params.apply = this.$route.meta.menusParams.apply
            this.modal = false;
            this.afs.newTab(
                this,
                "projects/basic/pages/contract/post-loan-info-change/change-contact-detial/index",
                "联系方式变更详情",
                "ios-add",
                params,
                "change-contact-detail-" + md5(params.caseNo || params.contractNo),
                [],
                true
            );
            this.pageNo += 1;
        },
        // 弹窗
        showModal() {
            this.modal = true;
        },
        // 导出
        exportDataClick() {
            this.$refs.child.exportData();
        },
        closeModal() {
            this.modal = false;
        }
    }
};
</script>
<style lang="less" >
@import "../../../../styles/common/form.less";

.page {
    padding-top: 8px;
}

.text-center {
    text-align: center;
}
</style>
