<template>
    <div>
        <Card>
            <div v-if="!isSP">
            <div class="contract-detial-btns top-btn">
                <Button type="primary" @click="back">返回</Button>
                <template v-if="!isReadOnly">
                    <template v-if="isApprove">
                        <Button type="primary" @click="approve('pass')">审核通过</Button>
                        <Button type="primary" @click="approve('return')">退件</Button>
                        <Button type="primary" @click="approve('reject')">拒绝</Button>
                    </template>
                    <template v-else-if="!isApproved">
                        <Button type="primary" @click="save">保存</Button>
                        <Button type="primary" @click="submit">提交</Button>
                    </template>
                </template>
            </div>
            <div class="contract-detail-collapse">
                <Collapse v-model="openPanel">
                    <Panel name="1" v-if="(isApprove || isApproved) && !this.Obj.roleType">
                        日志
                        <div class="content" slot="content">
                            <approveHistory :Obj="Obj"/>
                        </div>
                    </Panel>
                    <Panel name="2">
                        基本信息
                        <div class="content" slot="content">
                            <BaseInfo ref="baseInfo" :formData="pageInfo"/>
                        </div>
                    </Panel>
                    <Panel name="3">
                        合同信息
                        <div class="content" slot="content">
                            <contractInfo ref="contractInfo" :Obj="Obj" pageName="changeContact"/>
                        </div>
                    </Panel>
                        <!--            <Panel name="4" v-if="!isSP">-->
                        <!--                融资租赁产品-->
                        <!--                <div class="content" slot="content"  >-->
                        <!--                    <FinanceInfo :Obj="Obj" />-->
                        <!--                </div>-->
                        <!--            </Panel>-->
                    <Panel name="5">
                            业务办理
                            <div class="content" slot="content">
                                <Business
                                    ref="business"
                                    :Obj="Obj"
                                    :pageParams="pageInfo"
                                    :dataDict="dataDict"
                                    :dataSource="dataSource"
                                    :isApprove="isApprove"
                                    :isApproved="isApproved"
                                    :isReadOnly="isReadOnly"
                                    :disableSubject="disableSubject"
                                    @saveDataFn="saveDataFn"
                                    @submitDataFn="submitDataFn"
                                    @childBusinessForm="childBusinessForm"
                                    @closeSpin="closeSpin"
                                    @baseInfo="setBaseInfo"
                                />
                            </div>
                        </Panel>
                </Collapse>
                </div>
            </div>
        </Card>
    <div  v-if="isSP" class="applyDetail contract-entry">
        <div class="detail-card" style="margin-top: 16px;">
            <Collapse v-model="openPanel">
                <Panel name="1" v-if="(isApprove || isApproved) && !this.Obj.roleType">
                    日志
                    <div class="content" slot="content">
                        <approveHistory :Obj="Obj"/>
                    </div>
                </Panel>
                <Panel name="2">
                    基本信息
                    <div class="content" slot="content">
                        <BaseInfo ref="baseInfo" :formData="pageInfo"/>
                    </div>
                </Panel>
                <Panel name="3">
                    合同信息
                    <div class="content" slot="content">
                        <contractInfo ref="contractInfo" :Obj="Obj" pageName="changeContact"/>
                    </div>
                </Panel>
                <!--            <Panel name="4" v-if="!isSP">-->
                <!--                融资租赁产品-->
                <!--                <div class="content" slot="content"  >-->
                <!--                    <FinanceInfo :Obj="Obj" />-->
                <!--                </div>-->
                <!--            </Panel>-->
                <Panel name="5">
                    业务办理
                    <div class="content" slot="content">
                        <Business
                            ref="business"
                            :Obj="Obj"
                            :pageParams="pageInfo"
                            :dataDict="dataDict"
                            :dataSource="dataSource"
                            :isApprove="isApprove"
                            :isApproved="isApproved"
                            :isReadOnly="isReadOnly"
                            :disableSubject="disableSubject"
                            @saveDataFn="saveDataFn"
                            @submitDataFn="submitDataFn"
                            @childBusinessForm="childBusinessForm"
                            @closeSpin="closeSpin"
                            @baseInfo="setBaseInfo"
                        />
                    </div>
                </Panel>
            </Collapse>
        </div>
        <FixedNav @save="save" :changeFlag="1" :saveFlag="isReadOnly"/>
        <div class="footer" v-if="!isReadOnly">
            <div class="footer_content content-box">
                <Button v-if="isApprove" style="height: 32px;font-size: 12px;text-align: center;padding: 0 15px;border-radius: 4px;" type="primary" @click="approve('pass')">审核通过</Button>
                <Button v-if="isApprove" style="height: 32px;font-size: 12px;text-align: center;padding: 0 15px;border-radius: 4px;" type="primary" @click="approve('return')">退件</Button>
                <Button v-if="isApprove" style="height: 32px;font-size: 12px;text-align: center;padding: 0 15px;border-radius: 4px;" type="primary" @click="approve('reject')">拒绝</Button>
                <Button v-if="!isApprove" style="height: 32px;font-size: 12px;text-align: center;padding: 0 15px;border-radius: 4px;" type="primary" @click="save">保存</Button>
                <Button v-if="!isApprove" style="height: 32px;font-size: 12px;text-align: center;padding: 0 15px;border-radius: 4px;" type="primary" @click="submit">提交</Button>
            </div>
        </div>

    </div>
    </div>

</template>
<script>
    import moment from "moment";
    import {
        changeContactChange,
        changeContactSubmit,
        // saveProposalApplicationInfo
    } from "_p/basic/api/contract/post-loan-info-change/change-contact";

    import {deepClone} from "@/libs/utils/ObjectClone";

    // 日志
    import approveHistory from "_p/basic/pages/contract/common/approve-history/approve-history";
    // 融资租赁产品
    import FinanceInfo from "_p/basic/pages/contract/common/finance-info/finance-info";
    // 业务办理
    import Business from "_p/basic/pages/contract/post-loan-info-change/change-contact-detial/Business";
    // 合同信息
    import contractInfo from "_p/basic/pages/contract/common/contract-info/contract-info";
    // 基本信息
    import BaseInfo from "_p/basic/pages/contract/common/base-info";
    // 当前用户
    import ContractCurrentUser from "_p/basic/pages/contract/common/current-user/contractCurrentUser";
    import {findByContractNo} from "_p/basic/api/contract/activated-case";
    import { channelType } from "_p/basic/api/contract/channel-type/channel-type";
    import FixedNav from '../../../contract/common/fixedNav'
    export default {
        name: "ChangeContactDetial",
        components: {
            approveHistory,
            FinanceInfo,
            contractInfo,
            BaseInfo,
            ContractCurrentUser,
            Business,
            FixedNav
        },
        props: {
            Obj: {
                //caseNo, contractNo, roleType
                type: Object,
                required: true
            },
            spinShow: {
                type: Boolean,
                required: true
            },
            //是否是审批页面
            isApprove: {
                type: Boolean,
                required: true
            },
            //是否 审批完成
            isApproved: {
                type: Boolean,
                required: true
            },
            isReadOnly: {
                type: Boolean,
                default: false
            },
            pageParams: {
                type: Object,
                required: true
            },
            dataSource:{
                type: String,
                required: true
            },
            //合同号
            contractNo: {
                type: String,
                required: true
            },
            dataDict: {
                type: Object
            },
            // userInfo: {
            //     type: Object,
            //     required: true
            // },
            businessSource: {
                type: String,
                required: true
            },
            // roleType: {
            //     type: Array,
            //     required: true
            // }
            // 是否是供应商
            isSP: {
                type: Boolean,
                required: true
            },
        },
        data() {
            return {
                // isSp:false,
                disableSubject: false,
                loading: true,
                openPanel: [0, 2, 3, 4, 5, 6],
                readOnly: null,
                approvalOperating: "", //审批结果
                approveType: null, //
                pageInfo: this.pageParams
            };
        },

        mounted() {
            this.init();
        },
        methods: {
            init() {
                // this.isSp = channelType(this.$route.meta.menusParams)
                if (this.$refs.baseInfo) {
                    this.$refs.baseInfo.formData = Object.assign(
                        {},
                        this.$refs.baseInfo.formData,
                        this.pageInfo
                    );
                }
            },
            save() {
                if (this.$refs.business.formData) {
                    this.showSpin();
                    this.$refs.business.formValidate("saveDataFn");
                    console.log(this.$refs.business,"this.$refs.business");
                }
            },
            submit() {
                this.$refs.business.formValidate("submitDataFn");
            },
            // 审核等按钮
            approve(name) {
                if (!name) return;
                let t, val;
                switch (name) {
                    case "pass":
                        t = "审核通过";
                        val = "suggestCheckF";
                        break;
                    case "return":
                        t = "退件";
                        val = "sendBack";
                        break;
                    case "reject":
                        t = "拒绝";
                        val = "suggestRejectF";
                        break;
                }
                this.approveType = name;
                this.$Modal.confirm({
                    title: `确认${t}？`,
                    onOk: () => {
                        if (this.$refs.business) {
                            this.showSpin();
                            this.approvalOperating = val;
                            // 验证 business 组件数据 给下一个方法
                            this.$refs.business.formValidate("childBusinessForm");
                        }
                    }
                });
            },
            // 保存 请求
            saveDataFn(data) {
                if (!data) return;
                let d = this.formatData(data);
                console.log(d,"d")

                changeContactChange(d,this.isSp)
                    .then(res => {
                        this.$emit("closeSpin");
                        if (res.code === "0000") {
                            this.$Message.success("保存成功!");
                            this.afs.setPageParams(this, {caseNo: res.data.caseNo});
                            this.pageInfo = res.data;
                            console.log("this.pageInfo",this.pageInfo)
                            // this.$refs.baseInfo.formData = res.data;
                            this.disableSubject = true;
                        }
                    })
                    .catch(e => {
                        this.$emit("closeSpin");
                    });
            },
            //提交 请求
            submitDataFn(data) {
                let val = "suggestCheckF";
                let d = this.formatData(data);
                console.log("d",d)
                d.approvalOperating = val;
                this.$Modal.confirm({
                    title: "确认提交？",
                    onOk: () => {
                        this.submitOverpayment(d);
                    }
                });
            },
            submitOverpayment(data) {
                if (!data) return;
                let d = this.formatData(data);
                changeContactSubmit(d,this.isSp)
                    .then(res => {
                        this.$emit("closeSpin");
                        if (res.code === "0000") {
                            this.$Message.success("提交成功!");
                            this.back();
                        }
                    })
                    .catch(e => {
                        this.$emit("closeSpin");
                    });
            },
            // 审批 请求
            // Business 组件数据
            childBusinessForm(data) {
                if (!data) return;
                // let bussiness = this.formatData(data);
                // 验证 contractCurrentUser 组件数据 传给下个方法
                this.$refs.contractCurrentUser.formValidate(
                    "childUserForm",
                    this.formatData(data),
                    this.approveType
                );
            },
            // contractCurrentUser 组件数据
            childUserForm(data) {
                if (!data) return;
                let d = Object.assign({}, data, {
                    approvalOperating: this.approvalOperating
                });
                //都没 saveProposalApplicationInfo这个方法瞎搞啥
                // saveProposalApplicationInfo(d,this.isSp)
                //     .then(res => {
                //         this.$emit("closeSpin");
                //         if (res.code === "0000") {
                //             this.$Message.success("成功!");
                //             this.back();
                //         }
                //     })
                //     .catch(e => {
                //         this.$emit("closeSpin");
                //     });
            },

            // 格式化
            formatData(data) {
                if (!data) return {};
                let d = Object.assign({businessSource: this.businessSource}, this.pageInfo, data);
                d.custName = this.$refs.contractInfo.formData.basicCustBaseInfo.custName;
                if (d.createTime instanceof Date)
                    d.createTime = moment(d.createTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                    );
                if (d.endDate instanceof Date)
                    d.endDate = moment(d.endDate).format("YYYY-MM-DD HH:mm:ss");
                return d;
            },
            setBaseInfo(data) {
                const {caseNo, applyBy, applyDate} = data;
                let obj = {
                    caseNo: this.pageInfo.caseNo || caseNo,
                    applyBy: this.pageInfo.applyBy || applyBy,
                    applyDate: this.pageInfo.applyDate || applyDate
                }
                this.pageInfo = Object.assign({}, this.pageInfo, obj)
            },
            showSpin() {
                this.$emit("showSpin");
            },
            closeSpin() {
                this.$emit("closeSpin");
            },
            back() {
                this.$emit("back");
            }
        }
    };
</script>
<style lang="less" scoped="scoped">
    @import "../../../../pages/contract/common/contract-entry.less";

    .div-layer {
        position: relative;
    }

    .upimg:hover {
        background: #EB9620;
        text-decoration: none;
    }

    .upimg {
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background: #EB9620;
        border-radius: 5px;
        color: #FFFFFF;
        position: absolute;
        top: 40%;
        left: 33%;
        transform: translate(-50%, -50%);
    }

    .upimg2 {
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background: #EB9620;
        border-radius: 5px;
        color: #FFFFFF;
        position: absolute;
        top: 40%;
        left: 62%;
        transform: translate(-50%, -50%);
    }

    .upimg input {
        position: absolute;
        left: 0;
        right: 0;
        width: 100%;
        top: 0;
        bottom: 0;
        height: 100%;
        filter: alpha(opacity=0);
        opacity: 0;
        cursor: pointer;
        overflow: hidden;
    }

    .upimg2 input {
        position: absolute;
        left: 0;
        right: 0;
        width: 100%;
        top: 0;
        bottom: 0;
        height: 100%;
        filter: alpha(opacity=0);
        opacity: 0;
        cursor: pointer;
        overflow: hidden;
    }

    .showImg i {
        position: absolute;
        right: 0;
        bottom: 0;
    }

    .showImg > li {
        width: 100px;
        height: 100px;
        position: relative;
        border: 1px dotted #ccc;
        margin-left: 100px;
    }

    .showImg > li img {
        width: 100%;
        height: 100%;
    }

    .tree-bar-box {
        position: relative;
        z-index: 100;

        .tree-bar {
            background-color: #fff;
            position: absolute;
            top: 30px;
            border: 1px solid rgb(220, 222, 226);
        }

        .tree-mask {
            display: none;
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 99;

            &.open {
                display: block;
            }
        }
    }

    .footer {
        position: fixed;
        height: 60px;
        width: 100%;
        background-color: white;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.27), 0px -1px 12px 0px rgba(0, 0, 0, 0.05);
    }

    .footer .footer_content {
        max-width: 1600px;
        //min-width: 1200px;
        margin: auto;
        height: 100%;
        line-height: 44px;
        padding: 0 25px;
        text-align: right;

    }

    .footer .footer_content > p > button {
        height: 30px;
        font-size: 12px;
        text-align: center;
    }

    .footer .footer_content > p > button.submitBtn {
        margin-left: 10px;
    }
</style>
<style lang="less" scoped>
    @import "../../../../pages/contract/common/applyDetail";

    .contract-entry .fileOperation {
        padding-top: 8px;
    }
</style>
<style lang="less">
    .ivu-tabs-nav-container {
        font-size: 14px;
    }

    .applyDetail .ivu-tabs-nav .ivu-tabs-tab:hover {
        color: #EB9620;
    }

    .applyDetail .detail-card .ivu-tabs-nav .ivu-tabs-tab {
        padding: 0px 16px 16px;
    }
</style>
