<style lang="less" scoped="scoped">
@import "contract-entry.less";
@import "../../../../assets/css/applyDetail";

//.abs-create-time-left {
//    margin-left: 15%;
//}
//.abs-create-time-right {
//    margin-right: 20%;
//}
.leaveComments .detail_comment button{
    margin: 0px;
}

table.gridtable {
   font-family: verdana,arial,sans-serif; margin-bottom: 10px;
   font-size:11px;
   color:#333333;
   border: 1px solid #dcdee2;
   border-collapse: collapse;
   width: 100%;
 }
 table.gridtable th {text-align: left; padding-left:8px;
    padding: 8px;
    border: 1px solid #dcdee2;
 }
 table.gridtable td {
    padding: 8px;
    border: 1px solid #dcdee2;
    background-color: #ffffff;
}
table.gridtable td .ivu-input-wrapper{
  width: 100%;
}

table.gridtable td  .ivu-form-item{margin-bottom:0px}
.footer {
    position: fixed;
    height: 60px;
    width: 100%;
    background-color: white;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
    box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.27), 0px -1px 12px 0px rgba(0, 0, 0, 0.05);
}

.footer .footer_content {
    max-width: 1600px;
    //min-width: 1200px;
    margin: auto;
    height: 100%;
    line-height: 44px;
    padding: 0 25px;
    text-align: right;

}

.footer .footer_content > p > button {
    height: 30px;
    font-size: 12px;
    text-align: center;
}

.footer .footer_content > p > button.submitBtn {
    margin-left: 10px;
}

</style>
<style lang="less">
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色 */
    z-index: 1000; /* 确保蒙层覆盖其他元素 */
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.tw100{width: calc(50% + 280px) !important;}
.addressBox  .ivu-form-item{width: 100% !important;}
.ivu-tabs-nav-container {
    font-size: 14px;
}

.applyDetail .ivu-tabs-nav .ivu-tabs-tab:hover {
    color: #EB9620;
}

.applyDetail .detail-card .ivu-tabs-nav .ivu-tabs-tab {
    padding: 0px 16px 16px;
}
.marginLeftNine{
    margin-left: 7%;
}
.marginLeftOne{
    margin-left: 1%;
}
.color_red {
    .ivu-input {
        color: red;
    }
}
.leftRight{
    color: #2960f2;
    font-weight: normal;
    font-size: 13px;
    width: 40px;
    cursor: pointer;
    float: right;
    margin-right: 1%;
}
.lineHeigh{
    line-height: 38px;
}
.widthMoney{
    width: 106%;
}
.leaveComments {
    position: fixed;
    top: 50%;
    right: 10%;
    transform: translateY(-50%);
    margin-top: 40px;
    z-index: 99;
}
.leaveComments .detail_comment {
    position: absolute;
    background-color: white;
    width: 300px;
    height: 380px;
    box-shadow: 0px 0px 18px #ccc;
    top: 18px;
    transition: left 2s;
    border-radius: 5px;
}

.leaveComments .detail_comment > h2 {
    background-color: #EB9620;
    font-weight: normal;
    font-size: 14px;
    color: white;
    padding: 5px 0px 5px 10px;
    line-height: 25px;
    border-radius: 5px 5px 0 0;
    position: relative;
}

.leaveComments .detail_comment > h2 > i {
    font-size: 16px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
}
.leaveComments .detail_comment ul {
    height: calc(100% - 70px);
    padding: 0 0 0 10px;
    font-size: 12px;
    overflow: auto;
    padding-bottom: 30px;
    background: rgba(235, 150, 2, 0.1);
}

.leaveComments .detail_comment button {
    vertical-align: top;
    margin-top: 10px;
}

.leaveComments /deep/ .ivu-tooltip-light .ivu-tooltip-inner {
    font-size: 14px;
}

.leaveComments /deep/ .ivu-tooltip {
    width: 100%;
}
.tapImg {
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
}
.tapImg > img {
    width: 40px;
    cursor: pointer;
}
.fade_commonent {
    position: relative;
    height: 420px;
    width: 400px;
    overflow: hidden;
    transition: all 2s;
}
.detail_comment .textarea /deep/ textarea.ivu-input {
    resize: none;
    height: 40px;
    border: none;
    padding-top: 10px;
}

.detail_comment .textarea /deep/ textarea.ivu-input:focus {
    box-shadow: none;
}

.detail_comment p {
    width: 250px;
    font-size: 12px;
    box-shadow: 0 4px 6px 2px #eaebee;
    border-radius: 5px;
    margin: 10px 0 0;
    position: relative;
    min-height: 36px;
    background-color: white;
    padding: 5px 10px;
}

.detail_comment p::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: -6px;
    border-style: solid;
    border-width: 6px;
    border-color: transparent transparent #fff #fff;
    transform: rotate(135deg);
    -webkit-box-shadow: -2px 2px 3px 0 #eaebee;
    -moz-box-shadow: -2px 2px 3px 0 #eaebee;
    box-shadow: -2px 2px 4px 0 #eaebee;
}

.detail_comment p.other::before {
    left: 15px;
}

.detail_comment p.self::before {
    text-align: right;
    right: 15px;
}
.detail_comment .textarea {
    padding: 0px 20px 0 10px;
    position: absolute;
    width: 100%;
    bottom: 0px;
    background: white;
    border-radius: 0px 0px 5px 5px;
}
.noWidth {
    width: 0px;
}
.defaultWidth {
    width: 400px;
}
.hiddenClass {
    left: calc(100% + 18px);
}
.defaultClass {
    left: 68px;
}


.my-button {
    /* 相对父级定位，处于父级的右下角，距离右边30px、底部50px的位置 */
    right: 0px;
    bottom: 0px;
}

.input {
    /* 直接设置1px边框覆盖 */
    border: 1px solid ;
    /* 取消默认的上下1px内边距 */
    padding: 0;
    float: right;
}
</style>
<template>
    <div class="applyDetail contract-entry">
        <transition name="fade">
            <div v-if="showOverlay" class="overlay"></div>
        </transition>
          <!--  数据维护  -->
                <maintenance-model
                @mainCloseModal="mainCloseModal"
                @changeVerType="changeVerType"
                :applyNo="applyNo"
                :holidayConfig="holidayConfig"
                :carVin="carDetails.carVin"
                :uploadParam="uploadParam"
                :path="path"
            ></maintenance-model>

        <basicinfo v-bind:orderInfo="this.formEntry.orderInfo" :recordDetails="this.formEntry.recordDetails" :isNeedApproval="true"></basicinfo>
        <Spin size="large" fix v-if="loading" class="apply-spin" style="position: fixed;z-index: 999;"></Spin>
        <div class="detail-card" style="margin-top: 16px;">
            <Tabs v-model="openTabs" @on-click="turnToUpload">
                <TabPane label="基本信息" name="baseInfo">
                    <div class="tab" v-show="openTabs === 'baseInfo'">

                            <Collapse v-model="openPanel" simple>
                                <client-info
                                    ref="clientInfo"
                                    :applyNo="applyNo"
                                    :isShowDetails="onlyCheck"
                                    @updateFile="updateFileOperation"
                                    @changePersonalToEnterprise="changePersonalToEnterprise"
                                />
                                <Panel name="2">
                                    <span>车辆信息<span style="color: red;">（生成合同必填信息）</span></span>
                                    <span @click="saveCarDetails" class="leftRight" v-show="!isBusinessType"  @click.stop>保存</span>

                                    <div class="panel-content" slot="content">
                                         <Form :model="carDetails" ref="carDetails" :rules="certificateDiscernRule" :label-width="160" inline>
                                        <div class="form-box">
                                            <Row>
                                                <FormItem label="品牌" prop="brandName" :rules="{required: true, message: '品牌不能为空', trigger: 'blur'}">
                                                    <Input type="text" v-model="carDetails.brandName"
                                                           disabled/>
                                                </FormItem>
                                                <FormItem label="车系" prop="seriesName" :rules="{required: true, message: '车系不能为空', trigger: 'blur'}">
                                                    <Input type="text" v-model="carDetails.seriesName"
                                                           disabled/>
                                                </FormItem>
                                                <FormItem label="预计上牌地">
                                                    <rui-region
                                                        v-model="licenseProvinceCity"
                                                        :area="false"
                                                        :town="false"
                                                        :road="false"
                                                        :detail="false"
                                                        disabled
                                                    />
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="车型" prop="modelName" :rules="{required: true, message: '车型不能为空', trigger: 'blur'}" >
                                                    <Input type="text" v-model="carDetails.modelName" disabled/>
                                                </FormItem>
                                                <FormItem label="车架号" prop="carVin" >
                                                    <Input type="text" v-model="certificateDiscern.carVin"
                                                           :disabled="isBusinessType" :rules="{required: true, message: '车架号不能为空', trigger: 'blur'}"/>
                                                    <Button type="primary" class="ocrButton" @click="CardIdOCR('img_certificateCard')">OCR</Button>
                                                    <input type="file" accept="image/*" @change="getCertificateCardOCR($event,'basicInfor')" v-show="false" value="" ref="img_certificateCard"/>
                                                </FormItem>
                                                <FormItem label="发动机号" prop="engineNo" >
                                                    <Input type="text" v-model="certificateDiscern.engineNo"
                                                           :disabled="isBusinessType" :rules="{required: true, message: '发动机号不能为空', trigger: 'blur'}"/>
                                                </FormItem>
                                                <FormItem label="车辆颜色" prop="carColor" >
                                                    <Input type="text" v-model="certificateDiscern.carColor"
                                                           :disabled="isBusinessType"/>
                                                </FormItem>
<!--                                                <FormItem label="国标码" prop="gbCode">
                                                    <Input type="text" v-model="certificateDiscern.gbCode"
                                                           :disabled="isBusinessType" />
                                                </FormItem>-->
                                               <FormItem  label="车辆出厂日期" prop="vehicleMadeDate" >
                                                    <DatePicker type="date" v-model="certificateDiscern.vehicleMadeDate"
                                                                format="yyyy-MM-dd"  :disabled="isBusinessType" :editable="false"></DatePicker>
                                                </FormItem>
                                            </Row>
                                        </div>
                                          </Form>
                                    </div>
                                </Panel>
                                <Panel name="18" v-if="isChannelBelong">
                                    <span>车辆订金支付信息</span>
                                    <span class="leftRight" @click="editDepositConfig" v-show="!isDisabled && !onlyCheck" @click.stop>编辑</span>
                                    <div class="panel-content" slot="content">
                                        <depositInfo ref="depositForm"   :applyNo="applyNo" :paySplitFlag="paySplitFlag" @queryPayee="queryPayeeInfo"/>
                                    </div>
                                </Panel>
                                <Panel name="4" v-if="!isChannelBelong">
                                    <span>收款方账号信息<span style="color: red;">（生成合同必填信息）</span></span>
                                    <span class="leftRight" @click="saveApplyContractInfoPayee" v-show="!isView" @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                        <Form :label-width="160" inline v-model="contractInfoForm">
                                            <div class="form-box"  v-for="(item, index) in contractInfoForm" :key="index">
                                                <FormItem label="收款方账号"  :required='true'>
                                                    <Input type="text" v-model="item.receiptBankCode" :disabled="true"/>
                                                </FormItem>
                                                <FormItem label="收款方名称" :required='true'>
                                                    <Input type="text" v-model="item.receiptAccount" :disabled="true"/>
                                                </FormItem>
                                                <Button  v-show="!isView" type="primary" @click="selectedItemFun('sp')">选择</Button>
                                            </div>
                                        </Form>
                                    </div>
                                </Panel>
                                <Panel name="9" v-if="isChannelBelong">
                                    <span>收款方账号信息<span style="color: red;">（生成合同必填信息）（“付款备注”默认自动附加“合同编号_承租人名称”，请不要重复录入“合同编号或承租人名称”等信息）</span></span>
                                    <span class="panel-head-right" @click.stop>

                                        <!-- <Button  @click="addContractInfo"  v-show="!isDisabled"  style="margin-right: 5px">新增</Button> -->
                                        <Button  @click="SelectContractInfo"  v-show="!isDisabled && !isDistributeDisabled"  style="margin-right: 5px">
                                             {{paySplitFlag==0?"拆分":"全款"}}
                                        </Button>

                                        <Button type="primary" @click="savePayee" :disabled="savePayeeFlag"  v-show="!isDisabled && !isDistributeDisabled"  @click.stop  style="margin-right: 5px">保存 </Button>
                                    </span>

                                    <div class="panel-content" slot="content">
                                    <Form :label-width="0" ref="contractInfoForm" inline>
                                        <div class="form-box">
                                         <table class="gridtable">
                                            <tr>
                                                <th>支付项目</th>
                                                <th>应付金额</th>
                                                <th>后置支付</th>
                                                <th width="100">实付金额</th>
                                                <th>放款方式</th>
                                                <th>收款方名称</th>
                                                <th>收款账号类型</th>
                                                <th>收款账号</th>
                                                <th>付款备注</th>
                                            </tr>
                                            <tr  v-for="(item, index) in contractInfoForm" :key="index" >
                                                <td> {{collectionTypeArray[item.collectionType]}} </td>
                                                <td>{{item.preCollectionAmount | getRMB}}</td>
                                                <td>
                                                    <FormItem v-if="item.collectionType==3 || item.collectionType==4">
                                                          <!-- <input type="checkbox" class="checkboxSize" :checked="item.afterPayFlag==1" /> -->
                                                          <Checkbox label="香蕉" :disabled='isDistributeDisabled'  v-model="item.afterPayFlag" :true-value="1"   @on-change="selectAfterPayFlag(index)"></Checkbox>
                                                    </FormItem>
                                                </td>
                                                 <td >
                                                    <FormItem>
                                                            <NumberInput
                                                                v-model="item.collectionAmount"
                                                                number
                                                                :formatter="value =>  currencyFormat(value,2)"
                                                                :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                :min="0"
                                                                :disabled="!(item.collectionType==3 || item.collectionType==4) || item.afterPayFlag==1 || isDistributeDisabled"
                                                                style="width:130px"
                                                            />
                                                    </FormItem>
                                                </td>
                                                <td><FormItem>
                                                        <Select v-model="item.autoFlag"
                                                        :disabled="item.afterPayFlag==1 || isDistributeDisabled"
                                                         style="width:80px">
                                                            <Option v-for="itemList in dataDic.loanWay"  :value="itemList.value"  :key="itemList.value" :label="itemList.title">
                                                                    {{ itemList.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </td>
                                                <td style="display:flex">
                                                    <FormItem>
                                                            <Input type="text" v-model="item.receiptAccount" :disabled="true"/>
                                                    </FormItem>
                                                    <Button v-if="!isDistributeDisabled" type="primary" @click="selectedItemFun(index)"
                                                    :disabled="item.afterPayFlag==1 || isDistributeDisabled"
                                                    >选择</Button>
                                                </td>
                                                <td> {{collectionTypeArray[item.receiptType]}}</td>
                                                <td>
                                                       <FormItem><Input type="text" v-model="item.receiptBankCode" :disabled="true"/></FormItem>
                                                </td>
                                                <td>
                                                       <FormItem><Input type="text" v-model="item.remark"
                                                        :disabled="item.afterPayFlag==1 || isDistributeDisabled"
                                                        maxlength="15" show-word-limit
                                                       /></FormItem>
                                                </td>
                                            </tr>
                                        </table>
                                        </div>
                                         </Form>
                                    </div>
                                </Panel>
                                 <Panel name="10"  v-if="isChannelBelong">
                                    <span>{{bankPanelTitle}}<span style="color: red;">（生成合同必填信息）</span></span>
                                    <span class="leftRight" @click="saveBankInformation" v-show="!isView"  @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                         <Form :model="bankCard" ref="bankCardFrom" :label-width="160" inline>
                                         <Card v-if="personalBack">

                                            <h2 style="margin-bottom: 10px;">个人账户</h2>
                                            <div class="form-box">

                                                <Row>
                                                    <FormItem label="签约方式" prop="authorizeWay" >
                                                        <Select v-model="bankCard.authorizeWay"
                                                                @on-change="changeVerType" :disabled="isView||isBankDisabled">
                                                            <Option value="offline">常规签署</Option>
                                                        </Select>
                                                    </FormItem>
                                                    <FormItem label="开户人">
                                                        <Select v-model="bankCard.accountName" clearable @on-change="bankCardaccount"
                                                                :disabled="isBankDisabled||isView" >
                                                            <Option v-for="(item,index) in accountPerson" :value="item.custName"
                                                                    :key="item.custName">
                                                                {{ item.custName}}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                    <FormItem label="开户人银行卡号" prop="accountNo" :rules="{required: personalBackRequired, message: '开户人银行卡号不能为空', trigger: 'blur'}">
                                                        <Input type="text" v-model="bankCard.accountNo" maxlength="23" @keypress.native="inputCard"
                                                            :disabled="isBankDisabled||isView" @on-blur="getSignBanknameBynum"/>
                                                        <Button type="primary" class="ocrButton" @click="CardIdOCR('img_bankCard')">OCR</Button>
                                                        <input type="file" accept="image/*" @change="getBankCardOCR($event,'bankInfor')" v-show="false" value="" ref="img_bankCard"/>
                                                    </FormItem>
                                                </Row>
                                                <Row>
                                                    <FormItem label="开户行" prop="bankBranch" :rules="{required: personalBackRequired, message: '开户行不能为空', trigger: 'blur'}" >
                                                        <Input type="text" disabled v-model="bankCard.bankBranch"/>
                                                    </FormItem>
                                                    <FormItem label="预留手机号码" prop="bankPhone" :rules="{required: personalBackRequired, message: '预留手机号码不能为空', trigger: 'blur'}" :required=isViewVer>
                                                        <Input type="text" v-model="bankCard.bankPhone"
                                                            :disabled="isBankDisabled||isView" maxlength="11"/>
                                                    </FormItem>
                                                    <FormItem label="证件类型">
                                                        <Select v-model="bankCard.certType" :disabled="showIdType" @on-change="idTypeChange" clearable>
                                                            <Option v-for="(item,index) in idTypeList" :value="item.value"
                                                                    :key="item.value" >
                                                                {{ item.title}}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Row>
                                                <div  v-for="(item,index) in bankCardChannel" :key="item.key">
                                                    <FormItem label="证件号码"  >
                                                        <Input v-model="bankCard.certNo" placeholder="请输入证件号码" :disabled="isBankDisabled||isView||isCert"/>
                                                    </FormItem>
                                                    <FormItem label="绑卡渠道"  >
                                                        <Input  disabled :placeholder="item.title" />
                                                    </FormItem>
                                                    <FormItem label="验证码">
                                                        <Input type="text" v-model="item.verCode" :disabled="item._disabled ||isView"/>
                                                        <Button type="primary" :disabled="item._disabled ||isView" :loading="item.loading" @click="sendMessage(index)" >
                                                            <span v-show="!item.loading" >获取验证码</span>
                                                            <span v-show="item.loading">{{item.Count}} s</span>
                                                        </Button>
                                                        <Button type="primary" @click="checkMessage(index)" :disabled="item._disabled ||isView">验证</Button>
                                                    </FormItem>
                                                    <Icon type="ios-checkmark-circle" style="font-size: 30px" color="green" v-show="item.isSendCode==1"/>
                                                    <Icon type="md-close-circle" style="font-size: 30px" color="red"  v-show="item.isSendCode==2"/>
                                                </div>
                                            </div>
                                        </Card>
                                          <Card v-if="enterprise">
                                            <h2 style="margin-bottom: 10px;">企业账户</h2>
                                            <div class="form-box">
                                                <Row>
                                                    <FormItem label="企业开户名称"  prop="enterpriseAccountName" :rules="{required: enterpriseRequired, message: '请填写企业开户名称', trigger: 'blur'}">
                                                         <Input type="text" v-model="bankCard.enterpriseAccountName" :disabled="isView"/>
                                                    </FormItem>
                                                    <FormItem label="开户人银行卡号" prop="enterpriseAccountNo" :rules="{required: enterpriseRequired, message: '开户人银行卡号不能为空', trigger: 'blur'}">
                                                        <Input type="text" v-model="bankCard.enterpriseAccountNo" maxlength="23" :disabled="isView"/>
                                                    </FormItem>
                                                    <FormItem label="开户行" prop="enterpriseBankCode" :rules="{required: enterpriseRequired, message: '开户行不能为空', trigger: 'blur'}">
                                                         <Select v-model="bankCard.enterpriseBankCode" filterable :disabled="isView" @on-change="getSignEnterprise">
                                                            <Option v-for="item in dataDic.bankCode" :value="item.value"
                                                                    :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Row>
                                            </div>

                                        </Card>
                                         </Form>
                                    </div>
                                </Panel>
                                <Panel name="10"  v-if="!isChannelBelong">
                                    <span>{{bankPanelTitle}}<span style="color: red;">（生成合同必填信息）</span></span>
                                    <span class="leftRight" @click="saveBankInformation" v-show="false"  @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                         <Form :model="bankCard" ref="bankCard" :label-width="160" :rules="bankCarde" inline>
                                        <div class="form-box">
                                            <Row>
                                                <FormItem label="签约方式" prop="authorizeWay" >
                                                    <Select v-model="bankCard.authorizeWay" @on-change="changeVerType" :disabled="isView||isBankDisabled">
                                                        <Option value="offline">常规签署</Option>
                                                    </Select>
                                                </FormItem>
                                                <FormItem label="开户人">
                                                    <Select v-model="bankCard.accountName" clearable  @on-change="bankCardaccount"
                                                            :disabled="isBankDisabled||isView" >
                                                        <Option v-for="(item,index) in accountPerson" :value="item.custName"
                                                                :key="item.index" >
                                                            {{ item.custName}}
                                                        </Option>
                                                    </Select>
                                                </FormItem>
                                                <FormItem label="开户人银行卡号" prop="accountNo">
                                                    <Input type="text" v-model="bankCard.accountNo" maxlength="23" @keypress.native="inputCard"
                                                           :disabled="isBankDisabled||isView" @on-blur="getSignBanknameBynum"/>
                                                    <Button type="primary" class="ocrButton" @click="CardIdOCR('img_bankCard')">OCR</Button>
                                                    <input type="file" accept="image/*" @change="getBankCardOCR($event,'bankInfor')" v-show="false" value="" ref="img_bankCard"/>
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="开户行" prop="bankBranch" :rules="{required: true, message: '开户行不能为空', trigger: 'blur'}" >
                                                    <Input type="text" disabled v-model="bankCard.bankBranch"/>
                                                </FormItem>
                                                <FormItem label="预留手机号码" prop="bankPhone" :required=isViewVer>
                                                    <Input type="text" v-model="bankCard.bankPhone"
                                                           :disabled="isBankDisabled||isView" maxlength="11"/>
                                                </FormItem>
                                                <FormItem label="证件类型">
                                                    <Select v-model="bankCard.certType" :disabled="isBankDisabled||isView" @on-change="idTypeChange" clearable>
                                                        <Option v-for="(item,index) in idTypeList" :value="item.value"
                                                                :key="item.value" >
                                                            {{ item.title}}
                                                        </Option>
                                                    </Select>
                                                </FormItem>
                                            </Row>
                                            <div  v-for="(item,index) in bankCardChannel" :key="item.key">
                                                <FormItem label="证件号码"  >
                                                    <Input v-model="bankCard.certNo" placeholder="请输入证件号码" :disabled="isBankDisabled||isView||isCert"/>
                                                </FormItem>
                                                <FormItem label="绑卡渠道"  >
                                                    <Input  disabled :placeholder="item.title" />
                                                </FormItem>
                                                <FormItem label="验证码">
                                                    <Input type="text" v-model="item.verCode" :disabled="item._disabled ||isView"/>
                                                    <Button type="primary" :disabled="item._disabled ||isView" :loading="item.loading" @click="sendMessage(index)" >
                                                        <span v-show="!item.loading" >获取验证码</span>
                                                        <span v-show="item.loading">{{item.Count}} s</span>
                                                    </Button>
                                                    <Button type="primary" @click="checkMessage(index)" :disabled="item._disabled ||isView">签约</Button>
                                                </FormItem>
                                                <Icon type="ios-checkmark-circle" style="font-size: 30px" color="green" v-show="item.isSendCode==1"/>
                                                <Icon type="md-close-circle" style="font-size: 30px" color="red"  v-show="item.isSendCode==2"/>
                                            </div>
                                        </div>
                                        </Form>
                                    </div>
                                </Panel>
                                <Panel name="5" v-if="isViewInvoice">
                                         <span >发票信息</span>
                                         <span @click="editInvoice" class="leftRight" v-show="!isView" @click.stop>编辑</span>
                                    <div class="panel-content" slot="content">
                                    <Form ref="invoiceDiscern" :model="invoiceDiscern" :label-width="160" :rules="invoiceCarValidate" inline>
                                            <Row v-if="isChannelBelong">
                                                <Col span="8">
                                                <FormItem label="是否后补发票">
                                                        <Select v-model="invoiceDiscern.isLaterSupply"
                                                                @on-change="carInvoiceIsLaterSupply" disabled>
                                                            <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                                    :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                        <Row>
                                            <Col span="8">
                                                <FormItem prop="invoiceUnit"
                                                          :rules="{required: fpIsLaterSupply, message: '车辆开票单位不能为空', trigger: 'blur'}"
                                                          label="车辆开票单位">
                                                    <Input type="text" v-model="invoiceDiscern.invoiceUnit"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem label="车辆发票金额"
                                                          :rules="{required: fpIsLaterSupply,type:'number',message: '车辆发票金额不能为空', trigger: 'blur'}"
                                                          prop="invoiceAmt">
                                                    <NumberInput
                                                        v-model="invoiceDiscern.invoiceAmt"
                                                        number
                                                        :formatter="value =>  currencyFormat(value,2)"
                                                        :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                        :class="{'color_red':pageStyle.carDifferPriceColor, 'w200':true}"
                                                        disabled @on-blur="checkCarDifferPrice"
                                                    />
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem label="车款差额">
                                                    <Input type="text" v-model="this.carDifferPrice"
                                                           :class="{'color_red':hasColor, 'w200':true}"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col span="8">
                                                <FormItem label="发票类型" prop="invoiceType" :rules="[ {required: this.fpIsLaterSupply, message: '发票类型不能为空', trigger: 'blur'} ]">
                                                    <Select v-model="invoiceDiscern.invoiceClass" disabled>
                                                        <Option v-for="item in dataDic.invoiceClass" :value="item.value"  :key="item.value" :label="item.title">
                                                            {{ item.title }}
                                                        </Option>
                                                    </Select>
                                                </FormItem>
                                            </Col>
                                            <Col span="8" v-if = "invoiceCodeShow">
                                                <FormItem label="车辆发票代码"
                                                          :rules="{required: this.fpIsLaterSupply, message: '车辆发票代码不能为空', trigger: 'blur'}"
                                                          prop="invoiceCode">
                                                    <Input type="text" v-model="invoiceDiscern.invoiceCode"
                                                           @on-blur="checkNeedInvoiceCheck"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">

                                                <FormItem label="车辆发票号" prop="invoiceNo"
                                                          :rules="{required: this.fpIsLaterSupply, message: '车辆发票号码不能为空', trigger: 'blur'}">
                                                    <Input type="text" v-model="invoiceDiscern.invoiceNo"
                                                           @on-blur="checkNeedInvoiceCheck" disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">

                                                <FormItem label="车辆开票日期" prop="invoiceDate"
                                                          :rules="{ type:'date',required: fpIsLaterSupply,message: '车辆开票日期不能为空', trigger: 'change'}">
                                                    <DatePicker type="date"
                                                                v-model="invoiceDiscern.invoiceDate"
                                                                @on-change="checkNeedInvoiceCheck"
                                                                format="yyyy-MM-dd"
                                                                disabled :editable="false"></DatePicker>
                                                </FormItem>
                                            </Col>

                                            <!--                                            <FormItem  label="增值税额" prop="invoiceTaxAmt" :rules="{required: fpIsLaterSupply ,type:'number', message: '增值税额不能为空', trigger: 'blur'}">
                                                                                            <NumberInput class="widthMoney"
                                                                                                v-model="invoiceDiscern.invoiceTaxAmt"
                                                                                                number
                                                                                                :formatter="value =>  currencyFormat(value,2)"
                                                                                                :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                                                disabled
                                                                                            />
                                                                                        </FormItem>-->
                                        </Row>
                                        <Row>
                                            <Col span="8">
                                                <FormItem label="发票税率(%)"
                                                          :rules="{required: this.fpIsLaterSupply, message: '发票税率(%)代码不能为空', trigger: 'blur'}"
                                                          prop="invoiceRate">
                                                    <Input v-model="invoiceDiscern.invoiceRate"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem label="不含税价" prop="excludingTaxAmt"
                                                          :rules="{required: fpIsLaterSupply,type:'number', message: '不含税价不能为空', trigger: 'blur'}">
                                                    <NumberInput class="widthMoney"
                                                                 v-model="invoiceDiscern.excludingTaxAmt"
                                                                 number
                                                                 @on-blur="checkNeedInvoiceCheck"
                                                                 :formatter="value =>  currencyFormat(value,2)"
                                                                 :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                 disabled
                                                    />
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem prop="invoiceAmt" label="总金额">
                                                    <Input type="text" v-model="invoiceDiscern.invoiceAmt"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col span="8">
                                                <FormItem prop="buyerName" label="购买方名称"
                                                          :rules="[{required: fpIsLaterSupply, message: '购买方名称不能为空', trigger: 'blur'}]">
                                                    <Input type="text" v-model="invoiceDiscern.buyerName"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem prop="buyerIdcardNo" label="购买方纳税人识别号"
                                                          :rules="[{required: fpIsLaterSupply, message: '购买方纳税人识别号不能为空', trigger: 'blur'}]">
                                                    <Input type="text" v-model="invoiceDiscern.buyerIdcardNo"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem prop="carVin" label="车架号/车辆识别代码"
                                                          :rules="[{required: fpIsLaterSupply, message: '车架号/车辆识别代码不能为空', trigger: 'blur'}]">
                                                    <Input type="text" v-model="invoiceDiscern.carVin"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col span="8">
                                                <FormItem prop="engineNo" label="发动机号"
                                                          :rules="[{required: fpIsLaterSupply, message: '发动机号不能为空', trigger: 'blur'}]">
                                                    <Input type="text" v-model="invoiceDiscern.engineNo"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                            <Col span="8">
                                                <FormItem prop="saleName" label="销售方名称">
                                                    <Input type="text" v-model="invoiceDiscern.saleName"
                                                           disabled/>
                                                </FormItem>
                                            </Col>
                                        </Row>
                                        <Row >
                                            <div style="width: 130px;" align="center" v-if="isInvoiceSameDiv">
                                                <span v-if="isInvoiceSame" style="color: green;font-size: 14px;">发票匹配一致</span>
                                                <span v-else style="color: red;font-size: 14px;">发票匹配不一致</span>
                                            </div>
                                            <div class="form-box" v-if="this.invoiceCheckBody.length > 0">
                                                <Table border :row-class-name="rowClassName" :columns="invoiceVerification"
                                                       :data="this.invoiceCheckBody">
                                                    <template slot-scope="{ row, index }" slot="state">
                                                        <div>
                                                            <div v-if="row.state=='0'">
                                                                正常
                                                            </div>
                                                            <div v-if="row.state=='2'">
                                                                作废
                                                            </div>
                                                        </div>
                                                    </template>
                                                </Table>
                                            </div>
                                        </Row>
                                         </Form>
                                    </div>
                                </Panel>

                                <Panel name="7" v-if="isViewDecorationInvoice">
                                    <span>精品装潢发票信息</span>
                                    <span @click="saveCarInvoice" class="leftRight" v-show="!isDisabled" @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                         <Form :model="decorationInvoiceForm" ref="decorationInvoiceForm"  :label-width="160" :rules="invoiceValidate" inline>
                                        <div class="form-box">
                                            <Row>
                                                <FormItem prop="invoiceUnit" :rules="{required: true, message: '车辆开票单位不能为空', trigger: 'blur'}" label="车辆开票单位">
                                                    <Input type="text" v-model="decorationInvoiceForm.invoiceUnit"
                                                           :disabled="isDisabled"/>
                                                </FormItem>
                                                <FormItem prop="invoiceNo" label="发票号码">
                                                    <Input type="text" v-model="decorationInvoiceForm.invoiceNo"
                                                           :disabled="isDisabled"/>
                                                </FormItem>
                                                <FormItem prop="invoiceCode" label="发票代码">
                                                    <Input type="text" v-model="decorationInvoiceForm.invoiceCode"
                                                           :disabled="isDisabled"/>
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem  label="开票日期" prop="invoiceDate" :rules="{ type:'date',required: true,message: '开票日期不能为空', trigger: 'change'}" >
                                                    <DatePicker type="date" v-model="decorationInvoiceForm.invoiceDate"
                                                                format="yyyy-MM-dd"
                                                                :disabled="isDisabled" :editable="false"></DatePicker>
                                                </FormItem>
                                                <FormItem prop="invoiceRate" :rules="{required: true, message: '车辆发票税率(%)不能为空', trigger: 'blur'}"  label="发票税率(%)">
                                                    <Input type="text" v-model="decorationInvoiceForm.invoiceRate"
                                                           :disabled="isDisabled"/>
                                                </FormItem>
                                                <FormItem prop="excludingTaxAmt" :rules="{required: true,type:'number', message: '车辆不含税价不能为空', trigger: 'blur'}"  label="不含税价">
                                                    <NumberInput
                                                        v-model="decorationInvoiceForm.excludingTaxAmt"
                                                        number
                                                        :formatter="value =>  currencyFormat(value,2)"
                                                        :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                        :class="{'color_red':pageStyle.carDifferPriceColor, 'w200':true}"
                                                        :disabled="isDisabled"
                                                    />
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem prop="invoiceAmt"  :rules="{required: true,type:'number', message: '车辆发票金额不能为空', trigger: 'blur'}" label="发票金额">
                                                    <NumberInput
                                                        v-model="decorationInvoiceForm.invoiceAmt"
                                                        number
                                                        :formatter="value =>  currencyFormat(value,2)"
                                                        :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                        :class="{'color_red':pageStyle.carDifferPriceColor, 'w200':true}"
                                                        :disabled="isDisabled"
                                                    />
                                                </FormItem>
                                            </Row>
                                        </div>
                                          </Form>
                                    </div>
                                </Panel>
<!--                                 <Panel name="8" >
                                    <span>GPS信息</span>
                                    <span class="panel-head-right" @click.stop>
                                        <Button type="primary" @click="saveGpsInfo" :loading="distributeLoading" v-show="!isView" :disabled="isDisabledGpsSave"
                                                style="margin-right: 5px">GPS保存
                                        </Button>
&lt;!&ndash;                                        <Button type="primary" @click="distribute" :loading="distributeLoading"
                                                style="margin-right: 5px"
                                                :disabled="isGPSDistributeLeaflets">GPS派单
                                        </Button>
                                        <Button type="primary" @click="locationbute" :loading="distributeLoading" :disabled="isDisabledLocationbute"
                                                style="margin-right: 5px">GPS定位
                                        </Button>&ndash;&gt;
                                    </span>
                                    <div class="panel-content" slot="content">
                                        <Form :model="gpsInfo" :label-width="160" ref="gpsInfo" :rules="gpsInfoFV" inline>
                                        <div class="form-box">
                                            <FormItem label="GPS供应商" prop="gpsSupplier" :rules="{required: true, message: 'GPS供应商不能为空', trigger: 'change'}">
                                                <Select v-model="gpsInfo.gpsSupplier" filterable clearable :disabled="isGPSDistribute">
&lt;!&ndash;                                                        @on-change="changeGpsSupplier" &ndash;&gt;
                                                    <Option v-for="item in dataDic.gpsSupplier" :value="item.value"
                                                            :key="item.value" :label="item.title">
                                                        {{ item.title }}
                                                    </Option>
                                                </Select>
                                            </FormItem>
&lt;!&ndash;                                            <FormItem label="GPS型号"v-show="false">&ndash;&gt;
&lt;!&ndash;                                                <Input type="text" v-model="gpsFullName" disabled/>&ndash;&gt;
&lt;!&ndash;                                            </FormItem>&ndash;&gt;
&lt;!&ndash;                                            <FormItem label="租赁期限" prop="loanTerm" v-show="false">&ndash;&gt;
&lt;!&ndash;                                                <Input type="text" v-model="gpsInfo.loanTerm" disabled/>&ndash;&gt;
&lt;!&ndash;                                            </FormItem>&ndash;&gt;
&lt;!&ndash;                                            <FormItem label="现场联系人" prop="siteContact" :rules="{required: gpsInfo.gpsSupplier != '0', message: '现场联系人不能为空', trigger: 'blur'}" >&ndash;&gt;
&lt;!&ndash;                                                <Input type="text" v-model="gpsInfo.siteContact"&ndash;&gt;
&lt;!&ndash;                                                       :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')"/>&ndash;&gt;
&lt;!&ndash;                                            </FormItem>&ndash;&gt;
&lt;!&ndash;                                            <FormItem label="联系电话" prop="contactPhone" :rules="[{required: gpsInfo.gpsSupplier != '0', message: '联系电话不能为空', trigger: 'blur'}]" >&ndash;&gt;
&lt;!&ndash;                                                <Input type="text" v-model="gpsInfo.contactPhone"&ndash;&gt;
&lt;!&ndash;                                                       maxlength="11" :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')"/>&ndash;&gt;
&lt;!&ndash;                                            </FormItem>&ndash;&gt;
&lt;!&ndash;                                            <FormItem label="安装时间" prop="preInstallTime" :rules="{ type: 'date', required: gpsInfo.gpsSupplier != '0', message: '请选择安装时间', trigger: 'change' }">&ndash;&gt;
&lt;!&ndash;                                                <DatePicker type="date" v-model="gpsInfo.preInstallTime"&ndash;&gt;
&lt;!&ndash;                                                            format="yyyy-MM-dd"&ndash;&gt;
&lt;!&ndash;                                                            :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')" :editable="false"&ndash;&gt;
&lt;!&ndash;                                                            @on-change="getDate()"></DatePicker>&ndash;&gt;
&lt;!&ndash;                                            </FormItem>&ndash;&gt;
&lt;!&ndash;&lt;!&ndash;                                            <Row>&ndash;&gt;&ndash;&gt;
&lt;!&ndash;                                                <FormItem label="安装地址" prop="installAddressTemp"&ndash;&gt;
&lt;!&ndash;                                                          class="inline-block show_cascader_detail" :rules="{ type: 'date', required: gpsInfo.gpsSupplier != '0', message: '安装地址不能为空', trigger: 'change', type: 'array'}">&ndash;&gt;
&lt;!&ndash;                                                    <rui-region&ndash;&gt;
&lt;!&ndash;                                                        v-model="gpsInfo.installAddressTemp"&ndash;&gt;
&lt;!&ndash;                                                        style="width: 210px"&ndash;&gt;
&lt;!&ndash;                                                        :town="false"&ndash;&gt;
&lt;!&ndash;                                                        :road="false"&ndash;&gt;
&lt;!&ndash;                                                        :detail="false"&ndash;&gt;
&lt;!&ndash;                                                        :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')"&ndash;&gt;
&lt;!&ndash;                                                    />&ndash;&gt;
&lt;!&ndash;                                                </FormItem>&ndash;&gt;
&lt;!&ndash;                                                <FormItem label="详细地址" prop="installAddress"  class="inline-block show_cascader_detail" :rules="{required: gpsInfo.gpsSupplier != '0', message: '详细地址不能为空', trigger: 'blur'}">&ndash;&gt;
&lt;!&ndash;                                                    <Input type="text" v-model="gpsInfo.installAddress" :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')"/>&ndash;&gt;
&lt;!&ndash;                                                </FormItem>&ndash;&gt;
&lt;!&ndash;                                                <FormItem label="备注" prop="remarks">&ndash;&gt;
&lt;!&ndash;                                                    <Input type="text" v-model="gpsInfo.remarks"&ndash;&gt;
&lt;!&ndash;                                                            :disabled="isGPSDistribute || (gpsInfo.gpsSupplier == '0')"/>&ndash;&gt;
&lt;!&ndash;                                                </FormItem>&ndash;&gt;
&lt;!&ndash;                                            </Row>&ndash;&gt;
                                            <Row>
                                                <FormItem label="GPS设备号(无线)"  v-if="wirelessViewGps">
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.isNotFlag" disabled/>
                                                </FormItem>
                                                <FormItem label="GPS在线状态(无线)" v-if="wirelessViewGps" >
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.isNotsbcStatus" disabled/>
                                                </FormItem>
                                                <FormItem label="GPS定位地址(无线)" v-if="wirelessViewGps" >
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.locationAdd" disabled/>
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="GPS设备号(有线)" v-if="wireViewGps" >
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.isFlag" disabled/>
                                                </FormItem>
                                                <FormItem label="GPS在线状态(有线)" v-if="wireViewGps">
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.sbcstatus" disabled/>
                                                </FormItem>
                                                <FormItem label="GPS定位地址(有线)" v-if="wireViewGps" >
                                                    <Input type="text" v-model="getAllEqPositionsReqDTO.locationAdd" disabled/>
                                                </FormItem>
                                            </Row>
                                            <Row v-if="isChannelBelong" >
                                                <FormItem label="GPS定位" >
                                                    <Select v-model="gpsInfo.locationStatus"  :disabled="isDisabled">
                                                            <Option v-for="item in dataDic.isDefault" :value="item.value" :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                </FormItem>
                                            </Row>
                                        </div>
                                          </Form>
                                    </div>
                                </Panel>-->


                                <Panel name="11" v-show="isShowInsureInfo">
                                    <span>保险信息(商业险)</span>
                                    <span @click="saveBusinessInsurance" class="leftRight"  v-show="!isView" @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                        <Form :model="insuranceInfoBusiness"  ref="insuranceInfoBusiness" :label-width="160" :rules="insuranceInfoBusinesse" inline>
                                        <div class="form-box">
                                            <Row v-if="isChannelBelong">
                                                <Col span="8">
                                                    <FormItem label="是否后补商业险">
                                                        <Select v-model="insuranceInfoBusiness.isLaterSupply"
                                                                @on-change="insuranceInfoBusinessInfoIsLaterSupply" :disabled="isDisabled">
                                                            <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                                    :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <FormItem label="投保方式" prop="insuranceMode"  :rules="{required:businessIsLaterSupply, message: '请选择投保方式', trigger: 'change'}">
                                                    <Select v-model="insuranceInfoBusiness.insuranceMode" clearable
                                                            :disabled="isDisabledBusiness">
                                                        <Option value="electronics">电子保单</Option>
                                                        &lt;!&ndash;<Option value="paper">纸质保单</Option>&ndash;&gt;
                                                    </Select>
                                                </FormItem>
                                                <FormItem label="商业险保单号" prop="insuranceNo" >
                                                    <Input type="text" v-model="insuranceInfoBusiness.insuranceNo"
                                                           :disabled="isDisabledBusiness"/>
                                                </FormItem>
                                                <FormItem label="商业险保费" prop="insuranceAmt" :rules="{required: businessIsLaterSupply,type:'number', message: '商业险保费不能为空', trigger: 'blur'}">
                                                    <NumberInput class="widthMoney"
                                                                 v-model="insuranceInfoBusiness.insuranceAmt"
                                                                 number
                                                                 :min="0"
                                                                 :formatter="value =>  currencyFormat(value,2)"
                                                                 :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                 :disabled="isDisabledBusiness"
                                                    />
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="商业险生效日" prop="insuranceStartTime" :rules="{ type:'date',required: businessIsLaterSupply,message: '商业险生效日不能为空', trigger: 'change'}">
                                                    <DatePicker type="date"
                                                                v-model="insuranceInfoBusiness.insuranceStartTime"
                                                                @on-change="businessDate" format="yyyy-MM-dd"
                                                                :disabled="isDisabledBusiness" transfer :editable="false"></DatePicker>
                                                </FormItem>
                                                <FormItem label="商业险失效日" prop="insuranceEndTime" :rules="{ type:'date',required: businessIsLaterSupply,message: '商业险失效日不能为空', trigger: 'change'}">
                                                    <DatePicker type="date"
                                                                v-model="insuranceInfoBusiness.insuranceEndTime"
                                                                format="yyyy-MM-dd"
                                                                :disabled="isDisabledBusiness" transfer :editable="false"></DatePicker>
                                                </FormItem>
                                                <FormItem label="车损险保额" prop="insuranceMoney" :rules="{required: businessIsLaterSupply,type:'number', message: '车损险保额不能为空', trigger: 'blur'}">
                                                    <NumberInput class="widthMoney"
                                                                 v-model="insuranceInfoBusiness.insuranceMoney"
                                                                 number
                                                                 :formatter="value =>  currencyFormat(value,2)"
                                                                 :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                 :disabled="isDisabledBusiness"
                                                    />
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="三者险" prop="thirdInsurance" :rules="{required: businessIsLaterSupply,type:'number', message: '三者险不能为空', trigger: 'blur'}">
                                                    <NumberInput class="widthMoney"
                                                                 number
                                                                 v-model="insuranceInfoBusiness.thirdInsurance"
                                                                 :formatter="value =>  currencyFormat(value,2)"
                                                                 :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                                 :disabled="isDisabledBusiness"
                                                    />
                                                </FormItem>
                                            </Row>
                                        </div>
                                          </Form>
                                    </div>
                                </Panel>
                                 <Panel name="12" v-show="isShowInsureInfo">
                                    <span>保险信息(交强险)</span>
                                    <span @click="saveCompulsory" class="leftRight" v-show="!isView" @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                        <Form :model="insuranceInfo" ref="insuranceInfo" :label-width="160" :rules="insuranceInfos" inline>
                                        <div class="form-box">
                                            <Row v-if="isChannelBelong">
                                                <Col span="8">
                                                <FormItem label="是否后补交强险">
                                                        <Select v-model="insuranceInfo.isLaterSupply"
                                                                @on-change="insuranceInfoIsLaterSupply" :disabled="isDisabled">
                                                            <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                                    :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <FormItem label="交强险保单号" prop="insuranceNo">
                                                    <Input type="text" v-model="insuranceInfo.insuranceNo"
                                                           :disabled="isDisabledBusiness"/>
                                                </FormItem>
                                                <FormItem label="交强险保费" prop="insuranceAmt" :rules="{required: jqxIsLaterSupply,type:'number', message: '交强险保费不能为空', trigger: 'blur'}">
                                                    <NumberInput
                                                        v-model="insuranceInfo.insuranceAmt"
                                                        number
                                                        :min="0"
                                                        :formatter="value =>  currencyFormat(value,2)"
                                                        :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                                                        :class="{'color_red':pageStyle.carDifferPriceColor, 'w200':jqxIsLaterSupply}"
                                                        :disabled="isDisabledBusiness"
                                                    />
                                                </FormItem>
                                                <FormItem label="交强险生效日" prop="insuranceStartTime" :rules="{ type:'date',required: jqxIsLaterSupply,message: '交强险生效日不能为空', trigger: 'change'}">
                                                    <DatePicker type="date" v-model="insuranceInfo.insuranceStartTime"
                                                                @on-change="strongDate" format="yyyy-MM-dd" :disabled="isDisabledBusiness"
                                                                :editable="false"></DatePicker>
                                                </FormItem>
                                            </Row>
                                            <Row>
                                                <FormItem label="交强险失效日" prop="insuranceEndTime" :rules="{ type:'date',required: jqxIsLaterSupply,message: '交强险失效日不能为空', trigger: 'change'}">
                                                    <DatePicker type="date" v-model="insuranceInfo.insuranceEndTime"
                                                                format="yyyy-MM-dd" :disabled="isDisabledBusiness" :editable="false"></DatePicker>
                                                </FormItem>
                                            </Row>
                                        </div>
                                           </Form>
                                    </div>
                                </Panel>
                                <Panel name="6" v-if="isChannelBelong">
                                    <span>申请开票信息</span>
                                    <span @click="saveReceipt" class="leftRight" v-show="!isDisabled"  @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                         <Form ref="InvoiceApplication" :model="InvoiceApplication" :label-width="160" :rules="InvoiceApplicationValidate" inline>
                                            <Row>
                                                <Col span="8">
                                                    <FormItem label="是否开票" >
                                                        <Select v-model="InvoiceApplication.invoiceFlag"
                                                                @on-change="InvoiceApplicationFun" :disabled="isDisabled  || invoiceDisabled">
                                                            <Option v-for="item in dataDic.isDefault" :value="item.value"
                                                                    :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                </FormItem>
                                            </Col>
                                             <Col span="8">
                                                <FormItem label="发票抬头" >
                                                     <Input type="text" v-model="InvoiceApplication.invoiceTitle" disabled/>
                                                </FormItem>
                                                </Col>

                                             <Col span="8">
                                                <FormItem  label="纳税人识别号">
                                                    <Input type="text" v-model="InvoiceApplication.taxpayerNum"  disabled/>
                                                </FormItem>
                                                   </Col>
                                            </Row>
                                            <Row>
                                             <Col span="8">
                                                <FormItem prop="bankName" :rules="{required: !mailRequired && invoiceDisabledrequired, message: '开户银行不能为空', trigger: 'blur'}" label="开户银行">
                                                    <Input type="text" v-model="InvoiceApplication.bankName"
                                                        :disabled="isDisabled"/>
                                                </FormItem>
                                                   </Col>
                                             <Col span="8">
                                                <FormItem prop="bankAccount" :rules="{required: !mailRequired && invoiceDisabledrequired, message: '银行账号不能为空', trigger: 'blur'}" label="银行账号">
                                                    <Input type="text" v-model="InvoiceApplication.bankAccount"
                                                        :disabled="isDisabled"/>
                                                </FormItem>
                                             </Col>
                                             <Col span="8">
                                                <FormItem label="电子邮箱"
                                                :rules='[{ required:mailRequired && invoiceDisabledrequired, message: "请输入邮箱地址" },{ type: "email", message: "邮箱格式不正确" }]'
                                                 prop="email">
                                                     <Input type="text" v-model="InvoiceApplication.email" :disabled="isDisabled"/>
                                                </FormItem>
                                                </Col>
                                             </Row>
                                            <Row>
                                            <Col span="16" class="addressBox">
                                                <FormItem label="企业地址" prop="businessAddressBak">
                                                    <rui-region
                                                            v-model="InvoiceApplication.businessAddressBak"
                                                            :town="false"
                                                            :road="false"
                                                            :disabled="isDisabled"
                                                            class="tw100"
                                                        />
                                                </FormItem>
                                                </Col>
                                                <Col span="8">
                                                    <FormItem prop="businessPhone" :rules="[{required:!mailRequired && invoiceDisabledrequired, message: '企业电话不能为空', trigger: 'blur'}]" label="企业电话">
                                                        <Input type="text" v-model="InvoiceApplication.businessPhone" :disabled="isDisabled"/>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                            <Row>
                                             <Col span="8">
                                                <FormItem label="收件人">
                                                    <Input type="text" v-model="InvoiceApplication.recipient"
                                                        :disabled="isDisabled"/>
                                                </FormItem>
                                                   </Col>
                                             <Col span="8">
                                                <FormItem   label="收件人联系方式">
                                                    <Input type="text" v-model="InvoiceApplication.recipientContact"
                                                        :disabled="isDisabled"/>
                                                </FormItem>
                                             </Col>
                                             <Col span="8">
                                                <FormItem label="邮政编号">
                                                    <Input  v-model="InvoiceApplication.postalCode"  type="text" maxlength="6" :disabled="isDisabled" />
                                                </FormItem>
                                                </Col>
                                             </Row>
                                              <Row>
                                                <Col span="16" class="addressBox">
                                                    <FormItem label="邮寄地址">
                                                        <rui-region
                                                            v-model="InvoiceApplication.sendAddressBak"
                                                            :town="false"
                                                            :road="false"
                                                            :disabled="isDisabled"
                                                            class="tw100"
                                                        />
                                                    </FormItem>
                                                </Col>
                                             </Row>
                                             </Form>
                                    </div>
                                </Panel>
                                 <!-- <Panel name="16">
                                    <span>起租条件</span>
                                    <div class="panel-content" slot="content">
                                        <Row>
                                            <Col span="24">
                                                <Input v-model="formEntry.orderInfo.rentRemark" placeholder="请输入备注" type="textarea"   style="width:100%"  disabled/>
                                            </Col>
                                        </Row>
                                    </div>
                                </Panel> -->
                                <Panel name="19" v-if="isShowIsDataPostStatus && isAuctionCar">
                                    <span>资料后置开启状态<span style="color: red;">（选择后请点击右侧保存按钮）</span></span>
                                    <span @click.stop="saveIsDataPostStatus" class="leftRight" v-show="!isDisabled" >保存</span>
                                    <div class="panel-content" slot="content">

                                        <Modal v-model="isOpenStatus" width="400" :closable="false" :mask-closable="false" title="选择资料后置开启">
                                            <div>
                                                <p>需要阅读{{ timeLeft }}秒后，才能进行操作：</p>
                                                <p>{{ saveDataPostWarnContent }}</p>
                                            </div>

                                           <div slot="footer">
                                            <Button @click="qxOpenStatus" :disabled=this.buttonFlag>不同意</Button>
                                            <Button type="primary" @click.stop="saveIsDataPostStatus" :disabled=this.buttonFlag>同意</Button>
                                           </div>
                                        </Modal>
                                        <Form ref="formIsDataPostStatus" :model="formEntry.contractInfo" :label-width="160" :rules="isDataPostStatusValidate" inline>
                                            <Row>
                                                <Col span="8">
                                                    <FormItem label="资料后置是否开启" prop="isDataPostStatus">
                                                        <Select v-model="formEntry.contractInfo.isDataPostStatus" :disabled="isDisabled" @on-change="openStatus">
                                                            <Option v-for="item in dataDic.isDataPost" :value="item.value"  :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                                <Col span="8">
                                                    <FormItem  label="资料后置超时日期（天）" :rules="{type:'number', message: '资料后置超时日期（天）只能录入数字', trigger: 'change'}">
                                                        <Input type="text" v-model="formEntry.contractInfo.overDay" :disabled="true"/>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                        </Form>
                                    </div>
                                </Panel>
                                <Panel name="19" v-if="isShowAuthorizeWay">
                                    <span>合同签署方式<span style="color: red;">（注意修改且保存签署方式后，需要重新签署签约合同）</span></span>
                                    <span @click="saveAuthorizeWay" class="leftRight" v-show="!isDisabled"  @click.stop>保存</span>
                                    <div class="panel-content" slot="content">
                                        <Form ref="formAuthorizeWay" :model="formEntry.contractInfo" :label-width="160" :rules="authorizeWayRuleValidate" inline>
                                            <Row>
                                                <Col span="8">
                                                    <FormItem label="合同签署方式" prop="authorizeWay">
                                                        <Select v-model="formEntry.contractInfo.authorizeWay" :disabled="isDisabled" >
                                                            <Option v-for="item in dataDic.contractSignType" :value="item.value"  :key="item.value" :label="item.title">
                                                                {{ item.title }}
                                                            </Option>
                                                        </Select>
                                                    </FormItem>
                                                </Col>
                                            </Row>
                                        </Form>
                                    </div>
                                </Panel>
                                <Panel name="17">
                                    <span>备注</span>
<!--                                    update yekaixuan 2022-09-30-->
<!--                                    <span @click="addRemark" class="leftRight" v-show="!isDisabled"  @click.stop>新增</span>-->
                                    <div class="panel-content" slot="content">
                                        <Form ref="oldRemarks" :model="oldRemarks" :label-width="160"  v-if="oldRemarks.length>0">
                                            <div v-for="(item,index) in oldRemarks" :key="index">
                                                <Col span="16">
                                                    <FormItem label="备注" :prop="'remark'+index" >
                                                        <Input :value="item" placeholder="请输入备注" type="textarea"
                                                               style="width:800px"
                                                               :disabled=true />
                                                    </FormItem>
                                                </Col>
                                            </div>
                                        </Form>
                                        <Form ref="formEntry" :model="formEntry" :rules="formRules" :label-width="160" inline>
                                            <div class="form-box">
                                                <FormItem label="备注" v-if="remarkShow">
                                                    <Input v-model="formEntry.contractInfo.remarks" placeholder="请输入备注" type="textarea"
                                                           style="width:800px"
                                                           maxlength="256"
                                                           :disabled="isDisabled"/>
                                                </FormItem>
                                            </div>
                                        </Form>
                                    </div>
                                </Panel>
                            </Collapse>
                    </div>
                </TabPane>

                <TabPane label="影像上传" name="uploadInfo">
                    <div class="tab" v-show="openTabs === 'uploadInfo'">
                        <div style="width: 100%;height: 88%">
                            <file-operation ref="fileOperation"
                                :uploadParam="uploadParam" :isInt="isInt" :path="path" :read-only="fileRead">
                            </file-operation>
                        </div>
                    </div>
                </TabPane>
            </Tabs>
            <div class="tabsBox" style="padding-top: 16px;">
            </div>
            <FixedNav :showSave="showType!=1" @save="save" :showDownloadFile="isDownLoad && !fileDownLoad" :isChannelBelong="isChannelBelong"
                      @downloadFile="downloadFile"  @noticeRent="noticeRent" :hasContract="hasContract"  :saveFlag="saveFlag" :saveLoading="saveLoading" v-show="!isDisabled"
                      :applyNo="applyNo" :contractNo="contractNo" :belongingCapital="row.belongingCapital" :diffType="diffType"
                      @signClick="signClick" ref="FixedNav"/>
            <div class="footer">
                <div class="footer_content content-box">
                    <Modal v-model="isShowWarn" width="400" :closable="false" :mask-closable="false" title="提醒">
                        <div>
                            <p>{{ submitWarnContent }}</p>
                        </div>

                        <div slot="footer">
                            <Button @click="closeShow">取消</Button>
                            <Button type="primary" @click.stop="checkFile">确认</Button>
                        </div>
                    </Modal>
                    <div v-show="openTabs === 'baseInfo'">
                        <Button type="primary" @click="save('forever')" v-show="!isView" size="default" :disabled="saveLoading">下一步 影像上传
                        </Button>
                        <Button :loading="saveLoading" type="primary" size="default" v-show="!isView"
                                @click="checkFileMethed">提交
                        </Button>
                    </div>
                    <div v-show="openTabs === 'uploadInfo'">
                        <Button :loading="saveLoading" type="primary" size="default" v-show="!isView"
                                @click="checkFileMethed">提交
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        <Modal v-model="templateModel" width="60%" :closable="false">
            <div style="display: flex;flex-direction: row;justify-content: space-between;">
                 <div style="color:#17233d;font-weight:500 ;">文件模版</div>
                 <Button type="primary" @click="updateFile">重新生成</Button>
             </div>
            <div class="roleCheckGroup">
                <div>
                    <Button class="allBtn">
                        <Checkbox
                            :indeterminate="indeterminate"
                            :value="checkAll"
                            class="checkAll"
                            @click.prevent.native="handleCheckAll">
                            全部
                        </Checkbox>
                    </Button>
<!--                    <Button @click="downloadBatchFile(fileList)">批量下载</Button>-->
                </div>
                <div>
                    <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                        <Row v-for="(item,index) in fileList" :key="index" style="padding: 5px 0px">
                            <Col span="11" offset="1">
                                <Checkbox
                                    :label="item.attachmentCode"
                                    :key="item.attachmentCode"
                                    style="margin-top: 3px;" @click="getAttachmentData">
                                    <span>{{ item.fileName }}</span>
                                </Checkbox>
                            </Col>
                            <Col span="4">
                                <span>{{ item.createTime }}</span>
                            </Col>
                            <Col span="3">
                                <!-- <Button>打印预览</Button> -->
                                <Button @click="pdfPreview(item)">预览</Button>
                            </Col>
                            <Col span="4">
                                <Button @click="downloadSingleFile(item)">下载</Button>
                            </Col>
                        </Row>
                    </CheckboxGroup>
                </div>
            </div>
            <Modal width="25%" v-model="isShowConfirm">
                 <div style="font-size: 16px;color:#17233d;text-align: center;" >
                     重新生成合同会导致已电子签约的合同失效,请确认!
                 </div>
                 <div slot="footer" style="display: flex;flex-direction: row;justify-content: space-between;">
                      <Button type="text" @click="cancleModalClick">取消</Button>
                      <Button type="text" @click="confirmModalClick">确认</Button>
                 </div>
            </Modal>

        </Modal>
        <div v-if="isShowAddModel">
                <selected-item
                @addCloseModal="addCloseModal"
                @updataPayee="updataPayee"
                :applyNo="applyNo"
                :itemCollectionType='itemCollectionType'
                :newCarModer="true"
                :dataDic="dataDic"
            ></selected-item>
        </div>
        <div v-if="invoiceIdentification" >
            <plantInvoiceIdentification :isChannelBelong="isChannelBelong" :invoiceIdentification="invoiceIdentification" :licenseProvinceCity=licenseProvinceCity :plantDetails="carDetails"  @closeInvoiceIdentification="closeInvoiceIdentification"/>
        </div>
        <Modal v-model="isShowCarModel" width="1250" :closable="false">
            <car-screenmodal :carData="carPeopleInfos"></car-screenmodal>
        </Modal>
        <Modal v-model="invoiceModel" width="1200px"  :closable="false" :title="'发票信息维护'">
            <invoiceDiscern ref="invoiceDiscernFrom"   :dataDic="dataDic" :isChannelBelong="isChannelBelong"  :formEntry="formEntry" :invoiceType="'03'" :applyNo="applyNo"
            @invoicediscernSaveSuccess="invoicediscernSaveSuccess" />
            <div slot="footer">
                <Button type="text" @click="cancleInvoice">关闭</Button>
                <Button type="primary"  @click="saveInvoiceInfo">保存</Button>
            </div>
        </Modal>
        <!-- 留言模块 -->
        <div class="leaveComments">
            <div class="fade_commonent" :class="moveComment?'noWidth':'defaultWidth'">
                <div class="detail_comment" :class="moveComment?'hiddenClass':'defaultClass'">
                    <h2>留言
                        <Icon type="ios-close" @click="closeComment"/>
                    </h2>
                    <ul style="list-style-type: none;">
                        <li v-for="(item,index) in remindList" :key="index" style="min-height: 86px;">
                            <div>
                                <div class="inline-block"  :class="{ self: item.isSelf==='1'}">
                                    <div v-if="item.isSelf!=='1'" style="text-align: left;">
                                        <span>审批人员</span> {{item.createTime}}
                                    </div>

                                    <div v-else style="text-align: right">
                                        {{item.createTime}} <span>我&nbsp&nbsp&nbsp&nbsp</span>
                                    </div>
                                    <p :class="item.isSelf!=='1'?'other':'self'">
                                        {{item.backWords}}
                                    </p>
                                </div>
                            </div>
                        </li>
                    </ul>

                    <div>
                        <Input  type="text"   v-model="msgContent"/>
                        <Button type="primary" class="my-button"   style="padding-top: -10px" @click="savaRemindDetails">&nbsp&nbsp;发&nbsp;&nbsp;&nbsp;送&nbsp;&nbsp;</Button>
                    </div>
                </div>
            </div>
            <div class="tapImg" v-if="remindList.length>0">
                <img src="../../../../assets/img/msg.png" alt="" @click="tagImage">
            </div>
        </div>
    </div>
</template>
<script>
import RuiSelectPage from "_p/basic/pages/contract/common/selectPage/RuiSelectPage"
import NumberInput from '_c/rui-auto/rewrite/number-input'
import {currencyFormat} from "@/libs/tools";
import AfsUtils from '@/libs/afs-utils/index'
import plantInvoiceIdentification from "./plant-invoice-identification";
import  carScreenmodal from './carScreenmodal'
import invoiceDiscern from "../invoice/invoice-discern"
import depositInfo from "../deposit/deposit-info"
import { getInvoice } from "_p/afs-apply/api/apply-contract/contract/invoice/invoice";
import {
    getSignBanknameBynum,
    distribute,
    getAccountPerson,
    getEntryPageInfo,
    getGpsSupplier,
    getLendingMode,
    loanApplySubmitInfo,
    saveToBackend,
    getdrawerParty,
    doVerify,
    signBank,
    getIsFirstMortgage,
    getCompareInfo,
    getChannelInfoByApplyNo,
    getSignBankType,
    carCertificateDiscern,
    carModelScreen,
    invoiceTificateDiscern,
    invoiceCheck,
    locationbute,
    saveGpsInfo,
    saveInsurance,
    saveApplyContractInfoPayee,
    saveCarInvoice,
    saveCarDetails,
    submitVINCarModelRecheck,
    saveReceipt,
    savePayee,
    queryReceipt,
    queryPayee,
    queryCarScreenRecord,
    carModelScreening,
    saveBankInformation,
    queryApplyCarModelRecheck,
    sendMessage,
    checkMessage,
    saveBankCard,
    checkHasHistory,
    saveByHistory, removeAllTemplete, saveAuthorizeWay, saveIsDataPostStatus, saveApplyDataPostSaveRecord,
} from "_p/afs-apply/api/apply-contract/contract/entry/contract-entry";
import {
    getLocation,
    getContractRemindList,
    savaRemindDetails,
    getCurrentSysHolidayInfo
} from "_p/afs-apply/api/apply-report/personal/common";
import {formOperation} from "@/libs/utils/form-operation-util";
import {getByTypes} from "_p/basic/api/admin/datadic";
import {getDictDataByType} from "_p/basic/api/admin/datadic.js";
import LeaseFileUpload from '_c/file-upload/index';
import * as applyUtils from '_p/afs-apply/assets/js/utils.js'
import {getUri} from "@/libs/tools";
import {
    custColumnsHead,
    recordTableHead,
    specialTableHead,
    invoiceVerification
} from "_p/afs-apply/api/apply-contract/contract/entry/contract-entry-table-head";
import {deepClone} from '@/libs/utils/ObjectClone';
import basicinfo from "_p/afs-apply/pages/apply-report/common/apply-basic-info.vue";
import utils from "@/libs/util.js";
import {getChannelId} from "@/projects/afs-apply/api/register-mng/listRecord";
import {getFilterParams} from "../../../../api/apply-report/personal/common";
import FileOperation from "../../../../../basic/pages/image/upload-file/fileOperation";
import {
    contractBatchPrint,
    contractSignRelationQuery,
    downloadBatchFile,
    downloadSingleFile,
    getComAttach,
    checkHasContract,
    checkHasLeaseNotice,
    leaseNoticePrint, contractLetterBatchPrint, getLetterComAttach,
} from "../../../../api/contract/corporate/corporateTransferManagement";
import CardUpload from "@/components/identitycard-upload"
import {
    checkFile, uploadFile,
} from "_p/basic/api/image/image-upload";
import {getUserInfo} from "../../../../../basic/api/admin/user";
import FixedNav from './fixedNav'
import ImagePreview from "../../../../../basic/pages/image/file-audit/view/imagePreview";
import getUuid from "@/libs/afs-uuid";
import { number } from 'echarts/lib/export';
import SelectedItem from './components/selectedItem.vue'
import MaintenanceModel from "./components/maintenanceModel"
import Car300InfoPage from "./components/car300InfoPage"

import * as utilsAddress from '_p/afs-apply/assets/js/utils.js'
import ClientInfo from './clientInfo'
import {formatDate} from "_c/iview/components/date-picker/util";
import {getOrderInfo} from '_p/afs-apply/api/apply-report/personal/businessOption.js';
import {getCertificateCardOCR,getBankCardOCR} from "_p/afs-apply/api/apply-report/personal/customInfo";
import TextType from "_p/basic/pages/contract/voucher/voucher-rules/src/component/express/input/Text";
export default {
    components: {
        TextType,
        NumberInput,
        RuiSelectPage,
        FileOperation,
        FixedNav,
        'basicinfo': basicinfo,
        CardUpload,
        ImagePreview,
        SelectedItem,
        plantInvoiceIdentification,
        carScreenmodal,
        invoiceDiscern,
        ClientInfo,
        MaintenanceModel,
        Car300InfoPage,
        depositInfo
    },
    computed: {
        getDataFn: () => getdrawerParty,
        /*businessTypeStr: function () {
            for (let i = 0; i < this.dataDic.businessType.length; i++) {
                if (this.dataDic.businessType[i].value === this.formEntry.orderInfo.businessType) {
                    return this.dataDic.businessType[i].title;
                }
            }
        },
        operateWayStr: function () {
            for (let i = 0; i < this.dataDic.operateWay.length; i++) {
                if (this.dataDic.operateWay[i].value === this.formEntry.orderInfo.operateWay) {
                    return this.dataDic.operateWay[i].title;
                }
            }
        },
        affiliatedWayStr: function () {
            for (let i = 0; i < this.dataDic.affiliatedWay.length; i++) {
                if (this.dataDic.affiliatedWay[i].value === this.formEntry.orderInfo.affiliatedWay) {
                    return this.dataDic.affiliatedWay[i].title;
                }
            }
        },
        carPurposeStr: function () {
            for (let i = 0; i < this.dataDic.carPurpose.length; i++) {
                if (this.dataDic.carPurpose[i].value === this.formEntry.orderInfo.carPurpose) {
                    return this.dataDic.carPurpose[i].title;
                }
            }
        },*/
        modelNumComputed: {
            get: function () {
                return this.carDetails.fullModelNum;
            },
            set: function (val) {
                this.carDetails.fullModelNum = val;
            }
        },
        vinNoComputed: {
            get: function () {
                return this.formEntry.carDetails.carVin;
            },
            set: function (val) {
                this.formEntry.carDetails.carVin = val.toUpperCase();
            }
        },
        engineNoComputed: {
            get: function () {
                return this.formEntry.carDetails.engineNo;
            },
            set: function (val) {
                this.formEntry.carDetails.engineNo = val;
            }
        }
    },
    data() {
        let validatecontactPhone=(rule,value,callback)=>{
            var reg = /^[0-9]*$/
            if (value != "" && value != null) {
                if (!reg.test(value)) {
                    callback(new Error("请输入数字"))
                }
            }
            callback();
        }
        let isVinNoValiate = (rule, value, callback) => {
            if (value) {
                if (!applyUtils.isCharNum(value)) {
                    callback(new Error("格式为英文与数字"))
                    return;
                } else {
                    if (value.length != 17) {
                        callback(new Error("vin号位数为17位"))
                        return;
                    }
                }
            }
            callback()
        }
        let isSocValiate = (rule, value, callback) => {
            if (value) {
                if (!applyUtils.isMoreCharNum(value)) {
                    callback(new Error("格式为英文与数字或纯数字"))
                    return;
                }
            }
            callback()
        }
        let idCardValiate = (rule, value, callback) => {
            if (value) {
                if (value===this.certNoMain) {
                    callback(new Error("不能与承租人身份证相同"))
                    return;
                }else if(value===this.certNoBorrow){
                    callback(new Error("不能与共同承租人身份证相同"))
                    return;
                }else if(value===this.certNoCommon){
                    callback(new Error("不能与担保人身份证相同"))
                    return;
                }
            }
            callback()
        }
        return {
            invoiceCodeShow:true,
            bankPanelTitle:'银行卡信息',
            diffType:'details',
            isAuctionCar:true,
            isOpenStatus:false,
            buttonFlag:true,
            isShowWarn:false,
            timeLeft: 5, // 初始倒计时时间
            countdownActive: false, // 标记倒计时是否正在进行
            countdownInterval: null, // 存储定时器的引用
            msgContent:"",
            remarkShow:false,
            savePayeeFlag:false,
            paySplitFlag:'0',
            collectionTypeArray:{},
            itemCollectionType:'',
            orderInfoMoney:0,
            formEntryContractInfoReceivingId:"",
            contractInfoFormID:"",
            indexSelect:0,
            InvoiceApplicationValidate:{
                businessAddressBak:[]
            },
            invoiceDisabled:false,
            mailRequired:false,
            invoiceDisabledrequired:true,
            isShowAddModel:false,
            InvoiceApplication:{},
            contractInfoForm:[
                {
                    id:"",
                    applyNo:this.applyNo,
                    collectionType:'',
                    collectionAmount:null,
                    autoFlag:'1',
                    receiptAccount:'',
                    receiptId:"",
                    receiptBankCode:"",
                    delFlag:'0',
                },
            ],
            personalBack:true, //个人
            personalBackRequired:true,
            enterprise:false,  //企业
            enterpriseRequired:false,
            fpIsLaterSupply:true,//发票校验
            jqxIsLaterSupply:true,//交强险校验
            businessIsLaterSupply:true,//商业险校验
            isChannelBelong:false,//是否直营
            invoiceType:'',//发票类型
            row:{},//list返回数据
            isShowCarModel:false,//hong
            isAgreement:null,//hong
            invoiceIdentification:false,//hong
            carPeopleInfos:[],//hong
            licenseProvinceCity:[],
            decorationInvoiceForm:[],
            carDetails: {
                engineNo:'',
                carColor:'',
                carVin:'',
                gbCode:'',
                brandName:'',
                licensePlate:''
            },
            isShowConfirm:false,
            //读秒按钮展示
            isInvoiceUnitInit: false,//企业单位名称初始化
            fnArgv: {},//企业单位
            holidayConfig:false,
            invoiceCarValidate:{
            },
            invoiceValidate:{
                invoiceNo: [
                    { required: true, message: '发票号码不能为空', trigger: 'blur'},
                    { min: 8, max: 12, message: '长度在 8 到 12 个字符', trigger: ['blur', 'change'] },
                ],
                invoiceCode:[
                    { required: true, message: '发票代码不能为空', trigger: 'blur'},
                    { min: 8, max: 12, message: '长度在 8 到 12 个字符', trigger: ['blur', 'change'] },
                ],
            },

            certificateDiscern:{
                engineNo:''
            },//车辆OCR识别数据
            carFormDatas:{
                engineNo:''
            },//车辆OCR识别数据
            consistentModel:true,//车型甄别一致不一致
            certificateDiscernRule:{},
            insuranceInfoBusinesse:{},
            insuranceInfos:{},
            bankCarde:{},
            pageStyle:{
                carDifferPriceColor:""
            },//页面动态样式，所有样式都在这里配
            carDifferPrice: 0,//车款差额
            openPanel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10,11,12,13,14,15,16,17,18,19],//panel页
            openTabs: 'baseInfo',//默认为新增请款申请，主要区别在于提交按钮展示
            invoiceCheckBody:[],//发票查验数据
            tabsType: [
                {
                    value: "baseInfo",
                    title: '基本信息'
                },
                {
                    value: "uploadInfo",
                    title: '影像上传'
                },
            ],//tab页切换
            loading:false,
            treeLoading: false,//获取开票单位 树加载状态
            contractFrontType: "",//承租人 共同承租人 :frontType样式动态调整(影像信息)
            contractBlackType: "",//承租人 共同承租人 :blackType样式动态调整(影像信息)
            contractStatus: '',//01：待提交 02：已提交 03：审核中 04：审核通过 05：已退回 06：已取消 07：合同关闭
            isBankDisabled: false,//开户>显示隐藏配置，isBankDisabled||isView
            isZjBankDisabled:false,//显示隐藏中金支付渠道验证码
            isBankDisableds:false,//显示隐藏农行直连渠道验证码
            isCustDisabled: false,//:disabled 变更按钮，
            fileDownLoad: true,//:showDownloadFile="isDownLoad && !fileDownLoad"
            disabledCertEndDate: false,//:disabled 有效期变更，
            uniqueCodes:"",//:codes 图片
            isView:false,//开户>显示隐藏配置，isBankDisabled||isView
            busiNo:"",//合同号码
            belongNo:"",//合同号码
            access:"code",//图片定值
            path: "apply",//附件路径
            pcOrApp:"pc",//保存提交时 定值
            isupload: false,//是否展示影响信息
            backInfo: 0,//是否有退件记录
            isInt: false,//获取影像件：true，保存合同录入信息：false
            indeterminate: false,//文件列表 check全部
            fileRead: false,//是否只读
            checkAll: false,//文件模板 check全部
            gpsFullName: '',//gps型号
            businessType: '',//业务类型 01：新车 02：二手车
            checkAllGroup: [],//批量文件下载
            //witnessList: [],//见证人列表
            newSurplus: 0,//新车剩余
            oldSurplus: 0,//二手车剩余
            newOccupied: 0,//新车占用
            oldOccupied: 0,//二手车占用
            getAllEqPositionsReqDTO:{
                //GPS派单返回值
                isFlag:'', //有线设备号
                sbcstatus:'', //有线设备是否在线
                isNotFlag:'', //无线设备号
                isNotsbcStatus:'',//无线设备是否在线
                locationAdd:''//定位地址
            },
            fileList: [],//选中的批量文件
            uploadParam: {
                busiNo: "",
                belongNo: "",
                busiType: "loanApply",
                busiData: {}
            },
            applyNo: "",//申请编号
            isContractEntry: true,//已退回 isContractEntry等于true
            isViewGps: false,//是否展示GPS模块
            wirelessViewGps:false,//gps无线是否展示
            wireViewGps:false,//gps有线是否展示
            isViewInvoice:true,//发票信息是否展示
            isViewDecorationInvoice:false,//是否展示精品装潢发票模块
            sign: "signing",//签约放款
            uploadFileModal: false,//有合同信息=true
            templateModel: false,//文件模板
            isVinNoValiate: isVinNoValiate,//校验
            isSocValiate: isSocValiate,//校验
            idCardValiate: idCardValiate,//校验
            validatecontactPhone: validatecontactPhone,//校验
            isDisabled: false,//设置页面禁用元素
            certUpdated:false,//设置证件更新禁用元素
            // 直租业务展示保险信息，回租业务不展示
            isShowInsureInfo:true,
            isShowAuthorizeWay: false,
            isShowIsDataPostStatus: false,
            isDataPostStatus: "0",
            submitWarnContent: "",
            saveDataPostWarnContent: "",
            isAutoBack: false,
            isDisabledBusiness:false,//设置保险信息禁用元素
            isBusinessType:false,
            isinvoiceDiscern:false,//发票识别禁用元素
            isHaveGpsType: false,//是否有GPS Type
            imageFlag: false,
            isEnoDisabled: false,//是否展示发动机号 是新车，不是挂牌新车 = false，未退回 = true
            isCarDisabled: false,//是否展示车辆型号 是二手车 = true
            houseAddressTemp: [],//户籍地址
            showType: 0,//判断显示
            isDistribute: false,//是否已派单 有工单号=true
            installAddressTemp: [],//GPS安装地址
            gpsInfoFV:{
                installAddressTemp:[
                    {validator:(rule,value,callback)=>{ utilsAddress.addressArrayFive("安装地址",value,callback,2) },trigger:"change"}
                ],
            },
            houseAddress: "",// =gpsInfo.houseAddress
            modelType: 0,//签约方式
            installProvince:'',
            installCity:'',
            installCounty:'',
            installStreet:'',
            saveFlag: "0",
            isDownLoad: true,
            isHouseAddressRequired: false,//:required>户籍地址 是盗抢包 = true
            saleNameRequired: false,//未使用
            saleCertEndDateRequired: false,//未使用
            saleCertNoRequired: false,//未使用
            salePhoneRequired: false,//未使用
            isInsuranceRequired: false,
            isViewVer: false,//:required 调用
            userRelName: '',//系统用户名称
            phone: '',//系统用户手机号
            isViewRobberyBag: false,//是盗抢包 = true
            saveLoading: false,
            distributeLoading: false,
            submitLoading: false,//未使用
            isDistributeDisabled: false,//页面禁用元素管理
            isGPSDistribute:false,//GPS派单成功禁用元素
            isGPSDistributeLeaflets:true,//GPS派单按钮禁用元素
            isDisabledLocationbute:true,//GPS定位按钮禁用元素
            isDisabledGpsSave:false,//GPS保存禁用元素
            VinIsDisabled: false,//是否展示车架号 新车=false，其他情况=true
            certNoMain:'',//承租人身份证
            certNoBorrow:'',//共同承租人身份证
            certNoCommon:'',//担保人身份证
            quotaControlSwitch: null,
            channelBelong:'',//渠道归属:00：SP  01:直营02:总对总
            options3: {
                disabledDate(date) {
                    return date && date.valueOf() < Date.now() - 86400000;
                }
            },//未使用
            gpsLoanTerm: 0,//期数 .getLoanTerm());//期数
            firstMortgage: false,//获取收单，true：不是首单，false：是首单
            recordColumnsHead: recordTableHead,//退件记录 table
            specialTableHead: specialTableHead,//特殊业务 table
            custColumnsHead: custColumnsHead,//证件更新 table

            invoiceVerification: invoiceVerification,//发票查验 table
            upperId: "",
            financingItemsAmt: "",//未使用
            decorationDetailed: "",//附加租赁项 明细
            isSave: 0,//下一步影像上传，保存
            contractNo: '',//合同号
            bankName:'',//开户行
            bankPhone:'',//银行电话
            formCust: {},//客户身份信息更改 :model="formCust"
            formCustRules: {
                'formCust.certEndDate': [
                    {required: true, type: 'date', message: '有效期变更不能为空', trigger: 'blur'},
                ]
            },//客户身份信息更改 表单验证
            formRules: {
                'affiliatedUnit.socUniCrtCode': [
                    {required: true, message: '社会统一信用代码不能为空', trigger: 'blur'},
                    {required: true, validator: isSocValiate, trigger: "blur"}
                ],
                "engineNo": [
                    { required: true, message: 'The name cannot be empty', trigger: 'blur' }
                ],
                'carInvoice.saleCertNo':[],
                'carInvoice.salePhone': [],
                'bankCard.accountNo':[],
                'invoiceDiscern.invoiceNo':[],
            },//多模块统一表单验证
            witnessValidate: {
                'contractInfo.witnessIdCard': [
                    {required: true, message: "见证人不能为空", trigger: "change"}
                ]
            },//影像上传 form表单验证， :rules="witnessValidate"
            insuranceInfo: {
                insuranceAmt:0,
                isLaterSupply:"0"
            },//交强险
            insuranceInfoBusiness: {
                insuranceAmt:0,
                insuranceMoney:0,
                isLaterSupply:"0"
            },//商业险
            invoiceDiscern:{},
            carBaseInfos:[],
            carDetailsOld: {},
            needShowConfirm: false,
            gpsInfo: {},
            bankCard: {
                accountNo:"",
                certNo:"",
                enterpriseAccountName:"",
                enterpriseAccountNo:"",
                enterpriseBankName:"",
                enterpriseBankCode:"",
            },//银行卡模块信息
            showIdType: false,
            bankCode:{},
            orderInfo: {},
            receivingAccount:{},
            gpsProductInfo:{},//GPS产品
            showOverlay: false, // 控制蒙层显示与隐藏的状态
            formEntry: {
                flag: "",
                receivingAccountId:"",//收款方账号ID v-model
                orderInfo: {},
                carInvoice: {},
                decorationInvoiceForm: {
                    id:"",
                    applyNo:"",
                    invoiceUnit:"",
                    buyerIdcardNo:"",
                    carVin:"",
                    saleName:"",
                    buyerName:"",
                    invoiceNo:"",
                    invoiceCode:"",
                    invoiceDate:"",
                    checkCode:"",
                    invoiceAmt:"", //开票金额
                    invoiceTaxAmt:"", //增值税额
                    excludingTaxAmt:"", //不含税价
                    invoiceRate:"" //税率
                },
                bankCard: {},
                gpsInfo: {},
                affiliatedUnit: {},
                financingItems: [],
                returnRecords: [],
                insuranceInfos: [],
                applyOprRecords: [],
                custAddressDetails: {},
                carDetails: {},
                contractInfo: {
                    gpsStatus:undefined
                },
                custBaseInfos: [],
                recordDetails: [],
                custHistory: [],
                costDetails: {
                    contractAmt:0
                },
                financingItemsApply:{}
            },//多模块表单统一数据管理
            leaseSumMoney:0,
            strongFinancingAmount:0,//交强险融资额
            merchantFinancingAmount:0,//商业险融资额
            channelId: '',//渠道编号
            //定义数据字典接收
            dataDic: {
                businessType: [],
                operateWay: [],
                affiliatedWay: [],
                carPurpose: [],
                invoiceClass: []
            },
            accountPerson: [],//主共同承租人信息列表
            lendingMode: [],//放款模式列表
            loanTerm: 0,
            // 加载状态
            spinShow: false,//重出
            canUpload: true,
            text:"请选择证件有效期",
            isDisableAuthorizeWay:false,//未使用
            showInvoiceNotice: false, //是否在提交时提示发票低开
            needInvoiceCheck: false, //监听发票数据变更确认是否发起发票校验
            isInvoiceSameDiv: false, //发票查验结果框是否展示
            isInvoiceSame: false,   //发票查验是否一致
            hasColor: false,        //发票查验框字体是否置红
            hasContract: false,     //是否已生成合同
            moveComment: true,
            remindList: [],
            oldRemarks:[],
            orderType:'0',//订单类型 0 零售 1 小微
            invoiceModel:false,
            bankCardChannel:[
                {
                    title:"",
                    key:"",
                    verCode:'',
                    loading:false,
                    _disabled:false,
                    isSendMessage:false,
                    isSendCode:0, //设计 0:默认 1:发送成功 2：发送失败
                    Count:0,
                    bankCardTimer:null,
                },
            ],
            bankCardData:[],//存储银行卡保存的数据
            changeToEnterprise: false, //是否个人转企业按钮切换
            onlyCheck: false, //客户信息是否仅查看
            authorizeWayRuleValidate: {
                authorizeWay: [
                    { validator: (rule, value, callback) => {
                            if (value === null || value === undefined || value === '') {
                                callback(new Error("请选择合同签署方式"));
                            }
                            callback();
                        }, trigger: 'blur', required: true}
                ]
            },
            isDataPostStatusValidate: {
                isDataPostStatus: [
                    { validator: (rule, value, callback) => {
                            if (value === null || value === undefined || value === '') {
                                callback(new Error("请选择和保存是否开启资料后置"));
                            }
                            callback();
                        }, trigger: 'blur', required: true}
                ]
            },
            idTypeList: [],
            isCert: true,
            currentCertNo: null,
        }
    },
    props: {
        isShowDetails: {
            type: Boolean,
        },
        isAssertChange: {
            type: Boolean,
        }
    },
    created() {
        if (this.afs.getPageParams(this)) {
            this.applyNo = this.afs.getPageParams(this).applyNo;
            this.contractNo = this.afs.getPageParams(this).contractNo;
            this.contractStatus = this.afs.getPageParams(this).contractStatus;
            if (this.afs.getPageParams(this).contractType) {
                this.isDownLoad = true;
            } else {
                this.isDownLoad = false;
            }
            if (this.afs.getPageParams(this).type === 1) {
                this.saveFlag = "1";
                this.showType = 1;
                this.fileRead = true;
                this.isContractEntry = false;
                this.isCarDisabled = true;
                this.isCustDisabled = true;
                this.VinIsDisabled = true;
                this.isEnoDisabled = true;
                this.isBankDisabled = true;
                this.isView=true;
                this.isGPSDistribute=true;
            }
            if (this.contractStatus === '05') {
                this.isContractEntry = true;
                this.isCustDisabled = false;
                this.VinIsDisabled = true;
                this.isEnoDisabled = true;
                this.isCarDisabled = true;
                this.isBankDisabled = true;
            }
            const row = this.afs.getPageParams(this).row;
            this.row=row;
            if (row.belongingCapital && /^BANK/i.test(row.belongingCapital)) {
                this.bankPanelTitle='弗迪银行卡信息';
            }
            this.diffType=this.afs.getPageParams(this).diffType;
            //处理新增需求 企业个人业务 个人 0  企业 1
            if(row.inputType=="0"){
                this.InvoiceApplication.invoiceFlag="0";
                this.invoiceDisabled=false;
                this.mailRequired=true
                this.invoiceDisabledrequired=false;
            }else{
                this.InvoiceApplication.invoiceFlag="1";
                this.invoiceDisabled=true;
                 this.mailRequired=false
                this.invoiceDisabledrequired=true;
            }
            this.InvoiceApplicationValidateFun();
            if(row.channelBelong=='01'){
                this.isChannelBelong=true;
                this.certificateDiscernRule={
                    carVin: [
                        { required: true, message: '车架号不能为空', trigger: 'blur' },
                    ],
                }
                //处理新增业务
                const customerType = this.row.customerType //客户类型 ;0个人/1企业
                const customerCertType =this.row.customerCertType //客户证件类型
                const isExistFirstGuarantor = this.row.isExistFirstGuarantor //是否存在第一担保人 0否  1是
                const guarantorCertType= this.row.guarantorCertType || 0 //第一担保人证件类型
                //当承租人为个人，且证件类型不为“港澳台证件”时，“个人账号”必填，“企业账户”隐藏
                if(customerType=='0' && (customerCertType=='00001' || customerCertType=='00002')){
                    this.personalBack=true;
                    this.personalBackRequired=  true  ;
                    this.enterprise=false
                    this.enterpriseRequired=false
                }
                //当承租人为个人，且证件类型为“港澳台证件”时，“个人账号”和“企业账户”均非必填；
                if(customerType=='0' && (customerCertType!='00001' && customerCertType!='00002')){
                    this.personalBack=true;
                    this.personalBackRequired=  false  ;
                    this.enterprise=true
                    this.enterpriseRequired=false
                }
                //当承租人为企业时，且第一担保人证件类型不为“港澳台证件”时，“个人账号”必填，“企业账号”非必填；
                if(customerType=='1'){
                    this.personalBack=true;
                    this.personalBackRequired=false;
                    this.enterprise=true
                    this.enterpriseRequired=false
                }
                //当承租人为企业时，且存在第一担保人且证件类型为“港澳台证件”时， “个人账号”和“企业账户”均非必填
                // if(customerType=='1' && isExistFirstGuarantor=='1' && (guarantorCertType=='00005' || guarantorCertType=='00006')){
                //     this.personalBack=true;
                //     this.personalBackRequired=false;
                //     this.enterprise=true
                //     this.enterpriseRequired=false
                // }
                //当承租人为企业时，且不存在第一担保人时，“个人账号”隐藏，“企业账号”必填；
                // if(customerType=='1' && isExistFirstGuarantor=='0'){

                // }

            }else{
                this.isChannelBelong=false;
                this.certificateDiscernRule={
                    engineNo: [
                        { required: true, message: '发动机号不能为空', trigger: 'blur'},
                    ],
                    /*carColor: [
                        {required: true, message: '车辆颜色不能为空', trigger: 'blur' },
                    ],*/
                    carVin: [
                        { required: true, message: '车架号不能为空', trigger: 'blur' },
                    ],
                 /*   gbCode: [
                        { required: true, message: '国标码不能为空', trigger: 'blur' },
                    ],*/
                }

                this.insuranceInfoBusinesse={
                    insuranceNo: [
                        {required: this.businessIsLaterSupply, message: '保单号不能为空', trigger: 'blur'},
                        {
                            validator: (rule, value, callback) => {
                                var reg = /^[0-9a-zA-Z]*$/
                                if (!reg.test(value)){
                                    callback(new Error("请输入数字和字母"))
                                 }else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },
                    ],
                }
                this.insuranceInfos={
                    insuranceNo: [
                        {required: this.jqxIsLaterSupply, message: '交强险保单号不能为空', trigger: 'blur'},
                        {
                            validator: (rule, value, callback) => {
                                var reg = /^[0-9a-zA-Z]*$/
                                if (!reg.test(value)){
                                    callback(new Error("请输入数字和字母"))
                                }else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },
                    ],


                }
                this.bankCarde={
                    accountNo: [
                        {required: true, message: '开户人银行卡号不能为空', trigger: 'blur'},
                        /*{
                            validator: (rule, value, callback) => {
                                console.log(value)

                                var reg = /^[0-9]*$/
                                if (value == ''){
                                    callback(new Error("开户人银行卡号不能为空"))
                                }else if (!reg.test(value)){
                                    callback(new Error("请输入数字"))
                                }else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },*/
                    ],
                    bankPhone: [
                        {required: true, message: '预留手机号码不能为空', trigger: 'blur'},
                        {
                            validator: (rule, value, callback) => {
                                var reg = /^[0-9]*$/
                                if (!reg.test(value)){
                                    callback(new Error("请输入数字"))
                                }else {
                                    callback();
                                }
                            },
                            trigger: "blur",
                        },
                    ],
                }
            }
            //客户信息是否仅查看
            this.onlyCheck = this.afs.getPageParams(this).onlyCheck;

        }
    },
    mounted() {
        this.init();
        this.queryReceipt();
        this.queryRemind();
    },
    updated() {
        if (this.canUpload) {
            let formEntry = this.$refs.formEntry
            if (formEntry && formEntry.fields && formEntry.fields.length) {
                this.loadForm();
                this.canUpload = false;
            }
        }
    },
    methods: {
        getDate() {
            if (this.gpsInfo.preInstallTime !== null && this.gpsInfo.preInstallTime !== "") {
                let s1 = formatDate(new Date, "yyyy-MM-dd");
                let s0 = formatDate(this.gpsInfo.preInstallTime, "yyyy-MM-dd");
                if (s0 < s1) {
                    this.$Message.error("安装时间大于等于当前时间");
                    this.gpsInfo.preInstallTime = ""
                } else {
                    this.gpsInfo.preInstallTime = s0;
                }
            }
        },
        /**
         * OCR
         */
        CardIdOCR(id) {
            this.$refs[id].click();
        },
        getCertificateCardOCR(event,callback,num=null) {
            console.log(" =========================================== 合格证OCR =========================================== ")
            let files = event.target.files;
            console.log(files, "files")
            let AllUpExt = ".jpg|.bmp|.jpeg|.png|.gif|";
            let extName = files[0].name.substring(files[0].name.lastIndexOf(".")).toLowerCase();
            if (AllUpExt.indexOf(extName + "|") == "-1") {
                this.$Message.warning("文件格式不正确！");
                return;
            }

            let size = files[0].size;
            size = size / (1024 * 1024);
            console.log(size)
            if (size > 5) {  // 5M
                this.$Message.warning("请上传小于5M的图片！");
                return;
            }

            let file = files[0];
            let formData = new FormData();//创建 formdata对象
            formData.append('file', file);
            getCertificateCardOCR(formData).then(res => {
                if (res.code === "0000") {
                    if( res.data){
                        this.parsingAddress(res.data,callback,num)
                    }else{
                        this.$Message.error("识别失败");
                    }
                } else {
                    this.$Message.error("识别失败");
                }
            });
        },
        getBankCardOCR(event,callback,num=null) {
            console.log(" =========================================== 银行卡OCR =========================================== ")
            let files = event.target.files;
            console.log(files, "files")
            let AllUpExt = ".jpg|.bmp|.jpeg|.png|.gif|";
            let extName = files[0].name.substring(files[0].name.lastIndexOf(".")).toLowerCase();
            if (AllUpExt.indexOf(extName + "|") == "-1") {
                this.$Message.warning("文件格式不正确！");
                return;
            }

            let size = files[0].size;
            size = size / (1024 * 1024);
            console.log(size)
            if (size > 5) {  // 5M
                this.$Message.warning("请上传小于5M的图片！");
                return;
            }

            let file = files[0];
            let formData = new FormData();//创建 formdata对象
            formData.append('file', file);
            getBankCardOCR(formData).then(res => {
                if (res.code === "0000") {
                    if( res.data){
                        this.parsingBankCardAddress(res.data,callback,num)
                    }else{
                        this.$Message.error("识别失败");
                    }
                } else {
                    this.$Message.error("识别失败");
                }
            });
        },
        /**
         * 解析字符
         */
        parsingAddress(jsonData,callback,num) {
            let data={};
            // 车架号
            data.vehicleCertificationModelVin = jsonData.vehicleCertificationModelVin;
            // 发动机号
            data.vehicleCertificationEngineNumber = jsonData.vehicleCertificationEngineNumber;
            this[callback](data,num);
        },
        /**
         * ocr银行卡解析信息
         * @param jsonData
         * @param callback
         * @param num
         */
        parsingBankCardAddress(jsonData,callback,num) {
            let data={};
            data.cardNo=jsonData.cardNo;
            this[callback](data,num);
        },
        /**
         * 更新车架号和发动机号
         */
        basicInfor(data){
            // 车架号
            this.certificateDiscern.carVin = data.vehicleCertificationModelVin;
            // 发动机号
            this.certificateDiscern.engineNo = data.vehicleCertificationEngineNumber;
        },
        /**
         * ocr银行卡
         * @param data
         */
        bankInfor(data){
            // 银行卡号
            this.bankCard.accountNo = data.cardNo;
            this.getSignBanknameBynum();
        },
        signClick(){

            // if(!this.hasContract){
            //     this.$Message.error("暂未生成合同, 请稍等生成合同后或者手动在合同下载页面重新生成合同后再进行签约!");
            //     this.saveLoading=false;
            //     return;
            // }

            // 客户信息
            if(!this.$refs.clientInfo.checkClientInfo()){
                this.$Message.error("请补全页面客户信息");
                return false;
            }
            if(!this.isChannelBelong){
                //车辆信息 > 发票信息 > 收款方账号信息 > 银行卡信息 > 商业险保险信息 > 交强险保险信息
                if(!this.carDetails){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.carDetails.carVin){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.checkCarDetailsUpdate()){
                    this.$Message.error("请先行保存车辆信息");
                    return false;
                }
                // let bankCardList = this.getBankData();
                // if(bankCardList.length<1){
                //     this.$Message.error("请补全页面银行卡信息");
                //     return;
                // }
                if(!this.formEntryContractInfoReceivingId){
                    this.$Message.error("请补全页面收款方账号信息");
                    return false;
                }
             }else{
                 //车辆信息 > 发票信息 > 收款方账号信息 > 银行卡信息 > 商业险保险信息 > 交强险保险信息
                if(!this.carDetails){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.carDetails.carVin){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.checkCarDetailsUpdate()){
                    this.$Message.error("请先行保存车辆信息");
                    return false;
                }
                if(!this.formEntryContractInfoReceivingId){
                    this.$Message.error("请补全页面收款方账号信息");
                    return false;
                }
                if(this.personalBackRequired){
                    // let bankCardList = this.getBankData();
                    // if(bankCardList.length<1){
                    //     this.$Message.error("请补全页面银行卡信息");
                    //     return;
                    // }
                }
            }
            this.$refs.FixedNav.isShowMessageModel = true;
        },
        createHoliday(){
            this.holidayConfig = true
        },
        mainCloseModal(){
            this.holidayConfig = false
        },
        invoiceIdentificationModel(){
            this.invoiceIdentification=true;
        },
        //第一个参数是否更新，第二个参数是否一致
        closeInvoiceIdentification(isUpdate,isAgreement){
            //ocr
            this.invoiceIdentification=false;
            // this.getPlantInfo();
            if(isUpdate){
                this.isAgreement = isAgreement;
                this.queryCarScreenRecord();
            }
        },
        queryCarScreenRecord(){
        },
        //人工甄别请求接口
        queryApplyCarModelRecheck(){
            var parms = {
                id:this.carDetails.id,
                carVin:this.certificateDiscern.carVin,
            };
            queryApplyCarModelRecheck(parms).then((res)=>{
                if(res.code="0000"){
                    this.carPeopleInfos = res.data;
                }
            });
        },
        //车型甄别
        carModelScreening(){
            this.isShowCarModel = true;
            this.queryApplyCarModelRecheck();
        },
        checkNeedInvoiceCheck(){
            if(!this.invoiceDiscern.invoiceNo || !this.invoiceDiscern.invoiceCode || !this.invoiceDiscern.invoiceDate || !this.invoiceDiscern.excludingTaxAmt){
                return;
            }
            if(this.needInvoiceCheck){
                this.toInvoiceCheck();
            }
        },
        inputCard() {
            let s = this.bankCard.accountNo.replace(/\D/g, ''); // 不允许输入非数字字符
            this.bankCard.accountNo = s.replace(/(\d{4})(?=\d)/g, '$1 ') // 4位一组，非获取匹配最后一组数字，避免删除到空格时会马上自动补齐
            console.log(this.bankCard.accountNo,"四位一组")
        },
        amtEqual(name,code,amt){
            this.formEntry.financingItemsApply.forEach(en => {
                if (code == en.financeItemCode && amt<en.financeItemAmt) {
                    this.$Message.error(name+"加融金额不符合放款准入标准，请核实！");
                }
            });
        },
        //开户人银行卡号失焦事件
        getSignBanknameBynum(){
            if (this.bankCard.accountNo) {
                getSignBanknameBynum(this.bankCard.accountNo.replace(" ", ""),this.applyNo).then(res => {
                    if (res.code === '0000') {
                        //重新渲染数据
                        this.$set(this.bankCard, "bankBranch", res.data.bankTypeName);
                        this.$set(this.bankCard, "bankCode", res.data.bankTypeCode);
                        let channels=res.data.channels;
                        let bankCardChannel=[]
                        let dicKey="card-sign-"+res.data.bankTypeCode;
                        this.getDicDataByKey(dicKey);
                        channels.forEach(item=>{
                           bankCardChannel.push({
                                title:item.title,
                                key:item.key,
                                verCode:'',
                                loading:false,
                                _disabled:false,
                                isSendMessage:false,
                                isSendCode:0, //设计 0:默认 1:发送成功 2：发送失败
                                Count:0,
                                bankCardTimer:null,
                                id:''
                            })
                        })
                        this.bankCardChannel=bankCardChannel;
                    }else {
                        this.$Message.error("银行卡信息有误，请重新输入");
                    }

                });
            }
        },
        //企业银行
        getSignEnterprise(val){
            this.dataDic.bankCode.forEach(item=>{
                if(item.value==val){
                     this.bankCard.enterpriseBankName=item.title;
                }
            })
        },
        //获取点选数据
        getAttachmentData(v) {
            console.log("点选获取的值:", v);
        },
        //检查是否下载过合同
        checkHasContract(){

            let params = {
                applyNo: this.applyNo,
                contractNo: this.contractNo
            }
            checkHasContract(params).then(res => {
                if (res.code === '0000') {
                    if(res.data==true){//代表下载过
                        this.loading=true;
                        this.templateModel = true;
                        this.initGetComAttach("printLoanApply");//合同模板
                        this.isRegenerate = true;
                    }else{
                        this.contractBatchPrint();
                    }
                }else{
                     this.contractBatchPrint();
                }
            }).catch(e=>{

            })
        },
        //模板生成
        contractBatchPrint() {

            // 客户信息
            if(!this.$refs.clientInfo.checkClientInfo()){
                this.$Message.error("请补全页面客户信息");
                return false;
            }

            const opt={
                "applyPayeeInfo": {
                    applyNo:this.applyNo
                },
                "paySplitFlag":this.paySplitFlag
            }
            queryPayee(opt).then(res => {
                if (res.code === "0000" && res.data) {
                    if(res.data.length>0){
                    }else{
                        this.$Message.error("请补全页面银行卡信息");
                        return;
                    }
                }else{
                    this.$Message.error("请补全页面银行卡信息");
                    return;
                }
            });

            if(!this.isChannelBelong){
                //车辆信息 > 发票信息 > 收款方账号信息 > 银行卡信息 > 商业险保险信息 > 交强险保险信息
                if(!this.carDetails){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.carDetails.carVin){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.checkCarDetailsUpdate()){
                    this.$Message.error("请先行保存车辆信息");
                    return false;
                }

                /*let bankCardList = this.getBankData();
                if(bankCardList.length<1){
                    this.$Message.error("请补全页面银行卡信息");
                    return;
                }*/

                if(!this.formEntryContractInfoReceivingId){
                    this.$Message.error("请补全页面收款方账号信息");
                    return false;
                }
             }else{
                 //车辆信息 > 发票信息 > 收款方账号信息 > 银行卡信息 > 商业险保险信息 > 交强险保险信息
                if(!this.carDetails){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.carDetails.carVin){
                    this.$Message.error("请补全页面车辆信息");
                    return false;
                }
                if(!this.checkCarDetailsUpdate()){
                    this.$Message.error("请先行保存车辆信息");
                    return false;
                }
                if(!this.formEntryContractInfoReceivingId){
                    this.$Message.error("请补全页面收款方账号信息");
                    return false;
                }
                if(this.personalBackRequired){
                    // let bankCardList = this.getBankData();
                    // if(bankCardList.length<1){
                    //     this.$Message.error("请补全页面银行卡信息");
                    //     return;
                    // }
                }
            }
            this.loading=true;
            let params = {
                applyNo: this.applyNo,
                contractNo: this.contractNo
            }
            contractBatchPrint(params).then(res => {
                this.loading=false;
                if (res.code === '0000') {
                    // this.$Message.success("模板已生成")
                    this.hasContract = true;
                    this.templateModel = true;
                    this.changeToEnterprise = false;
                    this.$refs.clientInfo.ifChangeToEnterprise = this.$refs.clientInfo.setSubmitData().ifPersonalToEnterprise;
                    this.isRegenerate = true;
                    this.initGetComAttach("printLoanApply");//合同模板
                }
            }).catch(e=>{
                this.loading=false;
            })
        },
        //批量文件下载
        downloadBatchFile(v) {
            let downFileList = [];
            if (this.checkAllGroup.length > 0) {
                if (v.length > 0) {
                    let tempArr = [];
                    v.forEach((item, index) => {
                        let flag=true;
                        let date=new Date(this.formEntry.orderInfo.startTime);
                        let day = ((new Date()).getTime() - date.getTime()) / 1000/60/60/24;
                        if(day>15) {
                            if (item.uniqueCode == 'carMortgageLoanContractTJ' || item.uniqueCode == 'carMortgageLoanContractTJZY') {
                                flag = false;
                                this.$Message.error("此合同激活时间已超15天，不能再下载抵押贷款合同");
                                return false;
                            }
                            if (item.uniqueCode == 'automobileMortgageContract') {
                                flag = false;
                                this.$Message.error("此合同激活时间已超15天，不能再下载汽车抵押合同");
                                return false;
                            }
                            if (item.uniqueCode == 'vehicleAttachmentAgreement') {
                                flag = false;
                                this.$Message.error("此合同激活时间已超15天，不能再下载车辆挂靠协议");
                                return false;
                            }
                        }
                        if (this.checkAllGroup.indexOf(item.attachmentCode) != "-1" && flag) {
                            tempArr.push(item);
                        }
                    })
                    downFileList = tempArr;
                }
            } else {
                this.$Message.warning("请选择文件模版下载");
                return false;
            }
            let params = {
                attachmentList: downFileList,
                applyNo: this.contract
            }
            let self = this;
            downloadBatchFile(params).then(function (response) {
                if (response.status === 200) {
                    self.afs.downloadFile(response);
                }
            })
        },
        pdfPreview(item) {
            //不要乱改
            console.log('item',item);
            let pdfurl = `${_AFS_PROJECT_CONFIG.apiUri}/apply/upload/getBlob/pdf/${item.fileId}?adScope=apply&token=${this.$store.getters.access_token}`;
            window.open(pdfurl);


            return;
            if(typeof this.formEntry.orderInfo.startTime!=='undefined'){
                let date=new Date(this.formEntry.orderInfo.startTime);
                let day = ((new Date()).getTime() - date.getTime()) / 1000/60/60/24;
                if(day>15){
                    if(item.uniqueCode == 'carMortgageLoanContractTJ' || item.uniqueCode == 'carMortgageLoanContractTJZY' ||
                        item.uniqueCode == 'automobileMortgageContract' || item.uniqueCode == 'vehicleAttachmentAgreement'){
                        this.$Message.error("此合同激活时间已超15天，不能再预览该类合同");
                        return;
                    }
                }
            }
            // 浏览器新tab打开代码
            const token = this.getToken();
            console.log(token)
            let _afsDynamicLinkKey = md5(this.$route.name + this.$store.state.app.sessionKey)
            let data = {
                token: token,
                sessionKey:this.$store.state.app.sessionKey,
                pageData: {
                    params: Object.assign({_afsDynamicLinkKey:'key_'+getUuid()},{requestPath:`/${getUri('fileUri')}/filestore/file/0/${item.fileId}?adScope=apply&token=${this.$store.getters.access_token}`,requestParam:''}),
                    component: 'components/pdf/pdf-js',
                    isFull: true,
                    pageTitle: `文件${item.fileName}预览`,
                    eventHashKey: _afsDynamicLinkKey
                }
            };
            window.open(this.$router.resolve('/d/afsLink').href + '?_link=' + Base64.encode(JSON.stringify(data), true),'_blank');
            // let pdfurl = `${_AFS_PROJECT_CONFIG.apiUri}/${this.path}/upload/getBlob/pdf/${item.fileId}`;
            // window.open(pdfurl);
        },
        //单个文件下载
        downloadSingleFile(item) {
            if(typeof this.formEntry.orderInfo.startTime!=='undefined'){
                let date=new Date(this.formEntry.orderInfo.startTime);
                let day = ((new Date()).getTime() - date.getTime()) / 1000/60/60/24;
                if(day>15){
                    if(item.uniqueCode == 'carMortgageLoanContractTJ' || item.uniqueCode == 'carMortgageLoanContractTJZY' ||
                        item.uniqueCode == 'automobileMortgageContract' || item.uniqueCode == 'vehicleAttachmentAgreement'){
                        this.$Message.error("此合同激活时间已超15天，不能再下载该类合同");
                        return;
                    }
                }
            }
            let params = {
                id: item.id,
                hasSeal: "0",
            }
            let self = this;
            downloadSingleFile(params).then(function (response) {
                console.log(response)
                if (response.status === 200) {
                    self.afs.downloadFile(response);
                }
            })
        },
        initGetComAttach(type) {
            this.loading=true;
            let params = {
                busiNo: this.contractNo,
                pageSize: 500,
                pageNumber: 1,
                printNode: type,
                pcOrApp:"pc"

            }
            //获取影像件数据
            getComAttach(params).then(res => {
                this.loading=false;
                if (res.code === '0000') {
                    this.fileList = res.data.records;
                    console.log(res.data);
                }
            }).catch(e=>{
                this.loading=false;
            })
        },
        //放款资料上传按钮
        loanInfoUploadBtn() {
            if (this.contractNo == '' || this.contractNo == undefined) {
                this.$Message.info("您好，请先录合同信息");
            } else {
                this.initGetFilterParams();
                this.uploadFileModal = true;
            }
        },
        // 查询影像件需要的数据
        initGetFilterParams() {
            let params = {
                applyNo: this.applyNo,
            }
            getFilterParams(params).then(res => {
                if (res.code === '0000') {
                    this.uploadParam.busiNo = this.contractNo;
                    this.uploadParam.belongNo = this.contractNo;
                    this.uploadParam.busiData = res.data;
                    this.uploadParam.busiType = "loanApply";
                    this.isInt = true;
                }
            });
        },
        init() {
            this.initSelectData();
            this.getIsFirstModel();
            this.getEntryPageInfo();
            this.contractSignRelationQuery();
            // this.loadForm();
            this.setDisabled();
            this.initGteUserInfo();
            this.initGetFilterParams();
        },
        getIsFirstModel() {
            //获取首单模式
            getIsFirstMortgage({applyNo: this.applyNo}).then(res => {
                if (res.code === "0000") {
                    this.firstMortgage = res.data;
                }
            })
        },
        //初始化获得用户信息
        initGteUserInfo() {
            getUserInfo().then(res => {
                if (res.code === '0000') {
                    let sysUser = res.data.sysUser;
                    this.userRelName = sysUser.userRealName;
                    this.phone = sysUser.phone;
                }
            })
        },
        //加载页面form校验
        loadForm() {
            formOperation(this.$refs.formEntry.fields, this.formRules);
            this.extraValiate(this.formRules);
        },
        extraValiate(formRules) {
            formRules['carDetails.carVin'].push({
                validator: this.isVinNoValiate,
                trigger: "blur"
            })
            formRules['gpsInfo.contactPhone'].push({
                validator: applyUtils.isPhoneValiate,
                trigger: "blur",
            })
            formRules['carInvoice.saleCertNo'].push({required: true, message: '出卖方身份证不能为空', trigger: 'blur'},{
                required: true,
                validator: applyUtils.doValidID,
                trigger: "blur"
            },{ required: true,validator: this.idCardValiate, trigger: "blur"})

            formRules['carInvoice.salePhone'].push({required: true, message: '出卖方手机号不能为空', trigger: 'blur'},{
                required: true,
                validator: applyUtils.isPhoneValiate,
                trigger: "blur"
            })
            formRules['bankCard.accountNo'].push({required: true, message: '还款账号不能为空', trigger: 'blur'})
        },
        //设置页面禁用元素
        setDisabled() {
            //是否 编辑/申请编号进入
            if (this.isContractEntry) {
                this.isDisabled = false;
                // this.isBankDisabled=false;
            } else {
                this.isDisabled = true;
                this.isDistributeDisabled = true;
                this.VinIsDisabled = true;
                this.isEnoDisabled = true;
                //置灰保存按钮
                // this.isDisabledGpsSave=true;
                //派单按钮置灰
                this.isGPSDistributeLeaflets=true;
                //置灰定位按钮
                this.isDisabledLocationbute=true;
                //置灰保险信息
                this.isDisabledBusiness=true;
                //车辆信息
                this.isBusinessType=true;
                // this.isBankDisabled=true;
            }
        },
        // checkCarDifferPrice(){
        //     let invoicePrice = parseInt(this.invoiceDiscern.invoiceAmt).toFixed(2);
        //     let salePrice = parseInt(this.formEntry.carDetails.salePrice).toFixed(2);
        //     let carType = this.formEntry.orderInfo.carType;
        //     //乘用车
        //     if(invoicePrice != "NaN"&&salePrice != "NaN"){
        //         this.carDifferPrice = invoicePrice - salePrice;
        //         //发票价低于销售价大于等于3000元且未针对此项已经特批
        //         if(carType == "2"&&this.carDifferPrice >= 3000){
        //             this.hasColor = true;
        //         }
        //     }
        // },
        checkCarDifferPrice() {
            // 将价格转换为分（整数）进行计算，避免浮点数问题
            let invoicePrice = Math.round(parseFloat(this.invoiceDiscern.invoiceAmt || 0) * 100);
            let salePrice = Math.round(parseFloat(this.formEntry.carDetails.salePrice || 0) * 100);
            let carType = this.formEntry.orderInfo.carType;
            this.hasColor = false;

            // 确保两个价格都是有效数字
            if (!isNaN(invoicePrice) && !isNaN(salePrice)) {
                // 计算差价（以分为单位，整数运算无精度问题）
                const differPriceInCents = invoicePrice - salePrice;

                // 转换回元并保留两位小数（仅用于显示）
                this.carDifferPrice = (differPriceInCents / 100).toFixed(2);

                // 使用整数比较（单位：分）
                if (carType === "2" && differPriceInCents >= 300000) {
                    this.hasColor = true;
                }
            }
        },
        //加载页面数据
        getEntryPageInfo() {
            getEntryPageInfo(this.applyNo).then(res => {
                res.data.financingItems.forEach(e => {
                    if (e.financeItemCode === "ZH") {
                        this.isViewDecorationInvoice = true;
                    }
                    if(e.financeItemCode === "JQX"){
                        this.strongFinancingAmount=e.financeItemAmt;//获取交强险融资额

                    }
                    if(e.financeItemCode === "SYX"){
                        this.merchantFinancingAmount=e.financeItemAmt;//获取商业险融资额
                    }
                });
                if (res.code === "0000") {
                    if (typeof res.data.gpsFullName !== "undefined") {
                        this.gpsFullName = res.data.gpsFullName;
                    }
                    if(res.data.orderInfo){
                        if(res.data.orderInfo.inputType&&res.data.orderInfo.inputType=="1"){
                            this.certUpdated=true;
                        }
                         // 取值订单类型
                         this.orderType=res.data.orderInfo.orderType;
                        if (res.data.orderInfo.rentType && res.data.orderInfo.rentType === '1') {
                            // 回租业务不展示保险信息
                            this.isShowInsureInfo = false;
                        }
                        if(res.data.orderInfo.affiliatedWay === '04' || res.data.orderInfo.affiliatedWay === '01') {
                            this.isShowAuthorizeWay = true
                        }
                    }

                    //收款方账号
                    this.formEntry.receivingAccountId=res.data.contractInfo.receivingAccount;
                    this.formEntry.contractInfo=res.data.contractInfo;

                    if(res.data.isDataPost === '1') {
                        this.isShowIsDataPostStatus = true
                    }

                    if(this.formEntry.contractInfo.backTimes > 0){
                        this.isAutoBack = true;
                    }

                    this.isDataPostStatus = this.formEntry.contractInfo.isDataPostStatus;
                    this.submitWarnContent = res.data.submitWarnContent;
                    this.saveDataPostWarnContent = res.data.saveDataPostWarnContent;

                    if (res.data.contractInfo.remarks !== '' && res.data.contractInfo.remarks != null && res.data.contractInfo.remarks != undefined) {
                        this.remarkShow=true
                    } else {
                        if(!res.data.remarkList.length>0){
                            this.remarkShow=true
                        }
                    }
                    if(res.data.remarkList.length>0){
                        this.oldRemarks=res.data.remarkList
                    }
                    this.gpsInfo.installAddressTemp=res.data.gpsInfo.installAddressTemp;
                    this.formEntry.gpsInfo.installAddressTemp=res.data.gpsInfo.installAddressTemp;
                    this.gpsInfo=deepClone(res.data.gpsInfo);
                    if (this.isContractEntry) {
                        //gps是否已保存,gps默认会传值delFlag
                        if (res.data.gpsInfo) {
                            if (res.data.gpsInfo.applyNo) {
                                //是否已派单
                                this.formEntry.gpsInfo.applyStatus=res.data.contractInfo.gpsStatus
                                if(res.data.contractInfo.gpsStatus){
                                    //已派单，置灰派单按钮
                                    this.isGPSDistributeLeaflets=true;
                                    //派单成功，定位放开
                                    this.isDisabledLocationbute=false;
                                    //gps信息置灰
                                    this.isGPSDistribute=true;
                                }else{
                                    //派单按钮置灰
                                    this.isGPSDistributeLeaflets=false;
                                    //置灰定位按钮
                                    this.isDisabledLocationbute=true;
                                }
                            } else {
                                //派单按钮置灰
                                this.isGPSDistributeLeaflets = true;
                                //置灰定位按钮
                                this.isDisabledLocationbute = true;
                            }
                        }
                    }
                    this.infoBankCard(res.data.bankCard);
                    this.formEntry = deepClone(res.data);
                    this.orderInfoMoney=this.formEntry.orderInfo.money;
                    this.certificateDiscern = {};
                    this.certificateDiscern = res.data.carDetails;
                    this.$set(this.certificateDiscern, "gbCode", res.data.carDetails.gbCode || '')
                    this.$set(this.certificateDiscern, "engineNo", res.data.carDetails.engineNo || '')
                    this.$set(this.certificateDiscern, "carColor", res.data.carDetails.carColor || '')
                    this.$set(this.certificateDiscern, "carVin", res.data.carDetails.carVin || '')
                    //this.$set(this.affiliatedUnit, "socUniCrtCode", res.data.affiliatedUnit.socUniCrtCode || '')
                    this.businessType = this.formEntry.orderInfo.businessType;
                    this.carDetails = res.data.carDetails;
                    this.carDetailsOld = deepClone(res.data.carDetails);
                    this.licenseProvinceCity.push(res.data.carDetails.licenseProvince);
                    this.licenseProvinceCity.push(res.data.carDetails.licenseCity);
                    this.gpsInfo.loanTerm=res.data.costDetails[0].loanTerm;
                    if(res.data.gpsProductInfo){
                        this.gpsInfo.gpsSupplier=res.data.gpsProductInfo.gpsFirm;
                    }
                    if(res.data.gpsProductInfo){
                        this.formEntry.gpsInfo.gpsSupplier=res.data.gpsProductInfo.gpsFirm;
                    }
                    this.gpsFullName=res.data.gpsFullName;
                    this.leaseSumMoney=res.data.orderInfo.money;//获取融资额
                    var today = new Date();
                    //商业险
                    this.formEntry.insuranceInfos.forEach((item, index) => {
                        if (item.insuranceType == "business") {
                            this.insuranceInfoBusiness = item;
                            var day=   AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                            var insuranceStartTimeDay=  AfsUtils.toDateString(this.insuranceInfoBusiness.insuranceStartTime, 'yyyy-MM-dd');
                            if(AfsUtils.getWhatDay(day, -30) >AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("保单超期，请提供特殊投保承诺函");
                            }
                            if(AfsUtils.getWhatDay(day, 15)<AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                            }
                            if(AfsUtils.getWhatDay(day, 1)<AfsUtils.getWhatDay(insuranceStartTimeDay,0)&&AfsUtils.getWhatDay(day, 15)>=AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("保单超期，请提供批单修改生效日期");
                            }
                        } else{
                            this.insuranceInfo = item;
                            var day=   AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                            var insuranceStartTimeDay=  AfsUtils.toDateString(this.insuranceInfo.insuranceStartTime, 'yyyy-MM-dd');
                            if(AfsUtils.getWhatDay(day, -30) >AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("保单超期，请提供特殊投保承诺函");
                            }
                            if(AfsUtils.getWhatDay(day, 15)<AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("保单超期，请提供批单修改生效日期或请提供特殊投保承诺函");
                            }
                            if(AfsUtils.getWhatDay(day, 1)<AfsUtils.getWhatDay(insuranceStartTimeDay,0)&&AfsUtils.getWhatDay(day, 15)>=AfsUtils.getWhatDay(insuranceStartTimeDay,0)){
                                this.$Message.error("保单超期，请提供批单修改生效日期");
                            }
                        }
                    });
                    //车辆发票信息
                    if(res.data.carInvoice.invoiceRate!==undefined){
                        res.data.carInvoice.invoiceRate=String(res.data.carInvoice.invoiceRate);
                    }

                    if(res.data.carInvoice.invoiceClass === '1'){
                        this.invoiceCodeShow = false;
                    }

                    this.invoiceDiscern = res.data.carInvoice;
                    if(res.data.carInvoice){
                        this.fpIsLaterSupply=this.invoiceDiscern.isLaterSupply!='1';//发票校验
                    }
                    //订单信息 车辆类型
                    this.formEntry.orderInfo.carType = res.data.orderInfo.carType;

                    //计算 车款差额
                    if(this.invoiceDiscern&&this.invoiceDiscern.invoiceAmt&&this.formEntry.carDetails.salePrice){
                        this.checkCarDifferPrice();
                    }
                    this.formEntry.decorationInvoiceForm = deepClone(res.data.decorationInvoiceForm);
                    this.decorationInvoiceForm = deepClone(res.data.decorationInvoiceForm);
                    //保险信息查验数据
                    if(res.data.invoiceVerifications){
                        this.invoiceCheckBody = [];
                        this.invoiceCheckBody.push(res.data.invoiceVerifications);
                        let invoiceCheckData = deepClone(res.data.invoiceVerifications);
                        this.isInvoiceSameDiv = true;
                        this.isInvoiceSame = invoiceCheckData.checkIsSame;
                    };
                    //05:已退回
                    if (this.contractStatus === '05') {
                        if (this.formEntry.returnRecords.length > 0) {
                            this.formEntry.returnRecords.forEach(en => {
                                if (Object.is(en.uniqueCode, "loanContractFile") && en.isStandard === "notStandard") {
                                    //carNature "01": "新车" "02": "挂牌新车" "03": "二手车" "04": "认证二手车" "05": "试乘试驾车"
                                    if (this.formEntry.orderInfo.businessType === "01" && this.formEntry.orderInfo.carNature !== "02") {
                                        this.VinIsDisabled = false;//是否展示车架号
                                        this.isEnoDisabled = false;//是否展示发动机号
                                        this.isCarDisabled = false;//是否展示车辆型号
                                    }
                                }
                            });
                        }
                    } else {
                        if (this.formEntry.orderInfo.businessType === "02") {
                            this.VinIsDisabled = true;
                            this.isEnoDisabled = true;
                            this.isCarDisabled = true;
                            // this.isBusinessType =true;
                        } else if (this.formEntry.orderInfo.businessType === "01" && this.formEntry.orderInfo.carNature === "02") {
                            this.VinIsDisabled = true;
                            this.isEnoDisabled = true;
                        }
                    }
                    if (this.formEntry.returnRecords.length > 0) {
                        this.backInfo = 1;
                    }
                    if(this.formEntry.custBaseInfos){
                        this.formEntry.custBaseInfos.forEach(en => {
                            if (Object.is(en.isLongTerm, "true")) {
                                en.isLongTerm = "是";
                            } else if (Object.is(en.isLongTerm, "false")) {
                                en.isLongTerm = "否";
                            }
                            if(en.custRole==="01"){
                                this.certNoMain=en.certNo;
                            }else if(en.custRole==="02"){
                                this.certNoBorrow=en.certNo;
                            }else if(en.custRole==="03"){
                                this.certNoCommon=en.certNo;
                            }
                        });
                    }
                    if (res.data.orderInfo.businessType == "02") {
                        // this.isBusinessType =true;
                        this.isViewDecorationInvoice =false;
                        this.isViewInvoice =false;
                    }
                    if (res.data.orderInfo.carType == "11") {
                        this.isViewInvoice = false;
                        this.isAuctionCar = false;
                    }
                    this.formEntry.carInvoice.invoiceAmt = res.data.invoiceAmt;
                    this.formEntry.costDetails.loanTerm = res.data.costDetails[0].loanTerm;
                    this.loanTerm = res.data.costDetails[0].loanTerm;
                    this.gpsLoanTerm = res.data.costDetails[0].loanTerm;
                    this.bankCard.authorizeWay = res.data.bankCard.authorizeWay || "offline";
                    this.currentCertNo=res.data.custBaseInfo.certNo;
                    this.bankCard.certNo=res.data.custBaseInfo.certNo;
                    if(res.data.bankCard&&res.data.bankCard[0].certNo){
                        this.bankCard.certNo=res.data.bankCard[0].certNo;
                    }
                    if(this.bankCard.accountNo){
                        let s =this.bankCard.accountNo.replace(/\D/g, '');
                        this.bankCard.accountNo = s.replace(/(\d{4})(?=\d)/g, '$1 ');
                    }
                    if(res.data.gpsType){
                        if (res.data.gpsType == "undefined"||res.data.gpsType == "noInstall") {
                            this.formEntry.gpsInfo.gpsType = res.data.gpsType;
                            this.isViewGps = false;
                        } else {
                            this.formEntry.gpsInfo.gpsType = res.data.gpsType;
                            this.isViewGps = true;
                        }
                    }else{
                        this.isViewGps = false;
                    }
                    this.formEntry.financingItems.forEach(e => {
                        this.formEntry.addPriceItems.forEach(en => {
                            if (e.financeItemCode === en.financeItemCode) {
                                e.financingItemsAmt = en.addFinanceAmt;
                            }
                        });
                    });
                    if (typeof this.formEntry.gpsInfo.houseAddressTemp === "undefined") {
                        if (typeof this.formEntry.custAddressDetails != "undefined") {
                            this.formEntry.gpsInfo.houseAddressTemp = this.formEntry.custAddressDetails.addressTemp;
                        }
                    }
                let status = res.data.carDetails.checkState || '';
                if(status=="1"){
                    this.isAgreement = true;
                }else if(status=="2" || status=="3"|| status=="4"){
                    this.isAgreement = false;
                }else{
                    this.isAgreement= null;
                }
                this.queryCarScreenRecord();
                this.queryApplyCarModelRecheck();
                }
                if(this.isChannelBelong){
                    this.invoiceDiscern.isLaterSupply=res.data.carInvoice.invoiceType;
                    this.insuranceInfoBusinessInfoIsLaterSupply();
                    //交强险
                    this.insuranceInfoIsLaterSupply();
                    this.carInvoiceIsLaterSupply();
                }
                this.paySplitFlag=res.data.paySplitFlag;
                this.queryPayee()
            });
        },
        //初始化下拉框
        initSelectData() {
            //数据字典
            let arr = ['isDataPost','contractSignType','collectionType','accountSource','loanWay',"isDefault","bankCode", "lendingMode", "gpsType", "operateWay", "bank", "insurance", "affiliatedWay", "carPurpose", "businessType", "invoiceNature", "contractStatus",'invoiceClass','gpsSupplier'];
            getByTypes(arr).then(res => {
                if (res.code === "0000") {
                    this.dataDic = deepClone(res.data);
                    let collectionTypeArray={}
                    res.data.collectionType.forEach(item=>{
                        collectionTypeArray[item.value]=item.title
                    })
                    this.collectionTypeArray = collectionTypeArray;
                }
            });

            getChannelInfoByApplyNo(this.applyNo).then(ress => {
                if (ress.code === "0000") {
                    let associatedChannelId='';
                    let channelId='';
                    //渠道归属:00：SP  01:直营02:总对总
                    if(ress.data.channelBelong=='01'){
                        channelId=ress.data.directCarDealersId;
                        associatedChannelId=ress.data.channelId;
                    }else{
                        channelId=ress.data.channelId;
                        associatedChannelId='';
                    }
                    getGpsSupplier({channelId: channelId,associatedChannelId:associatedChannelId}).then(res => {
                        if (res.code === "0000") {
                            for(let i=0;i<res.data.quota.length;i++){
                                if(res.data.quota[i].quotaType==='2'){
                                    this.quotaControlSwitch = res.data.quota[i].quotaControlSwitch;
                                    if (res.data.quota[i].businessType == "01") {
                                        this.newOccupied = res.data.quota[i].occupiedQuota;
                                        this.newSurplus = res.data.quota[i].surplusQuota;
                                        console.log("新车先抵后放额度" + res.data.quota[i].occupiedQuota + "," + res.data.quota[i].surplusQuota);
                                    } else {
                                        this.oldOccupied = res.data.quota[i].occupiedQuota;
                                        this.oldSurplus = res.data.quota[i].surplusQuota;
                                        console.log("二手车先抵后放额度" + res.data.quota[i].occupiedQuota + "," + res.data.quota[i].surplusQuota);
                                    }
                                }
                            }
                        }
                    });
                }
            });
            //开户人
            getAccountPerson(this.applyNo).then(res => {
                if (res.code === "0000") {
                    this.accountPerson.push(...res.data);
                }
            });
        },
        saveBankInformation(){
            //历史bug 没有身份证号码，从新获取
            if(!this.bankCard.certNo){
                if(this.accountPerson && this.accountPerson.length > 0){
                    this.accountPerson.forEach(account => {
                        if(this.bankCard.accountName == account.custName){
                            this.bankCard.certNo = account.certNo;
                        }
                    });
                }
            }
            //直营保存
            if(this.isChannelBelong){
                this.bankCard.applyNo=this.row.applyNo;
                this.$refs.bankCardFrom.validate((valid) => {
                    if (valid) {
                        saveBankInformation(this.bankCard).then(res => {
                            if(res.data == '0000'){
                                this.$Message.success("保存成功");
                            }else{
                                 this.$Message.error("保存失败")
                            }
                        })
                    }
                })
            }else{
                let bankCardList = this.getBankData();
/*                if(bankCardList.length<1){
                    this.$Message.error("请验证扣款渠道后再保存");
                    return;
                }*/
                saveBankCard(bankCardList).then(res => {
                    if(res.code == '0000'){
                        this.$Message.success("保存成功");
                    }else{
                        this.$Message.error("保存失败");
                    }
                })
            }
        },
        //获取已绑定 银行卡信息
        getBankData(){
            let bankCardList = [];
            this.bankCardData.forEach(item=>{
                if(item.verStatus=='alreadySign'){
                     if(!item.certNo){
                            if(this.accountPerson && this.accountPerson.length > 0){
                                this.accountPerson.forEach(account => {
                                    if(item.accountName == account.custName){
                                        item.certNo = account.certNo;
                                    }
                                });
                            }
                        }
                    bankCardList.push(item);
                }
            })
            return bankCardList;
        },
        save(flag) {

            this.saveLoading=true;
            // 合同电子签约的时候没必要校验纸质合同是否签约，暂时先注释掉
            /*if(!this.hasContract){
                this.$Message.error("暂未生成合同, 请生成合同后提交!");
                this.saveLoading=false;
                return;
            }*/

            if(this.changeToEnterprise) {
                this.$refs.clientInfo.saveClientInfo();
                this.$Message.error("客户信息变化, 请重新生成合同后提交!");
                this.saveLoading = false;
                return;
            }
            //校验收款方账号信息 || 发票申请信息
            let flagA=this.checkSavePayee();
            if(this.isChannelBelong){
                this.$refs.InvoiceApplication.validate((valid) => {
                    if (!valid) {
                        flagA=false
                    }
                })
            }

            if(this.isShowAuthorizeWay) {
                this.$refs.formAuthorizeWay.validate((valid) => {
                    if (!valid) {
                        flagA=false
                    }
                })
            }

            if(this.formEntry.orderInfo.carType !== "11"){
                if(this.isShowIsDataPostStatus) {
                    this.$refs.formIsDataPostStatus.validate((valid) => {
                        if (!valid) {
                            flagA=false
                        }
                    })
                }
            }

            if(flagA===false){
                this.saveLoading=false;
                return
            }

            this.saveCar();

            //不是后补商业险 保存原有程序校验，如果是跳过校验
            if(this.businessIsLaterSupply){
                //商业险融资额//交强险融资额
                if(this.strongFinancingAmount>0||this.merchantFinancingAmount>0){
                    if(this.formEntry.orderInfo.carType==="2"){
                        if(this.strongFinancingAmount+this.merchantFinancingAmount-this.insuranceInfoBusiness.insuranceAmt-this.insuranceInfo.insuranceAmt>1000){
                            this.$Message.error("新车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于1000元");
                            // this.saveLoading=false;
                            // return;
                        }
                    }
                    if(this.formEntry.orderInfo.carType==="1"){
                        if(this.strongFinancingAmount+this.merchantFinancingAmount-this.insuranceInfoBusiness.insuranceAmt-this.insuranceInfo.insuranceAmt>=this.formEntry.carDetails.salePrice/100){
                            this.$Message.error("商用车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于车价的1%");
                            // this.saveLoading=false;
                            // return;
                        }
                    }
                }
                // 处理保险信息的结束日期不能早于开始日期，默认间隔一年，可修改的需求问题
                //  新车：保单生效日距放款申请日原则上不得早于第一次放款申请日30天，不得晚于提交放款第二天零点；
                // 如生效日期晚于提交放款15天后，需提交放款页面提示“保单超期，请提供批单修改生效日期”；
                // 如早于30天或晚于提交放款第二天零点，在提交放款页面提示“保单超期，请提供特殊投保承诺函”，同时放款审核页面保单生效日期标红；
                console.log("this.isShowInsureInfo12345" + this.isShowInsureInfo)
                if (this.isShowInsureInfo) {
                    if (this.insuranceInfoBusiness.insuranceStartTime == "") {
                        this.$Message.error("商业险生效日期不能为空！");
                        this.saveLoading = false;
                        return;
                    }
                    var day = AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                    var insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfoBusiness.insuranceStartTime, 'yyyy-MM-dd');
                    if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("保单超期，请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                    }
                    if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("保单超期，请提供批单修改生效日期");
                    }
                    if (this.insuranceInfo.insuranceStartTime == "") {
                        this.$Message.error("交强险生效日期不能为空！");
                        this.saveLoading = false;
                        return;
                    }
                    insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfo.insuranceStartTime, 'yyyy-MM-dd');
                    if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("保单超期，请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("保单超期，请提供批单修改生效日期或请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("保单超期，请提供批单修改生效日期");
                    }
                }
            }

            if (this.formEntry.orderInfo.businessType === "02") {
                this.isViewDecorationInvoice =false;
                this.isViewInvoice =false;
            }
            if (this.formEntry.orderInfo.carType === "11") {
                this.isViewInvoice = false;
                this.isAuctionCar = false;
            }
            this.formEntry.applyNo = this.applyNo;
            this.formEntry.bankCard[0]=this.bankCard;
            this.formEntry.carDetails=this.carDetails;
            this.carDetails.carColor = this.certificateDiscern.carColor;
            this.carDetails.gbCode = this.certificateDiscern.gbCode;
            this.carDetails.engineNo = this.certificateDiscern.engineNo;
            this.carDetails.carVin = this.certificateDiscern.carVin;
            this.formEntry.flag = flag;
            if(this.isChannelBelong){
                this.InvoiceApplication.applyNo=this.applyNo;
                this.InvoiceApplication.sendJsonAddress=JSON.stringify(this.InvoiceApplication.sendAddressBak)
                this.InvoiceApplication.businessJsonAddress=JSON.stringify(this.InvoiceApplication.businessAddressBak)
                this.formEntry.applyReceiptInfo=this.InvoiceApplication
            }else{
                this.formEntry.applyReceiptInfo={}
            }
            this.contractInfoForm.forEach(item=>{
                item.applyNo=this.applyNo
            })
            this.formEntry.applyPayeeInfoList=this.contractInfoForm
            // 个人转个体工商户 增加字段
            this.formEntry.ifPersonalToEnterprise = this.$refs.clientInfo.setSubmitData().ifPersonalToEnterprise;
            getCompareInfo(this.formEntry).then(res => {
                if (res.code === "0000") {
                    if(res.data.isSign === "1"){
                        this.$Modal.confirm({
                            title: "修改内容涉及"+res.msg+"模板重出，是否确认修改？",
                            onOk: () => {
                                this.loading=true;
                                this.spinShow = true;
                                if (Object.is(flag, "forever")) {
                                    this.isSave = 1;
                                    this.saveBackend(flag);
                                } else {
                                    this.saveBackend(flag);
                                }
                            },
                            onCancel: ()=>{
                                this.loanInfoUploadBtn();
                                this.getEntryPageInfo();
                                this.setDisabled();
                                this.saveLoading=false;
                            }
                        });
                    }else {
                        this.loading=true;
                        this.spinShow = true;
                        if (Object.is(flag, "forever")) {
                            this.isSave = 1;
                            this.saveBackend(flag);
                        } else {
                            this.saveBackend(flag);
                        }
                    }
                } else {
                    this.saveLoading=false;
                    this.$Message.error("信息比对失败！");
                }
            });
        },
        saveBackend(flag) {
                /*if (typeof this.certificateDiscern.vehicleMadeDate === "undefined" || this.certificateDiscern.vehicleMadeDate === "") {
                        this.$Message.error("请选择车辆制造日期");
                        this.saveLoading = false;
                        this.$Loading.finish()
                        return;
                    }*/

                //不是后补商业险 保存原有程序校验，如果是跳过校验
               if(this.businessIsLaterSupply){
                   if (this.isShowInsureInfo) {
                    if (this.insuranceInfoBusiness.insuranceMoney <this.leaseSumMoney) {
                        this.$Message.error("车损险保额必须大于融资额");
                        // this.saveLoading = false;
                        // this.loading=false;
                        // return false;
                    }
                    if (typeof this.insuranceInfoBusiness.thirdInsurance === "undefined" || this.insuranceInfoBusiness.thirdInsurance === "") {
                        this.$Message.error("请填写三者险");
                        this.saveLoading = false;
                        this.loading=false;
                        return false;
                    }
                    if (this.insuranceInfoBusiness.thirdInsurance <500000) {
                        this.$Message.error("三者险不能少于50万");
                        // this.saveLoading = false;
                        // this.loading=false;
                        // return false;
                    }
                   }
            }
            // 校验客户信息
            if (!this.$refs.clientInfo.checkClientInfo()) {
                this.$Message.error('请完善客户信息');
                this.saveLoading = false;
                this.loading = false;
                this.$Loading.finish();
                return;
            }
            this.formEntry.carDetails=this.carDetails;
            this.carDetails.carColor = this.certificateDiscern.carColor;
            this.carDetails.gbCode = this.certificateDiscern.gbCode;
            this.carDetails.engineNo = this.certificateDiscern.engineNo;
            this.carDetails.carVin = this.certificateDiscern.carVin;
            this.formEntry.flag = flag;
            //将applyNo填充到每个对象里
            this.formEntry.applyNo = this.applyNo;
            this.formEntry.orderInfo.applyNo = this.applyNo;
            this.formEntry.carInvoice.applyNo = this.applyNo;
            if (typeof this.formEntry.carInvoice.saleCertEndDate != "undefined" && this.formEntry.carInvoice.saleCertEndDate != "") {
                this.formEntry.carInvoice.saleCertEndDate = utils.formatDate(this.formEntry.carInvoice.saleCertEndDate, 'YYYY-MM-DD hh:mm:ss');
            } else {
                this.formEntry.carInvoice.saleCertEndDate = "";
            }
            if(this.formEntry.carDetails.vehicleMadeDate){
                this.formEntry.carDetails.vehicleMadeDate = utils.formatDate(this.formEntry.carDetails.vehicleMadeDate, 'YYYY-MM-DD');
            }else{
                this.formEntry.carDetails.vehicleMadeDate=null;
            }
            this.bankCard.applyNo = this.applyNo;
            this.formEntry.carDetails.applyNo = this.applyNo;
            this.formEntry.contractInfo.applyNo = this.applyNo;
            this.formEntry.orderInfo.applyNo = this.applyNo;
            this.insuranceInfo.applyNo = this.applyNo;
            this.insuranceInfoBusiness.applyNo = this.applyNo;

            //商业险 code
            this.insuranceInfoBusiness.insuranceType = "business";
            //交强险 code
            this.insuranceInfo.insuranceType = "compulsory";
            this.insuranceInfo.insuranceMode = "electronics";
            this.formEntry.pcOrApp=this.pcOrApp;
            //保险信息录入（商业险）
            //保险信息录入（交强险）
            this.formEntry.insuranceInfos = [];
            this.formEntry.insuranceInfos[0] = this.insuranceInfoBusiness;
            this.formEntry.insuranceInfos[1] = this.insuranceInfo;
            //发票信息 修改原先 ，将数据组装到carInvoice
            this.formEntry.carInvoice = this.invoiceDiscern;
            this.formEntry.carInvoice.invoiceType = "1"
            //车辆基本信息
            this.formEntry.carDetails.carColor = this.certificateDiscern.carColor;
            this.formEntry.carDetails.engineNo = this.certificateDiscern.engineNo;
            this.formEntry.carDetails.carColor = this.certificateDiscern.carColor;
            this.formEntry.carDetails.carVin = this.certificateDiscern.carVin;
            //GPS信息是否展示
            if(this.isViewGps==false){
                this.formEntry.gpsInfo= {};
            }else{
                this.formEntry.gpsInfo=this.gpsInfo;
                this.formEntry.gpsInfo.loanTerm = this.loanTerm;
                this.formEntry.gpsInfo.applyNo = this.applyNo;
                this.formEntry.gpsInfo.isRobberyBag = 0;
                if (typeof this.formEntry.gpsInfo.preInstallTime != "undefined" && this.formEntry.gpsInfo.preInstallTime != "") {
                    this.formEntry.gpsInfo.preInstallTime = utils.formatDate(this.formEntry.gpsInfo.preInstallTime, 'YYYY-MM-DD hh:mm:ss');
                } else {
                    this.formEntry.gpsInfo.preInstallTime = "";
                }
            }
            //精品装潢信息是否展示
            if(this.isViewDecorationInvoice===false){
                this.formEntry.decorationInvoiceForm=null;
            }else{
                //发票信息 保存
                this.formEntry.decorationInvoiceForm=this.decorationInvoiceForm;
                this.formEntry.decorationInvoiceForm.applyNo = this.applyNo;
                this.formEntry.decorationInvoiceForm.invoiceType = "2";
            }
            //this.formEntry.carDetails = this.certificateDiscern;
            //
            if(this.isChannelBelong){
                this.InvoiceApplication.applyNo=this.applyNo;
                this.InvoiceApplication.sendJsonAddress=JSON.stringify(this.InvoiceApplication.sendAddressBak)
                this.InvoiceApplication.businessJsonAddress=JSON.stringify(this.InvoiceApplication.businessAddressBak)
                this.formEntry.applyReceiptInfo=this.InvoiceApplication
            }else{
                this.formEntry.applyReceiptInfo={}
            }
            this.contractInfoForm.forEach(item=>{
                item.applyNo=this.applyNo
            })
            this.formEntry.applyPayeeInfoList=this.contractInfoForm
            //设置客户信息
            const clientInfo = this.$refs.clientInfo.setSubmitData();
            this.formEntry = {...this.formEntry, ...clientInfo};
            //收款账号绑定
            saveToBackend(this.formEntry).then(res => {
                this.saveLoading = true;
                if (res.code === "0000"){
                    this.loading=false;
                    this.isInt = false;
                    this.queryPayee();
                    this.$Message.success("保存成功");
                    this.saveLoading = false;
                    if (flag === "forever") {
                        this.openTabs = 'uploadInfo';
                    }
                    this.loanInfoUploadBtn();
                    this.getEntryPageInfo();
                } else {
                    this.$Message.error("保存失败");
                    this.saveLoading = false;
                    this.loading=false;

                }
                this.spinShow = false;
            }).catch(error=>{
                this.loading=false;
                this.saveLoading=false;
            });
        },
        //签约方式change事件
        changeVerType(val) {
            this.bankCard.certNo="";
            this.bankCard.bankCode="";
            this.bankCard.accountNo="";
            this.bankCard.openCity="";
            this.bankCard.openProvince="";
            this.bankCard.openAddressTemp=[];
            if (Object.is(val, "offline")) {
                this.isBankDisabled = false;
            } else if (Object.is(val, "online")) {
                this.isBankDisabled = true;
            }
        },
        //30天内过期的证件，表格给底色
        rowClassName(row, index) {
            if (row.isExpire) {
                return 'demo-table-info-row';
            }
            return null;
        },
        businessDate() {
            // 处理保险信息的结束日期不能早于开始日期，默认间隔一年，可修改的需求问题
            //  新车：保单生效日距放款申请日原则上不得早于第一次放款申请日30天，不得晚于提交放款第二天零点；
            // 如生效日期晚于提交放款15天后，需提交放款页面提示“保单超期，请提供批单修改生效日期”；
            // 如早于30天或晚于提交放款第二天零点，在提交放款页面提示“保单超期，请提供特殊投保承诺函”，同时放款审核页面保单生效日期标红；
            console.log("this.isShowInsureInfo2" + this.isShowInsureInfo)
            if (this.isShowInsureInfo) {
                if (this.insuranceInfoBusiness.insuranceStartTime == "") {
                    this.$Message.error("商业险生效日期不能为空！");
                    return;
                }
                this.insuranceInfoBusiness.insuranceEndTime = AfsUtils.getWhatDay(AfsUtils.getWhatYear(this.insuranceInfoBusiness.insuranceStartTime, 1), -1);
                var day = AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                var insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfoBusiness.insuranceStartTime, 'yyyy-MM-dd');
                if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("商业险保单超期，请提供特殊投保承诺函");
                }
                if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                }
                if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                }
            }
        },
        strongDate(val) {
            if (this.isShowInsureInfo) {
                if (this.insuranceInfo.insuranceStartTime == "") {
                    this.$Message.error("交强险生效日期不能为空！");
                    return;
                }
                this.insuranceInfo.insuranceEndTime = AfsUtils.getWhatDay(AfsUtils.getWhatYear(this.insuranceInfo.insuranceStartTime, 1), -1);
                var day = AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                var insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfo.insuranceStartTime, 'yyyy-MM-dd');
                if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("交强险保单超期，请提供特殊投保承诺函");
                }
                if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("交强险保单超期，请提供批单修改生效日期或请提供特殊投保承诺函");
                }
                if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                    this.$Message.error("交强险保单超期，请提供批单修改生效日期");
                }
            }
        },
        getTime(v) {
            var year = v.getFullYear() + 1; //年 ,从 Date 对象以四位数字返回年份
            var month = v.getMonth() + 1; //月 ,从 Date 对象返回月份 (0 ~ 11) ,date.getMonth()比实际月份少 1 个月
            var day = v.getDate() - 1; //日 ,从 Date 对象返回一个月中的某一天 (1 ~ 31)
            var hours = v.getHours(); //小时 ,返回 Date 对象的小时 (0 ~ 23)
            var minutes = v.getMinutes(); //分钟 ,返回 Date 对象的分钟 (0 ~ 59)
            var seconds = v.getSeconds(); //秒 ,返回 Date 对象的秒数 (0 ~ 59)
            //修改月份格式
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            //修改日期格式
            if (day >= 0 && day <= 9) {
                day = "0" + day;
            }
            //修改小时格式
            if (hours >= 0 && hours <= 9) {
                hours = "0" + hours;
            }
            //修改分钟格式
            if (minutes >= 0 && minutes <= 9) {
                minutes = "0" + minutes;
            }
            //修改秒格式
            if (seconds >= 0 && seconds <= 9) {
                seconds = "0" + seconds;
            }
            //格式(yyyy-mm-dd hh:mm:ss)
            var currentFormatDate =
                year +
                "-" +
                month +
                "-" +
                day +
                " " +
                hours +
                ":" +
                minutes +
                ":" +
                seconds;
            return currentFormatDate;
        },
        toggleOverlay() {
            this.showOverlay = !this.showOverlay; // 切换蒙层的显示状态
        },
        //影像件校验
        checkFileMethed() {

            this.isDataPostStatus = this.formEntry.contractInfo.isDataPostStatus;
            if('0' === this.isDataPostStatus && this.isShowIsDataPostStatus && this.isAuctionCar && this.formEntry.contractInfo.backTimes >= 2){

                // 如果是未开启资料后置，且退回次数到两次以上，则判断是否今天是否是节假日
                getCurrentSysHolidayInfo().then(res => {
                    if(res.code === '0000'){
                        if(res.data === '1'){
                            this.isShowWarn  = true;
                        }else{
                            this.checkFile();
                        }
                    }else{
                        this.$Message.error("节假日信息获取失败！");
                    }
                });
            }else{
                this.checkFile();
            }
        },

        closeShow(){
            this.isShowWarn = false;
        },

        checkFile(){
            this.isShowWarn = false;
            this.toggleOverlay();
            if(!this.cardAlreadySign()){
                this.$Message.error("操作失败，当前还款卡暂未完成授权");
                this.toggleOverlay();
                return;
            }
            let flagA=this.checkSavePayee()
            if(flagA==false){
                this.saveLoading=false;
                this.toggleOverlay();
                return
            }
            // 合同电子签约的时候没必要校验纸质合同是否签约，暂时先注释掉
            /*console.log("311开始影像件i奥眼中的合同重出校验。。。。。")
            if(!this.hasContract){
                this.$Message.error("暂未生成合同, 请生成合同后提交!");
                return;
            }*/

            this.$Loading.start()
            // 校验客户信息
            if (!this.$refs.clientInfo.checkClientInfo()) {
                this.$Message.error('请完善客户信息');
                this.saveLoading = false;
                this.toggleOverlay();
                this.$Loading.finish();
                return;
            }
            /*if (typeof this.certificateDiscern.vehicleMadeDate === "undefined" || this.certificateDiscern.vehicleMadeDate === "") {
                    this.$Loading.finish()
                    this.$Message.error("请选择车辆制造日期");
                    this.saveLoading = false;
                    return;
                }*/
            if(this.businessIsLaterSupply){
                if(this.strongFinancingAmount>0||this.merchantFinancingAmount>0){
                    if(this.formEntry.orderInfo.carType==="2"){
                        if(this.strongFinancingAmount+this.merchantFinancingAmount-this.insuranceInfoBusiness.insuranceAmt-this.insuranceInfo.insuranceAmt>1000){
                            this.$Message.error("新车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于1000元");
                            // this.$Loading.finish()
                            // return;
                        }
                    }
                    if(this.formEntry.orderInfo.carType==="1"){
                        if(this.strongFinancingAmount+this.merchantFinancingAmount-this.insuranceInfoBusiness.insuranceAmt-this.insuranceInfo.insuranceAmt>=this.formEntry.carDetails.salePrice/100){
                            this.$Message.error("商用车：商业险融资额+交强险融资额-商业险实际保费-交强险实际保费不得高于车价的1%");
                            // this.$Loading.finish()
                            // return;
                        }
                    }
                }
                console.log("this.isShowInsureInfo3" + this.isShowInsureInfo)
                if (this.isShowInsureInfo) {
                    if (this.insuranceInfoBusiness.insuranceStartTime == "") {
                        this.$Message.error("商业险生效日期不能为空！");
                        this.$Loading.finish()
                        return;
                    }
                    var day = AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                    var insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfoBusiness.insuranceStartTime, 'yyyy-MM-dd');
                    if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("商业险保单超期，请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                    }
                    if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("商业险保单超期，请提供批单修改生效日期");
                    }

                    if (this.insuranceInfo.insuranceStartTime == "") {
                        this.$Message.error("交强险生效日期不能为空");
                        this.$Loading.finish()
                        return;
                    }
                    day = AfsUtils.toDateString(new Date(), 'yyyy-MM-dd');
                    insuranceStartTimeDay = AfsUtils.toDateString(this.insuranceInfo.insuranceStartTime, 'yyyy-MM-dd');
                    if (AfsUtils.getWhatDay(day, -30) > AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("交强险保单超期，请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 15) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("交强险保单超期，请提供批单修改生效日期或请提供特殊投保承诺函");
                    }
                    if (AfsUtils.getWhatDay(day, 1) < AfsUtils.getWhatDay(insuranceStartTimeDay, 0) && AfsUtils.getWhatDay(day, 15) >= AfsUtils.getWhatDay(insuranceStartTimeDay, 0)) {
                        this.$Message.error("交强险保单超期，请提供批单修改生效日期");
                    }
                }
            }

            let params = {
                applyNo: this.applyNo,
            }

            getFilterParams(params).then(res => {
                if (res.code === '0000') {
                    this.uploadParam.busiNo = this.contractNo;
                    this.uploadParam.busiData = res.data;
                    this.saveLoading = true;
                    if (this.uploadParam.busiNo != '') {
                        checkFile(this.uploadParam).then(res => {
                            if (res.code === '0000') {
                                this.imageFlag = true;
                                this.submitBusiness();
                            }
                        }).catch(error=>{
                            this.saveLoading = false;
                            this.toggleOverlay();
                        })
                    } else {
                        this.saveLoading = false;
                        this.toggleOverlay();
                        this.$Message.info("温馨提示:提交前请检查是否有未录入的资料");
                    }
                }
            });
        },

        //页面提交按钮，提交流程审批
        submitBusiness() {
             //校验收款方账号信息 || 发票申请信息
            let flagA=this.checkSavePayee();
            if(this.isChannelBelong){
                this.$refs.InvoiceApplication.validate((valid) => {
                    if (!valid) {
                        flagA=false
                    }
                })
            }
            if(this.isShowAuthorizeWay) {
                this.$refs.formAuthorizeWay.validate((valid) => {
                    if (!valid) {
                        flagA=false
                    }
                })
            }

            if(flagA==false){
                this.saveLoading = false;
                this.loading=false;
                this.$Loading.finish();
                this.toggleOverlay();
                return
            }
            //GPS信息是否展示
            if(this.isViewGps==false){
                this.formEntry.gpsInfo= {};
            }else{
                if(this.formEntry.gpsInfo){
                    console.log("是否已派单",this.formEntry.contractInfo.gpsStatus,!this.formEntry.contractInfo.gpsStatus)
                    //未派单校验
                    if(!this.formEntry.contractInfo.gpsStatus){
                        this.$Message.error("GPS未派单!");
                        this.saveLoading = false;
                        this.loading=false;
                        this.$Loading.finish()
                        this.toggleOverlay();
                        return false;
                    }
                    //分直营 校验
                    //直营满足条件 this.gpsInfo.locationStatus == "1" &&  this.formEntry.contractInfo.gpsStatus!='sendTask'
                    //gpsStatus=sendTask已派单 gpsStatus=endExamine已安装 gpsStatus= null&undefined 未派单未保存
                    // if((this.gpsInfo.locationStatus == "1" &&  this.formEntry.contractInfo.gpsStatus!='sendTask') || !this.isChannelBelong){
                    if(this.gpsInfo.locationStatus == "1" || !this.isChannelBelong){
                        if(this.formEntry.contractInfo.gpsStatus != 'endExamine' &&this.formEntry.gpsInfo.applyStatus != 'completeInstall'){
                            this.$Message.error("GPS未安装成功,请确认已安装!");
                            this.saveLoading = false;
                            this.loading=false;
                            this.$Loading.finish()
                            this.toggleOverlay();
                            return false;
                        }
                        //提交前定位
                        if (typeof this.carDetails.carVin === "undefined" || this.carDetails.carVin === "") {
                            this.$Message.error("请填写VIN码");
                            this.saveLoading = false;
                            this.loading=false;
                            this.$Loading.finish()
                            this.toggleOverlay();
                            return;
                        }
                        if (typeof this.gpsInfo.gpsSupplier === "undefined" || this.gpsInfo.gpsSupplier === "") {
                            this.$Message.error("请选择GPS厂商");
                            this.saveLoading = false;
                            this.loading=false;
                            this.$Loading.finish()
                            this.toggleOverlay();
                            return;
                        }
                        this.gpsInfo.applyNo = this.applyNo;
                        // this.formEntry.gpsInfo.carVin = this.formEntry.carDetails.carVin;
                        this.gpsInfo.carVin =this.certificateDiscern.carVin;
                        locationbute(this.formEntry.gpsInfo).then(res => {
                            if(res.code == '0000'){
                                console.log("成功定位");
                                if(res.data == null || res.data.length == 0){
                                    this.$Message.error("未检测到无线GPS设备！");
                                    this.saveLoading = false;
                                    this.loading=false;
                                    this.toggleOverlay();
                                    return false;
                                }else{
                                    this.$Message.success("GPS定位成功");
                                }
                            }else{
                                this.$Message.error("未检测到有线GPS设备与无线GPS设备！");
                                this.saveLoading = false;
                                this.loading=false;
                                this.toggleOverlay();
                                return false;
                            }
                        }).catch(error => {
                            this.$Message.error("GPS定位失败！");
                            this.saveLoading = false;
                            this.loading=false;
                            this.toggleOverlay();
                            return false;

                        });
                    }
                }
            }
            console.log("this.isShowInsureInfo1234454" + this.isShowInsureInfo)
            if (this.isShowInsureInfo) {
                if(this.businessIsLaterSupply){
                        if (this.insuranceInfoBusiness.insuranceMoney <this.leaseSumMoney) {
                            this.$Message.error("车损险保额必须大于融资额");
                            // this.saveLoading = false;
                            // this.loading=false;
                            // return false;
                        }
                        if (typeof this.insuranceInfoBusiness.thirdInsurance === "undefined" || this.insuranceInfoBusiness.thirdInsurance === "") {
                            this.$Message.error("请填写三者险");
                            this.saveLoading = false;
                            this.loading=false;
                            this.toggleOverlay();
                            return false;
                        }
                        if (this.insuranceInfoBusiness.thirdInsurance <500000) {
                            this.$Message.error("三者险不能少于50万");
                            // this.saveLoading = false;
                            // this.loading=false;
                            // return false;
                        }
                }
            }

            this.formEntry.flag = "forever";
            //将applyNo填充到每个对象里
            this.formEntry.applyNo = this.applyNo;
            this.formEntry.orderInfo.applyNo = this.applyNo;
            this.formEntry.carInvoice.applyNo = this.applyNo;
            this.bankCard.applyNo = this.applyNo;
            this.formEntry.carDetails.applyNo = this.applyNo;
            this.formEntry.contractInfo.applyNo = this.applyNo;
            this.formEntry.orderInfo.applyNo = this.applyNo;
            this.insuranceInfo.applyNo = this.applyNo;
            this.insuranceInfoBusiness.applyNo = this.applyNo;
            //商业险 code
            this.insuranceInfoBusiness.insuranceType = "business";
            //交强险 code
            this.insuranceInfo.insuranceType = "compulsory";
            this.formEntry.pcOrApp=this.pcOrApp;
            this.insuranceInfo.insuranceMode = this.insuranceInfoBusiness.insuranceMode;
            //精品装潢信息是否展示
            if(this.isViewDecorationInvoice===false){
                this.formEntry.decorationInvoiceForm=null;
            }else{
                //发票信息 保存
                this.formEntry.decorationInvoiceForm=this.decorationInvoiceForm;
                this.formEntry.decorationInvoiceForm.applyNo = this.applyNo;
                this.formEntry.decorationInvoiceForm.invoiceType = "2";
            }
            //检测发票低开（去掉，改提交时后台校验-wjy修改）
            //this.checkInvoice();
            let loanAllMoney = 0;
            for (let i = 0; i < this.formEntry.costDetails.length; i++) {
                loanAllMoney += this.formEntry.costDetails[i].loanAmt;
            }
            console.log(this.quotaControlSwitch);
            if (this.quotaControlSwitch === '1') {
                if (this.formEntry.contractInfo.lendingMode === "02") {
                    if (this.formEntry.orderInfo.businessType === "01") {
                        if (loanAllMoney > this.newSurplus) {
                            this.$Modal.confirm({
                                title: "新车先放后抵额度不足，是否确认提交？",
                                onOk: () => {
                                   this.saveToBackendFun();
                                }
                            });
                        } else {
                           this.saveToBackendFun();
                        }
                    } else {
                        if (loanAllMoney > this.oldSurplus) {
                            this.$Modal.confirm({
                                title: "二手车先放后抵额度不足，是否确认提交？",
                                onOk: () => {
                                    this.saveToBackendFun();
                                }
                            });
                        } else {
                            this.saveToBackendFun();
                        }
                    }
                } else {
                   this.saveToBackendFun();
                }
            } else {
                this.saveToBackendFun();
            }
        },
        saveToBackendFun(){
            this.spinShow = true;
            //新增业务
            if(this.isChannelBelong){
                this.InvoiceApplication.applyNo=this.applyNo;
                this.InvoiceApplication.sendJsonAddress=JSON.stringify(this.InvoiceApplication.sendAddressBak)
                this.InvoiceApplication.businessJsonAddress=JSON.stringify(this.InvoiceApplication.businessAddressBak)
                this.formEntry.applyReceiptInfo=this.InvoiceApplication
            }else{
                this.formEntry.applyReceiptInfo={}
            }
            this.formEntry.applyPayeeInfoList=this.contractInfoForm
            //设置客户信息
            const clientInfo = this.$refs.clientInfo.setSubmitData();
            this.formEntry = {...this.formEntry, ...clientInfo};
            saveToBackend(this.formEntry).then(res => {
                if (res.code === "0000") {
                    let param = {
                        applyNo: this.applyNo,
                        contractNo: this.contractNo,
                        submitFlag: this.contractStatus,
                        userRelName: this.userRelName,
                        phone: this.phone
                    };
                    loanApplySubmitInfo(param).then(result => {
                        if (result.code === "0000") {
                            this.$Message.success("申请成功");
                            this.afs.closeTab(this);
                            this.spinShow = false;
                            this.saveLoading = false;
                            this.toggleOverlay();
                        }
                    }).catch(error => {
                        this.saveLoading = false;
                        this.toggleOverlay();
                    });
                } else {
                    this.spinShow = false;
                    this.$Message.error("保存失败");
                    this.toggleOverlay();
                }
            }).catch(error => {
                this.saveLoading = false;
                this.toggleOverlay();
            });
        },
        cancleModalClick(){
            this.isShowConfirm =false;
        },
        //重新生成合同
        confirmModalClick(){
            this.isShowConfirm =false;
            this.templateModel = false;
            if(this.isRegenerate){
                this.contractBatchPrint();
            }else{
                let opt ={
                    "applyNo": this.applyNo,
                    "contractNo": this.contractNo
                }
                this.leaseNoticePrint(opt)
            }
            this.init();
        },

        downloadFile() {
            this.checkHasContract();
        },

        //确定二级弹框
        updateFile(){
            this.isShowConfirm = true;
        },
        //GPS派单按钮
        distribute() {
            this.$refs.gpsInfo.validate((valid) => {
                if (valid) {
                    this.$Loading.start()
                    //封装请求参数
                    this.formEntry.gpsInfo.loanTerm = this.gpsLoanTerm;
                    this.formEntry.gpsInfo.isRobberyBag = 0;
                    this.formEntry.gpsInfo.carVin = this.certificateDiscern.carVin;
                    this.formEntry.gpsInfo.carId = this.carDetails.id;
                    this.formEntry.gpsInfo.contractNo = this.contractNo;
                    this.formEntry.gpsInfo.applyNo = this.applyNo;
                    this.formEntry.gpsInfo.invoiceAmt = this.invoiceDiscern.invoiceAmt;
                    this.formEntry.gpsInfo.installAddressTemp = this.gpsInfo.installAddressTemp;
                    this.formEntry.gpsInfo.installAddress = this.gpsInfo.installAddress;
                    this.formEntry.gpsInfo.preInstallTime = this.gpsInfo.preInstallTime;
                    this.formEntry.gpsInfo.siteContact = this.gpsInfo.siteContact;
                    this.formEntry.gpsInfo.contactPhone = this.gpsInfo.contactPhone;
                    this.formEntry.gpsInfo.gpsSupplier = this.gpsInfo.gpsSupplier;

                    this.spinShow = true;
                    this.loading = true;
                    distribute(this.formEntry.gpsInfo).then(res => {
                        this.distributeLoading = true;
                        if (res.code === "0000") {
                            this.formEntry.contractInfo.gpsStatus = 'endExamineNext';
                            this.$Loading.finish()
                            this.spinShow = false;
                            this.distributeLoading = false;
                            this.isDistribute = true;
                            this.saveLoading = false;
                            this.loading = false;
                            this.isDistributeDisabled = true;
                            this.isGPSDistribute = true;
                            //派单成功置灰派单按钮
                            this.isGPSDistributeLeaflets = true;
                            //派单成功放开GPS定位按钮
                            this.isDisabledLocationbute = false;
                            this.$Message.success(res.msg);
                            this.$Loading.finish();
                        } else if (res.code === "9999") {
                            this.$Loading.error()
                            this.$Message.error(res.msg);
                            this.saveLoading = false;
                            this.spinShow = true;
                            this.loading = false;
                            this.distributeLoading = false;
                            this.$Loading.finish();
                        }
                    }).catch(error => {
                        this.$Loading.error()
                        this.saveLoading = false;
                        console.log(error);
                        this.spinShow = false;
                        this.loading = false;
                        this.distributeLoading = false;
                    });
                }
            })
        },
        //GPS定位按钮
        locationbute() {
            this.$Loading.start()
            if (typeof this.carDetails.carVin === "undefined" || this.carDetails.carVin === "") {
                this.$Message.error("请填写VIN码");
                this.saveLoading = false;
                return;
            }
            if (typeof this.gpsInfo.gpsSupplier === "undefined" || this.gpsInfo.gpsSupplier === "") {
                this.$Message.error("请选择GPS厂商");
                this.saveLoading = false;
                return;
            }
            this.gpsInfo.applyNo = this.applyNo;
            // this.formEntry.gpsInfo.carVin = this.formEntry.carDetails.carVin;
            this.gpsInfo.carVin =this.certificateDiscern.carVin;
            locationbute(this.formEntry.gpsInfo).then(res => {
                if(res.code == '0000'){
                    this.$Loading.finish()
                    //定位成功页面跳过gps排位
                    this.gpsInfo.locationStatus = "0";
                    this.formEntry.contractInfo.gpsStatus = 'endExamine';
                    this.formEntry.gpsInfo.applyStatus = 'completeInstall';
                    if(res.data == null || res.data.length == 0){
                        this.$Message.error("未检测到无线GPS设备！");
                        return;
                    }
                    //安装是否是有线+无线
                    if (this.formEntry.gpsInfo.gpsType === "wiredAndWireless") {
                        //展示无线
                        this.wirelessViewGps = true;
                        //展示有线
                        this.wireViewGps=true;
                    }else if (this.formEntry.gpsInfo.gpsType === "wired") {
                        //展示有线
                        this.wireViewGps=true;
                    }else if(this.formEntry.gpsInfo.gpsType === "wireless") {
                        //展示无线
                        this.wirelessViewGps = true;
                    }
                    res.data.forEach(resultDTOS => {
                        console.log("循环："+resultDTOS);
                        //是否有线
                        if(resultDTOS.typestr.indexOf("有线")>-1){
                            this.getAllEqPositionsReqDTO.isFlag=resultDTOS.equipment;
                            this.getAllEqPositionsReqDTO.sbcstatus=resultDTOS.sbcstatus;
                            this.getAllEqPositionsReqDTO.locationAdd=resultDTOS.locationAdd;
                        }else{
                            this.getAllEqPositionsReqDTO.isNotFlag=resultDTOS.equipment;
                            this.getAllEqPositionsReqDTO.isNotsbcStatus=resultDTOS.sbcstatus;
                            this.getAllEqPositionsReqDTO.locationAdd=resultDTOS.locationAdd;
                        }
                    });
                }else{
                    this.$Loading.finish()
                    this.$Message.error("未检测到有线GPS设备与无线设备！");
                }
            }).catch(error => {
                this.$Loading.error()
                // this.$Message.error("GPS发送请求失败！");
            });
        },
        //GPS保存按钮
        saveGpsInfo(){
            this.$refs.gpsInfo.validate((valid) => {
                if (valid) {
                    this.gpsInfo.loanTerm = this.gpsLoanTerm;
                    this.gpsInfo.isRobberyBag = 0;
                    this.gpsInfo.gpsType = this.formEntry.gpsType;
                    this.gpsInfo.carVin = this.certificateDiscern.carVin;
                    this.gpsInfo.carId = this.carDetails.id;
                    this.gpsInfo.contractNo = this.contractNo;
                    this.gpsInfo.applyNo = this.applyNo;
                    this.gpsInfo.invoiceAmt = this.invoiceDiscern.invoiceAmt;
                    //保存
                    saveGpsInfo(this.gpsInfo).then(res => {
                        if(res.code == '0000'){
                            console.log("成功保存");
                            if(!res.data){
                                //保存成功，派单按钮放开
                                this.isGPSDistributeLeaflets=false;
                            }
                            this.$Message.success("保存成功");
                        }else{
                            this.$Message.error("未检测到有线GPS设备与无线设备！");
                        }
                    }).catch(error => {
                        // this.$Message.error("GPS保存失败！");
                    });

                }
            })
        },
        saveApplyContractInfoPayee(){
            if (typeof this.formEntryContractInfoReceivingId === "undefined" || this.formEntryContractInfoReceivingId === "") {
                this.$Message.error("请选择收款人账号");
                this.saveLoading = false;
                return;
            }
            savePayee(this.contractInfoForm).then(res => {
                if (res.code === "0000") {
                    this.$Message.success("操作成功");
                }
            });
        },
        saveBusinessInsurance(){
            if (this.insuranceInfoBusiness.thirdInsurance <500000) {
                this.$Message.error("三者险不能少于50万");
                return false;
            }
             this.$refs.insuranceInfoBusiness.validate((valid) => {
                if (valid) {
                    this.insuranceInfoBusiness.applyNo = this.applyNo;
                    this.insuranceInfoBusiness.insuranceType = 'business';
                    //保险信息(商业险)保存按钮
                    saveInsurance(this.insuranceInfoBusiness).then(res => {
                        if(res.code == '0000'){
                            console.log("成功保存");
                            this.$Message.success("保存成功");
                        }else{
                            this.$Message.error("保存失败！");
                        }
                    }).catch(error => {
                        // this.$Message.error("GPS保存失败！");
                    });
                }
            })
        },
        saveCompulsory(){
            this.$refs.insuranceInfo.validate((valid) => {
                if (valid) {
                    this.insuranceInfo.applyNo = this.applyNo;
                    this.insuranceInfo.insuranceMode = "electronics";
                    this.insuranceInfo.insuranceType = 'compulsory';
                    //保险信息(交强险)保存按钮
                    //   this.formEntry.insuranceInfo;
                    saveInsurance(this.insuranceInfo).then(res => {
                        if(res.code == '0000'){
                            console.log("成功保存");
                            this.$Message.success("保存成功");
                        }else{
                            this.$Message.error("保存失败！");
                        }
                    }).catch(error => {
                        // this.$Message.error("GPS保存失败！");
                    });
                }
            });
        },
        saveCarInvoice(){
            if (typeof this.decorationInvoiceForm.invoiceUnit === "undefined" || this.decorationInvoiceForm.invoiceUnit === "") {
                this.$Message.error("请填写车辆开票单位");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.invoiceNo === "undefined" || this.decorationInvoiceForm.invoiceNo === "") {
                this.$Message.error("请填写车辆发票号码");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.invoiceCode === "undefined" || this.decorationInvoiceForm.invoiceCode === "") {
                this.$Message.error("请填写车辆发票代码");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.invoiceDate === "undefined" || this.decorationInvoiceForm.invoiceDate === "") {
                this.$Message.error("请填写开票日期");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.invoiceRate === "undefined" || this.decorationInvoiceForm.invoiceRate === "") {
                this.$Message.error("请填写发票税率");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.excludingTaxAmt === "undefined" || this.decorationInvoiceForm.excludingTaxAmt === "") {
                this.$Message.error("请填写不含税价");
                this.saveLoading = false;
                return;
            }
            if (typeof this.decorationInvoiceForm.invoiceAmt === "undefined" || this.decorationInvoiceForm.invoiceAmt === "") {
                this.$Message.error("请填写车辆发票金额");
                this.saveLoading = false;
                return;
            }
            if(typeof this.decorationInvoiceForm.invoiceRate >=100){
                this.$Message.error("发票税率填写错误");
                this.saveLoading = false;
                return;
            }
            this.decorationInvoiceForm.applyNo = this.applyNo;
            //精品装潢发票信息保存按钮
            this.decorationInvoiceForm.invoiceType = '2';
            saveCarInvoice(this.decorationInvoiceForm).then(res => {
                if(res.code == '0000'){
                    console.log("成功保存");
                    this.$Message.success("保存成功");
                }else{
                    this.$Message.error("保存失败！");
                }
            }).catch(error => {
                // this.$Message.error("GPS保存失败！");
            });
        },
        //车辆信息保存按钮
        saveCarDetails(){
            this.$refs.carDetails.validate((valid) => {
                if (valid) {
                    //ocr识别的车架号如果和当前不一致，提交失败
                    let ocv=this.invoiceDiscern.carVin;//车架号
                    let engineNo=this.invoiceDiscern.engineNo;//发动机号
                    // 这个地方统一改为后台提交的时候校验
                    /*if (ocv!=null&&ocv!==this.certificateDiscern.carVin) {
                        this.$Message.error("车架号与OCR识别的车架号不一致");
                        return;
                    }
                    if (engineNo!=null&&engineNo!==this.certificateDiscern.engineNo) {
                        this.$Message.error("发动机号与OCR识别的发动机号不一致");
                        return;
                    }*/
                        //车辆制造日期
                        this.carDetails.vehicleMadeDate=this.certificateDiscern.vehicleMadeDate;
                        //车辆颜色
                        this.carDetails.carColor = this.certificateDiscern.carColor;
                        //国标码
                        this.carDetails.gbCode = this.certificateDiscern.gbCode;
                        //发动机号
                        this.carDetails.engineNo = this.certificateDiscern.engineNo;
                        //车架号
                        this.carDetails.carVin = this.certificateDiscern.carVin;
                        this.carDetails.applyNo = this.applyNo;
                        if(!this.needShowConfirm){
                            this.saveCar();
                            return;
                        }
                        if(!this.checkCarDetailsUpdate()){
                            this.$Modal.confirm({
                                title: "修改内容涉及合同模板重出，是否确认修改？",
                                onOk: () => {
                                    // 保存车辆信息
                                    this.saveCar();
                                    // 撤销生成的合同
                                    // this.removeAllTemplete();
                                },
                                onCancel: ()=>{
                                }
                            });
                        }else {
                            this.saveCar();
                    }
                }
            })
        },
        contractSignRelationQuery(){
            checkHasContract({"contractNo": this.contractNo}).then(res => {
                if(res.code == '0000'){
                    this.hasContract = res.data;
                    this.needShowConfirm = res.data;
                }
            });
        },
        savaRemindDetails(){
            if(this.msgContent.length <= 1) {
                this.warn = true
                this.msgContent = ''
                setTimeout(() => {
                    this.warn = false;
                }, 1000)
            }else {
                let param = {
                    applyNo: this.applyNo,
                    msgContent: this.msgContent
                }
                savaRemindDetails(param).then(res => {
                    if (res.code === '0000') {
                        console.log("发送成功");
                        this.queryRemind();
                    }
                })
                this.msgContent = '';
            }
        },
        saveCar(){
            saveCarDetails(this.carDetails).then(res => {
                if(res.code == '0000'){
                    this.carDetailsOld = deepClone(this.carDetails);
                    this.getEntryPageInfo();
                    this.$Message.success("保存成功");
                }else{
                    this.$Message.error("保存失败！");
                }
            }).catch(error => {
                // this.$Message.error("GPS保存失败！");
            });
        },
        removeAllTemplete(){
            let param = {
                contractNo: this.contractNo,
            }
            removeAllTemplete(param).then(res => {
                if(res.code == '0000'){
                    console.log("删除成功");
                    this.$Message.success("合同重出，旧合同已删除成功，请重新生成");
                    this.hasContract = false;
                }else{
                    this.$Message.error("合同重出，删除失败，请手动重新生成合同并完成签约！");
                    this.hasContract = false;
                }
            }).catch(error => {
                // this.$Message.error("GPS保存失败！");
            });
        },
        checkCarDetailsUpdate() {
            // 合同重出校验取消对三个非必填的校验
            /*let timeString = utils.formatDate(this.carDetails.vehicleMadeDate, 'YYYY-MM-DD');
            console.log(timeString);
            if(typeof this.carDetailsOld.vehicleMadeDate == "object"){
                this.carDetailsOld.vehicleMadeDate = utils.formatDate(this.carDetailsOld.vehicleMadeDate, 'YYYY-MM-DD');
            }*/

            this.entryData = [];

            return  this.carDetailsOld.engineNo === this.carDetails.engineNo
                && this.carDetailsOld.carVin === this.carDetails.carVin;
        },
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;
            if (this.checkAll) {
                let codeList = this.fileList.map((item, index) => {
                    return item.attachmentCode;
                })
                this.checkAllGroup = codeList;
            } else {
                this.checkAllGroup = [];
            }
        },
        checkAllGroupChange(data) {
            if (data.length === this.fileList.length) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (data.length > 0) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
        },
        turnToUpload(name) {
            this.openTabs = name;
            if (name === 'uploadInfo' && this.saveFlag === '0') {
                this.save('temp');
            } else {
                this.loanInfoUploadBtn();
                this.getEntryPageInfo();
                this.setDisabled();
            }
        },
        getToken(){
            return  {
                accessToken:this.$store.getters.access_token,
                refreshToken:this.$store.getters.refresh_token,
                expiresIn:(this.$store.getters.expires_in-Date.now())/1000
            }
        },
        //商业险
        insuranceInfoBusinessInfoIsLaterSupply(){
           let isLaterSupply=this.insuranceInfoBusiness.isLaterSupply;
           if(isLaterSupply=="1"){
               this.businessIsLaterSupply=false

           }else{
               this.businessIsLaterSupply=true;
           }
        },
        //交强险
        insuranceInfoIsLaterSupply(){
           let isLaterSupply=this.insuranceInfo.isLaterSupply;
           if(isLaterSupply=="1"){
               this.jqxIsLaterSupply=false
           }else{
               this.jqxIsLaterSupply=true;
           }
        },
        carInvoiceIsLaterSupply(){
           let isLaterSupply=this.invoiceDiscern.isLaterSupply;
           if(isLaterSupply=="1"){
               this.fpIsLaterSupply=false
           }else{
               this.fpIsLaterSupply=true;
           }
        },
        //新增 收款方账号信息
        addContractInfo(){
            let array =this.contractInfoForm;
            array.push({
                    id:"",
                    applyNo:this.applyNo,
                    collectionType:'',
                    collectionAmount:null,
                    autoFlag:'1',
                    receiptAccount:'',
                    receiptId:"",
                    receiptBankCode:"",
                    delFlag:'0',
                    sendJsonAddress:"",
                })
            this.contractInfoForm=array
        },
        selectedItemFun(index){
            if(index=='sp'){
                this.itemCollectionType= index
            }else{
                this.itemCollectionType=this.contractInfoForm[index].collectionType
            }
            this.isShowAddModel=true;
            this.indexSelect=index;

        },
        /**********新增功能需求 开始   发票申请，*******************/
        addCloseModal(){
                this.isShowAddModel=false;
        },
        updataPayee(row){
            //sp 总对总
            if(this.indexSelect=='sp'){
                    this.formEntryContractInfoReceivingId=row.id
                    this.contractInfoForm=[{
                            receiptAccount:row.receivingName,
                            receiptBankCode:row.receivingAccount,
                            receiptId:row.id,
                            applyNo:this.applyNo,
                            id:this.contractInfoFormID
                    }]
            }else{
                //直营
                let contractInfoForm=this.contractInfoForm;
                contractInfoForm[this.indexSelect].receiptId=row.id
                contractInfoForm[this.indexSelect].receiptAccount=row.receivingName
                contractInfoForm[this.indexSelect].receiptBankCode=row.receivingAccount
                contractInfoForm[this.indexSelect].receiptType=row.collectionType
                this.contractInfoForm=contractInfoForm
            }
            this.isShowAddModel=false;
        },
        saveAuthorizeWay(){
            this.$refs.formAuthorizeWay.validate((valid) => {
                if (valid) {
                    saveAuthorizeWay(this.formEntry.contractInfo).then(res => {
                        if(res.code == '0000'){
                            this.$Message.success("保存成功");
                        }else{
                            this.$Message.error("保存失败！");
                        }
                    }).catch(error => {

                    });

                }
            })
        },
        stopCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval); // 清除定时器
                this.countdownInterval = null; // 重置定时器的引用
            }
            this.countdownActive = false; // 设置倒计时为非活动状态
        },
        startCountdown() {
            if (this.countdownActive) return; // 如果倒计时已经在运行，则不重复启动
                this.timeLeft = 5; // 重置倒计时时间
                this.countdownActive = true; // 设置倒计时为活动状态

                // 创建并存储定时器的引用
                this.countdownInterval = setInterval(() => {
                    this.timeLeft--;

                    if (this.timeLeft <= 0) {
                        this.stopCountdown(); // 倒计时结束时调用停止方法
                        this.buttonFlag = false;
                    }
                }, 1000); // 每秒更新一次
        },
        openStatus(){
           let isDataPostStatus = this.formEntry.contractInfo.isDataPostStatus
           if(isDataPostStatus=='1'){
                this.isOpenStatus=true;
                this.startCountdown();
           }else if(isDataPostStatus=='0'){
                this.saveIsDataPostStatus();
           }else{
                this.$Message.error("请选择资料后置是否开启");
           }
        },
        qxOpenStatus(){
            this.isOpenStatus=false;
            this.formEntry.contractInfo.isDataPostStatus = '0';
            this.saveDataPostWarnRecord('fail','');
            this.stopCountdown();
            this.buttonFlag = true;
        },
        /**
         *
         * @param operateType 是否同意，1-是，0-否
         * @param passType 自动同意 - auto，手动同意 - no_auto
         */
        saveDataPostWarnRecord(operateType,passType){
            let param = {
                applyNo: this.formEntry.contractInfo.applyNo,
                contractNo: this.formEntry.contractInfo.contractNo,
                passType: passType,
                operateType: operateType,
                warnContent: this.saveDataPostWarnContent,
            }
            saveApplyDataPostSaveRecord(param).then(res => {
                if(res.code == '0000'){
                    this.$Message.success("日志保存成功！");
                }else{
                    this.$Message.error("日志保存失败！");
                }
            }).catch(error => {

            });
        },
        saveIsDataPostStatus(){

            this.saveDataPostWarnRecord('pass','no_auto');
            saveIsDataPostStatus(this.formEntry.contractInfo).then(res => {
                if(res.code == '0000'){
                    this.$Message.success("保存成功");
                    this.isOpenStatus=false;
                    this.stopCountdown();
                    this.buttonFlag = true;
                    this.getEntryPageInfo();
                }else{
                    this.$Message.error("保存失败！");
                }
            }).catch(error => {

            });
        },
        noLogSaveIsDataPostStatus(){

            saveIsDataPostStatus(this.formEntry.contractInfo).then(res => {
                if(res.code == '0000'){
                    this.$Message.success("保存成功");
                    this.isOpenStatus=false;
                    this.stopCountdown();
                }else{
                    this.$Message.error("保存失败！");
                }
            }).catch(error => {

            });
        },
        saveReceipt(){
            this.$refs.InvoiceApplication.validate((valid) => {
                if (valid) {

                     this.InvoiceApplication.applyNo=this.applyNo;
                     this.InvoiceApplication.sendJsonAddress=JSON.stringify(this.InvoiceApplication.sendAddressBak)
                     this.InvoiceApplication.businessJsonAddress=JSON.stringify(this.InvoiceApplication.businessAddressBak)
                    saveReceipt(this.InvoiceApplication).then(res => {
                        if(res.code == '0000'){
                            console.log("成功保存");
                            this.$Message.success("保存成功");
                        }
                        if(res.data == "9999"){
                            console.log("保存失败");
                            this.$Message.error("保存失败")
                        }
                    }).catch(error => {
                        // this.$Message.error("银行卡保存失败！");
                    });
                }
            })
        },
        async savePayee(){
            //saveApplyContractInfoPayee 老代码业务
            let flag= await this.checkSavePayee();
            if(flag){
                this.savePayeeFlag=true
                this.contractInfoForm.forEach(item=>{
                    item.applyNo=this.applyNo
                    })
                    console.log(this.contractInfoForm);
                 savePayee(this.contractInfoForm).then(res => {
                    if (res.code === "0000") {
                        this.formEntryContractInfoReceivingId=this.contractInfoForm[0].receiptId;
                        this.queryPayee();
                        this.$Message.success("操作成功");
                    }else{
                        this.savePayeeFlag=false
                    }
                }).catch(e=>{
                        this.savePayeeFlag=false
                })
            }
        },
        checkSavePayee(){
            let  flag = true;
            if(this.businessType==="01" && this.formEntry.orderInfo.carType !== "11"){
                if(this.invoiceDiscern){
                    if(!this.isChannelBelong){
                        this.$refs.invoiceDiscern.validate((valid) => {
                            if(!valid){
                                this.$Message.error("发票信息未填写完整")
                                flag=false;
                            }
                        })
                        if(!flag){
                            return  flag;
                        }
                    }
                }
            }

            //非直营
            if(!this.isChannelBelong){
                if(!this.formEntryContractInfoReceivingId){
                    this.$Message.warning("请添加收款方账号信息");
                    return false
                }
                return flag;
             }
             //直营
            this.contractInfoForm.forEach(item=>{
                if((item.collectionType==3 || item.collectionType==4) && item.afterPayFlag!=1){
                    if(item.collectionAmount<=0 || item.collectionAmount>item.preCollectionAmount){
                        this.$Message.warning("实付金额不可高于“应付金额”,实付金额必须大于0");
                        flag=false;
                    }
                }
                if(item.afterPayFlag!=1 && !item.receiptAccount){
                     this.$Message.warning("请选择支付项（"+this.collectionTypeArray[item.collectionType]+"）收款方名称");
                     flag=false;
                }
                if(!item.autoFlag && item.afterPayFlag!=1 ){
                    this.$Message.warning("请选择【"+this.collectionTypeArray[item.collectionType]+"】的放款方式");
                    flag=false;
                }
            })
            return flag;
        },

        queryReceipt(){
            const opt={applyNo:this.applyNo}
            this.InvoiceApplication.invoiceFlag="0";
            queryReceipt(opt).then(res => {
                if (res.code === "0000" && res.data) {
                    this.InvoiceApplication= res.data
                    if(res.data.sendJsonAddress){
                        this.InvoiceApplication.sendAddressBak=JSON.parse(res.data.sendJsonAddress)
                    }
                    if(res.data.businessJsonAddress){
                        this.InvoiceApplication.businessAddressBak=JSON.parse(res.data.businessJsonAddress)
                    }
                    if(this.row.inputType=="0"){
                        this.InvoiceApplication.invoiceFlag="0";
                    }else{
                        this.InvoiceApplication.invoiceFlag="1";
                    }
                }
            });
        },
        queryPayee(){
            const opt={
                    "applyPayeeInfo": {
                        applyNo:this.applyNo
                    },
                    "paySplitFlag":this.paySplitFlag
                }
            queryPayee(opt).then(res => {
                if (res.code === "0000" && res.data) {
                    if(res.data.length>0){
                        let resData=res.data
                        if(this.isChannelBelong){
                            resData.forEach(item=>{
                                item.afterPayFlag=item.afterPayFlag*1;
                                if(item.collectionType==0 || item.collectionType==1){  //collectionType 0：全款 ，1长款
                                    this.formEntryContractInfoReceivingId=item.receiptId;
                                }
                            })
                        }else{
                            this.contractInfoFormID=res.data[0].id
                            this.formEntryContractInfoReceivingId=res.data[0].receiptId;

                        }
                        this.contractInfoForm=resData;
                    }
                }
                this.savePayeeFlag=false
                this.saveLoading = false;
            })
        },
        queryPayeeInfo(v){
            if(v.length>0){
                if(this.isChannelBelong){
                    v.forEach(item=>{
                        item.afterPayFlag=item.afterPayFlag*1;
                        if(item.collectionType==0 || item.collectionType==1){  //collectionType 0：全款 ，1长款
                            this.formEntryContractInfoReceivingId=item.receiptId;
                        }
                    })
                    this.contractInfoForm=v;
                }
            }
        },
        InvoiceApplicationFun(){
          if(this.InvoiceApplication.invoiceFlag=="0"){
                this.invoiceDisabledrequired=false;
            }else{
                this.invoiceDisabledrequired=true;
            }
            this.InvoiceApplicationValidateFun();
        },
        InvoiceApplicationValidateFun(){
            this.InvoiceApplicationValidate.businessAddressBak=[
                    {required:!this.mailRequired && this.invoiceDisabledrequired,message: '企业地址不能为空', trigger: 'change', type:'array'},
                    {validator:(rule,value,callback)=>{ applyUtils.addressArrayFive('企业地址',value,callback,2) },trigger:'change'}
                ]
        },
        /**********新增功能需求 结束*******************/
        /**********审批记录*******************/
        tagImage() {
            this.moveComment = !this.moveComment;
            console.log(this.moveComment)
        },
        closeComment() {
            this.moveComment = true;
        },
        // 查询留言
        queryRemind() {
            let param = {
                contractNo: this.contractNo,
            }
            getContractRemindList(param).then(res => {
                if (res.code == "0000") {
                    this.remindList = res.data;
                }
                // this.remindList.forEach(account=> {
                //      var rop =new RegExp('[A-Za-z]+');
                //     if(rop.test(account.backWords)){
                //         account.backWords = account.problemDesc;
                //     }
                // });

            })
        },
        /**********审批记录查询结束*******************/
        /** 银行卡发送绑定短信 */
        sendMessage(indexNum){
            let type=this.bankCardChannel[indexNum].key;
            if(!type){
                this.$Message.error("数据报错,请联系管理员");
                return false;
            }
            if(!this.checkBankCardData()){
                return false;
            }
            this.bankCard.applyNo = this.applyNo;
            this.bankCard.contractNo = this.contractNo;
            this.bankCard.verChannelBelong = type;
            let params = deepClone(this.bankCard);
            params.accountNo = String(params.accountNo).replace(/\s+/g,"");
            if(this.accountPerson && this.accountPerson.length > 0){
                this.accountPerson.forEach(account => {
                    if(this.bankCard.accountName == account.custName){
                        params.custId = account.id;
                    }
                });
            }
            checkHasHistory(params).then(res=>{
                if (res.code == "0000"){
                    if(res.data){
                        //表示有历史银行卡鉴权记录, 弹框提示是否沿用历史记录
                        this.$Modal.confirm({
                            title: "检测到当前录入银行卡已签署委托扣款协议，是否确认使用该协议?",
                            okText: "确认",
                            cancelText: "继续发送短信",
                            onOk: () =>{
                                //确认使用历史鉴权信息, 保存银行卡签约信息
                                saveByHistory(params).then(res => {
                                    if(res.code == "0000"){
                                        //刷新页面银行卡数据
                                        this.showBankCardInfo(res.data);
                                    }else{
                                        this.$Message.error(res.msg);
                                    }
                                })
                            },
                            onCancel: ()=>{
                                //不沿用历史信息,继续发送短信
                                this.sendMessageRequest(params,indexNum);
                            },
                        });
                    }else{
                        //没有历史鉴权记录, 直接发送短信
                        this.sendMessageRequest(params,indexNum);
                    }
                }else{
                    this.$Message.error(res.msg);
                }
            });
        },
        //更新 银行卡数据
        updataBankCar(data){
            this.bankCardData.push(data);
        },
        sendMessageRequest(params,indexNum){
            console.log(this.bankCardData)
            sendMessage(params).then(res=> {
                if (res.code == "0000"){
                    this.$Message.success("验证码发送成功");
                    //防重复的数据
                    this.updataBankCar(res.data);
                    this.bankCardChannel[indexNum].isSendMessage=true;
                    this.bankCardChannel[indexNum].loading = true;
                    this.bankCardChannel[indexNum].Count = 60;
                    this.bankCardChannel[indexNum].id = res.data.id;
                    if (!this.bankCardChannel[indexNum].bankCardTimer) {
                        this.bankCardChannel[indexNum].bankCardTimer = setInterval(() => {
                            if (this.bankCardChannel[indexNum].Count > 0) {
                                this.bankCardChannel[indexNum].Count--;
                            } else {
                                this.bankCardChannel[indexNum].loading = false;
                                clearInterval(this.bankCardChannel[indexNum].bankCardTimer);
                                this.bankCardChannel[indexNum].bankCardTimer = null;
                            }
                            this.bankCardChannel= JSON.parse(JSON.stringify(this.bankCardChannel))
                        }, 1000)
                    }
                }else{
                    this.$Message.error(res.msg);
                }
            })
        },
        /** 银行卡验证绑定短信 */
        checkMessage(indexNum){
            let BankChannel=this.bankCardChannel[indexNum];
            let type= BankChannel.key
            if(!type){
                this.$Message.error("数据问题，请联系管理员");
                return;
            }
            if(!BankChannel.isSendMessage){
                this.$Message.error("请先发送验证码");
                return
            }
            if(!BankChannel.verCode || BankChannel.verCode.length != 6){
                this.$Message.error("请输入六位验证码");
                return
            }
            if(!this.checkBankCardData()){
                return;
            }
            let checkBankCard={};
            console.log(this.bankCardData,BankChannel)
            this.bankCardData.forEach(item=>{
                if(BankChannel.id == item.id){
                    checkBankCard=deepClone(item)
                }
            })
            checkBankCard.verCode=BankChannel.verCode;
            checkMessage(checkBankCard).then( res=>{
                if(res.code == "0000") {
                        this.$Message.success("绑定成功");
                        let bankCardData=[]
                        this.bankCardData.forEach(item=>{
                            if(item.id==res.data.id){
                                bankCardData.push(res.data)
                            }else{
                                bankCardData.push(item)
                            }
                        })
                        this.bankCardData=bankCardData;

                        this.bankCardChannel[indexNum].loading=false;
                        this.bankCardChannel[indexNum]._disabled=true;
                        this.bankCardChannel[indexNum].isSendCode=1;
                        this.isBankDisabled = true;
                        if(this.bankCardChannel[indexNum].bankCardTimer!=null){
                            clearInterval(this.bankCardChannel[indexNum].bankCardTimer);
                            this.bankCardChannel[indexNum].bankCardTimer = null;
                        }
                        this.bankCardChannel= JSON.parse(JSON.stringify(this.bankCardChannel))
                }else{
                    this.$Message.error("绑定失败");
                }
            })
        },
        /** 银行卡字段校验-(发送短信前验证字段非空) */
        checkBankCardData(){
            if (this.bankCard.authorizeWay == "" || this.bankCard.authorizeWay == null) {
                this.$Message.error("请填写签约类型信息");
                return false;
            }
            if (this.bankCard.accountNo == "" || this.bankCard.accountNo == null) {
                this.$Message.error("请填写银行卡号信息");
                return false;
            }
            if (this.bankCard.accountName == "" || this.bankCard.accountName == null) {
                this.$Message.error("请填写开户人信息");
                return false;
            }
            if (!this.bankCard.bankCode) {
                this.$Message.error("查询开户行信息失败!");
                return false;
            }
            if (this.bankCard.bankPhone == "" || this.bankCard.bankPhone == null) {
                this.$Message.error("请填写预留手机号码信息");
                return false;
            }
            return true;
        },
        cardAlreadySign(){
            return this.bankCardChannel && this.bankCardChannel[0].isSendCode==1;
        },
        infoBankCard(data){
            if(data.length>0){
                this.bankCard=deepClone(data[0]);
                if (this.bankCard.certType!=='00001') {
                    //非身份证不禁用
                    this.isCert=false;
                }
                if(this.bankCard.accountNo){
                    getSignBanknameBynum(this.bankCard.accountNo.replace(" ", ""),this.applyNo).then(res => {
                        if (res.code === '0000') {
                            //重新渲染数据
                            this.$set(this.bankCard, "bankBranch", res.data.bankTypeName);
                            this.$set(this.bankCard, "bankCode", res.data.bankTypeCode);
                            let channels=res.data.channels;
                            this.bankCardChannel=[]
                            let dicKey="card-sign-"+res.data.bankTypeCode;
                            this.getDicDataByKey(dicKey);
                            channels.forEach(item=>{
                            this.bankCardChannel.push({
                                    title:item.title,
                                    key:item.key,
                                    verCode:'',
                                    loading:false,
                                    _disabled:false,
                                    isSendMessage:false,
                                    isSendCode:0, //设计 0:默认 1:发送成功 2：发送失败
                                    Count:0,
                                    bankCardTimer:null
                                })
                            })
                            data.forEach(itemData=>{
                                    this.updataBankCar(itemData)
                                    if(itemData.verStatus == "alreadySign"){
                                        this.bankCardChannel.forEach(item=>{
                                            if(item.key ==itemData.verChannelBelong){
                                                item.loading=false;
                                                item._disabled=true;
                                                item.verCode=itemData.verCode;
                                                item.isSendCode=1;
                                                this.isBankDisabled = true;
                                                item.id=itemData.id
                                                if(item.bankCardTimer!=null){
                                                    clearInterval(this.bankCardChannel[indexNum].bankCardTimer);
                                                    this.bankCardChannel[indexNum].bankCardTimer = null;
                                                }else{
                                                    item.bankCardTimer = null;
                                                }
                                            }
                                        })
                                    }
                            })
                            this.bankCardChannel= JSON.parse(JSON.stringify(this.bankCardChannel))
                        }else {
                            this.$Message.error("银行卡信息有误，请重新输入");
                        }
                    });
                 }
            }
        },
        showBankCardInfo(data){
            if(data.length>0){
                this.bankCard=deepClone(data[0]);
                //this.currentCertNo=this.bankCard.certNo;
                data.forEach(itemData=>{
                    this.updataBankCar(itemData)
                    if(itemData.verStatus == "alreadySign"){
                        this.bankCardChannel.forEach(item=>{
                            if(item.key ==itemData.verChannelBelong){
                                item.loading=false;
                                item._disabled=true;
                                item.verCode=itemData.verCode;
                                item.isSendCode=1;
                                item.id=itemData.id
                                this.isBankDisabled = true;
                                if(item.bankCardTimer!=null){
                                    clearInterval(this.bankCardChannel[indexNum].bankCardTimer);
                                    this.bankCardChannel[indexNum].bankCardTimer = null;
                                }else{
                                    item.bankCardTimer = null;
                                }
                            }
                        })
                    }
               })
               this.bankCardChannel= JSON.parse(JSON.stringify(this.bankCardChannel))
            }
        },
        SelectContractInfo(){
                    this.paySplitFlag = this.paySplitFlag=="0"?"1":"0"
                    this.queryPayee();
                },
        selectAfterPayFlag(index){
            if(this.contractInfoForm[index].afterPayFlag==1){
                this.contractInfoForm[index].collectionAmount=0
            }
        },
        addRemark(){
            this.remarkShow=true;
        },
        editInvoice(){
            this.invoiceModel=true;
            this.$refs.invoiceDiscernFrom.getInvoice(this.certificateDiscern);
        },
        cancleInvoice(){
            this.invoiceModel=false;
            this.getInvoice();
        },
        saveInvoiceInfo(){
            this.$refs['invoiceDiscernFrom'].saveInvoice();
        },
        invoicediscernSaveSuccess(){
            this.invoiceModel=false;
            this.getInvoice();
        },
        getInvoice(){
            getInvoice(this.applyNo,'03').then(res => {
                if(res.code == '0000'&&res.data){
                    this.invoiceDiscern=res.data.carInvoice;
                    if(res.data.carInvoice){
                        //this.certificateDiscern.carVin=res.data.carInvoice.carVin;//车架号-wjy添加
                        //this.certificateDiscern.engineNo=res.data.carInvoice.engineNo;//发动机号-wjy添加
                        if(res.data.carInvoice.invoiceRate!==undefined){
                            res.data.carInvoice.invoiceRate=String(res.data.carInvoice.invoiceRate);
                        }
                        this.checkCarDifferPrice();
                        this.fpIsLaterSupply=this.invoiceDiscern.isLaterSupply!='1';//发票校验
                    }
                    if(res.data.invoiceVerifications){
                        this.invoiceCheckBody =[];
                        this.isInvoiceSameDiv = true;
                        this.isInvoiceSame = res.data.invoiceVerifications.checkIsSame;
                        this.invoiceCheckBody.push(res.data.invoiceVerifications);
                    }
                }
            })
        },
        checkInvoice(){
            let invoicePrice = parseInt(this.invoiceDiscern.invoiceAmt).toFixed(2);
            let salePrice = parseInt(this.formEntry.carDetails.salePrice).toFixed(2);
            let carType = this.formEntry.orderInfo.carType;
            //乘用车
            if (invoicePrice != "NaN" && salePrice != "NaN" && carType == "2") {
                //发票价低于销售价大于等于3000元且未针对此项已经特批
                if (salePrice - invoicePrice >= 3000) {
                    this.$Message.error("低开，不符合准入");
                }
            }
        },
        // 更新影像上传列表
        updateFileOperation() {
            let params = {
                applyNo: this.applyNo
            };
            // 查询影像件需要的数据
            getFilterParams(params).then(res => {
                if (res.code === '0000') {
                    this.uploadParam.busiNo = this.contractNo;
                    this.uploadParam.belongNo = this.contractNo;
                    this.uploadParam.busiData = res.data;
                    this.uploadParam.busiType = "loanApply";
                    this.isInt = true;
                    this.$refs.fileOperation.init();
                }
            });
        },
        // 个人转企业选项变化
        changePersonalToEnterprise(data) {
            this.changeToEnterprise = data.initValue !== data.actualValue;
            const { enterpriseName, socunicrtCode, custName } = data.info;
            this.$set(this.InvoiceApplication, "invoiceTitle", data.actualValue === "1" ? enterpriseName : custName);
            this.$set(this.InvoiceApplication, "taxpayerNum", data.actualValue === "1" ? socunicrtCode : "");
        },
        bankCardaccount(val){
            this.accountPerson.forEach(item=>{
                if(item.custName==val){
                    this.bankCard.certNo=item.certNo;
                }
            })
        },
        //起租通知
        noticeRent(){
            let opt ={
                 "applyNo": this.applyNo,
                 "contractNo": this.contractNo
                }
             checkHasLeaseNotice(opt).then(res => {
                if(res.code == '0000'){
                    this.isRegenerate= false
                   // 0：合同没有激活   1：false 的逻辑， 2是 true 的逻辑
                    if(res.data=='2'){
                        this.templateModel = true;
                        this.initGetComAttach("printLeaseNotice")
                    }else if(res.data=='1'){
                        this.leaseNoticePrint(opt)
                    }else{
                        this.$Message.error("合同当前未激活，请激活后再操作！");
                    }
                }
            });
        },
        leaseNoticePrint(opt){
            this.loading=true;
            leaseNoticePrint(opt).then(res => {
                this.loading=false;
                if (res.code === '0000') {
                    this.templateModel = true;
                    this.fileList = res.data;
                }
            }).catch(e=>{
                this.loading=false;
            })
        },
        editDepositConfig(){
            this.$refs['depositForm'].editDepositConfig();
        },
        getDicDataByKey(key) {
            let arr=[key];
            getByTypes(arr).then(res => {
                if (res.code === "0000") {
                    console.log("获取支持的证件下拉key=",key,res.data,res.data[key])
                    this.idTypeList=res.data[key]
                    this.bankCard.certType=this.getIdType();
                }
            });
        },
        idTypeChange(v) {
            console.log("修改下拉，当前值",v)
            let title="";
            this.idTypeList.forEach(ele=>{
                if (ele.value===v) {
                    title=ele.title;
                }
            })
            if ("居民身份证"===title) {
                this.isCert=true;
                this.bankCard.certNo=this.currentCertNo;
                console.log("触发身份证，重新赋值",this.bankCard.certNo)
            }else {
                this.isCert=false;
            }
        },
        getIdType() {
            let matched=false;
            let certVal;
            let title;
            this.idTypeList.forEach(ele=>{
                console.log("val=",ele.value,"title=",ele.title)
                if (ele.value===this.bankCard.certType) {
                    matched=true;
                    title=ele.title;
                    console.log("匹配成功",ele.value)
                }
                if (ele.title==="居民身份证") {
                    certVal=ele.value;
                    console.log("默认",certVal)
                }
            })
            if (matched) {
                certVal=this.bankCard.certType;
            }
            if(title==null||title==='居民身份证') {
                this.isCert=true;
                this.bankCard.certNo=this.currentCertNo;
                console.log("触发设置身份证",this.currentCertNo)
            }
            console.log("最终证件类型为",certVal)
            return certVal;
        },
    },
    filters:{
            //处理函数
            getRMB(val){
                if (typeof (val) === 'number') {
                    return (val).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
                }
            }
        },
}
</script>
