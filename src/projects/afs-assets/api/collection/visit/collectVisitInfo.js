import axios from '@/libs/request/axios'
// 获取催收组别
export function list(obj,path) {
  return axios.request({
    url: `/${path}/collectVisitInfo/list`,
    method: 'POST',
      data: obj
  })
}
//新增
export function addCollectVisitInfo(parms,path) {
    return axios.request({
        url: `/${path}/collectVisitInfo/addCollectVisitInfo`,
        method: 'post',
        data: parms
    });
}
//修改
export function modifyCollectVisitInfo(parms,path) {
    return axios.request({
        url: `/${path}/collectVisitInfo/modifyCollectVisitInfo`,
        method: 'post',
        data: parms
    });
}
//提交
export function submitCollectVisitInfo(parms,path) {
    return axios.request({
        url: `/${path}/collectVisitInfo/submitCollectVisitInfo`,
        method: 'post',
        data: parms
    });
}


export function getCollectVisitInfo(id,path) {
    return axios.request({
        url: `/${path}/collectVisitInfo/getCollectVisitInfo/${id}`,
        method: 'post',
    });
}

export const getCollectVisitworkList = (params) => {
    return axios.request({
        url: `/assets/collectVisitInfo/getCollectVisitworkList`,
        data: params,
        method: 'post'
    })
};

export const collectVisitworkButton = (params) => {
    return axios.request({
        url: `/assets/collectVisitInfo/collectVisitworkButton`,
        data: params,
        method: 'post'
    })
};


export function cancelFlowForCollectId(id,path) {
    return axios.request({
        url: `/${path}/collectVisitInfo/cancelFlow/${id}`,
        method: 'post',
    });
}
