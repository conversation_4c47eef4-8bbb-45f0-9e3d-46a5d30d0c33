<style lang="less" rel="stylesheet/less" scoped>
.flow-editor {
    display: inline-block;
    width: 100%;
    height: 100%;
    user-select: none;
    overflow: hidden;
}
.spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
</style>
<template>
    <div class="flow-editor" @click="handleEditorClick" v-if="G6!==undefined">
        <ToolBar :datas="templateStatus" :mode="initMode" @toolTrigger="handleToolTrigger" :process-id="processId"
                 :spin="spin"></ToolBar>
        <div style="height: 100%">
            <NodeItems v-if="initMode==='design'" :editor="editor"></NodeItems>
            <FlowCanvas :ele-id="elId"></FlowCanvas>
        </div>
        <ContextMenu :context-info="contextInfo" :parent-info="this"></ContextMenu>

        <Setting v-if="initMode==='design'" v-model="setting.open" :config="setting" :configData="currentData"
                 :editor="editor"
                 :request-methods="requestMethods"
                 :flow-config="flowConfig"
                 :taskNodeList="taskNodeList"
                 :templateStatus="templateStatus"
                 :process-info="processInfo"
                 :item="currentItem" :variables="processInfo.variables" @updateConfig="updateConfig"  @resetValue="resetValueHandle"></Setting>
        <Drawer :title="historyInfo.title" :on-close="clearHisInfo"  :closable="false" width="500" v-model="historyInfo.show">
            <Card >
                <p slot="title">
                    任务({{historyInfo.log.activityDescName}})信息
                </p>
                <Row>
                    <Col span="12">
                        任务创建时间: <span style="color: #2b85e4">{{historyInfo.log.startTime}}</span>
                    </Col>
                    <Col span="12">
                        任务领取时间: <span style="color: #2b85e4">{{historyInfo.log.takeTime}}</span>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        任务结束时间: <span style="color: #2b85e4">{{historyInfo.log.endTime}}</span>
                    </Col>
                    <Col span="12">
                        任务持续时间: <span style="color: #2b85e4">{{historyInfo.log.duration}}ms</span>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                        任务状态: <span style="color: #2b85e4">{{historyInfo.log.statusName}}</span>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                        任务所有者: <span style="color: #2b85e4">{{historyInfo.log.ownerName}}</span>
                    </Col>
                </Row>
                <Row>
                    <Col span="12">
                        提交用户ID: <span style="color: #2b85e4">{{historyInfo.log.executorId}}</span>
                    </Col>
                    <Col span="12">
                        提交用户名称: <span style="color: #2b85e4">{{historyInfo.log.ownerName}}</span>
                    </Col>
                </Row>
            </Card>
            <Card >
                <p slot="title">
                    变量信息
                </p>
                <Row v-for="item in historyVariables">
                    <Col span="8">
                        变量名: <span style="color: #2b85e4">{{item.bizName}}</span>
                    </Col>
                    <Col span="8">
                        表达式: <span style="color: #2b85e4">{{item.name}}</span>
                    </Col>
                    <Col span="8">
                        值: <span style="color: #2b85e4">{{item.value}}</span>
                    </Col>
                </Row>
            </Card>
        </Drawer>
    </div>
</template>

<script>
import '../../assets/styles/main.less'
import ToolBar from './containers/ToolBar'
import FlowCanvas from './containers/FlowCanvas'
import NodeItems from './containers/NodeItems'
import ContextMenu from './containers/ContextMenu'
import Setting from './components/setting/SettingIndex'
import screenfull from 'screenfull'
import Mousetrap from 'mousetrap'
import config from './config/index'
import JpdlLUtil from '../../global/utils/flowNodes'
import edge from "../../global/g6/config/edge";
import utils from "../../global/g6/utils";
export default {
    name: 'FlowDesigner',
    props: {
        packageId: {
            type: String,
            required: true
        },
        templateId: {
            type: String,
            required: true
        },
        templateVersion: {
            type: String,
            required: true
        },
        processId: {
            type: String,
            required: false
        },
        initMode: {
            type: String,
            required: true
        },
        requestMethods:{
            type:Object,
            default:()=>{
                return {}
            }
        },
        flowConfig:{
            type:Object,
            default:()=>{
                return {
                    eventAdapterType:'3',
                    allowedEvtAdapterTypes:['1','2','3']
                }
            }
        }
    },
    components: {
        ToolBar,
        FlowCanvas,
        NodeItems,
        ContextMenu,
        Setting
    },
    data() {
        return {
            elId:'',
            editorInfo: {},
            width: 1024,
            height: 768,
            spin: false,
            // mode:'design',
            contextInfo: {
                contextShow: false,
                data: null
            },
            editor: {},
            isFullScreen: false,
            clipboard: {
                data: null,
                count: 0
            },
            edgeHis: [],
            setting: {
                title: '',
                open: false,
                type: ''
            },
            processInfo: {
                base: {},
                variables: [],
                events: [],
                userexpansions: [{}, {}, {}, {}, {}, {}, {}, {}]
            },
            templateStatus: {},
            nodes: [],
            edges: [],
            currentItem: {},
            currentData: {},
            currentItems: [],
            log: {
                current: null,
                list: []
            },
            hisRunStatus: {
                status: 'run',
                index: 0,
                running:false
            },
            historyInfo:{
                show:false,
                title:'',
                log: {}
            },
            historyVariables:[],
            G6:undefined
        }
    },
    computed: {
        taskNodeList(){
            let taskNodes = [];
            if(this.editor.getNodes) {
                this.editor.getNodes().forEach(item => {
                    let model = item.getModel();
                    if(model.configKey==="artific"){
                        taskNodes.push({value:model.id,label:model.label});
                    }
                });
            }
            return taskNodes
        }
    },
    methods: {
        init() {
            let _t = this
            let el = _t.$el
            if (_t.initMode === 'history') {
                el.querySelector('#canvas-box-'+this.elId).style = 'width:100%'
            }
            // 画板
            let flowCanvas = el.querySelector('#flow-canvas-'+this.elId)
            _t.width = flowCanvas.clientWidth ;
            _t.height = flowCanvas.clientHeight;
            const grid = new this.G6.Grid()
            // 生成编辑器实例
            _t.editor = new this.G6.Graph({
                plugins: [grid],
                container: flowCanvas,
                width: _t.width,
                height: _t.height,
                fitView: false,
                // 模式
                modes: {
                    design: [
                        'drag-canvas',
                        'click-select',
                        'jpdl-design'
                    ],
                    contextMenu: [],
                    // 只读，
                    history: ['drag-canvas','jpdl-history-view']
                },
            })
            _t.editor.jpdl = {
                ...config.style,
                vue: _t
            }
            // 挂载G6配置
            _t.editor.$C = this.G6.$C
            // 设置模式
            _t.doSetMode(_t.initMode)
            // 绑定事件
            _t.editor.on('canvas:mousedown', _t._canvasMousedown)
            // 绑定事件
            _t.editor.on('canvas:mouseup', _t._canvasMouseup)
            _t.editor.on('node:mousedown', _t._nodeMousedown)
            _t.editor.on('node:mouseover', _t._nodeHover)
            _t.editor.on('node:mouseout', _t._nodeOut)
            _t.editor.on('node:dblclick', _t.prepareSettingData)
            _t.editor.on('edge:mousedown', _t._edgeMousedown)
            _t.editor.on('edge:dblclick', _t.openEdgeSetting)
            _t.editor.on('editor:getItem', function (data) {
                _t.setItem(data)
            })
            _t.editor.on('editor:setItem', function (data) {
                data.forEach((item, index) => {
                    let node = _t.editor.findById(item.id)
                    if (!index) {
                        // 更新第一个节点
                        _t.editor.updateItem(node, item.model)
                    } else {
                        // FIXME 更新同组节点，只更新样式部分
                        _t.editor.updateItem(node, {
                            style: data[0].model.style
                        })
                    }
                })
                _t.editor.paint()
            })
            _t.editor.on('editor:contextmenu', function (data) {
                _t.doSetMode('contextMenu');
                _t.contextInfo.contextShow = true;
                _t.contextInfo.data = data;
            })
            _t.editor.on('editor:record', function (from) {
                // console.log('editor:record from', from)
                // 更新操作日志
                _t.doLog({
                    action: 'record',
                    data: {
                        time: new Date(),
                        content: _t.editor.save()
                    }
                });
            })
            _t.editor.on('view:showHistory', function (item) {
               console.log(item)
                _t.historyInfo.title = item.getModel().label;
               _t.historyInfo.log = item.getModel().historyInfo;
               _t.historyInfo.show = true;
            })
            // 绑定热键
            _t.bindShortcuts()
            // 绑定unload
            _t.bindUnload()
            // 更新编辑器实例
            _t.spin = true;
            this.requestMethods.templateDetail( _t.packageId,_t.templateId,_t.templateVersion)
                .then(response=>{
                    if (response.code && response.code === '0000') {
                        JpdlLUtil.jpdlNodeToG6Nodes(response.data, _t.nodes, _t.edges, _t.processInfo);
                        _t.templateStatus = response.data.templeteBeanOut;
                        _t.handle = response.data.templeteBeanOut.handle;
                        if(_t.processInfo.base.packageCheckMd5===undefined||_t.processInfo.base.packageCheckMd5===''){
                            _t.processInfo.base.packageCheckMd5 = response.data.templeteBeanOut.packageCheckMd5;
                        }
                        console.log({nodes:_t.nodes,edges:_t.edges,groups:[]})
                        _t.editor.data({nodes: _t.nodes, edges: _t.edges, groups: []})
                        console.log(_t.nodes);
                        console.log(_t)
                        // 渲染
                        _t.editor.render()
                        _t.updateCanvas(_t.processInfo.maxX - _t.processInfo.minX, _t.processInfo.maxY - _t.processInfo.minY)
                        _t.spin = false;
                        _t.editor.emit('editor:record', '----init jpdl---')
                        _t.editor.getEdges().forEach(edge => {
                            utils.edge.updateEdgeAnchor(_t.editor, edge)
                        })
                        if (_t.initMode === 'history') {
                            _t.hideAncharos();
                            _t.loadProcessData();
                        }
                    }
                }).catch(e=>{
                    console.error(e)
                });
        },
        _canvasMousedown() {
            let _t = this
            // console.log('_canvasMousedown ')
            if (_t.initMode === 'history') return;
            _t.doClearAllStates()
            // 更新currentItem
            _t.setItem([])
        },
        _canvasMouseup() {
            let _t = this
            // console.log('_canvasMouseup')
            if (_t.initMode === 'history') return;
            if (_t.editor.getCurrentMode() != _t.initMode) {
                _t.editor.setMode(_t.initMode)
            }
        },
        _nodeMousedown(event) {
            let _t = this
            if (_t.initMode === 'history') return;
            _t.doClearAllStates()
            _t.editor.setItemState(event.item, 'active', true)
        },
        _nodeHover(event) {
            let _t = this
            if (_t.initMode === 'history') return;
            if (!event.item.hasState('active')) {
                _t.editor.setItemState(event.item, 'hover', true)
            }
        },
        _nodeOut(event) {
            let _t = this
            if (_t.initMode === 'history') return;
            _t.editor.setItemState(event.item, 'hover', false)
        },
        _edgeMousedown(event) {
            let _t = this
            if (_t.initMode === 'history') return;
            _t.doClearAllStates()
            if (event.item && !event.item.destroyed) {
                _t.editor.setItemState(
                    event.item,
                    'active',
                    !event.item.hasState('active')
                )
            }
        },
        // 清除所有状态
        doClearAllStates() {
            let _t = this
            if (!_t.editor) {
                return
            }
            // 批量操作时关闭自动重绘，以提升性能
            _t.editor.setAutoPaint(false)
            _t.editor.getNodes().forEach(function (node) {
                _t.editor.clearItemStates(node, ['active', 'hover', 'selected'])
            })
            _t.editor.getEdges().forEach(function (edge) {
                _t.editor.clearItemStates(edge, ['active', 'hover', 'selected'])
            })
            _t.editor.paint()
            _t.editor.setAutoPaint(true)
        },
        doSetMode(name) {
            let _t = this
            _t.mode = name
            _t.editor.setMode(name)
        },
        handleToolTrigger(info) {
            let _t = this
            // 是否记录日志标识
            let isRecord = false
            switch (info.name) {
                case 'undo':
                case 'redo':
                case 'clearLog':
                    _t.doLog({
                        action: info.name
                    });
                    if (['undo', 'redo'].includes(info.name)) {
                        _t.$nextTick(function () {
                            if (_t.log.list.length) {
                                if (_t.log.current !== null) {
                                    let data = _t.log.list[_t.log.current]
                                    // 渲染
                                    _t.editor.read(data.content)
                                    _t.editor.paint()
                                    _t.editor.zoomTo(1);
                                } else {
                                    // 清除
                                    _t.editor.clear()
                                    _t.editor.paint()
                                    _t.editor.zoomTo(1);
                                }
                            } else {
                                _t.editor.zoomTo(1);
                            }
                        })
                        // 更新currentItem
                        _t.setItem([])
                    }
                    break
                case 'copy':
                    (() => {
                        let data = _t.currentItems
                            ? _t.currentItems.filter(
                                (item) => item.type === 'node'
                            )
                            : []
                        _t.clipboard = {
                            data,
                            count: 0
                        }
                    })()
                    break
                case 'setting':
                    this.currentItem = {};
                    this.currentData = this.processInfo;
                    this.openSetting('global', '全局设置');
                    break;
                case 'paste':
                    (() => {
                        let data = _t.clipboard.data
                        _t.clipboard.count++
                        if (data.length) {
                            data.forEach((item, index) => {
                                let model = item.model
                                // 计算坐标，添加一定偏移量，防止重叠
                                let x = model.x + 10 * _t.clipboard.count
                                let y = model.y + 10 * _t.clipboard.count
                                // 如果通过右键菜单触发的，则获取触发菜单时的canvas坐标
                                if (
                                    info &&
                                    info.context === 'ContextMenu' &&
                                    info.data
                                ) {
                                    if (info.data.hasOwnProperty('canvasX')) {
                                        x =
                                            model.x +
                                            info.data.canvasX -
                                            data[0].model.x
                                    }
                                    if (info.data.hasOwnProperty('canvasY')) {
                                        y =
                                            model.y +
                                            info.data.canvasY -
                                            data[0].model.y
                                    }
                                }
                                let cdata = {};
                                JpdlLUtil.clone(model.data, cdata);
                                let id = cdata.properties.base.id.split('_')[0] + '_' + JpdlLUtil.randomStr();
                                cdata.properties.base.id = id;
                                let node = {
                                    ...model,
                                    id: id,
                                    groupId: '',
                                    x,
                                    y
                                }
                                node.data = cdata;
                                _t.editor.addItem('node', node)
                            })
                            _t.editor.emit('editor:record', 'paste')
                        }
                    })()
                    break
                case 'delete':
                    // 删除逻辑
                    let deleteNodeIds='|||';
                    let deleteObjs=[];
                    _t.editor.getNodes().forEach((node) => {
                        if (node.hasState('active')) {
                            isRecord = true
                            deleteNodeIds+=(node.getID()+'|||');
                            deleteObjs.push(node)
                        }
                    })
                    deleteObjs.forEach(node=>{
                        _t.editor.removeItem(node)
                    })
                    deleteObjs=[];
                    let deleteEdgeStartId;
                    let edges = [];
                    _t.editor.getEdges().forEach((edge) => {
                        if (edge.hasState('active')) {
                            isRecord = true
                            deleteEdgeStartId = edge.getSource().getID();
                            deleteObjs.push(edge);
                        }
                        if(deleteNodeIds!=='|||'&&(deleteNodeIds.indexOf(edge.getSource().getID())>0||deleteNodeIds.indexOf(edge.getTarget().getID())>0)){
                            deleteObjs.push(edge);
                        }
                    })
                    deleteObjs.forEach(edge=>{
                        _t.editor.removeItem(edge)
                    });
                    deleteObjs=[];
                    if (deleteEdgeStartId) {
                        _t.editor.getEdges().forEach((edge) => {
                            let model = edge.getModel();
                            if (edge.getSource().getID() === deleteEdgeStartId) {
                                edges.push(edge);
                            }
                        })
                        if (edges.length === 1) {
                            let data = edges[0].getModel().data;
                            data.content = '';
                            _t.editor.updateItem(edges[0], {
                                label: '',
                                data: data
                            })
                        }
                    }
                    if (isRecord) {
                        _t.editor.emit('editor:record', 'delete')
                    }
                    // 更新currentItem
                    _t.setItem([])
                    break
                case 'resetToLine':
                    console.log('resetToLine')
                    let tempEdge;
                    _t.editor.getEdges().forEach((edge) => {
                        if (edge.hasState('active')) {
                            isRecord = true
                            tempEdge = edge;
                        }
                    })
                    if (isRecord) {
                        _t.editor.emit('editor:record', 'resetToLine')
                    }
                    tempEdge._cfg.model.data.pointInfos.splice(0, tempEdge._cfg.model.data.pointInfos.length - 2)
                    _t.editor.updateItem(tempEdge, {})
                    utils.edge.updateEdgeAnchor(_t.editor, tempEdge)
                    _t.editor.clearItemStates(tempEdge, ['active', 'hover', 'selected'])
                    _t.setItem([])
                    break
                case 'clear':
                    _t.$Modal.confirm({
                        title: '确认清空画布',
                        // 确认清空画布？
                        content: '确认清空画布',
                        onOk: function () {
                            _t.doLog({
                                action: 'clear'
                            });
                            _t.editor.clear()
                            _t.editor.paint()
                        }
                    })
                    // 更新currentItem
                    _t.setItem([])
                    break
                case 'fullscreen':
                    if (screenfull.enabled) {
                        screenfull.toggle()
                    }
                    break
                case 'export':
                    let fileName = _t.templateStatus.name + '_' + _t.templateStatus.id + '_v' + _t.templateStatus.version
                    if (_t.initMode === 'history') {
                        fileName += '_' + _t.processId
                    }
                    _t.editor.getNodes().forEach(node => {
                        if ((node.x - 60) < _t.processInfo.minX) {
                            _t.processInfo.minX = (node.x - 60);
                        }
                        if ((node.x + 60) >= _t.processInfo.maxX) {
                            _t.processInfo.maxX = (node.x + 60);
                        }
                        if ((node.y - 60) < _t.processInfo.minY) {
                            _t.processInfo.minY = (node.y - 60);
                        }
                        if ((node.y + 60) >= _t.processInfo.maxY) {
                            _t.processInfo.maxY = (node.y + 60);
                        }
                    })

                    _t.editor.getEdges().forEach(edge => {
                        edge.getModel().data.pointInfos.forEach(node => {
                            if ((node.x - 60) < _t.processInfo.minX) {
                                _t.processInfo.minX = (node.x - 60);
                            }
                            if ((node.x + 60) >= _t.processInfo.maxX) {
                                _t.processInfo.maxX = (node.x + 60);
                            }
                            if ((node.y - 60) < _t.processInfo.minY) {
                                _t.processInfo.minY = (node.y - 60);
                            }
                            if ((node.y + 60) >= _t.processInfo.maxY) {
                                _t.processInfo.maxY = (node.y + 60);
                            }
                        })
                    })
                    _t.updateCanvas(_t.processInfo.maxX - _t.processInfo.minX, _t.processInfo.maxY - _t.processInfo.minY)
                    _t.editor.downloadImage(fileName)
                    break
                case 'check':
                    _t.checkAll();
                    break;
                case 'save':
                    _t.saveTemplate();
                    break;
                case 'deploy':
                    _t.deployTemplate();
                    break;
                case 'undeploy':
                    _t.unDeployTemplate();
                    break;
                case 'play':
                    _t.play();
                    break;
                case 'stop':
                    _t.stop();
                    break;
                case 'pause':
                    _t.pause();
                    break;
            }
            if (isRecord) {
                // 记录操作日志
                _t.editor.emit('editor:record', 'handleToolTrigger')
            }
            if(_t.initMode!=='history'){
                _t.doSetMode('design');
            }
        },
        bindShortcuts() {
            let _t = this
            config.tools.toolList.forEach((item) => {
                if (item.enable && item.shortcuts) {
                    Mousetrap.bind(item.shortcuts, function (e) {
                        if (e.preventDefault) {
                            e.preventDefault()
                        } else {
                            // internet explorer
                            e.returnValue = false
                        }
                        _t.handleToolTrigger({
                            name: item.name,
                            data: {}
                        })
                        return false
                    })
                }
            })
            // 绑定按键事件
            document.addEventListener('keyup', function () {
                _t.doSetMode(_t.initMode)
                _t.contextInfo.contextShow = false;
            })
        },
        bindUnload() {
            window.onbeforeunload = function (event) {
                event.returnValue = false
                return false
            }
        },
        handleEditorClick() {
            let _t = this
            _t.contextInfo.contextShow = false;
        },
        prepareSettingData(evt) {
            this.currentItem = evt.item;
            this.currentData = evt.item._cfg.model.data.properties;
            this.openSetting(evt.item._cfg.model.configKey, evt.item._cfg.model.data.properties.base.name + '配置');
        },
        openSetting(type, title) {
            this.setting.open = true;
            this.setting.type = type;
            this.setting.title = title;
        },
        openEdgeSetting(evt) {
            let edges = this.editor.getEdges()
            if (!edges || !edges.length) {
                return
            }
            if(!evt.item.getSource()){
                return;
            }
            if(evt.item.getSource().getModel().configKey==='fork'){
                return;
            }
            let startId = evt.item.getSource().getID();
            let count = 0;
            edges.forEach(edge => {
                if (startId === edge.getSource().getID()) {
                    count++;
                }
            })
            console.log("edge db click",evt.item);
            let _t = this
            if (evt.item && !evt.item.destroyed) {
                _t.editor.setItemState(
                    evt.item,
                    'active',
                    false
                )
            }
            this.currentItem = evt.item;
            this.currentData = evt.item._cfg.model.data;
            if (count < 2 || evt.item.getTarget().getModel().configKey==='join') {
                this.openSetting('edgeOnlyParam', '路由回调参数配置');
            }else {
                this.openSetting('edge', '路由条件配置');
            }
        },
        checkAll(callback) {
            let _t = this;
            let _result = true;
            let nodes = this.editor.getNodes();
            let edges = this.editor.getEdges();
            try {
                nodes.forEach(item => {
                    let model = item.getModel();
                    //节点边检测
                    let nodeId = model.id;
                    let inCount = 0;
                    let outCount = 0;
                    console.log(model)
                    edges.forEach(edge => {
                        if (nodeId === edge.getSource().getID() || nodeId === edge.getTarget().getID()) {
                            count++;
                        }
                    })
                    edges.forEach(edge => {
                        if (nodeId === edge.getSource().getID() ) {
                            outCount++;
                        }
                        if (nodeId === edge.getTarget().getID()) {
                            inCount++;
                        }
                    })
                    if (inCount === 0 && model.configKey!=='start') {
                        _result = false;
                        if (callback) {
                            callback(false, model.label + '没有路由连接线');
                            throw new Error("EndIterative");
                        } else {
                            _t.$Message.error(model.label + '没有路由连接线');
                            throw new Error("EndIterative");
                        }
                    }
                    if (outCount === 0 && model.configKey!=='end') {
                        _result = false;
                        if (callback) {
                            callback(false, model.label + '没有路由连接线');
                            throw new Error("EndIterative");
                        } else {
                            _t.$Message.error(model.label + '没有路由连接线');
                            throw new Error("EndIterative");
                        }
                    }
                });
            } catch (e) {
                if (e.message !== "EndIterative") {
                    if (callback) {
                        callback(false, e.message);
                    } else {
                        throw e;
                    }
                }
            }
            try {
                edges.forEach(edge => {
                    let edgeModel = edge.getModel()
                    let count = 0;
                    let startId = edge.getSource().getID();
                    edges.forEach(edgeTmp => {
                        if (startId === edgeTmp.getSource().getID()) {
                            count++;
                        }
                    })
                    if (count > 1 && (!edgeModel.data.content || edgeModel.data.content === '') && startId.indexOf('Fork') < 0) {
                        _result = false;
                        // console.log(edgeModel)
                        // console.log(nodes)
                        let message = '';
                        for (let index = 0; index < nodes.length; index++) {
                            if (nodes[index]._cfg.id === edgeModel.data.from) {
                                message += '节点 ['
                                message += nodes[index]._cfg.model.label;
                                message += '] 存在没有条件的路由边'
                                break;
                            }
                        }
                        if (message === '') {
                            message += '存在没有条件的路由边';
                        }
                        if (callback) {
                            callback(false, message);
                            throw new Error("EndIterative");
                        } else {
                            _t.$Message.error(message);
                            throw new Error("EndIterative");
                        }
                    }
                })
            } catch (e) {
                if (e.message !== "EndIterative") {
                    if (callback) {
                        callback(false, e.message);
                    } else {
                        throw e;
                    }
                }
            }

            if (_result) {
                let self = this;
                let process = JpdlLUtil.g6NodesToJpdlNode(self.editor.getNodes(), self.editor.getEdges(), self.processInfo)
                _t.spin = true;
                this.requestMethods.checkDesignTemplate(process).then(response=>{
                    _t.spin = false;
                    if (response.code && response.code === '0000') {
                        if (response.data === 'true') {
                            if (callback) {
                                callback(true, 'success');
                            } else {
                                _t.$Message.success('校验成功');
                            }
                        } else {
                            if (callback) {
                                callback(false, response.data);
                            } else {
                                _t.$Message.success(response.data);
                            }
                        }
                    } else {
                        this.$Message.error(response.msg);
                    }
                }).catch(e=>{
                    _t.spin = false;
                })
            }
        },
        updateCanvas(width, height) {
            if (width < this.width) {
                width = this.width;
            }
            if (height < this.height) {
                height = this.height;
            }
            this.editor.changeSize(width, height);
        },
        saveTemplate() {
            let self = this;
            let process = JpdlLUtil.g6NodesToJpdlNode(self.editor.getNodes(), self.editor.getEdges(), self.processInfo)
            self.spin = true;
            this.requestMethods.saveDesignTemplate(process,self.processInfo.base.handle,false).then(res=>{
                self.spin = false;
                let response = res;
                if (response.code && response.code === '0000') {
                    self.processInfo.base.version = response.data.version;
                    self.processInfo.base.handle = response.data.handle;
                    self.templateStatus.isDeployed = response.data.isDeployed;
                    self.templateStatus.version = response.data.version;
                    self.$emit('flowConfigChange',{templateId:response.data.handle,templateVersion:response.data.version})
                    if (self.templateStatus.isDeployed === '1') {
                        self.templateStatus.isDeployedName = '已部署';
                    } else {
                        self.templateStatus.isDeployedName = '未部署';
                    }
                    self.$Message.success('保存成功');
                } else {
                    self.$Message.error(response.msg);
                }
            }).catch(()=>{
                self.spin = false;
            })
        },
        deployTemplate() {
            let self = this;
            if (self.templateStatus.isDeployed === '1') {
                self.$Message.error('模板已部署，请修改保存后再重新部署');
                return;
            }
            let process = JpdlLUtil.g6NodesToJpdlNode(self.editor.getNodes(), self.editor.getEdges(), self.processInfo)
            self.spin = true;
            this.checkAll((result, message) => {
                if (result) {
                    self.spin = true;
                    this.requestMethods.doDeployTemplate(process,self.processInfo.base.handle).then(response=>{
                        self.spin = false;
                        if (response.code && response.code === '0000') {
                            self.processInfo.base.version = response.data.version;
                            self.processInfo.base.handle = response.data.handle;
                            self.processInfo.base.deploydate = response.data.deployedTime;
                            self.templateStatus.isDeployed = response.data.isDeployed;
                            self.templateStatus.isDeployedName = '已部署';
                            self.templateStatus.version = response.data.version;
                            self.$emit('flowConfigChange',{templateId:response.data.handle,templateVersion:response.data.version})
                            self.$Message.success('部署成功');
                        } else {
                            self.$Message.error(response.message);
                        }
                    }).catch(e=>{
                        self.spin = false;
                    })
                } else {
                    self.spin = false;
                    self.$Message.error('不能部署 [' + message + ']');
                }
            });
        }, unDeployTemplate() {
            let self = this;
            self.spin = true;
            this.requestMethods
                .doUnDeployTemplate(self.processInfo.base.packageid,self.processInfo.base.handle)
                .then(response=>{
                    if (response.code && response.code === '0000') {
                        self.$Message.success('反部署成功');
                        self.templateStatus.isDeployed = '2'
                        self.templateStatus.isDeployedName = '未部署';
                    } else {
                        self.$Message.error(response.message);
                    }
                    self.spin = false;
                }).catch(()=>{
                    self.spin = false;
                })
        },
        hideAncharos() {
            let self = this;
            self.editor.getNodes().forEach(node => {
                self.editor.setItemState(node, 'hideAnvhor', true);
            })
        },
        play() {
            let self = this;
            if(this.hisRunStatus.status==='play'||this.hisRunStatus.running){
                return
            }
            if(this.hisRunStatus.status !== 'pause') {
                self.edgeHis.forEach(edge => {
                    edge.clearStates('history')
                    let startId = edge.getModel().source;
                    self.editor.setItemState(self.editor.findById(startId), 'history', false);
                });
            }
            this.hisRunStatus.status = 'play'
            self.editor.setItemState(self.edgeHis[self.hisRunStatus.index], 'runActiveHis', {
                hisRunStatus: self.hisRunStatus,
                editor: self.editor,
                edges: self.edgeHis,
                self: self
            });
        },
        pause() {
            this.hisRunStatus.status = 'pause'
        },
        stop() {
            let self = this;
            self.editor.paint()
            self.hisRunStatus.status = 'stop'
            self.edgeHis.forEach(edge => {
                edge.clearStates('history')
                let startId = edge.getModel().source;
                self.editor.setItemState(self.editor.findById(startId), 'history', false);
            });
            self.hisRunStatus.index = 0;
        },
        doLog(data) {
            let self = this;
            if (!data.hasOwnProperty('action') || !data.action) {
                return
            }
            switch (data.action) {
                // 记录
                case 'record':
                    self.log.list.push(JSON.parse(JSON.stringify(data.data)));
                    self.log.current = self.log.list.length - 1
                    break
                // 撤销
                case 'undo':
                    self.log.current =
                        (self.log.current - 1 < 0) ? 0 : (self.log.current - 1)
                    break
                // 重做
                case 'redo':
                    self.log.current =
                        (self.log.current + 1) > (self.log.list.length - 1)
                            ? (self.log.list.length - 1)
                            : (self.log.current + 1)
                    break
                // 清空
                case 'clearLog':
                    self.log.list = [];
                    self.log.current = 0;
                    break
            }
        }
        ,
        loadProcessData() {
            let self = this;
            this.requestMethods.processHistoryTrack(self.processId).then(response=>{
                    if (response.code && response.code === '0000') {
                        let activesIds = [];
                        if (response.data.activityNodes) {
                            response.data.activityNodes.forEach(node => {
                                activesIds.push(node.nodeId)
                                self.editor.setItemState(self.editor.findById(node.nodeId), 'history_avtive', true);
                            });
                        }
                        if(response.data.variables){
                            self.historyVariables.push(...response.data.variables);
                        }
                        if (response.data.history) {
                            let hisArray = [];
                            Object.keys(response.data.history).forEach(key => {
                                response.data.history[key].forEach(node => {
                                    node.startId = key;
                                    hisArray.push(node);
                                })
                            });
                            // console.log(hisArray);
                            hisArray = hisArray.sort((val1, val2) => {
                                for (let i = 0; i < (val1.activityDbid).length; i++) {
                                    if (val1.activityDbid.charCodeAt(i) == val2.activityDbid.charCodeAt(i))
                                        continue;
                                    return val1.activityDbid.charCodeAt(i) - val2.activityDbid.charCodeAt(i);
                                }
                            });
                            for (let index = 0; index < hisArray.length - 1; index++) {
                                let start = hisArray[index].startId;
                                let end = hisArray[index + 1].startId;
                                self.editor.getEdges().forEach(edge => {
                                    let model = edge.getModel();
                                    let estart = model.source;
                                    let eend = model.target;
                                    if (start === estart && end === eend) {
                                        self.edgeHis.push(edge);
                                    }
                                })
                                //处理历史信息
                                let node = self.editor.findById(hisArray[index].startId)
                                if(node){
                                    node.getModel().historyInfo = hisArray[index];
                                }
                                if(index===(hisArray.length-2)){
                                    let lastNode = self.editor.findById(hisArray[index+1].startId)
                                    if(lastNode){
                                        lastNode.getModel().historyInfo = hisArray[index+1];
                                    }
                                }
                            }
                        }
                    } else {
                        self.$Message.error('流程轨迹加载失败');
                    }
                }).catch(e=>{
                    console.error(e)
                });
        }
        , setItem(data) {
            this.editor.emit('editor:setItem', data)
            this.currentItems = data;
        },
        resetValueHandle(type, item, value) {
            console.log(type, 'reset type')
            console.log(item, 'reset item')
            console.log(value, 'reset value')
            if ('global' === type) {
                this.processInfo = value;
            } else if ('edge' === type) {
                item._cfg.model.data = value
            } else if ('edgeOnlyParam' === type) {
                item._cfg.model.data = value
            } else {
                item._cfg.model.data.properties = value
            }
        },
        clearHisInfo(){
            this.historyInfo.log= {};
            this.historyInfo.title='';
        },
        updateConfig(modifyItemInfo){
            let self = this;
            console.log("更新已部署流程 keyInfo====>",modifyItemInfo)
            let process = JpdlLUtil.g6NodesToJpdlNode(self.editor.getNodes(), self.editor.getEdges(), self.processInfo)
            self.spin = true;
            this.checkAll((result, message) => {
                if (result) {
                    self.spin = true;
                    let data = {
                        template:{process:process,handle:self.processInfo.base.handle},
                        modifyItemInfo:modifyItemInfo
                    }
                    this.requestMethods.updateDeployTemplateConfig(data).then(response=>{
                        self.spin = false;
                        if (response.code && response.code === '0000') {
                            self.$Message.success('更新成功');
                        } else {
                            self.$Message.error(response.message);
                        }
                    }).catch(e=>{
                        self.spin = false;
                    })
                } else {
                    self.spin = false;
                    self.$Message.error('不能更新 [' + message + ']');
                }
            });
        }
    },
    created() {
        let _t = this
        const loader = require.ensure([], function() { // 处理事件逻辑之前会先引入模块
            const temp =  require('@/components/flow-designer/global/g6/index')
            _t.G6 = temp.default
        },'ant-g6')
        loader.then(()=>{
            _t.mode = this.initMode;
            _t.elId=Date.now()+'';
            _t.$nextTick(_t.init)
        })
    }
}
</script>
