<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="80" label="节点id:">
                            <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="节点名称:">
                            <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <div class="properties_contain">
                            <span class="title">
                                延迟时间
                            </span>
                            <FormItem :label-width="100" label="按工作日历:">
                                <i-switch v-model="datas.base.delayrefcalendar" :falseValue="'0'" :trueValue="'1'"
                                          size="large">
                                    <span slot="open">开</span>
                                    <span slot="close">关</span>
                                </i-switch>
                            </FormItem>
                            <FormItem :label-width="100" label="延迟时间:">
                                <InputNumber v-model="datas.base.delayday" :min="0" :step="1" placeholder=""
                                             style="width: 50px"/>
                                天
                                <InputNumber v-model="datas.base.delayhour" :max="23" :min="0" :step="1" placeholder=""
                                             style="width: 50px"/>
                                小时
                                <InputNumber v-model="datas.base.delayminute" :max="59" :min="0" :step="1"
                                             placeholder="" style="width: 50px"/>
                                分钟
                                <InputNumber v-model="datas.base.delaysecond" :max="59" :min="0" :step="1"
                                             placeholder="" style="width: 50px"/>
                                秒
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                循环设置（在规定的循环条件内重复执行逻辑）
                            </span>

                            <FormItem :label-width="100" label="循环类型:">
                                <Select v-model="datas.base.cycletype" style="width:150px" @on-change="setStatus">
                                    <Option :value="'1'">无循环</Option>
                                    <Option :value="'2'">有次数限制循环</Option>
                                    <Option :value="'3'">有时间限制的循环</Option>
                                </Select>
                                <span v-if="datas.base.cycletype==2">
                                    &nbsp;&nbsp;&nbsp;&nbsp;循环次数&nbsp;&nbsp;
                                    <InputNumber v-model="datas.base.cyclecount" :min="0" :step="1" placeholder=""
                                                 style="width: 50px"/>
                                 </span>
                            </FormItem>
                            <br>
                            <div v-if="datas.base.cycletype!=1">
                                <FormItem :label-width="100" label="按工作日历:">
                                    <i-switch v-model="datas.base.intervalrefcalendar" :falseValue="'0'" :trueValue="'1'"
                                              size="large">
                                        <span slot="open">开</span>
                                        <span slot="close">关</span>
                                    </i-switch>
                                </FormItem>
                                <FormItem :label-width="100" label="间隔时间:">
                                    <InputNumber v-model="datas.base.intervalday" :min="0" :step="1" placeholder=""
                                                 style="width: 50px"/>
                                    天
                                    <InputNumber v-model="datas.base.intervalhour" :max="23" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    小时
                                    <InputNumber v-model="datas.base.intervalminute" :max="59" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    分钟
                                    <InputNumber v-model="datas.base.intervalsecond" :max="59" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    秒
                                </FormItem>
                            </div>
                            <div v-if="datas.base.cycletype==3">
                                <FormItem :label-width="100" label="按工作日历:">
                                    <i-switch v-model="datas.base.continuerefcalendar" :falseValue="'0'" :trueValue="'1'"
                                              size="large">
                                        <span slot="open">开</span>
                                        <span slot="close">关</span>
                                    </i-switch>
                                </FormItem>
                                <FormItem :label-width="100" label="持续时间:">
                                    <InputNumber v-model="datas.base.continueday" :min="0" :step="1" placeholder=""
                                                 style="width: 50px"/>
                                    天
                                    <InputNumber v-model="datas.base.continuehour" :max="23" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    小时
                                    <InputNumber v-model="datas.base.continueminute" :max="59" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    分钟
                                    <InputNumber v-model="datas.base.continuesecond" :max="59" :min="0" :step="1"
                                                 placeholder="" style="width: 50px"/>
                                    秒
                                </FormItem>
                            </div>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                执行逻辑
                            </span>
                            <FormItem style="width: 600px" :label-width="120"  label="调用方式:">
                                <RadioGroup v-model="datas.base.callType" @on-change="callTypeHandler" >
                                    <Radio label="1">
                                        <span>本地</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>远端</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem :label-width="120" label="适配器类型:">
                                <RadioGroup v-model="datas.base.adaptertype" @on-change="callAdapterHandler" >
                                    <Radio label="1">
                                        <span>Spring Bean</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>普通JavaBean</span>
                                    </Radio>
                                    <Radio label="3" v-show="showUri">
                                        <span>URI</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem :label-width="120"  v-if="datas.base.adaptertype!=='3'" label="适配器名称:">
                                <Input v-model="datas.base.adaptername" :key="`${timeStamp}-adaptername`" placeholder="适配器名" style="width: 480px"
                                       type="text">
                                </Input>
                                <span style="vertical-align:middle;font-size: 14px;line-height:24px;color: red">*</span>
                            </FormItem>
                            <FormItem :label-width="120"    v-if="datas.base.adaptertype==='3'" label="回调URI:">
                                <Input v-model="datas.base.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 480px" type="text">
                                </Input>
                                <span style="vertical-align:middle;font-size: 14px;line-height:24px;color: red">*</span>
                            </FormItem>
                            <FormItem :label-width="120"  label="回调参数:">
                                <Input v-model="datas.base.callBackParam" :key="`${timeStamp}-callBackParam`" placeholder="回调参数" style="width: 480px" type="text">
                                </Input>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                跳转退回权限
                            </span>
                            <FormItem :label-width="200" label="其他节点能否跳转到本节点:">
                                <i-switch v-model="datas.base.skipauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                            <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                                <i-switch v-model="datas.base.rejectauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
    </Tabs>
</template>

<script>
import EventRegister from "./EventRegister";
export default {
    name: 'TimerNodeSetting',
    components:{
        EventRegister
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            eventTypeList: [{
                value: '1',
                label: '节点开始时'
            }, {
                value: '2',
                label: '节点结束时'
            }],
            showUri:true,
            timeStamp:''
        }
    },
    methods: {
        callTypeHandler(){
            this.$nextTick(()=>{
                if(this.datas.base.callType==='1'&&this.datas.base.adaptertype==='3'){
                    this.datas.base.adaptertype='1';
                }
                this.showUri = this.datas.base.callType==='2'
            })
        },
        callAdapterHandler(){
            if(this.datas.base.adaptertype==='3'){
                this.$nextTick(()=>{
                    this.datas.base.adaptername='';
                })
            }else{
                this.$nextTick(()=>{
                    this.datas.base.callBackURI='';
                    this.datas.base.callBackParam='';
                })
            }
        },
        setStatus() {
            // console.log('change')
            if (this.datas.base.cycletype == '1') {
                this.datas.base.cyclecount = 0;
                this.datas.base.intervalrefcalendar = 0;
                this.datas.base.intervalday = 0;
                this.datas.base.intervalhour = 0;
                this.datas.base.intervalminute = 0;
                this.datas.base.intervalsecond = 0;

                this.datas.base.continuerefcalendar = 0;
                this.datas.base.continueday = 0;
                this.datas.base.continuehour = 0;
                this.datas.base.continueminute = 0;
                this.datas.base.continuesecond = 0;
            } else if (this.datas.base.cycletype == '2') {
                this.datas.base.continuerefcalendar = 0;
                this.datas.base.continueday = 0;
                this.datas.base.continuehour = 0;
                this.datas.base.continueminute = 0;
                this.datas.base.continuesecond = 0;
            } else if (this.datas.base.cycletype == '3') {
                this.datas.base.continuerefcalendar = 0;
                this.datas.base.cyclecount = 0;
            }
        }
    },
    created(){
        if(!this.datas.base.callType||this.datas.base.callType==='') {
            this.datas.base.callType = this.flowConfig.defaultCallType;
        }
        this.showUri = this.datas.base.callType==='2'
        this.timeStamp = Date.now()+'';
    }
}
</script>
