/**
 *
 * 边配置
 */

export default {
    style: {
        default: {
            stroke: '#000000',
            strokeOpacity: 1,
            lineWidth: 1,
            endArrow: {
                path: 'M 0,0 L 6,3 L 6,-3 Z',  // 自定义箭头为中心点在(0, 0)，指向 x 轴正方向的path
                d: 0,
                fill: '#000000',
                stroke: '#000000',
                opacity: 1,
            },
        },
        active: {
            stroke: '#f52816',
            shadowBlur: 4,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            endArrow: {
                path: 'M 0,0 L 6,3 L 6,-3 Z',  // 自定义箭头为中心点在(0, 0)，指向 x 轴正方向的path
                d: 0,
                fill: '#f52816',
                stroke: '#f52816',
                opacity: 1,
            },
        },
        inactive: {
            stroke: '#000000',
            shadowColor: '',
            endArrow: {
                path: 'M 0,0 L 6,3 L 6,-3 Z',  // 自定义箭头为中心点在(0, 0)，指向 x 轴正方向的path
                d: 0,
                fill: '#000000',
                stroke: '#000000',
                opacity: 1,
            },
        }, history: {
            stroke: '#f52816',
            shadowBlur: 4,
            lineWidth: 2,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            endArrow: {
                path: 'M 0,0 L 6,3 L 6,-3 Z',  // 自定义箭头为中心点在(0, 0)，指向 x 轴正方向的path
                d: 0,
                fill: '#f52816',
                stroke: '#f52816',
                opacity: 1,
            },
        },
    },
    type: {
        solid: {
            lineDash: []
        },
        dashed: {
            lineDash: [5, 5]
        },
        dot: {
            lineDash: [2, 2]
        }
    }
}
