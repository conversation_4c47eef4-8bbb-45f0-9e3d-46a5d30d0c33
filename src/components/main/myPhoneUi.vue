
<template>
    <div>
        <div class="approve-softphone" v-show="phoneVisible">
            <div class="softphone-box" :class="active ? 'active':'no-active'">
                <div class="softContent">
                    <div class="softBack">
                        <div class="headerIcon" style="margin-bottom: 15px;">
                            <div class="heaerPhone">
                                <img src="../../assets/img/cateType.png" />
                            </div>
                            <Select v-model="statusValue" style="width:100%" @on-change="changeStatue">
                                <Option v-for="item in statusOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </div>
                        <div  class="headerIcon">
                            <div class="heaerPhone">
                                <img src="../../assets/img/callPhone.png" />
                            </div>
                            <Input clearable :value="softPhoneNumber" placeholder="请输入电话号码"
                                @on-change="changePhoneNumber"></Input>
                        </div>
                        <div class="middle">
                            <img class="timeIcon" src="../../assets/img/status.png" />
                            <span class="commonStues">通话状态：</span>
                            <span v-if="isCalling==='0'">拨打中...</span>
                            <span v-else-if="isCalling==='1'">通话中...</span>
                            <span v-else-if="isCalling==='2'">通话结束...</span>
                            <span v-else-if="isCalling==='3'">通话挂断失败...</span>
                            <span v-else></span>
                        </div>
<!--                       <div class="middle">
                            <img  class="timeIcon" src="../../assets/img/time.png" />
                            <span class="commonStues">录音时间：</span>
                            <span id="allTime" style="color: #45629F;">{{phoneTime}}</span>
                        </div> -->
                        <div style="margin-top: 15px;display: flex;flex-direction: row;justify-content: space-around">
                            <div class="callphoneButton" type="text" @click="callphone">
                                <img src="../../assets/img/call.png" />
                                <span>呼出</span>
                            </div>
                            <div class="hangupButton" type="text" @click="hangupClick">
                                <img src="../../assets/img/hangup.png" />
                                <span>挂断</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="active" class="close" @click.stop="CloseClick">
                    <img src="../../assets/img/close.png" />
                </div>
            </div>
        </div>
        <div class="phoneIcon" @click="showSoftHhone">
            <img src="../../assets/img/callPhone.png" />
            <span>电话</span>
        </div>
    </div>
</template>


<script>
    import {setStore,getStore} from "@/libs/utils/store";
    import Constants from "@/const/common/Constants"
    export default{
        data(){
            return{
                isCalling:'',
                timer:null,
                statusValue:'',
                statusOptions:[],
                phoneTime:'00:00:00',
                phoneVisible:true,
            }
        },
        props:{
            //是否显示电话界面
            active:{
                type:Boolean,
                default:false,
            },
            softPhoneNumber:{
                type:String,
                default:'',
            },
            customInfo:{
                type:Object,
                default:()=>{
                    return{}
                }
            }
        },
        mounted() {

        },
        methods:{
            changeStatue(){
                let broadChannel = new BroadcastChannel('newsoftPhone');
                broadChannel.postMessage({
                    methodType:'status',
                    statusValue:this.statusValue
                });
                console.log("soft----status");
                broadChannel.close();
            },
            CloseClick(){
                this.$emit('update:active',false);
            },
            changePhoneNumber(event){
                let value = event.target.value;
                this.$emit('update:softPhoneNumber',value);
            },
            showSoftHhone(){
                if(this.active){
                    this.$emit('update:active',false);
                }else{
                    this.$emit('update:active',true);
                }
            },
            //打电话
            callphone(){
                if(!this.softPhoneNumber){
                    this.$Message.error('请输入号码');
                    return;
                }
                if(this.statusValue!='1'){
                    this.$Message.error('在线状态才可以拨打电话');
                    return;
                }
                let broadChannel = new BroadcastChannel('newsoftPhone');
                broadChannel.postMessage({
                    methodType:'callPhone',
                    phone:this.softPhoneNumber
                });
                console.log("soft----callphone");
                broadChannel.close();
            },
            //挂断
            hangupClick(){
                if(this.isCalling==='0'||this.isCalling==='1'){
                    let broadChannel = new BroadcastChannel('newsoftPhone');
                    broadChannel.postMessage({
                        methodType:'hangup',
                        phone:this.softPhoneNumber
                    });
                    console.log("soft----hangupClick");
                    broadChannel.close();
                }else{
                    
                }
            },
            doneClick(){
                let broadChannel = new BroadcastChannel('newsoftPhone');
                broadChannel.postMessage({
                    methodType:'done',
                    phone:this.softPhoneNumber
                });
                console.log("soft----doneClick");
                broadChannel.close();
            }
        },
        created() {
            this.timer = setInterval(()=> {
                this.statusOptions = getStore({name:Constants.phone_statusList});
                this.statusValue = getStore({name:Constants.phone_statusValue});
                this.isCalling = getStore({name:Constants.phone_isCalling});
                console.log('--------');
            }, 1000)
        },
        destroyed(){
            console.log("newSoftPhone.vue----destroyed");
        }
    }
</script>


<style lang="less" scoped>
//电话
.phoneIcon{
    cursor: pointer;
    position: fixed;
    right: 0;
    width: 60px;
    height: 80px;
    bottom: 250px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #3D73E6;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 20px;
    border-radius: 10px;
    & img{
        width: 24px;
        height: 24px;
    }
}


.approve-softphone /deep/ .ivu-input-wrapper{
    width: 100%;
}

.approve-softphone /deep/ .ivu-input{
    height: 34px;
    border:1px solid #A1BEFF;
    border-left: none;
}

.approve-softphone /deep/ .ivu-select-small.ivu-select-single .ivu-select-selection{
    height: 34px;
    border:1px solid #A1BEFF;
    border-left: none;
    line-height: 32px;
}

.approve-softphone /deep/ .ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-selected-value{
    line-height: 32px;
    border-left: none;
    line-height: 32px;
}


.approve-softphone /deep/ .ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-placeholder{
    height: 34px;
    line-height: 32px;
}
.approve-softphone {
    position: fixed;
    right: 0;
    height: 200px;
    bottom: 200px;
    z-index: 999;
    .softphone-box {
        overflow: hidden;
        transition: all linear 0.3s;
        &.active {
            width: 400px;
        }
        &.no-active {
            width: 0px;
        }
    }
    //包裹两个
    .softphone{
        display: flex;
        flex-direction: row;
    }
    //左边那块
    .softContent{
        width: 330px;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 6px 16px 2px rgba(232,232,232,0.9100);
        border-radius: 10px;
        padding: 15px;
        position: relative;
    }
    .close{
        position: absolute;
        right: 70px;
        width:20px ;
        height: 20px;
        top: -10px;
        z-index: 5000;
        cursor: pointer;
    }

    //底下
    .softBack{
        background: #F3F7FF;
        padding: 15px;
        border-radius: 10px;
    }
    .headerIcon{
        display: flex;
        flex-direction: row;
    }
    .heaerPhone{
        background-color: #3D73E6;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        width: 30px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .heaerPhone img{
        width: 16px;
        height: 16px;
        display: flex;
        flex-direction: row;
    }

    //中间
    .middle{
        margin-top: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
    }
    .timeIcon{
        width: 16px;
        height: 16px;
        margin-left: 5px;
    }
    .commonStues{
        color: #647EB7;
        font-size: 12px;
        line-height: 16px;
        margin-left: 8px;
    }

    .callphoneButton,.hangupButton,.agreeButton{
        width: 78px;
        height: 26px;
        border-radius: 13px;
        color: #FFFFFF;
        font-weight: 500;
        margin-right: 10px;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .callphoneButton img,.hangupButton img,.agreeButton img{
        margin-right: 5px;
    }
    .callphoneButton{
        background: linear-gradient(90deg, #65AAFF 0%, #3D73E6 100%);
    }
    .hangupButton{
        background: linear-gradient(90deg, #FF7C7C 0%, #F73D3D 100%);
    }
    .agreeButton{
        background: linear-gradient(90deg, #FFB748 0%, #F7943D 100%);
    }

}
</style>
