package com.ruicar.afs.cloud.channel.online.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.channel.affiliation.mapper.ChannelBaseInfoTempMapper;
import com.byd.afc.cloud.oa.config.BydChannelOaProperties;
import com.ruicar.afs.cloud.channel.affiliation.service.ChannelBaseInfoTempService;
import com.ruicar.afs.cloud.channel.cardealer.common.Constants;
import com.ruicar.afs.cloud.channel.cardealer.service.CarDealerService;
import com.ruicar.afs.cloud.channel.channelnetwork.entity.ChannelNetwork;
import com.ruicar.afs.cloud.channel.channelnetwork.service.ChannelNetworkService;
import com.ruicar.afs.cloud.channel.channelworkflow.entity.WorkTaskDetail;
import com.ruicar.afs.cloud.channel.channelworkflow.service.WorkTaskApproveRecordService;
import com.ruicar.afs.cloud.channel.channelworkflow.service.WorkTaskDetailService;
import com.ruicar.afs.cloud.channel.common.executor.ElasticService;
import com.ruicar.afs.cloud.channel.common.feign.ChannelUseCaseService;
import com.ruicar.afs.cloud.channel.common.mq.sender.ChannelOnlineInfoToCaseSender;
import com.ruicar.afs.cloud.channel.common.mq.sender.SenderChannelToApply;
import com.ruicar.afs.cloud.channel.common.mq.sender.impl.ChannelIdToCommissionSystemImpl;
import com.ruicar.afs.cloud.channel.common.until.ApplyConstants;
import com.ruicar.afs.cloud.channel.common.until.ChannelConfig;
import com.ruicar.afs.cloud.channel.common.until.ChannelRoleEnum;
import com.ruicar.afs.cloud.channel.common.until.Const;
import com.ruicar.afs.cloud.channel.common.until.RoleTypeDic;
import com.ruicar.afs.cloud.channel.intend.entity.IntendDealerInfo;
import com.ruicar.afs.cloud.channel.intend.service.IntendDealerInfoService;
import com.ruicar.afs.cloud.channel.jointparty.entity.ChannelBaseJointPartyRel;
import com.ruicar.afs.cloud.channel.jointparty.entity.ChannelJointPartyInfo;
import com.ruicar.afs.cloud.channel.jointparty.service.ChannelJointPartyRelationService;
import com.ruicar.afs.cloud.channel.jointparty.service.JointPartyInfoService;
import com.ruicar.afs.cloud.channel.jointparty.vo.ChannelJointPartyInfoDto;
import com.ruicar.afs.cloud.channel.mange.entity.ChannelAppertainRegion;
import com.ruicar.afs.cloud.channel.mange.entity.ChannelUser;
import com.ruicar.afs.cloud.channel.mange.entity.ChannelUserAppertain;
import com.ruicar.afs.cloud.channel.mange.enums.RegionLevel;
import com.ruicar.afs.cloud.channel.mange.service.ChannelAppertainRegionService;
import com.ruicar.afs.cloud.channel.mange.service.ChannelUserAppertainService;
import com.ruicar.afs.cloud.channel.mange.service.ChannelUserService;
import com.ruicar.afs.cloud.channel.online.condition.ArchivesLogInfoCondition;
import com.ruicar.afs.cloud.channel.online.condition.BatchManagerDTO;
import com.ruicar.afs.cloud.channel.online.condition.ChannelAccountInfoCondition;
import com.ruicar.afs.cloud.channel.online.condition.ChannelAuthRegionCondition;
import com.ruicar.afs.cloud.channel.online.condition.ChannelBasicTempCondition;
import com.ruicar.afs.cloud.channel.online.condition.ChannelExcelConditionVO;
import com.ruicar.afs.cloud.channel.online.condition.ChannelMaintainApproveSubmitVo;
import com.ruicar.afs.cloud.channel.online.condition.ChannelOnlineCondition;
import com.ruicar.afs.cloud.channel.online.condition.ChannelOnlineConditionUpd;
import com.ruicar.afs.cloud.channel.online.condition.ChannelShareholderInfoCondition;
import com.ruicar.afs.cloud.channel.online.condition.DevelopDTO;
import com.ruicar.afs.cloud.channel.online.condition.RegionDTO;
import com.ruicar.afs.cloud.channel.online.constant.ChannelInfoUpdatesConstants;
import com.ruicar.afs.cloud.channel.online.entity.ChannelArchivesInfo;
import com.ruicar.afs.cloud.channel.online.entity.ChannelAuthorizeRegionTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelAuthorizeRegionTempUpd;
import com.ruicar.afs.cloud.channel.online.entity.ChannelAuthorizeVehicleTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelAuthorizeVehicleTempUpd;
import com.ruicar.afs.cloud.channel.online.entity.ChannelBaseInfoTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelBaseInfoTempUpd;
import com.ruicar.afs.cloud.channel.online.entity.ChannelInfoUpdates;
import com.ruicar.afs.cloud.channel.online.entity.ChannelMainBrand;
import com.ruicar.afs.cloud.channel.online.entity.ChannelQualityGradeTempImport;
import com.ruicar.afs.cloud.channel.online.entity.ChannelQuotaInfoTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelQuotaInfoTempUpd;
import com.ruicar.afs.cloud.channel.online.entity.ChannelReceivablesAccountTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelRiskInfoTemp;
import com.ruicar.afs.cloud.channel.online.entity.ChannelRiskInfoTempUpd;
import com.ruicar.afs.cloud.channel.online.entity.ChannelShareholderInfo;
import com.ruicar.afs.cloud.channel.online.entity.ChannelWitnessesRecord;
import com.ruicar.afs.cloud.channel.online.entity.EasyResEntry;
import com.ruicar.afs.cloud.channel.online.enums.ChannelImportTypeEnum;
import com.ruicar.afs.cloud.channel.online.enums.ChannelImportUpdateEnum;
import com.ruicar.afs.cloud.channel.online.enums.ChannelOnlineEnum;
import com.ruicar.afs.cloud.channel.online.feign.AdminDeptFeign;
import com.ruicar.afs.cloud.channel.online.feign.ApplyFeignService;
import com.ruicar.afs.cloud.channel.online.feign.CaseFeignService;
import com.ruicar.afs.cloud.channel.online.service.ChannelAccountInfoService;
import com.ruicar.afs.cloud.channel.online.service.ChannelArchivesInfoService;
import com.ruicar.afs.cloud.channel.online.service.ChannelAuthorizeRegionService;
import com.ruicar.afs.cloud.channel.online.service.ChannelAuthorizeRegionUpdService;
import com.ruicar.afs.cloud.channel.online.service.ChannelAuthorizeVehicleTempService;
import com.ruicar.afs.cloud.channel.online.service.ChannelAuthorizeVehicleTempUpdService;
import com.ruicar.afs.cloud.channel.online.service.ChannelInfoUpdateService;
import com.ruicar.afs.cloud.channel.online.service.ChannelMainBrandService;
import com.ruicar.afs.cloud.channel.online.service.ChannelOnlineService;
import com.ruicar.afs.cloud.channel.online.service.ChannelOnlineUpdService;
import com.ruicar.afs.cloud.channel.online.service.ChannelQuotaInfoService;
import com.ruicar.afs.cloud.channel.online.service.ChannelQuotaInfoUpdService;
import com.ruicar.afs.cloud.channel.online.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.channel.online.service.ChannelRiskInfoUpdService;
import com.ruicar.afs.cloud.channel.online.service.ChannelShareholderInfoTempService;
import com.ruicar.afs.cloud.channel.online.service.ChannelWitnessesRecordService;
import com.ruicar.afs.cloud.channel.online.untils.ChannelOnlineConstants;
import com.ruicar.afs.cloud.channel.online.untils.DicUtils;
import com.ruicar.afs.cloud.channel.online.untils.EasyExcelUtils;
import com.ruicar.afs.cloud.channel.online.untils.FieldAnnotationVerifyUtil;
import com.ruicar.afs.cloud.channel.online.vo.ChannelAbleIdsVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelAehicle;
import com.ruicar.afs.cloud.channel.online.vo.ChannelApplyPowerVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelBaseAccountVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelContractNumberVO;
import com.ruicar.afs.cloud.channel.online.vo.ChannelCustManageVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelOnlineInfoVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelOnlineInterFaceVo;
import com.ruicar.afs.cloud.channel.online.vo.ChannelReportVo;
import com.ruicar.afs.cloud.channel.subject.entity.SubjectInfo;
import com.ruicar.afs.cloud.channel.subject.service.business.BusinessSubjectInfoService;
import com.ruicar.afs.cloud.channel.witness.entity.ChannelWitnessInfoTemp;
import com.ruicar.afs.cloud.channel.witness.service.ChannelWitnessInfoTempService;
import com.ruicar.afs.cloud.channel.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.channel.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.channel.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.channel.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.channel.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.channel.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.channel.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.channel.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.uid.UidGenerator;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.channel.enums.ChannelBusinessType;
import com.ruicar.afs.cloud.common.modules.channel.enums.ChannelStatus;
import com.ruicar.afs.cloud.common.modules.excelentity.channel.ChannelBaseInfoTempImport;
import com.ruicar.afs.cloud.common.modules.excelentity.channel.ChannelReceivablesAccountTempImport;
import com.ruicar.afs.cloud.common.modules.excelentity.channel.ChannelShareholderInfoImport;
import com.ruicar.afs.cloud.common.modules.utils.AttributeVerificationRulesFunction;
import com.ruicar.afs.cloud.common.modules.utils.ResponseHeadUtils;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.config.api.address.dto.AddrQueryDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.parameter.commom.utils.ParamConstants;
import com.ruicar.afs.cloud.salesmanage.vo.SaleTeamUserVo;
import com.ruicar.afs.cloud.salesmanage.workflow.constant.SalesManageFlowConstant;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.ProcessInstanceDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.channel.witness.utils.WitnessDic.BUSINESS_ATTRIBUTES_NEW;

/**
 * @ClassName:ChannelOnlineController
 * @Description: 渠道上线申请保存信息-controller
 * @Author:mingzhi.li
 * @Date:2020/5/18 19:06
 * @Version: V1.0
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/channelOnline")
@Api(value = "channelOnline", description = "渠道上线申请")
public class ChannelOnlineController {

    /**
     *  联合方信息与合作商关联信息
     */
    @Autowired
    private ChannelJointPartyRelationService channelJointPartyRelationService;

    /**
     *  合作商信息
     */
    @Autowired
    private ChannelBaseInfoTempService channelBaseInfoTempService;

    /**
     *  联合方信息
     */
    @Autowired
    private JointPartyInfoService jointPartyInfoService;

    /**
     * 基本信息
     */
    @Autowired
    private ChannelOnlineService channelOnlineService;

    /**
     * 收款账号
     */
    @Autowired
    private ChannelAccountInfoService channelAccountInfoService;
    @Autowired
    private ChannelShareholderInfoTempService channelShareholderInfoTempService;

    /**
     * 风控数据
     */
    @Autowired
    private ChannelRiskInfoService channelRiskInfoService;

    /**
     * 保证金
     */
    @Autowired
    private ChannelQuotaInfoService channelQuotaInfoService;

    /**
     * 授权区域
     */
    @Autowired
    private ChannelAuthorizeRegionService channelAuthorizeRegionService;

    /**
     * 建档日期
     */
    @Autowired
    private ChannelArchivesInfoService channelArchivesInfoService;

    /**
     *
     */
    private AddressService service;

    /**
     * 授权车型
     */
    @Autowired
    private ChannelAuthorizeVehicleTempService channelAuthorizeVehicleTempService;

    /**
     * 主营品牌
     */
    @Autowired
    private ChannelMainBrandService channelMainBrandService;

    private final ChannelUserService userService;
    /**
     * 工作流
     */
    private final WorkTaskDetailService workTaskDetailService;
    private final UidGenerator uidGenerator;
    private final WorkTaskApproveRecordService workTaskApproveRecordService;
    /**
     * 授权区域
     */
    private final ChannelAppertainRegionService appertainRegionService;
    private final ChannelUserAppertainService userAppertainService;
    private final ChannelConfig channelConfig;
    private final ChannelUseCaseService channelUseCaseService;
    private final ChannelInfoUpdateService channelInfoUpdateService;
    private final ChannelOnlineInfoToCaseSender channelOnlineInfoToCaseSender;
    private final SenderChannelToApply senderChannelToApply;
    private final ChannelIdToCommissionSystemImpl channelIdToCommissionSystem;
    private final CarDealerService carDealerService;
    private final ChannelWitnessInfoTempService channelWitnessInfoTempService;
    private final ChannelNetworkService channelNetworkService;

    private final ApplyFeignService applyFeignService;
    private ElasticService elasticService;

    @Autowired
    private WorkflowHelper workflowHelper;
    private FlowConfigProperties flowConfigProperties;

    private final WorkflowTaskInfoService workflowTaskInfoService;
    private final IntendDealerInfoService intendDealerInfoService;
    private final BusinessSubjectInfoService businessSubjectInfoService;

    private final CaseFeignService caseFeignService;
    private AdminDeptFeign adminDeptFeign;


    /**
     * 基本信息-审批修改
     */
    @Autowired
    private ChannelOnlineUpdService channelOnlineUpdService;

    /**
     * 风控数据-审批修改
     */
    @Autowired
    private ChannelRiskInfoUpdService channelRiskInfoUpdService;

    /**
     * 保证金-审批修改
     */
    @Autowired
    private ChannelQuotaInfoUpdService channelQuotaInfoUpdService;

    /**
     * 授权区域-审批修改
     */
    @Autowired
    private ChannelAuthorizeRegionUpdService channelAuthorizeRegionUpdService;

    /**
     * 授权车型-审批修改
     */
    @Autowired
    private ChannelAuthorizeVehicleTempUpdService channelAuthorizeVehicleTempUpdService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final ChannelWitnessesRecordService witnessesRecordService;
    private ChannelBaseInfoTempMapper channelBaseInfoTempMapper;
    private BydChannelOaProperties bydChannelOaProperties;


    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "保存渠道上线申请信息")
    @SysLog("保存渠道上线申请信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveOrUpdate(@RequestBody ChannelOnlineCondition channelOnlineCondition) {

        //经销商保存时，后台强制校验下，注册省份不能为空，否则直接抛异常提示下。合作商代码，SAP代码，不能为空
        if(StringUtils.isEmpty(channelOnlineCondition.getChannelBaseInfoTemp().getChannelCode())){
            throw new AfsBaseException("合作商代码不能为空");
        }
        if (StringUtils.isEmpty(channelOnlineCondition.getChannelBaseInfoTemp().getSpaCode())){
            throw new AfsBaseException("SAP代码不能为空");
        }

        String socUniCrtCode = channelOnlineCondition.getChannelBaseInfoTemp().getSocUniCrtCode();
        if (StrUtil.isBlank(socUniCrtCode)){
            throw new AfsBaseException("统一社会信用代码不能为空");
        }
        socUniCrtCode = socUniCrtCode.toUpperCase();
        if (Arrays.stream(ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS).anyMatch(socUniCrtCode::contains)) {
            String joinedChars = String.join("、", ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS);
            return IResponse.fail(MessageFormat.format("“{0}”不能用于编码，请检查后重新录入", joinedChars));
        }

        // 拿我的角色看看我是属于新车权限还是二手车权限
        List<String> role =SecurityUtils.getRoles();
        // 看看合作商的类型：直营或者非直营
        if("01".equals(channelOnlineCondition.getChannelBaseInfoTemp().getChannelBelong())){
            // 说明是直营
            if(role.contains(ChannelRoleEnum.ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP.name())){
                // 说明有直营新车
            }
            if(role.contains(ChannelRoleEnum.ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP.name())){
                // 说明是直营二手车
            }
        }else {
            // 说明是非直营
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP.name())){
                // 说明有非直营新车
            }
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP.name())){
                // 说明是非直营二手车
            }
        }

        // 声明一个不可以修改的状态集合
        List<String> statusList=new ArrayList<>();
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);

        List list = new ArrayList<>();
        //渠道基本信息
        ChannelBaseInfoTemp temp = channelOnlineCondition.getChannelBaseInfoTemp();
        // 重新处理为空的数据（之前有数据现在没有数据）
        temp = channelOnlineService.processingValues(temp);
        // 判断这个合作商的状态是否可以修改
        ChannelBaseInfoTemp oldChannelTemp=channelOnlineService.checkChannelUpdata(channelOnlineCondition,temp,statusList);
        //校验合作商代码和合作商名称是否重复
        if (channelOnlineService.checkChannelNameAndCodeRepeat(temp)) {
            return IResponse.fail("合作商代码或名称重复！");
        }
        // 重新更新合作商信息
        temp =channelOnlineService.saveOrUpdataChannel(channelOnlineCondition,temp, ChannelBusinessType.SAVE.getValue().toString());
        list.add(temp);

        if (ObjectUtils.isNotEmpty(channelOnlineCondition.getChannelBaseId())) {
            IntendDealerInfo intendDealerInfo = intendDealerInfoService.getOne(Wrappers.<IntendDealerInfo>lambdaQuery().eq(IntendDealerInfo::getChannelBaseId,channelOnlineCondition.getChannelBaseId()));
            intendDealerInfo.setIntendStatus(ChannelStatus.STATUS_ZORO_DRAFT.getValue());
            intendDealerInfoService.updateById(intendDealerInfo);
        }
        // 这个地方单独处理下不控制业务类型的情况
        if(CommonConstants.COMMON_NO.equals(temp.getControlBusinessType())){
            // 说明不控制业务类型，这样话就需要把二手车的清空，然后把新车给到二手车
            channelOnlineCondition.setMainAreaOld(channelOnlineCondition.getMainArea());
            channelOnlineCondition.setMainCarOld(channelOnlineCondition.getMainCar());
        }

        //  这边是判断处理新车
        if (CommonConstants.COMMON_YES.equals(channelOnlineCondition.getIsNewCarRole())&&(!statusList.contains(oldChannelTemp.getChannelStatus()))) {
            list= channelOnlineService.saveRelevanceChannel(channelOnlineCondition,temp,ChannelBusinessType.NEW_CAR.getValue().toString(),list);
        }

        //判断当前登陆者是否拥有二手车权限，由前端传入
        if (CommonConstants.COMMON_YES.equals(channelOnlineCondition.getIsOldCarRole())&&(!statusList.contains(oldChannelTemp.getChannelStatusOldCar()))) {
            list= channelOnlineService.saveRelevanceChannel(channelOnlineCondition,temp,ChannelBusinessType.OLD_CAR.getValue().toString(),list);
        }

        // 最后处理一下新车二手车的状态 temp
        ChannelBaseInfoTemp nowTemp = channelOnlineService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId, temp.getId()));
        if(!nowTemp.getBusinessType().contains(ChannelBusinessType.NEW_CAR.getValue().toString())){
            // 说明没有新车
            channelOnlineService.update(Wrappers.<ChannelBaseInfoTemp>lambdaUpdate().set(ChannelBaseInfoTemp::getChannelStatus,null).eq(ChannelBaseInfoTemp::getId,temp.getId()));
        }
        if(!nowTemp.getBusinessType().contains(ChannelBusinessType.OLD_CAR.getValue().toString())){
            // 说明没有二手车
            channelOnlineService.update(Wrappers.<ChannelBaseInfoTemp>lambdaUpdate().set(ChannelBaseInfoTemp::getChannelStatusOldCar,null).eq(ChannelBaseInfoTemp::getId,temp.getId()));
        }

        return IResponse.success(list);
    }

    @PostMapping("/getChannelBasicInfo")
    @ApiOperation(value = "获取渠道基本信息")
    @SysLog("获取渠道基本信息")
    public IResponse getChannelBasicInfo(ChannelOnlineCondition channelOnlineCondition) {
        List<ChannelBaseInfoTemp> list = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getChannelCode, channelOnlineCondition.getId()));
        Map<String, String> map = new HashMap<>();
        list.stream().forEach(channelBaseInfoTemp -> {
                    map.put(String.valueOf(channelBaseInfoTemp.getId()), channelBaseInfoTemp.getChannelCode());
                }
        );
        return new IResponse<Map<String, String>>().setData(map);
    }

    @RequestMapping(value = "/getInfoById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取基本信息")
    public IResponse<List<ChannelBaseInfoTemp>> getInfoById(@PathVariable String id) {
        List<ChannelBaseInfoTemp> list = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId, id));
        list.stream().forEach(channelBaseInfoTemp -> {
            SubjectInfo  subjectInfo =businessSubjectInfoService.getInfoById(channelBaseInfoTemp.getSubjectAttributionId());
           if(subjectInfo!=null){
               channelBaseInfoTemp.setSubjectAttributionTitle(subjectInfo.getSubjectName());
           }
        }
        );
        return new IResponse<List<ChannelBaseInfoTemp>>().setData(list);
    }

    @RequestMapping(value = "/getRiskById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取风控信息")
    public IResponse<List<ChannelRiskInfoTemp>> getRiskById(@PathVariable String id) {
        List<ChannelRiskInfoTemp> list = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda().eq(ChannelRiskInfoTemp::getChannelId, id));
        return new IResponse<List<ChannelRiskInfoTemp>>().setData(list);
    }

    @RequestMapping(value = "/getQuotaInfoById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取保证金信息")
    public IResponse<List<ChannelQuotaInfoTemp>> getQuotaInfoById(@PathVariable String id) {
        List<ChannelQuotaInfoTemp> list = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfoTemp>query().lambda().eq(ChannelQuotaInfoTemp::getChannelId, id));
        return new IResponse<List<ChannelQuotaInfoTemp>>().setData(list);
    }

    @RequestMapping(value = "/getMainAreaById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取授权区域信息")
    public IResponse<List<ChannelAuthorizeRegionTemp>> getMainAreaById(@PathVariable String id) {
        List<ChannelAuthorizeRegionTemp> list = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId, id));
        return new IResponse<List<ChannelAuthorizeRegionTemp>>().setData(list);
    }

    @RequestMapping(value = "/getNewMainAreaById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取新车授权区域信息")
    public IResponse<List<ChannelAuthorizeRegionTemp>> getNewMainAreaById(@PathVariable String id) {
        List<ChannelAuthorizeRegionTemp> list = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                .eq(ChannelAuthorizeRegionTemp::getChannelId, id)
                .eq(ChannelAuthorizeRegionTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
        return new IResponse<List<ChannelAuthorizeRegionTemp>>().setData(list);
    }

    @RequestMapping(value = "/getOldMainAreaById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取二手车授权区域信息")
    public IResponse<List<ChannelAuthorizeRegionTemp>> getOldMainAreaById(@PathVariable String id) {
        List<ChannelAuthorizeRegionTemp> list = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                .eq(ChannelAuthorizeRegionTemp::getChannelId, id)
                .eq(ChannelAuthorizeRegionTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
        return new IResponse<List<ChannelAuthorizeRegionTemp>>().setData(list);
    }

    @RequestMapping(value = "/getMaiBrandById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取主营品牌信息")
    public IResponse<List<ChannelMainBrand>> getMaiBrandById(@PathVariable String id) {
        List<ChannelMainBrand> list = channelMainBrandService.list(Wrappers.<ChannelMainBrand>query().lambda().eq(ChannelMainBrand::getChannelId, id));
        return new IResponse<List<ChannelMainBrand>>().setData(list);
    }

    @RequestMapping(value = "/getVehicleById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取授权车型信息")
    public IResponse<List<ChannelAuthorizeVehicleTemp>> getVehicleById(@PathVariable String id) {
        List<ChannelAuthorizeVehicleTemp> list = channelAuthorizeVehicleTempService.list(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda().eq(ChannelAuthorizeVehicleTemp::getChannelId, id));
        return new IResponse<List<ChannelAuthorizeVehicleTemp>>().setData(list);
    }

    @RequestMapping(value = "/getNewVehicleById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取新车授权车型信息")
    public IResponse<List<ChannelAuthorizeVehicleTemp>> getNewVehicleById(@PathVariable String id) {
        List<ChannelAuthorizeVehicleTemp> list = channelAuthorizeVehicleTempService.list(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda()
                .eq(ChannelAuthorizeVehicleTemp::getChannelId, id)
                .eq(ChannelAuthorizeVehicleTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
        return new IResponse<List<ChannelAuthorizeVehicleTemp>>().setData(list);
    }

    @RequestMapping(value = "/getOldVehicleById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取二手车授权车型信息")
    public IResponse<List<ChannelAuthorizeVehicleTemp>> getOldVehicleById(@PathVariable String id) {
        List<ChannelAuthorizeVehicleTemp> list = channelAuthorizeVehicleTempService.list(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda()
                .eq(ChannelAuthorizeVehicleTemp::getChannelId, id)
                .eq(ChannelAuthorizeVehicleTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
        return new IResponse<List<ChannelAuthorizeVehicleTemp>>().setData(list);
    }

    @PostMapping(value = "/getByCondition")
    @ApiOperation(value = "多条件分页渠道信息列表")
    public IResponse<ChannelBaseInfoTemp> getByCondition(@RequestBody ChannelOnlineCondition condition) {
        IResponse<ChannelOnlineInfoVo> channelList = channelOnlineService.getByCondition(condition);
        return IResponse.success(channelList);
    }

    @PostMapping(value = "/pageExcelHis")
    @ApiOperation(value = "合作商信息excel修改信息")
    public IResponse<?> pageExcelHis(@RequestBody ChannelExcelConditionVO condition) {
        return channelOnlineService.getChannelInfoUpdateByCondition(condition);
    }

    @PostMapping(value = "/getAreaListCondition")
    @ApiOperation(value = "多条件分页获取渠道展业")
    public IResponse<IPage<ChannelBaseInfoTemp>> getAreaListCondition(@RequestBody ChannelOnlineCondition condition) {
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getAreaListCondition(page, condition);
        return IResponse.success(channelList);
    }

    @PostMapping("/getArchivesLogInfo")
    @ApiOperation(value = "分页查询档案日志信息")
    public IResponse<IPage<ChannelArchivesInfo>> getArchivesLogInfo(@RequestBody ArchivesLogInfoCondition condition) {
        IPage<ChannelArchivesInfo> list = channelArchivesInfoService.page(new Page(condition.getPageNumber(), condition.getPageSize())
                , Wrappers.<ChannelArchivesInfo>query().lambda()
                        .eq(condition.getChannelId() != null, ChannelArchivesInfo::getChannelId, condition.getChannelId()));
        return IResponse.success(list);
    }


    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.POST)
    @ApiOperation(value = "批量通过ids删除")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("批量通过ids删除")
    public IResponse<Boolean> delAllByIds(@PathVariable Long[] ids) {
        channelOnlineService.removeByIds(Arrays.asList(ids));
        return new IResponse<Boolean>().setMsg("批量通过id删除数据成功");
    }

    /**
     * 收款账号信息录入
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/saveAccountInfo", method = RequestMethod.POST)

    @ApiOperation(value = "保存数据")
    public IResponse saveAccountInfo(@Valid @RequestBody ChannelAccountInfoCondition condition) throws ParseException {
        ChannelReceivablesAccountTemp temp = condition.getChannelReceivablesAccountTemp();

        List<ChannelReceivablesAccountTemp> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getChannelId())
                .eq(ChannelReceivablesAccountTemp::getAccountAttribute, temp.getAccountAttribute())
                .and(wrapper -> wrapper
                        .eq(ChannelReceivablesAccountTemp::getCollectionType, temp.getCollectionType())
                        .eq(ChannelReceivablesAccountTemp::getAccountType, temp.getAccountType()))
                .and(wrapper -> wrapper
                        .in(ChannelReceivablesAccountTemp::getStatus
								, ChannelOnlineConstants.ACCOUNT_STATUS_TWO
                                , ChannelOnlineConstants.ACCOUNT_STATUS_ZERO
                                , ChannelOnlineConstants.ACCOUNT_STATUS_FIVE)
                ));

        if (accountList != null && accountList.size() > 0) {
            return IResponse.fail("此类收款账号已建立！");
        }

        temp.setStatus(ChannelOnlineConstants.ACCOUNT_STATUS_TWO);
        channelAccountInfoService.save(temp);
        return IResponse.success(Boolean.TRUE).setMsg("录入成功");
    }

    /**
     * 收款账号修改
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/updateAccountInfo", method = RequestMethod.POST)

    @ApiOperation(value = "修改数据")
    public IResponse updateAccountInfo(@Valid @RequestBody ChannelAccountInfoCondition condition) throws ParseException {

        List<ChannelReceivablesAccountTemp> list = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                .eq(ChannelReceivablesAccountTemp::getChannelId, condition.getChannelId())
                .eq(ChannelReceivablesAccountTemp::getAccountAttribute, condition.getAccountAttribute())
                .and(wrapper -> wrapper
                        .eq(ChannelReceivablesAccountTemp::getAccountType, condition.getAccountType())
                        .eq(ChannelReceivablesAccountTemp::getCollectionType, condition.getCollectionType()))
                .ne(ChannelReceivablesAccountTemp::getId, condition.getId().toString())
                .in(ChannelReceivablesAccountTemp::getStatus,
                        ChannelOnlineConstants.ACCOUNT_STATUS_TWO,
                        ChannelOnlineConstants.ACCOUNT_STATUS_ZERO,
                        ChannelOnlineConstants.ACCOUNT_STATUS_FIVE
                )
        );

        if (list.size() > 0) {
            return IResponse.fail("此类收款账号已建立");
        }

        condition.setStatus(ChannelOnlineConstants.ACCOUNT_STATUS_TWO);
        channelAccountInfoService.updateById(condition);
        return IResponse.success(Boolean.TRUE).setMsg("录入成功");
    }

    @RequestMapping(value = "/deleteAccount/{ids}", method = RequestMethod.POST)
    @ApiOperation(value = "删除收款账号信息")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("通过ids删除")
    public IResponse<Boolean> deleteAccount(@PathVariable Long[] ids) {
        channelAccountInfoService.removeByIds(Arrays.asList(ids));
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }

    /**
     * 根据多条件，分页获取收款账号信息
     *
     * @param condition
     * @return
     */
    @RequestMapping(value = "/getAccountInfo", method = RequestMethod.POST)
    @ApiOperation(value = "多条件分页获取收款账号信息")
    public IResponse<IPage<ChannelReceivablesAccountTemp>> getAccountInfo(@ModelAttribute ChannelAccountInfoCondition condition) {
        //渠道id为空null
        if (condition.getChannelId() == null) {
            return IResponse.fail("渠道id不能为空");
        }
        IPage<ChannelReceivablesAccountTemp> list = channelAccountInfoService.page(new Page(condition.getPageNumber(), condition.getPageSize())
                , Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                        .eq(condition.getChannelId() != null, ChannelReceivablesAccountTemp::getChannelId, condition.getChannelId()));
        return IResponse.success(list);
    }




    /**
     * 股东信息录入    ShareholderInfo
     * ChannelShareholderInfoCondition    ChannelShareholderInfoTemp
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/saveShareholderInfo", method = RequestMethod.POST)
    @ApiOperation(value = "保存股东数据")
    public IResponse saveShareholderInfo(@Valid @RequestBody ChannelShareholderInfoCondition condition) throws ParseException {
        ChannelShareholderInfo temp = condition.getChannelShareholderInfo();

        List<ChannelShareholderInfo> accountList = channelShareholderInfoTempService.list(Wrappers.<ChannelShareholderInfo>query().lambda()
                .eq(ChannelShareholderInfo::getChannelId, temp.getChannelId())
                .eq(ChannelShareholderInfo::getShareholderIdCard, temp.getShareholderIdCard()));
        if (accountList != null && accountList.size() > 0) {
            return IResponse.fail("此股东信息已建立！");
        }
        temp.setStatus(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO);
        channelShareholderInfoTempService.save(temp);
        return IResponse.success(Boolean.TRUE).setMsg("录入成功");
    }




    /**
     * 根据多条件，分页获取股东信息
     *
     * @param condition
     * @return
     */
    @RequestMapping(value = "/getShareholderInfo", method = RequestMethod.POST)
    @ApiOperation(value = "多条件分页获取股东信息")
    public IResponse<IPage<ChannelShareholderInfo>> getShareholderInfo(@ModelAttribute ChannelShareholderInfoCondition condition) {
        //渠道id为空null
        if (condition.getChannelId() == null) {
            return IResponse.fail("渠道id不能为空");
        }
        IPage<ChannelShareholderInfo> list = channelShareholderInfoTempService.page(new Page(condition.getPageNumber(), condition.getPageSize())
                , Wrappers.<ChannelShareholderInfo>query().lambda()
                        .eq(condition.getChannelId() != null, ChannelShareholderInfo::getChannelId, condition.getChannelId()));
        return IResponse.success(list);
    }


    /**
     * 股东信息修改  ShareholderInfo
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/updateShareholderInfo", method = RequestMethod.POST)

    @ApiOperation(value = "修改数据")
    public IResponse updateShareholderInfo(@Valid @RequestBody ChannelShareholderInfoCondition condition) throws ParseException {
        channelShareholderInfoTempService.updateById(condition);
        return IResponse.success(Boolean.TRUE).setMsg("录入成功");
    }


    @RequestMapping(value = "/deleteShareholderInfo/{ids}", method = RequestMethod.POST)
    @ApiOperation(value = "删除股东信息")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("通过ids删除")
    public IResponse<Boolean> deleteShareholderInfo(@PathVariable String[] ids) {
        channelShareholderInfoTempService.removeByIds(Arrays.asList(ids),false);
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }






    /**
     * 渠道发票信息保存
     *
     * @param channelOnlineCondition
     * @return
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/saveInvoiceInfo", method = RequestMethod.POST)
    @ApiOperation(value = "修改渠道发票信息")
    public IResponse saveInvoiceInfo(@RequestBody ChannelOnlineCondition channelOnlineCondition) throws ParseException {
        ChannelBaseInfoTemp temp = channelOnlineCondition.getChannelBaseInfoTemp();
        List<ChannelBaseInfoTemp> list = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId, temp.getId()));
        ChannelBaseInfoTemp infoTemp = null;
        if (list != null && list.size() > 0) {
            infoTemp = list.get(0);
            infoTemp.setTaxpayerType(temp.getTaxpayerType());
            infoTemp.setTaxpayerIdNumber(temp.getTaxpayerIdNumber());
            infoTemp.setInvoiceTelNumber(temp.getInvoiceTelNumber());
            infoTemp.setInvoiceAddress(temp.getInvoiceAddress());
            infoTemp.setOpeningBank(temp.getOpeningBank());
            infoTemp.setBankAccount(temp.getBankAccount());
            infoTemp.setTaxRate(temp.getTaxRate());
            channelOnlineService.updateById(infoTemp);
        }
        return IResponse.success(infoTemp);
    }

    @PostMapping("/socUniCrtCodeCheck")
    @ApiOperation(value = "校验统一社会信用代码")
    public Object checkBlack(@RequestBody ChannelOnlineCondition condition) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Long newId = condition.getId();
            if (!StrUtil.isBlank(condition.getSocUniCrtCode())) {
                List<ChannelBaseInfoTemp> socUniCrtCodeList = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getSocUniCrtCode, condition.getSocUniCrtCode()));
                if (newId == null) {
                    if (socUniCrtCodeList != null && socUniCrtCodeList.size() > 0) {
                        resultMap.put("yes", ChannelOnlineConstants.TYPE_YES);
                    }
                } else {
                    String oldId = null;
                    if (socUniCrtCodeList != null && socUniCrtCodeList.size() > 0) {
                        for (ChannelBaseInfoTemp baseInfoTemp : socUniCrtCodeList) {
                            oldId = baseInfoTemp.getId().toString();
                            if (!oldId.equals(newId.toString())) {
                                resultMap.put("yes", ChannelOnlineConstants.TYPE_YES);
                            }
                        }
                    }
                }
                resultMap.put("code", "0000");
                resultMap.put("msg", "success");
            }
        } catch (Exception e) {
            resultMap.put("code", "9999");
            resultMap.put("msg", "查询失败，请联系管理员！");
        }
        return resultMap;
    }

    @RequestMapping(value = "/channelSubmit", method = RequestMethod.POST)
    @ApiOperation(value = "渠道上线审批通过逻辑处理")
    @SysLog("渠道上线审批通过逻辑处理")
    @Transactional(rollbackFor = Exception.class)
    public IResponse channelSubmit(@RequestBody ChannelOnlineCondition channelOnlineCondition) {
        ChannelBaseInfoTemp temp = channelOnlineCondition.getChannelBaseInfoTemp();
        //Step 1：修改渠道状态为 00：启用
        List<ChannelBaseInfoTemp> list = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId, temp.getId()));
        String isNewCarRole = channelOnlineCondition.getIsNewCarRole();
        String isOldCarRole = channelOnlineCondition.getIsOldCarRole();
        if (list != null && list.size() > 0) {
            ChannelBaseInfoTemp infoTemp = list.get(0);

            //提交验证汇款对象是否为主体：如果是则上线阶段需要验证是否录入过收款账号
            //新车验证
            if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isNewCarRole)) {
                if (!StrUtil.isBlank(infoTemp.getChannelStatus())) {
                    infoTemp.setChannelStatus(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
                }
                if (ChannelOnlineConstants.PAYMENT_OBJECT_ZERO.equals(infoTemp.getPaymentObject())) {
                    List<ChannelReceivablesAccountTemp> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_ONE)
                            .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
                    if (accountList == null || accountList.size() == 0) {
                        return IResponse.fail("当前渠道汇款对象为主体，请录入新车全款收款账号信息！");
                    }

                    List<ChannelReceivablesAccountTemp> accountTempList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_TWO));
                    if (accountTempList == null || accountTempList.size() == 0) {
                        return IResponse.fail("当前渠道汇款对象为主体，请录入新车佣金收款账号信息！");
                    }
                }
            }

            //二手车验证
            if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isOldCarRole)) {
                if (!StrUtil.isBlank(infoTemp.getChannelStatusOldCar())) {
                    infoTemp.setChannelStatusOldCar(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
                }
                if (ChannelOnlineConstants.PAYMENT_OBJECT_ZERO.equals(infoTemp.getPaymentObject())) {
                    List<ChannelReceivablesAccountTemp> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_ONE)
                            .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
                    if (accountList == null || accountList.size() == 0) {
                        return IResponse.fail("当前渠道汇款对象为主体，请录入二手车全款收款账号信息！");
                    }

                    List<ChannelReceivablesAccountTemp> accountTempList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_TWO)
                            .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
                    if (accountTempList == null || accountTempList.size() == 0) {
                        return IResponse.fail("当前渠道汇款对象为主体，请录入二手车佣金收款账号信息！");
                    }
                }
            }

            channelOnlineService.updateById(infoTemp);

            //Step 2：同步信息到案件服务
            ChannelOnlineInterFaceVo vo = new ChannelOnlineInterFaceVo();

            vo.setChannelBaseInfo(infoTemp);

            //查询风控信息
            List<ChannelRiskInfoTemp> channelRiskInfoTempList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda().eq(ChannelRiskInfoTemp::getChannelId, infoTemp.getId().toString()));
            List<ChannelRiskInfoTemp> riskInfoTempList = new ArrayList<>();
            if (channelRiskInfoTempList != null && channelRiskInfoTempList.size() > 0) {
                channelRiskInfoTempList.forEach(risk -> {
                    risk.setId(null);
                    riskInfoTempList.add(risk);
                });
                //返回风控数据
                vo.setRisk(riskInfoTempList);
            }
            //查询渠道保证金信息
            List<ChannelQuotaInfoTemp> channelQuotaInfoTempList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfoTemp>query().lambda().eq(ChannelQuotaInfoTemp::getChannelId, infoTemp.getId().toString()));
            List<ChannelQuotaInfoTemp> quotaInfoTempList = new ArrayList<>();
            if (channelQuotaInfoTempList != null && channelQuotaInfoTempList.size() > 0) {
                channelQuotaInfoTempList.forEach(quota -> {
                    quota.setId(null);
                    quotaInfoTempList.add(quota);
                });
                //返回渠道保证金信息
                vo.setQuota(quotaInfoTempList);
            }

            //查询渠道授权区域信息
            List<ChannelAuthorizeRegionTemp> channelAuthorizeRegionTempList = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId, temp.getId().toString()));
            List<ChannelAuthorizeRegionTemp> authorizeRegionTempList = new ArrayList<>();
            if (channelAuthorizeRegionTempList != null && channelAuthorizeRegionTempList.size() > 0) {
                channelAuthorizeRegionTempList.forEach(authorize -> {
                    authorize.setId(null);
                    authorizeRegionTempList.add(authorize);
                });
                //返回授权区域信息
                vo.setRegion(authorizeRegionTempList);
            }

            List<ChannelReceivablesAccountTemp> accountList = new ArrayList<>();
            List<ChannelReceivablesAccountTemp> accountTempList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda().eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString()));
            if (accountTempList != null && accountTempList.size() > 0) {
                //修改收款账号状态为启用
                for (ChannelReceivablesAccountTemp accountTemp : accountTempList) {

                    accountTemp.setStatus(ChannelOnlineConstants.ACCOUNT_STATUS_ZERO);
                    channelAccountInfoService.updateById(accountTemp);
                    accountTemp.setId(null);
                    accountList.add(accountTemp);
                }
                //返回收款账号信息
                vo.setAcount(accountList);
            }

            //查询授权车型
            List<ChannelAuthorizeVehicleTemp> vehicleTempList = channelAuthorizeVehicleTempService.list(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda().eq(ChannelAuthorizeVehicleTemp::getChannelId, temp.getId().toString()));
            List<ChannelAuthorizeVehicleTemp> authorizeVehicleList = new ArrayList<>();
            if (vehicleTempList != null && vehicleTempList.size() > 0) {
                vehicleTempList.forEach(authorize -> {
                    authorize.setId(null);
                    authorizeVehicleList.add(authorize);
                });
                //返回授权车型
                vo.setVehicle(vehicleTempList);
            }
            channelOnlineService.synchronousDataToCase(vo);



            // 传给进件端合作商车型数据
            ChannelAehicle aehicle= new ChannelAehicle();
            aehicle.setVehicle(vo.getVehicle());
            aehicle.setBusinessType(vo.getBusinessType());
            // 同步车型库数据到进件
            AfsTransEntity<ChannelAehicle> transEntity = new AfsTransEntity<>();
            transEntity.setTransCode(MqTransCode.CHANNEL_CHANNEL_APPLY_POS_SEND_CHANNEL_VEHICLE_APPLY);
            transEntity.setData(aehicle);
            senderChannelToApply.sendVehicle(transEntity);

            //Step 3：优先同步授权区域信息
            List<AddrQueryDto> paramList = service.getAllLocationInfo();
            List<AddrQueryDto> paramOnlyCountryList = paramList.stream().filter(dto -> ParamConstants.ADDRESS_FIRST_LEVEL.equals(dto.getAddrLevel())).toList();
            ChannelRiskInfoTemp channelRiskNew = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, infoTemp.getId().toString())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
            ChannelRiskInfoTemp channelRiskOld = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, infoTemp.getId().toString())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
            List<ChannelAuthorizeRegionTemp> regionTempList = new ArrayList<>();
            if (channelRiskNew != null) {
                if (ChannelOnlineConstants.TYPE_NO.equals(channelRiskNew.getAuthRegionSwitch())) {
                    if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isNewCarRole)) {
                        for (int i = 0; i < paramOnlyCountryList.size(); i++) {
                            AddrQueryDto param = paramOnlyCountryList.get(i);
                            ChannelAuthorizeRegionTemp tegionTemp = new ChannelAuthorizeRegionTemp();
                            tegionTemp.setCode(param.getValue());
                            tegionTemp.setIsParent(param.getIsParent() + "");
                            tegionTemp.setTitle(param.getLabel());
                            tegionTemp.setChannelId(infoTemp.getId());
                            tegionTemp.setParentId(param.getUpperCode());
                            tegionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_NEW_CAR);
                            regionTempList.add(tegionTemp);
                        }
                    }
                }
            }

            if (channelRiskOld != null) {
                if (ChannelOnlineConstants.TYPE_NO.equals(channelRiskOld.getAuthRegionSwitch())) {
                    if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isOldCarRole)) {
                        for (int i = 0; i < paramList.size(); i++) {
                            AddrQueryDto param = paramList.get(i);
                            ChannelAuthorizeRegionTemp tegionTemp = new ChannelAuthorizeRegionTemp();
                            tegionTemp.setCode(param.getValue());
                            tegionTemp.setIsParent(param.getIsParent() + "");
                            tegionTemp.setTitle(param.getLabel());
                            tegionTemp.setChannelId(infoTemp.getId());
                            tegionTemp.setParentId(param.getUpperCode());
                            tegionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_OLD_CAR);
                            regionTempList.add(tegionTemp);
                        }
                    }
                }
            }

            ChannelAuthRegionCondition channelAuthRegionCondition = new ChannelAuthRegionCondition();
            if (channelAuthorizeRegionTempList != null && channelAuthorizeRegionTempList.size() > 0) {
                channelAuthorizeRegionTempList.forEach(authorizeRegion -> {
                    if (ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR.equals(authorizeRegion.getBusinessType())) {
                        authorizeRegion.setBusinessType(ChannelOnlineConstants.BUSINESS_NEW_CAR);
                    } else if (ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR.equals(authorizeRegion.getBusinessType())) {
                        authorizeRegion.setBusinessType(ChannelOnlineConstants.BUSINESS_OLD_CAR);
                    }
                    regionTempList.add(authorizeRegion);
                });
            }
            channelAuthRegionCondition.setRegions(regionTempList);
            channelAuthRegionCondition.setType(ChannelOnlineConstants.AUTHORIZE_TYPE_ADD);
            channelOnlineService.synchronousAuthRegion(channelAuthRegionCondition);
            // 需要把车型库数据存


            //Step 4:创建账号
            List<ChannelRiskInfoTemp> newRisk = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, infoTemp.getId().toString())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
            List<ChannelRiskInfoTemp> oldRisk = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, infoTemp.getId().toString())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
            ChannelBasicTempCondition dto = new ChannelBasicTempCondition();
            dto.setChannelId(infoTemp.getId());
            dto.setChannelAdmin(temp.getChannelAdmin());
            dto.setChannelCode(infoTemp.getChannelCode());
            dto.setChannelFullName(infoTemp.getChannelFullName());
            dto.setChannelType(infoTemp.getChannelType());
            dto.setHierarchy(infoTemp.getHierarchy());
            dto.setIdentityNumber(infoTemp.getChannelAdminIdCard());
            dto.setEmail(infoTemp.getChannelAdminMail());
            dto.setPhone(infoTemp.getChannelAdminTel());
            dto.setChannelBelong(infoTemp.getChannelBelong());
            dto.setChoiceCardealerSwitch(infoTemp.getChoiceCardealerSwitch());
            if (newRisk != null && newRisk.size() > 0) {
                dto.setNewCarUserHierarchy(newRisk.get(0).getAccountMaxNum());
            }
            if (oldRisk != null && oldRisk.size() > 0) {
                dto.setOldCarUserHierarchy(oldRisk.get(0).getAccountMaxNum());
            }
            channelOnlineService.createByParentId(dto);
        }

        //Step 5：“保证金缴纳记录”记录到档案中；将此次评级结果，记录到档案中；将“上线时间”记录到档案中
        List<ChannelArchivesInfo> channelArchivesInfoList = channelArchivesInfoService.list(Wrappers.<ChannelArchivesInfo>query().lambda()
                .eq(ChannelArchivesInfo::getChannelId, temp.getId().toString()));
        if (channelArchivesInfoList != null && channelArchivesInfoList.size() > 0) {
            for (ChannelArchivesInfo archivesInfo : channelArchivesInfoList) {
                channelArchivesInfoService.removeById(archivesInfo.getId());
            }
        }
        //判断当前登陆者是否拥有新车权限，由前端传入
        List<ChannelArchivesInfo> archivesInfoList = channelOnlineCondition.getChannelArchivesInfoList();
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isNewCarRole)) {
            if (archivesInfoList != null && archivesInfoList.size() > 0) {
                channelArchivesInfoService.saveOrUpdateBatch(archivesInfoList);
            }
        }
        //判断当前登陆者是否拥有二手车权限，由前端传入
        List<ChannelArchivesInfo> archivesInfoOldList = channelOnlineCondition.getChannelArchivesInfoOldList();
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isOldCarRole)) {
            if (archivesInfoOldList != null && archivesInfoOldList.size() > 0) {
                channelArchivesInfoService.saveOrUpdateBatch(archivesInfoOldList);
            }
        }

        return IResponse.success("提交成功");
    }

    @RequestMapping(value = "/channelSubmitWorkflow", method = RequestMethod.POST)
    @ApiOperation(value = "提交上线流程")
    @SysLog("提交上线流程")
    @Transactional(rollbackFor = Exception.class)
    public IResponse channelSubmitWorkflow(@RequestBody ChannelOnlineCondition channelOnlineCondition) {

        IResponse iResponse = applyFeignService.getUserRealNameByUsername(channelOnlineCondition.getChannelBaseInfoTemp().getChannelCode());
        Assert.isTrue(ObjectUtil.isEmpty(iResponse.getData()),"经销商代码被业务账号占用");

        // 声明一个不可以修改数据的状态集合
        List<String> statusList =new ArrayList<>();
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);

        //判断是新提交流程还是退回流程
        Boolean taskFlag = false;

        List list = new ArrayList<>();
        //渠道基本信息
        ChannelBaseInfoTemp temp = channelOnlineCondition.getChannelBaseInfoTemp();
        if(temp.getChannelAdmin()==""){
            temp.setChannelAdmin(null);
        }
        if(temp.getChannelAdminAddress()==""){
            temp.setChannelAdminAddress(null);
        }
        if(temp.getChannelAdminIdCard()==""){
            temp.setChannelAdminIdCard(null);
        }
        if(temp.getChannelAdminTel()==""){
            temp.setChannelAdminTel(null);
        }
        if(temp.getChannelAdminMail()==""){
            temp.setChannelAdminMail(null);
        }
        if(temp.getChannelProvince()==""){
            temp.setChannelProvince(null);
        }
        if(temp.getChannelAdminCity()==""){
            temp.setChannelAdminCity(null);
        }
        if(temp.getChannelAddress()==""){
            temp.setChannelAddress(null);
        }
        if(temp.getLegalPerson()==""){
            temp.setLegalPerson(null);
        }
        if(temp.getLegalPersonIdCard()==""){
            temp.setLegalPersonIdCard(null);
        }
        if(temp.getLegalPersonTel()==""){
            temp.setLegalPersonTel(null);
        }
        if(temp.getActualController()==""){
            temp.setActualController(null);
        }
        if(temp.getActualControllerIdCard()==""){
            temp.setActualControllerIdCard(null);
        }
        if(temp.getActualControllerTel()==""){
            temp.setActualControllerTel(null);
        }
        if(temp.getChannelProvince()==""){
            temp.setChannelProvince(null);
        }
        if(temp.getChannelCity()==""){
            temp.setChannelCity(null);
        }
        if(temp.getChannelAddress()==""){
            temp.setChannelAddress(null);
        }
        if(temp.getLongitude()==""){
            temp.setLongitude(null);
        }
        if(temp.getOfficeProvince()==""){
            temp.setOfficeProvince(null);
        }
        if(temp.getOfficeCity()==""){
            temp.setOfficeCity(null);
        }
        if(temp.getOfficeAddress()==""){
            temp.setOfficeAddress(null);
        }
        if(temp.getLongitudeLatitudeRange()==""){
            temp.setLongitudeLatitudeRange(null);
        }
        if(temp.getAccountInfo()==""){
            temp.setAccountInfo(null);
        }
        if(temp.getCarDealType()==""){
            temp.setCarDealType(null);
        }
        if(temp.getBusinessArea()==""){
            temp.setBusinessArea(null);
        }
        if(temp.getPaymentObject()==""){
            temp.setPaymentObject(null);
        }
        if(temp.getGpsVendorAuthor()==""){
            temp.setGpsVendorAuthor(null);
        }
        if(temp.getGpsInstalMode()==""){
            temp.setGpsInstalMode(null);
        }
        if(temp.getTaxpayerType()==""){
            temp.setTaxpayerType(null);
        }
        if(temp.getTaxpayerIdNumber()==""){
            temp.setTaxpayerIdNumber(null);
        }
        if(temp.getInvoiceTelNumber()==""){
            temp.setInvoiceTelNumber(null);
        }
        if(temp.getInvoiceAddress()==""){
            temp.setInvoiceAddress(null);
        }
        if(temp.getOpeningBank()==""){
            temp.setOpeningBank(null);
        }
        if(temp.getBankAccount()==""){
            temp.setBankAccount(null);
        }
        if(temp.getLongitudeLatitudeSwitch()==""){
            temp.setLongitudeLatitudeSwitch(null);
        }
        if(temp.getPersonRelCardealerSwitch()==""){
            temp.setPersonRelCardealerSwitch(null);
        }
        if(temp.getChoiceCardealerSwitch()==""){
            temp.setChoiceCardealerSwitch(null);
        }
        if(temp.getLatitude()==""){
            temp.setLatitude(null);
        }
        if(temp.getLongitudeLatitudeSwitchOld()==""){
            temp.setLongitudeLatitudeSwitchOld(null);
        }
        if(temp.getPersonRelCardealerSwitchOld()==""){
            temp.setPersonRelCardealerSwitchOld(null);
        }
        if(temp.getChoiceCardealerSwitchOld()==""){
            temp.setChoiceCardealerSwitchOld(null);
        }

        String socUniCrtCode = temp.getSocUniCrtCode();
        if (StrUtil.isBlank(socUniCrtCode)){
            throw new AfsBaseException("统一社会信用代码不能为空");
        }
        socUniCrtCode = socUniCrtCode.toUpperCase();
        if (Arrays.stream(ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS).anyMatch(socUniCrtCode::contains)) {
            String joinedChars = String.join("、", ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS);
            return IResponse.fail(MessageFormat.format("“{0}”不能用于编码，请检查后重新录入", joinedChars));
        }

        // 声明一个存放老合作商数据信息的地方
        ChannelBaseInfoTemp oldChannelTemp =new ChannelBaseInfoTemp();
        if(temp!=null&&temp.getId()!=null){
            oldChannelTemp=channelOnlineService.getById(temp.getId());
            if(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE.equals(oldChannelTemp.getChannelStatus()) && ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE.equals(oldChannelTemp.getChannelStatusOldCar()) ){
                taskFlag = true;
            }
            if(statusList.contains(oldChannelTemp.getChannelStatus())&&statusList.contains(oldChannelTemp.getChannelStatusOldCar())){
                return IResponse.fail("当前合作商状态不允许修改，请核实!");
            }
        }else {
            // 根据合作商简称判断该合作商是否唯一
            ChannelBaseInfoTemp checkTemp = channelOnlineService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda()
                    .eq(ChannelBaseInfoTemp::getChannelFullName, temp.getChannelFullName()));
            if (checkTemp != null) {
                if(!temp.getChannelBelong().equals(checkTemp.getChannelBelong())){
                    throw new AfsBaseException("合作商名称一致，但渠道归属不一致");
                }
                if (statusList.contains(checkTemp.getChannelStatus()) && statusList.contains(checkTemp.getChannelStatusOldCar())) {
                    return IResponse.fail("当前合作商状态不允许修改，请核实!");
                }else {
                    // 说明这个合作商之前上线一家了。一条线或者上线了二条线但是存在可以修改的状态
                    if(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE.equals(temp.getBusinessType())||ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_TWO.equals(temp.getBusinessType())){
                        // 说明进来的是新车二手车都有，那么我现在就不用考虑数据问题了
                        // 赋值id
                        temp.setId(checkTemp.getId());
                        temp.setChannelStatus(checkTemp.getChannelStatus());
                        temp.setChannelStatusOldCar(checkTemp.getChannelStatusOldCar());
                        // 这个地方再去校验这个人是单一类型管理
                        if(ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole())&&(!ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsOldCarRole()))){
                            // 说明这个人只有新车权限，那么需要去处理新车的当前状态
                            if(statusList.contains(checkTemp.getChannelStatus())){
                                return IResponse.fail("当前合作商新车状态不允许修改，请核实!");
                            }
                        }
                        // 说明这个人只有二手车权限。那么需要去处理二手车当前状态
                        if(ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsOldCarRole())&&(!ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole()))){
                            // 说明这个人只有新车权限，那么需要去处理新车的当前状态
                            if(statusList.contains(checkTemp.getChannelStatusOldCar())){
                                return IResponse.fail("当前合作商二手车状态不允许修改，请核实!");
                            }
                        }
                    }else if(RoleTypeDic.NEW_CAR.equals(temp.getBusinessType())){
                        // 说明是 只有新车。这种情况要先看新车的
                        if(statusList.contains(checkTemp.getChannelStatus())){
                            // 说明选择的只有新车。并且新车状态不可以修改
                            return IResponse.fail("当前合作商新车状态不允许修改，请核实!");
                        }else {
                            if(RoleTypeDic.OLD_CAR.equals(checkTemp.getBusinessType())){
                                // 说明之前只有二手车，那么就要变成新车二手车都有
                                temp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE);
                            }
                            // 赋值id
                            temp.setId(checkTemp.getId());
                            temp.setChannelStatus(checkTemp.getChannelStatus());
                            temp.setChannelStatusOldCar(checkTemp.getChannelStatusOldCar());
                        }
                    }else if(RoleTypeDic.OLD_CAR.equals(temp.getBusinessType())){
                        // 说明只有二手车。这个情况先看二手车状态
                        if(statusList.contains(checkTemp.getChannelStatusOldCar())){
                            return IResponse.fail("当前合作商二手车状态不允许修改，请核实!");
                        }else {
                            if(RoleTypeDic.NEW_CAR.equals(checkTemp.getBusinessType())){
                                // 说明之前只有新车，那么就要变成新车二手车都有
                                temp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE);
                            }
                            // 赋值id
                            temp.setId(checkTemp.getId());
                            temp.setChannelStatus(checkTemp.getChannelStatus());
                            temp.setChannelStatusOldCar(checkTemp.getChannelStatusOldCar());
                        }

                    }
                }
                // 处理合作商的业务类型
                if(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE.equals(checkTemp.getBusinessType())||ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_TWO.equals(checkTemp.getBusinessType())){
                    temp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE);
                }else if(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE.equals(temp.getBusinessType())&&ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_TWO.equals(checkTemp.getBusinessType())){
                    // 说我自己是新车。之前的是二手车
                    temp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE);
                }else if(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_TWO.equals(temp.getBusinessType())&&ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE.equals(checkTemp.getBusinessType())){
                    // 我自己是二手车。之前是新车
                    temp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE);
                }
            }
        }

        //管理员所在省市
        String[] channelAdminAddressValue = channelOnlineCondition.getChannelAdminAddressValue();
        //注册地所在省市
        String[] channelAddressValue = channelOnlineCondition.getChannelAddressValue();
        //办公所在省市
        String[] officeAddressValue = channelOnlineCondition.getOfficeAddressValue();
        //新车授权区域
        String[] mainArea = channelOnlineCondition.getMainArea();
        //二手车授权区域
        String[] mainAreaOld = channelOnlineCondition.getMainAreaOld();
        //主营品牌
        String[] mainBrand = channelOnlineCondition.getMainBrand();
        //授权车型
        String[] mainCar = channelOnlineCondition.getMainCar();
        //二手车授权车型
        String[] mainCarOld = channelOnlineCondition.getMainCarOld();
        //判断管理员地址是否为空
        if (channelAdminAddressValue != null && channelAdminAddressValue.length > 0) {
            temp.setChannelAdminProvince(channelAdminAddressValue[0]);
            temp.setChannelAdminCity(channelAdminAddressValue[1]);
        }
        //判断注册省份是否为空
        if (channelAddressValue != null && channelAddressValue.length > 0) {
            temp.setChannelProvince(channelAddressValue[0]);
            temp.setChannelCity(channelAddressValue[1]);
        }
        //判断办公省份是否为空
        if (officeAddressValue != null && officeAddressValue.length > 0) {
            temp.setOfficeProvince(officeAddressValue[0]);
            temp.setOfficeCity(officeAddressValue[1]);
        }
        if (StrUtil.isBlank(temp.getChannelCode())) {
            //渠道代码为空则按照规则从新拼接
            temp.setChannelCode(channelOnlineService.channelCodeSequence(ChannelOnlineConstants.VALUE_CHANNEL_CODE, ChannelOnlineConstants.VALUE_CHANNEL_CODE));
        }
        // 处理状态数据状态
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole())) {
            // 说明这个人有新车权限。接下来要看 当前的状态
            if(temp.getChannelStatus()==null||temp.getChannelStatus()==""||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO.equals(temp.getChannelStatus())||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE.equals(temp.getChannelStatus())||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_FOUR.equals(temp.getChannelStatus())){
                if(temp.getBusinessType().contains(RoleTypeDic.NEW_CAR)){
                    temp.setChannelStatus(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
                }
            }
        }
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsOldCarRole())) {
            if(temp.getChannelStatusOldCar()==null||temp.getChannelStatusOldCar()==""||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO.equals(temp.getChannelStatusOldCar())||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE.equals(temp.getChannelStatusOldCar())||ChannelOnlineConstants.VALUE_CHANNEL_STATUS_FOUR.equals(temp.getChannelStatusOldCar())){
                if(temp.getBusinessType().contains(RoleTypeDic.OLD_CAR)){
                    temp.setChannelStatusOldCar(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
                }
            }
        }
        if(temp.getSubmitOrderAuth() == null){
            temp.setSubmitOrderAuth("1");
        }
        if(temp.getLoanAuth() == null){
            temp.setLoanAuth("1");
        }
        //基本信息基本信息
        channelOnlineService.saveOrUpdate(temp);

        /** 保存渠道主营品牌信息前先清除旧的数据*/
        channelMainBrandService.remove(Wrappers.<ChannelMainBrand>query().lambda().eq(ChannelMainBrand::getChannelId, temp.getId()));

        //保存渠道主营品牌
        List<ChannelMainBrand> channelMainBrandList = new ArrayList<>();
        if (mainBrand != null && mainBrand.length > 0) {
            for (String main : mainBrand) {
                ChannelMainBrand channelMainBrand = new ChannelMainBrand();
                String code = main.split(",")[0];
                String title = main.split(",")[1];
                String indeterminate = main.split(",")[2];
                String carLevel = main.split(",")[3];
                String spellCode = main.split(",",-1)[4];
                channelMainBrand.setChannelId(temp.getId());
                channelMainBrand.setCode(code);
                channelMainBrand.setTitle(title);
                channelMainBrand.setChannelBelong(temp.getChannelBelong());
                if (StrUtil.isNotBlank(indeterminate)) {
                    channelMainBrand.setIndeterminate(indeterminate);
                } else {
                    channelMainBrand.setIndeterminate("false");
                }
                if (StrUtil.isNotBlank(spellCode)) {
                    channelMainBrand.setSpellCode(spellCode);
                }
                channelMainBrand.setCarLevel(carLevel);
                channelMainBrandList.add(channelMainBrand);
            }
            int size = channelMainBrandList.size();
            if (size <= 1000) {
                channelMainBrandService.saveBatch(channelMainBrandList);
            } else {
                for (int i = 0; i < size / 1000; i++) {
                    if (size / 1000 - i > 1) {
                        channelMainBrandService.saveBatch(channelMainBrandList.subList(1000 * i, 1000 * i + 999));
                    } else {
                        channelMainBrandService.saveBatch(channelMainBrandList.subList(1000 * i, size - 1));
                    }
                }
            }
        }

        //判断当前登陆者是否拥有新车权限，由前端传入
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole())&&(!statusList.contains(oldChannelTemp.getChannelStatus()))) {
            if (ChannelOnlineConstants.PAYMENT_OBJECT_ZERO.equals(temp.getPaymentObject())) {
                List<ChannelReceivablesAccountTemp> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                        .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                        .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_ONE)
                        .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
                Assert.isTrue(accountList != null && accountList.size() != 0, "当前渠道汇款对象为主体，请录入全款收款账号信息！");

                //工程机械校验.新车全款 copy二手车全款
                if(Constants.ENGINEERING.equals(temp.getChannelBelong())){
                    List<ChannelReceivablesAccountTemp> accountListGcjx = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_ONE)
                            .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR)
                            .or()
                            .eq(ChannelReceivablesAccountTemp::getStatus, Constants.ENABLED)
                            .eq(ChannelReceivablesAccountTemp::getStatus, Constants.REVIEW)
                            .eq(ChannelReceivablesAccountTemp::getStatus, Constants.DRAFT));
                    log.info("accountListGcjxSize={}",accountListGcjx==null?0:accountListGcjx.size());
                    if(CollUtil.isEmpty(accountListGcjx)
                            || StringUtils.isEmpty(accountListGcjx.get(0).getAccountAttribute())){
                        ChannelReceivablesAccountTemp  oldCarChannelReceivablesAccountTempCopy = accountList.get(0);
                        oldCarChannelReceivablesAccountTempCopy.setId(null);
                        oldCarChannelReceivablesAccountTempCopy.setAccountAttribute(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                        oldCarChannelReceivablesAccountTempCopy.setStatus(Constants.REVIEW);
                        //save 二手车copy
                        channelAccountInfoService.save(oldCarChannelReceivablesAccountTempCopy);
                        log.info("copyOldCarId={}",oldCarChannelReceivablesAccountTempCopy.getId());
                    }
                }else{
                    List<ChannelReceivablesAccountTemp> accountTempList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                            .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId())
                            .eq(ChannelReceivablesAccountTemp::getCollectionType, ChannelOnlineConstants.COLLECTION_TYPE_TWO)
                            .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
                    Assert.isTrue(accountTempList != null && accountTempList.size() != 0, "当前渠道汇款对象为主体，请录入佣金收款账号信息！");
                }
                // 判断收款类型+账户类型是否重复
				checkCollectAccountTypeDuplicatesFlag(temp.getId());

                //合作商上线审批拒绝后仍可编辑后重新提交，被拒合作商再次提交审批通过后需将收款账号由拒绝更改为启用
                List<ChannelReceivablesAccountTemp> oldAccountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                        .eq(ChannelReceivablesAccountTemp::getChannelId, temp.getId().toString())
                        .in(ChannelReceivablesAccountTemp::getStatus, ChannelOnlineConstants.ACCOUNT_STATUS_TWO,ChannelOnlineConstants.ACCOUNT_STATUS_FOUR)
                        .eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
                if(oldAccountList.size()>0){
                    log.info("历史收款账号{}",oldAccountList);
                    for (ChannelReceivablesAccountTemp accountTemp:oldAccountList){
                        accountTemp.setStatus(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
                    }
                    channelAccountInfoService.updateBatchById(oldAccountList);
                }
            }
            channelRiskInfoService.remove(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, temp.getId().toString())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            channelQuotaInfoService.remove(Wrappers.<ChannelQuotaInfoTemp>query().lambda()
                    .eq(ChannelQuotaInfoTemp::getChannelId, temp.getId().toString())
                    .eq(ChannelQuotaInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            channelAuthorizeRegionService.remove(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                    .eq(ChannelAuthorizeRegionTemp::getChannelId, temp.getId().toString())
                    .eq(ChannelAuthorizeRegionTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            channelAuthorizeVehicleTempService.remove(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda()
                    .eq(ChannelAuthorizeVehicleTemp::getChannelId, temp.getId().toString())
                    .eq(ChannelAuthorizeVehicleTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车风控信息*/
            ChannelRiskInfoTemp channelRiskInfoTemp = channelOnlineCondition.getChannelRiskInfoTemp();
            //渠道id赋值
            channelRiskInfoTemp.setChannelId(temp.getId());
            channelRiskInfoService.saveOrUpdate(channelRiskInfoTemp);
            list.add(channelRiskInfoTemp);

            //保存新车保证金信息
            List<ChannelQuotaInfoTemp> channelQuotaInfoList = channelOnlineCondition.getChannelQuotaInfoTempList();
            for (ChannelQuotaInfoTemp quotaInfo : channelQuotaInfoList) {
                quotaInfo.setChannelId(temp.getId());
                //临时额度
                BigDecimal tempQuota = quotaInfo.getTempQuota() == null ? BigDecimal.ZERO : quotaInfo.getTempQuota();
                //签放额度
                BigDecimal quotaSigning = quotaInfo.getQuotaAmount() == null ? BigDecimal.ZERO : quotaInfo.getQuotaAmount();
                //剩余额度 = 临时额度+签放额度
                quotaInfo.setSurplusQuota(tempQuota.add(quotaSigning));
                //占用额度为0
                quotaInfo.setOccupiedQuota(BigDecimal.ZERO);
                channelQuotaInfoService.saveOrUpdate(quotaInfo);
            }

            //保存渠道新车授权区域
            List<ChannelAuthorizeRegionTemp> channelAuthorizeRegionTempList = new ArrayList<>();
            if("0".equals(channelOnlineCondition.getChannelRiskInfoTemp().getAuthRegionSwitch())){
                // 说明选的是否
                ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                regionTemp.setChannelId(temp.getId());
                regionTemp.setCode("1");
                regionTemp.setTitle("全国区域");
                regionTemp.setIsParent("1");
                regionTemp.setParentId("0");
                regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                regionTemp.setIndeterminate("false");
                channelAuthorizeRegionService.save(regionTemp);
            }else {
                if (mainArea != null && mainArea.length > 0) {
                    if("false".equals(mainArea[0].split(",",-1)[4].toString())){
                        ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                        regionTemp.setChannelId(temp.getId());
                        regionTemp.setCode("1");
                        regionTemp.setTitle("全国区域");
                        regionTemp.setIsParent("1");
                        regionTemp.setParentId("0");
                        regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                        regionTemp.setIndeterminate("false");
                        channelAuthorizeRegionService.save(regionTemp);
                    }else {
                        for (String area : mainArea) {
                            ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                            String code = area.split(",")[0];
                            String title = area.split(",")[1];
                            String isParent = area.split(",")[2];
                            String parentId = area.split(",")[3];
                            regionTemp.setChannelId(temp.getId());
                            regionTemp.setCode(code);
                            // 单独处理 全国区域会出现 undefined
                            if("全国区域".equals(title)){
                                regionTemp.setIsParent("1");
                                regionTemp.setParentId("0");
                            }else {
                                regionTemp.setIsParent(isParent);
                                regionTemp.setParentId(parentId);
                            }
                            regionTemp.setTitle(title);

                            regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeRegionTempList.add(regionTemp);
                        }
                        int size = channelAuthorizeRegionTempList.size();
                        if (size <= 1000) {
                            channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

            //保存渠道新车授权车型
            // 先看看授权，选的是还是否
            List<ChannelAuthorizeVehicleTemp> channelAuthorizeVehicleList = new ArrayList<>();
            if("0".equals(channelOnlineCondition.getChannelRiskInfoTemp().getAuthVehicleTypeSwitch())){
                ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                channelAuthorizeVehicleTemp.setCode("00");
                channelAuthorizeVehicleTemp.setParentId("0000");
                channelAuthorizeVehicleTemp.setCarLevel("0");
                channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                channelAuthorizeVehicleTemp.setIndeterminate("false");
                channelAuthorizeVehicleTemp.setIsActive("1");
                channelAuthorizeVehicleTemp.setOnlyId("0");
                channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
            }else {
                // 说明选择了
                if (mainCar != null && mainCar.length > 0) {
                    if("false".equals(mainCar[0].split(",",-1)[3].toString())){
                        ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                        channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                        channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                        channelAuthorizeVehicleTemp.setTitle("品牌名称");
                        channelAuthorizeVehicleTemp.setCode("00");
                        channelAuthorizeVehicleTemp.setParentId("0000");
                        channelAuthorizeVehicleTemp.setCarLevel("0");
                        channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                        channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                        channelAuthorizeVehicleTemp.setIndeterminate("false");
                        channelAuthorizeVehicleTemp.setIsActive("1");
                        channelAuthorizeVehicleTemp.setOnlyId("0");
                        channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
                    }else {
                        for (String main : mainCar) {
                            ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                            String code = main.split(",")[0];
                            String title = main.split(",")[1];
                            String parentId = main.split(",")[2];
                            String indeterminate = main.split(",")[3];
                            String carLevel = main.split(",")[4];
                            String spellCode = main.split(",")[5];
                            String carType = main.split(",")[6];
                            String isActive = main.split(",")[7];
                            String onlyId = main.split(",")[8];
                            channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                            if("品牌名称".equals(title)){
                                channelAuthorizeVehicleTemp.setCode("00");
                                channelAuthorizeVehicleTemp.setTitle(title);
                                channelAuthorizeVehicleTemp.setParentId("00");
                            }else {
                                channelAuthorizeVehicleTemp.setCode(code);
                                channelAuthorizeVehicleTemp.setTitle(title);
                                channelAuthorizeVehicleTemp.setParentId(parentId);
                            }

                            channelAuthorizeVehicleTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeVehicleTemp.setCarLevel(carLevel);
                            if("null".equals(carType)){
                                channelAuthorizeVehicleTemp.setCarType("");
                            }else{
                                channelAuthorizeVehicleTemp.setCarType(carType);
                            }
                            if (StrUtil.isNotBlank(indeterminate)) {
                                channelAuthorizeVehicleTemp.setIndeterminate(indeterminate);
                            } else {
                                channelAuthorizeVehicleTemp.setIndeterminate("false");
                            }
                            if (StrUtil.isNotBlank(spellCode)) {
                                channelAuthorizeVehicleTemp.setSpellCode(spellCode);
                            }
                            channelAuthorizeVehicleTemp.setIsActive(isActive);
                            channelAuthorizeVehicleTemp.setOnlyId(onlyId);
                            channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
                        }
                        int size = channelAuthorizeVehicleList.size();
                        if (size <= 1000) {
                            channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

        }

        //发起审批流程
        String isNewCarRole = channelOnlineCondition.getIsNewCarRole();
        String isOldCarRole = channelOnlineCondition.getIsOldCarRole();
        // 处理收款账户状态问题
        if(ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isNewCarRole)){

        }

        //判断拥有新车角色权限、且(渠道从未发起过新车审批流程)
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isNewCarRole)&&(!statusList.contains(oldChannelTemp.getChannelStatus()))) {
            log.info("============准备发起新车审批流合作商"+oldChannelTemp.getId());
            String dataId =String.valueOf(uidGenerator.getUID());
            if(StringUtils.isNotEmpty(temp.getNewDataId())){
                List<String> status =new ArrayList<>();
                status.add(FlowStatusEnum.CREATE.getCode());
                status.add(FlowStatusEnum.ACTIVE.getCode());
                status.add(FlowStatusEnum.SUSPEND.getCode());
              List<WorkflowTaskInfo> taskInfoList =  workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                      .eq(WorkflowTaskInfo::getBusinessNo,temp.getNewDataId())
                      .in(WorkflowTaskInfo::getStatus,status));
              if(taskInfoList.size()>0){
                    throw new AfsBaseException("合作商已处于流程中,请稍后再试!");
              }
            }else {
                temp.setNewDataId(dataId);
            }
            if(StringUtils.isEmpty(temp.getNewDataId())){
                throw new AfsBaseException("未生成流程编号，请联系管理员!");
            }

            channelOnlineService.updateById(temp);

            String key= MessageFormat.format("{0}.{1}","CHANNEL_MAINTAIN_SUBMIT",temp.getId());
            String lockValue=temp.getId().toString()+System.currentTimeMillis();
            Boolean res= stringRedisTemplate.opsForValue().setIfAbsent(key, lockValue, 3, TimeUnit.MINUTES);
            IResponse<ProcessInstanceDto> response=null;
            if(res) {
                try {

                    if(taskFlag){
                        List<WorkflowTaskInfo> lst = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                                .eq(WorkflowTaskInfo::getBusinessNo, temp.getNewDataId().toString()));
                        if(lst.size() > 0){
                            response = workflowHelper.resumeProcess(lst.get(0).getProcessInstanceId());
                            if(!CommonConstants.SUCCESS.equals(response.getCode())){
                                throw new AfsBaseException("合作商上线恢复流程异常!");
                            }
                        }else{
                            throw new AfsBaseException("合作商上线恢复流程异常!WorkflowTaskInfo没有数据！");
                        }
                    }else{
                        //发起流程
                        if (bydChannelOaProperties.getOaFlowSwitch()) {
                            channelOnlineService.createOaFlow(temp.getId());
                        }else {
                            //发起新车上线审批流程
                            StartFlowRequestBo startFlowRequestBo = new StartFlowRequestBo();
                            startFlowRequestBo.setBusinessNo(temp.getNewDataId());
                            //这两个ID为指定工作流的id，后面可以写在配置文件里面。
                            startFlowRequestBo.setPackageId(flowConfigProperties.getOnlinePackageId());
                            startFlowRequestBo.setTemplateId(flowConfigProperties.getOnlineTemplateId());
                            Date date = new Date();
                            startFlowRequestBo.setSubject("合作商上线:"+temp.getChannelFullName()+String.valueOf(date.getTime()));
                            // 植入业务参数
                            if (startFlowRequestBo.getParams() == null) {
                                startFlowRequestBo.setParams(new JSONObject());
                            }
                            startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_ID, temp.getId());
                            if(CommonConstants.COMMON_NO.equals(temp.getControlBusinessType())){
                                startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_TYPE, RoleTypeDic.ALL_CAR);
                            }else {
                                startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_TYPE, RoleTypeDic.NEW_CAR);
                            }
                            startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_USER, SecurityUtils.getUser().getUserRealName());
                            startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_OPINION, FlowTaskOperationEnum.CREATE.getDesc());
                            startFlowRequestBo.getParams().put(FlowConstant.TASK_NODE_NAME, "流程发起");
                            startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_TIME, String.valueOf(date.getTime()));
                            startFlowRequestBo.getParams().put(FlowConstant.CHANNEL_ONLINE_TYPE, channelOnlineCondition.getChannelRiskInfoTemp().getCarType());
                            startFlowRequestBo.getParams().put(FlowConstant.CHANNEL_BELONG,temp.getChannelBelong());
                            startFlowRequestBo.getParams().put(SalesManageFlowConstant.FLOW_SALES_USER_ID,channelOnlineCondition.getChannelRiskInfoTemp().getCustManageId()); //放入销售团队用户id
                            // 设置工作流需要的参数
                            response = workflowHelper.startFlow(startFlowRequestBo, UseSceneEnum.APPROVE);
                            if(!CommonConstants.SUCCESS.equals(response.getCode())){
                                throw new AfsBaseException("合作商上线流程异常!");
                            }
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    throw new AfsBaseException(e.getMessage());
                }finally {
                    String value = stringRedisTemplate.opsForValue ( ).get ( key );
                    if (lockValue.equals ( value )) {
                        stringRedisTemplate.delete ( key );
                    }
                }
            }else{
                throw new AfsBaseException ("当前正在处理，请勿重复操作!" );
            }



        }
        //判断拥有二手车角色权限、且(渠道从未发起过二手车审批流程)
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(isOldCarRole)&&(!statusList.contains(oldChannelTemp.getChannelStatusOldCar()))) {
            log.info("准备发起二手车审批流合作商"+oldChannelTemp.getId());
            //发起二手车上线审批流程
            WorkTaskDetail workTaskDetail = workTaskDetailService.getOne(Wrappers.<WorkTaskDetail>query().lambda()
                    .eq(WorkTaskDetail::getBusinessKey, temp.getId())
                    .eq(WorkTaskDetail::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
            log.info("准备发起二手车审批流合作商流程信息"+workTaskDetail);
        }

        return IResponse.success("提交成功");
    }

    /**
     * 判断收款类型+账户类型是否重复
     * @param channelId 渠道id
     */
	public void checkCollectAccountTypeDuplicatesFlag(Long channelId) {
		List<ChannelReceivablesAccountTemp> channelReceivablesAccountTemps = channelAccountInfoService
				.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
						.eq(ChannelReceivablesAccountTemp::getChannelId, channelId)
						.eq(ChannelReceivablesAccountTemp::getAccountAttribute, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR)
						.and(wrapper -> wrapper
								.in(ChannelReceivablesAccountTemp::getStatus
										, ChannelOnlineConstants.ACCOUNT_STATUS_TWO
										, ChannelOnlineConstants.ACCOUNT_STATUS_FIVE
										, ChannelOnlineConstants.ACCOUNT_STATUS_ZERO)));
		// 判断收款类型+账户类型是否重复
		boolean hasDuplicates = channelReceivablesAccountTemps.stream()
				.collect(Collectors.groupingBy(
						item -> Arrays.asList(item.getCollectionType(), item.getAccountType()),
						Collectors.counting()
				))
				.values().stream()
				.anyMatch(count -> count > 1);
		if (hasDuplicates) {
			throw new AfsBaseException("存在重复的收款类型或账户类型！");
		}
	}


    @RequestMapping(value = "/commitModifyUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "提交渠道维护修改信息")
    @SysLog("提交渠道维护修改信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse commitFormalOutLine(@RequestBody ChannelOnlineConditionUpd channelOnlineCondition) {

        log.info("================================入参：{}",JSON.toJSONString(channelOnlineCondition));
        List list = new ArrayList<>();

        //渠道基本信息
        ChannelBaseInfoTempUpd temp = channelOnlineCondition.getChannelBaseInfoTemp();
        List<String> salesNetworkLists = temp.getSalesNetworkLists();
        if (CollUtil.isNotEmpty(salesNetworkLists)) {
            temp.setSalesNetwork(String.join(",", salesNetworkLists));
        }
        if (ObjectUtil.isNotEmpty(temp.getId())) {
            // 校验字段唯一性
            checkFieldUnique(temp);
        }
        //管理员所在省市
        String[] channelAdminAddressValue = channelOnlineCondition.getChannelAdminAddressValue();
        //注册地所在省市
        String[] channelAddressValue = channelOnlineCondition.getChannelAddressValue();
        //办公所在省市
        String[] officeAddressValue = channelOnlineCondition.getOfficeAddressValue();
        //新车授权区域
        String[] mainArea = channelOnlineCondition.getMainArea();
        //二手车授权区域
        String[] mainAreaOld = channelOnlineCondition.getMainAreaOld();
        //授权车型
        String[] mainCar = channelOnlineCondition.getMainCar();
        //二手车授权车型
        String[] mainCarOld = channelOnlineCondition.getMainCarOld();


        if(CommonConstants.COMMON_NO.equals(temp.getControlBusinessType())){
            mainAreaOld = channelOnlineCondition.getMainArea();
            mainCarOld = channelOnlineCondition.getMainCar();
        }
        //判断管理员地址是否为空
        if (channelAdminAddressValue != null && channelAdminAddressValue.length > 0) {
            temp.setChannelAdminProvince(channelAdminAddressValue[0]);
            temp.setChannelAdminCity(channelAdminAddressValue[1]);
        }
        //判断注册省份是否为空
        if (channelAddressValue != null && channelAddressValue.length > 0) {
            temp.setChannelProvince(channelAddressValue[0]);
            temp.setChannelCity(channelAddressValue[1]);
        }
        //判断办公省份是否为空
        if (officeAddressValue != null && officeAddressValue.length > 0) {
            temp.setOfficeProvince(officeAddressValue[0]);
            temp.setOfficeCity(officeAddressValue[1]);
        }
        String socUniCrtCode = temp.getSocUniCrtCode();
        if (StrUtil.isBlank(socUniCrtCode)){
            throw new AfsBaseException("统一社会信用代码不能为空");
        }
        socUniCrtCode = socUniCrtCode.toUpperCase();
        if (Arrays.stream(ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS).anyMatch(socUniCrtCode::contains)) {
            String joinedChars = String.join("、", ApplyConstants.CHANNEL_SOC_CODE_NOTALLOW_CHARS);
            return IResponse.fail(MessageFormat.format("“{0}”不能用于编码，请检查后重新录入", joinedChars));
        }
        channelOnlineUpdService.saveOrUpdate(temp);
        list.add(temp);

        //判断当前登陆者是否拥有新车权限，由前端传入
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole())) {

            //保存渠道 新车风控信息前做清除
            channelRiskInfoUpdService.remove(Wrappers.<ChannelRiskInfoTempUpd>query().lambda()
                    .eq(ChannelRiskInfoTempUpd::getChannelId, temp.getId())
                    .eq(ChannelRiskInfoTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车保证金信息前先清除旧的数据*/
            channelQuotaInfoUpdService.remove(Wrappers.<ChannelQuotaInfoTempUpd>query().lambda()
                    .eq(ChannelQuotaInfoTempUpd::getChannelId, temp.getId())
                    .eq(ChannelQuotaInfoTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车授权区域信息前先清除旧的数据*/
            channelAuthorizeRegionUpdService.remove(Wrappers.<ChannelAuthorizeRegionTempUpd>query().lambda()
                    .eq(ChannelAuthorizeRegionTempUpd::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeRegionTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车授权车型信息前先清除旧的数据*/
            channelAuthorizeVehicleTempUpdService.remove(Wrappers.<ChannelAuthorizeVehicleTempUpd>query().lambda()
                    .eq(ChannelAuthorizeVehicleTempUpd::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeVehicleTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车风控信息*/
            ChannelRiskInfoTempUpd channelRiskInfoTemp = channelOnlineCondition.getChannelRiskInfoTemp();
            //渠道id赋值
            channelRiskInfoTemp.setChannelId(temp.getId());
            channelRiskInfoTemp.setChannelDepositPaidIn(channelRiskInfoTemp.getChannelDeposit());
            channelRiskInfoUpdService.saveOrUpdate(channelRiskInfoTemp);
            list.add(channelRiskInfoTemp);

            //保存新车保证金信息
            List<ChannelQuotaInfoTempUpd> channelQuotaInfoList = channelOnlineCondition.getChannelQuotaInfoTempList();

            for (ChannelQuotaInfoTempUpd quotaInfo : channelQuotaInfoList) {
                quotaInfo.setChannelId(temp.getId());
                //临时额度
                BigDecimal tempQuota = quotaInfo.getTempQuota() == null ? BigDecimal.ZERO : quotaInfo.getTempQuota();
                //签放额度
                BigDecimal quotaSigning = quotaInfo.getQuotaAmount() == null ? BigDecimal.ZERO : quotaInfo.getQuotaAmount();
                //剩余额度 = 临时额度+签放额度
                quotaInfo.setSurplusQuota(tempQuota.add(quotaSigning));
                //占用额度为0
                quotaInfo.setOccupiedQuota(BigDecimal.ZERO);
                channelQuotaInfoUpdService.saveOrUpdate(quotaInfo);
            }

            //保存渠道新车授权区域
            List<ChannelAuthorizeRegionTempUpd> channelAuthorizeRegionTempUpdList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthRegionSwitch())){
                // 说明选的是否
                ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                regionTempUpd.setChannelId(temp.getId());
                regionTempUpd.setCode("1");
                regionTempUpd.setTitle("全国区域");
                regionTempUpd.setIsParent("1");
                regionTempUpd.setParentId("0");
                regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                regionTempUpd.setIndeterminate("false");
                channelAuthorizeRegionUpdService.save(regionTempUpd);
            }else {
                if (mainArea != null && mainArea.length > 0) {
                    if("false".equals(mainArea[0].split(",",-1)[4].toString())){
                        ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                        regionTempUpd.setChannelId(temp.getId());
                        regionTempUpd.setCode("1");
                        regionTempUpd.setTitle("全国区域");
                        regionTempUpd.setIsParent("1");
                        regionTempUpd.setParentId("0");
                        regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                        regionTempUpd.setIndeterminate("false");
                        channelAuthorizeRegionUpdService.save(regionTempUpd);
                    }else {
                        for (String area : mainArea) {
                            ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                            String code = area.split(",")[0];
                            String title = area.split(",")[1];
                            String isParent = area.split(",")[2];
                            String parentId = area.split(",")[3];
                            regionTempUpd.setChannelId(temp.getId());
                            regionTempUpd.setCode(code);
                            // 单独处理 全国区域会出现 undefined
                            if("全国区域".equals(title)){
                                regionTempUpd.setIsParent("1");
                                regionTempUpd.setParentId("0");
                            }else {
                                regionTempUpd.setIsParent(isParent);
                                regionTempUpd.setParentId(parentId);
                            }
                            regionTempUpd.setTitle(title);

                            regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeRegionTempUpdList.add(regionTempUpd);
                            ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                            BeanUtils.copyProperties(regionTempUpd, regionTemp);
                        }
                        int size = channelAuthorizeRegionTempUpdList.size();
                        if (size <= 1000) {
                            channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

            //保存渠道新车授权车型
            List<ChannelAuthorizeVehicleTempUpd> channelAuthorizeVehicleUpdList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTempUpd();
                channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                channelAuthorizeVehicleTemp.setCode("00");
                channelAuthorizeVehicleTemp.setParentId("0000");
                channelAuthorizeVehicleTemp.setCarLevel("0");
                channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                channelAuthorizeVehicleTemp.setIndeterminate("false");
                channelAuthorizeVehicleTemp.setIsActive("1");
                channelAuthorizeVehicleTemp.setOnlyId("0");
                channelAuthorizeVehicleTempUpdService.save(channelAuthorizeVehicleTemp);

                channelAuthorizeVehicleUpdList.add(channelAuthorizeVehicleTemp);
            }else {
                if (mainCar != null && mainCar.length > 0) {
                    if("false".equals(mainCar[0].split(",",-1)[3].toString())){
                        ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTempUpd();
                        channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                        channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                        channelAuthorizeVehicleTemp.setTitle("品牌名称");
                        channelAuthorizeVehicleTemp.setCode("00");
                        channelAuthorizeVehicleTemp.setParentId("0000");
                        channelAuthorizeVehicleTemp.setCarLevel("0");
                        channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                        channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                        channelAuthorizeVehicleTemp.setIndeterminate("false");
                        channelAuthorizeVehicleTemp.setIsActive("1");
                        channelAuthorizeVehicleTemp.setOnlyId("0");
                        channelAuthorizeVehicleTempUpdService.save(channelAuthorizeVehicleTemp);
                        channelAuthorizeVehicleUpdList.add(channelAuthorizeVehicleTemp);
                    }else {
                        for (String main : mainCar) {
                            ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTempUpd();
                            String code = main.split(",")[0];
                            String title = main.split(",")[1];
                            String parentId = main.split(",")[2];
                            String indeterminate = main.split(",")[3];
                            String carLevel = main.split(",")[4];
                            String spellCode = main.split(",")[5];
                            String carType = main.split(",")[6];
                            channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                            if("品牌名称".equals(title)){
                                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                                channelAuthorizeVehicleTemp.setCode("00");
                                channelAuthorizeVehicleTemp.setParentId("0000");
                            }else {
                                channelAuthorizeVehicleTemp.setCode(code);
                                channelAuthorizeVehicleTemp.setTitle(title);
                                channelAuthorizeVehicleTemp.setParentId(parentId);
                            }
                            channelAuthorizeVehicleTemp.setCarLevel(carLevel);
                            if("null".equals(carType)){
                                channelAuthorizeVehicleTemp.setCarType("");
                            }else{
                                channelAuthorizeVehicleTemp.setCarType(carType);
                            }
                            if (StrUtil.isNotBlank(indeterminate)) {
                                channelAuthorizeVehicleTemp.setIndeterminate(indeterminate);
                            } else {
                                channelAuthorizeVehicleTemp.setIndeterminate("false");
                            }
                            if (StrUtil.isNotBlank(spellCode)) {
                                channelAuthorizeVehicleTemp.setSpellCode(spellCode);
                            }
                            channelAuthorizeVehicleTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeVehicleUpdList.add(channelAuthorizeVehicleTemp);
                        }
                        int size = channelAuthorizeVehicleUpdList.size();
                        if (size <= 1000) {
                            channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

            log.info("==================================新车结束==================================》");
        }

        //判断当前登陆者是否拥有二手车权限，由前端传入
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsOldCarRole())) {
            //保存渠道二手车风控信息前做清除
            channelRiskInfoUpdService.remove(Wrappers.<ChannelRiskInfoTempUpd>query().lambda()
                    .eq(ChannelRiskInfoTempUpd::getChannelId, temp.getId())
                    .eq(ChannelRiskInfoTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车保证金信息前先清除旧的数据*/
            channelQuotaInfoUpdService.remove(Wrappers.<ChannelQuotaInfoTempUpd>query().lambda()
                    .eq(ChannelQuotaInfoTempUpd::getChannelId, temp.getId())
                    .eq(ChannelQuotaInfoTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车授权区域信息前先清除旧的数据*/
            channelAuthorizeRegionUpdService.remove(Wrappers.<ChannelAuthorizeRegionTempUpd>query().lambda()
                    .eq(ChannelAuthorizeRegionTempUpd::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeRegionTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车授权车型信息前先清除旧的数据*/
            channelAuthorizeVehicleTempUpdService.remove(Wrappers.<ChannelAuthorizeVehicleTempUpd>query().lambda()
                    .eq(ChannelAuthorizeVehicleTempUpd::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeVehicleTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车风控信息*/
            ChannelRiskInfoTempUpd channelRiskInfoTemp = channelOnlineCondition.getChannelRiskInfoTempOld();
            //渠道id赋值
            channelRiskInfoTemp.setChannelId(temp.getId());
            channelRiskInfoTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
            channelRiskInfoTemp.setChannelDepositPaidIn(channelRiskInfoTemp.getChannelDeposit());
            channelRiskInfoUpdService.saveOrUpdate(channelRiskInfoTemp);
            list.add(channelRiskInfoTemp);

            //保存二手车保证金信息
            List<ChannelQuotaInfoTempUpd> quotaInfoTempUpdList = new ArrayList<>();
            List<ChannelQuotaInfoTempUpd> channelQuotaInfoList = channelOnlineCondition.getChannelQuotaInfoTempOldList();

            for (ChannelQuotaInfoTempUpd quotaInfo : channelQuotaInfoList) {
                quotaInfo.setChannelId(temp.getId());
                quotaInfo.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                //临时额度
                BigDecimal tempQuota = quotaInfo.getTempQuota() == null ? BigDecimal.ZERO : quotaInfo.getTempQuota();
                //签放额度
                BigDecimal quotaSigning = quotaInfo.getQuotaAmount() == null ? BigDecimal.ZERO : quotaInfo.getQuotaAmount();
                //剩余额度 = 临时额度+签放额度
                quotaInfo.setSurplusQuota(tempQuota.add(quotaSigning));
                //占用额度为0
                quotaInfo.setOccupiedQuota(BigDecimal.ZERO);
                channelQuotaInfoUpdService.saveOrUpdate(quotaInfo);
                quotaInfoTempUpdList.add(quotaInfo);
            }

            //保存渠道二手车授权区域
            List<ChannelAuthorizeRegionTempUpd> channelAuthorizeRegionTempUpdList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                // 说明选的是否
                ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                regionTempUpd.setChannelId(temp.getId());
                regionTempUpd.setCode("1");
                regionTempUpd.setTitle("全国区域");
                regionTempUpd.setIsParent("1");
                regionTempUpd.setParentId("0");
                regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                regionTempUpd.setIndeterminate("false");
                channelAuthorizeRegionUpdService.save(regionTempUpd);

            }else {
                if (mainAreaOld != null && mainAreaOld.length > 0) {
                    if("false".equals(mainAreaOld[0].split(",",-1)[4].toString())){
                        ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                        regionTempUpd.setChannelId(temp.getId());
                        regionTempUpd.setCode("1");
                        regionTempUpd.setTitle("全国区域");
                        regionTempUpd.setIsParent("1");
                        regionTempUpd.setParentId("0");
                        regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                        regionTempUpd.setIndeterminate("false");
                        channelAuthorizeRegionUpdService.save(regionTempUpd);
                    }else {
                        for (String area : mainAreaOld) {
                            ChannelAuthorizeRegionTempUpd regionTempUpd = new ChannelAuthorizeRegionTempUpd();
                            String code = area.split(",")[0];
                            String title = area.split(",")[1];
                            String isParent = area.split(",")[2];
                            String parentId = area.split(",")[3];
                            String indeterminate =area.split(",")[4];
                            regionTempUpd.setChannelId(temp.getId());
                            regionTempUpd.setCode(code);
                            regionTempUpd.setTitle(title);
                            if("全国区域".equals(title)){
                                regionTempUpd.setIsParent("1");
                                regionTempUpd.setParentId("0");
                            }else {
                                regionTempUpd.setIsParent(isParent);
                                regionTempUpd.setParentId(parentId);
                            }
                            regionTempUpd.setParentId(parentId);
                            regionTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                            regionTempUpd.setIndeterminate(indeterminate);
                            channelAuthorizeRegionTempUpdList.add(regionTempUpd);
                        }
                        int size = channelAuthorizeRegionTempUpdList.size();
                        if (size <= 1000) {
                            channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeRegionUpdService.saveBatch(channelAuthorizeRegionTempUpdList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }
            }

            //保存渠道二手车授权车型
            List<ChannelAuthorizeVehicleTempUpd> channelAuthorizeVehicleUpdList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTempUpd = new ChannelAuthorizeVehicleTempUpd();
                channelAuthorizeVehicleTempUpd.setChannelId(temp.getId());
                channelAuthorizeVehicleTempUpd.setBusinessType(RoleTypeDic.OLD_CAR);
                channelAuthorizeVehicleTempUpd.setTitle("品牌名称");
                channelAuthorizeVehicleTempUpd.setCode("00");
                channelAuthorizeVehicleTempUpd.setParentId("0000");
                channelAuthorizeVehicleTempUpd.setCarLevel("0");
                channelAuthorizeVehicleTempUpd.setCreateBy(SecurityUtils.getUsername());
                channelAuthorizeVehicleTempUpd.setCreateTime(new Date(System.currentTimeMillis()));
                channelAuthorizeVehicleTempUpd.setIndeterminate("false");
                channelAuthorizeVehicleTempUpd.setIsActive("1");
                channelAuthorizeVehicleTempUpd.setOnlyId("0");
                channelAuthorizeVehicleTempUpdService.save(channelAuthorizeVehicleTempUpd);
            }else {
                if (mainCarOld != null && mainCarOld.length > 0) {
                    if("false".equals(mainCarOld[0].split(",",-1)[3].toString())){
                        ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTempUpd = new ChannelAuthorizeVehicleTempUpd();
                        channelAuthorizeVehicleTempUpd.setChannelId(temp.getId());
                        channelAuthorizeVehicleTempUpd.setBusinessType(RoleTypeDic.OLD_CAR);
                        channelAuthorizeVehicleTempUpd.setTitle("品牌名称");
                        channelAuthorizeVehicleTempUpd.setCode("00");
                        channelAuthorizeVehicleTempUpd.setParentId("0000");
                        channelAuthorizeVehicleTempUpd.setCarLevel("0");
                        channelAuthorizeVehicleTempUpd.setCreateBy(SecurityUtils.getUsername());
                        channelAuthorizeVehicleTempUpd.setCreateTime(new Date(System.currentTimeMillis()));
                        channelAuthorizeVehicleTempUpd.setIndeterminate("false");
                        channelAuthorizeVehicleTempUpd.setIsActive("1");
                        channelAuthorizeVehicleTempUpd.setOnlyId("0");
                        channelAuthorizeVehicleTempUpdService.save(channelAuthorizeVehicleTempUpd);
                        channelAuthorizeVehicleUpdList.add(channelAuthorizeVehicleTempUpd);
                    }else {
                        for (String main : mainCarOld) {
                            ChannelAuthorizeVehicleTempUpd channelAuthorizeVehicleTempUpd = new ChannelAuthorizeVehicleTempUpd();
                            String code = main.split(",")[0];
                            String title = main.split(",")[1];
                            String parentId = main.split(",")[2];
                            String indeterminate = main.split(",")[3];
                            String carLevel = main.split(",")[4];
                            String spellCode = main.split(",")[5];
                            String carType = main.split(",")[6];
                            String isActive = main.split(",")[7];
                            String onlyId = main.split(",")[8];
                            channelAuthorizeVehicleTempUpd.setChannelId(temp.getId());
                            if("品牌名称".equals(title)){
                                channelAuthorizeVehicleTempUpd.setTitle("品牌名称");
                                channelAuthorizeVehicleTempUpd.setCode("00");
                                channelAuthorizeVehicleTempUpd.setParentId("0000");
                            }else {
                                channelAuthorizeVehicleTempUpd.setCode(code);
                                channelAuthorizeVehicleTempUpd.setTitle(title);
                                channelAuthorizeVehicleTempUpd.setParentId(parentId);
                            }
                            channelAuthorizeVehicleTempUpd.setCarLevel(carLevel);
                            if("null".equals(carType)){
                                channelAuthorizeVehicleTempUpd.setCarType("");
                            }else{
                                channelAuthorizeVehicleTempUpd.setCarType(carType);
                            }
                            if (StrUtil.isNotBlank(indeterminate)) {
                                channelAuthorizeVehicleTempUpd.setIndeterminate(indeterminate);
                            } else {
                                channelAuthorizeVehicleTempUpd.setIndeterminate("false");
                            }
                            if (StrUtil.isNotBlank(spellCode)) {
                                channelAuthorizeVehicleTempUpd.setSpellCode(spellCode);
                            }
                            channelAuthorizeVehicleTempUpd.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                            channelAuthorizeVehicleTempUpd.setIsActive(isActive);
                            channelAuthorizeVehicleTempUpd.setOnlyId(onlyId);
                            channelAuthorizeVehicleUpdList.add(channelAuthorizeVehicleTempUpd);
                        }
                        int size = channelAuthorizeVehicleUpdList.size();
                        if (size <= 1000) {
                            channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeVehicleTempUpdService.saveBatch(channelAuthorizeVehicleUpdList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }
            }

            log.info("==================================二手车结束==================================》");
        }

        channelOnlineUpdService.commitChannelMaintain(temp);
        return IResponse.success(list);
    }

    /**
     * 校验字段唯一性: 合作商代码、SAP代码、经销商名称、统一社会信用代码
     * @param temp
     */
    private void checkFieldUnique(ChannelBaseInfoTempUpd temp) {
        checkUniqueField(temp.getId(), temp.getChannelCode(), ChannelBaseInfoTemp::getChannelCode, "合作商代码已存在");
        checkUniqueField(temp.getId(), temp.getSpaCode(), ChannelBaseInfoTemp::getSpaCode, "SAP代码已存在");
        checkUniqueField(temp.getId(), temp.getChannelFullName(), ChannelBaseInfoTemp::getChannelFullName, "经销商名称已存在");
        checkUniqueField(temp.getId(), temp.getSocUniCrtCode(), ChannelBaseInfoTemp::getSocUniCrtCode, "统一社会信用代码已存在");
    }

    /**
     * 校验字段唯一性
     * @param fieldValue
     * @param fieldGetter
     * @param errorMessage
     */
    private void checkUniqueField(Long channelId, String fieldValue, SFunction<ChannelBaseInfoTemp, String> fieldGetter, String errorMessage) {
        if (StrUtil.isNotBlank(fieldValue)) {
            long count = channelBaseInfoTempService.count(Wrappers.<ChannelBaseInfoTemp>query().lambda()
                    .ne(ChannelBaseInfoTemp::getId, channelId)
                    .eq(fieldGetter, fieldValue));
            if (count > 0) {
                throw new AfsBaseException(errorMessage);
            }
        }
    }

    @RequestMapping(value = "/modifyUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "启用渠道修改保存")
    @SysLog("启用渠道修改保存")
    @Transactional(rollbackFor = Exception.class)
    public IResponse modifyUpdate(@RequestBody ChannelOnlineCondition channelOnlineCondition) {

        List<String> statusList =new ArrayList<>();
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);

        List list = new ArrayList<>();
        //渠道基本信息
        ChannelBaseInfoTemp temp = channelOnlineCondition.getChannelBaseInfoTemp();
        //管理员所在省市
        String[] channelAdminAddressValue = channelOnlineCondition.getChannelAdminAddressValue();
        //注册地所在省市
        String[] channelAddressValue = channelOnlineCondition.getChannelAddressValue();
        //办公所在省市
        String[] officeAddressValue = channelOnlineCondition.getOfficeAddressValue();
        //新车授权区域
        String[] mainArea = channelOnlineCondition.getMainArea();
        //二手车授权区域
        String[] mainAreaOld = channelOnlineCondition.getMainAreaOld();
        //主营品牌
        String[] mainBrand = channelOnlineCondition.getMainBrand();
        //授权车型
        String[] mainCar = channelOnlineCondition.getMainCar();
        //二手车授权车型
        String[] mainCarOld = channelOnlineCondition.getMainCarOld();
        if(CommonConstants.COMMON_NO.equals(temp.getControlBusinessType())){
            mainAreaOld = channelOnlineCondition.getMainArea();
            mainCarOld = channelOnlineCondition.getMainCar();
        }
        //判断管理员地址是否为空
        if (channelAdminAddressValue != null && channelAdminAddressValue.length > 0) {
            temp.setChannelAdminProvince(channelAdminAddressValue[0]);
            temp.setChannelAdminCity(channelAdminAddressValue[1]);
        }
        //判断注册省份是否为空
        if (channelAddressValue != null && channelAddressValue.length > 0) {
            temp.setChannelProvince(channelAddressValue[0]);
            temp.setChannelCity(channelAddressValue[1]);
        }
        //判断办公省份是否为空
        if (officeAddressValue != null && officeAddressValue.length > 0) {
            temp.setOfficeProvince(officeAddressValue[0]);
            temp.setOfficeCity(officeAddressValue[1]);
        }
        // 找个时候先去对比信息
        // 先去对比注册地址
        ChannelBaseInfoTemp oldTmep =channelOnlineService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId, temp.getId()));
        if(oldTmep.getChannelProvince().equals(temp.getChannelProvince())&&oldTmep.getChannelCity().equals(temp.getChannelCity())&&oldTmep.getChannelAddress().equals(temp.getChannelAddress())){

        }else {
            ChannelArchivesInfo info =new  ChannelArchivesInfo();
            info.setChannelId(oldTmep.getId());
            info.setEventName("注册地址");
            String proParam = service.getLabelByCode(temp.getChannelProvince());
            String cityParam = service.getLabelByCode(temp.getChannelCity());

            String oldParam = service.getLabelByCode(oldTmep.getChannelProvince());
            String oldCityParam = service.getLabelByCode(oldTmep.getChannelCity());


            info.setDescription("原注册地址"+oldParam+oldCityParam+oldTmep.getChannelAddress()+"，现注册地址："+proParam+cityParam+temp.getChannelAddress());
            info.setCreateBy(SecurityUtils.getUsername());
            info.setCreateTime(new Date(System.currentTimeMillis()));
            channelArchivesInfoService.save(info);
        }

        //对比办公地址
        if(temp.getOfficeProvince().equals(oldTmep.getOfficeProvince())&&temp.getOfficeCity().equals(oldTmep.getOfficeCity())&&temp.getOfficeAddress().equals(oldTmep.getOfficeAddress())){

        }else {
            ChannelArchivesInfo workInfo =new  ChannelArchivesInfo();
            workInfo.setChannelId(temp.getId());
            workInfo.setEventName("办公地址");
            String workParam = service.getLabelByCode(temp.getOfficeProvince());
            String workcityParam =  service.getLabelByCode(temp.getOfficeCity());

            String workOldParam = service.getLabelByCode(oldTmep.getOfficeProvince());
            String workcityOldParam =service.getLabelByCode(oldTmep.getOfficeCity());


            workInfo.setDescription("原办公地址："+workOldParam+workcityOldParam+oldTmep.getOfficeAddress()+",现办公地址："+workParam+workcityParam+temp.getOfficeAddress());
            workInfo.setCreateBy(SecurityUtils.getUsername());
            workInfo.setCreateTime(new Date(System.currentTimeMillis()));
            channelArchivesInfoService.save(workInfo);
        }
        // 判断组织机构架构信息
        if(!checkEquals(temp.getDeptAttributionId(),oldTmep.getDeptAttributionId())){
            ChannelArchivesInfo userInfo =new  ChannelArchivesInfo();
            userInfo.setChannelId(temp.getId());
            userInfo.setEventName("组织机构架构");
            userInfo.setDescription("组织机构架构信息:"+oldTmep.getDeptAttributionId());
            userInfo.setCreateBy(SecurityUtils.getUsername());
            userInfo.setCreateTime(new Date(System.currentTimeMillis()));
            channelArchivesInfoService.save(userInfo);
        }
        // 判断组织机构架构信息
         if(!checkEquals(temp.getSubjectAttributionId(),oldTmep.getSubjectAttributionId())){
            ChannelArchivesInfo userInfo =new  ChannelArchivesInfo();
            userInfo.setChannelId(temp.getId());
            userInfo.setEventName("归属业务主体");
            userInfo.setDescription("归属业务主体信息:"+oldTmep.getDeptAttributionId());
            userInfo.setCreateBy(SecurityUtils.getUsername());
            userInfo.setCreateTime(new Date(System.currentTimeMillis()));
            channelArchivesInfoService.save(userInfo);
        }
        // 去判断管理员信息
        if(temp.getChannelAdmin().equals(oldTmep.getChannelAdmin())&&temp.getChannelAdminIdCard().equals(oldTmep.getChannelAdminIdCard())&&temp.getChannelAdminTel().equals(oldTmep.getChannelAdminTel())){

        }else {
            ChannelArchivesInfo userInfo =new  ChannelArchivesInfo();
            userInfo.setChannelId(temp.getId());
            userInfo.setEventName("管理员信息");
            userInfo.setDescription("原管理员信息：姓名"+oldTmep.getChannelAdmin()+"身份证："+oldTmep.getChannelAdminIdCard()+"电话："+oldTmep.getChannelAdminTel()+"，现管理员信息姓名："+temp.getChannelAdmin()+"身份证："+temp.getChannelAdminIdCard()+"电话："+temp.getChannelAdminTel());
            userInfo.setCreateBy(SecurityUtils.getUsername());
            userInfo.setCreateTime(new Date(System.currentTimeMillis()));
            channelArchivesInfoService.save(userInfo);
        }

        //3.渠道评级新车
        ChannelRiskInfoTemp riskInfoTemp = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfoTemp>query().lambda().eq(ChannelRiskInfoTemp::getChannelId, temp.getId().toString())
                .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
        if(riskInfoTemp!=null){
            if(riskInfoTemp.getChannelGrade().equals(channelOnlineCondition.getChannelRiskInfoTemp().getChannelGrade())){

            }else {
                ChannelArchivesInfo onlineGrade = new ChannelArchivesInfo();
                onlineGrade.setChannelId(temp.getId());
                onlineGrade.setEventName("新车渠道评级");
                onlineGrade.setDescription("原新车渠道评级:"+setInfo(riskInfoTemp.getChannelGrade())+",现新车渠道评级:"+setInfo(channelOnlineCondition.getChannelRiskInfoTemp().getChannelGrade()));
                onlineGrade.setCreateBy(SecurityUtils.getUsername());
                onlineGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(onlineGrade);
            }
            if(riskInfoTemp.getQualityGrade().equals(channelOnlineCondition.getChannelRiskInfoTemp().getQualityGrade())){

            }else {
                // 优质等级
                ChannelArchivesInfo qualityGrade =new ChannelArchivesInfo();
                qualityGrade.setChannelId(temp.getId());
                qualityGrade.setEventName("新车优质等级");
                qualityGrade.setDescription("原新车优质等级:"+setInfo(riskInfoTemp.getQualityGrade())+",现新车优质等级:"+setInfo(channelOnlineCondition.getChannelRiskInfoTemp().getQualityGrade()));
                qualityGrade.setCreateBy(SecurityUtils.getUsername());
                qualityGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(qualityGrade);
            }
        }

        //3.渠道评级二手车
        ChannelRiskInfoTemp riskOldInfoTemp = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfoTemp>query().lambda().eq(ChannelRiskInfoTemp::getChannelId, temp.getId().toString())
                .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
        if(riskOldInfoTemp!=null){
            if(riskOldInfoTemp.getChannelGrade().equals(channelOnlineCondition.getChannelRiskInfoTempOld().getChannelGrade())){

            }else {
                ChannelArchivesInfo onlineOldGrade = new ChannelArchivesInfo();
                onlineOldGrade.setChannelId(temp.getId());
                onlineOldGrade.setEventName("二车渠道评级");
                onlineOldGrade.setDescription("二新车渠道评级:"+setInfo(riskOldInfoTemp.getChannelGrade())+",现二车渠道评级:"+setInfo(channelOnlineCondition.getChannelRiskInfoTempOld().getChannelGrade()));
                onlineOldGrade.setCreateBy(SecurityUtils.getUsername());
                onlineOldGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(onlineOldGrade);
            }
            if(riskOldInfoTemp.getQualityGrade().equals(channelOnlineCondition.getChannelRiskInfoTempOld().getQualityGrade())){

            }else {
                // 优质等级
                ChannelArchivesInfo qualityOldGrade =new ChannelArchivesInfo();
                qualityOldGrade.setChannelId(temp.getId());
                qualityOldGrade.setEventName("二车优质等级");
                qualityOldGrade.setDescription("原二车优质等级:"+setInfo(riskOldInfoTemp.getQualityGrade())+",现二车优质等级:"+setInfo(channelOnlineCondition.getChannelRiskInfoTempOld().getQualityGrade()));
                qualityOldGrade.setCreateBy(SecurityUtils.getUsername());
                qualityOldGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(qualityOldGrade);
            }
        }

        // 记录新车的原展业区域
        List<ChannelAuthorizeRegionTemp> seeNewRegion =channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId,temp.getId()).eq(ChannelAuthorizeRegionTemp::getBusinessType,ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR).eq(ChannelAuthorizeRegionTemp::getParentId,"1"));

        List<ChannelAuthorizeRegionTemp> seeOldRegion =channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId,temp.getId()).eq(ChannelAuthorizeRegionTemp::getBusinessType,ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR).eq(ChannelAuthorizeRegionTemp::getParentId,"1"));

        channelOnlineService.saveOrUpdate(temp);
        list.add(temp);

        //判断当前登陆者是否拥有新车权限，由前端传入
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsNewCarRole())) {
            ChannelOnlineInterFaceVo vo = new ChannelOnlineInterFaceVo();
            log.info("==================================基本信息组装开始==================================》");
            vo.setChannelBaseInfo(temp);
            log.info("==================================基本信息组装完毕==================================》");
            //保存渠道 新车风控信息前做清除
            channelRiskInfoService.remove(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, temp.getId())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车保证金信息前先清除旧的数据*/
            channelQuotaInfoService.remove(Wrappers.<ChannelQuotaInfoTemp>query().lambda()
                    .eq(ChannelQuotaInfoTemp::getChannelId, temp.getId())
                    .eq(ChannelQuotaInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车授权区域信息前先清除旧的数据*/
            channelAuthorizeRegionService.remove(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                    .eq(ChannelAuthorizeRegionTemp::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeRegionTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车授权车型信息前先清除旧的数据*/
            channelAuthorizeVehicleTempService.remove(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda()
                    .eq(ChannelAuthorizeVehicleTemp::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeVehicleTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));

            /** 保存渠道新车风控信息*/
            ChannelRiskInfoTemp channelRiskInfoTemp = channelOnlineCondition.getChannelRiskInfoTemp();
            //渠道id赋值
            channelRiskInfoTemp.setChannelId(temp.getId());
            channelRiskInfoTemp.setChannelDepositPaidIn(channelRiskInfoTemp.getChannelDeposit());
            channelRiskInfoService.saveOrUpdate(channelRiskInfoTemp);
            list.add(channelRiskInfoTemp);
            log.info("==================================新车风控信息组装开始==================================》");
            List<ChannelRiskInfoTemp> riskInfoTempList = new ArrayList<>();
            riskInfoTempList.add(channelRiskInfoTemp);
            vo.setRisk(riskInfoTempList);
            log.info("==================================新车风控信息组装完毕==================================》");

            //保存新车保证金信息
            List<ChannelQuotaInfoTemp> quotaInfoTempList = new ArrayList<>();
            List<ChannelQuotaInfoTemp> channelQuotaInfoList = channelOnlineCondition.getChannelQuotaInfoTempList();
            for (ChannelQuotaInfoTemp quotaInfo : channelQuotaInfoList) {
                quotaInfo.setChannelId(temp.getId());
                //临时额度
                BigDecimal tempQuota = quotaInfo.getTempQuota() == null ? BigDecimal.ZERO : quotaInfo.getTempQuota();
                //签放额度
                BigDecimal quotaSigning = quotaInfo.getQuotaAmount() == null ? BigDecimal.ZERO : quotaInfo.getQuotaAmount();
                //剩余额度 = 临时额度+签放额度
                quotaInfo.setSurplusQuota(tempQuota.add(quotaSigning));
                //占用额度为0
                quotaInfo.setOccupiedQuota(BigDecimal.ZERO);
                channelQuotaInfoService.saveOrUpdate(quotaInfo);
                quotaInfoTempList.add(quotaInfo);
            }
            log.info("==================================新车保证金信息组装开始==================================》");
            vo.setQuota(quotaInfoTempList);
            log.info("==================================新车保证金信息组装结束==================================》");

            //保存渠道新车授权区域
            List<ChannelAuthorizeRegionTemp> channelAuthorizeRegionTempList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthRegionSwitch())){
                // 说明选的是否
                ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                regionTemp.setChannelId(temp.getId());
                regionTemp.setCode("1");
                regionTemp.setTitle("全国区域");
                regionTemp.setIsParent("1");
                regionTemp.setParentId("0");
                regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                regionTemp.setIndeterminate("false");
                channelAuthorizeRegionService.save(regionTemp);
            }else {
                if (mainArea != null && mainArea.length > 0) {
                    if("false".equals(mainArea[0].split(",",-1)[4].toString())){
                        ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                        regionTemp.setChannelId(temp.getId());
                        regionTemp.setCode("1");
                        regionTemp.setTitle("全国区域");
                        regionTemp.setIsParent("1");
                        regionTemp.setParentId("0");
                        regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                        regionTemp.setIndeterminate("false");
                        channelAuthorizeRegionService.save(regionTemp);
                    }else {
                        for (String area : mainArea) {
                            ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                            String code = area.split(",")[0];
                            String title = area.split(",")[1];
                            String isParent = area.split(",")[2];
                            String parentId = area.split(",")[3];
                            regionTemp.setChannelId(temp.getId());
                            regionTemp.setCode(code);
                            // 单独处理 全国区域会出现 undefined
                            if("全国区域".equals(title)){
                                regionTemp.setIsParent("1");
                                regionTemp.setParentId("0");
                            }else {
                                regionTemp.setIsParent(isParent);
                                regionTemp.setParentId(parentId);
                            }
                            regionTemp.setTitle(title);

                            regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeRegionTempList.add(regionTemp);
                        }
                        int size = channelAuthorizeRegionTempList.size();
                        if (size <= 1000) {
                            channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

            log.info("==================================新车授权区域信息组装开始==================================》");
            vo.setRegion(channelAuthorizeRegionTempList);
            log.info("==================================新车授权区域信息组装结束==================================》"+"新车是否控制车型"+channelRiskInfoTemp.getAuthVehicleTypeSwitch());
            //保存渠道新车授权车型
            List<ChannelAuthorizeVehicleTemp> channelAuthorizeVehicleList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                channelAuthorizeVehicleTemp.setCode("00");
                channelAuthorizeVehicleTemp.setParentId("0000");
                channelAuthorizeVehicleTemp.setCarLevel("0");
                channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                channelAuthorizeVehicleTemp.setIndeterminate("false");
                channelAuthorizeVehicleTemp.setIsActive("1");
                channelAuthorizeVehicleTemp.setOnlyId("0");
                channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
                channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
            }else {
                if (mainCar != null && mainCar.length > 0) {
                    if("false".equals(mainCar[0].split(",",-1)[3].toString())){
                        ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                        channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                        channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.NEW_CAR);
                        channelAuthorizeVehicleTemp.setTitle("品牌名称");
                        channelAuthorizeVehicleTemp.setCode("00");
                        channelAuthorizeVehicleTemp.setParentId("0000");
                        channelAuthorizeVehicleTemp.setCarLevel("0");
                        channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                        channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                        channelAuthorizeVehicleTemp.setIndeterminate("false");
                        channelAuthorizeVehicleTemp.setIsActive("1");
                        channelAuthorizeVehicleTemp.setOnlyId("0");
                        channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
                        channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
                    }else {
                        for (String main : mainCar) {
                            ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                            String code = main.split(",")[0];
                            String title = main.split(",")[1];
                            String parentId = main.split(",")[2];
                            String indeterminate = main.split(",")[3];
                            String carLevel = main.split(",")[4];
                            String spellCode = main.split(",")[5];
                            String carType = main.split(",")[6];
                            channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                            if("品牌名称".equals(title)){
                                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                                channelAuthorizeVehicleTemp.setCode("00");
                                channelAuthorizeVehicleTemp.setParentId("0000");
                            }else {
                                channelAuthorizeVehicleTemp.setCode(code);
                                channelAuthorizeVehicleTemp.setTitle(title);
                                channelAuthorizeVehicleTemp.setParentId(parentId);
                            }
                            channelAuthorizeVehicleTemp.setCarLevel(carLevel);
                            if("null".equals(carType)){
                                channelAuthorizeVehicleTemp.setCarType("");
                            }else{
                                channelAuthorizeVehicleTemp.setCarType(carType);
                            }
                            if (StrUtil.isNotBlank(indeterminate)) {
                                channelAuthorizeVehicleTemp.setIndeterminate(indeterminate);
                            } else {
                                channelAuthorizeVehicleTemp.setIndeterminate("false");
                            }
                            if (StrUtil.isNotBlank(spellCode)) {
                                channelAuthorizeVehicleTemp.setSpellCode(spellCode);
                            }
                            channelAuthorizeVehicleTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);
                            channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
                        }
                        int size = channelAuthorizeVehicleList.size();
                        if (size <= 1000) {
                            channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }

            }

            log.info("==================================新车授权车型信息组装开始==================================》");
            vo.setVehicle(channelAuthorizeVehicleList);
            log.info("==================================新车授权车型信息组装结束==================================》");

            //赋值业务类型
            vo.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR);

            log.info("==================================新车同步数据开始==================================》");
            AfsTransEntity<ChannelOnlineInterFaceVo> transEntity = new AfsTransEntity<>();
            transEntity.setTransCode(MqTransCode.CHANNEL_ONLINE_DEALER_INFO__TO_CASE);
            transEntity.setData(vo);
            channelOnlineInfoToCaseSender.sendForCaseChannelOnlineInfo(transEntity);

            // 传给进件端合作商车型数据
            ChannelAehicle aehicle= new ChannelAehicle();
            aehicle.setVehicle(vo.getVehicle());
            aehicle.setBusinessType(vo.getBusinessType());
            // 同步车型库数据到进件
            AfsTransEntity<ChannelAehicle> aehicleEntity = new AfsTransEntity<>();
            aehicleEntity.setTransCode(MqTransCode.CHANNEL_CHANNEL_APPLY_POS_SEND_CHANNEL_VEHICLE_APPLY);
            aehicleEntity.setData(aehicle);
            senderChannelToApply.sendVehicle(aehicleEntity);
            log.info("==================================新车同步数据结束==================================》");
        }

        //判断当前登陆者是否拥有二手车权限，由前端传入
        if (ChannelOnlineConstants.IS_HAVE_CAR_ROLE_YES.equals(channelOnlineCondition.getIsOldCarRole())) {
            ChannelOnlineInterFaceVo vo = new ChannelOnlineInterFaceVo();
            log.info("==================================基本信息组装开始==================================》");
            vo.setChannelBaseInfo(temp);
            log.info("==================================基本信息组装完毕==================================》");

            //保存渠道二手车风控信息前做清除
            channelRiskInfoService.remove(Wrappers.<ChannelRiskInfoTemp>query().lambda()
                    .eq(ChannelRiskInfoTemp::getChannelId, temp.getId())
                    .eq(ChannelRiskInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车保证金信息前先清除旧的数据*/
            channelQuotaInfoService.remove(Wrappers.<ChannelQuotaInfoTemp>query().lambda()
                    .eq(ChannelQuotaInfoTemp::getChannelId, temp.getId())
                    .eq(ChannelQuotaInfoTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车授权区域信息前先清除旧的数据*/
            channelAuthorizeRegionService.remove(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                    .eq(ChannelAuthorizeRegionTemp::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeRegionTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车授权车型信息前先清除旧的数据*/
            channelAuthorizeVehicleTempService.remove(Wrappers.<ChannelAuthorizeVehicleTemp>query().lambda()
                    .eq(ChannelAuthorizeVehicleTemp::getChannelId, temp.getId())
                    .eq(ChannelAuthorizeVehicleTemp::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));

            /** 保存渠道二手车风控信息*/
            ChannelRiskInfoTemp channelRiskInfoTemp = channelOnlineCondition.getChannelRiskInfoTempOld();
            //渠道id赋值
            channelRiskInfoTemp.setChannelId(temp.getId());
            channelRiskInfoTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
            channelRiskInfoTemp.setChannelDepositPaidIn(channelRiskInfoTemp.getChannelDeposit());
            channelRiskInfoService.save(channelRiskInfoTemp);
            list.add(channelRiskInfoTemp);
            log.info("==================================二手车风控信息组装开始==================================》");
            List<ChannelRiskInfoTemp> riskInfoTempList = new ArrayList<>();
            riskInfoTempList.add(channelRiskInfoTemp);
            vo.setRisk(riskInfoTempList);
            log.info("==================================二手车风控信息组装完毕==================================》");

            //保存二手车保证金信息
            List<ChannelQuotaInfoTemp> quotaInfoTempList = new ArrayList<>();
            List<ChannelQuotaInfoTemp> channelQuotaInfoList = channelOnlineCondition.getChannelQuotaInfoTempOldList();
            for (ChannelQuotaInfoTemp quotaInfo : channelQuotaInfoList) {
                quotaInfo.setChannelId(temp.getId());
                quotaInfo.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                //临时额度
                BigDecimal tempQuota = quotaInfo.getTempQuota() == null ? BigDecimal.ZERO : quotaInfo.getTempQuota();
                //签放额度
                BigDecimal quotaSigning = quotaInfo.getQuotaAmount() == null ? BigDecimal.ZERO : quotaInfo.getQuotaAmount();
                //剩余额度 = 临时额度+签放额度
                quotaInfo.setSurplusQuota(tempQuota.add(quotaSigning));
                //占用额度为0
                quotaInfo.setOccupiedQuota(BigDecimal.ZERO);
                channelQuotaInfoService.saveOrUpdate(quotaInfo);
                quotaInfoTempList.add(quotaInfo);
            }
            log.info("=================================二手车保证金信息组装开始==================================》");
            vo.setQuota(quotaInfoTempList);
            log.info("==================================二手车保证金信息组装结束==================================》");

            //保存渠道二手车授权区域
            List<ChannelAuthorizeRegionTemp> channelAuthorizeRegionTempList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                // 说明选的是否
                ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                regionTemp.setChannelId(temp.getId());
                regionTemp.setCode("1");
                regionTemp.setTitle("全国区域");
                regionTemp.setIsParent("1");
                regionTemp.setParentId("0");
                regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                regionTemp.setIndeterminate("false");
                channelAuthorizeRegionService.save(regionTemp);
                channelAuthorizeRegionTempList.add(regionTemp);
            }else {
                if (mainAreaOld != null && mainAreaOld.length > 0) {
                    if("false".equals(mainAreaOld[0].split(",",-1)[4].toString())){
                        ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                        regionTemp.setChannelId(temp.getId());
                        regionTemp.setCode("1");
                        regionTemp.setTitle("全国区域");
                        regionTemp.setIsParent("1");
                        regionTemp.setParentId("0");
                        regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                        regionTemp.setIndeterminate("false");
                        channelAuthorizeRegionService.save(regionTemp);
                        channelAuthorizeRegionTempList.add(regionTemp);
                    }else {
                        for (String area : mainAreaOld) {
                            ChannelAuthorizeRegionTemp regionTemp = new ChannelAuthorizeRegionTemp();
                            String code = area.split(",")[0];
                            String title = area.split(",")[1];
                            String isParent = area.split(",")[2];
                            String parentId = area.split(",")[3];
                            String indeterminate =area.split(",")[4];
                            regionTemp.setChannelId(temp.getId());
                            regionTemp.setCode(code);
                            regionTemp.setTitle(title);
                            if("全国区域".equals(title)){
                                regionTemp.setIsParent("1");
                                regionTemp.setParentId("0");
                            }else {
                                regionTemp.setIsParent(isParent);
                                regionTemp.setParentId(parentId);
                            }
                            regionTemp.setParentId(parentId);
                            regionTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                            regionTemp.setIndeterminate(indeterminate);
                            channelAuthorizeRegionTempList.add(regionTemp);
                        }
                        int size = channelAuthorizeRegionTempList.size();
                        if (size <= 1000) {
                            channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeRegionService.saveBatch(channelAuthorizeRegionTempList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }
            }
            log.info("==================================二手车授权区域信息组装开始==================================》");
            vo.setRegion(channelAuthorizeRegionTempList);
            log.info("==================================二手车授权区域信息组装结束==================================》"+"二手车是否控制车型："+channelRiskInfoTemp.getAuthVehicleTypeSwitch());

            //保存渠道二手车授权车型
            List<ChannelAuthorizeVehicleTemp> channelAuthorizeVehicleList = new ArrayList<>();
            if("0".equals(channelRiskInfoTemp.getAuthVehicleTypeSwitch())){
                ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.OLD_CAR);
                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                channelAuthorizeVehicleTemp.setCode("00");
                channelAuthorizeVehicleTemp.setParentId("0000");
                channelAuthorizeVehicleTemp.setCarLevel("0");
                channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                channelAuthorizeVehicleTemp.setIndeterminate("false");
                channelAuthorizeVehicleTemp.setIsActive("1");
                channelAuthorizeVehicleTemp.setOnlyId("0");
                channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
                channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
            }else {
                if (mainCarOld != null && mainCarOld.length > 0) {
                    if("false".equals(mainCarOld[0].split(",",-1)[3].toString())){
                        ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                        channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                        channelAuthorizeVehicleTemp.setBusinessType(RoleTypeDic.OLD_CAR);
                        channelAuthorizeVehicleTemp.setTitle("品牌名称");
                        channelAuthorizeVehicleTemp.setCode("00");
                        channelAuthorizeVehicleTemp.setParentId("0000");
                        channelAuthorizeVehicleTemp.setCarLevel("0");
                        channelAuthorizeVehicleTemp.setCreateBy(SecurityUtils.getUsername());
                        channelAuthorizeVehicleTemp.setCreateTime(new Date(System.currentTimeMillis()));
                        channelAuthorizeVehicleTemp.setIndeterminate("false");
                        channelAuthorizeVehicleTemp.setIsActive("1");
                        channelAuthorizeVehicleTemp.setOnlyId("0");
                        channelAuthorizeVehicleTempService.save(channelAuthorizeVehicleTemp);
                        channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
                    }else {
                        for (String main : mainCarOld) {
                            ChannelAuthorizeVehicleTemp channelAuthorizeVehicleTemp = new ChannelAuthorizeVehicleTemp();
                            String code = main.split(",")[0];
                            String title = main.split(",")[1];
                            String parentId = main.split(",")[2];
                            String indeterminate = main.split(",")[3];
                            String carLevel = main.split(",")[4];
                            String spellCode = main.split(",")[5];
                            String carType = main.split(",")[6];
                            String isActive = main.split(",")[7];
                            String onlyId = main.split(",")[8];
                            channelAuthorizeVehicleTemp.setChannelId(temp.getId());
                            if("品牌名称".equals(title)){
                                channelAuthorizeVehicleTemp.setTitle("品牌名称");
                                channelAuthorizeVehicleTemp.setCode("00");
                                channelAuthorizeVehicleTemp.setParentId("0000");
                            }else {
                                channelAuthorizeVehicleTemp.setCode(code);
                                channelAuthorizeVehicleTemp.setTitle(title);
                                channelAuthorizeVehicleTemp.setParentId(parentId);
                            }
                            channelAuthorizeVehicleTemp.setCarLevel(carLevel);
                            if("null".equals(carType)){
                                channelAuthorizeVehicleTemp.setCarType("");
                            }else{
                                channelAuthorizeVehicleTemp.setCarType(carType);
                            }
                            if (StrUtil.isNotBlank(indeterminate)) {
                                channelAuthorizeVehicleTemp.setIndeterminate(indeterminate);
                            } else {
                                channelAuthorizeVehicleTemp.setIndeterminate("false");
                            }
                            if (StrUtil.isNotBlank(spellCode)) {
                                channelAuthorizeVehicleTemp.setSpellCode(spellCode);
                            }
                            channelAuthorizeVehicleTemp.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
                            channelAuthorizeVehicleTemp.setIsActive(isActive);
                            channelAuthorizeVehicleTemp.setOnlyId(onlyId);
                            channelAuthorizeVehicleList.add(channelAuthorizeVehicleTemp);
                        }
                        int size = channelAuthorizeVehicleList.size();
                        if (size <= 1000) {
                            channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList);
                        } else {
                            for (int i = 0; i < size / 1000; i++) {
                                if (size / 1000 - i > 1) {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, 1000 * i + 999));
                                } else {
                                    channelAuthorizeVehicleTempService.saveBatch(channelAuthorizeVehicleList.subList(1000 * i, size - 1));
                                }
                            }
                        }
                    }
                }
            }
            log.info("==================================二手车授权车型信息组装开始==================================》");
            vo.setVehicle(channelAuthorizeVehicleList);
            log.info("==================================二手车授权车型信息组装结束==================================》");

            //赋值业务类型
            vo.setBusinessType(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR);
            log.info("==================================二手车同步数据开始==================================》");
            AfsTransEntity<ChannelOnlineInterFaceVo> transEntity = new AfsTransEntity<>();
            transEntity.setTransCode(MqTransCode.CHANNEL_ONLINE_DEALER_INFO__TO_CASE);
            transEntity.setData(vo);
            channelOnlineInfoToCaseSender.sendForCaseChannelOnlineInfo(transEntity);
            // 传给进件端合作商车型数据
            ChannelAehicle aehicle= new ChannelAehicle();
            aehicle.setVehicle(vo.getVehicle());
            aehicle.setBusinessType(vo.getBusinessType());
            // 同步车型库数据到进件
            AfsTransEntity<ChannelAehicle> aehicleEntity = new AfsTransEntity<>();
            aehicleEntity.setTransCode(MqTransCode.CHANNEL_CHANNEL_APPLY_POS_SEND_CHANNEL_VEHICLE_APPLY);
            aehicleEntity.setData(aehicle);
            senderChannelToApply.sendVehicle(aehicleEntity);
            log.info("==================================二手车同步数据结束==================================》");
        }

        // 这个时候去记录授权区域的变化
        List<ChannelAuthorizeRegionTemp> nowNewRegion =channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId,temp.getId()).eq(ChannelAuthorizeRegionTemp::getBusinessType,ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR).eq(ChannelAuthorizeRegionTemp::getParentId,"1"));

        List<ChannelAuthorizeRegionTemp> nowOldRegion =channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda().eq(ChannelAuthorizeRegionTemp::getChannelId,temp.getId()).eq(ChannelAuthorizeRegionTemp::getBusinessType,ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR).eq(ChannelAuthorizeRegionTemp::getParentId,"1"));

        if(seeNewRegion.size()>0){
            List<String> ge=getLose(seeNewRegion,nowNewRegion);
            List<String> eg =getLose(nowNewRegion,seeNewRegion);
            if(ge.size()>0||eg.size()>0){
                ChannelArchivesInfo qualityGrade =new ChannelArchivesInfo();
                qualityGrade.setChannelId(temp.getId());
                qualityGrade.setEventName("新车授权省份");
                StringBuffer pro=new StringBuffer();
                for (String g:ge){
                    pro.append(g)
                            .append(",");
                }
                StringBuffer pp= new StringBuffer();
                for (int i = 0; i < eg.size(); i++) {
                    pp.append(",");
                }
                if(ge.size()>0&&eg.size()>0){
                    qualityGrade.setDescription("减少省份："+pro+"新增省份："+pp);
                }else if(ge.size()>0){
                    qualityGrade.setDescription("减少省份："+pro);
                }else if(eg.size()>0){
                    qualityGrade.setDescription("新增省份："+pp);
                }
                qualityGrade.setCreateBy(SecurityUtils.getUsername());
                qualityGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(qualityGrade);
            }
        }
        if(seeOldRegion.size()>0){
            List<String> oldge = getLose(seeOldRegion,nowOldRegion);
            List<String> oldeg = getLose(nowOldRegion,seeOldRegion);
            if(oldge.size()>0||oldeg.size()>0){
                ChannelArchivesInfo qualityGrade =new ChannelArchivesInfo();
                qualityGrade.setChannelId(temp.getId());
                qualityGrade.setEventName("二手车授权省份");
                String pro=oldge.stream().collect(Collectors.joining(","));
                String pp=oldeg.stream().collect(Collectors.joining(","));
                if(oldge.size()>0&&oldeg.size()>0){
                    qualityGrade.setDescription("减少省份："+pro+"新增省份："+pp);
                }else if(oldge.size()>0){
                    qualityGrade.setDescription("减少省份："+pro);
                }else if(oldeg.size()>0){
                    qualityGrade.setDescription("新增省份："+pp);
                }
                qualityGrade.setCreateBy(SecurityUtils.getUsername());
                qualityGrade.setCreateTime(new Date(System.currentTimeMillis()));
                channelArchivesInfoService.save(qualityGrade);
            }
        }

        log.info("==================================进件授权同步数据开始==================================》");

        elasticService.sendChannelToApplyAdmin(temp,null,null,null,null,null,null);


        log.info("==================================进件授权同步数据结束==================================》");
        try{
            channelIdToCommissionSystem.sendAlterDealerMq(temp.getId().toString(),ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO.equals(temp.getChannelStatusOldCar()) || ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO.equals(temp.getChannelStatus())?"2":"1" );
        }catch (Exception e){
            log.error("同步佣金系统mq时报");
        }
        return IResponse.success(list);
    }

    /**
     * 动态评级的匹配
     * @param grade
     * @return
     */
    public String setInfo(String grade) {
        if(AfsEnumUtil.key(ChannelOnlineEnum.GRADE_A).equals(grade)){
            grade=AfsEnumUtil.desc(ChannelOnlineEnum.GRADE_A);
        }else if(AfsEnumUtil.key(ChannelOnlineEnum.GRADE_B).equals(grade)){
            grade=AfsEnumUtil.desc(ChannelOnlineEnum.GRADE_B);
        }else if(AfsEnumUtil.key(ChannelOnlineEnum.GRADE_C).equals(grade)){
            grade=AfsEnumUtil.desc(ChannelOnlineEnum.GRADE_C);
        }else{
            grade=AfsEnumUtil.desc(ChannelOnlineEnum.GRADE_D);
        }
        return grade;
    }

    /**
     * 获取丢失的省市
     * @param list1
     * @param list2
     * @return
     */
    private static List<String> getLose(List<ChannelAuthorizeRegionTemp> list1, List<ChannelAuthorizeRegionTemp> list2) {
        List<String> newList = new ArrayList<String>();
        // list1：原来的数据  list2:现在的数据

        // 第一步先把现在所有的数据放在集合里面
        if (list2 != null && !list2.isEmpty()) {
            Map<String, String> dataMap = new HashMap<String, String>();
            for (ChannelAuthorizeRegionTemp id : list2) {
                    dataMap.put(id.getCode(), id.getCode());

            }

            //  第二部 把原来的数据现在没有的，就把这个数据存储
            if (list1.size()>0){
                for (ChannelAuthorizeRegionTemp id : list1) {
                    if (!dataMap.containsKey(id.getCode())) {
                        newList.add(id.getTitle());
                    }
                }
            }
            return newList;
        } else {
            return newList;
        }
    }

    @RequestMapping(value = "/modifyRange", method = RequestMethod.POST)
    @ApiOperation(value = "批量修改经纬度范围")
    @SysLog("批量修改经纬度范围")
    @Transactional(rollbackFor = Exception.class)
    public IResponse modifyRange(@RequestBody ChannelOnlineCondition channelOnlineCondition) {
        log.info("==================================批量修改渠道服务经纬度范围开始==================================》");
        List<ChannelBaseInfoTemp> tempList = channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getId, Arrays.asList(channelOnlineCondition.getChannelIds())));
        if (tempList != null && tempList.size() > 0) {
            tempList.forEach(temp -> {
                temp.setLongitudeLatitudeRange(channelOnlineCondition.getLlRangeModify());
            });
            channelOnlineService.saveOrUpdateBatch(tempList);
        }
        log.info("==================================批量修改渠道服务经纬度范围结束==================================》");
        log.info("==================================同步修改案件服务经纬度范围开始==================================》");
        channelOnlineService.synchronousLlRangeToCase(tempList);
        log.info("==================================同步修改案件服务经纬度范围结束==================================》");
        return IResponse.success("修改成功");
    }
    @PostMapping("/queryChannelByCust/{ids}")
    public IResponse<List<ChannelBaseInfoTemp>> getChannelListByCust(@PathVariable List<Long> ids){
        List<ChannelBaseInfoTemp> returnList = new ArrayList<>();
        if(ids != null && ids.size() > 0){
            List<ChannelRiskInfoTemp> riskInfoTemps = channelRiskInfoService.lambdaQuery().in(ChannelRiskInfoTemp::getCustManageId,ids).list();
            List<Long> channelId = riskInfoTemps.stream().map(ChannelRiskInfoTemp::getChannelId).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(channelId)) {
                returnList = channelBaseInfoTempService.lambdaQuery().in(ChannelBaseInfoTemp::getId, channelId).list();
            }
        }
        return IResponse.success(returnList);
    }

    @PostMapping("/queryChannelInfoByIds")
    public IResponse queryChannelInfoByIds(@RequestBody List<String> ids){
        List<ChannelBaseInfoTemp> returnList = new ArrayList<>();
        if(CollUtil.isNotEmpty(ids)) {
            returnList = channelBaseInfoTempService.list(Wrappers.<ChannelBaseInfoTemp>lambdaQuery().in(ChannelBaseInfoTemp::getId, ids));
        }
        return IResponse.success(returnList);
    }

    @PostMapping(value = "/batchModifyManager")
    @SysLog("批量修改渠道服务区域经理")
    @Transactional(rollbackFor = Exception.class)
    public IResponse batchModifyManager(@RequestBody @Validated ChannelCustManageVo vo){
            List<Long> ids = vo.getChannelIds();
            //目前不区分新车二手车，如果区分需要单独查询后进行判断
            channelRiskInfoService.lambdaUpdate().set(ChannelRiskInfoTemp::getCustManageId,vo.getCustId())
                .set(ChannelRiskInfoTemp::getCustomerManager,vo.getCustomerManage()).in(ChannelRiskInfoTemp::getChannelId,ids).update();
            IResponse resp = caseFeignService.syncCustManagerToCase(vo);
            if(CommonConstants.SUCCESS.equals(resp.getCode())){
               return IResponse.success("修改成功");
            }else{
                log.error("批量修改区域经理失败{}",resp.getMsg());
                throw new AfsBaseException("修改失败");
            }
    }

    @RequestMapping(value = "/modifyManager", method = RequestMethod.POST)
    @ApiOperation(value = "批量修改渠道服务区域经理")
    @SysLog("批量修改渠道服务区域经理")
    @Transactional(rollbackFor = Exception.class)
    public IResponse modifyManager(@RequestBody ChannelOnlineCondition channelOnlineCondition) {
        log.info("==================================批量修改渠道服务区域经理开始==================================》");
        List<ChannelRiskInfoTemp> riskInfoTempList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda().in(ChannelRiskInfoTemp::getChannelId, Arrays.asList(channelOnlineCondition.getChannelIds())));
        if (riskInfoTempList != null && riskInfoTempList.size() > 0) {
            riskInfoTempList.forEach(temp -> {
                if (ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR.equals(temp.getBusinessType())) {
                    if (!StrUtil.isBlank(channelOnlineCondition.getCustomerManagerNew())) {
                        temp.setCustomerManager(channelOnlineCondition.getCustomerManagerNew());
                    }
                }
                if (ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR.equals(temp.getBusinessType())) {
                    if (!StrUtil.isBlank(channelOnlineCondition.getCustomerManagerOld())) {
                        temp.setCustomerManager(channelOnlineCondition.getCustomerManagerOld());
                    }
                }
            });
            channelRiskInfoService.saveOrUpdateBatch(riskInfoTempList);
        }
        log.info("==================================批量修改渠道服务区域经理结束==================================》");
        log.info("==================================同步修改案件区域经理开始==================================》");
        channelOnlineService.synchronousLlManagerToCase(channelOnlineCondition);
        log.info("==================================同步修改案件区域经理结束==================================》");
        return IResponse.success("修改成功");
    }

    @PostMapping(value = "/getByRegion")
    @ApiOperation(value = "多条件分页渠道信息列表")
    public IResponse<IPage<ChannelBaseInfoTemp>> getByRegion(@RequestBody RegionDTO condition) {
        List<String> role = SecurityUtils.getRoles();
        // 声明非直营权限
        Boolean type=false;
        // 声明直营权限
        Boolean belongType =false;
        //  这边获取我应该查询到的渠道id
        List<Long> channelIds = new ArrayList<>();
        List<ChannelBaseInfoTemp> base = new ArrayList<>();

        // 根据类型去判断我拥有的权限
        if(RoleTypeDic.NEW_CAR.equals(condition.getBusinessType())){
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP.toString())){
                type=true;
            }
            if(role.contains(ChannelRoleEnum.ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP.toString())){
                belongType=true;
            }

        }else if(RoleTypeDic.OLD_CAR.equals(condition.getBusinessType())){
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP.toString())){
                type=true;
            }
            if(role.contains(ChannelRoleEnum.ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP.toString())){
                belongType=true;
            }
        }else {
            return IResponse.fail("获取业务类型失败!");
        }
        if(type==false&&belongType==false){
            return IResponse.fail("该用户暂无权限!");
        }
        // 声明一个集合
        List<String> statusList =new ArrayList<>();
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);
        if(RoleTypeDic.NEW_CAR.equals(condition.getBusinessType())){
            if(type&&belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(type){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).notIn(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).eq(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }
        }else if(RoleTypeDic.OLD_CAR.equals(condition.getBusinessType())){
            if(type&&belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(type){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).notIn(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).eq(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }
        }
        for(ChannelBaseInfoTemp b :base){
            channelIds.add(b.getId());
        }
        if (channelIds.size() <= 0) {
            return IResponse.fail("暂无匹配合作商信息！");
        }
        // 判断传递过来的区域有多大
        List<String> codes = new ArrayList<>();
        if (condition.getRegionId() != null && condition.getRegionId() != "") {
            // 下面这个是没有用递归的  比较垃圾的算法
            if (RegionLevel.REGION_LEVEL_ONE.equals(condition.getRegionLevel().toString())) {
                List<ChannelAppertainRegion> list = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                        .lambda().eq(ChannelAppertainRegion::getLevel, "4").eq(ChannelAppertainRegion::getBusinessType,condition.getBusinessType()));
                if (list.size() > 0) {
                    for (ChannelAppertainRegion dep : list) {
                        codes.add(dep.getCode());
                    }
                }
            } else if (RegionLevel.REGION_LEVEL_TWO.equals(condition.getRegionLevel().toString())) {
                // 声明一个集合，获取所有的  自己下面 的3级 id
                List<Long> ids = new ArrayList<>();

                // 先查询出来所有的下级的id
                List<ChannelAppertainRegion> list = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                        .lambda().eq(ChannelAppertainRegion::getParentId, condition.getRegionId()).eq(ChannelAppertainRegion::getBusinessType,condition.getBusinessType()));
                if (list.size() > 0) {
                    for (ChannelAppertainRegion dep : list) {
                        ids.add(dep.getId());
                    }
                    // 再去查询 4级别的数据
                    List<ChannelAppertainRegion> reg = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                            .lambda().in(ChannelAppertainRegion::getParentId, ids).eq(ChannelAppertainRegion::getBusinessType,condition.getBusinessType()));
                    if (reg.size() > 0) {
                        for (ChannelAppertainRegion dep : reg) {
                            codes.add(dep.getCode());
                        }
                    }
                }
            } else if (RegionLevel.REGION_LEVEL_THREE.equals(condition.getRegionLevel().toString())) {
                // 先查询出来所有的下级的id
                List<ChannelAppertainRegion> list = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                        .lambda().eq(ChannelAppertainRegion::getParentId, condition.getRegionId()).eq(ChannelAppertainRegion::getBusinessType,condition.getBusinessType()));
                if (list.size() > 0) {
                    for (ChannelAppertainRegion dep : list) {
                        codes.add(dep.getCode());
                    }
                }
            } else if (RegionLevel.REGION_LEVEL_FOUR.equals(condition.getRegionLevel().toString())) {
                // 说明这边就是市级别
                ChannelAppertainRegion c = appertainRegionService.getById(condition.getRegionId());
                codes.add(c.getCode());
            } else {
                return IResponse.fail("获取区域树层级失败！");
            }
        } else {
            List<ChannelAppertainRegion> list = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                    .lambda().eq(ChannelAppertainRegion::getLevel, "4").eq(ChannelAppertainRegion::getBusinessType,condition.getBusinessType()));
            if (list.size() > 0) {
                for (ChannelAppertainRegion dep : list) {
                    codes.add(dep.getCode());
                }
            }
        }
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        if (codes.size() == 0) {
            return IResponse.fail("暂无匹配城市!");
        }
        if (condition.getUserName() != null && condition.getUserName() != "") {
            List<ChannelRiskInfoTemp> risks = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query()
                    .lambda().eq(ChannelRiskInfoTemp::getCustomerManager, condition.getUserName()).in(ChannelRiskInfoTemp::getChannelId, channelIds).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()));
            if (risks.size() > 0) {
                List<Long> cIds = new ArrayList<>();
                for (ChannelRiskInfoTemp c : risks) {
                    cIds.add(Long.valueOf(c.getChannelId()));
                }
                IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByRegion(page, condition, codes, cIds);
                return IResponse.success(channelList);
            } else {
                return IResponse.success(new ArrayList<>());
            }
        }
        IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByRegion(page, condition, codes, channelIds);
        return IResponse.success(channelList);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/batchManager")
    @ApiOperation(value = "批量修改区域经理")
    public IResponse<Boolean> batchManager(@RequestBody BatchManagerDTO dto) {
        String[] ids = dto.getIds().split(",");
        Set<Long> channelIds = new HashSet<>();
        if (ids.length > 0) {
            for (int j = 0; j < Arrays.asList(ids).size(); j++) {
                Long id = Long.valueOf(Arrays.asList(ids).get(j));
                channelIds.add(id);
            }
        }
        List<ChannelRiskInfoTemp> aa = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query()
                .lambda().eq(ChannelRiskInfoTemp::getBusinessType, dto.getBorder()).in(ChannelRiskInfoTemp::getChannelId, channelIds));
        if (aa.size() > 0) {
            aa.forEach(en -> {
                en.setCustomerManager(dto.getAreaManager());
            });
            channelRiskInfoService.updateBatchById(aa);
        }
        //  再去同步案件的数据
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("clientId", channelConfig.getCaseClientId());
        headers.put("clientSecret", channelConfig.getCaseClientSecret());
        IResponse result = channelUseCaseService.upCaseManager(dto, headers);
        if (!CommonConstants.SUCCESS.equalsIgnoreCase(result.getCode())) {
            throw new AfsBaseException("数据同步案件失败！！");
        }
        return new IResponse<Boolean>().setMsg("修改成功！");
    }

    @PostMapping(value = "/getByDevelop")
    @ApiOperation(value = "多条件分页渠道信息列表用于渠道发展")
    public IResponse<IPage<ChannelBaseInfoTemp>> getByDevelop(@RequestBody DevelopDTO condition) {
        List<String> role = SecurityUtils.getRoles();
        // 声明非直营权限
        Boolean type=false;
        // 声明直营权限
        Boolean belongType =false;

        //  这边获取我应该查询到的渠道id
        List<Long> channelIds = new ArrayList<>();
        List<ChannelBaseInfoTemp> base =new ArrayList<>();
        // 根据类型去判断我拥有的权限
        if(RoleTypeDic.NEW_CAR.equals(condition.getBusinessType())){
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP.toString())){
                type=true;
            }
            if(role.contains(ChannelRoleEnum.ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP.toString())){
                belongType=true;
            }

        }else if(RoleTypeDic.OLD_CAR.equals(condition.getBusinessType())){
            if(role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP.toString())){
                type=true;
            }
            if(role.contains(ChannelRoleEnum.ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP.toString())||role.contains(ChannelRoleEnum.ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP.toString())){
                belongType=true;
            }
        }else {
            return IResponse.fail("获取业务类型失败!");
        }
        if(type==false&&belongType==false){
            return IResponse.fail("该用户暂无权限!");
        }
        // 声明一个集合
        List<String> statusList =new ArrayList<>();
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
        statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);
        if(RoleTypeDic.NEW_CAR.equals(condition.getBusinessType())){
            if(type&&belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(type){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).notIn(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatus,statusList).eq(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }
        }else if(RoleTypeDic.OLD_CAR.equals(condition.getBusinessType())){
            if(type&&belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(type){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).notIn(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }else if(belongType){
                base=channelOnlineService.list(Wrappers.<ChannelBaseInfoTemp>query().lambda().in(ChannelBaseInfoTemp::getChannelStatusOldCar,statusList).eq(ChannelBaseInfoTemp::getChannelBelong,ChannelOnlineConstants.CHENNEL_BELONG_OWN).eq(ChannelBaseInfoTemp::getChannelType,ChannelOnlineConstants.CHANNEL_TYPE_CHANNEL));
            }
        }
        for(ChannelBaseInfoTemp b :base){
            channelIds.add(b.getId());
        }
        if (channelIds.size() <= 0) {
            return IResponse.fail("暂无匹配合作商信息！");
        }
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        // 先判断下传递过来的哪层
        if (RegionLevel.REGION_LEVEL_ONE.equals(condition.getRegionLevel()) || condition.getRegionLevel() == null|| condition.getRegionLevel()=="") {
            // 这边是点击全国或者初始化页面进这里
            if (channelIds.size() > 0) {
                if (condition.getUserName() != null && condition.getUserName() != "") {
                    List<ChannelRiskInfoTemp> risks = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query()
                            .lambda().eq(ChannelRiskInfoTemp::getCustomerManager, condition.getUserName()).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()).in(ChannelRiskInfoTemp::getChannelId, channelIds));
                    if (risks.size() > 0) {
                        List<Long> cIds = new ArrayList<>();
                        for (ChannelRiskInfoTemp c : risks) {
                            cIds.add(Long.valueOf(c.getChannelId()));
                        }
                        IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, cIds);
                        return IResponse.success(channelList);
                    } else {
                        return IResponse.success(new ArrayList<>());
                    }
                }
                IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, channelIds);
                return IResponse.success(channelList);
            } else {
                return IResponse.success(new ArrayList<>());
            }
        } else {
            if (RegionLevel.REGION_LEVEL_TWO.equals(condition.getRegionLevel())) {
                //  声明一个集合放用户名
                List<String> userName = new ArrayList<>();
                // 进入这里说明是第二层  下级有d
                List<Long> userIds = new ArrayList<>();
                this.user(userIds, condition.getRegionId());
                userIds.add(condition.getUserId());
                List<ChannelUser> cUser = userService.list(Wrappers.<ChannelUser>query().lambda().in(ChannelUser::getUserId, userIds));
                if (cUser.size() > 0) {
                    for (ChannelUser c : cUser) {
                        userName.add(c.getUsername());
                    }
                    List<Long> useId = new ArrayList<>();
                    // 通过用户名去查询  区域经理叫这个的
                    List<ChannelRiskInfoTemp> risk = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda().in(ChannelRiskInfoTemp::getCustomerManager, userName).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()));
                    if (risk.size() > 0) {
                        for (ChannelRiskInfoTemp t : risk) {
                            if (channelIds.contains(t.getChannelId())) {
                                useId.add(Long.valueOf(t.getChannelId()));
                            }
                        }
                        if (useId.size() > 0) {
                            if (condition.getUserName() != null && condition.getUserName() != "") {
                                List<ChannelRiskInfoTemp> risks = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query()
                                        .lambda().eq(ChannelRiskInfoTemp::getCustomerManager, condition.getUserName()).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()).in(ChannelRiskInfoTemp::getChannelId, useId));
                                if (risks.size() > 0) {
                                    List<Long> cIds = new ArrayList<>();
                                    for (ChannelRiskInfoTemp c : risks) {
                                        cIds.add(Long.valueOf(c.getChannelId()));
                                    }
                                    IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, cIds);
                                    return IResponse.success(channelList);
                                } else {
                                    return IResponse.success(new ArrayList<>());
                                }
                            }
                            IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, useId);
                            return IResponse.success(channelList);
                        } else {
                            return IResponse.success(new ArrayList<>());
                        }


                    }
                }

            } else {
                // 直接查传递过来的用户id
                ChannelUser user = userService.getOne(Wrappers.<ChannelUser>query().lambda().eq(ChannelUser::getUserId, condition.getUserId()));
                if (user != null) {
                    List<ChannelRiskInfoTemp> risk = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query().lambda().eq(ChannelRiskInfoTemp::getCustomerManager, user.getUsername()).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()));
                    List<Long> useId = new ArrayList<>();
                    if (risk.size() > 0) {
                        for (ChannelRiskInfoTemp t : risk) {
                            if (channelIds.contains(t.getChannelId())) {
                                useId.add(Long.valueOf(t.getChannelId()));
                            }
                        }
                        if (useId.size() > 0) {
                            if (condition.getUserName() != null && condition.getUserName() != "") {
                                List<ChannelRiskInfoTemp> risks = channelRiskInfoService.list(Wrappers.<ChannelRiskInfoTemp>query()
                                        .lambda().eq(ChannelRiskInfoTemp::getCustomerManager, condition.getUserName()).in(ChannelRiskInfoTemp::getChannelId, useId).eq(ChannelRiskInfoTemp::getBusinessType,condition.getBusinessType()));
                                if (risks.size() > 0) {
                                    List<Long> cIds = new ArrayList<>();
                                    for (ChannelRiskInfoTemp c : risks) {
                                        cIds.add(Long.valueOf(c.getChannelId()));
                                    }
                                    IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, cIds);
                                    return IResponse.success(channelList);
                                } else {
                                    return IResponse.success(new ArrayList<>());
                                }
                            }
                            IPage<List<ChannelOnlineInfoVo>> channelList = channelOnlineService.getByDevelop(page, condition, useId);
                            return IResponse.success(channelList);
                        } else {
                            return IResponse.success(new ArrayList<>());
                        }
                    }
                }
            }
        }

        return IResponse.success(new ArrayList<>());
    }

    public void user(List<Long> allUserIds, String parentId) {
        // 说明展示的第三层，那么 level 就是 3和4的
        List<Long> threeRegion = new ArrayList<>();
        Set<Long> allIds = new HashSet<>();
        // 这个是查出来所有的归属第二级的第三级数据
        List<ChannelAppertainRegion> li = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                .lambda().eq(ChannelAppertainRegion::getParentId, parentId));
        if (li.size() > 0) {
            for (ChannelAppertainRegion l : li) {
                threeRegion.add(l.getId());
                allIds.add(l.getId());
            }
            List<ChannelAppertainRegion> fe = appertainRegionService.list(Wrappers.<ChannelAppertainRegion>query()
                    .lambda().in(ChannelAppertainRegion::getParentId, threeRegion));
            if (fe.size() > 0) {
                for (ChannelAppertainRegion f : fe) {
                    allIds.add(f.getId());
                }
            }
            // 查询出来用户的id
            if (allIds.size() > 0) {
                List<ChannelUserAppertain> appertain = userAppertainService.list(Wrappers.<ChannelUserAppertain>query().lambda().in(ChannelUserAppertain::getAppertainRegionId, allIds));
                if (appertain.size() > 0) {
                    for (ChannelUserAppertain u : appertain) {
                        allUserIds.add(u.getUserId());
                    }
                }
            }

        }
    }

    @RequestMapping(value = "/afterArchive", method = RequestMethod.POST)
    @ApiOperation(value = "贷后归档mq消息推送")
    @SysLog("贷后归档mq消息推送")
    @Transactional(rollbackFor = Exception.class)
    public IResponse afterArchive(@RequestBody ChannelOnlineCondition channelOnlineCondition) {
        log.info("==================================归档mq消息推送开始==================================》");

        log.info("==================================归档mq消息推送结束==================================》");
        return IResponse.success("推送成功");
    }

    @RequestMapping(value = "/getInfoBySocUniCrtCode/{socUniCrtCode}", method = RequestMethod.POST)
    @ApiOperation(value = "根据统一社会信用代码获取基本信息")
    public IResponse getInfoBySocUniCrtCode(@PathVariable String socUniCrtCode) {
        ChannelBaseInfoTemp temp = channelOnlineService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getSocUniCrtCode, socUniCrtCode));
        if (temp != null) {
            //新车或者二手车状态为启用状态情况下处理这种情况
            if (ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO.equals(temp.getChannelStatus()) || ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO.equals(temp.getChannelStatusOldCar())) {
                return IResponse.success(temp);
            }
        } else {
            return IResponse.success(false);
        }
        return IResponse.success(false);
    }

    @PostMapping(value = "/getChannelBaseInfoByCode")
    @ApiOperation(value = "根据社会统一信用代码合作商名称和上线日期判断合作商是否存在")
    public IResponse getChannelBaseInfoByCode(@RequestBody ChannelBaseInfoTemp channel) {
        ChannelBaseInfoTemp temp = channelOnlineService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getSocUniCrtCode, channel.getSocUniCrtCode()).eq(ChannelBaseInfoTemp::getChannelFullName,channel.getChannelFullName()));
        log.info("合作商上线输入社会统一信用代码。自动带出来数据====="+channel);
        if (temp != null) {
            List<String> statusList =new ArrayList<>();
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ONE);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_FIVE);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SIX);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_SEVEN);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_EIGHT);
            statusList.add(ChannelOnlineConstants.VALUE_CHANNEL_STATUS_NINE);
            if(!temp.getChannelBelong().equals(channel.getChannelBelong())){
                return IResponse.success(false);
            }
            if(statusList.contains(temp.getChannelStatus())&&statusList.contains(temp.getChannelStatusOldCar())){
                return IResponse.success(false);
            }else {
                if(!temp.getChannelBelong().equals(channel.getChannelBelong())){
                    return IResponse.success(false);
                }
                // 这个地方还是要再看下当前用户。是否有权限处理
                // 拿我的角色看看我是属于新车权限还是二手车权限
                Boolean newCarPower=false;
                Boolean oldCarPower=false;
                List<String> role =SecurityUtils.getRoles();
                // 看看合作商的类型：直营或者非直营
                if("01".equals(channel.getChannelBelong())){
                    // 说明是直营
                    if(role.contains(ChannelRoleEnum.ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP.name())){
                        // 说明有直营新车
                        newCarPower=true;
                    }
                    if(role.contains(ChannelRoleEnum.ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP.name())){
                        // 说明是直营二手车
                        oldCarPower=true;
                    }
                }else {
                    // 说明是非直营
                    if(role.contains(ChannelRoleEnum.ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP.name())){
                        // 说明有非直营新车
                        newCarPower=true;
                    }
                    if(role.contains(ChannelRoleEnum.ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP.name())){
                        // 说明是非直营二手车
                        oldCarPower=true;
                    }
                }

                // 这个地方再去校验这个人是单一类型管理
                if(newCarPower==true&&oldCarPower==false){
                    // 说明这个人只有新车权限，那么需要去处理新车的当前状态
                    if(statusList.contains(temp.getChannelStatus())){
                        return IResponse.success(false);
                    }
                }
                // 说明这个人只有二手车权限。那么需要去处理二手车当前状态
                if(oldCarPower==true&&newCarPower==false){
                    // 说明这个人只有新车权限，那么需要去处理新车的当前状态
                    if(statusList.contains(temp.getChannelStatusOldCar())){
                        return IResponse.success(false);
                    }
                }

                return IResponse.success(temp);
            }
        } else {
            return IResponse.success(true);
        }
    }

    /**
     * 校验当前合作商信息是否可以修改
     * @param temp
     * @return
     */
    public Boolean checkChannelSave(ChannelBaseInfoTemp temp){
        ChannelBaseInfoTemp baseInfoTemp =channelOnlineService.getById(temp.getId());
        if(ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR.equals(temp.getBusinessType())){
            if(baseInfoTemp.getChannelStatus()!=null&&baseInfoTemp.getChannelStatus()!=""){
                // 说明是新车
                if(baseInfoTemp.getChannelStatus()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO||baseInfoTemp.getChannelStatus()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE){
                    return true;
                }
            }
        }
        if(ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR.equals(temp.getBusinessType())){
            if(baseInfoTemp.getChannelStatusOldCar()!=null&&baseInfoTemp.getChannelStatusOldCar()!=""){
                // 说明是二手车
                if(baseInfoTemp.getChannelStatusOldCar()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO||baseInfoTemp.getChannelStatusOldCar()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE){
                    return true;
                }
            }
        }
        if(ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_ONE.equals(temp.getBusinessType())||ChannelOnlineConstants.BUSINESS_TYPE_ALL_CAR_TWO.equals(temp.getBusinessType())){
            if(baseInfoTemp.getChannelStatusOldCar()!=null&&baseInfoTemp.getChannelStatusOldCar()!=""&&baseInfoTemp.getChannelStatus()!=null&&baseInfoTemp.getChannelStatus()!=""){
                if(baseInfoTemp.getChannelStatus()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO||baseInfoTemp.getChannelStatus()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE||baseInfoTemp.getChannelStatusOldCar()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_TWO||baseInfoTemp.getChannelStatusOldCar()!=ChannelOnlineConstants.VALUE_CHANNEL_STATUS_THREE){
                    return true;
                }
            }

        }
        return  false;
    }

    @PostMapping(value = "/getAllChannelAccountTemp")
    @ApiOperation(value = "获取这个渠道的所有收款账号")
    public IResponse getAllChannelAccountTemp(@RequestBody ChannelBaseInfoTemp channelId) {
        List<ChannelReceivablesAccountTemp> tempList =channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda().eq(ChannelReceivablesAccountTemp::getChannelId,channelId.getId().toString()));
        return IResponse.success(tempList);
    }

    @PostMapping(value = "/synchroApply")
    @ApiOperation(value = "一件同步合作商进件状态")
    public IResponse synchroApply() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("clientId", channelConfig.getApplyAdminClientId());
        headers.put("clientSecret", channelConfig.getApplyAdminClientSecret());
        IResponse result = applyFeignService.getManageUser( headers);
        if (!CommonConstants.SUCCESS.equals(result.getCode())) {
            throw new AfsBaseException("获取进件管理员状态失败！");
        } else {
            String u = JSON.toJSONString(result.getData());
            if (u != null && u != "" && u != "null") {
               List<ChannelApplyPowerVo> list = JSONArray.parseArray(u, ChannelApplyPowerVo.class);
               if(list.size()>0){
                   // 查询合作商信息
                   List<ChannelBaseInfoTemp> baseInfoTempList =channelOnlineService.list();
                   for(ChannelApplyPowerVo vo:list){
                       for(ChannelBaseInfoTemp temp:baseInfoTempList){
                           if(vo.getChannelId().toString().equals(temp.getId().toString())){
                               // 说明找到匹配的合作商权限了
                               if(vo.getNewApplyPower()!=null&&vo.getNewApplyPower()!=""){
                                   temp.setNewApplyPower(vo.getNewApplyPower());
                               }
                               if(vo.getOdlApplyPower()!=null&&vo.getOdlApplyPower()!=""){
                                  temp.setOdlApplyPower(vo.getOdlApplyPower());
                               }
                               break;
                           }
                       }
                   }
                   channelOnlineService.updateBatchById(baseInfoTempList);
               }
            }
        }

        return  IResponse.success(true);
    }

    @PostMapping(value = "/getChannelReportsByCondition")
    @ApiOperation(value = "多条件获取渠道信息报表")
    public IResponse getQuotaReportsByCondition(@RequestBody ChannelOnlineCondition condition) {
        condition.setPageNumber(0);
        condition.setPageSize(0);
        //展业区域表中区别省份和城市用的
        String provinceFlag = "1";
        String cityFlag = "0";
        //获取当前年月,算月均合同量的时候使用
        Calendar nowDate = Calendar.getInstance();
        int nowYear = nowDate.get(Calendar.YEAR);
        int nowMonth = nowDate.get(Calendar.MONTH);
        int nowDay = nowDate.get(Calendar.DAY_OF_MONTH);
        //地址库
        List<AddrQueryDto> addressParams = service.getAllLocationInfo();
        List<ChannelOnlineInfoVo> baseInfoTempList = channelOnlineService.getByConditionDisablePage(condition);
        List<Long> ids = new ArrayList<>();
        List<String> channelCodeList = new ArrayList<>();
        List<ChannelReportVo> channelReportVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(baseInfoTempList)) {
            baseInfoTempList.forEach(base -> {
                ids.add(base.getId());
                channelCodeList.add(base.getChannelCode());
                ChannelMainBrand brand = channelMainBrandService.getOne(Wrappers.<ChannelMainBrand>query().lambda()
                        .eq(ChannelMainBrand::getChannelId, base.getId())
                        .eq(ChannelMainBrand::getCarLevel, ChannelOnlineConstants.CAR_LEVEL)
                        .eq(ChannelMainBrand::getIndeterminate, "false"));
                if (brand != null) {
                    base.setMainBrand(ChannelOnlineConstants.ALL_BRAND);
                }
                //翻译注册地址 办公地址
                addressParams.forEach(address -> {
                    if (address.getValue().equals(base.getChannelProvince())) {
                        base.setChannelProvince(address.getLabel());
                    }
                    if (address.getValue().equals(base.getChannelCity())) {
                        base.setChannelCity(address.getLabel());
                    }
                    if (address.getValue().equals(base.getOfficeProvince())) {
                        base.setOfficeProvince(address.getLabel());
                    }
                    if (address.getValue().equals(base.getOfficeCity())) {
                        base.setOfficeCity(address.getLabel());
                    }
                });
                base.setChannelAddress((base.getChannelProvince() + base.getChannelCity() + base.getChannelAddress()).replaceAll(",","，").replaceAll("null",""));
                base.setOfficeAddress((base.getOfficeProvince() + base.getOfficeCity() + base.getOfficeAddress()).replaceAll(",","，").replaceAll("null",""));
                //资质等级AA,AA 变为AA
                if (StrUtil.isNotBlank(base.getQualityGrade())) {
                    String[] split = base.getQualityGrade().split(",");
                    base.setQualityGrade(split[0]);
                }
            });
        } else {
            return IResponse.success(null);
        }
        //展业地区
        List<ChannelAuthorizeRegionTemp> regionTemps = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegionTemp>query().lambda()
                .in(ChannelAuthorizeRegionTemp::getChannelId, ids)
        );
        //见证人
        List<ChannelWitnessInfoTemp> witnessInfoTempList = channelWitnessInfoTempService.list(Wrappers.<ChannelWitnessInfoTemp>query().lambda()
                .in(ChannelWitnessInfoTemp::getChannelId, ids)
                .eq(ChannelWitnessInfoTemp::getStatus, ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO)
        );
        //退网时间
        List<ChannelNetwork> channelNetworkList = channelNetworkService.list(Wrappers.<ChannelNetwork>query().lambda()
                .in(ChannelNetwork::getParentId, ids)
                .eq(ChannelNetwork::getChangeStatus, ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO)
        );
        //合同数量
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("clientId", channelConfig.getCaseClientId());
        headers.put("clientSecret", channelConfig.getCaseClientSecret());
        ChannelOnlineCondition onlineCondition = new ChannelOnlineCondition();
        onlineCondition.setChannelCodeList(channelCodeList);
        List<ChannelContractNumberVO> contractNumberVOList = channelUseCaseService.getContractNumberByChannelCode(onlineCondition, headers).getData();
        baseInfoTempList.forEach(base -> {
            ChannelReportVo vo = new ChannelReportVo();
            BeanUtils.copyProperties(base, vo);
            List<String> provinceCodes = new ArrayList<>();
            List<String> cityCodes = new ArrayList<>();
            List<String> provinceCodesOldCar = new ArrayList<>();
            List<String> cityCodesOldCar = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(regionTemps)) {
                regionTemps.forEach(regionTemp -> {
                    if (base.getId().equals(regionTemp.getChannelId())) {
                        //查新车省份
                        if (provinceFlag.equals(regionTemp.getParentId())) {
                            addressParams.forEach(address -> {
                                if (address.getValue().equals(regionTemp.getCode())) {
                                    provinceCodes.add(address.getLabel());
                                    return;
                                }
                            });
                        } else if (cityFlag.equals(regionTemp.getIsParent())) {
                            addressParams.forEach(address -> {
                                if (regionTemp.getCode().equals(address.getValue())) {
                                    cityCodes.add(address.getLabel());
                                    return;
                                }
                            });
                        }
                    }
                });
                vo.setNewCarCityNumber(cityCodes.size());
                vo.setOldCarCityNumber(cityCodesOldCar.size());
                vo.setNewCarCity(cityCodes.toString().replace(",", "、").replace("[", "").replace("]", ""));
                vo.setNewCarProvince(provinceCodes.toString().replace(",", "、").replace("[", "").replace("]", ""));
                vo.setOldCarCity(cityCodesOldCar.toString().replace(",", "、").replace("[", "").replace("]", ""));
                vo.setOldCarProvince(provinceCodesOldCar.toString().replace(",", "、").replace("[", "").replace("]", ""));
            }
            //见证人数量
            if (CollectionUtil.isNotEmpty(witnessInfoTempList)) {
                witnessInfoTempList.forEach(witness -> {
                    if (base.getId().equals(witness.getChannelId())) {
                        if (BUSINESS_ATTRIBUTES_NEW.equals(witness.getBusinessAttributes())) {
                            vo.setNewCarWitnessNumber(vo.getNewCarWitnessNumber() + 1);
                        } else {
                            vo.setOldCarWitnessNumber(vo.getOldCarWitnessNumber() + 1);
                        }
                    }
                });
            }
            //退网时间
            if (CollectionUtil.isNotEmpty(channelNetworkList)) {
                channelNetworkList.forEach(network -> {
                    if (base.getId().equals(network.getParentId())) {
                        if (ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR.equals(network.getBusinessType())) {
                            vo.setNewCarExitTime(network.getUpdateTime());
                        } else {
                            vo.setOldCarExitTime(network.getUpdateTime());
                        }
                    }
                });
            }
            //合同量查询
            if (CollectionUtil.isNotEmpty(contractNumberVOList)) {
                contractNumberVOList.forEach(contractNumberVO -> {
                    if (base.getChannelCode().equals(contractNumberVO.getChannelCode())) {
                        vo.setContractNum(contractNumberVO.getContractNum());
                        //算月均
                        Calendar onlineDate = Calendar.getInstance();
                        if(ObjectUtils.isNotEmpty(base.getOnlineDate())) {
                            onlineDate.setTime(base.getOnlineDate());
                            if (onlineDate.before(nowDate)) {
                                onlineDate.setTime(base.getOnlineDate());
                                int onlineYear = onlineDate.get(Calendar.YEAR);
                                int onlineMonth = onlineDate.get(Calendar.MONTH);
                                int onlineDay = onlineDate.get(Calendar.DAY_OF_MONTH);

                                // 获取年的差值
                                int yearInterval = nowYear - onlineYear;
                                // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
                                if (nowMonth < onlineMonth || nowMonth == onlineMonth && nowDay < onlineDay) {
                                    yearInterval--;
                                }
                                // 获取月数差值
                                int monthInterval = (nowMonth + 12) - onlineMonth;
                                if (nowDay < onlineDay) {
                                    monthInterval--;
                                }
                                monthInterval %= 12;
                                int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
                                vo.setMonthlyContractNum(String.valueOf(Math.round(vo.getContractNum() / (monthsDiff == 0 ? 1 : monthsDiff))));
                            } else {
                                vo.setMonthlyContractNum("合作商上线时间有误，无法计算月均合同数");
                            }
                        }else {
                            vo.setMonthlyContractNum("合作商未上线");
                        }
                    }
                });
            }
            channelReportVoList.add(vo);
        });
        return IResponse.success(channelReportVoList);
    }

    @PostMapping("/getChannelBaseInfo")
    @ApiOperation(value = "获取联合方信息")
    @SysLog("获取联合方信息")
    public IResponse getChannelBaseInfo(@RequestBody ChannelJointPartyInfoDto channelJointPartyInfo) {
        //获取全部联合方
        IPage<ChannelJointPartyInfo> channelBaseInfoTemps =jointPartyInfoService.page(new Page(channelJointPartyInfo.getPageNumber(), channelJointPartyInfo.getPageSize())
                ,Wrappers.<ChannelJointPartyInfo>query().lambda()
                        .like(ChannelJointPartyInfo::getJointPartyCode,
                                channelJointPartyInfo.getChannelJointPartyInfo().getJointPartyCode()!=null?channelJointPartyInfo.getChannelJointPartyInfo().getJointPartyCode():"")
                        .like(ChannelJointPartyInfo::getJointPartyName,
                                channelJointPartyInfo.getChannelJointPartyInfo().getJointPartyFullName()!=null?channelJointPartyInfo.getChannelJointPartyInfo().getJointPartyFullName():""));
        return IResponse.success(channelBaseInfoTemps);
    }

    @PostMapping("/getChannelBaseInfoJoint")
    @ApiOperation(value = "查询联合方下的合作商信息")
    @SysLog("查询联合方下的合作商信息")
    public IResponse getChannelBaseInfoJoint(@RequestBody ChannelJointPartyInfoDto channelJointPartyInfo) {
        //获取全部联合方code
        List<ChannelBaseJointPartyRel> channelBaseJointPartyRels =channelJointPartyRelationService.list(Wrappers.<ChannelBaseJointPartyRel>query().lambda()
                .eq(ChannelBaseJointPartyRel::getJointPartyCode,channelJointPartyInfo.getChannelJointPartyInfo().getJointPartyCode()));

        List<String> channelCode=new ArrayList<>();

        for(ChannelBaseJointPartyRel channelBaseJointPartyRel:channelBaseJointPartyRels){
            channelCode.add(channelBaseJointPartyRel.getChannelCode());
        }

        if(channelCode.size()==0){
            return IResponse.success("");
        }

        IPage<ChannelBaseInfoTemp> channelBaseInfoTemps =channelBaseInfoTempService.page(new Page(channelJointPartyInfo.getPageNumber(), channelJointPartyInfo.getPageSize())
                ,Wrappers.<ChannelBaseInfoTemp>query().lambda()
                .in(ChannelBaseInfoTemp::getChannelCode,channelCode)
                        .like(ChannelBaseInfoTemp::getChannelCode,channelJointPartyInfo.getChannelBaseInfoTemp().getChannelCode()!=null?channelJointPartyInfo.getChannelBaseInfoTemp().getChannelCode():"")
                        .like(ChannelBaseInfoTemp::getChannelFullName,channelJointPartyInfo.getChannelBaseInfoTemp().getChannelFullName()!=null?channelJointPartyInfo.getChannelBaseInfoTemp().getChannelFullName():""));

        return IResponse.success(channelBaseInfoTemps);
    }

    @PostMapping("/getChannelBaseInfoJointNo")
    @ApiOperation(value = "获取未分配联合方的合作商信息")
    @SysLog("获取未分配联合方的合作商信息")
    public IResponse getChannelBaseInfoJointNo(@RequestBody ChannelJointPartyInfoDto channelJointPartyInfo) {
        //获取全部联合方
        List<ChannelBaseJointPartyRel> channelBaseJointPartyRels =channelJointPartyRelationService.list(Wrappers.<ChannelBaseJointPartyRel>query().lambda());
        List<String> strings =new ArrayList<>();
        for (ChannelBaseJointPartyRel channelBaseJointParty:channelBaseJointPartyRels){
            strings.add(channelBaseJointParty.getChannelCode());
        }
        IPage<ChannelBaseInfoTemp> channelBaseInfoTemps =channelBaseInfoTempService.page(new Page(channelJointPartyInfo.getPageNumber(), channelJointPartyInfo.getPageSize()),Wrappers.<ChannelBaseInfoTemp>query().lambda()
                .notIn(CollUtil.isNotEmpty(strings),ChannelBaseInfoTemp::getChannelCode,strings)
                .like(ChannelBaseInfoTemp::getChannelCode,channelJointPartyInfo.getChannelBaseInfoTemp().getChannelCode()!=null?channelJointPartyInfo.getChannelBaseInfoTemp().getChannelCode():"")
                .like(ChannelBaseInfoTemp::getChannelFullName,channelJointPartyInfo.getChannelBaseInfoTemp().getChannelFullName()!=null?channelJointPartyInfo.getChannelBaseInfoTemp().getChannelFullName():""));
        return IResponse.success(channelBaseInfoTemps);
    }
    @PostMapping("/getChannelBaseInfoJointAdd")
    @ApiOperation(value = "绑定联合方合作商信息")
    @SysLog("绑定联合方合作商信息")
    public IResponse getChannelBaseInfoJointAdd(@RequestBody List<ChannelBaseJointPartyRel> channelBaseJointPartyRel) {
        List<String> strings =new ArrayList<>();
        for (ChannelBaseJointPartyRel channelBaseJointPartyRel1:channelBaseJointPartyRel){
            strings.add(channelBaseJointPartyRel1.getChannelCode());
        }
        //获取全部联合方
        List<ChannelBaseJointPartyRel> channelBaseJointPartyRels = channelJointPartyRelationService
                .list(Wrappers.<ChannelBaseJointPartyRel>query().lambda()
                .in(ChannelBaseJointPartyRel::getChannelCode,strings));
        if(channelBaseJointPartyRels.size()>0){
            return IResponse.fail("联合方信息已绑定，请不要重复绑定");
        }
        if(!channelJointPartyRelationService.saveBatch(channelBaseJointPartyRel)){
            return IResponse.fail("绑定合作商信息失败");
        }
        return IResponse.success("绑定合作商信息成功");
    }
    @PostMapping("/getChannelJointPartyInfoAdd")
    @ApiOperation(value = "添加联合方信息")
    @SysLog("添加联合方信息")
    public IResponse getChannelJointPartyInfoAdd(@RequestBody ChannelJointPartyInfo channelJointPartyInfo) {
        List<ChannelJointPartyInfo> channelBaseJointPartyRels =jointPartyInfoService.list(Wrappers.<ChannelJointPartyInfo>query().lambda()
                .eq(ChannelJointPartyInfo::getJointPartyCode,channelJointPartyInfo.getJointPartyCode()));
        if(channelBaseJointPartyRels.size()>0){
            return IResponse.fail("添加失败,联合方代码不能重复");
        }
        if(!jointPartyInfoService.save(channelJointPartyInfo)){
            return IResponse.fail("添加失败");
        }
        return IResponse.success("联合方添加成功！");
    }

    @PostMapping("/channelJointPartyInfoSave")
    @ApiOperation(value = "修改联合方信息")
    @SysLog("修改联合方信息")
    public IResponse channelJointPartyInfoSave(@RequestBody ChannelJointPartyInfo channelJointPartyInfo) {
        ChannelJointPartyInfo oldData =jointPartyInfoService.getOne(Wrappers.<ChannelJointPartyInfo>query().lambda()
                .eq(ChannelJointPartyInfo::getId,channelJointPartyInfo.getId()));

        if (!oldData.getJointPartyCode().equals(channelJointPartyInfo.getJointPartyCode())){
            List<ChannelJointPartyInfo> channelBaseJointPartyRels =jointPartyInfoService.list(Wrappers.<ChannelJointPartyInfo>query().lambda()
                    .eq(ChannelJointPartyInfo::getJointPartyCode,channelJointPartyInfo.getJointPartyCode()));
            if (channelBaseJointPartyRels.size() > 0){
                return IResponse.fail("修改失败,联合方代码不能重复");
            }
        }
        //替换ID
        channelJointPartyInfo.setId(oldData.getId());
        //获取创建人
        channelJointPartyInfo.setUpdateBy(SecurityUtils.getUser().getUsername());
        channelJointPartyInfo.setUpdateTime(new Date());
        if(!jointPartyInfoService.updateById(channelJointPartyInfo)){
            return IResponse.fail("修改失败");
        }
        return IResponse.success("联合方修改成功！");
    }

    @PostMapping("/deleteChannelJointPartyInfo")
    @ApiOperation(value = "删除联合方信息")
    @SysLog("删除联合方信息")
    public IResponse deleteChannelJointPartyInfo(@RequestBody ChannelBaseInfoTemp channelBaseInfoTemp) {
        ChannelBaseJointPartyRel channelBaseJointPartyRels =channelJointPartyRelationService.getOne(Wrappers.<ChannelBaseJointPartyRel>query().lambda()
                .eq(ChannelBaseJointPartyRel::getChannelCode,channelBaseInfoTemp.getChannelCode()));
        if(channelBaseJointPartyRels.getId()==null){
            return IResponse.fail("未找到合作商信息");
        }        //获取全部联合方
        if(!channelJointPartyRelationService.removeById(channelBaseJointPartyRels.getId())){
            return IResponse.fail("删除失败");
        }
        return IResponse.success("合作商删除成功！");
    }

    private static boolean checkEquals(Long oldValue, Long newValue){
        if(oldValue==null&&newValue==null){
            return true;
        }
        if(newValue!=null){
            return  newValue.equals(oldValue);
        }
        return  oldValue.equals(newValue);
    }

    @PostMapping("/queryBaseInfoByCreditCode/{id}")
    public IResponse queryBaseInfoByCreditCode(@PathVariable("id") String id) {
        List<ChannelBaseInfoTemp> list = channelBaseInfoTempService.list(Wrappers.<ChannelBaseInfoTemp>lambdaQuery()
                .eq(ChannelBaseInfoTemp::getSocUniCrtCode, id)
                .eq(ChannelBaseInfoTemp::getChannelStatus, "00"));
        ChannelBaseInfoTemp infoTemp = new ChannelBaseInfoTemp();
        if (CollectionUtil.isNotEmpty(list)) {
            ChannelBaseInfoTemp baseInfoTemp = list.get(0);
            infoTemp.setChannelFullName(baseInfoTemp.getChannelFullName());
            infoTemp.setChannelAbbreviationName(baseInfoTemp.getChannelAbbreviationName());
            infoTemp.setDealerValidityPeriod(baseInfoTemp.getDealerValidityPeriod());
            infoTemp.setOnlineDate(baseInfoTemp.getOnlineDate());
            infoTemp.setLegalPerson(baseInfoTemp.getLegalPerson());
            infoTemp.setLegalPersonIdCard(baseInfoTemp.getLegalPersonIdCard());
            infoTemp.setLegalPersonTel(baseInfoTemp.getLegalPersonTel());
            return IResponse.success(infoTemp);
        }
        return IResponse.success(null);
    }

    /**
     * 查询所有上线的合作商进行数据同步
     * @return
     */
    @PostMapping("/findAllChannelBaseInfo")
    public IResponse<JSONObject> findAllChannelBaseInfo() {
        return channelOnlineService.findAllChannelBaseInfo();
    }

    @GetMapping(value = "/queryApproveMaintainBasicUpd/{id}")
    @ApiOperation(value = "根据ID查询渠道维护临时提交审批基本信息")
    public IResponse<ChannelBaseInfoTempUpd> queryApproveMaintainBasicUpd(@PathVariable("id") Long id) {
        ChannelBaseInfoTempUpd channelBaseInfo = channelOnlineUpdService.getById(id);
        return IResponse.success(channelBaseInfo);
    }

    @PostMapping(value = "/submitFlow")
    @ApiOperation(value = "维护提交")
    public IResponse testStartFlow(@RequestBody ChannelMaintainApproveSubmitVo condition) {
        channelOnlineUpdService.submitWorkFlow(condition);
        return IResponse.success(true);
    }

    @RequestMapping(value = "/getInfoUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据ID查询渠道维护临时提交审批基本信息")
    public IResponse<List<ChannelBaseInfoTempUpd>> getInfoUpdById(@PathVariable String id) {
        List<ChannelBaseInfoTempUpd> list = channelOnlineUpdService.list(Wrappers.<ChannelBaseInfoTempUpd>query().lambda().eq(ChannelBaseInfoTempUpd::getId, id));
        ChannelBaseInfoTemp temp = channelBaseInfoTempService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda().eq(ChannelBaseInfoTemp::getId,list.get(0).getId()));
        list.stream().forEach(channelBaseInfoTemp -> {
                    SubjectInfo  subjectInfo =businessSubjectInfoService.getInfoById(channelBaseInfoTemp.getSubjectAttributionId());
                    if(subjectInfo!=null){
                        channelBaseInfoTemp.setSubjectAttributionTitle(subjectInfo.getSubjectName());
                    }
                    channelBaseInfoTemp.setRetentionOfwitnesses(temp.getRetentionOfwitnesses());
                }
        );
        return new IResponse<List<ChannelBaseInfoTempUpd>>().setData(list);
    }

    @RequestMapping(value = "/getRiskUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取维护临时提交审批风控信息")
    public IResponse<List<ChannelRiskInfoTempUpd>> getRiskUpdById(@PathVariable String id) {
        List<ChannelRiskInfoTempUpd> list = channelRiskInfoUpdService.list(Wrappers.<ChannelRiskInfoTempUpd>query().lambda().eq(ChannelRiskInfoTempUpd::getChannelId, id));
        return new IResponse<List<ChannelRiskInfoTempUpd>>().setData(list);
    }

    @RequestMapping(value = "/getQuotaInfoUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取维护临时提交审批保证金信息")
    public IResponse<List<ChannelQuotaInfoTempUpd>> getQuotaInfoUpdById(@PathVariable String id) {
        List<ChannelQuotaInfoTempUpd> list = channelQuotaInfoUpdService.list(Wrappers.<ChannelQuotaInfoTempUpd>query().lambda().eq(ChannelQuotaInfoTempUpd::getChannelId, id));
        return new IResponse<List<ChannelQuotaInfoTempUpd>>().setData(list);
    }

    @RequestMapping(value = "/getNewVehicleUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取维护临时提交审批新车授权车型信息")
    public IResponse<List<ChannelAuthorizeVehicleTempUpd>> getNewVehicleUpdById(@PathVariable String id) {
        List<ChannelAuthorizeVehicleTempUpd> list = channelAuthorizeVehicleTempUpdService
                .list(Wrappers.<ChannelAuthorizeVehicleTempUpd>query().lambda()
                .eq(ChannelAuthorizeVehicleTempUpd::getChannelId, id)
                .eq(ChannelAuthorizeVehicleTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_NEW_CAR));
        return new IResponse<List<ChannelAuthorizeVehicleTempUpd>>().setData(list);
    }

    @RequestMapping(value = "/getOldVehicleUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取维护临时提交审批二手车授权车型信息")
    public IResponse<List<ChannelAuthorizeVehicleTempUpd>> getOldVehicleUpdById(@PathVariable String id) {
        List<ChannelAuthorizeVehicleTempUpd> list = channelAuthorizeVehicleTempUpdService.list(Wrappers.<ChannelAuthorizeVehicleTempUpd>query().lambda()
                .eq(ChannelAuthorizeVehicleTempUpd::getChannelId, id)
                .eq(ChannelAuthorizeVehicleTempUpd::getBusinessType, ChannelOnlineConstants.BUSINESS_TYPE_OLD_CAR));
        return new IResponse<List<ChannelAuthorizeVehicleTempUpd>>().setData(list);
    }

    @RequestMapping(value = "/getMainAreaUpdById/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "根据渠道id获取授权区域信息")
    public IResponse<List<ChannelAuthorizeRegionTempUpd>> getMainAreaUpdById(@PathVariable String id) {
        List<ChannelAuthorizeRegionTempUpd> list = channelAuthorizeRegionUpdService
                .list(Wrappers.<ChannelAuthorizeRegionTempUpd>query().lambda().eq(ChannelAuthorizeRegionTempUpd::getChannelId, id));
        return new IResponse<List<ChannelAuthorizeRegionTempUpd>>().setData(list);
    }

    @GetMapping("/getChannelSaleNets")
    @ApiOperation(value = "根据channelId获取经销商销售网络")
    public List<String> getChannelSaleNets(@RequestParam("channelId") Long channelId){
        ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(channelId);
        return channel.getSalesNetworkLists();
    }
    @RequestMapping(value = "/ableSubmitOrderAuth", method = RequestMethod.POST)
    @ApiOperation(value = "启用禁用提单权限")
    public IResponse ableSubmitOrderAuth(@RequestBody ChannelAbleIdsVo vo) {
        for(String id : vo.getIds()){
            ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(id);
            if(channel == null){
                return IResponse.success(false);
            }
            if(channel.getSubmitOrderAuth() == null || "1".equals(channel.getSubmitOrderAuth())){
                channel.setSubmitOrderAuth("0");
            }else{
                channel.setSubmitOrderAuth("1");
            }
            channelBaseInfoTempService.updateById(channel);
        }
        return IResponse.success(true);
    }


    @RequestMapping(value = "/ableLoanAuth", method = RequestMethod.POST)
    @ApiOperation(value = "启用禁用放款权限")
    public IResponse ableLoanAuth(@RequestBody ChannelAbleIdsVo vo) {
        for(String id : vo.getIds()){
            ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(id);
            if(channel == null){
                return IResponse.success(false);
            }
            if(channel.getLoanAuth() == null || "1".equals(channel.getLoanAuth())){
                channel.setLoanAuth("0");
            }else{
                channel.setLoanAuth("1");
            }
            channelBaseInfoTempService.updateById(channel);
        }
        return IResponse.success(true);
    }

    @GetMapping("/getChannelSubmitOrderAuth")
    @ApiOperation(value = "根据channelId获取经销商提单权限")
    public IResponse<String> getChannelBaseInfo(@RequestParam("channelId") String channelId){
        ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(channelId);
        return IResponse.success(channel.getSubmitOrderAuth());
    }

    @GetMapping("/getThreeProtocolNo")
    @ApiOperation(value = "根据channelId获取经销商三方协议档案号")
    public IResponse<String> getThreeProtocolNo(@RequestParam("channelId") String channelId){
        ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(channelId);
        return IResponse.success(ObjectUtils.isNotEmpty(channel) ? channel.getThreeProtocolNo() : "");
    }

    @GetMapping("/getChannelLoanAuth")
    @ApiOperation(value = "根据channelId获取经销商放款权限")
    public IResponse<String> getChannelLoanAuth(@RequestParam("channelId") String channelId){
        ChannelBaseInfoTemp channel = channelBaseInfoTempService.getById(channelId);
        return IResponse.success(channel.getLoanAuth());
    }

    /**
     * 根据channelId获取服务费收款账号信息
     */
    @GetMapping("/getWriteOffAccount")
    @ApiOperation(value = "获取服务费收款账号信息")
    public IResponse<ChannelReceivablesAccountTemp> getAccountInfo(@RequestParam("channelId") Long channelId) {
        List<ChannelReceivablesAccountTemp> list = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccountTemp>query().lambda()
                .eq(ChannelReceivablesAccountTemp::getChannelId, channelId)
                .eq(ChannelReceivablesAccountTemp::getStatus, Constants.ENABLED)
                .eq(ChannelReceivablesAccountTemp::getCollectionType, Constants.COMMISSION));
        if (list.size() == 0) {
            return IResponse.fail("服务费收款信息为空");
        }
        return IResponse.success(list.get(0));
    }

    /**
     * 根据经销商代码获取经销商信息
     * @param channelCode
     * @return
     */
    @RequestMapping("/getChannelInfoByCode")
    public IResponse<?> getChannelInfoByCode(@RequestParam("channelCode") String channelCode){
        ChannelBaseInfoTemp channel = channelBaseInfoTempService.getOne(Wrappers.<ChannelBaseInfoTemp>query().lambda()
                .eq(ChannelBaseInfoTemp::getChannelCode,channelCode));
        return IResponse.success(channel);
    }

    /**
     * 下载合作商上线导入模板
     * @param response
     */
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        InputStream is=null;
        ServletOutputStream outputStream=null;
        try {
            ResponseHeadUtils.setExcelHeader(response,"合作商上线导入模板.xlsx");
            is = this.getClass().getClassLoader().getResourceAsStream("excel/import/channel_online_template.xlsx");
            outputStream = response.getOutputStream();
            byte[] buf=new byte[8*1024];
            int read;
            while ((read=is.read(buf))!=-1) {
                outputStream.write(buf,0,read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (outputStream!=null) {
                    outputStream.close();
                }
                if (is!=null) {
                    is.close();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
    @PostMapping("/channelUpdateTemplateExport")
    @ApiOperation("模板导出")
    public void channelUpdateTemplateExport(HttpServletResponse response) {
        channelBaseInfoTempService.channelUpdateTemplateExport(response);
    }
    @PostMapping(value = "/importChannelInfoUpdate")
    @ApiOperation(value = "根据导入的Excel文件导入渠道信息")
    public IResponse importChannelInfoUpdate(@RequestBody MultipartFile file) {
        log.info("开始导入经销商信息信息: {}", file.getOriginalFilename());
        return channelBaseInfoTempService.importChannelInfoUpdate(file);
    }
    /**
     * 合作商上线导入功能
     * @param file
     * @param response
     * @return
     */
    @RequestMapping("/importSp")
    public void importSp(MultipartFile file, HttpServletResponse response) {
        //查询当前的合作商数据，用于去重
        List<ChannelBaseInfoTemp> allInfos = channelOnlineService.list();
        //所有的经销商代码
        Set<String> allList=allInfos.stream().map(ChannelBaseInfoTemp::getChannelCode).collect(Collectors.toSet());
        Set<String> allNames=allInfos.stream().map(ChannelBaseInfoTemp::getChannelFullName).collect(Collectors.toSet());
        //查询归属组织架构所有数据，取第一条
        IResponse<List<JSONObject>> deptRes = adminDeptFeign.getSysDepartment();
        Long deptId=null;
        if (Objects.equals(CommonConstants.SUCCESS,deptRes.getCode())) {
            List<JSONObject> sortRes = deptRes.getData().stream().sorted((o1, o2) -> {
                BigDecimal order1 = o1.getBigDecimal("sortOrder");
                BigDecimal order2 = o2.getBigDecimal("sortOrder");
                return order1.subtract(order2).intValue();
            }).toList();
            deptId= Optional.ofNullable(sortRes).map(arr->arr.get(0)).map(info->info.getLong("id")).orElse(null);
        }
        //查询归属业务主题数据，取第一条
        List<SubjectInfo> bySubjectLevel = businessSubjectInfoService.getBySubjectLevel("1");
        Long subId = Optional.ofNullable(bySubjectLevel).map(arr -> arr.get(0)).map(BaseEntity::getId).orElse(null);
        //查询区域经理所有数据，取第一条
        IResponse<List<SaleTeamUserVo>> teams = caseFeignService.querySaleTeamUsers();
        String teamUserName=null;
        Long teamId=null;
        if (Objects.equals(CommonConstants.SUCCESS,teams.getCode())) {
            SaleTeamUserVo saleTeamUserVo = Optional.ofNullable(teams.getData()).map(arr -> arr.get(0)).orElse(null);
            if (saleTeamUserVo!=null) {
                teamUserName=saleTeamUserVo.getUserName();
                teamId=saleTeamUserVo.getId();
            }
        }
        //1.导入-基本信息
        EasyResEntry<ChannelBaseInfoTempImport> baseEntry = EasyExcelUtils.importExcel(file,
                ChannelBaseInfoTempImport.class,
                0,
                new AttributeVerificationRulesFunction<>() {
                    @Override
                    public String verify(ChannelBaseInfoTempImport obj) {
                        if (!(allList.add(obj.getChannelCode())&&allNames.add(obj.getChannelFullName()))) {
                            return "重复的数据!";
                        }
                        List<String> salesNetworkLists = obj.getSalesNetworkLists();
                        if (salesNetworkLists==null||salesNetworkLists.size()==0) {
                            return "销售网络不能为空";
                        }
                        for (String salesNetwork : salesNetworkLists) {
                            String networkStr = DicUtils.getDicTitleOrValue("salesNetworks", salesNetwork, 1);
                            if (StrUtil.isBlank(networkStr)) {
                                return "请输入正确的销售网络";
                            }
                        }
                        String channelBelongStr = obj.getChannelBelongStr();
                        if (StrUtil.isBlank(channelBelongStr)) {
                            return "[渠道归属]不能为空";
                        }
                        String channelBelong = DicUtils.getDicTitleOrValue("channelBelong", channelBelongStr, 2);
                        if (StrUtil.isBlank(channelBelong)) {
                            return "渠道归属匹配不成功，请联系管理员!";
                        }
                        obj.setChannelBelongStr(channelBelong);
                        String qualityGradeStr = obj.getQualityGrade();
                        if (StrUtil.isBlank(qualityGradeStr)) {
                            return "[资质等级]不能为空";
                        }
                        String qualityGrade = DicUtils.getDicTitleOrValue("qualityGrade", qualityGradeStr, 2);
                        if (StrUtil.isBlank(qualityGrade)) {
                            return "资质等级匹配不成功，请联系管理员!";
                        }
                        obj.setQualityGrade(qualityGrade);
                        return FieldAnnotationVerifyUtil.checkField(obj,",");
                    }

                    @Override
                    public Boolean checkUnique() {
                        return true;
                    }
                });
        //2.导入-控股人信息
        EasyResEntry<ChannelShareholderInfoImport> shareholderEntry = EasyExcelUtils.importExcel(file,
                ChannelShareholderInfoImport.class,
                1,
                new AttributeVerificationRulesFunction<>() {
                    @Override
                    public String verify(ChannelShareholderInfoImport obj) {
                        return FieldAnnotationVerifyUtil.checkField(obj,",");
                    }

                    @Override
                    public Boolean checkUnique() {
                        return false;
                    }
                });
        //3.导入-收款账户信息
        EasyResEntry<ChannelReceivablesAccountTempImport> receivablesAccountEntry = EasyExcelUtils.importExcel(file,
                ChannelReceivablesAccountTempImport.class,
                2,
                new AttributeVerificationRulesFunction<>() {
                    @Override
                    public String verify(ChannelReceivablesAccountTempImport obj) {
                        return FieldAnnotationVerifyUtil.checkField(obj,",");
                    }

                    @Override
                    public Boolean checkUnique() {
                        return false;
                    }
                });
        //4.处理导入的所有数据
        List<ChannelBaseInfoTempImport> baseInfoTempLists = baseEntry.getSuccessList();
        List<ChannelShareholderInfoImport> shareholderSuccessList = shareholderEntry.getSuccessList();
        List<ChannelReceivablesAccountTempImport> receivablesAccountSuccessList = receivablesAccountEntry.getSuccessList();
        List<ChannelBaseInfoTempImport> baseFailList = baseEntry.getFailList();
        List<ChannelShareholderInfoImport> shareholderFailList = shareholderEntry.getFailList();
        List<ChannelReceivablesAccountTempImport> receivablesAccountFailList = receivablesAccountEntry.getFailList();
        List<String> shareholderBaseFailMsg = shareholderEntry.getFailMsg();
        List<String> receivablesAccountFailMsg = receivablesAccountEntry.getFailMsg();
        for (ChannelBaseInfoTempImport baseFail : baseFailList) {
            String bisiId = baseFail.getBisiId();
            Iterator<ChannelShareholderInfoImport> iterator = shareholderSuccessList.iterator();
            while (iterator.hasNext()) {
                ChannelShareholderInfoImport next = iterator.next();
                if (Objects.equals(next.getBisiId(),bisiId)) {
                    //错误列表添加
                    shareholderFailList.add(next);
                    shareholderBaseFailMsg.add("渠道基本信息错误");
                    //成功列表删除
                    iterator.remove();
                }
            }
            Iterator<ChannelReceivablesAccountTempImport> iterator1 = receivablesAccountSuccessList.iterator();
            while (iterator1.hasNext()) {
                ChannelReceivablesAccountTempImport next = iterator1.next();
                if (Objects.equals(next.getBisiId(),bisiId)) {
                    //错误列表添加
                    receivablesAccountFailList.add(next);
                    receivablesAccountFailMsg.add("渠道基本信息错误");
                    //成功列表删除
                    iterator1.remove();
                }
            }
        }
        //5.处理合作商基本数据并插入
        List<ChannelOnlineCondition> channelBaseList=new ArrayList<>();
        for (ChannelBaseInfoTempImport baseInfoTempList : baseInfoTempLists) {
            ChannelOnlineCondition condition = new ChannelOnlineCondition();
            ChannelBaseInfoTemp channelBaseInfoTemp = new ChannelBaseInfoTemp();
            //设置渠道归属
            channelBaseInfoTemp.setChannelBelong(baseInfoTempList.getChannelBelongStr());
            //设置合作商有效期(长期有效)
            channelBaseInfoTemp.setDealerValidityPeriod(Const.IS_LONG_PERIOD);
            //归属组织架构默认值设置
            channelBaseInfoTemp.setDeptAttributionId(deptId);
            //归属业务主体设置默认值
            channelBaseInfoTemp.setSubjectAttributionId(subId);
            BeanUtils.copyProperties(baseInfoTempList,channelBaseInfoTemp);
            channelBaseInfoTemp.setSalesNetwork(StringUtils.join(baseInfoTempList.getSalesNetworkLists().toArray(),","));
            condition.setChannelBaseInfoTemp(channelBaseInfoTemp);
            String[] arr=new String[2];
            //设置地址信息
            condition.setChannelAddressValue(baseInfoTempList.getChannelAddressValue().toArray(arr));
            condition.setOfficeAddressValue(baseInfoTempList.getOfficeAddressValue().toArray(arr));
            //设置资质等级
            ChannelRiskInfoTemp channelRiskInfoTemp = new ChannelRiskInfoTemp();
            channelRiskInfoTemp.setQualityGrade(baseInfoTempList.getQualityGrade());
            //区域经理
            channelRiskInfoTemp.setCustomerManager(teamUserName);
            //区域经理id
            channelRiskInfoTemp.setCustManageId(teamId);
            condition.setChannelRiskInfoTemp(channelRiskInfoTemp);
            setDefaultVal(condition);
            channelBaseList.add(condition);
        }
        channelOnlineService.batchImportSpData(channelBaseList,shareholderSuccessList,receivablesAccountSuccessList);
        //6.处理导入错误的数据，导出到excel中返回
        List<EasyResEntry> allFailList=new ArrayList<>();
        if (baseEntry.getFailList()!=null&&baseEntry.getFailList().size()>0) {
            allFailList.add(baseEntry);
        }
        if (shareholderEntry.getFailList()!=null&&shareholderEntry.getFailList().size()>0) {
            allFailList.add(shareholderEntry);
        }
        if (receivablesAccountEntry.getFailList()!=null&&receivablesAccountEntry.getFailList().size()>0) {
            allFailList.add(receivablesAccountEntry);
        }
        response.setHeader("successSize",String.valueOf(channelBaseList.size()));
        response.setHeader("failSize",String.valueOf(Optional.ofNullable(baseFailList).map(List::size).orElse(0)));
        if(allFailList.size()>0) {
            Workbook failWorkbook = EasyExcelUtils.setFailListToExcel("excel/import/channel_online_template.xlsx"
                    ,allFailList.toArray(new EasyResEntry[allFailList.size()]));
            if (failWorkbook!=null) {
                ResponseHeadUtils.setExcelHeader(response,file.getOriginalFilename());
                try {
                    ServletOutputStream outputStream = response.getOutputStream();
                    failWorkbook.write(outputStream);
                    failWorkbook.close();
                    outputStream.close();
                }catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
    }

    /**
     * 设置合作商上线的默认参数，需保持和前端设置的一致
     * @param condition
     */
    private void setDefaultVal(ChannelOnlineCondition condition) {
        //设置基本信息默认值
        ChannelBaseInfoTemp channelBaseInfoTemp=condition.getChannelBaseInfoTemp();
        channelBaseInfoTemp.setChannelType("01");
        channelBaseInfoTemp.setLongitudeLatitudeSwitch("0");
        channelBaseInfoTemp.setChoiceCardealerSwitch("0");
        channelBaseInfoTemp.setLongitudeLatitudeSwitchOld(channelBaseInfoTemp.getLongitudeLatitudeSwitch());
        channelBaseInfoTemp.setChoiceCardealerSwitchOld("1");
        channelBaseInfoTemp.setControlBusinessType("0");
        //公司层级数
        channelBaseInfoTemp.setHierarchy(1);
        //基本状态
        channelBaseInfoTemp.setBaseStatus("1");
        //业务类型
        channelBaseInfoTemp.setBusinessType("01,02");
        channelBaseInfoTemp.setDealerGrade("0");
        //汇款对象
        channelBaseInfoTemp.setPaymentObject("0");
        //根据用户角色设置新车，二手车权限，前端页面：approve-ui\src\projects\afs-channel\pages\dealer-apply
        List<String> roles = SecurityUtils.getUser().getRoleCodeList();
        boolean ROLE_ADMIN = roles.contains("ROLE_ADMIN"),
                ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP = roles.contains("ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP"),
                ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP = roles.contains("ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP"),
                ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP = roles.contains("ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP"),
                ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP = roles.contains("ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP"),
                ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP = roles.contains("ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP"),
                ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP = roles.contains("ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP"),
                ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP = roles.contains("ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP"),
                ROLE_GCJX_HZSKG = roles.contains("ROLE_GCJX_HZSKG"),
                ROLE_GCJX_HZSGL = roles.contains("ROLE_GCJX_HZSGL"),
                ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP = roles.contains("ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP");
        if (ROLE_ADMIN) {
            condition.setIsNewCarRole("1");
            condition.setIsOldCarRole("1");
        } else {
            //工程机械默认角色 - 合作商开发、合作商管理角色赋权
            if(ROLE_GCJX_HZSKG || ROLE_GCJX_HZSGL) {
                condition.setIsNewCarRole("1");
                condition.setIsOldCarRole("1");
            }
            //非直营角色判断
            if ((ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP || ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP ) &&
                    (ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP || ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP)) {
                condition.setIsNewCarRole("1");
                condition.setIsOldCarRole("1");
            } else if (ROLE_OTHER_OLD_MANAGE_CHANNEL_DEVELOP || ROLE_OTHER_OLD_SEE_CHANNEL_DEVELOP) {
                // 说明是二手车非直营管理
                condition.setIsNewCarRole("0");
                condition.setIsOldCarRole("1");
            } else if (ROLE_OTHER_NEW_MANAGE_CHANNEL_DEVELOP || ROLE_OTHER_NEW_SEE_CHANNEL_DEVELOP) {
                // 说明是新车车非直营管理
                condition.setIsNewCarRole("1");
                condition.setIsOldCarRole("0");
            }
            //直营角色判断
            if ((ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP || ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP) &&
                    (ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP || ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP)) {
                //说明当前用户直营权限为：新车and二手车
                condition.setIsNewCarRole("1");
                condition.setIsOldCarRole("1");
            } else if (ROLE_OWN_NEW_MANAGE_CHANNEL_DEVELOP || ROLE_OWN_NEW_SEE_CHANNEL_DEVELOP) {
                //说明当前用户直营权限为：新车
                condition.setIsNewCarRole("1");
                condition.setIsOldCarRole("0");
            } else if (ROLE_OWN_OLD_MANAGE_CHANNEL_DEVELOP || ROLE_OWN_OLD_SEE_CHANNEL_DEVELOP) {
                //说明当前用户直营权限为：二手车
                condition.setIsNewCarRole("0");
                condition.setIsOldCarRole("1");
            }
        }
        //设置风险信息（新车）
        ChannelRiskInfoTemp channelRiskInfoTemp = condition.getChannelRiskInfoTemp();
        channelRiskInfoTemp.setCarType("2");
        channelRiskInfoTemp.setAccountMaxNum(50);
        channelRiskInfoTemp.setIsMortgage("1");
        channelRiskInfoTemp.setPostbackDays("60");
        //设置风险信息(二手车)
        ChannelRiskInfoTemp channelRiskInfoTempOld=new ChannelRiskInfoTemp();
        BeanUtils.copyProperties(channelRiskInfoTemp,channelRiskInfoTempOld);
        channelRiskInfoTempOld.setBusinessType("02");
        condition.setChannelRiskInfoTempOld(channelRiskInfoTempOld);
        //设置额度信息(新车)
        ChannelQuotaInfoTemp channelQuotaInfoTemp = new ChannelQuotaInfoTemp();
        channelQuotaInfoTemp.setQuotaType("2");
        channelQuotaInfoTemp.setBusinessType("01");
        channelQuotaInfoTemp.setQuotaControlSwitch("0");
        channelQuotaInfoTemp.setQuotaAmount(new BigDecimal("**********"));
        condition.setChannelQuotaInfoTempList(Collections.singletonList(channelQuotaInfoTemp));
        //设置额度信息(二手车)
        ChannelQuotaInfoTemp channelQuotaInfoTempOld = new ChannelQuotaInfoTemp();
        BeanUtils.copyProperties(channelQuotaInfoTemp,channelQuotaInfoTempOld);
        channelQuotaInfoTemp.setBusinessType("02");
        condition.setChannelQuotaInfoTempOldList(Collections.singletonList(channelQuotaInfoTempOld));
    }

    @PostMapping("/findChannelBaseInfoBySocUniCrtCode")
    @ApiOperation(value = "大额对公获取渠道基本信息")
    @SysLog("大额对公获取渠道基本信息")
    public IResponse<ChannelBaseAccountVo> findChannelBaseInfoBySocUniCrtCode(@RequestParam(value = "socUniCrtCode") String socUniCrtCode){
        ChannelBaseAccountVo channelBaseAccountVo=channelOnlineService.findChannelBaseInfoBySocUniCrtCode(socUniCrtCode);
        if(ObjectUtil.isNotNull(channelBaseAccountVo)){
            return IResponse.success(channelBaseAccountVo);
        }
        return IResponse.fail("合作商信息不存在！");
    }

    /**
     * 下载经销商评级导入模板
     *
     * @param response
     */
    @GetMapping("/exportQualityGradeTemplate")
    public void exportQualityGradeTemplate(HttpServletResponse response) {
        InputStream is = null;
        ServletOutputStream outputStream = null;
        try {
            ResponseHeadUtils.setExcelHeader(response, "经销商修改导入模板.xlsx");
            is = this.getClass().getClassLoader().getResourceAsStream("excel/import/channel_qualitygrade_template.xlsx");
            outputStream = response.getOutputStream();
            byte[] buf = new byte[8 * 1024];
            int read;
            while ((read = is.read(buf)) != -1) {
                outputStream.write(buf, 0, read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 经销商评级导入功能
     * @param file
     * @param response
     * @return
     */
    @RequestMapping("/importQualityGrade")
    public IResponse importQualityGrade(MultipartFile file, String updateType, HttpServletResponse response) throws IOException {
        //1、查询当前的合作商数据，用于校验是否有效
        List<ChannelBaseInfoTemp> allInfos = channelOnlineService.selectAllOnlineSp();
        // 所有的经销商代码、名称
        Set<String> allList = allInfos.stream().map(ChannelBaseInfoTemp::getChannelCode).collect(Collectors.toSet());
        Set<String> allNames = allInfos.stream().map(ChannelBaseInfoTemp::getChannelFullName).collect(Collectors.toSet());
        Map<String, ChannelBaseInfoTemp> infoTempMap = allInfos.stream()
            .collect(Collectors.toMap(ChannelBaseInfoTemp::getChannelCode, Function.identity()));
        StringBuilder sb = new StringBuilder();

        EasyResEntry<ChannelQualityGradeTempImport> baseEntry = EasyExcelUtils.importExcel(file,
                ChannelQualityGradeTempImport.class,
                new AttributeVerificationRulesFunction<>() {
                    @Override
                    public String verify(ChannelQualityGradeTempImport obj) {
                        // 如果经销商数据不存在，或者数据有问题，要抛出异常，页面进行提示，不能导入。
                        String channelFullName = obj.getChannelFullName();
                        // 验证是否存在
                        if (!(allList.contains(obj.getChannelCode()) && allNames.contains(channelFullName))) {
                            sb.append(String.format("[%s] 经销商数据不存在, 请输入正确的经销商代码或经销商名称!", channelFullName));
                            return String.format("[%s] 经销商数据不存在, 请输入正确的经销商代码或经销商名称!", channelFullName);
                        }
                        // 验证数据是否有问题
                        if (ChannelInfoUpdatesConstants.UPDATE_TYPE_QUALITY_GRADE.equals(updateType)) {
                            String qualityGradeStr = obj.getQualityGrade();
                            if (StrUtil.isBlank(qualityGradeStr)) {
                                sb.append(String.format("[%s 经销商评级]不能为空!", channelFullName));
                                return String.format("[%s 经销商评级]不能为空!", channelFullName);
                            }
                            // 去查字典值 - 经销商评级
                            String qualityGrade = DicUtils.getDicTitleOrValue("qualityGrade", qualityGradeStr, 2);
                            if (StrUtil.isBlank(qualityGrade)) {
                                sb.append(String.format("[%s] 经销商评级匹配不成功，请输入正确的经销商评级!]", channelFullName));
                                return String.format("[%s] 经销商评级匹配不成功，请输入正确的经销商评级!]", channelFullName);
                            }
                        } else if (ChannelInfoUpdatesConstants.UPDATE_TYPE_IS_MORTGAGE.equals(updateType)) {
                            String isMortgageStr = obj.getIsMortgage();
                            if (StrUtil.isBlank(isMortgageStr)) {
                                sb.append(String.format("[%s] 先放后抵]不能为空!", channelFullName));
                                return String.format("[%s] 先放后抵不能为空!", channelFullName);
                            }
                            // 是否先放后抵
                            if (!StrUtil.equalsAny(obj.getIsMortgage(), "1","0")) {
                                sb.append(String.format("[%s] 是否先放后抵匹配不成功，请输入正确的是否先放后抵!", channelFullName));
                                return String.format("[%s] 是否先放后抵匹配不成功，请输入正确的是否先放后抵!", channelFullName);
                            }
                        } else if (ChannelInfoUpdatesConstants.UPDATE_TYPE_DEALER_CODE.equals(updateType)) {
                            // 经销商代码
                            if (StrUtil.isNotBlank(obj.getDealerCode())) {
                                //检测经销商代码格式=>需大写去空格
                                log.info("******************  "+obj.getDealerCode().trim().toUpperCase());
                                if (!obj.getDealerCode().trim().equals(obj.getDealerCode().trim().toUpperCase())){
                                    sb.append(String.format("[%s] 新的经销商代码格式不正确，请输入正确的经销商代码!",channelFullName));
                                    return String.format("[%s] 新的经销商代码格式不正确，请输入正确的经销商代码!",channelFullName);
                                }
                            }
                            // 经销商名称
                            if (StrUtil.isNotBlank(obj.getNewChannelFullName())) {
                                if (allNames.contains(obj.getNewChannelFullName().trim())) {
                                    sb.append(String.format("[%s] 新的经销商名称已存在，请输入正确!",channelFullName));
                                    return String.format("[%s] 新的经销商名称已存在，请输入正确!",channelFullName);
                                }
                            }
                            // 经销商spa代码
                            if (StrUtil.isNotBlank(obj.getSpaDealerCode())){
                                if (!obj.getSpaDealerCode().trim().equals(obj.getSpaDealerCode().trim().toUpperCase())){
                                    sb.append(String.format("[%s] 新的sap经销商代码格式不正确，请输入正确的sap经销商代码!",channelFullName));
                                    return String.format("[%s] 新的sap经销商代码格式不正确，请输入正确的sap经销商代码!",channelFullName);
                                }
                            }
                            // 经销商spa供应商代码
                            if (StrUtil.isNotBlank(obj.getSpaSupplierCode())){
                                if (!obj.getSpaSupplierCode().trim().equals(obj.getSpaSupplierCode().trim().toUpperCase())){
                                    sb.append(String.format("[%s] 新的sap供应商代码格式不正确，请输入正确的sap供应商代码!",channelFullName));
                                    return String.format("[%s] 新的sap供应商代码格式不正确，请输入正确的sap供应商代码!",channelFullName);
                                }
                            }
                        }
                        return FieldAnnotationVerifyUtil.checkField(obj,",");
                    }

                    @Override
                    public Boolean checkUnique() {
                        return true;
                    }
                });
        log.info("------------------经销商更新评级导入的数据： {}----------------", baseEntry);
        List<ChannelQualityGradeTempImport> failList = baseEntry.getFailList();
        List<String> failMsg = baseEntry.getFailMsg();
        Map resultMap = new HashMap(4);
        if (!failList.isEmpty()) {
            // 只要有一个验证不通过的就返回错误信息
            resultMap.put("failMsg", Optional.of(failMsg.isEmpty() ? "" : failMsg.get(0)).orElse(""));
            resultMap.put("successSize", 0);
            log.info("-----导入后返回的resultMap---------{}-", resultMap);
            return IResponse.success(resultMap);
        }

        // 处理导入的数据
        List<ChannelQualityGradeTempImport> successList = baseEntry.getSuccessList();
        if (!successList.isEmpty()) {
            // 有未修改成功的经销商禁止再次修改
            if (ChannelInfoUpdatesConstants.UPDATE_TYPE_DEALER_CODE.equals(updateType)) {
                for (ChannelQualityGradeTempImport tempImport : successList) {
                    ChannelBaseInfoTemp temp = infoTempMap.get(tempImport.getChannelCode());
                    List<ChannelInfoUpdates> list = channelInfoUpdateService.lambdaQuery()
                        .eq(ChannelInfoUpdates::getChannelId, String.valueOf(temp.getId()))
                        .eq(ChannelInfoUpdates::getUpdateType, ChannelInfoUpdatesConstants.UPDATE_TYPE_DEALER_CODE)
                        .eq(ChannelInfoUpdates::getStatus, ChannelInfoUpdatesConstants.STATUS_FAILED)
                        .list();
                    if (CollUtil.isNotEmpty(list)) {
                        throw new AfsBaseException("经销商" + temp.getChannelFullName() + "上次修改未完成,禁止再次修改");
                    }
                }
            }
            for (ChannelQualityGradeTempImport channelQualityGradeTempImport : successList) {
                // 查询 channelId
                String channelCode = channelQualityGradeTempImport.getChannelCode();
                log.info("----------------调用 case 服务查询 channelCode: {}", channelCode);
                String channelId = caseFeignService.queryChannelIdByChannelCode(channelCode);
                log.info("----------------调用 case 服务查询 channelid: {}", channelId);

                ChannelInfoUpdates channelInfoUpdates = new ChannelInfoUpdates();
                // 通用的一些设置
                updateBaseInfo(channelInfoUpdates, channelId, channelCode);
                log.info("----------------通用的一些设置之后 channelInfoUpdates: {}  " , channelId);

                log.info("updateType :{}",updateType);
                if (ChannelInfoUpdatesConstants.UPDATE_TYPE_QUALITY_GRADE.equals(updateType)) {
                    // 更新评级
                    log.info("----------------更新评级之前 channelInfoUpdates: {}  " , channelInfoUpdates);
                    updateQualityGrade(channelQualityGradeTempImport, channelInfoUpdates, channelId);
                    log.info("----------------更新评级之后 channelInfoUpdates: {}  " , channelInfoUpdates);
                } else if (ChannelInfoUpdatesConstants.UPDATE_TYPE_IS_MORTGAGE.equals(updateType)) {
                    // 更新先放后抵
                    updateIsMortgage(channelQualityGradeTempImport, channelInfoUpdates, channelId);
                    log.info("----------------更新先放后抵之后 channelInfoUpdates: {}  ", channelInfoUpdates);
                } else if (ChannelInfoUpdatesConstants.UPDATE_TYPE_DEALER_CODE.equals(updateType)) {
                    updateDealerCode(channelQualityGradeTempImport, channelInfoUpdates);
                }
            }
        }

        resultMap.put("failMsg", Optional.of(failMsg.isEmpty() ? "" : failMsg.get(0)).orElse(""));
        resultMap.put("successSize", Optional.of(successList.size()).orElse(0));
        log.info("-----导入后返回的resultMap---------{}-", resultMap);
        return IResponse.success(resultMap);
    }

    /**
     * 通用的一些设置
     * @param channelInfoUpdates
     * @param channelId
     * @param channelCode
     */
    private void updateBaseInfo(ChannelInfoUpdates channelInfoUpdates, String channelId, String channelCode) {
        channelInfoUpdates.setId(null);
        channelInfoUpdates.setChannelId(Long.valueOf(channelId));
        // 默认失败方便后续筛选
        channelInfoUpdates.setStatus(ChannelInfoUpdatesConstants.STATUS_FAILED);
        channelInfoUpdates.setExecutionAttempts(0);
        channelInfoUpdates.setExecutedAt(null);
        channelInfoUpdates.setSuccess(0);
        channelInfoUpdates.setErrorMessage(null);
        channelInfoUpdates.setTaskName(channelCode + DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        channelInfoUpdates.setDelFlag("0");
        channelInfoUpdates.setCreateTime(new Date());
        channelInfoUpdates.setCreateBy(SecurityUtils.getUsername());
        channelInfoUpdates.setUpdateBy(SecurityUtils.getUsername());
    }

    /**
     * 更新先放后抵
     * @param channelQualityGradeTempImport
     * @param channelInfoUpdates
     * @param channelId
     */

    private void updateIsMortgage(ChannelQualityGradeTempImport channelQualityGradeTempImport, ChannelInfoUpdates channelInfoUpdates, String channelId) {
        // 二 、更新是否先放后抵
        channelInfoUpdates.setUpdateType(ChannelInfoUpdatesConstants.UPDATE_TYPE_IS_MORTGAGE);
        String isMortgage = channelQualityGradeTempImport.getIsMortgage();
        channelInfoUpdates.setChannelId(channelInfoUpdates.getChannelId());
        channelInfoUpdates.setIsMortgage(isMortgage);
        channelInfoUpdates.setPriority("0");
        List<ChannelInfoUpdates> channelInfoUpdatesList = new ArrayList<>();

        // leasing_channel
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_CHANNEL);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP);
        ChannelInfoUpdates channelInfoUpdates1 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates1);
        channelInfoUpdatesList.add(channelInfoUpdates1);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP_UPD);
        ChannelInfoUpdates channelInfoUpdates2 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates2);
        channelInfoUpdatesList.add(channelInfoUpdates2);


        // leasing_case
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_CASE);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO);
        ChannelInfoUpdates channelInfoUpdates3 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates3);
        channelInfoUpdatesList.add(channelInfoUpdates3);

        // leasing_contract_manage
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_CONTRACT_MANAGE);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_MANAGE_ASSET_CHANGE);
        ChannelInfoUpdates channelInfoUpdates4 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates4);
        channelInfoUpdatesList.add(channelInfoUpdates4);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_MANAGE_CANCEL_RECORD);
        ChannelInfoUpdates channelInfoUpdates5 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates5);
        channelInfoUpdatesList.add(channelInfoUpdates5);

        log.info("----------------更新 更新是否先放后抵：channelInfoUpdatesList: {}  " , channelInfoUpdatesList);
        // 批量更新
        channelOnlineService.batchInsertChannelInfo(channelInfoUpdatesList);
    }

    /**
     * 更新评级
     * @param channelQualityGradeTempImport
     * @param channelInfoUpdates
     * @param channelId
     */
    private void updateQualityGrade(ChannelQualityGradeTempImport channelQualityGradeTempImport, ChannelInfoUpdates channelInfoUpdates, String channelId) {
        // 一 、更新评级
        channelInfoUpdates.setUpdateType(ChannelInfoUpdatesConstants.UPDATE_TYPE_QUALITY_GRADE);
        channelInfoUpdates.setChannelId(channelInfoUpdates.getChannelId());
        String qualityGrade = channelQualityGradeTempImport.getQualityGrade();
        channelInfoUpdates.setChannelGrade(qualityGrade);
        List<ChannelInfoUpdates> channelInfoUpdatesList = new ArrayList<>();

        // 1、 leasing_channel （先更新 channel_grade (0)后更新 quality_grade(1)）
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_CHANNEL);
        // 更新 channel_grade

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP);
        channelInfoUpdates.setPriority("0");
        // 避免引用传递
        ChannelInfoUpdates channelInfoUpdates1 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates1);
        channelInfoUpdatesList.add(channelInfoUpdates1);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP_UPD);
        channelInfoUpdates.setPriority("0");
        ChannelInfoUpdates channelInfoUpdates2 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates2);
        channelInfoUpdatesList.add(channelInfoUpdates2);

        // 更新 quality_grade
        String qualityGradeNum = DicUtils.getDicTitleOrValue("qualityGrade", qualityGrade, 2);
        channelInfoUpdates.setQualityGrade(qualityGradeNum);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP);
        channelInfoUpdates.setPriority("1");
        ChannelInfoUpdates channelInfoUpdates3 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates3);
        channelInfoUpdatesList.add(channelInfoUpdates3);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO_TEMP_UPD);
        channelInfoUpdates.setPriority("1");
        ChannelInfoUpdates channelInfoUpdates4 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates4);
        channelInfoUpdatesList.add(channelInfoUpdates4);

        // 2、 leasing_case
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_CASE);
        // 更新 channel_grade
        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO);
        channelInfoUpdates.setPriority("0");
        ChannelInfoUpdates channelInfoUpdates5 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates5);
        channelInfoUpdatesList.add(channelInfoUpdates5);
        // 更新 quality_grade
        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_CASE_CHANNEL_INFO);
        channelInfoUpdates.setPriority("1");
        ChannelInfoUpdates channelInfoUpdates6 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates6);
        channelInfoUpdatesList.add(channelInfoUpdates6);

        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_RISK_INFO);
        channelInfoUpdates.setPriority("1");
        ChannelInfoUpdates channelInfoUpdates7 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates7);
        channelInfoUpdatesList.add(channelInfoUpdates7);

        // 3、leasing_apply
        channelInfoUpdates.setDatabaseName(ChannelInfoUpdatesConstants.DATABASE_NAME_LEASING_APPLY);
        // 更新 channel_grade
        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_APPLY_CHANNEL_INFO);
        channelInfoUpdates.setPriority("0");
        ChannelInfoUpdates channelInfoUpdates8 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates8);
        channelInfoUpdatesList.add(channelInfoUpdates8);

        // 更新 quality_grade
        channelInfoUpdates.setTableName(ChannelInfoUpdatesConstants.TABLE_NAME_APPLY_CHANNEL_INFO);
        channelInfoUpdates.setPriority("1");
        ChannelInfoUpdates channelInfoUpdates9 = new ChannelInfoUpdates();
        BeanUtils.copyProperties(channelInfoUpdates, channelInfoUpdates9);
        channelInfoUpdatesList.add(channelInfoUpdates9);

        log.info("----------------更新 更新评级：channelInfoUpdatesList: {}  " , channelInfoUpdatesList);
        // 批量更新
        channelOnlineService.batchInsertChannelInfo(channelInfoUpdatesList);
    }

    /**
     * 更新经销商code信息
     * @param channelQualityGradeTempImport 导入信息
     * @param channelInfoUpdates 插入信息
     */
    private void updateDealerCode(ChannelQualityGradeTempImport channelQualityGradeTempImport, ChannelInfoUpdates channelInfoUpdates){
        // 修改类型
        List<ChannelImportTypeEnum> typeEnums = new ArrayList<>();
        if (StrUtil.isNotBlank(channelQualityGradeTempImport.getDealerCode()) && !StrUtil.equals(
            channelQualityGradeTempImport.getChannelCode(), channelQualityGradeTempImport.getDealerCode())) {
            typeEnums.add(ChannelImportTypeEnum.CODE);
        }
        if (StrUtil.isNotBlank(channelQualityGradeTempImport.getNewChannelFullName()) && !StrUtil.equals(
            channelQualityGradeTempImport.getChannelFullName(),
            channelQualityGradeTempImport.getNewChannelFullName())) {
            typeEnums.add(ChannelImportTypeEnum.NAME);
        }
        if (StrUtil.isNotBlank(channelQualityGradeTempImport.getSpaDealerCode())) {
            typeEnums.add(ChannelImportTypeEnum.SPA);
        }
        if (StrUtil.isNotBlank(channelQualityGradeTempImport.getSpaSupplierCode())) {
            typeEnums.add(ChannelImportTypeEnum.SPAS);
        }

        channelInfoUpdates.setChannelCode(channelQualityGradeTempImport.getChannelCode().trim());
        channelInfoUpdates.setChannelName(channelQualityGradeTempImport.getChannelFullName().trim());
        channelInfoUpdates.setDealerCode(channelQualityGradeTempImport.getDealerCode());
        channelInfoUpdates.setDealerName(channelQualityGradeTempImport.getNewChannelFullName());
        channelInfoUpdates.setSpaDealerCode(channelQualityGradeTempImport.getSpaDealerCode());
        channelInfoUpdates.setSpaDealerSupplierCode(channelQualityGradeTempImport.getSpaSupplierCode());
        channelInfoUpdates.setUpdateType(ChannelInfoUpdatesConstants.UPDATE_TYPE_DEALER_CODE);
        List<ChannelInfoUpdates> list = new ArrayList<>();
        for (ChannelImportUpdateEnum updateEnum : ChannelImportUpdateEnum.values()) {
            if (CollectionUtils.containsAny(updateEnum.getType(), typeEnums)) {

                channelInfoUpdates.setDatabaseName(updateEnum.getService());
                channelInfoUpdates.setTableName(updateEnum.getTable());
                ChannelInfoUpdates channelUpdate = new ChannelInfoUpdates();
                BeanUtils.copyProperties(channelInfoUpdates, channelUpdate);
                list.add(channelUpdate);
            }
        }
        log.info("----------------更新 更新经销商code信息：list: {}  ", JSON.toJSONString(list));
        channelOnlineService.batchInsertChannelInfo(list);
    }

    @PostMapping(value = "/getWitnessesRecords")
    @ApiOperation(value = "多条件分页渠道信息列表")
    public IResponse<?> getWitnessesRecords(@RequestBody ChannelOnlineCondition condition) {
        List<ChannelWitnessesRecord> witnessesRecords = witnessesRecordService.list(Wrappers.<ChannelWitnessesRecord>lambdaQuery()
                .eq(ChannelWitnessesRecord::getRelationId, condition.getChannelCode()));
        return IResponse.success(witnessesRecords);
    }

    @PostMapping(value = "/getChannelIdsByCity/{channelCity}")
    @ApiOperation(value = "通过城市查询经销商ID")
    public List<String> getChannelIdsByCity(@PathVariable String channelCity){
        List<ChannelBaseInfoTemp> list = channelBaseInfoTempService.list(Wrappers.<ChannelBaseInfoTemp>lambdaQuery()
                .eq(ChannelBaseInfoTemp::getChannelCity,channelCity));
        List<String> strings = new ArrayList<>();
        for (ChannelBaseInfoTemp temp : list){
            strings.add(temp.getId().toString());
        }
        return strings;
    }


    /**
     * 根据经销商信息查询经销商是否存在
     *
     * @param unitName unitName
     * @return 根据经销商信息查询经销商是否存在
     */
    @PostMapping(value = "/getChannelInfoExistFlag/{unitName}")
    @ApiOperation("根据经销商信息查询经销商是否存在")
    public Boolean getChannelInfoExistFlag(@PathVariable(value = "unitName") String unitName) {
        long count = channelBaseInfoTempService.count(Wrappers.<ChannelBaseInfoTemp>lambdaQuery()
                .apply("BINARY channel_full_name = {0}", unitName)  // 使用BINARY强制区分括号
                .eq(ChannelBaseInfoTemp::getChannelStatus, ChannelOnlineConstants.VALUE_CHANNEL_STATUS_ZERO));
        return count > 0;
    }

    @PostMapping(value = "getChannelCodes")
    @ApiOperation("获取经销商统一信用代码")
    public List<String> getChannelCodes(@RequestBody List<String> channelIds){
        return channelBaseInfoTempMapper.selectObjs(Wrappers.<ChannelBaseInfoTemp>lambdaQuery()
                .in(ChannelBaseInfoTemp::getId,channelIds)
                .select(ChannelBaseInfoTemp::getSocUniCrtCode)).stream().map(obj -> (String)obj).collect(Collectors.toList());
    }
}
