package com.ruicar.afs.cloud.gateway.config;

import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.gateway.filter.RequestFilterHandler;
import com.ruicar.afs.cloud.gateway.handler.*;
import com.ruicar.afs.cloud.gateway.swagger.SwaggerConfigHandler;
import com.ruicar.afs.cloud.gateway.swagger.SwaggerInfoHandler;
import com.ruicar.afs.cloud.gateway.swagger.SwaggerProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;
import reactor.core.publisher.Mono;

import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2019-01-24
 * 路由配置信息
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class RouterFunctionConfiguration {
	private final SentinelFallbackHandler sentinelFallbackHandler;
	private final CaptchaCodeHandler captchaCodeHandler;
	private final AfsLoginHandler afsLoginHandler;
	private final AfsRefreshTokenHandler afsRefreshTokenHandler;
	private final SystemRsaPublicKeyHandler rsaPublicKeyHandler;
	private final RequestFilterHandler requestFilterHandler;

	private final Optional<SwaggerConfigHandler> swaggerConfigHandler;
	private final Optional<SwaggerInfoHandler> swaggerInfoHandler;
	private final RouteDefinitionRepository routeDefinitionRepository;
	private final FilterIgnorePropertiesConfig filterIgnorePropertiesConfig;
	private final ObjectProvider<SwaggerProperties> swaggerProperties;
	@Bean
	public RouterFunction routerFunction() {
		return RouterFunctions.route(RequestPredicates.path("/fallback").and(RequestPredicates.accept(MediaType.TEXT_PLAIN)), sentinelFallbackHandler)
				.andRoute(RequestPredicates.GET("/captcha").and(RequestPredicates.accept(MediaType.TEXT_PLAIN)), captchaCodeHandler)
				.andRoute(RequestPredicates.GET("/swagger-config").and(RequestPredicates.accept(MediaType.ALL)), swaggerConfigHandler.orElse(new SwaggerConfigHandler(routeDefinitionRepository,filterIgnorePropertiesConfig,swaggerProperties.getIfAvailable()){
							@Override
							public Mono<ServerResponse> handle(ServerRequest request) {
								return ServerResponse.status(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON).bodyValue("{}");
							}
						}))
				.andRoute(RequestPredicates.GET(CommonConstants.API_URI).and(RequestPredicates.accept(MediaType.ALL)), swaggerInfoHandler.orElse(new SwaggerInfoHandler(swaggerProperties.getIfAvailable()){
							@Override
							public Mono<ServerResponse> handle(ServerRequest request) {
								return ServerResponse.status(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON).bodyValue("{}");
							}
						}))
				.andRoute(RequestPredicates.POST("/userlogin").and(RequestPredicates.accept(MediaType.APPLICATION_FORM_URLENCODED)), afsLoginHandler)
				.andRoute(RequestPredicates.POST("/refreshtoken").and(RequestPredicates.accept(MediaType.APPLICATION_FORM_URLENCODED)), afsRefreshTokenHandler)
				.andRoute(RequestPredicates.GET("/pubkey").and(RequestPredicates.accept(MediaType.APPLICATION_FORM_URLENCODED)), rsaPublicKeyHandler)
				.filter(requestFilterHandler)
				;
	}

}
