package com.ruicar.afs.cloud.gateway.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.log.util.SysLogUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.IpUtils;
import com.ruicar.afs.cloud.common.gateway.dto.GatewayFilterProcessDto;
import com.ruicar.afs.cloud.common.gateway.filters.AfsFilterHelper;
import com.ruicar.afs.cloud.gateway.config.RequestPathTokenConfig;
import com.ruicar.afs.cloud.gateway.util.AfsGateWayHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.common.core.constant.CommonConstants.AFS_TRACKER_ID;
import static com.ruicar.afs.cloud.common.core.security.constant.OAuthRedisKeyPrefix.ACCESS_TOKEN_REF;
import static com.ruicar.afs.cloud.common.core.security.constant.OAuthRedisKeyPrefix.AUTH_KICK_OUT;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.addOriginalRequestUrl;

/**
 * <AUTHOR>
 * @date 2019-01-21
 * <p>
 * 全局拦截器，作用所有的微服务
 * <p>
 * 1. 对请求头中参数进行处理 from 参数进行清洗
 * 2. 重写StripPrefix = 1,支持全局
 * <p>
 */
@Component
@Slf4j
@AllArgsConstructor
public class RequestGlobalFilter implements GlobalFilter, Ordered {
	private static final  AntPathMatcher PATH_MATCHER = new AntPathMatcher();
	private final StringRedisTemplate stringRedisTemplate;
	private RequestPathTokenConfig requestPathTokenConfig;
	/**
	 * Process the Web request and (optionally) delegate to the next
	 * {@code WebFilter} through the given {@link GatewayFilterChain}.
	 *
	 * @param exchange the current server exchange
	 * @param chain    provides a way to delegate to the next filter
	 * @return {@code Mono<Void>} to indicate when request processing is complete
	 */
	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		try {
			AfsGateWayHelper.setTracker(exchange);
			SysLogUtils.setSystemName();
			ServerHttpRequest request = exchange.getRequest();
			addOriginalRequestUrl(exchange, request.getURI());
			String rawPath = request.getURI().getRawPath();
			String newPath = "/" + Arrays.stream(StringUtils.tokenizeToStringArray(rawPath, "/")).skip(1L).collect(Collectors.joining("/"));
			String accessToken = AfsGateWayHelper.getHeaderValue(exchange, CommonConstants.ACCESS_TOKEN);
			String adScope = AfsGateWayHelper.getHeaderValue(exchange, CommonConstants.DYNAMIC_SERVICE_HEADER_KEY);
			if (StrUtil.isBlank(accessToken)) {
				//匹配允许requestParams获取token的请求路径
				List<String> collect = requestPathTokenConfig.getUris().stream().filter(pattern -> PATH_MATCHER.match(pattern, rawPath)).collect(Collectors.toList());
				if (collect.size()>0) {
					Map<String, String> params = request.getQueryParams().toSingleValueMap();
					String token = params.get(requestPathTokenConfig.getTokenKey());
					String reqScope = params.get(requestPathTokenConfig.getScopeKey());
					if (StrUtil.isNotBlank(token)) {
						accessToken=token;
					}
					if(StrUtil.isNotBlank(reqScope)) {
						adScope=reqScope;
					}
				}
			}
			String tenantId = AfsGateWayHelper.getHeaderValue(exchange, CommonConstants.TENANT_ID);
			if (StrUtil.isEmpty(accessToken)) {
				List<Map.Entry<String, String>> afsMatches = AfsFilterHelper.getPatternNameSet().stream().filter(pattern -> PATH_MATCHER.match(pattern.getKey(), rawPath)).toList();
				if (afsMatches.size() < 1) {
					ServerHttpRequest.Builder serverHttpBuilder = request.mutate().path(newPath);
					serverHttpBuilder.header(CommonConstants.TENANT_ID, tenantId);
					serverHttpBuilder.header(CommonConstants.REAL_REQUEST_IP, IpUtils.getClientIp(request));
					return doHttpServerRequest(exchange,chain,serverHttpBuilder);
				} else if (afsMatches.size() > 1) {
					ServerHttpResponse response = exchange.getResponse();
					response.setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
					response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
					log.error("[{}] 匹配到多个过滤器，服务端配置错误[{}]", newPath, afsMatches.stream().map(Map.Entry::getValue).collect(Collectors.joining("|")));
					return response.writeWith(Mono.just(response.bufferFactory()
							.wrap(JSON.toJSONBytes(IResponse.fail("service config error")))));
				} else {
					GatewayFilterProcessDto gatewayFilterProcessDto =  AfsFilterHelper.getAfsFilter(afsMatches.get(0).getValue()).processRequest(exchange,chain);
					if(gatewayFilterProcessDto.getFilterProcessResultType()== GatewayFilterProcessDto.FilterProcessResultType.BUILDER){
						return chain.filter(gatewayFilterProcessDto.getBuilder().build());
					}else if(gatewayFilterProcessDto.getFilterProcessResultType()== GatewayFilterProcessDto.FilterProcessResultType.MONO){
						return gatewayFilterProcessDto.getMono();
					}else {
						throw new RuntimeException("不支持的处理类型");
					}
				}
			} else {
				ServerHttpRequest.Builder serverHttpBuilder = request.mutate().path(newPath);
				String tokenMd5 = SecureUtil.md5(accessToken);
				if(Boolean.TRUE.equals(this.stringRedisTemplate.hasKey(ACCESS_TOKEN_REF.concat(tokenMd5)))){
					serverHttpBuilder.header(CommonConstants.AUTHORIZATION, CommonConstants.BEARER + accessToken);
					serverHttpBuilder.header(CommonConstants.TENANT_ID, tenantId);
					serverHttpBuilder.header(CommonConstants.ACCESS_TOKEN, accessToken);
					serverHttpBuilder.header(CommonConstants.REAL_REQUEST_IP, IpUtils.getClientIp(request));
					serverHttpBuilder.header(CommonConstants.DYNAMIC_SERVICE_HEADER_KEY,adScope);
					return doHttpServerRequest(exchange,chain,serverHttpBuilder);
				}else if(Boolean.TRUE.equals(this.stringRedisTemplate.hasKey(AUTH_KICK_OUT.concat(tokenMd5)))){
					ServerHttpResponse response = exchange.getResponse();
					return response.writeWith(Mono.just(response.bufferFactory()
							.wrap(JSON.toJSONBytes(
									IResponse.fail("kick out").setCode(CommonConstants.AUTH_KICK_OUT)))));
				}else{
					ServerHttpResponse response = exchange.getResponse();
					response.setStatusCode(HttpStatus.UNAUTHORIZED);
					response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
					return response.writeWith(Mono.just(response.bufferFactory()
							.wrap(JSON.toJSONBytes(
									IResponse.fail("invalidate token")))));
				}
			}
		}finally {
			MDC.clear();
		}
	}

	private Mono<Void> doHttpServerRequest(ServerWebExchange exchange, GatewayFilterChain chain,ServerHttpRequest.Builder serverHttpBuilder){
		serverHttpBuilder.header(AFS_TRACKER_ID,MDC.get(AFS_TRACKER_ID));
		ServerHttpRequest serverHttpRequest = serverHttpBuilder.build();
		exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, serverHttpRequest.getURI());
		MDC.remove(AFS_TRACKER_ID);
		return chain.filter(exchange.mutate().request(serverHttpRequest).build());
	}
	@Override
	public int getOrder() {
		return Ordered.HIGHEST_PRECEDENCE+2;
	}
}
