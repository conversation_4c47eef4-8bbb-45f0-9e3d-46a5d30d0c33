package com.ruicar.afs.cloud.basic.proceeds.mq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruicar.afs.cloud.basic.common.entity.BasicFinancialAgreement;
import com.ruicar.afs.cloud.common.modules.contract.enums.AdjustTypeEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.AssetTypeEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.CalculateType;
import com.ruicar.afs.cloud.common.modules.contract.enums.DiscountType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Lee
 * @date 2020-05-29 09:31:34
 * @description 合同金融协议
 */
@Data
public class ContractFinancialAgreementDTO extends ContractMqBaseDataDTO<BasicFinancialAgreement>{

	@JsonSerialize(using= ToStringSerializer.class)
	@JSONField(serializeUsing = com.alibaba.fastjson.serializer.ToStringSerializer.class)
	private Long id;
	/** 合同号码 */
	@ApiModelProperty("合同号码")
	private String contractNo;
	/** 车辆ID */
	@ApiModelProperty("车辆ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long carId;
	/** 产品ID */
	@ApiModelProperty("产品ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long productId;
	/** 融资费用类型 车款，附加贷 */
	@ApiModelProperty("融资费用类型 车款，附加贷")
	private AssetTypeEnum costType;
	/** 合同金额 */
	@ApiModelProperty("合同金额")
	private BigDecimal contractAmt;
	/** 客户利率/贴息后利率 */
	@ApiModelProperty("客户利率/贴息后利率")
	private Double custRate;
	/** 结算利率 */
	@ApiModelProperty("结算利率")
	private Double settleRate;
	/** 上调/下调 枚举值：上调，下调 */
	@ApiModelProperty("上调/下调 枚举值：上调，下调")
	private AdjustTypeEnum adjustType;
	/** 调整基点 */
	@ApiModelProperty("调整基点")
	private Integer basicPoint;
	/** 首付比例 */
	@ApiModelProperty("首付比例")
	private Double downPayScale;
	/** 首付金额 */
	@ApiModelProperty("首付金额")
	private BigDecimal downPayAmt;
	/** 尾付比例 */
	@ApiModelProperty("尾付比例")
	private Double tailPayScale;
	/** 尾付金额 */
	@ApiModelProperty("尾付金额")
	private BigDecimal tailPayAmt;
	/** 贴息总额 */
	@ApiModelProperty("贴息总额")
	private BigDecimal totalDiscountAmt;
	/** 贷款金额 */
	@ApiModelProperty("贷款金额")
	private BigDecimal loanAmt;
	/** 融资期数 */
	@ApiModelProperty("融资期数")
	private Integer loanTerm;
	/** 月供金额 */
	@ApiModelProperty("月供金额")
	private BigDecimal monthPayAmt;
	/** 厂商最大贴息额 */
	@ApiModelProperty("厂商最大贴息额")
	private BigDecimal maxDiscountAmt;
	/** 算法类型 */
	@ApiModelProperty("算法类型")
	private CalculateType algorithmType;
	/** 贴息方式 */
	@ApiModelProperty("贴息方式")
	private DiscountType discountType;

	/** 是否有子产品 */
	@ApiModelProperty("是否有子产品")
	private String isSubProduct;
	/** 附加金额 */
	@ApiModelProperty("附加金额")
	private BigDecimal addAmt;
	@ApiModelProperty("利息收入")
	private BigDecimal interestIncome;

	/**
	 * 付款金额
	 */
	private BigDecimal riskLoanAmt;
	@ApiModelProperty("厂商保证金比例(%)")
	private BigDecimal firmMarginRatio;
	@ApiModelProperty("厂商保证金金额")
	private BigDecimal firmMarginAmt;
	@ApiModelProperty("客户保证金比例(%)")
	private BigDecimal custMarginRatio;
	@ApiModelProperty("客户保证金金额")
	private BigDecimal custMarginAmt;
	@ApiModelProperty("服务费金额")
	private BigDecimal handlingFeeAmount;
	@ApiModelProperty(value = "厂商手续费比例(%)")
	private String firmHandlingRatio;
	@ApiModelProperty(value = "厂商手续费金额")
	private BigDecimal firmHandlingAmt;
	@ApiModelProperty(value = "客户手续费比例(%)")
	private String custHandlingRatio;
	@ApiModelProperty(value = "客户手续费金额")
	private BigDecimal custHandlingAmt;
	@ApiModelProperty(value = "厂商服务费比例(%)")
	private String firmServiceRatio;
	@ApiModelProperty(value = "厂商服务费金额")
	private BigDecimal firmServiceAmt;
	@ApiModelProperty(value = "客户服务费比例(%)")
	private String custServiceRatio;
	@ApiModelProperty(value = "客户服务费金额")
	private BigDecimal custServiceAmt;

	@ApiModelProperty(value = "GPS主键id")
	private String gpsId;
	@ApiModelProperty(value = "GPS厂商")
	private String gpsFirm;
	@ApiModelProperty(value = "GPS成本价")
	private BigDecimal gpsCostPrice;
	@ApiModelProperty(value = "首期支付类型:advancePayment-先付;afterPayment-后付")
	private String downPaymentType;
	@ApiModelProperty(value = "首付收款方式:write_off-收款核销;deduct-坐扣")
	private String downPaymentMethod;
	@ApiModelProperty(value = "租金支付日")
	private Integer rentPaymentDate;
	/**20220414 zys*/
	@ApiModelProperty(value = "还款日类型:equalPeriod-等期;fixedDay-固定日;customize-自定义")
	private String repaymentDateType;
	@ApiModelProperty(value = "还款日明细")
	private Integer repaymentDateDetail;
	@ApiModelProperty(value = "xirr")
	private BigDecimal xirr;
	@ApiModelProperty(value = "irr")
	private BigDecimal irr;
	@ApiModelProperty(value = "还款频率 1-月付;2-双月;3-季付;6-半年;12-年付")
	private String repaymentFrequency;
	@ApiModelProperty(value = "残值")
	@JSONField(name = "residual_value")
	@JsonProperty("residual_value")
	private BigDecimal residualValue;
	@ApiModelProperty(value = "返利金额")
	private BigDecimal rebate;
	@ApiModelProperty(value = "购置税手续费比例(%)",example = "20")
	private String purchaseHandlingRatio;
	@ApiModelProperty(value = "购置税手续费金额",example = "30000")
	private BigDecimal purchaseHandlingAmt;
	@ApiModelProperty(name = "是否补足期数",example = "0-是,1-否")
	private String supplementTerms;
	@ApiModelProperty(value = "还款期次", example = "7")
	private Integer repaymentTerm;

	@ApiModelProperty(value = "结构化期数", example = "7")
	private Integer structuredMonth = 0;

}
