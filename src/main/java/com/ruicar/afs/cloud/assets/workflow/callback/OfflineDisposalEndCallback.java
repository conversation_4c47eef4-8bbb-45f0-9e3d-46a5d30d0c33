package com.ruicar.afs.cloud.assets.workflow.callback;

import com.ruicar.afs.cloud.assets.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.assets.workflow.enums.CollectStatusEnum;
import com.ruicar.afs.cloud.assets.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.assets.workflow.event.OfflineDisposalEndEvent;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * @ClassName: OfflineDisposalEndCallback
 * @Description: 线下处置出库结束回调
 * <AUTHOR>
 * @Date 2023/2/20
 * @Version 1.0
 */
@Slf4j
@AllArgsConstructor
@Component
public class OfflineDisposalEndCallback implements CommonAdapter {

    private ApplicationEventPublisher eventPublisher;

    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
                                       String extParam, Map<String, String> flowVariables) {
        log.info("flow : {} variables : {}", flowInstanceId, flowVariables);
        // TODO 验证 修改订单状态 + 通知前置业务系统
        final String applyNo = flowVariables.get(FlowConstant.BUSINESS_NO);
        final String contractNo = flowVariables.get(FlowConstant.CONTRACT_NO);

        switch (FlowTaskOperationEnum.valueOf(flowVariables.get(FlowConstant.LAST_OPERATION))) {
            case SUBMIT:
                // 正常通过
                eventPublisher.publishEvent(
                        new OfflineDisposalEndEvent(this, applyNo, contractNo, flowInstanceId,
                                CollectStatusEnum.OFFLINEDISPOSAL_APPROVE,
                                flowVariables.get(FlowConstant.LAST_APPROVE_REASON),
                                flowVariables.get(FlowConstant.LAST_APPROVE_REMARK),
                                flowVariables.get(FlowConstant.LAST_OPERATOR_LOGIN_NAME)
                        ));
                break;
            case REFUSE:
                // 拒绝
                eventPublisher.publishEvent(
                        new OfflineDisposalEndEvent(this, applyNo, contractNo, flowInstanceId,
                                CollectStatusEnum.SUGGEST_REJECT_FINAL,
                                flowVariables.get(FlowConstant.LAST_APPROVE_REASON),
                                flowVariables.get(FlowConstant.LAST_APPROVE_REMARK),
                                flowVariables.get(FlowConstant.LAST_OPERATOR_LOGIN_NAME)
                        ));
                break;
            default:
                log.info("not support operation : {}", flowVariables.get(FlowConstant.LAST_OPERATION));
        }

        return Collections.singletonMap("CollectVisitInfoChangeCallback", "ok");
    }
}
