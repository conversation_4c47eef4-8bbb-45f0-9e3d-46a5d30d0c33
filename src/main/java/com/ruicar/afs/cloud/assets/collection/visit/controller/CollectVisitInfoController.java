package com.ruicar.afs.cloud.assets.collection.visit.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.assets.collection.collect.entity.CollectUserEntity;
import com.ruicar.afs.cloud.assets.collection.collect.service.CollectUserService;
import com.ruicar.afs.cloud.assets.collection.collecting.enums.ManualTypeEnum;
import com.ruicar.afs.cloud.assets.collection.inneruser.entity.AssetsCollectInnerUserEntity;
import com.ruicar.afs.cloud.assets.collection.inneruser.service.AssetsCollectInnerUserService;
import com.ruicar.afs.cloud.assets.collection.visit.condition.CollectVisitInfoCondition;
import com.ruicar.afs.cloud.assets.collection.visit.entity.CollectVisitInfo;
import com.ruicar.afs.cloud.assets.collection.visit.service.CollectVisitInfoService;
import com.ruicar.afs.cloud.assets.collection.visit.vo.CollectVisitInfoVo;
import com.ruicar.afs.cloud.assets.common.dto.CollectInfoTaskCondition;
import com.ruicar.afs.cloud.assets.common.vo.CollectInfoTaskInfoVo;
import com.ruicar.afs.cloud.assets.parameter.common.vo.CollectButtonVo;
import com.ruicar.afs.cloud.assets.workflow.service.WorkflowTaskCommonService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 现场催收信息
 *
 * <AUTHOR>
 * @email
 * @date 2023-02-27 15:16:15
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/collectVisitInfo")
@Api("现场催收信息控制类")
public class CollectVisitInfoController {
    @Autowired
    private CollectVisitInfoService collectVisitInfoService;

    private WorkflowTaskCommonService workflowTaskCommonService;

    private CollectUserService collectUserService;

    private AssetsCollectInnerUserService assetsCollectInnerUserService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @ApiOperation("列表")
    public IResponse list(@RequestBody QueryCondition<CollectVisitInfoCondition> condition) {
        IPage<CollectVisitInfo> page = collectVisitInfoService.queryPage(condition);

        List<CollectVisitInfoVo> list = new ArrayList<CollectVisitInfoVo>();

        page.getRecords().forEach(batch -> {
                    CollectVisitInfoVo vo = new CollectVisitInfoVo();
                    BeanUtils.copyProperties(batch, vo);
                    vo.setCreatedByName(collectUserService.getUserRealName(vo.getCreatedBy()));

                    if(ManualTypeEnum.OUTER_TYPE.getCode().equals(batch.getVisitUserType())){
                        if(!StringUtils.isEmpty(batch.getVisitPerson())){
                            CollectUserEntity collectUserEntity = collectUserService.queryCollectUserById(Long.parseLong(batch.getVisitPerson()));
                            if(collectUserEntity!=null){
                                vo.setVisitPersonName(collectUserEntity.getUsername());
                            }
                        }
                    }else {
                        if(!StringUtils.isEmpty(batch.getVisitPerson())){
                            AssetsCollectInnerUserEntity innerUser = assetsCollectInnerUserService.getById(Long.parseLong(batch.getVisitPerson()));
                            if(innerUser!=null){
                                vo.setVisitPersonName(innerUser.getUserRealName());
                            }
                        }
                    }
                    list.add(vo);
                }
        );
        IPage<CollectVisitInfoVo> newPage = new Page<CollectVisitInfoVo>();
        newPage.setRecords(list);
        newPage.setPages(page.getPages());
        newPage.setCurrent(page.getCurrent());
        newPage.setSize(page.getSize());
        newPage.setTotal(page.getTotal());
        return IResponse.success(newPage);
    }

    /**
     * 增加保存
     */
    @PostMapping("/addCollectVisitInfo")
    @ApiOperation("增加保存")
    public IResponse saveCollectVisitInfo(@RequestBody CollectVisitInfo entity) {
        return IResponse.success(collectVisitInfoService.saveCollectVisitInfo(entity));
    }

    /**
     * 提交申请
     */
    @PostMapping("/submitCollectVisitInfo")
    @ApiOperation("提交")
    public IResponse submitCollectVisitInfo(@RequestBody CollectVisitInfo entity) {
        return collectVisitInfoService.submitCollectVisitInfo(entity);
    }

    /**
     * 修改
     */
    @PostMapping("/modifyCollectVisitInfo")
    @ApiOperation("修改")
    public IResponse updateCollectVisitInfo(@RequestBody CollectVisitInfo entity) {
        return IResponse.success(collectVisitInfoService.updateCollectVisitInfo(entity));
    }

    @RequestMapping(value = "/getCollectVisitInfo/{id}")
    @ApiOperation("根据id查询 ")
    public IResponse queryCollectVisitInfoeById(@PathVariable Long id) {
        return IResponse.success(collectVisitInfoService.queryCollectVisitInfoById(id));
    }

    @RequestMapping(value = "/deleteCollectVisitInfo/{id}")
    @ApiOperation("根据id删除 ")
    @Transactional(rollbackFor = Exception.class)
    public IResponse deleteCollectVisitInfoeById(@PathVariable Long id) {
        return IResponse.success(collectVisitInfoService.deleteCollectVisitInfoById(id));
    }

    @PostMapping(value = "/getCollectVisitworkList")
    @ApiOperation(value = "多条件分页获取待审核任务--仅展示当前登录用户的待审核任务")
    public IResponse<IPage<CollectInfoTaskInfoVo>> getCollectVisitInfoTask(@RequestBody CollectInfoTaskCondition condition) {
        Assert.hasLength(condition.getPackageId(), "流程包id不能为空");
        Assert.hasLength(condition.getTemplateId(), "流程模版id不能为空");
        Assert.hasLength(condition.getUserDefinedIndex(), "流程节点自定义key不能为空");
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        return IResponse.success(collectVisitInfoService.queryCollectVisitInfoTaskList(page, condition));
    }

    @PostMapping(value = "/collectVisitworkButton")
    @ApiOperation(value = "审核通过、拒绝、退件")
    public IResponse collectVisitworkButton(@RequestBody CollectButtonVo buttonVo) {
        return workflowTaskCommonService.comprehensiveButton(buttonVo);
    }
    @PostMapping(value = "/cancelFlow/{id}")
    @ApiOperation("根据CollectId撤回流程 ")
    @Transactional(rollbackFor = Exception.class)
    public IResponse cancelFlow(@PathVariable String id) {
        collectVisitInfoService.cancelFlowForCollectId(id);
        return IResponse.success(null);
    }

}
