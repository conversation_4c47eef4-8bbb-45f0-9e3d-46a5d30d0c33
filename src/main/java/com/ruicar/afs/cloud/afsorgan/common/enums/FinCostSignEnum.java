package com.ruicar.afs.cloud.afsorgan.common.enums;

import com.ruicar.afs.cloud.common.core.enums.AfsBaseEnum;
import com.ruicar.afs.cloud.common.core.enums.annotations.AfsEnum;
import lombok.Getter;

@Getter
public enum FinCostSignEnum implements AfsBaseEnum {

    /**
     * 审批阶段
     */
    @AfsEnum(key = "approve", desc = "审批阶段")
    APPROVE("approve", "审批阶段"),
    /**
     * 放款阶段
     */
    @AfsEnum(key = "generalLoan", desc = "放款阶段")
    GENERAL_LOAN("generalLoan", "放款阶段"),;

    public String state;
    public String desc;

    FinCostSignEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    }
