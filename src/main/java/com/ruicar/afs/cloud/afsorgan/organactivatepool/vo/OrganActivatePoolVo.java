package com.ruicar.afs.cloud.afsorgan.organactivatepool.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 放款待激活池VO类
 * @date 2020/5/18 17:45
 */
@Data
public class OrganActivatePoolVo {

    /**
     * 主键
     */
    private String id;
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 经销商姓名
     */
    private String dealerName;

    /**
     * 放款审核状态
     */
    private String loanApproveStatus;

    /**
     * 放款审核通过时间
     */
    private Date loanApproveTime;

    /**
     * GPS审核状态
     */
    private String gpsApproveStatus;

    /**
     * GPS审核通过时间
     */
    private Date gpsApproveTime;

    /**
     * 额度判断状态
     */
    private String limitApproveStatus;

    /**
     * 额度审核通过时间
     */
    private Date limitApproveTime;

    /**
     * 激活状态
     */
    private String actStatus;

    /**
     * 激活时间
     */
    private Date actTime;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 车辆属性
     */
    private String carNature;

    /**
     * 运营方式
     */
    private String operateWay;

    /**
     * 挂靠方式
     */
    private String affiliatedWay;

    /**
     * 放款模式
     */
    private String lendingMode;

    /**
     * 落库通知
     */
    private String contractActivateFlag;
    /**
     * 是否归档超期
     */
    private String isOverdue;
    /**
     * 是否超时效
     */
    private String isOverTime;

    /**激活步骤*/
    private int contractActiveSteps;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 激活类型
     */
    private String actType;

    /**
     * 贷款金额
     */
    private BigDecimal amounts;
}

