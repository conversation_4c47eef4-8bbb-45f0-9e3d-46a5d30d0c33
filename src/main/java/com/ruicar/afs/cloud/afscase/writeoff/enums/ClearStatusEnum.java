package com.ruicar.afs.cloud.afscase.writeoff.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 清账状态
 */
@Getter
@AllArgsConstructor
public enum ClearStatusEnum {

    /**
     * 待清账
     */
    WAIT_CLEAR("0", "待清账"),
    /**
     * 已清账
     */
    CLEAR_END("1", "已清账"),
    /**
     * 清账失败
     */
    CLEAR_FAIL("2", "清账失败");
    /**
     * The Code.
     */
    String code;
    /**
     * The Desc.
     */
    String desc;

    private static Map<String, ClearStatusEnum> map = new HashMap<>(ClearStatusEnum.values().length);

    static {
        for (ClearStatusEnum value : ClearStatusEnum.values()) {
            map.put(value.getCode(), value);
        }
    }

    /**
     * Create ClearStatusEnum
     *
     * @param code the code
     * @return the enum
     */
    public static ClearStatusEnum create(String code) {
        return map.get(code);
    }
}
