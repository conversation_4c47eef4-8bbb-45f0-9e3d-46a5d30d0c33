package com.ruicar.afs.cloud.afscase.mq.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.mq.condition.AffiliatedCompanyFilingCondition;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.vo.AffiliatedCompanyFilingVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;


public interface AffiliatedCompanyFilingMapper extends BaseMapper<AffiliatedCompanyFiling> {
    /**
     * 获取备案任务信息列表
     * @param page
     * @param companyFilingCondition
     * @return
     */
    IPage<List<AffiliatedCompanyFilingVo>> queryApproveTaskLaunchList(Page page, @Param("condition") AffiliatedCompanyFilingCondition companyFilingCondition);
}
