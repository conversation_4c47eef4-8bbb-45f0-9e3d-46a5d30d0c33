package com.ruicar.afs.cloud.afscase.archive.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.archive.dto.ArchiveApiDto;
import com.ruicar.afs.cloud.afscase.archive.dto.SummaryInfoDto;
import com.ruicar.afs.cloud.afscase.archive.service.ArchiveApiService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelNetwork;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelQuotaInfo;
import com.ruicar.afs.cloud.afscase.channel.feign.ChannelFeignService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelNetworkService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelQuotaInfoService;
import com.ruicar.afs.cloud.afscase.channel.utils.CaseConfig;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CasePriorityRecord;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityChangeService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityRecordService;
import com.ruicar.afs.cloud.archive.dto.FileArchivedDto;
import com.ruicar.afs.cloud.archive.dto.MultiDealerDto;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsTypeNumEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanModelEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PaymentObjectEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.QuotaStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.QuotaTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.SignTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.ruicar.afs.cloud.afscase.channel.common.Constants.DIRECT;

/**
 * @Description:
 * @Author: fangchenliang
 * @Date: 2020/8/23 16:43
 */
@Service
@AllArgsConstructor
@Slf4j
@Data
public class ArchiveApiServiceImpl implements ArchiveApiService {

    private CaseContractInfoService caseContractInfoService;

    private ChannelQuotaInfoService channelQuotaInfoService;

    private CaseBaseInfoService caseBaseInfoService;

    private ChannelBaseInfoService channelBaseInfoService;

    private CaseChannelInfoService caseChannelInfoService;

    private ApplyCostDetailsService applyCostDetailsService;

    private CaseConfig caseConfig;

    private ChannelFeignService channelFeignService;


    private CasePriorityRecordService casePriorityRecordService;

    private CasePriorityChangeService casePriorityChangeService;

    private final ChannelNetworkService channelNetworkService;


    private final CaseChannelUniteInfoService caseChannelUniteInfoService;

    private final CaseCarInfoService caseCarInfoService;
    private final CaseCustInfoService caseCustInfoService;


    public JSONObject releaseQuota(ArchiveApiDto dto) {
        String contractId = dto.getContractId();
        String contractNo = dto.getContractNo();
        JSONObject jsonObject = new JSONObject();
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getContractNo, contractNo)
        );
        if (StringUtils.isNotBlank(contractId) && ObjectUtils.isEmpty(caseContractInfo)) {
            caseContractInfo = caseContractInfoService.getById(Long.valueOf(contractId));
            log.info("======{}额度释放开始=====", contractId);
        }else {
            log.info("======{}额度释放开始=====", contractNo);
        }
        //重复调用返回成功
        if(caseContractInfo != null && QuotaStatusEnum.release.equals(caseContractInfo.getQuotaStatus())){
            jsonObject.put("success",1);
            jsonObject.put("msg","成功");
            log.info("======{}此合同额度已释放过,重复调用额度释放接口,返回成功标识=====",caseContractInfo.getContractNo());
            return jsonObject;
        }
        if (!ObjectUtils.isEmpty(caseContractInfo)) {
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()));
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, caseContractInfo.getApplyNo()));
            ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
            String quotaType = transLoanMode(caseContractInfo.getLendingMode());
            // 声明额度的信息
            List<ChannelQuotaInfo> channelQuotaInfoList = new ArrayList<>();

            // 这个地方需要判断这个合作商是不是切换主体的
            List<ChannelNetwork> networkList =channelNetworkService.list(Wrappers.<ChannelNetwork>query().lambda().eq(ChannelNetwork::getParentId,channelBaseInfo.getId().toString()).eq(ChannelNetwork::getBusinessType,caseBaseInfo.getBusinessType()));
            if(networkList.size()>0){
                // 说明这个合作商是被切换主体的
                channelQuotaInfoList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfo>query().lambda()
                        .eq(ChannelQuotaInfo::getChannelId, networkList.get(0).getChannelId().toString())
                        .eq(ChannelQuotaInfo::getQuotaType, quotaType));
            }else {
                channelQuotaInfoList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfo>query().lambda()
                        .eq(ChannelQuotaInfo::getChannelId, channelBaseInfo.getId())
                        .eq(ChannelQuotaInfo::getQuotaType, quotaType));
            }
            if(!CollectionUtils.isEmpty(channelQuotaInfoList)){
                List<ChannelQuotaInfo> totalChannelQuotaInfoList = new ArrayList<>();
                CaseContractInfo finalCaseContractInfo = caseContractInfo;
                channelQuotaInfoList.forEach(channelQuotaInfo -> {
                    log.info("{}合作商额度释放开始", finalCaseContractInfo.getContractNo());
                    List<FinCostDetails> costDetailsList = applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, finalCaseContractInfo.getApplyNo()));
                    BigDecimal amounts = costDetailsList.stream().map(cost -> cost.getLoanAmt())
                            .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                    /**剩余额度*/
                    BigDecimal surplusQuota = channelQuotaInfo.getSurplusQuota() == null ? BigDecimal.ZERO : channelQuotaInfo.getSurplusQuota();
                    /**占有额度*/
                    BigDecimal occupiedQuot = channelQuotaInfo.getOccupiedQuota() == null ? BigDecimal.ZERO : channelQuotaInfo.getOccupiedQuota();
                    channelQuotaInfo.setOccupiedQuota(occupiedQuot.subtract(amounts).setScale(2, BigDecimal.ROUND_HALF_UP));
                    channelQuotaInfo.setSurplusQuota(surplusQuota.add(amounts).setScale(2, BigDecimal.ROUND_HALF_UP));
                    channelQuotaInfoService.updateById(channelQuotaInfo);
                    //同步渠道服务,更新额度信息
                    totalChannelQuotaInfoList.add(channelQuotaInfo);
                    if (DIRECT.equals(channelBaseInfo.getChannelBelong())) {
                        ChannelQuotaInfo directCarDealerQuotaInfo = channelQuotaInfoService.getOne(Wrappers.<ChannelQuotaInfo>query().lambda()
                                .eq(ChannelQuotaInfo::getAssociatedChannelId, channelBaseInfo.getId())
                                .eq(ChannelQuotaInfo::getChannelId, caseChannelInfo.getCarDealersId())
                                .eq(ChannelQuotaInfo::getBusinessType, caseBaseInfo.getBusinessType())
                                .eq(ChannelQuotaInfo::getQuotaType, quotaType));
                        if (!ObjectUtils.isEmpty(directCarDealerQuotaInfo)) {
                            log.info("{}直营车商额度释放开始", finalCaseContractInfo.getContractNo());
                            /**剩余额度*/
                            BigDecimal carDealerSurplusQuota = directCarDealerQuotaInfo.getSurplusQuota() == null ? BigDecimal.ZERO : directCarDealerQuotaInfo.getSurplusQuota();
                            /**占有额度*/
                            BigDecimal carDealerOccupiedQuot = directCarDealerQuotaInfo.getOccupiedQuota() == null ? BigDecimal.ZERO : directCarDealerQuotaInfo.getOccupiedQuota();
                            directCarDealerQuotaInfo.setOccupiedQuota(carDealerOccupiedQuot.subtract(amounts).setScale(2, BigDecimal.ROUND_HALF_UP));
                            directCarDealerQuotaInfo.setSurplusQuota(carDealerSurplusQuota.add(amounts).setScale(2, BigDecimal.ROUND_HALF_UP));
                            channelQuotaInfoService.updateById(directCarDealerQuotaInfo);
                            //同步渠道服务,更新额度信息
                            totalChannelQuotaInfoList.add(directCarDealerQuotaInfo);
                        }
                    }
                });
                //同步渠道服务,更新额度信息
                Map<String, String> headers = new HashMap<>();
                headers.put("clientId", caseConfig.getChannelClientId());
                headers.put("clientSecret", caseConfig.getChannelClientSecret());
                channelFeignService.updateChannelQuota(totalChannelQuotaInfoList, headers);
                //合同表额度状态字段改成释放
                finalCaseContractInfo.setQuotaStatus(QuotaStatusEnum.release);
                caseContractInfoService.updateById(finalCaseContractInfo);
                log.info("{}合同额度释放成功", finalCaseContractInfo.getContractNo());
                jsonObject.put("success",1);
                jsonObject.put("msg","成功");
                return jsonObject;
            } else {
                jsonObject.put("success",0);
                jsonObject.put("code","0");
                jsonObject.put("msg","失败");
                return jsonObject;
            }
        } else {
            jsonObject.put("success",0);
            jsonObject.put("code","0");
            jsonObject.put("msg","失败");
            log.info("======{}此合同号查不到合同信息,额度释放失败=====", contractNo);
            return jsonObject;
        }
    }

    public JSONObject removeOverdue(String dataId) {
        JSONObject jsonObject = new JSONObject();
        log.info("======{}合作商超期解除开始=====", dataId);
        ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, dataId));
        if (ObjectUtils.isEmpty(channelBaseInfo)) {
            channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getId, Long.valueOf(dataId)));
        }
        if (!ObjectUtils.isEmpty(channelBaseInfo)) {
            channelBaseInfo.setIsOverdue(WhetherEnum.NO.getCode());
            channelBaseInfoService.updateById(channelBaseInfo);
            List<CaseContractInfo> caseContractInfos = new ArrayList<>();
            caseContractInfos=caseContractInfoService.getOverdueContractByDirect(String.valueOf(channelBaseInfo.getId()));
            if(CollectionUtils.isEmpty(caseContractInfos)){
                caseContractInfos = caseContractInfoService.getOverdueContractByChannel(channelBaseInfo.getChannelCode());
            }
            if(!CollectionUtils.isEmpty(caseContractInfos)){
                if(AfsEnumUtil.key(ChannelBelongEnum.DIRECT).equals(channelBaseInfo.getChannelBelong())&&AfsEnumUtil.key(PaymentObjectEnum.car_dealer).equals(channelBaseInfo.getPaymentObject())){
                    log.info("======{}该合作商为直营车商，无需解除此合作商下的案件超期状态！",dataId);
                } else {
                    caseContractInfos.forEach(contract -> {
                        casePriorityRecordService.list(Wrappers.<CasePriorityRecord>lambdaQuery()
                                .eq(CasePriorityRecord::getContractNo, contract.getContractNo()).orderByDesc(CasePriorityRecord::getCreateTime));
                        contract.setIsOverdue(WhetherEnum.NO.getCode());
                        contract.setPriority(casePriorityChangeService.priorityChange(contract));
                        caseContractInfoService.updateById(contract);
                    });
                }
            }
            jsonObject.put("code","1");
            jsonObject.put("msg","成功");
            log.info("======{}合作商超期解除结束=====", dataId);
            return jsonObject;
        } else {
            jsonObject.put("code","0");
            jsonObject.put("msg","失败");
            log.info("======{}合作商超期解除结束=====", dataId);
            return jsonObject;
        }
    }

    private String transLoanMode(String lendingMode) {
        if (!ObjectUtils.isEmpty(lendingMode)) {
            if (lendingMode.equals(AfsEnumUtil.key(LoanModelEnum.sign))) {
                /**渠道额度表的额度类型：1，表示签约放款；2，表示抵押签放款*/
                return QuotaTypeEnum.sign.getCode();
            } else if (lendingMode.equals(AfsEnumUtil.key(LoanModelEnum.beforeMortgage))) {
                return QuotaTypeEnum.beforeMortgage.getCode();
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    public Boolean setOverdue(CaseContractInfo caseContractInfo){
        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, caseContractInfo.getApplyNo()));
        ChannelBaseInfo channelBaseInfo =null;
        if (null != caseChannelInfo) {
            channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                    .eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()), false);
            if(AfsEnumUtil.key(ChannelBelongEnum.DIRECT).equals(channelBaseInfo.getChannelBelong())&&AfsEnumUtil.key(PaymentObjectEnum.car_dealer).equals(channelBaseInfo.getPaymentObject())){
                if(StrUtil.isNotBlank(caseChannelInfo.getCarDealersId())){
                    channelBaseInfo=channelBaseInfoService.getById(Long.valueOf(caseChannelInfo.getCarDealersId()));
                }
            }
        }
        Assert.isTrue(channelBaseInfo!=null,caseContractInfo.getContractNo()+"未找到合作商信息");
        MultiDealerDto dealerDto =new MultiDealerDto();
        StringBuffer ids = new StringBuffer();
        Set<Long> channelIdSetList = channelNetworkService.getChannelRelation(channelBaseInfo.getId());
        if(!CollectionUtils.isEmpty(channelIdSetList)) {
            Object[] channelIdList = channelIdSetList.toArray();
            for (int i = 0; i < channelIdList.length; i++) {
                if (i != channelIdList.length - 1) {
                    ids.append(channelIdList[i]).append(",");
                } else {
                    ids.append(channelIdList[i]);
                }
            }
            dealerDto.setSigndealerIds(ids.toString());
            log.info("合同{},经销商{},调用多个经销商超期信息接口入参:{}！",caseContractInfo.getContractNo(),channelBaseInfo.getChannelFullName(),dealerDto);
            throw new AfsBaseException("接口异常，请联系管理员！");
        } else {
            log.info("合同{},经销商{},未找到经销商信息", channelBaseInfo.getChannelFullName(), caseContractInfo.getContractNo());
            return false;
        }
    }


    /**
     * 获取归档列表
     *
     * @param dto
     * @return
     */
    public IResponse getArchivedFileList(FileArchivedDto dto) {
        return IResponse.fail("接口异常，请联系管理员！");
    }

    /**
     * 获取概要信息数据
     * @param applyNo
     * @return
     */
    @Override
    public IResponse getSummaryInformation(String applyNo) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda().eq(CaseContractInfo::getApplyNo, applyNo));
        Assert.notNull (caseContractInfo , "合同信息不存在");
        FinCostDetails finCostDetails = applyCostDetailsService.getOne(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, applyNo));
        Assert.notNull (finCostDetails , "融资信息不存在");
        CaseChannelUniteInfo caseChannelUniteInfo = caseChannelUniteInfoService.getOne(Wrappers.<CaseChannelUniteInfo>lambdaQuery().eq(CaseChannelUniteInfo::getApplyNo, applyNo));
        Assert.notNull (caseChannelUniteInfo , "联合方信息不存在");
        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, applyNo));
        Assert.notNull (caseChannelInfo , "经销商信息不存在");
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,applyNo));
        Assert.notNull (caseBaseInfo , "挂靠信息不存在");

        /**查询授权区域*/
        String authorizedArea = caseCarInfoService.getTitleByApply(applyNo, caseBaseInfo.getBusinessType());

        SummaryInfoDto summaryInfoDto = new SummaryInfoDto();
        //申请编号
        summaryInfoDto.setApplyNo(applyNo);
        //合同编号
        summaryInfoDto.setContractNo(caseContractInfo.getContractNo());
        //合同放款日期
        summaryInfoDto.setLoanDate(caseContractInfo.getLoanDate());
        //融资金额
        summaryInfoDto.setLoanMoney(String.valueOf(finCostDetails.getLoanAmt()));
        //附加金额
        summaryInfoDto.setAddAmount(finCostDetails.getAddAmt());
        //联合方
        summaryInfoDto.setUniteName(caseChannelUniteInfo.getUniteName());
        //经销商名称
        summaryInfoDto.setChannelName(caseChannelInfo.getDealerName());
        //授权区域
        summaryInfoDto.setAuthorizedArea(authorizedArea);
        //产品名称
        summaryInfoDto.setProducteName(caseContractInfo.getProductName());
        //签约方式
        if(null == caseContractInfo.getSignType() || "".equals(caseContractInfo.getSignType())){
            summaryInfoDto.setSignType(AfsEnumUtil.desc(SignTypeEnum.offline));
        }else if(caseContractInfo.getSignType().equals(AfsEnumUtil.key(SignTypeEnum.online))){
            summaryInfoDto.setSignType(AfsEnumUtil.desc(SignTypeEnum.online));
        }else if(caseContractInfo.getSignType().equals(AfsEnumUtil.key(SignTypeEnum.offline))){
            summaryInfoDto.setSignType(AfsEnumUtil.desc(SignTypeEnum.offline));
        }
        //文件状态
        summaryInfoDto.setFileStatus("");//暂时传空
        //是否挂靠
        if(!AffiliatedWayEnum.NO.getCode().equals(caseBaseInfo.getAffiliatedWay())){
            summaryInfoDto.setIsGk(IsTypeNumEnum.YES.getCode());
        }
        else{
            summaryInfoDto.setIsGk(IsTypeNumEnum.NO.getCode());
        }
        //车辆类型
        summaryInfoDto.setVehicleType(caseBaseInfo.getCarType());
        //客戶姓名 证件号
        CaseCustInfo cust = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo).
                        eq(CaseCustInfo::getCustRole, AfsEnumUtil.key(CustRoleEnum.MIANCUST)));
        if(null == cust){
            return IResponse.fail("客戶信息不存在");
        }
        summaryInfoDto.setCustName(cust.getCustName());
        summaryInfoDto.setCustCertNo(cust.getCertNo());
        //车牌号
        CaseCarInfo car = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, applyNo));
        if(null == car){
            return IResponse.fail("车辆信息不存在");
        }
        summaryInfoDto.setIndBusinessName(car.getIndBusinessName());
        summaryInfoDto.setIndBusinessUsci(car.getIndBusinessUsci());
        summaryInfoDto.setCarNumber(car.getLicensePlate());
        log.info("summaryInfoDto:{}", JSONUtil.parse(summaryInfoDto));
        return IResponse.success(summaryInfoDto);
    }
}

