package com.ruicar.afs.cloud.afscase.approvetask.service;

import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveSubmitVO;

import java.util.List;

/**
 * 功能说明:
 * <AUTHOR>
 */
public interface FaceReviewHandlerService {
    /**
     * 视频面审处理
     * @param approveSubmitVO 申请编号
     */
    void handler(ApproveSubmitVO approveSubmitVO);

    /**
     * 视频面审影像件处理
     * @param fileUrlList 视频面审影像信息
     * @param applyNo 申请编号
     */
    void handlerImages(List<String> fileUrlList, String applyNo);
}
