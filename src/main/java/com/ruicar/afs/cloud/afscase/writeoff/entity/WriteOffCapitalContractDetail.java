package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资方服务费合同明细表
 */
@TableName(value ="write_off_capital_contract_detail")
@Data
public class WriteOffCapitalContractDetail extends BaseEntity<WriteOffCapitalContractDetail> implements Serializable {
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 所属资方
     */
    private String belongingCapital;
    /**
     * 资方回款时间
     */
    private Date capitalReturnTime;
    /**
     * 是否生成了核销项(1是，0否)
     */
    private String writeOffFlag;
    /**
     * 是否应收对账完成(1是，0否)
     */
    private String receiveFinishFlag;
    /**
     * 应收对账确认时间
     */
    private Date receiveConfirmTime;
    /**
     * 合同状态
     */
    private ContractStatusEnum contractStatus;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 经销商代码
     */
    private String channelCode;
    /**
     * 经销商名称
     */
    private String channelFullName;
    /**
     * 合同取消时间
     */
    private Date cancelDate;
    /**
     * 提前结清时间
     */
    private Date settleDate;
    /**
     * 已偿期数
     */
    private Integer paidTermNo;
    /**
     * 应扣金额
     */
    private BigDecimal deductAmt;
}
