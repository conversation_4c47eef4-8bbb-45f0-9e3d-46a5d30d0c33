package com.ruicar.afs.cloud.afscase.mq.enums;

import com.ruicar.afs.cloud.common.core.enums.AfsBaseEnum;
import com.ruicar.afs.cloud.common.core.enums.annotations.AfsEnum;

public enum FilingStatusEnum implements AfsBaseEnum {

    /**
     * 待提交
     */
    @AfsEnum(key = "01",desc = "待提交")
    SUBMIT_WAIT,
    /**
     * 审核中
     */
    @AfsEnum(key = "02",desc = "审核中")
    APPROVE,
    /**
     * 退回
     */
    @AfsEnum(key = "03",desc = "退回")
    SUSPEND,
    /**
     * 通过
     */
    @AfsEnum(key = "04",desc = "通过")
    PASS,
    /**
     * 拒绝
     */
    @AfsEnum(key = "05",desc = "拒绝")
    REFUSE
}
