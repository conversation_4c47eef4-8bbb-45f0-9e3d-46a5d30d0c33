package com.ruicar.afs.cloud.afscase.loanapprove.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPool;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.entity.CaseBackToPartnersInfo;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityChangeService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanWorkflowService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveLoanInfoService;
import com.ruicar.afs.cloud.afscase.mq.entity.CaseSysCode;
import com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.config.CaseFlowNodeProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.feign.FlowDesignerFeign;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.FlowNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.enums.common.LoanAutoApproveResultEnum;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2020/7/17
 * @description
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class LoanWorkflowServiceImpl implements LoanWorkflowService {
    private WorkProcessScheduleInfoService workProcessScheduleInfoService;
    private WorkTaskPoolService workTaskPoolService;
    private CaseContractInfoService caseContractInfoService;
    private CaseApproveRecordService caseApproveRecordService;
    private WorkflowHelper workflowHelper;
    private WorkflowWrapperService workflowWrapperService;

    private WorkflowProcessBusinessRefInfoService processBusinessRefInfoService;
    private FlowConfigProperties flowConfigProperties;
    private CaseBaseInfoService caseBaseInfoService;
    private CaseSysCode caseSysCode;
    private final FlowRunFeign flowRunFeign;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private ApproveLoanInfoService approveLoanInfoService;
    private FlowDesignerFeign flowDesignerFeign;
    private CaseFlowNodeProperties caseFlowNodeProperties;
    private CasePriorityChangeService casePriorityChangeService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitWorkflow(LoanApproveCondition condition) {
        WorkTaskPool pool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>lambdaQuery()
            .eq(StringUtils.isNotBlank(condition.getContractNo()), WorkTaskPool::getContractNo, condition.getContractNo())
            .eq(StringUtils.isNotBlank(condition.getUseScene()), WorkTaskPool::getProcessType, condition.getUseScene()), false);
        if (null == pool) {
            log.error("任务池未找到该数据");
        }
        CaseApproveRecord caseApproveRecord = updateApproveRecord(condition, pool);
        workProcessScheduleInfoService.getById(Long.parseLong(caseApproveRecord.getStageId()));
    }

    /**
     * @param
     * @description 强制结束流程
     * <AUTHOR>
     * @date 2020/8/1
     */
    @Override
    public void giveUpWorkflow(String contractNo, String useScene) {
        getWorkProcessScheduleInfo(contractNo, useScene);
    }

    /**
     * @param
     * @description 强制结束流程
     * <AUTHOR>
     * @date 2021/2/24
     */
    @Override
    public void giveOverWorkflow(String contractNo, String useScene) {
        log.info("----------工作流结束开始-------->");
        CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(contractNo), CaseContractInfo::getContractNo, contractNo));

        caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(contractNo), CaseBaseInfo::getApplyNo, contractInfo.getApplyNo()));
        WorkflowTaskInfo workflowTaskInfo = new WorkflowTaskInfo();
        if (LoanProcessTypeEnum.GENERAL_LOAN.getCode().equals(useScene)){
            workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getFlowPackageId, flowConfigProperties.getLoanPackageId())
                    .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId())
                    .eq(StringUtils.isNotBlank(contractInfo.getApplyNo()), WorkflowTaskInfo::getBusinessNo, contractInfo.getApplyNo())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1")
            );
        }
        if (LoanProcessTypeEnum.LOAN_CANCEL.getCode().equals(useScene)){
            workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getFlowPackageId, flowConfigProperties.getLoanCancelPackageId())
                    .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getLoanCancelTemplateId())
                    .eq(StringUtils.isNotBlank(contractInfo.getApplyNo()), WorkflowTaskInfo::getBusinessNo, contractInfo.getApplyNo())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1")
            );
        }
        if(ObjectUtils.isNotEmpty(workflowTaskInfo)) {
            new LoanApproveSubmitVO();
            flowRunFeign.cancelFlow(workflowTaskInfo.getProcessInstanceId());
            // 结束本地任务
            final WorkflowTaskInfo newTaskInfo = new WorkflowTaskInfo();
            newTaskInfo.setStatus(FlowStatusEnum.END.getCode());
            newTaskInfo.setId(workflowTaskInfo.getId());
            workflowTaskInfoService.updateById(newTaskInfo);
        }
        //信审工作流结束
        if(UseSceneEnum.APPROVE.getValue().equals(useScene)){
            workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getFlowPackageId, flowConfigProperties.getApprovePackageId())
                    .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId())
                    .eq(StringUtils.isNotBlank(contractInfo.getApplyNo()), WorkflowTaskInfo::getBusinessNo, contractInfo.getApplyNo())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1")
            );
            if(ObjectUtils.isNotEmpty(workflowTaskInfo)){
                flowRunFeign.cancelFlow(workflowTaskInfo.getProcessInstanceId());
            }
            workflowTaskInfoService.update(Wrappers.<WorkflowTaskInfo>lambdaUpdate()
                    .eq(WorkflowTaskInfo::getProcessInstanceId,workflowTaskInfo.getProcessInstanceId())
                    .set(WorkflowTaskInfo::getStatus, FlowStatusEnum.END.getCode())
            );
        }
        log.info("----------工作流结束完成-------->");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitWorkflowByScheduleInfo(LoanApproveCondition condition) {
        // TODO 根据回调参数操作，不用业务号反查
        final WorkflowProcessBusinessRefInfo refInfo = processBusinessRefInfoService.getOne(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
            .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, condition.getApplyNo())
            .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getLoanPackageId())
            .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId())
        );

        log.info("退回经销商修改 - 工作流恢复 applyNo:{} processInstanceId:{}", condition.getApplyNo(), refInfo.getProcessInstanceId());
        final IResponse response = workflowHelper.resumeProcess(refInfo.getProcessInstanceId());

        if (!CaseConstants.CODE_SUCCESS.equals(response.getCode())){
            new RuntimeException("工作流恢复失败");
        }
    }

    /**
     * 激活池提交流程 - 放宽自动审核专用
     * @param condition 数据
     * @param autoApproveFlag 放款自动审核结果,0-未审核，1-审核不通过，2-审核通过
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newSubmitWorkflowByScheduleInfo(LoanApproveCondition condition, String autoApproveFlag) {

        // TODO 根据回调参数操作，不用业务号反查
        final WorkflowProcessBusinessRefInfo refInfo = processBusinessRefInfoService.getOne(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, condition.getApplyNo())
                .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getLoanPackageId())
                .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId())
        );

        if(ObjectUtils.isNotEmpty(refInfo)){
            // 修改FlowConstant.LOAN_AUTO_APPROVE_TYPE的变量
            FlowVariable flowVariable=new FlowVariable();
            flowVariable.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable.setName(FlowConstant.LOAN_AUTO_APPROVE_TYPE);
            flowVariable.setValue(autoApproveFlag);
            workflowHelper.setFlowVariableByFlowInstance(flowVariable);

            // 修改FlowConstant.BIZ_OPERATION_TYPE的变量
            FlowVariable flowVariable2 = new FlowVariable();
            flowVariable2.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable2.setName(FlowConstant.BIZ_OPERATION_TYPE);
            flowVariable2.setValue("submit");
            workflowHelper.setFlowVariableByFlowInstance(flowVariable2);

            // 修改FlowConstant.LAST_APPROVE_REMARK的变量
            FlowVariable flowVariable3 = new FlowVariable();
            flowVariable3.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable3.setName(FlowConstant.LAST_APPROVE_REMARK);
            flowVariable3.setValue("正常通过");
            workflowHelper.setFlowVariableByFlowInstance(flowVariable3);

            // 修改FlowConstant.LAST_APPROVE_REASON的变量
            FlowVariable flowVariable4 = new FlowVariable();
            flowVariable4.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable4.setName(FlowConstant.LAST_APPROVE_REASON);
            flowVariable4.setValue("放款自动审核正常通过");
            workflowHelper.setFlowVariableByFlowInstance(flowVariable4);

            // 修改FlowConstant.LAST_OPERATION的变量
            FlowVariable flowVariable5 = new FlowVariable();
            flowVariable5.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable5.setName(FlowConstant.LAST_OPERATION);
            flowVariable5.setValue("SUBMIT");
            workflowHelper.setFlowVariableByFlowInstance(flowVariable5);

            log.info("退回经销商修改 - 工作流恢复 applyNo:{} processInstanceId:{}", condition.getApplyNo(), refInfo.getProcessInstanceId());
            final IResponse response = workflowHelper.resumeProcess(refInfo.getProcessInstanceId());

            if (!CaseConstants.CODE_SUCCESS.equals(response.getCode())){
                new RuntimeException("工作流恢复失败");
            }

            // 判断合同是否完成签约，如果签约完成的话，则直接保存合同完成的日志
            if(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_SUCCESS).equals(autoApproveFlag)){
                // 保存记录 - 放款自动审核记录
                CaseApproveRecord record = new CaseApproveRecord();
                record.setApplyNo(condition.getApplyNo());
                record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                record.setApproveSuggest(FlowTaskOperationEnum.AUTOLOANAPPROVED.getCode());
                record.setApproveSuggestName(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
                record.setApproveEndTime(new Date());
                record.setApproveStartTime(new Date());
                record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                record.setApproveRemark(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
                record.setDisposeNodeName("放款自动审核");
                record.setDisposeStaff("系统");
                caseApproveRecordService.save(record);
            }
        }
    }

    /**
     * 激活池提交流程 - 银行放款状态等待节点
     *
     * @param applyNo         申请编号
     * @param bankAwaitStatus 银行放款状态,41-放款通过，42-放款退回
     * @param approveResult 银行退回原因
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bankAwaitWorkflowByScheduleInfo(String applyNo, String bankAwaitStatus, String approveResult) {
        // TODO 根据回调参数操作，不用业务号反查
        final WorkflowProcessBusinessRefInfo refInfo = processBusinessRefInfoService.getOne(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, applyNo)
                .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getLoanPackageId())
                .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId())
        );
        log.info("节点名称" + refInfo.getCurrentNodeName());
        // 修改银行放款状态
        FlowVariable flowVariable=new FlowVariable();
        flowVariable.setFlowInstanceId(refInfo.getProcessInstanceId());
        flowVariable.setName(FlowConstant.BANK_LOAN_STATUS);
        flowVariable.setValue(bankAwaitStatus);
        workflowHelper.setFlowVariableByFlowInstance(flowVariable);

        // 通过
        if(CaseConstants.BANK_LOAN_STATUS_PASS.equals(bankAwaitStatus)){

            // 修改FlowConstant.LAST_APPROVE_REMARK的变量
            FlowVariable flowVariable3 = new FlowVariable();
            flowVariable3.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable3.setName(FlowConstant.LAST_APPROVE_REMARK);
            flowVariable3.setValue(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getDesc());
            workflowHelper.setFlowVariableByFlowInstance(flowVariable3);

            // 修改FlowConstant.LAST_APPROVE_REASON的变量
            FlowVariable flowVariable4 = new FlowVariable();
            flowVariable4.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable4.setName(FlowConstant.LAST_APPROVE_REASON);
            flowVariable4.setValue(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getDesc());
            workflowHelper.setFlowVariableByFlowInstance(flowVariable4);

        } else if(CaseConstants.BANK_LOAN_STATUS_BACK.equals(bankAwaitStatus)){

            // 修改FlowConstant.LAST_APPROVE_REMARK的变量
            FlowVariable flowVariable3 = new FlowVariable();
            flowVariable3.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable3.setName(FlowConstant.LAST_APPROVE_REMARK);
            flowVariable3.setValue(FlowTaskOperationEnum.AWAITBANKLOANSTATUSBACK.getDesc());
            workflowHelper.setFlowVariableByFlowInstance(flowVariable3);

            // 修改FlowConstant.LAST_APPROVE_REASON的变量
            FlowVariable flowVariable4 = new FlowVariable();
            flowVariable4.setFlowInstanceId(refInfo.getProcessInstanceId());
            flowVariable4.setName(FlowConstant.LAST_APPROVE_REASON);
            flowVariable4.setValue(FlowTaskOperationEnum.AWAITBANKLOANSTATUSBACK.getDesc());
            workflowHelper.setFlowVariableByFlowInstance(flowVariable4);
        }

        log.info("银行放款状态等待 - 工作流恢复 applyNo:{} processInstanceId:{}", applyNo, refInfo.getProcessInstanceId());
        //获取当前生效的流程节点
        IResponse response = new IResponse<>();
        JSONObject jsonParams = new JSONObject();
        jsonParams.put("processId",refInfo.getProcessInstanceId());
        IResponse flowRes = flowDesignerFeign.queryProcessHistoryTrack(jsonParams);
        JSONObject activeJson = Optional.ofNullable(flowRes.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
        JSONArray activeList = activeJson.getJSONArray("activityNodes");
        log.info("activeList日志：{}", activeList);
        for (Object o : activeList) {
            JSONObject activeNode=(JSONObject)o;
            if (Objects.equals(activeNode.getString("nodeId"),caseFlowNodeProperties.getFkRentLoanWaitNodeId())) {
                log.info("激活等待节点1" + refInfo.getCurrentNodeName());
                log.info("激活等待节点2" + caseFlowNodeProperties.getFkRentLoanWaitNodeId());
                response = workflowHelper.resumeProcess(refInfo.getProcessInstanceId());
            }
        }

        if (!CaseConstants.CODE_SUCCESS.equals(response.getCode())){
            throw new RuntimeException("工作流恢复失败");
        }else{
            if(CaseConstants.BANK_LOAN_STATUS_PASS.equals(bankAwaitStatus)){
                log.info("保存银行资方放款通过的记录！");
                CaseApproveRecord record = new CaseApproveRecord();
                record.setApplyNo(applyNo);
                record.setUseScene(UseSceneEnum.APPROVE.getValue());
                record.setApproveSuggest(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getCode());
                record.setApproveSuggestName(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getDesc());
                record.setApproveEndTime(new Date());
                record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                record.setApproveRemark(approveResult);
                record.setDisposeNodeName("银行审批");
                caseApproveRecordService.save(record);
            } else if(CaseConstants.BANK_LOAN_STATUS_BACK.equals(bankAwaitStatus)){
                log.info("保存银行资方放款退回的记录！");
                CaseApproveRecord record = new CaseApproveRecord();
                record.setApplyNo(applyNo);
                record.setUseScene(UseSceneEnum.APPROVE.getValue());
                record.setApproveSuggest(FlowTaskOperationEnum.AWAITBANKLOANSTATUSBACK.getCode());
                record.setApproveSuggestName(FlowTaskOperationEnum.AWAITBANKLOANSTATUSBACK.getDesc());
                record.setApproveEndTime(new Date());
                record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                record.setApproveRemark(approveResult);
                record.setDisposeNodeName("银行审批");
                caseApproveRecordService.save(record);

                // 保存退回原因
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getApplyNo, applyNo));
                // 修改放款审核状态
                if(ObjectUtils.isNotEmpty(caseContractInfo)){
                    caseContractInfo.setWorkflowPrevStep(1);
                    caseContractInfo.setFlowNode(FlowNodeEnum.CHANNEL.getCode());
                    caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_RETURN.getState());
                    caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
                    caseContractInfoService.updateById(caseContractInfo);

                    // 获取taskId
                    List<WorkflowTaskInfo> workflowTaskInfoList = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                            .select(WorkflowTaskInfo::getTaskId)
                            .eq(WorkflowTaskInfo::getProcessInstanceId, refInfo.getProcessInstanceId())
                            .eq(WorkflowTaskInfo::getBusinessNo, applyNo)
                            .orderByDesc(WorkflowTaskInfo::getCreateTime));

                    if(ObjectUtils.isNotEmpty(workflowTaskInfoList) && workflowTaskInfoList.size() > 0){

                        CaseBackToPartnersInfo info = new CaseBackToPartnersInfo();
                        info.setApplyNo(applyNo);
                        info.setContractNo(caseContractInfo.getContractNo());
                        info.setBackTime(new Date());
                        info.setTaskId(Long.valueOf(workflowTaskInfoList.get(0).getTaskId()));
                        info.setBackDesc(StrUtil.isNotBlank(approveResult) ? approveResult : "银行资方放款退回，退回到经销商");
                        List<CaseBackToPartnersInfo> infoList = new ArrayList<>();
                        infoList.add(info);

                        approveLoanInfoService.loanAutoBackToPartnersNotic(infoList);
                    } else{
                        throw new AfsBaseException("流程实例信息获取失败！");
                    }

                }else{
                    throw new AfsBaseException("合同信息获取失败！");
                }

            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void gpsSubmitWorkflowByScheduleInfo(LoanApproveCondition condition) {
        WorkProcessScheduleInfo info = getWorkProcessScheduleInfo(condition.getContractNo(), condition.getUseScene());
        WorkTaskPool pool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>lambdaQuery()
            .eq(StringUtils.isNotBlank(condition.getContractNo()), WorkTaskPool::getContractNo, condition.getContractNo())
            .eq(StringUtils.isNotBlank(condition.getUseScene()), WorkTaskPool::getProcessType, condition.getUseScene()), false);
        if (ObjectUtils.isNotEmpty(info)) {
            updateApproveRecord(condition, pool);
        }
    }

    public WorkProcessScheduleInfo getWorkProcessScheduleInfo(String contractNo, String useSceneEnum) {
        WorkTaskPool pool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>lambdaQuery()
            .eq(StringUtils.isNotBlank(contractNo), WorkTaskPool::getContractNo, contractNo)
            .eq(StringUtils.isNotBlank(useSceneEnum), WorkTaskPool::getProcessType, useSceneEnum), false);
        if (ObjectUtils.isNotEmpty(pool)) {
            return workProcessScheduleInfoService.getOne(Wrappers.<WorkProcessScheduleInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(pool.getStageId()), WorkProcessScheduleInfo::getId, Long.parseLong(pool.getStageId())), false);
        } else {
            return new WorkProcessScheduleInfo();
        }
    }

    private CaseApproveRecord updateApproveRecord(LoanApproveCondition condition, WorkTaskPool pool) {
        CaseApproveRecord caseApproveRecord = caseApproveRecordService.getOne(Wrappers.<CaseApproveRecord>lambdaQuery()
            .eq(CaseApproveRecord::getTaskId, pool.getId().toString())
            .eq(StringUtils.isNotBlank(condition.getContractNo()), CaseApproveRecord::getContractNo, condition.getContractNo()), false);
        if (ObjectUtils.isEmpty(caseApproveRecord)) {
            caseApproveRecord = new CaseApproveRecord();
            caseApproveRecord.setStageId(pool.getStageId());
            caseApproveRecord.setDisposeStaff(pool.getApproveStaffName());
            caseApproveRecord.setApplyNo(pool.getApplyNo());
            caseApproveRecord.setContractNo(pool.getContractNo());
            caseApproveRecord.setApproveStartTime(new Date());
            caseApproveRecord.setUseScene(pool.getProcessType() == null ? UseSceneEnum.GENERAL_LOAN.getValue() : pool.getProcessType());
            caseApproveRecord.setDisposeNodeName(pool.getTaskNodeName());
            caseApproveRecord.setDisposeNode(pool.getTaskNodeId());
            caseApproveRecord.setTaskId(pool.getId().toString());
            caseApproveRecordService.save(caseApproveRecord);
        }

        caseApproveRecord.setApproveSuggest(condition.getApproveSuggest());
        if (null != condition.getApproveSuggest()) {
            caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(AfsEnumUtil.getEnum(condition.getApproveSuggest(), LoanSubmitEnum.class)));
        }
        caseApproveRecord.setApproveEndTime(new Date());
        caseApproveRecord.setApproveMessage(condition.getApproveRemark());
        caseApproveRecord.setApproveRemark(condition.getApproveRemark());
        caseApproveRecord.setApproveType(condition.getApproveType());
        caseApproveRecord.setFlowNode(condition.getFlowNode());
        return caseApproveRecord;
    }
}
