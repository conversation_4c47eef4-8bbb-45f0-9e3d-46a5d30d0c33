package com.ruicar.afs.cloud.afscase.workflow.event;

import com.ruicar.afs.cloud.parameter.commom.enums.AProveBusinessTypeEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class ChannelServiceFeeEvent extends ApplicationEvent {
    /**
     * 申请编号
     */
    private String applyNo;
    private AProveBusinessTypeEnum aProveBusinessTypeEnum;
    /**
     * 是否分期
     */
    private String periodFlag;

    public ChannelServiceFeeEvent(Object source, String applyNo, AProveBusinessTypeEnum aProveBusinessTypeEnum, String periodFlag) {
        super(source);
        this.aProveBusinessTypeEnum = aProveBusinessTypeEnum;
        this.applyNo = applyNo;
        this.periodFlag = periodFlag;
    }
}

