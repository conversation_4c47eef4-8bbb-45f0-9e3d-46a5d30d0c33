package com.ruicar.afs.cloud.afscase.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.entity.RemindVerificationRule;
import com.ruicar.afs.cloud.afscase.risk.mapper.RemindVerificationRuleMapper;
import com.ruicar.afs.cloud.afscase.risk.service.CodeCategoryOrderService;
import com.ruicar.afs.cloud.afscase.risk.service.RemindVerificationRuleService;
import com.ruicar.afs.cloud.afscase.risk.vo.RemindVerificationRuleCondition;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: 提醒核实规则核查内容表实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class RemindVerificationRuleServiceImpl extends ServiceImpl<RemindVerificationRuleMapper, RemindVerificationRule> implements RemindVerificationRuleService {

    private final CodeCategoryOrderService codeCategoryOrderService;

    /**
     * 分页查询所有的提醒核实规则核查内容表信息
     * @param condition 参数
     * @return 返回结果
     */
    @Override
    public IResponse queryByPage(RemindVerificationRuleCondition condition) {
        Page<RemindVerificationRule> page = new Page<>(condition.getPageNumber(), condition.getPageSize());
        Page<RemindVerificationRule> remindVerificationRulePage = this.baseMapper.selectPage(page, Wrappers.<RemindVerificationRule>lambdaQuery()
                .eq(StrUtil.isNotBlank(condition.getRuleCode()), RemindVerificationRule::getRuleCode, condition.getRuleCode())
                .like(StrUtil.isNotBlank(condition.getCategory()), RemindVerificationRule::getCategory, condition.getCategory())
                .like(StrUtil.isNotBlank(condition.getDescription()), RemindVerificationRule::getDescription, condition.getDescription())
                .eq(StrUtil.isNotBlank(condition.getActiveFlag()), RemindVerificationRule::getActiveFlag, condition.getActiveFlag())
        );
        if (ObjectUtil.isNotEmpty(remindVerificationRulePage) && ObjectUtil.isNotEmpty(remindVerificationRulePage.getRecords())) {
            // 设置分类名称
            remindVerificationRulePage.getRecords().forEach(rule -> {
                rule.setRuleName(rule.getCategory() + "-" + rule.getRuleCode());
            });
        }
        return IResponse.success(remindVerificationRulePage);
    }

    /**
     * 根据条件查询提醒核实规则核查内容表信息
     * @param condition 参数
     * @return
     */
    @Override
    public List<RemindVerificationRule> queryByCondition(RemindVerificationRuleCondition condition) {
        LambdaQueryWrapper<RemindVerificationRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(condition.getRuleCode()), RemindVerificationRule::getRuleCode, condition.getRuleCode());
        queryWrapper.like(StrUtil.isNotBlank(condition.getCategory()), RemindVerificationRule::getCategory, condition.getCategory());
        queryWrapper.like(StrUtil.isNotBlank(condition.getDescription()), RemindVerificationRule::getDescription, condition.getDescription());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getActiveFlag()), RemindVerificationRule::getActiveFlag, condition.getActiveFlag());
        List<RemindVerificationRule> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            // 设置分类名称
            list.forEach(rule -> {
                rule.setRuleName(rule.getCategory() + "-" + rule.getRuleCode());
            });
        }
        return list;
    }

    /**
     * 查询所有符合条件的提醒核实规则核查内容表信息:只返回非隐藏的规则代码
     *
     * @return 返回结果
     */
    @Override
    public IResponse getAllShowRuleCodes() {
        List<Map<String, String>> allShowRuleCodes = codeCategoryOrderService.getAllShowRuleCodes();
        log.info("查询所有符合条件的提醒核实规则核查内容表信息结果[{}]", allShowRuleCodes);
        return IResponse.success(allShowRuleCodes);
    }

    /**
     * 保存或更新提醒核实规则核查内容表信息
     *
     * @param remindVerificationRule 提醒核实规则核查内容表
     * @return 返回结果
     */
    @Override
    public IResponse saveOrUpdateRvr(RemindVerificationRule remindVerificationRule) {
        // 1. 参数校验
        validateRemindVerificationRule(remindVerificationRule);

        // 2. 设置分类
        setDefaultCategoryIfMissing(remindVerificationRule);

        // 3. 检查规则码的唯一性
        checkRuleCodeAndDescriptionUniqueness(remindVerificationRule);

        // 4. 执行保存或更新
        if (remindVerificationRule.getId() == null) {
            this.save(remindVerificationRule);
        } else {
            updateExistingRule(remindVerificationRule);
        }

        return IResponse.success("操作成功");
    }

    /**
     * 验证提醒核实规则的基本参数
     */
    private void validateRemindVerificationRule(RemindVerificationRule rule) {
        if (rule == null) {
            throw new AfsBaseException("规则信息不能为空");
        }

        if (StrUtil.isBlank(rule.getRuleCode())) {
            throw new AfsBaseException("规则代码不能为空");
        }

        if (StrUtil.isBlank(rule.getManualCheckContent())) {
            throw new AfsBaseException("人工核查内容不能为空");
        }
    }

    /**
     * 如果分类为空，设置默认分类
     */
    private void setDefaultCategoryIfMissing(RemindVerificationRule rule) {
        if (StrUtil.isBlank(rule.getCategory())) {
            List<CodeCategoryOrder> codeCategoryOrderList = codeCategoryOrderService.list(
                    new LambdaQueryWrapper<CodeCategoryOrder>()
                            .eq(CodeCategoryOrder::getRuleCode, rule.getRuleCode())
            );
            rule.setCategory(codeCategoryOrderList.get(0).getCategory());
        }
    }

    /**
     * 检查规则码和描述的唯一性
     */
    private void checkRuleCodeAndDescriptionUniqueness(RemindVerificationRule rule) {
        LambdaQueryWrapper<RemindVerificationRule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RemindVerificationRule::getRuleCode, rule.getRuleCode());

        // 如果是更新操作，排除当前记录
        if (rule.getId() != null) {
            queryWrapper.ne(RemindVerificationRule::getId, rule.getId());
        }

        if (this.count(queryWrapper) > 0) {
            throw new AfsBaseException("该规则码人工核查内容已存在，请检查后重新输入！");
        }
    }

    /**
     * 更新现有规则
     */
    private void updateExistingRule(RemindVerificationRule rule) {
        RemindVerificationRule existingRule = this.getById(rule.getId());
        if (existingRule == null) {
            throw new AfsBaseException("要更新的记录不存在");
        }

        // 只更新必要的字段
        existingRule.setRuleCode(rule.getRuleCode());
        existingRule.setDescription(rule.getDescription());
        existingRule.setVerificationGesture(rule.getVerificationGesture());
        existingRule.setManualCheckContent(rule.getManualCheckContent());
        existingRule.setRemake(rule.getRemake());
        existingRule.setCategory(rule.getCategory());
        existingRule.setActiveFlag(rule.getActiveFlag());

        this.updateById(existingRule);
    }
}
