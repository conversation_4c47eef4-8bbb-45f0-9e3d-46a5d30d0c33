package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import lombok.Data;

import java.util.Date;

@Data
public class BasicContractInfoVo{
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 合同状态
     */
    private ContractStatusEnum contractStatus;
    /**
     * 合同到期日期
     */
    private Date endDate;
    /**
     * 放款时间/起息日期
     */
    private Date loanDate;
    /**
     * 合同取消日期
     */
    private Date cancelDate;
    /**
     * 进件首次提交日期
     */
    private Date intoFirstDate;
    /**
     * 合同结清日期
     */
    private Date settleDate;
    /**
     * 所属资方
     */
    private String belongingCapital;
    /**
     * 资方回款时间
     */
    private Date capitalReturnTime;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 经销商名称
     */
    private String channelFullName;
}
