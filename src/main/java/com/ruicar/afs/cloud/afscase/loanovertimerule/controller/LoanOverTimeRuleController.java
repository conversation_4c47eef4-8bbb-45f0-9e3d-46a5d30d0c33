package com.ruicar.afs.cloud.afscase.loanovertimerule.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.common.utils.SequenceUtil;
import com.ruicar.afs.cloud.afscase.loanovertimerule.condition.LoanOverTimeRuleCondition;
import com.ruicar.afs.cloud.afscase.loanovertimerule.entity.LoanOverTimeRule;
import com.ruicar.afs.cloud.afscase.loanovertimerule.service.LoanOverTimeRuleService;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 超时效规则控制层
 * @date 2020/10/27 17:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/loanOverTimeRule")
@Api("超时效规则配置")
public class LoanOverTimeRuleController {

    private LoanOverTimeRuleService service;

    private AfsRuleFeign afsRuleInfoService;

    /**
     * 获取规则配置数据
     *
     * @param condition
     * @return
     */
    @PostMapping(value = "/getOverTimeRuleList")
    @ApiOperation(value = "多条件分页获取超时效规则配置数据")
    public IResponse<IPage<LoanOverTimeRule>> getOverTimeRuleList(@RequestBody LoanOverTimeRuleCondition condition) {
        IPage<LoanOverTimeRule> loanOverTimeRuleIPage = service.page(new Page(condition.getPageNumber(), condition.getPageSize()), Wrappers.<LoanOverTimeRule>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getReviewName()), LoanOverTimeRule::getReviewName, condition.getReviewName())
                .eq(StringUtils.isNotEmpty(condition.getReviewNo()), LoanOverTimeRule::getReviewNo, condition.getReviewNo())
                .eq(StringUtils.isNoneBlank(condition.getRuleId()!=null?condition.getRuleId().toString():""), LoanOverTimeRule::getRuleId, condition.getRuleId()!=null?condition.getRuleId().toString():""));
        return IResponse.success(loanOverTimeRuleIPage);
    }

    /**
     * 新增规则配置数据
     *
     * @param loanOverTimeRule
     * @return
     */
    @PostMapping(value = "/addRule")
    @ApiOperation(value = "新增超时效规则配置数据")
    public IResponse<Boolean> addSuspendRules(@RequestBody LoanOverTimeRule loanOverTimeRule) {
        //按规则生成id
        loanOverTimeRule.setReviewNo(SequenceUtil.getSeq(Const.SUSPEND_NO));
        service.save(loanOverTimeRule);
        return new IResponse<Boolean>().setMsg("新增超时效规则成功！");
    }

    /**
     * 编辑规则配置数据
     *
     * @param loanOverTimeRule
     * @return
     */
    @PostMapping(value = "/editRule")
    @ApiOperation(value = "编辑超时效规则数据")
    public IResponse<Boolean> edit(@RequestBody LoanOverTimeRule loanOverTimeRule) {
        service.updateById(loanOverTimeRule);
        return new IResponse<Boolean>().setMsg("修改超时效规则成功！");
    }

    /**
     * 批量删除规则
     *
     * @patram ids
     * @reurn
     */
    @PostMapping(value = "/delRuleByIds/{ids}")
    @ApiOperation(value = "批量删除超时效规则")
    public IResponse<Object> delRule(@PathVariable String[] ids) {
        //使规则信息表数据失效
        service.deActiveRuleByRuleNo(ids);
        //删除超时效规则数据
        service.removeByIds(Arrays.asList(ids));
        return new IResponse<Object>().setMsg("删除超时效规则成功！");
    }

    /**
     * 启用超时效规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用超时效规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable String id) {
        service.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用超时效规则成功！");
    }

    /**
     * 停用超时效规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用超时效规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable String id) {
        service.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用超时效规则成功！");
    }


    /**
     * 更新规则表业务id
     *
     * @param loanOverTimeRule
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新超时效规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody LoanOverTimeRule loanOverTimeRule) {
        //更新状态为启用
        loanOverTimeRule.setIsEnable(WhetherEnum.NO.getCode());
        service.updateById(loanOverTimeRule);
        return new IResponse<Boolean>().setMsg("保存成功！");
    }

    /**
     * 失效规则
     *
     * @param ruleId
     * @return
     */
    @PostMapping("/deActiveRuleById/{ruleId}")
    public IResponse deActiveRuleById(@PathVariable("ruleId") String ruleId) {
        //先反激活再失效
        afsRuleInfoService.deActiveRuleByRuleId(Long.valueOf(ruleId));
        return IResponse.success("失效成功");
    }
}
