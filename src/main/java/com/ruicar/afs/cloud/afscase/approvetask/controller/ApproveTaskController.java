package com.ruicar.afs.cloud.afscase.approvetask.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.ApproveMakeLabel;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.LoanLabelInfo;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.ApproveMakeLabelService;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.LoanLabelInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.condition.ApproveCondition;
import com.ruicar.afs.cloud.afscase.approvetask.condition.ChangeParamsCondition;
import com.ruicar.afs.cloud.afscase.approvetask.condition.WorkTaskPoolCondition;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseSubmitRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfoTemp;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPool;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPoolHistory;
import com.ruicar.afs.cloud.afscase.approvetask.enums.ApproveNodeEnum;
import com.ruicar.afs.cloud.afscase.approvetask.enums.FilingNodeEnum;
import com.ruicar.afs.cloud.afscase.approvetask.enums.FlowBackOption;
import com.ruicar.afs.cloud.afscase.approvetask.enums.ProductApproveNodeEnum;
import com.ruicar.afs.cloud.afscase.approvetask.service.ApproveTaskServiceImpl;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseSubmitRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.FaceReviewHandlerService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoTempService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolHistoryService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveRecordVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveSummaryDataVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveSummaryVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ApproveTaskVo;
import com.ruicar.afs.cloud.afscase.approvetask.vo.TaskHandlerChangeVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.TaskHandlerPageVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.TaskHandlerVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.WorkflowAppointVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.WorkflowCallBackVO;
import com.ruicar.afs.cloud.afscase.approvetask.vo.WorkflowCancelVO;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.common.service.SplittingTimeService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.dispatch.enums.DispatchTypeEnum;
import com.ruicar.afs.cloud.afscase.dispatch.service.impl.DispatchServcieImpl;
import com.ruicar.afs.cloud.afscase.effecttime.effect.base.ICaseEffect;
import com.ruicar.afs.cloud.afscase.effecttime.effect.base.IEffectTime;
import com.ruicar.afs.cloud.afscase.effecttime.effect.builder.CaseEffectBuilder;
import com.ruicar.afs.cloud.afscase.effecttime.entity.CaseEffectRecord;
import com.ruicar.afs.cloud.afscase.effecttime.service.CaseEffectRecordService;
import com.ruicar.afs.cloud.afscase.effecttime.utils.EffectDateUtil;
import com.ruicar.afs.cloud.afscase.effecttime.vo.BetweenSeconds;
import com.ruicar.afs.cloud.afscase.fraud.entity.CaseApproveFraud;
import com.ruicar.afs.cloud.afscase.fraud.service.CaseApproveFraudService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.message.entity.CaseNoticeInfo;
import com.ruicar.afs.cloud.afscase.message.service.CaseNoticeInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveInformInfoService;
import com.ruicar.afs.cloud.afscase.option.entity.CaseCreditOption;
import com.ruicar.afs.cloud.afscase.option.service.CaseCreditOptionService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.processor.enums.WorkflowType;
import com.ruicar.afs.cloud.afscase.processor.service.ApproveWorkflowService;
import com.ruicar.afs.cloud.afscase.remind.entity.CaseRemindDetail;
import com.ruicar.afs.cloud.afscase.remind.service.RemindService;
import com.ruicar.afs.cloud.afscase.risk.entity.CaseLiangFuApply;
import com.ruicar.afs.cloud.afscase.risk.service.CaseLiangFuApplyService;
import com.ruicar.afs.cloud.afscase.risk.service.DayBookResolveService;
import com.ruicar.afs.cloud.afscase.salesmanage.service.ISaleTeamUserService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.entity.vo.QueryUserVo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.MainEventTypeEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.WorkflowNodeEnum;
import com.ruicar.afs.cloud.afscase.workflow.event.ApproveAuditEvent;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CallBackTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ReadEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemainKeyEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TortoiseApplyExecuteEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveOpenDto;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.risk.api.enums.RiskStatus;
import com.ruicar.afs.cloud.seats.condition.SysUserDto;
import com.ruicar.afs.cloud.seats.entity.MainEventLog;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import com.ruicar.afs.cloud.seats.service.MainEventLogService;
import com.ruicar.afs.cloud.seats.service.UserCollocationService;
import com.ruicar.afs.cloud.seats.util.PostStatus;
import com.ruicar.afs.cloud.websocket.utils.WebSocketUtils;
import com.ruicar.afs.cloud.workflow.sdk.dto.group.FlowUser;
import com.ruicar.afs.cloud.workflow.sdk.dto.group.request.ResponseUser;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>Description: 审批代办件
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-17 10:45
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("审核任务")
@RequestMapping("/approve")
public class ApproveTaskController {

    private WorkTaskPoolService workTaskPoolService;

    private CaseCarInfoService caseCarInfoService;

    private CaseBaseInfoService caseBaseInfoService;

    private ApproveMakeLabelService approveMakeLabelService;
    private LoanLabelInfoService loanLabelInfoService;
    private CaseApproveRecordService caseApproveRecordService;
    private WorkProcessScheduleInfoService workProcessScheduleInfoService;
    private WorkProcessScheduleInfoTempService workProcessScheduleInfoTempService;
    private ApproveInformInfoService approveInformInfoService;
    private UserCollocationService userCollocationService;
    private UserDetailsInfoFeign userDetailsInfoFeign;
    private CaseConfParamService caseConfParamService;
    private ApproveWorkflowService approveWorkflowService;
    private DayBookResolveService dayBookResolveService;
    private CaseNoticeInfoService caseNoticeInfoService;
    private WorkTaskPoolHistoryService workTaskPoolHistoryService;
    private ApproveInformInfoService informInfoService;
    private RemindService remindService;
    private CaseEffectRecordService caseEffectRecordService;
    private CaseCreditOptionService caseCreditOptionService;
    private CaseApproveFraudService caseApproveFraudService;

    private CaseContractInfoService caseContractInfoService;
    private CaseChannelInfoService caseChannelInfoService;
    private WorkflowHelper workflowHelper;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private WorkflowWrapperService workflowWrapperService;
    private ApplicationEventPublisher eventPublisher;

    private ChannelBaseInfoService channelBaseInfoService;
    private ChannelRiskInfoService channelRiskInfoService;
    private AddressService addressService;
    private ApproveTaskServiceImpl approveTaskService;
    private CaseLiangFuApplyService caseLiangFuApplyService;
    @Autowired
    private ISaleTeamUserService teamUserService;

    private MainEventLogService mainEventLogService;

    private FlowRunFeign flowRunFeign;

    private final FlowConfigProperties flowConfigProperties;

    private final SplittingTimeService splittingTimeService;

    private CaseSubmitRecordService caseSubmitRecordService;

    private FaceReviewHandlerService faceReviewHandlerService;
    private CaseUseApplyServiceFeign caseUseApplyServiceFeign;

    @PostMapping(value = "/queryApproveSummary")
    @ApiOperation(value = "查询审批人任务汇总信息")
    public IResponse<?> queryApproveSummaryData(@RequestBody ApproveSummaryVO summaryVO) {
        Assert.hasLength(summaryVO.getPackageId(), "流程包id不能为空");
        Assert.hasLength(summaryVO.getTemplateId(), "流程模版id不能为空");
        Assert.hasLength(summaryVO.getUserDefinedIndex(), "流程节点自定义key不能为空");

        ApproveSummaryDataVO dataVO = workflowTaskInfoService.queryApproveSummaryData(summaryVO.getPackageId(),
                summaryVO.getTemplateId(), summaryVO.getUserDefinedIndex());
        return IResponse.success(dataVO);
    }

    @PostMapping(value = "/queryApproveTaskList")
    @ApiOperation(value = "多条件分页获取待审核任务--仅展示当前登录用户的待审核任务，组长可以查看本组")
    public IResponse<IPage<ApproveTaskVo>> queryApproveTaskList(@ModelAttribute WorkTaskPoolCondition condition) {

        Assert.hasLength(condition.getPackageId(), "流程包id不能为空");
        Assert.hasLength(condition.getTemplateId(), "流程模版id不能为空");
        Assert.hasLength(condition.getUserDefinedIndex(), "流程节点自定义key不能为空");

        //流程类型条件
        List processList = new ArrayList();
        processList.add(ProcessTypeEnum.FORMAL_REVIEW.getCode());
        processList.add(ProcessTypeEnum.GENERA_APPROVE.getCode());
        processList.add(ProcessTypeEnum.INVENTORIES_ARE.getCode());
        processList.add(ProcessTypeEnum.SECONDARY_FRAUD.getCode());
        condition.setProcessTypeList(processList);

        //业务状态（内）claculateLoanAmtMax
        List statusList = new ArrayList();
        statusList.add(AfsEnumUtil.key(BusinessStateInEnum.WAIT_VISIT));
        statusList.add(AfsEnumUtil.key(BusinessStateInEnum.VISITING));
        statusList.add(AfsEnumUtil.key(BusinessStateInEnum.DONE));
        statusList.add(AfsEnumUtil.key(BusinessStateInEnum.WAIT_ALLOCATION));
        condition.setBusinessStatusList(statusList);

        //特殊流程类型条件
        List processTypeSpecial = new ArrayList();
        processTypeSpecial.add(ProcessTypeEnum.CHANGE_ASSETS.getCode());
        processTypeSpecial.add(ProcessTypeEnum.RECONSIDER_APPLY.getCode());

        condition.setProcessTypeSpecial(processTypeSpecial);

        //特殊流程业务状态（内）
        List businessStatusSpecialList = new ArrayList();
        businessStatusSpecialList.add(AfsEnumUtil.key(BusinessStateInEnum.APPROVED));
        businessStatusSpecialList.add(AfsEnumUtil.key(BusinessStateInEnum.CONDITIONAL_APPROVE));
        businessStatusSpecialList.add(AfsEnumUtil.key(BusinessStateInEnum.REJECT));
        businessStatusSpecialList.add(AfsEnumUtil.key(BusinessStateInEnum.REPEAL));
        businessStatusSpecialList.add(AfsEnumUtil.key(BusinessStateInEnum.CANCEL_ADVICE_CONDITIONAL));
        condition.setBusinessStatusSpecialList(businessStatusSpecialList);

        //获取标签Id
        String[] str = condition.getTag();
        List labelIdList = new ArrayList();
        if (str != null && str.length > 0) {
            for (int i = 0; i < str.length; i++) {
                labelIdList.add(str[i]);
            }
        }
        if (labelIdList != null && labelIdList.size() > 0) {
            condition.setLabelList(labelIdList);
        }
        //当前登录用户
        String useName = SecurityUtils.getUsername();
        condition.setDisposeStaff(useName);
        IPage<ApproveTaskVo> pageResult = null;

        if(StringUtils.equals("fangkuan",condition.getPackageId())){
            pageResult = workTaskPoolService.queryLoanTaskList(
                    new Page(condition.getPageNumber(), condition.getPageSize()),
                    condition);
            log.info("放款pageResult:{}"+pageResult);
            log.info("放款查询列表查询人:{}"+useName);
        }else{
            if (StrUtil.equals(condition.getUserDefinedIndex(),"FIRST_NODE")){
                pageResult = workTaskPoolService.queryApproveTaskListByFirst(
                        new Page(condition.getPageNumber(), condition.getPageSize()),
                        condition);
            }else {
                pageResult = workTaskPoolService.queryApproveTaskList(
                        new Page(condition.getPageNumber(), condition.getPageSize()),
                        condition);
            }
            log.info("信审pageResult:{}"+pageResult);
            log.info("信审查询列表查询人:{}"+useName);

        }

        // 处于当前业务状态集合下时，页面时效计时非动态显示
        List<String> effectUnActiveStates = Arrays.asList(
            AfsEnumUtil.key(BusinessStateInEnum.PAUSE),
            AfsEnumUtil.key(BusinessStateInEnum.SUSPEND),
            AfsEnumUtil.key(BusinessStateInEnum.REVISE_PARSE)
        );

        pageResult.getRecords().forEach(approveTaskVo -> {

            /**案件基础信息*/
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, approveTaskVo.getApplyNo()));
            List<CaseLiangFuApply> caseLiangFuApply= caseLiangFuApplyService.list(Wrappers.<CaseLiangFuApply>query().lambda()
                    .eq(CaseLiangFuApply::getApplyNo, approveTaskVo.getApplyNo())
                    .orderByDesc(CaseLiangFuApply::getCreateTime));
            approveTaskVo.setCusRank(caseLiangFuApply.size()>0&&null!=caseLiangFuApply.get(0).getCusRank()?caseLiangFuApply.get(0).getCusRank():null);
            /*
             *
             * 销售人员姓名
             * */
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(CaseChannelInfo::getApplyNo, approveTaskVo.getApplyNo()));
            if (ObjectUtils.isNotNull(caseChannelInfo)) {
                approveTaskVo.setSellerRealName(caseChannelInfo.getSaleAdvisor());
                approveTaskVo.setChannelGrade(caseChannelInfo.getChannelGrade());
                approveTaskVo.setSubjectName(caseChannelInfo.getSubjectName());
            }

            /**
             * 查询合同号码
             */
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getApplyNo, approveTaskVo.getApplyNo()));
            if (ObjectUtils.isNotNull(caseContractInfo)){
                approveTaskVo.setContractNo(caseContractInfo.getContractNo());
            }

            /**
             * 查询放款发起审批时间, 因为会存在老数据，老数据是没有最终提交时间的，所以在查看老数据的时候用以前逻辑
             */
            List<CaseApproveRecord> caseApproveRecord = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>query().lambda()
                    .eq(CaseApproveRecord::getApplyNo, approveTaskVo.getApplyNo())
                    .eq(CaseApproveRecord::getUseScene, UseSceneEnum.GENERAL_LOAN.getValue())
                    .eq(CaseApproveRecord::getApproveSuggest,"initiate")
                    .orderByDesc(CaseApproveRecord::getApproveEndTime));
            if (CollectionUtils.isNotEmpty(caseApproveRecord)){
                if (caseApproveRecord.size()>0){
                    approveTaskVo.setApproveEndTime(caseApproveRecord.get(0).getApproveEndTime());
                }
            }
            //最终提交时间，对应第一次提交、退回重新提交
            if (null != caseContractInfo && null != caseContractInfo.getFinalSubmitTime()){
                /**
                 * 新数据会在请求的时候插入
                 */
                approveTaskVo.setApproveEndTime(caseContractInfo.getFinalSubmitTime());
            }

            /*
             * 查询风险等级
             *
             * */
            // 不知道什么原因，用同样的条件查了两次表
            CaseChannelInfo caseChannelInfo2 = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(CaseChannelInfo::getApplyNo, approveTaskVo.getApplyNo()));
            /**---------------------------------   渠道风控信息    --------------------------------**/

            if (ObjectUtils.isNotNull(caseChannelInfo2)) {
                ChannelBaseInfo channelBaseInfo2 = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                    .eq(ChannelBaseInfo::getChannelCode, caseChannelInfo2.getDealerNo()));
                if (ObjectUtils.isNotEmpty(channelBaseInfo2)) {
                    ChannelRiskInfo channelRiskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                        .eq(ChannelRiskInfo::getChannelId, channelBaseInfo2.getChannelId())
                        .eq(ChannelRiskInfo::getBusinessType, caseBaseInfo.getBusinessType()));
                    if (ObjectUtils.isNotNull(channelRiskInfo)) {
                        approveTaskVo.setQualityGrade(channelRiskInfo.getQualityGrade());
                    }
                    approveTaskVo.setChannelBelong(channelBaseInfo2.getChannelBelong());
                }
            }

            /*
             * 查询合作商名称以及城市
             *
             * */
            ChannelBaseInfo channelBaseInfo = caseChannelInfoService.getChannelBaseInfo(approveTaskVo.getApplyNo());
            if (ObjectUtils.isNotNull(channelBaseInfo)) {
                String city = addressService.getLabelByCode(channelBaseInfo.getChannelCity());
                approveTaskVo.setChannelCity(city);
                approveTaskVo.setChannelFullName(channelBaseInfo.getChannelFullName());

            }

            /**车辆信息*/
             CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, approveTaskVo.getApplyNo()));
             String packageId = condition.getPackageId();
            if (ObjectUtils.isNotEmpty(caseBaseInfo) /*&& ProcessTypeEnum.GENERA_APPROVE.getCode().equals(approveTaskVo.getProcessType())*/) {//默认展示计时时间，不通过流程类型区分

                BetweenSeconds betweenSeconds = null;
                if (ObjectUtils.isNotEmpty(caseContractInfo)) {
                    betweenSeconds = this.getContractRecord(caseContractInfo, approveTaskVo);
                } else {
                    betweenSeconds = this.getCaseRecord(caseBaseInfo, approveTaskVo);
                }
                // 原始读秒
                approveTaskVo.setOriginalSeconds(betweenSeconds.getSeconds());
                // 计时时间，页面初始化时显示
                approveTaskVo.setTimming(betweenSeconds.getFormatStr());

                if ("fangkuan".equals(packageId)) {//放款
                    approveTaskVo.setActive(effectUnActiveStates.contains(caseContractInfo.getSuspendStatus()) ? false : betweenSeconds.isActive());
                } else {//信审
                    approveTaskVo.setActive(effectUnActiveStates.contains(caseBaseInfo.getSuspendStatus()) ? false : betweenSeconds.isActive());
                }

            }
            if (Objects.nonNull(caseCarInfo)) {
                approveTaskVo.setCarName(caseCarInfo.getModelName());
                approveTaskVo.setBrandName(caseCarInfo.getBrandName());
            }
            /**  标签信息 **/
            if(StringUtils.equals("1",condition.getPackageId())){
                List<ApproveMakeLabel> labelList = approveMakeLabelService.list(Wrappers.<ApproveMakeLabel>query().lambda()
                        .eq(ApproveMakeLabel::getApplyNo, approveTaskVo.getApplyNo())
                        .eq(ApproveMakeLabel::getLabelLocation, LabelPositionEnum.LIST.getCode())
                        .eq(ApproveMakeLabel::getLabelPhase, LabelPhaseEnum.CREDITADUIT));
                approveTaskVo.setLabelList(labelList);
                approveTaskVo.setSuspendStatus(caseBaseInfo.getSuspendStatus());
            }
            if(StringUtils.equals("fangkuan",condition.getPackageId())){
                List<LoanLabelInfo> loanLabelInfoList = loanLabelInfoService.list(new LambdaQueryWrapper<LoanLabelInfo>()
                        .eq(LoanLabelInfo::getApplyNo, approveTaskVo.getApplyNo())
                        .eq(LoanLabelInfo::getLabelLocation, LabelPositionEnum.LIST.getCode())
                        .eq(LoanLabelInfo::getLabelPhase, LabelPhaseEnum.LOANS));
                // 前端页面展示使用的是color和name
                List<ApproveMakeLabel> labelList = changeLabelList(loanLabelInfoList);
                approveTaskVo.setLabelList(labelList);
                approveTaskVo.setSuspendStatus(caseContractInfo.getSuspendStatus());
            }
            /** 流程信息 */
            List<WorkflowTaskInfo> workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getBusinessNo, approveTaskVo.getApplyNo())
                    .eq(WorkflowTaskInfo::getProcessInstanceId, approveTaskVo.getStageId())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime)
            );
            List<WorkflowTaskInfo> workflowTaskInfoSecodNodes = new ArrayList<>();
            List<WorkflowTaskInfo> workflowTaskInfoEndNodes = new ArrayList<>();
            for(WorkflowTaskInfo workflowTaskInfo : workflowTaskInfos){
                if(StringUtils.isEmpty(workflowTaskInfo.getTaskId())){
                    continue;
                }
                if(approveTaskVo.getTaskId().equals(workflowTaskInfo.getTaskId())){
                    if (Objects.nonNull(workflowTaskInfo)) {
                        if ("FIRST_NODE".equals(workflowTaskInfo.getTargetWebKey())) {
                            approveTaskVo.setFirstTrail(true);
                            approveTaskVo.setLastTrail(false);
                        } else if ("LAST_NODE".equals(workflowTaskInfo.getTargetWebKey())) {
                            approveTaskVo.setFirstTrail(false);
                            approveTaskVo.setLastTrail(true);
                        }
                    }
                }
                //获取审查的数据
                if(workflowTaskInfo.getUserDefinedIndex().equals(AfsEnumUtil.key(ApproveNodeEnum.SECOND_NODE))){
                    workflowTaskInfoSecodNodes.add(workflowTaskInfo);
                }

                //获取审批的数据
                if(workflowTaskInfo.getUserDefinedIndex().equals(AfsEnumUtil.key(ApproveNodeEnum.END_NODE))){
                    workflowTaskInfoEndNodes.add(workflowTaskInfo);
                }
            }
            //审查判断上一步做了什么操作
            if(workflowTaskInfoSecodNodes.size() > 1){
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoSecodNodes.get(1);
                if(StringUtils.isNotEmpty(workflowTaskInfo.getOperationType())
                && workflowTaskInfo.getOperationType().equals(FlowTaskOperationEnum.BACK2DEALER.getCode())
                && StringUtils.isNotEmpty(workflowTaskInfo.getFlowBackOption())
                && workflowTaskInfo.getFlowBackOption().equals(AfsEnumUtil.key(FlowBackOption.DIRECT))
                ){
                    approveTaskVo.setColourStatus("1");
                }
                if(StringUtils.isNotEmpty(workflowTaskInfo.getOperationType())
                        && workflowTaskInfo.getOperationType().equals(FlowTaskOperationEnum.BACK2DEALER.getCode())
                        && StringUtils.isNotEmpty(workflowTaskInfo.getFlowBackOption())
                        && workflowTaskInfo.getFlowBackOption().equals(AfsEnumUtil.key(FlowBackOption.REVIEW))){
                    approveTaskVo.setColourStatus("2");
                }
            }

            if(workflowTaskInfoEndNodes.size() > 0){
                WorkflowTaskInfo workflowTaskInfoN = workflowTaskInfoEndNodes.get(0);
                if(StringUtils.isNotEmpty(workflowTaskInfoN.getOperationType())
                        && workflowTaskInfoN.getOperationType().equals(FlowTaskOperationEnum.BACK.getCode())
                ){
                    approveTaskVo.setColourStatusEndNode("1");
                }
            }

            if (AfsEnumUtil.key(BusinessStateInEnum.PAUSE).equals(approveTaskVo.getBusinessStatusIn())) {
                // 暂停原因
                Optional<CaseRemindDetail> remindDetailOptional = remindService.queryNewestRemindOpt(approveTaskVo.getApplyNo());
                if (remindDetailOptional.isPresent()) {
                    approveTaskVo.setParseMessage(remindDetailOptional.get().getRemindContent());
                }
            }
            //二次欺诈
            if (ProcessTypeEnum.SECONDARY_FRAUD.getCode().equals(approveTaskVo.getProcessType())) {
                CaseApproveFraud caseApproveFraud = caseApproveFraudService.getOne(Wrappers.<CaseApproveFraud>query().lambda()
                    .eq(CaseApproveFraud::getApplyNo, approveTaskVo.getApplyNo()));
                if (ObjectUtils.isNotNull(caseApproveFraud)) {
                    approveTaskVo.setBusinessStatusIn(caseApproveFraud.getStatus());
                }
            }
            //设置订单最新提交时间
            List<CaseSubmitRecord> submitRecords = caseSubmitRecordService.list(Wrappers.<CaseSubmitRecord>lambdaQuery()
                    .eq(CaseSubmitRecord::getApplyNo, approveTaskVo.getApplyNo())
                    .orderByDesc(CaseSubmitRecord::getSubmitDate));
            if (CollectionUtil.isNotEmpty(submitRecords)) {
                approveTaskVo.setLatestSubmitDate(submitRecords.get(0).getSubmitDate());
            }else {
                //若为空，则取值进件时间
                approveTaskVo.setLatestSubmitDate(approveTaskVo.getPassFirstDate());
            }
        });
        List<ApproveTaskVo> records = pageResult.getRecords();
        pageResult.setRecords(records);

        log.info("待办任务records:{}"+pageResult);
        return IResponse.success(pageResult);
    }

    private BetweenSeconds getContractRecord(CaseContractInfo caseContractInfo, ApproveTaskVo approveTaskVo) {
        Date workDate = approveTaskVo.getApproveEndTime();
        Date endDate = new Date();
        CaseEffectRecord caseEffectRecord = caseEffectRecordService.getCaseEffectRecord(caseContractInfo.getApplyNo());
        if(Objects.isNull(caseEffectRecord)) {
            return BetweenSeconds.builder().formatStr("").seconds(0).active(false).build();
        } else {
            boolean active = true;
            ICaseEffect caseEffect = CaseEffectBuilder.build(caseEffectRecord.getAreaType());
            //获取首次挂起时间
            long suspendTimes = this.getAllContractSuspendTimes(caseContractInfo, caseEffect);

            if (Objects.isNull(caseEffectRecord.getEffectTimeReckon())) {
                active = ((IEffectTime)caseEffect).atWorkTime(new Date()) && active;
                long workMs = caseEffect.effectMs(workDate, endDate);
                return EffectDateUtil.uiEffectFmt(workMs - suspendTimes).setActive(active);
            }else{
                return EffectDateUtil.uiEffectFmt(caseEffectRecord.getEffectTimeReckon()).setActive(false);
            }
        }
    }

    /**
     * 获取放款首次挂起时间
     * @param caseContractInfo
     * @param caseEffect
     * @return
     */
    public long getContractSuspendTimes(CaseContractInfo caseContractInfo, ICaseEffect caseEffect) {
        long suspendMs = 0;
        Date startDate = caseContractInfo.getSuspendStart();
        Date endDate = caseContractInfo.getSuspendEnd();
        if ("once".equals(caseContractInfo.getSuspendTimes())) {//判断是否挂起过
            if (ObjectUtil.isNotNull(startDate) && ObjectUtil.isNotNull(endDate)) {//判断是否处于挂起中
                suspendMs = caseEffect.effectMs(startDate, endDate);
            } else {
                suspendMs = caseEffect.effectMs(startDate, new Date());
            }
        } else if ("again".equals(caseContractInfo.getSuspendTimes())) {
            if (ObjectUtil.isNotNull(startDate) && ObjectUtil.isNotNull(endDate)) {//判断是否处于挂起中
                suspendMs = caseEffect.effectMs(startDate, endDate);
            } else {
                suspendMs = caseEffect.effectMs(startDate, new Date());
            }
        } else {
            return suspendMs;
        }

        return suspendMs;
    }

    /**
     * 获取放款所有挂起时间
     * @param caseContractInfo
     * @param caseEffect
     * @return
     */
    private long getAllContractSuspendTimes(CaseContractInfo caseContractInfo, ICaseEffect caseEffect) {
        long suspendMs = 0;
        List<MainEventLog> mainEventLogs = mainEventLogService.list(Wrappers.<MainEventLog>lambdaQuery()
                .eq(MainEventLog::getApplyNo, caseContractInfo.getApplyNo())
                .eq(MainEventLog::getEventType, MainEventTypeEnum.SUSPEND_TASK.getCode())
                .eq(MainEventLog::getProcessType, WorkflowNodeEnum.LOAN)
                .orderByDesc(MainEventLog::getStartTime)
        );
        for(MainEventLog mainEventLog : mainEventLogs){
            if(ObjectUtils.isNull(mainEventLog.getEndTime())){
                suspendMs += caseEffect.effectMs(mainEventLog.getStartTime(),  new Date());
            }else {
                suspendMs += caseEffect.effectMs(mainEventLog.getStartTime(),  mainEventLog.getEndTime());
            }
        }
        return suspendMs;
    }

    /**
     * 信审计时
     * @param caseBaseInfo
     * @param approveTaskVo
     * @return
     */
    private BetweenSeconds getCaseRecord(CaseBaseInfo caseBaseInfo, ApproveTaskVo approveTaskVo) {
        //判断信审、放款
        Date workDate = null;
        if(ObjectUtil.isNotNull(approveTaskVo.getAssignDate())){
            workDate = approveTaskVo.getAssignDate();
        }else{
            workDate = approveTaskVo.getSubmitDate();
        }

        Date endDate = new Date();

        CaseEffectRecord caseEffectRecord = caseEffectRecordService.getCaseEffectRecord(caseBaseInfo.getApplyNo());
        if(Objects.isNull(caseEffectRecord)) {
            return BetweenSeconds.builder().formatStr("").seconds(0).active(false).build();
        } else {
            boolean active = true;
            ICaseEffect caseEffect = CaseEffectBuilder.build(caseEffectRecord.getAreaType());
            //获取挂起时间
            long suspendTimes = this.getAllSuspendTimes(caseBaseInfo, caseEffect);

            if (Objects.isNull(caseEffectRecord.getEffectTimeReckon())) {
                active = ((IEffectTime)caseEffect).atWorkTime(new Date()) && active;
                long workMs = caseEffect.effectMs(workDate, endDate);
                return EffectDateUtil.uiEffectFmt(workMs - suspendTimes).setActive(active);
            }else{
                return EffectDateUtil.uiEffectFmt(caseEffectRecord.getEffectTimeReckon()).setActive(false);
            }
        }
    }


    /**
     * 获取信审首次挂起时间
     * @param caseBaseInfo
     * @param caseEffect
     * @return
     */
    public long getSuspendTimes(CaseBaseInfo caseBaseInfo, ICaseEffect caseEffect) {
        long suspendMs = 0;
        Date startDate = caseBaseInfo.getSuspendStart();
        Date endDate = caseBaseInfo.getSuspendEnd();
        if ("once".equals(caseBaseInfo.getSuspendTimes())) {//判断是否挂起过
            if (ObjectUtil.isNotNull(startDate) && ObjectUtil.isNotNull(endDate)) {//判断是否处于挂起中
                suspendMs = caseEffect.effectMs(startDate, endDate);
            } else {
                suspendMs = caseEffect.effectMs(startDate, new Date());
            }
        } else if ("again".equals(caseBaseInfo.getSuspendTimes())) {
            suspendMs = caseEffect.effectMs(startDate, endDate);
        } else {
            return suspendMs;
        }

        return suspendMs;
    }

    /**
     * 获取信审所有挂起时间
     * @param caseBaseInfo
     * @param caseEffect
     * @return
     */
    private long getAllSuspendTimes(CaseBaseInfo caseBaseInfo, ICaseEffect caseEffect) {
        long suspendMs = 0;
        List<MainEventLog> mainEventLogs = mainEventLogService.list(Wrappers.<MainEventLog>lambdaQuery()
                .eq(MainEventLog::getEventType, MainEventTypeEnum.SUSPEND_TASK.getCode() )
                .eq(MainEventLog::getApplyNo, caseBaseInfo.getApplyNo())
                .eq(MainEventLog::getProcessType, WorkflowNodeEnum.CREDIT_CAR)
                .eq(MainEventLog::getStatus,"active")
                .orderByDesc(MainEventLog::getStartTime)
        );
        for(MainEventLog mainEventLog : mainEventLogs){
            if(ObjectUtils.isNull(mainEventLog.getEndTime())){
                suspendMs += caseEffect.effectMs(mainEventLog.getStartTime(),  new Date());
            }else {
                suspendMs += caseEffect.effectMs(mainEventLog.getStartTime(),  mainEventLog.getEndTime());
            }
        }
        return suspendMs;
    }


    @PostMapping(value = "/queryApproveTaskSummary")
    @ApiOperation(value = "查询审批任务概要信息")
    public IResponse<ApproveTaskVo> queryApproveTaskSummary(
        @ModelAttribute WorkTaskPoolCondition workTaskPoolCondition) {
        List<WorkTaskPool> workTaskPoolList = workTaskPoolService.list(Wrappers.<WorkTaskPool>query().lambda()
            .eq(WorkTaskPool::getApplyNo, workTaskPoolCondition.getApplyNo()));

        ApproveTaskVo approveTaskVo = new ApproveTaskVo();
        /**  标签信息 **/
        List<ApproveMakeLabel> labelList = approveMakeLabelService.list(Wrappers.<ApproveMakeLabel>query().lambda()
            .eq(ApproveMakeLabel::getApplyNo, workTaskPoolCondition.getApplyNo())
            .eq(ApproveMakeLabel::getLabelLocation, LabelPositionEnum.CREDITANALYSIS.getCode())
        );
        if (ObjectUtils.isNotEmpty(workTaskPoolList)) {
            BeanUtils.copyProperties(workTaskPoolList.get(0), approveTaskVo);
        }
        approveTaskVo.setLabelList(labelList);
        return IResponse.success(approveTaskVo);
    }

    @GetMapping("/queryApprove")
    @ApiOperation("暂存审批意见查询")
    public IResponse queryApprove(@ModelAttribute ApproveCondition approveCondition) {
        WorkTaskPool workTaskPool = workTaskPoolService.getOne(
            Wrappers.<WorkTaskPool>lambdaQuery()
                .eq(WorkTaskPool::getApplyNo, approveCondition.getApplyNo())
                .eq(WorkTaskPool::getStageId, approveCondition.getStageId())
        );
        if (Objects.nonNull(workTaskPool)) {
            String remainDataStr = workTaskPool.getRemainData();
            ApproveRecordVO record = new ApproveRecordVO();
            record.setApplyNo(approveCondition.getApplyNo());
            record.setStageId(approveCondition.getStageId());
            if (StringUtils.isNotBlank(remainDataStr)) {
                JSONObject remainJson = JSONObject.parseObject(remainDataStr);
                if (remainJson.containsKey(AfsEnumUtil.key(RemainKeyEnum.RECORD))) {
                    record = JSONObject.parseObject(remainJson.get(AfsEnumUtil.key(RemainKeyEnum.RECORD)).toString(), ApproveRecordVO.class);
                    record.setQueried(true);
                }
            }
            return IResponse.success(record);
        } else {
            return IResponse.success("");
        }
    }

    @GetMapping("/querySomeApprove")
    @ApiOperation("已出结果案件信贷报告展示")
    public IResponse querySomeApprove(@ModelAttribute ApproveCondition approveCondition) {
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(
            Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo, approveCondition.getApplyNo())
        );

        String businessStateIn = caseBaseInfo.getBusinessStateIn();
        if (businessStateIn.equals(AfsEnumUtil.key(BusinessStateInEnum.APPROVED)) || businessStateIn.equals(AfsEnumUtil.key(BusinessStateInEnum.REJECT))
            || businessStateIn.equals(AfsEnumUtil.key(BusinessStateInEnum.REPEAL)) || businessStateIn.equals(AfsEnumUtil.key(BusinessStateInEnum.CONDITIONAL_APPROVE))) {

            List<WorkTaskPool> workTaskPoolList = workTaskPoolService.list(
                Wrappers.<WorkTaskPool>query().lambda()
                    .eq(WorkTaskPool::getApplyNo, approveCondition.getApplyNo())
                    .orderByDesc(WorkTaskPool::getCreateTime));
            if (CollectionUtil.isNotEmpty(workTaskPoolList)) {
                WorkTaskPool workTaskPool = workTaskPoolList.get(0);
                if (Objects.nonNull(workTaskPool)) {
                    String remainDataStr = workTaskPool.getRemainData();
                    CaseApproveRecord record = new CaseApproveRecord();
                    record.setApplyNo(approveCondition.getApplyNo());
                    record.setStageId(approveCondition.getStageId());
                    if (StringUtils.isNotBlank(remainDataStr)) {
                        JSONObject remainJson = JSONObject.parseObject(remainDataStr);
                        if (remainJson.containsKey(AfsEnumUtil.key(RemainKeyEnum.RECORD))) {
                            record = JSONObject.parseObject(remainJson.get(AfsEnumUtil.key(RemainKeyEnum.RECORD)).toString(), CaseApproveRecord.class);
                        }
                    }
                    return IResponse.success(record);
                } else {
                    return IResponse.success("");
                }
            }
        }
        return IResponse.success("");
    }

    @PostMapping("/saveApprove")
    @ApiOperation("信贷分析暂存")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveApprove(@RequestBody ApproveSubmitVO approveSubmitVO) {
        if (CollectionUtil.isNotEmpty(approveSubmitVO.getCreditOptionList())) {
            List<CaseCreditOption> optionList = approveSubmitVO.getCreditOptionList();
            caseCreditOptionService.updateBatchById(optionList);
        }
        return IResponse.success(approveSubmitVO);
    }

    /**
     * TODO 如果时退回经销商修改，需要修改本地状态
     *
     * @param approveSubmitVO
     * @return
     */
    @PostMapping( "submitApprove")
    @ApiOperation("信贷分析提交审批")
    public IResponse submitApprove(@RequestBody ApproveSubmitVO approveSubmitVO) {
        CaseApproveRecord caseApproveRecord = approveSubmitVO.getApproveRecord();
        log.info("提交审批入参:{}",JSONObject.toJSON(approveSubmitVO));
        //当前登陆用户
        String useName = SecurityUtils.getUsername();
        WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
            .eq(StringUtils.isNotEmpty(caseApproveRecord.getApplyNo()),WorkflowTaskInfo::getBusinessNo, caseApproveRecord.getApplyNo())
            .eq(WorkflowTaskInfo::getTaskId, caseApproveRecord.getTaskId())
            .eq(WorkflowTaskInfo::getProcessInstanceId, caseApproveRecord.getStageId()));
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
            .eq(CaseBaseInfo::getApplyNo, caseApproveRecord.getApplyNo()));
        //判断案件挂起状态
        if (StrUtil.equals(WhetherEnum.YES.getCode(),caseBaseInfo.getSuspendStatus())){
            throw new AfsBaseException("此订单已挂起,请先人工解除挂起状态");
        }
        if (StrUtil.equals("end",workflowTaskInfo.getStatus())){
            throw new AfsBaseException("该案件已提交处理，请勿重复处理!");
        }
        String caseStatus = caseBaseInfo.getBusinessStateIn();
        log.info("订单号:{}开始向客户端{}发送websocket消息",caseApproveRecord.getApplyNo(),useName);
        WebSocketUtils.sendOneMessage(useName,"");
        //审批人员打开案件 此时案件已经回收
        if (AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(caseStatus)
            || AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGNING).equals(caseStatus)) {
            throw new AfsBaseException("该案件已不在当前队列，无法操作!");
        }
        if (ObjectUtils.isNotEmpty(workflowTaskInfo)) {
            if (useName.equals(workflowTaskInfo.getAssign())) {
                if(ObjectUtils.isNotEmpty(workflowTaskInfo.getOperationType())) {
                    if (workflowTaskInfo.getOperationType().equals(AfsEnumUtil.key(FlowTaskOperationEnum.DEALER3BACK))) {
                        return new IResponse().setCode("0001").setMsg("该案件被经销商主动撤回");
                    }
                }
                SubmitTaskParam submitTaskParam = approveTaskService.buildSubmitTaskParam(approveSubmitVO, caseApproveRecord, workflowTaskInfo);

                //判断下一个节点是否在分单时间内，如果不是分单时间内推到等待节点
                String nextUserDefinedIndex = "";
                FlowTaskOperationEnum operationEnum = null;
                operationEnum = FlowTaskOperationEnum.valueOf(submitTaskParam.getOperationType().toUpperCase());
                switch (operationEnum) {
                    case SUBMIT:
                        if(ApproveNodeEnum.FIRST_NODE.getCode().equals(workflowTaskInfo.getUserDefinedIndex())){
                            nextUserDefinedIndex = ApproveNodeEnum.SECOND_NODE.getCode();
                        }else if(ApproveNodeEnum.SECOND_NODE.getCode().equals(workflowTaskInfo.getUserDefinedIndex())){
                            nextUserDefinedIndex = ApproveNodeEnum.END_NODE.getCode();
                        }
                        break;
                    case BACK2DEALER:
                        nextUserDefinedIndex = workflowTaskInfo.getUserDefinedIndex();
                        break;
                    case REFUSE:
                        nextUserDefinedIndex = ApproveNodeEnum.FIRST_NODE.getCode();
                        break;
                    case BACK:
                        nextUserDefinedIndex = approveSubmitVO.getBackUserDefineIndex();
                        break;
                    case REFUSECHECK:
                        nextUserDefinedIndex = ApproveNodeEnum.END_NODE.getCode();
                        break;
                    default:
                        log.warn("no match workflow operation.");
                }
                //默认0
                String isLastSubmitNotSplittingTime = AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.NO);
                List<CaseBaseInfo> list = new ArrayList<>();
                list.add(caseBaseInfo);
                List<CaseBaseInfo> timeCaseList = splittingTimeService.timeBeforCase(list,nextUserDefinedIndex);
                if(timeCaseList.size() == 0){
                    isLastSubmitNotSplittingTime = AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.YES);
                }
                // 设置工作流需要的参数
                buildFlowVariable(workflowTaskInfo, caseBaseInfo, submitTaskParam);
                //查询是否有异常单子
                List<CaseBaseInfo> caseList = caseBaseInfoService.queryCaseBaseInfoByUserDefinedIndex(workflowTaskInfo.getUserDefinedIndex(),1,null);
                log.info("执行异常恢复-caseListSecond:{}"+caseList);
                Long caseInfoId = null;//异常恢复的案件id
                if(caseList.size() > 0){
                    CaseBaseInfo baseInfo = caseList.get(0);
                    //不在分单时间内，就不获取异常恢复的案件
                    List<CaseBaseInfo> list1 = new ArrayList<>();
                    list.add(baseInfo);
                    List<CaseBaseInfo> timeCaseList1 = splittingTimeService.timeBeforCase(list1,workflowTaskInfo.getUserDefinedIndex());
                    if(timeCaseList1.size() > 0){
                        if(AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGN).equals(baseInfo.getBusinessStateIn())
                                || AfsEnumUtil.key(BusinessStateInEnum.EXCEPTIONALLOCATION).equals(baseInfo.getBusinessStateIn())){
                            submitTaskParam.setRetryExceptionId(baseInfo.getFlowParseExceptionId());
                            caseInfoId = baseInfo.getId();
                            submitTaskParam.setCaseInfoId(caseInfoId);
                            caseBaseInfoService.update(
                                    Wrappers.<CaseBaseInfo>lambdaUpdate()
                                            .eq(CaseBaseInfo::getApplyNo,baseInfo.getApplyNo())
                                            .set(CaseBaseInfo::getIsSubmittingApproval,AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.NO))
                            );
                        }
                    }
                }
                submitTaskParam.setIsLastSubmitNotSplittingTime(isLastSubmitNotSplittingTime);
                submitTaskParam.setNextUserDefinedIndex(nextUserDefinedIndex);
                final IResponse<Boolean> response = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.APPROVE);
                log.info("workflowHelper.submitTask:{}",response);
                if(CaseConstants.CODE_SUCCESS.equals(response.getCode())){
                    caseBaseInfoService.update(
                            Wrappers.<CaseBaseInfo>lambdaUpdate()
                                    .eq(CaseBaseInfo::getApplyNo, caseBaseInfo.getApplyNo())
                                    .set(CaseBaseInfo::getIsLastSubmitNotSplittingTime, isLastSubmitNotSplittingTime));
                }
                doAfterWorkflowOpt(approveSubmitVO, workflowTaskInfo, caseBaseInfo, submitTaskParam, response);

                mainEventLogService.update(Wrappers.<MainEventLog>lambdaUpdate()
                        .eq(MainEventLog::getApplyNo, approveSubmitVO.getApproveRecord().getApplyNo())
                        .eq(MainEventLog::getEventType, MainEventTypeEnum.SUSPEND_TASK.getCode())
                        .eq(MainEventLog::getProcessType, WorkflowNodeEnum.CREDIT_CAR)
                        .eq(MainEventLog::getStatus,"active")
                        .set(MainEventLog::getStatus, "end"));
                return response;
            } else {
                return new IResponse().setCode("0001").setMsg("该案件已不在当前队列，无法操作");
            }
        } else {
            return IResponse.success("");
        }
    }

    /**
     * 信审审批 - 工作流参数
     *
     * @param workflowTaskInfo
     * @param caseBaseInfo
     * @param submitTaskParam
     */
    private void buildFlowVariable(WorkflowTaskInfo workflowTaskInfo, CaseBaseInfo caseBaseInfo,
        SubmitTaskParam submitTaskParam) {
        final UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>lambdaQuery()
            .eq(UserCollocation::getLoginName, workflowTaskInfo.getAssign())
        );

        final JSONObject extendParam = new JSONObject();
        extendParam.put("loanAmt", caseBaseInfo.getLoanAmtRepeat());
        extendParam.put("isRegular", userCollocation.getIsRegular());
        extendParam.put("creditAmtLimit", userCollocation.getApprovedAmount());
        extendParam.put("finallyAgree", submitTaskParam.getFinallyAgree()?WhetherEnum.YES.getCode(): WhetherEnum.NO.getCode());
        extendParam.put("refusecheck",submitTaskParam.getOperationType());
        submitTaskParam.setExtendParams(extendParam);
        WorkflowTaskInfo lastWorkflowTaskInfo = workflowTaskInfoService.getOne(new LambdaQueryWrapper<WorkflowTaskInfo>().eq(WorkflowTaskInfo::getBusinessNo,workflowTaskInfo.getBusinessNo()).orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1"));
        submitTaskParam.getExtendParams().put("lastApprovalNode",lastWorkflowTaskInfo == null ? "" : lastWorkflowTaskInfo.getUserDefinedIndex());

        // 后来加的风险融资额等参数
        workflowWrapperService.busiParamAdvice(submitTaskParam);
    }

    private void doAfterWorkflowOpt(ApproveSubmitVO approveSubmitVO, WorkflowTaskInfo workflowTaskInfo,
        CaseBaseInfo caseBaseInfo, SubmitTaskParam submitTaskParam,
        IResponse<Boolean> response) {
        if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
            // 操作工作流成功后 修改本地状态 或者 通知前置业务系统
            try {
                final CaseBaseInfo tmp = new CaseBaseInfo();
                tmp.setId(caseBaseInfo.getId());
                switch (FlowTaskOperationEnum.valueOf(approveSubmitVO.getOperationType().toUpperCase())) {
                    case BACK2DEALER:
                        // 通知
                        log.info("通知退回经销商start");
                        eventPublisher.publishEvent(new ApproveAuditEvent(this,
                            workflowTaskInfo.getBusinessNo(),
                            workflowTaskInfo.getProcessInstanceId(),
                            NormalSubmitType.SEND_BACK_TO_DEALER,
                            SecurityUtils.getUsername(),
                            approveSubmitVO.getApproveRecord().getApproveReason(),
                            submitTaskParam.getRemark()));
                        log.info("通知退回经销商end");
                        break;
                    case SUBMIT:
                        //通过
                        if (ApproveNodeEnum.FIRST_NODE.getCode().equals(approveSubmitVO.getUserDefineIndex())) {
                            //获取工作流变量，查看是否需要视频面审
                            IResponse<List<FlowVariable>> listIResponse = flowRunFeign.listVariables(workflowTaskInfo.getProcessInstanceId());
                            log.info("获取工作流变量结果={}",listIResponse);
                            if (Objects.equals(CommonConstants.SUCCESS,listIResponse.getCode())&&listIResponse.getData()!=null) {
                                for (FlowVariable datum : listIResponse.getData()) {
                                    if (FlowConstant.RISK_CHECK.equals(datum.getName())&&AfsEnumUtil.key(RiskStatus.RISK_RES_FACE_REVIEW).equals(datum.getValue())) {
                                        //添加视频面审节点审批日志
                                        Date date = new Date();
                                        CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                                        caseApproveRecord.setApplyNo(approveSubmitVO.getApproveRecord().getApplyNo());
                                        caseApproveRecord.setDisposeNode("faceReview");
                                        caseApproveRecord.setDisposeNodeName("视频面审");
                                        caseApproveRecord.setApproveSuggest(FlowTaskOperationEnum.FACEREVIEW.getCode());
                                        caseApproveRecord.setApproveSuggestName(FlowTaskOperationEnum.FACEREVIEW.getDesc());
                                        caseApproveRecord.setApproveRemark("自动转视频面审");
                                        caseApproveRecord.setDisposeStaff("视频面审");
                                        caseApproveRecord.setApproveStartTime(date);
                                        caseApproveRecord.setApproveEndTime(date);
                                        caseApproveRecord.setClickTime(date);
                                        caseApproveRecordService.save(caseApproveRecord);
                                        //通知进件需要视频面审
                                        List<String> msg= Collections.singletonList("自动转视频面审");
                                        approveSubmitVO.setApproveSuggestNameList(msg);
                                        faceReviewHandlerService.handler(approveSubmitVO);
                                    }
                                }
                            }
                        }
                        break;
                    case BACK:
                        // 修改状态
                        caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                                .eq(CaseBaseInfo::getApplyNo, approveSubmitVO.getApproveRecord().getApplyNo())
                                .eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.IN_CHECK))
                                .set(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK)));
                        //退回上一节点，修改决策引擎流程变量
                        FlowVariable flowVariable=new FlowVariable();
                        flowVariable.setFlowInstanceId(workflowTaskInfo.getProcessInstanceId());
                        flowVariable.setName(FlowConstant.RISK_CHECK);
                        flowVariable.setValue(AfsEnumUtil.key(RiskStatus.RISK_RES_NONE));
                        workflowHelper.setFlowVariableByFlowInstance(flowVariable);
                        break;
                    case FACEREVIEW: {
                        // 修改状态
                        caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                                .eq(CaseBaseInfo::getApplyNo, approveSubmitVO.getApproveRecord().getApplyNo())
                                .eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.IN_CHECK))
                                .set(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK)));
                        //通知进件需要视频面审
                        faceReviewHandlerService.handler(approveSubmitVO);
                        break;
                    }
                    default:
                        break;
                }

            } catch (Throwable throwable) {
                log.error("通知业务系统审批进度失败.", throwable);
            }
        }
    }

    @GetMapping("/testWorkflow")
    public IResponse testWorkflow(@ModelAttribute ApproveCondition approveCondition) {
        testStart(approveCondition);
        return IResponse.success("SUCCESS");
    }

    @Transactional(rollbackFor = Exception.class)
    public WorkProcessScheduleInfoTemp testStart(ApproveCondition approveCondition) {
        // 保存工作流实例
        WorkProcessScheduleInfoTemp info = new WorkProcessScheduleInfoTemp();
        info.setAfsFlowKey(WorkflowType.NORMAL_NEW.getAfsFlowKey());
        info.setApplyNo(approveCondition.getApplyNo());
        info.setProcessName(approveCondition.getApplyNo() + "常规审批测试");
        info.setStartTime(new Date());
        workProcessScheduleInfoTempService.save(info);
        ;
        return info;
    }

    /**
     * @Description 查询当前操作组所有人员
     * <AUTHOR>
     * @Date 2020/7/7 18:21
     */

    @GetMapping("/listTaskHandler")
    public IResponse getNodeHandler(ChangeParamsCondition condition) {
        log.info("查询当前操作组人员入口参数：" + condition);
        String taskNodeName = condition.getTaskNodeName();//审核节点

        IResponse<List<FlowUser>> response = new IResponse<>();
        List<FlowUser> flowUserList = new ArrayList<>();

        if(condition.getTaskIds() == null || condition.getTaskIds().size() == 0){
            List<WorkflowTaskInfo> workflowTaskInfos = null;
            if(StringUtils.isNotEmpty(condition.getTaskId())){
                workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getTaskId, condition.getTaskId())
                        .in(WorkflowTaskInfo::getStatus, FlowStatusEnum.CREATE.getCode(),FlowStatusEnum.ACTIVE.getCode())
                        .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getApprovePackageId())
                        .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId()));
            }else {
                workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo, condition.getApplyNo())
                        .in(WorkflowTaskInfo::getStatus, FlowStatusEnum.CREATE.getCode(),FlowStatusEnum.ACTIVE.getCode())
                        .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getApprovePackageId())
                        .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime).last("LIMIT 1") );
            }

            log.info("workflowTaskInfos-1:{}"+workflowTaskInfos);
            if(workflowTaskInfos.size() == 1){
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfos.get(0);
                CaseConfParam param = null;
                if(ApproveNodeEnum.FIRST_NODE.getCode().equals(workflowTaskInfo.getUserDefinedIndex())){
                    param = caseConfParamService.getOne(
                            Wrappers.<CaseConfParam>lambdaQuery()
                                    .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_FIRST)
                    );
                }else if(ApproveNodeEnum.SECOND_NODE.getCode().equals(workflowTaskInfo.getUserDefinedIndex())){
                    param = caseConfParamService.getOne(
                            Wrappers.<CaseConfParam>lambdaQuery()
                                    .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_SECOND)
                    );
                }else if(ApproveNodeEnum.END_NODE.getCode().equals(workflowTaskInfo.getUserDefinedIndex())){
                    param = caseConfParamService.getOne(
                            Wrappers.<CaseConfParam>lambdaQuery()
                                    .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_END)
                    );
                }
                if (Objects.isNull(param)) {
                    return IResponse.fail("分单任务参数不存在，请先维护当前参数");
                }
                log.info("CaseConfParam-1:{}"+param);
                IResponse<ResponseUser> responseUserRes = workflowHelper.listUser(param.getParamValue());
                log.info("ResponseUser-1:{}"+responseUserRes);
                if(CommonConstants.SUCCESS.equals(responseUserRes.getCode())){
                    ResponseUser responseUser = responseUserRes.getData();
                    for(int i=0;i<responseUser.getUsers().size();i++){
                        QueryUserVo vo = new QueryUserVo();
                        BeanUtils.copyProperties(responseUser.getUsers().get(i),vo);
                        FlowUser flowUser = new FlowUser();
                        flowUser.setUserId(vo.getUserId());
                        flowUser.setUserName(vo.getUserName());
                        flowUser.setTotalTaskLimit(vo.getTotalTaskLimit());
                        flowUserList.add(flowUser);
                    }
                }
            }else{
                List<WorkflowTaskInfo> workflowTaskInfos1 = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getTaskId, condition.getTaskId())
                        .eq(WorkflowTaskInfo::getFlowPackageId, flowConfigProperties.getApprovePackageId())
                        .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId()));
                if(workflowTaskInfos1.size() == 0){
                    CaseConfParam param = caseConfParamService.getOne(
                            Wrappers.<CaseConfParam>lambdaQuery()
                                    .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_FIRST)
                    );
                    if (Objects.isNull(param)) {
                        return IResponse.fail("分单任务参数不存在，请先维护当前参数");
                    }
                    IResponse<ResponseUser> responseUserRes = workflowHelper.listUser(param.getParamValue());
                    log.info("ResponseUser-2:{}"+responseUserRes);
                    if(CommonConstants.SUCCESS.equals(responseUserRes.getCode())){
                        ResponseUser responseUser = responseUserRes.getData();
                        for(int i=0;i<responseUser.getUsers().size();i++){
                            QueryUserVo vo = new QueryUserVo();
                            BeanUtils.copyProperties(responseUser.getUsers().get(i),vo);
                            FlowUser flowUser = new FlowUser();
                            flowUser.setUserId(vo.getUserId());
                            flowUser.setUserName(vo.getUserName());
                            flowUser.setTotalTaskLimit(vo.getTotalTaskLimit());
                            flowUserList.add(flowUser);
                        }
                    }
                }
            }
        }else{
            if(condition.getUserDefinedIndexs().size() > 0){
                List<String> myList = condition.getUserDefinedIndexs().stream().distinct().collect(Collectors.toList());
                if(myList.size() > 1){
                    return IResponse.fail("多条数据必须是同一岗位的数据!");
                }
            }
            String userDefinedIndex = condition.getUserDefinedIndexs().get(0);
            CaseConfParam param = null;
            if(ApproveNodeEnum.FIRST_NODE.getCode().equals(userDefinedIndex)){
                param = caseConfParamService.getOne(
                        Wrappers.<CaseConfParam>lambdaQuery()
                                .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_FIRST)
                );
            }else if(ApproveNodeEnum.SECOND_NODE.getCode().equals(userDefinedIndex)){
                param = caseConfParamService.getOne(
                        Wrappers.<CaseConfParam>lambdaQuery()
                                .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_SECOND)
                );
            }else if(ApproveNodeEnum.END_NODE.getCode().equals(userDefinedIndex)){
                param = caseConfParamService.getOne(
                        Wrappers.<CaseConfParam>lambdaQuery()
                                .eq(CaseConfParam::getParamType, Const.CREDIT_CAR_END)
                );
            }
            if (Objects.isNull(param)) {
                return IResponse.fail("分单任务参数不存在，请先维护当前参数");
            }
            log.info("CaseConfParam-3:{}"+param);
            IResponse<ResponseUser> responseUserRes = workflowHelper.listUser(param.getParamValue());
            log.info("ResponseUser-3:{}"+responseUserRes);
            if(CommonConstants.SUCCESS.equals(responseUserRes.getCode())){
                ResponseUser responseUser = responseUserRes.getData();
                for(int i=0;i<responseUser.getUsers().size();i++){
                    QueryUserVo vo = new QueryUserVo();
                    BeanUtils.copyProperties(responseUser.getUsers().get(i),vo);
                    FlowUser flowUser = new FlowUser();
                    flowUser.setUserId(vo.getUserId());
                    flowUser.setUserName(vo.getUserName());
                    flowUser.setTotalTaskLimit(vo.getTotalTaskLimit());
                    flowUserList.add(flowUser);
                }
            }
        }
        log.info("flowUsers:{}"+response);
        if (CommonConstants.SUCCESS.equals(response.getCode())) {
            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }
            if(flowUserList.size() == 0){
                flowUserList = response.getData();
            }
            log.info("查询对应部门所有人账号信息{}", flowUserList);
            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(flowUserList.size());
            for (FlowUser flowUser : flowUserList) {
                if (!SecurityUtils.getUser().getUserRealName().equals(flowUser.getUserName())) {
                    if (!ApproveNodeEnum.FIRST_NODE.getDesc().equals(taskNodeName)) {//通过信审复核查找复核额度人员
                        UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                                .eq(UserCollocation::getLoginName, flowUser.getUserId()));
                        log.info("该账号{}账号配置信息{}", flowUser.getUserId(), userCollocation);
                        //防止账号审批额度未配置，额度为空的不在改派查询展示
                        if (userCollocation == null || ObjectUtils.isEmpty(userCollocation.getApprovedAmount())) {
                            continue;
                        }
//                        if (userCollocation.getApprovedAmount().compareTo(loanAmt) == 1) {
                        //这块代码审批额度大于提交额度,如果有用请放开
//                        }
                        String status = userStatusMap.get(flowUser.getUserId());
                        taskHandlerVOList.add(TaskHandlerVO.builder()
                                .staffName(flowUser.getUserId())
                                .staffRealName(flowUser.getUserName())
                                .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
                    } else {
                        String status = userStatusMap.get(flowUser.getUserId());
                        taskHandlerVOList.add(TaskHandlerVO.builder()
                                .staffName(flowUser.getUserId())
                                .staffRealName(flowUser.getUserName())
                                .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
                    }

                }
            }

            List result =  Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail(response.getMsg());
        }
    }

    /**
     * @Description 查询当前操作组所有人员
     * <AUTHOR>
     * @Date 2020/7/7 18:21
     */

    @GetMapping("/queryChannelSale")
    public IResponse queryChannelSale() {

        IResponse<List<SysUserDto>> allUser = userDetailsInfoFeign.getAllUser();
        if (!CommonConstants.SUCCESS.equals(allUser.getCode())) {
            return IResponse.fail("查询所有用户失败");
        }
        List<SysUserDto> userData =  allUser.getData();

        if (ObjectUtils.isNotEmpty(userData)) {
            //根据userId去重
            List<SysUserDto> lists = userData.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(SysUserDto::getUserId))), ArrayList::new));

            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }

            log.info("查询所有人账号信息{}", userStatusMap);
            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(lists.size());
            for (SysUserDto list : lists) {
                if (!SecurityUtils.getUser().getUsername().equals(list.getUsername())) {
                    String status = userStatusMap.get(list.getUsername());
                    taskHandlerVOList.add(TaskHandlerVO.builder()
                            .staffName(list.getUsername())
                            .staffRealName(list.getUserRealName())
                            .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
                }
            }

            List result =  Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail("未查询到相关人员信息");
        }
    }
    /**
     * @Description 查询当前产品上线操作组所有人员
     * <AUTHOR>
     * @Date 2024/03/21
     */
    @GetMapping("/queryProductNewSale")
    public IResponse queryProductNewSale(ChangeParamsCondition condition) {
        log.info("queryProductNewSalecondition:{}"+condition);
        String userDefinedIndex = condition.getUserDefinedIndexs().get(0);

        if (StringUtils.isEmpty(userDefinedIndex)) {
            return IResponse.fail("参数不存在!");
        }
        log.info("userDefinedIndex:{}"+userDefinedIndex);

        List<FlowUser> flowUserList = new ArrayList<>();
        String groupId = ProductApproveNodeEnum.fromString(userDefinedIndex).getDesc();
        IResponse<ResponseUser> responseUserRes = workflowHelper.listUser(groupId);
        log.info("ResponseUser-1:{}" + responseUserRes);
        if (CommonConstants.SUCCESS.equals(responseUserRes.getCode())) {
            ResponseUser responseUser = responseUserRes.getData();
            for(int i=0;i<responseUser.getUsers().size();i++){
                QueryUserVo vo = new QueryUserVo();
                BeanUtils.copyProperties(responseUser.getUsers().get(i),vo);
                FlowUser flowUser = new FlowUser();
                flowUser.setUserId(vo.getUserId());
                flowUser.setUserName(vo.getUserName());
                flowUser.setTotalTaskLimit(vo.getTotalTaskLimit());
                flowUserList.add(flowUser);
            }

            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }

            if (flowUserList.size() == 0) {
                return IResponse.fail("未查询到相关人员信息");
            }

            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(flowUserList.size());
            for (FlowUser flowUser : flowUserList) {
                if (!SecurityUtils.getUser().getUserRealName().equals(flowUser.getUserName())) {
                    String status = userStatusMap.get(flowUser.getUserId());
                    taskHandlerVOList.add(TaskHandlerVO.builder()
                            .staffName(flowUser.getUserId())
                            .staffRealName(flowUser.getUserName())
                            .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
                }
            }

            List result = Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail("未查询到相关人员信息");
        }
    }

    @GetMapping("/queryFilingSale")
    public IResponse queryFilingSale(ChangeParamsCondition condition) {
        log.info("queryFilingSalecondition:{}"+condition);
        String userDefinedIndex = condition.getUserDefinedIndexs().get(0);

        if (StringUtils.isEmpty(userDefinedIndex)) {
            return IResponse.fail("参数不存在!");
        }
        log.info("userDefinedIndex:{}"+userDefinedIndex);

        List<FlowUser> flowUserList = new ArrayList<>();
        String groupId = FilingNodeEnum.fromString(userDefinedIndex).getDesc();
        IResponse<ResponseUser> responseUserRes = workflowHelper.listUser(groupId);
        log.info("ResponseUser-1:{}" + responseUserRes);
        if (CommonConstants.SUCCESS.equals(responseUserRes.getCode())) {
            ResponseUser responseUser = responseUserRes.getData();
            for(int i=0;i<responseUser.getUsers().size();i++){
                QueryUserVo vo = new QueryUserVo();
                BeanUtils.copyProperties(responseUser.getUsers().get(i),vo);
                FlowUser flowUser = new FlowUser();
                flowUser.setUserId(vo.getUserId());
                flowUser.setUserName(vo.getUserName());
                flowUser.setTotalTaskLimit(vo.getTotalTaskLimit());
                flowUserList.add(flowUser);
            }

            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }

            if (flowUserList.size() == 0) {
                return IResponse.fail("未查询到相关人员信息");
            }

            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(flowUserList.size());
            for (FlowUser flowUser : flowUserList) {
                if (!SecurityUtils.getUser().getUserRealName().equals(flowUser.getUserName())) {
                    String status = userStatusMap.get(flowUser.getUserId());
                    taskHandlerVOList.add(TaskHandlerVO.builder()
                            .staffName(flowUser.getUserId())
                            .staffRealName(flowUser.getUserName())
                            .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
                }
            }

            List result = Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail("未查询到相关人员信息");
        }
    }

    /**
     * @Description 任务操作人变更
     * <AUTHOR>
     * @Date 2020/7/7 20:41taskPool
     */
    @PostMapping("/taskHandler")
    public IResponse taskHandler(@RequestBody TaskHandlerChangeVO taskHandlerChangeVO) {
        return IResponse.fail(null);

    }

    /**
     * @Description 流水解析调用
     * <AUTHOR>
     * @Date 2020/8/31 22:07
     */
    @GetMapping("/dayBookResolve")
    public IResponse dayBookResolve(@RequestParam("applyNo") String applyNo) {
        dayBookResolveService.getDayBookResolve(applyNo);
        return IResponse.success("操作成功!");
    }

    /**
     * @Description 流程撤销
     * <AUTHOR>
     * @Date 2020/7/22 16:48
     */
    @PostMapping("/cancelFlow")
    public IResponse cancelFlow(@RequestBody WorkflowCancelVO cancelVO) {
        return IResponse.success("操作成功");
    }

    /**
     * @Description 指定处理人流程发起
     * <AUTHOR>
     * @Date 2020/7/23 19:43
     */
    @PostMapping("/appointFlow")
    public IResponse appointFlow(@RequestBody WorkflowAppointVO appointVO) {
        /**
         * 当案件处于待分配状态时，此时进行指定处理人时：
         * 1.首先会尝试获取分单定时任务的锁，获取失败时代表当前分单任务正在执行，抛出异常提示
         * 2.成功获得锁后会进行指定处理人操作，处理完毕或异常后释放锁
         */
        boolean hasLock = false;
        try {
            if (SpringContextHolder.getBean(DispatchServcieImpl.class).tryLock(DispatchTypeEnum.CREDIT)) {
                log.info("获取分单任务锁成功，进行执行指定处理人操作");
                hasLock = true;
            } else {
                throw new AfsBaseException("指定处理人失败：当前分单任务正在执行，请稍后重试");
            }
            approveWorkflowService.effectTask(appointVO);
            return IResponse.success("操作成功");
        } finally {
            if (hasLock) {
                log.info("指定处理人执行完毕，清除分单任务锁");
                SpringContextHolder.getBean(DispatchServcieImpl.class).unLock(DispatchTypeEnum.CREDIT);
            }
        }
    }

    /**
     * 抢单
     *
     * <AUTHOR>
     * @date 2020年11月18日
     */
    @PostMapping("/grabOneTask")
    public IResponse grabOneTask() {
        String approveStaff = SecurityUtils.getUsername();
        if (approveStaff == null) {
            throw new AfsBaseException("请重新登录后抢单");
        }
        CaseConfParam grabTaskConfParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
            .eq(CaseConfParam::getParamType, "GRAB_TASK_SWITCH"));
        if (ObjectUtil.isNull(grabTaskConfParam)) {
            throw new AfsBaseException("未配置抢单开关");
        }
        if (WhetherEnum.NO.getCode().equals(grabTaskConfParam.getParamValue())
            || AfsEnumUtil.key(YesOrNoEnum.no).equals(grabTaskConfParam.getParamStatus())) {
            throw new AfsBaseException("抢单开关未开启，无法抢单");
        }
        UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
            .eq(UserCollocation::getLoginName, approveStaff));
        //个人每日抢单上限值
        int weightUpper = userCollocation.getWeightUpper();
        //个人抢单权重
        int grabWeight = userCollocation.getGrabWeight();
        //当前能抢单的数量 = MIN((个人每日抢单上限值-待审核数-暂停数) , 个人抢单权重)
        int totalGrabTaskCount;
        if (weightUpper == 0) {
            throw new AfsBaseException("抢单日上限值为0，无法进行抢单");
        }
        if (grabWeight == 0) {
            throw new AfsBaseException("个人抢单权重为0，无法进行抢单");
        }

        //查询当前常规审批流程的待审核数+暂停数
        List<WorkTaskPool> workTaskPoolList = workTaskPoolService.list(Wrappers.<WorkTaskPool>query().lambda()
            .eq(WorkTaskPool::getApproveStaff, approveStaff)
            .eq(WorkTaskPool::getProcessType, AfsEnumUtil.key(ProcessTypeEnum.GENERA_APPROVE)));
        if (CollectionUtil.isNotEmpty(workTaskPoolList)) {
            workTaskPoolList = workTaskPoolList.stream().filter(task -> {
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo, task.getApplyNo())
                    .and(wrapper1 -> wrapper1.eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK))
                        .or(wrapper2 -> wrapper2.eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.PAUSE)))));
                if (ObjectUtils.isNotEmpty(caseBaseInfo)) {
                    return true;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
            //根据抢单权重判断当前审批人员可以抢单的数量是否小于等于0
            totalGrabTaskCount = Math.min((weightUpper - workTaskPoolList.size()), grabWeight);
        } else {
            totalGrabTaskCount = Math.min(weightUpper, grabWeight);
        }

        //判断抢单数是否已达到上限
        List<WorkTaskPool> grabWorkTaskList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(workTaskPoolList)) {
            grabWorkTaskList = workTaskPoolList.stream().filter(grabWorkTask -> {
                //筛选当前组的抢单任务
                WorkProcessScheduleInfoTemp temp = workProcessScheduleInfoTempService.list(Wrappers.<WorkProcessScheduleInfoTemp>lambdaQuery()
                    .eq(WorkProcessScheduleInfoTemp::getApplyNo, grabWorkTask.getApplyNo())
                    .eq(WorkProcessScheduleInfoTemp::getAfsFlowKey, WorkflowType.NORMAL_APPOINT.getAfsFlowKey())
                    .orderByDesc(WorkProcessScheduleInfoTemp::getCreateTime)
                ).stream().findFirst().orElse(new WorkProcessScheduleInfoTemp());
                if (grabWorkTask.getApproveStaff().equals(temp.getCreateBy()) && WhetherEnum.YES.getCode().equals(temp.getGrabMark())) {
                    return true;
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(grabWorkTaskList)) {
            totalGrabTaskCount = Math.min(grabWeight - grabWorkTaskList.size(), totalGrabTaskCount);
            log.info("审批员{}，每日抢单上限值{}，个人待办数{}，抢单权重{}，已抢单数{}，可抢单数=>{}",
                approveStaff, weightUpper, CollectionUtil.isNotEmpty(workTaskPoolList) ? workTaskPoolList.size() : 0, grabWeight,
                grabWorkTaskList.size(), totalGrabTaskCount);
            if (grabWorkTaskList.size() == grabWeight) {
                throw new AfsBaseException("抢单数量已达到上限(" + grabWeight + ")，无法继续抢单");
            }
        } else {
            log.info("审批员{}，每日抢单上限值{}，个人待办数{}，抢单权重{}，可抢单数=>{}",
                approveStaff, weightUpper, CollectionUtil.isNotEmpty(workTaskPoolList) ? workTaskPoolList.size() : 0, grabWeight, totalGrabTaskCount);
        }
        if (totalGrabTaskCount <= 0) {
            throw new AfsBaseException("可抢单数量为0，无法进行抢单");
        }

        List<CaseBaseInfo> caseBaseInfoAllocationList = caseBaseInfoService.list(Wrappers.<CaseBaseInfo>query().lambda()
            .between(CaseBaseInfo::getTotalLoanAmt, userCollocation.getGrabAmountDown(), userCollocation.getGrabAmountUp())
            .ne(CaseBaseInfo::getIsLock, WhetherEnum.YES.getCode())
            .in(CaseBaseInfo::getBusinessType, Arrays.asList(userCollocation.getGrabBusinessTypes().split(",")))
            .and(wrapper1 -> wrapper1.eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION))
                .or(wrapper2 -> wrapper2.eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGN))))
            .in(CaseBaseInfo::getCarType, Arrays.asList(userCollocation.getCarType().split(",")))
            .in(CaseBaseInfo::getCarNature, Arrays.asList(userCollocation.getCarNature().split(",")))
        );
        if (CollectionUtil.isEmpty(caseBaseInfoAllocationList)) {
            throw new AfsBaseException("当前时间段没有可抢的任务信息");
        }
        caseBaseInfoAllocationList = caseBaseInfoAllocationList.stream().sorted(Comparator.nullsLast(Comparator.comparing(CaseBaseInfo::getIsStick, Comparator.nullsLast(Comparator.reverseOrder())))
            .thenComparing(CaseBaseInfo::getPriority, Comparator.nullsLast(Comparator.reverseOrder()))
            .thenComparing(CaseBaseInfo::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        CaseBaseInfo baseInfo = caseBaseInfoAllocationList.get(0);
        List<WorkProcessScheduleInfoTemp> workProcessScheduleInfoTempList = workProcessScheduleInfoTempService.list(Wrappers.<WorkProcessScheduleInfoTemp>lambdaQuery()
            .eq(WorkProcessScheduleInfoTemp::getApplyNo, baseInfo.getApplyNo())
            .eq(WorkProcessScheduleInfoTemp::getAfsFlowKey, WorkflowType.NORMAL_APPOINT.getAfsFlowKey())
            .eq(WorkProcessScheduleInfoTemp::getGrabMark, WhetherEnum.YES.getCode())
            .orderByDesc(WorkProcessScheduleInfo::getCreateTime));
        if (CollectionUtil.isNotEmpty(workProcessScheduleInfoTempList)) {
            WorkTaskPool grabWorkTaskPoolTemp = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>query().lambda()
                .eq(WorkTaskPool::getStageId, workProcessScheduleInfoTempList.get(0).getId().toString())
                .eq(WorkTaskPool::getApplyNo, baseInfo.getApplyNo()));
            if (ObjectUtil.isNull(grabWorkTaskPoolTemp)) {
                if (caseBaseInfoAllocationList.size() == 1) {
                    throw new AfsBaseException("当前时间段没有可抢的任务信息");
                } else {
                    //继续抢下一单
                    baseInfo = caseBaseInfoAllocationList.get(1);
                }
            }
        }

        return IResponse.fail(null);

    }

    /**
     * 流程撤回
     *
     * <AUTHOR>
     * @date 2020年8月18日
     */
    @PostMapping("/callBackFlow")
    public IResponse callBackFlow(@RequestBody WorkflowCallBackVO callBackVO) {
        WorkProcessScheduleInfo workProcessScheduleInfo = workProcessScheduleInfoService.getById(callBackVO.getScheduleId());
        CaseBaseInfo baseInfo = caseBaseInfoService.getOne(
            Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo, workProcessScheduleInfo.getApplyNo())
        );
        //建议家访不可撤回
        List<WorkTaskPoolHistory> list = workTaskPoolHistoryService.list(Wrappers.<WorkTaskPoolHistory>lambdaQuery()
            .eq(WorkTaskPoolHistory::getApplyNo, workProcessScheduleInfo.getApplyNo())
            .eq(WorkTaskPoolHistory::getStageId, callBackVO.getScheduleId())
            .orderByDesc(WorkTaskPoolHistory::getCreateTime));
        if (CollectionUtils.isNotEmpty(list)) {
            WorkTaskPoolHistory workTaskPoolHistory = list.get(0);
            if (AfsEnumUtil.key(NormalSubmitType.SUGGEST_VISIT).equals(workTaskPoolHistory.getApproveSuggest())) {
                return IResponse.fail("此案件已建议家访，无法撤回！");
            }
        }
        String callBackApproveStaff = SecurityUtils.getUsername();
        String callBackApproveRealName = SecurityUtils.getUser().getUserRealName();
        //根据审批历史记录执行撤回校验
        boolean callbackCheckResult = workTaskPoolHistoryService.checkCallBackData(baseInfo, callBackVO.getScheduleId(), callBackApproveStaff);
        if (callbackCheckResult) {

            //#################审批出结果后撤回###############
            if (AfsEnumUtil.key(BusinessStateInEnum.APPROVED).equals(baseInfo.getBusinessStateIn())
                || AfsEnumUtil.key(BusinessStateInEnum.CONDITIONAL_APPROVE).equals(baseInfo.getBusinessStateIn())) {
                /** TODO 通知进件系统撤销核准数据 **/
                return IResponse.fail("已产生审批结果，无法撤回");
            }

            //#################修订暂停状态下撤回#############
            else if (AfsEnumUtil.key(BusinessStateInEnum.REVISE_PARSE).equals(baseInfo.getBusinessStateIn())) {
                log.info("通知进件系统撤回案件信息，申请编号{}************", baseInfo.getApplyNo());
                approveInformInfoService.submitApproveCallBackApply(baseInfo.getApplyNo(), baseInfo.getBusinessStateIn(), callBackVO.getScheduleId());
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException e) {
                    log.error("发生异常", e);
                }
                baseInfo = caseBaseInfoService.getCaseBaseInfoByApplyNoInTran(workProcessScheduleInfo.getApplyNo());
                if (AfsEnumUtil.key(BusinessStateInEnum.REVOCATION).equals(baseInfo.getBusinessStateIn())) {
                    return IResponse.success("撤回成功！");
                } else {
                    return IResponse.fail("撤回失败,您也可稍后重试");
                }
            }

            //#################审批内部撤回##########################
            else {
                log.info("审批内部撤回，撤回申请人：{}************", callBackApproveRealName);
            }
            return IResponse.success("撤回成功");
        } else {
            //#################未能直接撤回，保存撤回申请消息#############
            WorkTaskPool nextWorkTask = workTaskPoolService.getNextWorkTaskByCondition(workProcessScheduleInfo.getApplyNo(), callBackVO.getScheduleId(), callBackApproveStaff);
            CaseNoticeInfo caseNoticeInfo = new CaseNoticeInfo();
            caseNoticeInfo.setApplyNo(workProcessScheduleInfo.getApplyNo());
            caseNoticeInfo.setNoticeType(AfsEnumUtil.key(CallBackTypeEnum.WORKFLOW_APPROVER));
            caseNoticeInfo.setNotice("审批员【" + SecurityUtils.getUser().getUserRealName() + "】申请撤回提交，原因：" + callBackVO.getMessage());
            caseNoticeInfo.setSendBy(callBackApproveStaff);
            caseNoticeInfo.setReceivedBy(nextWorkTask.getApproveStaff());
            caseNoticeInfo.setIsRead(AfsEnumUtil.key(ReadEnum.UNREAD));
            caseNoticeInfoService.saveOrUpdate(caseNoticeInfo);
            return IResponse.fail("当前案件无法撤回");
        }
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * 查询常规审批流程下一节点任务是否是打开状态
     *
     * @param stageId
     */
    @GetMapping(value = "/getIsOpenByStageId/{stageId}")
    @ApiOperation(value = "查询常规审批流程下一节点任务是否是打开状态")
    public IResponse getIsOpenByStageId(@PathVariable(value = "stageId") String stageId) {
        WorkProcessScheduleInfo workProcessScheduleInfo = workProcessScheduleInfoService.getById(stageId);
        String callBackApproveStaff = SecurityUtils.getUsername();
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
            .eq(CaseBaseInfo::getApplyNo, workProcessScheduleInfo.getApplyNo()));
        if (AfsEnumUtil.key(BusinessStateInEnum.REPEAL).equals(caseBaseInfo.getBusinessStateIn())
            || AfsEnumUtil.key(BusinessStateInEnum.REJECT).equals(caseBaseInfo.getBusinessStateIn())) {
            throw new AfsBaseException("已产生审批结果，无法撤回");
        }
        //根据审批历史记录执行撤回校验
        workTaskPoolHistoryService.checkCallBackData(caseBaseInfo, stageId, callBackApproveStaff);
        WorkTaskPool nextWorkTask = workTaskPoolService.getNextWorkTaskByCondition(workProcessScheduleInfo.getApplyNo(), stageId, callBackApproveStaff);
        return IResponse.success(nextWorkTask.getIsOpen());
    }

    /**
     * @description: 维护是否打开状态
     * <AUTHOR>
     * @created 2020/7/7 20:54
     * @version 1.0
     */
    @PostMapping("/setStatusIsOpen")
    @Transactional(rollbackFor = Exception.class)
    public IResponse setStatusIsOpen(@ModelAttribute ApproveCondition approveCondition) {
        WorkTaskPool workTaskPool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>query().lambda()
            .eq(WorkTaskPool::getApproveStaff, approveCondition.getApproveStaff())
            .eq(WorkTaskPool::getStageId, approveCondition.getStageId()));
        if (ObjectUtils.isNotEmpty(workTaskPool)) {
            workTaskPool.setIsOpen(WhetherEnum.YES.getCode());
            log.info("更新是否打开状态：" + workTaskPool.getApproveStaff());
            workTaskPoolService.updateById(workTaskPool);
            ApproveOpenDto approveOpenDto = new ApproveOpenDto();
            approveOpenDto.setApplyNo(approveCondition.getApplyNo());
            approveOpenDto.setStageId(approveCondition.getStageId());
            approveOpenDto.setOpenTime(new Date());
            approveOpenDto.setOpenResult(WhetherEnum.YES.getCode());
            approveOpenDto.setProcessType(approveCondition.getProcessType());
            informInfoService.incomingApproveOpenResult(approveOpenDto);
            log.info("打开状态通知mq发送成功：{}" + approveCondition.getProcessType());
        } else {
            log.info("无流程信息更新失败！");
        }
        return IResponse.success("更新成功");
    }

    /**
     * <p>Description:</p>
     * 更新审批人员的打开状态（已打开）及操作时间，即个人待办任务的更新时间
     *
     * <AUTHOR>
     * @date 2020年10月25日
     */
    @PostMapping("/updateOperateTime")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateOperateTime(@ModelAttribute ApproveCondition approveCondition) {
        WorkTaskPool workTaskPool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>query().lambda()
            .eq(WorkTaskPool::getApproveStaff, approveCondition.getApproveStaff())
            .eq(WorkTaskPool::getStageId, approveCondition.getStageId()));
        if (ObjectUtils.isNotEmpty(workTaskPool)) {
            workTaskPool.setIsOpen(WhetherEnum.YES.getCode());
            workTaskPool.setUpdateTime(new Date());
            workTaskPoolService.updateById(workTaskPool);
        } else {
            log.info("无流程信息更新失败！");
            throw new AfsBaseException("该案件已不在当前队列，无法操作");
        }
        return IResponse.success("更新成功");
    }

    /**
     * @Description 获取当前节点可操作命令
     * <AUTHOR>
     * @Date 2020/9/24 14:26
     */
    @GetMapping("/getFlowCmd")
    public IResponse getFlowCmd(@RequestParam("stageId") String stageId) {
        List<String> cmdList = new ArrayList<>();
        if (StringUtils.isNotBlank(stageId)) {
            workProcessScheduleInfoService.getById(stageId);
            List<DicDataDto> dicDtoList = DicHelper.getDicMaps("approveSuggest").get("approveSuggest");
            if (CollectionUtil.isNotEmpty(dicDtoList)) {
                dicDtoList.stream().filter(
                    dic -> {
                        return !StringUtils.equals(dic.getValue(), AfsEnumUtil.key(NormalSubmitType.SEND_BACK))
                            && !StringUtils.equals(dic.getValue(), AfsEnumUtil.key(NormalSubmitType.SEND_BACK_TO_DEALER));
                    }
                ).collect(Collectors.toMap(DicDataDto::getTitle, Function.identity()));
            }
            return IResponse.success(cmdList);
        }
        return IResponse.success("操作成功!");
    }

    /**
     * 查询案件业务状态
     * @param applyNo 申请编号
     * <AUTHOR>
     * @date 2020年10月13日
     */
    @ApiOperation("查询案件业务状态")
    @GetMapping(value = "/getCaseBuinessStateInfo")
    public IResponse getCaseBuinessStateInfo(@RequestParam("applyNo") String applyNo) {
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne ( Wrappers.<CaseBaseInfo>query ( ).lambda ( )
                .eq ( CaseBaseInfo::getApplyNo, applyNo ) );
        if (AfsEnumUtil.key( TortoiseApplyExecuteEnum.PROCESSING ).equals ( caseBaseInfo.getTortoiseApplyExecute ( ) )) {
            return IResponse.success ( AfsEnumUtil.key ( BusinessStateInEnum.WAIT_QUERY ) );
        }
        return IResponse.success ( caseBaseInfo.getBusinessStateIn ( ) );
    }


    private List<ApproveMakeLabel> changeLabelList(List<LoanLabelInfo> loanLabelInfoList) {
        List<ApproveMakeLabel> list = new ArrayList<>();
        for(LoanLabelInfo labelInfo : loanLabelInfoList){
            ApproveMakeLabel label = new ApproveMakeLabel();
            label.setLabelName(labelInfo.getLabelName());
            label.setLabelColor(labelInfo.getLabelColor());
            list.add(label);
        }
        return list;
    }


    @ApiOperation("任务操作人批量变更")
    @PostMapping("/taskHandlerList")
    public IResponse taskHandlerList(@RequestBody TaskHandlerChangeVO taskHandlerChangeVO) {
        try {
            if(CollectionUtils.isNotEmpty(taskHandlerChangeVO.getChangeOwnerParamList())){
                Boolean flag = approveTaskService.taskHandlerList(taskHandlerChangeVO);
                if(!flag){
                    return IResponse.fail("有挂起的数据不可改派！");
                }
            }
            return IResponse.success(true);
        }catch (Exception e){
            log.info("批量改派任务操作人失败！",e);
            return IResponse.fail("批量改派任务操作人失败！");
        }
    }

    /**
     * @Description 查询大额对公风险组所有人员
     * <AUTHOR>
     * @Date 2020/7/7 18:21
     */
    @GetMapping("/queryOrganSale")
    public IResponse queryOrganSale() {
        CaseConfParam param = caseConfParamService.getOne(
                Wrappers.<CaseConfParam>lambdaQuery()
                        .eq(CaseConfParam::getParamType, Const.ORGAN_TASK_QUERY_DEPARTMENT)
        );
        if (Objects.isNull(param)) {
            return IResponse.fail("任务改派部门参数不存在，请先维护当前参数");
        }
        log.info("获取风险部门参数{}", JSON.toJSONString(param));
        IResponse<List<SysUserDto>> response = userDetailsInfoFeign.getUsersByDepartmentId(param.getParamValue());
        log.info("获取风险部门人员{}", JSON.toJSONString(response));

        if (CommonConstants.SUCCESS.equals(response.getCode())) {
            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }
            List<SysUserDto> userDtoList = response.getData();
            log.info("获取风险部门具体人员{}", JSON.toJSONString(response));

            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(userDtoList.size());
            for (SysUserDto sysUserDto : userDtoList) {
                String status = userStatusMap.get(sysUserDto.getUsername());
                taskHandlerVOList.add(TaskHandlerVO.builder()
                        .staffName(sysUserDto.getUsername())
                        .staffRealName(sysUserDto.getUserRealName())
                        .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
            }

            List result =  Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail(response.getMsg());
        }
    }

    /**
     * @Description 查询大额对公组所有人员
     * <AUTHOR>
     * @Date 2020/7/7 18:21
     */
    @GetMapping("/findOrganByParamType")
    public IResponse findOrganByParamType(@RequestParam String paramType) {
        CaseConfParam param = caseConfParamService.getOne(
                Wrappers.<CaseConfParam>lambdaQuery()
                        .eq(CaseConfParam::getParamType, paramType)
        );
        if (Objects.isNull(param)) {
            return IResponse.fail("任务改派部门参数不存在，请先维护当前参数");
        }
        log.info("获取风险部门参数{}", JSON.toJSONString(param));
        IResponse<List<SysUserDto>> response = userDetailsInfoFeign.getUsersByDepartmentId(param.getParamValue());
        log.info("获取风险部门人员{}", JSON.toJSONString(response));

        if (CommonConstants.SUCCESS.equals(response.getCode())) {
            List<UserCollocation> userCollocationList = userCollocationService.list();
            Map<String, String> userStatusMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userCollocationList)) {
                for (UserCollocation userCollocation : userCollocationList) {
                    userStatusMap.put(userCollocation.getLoginName(), userCollocation.getPostStatus());
                }
            }
            List<SysUserDto> userDtoList = response.getData();
            log.info("获取风险部门具体人员{}", JSON.toJSONString(response));

            List<TaskHandlerVO> taskHandlerVOList = new ArrayList<>(userDtoList.size());
            for (SysUserDto sysUserDto : userDtoList) {
                String status = userStatusMap.get(sysUserDto.getUsername());
                taskHandlerVOList.add(TaskHandlerVO.builder()
                        .staffName(sysUserDto.getUsername())
                        .staffRealName(sysUserDto.getUserRealName())
                        .postStatus(Objects.isNull(status) ? PostStatus.NO.getCode() : status).build());
            }

            List result =  Stream.iterate(0, f -> f + 1).limit(taskHandlerVOList.size()).parallel()
                    .map(s -> taskHandlerVOList.parallelStream().skip(s * 10).limit(10L).collect(Collectors.toList())).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            TaskHandlerPageVO pageVO = TaskHandlerPageVO.builder()
                    .dataTotal(taskHandlerVOList.size())
                    .pageList(result)
                    .allDataList(taskHandlerVOList).build();
            return IResponse.success(pageVO);
        } else {
            return IResponse.fail(response.getMsg());
        }
    }
    /**
     * @description: 维护审批中状态
     * <AUTHOR>
     * @created 2023/11/7 20:54
     * @version 1.0
     */
    @PostMapping("/setApproveStateInCheck")
    @Transactional(rollbackFor = Exception.class)
    public IResponse setApproveStateInCheck(@ModelAttribute ApproveCondition approveCondition) {
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo, approveCondition.getApplyNo()));
        if (ObjectUtils.isNotEmpty(caseBaseInfo) &&
                (AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK).equals(caseBaseInfo.getBusinessStateIn())
                        || AfsEnumUtil.key(BusinessStateInEnum.REVISE_REPLY).equals(caseBaseInfo.getBusinessStateIn()))) {
            log.info("维护案件前的状态：{},申请编号:{}",caseBaseInfo.getBusinessStateIn(),caseBaseInfo.getApplyNo());
            //当前登录用户
            String useName = SecurityUtils.getUsername();
            WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getBusinessNo, approveCondition.getApplyNo())
                    .eq(WorkflowTaskInfo::getStatus,FlowStatusEnum.ACTIVE.getCode())
                    .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getApprovePackageId())
                    .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId()));
            if(ObjectUtil.isNotNull(workflowTaskInfo) && workflowTaskInfo.getAssign().equals(useName)){
                caseBaseInfo.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                        .eq(CaseBaseInfo::getApplyNo, approveCondition.getApplyNo())
                        .set(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(BusinessStateInEnum.IN_CHECK)));
            }else{
                log.info("订单:{}非登录用户的案件，不能维护案件状态！",approveCondition.getApplyNo());
            }
        } else {
            log.info("订单:{}无需维护审批中状态！",approveCondition.getApplyNo());
        }
        CaseApproveRecord record = null;
        List<CaseApproveRecord> list = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>query().lambda()
                .eq(CaseApproveRecord::getApplyNo, approveCondition.getApplyNo())
                .eq(CaseApproveRecord::getTaskId,approveCondition.getTaskId())
                .orderByDesc(CaseApproveRecord::getCreateTime)
        );
        if(list.size() > 0){
            record = list.get(0);
        }
        if(ObjectUtils.isNotEmpty(record) && ObjectUtils.isEmpty(record.getClickTime())){
            record.setClickTime(new Date());
            caseApproveRecordService.updateById(record);
            List<CaseApproveRecord> recordList = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>query().lambda()
                    .eq(CaseApproveRecord::getApplyNo, approveCondition.getApplyNo())
                    .eq(CaseApproveRecord::getDisposeNodeName,"初审")
                    .orderByDesc(CaseApproveRecord::getCreateTime)
            );
            if (ObjectUtil.isNotNull(recordList) && recordList.size() == 1){
                //同步修改进件订单状态
                caseUseApplyServiceFeign.updateOrderStatus(record);
            }
        }
        return IResponse.success("更新成功");
    }

    /**
     * 判断是否为拒绝(需复核)
     * @param applyNo
     * @return
     */
    @ApiOperation("判断是否为拒绝(需复核)")
    @PostMapping(value = "/getRefuseCheck/{applyNo}")
    public IResponse getRefuseCheck(@PathVariable String applyNo){
        boolean isRefuseCheck = false;
        //审查选择拒绝需复核
        WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                .isNotNull(WorkflowTaskInfo::getOperationType)
                .orderByDesc(WorkflowTaskInfo::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotNull(workflowTaskInfo) && ObjectUtil.equal(workflowTaskInfo.getOperationType(),FlowTaskOperationEnum.REFUSECHECK.getCode()) && ObjectUtil.equal(workflowTaskInfo.getUserDefinedIndex(),ApproveNodeEnum.SECOND_NODE.getCode())){
            isRefuseCheck = true;
        }else{
            //退回直达
            WorkflowTaskInfo taskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                    .eq(WorkflowTaskInfo::getOperationType,FlowTaskOperationEnum.REFUSECHECK.getCode())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime)
                    .last("limit 1"));
            if (ObjectUtils.isNotNull(taskInfo)){
                List<WorkflowTaskInfo> list = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                        .isNotNull(WorkflowTaskInfo::getOperationType)
                        .gt(WorkflowTaskInfo::getCreateTime,taskInfo.getCreateTime()));
                if (list != null && !list.isEmpty()){
                    for (WorkflowTaskInfo info : list){
                        if (!ObjectUtil.equal(info.getOperationType(),FlowTaskOperationEnum.BACK2DEALER.getCode())){
                            return IResponse.success(false);
                        }
                    }
                    isRefuseCheck = true;
                }
            }
        }
        return IResponse.success(isRefuseCheck);
    }

}
