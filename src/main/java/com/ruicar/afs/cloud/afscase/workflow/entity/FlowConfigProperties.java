package com.ruicar.afs.cloud.afscase.workflow.entity;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class FlowConfigProperties {

    @Value("${com.ruicar.afs.flow.approve.packageId:1}")
    private String approvePackageId;

    @Value("${com.ruicar.afs.flow.approve.templateId:credit-car}")
    private String approveTemplateId;

    @Value("${com.ruicar.afs.flow.loan.packageId:fangkuan}")
    private String loanPackageId;

    @Value("${com.ruicar.afs.flow.loan.templateId:loan}")
    private String loanTemplateId;

    @Value("${com.ruicar.afs.flow.loan.templateId:loan-data-post-new}")
    private String dataPostLoanTemplateId;

    @Value("${com.ruicar.afs.flow.recon.packageId:Reconsideration}")
    private String reconPackageId;

    @Value("${com.ruicar.afs.flow.recon.templateId:Reconsideration}")
    private String reconTemplateId;

    @Value("${com.ruicar.afs.flow.recon.templateId:secondary-loan}")
    private String secondaryLoanTemplateId;

    @Value("${com.ruicar.afs.flow.special.packageId:Special}")
    private String specialPackageId;

    @Value("${com.ruicar.afs.flow.special.templateId:SpecialLoan}")
    private String specialLoanTemplateId;

    @Value("${com.ruicar.afs.flow.special.templateId:SpecialApprove}")
    private String specialApproveTemplateId;

    @Value("${com.ruicar.afs.flow.affiliatedOnline.packageId:channel}")
    private String channelOutlinePackageId;

    @Value("${com.ruicar.afs.flow.affiliatedOnline.templateId:channel-outline}")
    private String channelOutlineTemplateId;

    @Value("${com.ruicar.afs.flow.approve.packageId:1}")
    private String inspectionPackageId;

    @Value("${com.ruicar.afs.flow.flow.approve.templateId:inspection}")
    private String inspectionTemplateId;

    @Value("${com.ruicar.afs.flow.flow.approve.templateId:loan-inspection}")
    private String loanInspectionTemplateId;

    @Value("${com.ruicar.afs.flow.loan.templateId:loan-file}")
    private String loanFile;

    @Value("${com.ruicar.afs.flow.loan.templateId:approval-file}")
    private String loanApprovalFile;

    /**
     * 发起核销
     */
    @Value("${com.ruicar.afs.flow.initiateWriteOff.templateId:write-off-application}")
    private String initiateWriteOffPackageId;

    /**
     * 发起核销
     */
    @Value("${com.ruicar.afs.flow.initiateWriteOff.PackageId:write-off-application}")
    private String initiateWriteOffTemplateId;

    /**
     * 发起服务费草稿审批
     */
    @Value("${com.ruicar.afs.flow.writeOffDraftApproval.templateId:write-off-application}")
    private String writeOffDraftApprovalPackageId;

    /**
     * 发起服务费草稿审批
     */
    @Value("${com.ruicar.afs.flow.writeOffDraftApproval.PackageId:write-off-draft-approval}")
    private String writeOffDraftApprovalTemplateId;

    @Value("${com.ruicar.afs.flow.salvagedApproval.PackageId:salvaged}")
    private String salvagedApprovalTemplateId;

    @Value("${com.ruicar.afs.flow.salvagedApproval.templateId:salvaged}")
    private String salvagedApprovalPackageId;
    /**
     * 放款成功资料后置
     */
    @Value("${com.ruicar.afs.flow.loanDataPost.templateId:loan-data-post-new}")
    private String loanDataPostNewTemplateId;

    /**
     * 经销商取消放款审批
     */
    @Value("${com.ruicar.afs.flow.loan.packageId:loan-cancel}")
    private String loanCancelPackageId;

    @Value("${com.ruicar.afs.flow.loan.templateId:loan-cancel}")
    private String loanCancelTemplateId;


    @Value("${com.ruicar.afs.flow.onlinemortgage.packageId:materials}")
    private String mortgagePackageId;
    @Value("${com.ruicar.afs.flow.onlinemortgage.templateId:mortgageRevoke}")
    private String mortgageTemplateId;
    @Value("${com.ruicar.afs.flow.onlinemortgage.templateId:mortgageUntieRevoke}")
    private String mortgageUntieTemplateId;
    /**
     * 挂靠公司备案
     */
    @Value("${com.ruicar.afs.flow.filing.packageId:filing}")
    private String filingPackageId;
    @Value("${com.ruicar.afs.flow.filing.templateId:company-filing}")
    private String filingTemplateId;

}
