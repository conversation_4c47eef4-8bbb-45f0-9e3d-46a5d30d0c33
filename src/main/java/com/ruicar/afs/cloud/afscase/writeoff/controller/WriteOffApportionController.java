package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.common.feign.CaseToAccountFeign;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffApportionDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffApportionInfo;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApportionDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApportionInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务费分摊
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffApportion")
public class WriteOffApportionController {
    private final WriteOffRuleService writeOffRuleService;
    private final CaseToAccountFeign caseToAccountFeign;
    private final WriteOffApportionInfoService writeOffApportionInfoService;
    private final WriteOffApportionDetailService writeOffApportionDetailService;

    @PostMapping("/queryByCondition")
    @ApiOperation(value = "查询服务费分摊汇总数据")
    public IResponse queryList(@RequestBody QueryCondition<WriteOffApportionInfo> queryCondition) {
        WriteOffApportionInfo condition = queryCondition.getCondition();
        Page<WriteOffApportionInfo> page = writeOffApportionInfoService.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffApportionInfo>lambdaQuery()
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffApportionInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .eq(StrUtil.isNotBlank(condition.getChannelBelong()), WriteOffApportionInfo::getChannelBelong, condition.getChannelBelong()));
        for (WriteOffApportionInfo apportionInfo : page.getRecords()) {
            if ("外部".equals(apportionInfo.getSource()) && apportionInfo.getApportionTotalAmount() == null) {
                continue;
            }
            if (apportionInfo.getVoucherNo() == null) {
                IResponse<String> iResponse;
                if ("外部".equals(apportionInfo.getSource())) {
                    iResponse = caseToAccountFeign.getVoucherNoByTransNo(apportionInfo.getId().toString(), "40803");
                }else {
                    iResponse = caseToAccountFeign.getVoucherNoByTransNo(apportionInfo.getId().toString(), "40801");
                }
                if ("0000".equals(iResponse.getCode())) {
                    apportionInfo.setVoucherNo(iResponse.getData());
                    writeOffApportionInfoService.updateById(apportionInfo);
                }
            }
        }
        return IResponse.success(page);
    }

    @PostMapping("/selectByApportionId")
    @ApiOperation(value = "根据apportionId查询服务费分摊明细数据")
    public IResponse queryDetailList(@RequestBody QueryCondition<WriteOffApportionDetail> queryCondition) {
        WriteOffApportionDetail condition = queryCondition.getCondition();
        Assert.isTrue(condition.getApportionId() != null, "数据错误！");
        Page<WriteOffApportionDetail> page = writeOffApportionDetailService.page(new Page<WriteOffApportionDetail>(queryCondition.getPageNumber(), queryCondition.getPageSize()),
                Wrappers.<WriteOffApportionDetail>lambdaQuery()
                        .eq(WriteOffApportionDetail::getApportionId, condition.getApportionId())
                        .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffApportionDetail::getApplyNo, condition.getApplyNo())
                        .eq(StrUtil.isNotBlank(condition.getContractNo()), WriteOffApportionDetail::getContractNo, condition.getContractNo())
                        .eq(StrUtil.isNotBlank(condition.getBaseInfoApply()), WriteOffApportionDetail::getBaseInfoApply, condition.getBaseInfoApply()));
        return IResponse.success(page);
    }

    @PostMapping("/directApportion")
    @ApiOperation(value = "直营店服务费分摊")
    public IResponse directApportion() {
        return writeOffRuleService.directWriteOffApportionJob();
    }

    @PostMapping("/spApportion")
    @ApiOperation(value = "社会店服务费分摊")
    public IResponse spApportion() {
        return writeOffRuleService.spWriteOffApportionJob(null);
    }

    @PostMapping("/apportionImport")
    @ApiOperation("分摊-外部订单导入")
    public IResponse<String> apportionImport(@RequestBody MultipartFile file) {
        return writeOffApportionInfoService.apportionImport(file);
    }

    @PostMapping("/templateExport")
    @ApiOperation("分摊-模板导出")
    public void templateExport(HttpServletResponse response) {
        writeOffApportionInfoService.templateExport(response);
    }

    @PostMapping("/outDirectApportion")
    @ApiOperation(value = "外部订单-直营店服务费分摊")
    public IResponse outDirectApportion() {
        return writeOffApportionInfoService.outDirectApportionJob();
    }

    @PostMapping("/outSpApportion")
    @ApiOperation(value = "外部订单-社会店服务费分摊")
    public IResponse outSpApportion() {
        return writeOffApportionInfoService.outSpApportionJob();
    }

    @PostMapping("/outAdvanceApportion")
    @ApiOperation(value = "外部订单-提前结清分摊")
    public IResponse outAdvanceApportion(@RequestBody List<Long> detailIdlist) {
        return writeOffApportionInfoService.outAdvanceApportion(detailIdlist);
    }

    @PostMapping("/infoExport")
    @ApiOperation("导出分摊汇总数据")
    public void exportApportionInfo(@RequestBody WriteOffApportionInfo queryCondition, HttpServletResponse httpServletResponse){
        writeOffApportionInfoService.exportApportionInfo(queryCondition, httpServletResponse);
    }

    @PostMapping("/detailExport")
    @ApiOperation("导出分摊明细数据")
    public void exportApportionDetail(@RequestBody WriteOffApportionDetail queryCondition, HttpServletResponse httpServletResponse){
        writeOffApportionInfoService.exportApportionDetail(queryCondition, httpServletResponse);
    }

    @PostMapping("/outAdvanceImport")
    @ApiOperation(value = "外部订单-提前结清导入分摊")
    public IResponse outAdvanceImport(@RequestBody MultipartFile file) {
        return writeOffApportionInfoService.outAdvanceImport(file);
    }
}
