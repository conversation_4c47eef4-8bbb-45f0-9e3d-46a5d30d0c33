package com.ruicar.afs.cloud.afscase.mq.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("affiliated_company_filing")
public class AffiliatedCompanyFiling extends BaseEntity<AffiliatedCompanyFiling> {
    @ApiModelProperty(value = "备案类型")
    private String filingType;
    @ApiModelProperty(value = "经销商名称")
    private String channelName;
    @ApiModelProperty(value = "经销商id")
    private String channelId;
    @ApiModelProperty(value = "挂靠公司名称")
    private String affiliatedName;
    @ApiModelProperty(value = "统一社会信用代码")
    private String socUniCrtCode;
    @ApiModelProperty(value = "法人姓名")
    private String legalPersonName;
    @ApiModelProperty(value = "法人身份证号")
    private String legalPersonIdentity;
    @ApiModelProperty(value = "法人手机号")
    private String legalPersonPhone;
    @ApiModelProperty(value = "备案产品组")
    private String filingProductGroup;
    @ApiModelProperty(value = "授权代表名称")
    private String authorizerName;
    @ApiModelProperty(value = "授权代表手机号")
    private String authorizerPhone;
    @ApiModelProperty(value = "授权代表身份证号")
    private String authorizerIdCard;
    @ApiModelProperty(value = "授权代表银行卡号")
    private String authorizerBankCode;
    @ApiModelProperty(value = "授权代表职务")
    private String authorizerDuty;
    @ApiModelProperty(value = "审批状态")
    private String status;

    @TableField(exist = false)
    private String busiNo;
    @TableField(exist = false)
    private String[] productGroups;
    @TableField(exist = false)
    private String operatorRealName;

}
