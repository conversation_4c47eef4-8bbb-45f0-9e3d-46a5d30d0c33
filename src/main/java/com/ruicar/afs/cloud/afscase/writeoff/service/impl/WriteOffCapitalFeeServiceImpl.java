package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalFee;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffCapitalFeeMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffContractDetailManageService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.CapitalFeeExcelVo;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.enums.common.WriteOffExtractEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@AllArgsConstructor
@Service
public class WriteOffCapitalFeeServiceImpl extends ServiceImpl<WriteOffCapitalFeeMapper, WriteOffCapitalFee> implements WriteOffCapitalFeeService {

    private final WriteOffContractDetailManageService writeOffContractDetailManageService;
    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;

    @Override
    public void exportHxxFee(WriteOffCapitalFee condition, HttpServletResponse response) {
        List<WriteOffCapitalFee> list = this.list(Wrappers.<WriteOffCapitalFee>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffCapitalFee::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffCapitalFee::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getChannelCode()), WriteOffCapitalFee::getChannelCode, condition.getChannelCode())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffCapitalFee::getWriteOffMonth, condition.getWriteOffMonth())
                .ge(condition.getWaitAmtMin() != null, WriteOffCapitalFee::getWaitConfirmAmt, condition.getWaitAmtMin())
                .le(condition.getWaitAmtMax() != null, WriteOffCapitalFee::getWaitConfirmAmt, condition.getWaitAmtMax())
                .orderByDesc(WriteOffCapitalFee::getCreateTime));
        List<CapitalFeeExcelVo> exportVoList = list.stream().map(fee -> {
            CapitalFeeExcelVo excelVo = new CapitalFeeExcelVo();
            BeanUtil.copyProperties(fee, excelVo);
            excelVo.setWriteOffType(WriteOffTypeEnum.createTypeEnum(fee.getWriteOffType()).getDesc());
            return excelVo;
        }).toList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("租金贷核销项支付确认明细项导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), CapitalFeeExcelVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, "核销项支付确认明细项").build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public IResponse createTqRels() {
        List<String> applyNoList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                        .select(WriteOffBaseInfo::getApplyNo)
                        .eq(WriteOffBaseInfo::getWriteOffType, WriteOffTypeEnum.ZJD.getCode())
                        .in(WriteOffBaseInfo::getWriteOffStatus, List.of(WriteOffStatusEnum.ADVANCE_PAY, WriteOffStatusEnum.CLOSED)))
                .stream().map(WriteOffBaseInfo::getApplyNo).toList();
        Assert.isTrue(applyNoList.size() > 0, "没有发票已审核完成的租金贷核销项");
        List<WriteOffContractDetailManage> detailManageList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .select(WriteOffContractDetailManage::getId, WriteOffContractDetailManage::getBeforeAmount, WriteOffContractDetailManage::getBaseInfoApply)
                .eq(WriteOffContractDetailManage::getServerTqFlag, "0")
                .eq(WriteOffContractDetailManage::getPayConfirmFlag, "1")
                .in(WriteOffContractDetailManage::getBaseInfoApply, applyNoList));
        Assert.isTrue(detailManageList.size() > 0, "没有满足已支付确认但未提取的合同");
        String tqStatus = AfsEnumUtil.key(WriteOffExtractEnum.NOTEXTRACTED);
        List<Long> manageIds = detailManageList.stream().map(WriteOffContractDetailManage::getId).toList();
        writeOffContractDetailManageService.update(Wrappers.<WriteOffContractDetailManage>lambdaUpdate()
                .in(WriteOffContractDetailManage::getId, manageIds)
                .set(WriteOffContractDetailManage::getServerTqFlag, "1"));
        Map<String, List<WriteOffContractDetailManage>> collect = detailManageList.stream().collect(Collectors.groupingBy(WriteOffContractDetailManage::getBaseInfoApply));
        for (String baseInfoApply : collect.keySet()) {
            List<WriteOffContractDetailManage> detailManages = collect.get(baseInfoApply);
            WriteOffBaseInvoiceRel rel = writeOffBaseInvoiceRelService.getOne(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                    .eq(WriteOffBaseInvoiceRel::getApplyNo, baseInfoApply));
            WriteOffBaseInfo baseInfo = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .select(WriteOffBaseInfo::getBeforeAmount)
                    .eq(WriteOffBaseInfo::getApplyNo, baseInfoApply));
            Assert.isTrue(rel != null && baseInfo != null, "核销项编号[" + baseInfoApply + "]数据异常");
            WriteOffCapitalFee capitalFee = this.getOne(Wrappers.<WriteOffCapitalFee>lambdaQuery().eq(WriteOffCapitalFee::getApplyNo, baseInfoApply));
            Assert.isTrue(capitalFee != null, "核销项编号[" + baseInfoApply + "]确认数据异常");
            BigDecimal totalAmt = detailManages.stream().map(WriteOffContractDetailManage::getBeforeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            long count = writeOffContractDetailManageService.count(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .eq(WriteOffContractDetailManage::getBaseInfoApply, baseInfoApply)
                    .eq(WriteOffContractDetailManage::getServerTqFlag, "0"));
            if (count <= 0) {
                //最后一批次
                capitalFee.setHisConfirmAmt(capitalFee.getHisConfirmAmt().add(capitalFee.getThisConfirmAmt()));
                capitalFee.setWaitConfirmAmt(BigDecimal.ZERO);
                capitalFee.setThisConfirmAmt(capitalFee.getReceiveInvoiceAmt().subtract(capitalFee.getTotalConfirmAmt()));
                capitalFee.setTotalConfirmAmt(capitalFee.getTotalConfirmAmt().add(capitalFee.getThisConfirmAmt()));
                writeOffBaseInvoiceRelService.removeById(rel.getId());
            } else {
                capitalFee.setHisConfirmAmt(capitalFee.getHisConfirmAmt().add(capitalFee.getThisConfirmAmt()));
                capitalFee.setThisConfirmAmt(capitalFee.getReceiveInvoiceAmt().multiply(totalAmt).divide(baseInfo.getBeforeAmount(), 2, RoundingMode.HALF_UP));
                capitalFee.setTotalConfirmAmt(capitalFee.getTotalConfirmAmt().add(capitalFee.getThisConfirmAmt()));
                capitalFee.setWaitConfirmAmt(capitalFee.getReceiveInvoiceAmt().subtract(capitalFee.getTotalConfirmAmt()));
            }
            this.updateById(capitalFee);
            //保存提取项
            WriteOffBaseInvoiceRel lastRel = writeOffBaseInvoiceRelService.getOne(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                    .select(WriteOffBaseInvoiceRel::getApplyNo)
                    .likeRight(WriteOffBaseInvoiceRel::getApplyNo, baseInfoApply)
                    .ne(WriteOffBaseInvoiceRel::getId, rel.getId())
                    .orderByDesc(WriteOffBaseInvoiceRel::getCreateTime)
                    .last("limit 1"));
            String newApplyNo = "";
            if (lastRel != null) {
                int num = Integer.parseInt(lastRel.getApplyNo().substring(lastRel.getApplyNo().lastIndexOf("_") + 1)) + 1;
                newApplyNo = baseInfoApply + "_" + num;
            } else {
                newApplyNo = baseInfoApply + "_1";
            }
            WriteOffBaseInvoiceRel newRel = new WriteOffBaseInvoiceRel();
            BeanUtil.copyProperties(rel, newRel);
            newRel.setId(null);
            newRel.setCreateBy(null);
            newRel.setCreateTime(null);
            newRel.setUpdateBy(null);
            newRel.setUpdateTime(null);
            newRel.setApplyNo(newApplyNo);
            newRel.setInvoiceAmount(capitalFee.getThisConfirmAmt());
            newRel.setStatus(tqStatus);
            writeOffBaseInvoiceRelService.save(newRel);
        }
        return IResponse.success("操作成功");
    }
}
