package com.ruicar.afs.cloud.afscase.approvetask.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.enums.ApproveNodeEnum;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseLoanCannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.LoanActivateService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseLoanCannelInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.PushDataForPos;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanDiscardDTO;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @date: 2022/12/28 10:55
 * @author: 曹国庆
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/caseBaseInfo")
public class CaseBaseInfoController {
    private final CaseBaseInfoService caseBaseInfoService;
    private final CaseContractInfoService caseContractInfoService;
    private final WorkflowTaskInfoService taskInfoService;
    private final CaseApproveRecordService approveRecordService;
    private final LoanActivateService loanActivateService;

    private final CaseBaseInfoMapper caseBaseInfoMapper;
    private final CaseLoanCannelInfoService caseLoanCannelInfoService;
    private FlowConfigProperties flowConfigProperties;
    private final FlowRunFeign flowRunFeign;
    private final PushDataForPos infoSender;
    @PostMapping("/updateStatus")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateStatus(@RequestBody List<String> applyNoList) {
        log.info("updateStatus，接收的申请编号：{}", JSON.toJSONString(applyNoList));
        if (CollectionUtil.isEmpty(applyNoList)) {
            return IResponse.fail("申请编号不能为空");
        }
        for (String applyNo : applyNoList) {
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo, applyNo));
            if (null == caseBaseInfo) {
                log.error("错误的申请编号：{}",applyNo);
                continue;
            }
            caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.INVALID));
            caseBaseInfoService.updateById(caseBaseInfo);

            LambdaQueryWrapper<WorkflowTaskInfo> query = Wrappers.lambdaQuery();
            query.eq(WorkflowTaskInfo::getBusinessNo,applyNo);
            query.eq(WorkflowTaskInfo::getTaskNodeName,AfsEnumUtil.desc(ApproveNodeEnum.END_NODE));
            query.orderByDesc(WorkflowTaskInfo::getCreateTime);
            WorkflowTaskInfo taskInfo = taskInfoService.getOne(query,false);
            if (null == taskInfo) {
                LambdaQueryWrapper<WorkflowTaskInfo> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(WorkflowTaskInfo::getBusinessNo,applyNo);
                lambdaQuery.eq(WorkflowTaskInfo::getTaskNodeName,AfsEnumUtil.desc(ApproveNodeEnum.SECOND_NODE));
                lambdaQuery.orderByDesc(WorkflowTaskInfo::getCreateTime);
                taskInfo = taskInfoService.getOne(lambdaQuery,false);
                if(ObjectUtil.isEmpty(taskInfo)){
                    LambdaQueryWrapper<WorkflowTaskInfo> lambdaQueryStart = Wrappers.lambdaQuery();
                    lambdaQueryStart.eq(WorkflowTaskInfo::getBusinessNo,applyNo);
                    lambdaQueryStart.eq(WorkflowTaskInfo::getTaskNodeName,AfsEnumUtil.desc(ApproveNodeEnum.FIRST_NODE));
                    lambdaQueryStart.orderByDesc(WorkflowTaskInfo::getCreateTime);
                    taskInfo = taskInfoService.getOne(lambdaQueryStart,false);
                }

            }
            CaseApproveRecord record = new CaseApproveRecord();
            record.setUseScene(UseSceneEnum.APPROVE.getValue());
            record.setApproveEndTime(new Date());
            record.setApproveStartTime(new Date());
            record.setDisposeStaff("系统");
            record.setApplyNo(applyNo);
            if (null != taskInfo) {
                record.setStageId(taskInfo.getProcessInstanceId());
                record.setDisposeNodeName(taskInfo.getTaskNodeName());
                record.setDisposeNode(taskInfo.getTaskNodeId());
            }
            record.setApproveSuggest("INVALID");
            record.setApproveSuggestName("自动失效");
            record.setApproveReason("自动失效");
            record.setApproveRemark("自动失效");
            approveRecordService.save(record);
            log.info("applyNo:{},保存审批日志：{}",applyNo,record);
        }
        return IResponse.success(null);
    }

    @PostMapping("/updateContractApplyStatus")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateContractApplyStatus(@RequestBody List<String> applyNoList) {
        log.info("updateContractApplyStatus，接收的申请编号：{}", JSON.toJSONString(applyNoList));
        if (CollectionUtil.isEmpty(applyNoList)) {
            return IResponse.fail("申请编号不能为空");
        }
        for (String applyNo : applyNoList) {
            CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getApplyNo, applyNo));
            if (null == contractInfo) {
                log.error("错误的申请编号：{}",applyNo);
                continue;
            }
            contractInfo.setApplyStatus(AfsEnumUtil.key(BusinessStateInEnum.INVALID));
            caseContractInfoService.updateById(contractInfo);

            LambdaQueryWrapper<WorkflowTaskInfo> query = Wrappers.lambdaQuery();
            query.eq(WorkflowTaskInfo::getBusinessNo,applyNo);
            query.eq(WorkflowTaskInfo::getTaskNodeName,"请款审批");
            query.orderByDesc(WorkflowTaskInfo::getCreateTime);
            WorkflowTaskInfo taskInfo = taskInfoService.getOne(query,false);
            CaseApproveRecord record = new CaseApproveRecord();
            record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
            record.setApproveEndTime(new Date());
            record.setApproveStartTime(new Date());
            record.setDisposeStaff("系统");
            record.setApplyNo(applyNo);
            if (null != taskInfo) {
                record.setStageId(taskInfo.getProcessInstanceId());
                record.setDisposeNodeName(taskInfo.getTaskNodeName());
                record.setDisposeNode(taskInfo.getTaskNodeId());
            }
            record.setApproveSuggest("INVALID");
            record.setApproveSuggestName("自动失效");
            record.setApproveReason("自动失效");
            record.setApproveRemark("自动失效");
            approveRecordService.save(record);
            log.info("applyNo:{},保存审批日志：{}",applyNo,record);
        }
        return IResponse.success(null);
    }

    @PostMapping("/updateApplyLoanCannelStatus")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateApplyLoanCannelStatus(@RequestBody List<String> applyNoList) {
        log.info("updateApplyLoanCannelStatus，接收的申请编号：{}", JSON.toJSONString(applyNoList));
        if (CollectionUtil.isEmpty(applyNoList)) {
            return IResponse.fail("申请编号不能为空");
        }
        for (String applyNo : applyNoList) {
            List<CaseLoanCannelInfo> list = caseLoanCannelInfoService.list(Wrappers.<CaseLoanCannelInfo>query().lambda()
                    .eq(CaseLoanCannelInfo::getApplyNo, applyNo)
                    .eq(CaseLoanCannelInfo::getIsEnd,AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.NO))
            );
            if (list.size() == 0) {
                log.error("取消放款错误的申请编号：{}",applyNo);
                continue;
            }
            CaseLoanCannelInfo caseLoanCannelInfo = list.get(0);
            caseLoanCannelInfo.setCannelStatus(CaseConstants.LOAN_CANNEL_STATUS_07);
            caseLoanCannelInfo.setExpireDate(new Date());
            caseLoanCannelInfoService.updateById(caseLoanCannelInfo);

            List<WorkflowTaskInfo> work = taskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getBusinessNo,caseLoanCannelInfo.getSubmitNo())
                    .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getLoanCancelPackageId())
                    .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getLoanCancelTemplateId())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime)
            );
            WorkflowTaskInfo taskInfo = null;
            if(work.size() > 0){
                taskInfo = work.get(0);
            }
            //取消流程
            flowRunFeign.cancelFlow(taskInfo.getProcessInstanceId());
            taskInfo.setStatus(FlowStatusEnum.END.getCode());
            taskInfoService.updateById(taskInfo);

            CaseApproveRecord record = new CaseApproveRecord();
            record.setUseScene(UseSceneEnum.LOAN_CANNEL.getValue());
            record.setApproveEndTime(new Date());
            record.setApproveStartTime(new Date());
            record.setDisposeStaff("系统");
            record.setApplyNo(applyNo);
            if (null != taskInfo) {
                record.setStageId(taskInfo.getProcessInstanceId());
                record.setDisposeNodeName(taskInfo.getTaskNodeName());
                record.setDisposeNode(taskInfo.getTaskNodeId());
            }
            record.setApproveSuggest("INVALID");
            record.setApproveSuggestName("自动失效");
            record.setApproveReason("自动失效");
            record.setApproveRemark("自动失效");
            approveRecordService.save(record);
            log.info("applyNo:{},保存审批日志：{}",applyNo,record);

            //通知经销商
            LoanDiscardDTO loanCannelReturn = new LoanDiscardDTO();
            loanCannelReturn.setSubmitNo(caseLoanCannelInfo.getSubmitNo());
            loanCannelReturn.setApplyNo(caseLoanCannelInfo.getApplyNo());
            loanCannelReturn.setContractNo(caseLoanCannelInfo.getContractNo());
            loanCannelReturn.setCannelStatus(CaseConstants.LOAN_CANNEL_STATUS_07);
            loanCannelReturn.setApproveType(ApplyStatusEnum.LOAN_CANNEL_RETURN.getState());
            loanCannelReturn.setInitType(caseLoanCannelInfo.getInitType());
            log.info("*******取消放款变更失效通知进件数据MQ相关数据******{}",loanCannelReturn);
            infoSender.LoanCannelNotic(AfsTransEntity.<LoanDiscardDTO>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_APPLY_LOAN_CANNEL_NOTICE)
                    .data(loanCannelReturn).build());
        }
        return IResponse.success(null);
    }

    @GetMapping("/queryHistoryOrderNum")
    public IResponse queryHistoryOrderNum(@RequestParam(value = "certNo") String certNo
            ,@RequestParam(value = "applyNo") String applyNo) {
        int i = caseBaseInfoMapper.queryHistoryOrderNum(applyNo, certNo);
        return IResponse.success(i);
    }

    @GetMapping("/queryHistoryPersonalOrderNum")
    public IResponse queryHistoryPersonalOrderNum(@RequestParam(value = "certNo") String certNo
            ,@RequestParam(value = "applyNo") String applyNo) {
        int i = caseBaseInfoMapper.queryHistoryPersonalOrderNum(applyNo, certNo);
        return IResponse.success(i);
    }

    @GetMapping("/queryHistoryCompanyOrderNum")
    public IResponse queryHistoryCompanyOrderNum(@RequestParam(value = "indBusinessUsci") String indBusinessUsci
            ,@RequestParam(value = "applyNo") String applyNo) {
        int i = caseBaseInfoMapper.queryHistoryCompanyOrderNum(applyNo, indBusinessUsci);
        return IResponse.success(i);
    }

    @GetMapping("/queryHistoryPersonalOrderAmt")
    public IResponse queryHistoryPersonalOrderAmt(@RequestParam(value = "certNo") String certNo
            ,@RequestParam(value = "applyNo") String applyNo) {
        BigDecimal i = caseBaseInfoMapper.queryHistoryPersonalOrderAmt(applyNo, certNo);
        return IResponse.success(i);
    }

    @GetMapping("/queryHistoryCompanyOrderAmt")
    public IResponse queryHistoryCompanyOrderAmt(@RequestParam(value = "indBusinessUsci") String indBusinessUsci
            ,@RequestParam(value = "applyNo") String applyNo) {
        BigDecimal i = caseBaseInfoMapper.queryHistoryCompanyOrderAmt(applyNo, indBusinessUsci);
        return IResponse.success(i);
    }

    @PostMapping("/updateApprovedStatus")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateApprovedStatus(@RequestBody List<String> applyNoList) {
        log.info("updateApprovedStatus，接收的申请编号：{}", JSON.toJSONString(applyNoList));
        if (CollectionUtil.isEmpty(applyNoList)) {
            return IResponse.fail("申请编号不能为空");
        }
        for (String applyNo : applyNoList) {
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo, applyNo));
            if (null == caseBaseInfo) {
                log.error("错误的申请编号：{}",applyNo);
                continue;
            }
            caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.INVALID));
            caseBaseInfo.setIsApprovedInvalid(AfsEnumUtil.key(YesOrNoEnum.YES));
            caseBaseInfoService.updateById(caseBaseInfo);

            CaseApproveRecord record = new CaseApproveRecord();
            record.setUseScene(UseSceneEnum.APPROVE.getValue());
            record.setApproveEndTime(new Date());
            record.setApproveStartTime(new Date());
            record.setDisposeStaff("系统");
            record.setApplyNo(applyNo);
            record.setApproveSuggest("INVALID");
            record.setApproveSuggestName("核准后,未提交放款自动失效");
            record.setApproveReason("核准后,未提交放款自动失效");
            record.setApproveRemark("核准后,未提交放款自动失效");
            approveRecordService.save(record);
            log.info("applyNo:{},保存审批日志：{}",applyNo,record);
        }
        return IResponse.success(null);
    }

    @ApiOperation("通过申请编号查询合同状态")
    @PostMapping("/queryContractStatus")
    public CaseContractInfo queryContractStatus(@RequestParam String applyNo){
        return caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo,applyNo));
    }


    @ApiOperation("通过申请编号更新 业务状态(内)")
    @PostMapping("/updateBusinessStateIn")
    public IResponse updateBusinessStateIn(@RequestParam String applyNo){

        log.info("applyNo:{},更新合同状态为：{}",applyNo);
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo,applyNo));
        caseContractInfo.setBusinessStateIn(BusinessStateInEnum.IN_CHECK.key());
        caseContractInfoService.updateById(caseContractInfo);
        return IResponse.success("更新成功") ;
    }
}
