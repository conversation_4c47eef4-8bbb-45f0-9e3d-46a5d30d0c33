package com.ruicar.afs.cloud.afscase.manuallabel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.manuallabel.condition.ManualLabelCondition;
import com.ruicar.afs.cloud.afscase.manuallabel.entity.ManualLabel;
import com.ruicar.afs.cloud.afscase.manuallabel.service.ManualLabelService;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPositionEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 手动/自动标签库管理功能
 * @created 2020/5/19 11:38
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("手动标签库管理功能接口")
@RequestMapping("/manualLabel")

public class ManualLabelController {

    private final ManualLabelService manualLabelService;
    private final AfsRuleFeign afsRuleInfoService;

    @PostMapping(value = "/queryLabelList")
    @ApiOperation(value = "多条件分页获取信审标签库信息数据")
    public IResponse<IPage<ManualLabel>> queryLabelList(@RequestBody QueryCondition<ManualLabelCondition> manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumber(), manualLabel.getPageSize()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelName()), ManualLabel::getLabelName, manualLabel.getCondition().getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelPhase()), ManualLabel::getLabelPhase, manualLabel.getCondition().getLabelPhase())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelType()), ManualLabel::getLabelType, manualLabel.getCondition().getLabelType())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelLocation()), ManualLabel::getLabelLocation, manualLabel.getCondition().getLabelLocation())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelClassification()), ManualLabel::getLabelClassification, manualLabel.getCondition().getLabelClassification())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getStatus()), ManualLabel::getStatus, manualLabel.getCondition().getStatus())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabel()), ManualLabel::getLabel, manualLabel.getCondition().getLabel())
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @PostMapping(value = "/queryLoanLabelList")
    @ApiOperation(value = "多条件分页获取放款标签库信息数据")
    public IResponse<IPage<ManualLabel>> queryLoanLabelList(@RequestBody QueryCondition<ManualLabelCondition> manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumber(), manualLabel.getPageSize()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelName()), ManualLabel::getLabelName, manualLabel.getCondition().getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelPhase()), ManualLabel::getLabelPhase, manualLabel.getCondition().getLabelPhase())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelType()), ManualLabel::getLabelType, manualLabel.getCondition().getLabelType())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelLocation()), ManualLabel::getLabelLocation, manualLabel.getCondition().getLabelLocation())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabelClassification()), ManualLabel::getLabelClassification, manualLabel.getCondition().getLabelClassification())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getStatus()), ManualLabel::getStatus, manualLabel.getCondition().getStatus())
                .eq(StringUtils.isNotEmpty(manualLabel.getCondition().getLabel()), ManualLabel::getLabel, manualLabel.getCondition().getLabel())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @PostMapping(value = "/queryAllLabelList")
    @ApiOperation(value = "查詢所有信息数据")
    public IResponse<IPage<ManualLabel>> queryAllLabelList(@RequestBody ManualLabelCondition manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumberTag(), manualLabel.getPageSizeTag()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getLabelName()), ManualLabel::getLabelName, manualLabel.getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getStatus()), ManualLabel::getStatus, manualLabel.getStatus())
                .eq(ManualLabel::getLabelLocation, LabelPositionEnum.LIST.getCode())
                .eq(ManualLabel::getLabelPhase,LabelPhaseEnum.CREDITADUIT.getCode())
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @PostMapping(value = "/queryAllLabelListForManual")
    @ApiOperation(value = "查詢所有手动信息数据")
    public IResponse<IPage<ManualLabel>> queryAllLabelListForManual(@RequestBody ManualLabelCondition manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumberTag(), manualLabel.getPageSizeTag()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getLabelName()), ManualLabel::getLabelName, manualLabel.getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getLabel()), ManualLabel::getLabel, manualLabel.getLabel())
                .eq(StringUtils.isNotEmpty(manualLabel.getStatus()), ManualLabel::getStatus, manualLabel.getStatus())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.CREDITADUIT.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @RequestMapping(value = "/deleteLabel/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "通过id删除")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("删除标签库")
    public IResponse<Boolean> deleteLabel(@PathVariable String id) {
        ManualLabel manualLabel = manualLabelService.getById(id);
        if (manualLabel == null) {
            return new IResponse<Boolean>().setMsg("通过id删除数据成功");
        }
        manualLabelService.removeById(id);
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }

    @PostMapping(value = "/addLabel")
    @ApiOperation(value = "新增标签库")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("新增标签库")
    public IResponse<Boolean> addLabel(@RequestBody ManualLabel manualLabel) {
        String labelNo = manualLabel.getLabelNo();
        String labelName = manualLabel.getLabelName();
        List<ManualLabel> list1 = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelName, labelName)
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode()));
        List<ManualLabel> list = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelNo, labelNo)
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode()));
        if (list != null && list.size() > 0) {
            return new IResponse<Boolean>().setMsg("标签编号:" + labelNo + "重复,请重新提交！").setCode("0001");
        } else if (list1 != null && list1.size() > 0) {
            return new IResponse<Boolean>().setMsg("标签名称:" + labelName + "重复,请重新提交！").setCode("0002");
        } else {
            manualLabelService.save(manualLabel);
            return new IResponse<Boolean>().setMsg("新增标签库成功");
        }
    }

    @PostMapping(value = "/editLabel")
    @ApiOperation(value = "修改标签库")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("修改标签库")
    public IResponse<Boolean> editLabel(@RequestBody ManualLabel manualLabel) {
        String labelNo = manualLabel.getLabelNo();
        List<ManualLabel> list = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelNo, labelNo)
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode()));

        String labelName = manualLabel.getLabelName();
        List<ManualLabel> list1 = manualLabelService.list(Wrappers.<ManualLabel>lambdaQuery()
                .eq(ManualLabel::getLabelName, labelName)
                .ne(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode()));

        if ((list != null && list.size() > 0) || (list1 != null && list1.size() > 0)) {
            if (list != null && list.size() > 0) {
                if (list.get(0).getId().equals(manualLabel.getId())) {
                    if (list1 != null && list1.size() > 0) {
                        if (list1.get(0).getId().equals(manualLabel.getId())) {
                            manualLabelService.updateById(manualLabel);
                            return new IResponse<Boolean>().setMsg("新增标签库成功");
                        } else {
                        return new IResponse<Boolean>().setMsg("标签名称:" + labelName + "重复,请重新提交！").setCode("0002");
                        }
                    } else {
                    manualLabelService.updateById(manualLabel);
                    return new IResponse<Boolean>().setMsg("新增标签库成功");
                    }
                } else {
                    return new IResponse<Boolean>().setMsg("标签编号:" + labelNo + "重复,请重新提交！").setCode("0001");
                }
            } else {
                if (list1.get(0).getId().equals(manualLabel.getId())) {
                    manualLabelService.updateById(manualLabel);
                    return new IResponse<Boolean>().setMsg("新增标签库成功");
                } else {
                    return new IResponse<Boolean>().setMsg("标签名称:" + labelName + "重复,请重新提交！").setCode("0002");
                }
            }
        } else {
            manualLabelService.updateById(manualLabel);
            return new IResponse<Boolean>().setMsg("新增标签库成功");
        }
    }

    @PostMapping(value = "/activateOrExpiredTheRules/{flag}")
    @ApiOperation(value = "激活/失效")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("激活/失效规则")
    public IResponse<Boolean> activateOrExpiredTheRules(@RequestBody ManualLabel manualLabel, @PathVariable String flag) {
        if (StringUtils.isNotEmpty(flag) && !"null".equals(String.valueOf(manualLabel.getRuleId()))) {
            if ("activate".equals(flag)) {//激活规则
                afsRuleInfoService.activeRuleByRuleId(manualLabel.getRuleId());
            } else {                      //失效规则
                afsRuleInfoService.deActiveRule(manualLabel.getRuleId());
                afsRuleInfoService.expiredRule(manualLabel.getRuleId());
            }
        }
        manualLabelService.updateById(manualLabel);
        return new IResponse<Boolean>().setMsg("操作成功");

    }

    @PostMapping(value = "/queryInsLabelListForManual")
    @ApiOperation(value = "查詢所有质检手动标签信息数据")
    public IResponse<IPage<ManualLabel>> queryInsLabelListForManual(@RequestBody ManualLabelCondition manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumberTag(), manualLabel.getPageSizeTag()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getLabelName()), ManualLabel::getLabelName, manualLabel.getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getLabel()), ManualLabel::getLabel, manualLabel.getLabel())
                .eq(StringUtils.isNotEmpty(manualLabel.getStatus()), ManualLabel::getStatus, manualLabel.getStatus())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.QUALITYCONTROL.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @PostMapping(value = "/queryAllLoanLabelListForManual")
    @ApiOperation(value = "查詢所有手动放款信息数据")
    public IResponse<IPage<ManualLabel>> queryAllLoanLabelListForManual(@RequestBody ManualLabelCondition manualLabel) {
        return IResponse.success(manualLabelService.page(new Page(manualLabel.getPageNumberTag(), manualLabel.getPageSizeTag()), Wrappers.<ManualLabel>query().lambda()
                .like(StringUtils.isNotEmpty(manualLabel.getLabelName()), ManualLabel::getLabelName, manualLabel.getLabelName())
                .eq(StringUtils.isNotEmpty(manualLabel.getLabel()), ManualLabel::getLabel, manualLabel.getLabel())
                .eq(StringUtils.isNotEmpty(manualLabel.getStatus()), ManualLabel::getStatus, manualLabel.getStatus())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }
}
