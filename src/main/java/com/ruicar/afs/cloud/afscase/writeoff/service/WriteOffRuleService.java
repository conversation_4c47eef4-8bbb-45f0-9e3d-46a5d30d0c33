package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffRule;
import com.ruicar.afs.cloud.afscase.writeoff.vo.AdvanceResultDTO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.SettleContractDetailVO;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * The interface Write off rule service.
 */
public interface WriteOffRuleService extends IService<WriteOffRule> {

    /**
     * 服务费计算定时任务
     *
     * @param param        核销期数
     * @param zjdFlag      是否租金贷
     * @param zjdStartTime 租金贷周期开始时间
     * @param zjdEndTime   租金贷周期结束日期
     * @param ruleGroup    计算周期规则组
     * @return the response
     */
    IResponse writeOffCalRuleJob(String param, boolean zjdFlag, Date zjdStartTime, Date zjdEndTime, Long ruleGroup);

    /**
     * 直营店服务费分摊定时任务
     *
     * @return the response
     */
    IResponse directWriteOffApportionJob();

    /**
     * 提前结清扣减计算
     *
     * @param currentDetail currentDetail
     * @param settleData settleData
     * @param billingPeriod billingPeriod
     * @return IResponse
     */
    IResponse writeOffAdvanceRuleCal(List<WriteOffContractDetailManage> currentDetail,
        List<SettleContractDetailVO> settleData, String billingPeriod);

    /**
     * 社会店服务费分摊定时任务
     *
     * @param param
     * @return the response
     */
    IResponse spWriteOffApportionJob(String param);

    /**
     * 事务性保存
     *
     * @param value           value
     * @param generateApply   generateApply
     * @param serviceCharge   serviceCharge
     * @param key             key
     * @param billingPeriod   billingPeriod
     * @param seasonFlag      seasonFlag
     * @param settlement      提前结清计算数据
     * @param channelFullName 经销商名称
     * @param channelBelong   渠道归属
     * @param writeOffType    业务模式
     * @param notReceivedValue 未对账的合同
     */
    void transactionalSave(List<WriteOffContractDetailManage> value, String generateApply, BigDecimal serviceCharge,
                           String key, String billingPeriod, String seasonFlag, AdvanceResultDTO settlement, String channelFullName,
                           String channelBelong, WriteOffTypeEnum writeOffType, List<WriteOffContractDetailManage> notReceivedValue);


    /**
     * 服务费计算规则导出
     * @param response
     * @return
     */
    void writeOffRuleExport(HttpServletResponse response);

    /**
     * 服务费数据修复临时定时任务
     * @param param
     */
    void dataRepairTempJob(String param);
}


