package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRuleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffPayRuleDetailMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@AllArgsConstructor
@Service
public class WriteOffPayRuleDetailServiceImpl extends ServiceImpl<WriteOffPayRuleDetailMapper, WriteOffPayRuleDetail> implements WriteOffPayRuleDetailService {

}
