package com.ruicar.afs.cloud.afscase.common.utils;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionStrategyFirst;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionStrategySecond;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class DecisionStrategySorter {

	public static void categorizeAndSort(List<DecisionStrategyFirst> originList, Map<String, CodeCategoryOrder> codeCategoryOrderMap) {
		// 1. 对每个 dealTypeName 分组内的 DecisionStrategySecond 进行分类排序
		originList.forEach(strategy1 -> {
			if (strategy1.getDecisionStrategy2() != null) {
				// 按category分组
				Map<String, List<DecisionStrategySecond>> categoryMap = strategy1.getDecisionStrategy2().stream()
						.collect(Collectors.groupingBy(
								ds2 -> Optional.ofNullable(codeCategoryOrderMap.get(ds2.getRuleCode()))
										.map(CodeCategoryOrder::getCategory)
										.orElse("未分类")
						));

				// 对每个category组内按order排序
				categoryMap.values().forEach(list ->
						list.sort(Comparator.comparingInt(
										ds2 -> Optional.ofNullable(codeCategoryOrderMap.get(ds2.getRuleCode()))
												.map(CodeCategoryOrder::getSortOrder)
												.orElse(99)
								)
						));

				// 按category的order排序所有组
				List<DecisionStrategySecond> sortedList = categoryMap.entrySet().stream()
						.sorted(Comparator.comparingInt(
								entry -> Optional.ofNullable(codeCategoryOrderMap.get(entry.getValue().get(0).getRuleCode()))
										.map(CodeCategoryOrder::getSortOrder)
										.orElse(99)
						))
						.flatMap(entry -> entry.getValue().stream())
						.collect(Collectors.toList());

				// 替换原列表
				strategy1.setDecisionStrategy2(sortedList);
			}
		});

		// 2. 对DecisionStrategyFirst按第一个元素的category排序
		originList.sort((a, b) -> {
			if (a.getDecisionStrategy2() == null || a.getDecisionStrategy2().isEmpty()) {
				return 1;
			};
			if (b.getDecisionStrategy2() == null || b.getDecisionStrategy2().isEmpty()) {
				return -1;
			};

			int orderA = Optional.ofNullable(codeCategoryOrderMap.get(a.getDecisionStrategy2().get(0).getRuleCode()))
					.map(CodeCategoryOrder::getSortOrder)
					.orElse(99);

			int orderB = Optional.ofNullable(codeCategoryOrderMap.get(b.getDecisionStrategy2().get(0).getRuleCode()))
					.map(CodeCategoryOrder::getSortOrder)
					.orElse(99);

			return Integer.compare(orderA, orderB);
		});
	}
}