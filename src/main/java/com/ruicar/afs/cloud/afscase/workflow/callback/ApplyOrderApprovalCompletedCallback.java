package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.enums.ApproveSuggestEnum;
import com.ruicar.afs.cloud.afscase.workflow.feign.ApplySubmitFeign;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 功能说明:进件审批结束，通知进件触发资方审批
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Component
public class ApplyOrderApprovalCompletedCallback implements CommonAdapter {

    private CaseApproveRecordService caseApproveRecordService;
    private ApplySubmitFeign applySubmitFeign;
    private CaseBaseInfoService caseBaseInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId, String extParam, Map<String, String> flowVariables) {
        try {
            String applyNo = flowVariables.get(FlowConstant.BUSINESS_NO);
            //记录审批操作日志
            CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
            caseApproveRecord.setApplyNo(applyNo);
            caseApproveRecord.setApproveSuggest(ApproveSuggestEnum.CAPITAL_APPROVE.getCode());
            caseApproveRecord.setDisposeNodeName(ApproveSuggestEnum.CAPITAL_APPROVE.getDesc());
            caseApproveRecordService.save(caseApproveRecord);
            //设置流程业务状态为审批中
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));
            caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.BANK_SIGNING_IN_PROGRESS));
            caseBaseInfoService.updateById(caseBaseInfo);
            //将所属资方推送到进件
            JSONObject params = new JSONObject();
            params.put("applyNo",applyNo);
            params.put("belongingCapital",flowVariables.get(FlowConstant.BELONGING_CAPITAL));
            //设置订单状态为未签约
            params.put("applyStatus","20");
            IResponse iResponse = applySubmitFeign.applyEndNotice(params);
            if (!CommonConstants.SUCCESS.equals(iResponse.getCode())) {
                throw new AfsBaseException(iResponse.getMsg());
            }
        } catch (Exception e) {
            throw e;
        }
        return flowVariables;
    }
}
