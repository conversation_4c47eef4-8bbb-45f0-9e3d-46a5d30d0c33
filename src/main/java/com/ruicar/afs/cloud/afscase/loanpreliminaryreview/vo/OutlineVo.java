package com.ruicar.afs.cloud.afscase.loanpreliminaryreview.vo;

import com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.entity.LoanSpecialBusinessInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OutlineVo {
	/**
	 * 申请编号
	 */
	private String applyNo;
	/**
	 * 客户名称
	 */
	private String custName;
	/**
	 * 车辆类型
	 */
	private String carType;
	/**
	 *
	 * 融资期限
	 */
	private Integer loanTerm;
	/**
	 * 产品方案名称
	 */
	private String productName;
	/**
	 * 融资额
	 */
	private BigDecimal loanAmt;
	/**
	 * 融资额
	 */
	private BigDecimal downPayScale;
	/**
	 * 每期客户租金
	 */
	private BigDecimal totalRent;
	/**
	 * 授权区域
	 */
	private String title;
	/**
	 * 提报店名称
	 */
	private String channelFullName;

	/**
	 * 提报店评级
	 */
	private String channelGrade;

	/**渠道归属*/
	private String channelBelong;

	/**合同号码*/
	private String contractNo;

	/**
	 * 是否经销商担保
	 */
	private String ifDealerGuarantee;

	/**
	 * 是否先放后抵
	 */
	private String lendingMode;

	/**
	 * 联合方名称
	 */
	private String uniteName;
	/**
	 *是否联合租赁
	 */
	private String ifJointLease;
	/**
	 * 签约方式
	 */
	private String signType;
	/**
	 * 起租备注
	 */
	private String rentRemark;

	/**
	 * 进件备注
	 */
	private String remarks;

	/**
	 * 放款备注
	 */
	private String loanRemarks;

	/**
	 * 是否锁定
	 */
	private String isLock;

	/**
	 * 是否自动通过
	 */
	private String autoPass;
	/**
	 * 放款历史备注
	 */
	private List<String> loanRemarkList;
	/**
	 * 信审历史备注
	 */
	private List<String> remarkList;
	/**
	 * 特殊业务
	 */
	private List<LoanSpecialBusinessInfo> specialBusinessInfoList;
	/**
	 * 首次提交日期
	 */
	private Date passFirstDate;

	/**
	 * 合同最后生成时间
	 */
	private Date lastGenerationDate;

	@ApiModelProperty(value = "主体名称")
	private String subjectName ;
	/**
	 * 上牌地
	 */
	private String plateAddress;

	/**
	 * 抵押性质
	 * 0未抵押 1已抵押
	 */
	private String mortgageStatus;
	/**
	 * 客户经理
	 */
	private String manageRealName;
	/**
	 * 客户经理手机号
	 */
	private String managePhone;
	/**
	 * 资质等级
	 */
	private String qualityLevel;
	private String saleAdvisor;
	private String saleAdvisorPhone;
	/**
	 * 经销商省市
	 */
	private String channelAddress;

	/**
	 * 是否资料后置,0-关闭，1-开启
	 */
	private String isDataPostStatus;

	/**
	 * 是否TOP20经销商，1-是，0-否
	 */
	private String IsTopChannel;
	/**
	 * 是否TOP20经销商，是，否
	 */
	private String IsTopChannelName;

	/**
	 * 抵押要求，0-免抵，1-抵押
	 */
	private String mortgageClaim;

	/**
	 * 抵押要求，免抵，抵押
	 */
	private String mortgageClaimName;

	/**
	 * 是否赋强公证，0-否，1-是
	 */
	private String enforceableNotarizationStatus;

}
