package com.ruicar.afs.cloud.afscase.channel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.channel.condition.AffiliationStatusCondition;
import com.ruicar.afs.cloud.afscase.channel.condition.BaseInfoTypeDTO;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.dealeroutline.condition.ChannelOntlineCondition;
import com.ruicar.afs.cloud.afscase.dealeroutline.vo.ChannelBaseInfoVo;

import java.util.List;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-6-9 14:15
 */
public interface ChannelBaseInfoService extends IService<ChannelBaseInfo> {
    /**
     * Gets base info.
     *
     * @param affiliationStatusCondition the affiliation status condition
     * @return the base info
     */
    List<ChannelBaseInfo> getBaseInfo(AffiliationStatusCondition affiliationStatusCondition);

    /**
     * Gets base info page.
     *
     * @param page                       the page
     * @param affiliationStatusCondition the affiliation status condition
     * @return the base info page
     */
    IPage<List<ChannelBaseInfo>> getBaseInfoPage(Page page, AffiliationStatusCondition affiliationStatusCondition);

    /**
     * Gets base info by type.
     *
     * @param dto the dto
     * @return the base info by type
     */
    List<ChannelBaseInfo> getBaseInfoByType(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status.
     * 根据状态和channlId  去判断查询的条件
     *
     * @param dto the dto
     * @return the base info by status
     */
    List<ChannelBaseInfo> getBaseInfoByStatus(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status other all.
     * 根据状态和channlId  去判断查询的条件  非直营的新车二手车都做，直营的只做一种类型
     * @param dto the dto
     * @return the base info by status other all
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOtherAll(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status own all.
     * 根据状态和channlId  去判断查询的条件  直营的新车二手车都做，非直营的只做一种类型
     * @param dto the dto
     * @return the base info by status own all
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOwnAll(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status only own all.
     * 根据状态和channlId  去判断查询的条件  直营的新车二手车都做，非直营不行
     * @param dto the dto
     * @return the base info by status only own all
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOnlyOwnAll(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status only own type.
     * 根据状态和channlId  去判断查询的条件  直营只做一种类型，非直营不行
     *
     * @param dto the dto
     * @return the base info by status only own type
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOnlyOwnType(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status only other all.
     * 根据状态和channlId  去判断查询的条件  非直营的新车二手车都做，直营不行
     *
     * @param dto the dto
     * @return the base info by status only other all
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOnlyOtherAll(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status only other type.
     * 根据状态和channlId  去判断查询的条件  非直营只做一种类型，直营不行
     * @param dto the dto
     * @return the base info by status only other type
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOnlyOtherType(BaseInfoTypeDTO dto);

    /**
     * Gets base info by status other only own only.
     * 根据状态和channlId  去判断查询的条件  非直营是单一类型  直营是单一类型
     *
     * @param dto the dto
     * @return the base info by status other only own only
     */
    List<ChannelBaseInfo> getBaseInfoByStatusOtherOnlyOwnOnly(BaseInfoTypeDTO dto);

    /**
     * Gets channel base info vo.
     *
     * @param condition the condition
     * @return the channel base info vo
     */
    IPage<ChannelBaseInfoVo> getChannelBaseInfoVo(ChannelOntlineCondition condition);

    /**
     * Query approve basic channel base info.
     * 根据ID查询渠道基本信息
     *
     * @param id the id
     * @return the channel base info
     */
    ChannelBaseInfo queryApproveBasic(Long id);

    /**
     * 根据客户经理name获取渠道code列表
     *
     * @param username the username
     * @return channel code by manager
     */
    List<String> getChannelCodeByManager(String username);

    /**
     * 根据经销商code获取渠道信息.
     *
     * @param dealerId code.
     * @return channel by dealer id
     */
    ChannelBaseInfo getChannelByDealerId(String dealerId);

    /**
     * Query channel by channel id channel base info.
     *
     * @param channelId the channel id
     * @return the channel base info
     */
    ChannelBaseInfo queryChannelByChannelId(Long channelId);

    /**
     * 获取经销商id
     * @param channelCode 经销商代码
     * @return
     */
    String getChannelIdByChannelCode(String channelCode);
    /**
     * 根据渠道id获取渠道风险信息
     * @param channelId 渠道id
     * @param businessType 业务类型
     * @return channel risk info
     */
    ChannelRiskInfo getChannelRiskInfo(Long channelId, String businessType);
}
