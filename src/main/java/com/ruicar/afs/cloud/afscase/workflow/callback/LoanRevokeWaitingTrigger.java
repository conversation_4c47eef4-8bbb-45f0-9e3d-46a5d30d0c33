package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @date 2023/10/16 17:53
 * @description
 */
@AllArgsConstructor
@Component
@Slf4j
public class LoanRevokeWaitingTrigger implements CommonAdapter {
    private final CaseContractInfoService caseContractInfoService;

    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId, String extParam, Map<String, String> flowVariables) {
            String contractNo = flowVariables.get("contract_no");
            CaseContractInfo contractInfo = caseContractInfoService.getContractByContractNo(contractNo);
            contractInfo.setApplyStatus(ApplyStatusEnum.REVOCABLE_WAITING.getState());
            caseContractInfoService.updateById(contractInfo);
        return flowVariables;
    }
}
