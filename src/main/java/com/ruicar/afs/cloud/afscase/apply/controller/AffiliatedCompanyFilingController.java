package com.ruicar.afs.cloud.afscase.apply.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.apply.entity.FilingProductGroup;
import com.ruicar.afs.cloud.afscase.apply.service.FilingProductGroupService;
import com.ruicar.afs.cloud.afscase.apply.vo.AffiliatedFilingSubmitVo;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.mq.condition.AffiliatedCompanyFilingCondition;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.service.AffiliatedCompanyFilingService;
import com.ruicar.afs.cloud.afscase.mq.vo.AffiliatedCompanyFilingVo;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkTaskApproveRecord;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.event.AffiliatedFilingEvent;
import com.ruicar.afs.cloud.afscase.workflow.feign.ProducePlanFeign;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkTaskApproveRecordService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/filing")
public class AffiliatedCompanyFilingController {

    private AffiliatedCompanyFilingService affiliatedCompanyFilingService;
    private WorkTaskApproveRecordService workTaskApproveRecordService;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private UserDetailsInfoFeign userDetailsInfoFeign;
    private WorkflowHelper workflowHelper;
    private ApplicationEventPublisher eventPublisher;
    private FilingProductGroupService filingProductGroupService;
    private ProducePlanFeign producePlanFeign;

    @PostMapping(value = "/queryApproveTaskLaunchList")
    @ApiOperation(value = "获取备案任务信息列表")
    public IResponse<IPage<AffiliatedCompanyFilingVo>> queryApproveTaskLaunchList(@RequestBody QueryCondition<AffiliatedCompanyFilingCondition> condition){
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        //获取当前用户登录名
        AffiliatedCompanyFilingCondition companyFilingCondition = condition.getCondition();
        companyFilingCondition.setUsername(SecurityUtils.getUsername());
        //查询当前登录人的待办任务
        IPage<List<AffiliatedCompanyFilingVo>> list = affiliatedCompanyFilingService.queryApproveTaskLaunchList(page, companyFilingCondition);
        return IResponse.success(list);
    }

    @GetMapping("/listAllRecord")
    @ApiOperation(value = "查询置顶申请编号所有操作记录")
    public IResponse listAllRecord(@RequestParam String stageId) {
        List<WorkTaskApproveRecord> list = workTaskApproveRecordService.list(Wrappers.<WorkTaskApproveRecord>lambdaQuery()
                .eq(WorkTaskApproveRecord::getBusinessId, stageId)
                .orderByAsc(WorkTaskApproveRecord::getCreateTime));
        List<WorkflowTaskInfo> listTask = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                .eq(WorkflowTaskInfo::getBusinessNo, stageId).eq(WorkflowTaskInfo::getStatus,"active")
                .orderByDesc(WorkflowTaskInfo::getCreateTime));

        if(listTask.size() > 0){
            WorkflowTaskInfo workflowTaskInfo = listTask.get(0);
            WorkTaskApproveRecord record =new  WorkTaskApproveRecord();
            record.setTaskNodeName(workflowTaskInfo.getTaskNodeName());
            IResponse info = userDetailsInfoFeign.info(workflowTaskInfo.getAssign());
            Object data = info.getData();
            JSONObject jsonObject = (JSONObject) JSON.toJSON(data);
            Object sysUser = jsonObject.get("sysUser");
            JSONObject object = (JSONObject) JSON.toJSON(sysUser);
            String userRealName = object.getString("userRealName");
            record.setApprovalUser(userRealName);
            list.add(record);
        }
        return IResponse.success(list);
    }

    @GetMapping(value = "/queryFilingInfo")
    @ApiOperation(value = "查询挂靠公司详情")
    public IResponse queryFilingInfo(@RequestParam String id) {
        return IResponse.success(affiliatedCompanyFilingService.getById(Long.valueOf(id)));
    }

    @GetMapping(value = "/queryMessage")
    @ApiOperation(value = "查询退回留言")
    public String queryMessage(@RequestParam String id){
        String message = null;
        WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                .eq(WorkflowTaskInfo::getBusinessNo, id)
                .orderByDesc(WorkflowTaskInfo::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(workflowTaskInfo)){
            message = workflowTaskInfo.getRemark();
        }
        return message;
    }

    @PostMapping(value = "/submitFiling")
    @ApiOperation(value = "挂靠公司备案审批提交")
    public IResponse submitFiling(@RequestBody AffiliatedFilingSubmitVo affiliatedFilingSubmitVo){
        log.info("提交审批入参:{}",JSONObject.toJSON(affiliatedFilingSubmitVo));
        //当前登陆用户
        String useName = SecurityUtils.getUsername();
        WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(affiliatedFilingSubmitVo.getStageId()),WorkflowTaskInfo::getBusinessNo, affiliatedFilingSubmitVo.getStageId())
                .eq(WorkflowTaskInfo::getTaskId, affiliatedFilingSubmitVo.getTaskId())
                .eq(WorkflowTaskInfo::getProcessInstanceId, affiliatedFilingSubmitVo.getProcessInstanceId()));
        AffiliatedCompanyFiling affiliatedCompanyFiling = affiliatedCompanyFilingService.getById(affiliatedFilingSubmitVo.getStageId());
        if (StrUtil.equals(FlowStatusEnum.END.getDesc(),workflowTaskInfo.getStatus())){
            throw new AfsBaseException("该流程已提交处理，请勿重复处理!");
        }
        if (ObjectUtil.isNotEmpty(workflowTaskInfo)){
            if (StrUtil.equals(useName,workflowTaskInfo.getAssign())){
                SubmitTaskParam submitTaskParam = new SubmitTaskParam();
                submitTaskParam.setTaskId(workflowTaskInfo.getTaskId());
                submitTaskParam.setNodeId(workflowTaskInfo.getTaskNodeId());
                submitTaskParam.setApproveSuggest(affiliatedFilingSubmitVo.getApproveSuggest());
                submitTaskParam.setRemark(affiliatedFilingSubmitVo.getApprovalRemarks());
                submitTaskParam.setOperationType(affiliatedFilingSubmitVo.getApproveSuggest());
                submitTaskParam.setApproveSuggestName(affiliatedFilingSubmitVo.getApprovalRemarks());
                final IResponse<Boolean> response = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.AFFILIATED_COMPANY_FILING_NEW);
                log.info("workflowHelper.submitTask:",response);

                doAfterWorkflowOpt(affiliatedFilingSubmitVo, workflowTaskInfo, affiliatedCompanyFiling, submitTaskParam, response);
                return response;
            }else {
                throw new AfsBaseException("该流程已不在当前队列，无法操作!");
            }
        }
        return IResponse.success("");
    }

    private void doAfterWorkflowOpt(AffiliatedFilingSubmitVo affiliatedFilingSubmitVo, WorkflowTaskInfo workflowTaskInfo, AffiliatedCompanyFiling affiliatedCompanyFiling, SubmitTaskParam submitTaskParam, IResponse<Boolean> response) {
        if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
            // 操作工作流成功后 修改本地状态 或者 通知前置业务系统
            try {
                switch (FlowTaskOperationEnum.valueOf(affiliatedFilingSubmitVo.getApproveSuggest().toUpperCase())) {
                    case BACK2DEALER:
                        // 通知
                        log.info("通知退回经销商start");
                        eventPublisher.publishEvent(new AffiliatedFilingEvent(this,
                                workflowTaskInfo.getBusinessNo(),
                                workflowTaskInfo.getProcessInstanceId(),
                                NormalSubmitType.SEND_BACK_TO_DEALER,
                                SecurityUtils.getUsername(),
                                affiliatedFilingSubmitVo.getApproveSuggest(),
                                submitTaskParam.getRemark()));
                        log.info("通知退回经销商end");
                        break;
                    case REFUSE:
                        eventPublisher.publishEvent(new AffiliatedFilingEvent(this,
                                workflowTaskInfo.getBusinessNo(),
                                workflowTaskInfo.getProcessInstanceId(),
                                NormalSubmitType.SUGGEST_REJECT_FINAL,
                                SecurityUtils.getUsername(),
                                affiliatedFilingSubmitVo.getApproveSuggest(),
                                submitTaskParam.getRemark()));
                    case SUBMIT:
                        WorkflowTaskInfo info = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                                .eq(WorkflowTaskInfo::getBusinessNo,workflowTaskInfo.getBusinessNo())
                                .eq(WorkflowTaskInfo::getProcessInstanceId, affiliatedFilingSubmitVo.getProcessInstanceId())
                                .eq(WorkflowTaskInfo::getOperationType,FlowTaskOperationEnum.SUBMIT.getCode())
                                .eq(WorkflowTaskInfo::getUserDefinedIndex,"FOURTH_NODE")
                                .last("limit 1"));
                        if (ObjectUtil.isNotEmpty(info)){
                            eventPublisher.publishEvent(new AffiliatedFilingEvent(this,
                                    workflowTaskInfo.getBusinessNo(),
                                    workflowTaskInfo.getProcessInstanceId(),
                                    NormalSubmitType.SUGGEST_CHECK_FINAL,
                                    SecurityUtils.getUsername(),
                                    affiliatedFilingSubmitVo.getApproveSuggest(),
                                    submitTaskParam.getRemark()));
                        }
                        break;
                    default:
                        break;
                }
            }catch (Throwable throwable){
                log.error("通知业务系统审批进度失败.", throwable);
            }
        }
    }

    @PostMapping("/queryFilingProductGroupByPage")
    @ApiOperation(value = "分页查询备案产品组")
    public IResponse<IPage<FilingProductGroup>> queryFilingProductGroupByPage(@RequestBody QueryCondition<FilingProductGroup> condition){
        Page page = new Page(condition.getPageNumber(),condition.getPageSize());
        IPage<List<FilingProductGroup>> iPage = filingProductGroupService.page(page,Wrappers.<FilingProductGroup>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getCondition().getProductGroupName()),FilingProductGroup::getProductGroupName,condition.getCondition().getProductGroupName())
                .isNull(FilingProductGroup::getParentId));
        return IResponse.success(iPage);
    }

    @PostMapping("/saveOrUpdateProductGroup")
    @ApiOperation(value = "保存修改产品组")
    public IResponse saveOrUpdateProductGroup(@RequestBody FilingProductGroup filingProductGroup){
        if (ObjectUtil.isEmpty(filingProductGroup.getId())){
            filingProductGroupService.save(filingProductGroup);
        }else {
            filingProductGroupService.updateById(filingProductGroup);
        }
        return IResponse.success("保存成功");
    }

    @PostMapping("/removeProductGroup")
    @ApiOperation("删除产品组")
    public IResponse removeProductGroup(@RequestBody FilingProductGroup filingProductGroup){
        return IResponse.success(filingProductGroupService.removeById(filingProductGroup));
    }

    @PostMapping("/queryFilingProductByPage")
    @ApiOperation(value = "分页查询备案产品组详情")
    public IResponse queryFilingProductByPage(@RequestBody QueryCondition<FilingProductGroup> condition){
        Page page = new Page(condition.getPageNumber(),condition.getPageSize());
        IPage<List<FilingProductGroup>> iPage = filingProductGroupService.page(page,Wrappers.<FilingProductGroup>lambdaQuery()
                .eq(FilingProductGroup::getParentId,condition.getCondition().getParentId())
                .like(StrUtil.isNotBlank(condition.getCondition().getProductName()),FilingProductGroup::getProductName,condition.getCondition().getProductName()));
        return IResponse.success(iPage);
    }

    @PostMapping("/addProduct")
    @ApiOperation("添加产品")
    public IResponse addProduct(@RequestBody FilingProductGroup filingProductGroup){
        if (StrUtil.isEmpty(filingProductGroup.getProductId())){
            return IResponse.fail("产品不能为空");
        }
        IResponse resp = producePlanFeign.getProductById(filingProductGroup.getProductId());
        if (Objects.equals("0000", resp.getCode())){
            JSONObject productPlan = Optional.ofNullable(resp.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
            String produceName = productPlan.getString("productName");
            filingProductGroup.setProductName(produceName);
        }
        return IResponse.success(filingProductGroupService.save(filingProductGroup));
    }

    @PostMapping("removeProduct")
    @ApiOperation("删除产品")
    public IResponse removeProduct(@RequestBody FilingProductGroup filingProductGroup){
        return IResponse.success(filingProductGroupService.removeById(filingProductGroup));
    }

    @GetMapping("queryProductGroup")
    @ApiOperation("查询产品组")
    public IResponse queryProductGroup(){
        List<FilingProductGroup> list = filingProductGroupService.list(Wrappers.<FilingProductGroup>lambdaQuery()
                .eq(FilingProductGroup::getStatus,"0")
                .isNull(FilingProductGroup::getParentId));
        List<FilingProductGroup> collect = list.stream().map(group -> {
            FilingProductGroup filingProductGroup = new FilingProductGroup();
            filingProductGroup.setTitle(group.getProductGroupName());
            filingProductGroup.setValue(String.valueOf(group.getId()));
            return filingProductGroup;
        }).collect(Collectors.toList());
        return IResponse.success(collect);
    }
}
