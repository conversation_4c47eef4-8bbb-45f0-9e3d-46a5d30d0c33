package com.ruicar.afs.cloud.afscase.risk.service;


import com.alibaba.fastjson.JSONObject;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import java.util.List;

/**
 * The interface Third data service.
 */
public interface ThirdDataService {
    /**
     * Save.
     *
     * @param thirdData the third data
     */
    void save(ThirdData thirdData);

    /**
     * Query third url by apply no response.
     *
     * @param applyNo the apply no
     * @return the response
     */
    IResponse<String> queryThirdUrlByApplyNo(String applyNo);

    /**
     * Query third data by apply no response.
     *
     * @param applyNo the apply no
     * @return the response
     */
    IResponse queryThirdDataByApplyNo(String applyNo);

    /**
     * 通过申请编号判断是否为TOP20
     * @param applyNo 申请编号
     * @return 返回结果
     */
    boolean isTopChannelByApplyNo(String applyNo);

    /**
     * Save approve third data.
     *
     * @param applyNo   the apply no
     * @param iResponse the response
     * @param reqBody   the req body
     */
    void saveApproveThirdData(String applyNo, IResponse<JSONObject> iResponse, JSONObject reqBody);

    /**
     * Query third loan data by apply no response.
     *
     * @param applyNo the apply no
     * @return the response
     */
    IResponse queryThirdDataLoanByApplyNo(String applyNo);


    /**
     * Query loan third url by apply no response.
     *
     * @param applyNo the apply no
     * @return the response
     */
    IResponse<String> queryLoanThirdUrlByApplyNo(String applyNo);


    /**
     * 获取预审批结果
     * @param applyId
     * @return List<String>
     */
    List<String> queryDecisionEnginePre(String applyId);

    /**
     * 保存需要人工审核信息
     * @param applyNo
     * @param iResponse
     */
    void saveadaitInformation(String applyNo, IResponse<JSONObject> iResponse);
}
