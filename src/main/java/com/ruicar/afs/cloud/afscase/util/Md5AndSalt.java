package com.ruicar.afs.cloud.afscase.util;

import java.security.MessageDigest;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;


public class Md5AndSalt {
    private static final String DEFAULT_KEY ="86C63180C2806ED1F47B859DE501215B";

    public static String generate(String password) {

        String salt = DEFAULT_KEY;
        password = md5Hex(password + salt);
        char[] cs = new char[48];
        for (int i = 0; i < 48; i += 3) {
            cs[i] = password.charAt(i / 3 * 2);
            char c = salt.charAt(i / 3);
            cs[i + 1] = c;
            cs[i + 2] = password.charAt(i / 3 * 2 + 1);
        }
        return new String(cs);
    }

    /**
     * 获取十六进制字符串形式的MD5摘要
     */
    public static String md5Hex(String src) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bs = md5.digest(src.getBytes());
            return new String(new Hex().encode(bs));
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean verify(String password, String md5) {
        char[] cs1 = new char[32];
        char[] cs2 = new char[16];
        for (int i = 0; i < 48; i += 3) {
            cs1[i / 3 * 2] = md5.charAt(i);
            cs1[i / 3 * 2 + 1] = md5.charAt(i + 2);
            cs2[i / 3] = md5.charAt(i + 1);
        }
        String salt = DEFAULT_KEY;
        return md5Hex(password + salt).equals(new String(cs1));
    }

    public static String getPassword(String passwords, String solt) {
        // DigestUtils.md5Hex()此方法为加密方法
        String password = DigestUtils.md5Hex(passwords);
        //此处加密后加盐再进行加密
        return DigestUtils.md5Hex(password + solt);
    }

    public static String getPasswordByDefaultSolt(String passwords) {
        return getPassword(passwords, DEFAULT_KEY);
    }
}
