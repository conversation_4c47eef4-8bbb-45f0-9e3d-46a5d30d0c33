package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalContractDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalFee;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalContractDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalFeeService;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;


/**
 * 服务费租金贷处理
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffCapital")
public class WriteOffCapitalController {

    private final WriteOffCapitalContractDetailService writeOffCapitalContractDetailService;
    private final WriteOffCapitalFeeService writeOffCapitalFeeService;
    private final StringRedisTemplate stringRedisTemplate;
    private static final String SERVICE_FEE_CAL = "service:fee:rule:cal";
    private static final String ZJD_TQ_CREATE = "capital:serverFee:tq:create";

    @PostMapping("/queryByCondition")
    @ApiOperation(value = "查询资方合同明细")
    public IResponse queryByCondition(@RequestBody QueryCondition<WriteOffCapitalContractDetail> queryCondition) {
        WriteOffCapitalContractDetail condition = queryCondition.getCondition();
        Assert.isTrue(condition.getContractStatus() != null, "查询参数异常");
        boolean allFlag = ContractStatusEnum.contractEffective == condition.getContractStatus();
        Page<WriteOffCapitalContractDetail> page = writeOffCapitalContractDetailService.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffCapitalContractDetail::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffCapitalContractDetail::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getContractNo()), WriteOffCapitalContractDetail::getContractNo, condition.getContractNo())
                .eq(StrUtil.isNotBlank(condition.getBelongingCapital()), WriteOffCapitalContractDetail::getBelongingCapital, condition.getBelongingCapital())
                .eq(StrUtil.isNotBlank(condition.getWriteOffFlag()), WriteOffCapitalContractDetail::getWriteOffFlag, condition.getWriteOffFlag())
                .eq(StrUtil.isNotBlank(condition.getReceiveFinishFlag()), WriteOffCapitalContractDetail::getReceiveFinishFlag, condition.getReceiveFinishFlag())
                .like(StrUtil.isNotBlank(condition.getProductName()), WriteOffCapitalContractDetail::getProductName, condition.getProductName())
                .eq(!allFlag, WriteOffCapitalContractDetail::getContractStatus, condition.getContractStatus())
                .orderByDesc(WriteOffCapitalContractDetail::getCreateTime));
        return IResponse.success(page);
    }

    @PostMapping("/exportContractDetail")
    @ApiOperation(value = "导出资方合同明细")
    public void exportContractDetail(@RequestBody WriteOffCapitalContractDetail condition, HttpServletResponse response) {
        writeOffCapitalContractDetailService.exportContractDetail(condition, response);
    }

    @PostMapping("/updateContracts")
    @ApiOperation(value = "资方合同明细更新")
    public IResponse updateContracts() {
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(SERVICE_FEE_CAL, "lock", 5, TimeUnit.HOURS);
        if (lock != null && lock) {
            try {
                return writeOffCapitalContractDetailService.updateContracts();
            } finally {
                stringRedisTemplate.delete(SERVICE_FEE_CAL);
            }
        } else {
            throw new AfsBaseException("租金贷合同更新中，请不要重复操作！");
        }
    }

    @PostMapping("/queryHxxFee")
    @ApiOperation(value = "查询租金贷核销项金额明细")
    public IResponse queryZjdHxxFee(@RequestBody QueryCondition<WriteOffCapitalFee> queryCondition) {
        WriteOffCapitalFee condition = queryCondition.getCondition();
        Page<WriteOffCapitalFee> page = writeOffCapitalFeeService.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffCapitalFee>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffCapitalFee::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffCapitalFee::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getChannelCode()), WriteOffCapitalFee::getChannelCode, condition.getChannelCode())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffCapitalFee::getWriteOffMonth, condition.getWriteOffMonth())
                .ge(condition.getWaitAmtMin() != null, WriteOffCapitalFee::getWaitConfirmAmt, condition.getWaitAmtMin())
                .le(condition.getWaitAmtMax() != null, WriteOffCapitalFee::getWaitConfirmAmt, condition.getWaitAmtMax())
                .orderByDesc(WriteOffCapitalFee::getCreateTime));
        return IResponse.success(page);
    }

    @PostMapping("/exportHxxFee")
    @ApiOperation(value = "导出资方核销项金额明细")
    public void exportZjdHxxFee(@RequestBody WriteOffCapitalFee condition, HttpServletResponse response) {
        writeOffCapitalFeeService.exportHxxFee(condition, response);
    }

    @PostMapping("/createTqRels")
    @ApiOperation(value = "资方核销项生成提取项")
    public IResponse createTqRels() {
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(ZJD_TQ_CREATE, "lock", 2, TimeUnit.HOURS);
        if (lock != null && lock) {
            try {
                return writeOffCapitalFeeService.createTqRels();
            } finally {
                stringRedisTemplate.delete(ZJD_TQ_CREATE);
            }
        } else {
            throw new AfsBaseException("租金贷合同更新中，请不要重复操作！");
        }
    }
}
