package com.ruicar.afs.cloud.afscase.writeoff.entity;


import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;

import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.modules.contract.enums.PaymentStatusEnum;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WriteOffBaseInvoiceRel对象", description="")
public class WriteOffBaseInvoiceRel extends BaseEntity<WriteOffBaseInvoiceRel> {

    @ApiModelProperty(value = "服务费核销项编号")
    private String applyNo;
    @ApiModelProperty(value = "业务模式-租金贷和弗迪自营")
    private String writeOffType;
    @ApiModelProperty(value = "发票id")
    private Long invoiceId;

    private String businessNo;

    /**
     * 经销商编号
     */
    private String dealerCode;
    /**
     * 当前申请批次号
     */
    private String currentBatchNo;
    /**
     * 核销项金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 提取状态 -1 审核中 1已提取，0未提取
     */
    private String status;
    /**
     * 流程状态
     */
    private String approveStatus;
    /**
     * 流程id
     */
    private String approveId;
    /**
     * 经销商名称
     */
    private String dealerName;
    /**
     * 账期
     */
    private String writeOffMonth;
    /**
     * 业务类型 (0直租  1回租)
     */
    private String serviceType;
    /**
     * 进件发票申请 审核编号
     */
    private String caseNo;
    /**
     * 提交时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitDate;
    /**
     * 通过时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date passDate;
    /**
     * 核销项总金额
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;

    /**
     * cbs发送，请求流水号
     */
    private String cbsRefNbr;
    /**
     * cbs发送，对方接口返回流水号
     */
    private String cbsBusNbr;
    /**
     * 付款状态
     */
    private PaymentStatusEnum payStatus;
    /**
     * 付款账号
     */
    private String payAccount;
    /**
     * 收款账号
     */
    private String payReceiveAccount;
    /**
     * 付款失败原因
     */
    private String payFailReason;
    /**
     * 影像件busiNo
     */
    private String fileBusiNo;
    /**
     * 租金贷付款金额(凭证使用)
     */
    private BigDecimal zjdPayAmount;

    @TableField(exist = false)
    private FrozenStatusEnum frozenStatus;

    @TableField(exist = false)
    private String returnOverdueStatus;
}
