package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资方合同明细导出表
 */
@Data
public class CapitalContractExcelVo implements Serializable {

    /**
     * 申请编号
     */
    @ExcelProperty(value = "申请编号")
    private String applyNo;
    /**
     * 合同号
     */
    @ExcelProperty(value = "合同号")
    private String contractNo;
    /**
     * 所属资方
     */
    @ExcelProperty(value = "所属资方")
    private String belongingCapital;
    /**
     * 资方回款时间
     */
    @ExcelProperty(value = "回款时间")
    private Date capitalReturnTime;
    /**
     * 是否生成了核销项(1是，0否)
     */
    @ExcelProperty(value = "是否生成核销项")
    private String writeOffFlag;
    /**
     * 是否应收对账完成(1是，0否)
     */
    @ExcelProperty(value = "是否应收对账完成")
    private String receiveFinishFlag;
    /**
     * 应收对账确认时间
     */
    @ExcelProperty(value = "应收对账确认时间")
    private Date receiveConfirmTime;
    /**
     * 合同状态
     */
    @ExcelProperty(value = "合同状态")
    private String contractStatus;
    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 经销商名称
     */
    @ExcelProperty(value = "经销商名称")
    private String channelFullName;
    /**
     * 合同取消时间
     */
    @ExcelProperty(value = "合同取消时间")
    private Date cancelDate;
    /**
     * 提前结清时间
     */
    @ExcelProperty(value = "提前结清时间")
    private Date settleDate;
    /**
     * 已偿期数
     */
    @ExcelProperty(value = "已偿期数")
    private Integer paidTermNo;
    /**
     * 应扣金额
     */
    @ExcelProperty(value = "应扣金额")
    private BigDecimal deductAmt;
}
