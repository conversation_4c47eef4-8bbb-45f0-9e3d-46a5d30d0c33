package com.ruicar.afs.cloud.afscase.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkTaskApproveRecord;
import com.ruicar.afs.cloud.afscase.workflow.mapper.WorkTaskApproveRecordMapper;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkTaskApproveRecordService;
import org.springframework.stereotype.Service;

@Service
public class WorkTaskApproveRecordServiceImpl extends ServiceImpl<WorkTaskApproveRecordMapper, WorkTaskApproveRecord> implements WorkTaskApproveRecordService {
}
