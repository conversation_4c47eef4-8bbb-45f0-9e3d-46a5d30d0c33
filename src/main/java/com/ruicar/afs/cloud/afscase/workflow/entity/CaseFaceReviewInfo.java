package com.ruicar.afs.cloud.afscase.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.afscase.workflow.enums.FaceReviewEnum;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 功能说明:视频面审结果表
 * <AUTHOR>
 */
@Data
@TableName("case_face_review_info")
public class CaseFaceReviewInfo extends BaseEntity<CaseFaceReviewInfo> {
    /**
     * 进件编号
     */
    private String applyNo;
    /**
     * {@link FaceReviewEnum}
     */
    private String faceReviewResult;
    /**
     * 视频链接
     */
    private String spUrl;
    /**
     * 客户面审时经度
     */
    private String longitude;
    /**
     * 客户面审时纬度
     */
    private String latitude;
    /**
     * 地址信息
     */
    private String address;
    /**
     * 审批备注
     */
    private String remark;
    /**
     * 坐席名称
     */
    private String seatName;
    /**
     * 工单状态
     */
    private String status;
    /**
     * 办理时间
     */
    private Date processingTime;
}
