package com.ruicar.afs.cloud.afscase.onlinemortgage.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruicar.afs.cloud.afscase.onlinemortgage.MortgageService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.condition.VehicleMortgageRegistrationCondition;
import com.ruicar.afs.cloud.afscase.onlinemortgage.config.SocialMortgageConfig;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.CaseMortgageProxyInfo;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.CaseMortgageRecord;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.VehicleMortgageRegistration;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.BlqdEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.BusinessStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.ImpawnZllxEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.YwlxEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.CaseMortgageProxyInfoService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.CaseMortgageRecordService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.VehicleMortgageRegistrationService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnFlowRequest;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnPhotoRequest;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnRequest;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 车辆抵押登记信息表控制层
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/mortgage")
@Api("车辆抵押登记管理")
public class VehicleMortgageRegistrationController {

    private final VehicleMortgageRegistrationService vehicleMortgageRegistrationService;

    private final MortgageService mortgageService;

    private final CaseMortgageProxyInfoService caseMortgageProxyInfoService;

    private final StringRedisTemplate stringRedisTemplate;

    private final SocialMortgageConfig socialMortgageConfig;

    private final CaseMortgageRecordService mortgageRecordService;

    /**
     * 分页查询车辆抵押登记信息
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询车辆抵押登记信息")
    public IResponse<?> pageWithCondition(@RequestBody VehicleMortgageRegistrationCondition condition) {
        IPage<VehicleMortgageRegistration> page = vehicleMortgageRegistrationService.pageWithCondition(condition);
        return IResponse.success(page);
    }

    /**
     * 新增或更新车辆抵押登记信息
     *
     * @param request 车辆抵押登记信息
     * @return 操作结果
     */
    @PostMapping("saveOrUpdate")
    @ApiOperation(value = "新增或更新车辆抵押登记信息")
    public IResponse<Boolean> saveOrUpdate(@RequestBody VehicleMortgageRegistration request) {
        List<VehicleMortgageRegistration> existList = vehicleMortgageRegistrationService.lambdaQuery()
            .eq(VehicleMortgageRegistration::getApplyNo, request.getApplyNo())
            .eq(VehicleMortgageRegistration::getMortgageType, request.getMortgageType())
            .list();
        String desc = MortgageTypeEnum.getDesc(request.getMortgageType());
        if (request.getId() == null) {
            // 新增场景
            if (CollUtil.isNotEmpty(existList)) {
                throw new AfsBaseException("已存在合同号为" + request.getApplyNo() + "的" + desc + "登记信息，请勿重复");
            }
            CaseMortgageRecord entity = new CaseMortgageRecord();
            entity.setApplyNo(request.getApplyNo());
            entity.setMortgageType(request.getMortgageType());
            entity.setLog(
                String.format("金融机构[%s]创建订单，[%s]提交订单", request.getChannelName(), request.getSponsor()));
            mortgageRecordService.save(entity);
        } else {
            // 修改场景
            if (CollUtil.isNotEmpty(existList) && !existList.get(0).getId().equals(request.getId())) {
                throw new AfsBaseException("已存在合同号为" + request.getApplyNo() + "的" + desc + "登记信息，请勿重复");
            }
        }
        if (request.getApprove() != null) {
            request.setMortgageStatus(MortgageStatusEnum.TO_BE_SIGNED);
        }
        return IResponse.success(vehicleMortgageRegistrationService.saveOrUpdateVehicleMortgageRegistration(request));
    }

    /**
     * 发起抵押/解抵押申请
     *
     * @param request 发起抵押/解抵押申请
     * @return 操作结果
     */
    @PostMapping("initiateApplication")
    @ApiOperation(value = "发起抵押/解抵押申请")
    public IResponse<?> initiateApplication(@RequestBody VehicleMortgageRegistrationCondition request) {
        // 状态检查
        List<VehicleMortgageRegistration> list = vehicleMortgageRegistrationService.lambdaQuery()
            .eq(VehicleMortgageRegistration::getApplyNo, request.getApplyNo())
            .eq(VehicleMortgageRegistration::getMortgageType, request.getMortgageType())
            .list();
        Assert.isTrue(CollUtil.isNotEmpty(list), "数据不存在");
        VehicleMortgageRegistration registration = list.get(0);
        if (registration.getMortgageStatus() != MortgageStatusEnum.SIGNING_COMPLETED) {
            throw new AfsBaseException("非签约完成状态，不可发起");
        }
        // -----------------数据准备-----------------------
        boolean isMortgage = registration.getMortgageType() == MortgageTypeEnum.MORTGAGE;
        ImpawnRequest impawnRequest = new ImpawnRequest();
        // 基础信息设置
        impawnRequest.setJkxlh(socialMortgageConfig.getClientId());
        impawnRequest.setYwlx(YwlxEnum.IMPAWN.getCode());
        impawnRequest.setBlqd(BlqdEnum.PRIVATE.getCode());
        impawnRequest.setXh(isMortgage ? null : registration.getMortContractNo());
        impawnRequest.setCzlx("1");
        // 抵押权人信息
        impawnRequest.setZzjgdm(socialMortgageConfig.getUnifiedSocialCreditCode());
        impawnRequest.setDyhth(registration.getApplyNo());
        // 机动车所有人信息
        impawnRequest.setSyrsfzmmc(registration.getOwnerIdType().key());
        impawnRequest.setSyrsfzmhm(registration.getOwnerIdNo());
        impawnRequest.setSyr(registration.getOwnerName());
        impawnRequest.setSyrsjhm(registration.getOwnerPhone());
        // 车辆信息
        impawnRequest.setClsbdh(registration.getVinNo());
        impawnRequest.setHpzl(registration.getPlateType().key());
        impawnRequest.setHphm(registration.getIssuingAuthority()); // 号牌头或完整号牌
        // 所有人代理人信息(可选)
        if (registration.getAgentIdNo() != null) {
            impawnRequest.setSyrdlrsfzmmc(registration.getAgentIdType().key());
            impawnRequest.setSyrdlrsfzmhm(registration.getAgentIdNo());
            impawnRequest.setSyrdlrlxdh(registration.getAgentPhone());
            impawnRequest.setSyrdlrxm(registration.getAgentName());
        }
        // 抵押权人代理人信息
        impawnRequest.setDyqrdlrsfzmmc(registration.getMortgageeAgentIdType().key());
        impawnRequest.setDyqrdlrsfzmhm(registration.getMortgageeAgentIdNo());
        impawnRequest.setDyqrdlrxm(registration.getMortgageeAgentName());
        impawnRequest.setDyqrdlrlxdh(registration.getMortgageeAgentPhone());
        // 业务办理信息
        impawnRequest.setYwblsj(DateUtil.format(registration.getSignTime(),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))); // 金融机构业务办理时间，格式：yyyy-MM-dd HH:mm:ss
        impawnRequest.setBz(registration.getRemark());
        // 邮寄信息
        impawnRequest.setYjdz(socialMortgageConfig.getMailAddress());
        impawnRequest.setYjlxdh(socialMortgageConfig.getContactPhone());
        impawnRequest.setYjlxr(socialMortgageConfig.getFinancialInstitution());
        impawnRequest.setYjyzbm(socialMortgageConfig.getZipCode());
        // 附件信息
        List<ImpawnPhotoRequest> imageList = new ArrayList<>();
        for (ImpawnZllxEnum value : ImpawnZllxEnum.values()) {
            ImpawnPhotoRequest photoRequest = new ImpawnPhotoRequest();
            photoRequest.setZllx(value.getCode());
            if ("pdf".equals(value.getSuffix())) {
                photoRequest.setZpnr(MortgageService.getByte("C:\\1.pdf"));
            } else {
                photoRequest.setZpnr(MortgageService.getByte("C:\\1.jpg"));
            }
            imageList.add(photoRequest);
        }
        impawnRequest.setImageList(imageList);
        String lockValue = registration.getApplyNo();
        String key= MessageFormat.format("{0}{1}","lock-mortgage-submit:",lockValue);
        Boolean res= stringRedisTemplate.opsForValue().setIfAbsent(key, lockValue, 2, TimeUnit.MINUTES);
        Assert.isTrue(res, "不可重复提交！");
        try {
            // 发起请求
            JSONObject mortgage = isMortgage
                ? mortgageService.mortgage(impawnRequest)
                : mortgageService.releaseMortgage(impawnRequest);
            boolean suc = mortgage.getString("code") != null && StrUtil.equals(mortgage.getString("code"), "200");
            if (suc) {
                vehicleMortgageRegistrationService.lambdaUpdate()
                    .eq(VehicleMortgageRegistration::getId, registration.getId())
                    .set(VehicleMortgageRegistration::getMortgageStatus,
                        isMortgage ? MortgageStatusEnum.MORTGAGING : MortgageStatusEnum.MORTGAGE_RELEASING)
                    .set(VehicleMortgageRegistration::getDealStatus, BusinessStatusEnum.PRE_ACCEPTED)
                    .update();
            } else {
                // 失败记录原因
                vehicleMortgageRegistrationService.lambdaUpdate()
                    .eq(VehicleMortgageRegistration::getId, registration.getId())
                    .set(VehicleMortgageRegistration::getFailureReason, "发起失败:"+mortgage.getString("message"))
                    .update();
            }
        } catch (Exception e) {
            log.error("{}签约申请发起异常", request.getApplyNo(), e);
            vehicleMortgageRegistrationService.lambdaUpdate()
                .eq(VehicleMortgageRegistration::getId, registration.getId())
                .set(VehicleMortgageRegistration::getFailureReason, "发起异常:" + e.getMessage())
                .update();
            return IResponse.fail("签约发起异常" + e.getMessage());
        } finally {
            String value = stringRedisTemplate.opsForValue().get(key);
            if (lockValue.equals(value)) {
                stringRedisTemplate.delete(key);
            }
        }
        return IResponse.success("发起成功");
    }

    /**
     * 查询操作日志
     *
     * @param request 查询操作日志
     * @return 操作结果
     */
    @PostMapping("listLog")
    @ApiOperation(value = "查询操作日志")
    public IResponse<?> listLog(@RequestBody VehicleMortgageRegistrationCondition request) {
        return IResponse.success(mortgageRecordService.lambdaQuery()
            .eq(CaseMortgageRecord::getApplyNo, request.getApplyNo())
            .eq(CaseMortgageRecord::getMortgageType, request.getMortgageType())
            .orderByDesc(CaseMortgageRecord::getCreateTime)
            .list());
    }

    @PostMapping("/getInfoByApplyNo")
    @ApiOperation(value = "通过申请编号查询对应的信息")
    public IResponse<VehicleMortgageRegistration> getInfoByApplyNo(
        @RequestBody VehicleMortgageRegistrationCondition condition) {
        Assert.isTrue(StringUtil.isNotEmpty(condition.getApplyNo()), "申请编号不能为空！");
        return IResponse.success(vehicleMortgageRegistrationService.getInfoByApplyNo(condition.getApplyNo()));
    }

    /**
     * 抵押状态更新
     *
     * @param condition
     * @return
     */
    @PostMapping("/testStatusUpdate")
    @ApiOperation(value = "通过申请编号查询对应的信息")
    public IResponse<?> testStatusUpdate(@RequestBody VehicleMortgageRegistration condition) throws Exception {
        ImpawnFlowRequest impawnFlowRequest = new ImpawnFlowRequest();
        impawnFlowRequest.setJkxlh(socialMortgageConfig.getClientId());
        impawnFlowRequest.setXh(condition.getMortContractNo());
        impawnFlowRequest.setYwzt(condition.getDealStatus().getCode());
        impawnFlowRequest.setTbyyms(condition.getRemark());
        JSONObject data = mortgageService.testStatusUpdate(impawnFlowRequest);
        return IResponse.success(data);
    }

    /**
     * 代理人过期删除
     *
     * @param request 代理人过期删除
     * @return 操作结果
     */
    @PostMapping("/deletedAgentExpired")
    @ApiOperation(value = "代理人过期删除")
    public IResponse<?> deletedAgentExpired(@RequestBody VehicleMortgageRegistrationCondition request) {
        LambdaQueryWrapper<CaseMortgageProxyInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CaseMortgageProxyInfo::getApplyNo, request.getApplyNo());
        return IResponse.success(caseMortgageProxyInfoService.remove(wrapper));
    }
}
