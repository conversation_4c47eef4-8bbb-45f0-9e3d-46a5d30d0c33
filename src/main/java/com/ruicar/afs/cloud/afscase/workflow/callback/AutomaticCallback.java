package com.ruicar.afs.cloud.afscase.workflow.callback;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.ApproveMakeLabel;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.ApproveMakeLabelService;
import com.ruicar.afs.cloud.afscase.approveprev.entity.CaseApprovePrevInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.autoaudit.condition.CardDetectRecordChangeCondition;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.CardDetectRecordChange;
import com.ruicar.afs.cloud.afscase.autoaudit.service.CardDetectRecordChangeService;
import com.ruicar.afs.cloud.afscase.autoaudit.service.PropertyLicenseAiRecognitionService;
import com.ruicar.afs.cloud.afscase.carrierpigeon.service.CarrierPigeonRuleDetailService;
import com.ruicar.afs.cloud.afscase.carrierpigeon.service.CarrierPigeonService;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseIntelligentResult;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekDrivingInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekOperatorInfo;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseIntelligentResultService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekDrivingInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekOperatorInfoService;
import com.ruicar.afs.cloud.afscase.common.constants.RiskResConstants;
import com.ruicar.afs.cloud.afscase.common.constants.RskRuleConstants;
import com.ruicar.afs.cloud.afscase.common.enums.InterfaceIdentifyEnum;
import com.ruicar.afs.cloud.afscase.common.service.SplittingTimeService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.RiskCheckUtils;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBusinessLicense;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseFacePhotoInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBusinessLicenseService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.manuallabel.entity.ManualLabel;
import com.ruicar.afs.cloud.afscase.manuallabel.service.impl.ManualLabelServiceImpl;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveInformInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.afscase.risk.mapper.ThirdDataMapper;
import com.ruicar.afs.cloud.afscase.workflow.config.CaseLoanRentProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.service.CaseRiskCustInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApplyResultInfoDto;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.deepseek.entity.BusinessAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.StatementAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.service.BusinessAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.StatementAutomaticRecognitionService;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.parameter.commom.enums.ResultBooleanEnum;
import com.ruicar.afs.cloud.risk.api.enums.RiskStatus;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import com.ruicar.afs.cloud.zhengxin.entity.ComReportFile;
import com.ruicar.afs.cloud.zhengxin.service.ComReportFileService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.afscase.workflow.enums.FlowAutoEnum.SUBMITAUTO;
import static com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum.REFUSE;
import static com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum.SUBMIT;

/**
 * 自动审批、转人工审批- 信审审批专用
 */
@Slf4j
@AllArgsConstructor
@Component
public class AutomaticCallback implements CommonAdapter {

    private ManualLabelServiceImpl manualLabelService;
    private ApproveMakeLabelService approveMakeLabelService;
    private CaseBaseInfoService baseInfoService;
    private CaseApproveRecordService caseApproveRecordService;
    private final ComReportFileService comReportFileService;
    private final CaseCustInfoService caseCustInfoService;
    private final CaseRiskCustInfoService riskCustInfoService;
    private ThirdDataMapper thirdDataMapper;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private final FlowConfigProperties flowConfigProperties;
    private CaseLoanRentProperties caseLoanRentProperties;
    private CaseConfParamService caseConfParamService;
    private ComAttachmentManagementService comAttachmentManagementService;
    private CardDetectRecordChangeService cardDetectRecordChangeService;
    private StatementAutomaticRecognitionService statementAutomaticRecognitionService;
    private ComAttachmentFileService comAttachmentFileService;
    private DeepSeekDrivingInfoService deepSeekDrivingInfoService;
    private DeepSeekOperatorInfoService deepSeekOperatorInfoService;
    private final CaseIntelligentResultService intelligentResultService;
    private final CaseBusinessLicenseService caseBusinessLicenseService;
    private final CaseFacePhotoInfoService facePhotoInfoService;
    private final BusinessAutomaticRecognitionService businessAutomaticRecognitionService;
    private final PropertyLicenseAiRecognitionService propertyLicenseAiRecognitionService;
    private SplittingTimeService splittingTimeService;
    private final CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private final CarrierPigeonRuleDetailService carrierPigeonRuleDetailService;
    private ApproveInformInfoService approveInformInfoService;
    private final CaseCarInfoService caseCarInfoService;
    private final CaseCostInfoService caseCostInfoService;
    private final CarrierPigeonService carrierPigeonService;
    private final CaseChannelInfoService caseChannelInfoService;
    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
                                       String extParam, Map<String, String> flowVariables) {
        log.info("flow : {} variables : {}", flowInstanceId, flowVariables);
        final String applyNo = flowVariables.get(FlowConstant.BUSINESS_NO);
        CaseBaseInfo caseBaseInfo = baseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));
        // 正式复议(复议后重走审批)不走决策
        if(WhetherEnum.YES.getCode().equals(flowVariables.get("isReconsideration"))){
            log.info("审批自动节点，正式复议跳过决策调用：{}",applyNo);
            handleValid(flowVariables,caseBaseInfo);
            return flowVariables;
        }
        log.info("-------调用开始入参-------" + applyNo);
        flowVariables.put("bizOperationType", AfsEnumUtil.key(NormalSubmitType.ARTIFICIAL));
        //决策异常信息
        String riskErrorMsg="";
        //最终决策结果
        String finalDealTypeCode = AfsEnumUtil.key(RiskStatus.RISK_RES_REVIEW);
        //规则列表
        JSONArray codeList;
        //是否含有规则Q（决策结果通过且包含Q规则，走单岗审批）
        boolean hasQRule=false;
        JSONObject riskJsonRes;
        //是否包含S规则，包含走视频面审流程
        boolean hasSRule=false;
        //所属资方（默认FD）
        String belongingCapital=BelongingCapitalEnum.FD.getCode();
        //执行决策引擎
        if (Objects.equals(caseBaseInfo.getIsFirstSubmit(),1)) {
            //首次提交已经调用过决策引擎，无需再次调用
            ThirdData thirdData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                    .eq(ThirdData::getApproveId, applyNo)
                    .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
            riskJsonRes=JSONObject.parseObject(thirdData.getResponse());
        }else {
            String equeryid = null;
            String cqueryid = null;
            CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo,applyNo)
                    .eq(CaseCustInfo::getCustRole,"03"));
            if (ObjectUtil.isNotNull(custInfo)){
                ComReportFile comReportFile1 = comReportFileService.getOne(Wrappers.<ComReportFile>lambdaQuery()
                        .eq(ComReportFile::getBelongNo,custInfo.getCertNo())
                        .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
                if(ObjectUtil.isNotNull(comReportFile1)){
                    equeryid = comReportFile1.getElectronicNo();
                }
            }
            ComReportFile comReportFile = comReportFileService.getOne(Wrappers.<ComReportFile>lambdaQuery().eq(ComReportFile::getBelongNo,caseBaseInfo.getCertNoRepeat()).orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
            if (ObjectUtil.isNotNull(comReportFile)){
                cqueryid = comReportFile.getElectronicNo();
            }
            IResponse<JSONObject> riskRes = RiskCheckUtils.riskCheckExecute(applyNo,caseBaseInfo.getInputType(), AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.NO),equeryid,cqueryid);
            riskJsonRes=riskRes.getData();
        }
        List<String> ruleCode = new ArrayList<>();
        if (riskJsonRes!=null&&!riskJsonRes.isEmpty()) {
            if (Objects.equals(caseBaseInfo.getIsFirstSubmit(),1)){
                //原始报文解析
                finalDealTypeCode = Optional.of(riskJsonRes).map(json -> json.getString(RiskResConstants.FINAL_DEAL_TYPE_CODE)).orElse(AfsEnumUtil.key(RiskStatus.RISK_RES_REVIEW));
                codeList = Optional.ofNullable(riskJsonRes.getJSONObject("decisionDetailedResults")).map(res -> res.getJSONArray("ruleCustomIds")).orElse(new JSONArray());
            }else {
                //非原始报文解析
                finalDealTypeCode = riskJsonRes.getString(RiskResConstants.FINAL_DEAL_TYPE_CODE);
                riskErrorMsg = riskJsonRes.getString(RiskResConstants.RISK_ERROR_MSG);
                codeList = riskJsonRes.getJSONArray(RiskResConstants.RULE_CUSTOM_IDS);
            }
            // pboc客户信息保存
            riskCustInfoService.pbocCustInfo(riskJsonRes, applyNo);

            if (codeList != null){
                String lesseeGrade = "0";
                String isSecondHand = "0";
                String isMortgage = "0";
                String isHousingReduction = "0";
                for (Object o : codeList) {
                    String code = o.toString();
                    ruleCode.add(code);
                    if (code.startsWith(RskRuleConstants.FACE_REVIEW_PRE)) {
                        hasSRule = true;
                    }
                    if (code.startsWith(RskRuleConstants.POST_IMAGE_PRE)) {
                        //决策通过，且规则包含Q开头的，直接单岗审批
                        hasQRule=true;
                    }
                    if ("L028".equals(code)) {
                        //承租人低风险
                        lesseeGrade = "0";
                    } else if ("L029".equals(code)) {
                        //承租人中低风险
                        lesseeGrade = "1";
                    } else if ("L030".equals(code)) {
                        //承租人中风险
                        lesseeGrade = "2";
                    } else if ("L031".equals(code)) {
                        //承租人中高风险
                        lesseeGrade = "3";
                    } else if ("L032".equals(code)) {
                        //承租人高风险
                        lesseeGrade = "4";
                    } else if ("NH33".equals(code)) {
                        //二手单
                        isSecondHand = "1";
                    } else if (RskRuleConstants.RENT_LOAN_PRE.equals(code)) {
                        if (caseLoanRentProperties.getEnableInvestors().contains(BelongingCapitalEnum.BANK.getCode())) {
                            belongingCapital = BelongingCapitalEnum.BANK.getCode();
                        }
                    } else if (RskRuleConstants.RENT_LOAN_SH_PRE.equals(code)) {
                        if (caseLoanRentProperties.getEnableInvestors().contains(BelongingCapitalEnum.BANK_SH.getCode())) {
                            belongingCapital = BelongingCapitalEnum.BANK_SH.getCode();
                        }
                    } else if (RskRuleConstants.IS_MORTGAGE.equals(code)) {
                        // 是否抵押
                        isMortgage = "1";
                    } else if (RskRuleConstants.IS_HOUSING_REDUCTION.equals(code)) {
                        // 是否住房减免
                        isHousingReduction = "1";
                    }
                }
                baseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                        .set(StrUtil.isNotEmpty(lesseeGrade),CaseBaseInfo::getLesseeGrade,lesseeGrade)
                        .set(CaseBaseInfo::getIsSecondHand,isSecondHand)
                        .set(CaseBaseInfo::getBelongingCapital,belongingCapital)
                        .eq(CaseBaseInfo::getApplyNo,applyNo));
                caseUseApplyServiceFeign.updateOrderIsMortgage(applyNo, isMortgage);
                ApplyResultInfoDto applyResultInfoDto = new ApplyResultInfoDto();
                applyResultInfoDto.setApplyNo(applyNo);
                applyResultInfoDto.setHousingReductionFlag(isHousingReduction);
                caseUseApplyServiceFeign.updateOrderInfoByResultInfoDto(applyResultInfoDto);
            }
        }
        //状态映射
        Map<RiskStatus, NormalSubmitType> decidedResultTranslateMap = new HashMap<>();
        decidedResultTranslateMap.put(RiskStatus.RISK_RES_ACCEPT, NormalSubmitType.SUGGEST_CHECK_FINAL);
        decidedResultTranslateMap.put(RiskStatus.RISK_RES_REJECT, NormalSubmitType.SUGGEST_REJECT_FINAL);
        decidedResultTranslateMap.put(RiskStatus.RISK_RES_REVIEW, NormalSubmitType.ARTIFICIAL);
        RiskStatus riskStatus = (RiskStatus) AfsEnumUtil.getEnum(finalDealTypeCode, RiskStatus.class);
        NormalSubmitType finalType = decidedResultTranslateMap
                .getOrDefault(riskStatus, NormalSubmitType.ARTIFICIAL);
        //设置所属资方流程变量
        flowVariables.put(FlowConstant.BELONGING_CAPITAL, belongingCapital);
        flowVariables.put(FlowConstant.IS_CARRIER_PIGEON_BACK, ResultBooleanEnum.FALSE.getCode());
        CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
        if (finalType == NormalSubmitType.SUGGEST_CHECK_FINAL&&!hasQRule) {
            flowVariables.put("last_operation", SUBMIT.name());
            //自动通过时，添加审批日志
            caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SUGGEST_CHECK_FINAL));
            caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SUGGEST_CHECK_FINAL));
            caseApproveRecord.setApproveRemark("决策通过");
            //打自动通过标签
            autoLabel(SUBMIT.getDesc(), caseBaseInfo.getApplyNo());
            flowVariables.put("bizOperationType", SUBMIT.getCode());
        } else if (finalType == NormalSubmitType.SUGGEST_REJECT_FINAL) {
            flowVariables.put("last_operation", REFUSE.name());
            caseApproveRecord.setApproveReason("自动拒绝");
            //拒绝原因设置
            if (StrUtil.isNotBlank(riskErrorMsg)) {
                //去掉最后的逗号
                caseApproveRecord.setApproveRemark(riskErrorMsg.substring(0, riskErrorMsg.length() - 1));
            }else {
                caseApproveRecord.setApproveRemark("系统拒绝");
            }
            caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SUGGEST_REJECT_FINAL));
            caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SUGGEST_REJECT_FINAL));
            autoLabel(REFUSE.getDesc(), caseBaseInfo.getApplyNo());
            flowVariables.put("bizOperationType", REFUSE.getCode());
            //自动拒绝留言
            flowVariables.put(FlowConstant.LAST_APPROVE_REASON,"J05");
        } else {
            caseApproveRecord.setApproveSuggest(SUBMIT.getCode());
            caseApproveRecord.setApproveSuggestName(SUBMIT.getDesc());
            caseApproveRecord.setApproveRemark(String.format("决策转人工，决策输出结果：%s-%s",
                    finalDealTypeCode,riskStatus != null ? AfsEnumUtil.desc(riskStatus) : "无"));
            flowVariables.put("bizOperationType", AfsEnumUtil.key(NormalSubmitType.ARTIFICIAL));
            //决策结果
            if (hasSRule) {
                //包含S规则（仅人工审核出现），走决策引擎
                flowVariables.put(FlowConstant.RISK_CHECK,AfsEnumUtil.key(RiskStatus.RISK_RES_FACE_REVIEW));
            }else {
                flowVariables.put(FlowConstant.RISK_CHECK,finalDealTypeCode);
            }
            // 调用信鸽规则
            try {
                if(ResultBooleanEnum.FALSE.getCode().equals(caseBaseInfo.getIsCarrierPigeon())){
                    CaseCarInfo caseCarInfo = caseCarInfoService.queryInfoByApplyNo(applyNo);
                    FinCostDetails finCostInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, applyNo).eq(FinCostDetails::getCostType, CostTypeEnum.CARAMT.getCode()));
                    JSONObject runParams = new JSONObject();
                    runParams.put("brandName", caseCarInfo.getBrandCode());
                    runParams.put("seriesName", caseCarInfo.getSeriesName());
                    runParams.put("modelName", caseCarInfo.getModelName());
                    runParams.put("channelFullName", caseBaseInfo.getChannelFullName());
                    runParams.put("operateWay", caseBaseInfo.getOperateWay());
                    runParams.put("productName", caseBaseInfo.getProductName());
                    runParams.put("loanAmtRepeat", caseBaseInfo.getLoanAmtRepeat());
                    runParams.put("downPaymentRatio", null != finCostInfo ? finCostInfo.getDownPayScale() : BigDecimal.valueOf(0));
                    runParams.put("ruleCode", String.join("@#@", ruleCode));
                    log.info("【信鸽规则匹配】测试日志，调用规则参数：{}",JSONObject.toJSON(runParams));
                    Boolean carrierPigeonRule = carrierPigeonRuleDetailService.handleCarrierPigeonRule(runParams);
                    flowVariables.put(FlowConstant.IS_CARRIER_PIGEON_BACK,carrierPigeonRule.toString());
                    if(ResultBooleanEnum.TRUE.getCode().equals(carrierPigeonRule.toString())){
                        // 发送信鸽短信
                        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                                .eq(CaseChannelInfo::getApplyNo, applyNo));
                        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                                .eq(CaseCustInfo::getApplyNo, applyNo)
                                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
                        CaseApprovePrevInfo caseApprovePrevInfo = new CaseApprovePrevInfo();
                        caseApprovePrevInfo.setApplyNo(applyNo);
                        caseApprovePrevInfo.setCustName(caseCustInfo.getCustName());
                        caseApprovePrevInfo.setTelPhone(caseCustInfo.getTelPhone());
                        caseApprovePrevInfo.setChannelName(caseBaseInfo.getChannelFullName());
                        caseApprovePrevInfo.setSellerPhone(caseChannelInfo.getSalePhone());
                        caseApprovePrevInfo.setSellerRealName(caseChannelInfo.getSaleAdvisor());
                        log.info("【信鸽规则匹配】测试日志，发送短信信息：{}",JSONObject.toJSON(caseApprovePrevInfo));
                        carrierPigeonService.approveSendRuleMessage(caseApprovePrevInfo);
                        // 通知进件
                        CaseApproveRecord record = new CaseApproveRecord();
                        record.setUseScene(UseSceneEnum.APPROVE.getValue());
                        record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                        record.setApproveEndTime(new Date());
                        record.setApproveStartTime(new Date());
                        record.setDisposeStaff("系统");
                        record.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SEND_BACK_TO_DEALER));
                        record.setApproveSuggestName("退回经销商");
                        record.setApplyNo(applyNo);
                        record.setStageId(flowInstanceId);
                        record.setDisposeNodeName("自动节点");
                        record.setApproveRemark("信鸽规则匹配成功自动退回！");
                        baseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                                .set(CaseBaseInfo::getIsCarrierPigeon,carrierPigeonRule.toString())
                                .set(CaseBaseInfo::getBusinessStateIn,AfsEnumUtil.key(BusinessStateInEnum.REVISE_PARSE))
                                .eq(CaseBaseInfo::getApplyNo,applyNo));
                        caseApproveRecordService.save(record);
                        approveInformInfoService.submitApprovalNotic(record);
                        return flowVariables;
                    }
                }else{
                    log.info("【信鸽规则匹配】申请编号：{}测试日志，历史单或者已调用过，开始调用",applyNo);
                }
            } catch (Exception e) {
                flowVariables.put(FlowConstant.IS_CARRIER_PIGEON_BACK,ResultBooleanEnum.FALSE.getCode());
                log.error("【信鸽规则匹配】调用失败!", e);
            }
        }
        caseApproveRecord.setApplyNo(caseBaseInfo.getApplyNo());
        caseApproveRecord.setApproveEndTime(new Date());
        caseApproveRecord.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        caseApproveRecord.setDisposeStaff("系统");
        caseApproveRecordService.save(caseApproveRecord);
        log.info("决策自动审批状态{},审批状态{}", caseBaseInfo, flowVariables.get("bizOperationType"));
        handleValid(flowVariables,caseBaseInfo);
        return flowVariables;
    }

    /**
     * 智能识别
     * @param flowVariables
     * @param caseBaseInfo
     */
    public void handleValid(Map<String, String> flowVariables,CaseBaseInfo caseBaseInfo){
        try {
            String flowBackOption = flowVariables.get("flowBackOption");
            String backNodeId = flowVariables.get("backNodeId");
            AtomicBoolean isValidAll = new AtomicBoolean(true);
            log.info("{}【智能识别】申请编号, 智能识别判断flowBackOption:{},backNodeId:{}", caseBaseInfo.getApplyNo(),
                flowBackOption, backNodeId);
            // 智能初审标识，附件展示需要状态，每次提交全都需要识别
            boolean intelligentEnable = StrUtil.equals(
                AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.YES), caseBaseInfo.getApplyJointStatus());
            CaseIntelligentResult caseIntelligentResult = intelligentAttachmentRecognition(caseBaseInfo, isValidAll,
                intelligentEnable,true);
            intelligentResultService.saveOrUpdate(caseIntelligentResult);
            if ("DIRECT".equals(flowBackOption) && ("SECOND_NODE".equals(backNodeId) || "END_NODE".equals(
                backNodeId))) {
                isValidAll.set(false);
            }
            if(isValidAll.get()){
                // 保存初审日志
                CaseApproveRecord record = new CaseApproveRecord();
                record.setApproveSuggest("submit");
                record.setApproveSuggestName("自动通过");
                record.setApproveRemark("智能识别匹配成功！");
                record.setApplyNo(caseBaseInfo.getApplyNo());
                record.setApproveEndTime(new Date());
                record.setDisposeStaff("系统");
                record.setDisposeNodeName("初审");
                caseApproveRecordService.save(record);
                flowVariables.put("backNodeId", "SECOND_NODE");
                flowVariables.put("last_operation", "SUBMIT");
                try {
                    CaseBaseInfo baseInfo1 = baseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                            .eq(CaseBaseInfo::getApplyNo, caseBaseInfo.getApplyNo()));
                    if(!StrUtil.equals(baseInfo1.getBusinessStateIn(),BusinessStateInEnum.REVISE_PARSE.key())){
                        String isLastSubmitNotSplittingTime = AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.YES);
                        caseBaseInfo.update(
                                Wrappers.<CaseBaseInfo>lambdaUpdate()
                                        .eq(CaseBaseInfo::getApplyNo, caseBaseInfo.getApplyNo())
                                        .set(CaseBaseInfo::getIsLastSubmitNotSplittingTime, isLastSubmitNotSplittingTime)
                                        .set(CaseBaseInfo::getBusinessStateIn,BusinessStateInEnum.EXCEPTIONALLOCATION));
                    }
                }catch (Exception e){
                    log.error("执行分单时间规则判断报错:{}",e);
                }
            }
            flowVariables.put("isValidAll", String.valueOf(isValidAll.get()));
        } catch (Exception e) {
            flowVariables.put("isValidAll", "false");
            log.error("【智能识别】申请编号:{},智能识别出现异常！", caseBaseInfo.getApplyNo(), e);
        }
    }

    public CaseIntelligentResult intelligentAttachmentRecognition(CaseBaseInfo caseBaseInfo, AtomicBoolean isValidAll,
        boolean intelligentEnable,Boolean isLog) {
        if (isLog) {
            log.info("【智能识别】申请编号:{}, 开始智能识别", caseBaseInfo.getApplyNo());
        }
        List<CaseConfParam> caseConfParamList = caseConfParamService.list(Wrappers.<CaseConfParam>lambdaQuery()
            .eq(CaseConfParam::getParamLogo, CaseConstants.CREDIT_AUTO_APPROVES)
            .eq(CaseConfParam::getParamStatus, "yes"));
        Assert.isTrue(CollectionUtil.isNotEmpty(caseConfParamList), "影像智能识别配置为空！");

        CaseIntelligentResult caseIntelligentResult = new CaseIntelligentResult();
        caseIntelligentResult.setApplyNo(caseBaseInfo.getApplyNo());
        caseIntelligentResult.setIntelligent(intelligentEnable);

        Map<String, List<CaseConfParam>> stringListMap = caseConfParamList.stream()
            .collect(Collectors.groupingBy(CaseConfParam::getParamValue));
        stringListMap.forEach((key, val) -> {
            List<String> code = val.stream().map(CaseConfParam::getParamType).toList();
            boolean anyMatchFacePhoto = code.stream()
                .anyMatch(str -> str.equals(InterfaceIdentifyEnum.INTERFACE_FACE_PHOTO.getCode()));
            List<ComAttachmentManagement> managementList = comAttachmentManagementService.list(
                Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .in(ComAttachmentManagement::getUniqueCode, code));
            if (anyMatchFacePhoto) {
                List<ComAttachmentManagement> management = comAttachmentManagementService.list(
                    Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                        .eq(ComAttachmentManagement::getBusiNode, "orderApply"));
                managementList.addAll(management);
            }
            List<String> managementIdList = managementList.stream()
                .map(management -> String.valueOf(management.getId()))
                .collect(Collectors.toList());
            List<ComAttachmentFile> attachmentFiles = comAttachmentFileService.list(
                Wrappers.<ComAttachmentFile>lambdaQuery()
                    .in(ComAttachmentFile::getAttachmentCode, managementIdList)
                    .eq(ComAttachmentFile::getBelongNo, caseBaseInfo.getApplyNo())
                    .eq(ComAttachmentFile::getBusiNo, caseBaseInfo.getApplyNo()));
            InterfaceIdentifyEnum interfaceIdentifyEnum = InterfaceIdentifyEnum.create(key);
            switch (interfaceIdentifyEnum) {
                case INTERFACE_IDENTITY_CARD:
                    List<CaseCustInfo> custInfoList = caseCustInfoService.list(
                        Wrappers.<CaseCustInfo>lambdaQuery()
                            .eq(CaseCustInfo::getApplyNo, caseBaseInfo.getApplyNo())
                            .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));
                    List<ComAttachmentManagement> idCardList = managementList.stream()
                        .filter(s -> List.of(
                                AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode(),
                                AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode(),
                                AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode(),
                                AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode())
                            .contains(s.getUniqueCode())).toList();
                    for (ComAttachmentManagement comAttachmentManagement : idCardList) {
                        if (CollectionUtil.isEmpty(custInfoList)) {
                            if (comAttachmentManagement.getUniqueCode()
                                .equals(AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode())
                                || comAttachmentManagement.getUniqueCode()
                                .equals(AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode())) {
                                continue;
                            }
                        }
                        CardDetectRecordChangeCondition cardDetectRecordChangeCondition = new CardDetectRecordChangeCondition();
                        cardDetectRecordChangeCondition.setAttachmentCode(String.valueOf(comAttachmentManagement.getId()));
                        cardDetectRecordChangeCondition.setApplyNo(caseBaseInfo.getApplyNo());
                        IResponse<CardDetectRecordChange> iResponse
                            = cardDetectRecordChangeService.getByCondition(cardDetectRecordChangeCondition);
                        CardDetectRecordChange data = iResponse.getData();
                        if (data == null) {
                            if (isLog) {
                                log.warn("【智能识别】申请编号:{}, 附件名称:{} 智能识别查询数据为空！", caseBaseInfo.getApplyNo(), comAttachmentManagement.getAttachmentName());
                            }
                            setIdCardResultByType(comAttachmentManagement, caseIntelligentResult, false);
                            isValidAll.set(false);
                            cardDetectRecordChangeCondition.setRefreshOcr(CaseConstants.YES);
                            cardDetectRecordChangeService.getByCondition(cardDetectRecordChangeCondition);
                        } else {
                            // 检查当前数据的标志位是否全部为 "1"
                            boolean currentValid = isAllFlagsValid(data);
                            if (!currentValid) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{}, 附件名称：{} 智能识别存在不合格点！", caseBaseInfo.getApplyNo(), comAttachmentManagement.getAttachmentName());
                                }
                                setIdCardResultByType(comAttachmentManagement, caseIntelligentResult, false);
                                isValidAll.set(false);
                            } else {
                                // 担保人通过
                                if (comAttachmentManagement.getUniqueCode()
                                    .equals(
                                        AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode())) {
                                    caseIntelligentResult.setGuarantorIdCard(true);
                                }
                                if (comAttachmentManagement.getUniqueCode()
                                    .equals(
                                        AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode())) {
                                    caseIntelligentResult.setGuarantorIdCardBack(true);
                                }
                            }
                        }
                    }
                    break;
                case INTERFACE_CURRENT_ACCOUNT:
                    List<ComAttachmentManagement> collect = managementList.stream()
                        .filter(management -> AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode()
                            .equals(management.getUniqueCode()))
                        .toList();
                    if (collect.isEmpty()) {
                        break;
                    }
                    // 流水文件检查
                    List<ComAttachmentFile> list = attachmentFiles.stream()
                        .filter(
                            s -> StrUtil.equals(s.getAttachmentCode(), String.valueOf(collect.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(list)) {
                        break;
                    }
                    boolean noRe = CollUtil.isNotEmpty(list.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    List<StatementAutomaticRecognition> recognitionList = statementAutomaticRecognitionService.list(
                        Wrappers.<StatementAutomaticRecognition>lambdaQuery()
                            .eq(StatementAutomaticRecognition::getApplyNo, caseBaseInfo.getApplyNo())
                            .orderByDesc(StatementAutomaticRecognition::getUpdateTime));
                    boolean allMatch = recognitionList.stream()
                        .anyMatch(info -> info.getOverallJudgment() == 1);
                    if (CollectionUtil.isEmpty(recognitionList) || !allMatch) {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别流水类存在不合格点！", caseBaseInfo.getApplyNo());
                        }
                        if(noRe){
                            // 跳过类型
                            caseIntelligentResult.setBankStatement("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setBankStatement("0");
                        }
                    } else {
                       if (noRe){
                           caseIntelligentResult.setBankStatement("3");
                       }
                    }
                    // 判断识别记录是否完整
                    if (list.size() > recognitionList.size()) {
                        caseIntelligentResult.setBankStatementComplete(false);
                    }
                    break;
                case INTERFACE_DRIVER_LICENSE:
                    List<ComAttachmentManagement> collectDriver = managementList.stream()
                        .filter(management -> AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()
                            .equals(management.getUniqueCode()))
                        .toList();
                    if (collectDriver.isEmpty()) {
                        break;
                    }
                    // 驾驶证文件检查
                    List<ComAttachmentFile> driverList = attachmentFiles.stream()
                        .filter(s -> StrUtil.equals(s.getAttachmentCode(),
                            String.valueOf(collectDriver.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(driverList)) {
                        break;
                    }
                    boolean driverNoRe = CollUtil.isNotEmpty(driverList.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    List<DeepSeekDrivingInfo> recognitionListDriver = deepSeekDrivingInfoService.list(
                        Wrappers.<DeepSeekDrivingInfo>lambdaQuery()
                            .eq(DeepSeekDrivingInfo::getApplyNo, caseBaseInfo.getApplyNo())
                            .orderByDesc(DeepSeekDrivingInfo::getCreateTime));
                    if (CollectionUtil.isEmpty(recognitionListDriver)) {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别驾驶证类识别记录不存在！", caseBaseInfo.getApplyNo());
                        }
                        if(driverNoRe){
                        // 没识别记录，资料忽略
                            caseIntelligentResult.setDriverLicense("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setDriverLicense("0");
                        }
                        break;
                    }
                    // 识别记录完整性检查
                    if (driverList.size() > recognitionListDriver.size()) {
                        caseIntelligentResult.setDriverLicenseComplete(false);
                    }
                    Boolean electronDriver = false;
                    Boolean paperDriver = false;
                    // 电子和纸质有一个满足，纸质可能分成两个图片,多组图片匹配一个
                    Map<String, List<DeepSeekDrivingInfo>> drivingMap = recognitionListDriver.stream()
                        .collect(Collectors.groupingBy(DeepSeekDrivingInfo::getAnalysisType));
                    // 电子ds
                    List<DeepSeekDrivingInfo> dsList = drivingMap.get("ds");
                    if (CollectionUtil.isNotEmpty(dsList)) {
                        for (DeepSeekDrivingInfo deepSeekDrivingInfo : dsList) {
                            String driverResult = deepSeekDrivingInfo.getDsResult();
                            JSONObject driverRes = StrUtil.isBlank(driverResult)
                                ? null
                                : JSONObject.parseObject(driverResult);
                            if (driverRes == null) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别驾驶证类ds识别内容不存在！",
                                        caseBaseInfo.getApplyNo());
                                }
                                continue;
                            }
                            JSONObject approve = driverRes.getJSONObject("approve");
                            if (approve == null) {
                                continue;
                            }
                            boolean match = approve.values().stream().allMatch("1"::equals);
                            if (!match) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别驾驶证类ds存在不合格点！",
                                        caseBaseInfo.getApplyNo());
                                }
                                continue;
                            }
                            electronDriver = true;
                        }
                    }
                    List<DeepSeekDrivingInfo> ocr = drivingMap.get("ocr");
                    // 纸质,电子识别通过则跳过
                    if (CollectionUtil.isNotEmpty(ocr) && !electronDriver) {
                        // 正页 副页 驾驶证
                        Boolean front = false;
                        Boolean back = false;
                        Boolean all = false;
                        for (DeepSeekDrivingInfo deepSeekDrivingInfo : ocr) {
                            String driverResult = deepSeekDrivingInfo.getOcrResult();
                            JSONObject driverRes = StrUtil.isBlank(driverResult)
                                ? null
                                : JSONObject.parseObject(driverResult);
                            if (driverRes == null) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别驾驶证类识别内容不存在！",
                                        caseBaseInfo.getApplyNo());
                                }
                                continue;
                            }
                            JSONObject apply = driverRes.getJSONObject("apply");
                            if (apply == null) {
                                continue;
                            }
                            String type = apply.getString("证件类型");
                            if (type == null) {
                                continue;
                            }
                            JSONObject approve = driverRes.getJSONObject("approve");
                            if (approve == null) {
                                continue;
                            }
                            boolean match = approve.values().stream().allMatch("1"::equals);
                            if (!match) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别驾驶证类存在不合格点！",
                                        caseBaseInfo.getApplyNo());
                                }
                                continue;
                            }
                            if (StrUtil.equals(type, "纸质驾驶证")) {
                                all = true;
                            }
                            if (StrUtil.equals(type, "驾驶证正页")) {
                                front = true;
                            }
                            if (StrUtil.equals(type, "驾驶证副页")) {
                                back = true;
                            }
                        }
                        if (front && back || all) {
                            paperDriver = true;
                        }
                    }
                    // 都不通过，都等于false时
                    if (!paperDriver && !electronDriver) {
                        if (driverNoRe) {
                            // 不通过资料忽略
                            caseIntelligentResult.setDriverLicense("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setDriverLicense("0");
                        }
                    } else {
                        if (driverNoRe) {
                            caseIntelligentResult.setDriverLicense("3");
                        }
                    }
                    break;
                case INTERFACE_OPERATOR_CERTIFICATE:
                    List<ComAttachmentManagement> list1 = managementList.stream()
                        .filter(management -> AttachmentUniqueCodeEnum.OPERATOR_CERTIFICATE.getCode()
                            .equals(management.getUniqueCode()))
                        .toList();
                    if (list1.isEmpty()) {
                        break;
                    }
                    // 营运人证文件检查
                    List<ComAttachmentFile> list2 = attachmentFiles.stream()
                        .filter(
                            s -> StrUtil.equals(s.getAttachmentCode(), String.valueOf(list1.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(list2)) {
                        break;
                    }
                    boolean operatorNoRe = CollUtil.isNotEmpty(list2.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    List<DeepSeekOperatorInfo> recognitionListOperator = deepSeekOperatorInfoService.list(
                        Wrappers.<DeepSeekOperatorInfo>lambdaQuery()
                            .eq(DeepSeekOperatorInfo::getApplyNo, caseBaseInfo.getApplyNo())
                            .orderByDesc(DeepSeekOperatorInfo::getUpdateTime));

                    // 营运人证识别记录完整性检查
                    if (list2.size() > recognitionListOperator.size()) {
                        caseIntelligentResult.setOperatorCertificateComplete(false);
                    }
                    Boolean operatorFlag = Boolean.FALSE;
                    if (CollectionUtil.isNotEmpty(recognitionListOperator)) {
                        for (DeepSeekOperatorInfo deepSeekOperatorInfo : recognitionListOperator) {
                            JSONObject operatorRes = StrUtil.isBlank(deepSeekOperatorInfo.getDsResult())
                                ? null
                                : JSONObject.parseObject(deepSeekOperatorInfo.getDsResult());
                            if (operatorRes == null || ObjectUtil.isEmpty(
                                operatorRes.getJSONObject("approve"))) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别营运人证内容为空！",
                                        caseBaseInfo.getApplyNo());
                                }
                                break;
                            }
                            JSONObject operatorApprove = operatorRes.getJSONObject("approve");
                            boolean match = operatorApprove.values().stream().allMatch("1"::equals);
                            if (!match) {
                                if (isLog) {
                                    log.warn("【智能识别】申请编号:{} 智能识别营运人证类存在不合格点！",
                                        caseBaseInfo.getApplyNo());
                                }
                            } else {
                                operatorFlag = Boolean.TRUE;
                            }
                        }
                    } else {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别营运人证没有记录！", caseBaseInfo.getApplyNo());
                        }
                    }
                    if (!operatorFlag) {
                        if (operatorNoRe) {
                            caseIntelligentResult.setOperatorCertificate("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setOperatorCertificate("0");
                        }
                    } else {
                        if (operatorNoRe) {
                            caseIntelligentResult.setOperatorCertificate("3");
                        }
                    }
                    break;
                case INTERFACE_FACE_PHOTO:
                    List<ComAttachmentManagement> faceManagement = managementList.stream()
                        .filter(management -> "面签照".equals(management.getAttachmentName()))
                        .toList();
                    if (faceManagement.isEmpty()) {
                        break;
                    }
                    // 面签照文件判断
                    List<ComAttachmentFile> faceList = attachmentFiles.stream()
                        .filter(s -> StrUtil.equals(s.getAttachmentCode(),
                            String.valueOf(faceManagement.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(faceList)) {
                        break;
                    }
                    boolean faceNoRe = CollUtil.isNotEmpty(faceList.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    List<CaseFacePhotoInfo> faceResultList = facePhotoInfoService.lambdaQuery()
                        .eq(CaseFacePhotoInfo::getApplyNo, caseBaseInfo.getApplyNo())
                        .orderByDesc(CaseFacePhotoInfo::getCreateTime)
                        .list();
                    boolean faceFlag = false;
                    if (CollUtil.isNotEmpty(faceResultList)) {
                        Map<String, List<CaseFacePhotoInfo>> fileIdMap = faceResultList.stream()
                            .collect(Collectors.groupingBy(CaseFacePhotoInfo::getFileId));
                        // 面签照识别记录完整性检查
                        if (faceList.size() > fileIdMap.size()) {
                            caseIntelligentResult.setFacePhotoComplete(false);
                        }
                        faceFlag = faceJudge(fileIdMap, caseIntelligentResult,isLog);
                    } else {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别面签照失败,识别记录为空",
                                caseBaseInfo.getApplyNo());
                        }
                    }
                    if (!faceFlag) {
                        if (faceNoRe) {
                            caseIntelligentResult.setFacePhoto("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setFacePhoto("0");
                        }
                    } else {
                        if (faceNoRe) {
                            caseIntelligentResult.setFacePhoto("3");
                        }
                    }
                    break;
                case BUSINESS_LICENSE:
                    List<ComAttachmentManagement> blManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BUSINESS_LICENSE.getCode()));
                    if (blManage.isEmpty()) {
                        break;
                    }
                    // 营业执照文件判断
                    List<ComAttachmentFile> licenseList = attachmentFiles.stream()
                        .filter(s -> StrUtil.equals(s.getAttachmentCode(),
                            String.valueOf(blManage.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(licenseList)) {
                        break;
                    }
                    boolean licenseNoRe = CollUtil.isNotEmpty(licenseList.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    Boolean businessLicenseFlag = Boolean.FALSE;
                    List<CaseBusinessLicense> businessLicenseList = caseBusinessLicenseService.lambdaQuery()
                        .eq(CaseBusinessLicense::getApplyNo, caseBaseInfo.getApplyNo())
                        .orderByDesc(CaseBusinessLicense::getCreateTime)
                        .list();
                    if (CollUtil.isNotEmpty(businessLicenseList)) {
                        businessLicenseFlag = businessLicenseList.stream()
                            .anyMatch(s -> StrUtil.equals("1", s.getUnitNameComparison()) && StrUtil.equals("1",
                                s.getAffiliatedNameComparison()) && StrUtil.equals("1", s.getValidityDateComparison())
                                && StrUtil.equals("1", s.getOfficialSealLetteringComparison()) && StrUtil.equals("1",
                                s.getOfficialSeal()) && StrUtil.equals("1", s.getCopyOrScan()));
                    } else {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别营业执照失败,识别记录为空",
                                caseBaseInfo.getApplyNo());
                            caseIntelligentResult.setBusinessLicenseComplete(false);
                        }
                    }
                    if (!businessLicenseFlag) {
                        if (licenseNoRe) {
                            caseIntelligentResult.setBusinessLicense("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setBusinessLicense("0");
                        }
                    } else {
                        if (licenseNoRe) {
                            caseIntelligentResult.setBusinessLicense("3");
                        }
                    }
                    break;
                case OPERATING_LICENSE:
                    List<ComAttachmentManagement> opManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.OPERATING_LICENSE.getCode()));
                    if (opManage.isEmpty()) {
                        break;
                    }
                    // 经营许可证文件判断
                    List<ComAttachmentFile> operatingList = attachmentFiles.stream()
                        .filter(s -> StrUtil.equals(s.getAttachmentCode(),
                            String.valueOf(opManage.get(0).getId())))
                        .toList();
                    if (CollectionUtil.isEmpty(operatingList)) {
                        break;
                    }
                    boolean operatingNoRe = CollUtil.isNotEmpty(operatingList.stream().filter(s -> StrUtil.equals(s.getRemake(), "noRe")).toList());
                    Boolean operatingListFlag = Boolean.FALSE;

                    List<BusinessAutomaticRecognition> operatingListResult = businessAutomaticRecognitionService.list(
                        Wrappers.<BusinessAutomaticRecognition>lambdaQuery()
                            .eq(BusinessAutomaticRecognition::getApplyNo, caseBaseInfo.getApplyNo()));
                    if (CollUtil.isNotEmpty(operatingListResult)) {
                        operatingListFlag = operatingListResult.stream().anyMatch(s -> s.getOverallJudgment() == 1);
                    } else {
                        if (isLog) {
                            log.warn("【智能识别】申请编号:{} 智能识别经营许可证失败,识别记录为空", caseBaseInfo.getApplyNo());
                            caseIntelligentResult.setOperatingLicenseComplete(false);
                        }
                    }
                    if (!operatingListFlag) {
                        if (operatingNoRe) {
                            caseIntelligentResult.setOperatingLicense("2");
                        } else {
                            isValidAll.set(false);
                            caseIntelligentResult.setOperatingLicense("0");
                        }
                    } else {
                        if (operatingNoRe) {
                            caseIntelligentResult.setOperatingLicense("3");
                        }
                    }
                    break ;
                default:
                    if (isLog) {
                        log.info("【智能识别】 申请编号:{} 接口{}为查询到对应接口!", caseBaseInfo.getApplyNo(),
                            interfaceIdentifyEnum.getCode());
                    }
            }
        });
        if (!intelligentEnable) { isValidAll.set(false); }
        return caseIntelligentResult;
    }

    private boolean faceJudge(Map<String, List<CaseFacePhotoInfo>> fileIdMap, CaseIntelligentResult caseIntelligentResult,Boolean isLog) {
        for (Map.Entry<String, List<CaseFacePhotoInfo>> entry : fileIdMap.entrySet()) {
            Map<String, CaseFacePhotoInfo> map = entry.getValue().stream().collect(Collectors.toMap(CaseFacePhotoInfo::getType, Function.identity(), (s1, s2) -> s1));
            CaseFacePhotoInfo ds = map.get("ds");
            CaseFacePhotoInfo face = map.get("face");
            boolean dsJudge = ds != null && ds.getDsResult() != null && ds.getDsResult();
            boolean faceJudge = face != null && StrUtil.equals(face.getFaceResult(), "1");
            if (!dsJudge || !faceJudge) {
                if (isLog) {
                    log.warn("【智能识别】申请编号:{} 智能识别面签照失败,ds识别结果{},人脸识别结果{}",
                        caseIntelligentResult.getApplyNo(), dsJudge, faceJudge);
                }
            } else {
                return true;
            }
        }
        return false;
    }

    private void setIdCardResultByType(ComAttachmentManagement attachmentManagement, CaseIntelligentResult caseIntelligentResult, Boolean number) {
        String uniqueCode = attachmentManagement.getUniqueCode();
        if (AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode().equals(uniqueCode)) {
            caseIntelligentResult.setIdCard(number);
        } else if (AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode().equals(uniqueCode)) {
            caseIntelligentResult.setIdCardBack(number);
        } else if (AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode().equals(uniqueCode)) {
            caseIntelligentResult.setGuarantorIdCard(number);
        } else if (AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode().equals(uniqueCode)) {
            caseIntelligentResult.setGuarantorIdCardBack(number);
        }
    }

    /**
     * 检查身份证智能识别结果的所有标志位是否全部为 "1"
     *
     * @return 全部为 "1" 返回 true，否则返回 false
     */
    public boolean isAllFlagsValid(CardDetectRecordChange data) {
        return "1".equals(data.getIsPass())
                && "1".equals(data.getDefinitionDetectFlag())
                && "1".equals(data.getImageCompleteFlag())
                && "1".equals(data.getSpotDetectFlag())
                && "1".equals(data.getNonPicinpicFlag())
                && "1".equals(data.getCardOriginalFlag())
                && "1".equals(data.getImageOriginalFlag())
                && "1".equals(data.getPsDetectFlag())
                && "1".equals(data.getSameAsOwnName())
                && "1".equals(data.getSameAsOwnIdCard());
    }

    private void autoLabel(String decision, String applyNo){
        //自动打标签

        JSONObject params = new JSONObject();
        params.put("automaticTag", AfsEnumUtil.key(YesOrNoEnum.yes));
        //自动通过或自动拒绝，其他情况不考虑
        if(decision.equals(SUBMITAUTO)){
            params.put("hasAutoPass", AfsEnumUtil.key(WhetherEnum.YES));
        }else {
            params.put("hasAutoPass", AfsEnumUtil.key(WhetherEnum.NO));
        }
        List<ManualLabel> manualLabelList = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getStatus, LabelStatusEnum.TAKE_EFFECT.getCode())
                .eq(ManualLabel::getLabel, LabelWayEnum.AUTOMATIC.getCode())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.CREDITADUIT.getCode())
                .isNotNull(ManualLabel::getRuleId)
        );
        for (ManualLabel label : manualLabelList) {
            boolean ruleRunResult;
            try {
                ruleRunResult = RuleHelper.runRule(params, label.getId().toString()).getHit();
            } catch (Exception e) {
                ruleRunResult = false;
                log.info("调用规则失败异常原因：{}", e.getMessage());
            }
            if(ruleRunResult){
                ApproveMakeLabel approveMakeLabel = new ApproveMakeLabel();
                ManualLabel manualLabel = manualLabelService.queryAllLabelListById(label.getId());
                approveMakeLabel.setApplyNo(applyNo);
                approveMakeLabel.setLabelId(manualLabel.getId());
                approveMakeLabel.setLabelName(manualLabel.getLabelName());
                approveMakeLabel.setLabelPhase(manualLabel.getLabelPhase());
                approveMakeLabel.setLabelColor(manualLabel.getLabelColor());
                approveMakeLabel.setLabelType(manualLabel.getLabelType());
                approveMakeLabel.setLabelLocation(manualLabel.getLabelLocation());
                approveMakeLabel.setLabelSort(manualLabel.getLabelSort());
                approveMakeLabel.setLabelPattern(manualLabel.getLabel());
                approveMakeLabel.setLabelClassification(manualLabel.getLabelClassification());
                approveMakeLabel.setIsArchive(manualLabel.getIsArchive());
                approveMakeLabelService.save(approveMakeLabel);
            }
        }
    }

}
