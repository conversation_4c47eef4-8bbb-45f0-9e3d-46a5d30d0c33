package com.ruicar.afs.cloud.afscase.writeoff.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 经销商服务费入参
 * add by jinguoliang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelServiceFeeDTO {

    int pageSize;

    int pageNumber;

    private String channelCode;

    private String channelName;

    private String applyNo;
    private String businessNo;
    private String packageId;
    private String templateId;
    private String userDefinedIndex;
    private String stageId;
    private boolean check;
    private String processInstanceId;
    private String status;
    private String returnOverdueStatus;
    private String submitDateStart;
    private String submitDateStop;
    private String passDateStart;
    private String passDateStop;
    private String frozenStatus;
    private String writeOffMonth;
    private String approveId;
    private List<String> writeOffMonthList;
    /**
     * 可访问的经销商code列表
     */
    private List<String> hitCodeList;
    private String writeOffType;
}
