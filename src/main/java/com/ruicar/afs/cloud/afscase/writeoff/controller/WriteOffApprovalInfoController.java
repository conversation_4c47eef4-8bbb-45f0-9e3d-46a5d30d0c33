package com.ruicar.afs.cloud.afscase.writeoff.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffApprovalInfoCondition;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffCallBackCondition;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffInvoiceInfo;
import com.ruicar.afs.cloud.afscase.writeoff.enums.OverdueStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.feign.ApplyWriteOffFeign;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApprovalInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.ComprehensiveButtonVo;
import com.ruicar.afs.cloud.bizcommon.voucher.service.MqMessageQueueLogService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.OverdueVO;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.constant.VoucherBuriedPointNo;
import com.ruicar.afs.cloud.common.modules.dto.mq.voucher.VoucherFlowInfoDto;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 核销项申请信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
@Slf4j
@RestController
@RequestMapping("/writeOffApprovalInfo")
@AllArgsConstructor
public class WriteOffApprovalInfoController {
    private WriteOffApprovalInfoService writeOffApprovalInfoService;
    private final WorkflowHelper workflowHelper;
    private CaseApproveRecordService caseApproveRecordService;
    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final ApplyWriteOffFeign applyWriteOffFeign;
    private final WriteOffInvoiceInfoService writeOffInvoiceInfoService;
    private final WorkflowProcessBusinessRefInfoService workflowProcessBusinessRefInfoService;
    private final MqMessageQueueLogService mqMessageQueueLogService;
    private final ChannelBaseInfoService channelBaseInfoService;

    @PostMapping("/submitApprove")
    @Transactional(rollbackFor = Exception.class)
    public IResponse submitApprove(@RequestBody WriteOffApprovalInfoCondition writeOffApprovalInfoCondition){
        return writeOffApprovalInfoService.submitApprove(writeOffApprovalInfoCondition);
    }

    @PostMapping("/getWriteOfApprovalList")
    @ApiOperation("获取在审核列表")
    @PreAuthorize("@pms.hasPermission('write_approval_query')")
    public IResponse getWriteOfApprovalList(@RequestBody QueryCondition<WriteOffApprovalInfoCondition> condition){
        return writeOffApprovalInfoService.getWriteOfApprovalList(condition);
    }


    @PostMapping("/getBusinessInfo")
    public IResponse getBusinessInfo(@RequestParam("businessNo") String businessNo){
        return writeOffApprovalInfoService.getBusinessInfo(businessNo);
    }

    @PostMapping("/writeOffResult")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("财务退回/通过回调接口")
    public IResponse writeOffResult(@RequestBody WriteOffCallBackCondition callBackConditions){
        return writeOffApprovalInfoService.writeOffResult(callBackConditions);
    }

    @GetMapping("/billApplyCodeIsExist")
    @ApiOperation("判断当前费控单号是否存在")
    public IResponse billApplyCodeIsExist(@RequestParam("billApplyCode") String billApplyCode){
        return writeOffApprovalInfoService.billApplyCodeIsExist(billApplyCode);
    }

    @GetMapping("/selectLogByBusinessNo")
    @ApiOperation("根据申请编号查询日志")
    public IResponse selectLogByBusinessNo(@RequestParam("businessNo") String businessNo){
        return writeOffApprovalInfoService.selectLogByBusinessNo(businessNo);
    }

    @ApiOperation(value = "核销审核通过、拒绝、退件")
    @PostMapping("/comprehensiveButton")
    @Transactional(rollbackFor = Exception.class)
    public IResponse comprehensiveButton(@RequestBody ComprehensiveButtonVo buttonChange){
        SubmitTaskParam submitTaskParam = new SubmitTaskParam();
        JSONObject flowVariables = new JSONObject();
        flowVariables.put("bizOperationType", buttonChange.getApprovalOperating());
        submitTaskParam.setOperationType(buttonChange.getApprovalOperating());
        submitTaskParam.setApproveSuggest(buttonChange.getApprovalOperating());
        submitTaskParam.setApproveSuggestName(buttonChange.getReasonType());
        submitTaskParam.setRemark(buttonChange.getReasonType());
        submitTaskParam.setTaskId(buttonChange.getTaskId());
        submitTaskParam.setNodeId(buttonChange.getProcessInstanceId());
        submitTaskParam.setExtendParams(flowVariables);
        if (StringUtils.isNotEmpty(buttonChange.getRemark())){
            submitTaskParam.setRemark(buttonChange.getRemark());
        }else {
            submitTaskParam.setRemark("无");
        }
        if ("销售审核".equals(buttonChange.getTaskNodeName()) && "submit".equals(buttonChange.getApprovalOperating())) {
            //首节点，销售审核，设置发票通过时间
            Date date = new Date();
            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                    .eq(WriteOffBaseInvoiceRel::getCaseNo, buttonChange.getCaseNo()));
            //拿到批次号
            String currentBatchNo = writeOffBaseInvoiceRels.get(0).getCurrentBatchNo();
            //拿到发票信息
            writeOffInvoiceInfoService.update(Wrappers.<WriteOffInvoiceInfo>lambdaUpdate()
                    .set(WriteOffInvoiceInfo::getApproveTime, date)
                    .eq(WriteOffInvoiceInfo::getCurrentBatchNo, currentBatchNo));

            syncReturnTime(writeOffBaseInvoiceRels);
        }
        IResponse iResponse = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.INITIATE_WRITE);
        if ("财务审核".equals(buttonChange.getTaskNodeName()) && "back2dealer".equals(buttonChange.getApprovalOperating())) {
            CaseApproveRecord resultList = caseApproveRecordService.getOne(
                    Wrappers.<CaseApproveRecord>lambdaQuery()
                            .eq(CaseApproveRecord::getApplyNo, buttonChange.getCaseNo())
                            .orderByDesc(CaseApproveRecord::getCreateTime).last("limit 1"));
            resultList.setApproveSuggestName("退回上一个节点修改");
            caseApproveRecordService.updateById(resultList);
        }
        if ("财务审核".equals(buttonChange.getTaskNodeName()) && "submit".equals(buttonChange.getApprovalOperating())) {
            //纸质发票人工审核结束，记凭证
            WriteOffBaseInfo tempOne = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .eq(WriteOffBaseInfo::getCaseNo, buttonChange.getCaseNo())
                    .orderByDesc(WriteOffBaseInfo::getCreateTime).last("limit 1"));
            saveServiceFeeData(tempOne);
        }
        return iResponse;
    }

    private void syncReturnTime(List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels) {
        // 425 首节点通过解冻回司超期引起的核销项冻结
        Set<String> applyNo = writeOffBaseInvoiceRels.stream()
            .map(WriteOffBaseInvoiceRel::getApplyNo)
            .collect(Collectors.toSet());

        List<WriteOffBaseInfo> writeOffBaseInfos = writeOffBaseInfoService.lambdaQuery()
            .in(WriteOffBaseInfo::getApplyNo, applyNo)
            .orderByDesc(WriteOffBaseInfo::getWriteOffMonth)
            .list();

        WriteOffBaseInfo offBaseInfo = writeOffBaseInfos.get(0);
        if (StrUtil.isNotBlank(offBaseInfo.getReturnOverdueAppNo()) && JSONUtil.toList(
            offBaseInfo.getReturnOverdueAppNo(), String.class).contains(offBaseInfo.getApplyNo())) {

            // 该经销商所有的回司冻结核销项
            List<WriteOffBaseInfo> baseInfos = writeOffBaseInfoService.lambdaQuery()
                .eq(WriteOffBaseInfo::getOrganId, offBaseInfo.getOrganId())
                .eq(WriteOffBaseInfo::getReturnOverdueStatus, OverdueStatusEnum.FROZEN.getCode())
                .list();

            HashMap<String, List<String>> info = new HashMap<>();
            for (WriteOffBaseInfo baseInfo : baseInfos) {
                if (StrUtil.isNotBlank(baseInfo.getReturnOverdueAppNo())) {
                    List<String> appNos = JSONUtil.toList(baseInfo.getReturnOverdueAppNo(), String.class);
                    appNos.removeAll(applyNo);
                    String status = CollUtil.isEmpty(appNos)
                        ? OverdueStatusEnum.NORMAL.getCode()
                        : OverdueStatusEnum.FROZEN.getCode();
                    String overDue = CollUtil.isNotEmpty(appNos) ? JSONUtil.toJsonStr(appNos) : null;
                    writeOffBaseInfoService.lambdaUpdate()
                        .eq(WriteOffBaseInfo::getId, baseInfo.getId())
                        .set(WriteOffBaseInfo::getReturnOverdueStatus, status)
                        .set(WriteOffBaseInfo::getReturnOverdueAppNo, overDue)
                        .update();
                    info.put(baseInfo.getApplyNo(),appNos);
                }
            }
            // 同步进件状态
            IResponse<?> response = applyWriteOffFeign.updateReturnOverdueStatus(new OverdueVO(info));
            Assert.isTrue("0000".equals(response.getCode()),"同步进件回司状态失败");
        }
    }

    @ApiOperation(value = "核销批量审核通过、拒绝、退件")
    @PostMapping("/batchComprehensiveButtonList")
    @Transactional(rollbackFor = Exception.class)
    public IResponse batchComprehensiveButtonList(@RequestBody List<ComprehensiveButtonVo> buttonChange) {
        if ("财务审核".equals(buttonChange.get(0).getTaskNodeName()) && buttonChange.get(0).getApprovalOperating().equals(FlowTaskOperationEnum.SUBMIT.getCode())) {
            int passSum = 0;
            int refuseSum = 0;
            for (ComprehensiveButtonVo comprehensiveButtonVo : buttonChange) {
                try {
                    SubmitTaskParam submitTaskParam = new SubmitTaskParam();
                    JSONObject flowVariables = new JSONObject();
                    flowVariables.put("bizOperationType", comprehensiveButtonVo.getApprovalOperating());
                    submitTaskParam.setOperationType(comprehensiveButtonVo.getApprovalOperating());
                    submitTaskParam.setApproveSuggest(comprehensiveButtonVo.getApprovalOperating());
                    submitTaskParam.setApproveSuggestName(comprehensiveButtonVo.getApprovalOpinion());
                    submitTaskParam.setRemark(comprehensiveButtonVo.getApprovalOperating());
                    submitTaskParam.setTaskId(comprehensiveButtonVo.getTaskId());
                    submitTaskParam.setNodeId(comprehensiveButtonVo.getProcessInstanceId());
                    submitTaskParam.setExtendParams(flowVariables);
                    if (StringUtils.isNotEmpty(comprehensiveButtonVo.getRemark())) {
                        submitTaskParam.setRemark(comprehensiveButtonVo.getRemark());
                    } else {
                        submitTaskParam.setRemark("无");
                    }
                    IResponse iResponse = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.INITIATE_WRITE);
                    if (!CommonConstants.SUCCESS.equals(iResponse.getCode())) {
                        throw new AfsBaseException("服务费核销工作流提交失败！");
                    }
                    passSum++;
                    //纸质发票人工审核结束，记凭证
                    WriteOffBaseInfo tempOne = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                            .eq(WriteOffBaseInfo::getCaseNo, comprehensiveButtonVo.getCaseNo())
                            .orderByDesc(WriteOffBaseInfo::getCreateTime).last("limit 1"));
                    saveServiceFeeData(tempOne);
                } catch (AfsBaseException e) {
                    refuseSum++;
                }
            }
            return IResponse.success("流程审批通过,其中成功" + passSum + "笔" + "," + "失败" + refuseSum + "笔");
        } else {
            for (ComprehensiveButtonVo comprehensiveButtonVo : buttonChange) {
                SubmitTaskParam submitTaskParam = new SubmitTaskParam();
                JSONObject flowVariables = new JSONObject();
                flowVariables.put("bizOperationType", comprehensiveButtonVo.getApprovalOperating());
                submitTaskParam.setOperationType(comprehensiveButtonVo.getApprovalOperating());
                submitTaskParam.setApproveSuggest(comprehensiveButtonVo.getApprovalOperating());
                submitTaskParam.setApproveSuggestName(comprehensiveButtonVo.getApprovalOpinion());
                submitTaskParam.setRemark(comprehensiveButtonVo.getApprovalOperating());
                submitTaskParam.setTaskId(comprehensiveButtonVo.getTaskId());
                submitTaskParam.setNodeId(comprehensiveButtonVo.getProcessInstanceId());
                submitTaskParam.setExtendParams(flowVariables);
                if (StringUtils.isNotEmpty(comprehensiveButtonVo.getRemark())) {
                    submitTaskParam.setRemark(comprehensiveButtonVo.getRemark());
                } else {
                    submitTaskParam.setRemark("无");
                }
                IResponse iResponse = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.INITIATE_WRITE);
                if (!CommonConstants.SUCCESS.equals(iResponse.getCode())) {
                    throw new AfsBaseException("服务费核销工作流提交失败！");
                }
                if ("销售审核".equals(comprehensiveButtonVo.getTaskNodeName()) && "submit".equals(comprehensiveButtonVo.getApprovalOperating())) {
                    //首节点，销售审核，设置发票通过时间
                    Date date = new Date();
                    List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                            .select(WriteOffBaseInvoiceRel::getCurrentBatchNo)
                            .eq(WriteOffBaseInvoiceRel::getCaseNo, comprehensiveButtonVo.getCaseNo()));
                    //拿到批次号
                    String currentBatchNo = writeOffBaseInvoiceRels.get(0).getCurrentBatchNo();
                    //拿到发票信息
                    writeOffInvoiceInfoService.update(Wrappers.<WriteOffInvoiceInfo>lambdaUpdate()
                            .set(WriteOffInvoiceInfo::getApproveTime, date)
                            .eq(WriteOffInvoiceInfo::getCurrentBatchNo, currentBatchNo));

                    syncReturnTime(writeOffBaseInvoiceRels);
                }
                if ("财务审核".equals(comprehensiveButtonVo.getTaskNodeName()) && "back2dealer".equals(comprehensiveButtonVo.getApprovalOperating())) {
                    CaseApproveRecord resultList = caseApproveRecordService.getOne(
                            Wrappers.<CaseApproveRecord>lambdaQuery()
                                    .eq(CaseApproveRecord::getApplyNo, comprehensiveButtonVo.getCaseNo())
                                    .orderByDesc(CaseApproveRecord::getCreateTime).last("limit 1"));
                    resultList.setApproveSuggestName("退回上一个节点修改");
                    caseApproveRecordService.updateById(resultList);
                }
                if ("财务审核".equals(comprehensiveButtonVo.getTaskNodeName()) && "submit".equals(comprehensiveButtonVo.getApprovalOperating())) {
                    //纸质发票人工审核结束，记凭证
                    WriteOffBaseInfo tempOne = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                            .eq(WriteOffBaseInfo::getCaseNo, comprehensiveButtonVo.getCaseNo())
                            .orderByDesc(WriteOffBaseInfo::getCreateTime).last("limit 1"));
                    saveServiceFeeData(tempOne);
                }
            }
            return IResponse.success("服务费发票核销工作流提交成功");
        }
    }

    /**
     * 激活服务费发票流程,通过或退回
     */
    @ApiOperation("激活服务费发票流程")
    @PostMapping(value = "/resume")
    public IResponse resumeWriteOff(@RequestParam("businessNo") String businessNo, @RequestParam("bizOperationType") String bizOperationType, @RequestParam("disposeStaff") String disposeStaff, @RequestParam("checkStatus") Integer checkStatus) {
        return writeOffApprovalInfoService.resumeWriteOff(businessNo, bizOperationType, disposeStaff, checkStatus);
    }

    @ApiOperation("同步凭证号到服务费发票流程")
    @PostMapping("/syncVoucherToFlow")
    public void syncVoucherToFlow(@RequestParam String contractNo, @RequestParam String businessNo, @RequestParam String voucherNo, @RequestParam String postingDate) {
        try {
            WriteOffBaseInfo baseInfo = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .select(WriteOffBaseInfo::getCaseNo)
                    .eq(WriteOffBaseInfo::getOrganId, contractNo)
                    .eq(WriteOffBaseInfo::getVoucher, businessNo));
            Assert.isTrue(baseInfo != null && baseInfo.getCaseNo() != null, "服务费不能为空");
            String caseNo = baseInfo.getCaseNo();
            WorkflowProcessBusinessRefInfo taskInfo = workflowProcessBusinessRefInfoService.getOne(Wrappers.<WorkflowProcessBusinessRefInfo>query().lambda()
                    .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, caseNo).orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime).last("limit 1"));
            FlowVariable flowVariable = new FlowVariable();
            flowVariable.setFlowInstanceId(taskInfo.getProcessInstanceId());
            flowVariable.setName("voucherNoAndDate");
            flowVariable.setValue(voucherNo+","+postingDate);
            //修改流程变量信息
            IResponse booleanResponse = workflowHelper.setFlowVariableByFlowInstance(flowVariable);
            if (!CommonConstants.SUCCESS.equals(booleanResponse.getCode())) {
                throw new AfsBaseException("修改流程变量信息失败！");
            }
            IResponse response = workflowHelper.resumeProcess(taskInfo.getProcessInstanceId());
            if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                throw new AfsBaseException("流程激活失败!");
            }
            log.info("上传确认发票，激活等待节点成功" + caseNo);
        } catch (AfsBaseException e) {
            log.error("{}激活等待节点失败，异常{}", businessNo, e);
        }
    }

    private void saveServiceFeeData(WriteOffBaseInfo baseInfo) {
        log.info("服务费发布埋点数据：" + baseInfo.getApplyNo());
        VoucherFlowInfoDto voucherFlowInfoDto = new VoucherFlowInfoDto();
        voucherFlowInfoDto.setBuriedPointNo(WriteOffTypeEnum.ZJD.getCode().equals(baseInfo.getWriteOffType()) ? VoucherBuriedPointNo.rentLoanServiceUpload : VoucherBuriedPointNo.writeOffUploadInvoice);
        voucherFlowInfoDto.setTransNo(baseInfo.getCaseNo());
        voucherFlowInfoDto.setKeepAccountDate(new Date());
        voucherFlowInfoDto.setContractNo(baseInfo.getOrganId());
        voucherFlowInfoDto.setDealerName(baseInfo.getOrganName());

        //根据经销商code获取Channel
        ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getChannelByDealerId(baseInfo.getOrganId());
        if (channelBaseInfo != null) {
            voucherFlowInfoDto.setChannelId(String.valueOf(channelBaseInfo.getChannelId()));
            voucherFlowInfoDto.setCustNo(String.valueOf(channelBaseInfo.getChannelId()));
        }
        mqMessageQueueLogService.saveMqMessage(voucherFlowInfoDto);
        //尾差处理
        if (baseInfo.getWaitExcludeSum() != null && baseInfo.getWaitExcludeSum().compareTo(baseInfo.getExcludeTaxAmount()) != 0) {
            VoucherFlowInfoDto tailDto = new VoucherFlowInfoDto();
            tailDto.setBuriedPointNo(VoucherBuriedPointNo.writeOffTail);
            tailDto.setTransNo(baseInfo.getId().toString());
            tailDto.setKeepAccountDate(new Date());
            tailDto.setContractNo(baseInfo.getOrganId());
            tailDto.setDealerName(baseInfo.getOrganName());
            if (channelBaseInfo != null) {
                tailDto.setChannelId(String.valueOf(channelBaseInfo.getChannelId()));
                tailDto.setCustNo(String.valueOf(channelBaseInfo.getChannelId()));
            }
            mqMessageQueueLogService.saveMqMessage(tailDto);
        }
    }
}

