package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 服务费清账模块
 */
@TableName(value = "write_off_clear_info")
@Data
public class WriteOffClearInfo extends BaseEntity<WriteOffClearInfo> {

    /**
     * 核销项编号
     */
    private String applyNo;
    /**
     * 核销项账期
     */
    private String writeOffMonth;
    /**
     * 业务模式
     */
    private String writeOffType;
    /**
     * 经销商编号
     */
    private String channelCode;
    /**
     * 经销商名称
     */
    private String channelFullName;
    /**
     * 总服务费
     */
    private BigDecimal serviceCharge;
    /**
     * 暂估凭证号
     */
    private String feeVoucherNo;
    /**
     * 暂估转应付凭证号
     */
    private String invoiceVoucherNo;
    /**
     * 付款凭证号
     */
    private String payVoucherNo;
    /**
     * 尾差凭证号
     */
    private String tailVoucherNo;
    /**
     * 暂估清账状态
     */
    private String feeClearStatus;
    /**
     * 应付清账状态
     */
    private String payClearStatus;
}
