package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.common.feign.FinanceProductFeign;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.constant.WriteOffConstant;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ContractDetailManageSearchDTO;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffApportionDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffApportionInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalContractDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffChannelGroupDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffDeductDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffMonthConfig;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffRule;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffRuleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ApportionEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.CalRuleEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.OverdueStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.feign.WriteOffFeign;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.ContractDetailManageMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffRuleMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApportionDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApportionInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalContractDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffChannelGroupDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffDeductDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffContractDetailManageService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffMonthConfigService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.AdvanceResultDTO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.BasicContractInfoVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffRuleExportVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.SettleContractDetailVO;
import com.ruicar.afs.cloud.bizcommon.voucher.service.MqMessageQueueLogService;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.uid.AfsSequenceGenerator;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.constant.VoucherBuriedPointNo;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.voucher.VoucherFlowInfoDto;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleResult;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Service
public class WriteOffRuleServiceImpl extends ServiceImpl<WriteOffRuleMapper, WriteOffRule> implements WriteOffRuleService {

    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final WriteOffContractDetailManageService writeOffContractDetailManageService;
    private final ContractDetailManageMapper contractDetailManageMapper;
    private final WriteOffRuleDetailService writeOffRuleDetailService;
    private final AfsSequenceGenerator afsSequenceGenerator;
    private final CaseContractInfoService caseContractInfoService;
    private final ChannelRiskInfoService channelRiskInfoService;
    private final WriteOffApportionInfoService writeOffApportionInfoService;
    private final MqMessageQueueLogService mqMessageQueueLogService;
    private final WriteOffFeign writeOffFeign;
    private final WriteOffApportionDetailService writeOffApportionDetailService;
    private final FinanceProductFeign productFeign;
    private final WriteOffChannelGroupDetailService writeOffChannelGroupDetailService;
    private final WriteOffDeductDetailService writeOffDeductDetailService;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final WriteOffMonthConfigService writeOffMonthConfigService;
    private final WriteOffCapitalContractDetailService writeOffCapitalContractDetailService;

    /**
     * 服务费计算定时任务
     */
    @Override
    public IResponse writeOffCalRuleJob(String param, boolean zjdFlag, Date zjdStartTime, Date zjdEndTime, Long ruleGroup) {
        log.info("服务费计算{}-{}-{}-{}-{}", param, zjdFlag, zjdStartTime, zjdEndTime, ruleGroup);
        WriteOffTypeEnum writeOffType = zjdFlag ? WriteOffTypeEnum.ZJD : WriteOffTypeEnum.FD;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        Date yesterday = null;
        Date today = null;
        String billingPeriod = "";
        if (zjdFlag) {
            if (StrUtil.isNotBlank(param)) {
                WriteOffMonthConfig monthConfig = writeOffMonthConfigService.getOne(Wrappers.<WriteOffMonthConfig>lambdaQuery()
                        .eq(WriteOffMonthConfig::getRuleName, param)
                        .eq(WriteOffMonthConfig::getStatus, StatusEnum.YES.getCode()));
                Assert.isTrue(monthConfig != null, "没有生效的周期配置");
                Calendar startCalendar = Calendar.getInstance();
                Calendar endCalendar = Calendar.getInstance();
                int dayOfMonth = DateUtil.dayOfMonth(new Date());
                if (monthConfig.getNextEndDay() != null) {
                    if (dayOfMonth > monthConfig.getNextEndDay()) {
                        startCalendar.add(Calendar.MONTH, -1);
                        startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                        endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getNextEndDay() + 1);
                    } else {
                        startCalendar.add(Calendar.MONTH, -2);
                        startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                        endCalendar.add(Calendar.MONTH, -1);
                        endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getNextEndDay() + 1);
                    }
                } else {
                    Assert.isTrue(monthConfig.getThisEndDay() != null, "[" + monthConfig.getRuleName() + "]周期配置的时间异常");
                    if (dayOfMonth > monthConfig.getThisEndDay()) {
                        startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                        endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getThisEndDay() + 1);
                    } else {
                        startCalendar.add(Calendar.MONTH, -1);
                        startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                        endCalendar.add(Calendar.MONTH, -1);
                        endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getThisEndDay() + 1);
                    }
                }
                yesterday = DateUtil.beginOfDay(startCalendar.getTime());
                today = DateUtil.beginOfDay(endCalendar.getTime());
                ruleGroup = monthConfig.getId();
            } else {
                Assert.isTrue(zjdStartTime != null && zjdEndTime != null && ruleGroup != null, "租金贷计算参数异常");
                yesterday = zjdStartTime;
                today = zjdEndTime;
            }
            billingPeriod = DateUtil.dayOfMonth(today) > 16 ? dateFormat.format(today) : dateFormat.format(yesterday);
        } else {
            //获取上月的时间段
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MONTH, -1);
            c.set(Calendar.DAY_OF_MONTH, 1);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MILLISECOND, 0);
            yesterday = c.getTime();
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            today = calendar.getTime();
            String startDate = "";
            String endDate = "";
            if (StrUtil.isNotBlank(param)) {
                try {
                    YearMonth yearMonth = YearMonth.parse(param);
                    // 获取起始日期（第一天）
                    startDate = String.valueOf(yearMonth.atDay(1)) + " 00-00-00";
                    // 获取下月第一天日期
                    endDate = String.valueOf(yearMonth.plusMonths(1).atDay(1)) + " 00-00-00";
                    yesterday = format.parse(startDate);
                    today = format.parse(endDate);
                } catch (Exception e) {
                    log.error("服务费计算定时任务出现异常:" + e.getMessage());
                    throw new RuntimeException(e);
                }
            }
            billingPeriod = dateFormat.format(yesterday);
        }
        log.info("服务费计算定时任务,查询时间段{}<----->{}", format.format(yesterday), format.format(today));
        log.info("服务费计算定时任务,本期:{}", billingPeriod);
        String seasonFlag = "";
        int month = DateUtil.month(DateUtil.date()) + 1;
        if (month == 2 || month == 3 || month == 4) {
            seasonFlag = "Q1";
        } else if (month == 5 || month == 6 || month == 7) {
            seasonFlag = "Q2";
        } else if (month == 8 || month == 9 || month == 10) {
            seasonFlag = "Q3";
        } else {
            seasonFlag = "Q4";
        }
        ContractDetailManageSearchDTO searchDTO = new ContractDetailManageSearchDTO();
        searchDTO.setLoanStart(yesterday);
        searchDTO.setLoanEnd(today);
        List<BasicContractInfoVo> basicContractInfoVoList = null;
        if (zjdFlag) {
            IResponse<List<BasicContractInfoVo>> zjdResponse = writeOffFeign.queryZjdWriteOff(searchDTO);
            Assert.isTrue("0000".equals(zjdResponse.getCode()), "租金贷合同查询异常");
            basicContractInfoVoList = zjdResponse.getData();
        } else {
            IResponse<List<BasicContractInfoVo>> response = writeOffFeign.queryWriteOff(searchDTO);
            Assert.isTrue("0000".equals(response.getCode()), "合同查询异常");
            basicContractInfoVoList = response.getData();
        }
        if (CollectionUtil.isEmpty(basicContractInfoVoList)) {
            return IResponse.fail("上月不存在能生成服务费的合同！");
        }
        List<String> conList = basicContractInfoVoList.stream().map(BasicContractInfoVo::getContractNo).toList();
        searchDTO.setContractNoList(conList);
        Map<String, BasicContractInfoVo> map = new HashMap<>();
        for (BasicContractInfoVo infoVo : basicContractInfoVoList) {
            map.put(infoVo.getContractNo(), infoVo);
        }
        List<WriteOffContractDetailManage> detailList = contractDetailManageMapper.selectAllDetailManage(searchDTO);
        // 测试环境专属 - 数据过滤
        detailList = detailList.stream().filter(e->StrUtil.isNotBlank(e.getChannelCode())).toList();

        Set<Long> productIdSet = new HashSet<>();
        for (WriteOffContractDetailManage detailManage : detailList) {
            productIdSet.add(Long.parseLong(detailManage.getProductId()));
            BasicContractInfoVo contractInfoVo = map.get(detailManage.getContractNo());
            contractInfoVo.setBelongingCapital(detailManage.getBelongingCapital());
            detailManage.setContractStatus(contractInfoVo.getContractStatus());
            detailManage.setLoanDate(contractInfoVo.getLoanDate());
            detailManage.setEndDate(contractInfoVo.getEndDate());
            detailManage.setCapitalReturnTime(contractInfoVo.getCapitalReturnTime());
        }
        if (detailList.size() <= 0) {
            log.error("服务费计算定时任务，合同信息为空！");
            return IResponse.fail("没有能生成服务费的合同！");
        }
        //获取产品编号
        IResponse<Map<Long, String>> mapIResponse = productFeign.getProductNumber(productIdSet);
        Assert.isTrue("0000".equals(mapIResponse.getCode()), "获取产品编号异常");
        if (zjdFlag) {
            //计算周期规则
            Assert.isTrue(ruleGroup != null, "周期规则组不能为空");
            Long finalRuleGroup = ruleGroup;
            List<WriteOffContractDetailManage> manageList = detailList.stream().filter(detail -> this.runMonthRule(detail, mapIResponse.getData(), finalRuleGroup)).toList();
            if (manageList.size() <= 0) {
                log.error("服务费计算定时任务，没有符合周期规则的合同信息！");
                return IResponse.fail("没有能生成服务费的合同！");
            }
            detailList = manageList;
        }
        List<WriteOffRule> ruleList = this.list(Wrappers.<WriteOffRule>lambdaQuery()
                .eq(WriteOffRule::getStatus, StatusEnum.YES.getCode())
                .ne(WriteOffRule::getType, CalRuleEnum.EARLY_PAY.getCode()));
        Assert.isTrue(ruleList.size() > 0, "没有生效的服务费计算规则");
        boolean earlyPayFlag = this.list(Wrappers.<WriteOffRule>lambdaQuery()
                .eq(WriteOffRule::getStatus, StatusEnum.YES.getCode())
                .eq(WriteOffRule::getType, CalRuleEnum.EARLY_PAY.getCode())).size() > 0;
        log.info("是否有生效的提前结清规则{}", earlyPayFlag);
        //根据经销商分组
        Map<String, List<WriteOffContractDetailManage>> channelMap = detailList.stream().collect(Collectors.groupingBy(WriteOffContractDetailManage::getChannelCode));
        String finalSeasonFlag = seasonFlag;
        Map<String, String> conUniqueMap = new HashMap<>();
        //服务费经销商分组信息
        List<WriteOffChannelGroupDetail> groupDetailList = writeOffChannelGroupDetailService.list(Wrappers.<WriteOffChannelGroupDetail>lambdaQuery()
                .select(WriteOffChannelGroupDetail::getGroupId, WriteOffChannelGroupDetail::getGroupType, WriteOffChannelGroupDetail::getChannelCode)
                .eq(WriteOffChannelGroupDetail::getStatus, StatusEnum.YES.getCode()));
        Map<String, List<WriteOffChannelGroupDetail>> channelGroupMap = groupDetailList.stream().collect(Collectors.groupingBy(WriteOffChannelGroupDetail::getChannelCode));
        for (Map.Entry<String, List<WriteOffContractDetailManage>> entry : channelMap.entrySet()) {
            String key = entry.getKey();
            List<WriteOffContractDetailManage> value = entry.getValue();
            try {
                BigDecimal serviceCharge = BigDecimal.ZERO;
                for (WriteOffContractDetailManage detail : value) {
                    Assert.isTrue(StrUtil.isNotBlank(detail.getBelongingCapital()), "合同号{" + detail.getContractNo() + "}所属资方为空");
                    List<WriteOffRule> offRuleList = ruleList.stream().filter(k ->
                            Arrays.asList(k.getBelongingCapital().split(",")).contains(detail.getBelongingCapital())).toList();
                    Assert.isTrue(offRuleList.size() > 0, "合同号码:{" + detail.getContractNo() + "}没有配置生效的计算规则");
                    Assert.isTrue(!conUniqueMap.containsKey(detail.getContractNo()), "合同号码:{" + detail.getContractNo() + "}存在多个,不可重复");
                    conUniqueMap.put(detail.getContractNo(), "1");
                    JSONObject jsonObject = this.packageRuleAtom(detail, mapIResponse.getData(), channelGroupMap);
                    log.info("合同号码：{},计算服务费规则报文：{}", detail.getContractNo(), jsonObject);
                    RuleRunResult ruleRunResult = RuleHelper.runByGroup(jsonObject, "WriteOffCalRuleAtom", false, RuleRunEnum.PARALLEL, false);
                    boolean isHit = ruleRunResult.getHit();
                    Assert.isTrue(isHit, "合同号码:{" + detail.getContractNo() + "},计算服务费所有规则都未命中");
                    if (isHit) {
                        List<RuleResult> ruleResults = ruleRunResult.getResults();
                        Assert.isTrue(ruleResults.size() > 0, "合同号码:{" + detail.getContractNo() + "},计算服务费所有规则都未命中");
                        List<Long> ruleNoList = ruleResults.stream().map(k -> Long.valueOf(k.getRuleNo())).collect(Collectors.toList());
                        log.info("合同号码：{}，计算服务费命中规则ruleNos：{}", detail.getContractNo(), ruleNoList);
                        List<WriteOffRuleDetail> ruleDetailList = writeOffRuleDetailService.listByIds(ruleNoList);
                        for (WriteOffRule writeOffRule : offRuleList) {
                            CalRuleEnum calRuleEnum = CalRuleEnum.create(writeOffRule.getType());
                            List<WriteOffRuleDetail> details = ruleDetailList.stream().filter(k -> k.getWriteOffRuleId().equals(writeOffRule.getId())).collect(Collectors.toList());
                            if (details.size() == 0) {
                                throw new AfsBaseException("合同号码:{" + detail.getContractNo() + "}," + "[" + calRuleEnum.getDesc() + "]未命中规则");
                            }
                            if (details.size() >= 2) {
                                throw new AfsBaseException("合同号码:{" + detail.getContractNo() + "}," + "[" + calRuleEnum.getDesc() + "]命中多条规则");
                            }
                            switch (calRuleEnum) {
                                case PAY: {
                                    detail.setBackRate(details.get(0).getBackRate());
                                    break;
                                }
                                case FLAW: {
                                    detail.setFlawRate(details.get(0).getServeRate());
                                    break;
                                }
                                case CHANNEL: {
                                    detail.setChannelServeRate(details.get(0).getServeRate());
                                    break;
                                }
                                case CITY: {
                                    detail.setCityServeRate(details.get(0).getServeRate());
                                    break;
                                }
                                default: {
                                    throw new AfsBaseException("存在错误的计算规则");
                                }
                            }
                        }
                        //计算服务费，各计算规则相乘
                        BigDecimal backRate = BigDecimal.ONE;
                        BigDecimal flawRate = BigDecimal.ONE;
                        BigDecimal cityRate = BigDecimal.ONE;
                        BigDecimal channelRate = BigDecimal.ONE;
                        BigDecimal result = detail.getLoanAmt();
                        if (detail.getBackRate() != null) {
                            backRate = detail.getBackRate().divide(new BigDecimal("100"));
                        }
                        if (detail.getFlawRate() != null) {
                            flawRate = detail.getFlawRate().divide(new BigDecimal("100"));
                        }
                        if (detail.getCityServeRate() != null) {
                            cityRate = detail.getCityServeRate().divide(new BigDecimal("100"));
                        }
                        if (detail.getChannelServeRate() != null) {
                            channelRate = detail.getChannelServeRate().divide(new BigDecimal("100"));
                        }
                        result = result.multiply(backRate).multiply(flawRate).multiply(cityRate).multiply(channelRate).setScale(2, RoundingMode.HALF_UP);
                        detail.setServiceCharge(result);
                        serviceCharge = serviceCharge.add(result);
                    }
                }
                //核销项编号
                String generateApply = generateApply();
                // 提前结清扣罚金额计算
                AdvanceResultDTO settlement = null;
                if (earlyPayFlag) {
                    String channelId = value.get(0).getChannelId().toString();
                    IResponse<List<SettleContractDetailVO>> early = writeOffFeign.queryEarlyContract(channelId, zjdFlag);
                    Assert.isTrue("0000".equals(early.getCode()), "提前结清合同查询异常");
                    for (SettleContractDetailVO settleVO : early.getData()) {
                        BasicContractInfoVo infoVo = map.get(settleVO.getContractNo());
                        if (infoVo != null) {
                            settleVO.setBelongingCapital(infoVo.getBelongingCapital());
                        }
                    }
                    settlement = getEarlySettlementOfDeductionAmount(value, early.getData(), billingPeriod, zjdFlag);
                    log.info("经销商{},提前结清合同处理结果{}", key, JSON.toJSONString(settlement));
                }
                List<WriteOffContractDetailManage> notReceivedValue = new ArrayList<>();
                if (zjdFlag) {
                    List<WriteOffContractDetailManage> receivedValue = new ArrayList<>();
                    //租金贷合同需要已应收对账才能生成核销项，未对账的先保留计算结果
                    Map<String, List<WriteOffCapitalContractDetail>> capitalMap = writeOffCapitalContractDetailService.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                                    .eq(WriteOffCapitalContractDetail::getChannelCode, key)
                                    .eq(WriteOffCapitalContractDetail::getWriteOffFlag, "0"))
                            .stream().collect(Collectors.groupingBy(WriteOffCapitalContractDetail::getContractNo));
                    for (WriteOffContractDetailManage manage : value) {
                        List<WriteOffCapitalContractDetail> capitalContractDetails = capitalMap.get(manage.getContractNo());
                        if (CollUtil.isNotEmpty(capitalContractDetails) && "1".equals(capitalContractDetails.get(0).getReceiveFinishFlag())) {
                            receivedValue.add(manage);
                        } else {
                            notReceivedValue.add(manage);
                        }
                    }
                    //前期计算并已对账完成但未生成核销项的,此时一起生成核销项
                    for (WriteOffContractDetailManage manage : writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                            .eq(WriteOffContractDetailManage::getChannelCode, key)
                            .in(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.NOT_FD_CODE_SET)
                            .eq(WriteOffContractDetailManage::getWriteOffFlag, "0"))) {
                        List<WriteOffCapitalContractDetail> capitalContractDetails = capitalMap.get(manage.getContractNo());
                        if (CollUtil.isNotEmpty(capitalContractDetails) && "1".equals(capitalContractDetails.get(0).getReceiveFinishFlag())) {
                            receivedValue.add(manage);
                        }
                    }
                    value = receivedValue;
                }
                serviceCharge = value.stream().map(WriteOffContractDetailManage::getServiceCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                serviceCharge = serviceCharge == null ? BigDecimal.ZERO : serviceCharge;
                SpringContextHolder.getBean(WriteOffRuleService.class).transactionalSave(value, generateApply, serviceCharge, key, billingPeriod, finalSeasonFlag, settlement, null, null, writeOffType, notReceivedValue);
            } catch (Exception e) {
                //保存合同信息
                Set<String> codeSet = zjdFlag ? BelongingCapitalEnum.NOT_FD_CODE_SET : Set.of(BelongingCapitalEnum.FD.getCode());
                List<WriteOffContractDetailManage> list = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                        .select(WriteOffContractDetailManage::getId)
                        .eq(WriteOffContractDetailManage::getWriteOffMonth, billingPeriod)
                        .eq(WriteOffContractDetailManage::getWriteOffFlag, "2")
                        .eq(WriteOffContractDetailManage::getChannelCode, key)
                        .in(WriteOffContractDetailManage::getBelongingCapital, codeSet));
                if (list.size() > 0) {
                    writeOffContractDetailManageService.removeBatchByIds(list);
                }
                for (WriteOffContractDetailManage detailManage : value) {
                    detailManage.setId(null);
                    detailManage.setErrMsg(e.getMessage());
                    detailManage.setBaseInfoApply(null);
                    detailManage.setWriteOffFlag("2");
                    detailManage.setWriteOffMonth(billingPeriod);
                }
                writeOffContractDetailManageService.saveBatch(value);
                log.error("【服务费计算】出现异常，经销商：{}-{}，异常内容：{}", key, writeOffType.getCode(), e);
            }
        }
        //本月无服务费,有待扣罚,需要生成一个扣罚核销项
        List<ChannelBaseInfo> channelBaseInfoList = channelBaseInfoService.list(Wrappers.<ChannelBaseInfo>lambdaQuery()
                .select(ChannelBaseInfo::getChannelCode, ChannelBaseInfo::getChannelId, ChannelBaseInfo::getChannelFullName, ChannelBaseInfo::getChannelBelong)
                .notIn(ChannelBaseInfo::getChannelCode, channelMap.keySet()));
        for (ChannelBaseInfo channelBaseInfo : channelBaseInfoList) {
            try {
                WriteOffBaseInfo one = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                        .select(WriteOffBaseInfo::getId)
                        .eq(WriteOffBaseInfo::getOrganId, channelBaseInfo.getChannelCode())
                        .eq(WriteOffBaseInfo::getWriteOffMonth, billingPeriod)
                        .eq(WriteOffBaseInfo::getWriteOffType, writeOffType.getCode())
                        .isNotNull(WriteOffBaseInfo::getApplyNo));
                if (one != null) {
                    log.info("经销商：{}-{}，本月已成功生成核销项，跳过", channelBaseInfo.getChannelCode(), writeOffType.getDesc());
                    continue;
                }
                writeOffBaseInfoService.remove(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                        .eq(WriteOffBaseInfo::getOrganId, channelBaseInfo.getChannelCode())
                        .eq(WriteOffBaseInfo::getWriteOffMonth, billingPeriod)
                        .eq(WriteOffBaseInfo::getWriteOffType, writeOffType.getCode()));
                //核销项编号
                String generateApply = generateApply();
                String channelCode = channelBaseInfo.getChannelCode();
                // 提前结清扣罚金额计算
                AdvanceResultDTO settlement = null;
                if (earlyPayFlag) {
                    log.info("经销商：{},本月无服务费，提前结清扣罚金额计算开始", channelCode);
                    IResponse<List<SettleContractDetailVO>> early = writeOffFeign.queryEarlyContract(channelBaseInfo.getChannelId().toString(), zjdFlag);
                    Assert.isTrue("0000".equals(early.getCode()), "提前结清合同查询异常");
                    for (SettleContractDetailVO settleVO : early.getData()) {
                        BasicContractInfoVo infoVo = map.get(settleVO.getContractNo());
                        if (infoVo != null) {
                            settleVO.setBelongingCapital(infoVo.getBelongingCapital());
                        }
                    }
                    settlement = getEarlySettlementOfDeductionAmount(new ArrayList<>(), early.getData(), billingPeriod, zjdFlag);
                    log.info("经销商{},提前结清合同处理结果{}", channelCode, JSON.toJSONString(settlement));
                    if (settlement != null) {
                        Assert.isTrue(CollUtil.isEmpty(settlement.getCurDetails()), "经销商" + channelCode + "本月无服务费，本期不该有提前结清！");
                    }
                }
                List<WriteOffContractDetailManage> hxxDetailValue = new ArrayList<>();
                BigDecimal serverFee = BigDecimal.ZERO;
                if (zjdFlag) {
                    List<String> capitalRedNos = writeOffCapitalContractDetailService.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                                    .select(WriteOffCapitalContractDetail::getContractNo)
                                    .eq(WriteOffCapitalContractDetail::getChannelCode, channelCode)
                                    .eq(WriteOffCapitalContractDetail::getWriteOffFlag, "0")
                                    .eq(WriteOffCapitalContractDetail::getReceiveFinishFlag, "1"))
                            .stream().map(WriteOffCapitalContractDetail::getContractNo).toList();
                    if (capitalRedNos.size() > 0) {
                        hxxDetailValue = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                                .eq(WriteOffContractDetailManage::getWriteOffFlag, "0")
                                .in(WriteOffContractDetailManage::getContractNo, capitalRedNos)
                                .in(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.NOT_FD_CODE_SET));
                        serverFee = hxxDetailValue.stream().map(WriteOffContractDetailManage::getServiceCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        serverFee = serverFee == null ? BigDecimal.ZERO : serverFee;
                    }
                }
                SpringContextHolder.getBean(WriteOffRuleService.class).transactionalSave(hxxDetailValue, generateApply, serverFee, channelCode, billingPeriod, seasonFlag, settlement, channelBaseInfo.getChannelFullName(), channelBaseInfo.getChannelBelong(), writeOffType, new ArrayList<>());
            } catch (Exception e) {
                WriteOffBaseInfo errorBase = new WriteOffBaseInfo();
                errorBase.setOrganId(channelBaseInfo.getChannelCode());
                errorBase.setOrganName(channelBaseInfo.getChannelFullName());
                errorBase.setChannelBelong(channelBaseInfo.getChannelBelong());
                errorBase.setWriteOffMonth(billingPeriod);
                errorBase.setWriteOffStatus(WriteOffStatusEnum.PUNISH_WAIT);
                errorBase.setWriteOffType(writeOffType.getCode());
                if (zjdFlag) {
                    errorBase.setWriteOffExplain("本月无新增合同，生成租金贷核销项异常！");
                } else {
                    errorBase.setWriteOffExplain("本月无新增合同，生成待扣罚出现异常！");
                }
                writeOffBaseInfoService.save(errorBase);
                log.error("【生成待扣罚】出现异常，经销商编号：{}，异常内容：{}", channelBaseInfo.getChannelCode(), e);
            }
        }
        return IResponse.success("执行成功");
    }

    /**
     * 直营店服务费分摊定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IResponse directWriteOffApportionJob() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar last = Calendar.getInstance();
        last.add(Calendar.MONTH, -1);
        String lastPeriod = dateFormat.format(last.getTime());
        //分摊必须在服务费计算完之后
        long baseCount = writeOffBaseInfoService.count(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getWriteOffType, WriteOffTypeEnum.FD.getCode())
                .eq(WriteOffBaseInfo::getWriteOffMonth, lastPeriod));
        if (baseCount <= 0) {
            log.warn("服务费未计算，不能分摊");
            return IResponse.fail("服务费未计算，不能分摊");
        }
        long count = writeOffContractDetailManageService.count(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .eq(WriteOffContractDetailManage::getChannelBelong, ChannelBelongEnum.DIRECT.getKey())
                .eq(WriteOffContractDetailManage::getWriteOffMonth, lastPeriod)
                .eq(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.FD.getCode())
                .isNull(WriteOffContractDetailManage::getApportionFlag));
        if (count >= 1) {
            log.warn("[直营店]上月服务费还没全部审核完，未到分摊时机");
            return IResponse.fail("[直营店]上月服务费还没全部审核完，未到分摊时机");
        }
        List<String> flagList = new ArrayList<>();
        flagList.add(ApportionEnum.APPORTIONWAIT.getCode());
        flagList.add(ApportionEnum.APPORTIONING.getCode());
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date end = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        log.info("[直营店]服务费分摊定时任务,查询到期时间{}", format.format(end));
        //当前期
        String nowStage = dateFormat.format(new Date());
        WriteOffApportionInfo apportionInfo = null;
        apportionInfo = writeOffApportionInfoService.getOne(Wrappers.<WriteOffApportionInfo>lambdaQuery()
                .eq(WriteOffApportionInfo::getWriteOffMonth, nowStage)
                .eq(WriteOffApportionInfo::getChannelBelong, ChannelBelongEnum.DIRECT.getKey())
                .eq(WriteOffApportionInfo::getSource, "内部"));
        if (apportionInfo != null) {
            log.info("[直营店]服务费本月分摊已完成，不重复执行");
            return IResponse.fail("[直营店]服务费本月分摊已完成，请不要重复执行");
        }
        List<WriteOffContractDetailManage> list = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .eq(WriteOffContractDetailManage::getChannelBelong, ChannelBelongEnum.DIRECT.getKey())
                .in(WriteOffContractDetailManage::getApportionFlag, flagList)
                .eq(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.FD.getCode())
                .ge(WriteOffContractDetailManage::getEndDate, end));
        if (list.size() <= 0) {
            log.warn("[直营店]不存在分摊中的合同");
            return IResponse.fail("[直营店]不存在满足分摊的合同");
        }
        BigDecimal amountSum = BigDecimal.ZERO;
        BigDecimal excludeSum = BigDecimal.ZERO;
        BigDecimal residueSum = BigDecimal.ZERO;
        List<String> allContractNos = list.stream().map(WriteOffContractDetailManage::getContractNo).toList();
        ContractDetailManageSearchDTO searchDTO = new ContractDetailManageSearchDTO();
        searchDTO.setContractNoList(allContractNos);
        IResponse<List<BasicContractInfoVo>> response = writeOffFeign.queryWriteAdvance(searchDTO);
        Assert.isTrue("0000".equals(response.getCode()), "合同查询异常");
        List<BasicContractInfoVo> advanceContracts = response.getData();
        //呆账核销需一次性分摊
        IResponse<List<String>> badResponse = writeOffFeign.queryWriteBad(searchDTO);
        Assert.isTrue("0000".equals(badResponse.getCode()), "呆账核销合同查询异常");
        List<String> badContractNoList = badResponse.getData();
        log.info("[直营店]呆账核销合同号{}", badContractNoList);
        badContractNoList = badContractNoList == null ? new ArrayList<>() : badContractNoList;
        List<WriteOffApportionDetail> apportionDetailList = new ArrayList<>();
        if (CollectionUtil.isEmpty(advanceContracts)) {
            log.info("[直营店]不存在提前结清的合同");
            //分摊后逻辑
            for (WriteOffContractDetailManage detail : list) {
                Integer apportionNum = detail.getApportionNum() == null ? 1 : detail.getApportionNum() + 1;
                //判断是否是合同最后一期
                String endStage = dateFormat.format(detail.getEndDate());
                //本月分摊金额
                BigDecimal nowAmount = null;
                if (nowStage.equals(endStage) || badContractNoList.contains(detail.getContractNo())) {
                    //是最后一期或者是呆账
                    amountSum = amountSum.add(detail.getResidueApportionAmount());
                    nowAmount = detail.getResidueApportionAmount().add(BigDecimal.ZERO);
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getResidueApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                    detail.setApportionNum(apportionNum);
                    detail.setResidueApportionAmount(BigDecimal.ZERO);
                } else {
                    amountSum = amountSum.add(detail.getApportionAmount());
                    nowAmount = detail.getApportionAmount().add(BigDecimal.ZERO);
                    detail.setResidueApportionAmount(detail.getResidueApportionAmount().subtract(detail.getApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONING.getCode());
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getApportionAmount()));
                    detail.setApportionNum(apportionNum);
                }
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(detail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(detail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(list);
            //计算本月分摊总金额
            excludeSum = list.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            residueSum = list.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            apportionInfo = new WriteOffApportionInfo();
            apportionInfo.setApportionTotalAmount(amountSum);
            apportionInfo.setChannelBelong(ChannelBelongEnum.DIRECT.getKey());
            apportionInfo.setWriteOffMonth(nowStage);
            apportionInfo.setServiceTotalAmount(excludeSum);
            apportionInfo.setResidueTotalAmount(residueSum);
            apportionInfo.setVoucherNo(null);
            apportionInfo.setSource("内部");
            writeOffApportionInfoService.save(apportionInfo);
            for (WriteOffApportionDetail apportionDetail : apportionDetailList) {
                apportionDetail.setId(null);
                apportionDetail.setApportionId(apportionInfo.getId());
            }
            writeOffApportionDetailService.saveBatch(apportionDetailList);
        } else {
            log.info("[直营店]存在提前结清的合同");
            List<String> advanceContractNos = advanceContracts.stream().map(BasicContractInfoVo::getContractNo).toList();
            Map<String, ContractStatusEnum> contractStatusEnumMap = new HashMap<>();
            for (BasicContractInfoVo advanceContract : advanceContracts) {
                contractStatusEnumMap.put(advanceContract.getContractNo(), advanceContract.getContractStatus());
            }
            //提前结清的合同
            List<WriteOffContractDetailManage> advanceDetails = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .in(WriteOffContractDetailManage::getContractNo, advanceContractNos));
            List<String> noAdvanceContractNos = allContractNos.stream().filter(k -> !advanceContractNos.contains(k)).toList();
            //不是提前结清的合同
            List<WriteOffContractDetailManage> noAdvanceDetails = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .in(WriteOffContractDetailManage::getContractNo, noAdvanceContractNos));
            //提前结清需要一次性确认
            //分摊后逻辑
            for (WriteOffContractDetailManage advanceDetail : advanceDetails) {
                Integer apportionNum = advanceDetail.getApportionNum() == null ? 1 : advanceDetail.getApportionNum() + 1;
                amountSum = amountSum.add(advanceDetail.getResidueApportionAmount());
                //本月分摊金额
                BigDecimal nowAmount = advanceDetail.getResidueApportionAmount().add(BigDecimal.ZERO);
                advanceDetail.setContractStatus(contractStatusEnumMap.get(advanceDetail.getContractNo()));
                advanceDetail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                advanceDetail.setApportionedAmount(advanceDetail.getApportionedAmount().add(advanceDetail.getResidueApportionAmount()));
                advanceDetail.setResidueApportionAmount(BigDecimal.ZERO);
                advanceDetail.setApportionNum(apportionNum);
                //分摊明细表
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(advanceDetail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(advanceDetail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(advanceDetails);
            for (WriteOffContractDetailManage detail : noAdvanceDetails) {
                Integer apportionNum = detail.getApportionNum() == null ? 1 : detail.getApportionNum() + 1;
                //判断是否是合同最后一期
                String endStage = dateFormat.format(detail.getEndDate());
                //本月分摊金额
                BigDecimal nowAmount = null;
                if (nowStage.equals(endStage) || badContractNoList.contains(detail.getContractNo())) {
                    //是最后一期或者呆账
                    amountSum = amountSum.add(detail.getResidueApportionAmount());
                    nowAmount = detail.getResidueApportionAmount().add(BigDecimal.ZERO);
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getResidueApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                    detail.setResidueApportionAmount(BigDecimal.ZERO);
                    detail.setApportionNum(apportionNum);
                } else {
                    amountSum = amountSum.add(detail.getApportionAmount());
                    nowAmount = detail.getApportionAmount().add(BigDecimal.ZERO);
                    detail.setResidueApportionAmount(detail.getResidueApportionAmount().subtract(detail.getApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONING.getCode());
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getApportionAmount()));
                    detail.setApportionNum(apportionNum);
                }
                //分摊明细表
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(detail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(detail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(noAdvanceDetails);
            //本月分摊总金额
            excludeSum = advanceDetails.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            residueSum = advanceDetails.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal noAdvanceExcluedeSum = noAdvanceDetails.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal noAdvanceResidueSum = noAdvanceDetails.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            excludeSum = excludeSum.add(noAdvanceExcluedeSum);
            residueSum = residueSum.add(noAdvanceResidueSum);
            apportionInfo = new WriteOffApportionInfo();
            apportionInfo.setApportionTotalAmount(amountSum);
            apportionInfo.setChannelBelong(ChannelBelongEnum.DIRECT.getKey());
            apportionInfo.setWriteOffMonth(nowStage);
            apportionInfo.setServiceTotalAmount(excludeSum);
            apportionInfo.setResidueTotalAmount(residueSum);
            apportionInfo.setVoucherNo(null);
            apportionInfo.setSource("内部");
            writeOffApportionInfoService.save(apportionInfo);
            for (WriteOffApportionDetail apportionDetail : apportionDetailList) {
                apportionDetail.setId(null);
                apportionDetail.setApportionId(apportionInfo.getId());
            }
            writeOffApportionDetailService.saveBatch(apportionDetailList);
        }
        //凭证埋点
        WriteOffContractDetailManage detailOne = list.get(0);
        saveServiceFeeData(detailOne.getChannelCode(), detailOne.getChannelId(), apportionInfo.getId().toString(), detailOne.getChannelFullName());
        return IResponse.success("执行成功");
    }

    /**
     * 社会店服务费分摊定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IResponse spWriteOffApportionJob(String param) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        String lastPeriod = "";
        if (StrUtil.isNotBlank(param)) {
            lastPeriod = param;
        } else {
            Calendar last = Calendar.getInstance();
            last.add(Calendar.MONTH, -1);
            lastPeriod = dateFormat.format(last.getTime());
        }
        //分摊必须在服务费计算完之后
        long baseCount = writeOffBaseInfoService.count(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getWriteOffType, WriteOffTypeEnum.FD.getCode())
                .eq(WriteOffBaseInfo::getWriteOffMonth, lastPeriod));
        if (baseCount <= 0) {
            log.warn("服务费未计算，不能分摊");
            return IResponse.fail("服务费未计算，不能分摊");
        }
        long count = writeOffContractDetailManageService.count(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .eq(WriteOffContractDetailManage::getChannelBelong, ChannelBelongEnum.SP.getKey())
                .eq(WriteOffContractDetailManage::getWriteOffMonth, lastPeriod)
                .eq(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.FD.getCode())
                .isNull(WriteOffContractDetailManage::getApportionFlag));
        if (count >= 1) {
            log.warn("[社会店]上月服务费还没全部审核完，未到分摊时机");
            return IResponse.fail("[社会店]上月服务费还没全部审核完，未到分摊时机");
        }
        List<String> flagList = new ArrayList<>();
        flagList.add(ApportionEnum.APPORTIONWAIT.getCode());
        flagList.add(ApportionEnum.APPORTIONING.getCode());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        Date end = null;
        if (StrUtil.isNotBlank(param)) {
            YearMonth yearMonth = YearMonth.parse(param);
            // 获取下月第一天日期
            String endDate = String.valueOf(yearMonth.plusMonths(1).atDay(1))+" 00-00-00";
            try {
                end = format.parse(endDate);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            end = calendar.getTime();
        }
        log.info("[社会店]服务费分摊定时任务,查询到期时间{}", format.format(end));
        //当前期
        String nowStage = "";
        if (StrUtil.isNotBlank(param)) {
            YearMonth yearMonth = YearMonth.parse(param);
            // 获取下月第一天日期
            String nextMonth = String.valueOf(yearMonth.plusMonths(1).atDay(1)) + " 00-00-00";
            Date parse = null;
            try {
                parse = format.parse(nextMonth);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            nowStage = dateFormat.format(parse);
        } else {
            nowStage = dateFormat.format(new Date());
        }
        log.info("上期-{}，本期-{}", lastPeriod, nowStage);
        WriteOffApportionInfo apportionInfo = null;
        apportionInfo = writeOffApportionInfoService.getOne(Wrappers.<WriteOffApportionInfo>lambdaQuery()
                .eq(WriteOffApportionInfo::getWriteOffMonth, nowStage)
                .eq(WriteOffApportionInfo::getChannelBelong, ChannelBelongEnum.SP.getKey())
                .eq(WriteOffApportionInfo::getSource, "内部"));
        if (apportionInfo != null) {
            log.info("[社会店]服务费本月分摊已完成，不重复执行");
            return IResponse.fail("[社会店]服务费本月分摊已完成，请不要重复执行");
        }
        List<WriteOffContractDetailManage> list = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .eq(WriteOffContractDetailManage::getChannelBelong, ChannelBelongEnum.SP.getKey())
                .in(WriteOffContractDetailManage::getApportionFlag, flagList)
                .eq(WriteOffContractDetailManage::getBelongingCapital, BelongingCapitalEnum.FD.getCode())
                .ge(WriteOffContractDetailManage::getEndDate, end));
        if (list.size() <= 0) {
            log.warn("[社会店]不存在分摊中的合同");
            return IResponse.fail("[社会店]不存在满足分摊的合同");
        }
        BigDecimal amountSum = BigDecimal.ZERO;
        BigDecimal excludeSum = BigDecimal.ZERO;
        BigDecimal residueSum = BigDecimal.ZERO;
        List<String> allContractNos = list.stream().map(WriteOffContractDetailManage::getContractNo).toList();
        ContractDetailManageSearchDTO searchDTO = new ContractDetailManageSearchDTO();
        searchDTO.setContractNoList(allContractNos);
        IResponse<List<BasicContractInfoVo>> response = writeOffFeign.queryWriteAdvance(searchDTO);
        Assert.isTrue("0000".equals(response.getCode()), "合同查询异常");
        List<BasicContractInfoVo> advanceContracts = response.getData();
        //呆账核销需一次性分摊
        IResponse<List<String>> badResponse = writeOffFeign.queryWriteBad(searchDTO);
        Assert.isTrue("0000".equals(badResponse.getCode()), "呆账核销合同查询异常");
        List<String> badContractNoList = badResponse.getData();
        log.info("[社会店]呆账核销合同号{}", badContractNoList);
        badContractNoList = badContractNoList == null ? new ArrayList<>() : badContractNoList;
        List<WriteOffApportionDetail> apportionDetailList = new ArrayList<>();
        if (CollectionUtil.isEmpty(advanceContracts)) {
            log.info("[社会店]不存在提前结清的合同");
            //分摊后逻辑
            for (WriteOffContractDetailManage detail : list) {
                Integer apportionNum = detail.getApportionNum() == null ? 1 : detail.getApportionNum() + 1;
                //判断是否是合同最后一期
                String endStage = dateFormat.format(detail.getEndDate());
                //本月分摊金额
                BigDecimal nowAmount = null;
                if (nowStage.equals(endStage) || badContractNoList.contains(detail.getContractNo())) {
                    //是最后一期或者呆账
                    amountSum = amountSum.add(detail.getResidueApportionAmount());
                    nowAmount = detail.getResidueApportionAmount().add(BigDecimal.ZERO);
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getResidueApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                    detail.setApportionNum(apportionNum);
                    detail.setResidueApportionAmount(BigDecimal.ZERO);
                } else {
                    amountSum = amountSum.add(detail.getApportionAmount());
                    nowAmount = detail.getApportionAmount().add(BigDecimal.ZERO);
                    detail.setResidueApportionAmount(detail.getResidueApportionAmount().subtract(detail.getApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONING.getCode());
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getApportionAmount()));
                    detail.setApportionNum(apportionNum);
                }
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(detail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(detail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(list);
            //计算本月分摊总金额
            excludeSum = list.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            residueSum = list.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            apportionInfo = new WriteOffApportionInfo();
            apportionInfo.setApportionTotalAmount(amountSum);
            apportionInfo.setChannelBelong(ChannelBelongEnum.SP.getKey());
            apportionInfo.setWriteOffMonth(nowStage);
            apportionInfo.setServiceTotalAmount(excludeSum);
            apportionInfo.setResidueTotalAmount(residueSum);
            apportionInfo.setVoucherNo(null);
            apportionInfo.setSource("内部");
            writeOffApportionInfoService.save(apportionInfo);
            for (WriteOffApportionDetail apportionDetail : apportionDetailList) {
                apportionDetail.setId(null);
                apportionDetail.setApportionId(apportionInfo.getId());
            }
            writeOffApportionDetailService.saveBatch(apportionDetailList);
        } else {
            log.info("[社会店]存在提前结清的合同");
            List<String> advanceContractNos = advanceContracts.stream().map(BasicContractInfoVo::getContractNo).toList();
            Map<String, ContractStatusEnum> contractStatusEnumMap = new HashMap<>();
            for (BasicContractInfoVo advanceContract : advanceContracts) {
                contractStatusEnumMap.put(advanceContract.getContractNo(), advanceContract.getContractStatus());
            }
            //提前结清的合同
            List<WriteOffContractDetailManage> advanceDetails = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .in(WriteOffContractDetailManage::getContractNo, advanceContractNos));
            List<String> noAdvanceContractNos = allContractNos.stream().filter(k -> !advanceContractNos.contains(k)).toList();
            //不是提前结清的合同
            List<WriteOffContractDetailManage> noAdvanceDetails = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .in(WriteOffContractDetailManage::getContractNo, noAdvanceContractNos));
            //提前结清需要一次性确认
            //分摊后逻辑
            for (WriteOffContractDetailManage advanceDetail : advanceDetails) {
                Integer apportionNum = advanceDetail.getApportionNum() == null ? 1 : advanceDetail.getApportionNum() + 1;
                amountSum = amountSum.add(advanceDetail.getResidueApportionAmount());
                //本月分摊金额
                BigDecimal nowAmount = advanceDetail.getResidueApportionAmount().add(BigDecimal.ZERO);
                advanceDetail.setContractStatus(contractStatusEnumMap.get(advanceDetail.getContractNo()));
                advanceDetail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                advanceDetail.setApportionedAmount(advanceDetail.getApportionedAmount().add(advanceDetail.getResidueApportionAmount()));
                advanceDetail.setResidueApportionAmount(BigDecimal.ZERO);
                advanceDetail.setApportionNum(apportionNum);
                //分摊明细表
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(advanceDetail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(advanceDetail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(advanceDetails);
            for (WriteOffContractDetailManage detail : noAdvanceDetails) {
                Integer apportionNum = detail.getApportionNum() == null ? 1 : detail.getApportionNum() + 1;
                //判断是否是合同最后一期
                String endStage = dateFormat.format(detail.getEndDate());
                //本月分摊金额
                BigDecimal nowAmount = null;
                if (nowStage.equals(endStage) || badContractNoList.contains(detail.getContractNo())) {
                    //是最后一期或者呆账
                    amountSum = amountSum.add(detail.getResidueApportionAmount());
                    nowAmount = detail.getResidueApportionAmount().add(BigDecimal.ZERO);
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getResidueApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONEND.getCode());
                    detail.setResidueApportionAmount(BigDecimal.ZERO);
                    detail.setApportionNum(apportionNum);
                } else {
                    amountSum = amountSum.add(detail.getApportionAmount());
                    nowAmount = detail.getApportionAmount().add(BigDecimal.ZERO);
                    detail.setResidueApportionAmount(detail.getResidueApportionAmount().subtract(detail.getApportionAmount()));
                    detail.setApportionFlag(ApportionEnum.APPORTIONING.getCode());
                    detail.setApportionedAmount(detail.getApportionedAmount().add(detail.getApportionAmount()));
                    detail.setApportionNum(apportionNum);
                }
                //分摊明细表
                WriteOffApportionDetail apportionDetail = new WriteOffApportionDetail();
                BeanUtil.copyProperties(detail, apportionDetail);
                apportionDetail.setApportionNum(apportionNum);
                apportionDetail.setServiceCharge(detail.getExcludeTaxAmount());
                apportionDetail.setWriteOffMonth(nowStage);
                apportionDetail.setApportionAmount(nowAmount);
                apportionDetailList.add(apportionDetail);
            }
            writeOffContractDetailManageService.updateBatchById(noAdvanceDetails);
            //本月分摊总金额
            excludeSum = advanceDetails.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            residueSum = advanceDetails.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal noAdvanceExcluedeSum = noAdvanceDetails.stream().map(WriteOffContractDetailManage::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal noAdvanceResidueSum = noAdvanceDetails.stream().map(WriteOffContractDetailManage::getResidueApportionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            excludeSum = excludeSum.add(noAdvanceExcluedeSum);
            residueSum = residueSum.add(noAdvanceResidueSum);
            apportionInfo = new WriteOffApportionInfo();
            apportionInfo.setApportionTotalAmount(amountSum);
            apportionInfo.setChannelBelong(ChannelBelongEnum.SP.getKey());
            apportionInfo.setWriteOffMonth(nowStage);
            apportionInfo.setServiceTotalAmount(excludeSum);
            apportionInfo.setResidueTotalAmount(residueSum);
            apportionInfo.setVoucherNo(null);
            apportionInfo.setSource("内部");
            writeOffApportionInfoService.save(apportionInfo);
            for (WriteOffApportionDetail apportionDetail : apportionDetailList) {
                apportionDetail.setId(null);
                apportionDetail.setApportionId(apportionInfo.getId());
            }
            writeOffApportionDetailService.saveBatch(apportionDetailList);
        }
        //凭证埋点
        WriteOffContractDetailManage detailOne = list.get(0);
        saveServiceFeeData(detailOne.getChannelCode(), detailOne.getChannelId(), apportionInfo.getId().toString(), detailOne.getChannelFullName());
        return IResponse.success("执行成功");
    }

    /**
     * 事务性保存
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transactionalSave(List<WriteOffContractDetailManage> value, String generateApply,
                                  BigDecimal serviceCharge, String key, String billingPeriod, String seasonFlag,
                                  AdvanceResultDTO settlement, String channelFullName, String channelBelong,
                                  WriteOffTypeEnum writeOffType, List<WriteOffContractDetailManage> notReceivedValue) {

        Set<String> capitalCodeSet = WriteOffTypeEnum.ZJD == writeOffType ? BelongingCapitalEnum.NOT_FD_CODE_SET : Set.of(BelongingCapitalEnum.FD.getCode());
        List<WriteOffContractDetailManage> oldManageList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .select(WriteOffContractDetailManage::getId)
                .eq(WriteOffContractDetailManage::getWriteOffMonth, billingPeriod)
                .eq(WriteOffContractDetailManage::getWriteOffFlag, "2")
                .eq(WriteOffContractDetailManage::getChannelCode, key)
                .in(WriteOffContractDetailManage::getBelongingCapital, capitalCodeSet));
        if (oldManageList.size() > 0) {
            //先删除之前的合同计算明细
            writeOffContractDetailManageService.removeBatchByIds(oldManageList);
        }
        if (value.size() > 0) {
            //更改合同表状态
            List<String> contractNoList = value.stream().map(WriteOffContractDetailManage::getContractNo).collect(Collectors.toList());
            caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                    .in(CaseContractInfo::getContractNo, contractNoList)
                    .set(CaseContractInfo::getServiceFeeCheck, "1"));
        }
        if (notReceivedValue.size() > 0) {
            //更改合同表状态
            List<String> contractNoList = notReceivedValue.stream().map(WriteOffContractDetailManage::getContractNo).collect(Collectors.toList());
            caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                    .in(CaseContractInfo::getContractNo, contractNoList)
                    .set(CaseContractInfo::getServiceFeeCheck, "1"));
            //保存租金贷已计算但未对账的合同明细
            for (WriteOffContractDetailManage manage : notReceivedValue) {
                manage.setWriteOffFlag("0");
                manage.setWriteOffMonth(billingPeriod);
                manage.setBeforeAmount(manage.getServiceCharge());
            }
            writeOffContractDetailManageService.saveBatch(notReceivedValue);
        }
        boolean zjdFlag = writeOffType == WriteOffTypeEnum.ZJD;
        Map<String, WriteOffCapitalContractDetail> capitalReceivedMap;
        List<WriteOffCapitalContractDetail> capitalDeductList = null;
        if (zjdFlag) {
            //应收对账完成的资方合同
            List<WriteOffCapitalContractDetail> receivedCapitalList = writeOffCapitalContractDetailService.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                    .eq(WriteOffCapitalContractDetail::getChannelCode, key)
                    .eq(WriteOffCapitalContractDetail::getReceiveFinishFlag, "1"));
            capitalReceivedMap = receivedCapitalList.stream().collect(Collectors.toMap(WriteOffCapitalContractDetail::getContractNo, k -> k));
            List<ContractStatusEnum> enumList = List.of(ContractStatusEnum.advanceSettle, ContractStatusEnum.contractCancel);
            capitalDeductList = receivedCapitalList.stream().filter(k -> "0".equals(k.getWriteOffFlag())
                    && enumList.contains(k.getContractStatus()) && k.getDeductAmt() != null).toList();
        } else {
            capitalReceivedMap = new HashMap<>();
        }
        //保存核销项信息
        WriteOffBaseInfo baseInfo = new WriteOffBaseInfo();
        baseInfo.setSeasonFlag(seasonFlag);
        baseInfo.setWriteOffType(writeOffType.getCode());
        baseInfo.setCancelPunishAmount(BigDecimal.ZERO);
        baseInfo.setNextToCancelPunishAmount(BigDecimal.ZERO);
        baseInfo.setActualCancelPunishAmount(BigDecimal.ZERO);
        baseInfo.setLastToCancelPunishAmount(BigDecimal.ZERO);
        baseInfo.setEarlySettlementAmount(BigDecimal.ZERO);
        baseInfo.setNextToEarlySettleAmount(BigDecimal.ZERO);
        baseInfo.setActualEarlySettleAmount(BigDecimal.ZERO);
        baseInfo.setLastToEarlySettleAmount(BigDecimal.ZERO);
        baseInfo.setPunishAmount(BigDecimal.ZERO);
        baseInfo.setNextToOtherPunishAmount(BigDecimal.ZERO);
        baseInfo.setActualOtherPunishAmount(BigDecimal.ZERO);
        baseInfo.setLastToOtherPunishAmount(BigDecimal.ZERO);

        //合同扣减明细
        List<WriteOffDeductDetail> addDeductDetailList = new ArrayList<>();
        //资方合同扣减明细
        if (CollUtil.isNotEmpty(capitalDeductList)) {
            for (WriteOffCapitalContractDetail capitalDetail : capitalDeductList) {
                capitalDetail.setWriteOffFlag("1");
                WriteOffDeductDetail deductDetail = new WriteOffDeductDetail();
                deductDetail.setApplyNo(capitalDetail.getApplyNo());
                deductDetail.setContractNo(capitalDetail.getContractNo());
                deductDetail.setLoanDate(capitalDetail.getCapitalReturnTime());
                deductDetail.setBaseInfoApply(generateApply);
                deductDetail.setContractStatus(capitalDetail.getContractStatus());
                deductDetail.setChannelFullName(capitalDetail.getChannelFullName());
                deductDetail.setProductName(capitalDetail.getProductName());
                deductDetail.setPaidTermNo(capitalDetail.getPaidTermNo());
                deductDetail.setSettleDate(capitalDetail.getSettleDate());
                deductDetail.setCancelDate(capitalDetail.getCancelDate());
                deductDetail.setDeductAmount(capitalDetail.getDeductAmt());
                addDeductDetailList.add(deductDetail);
            }
            writeOffCapitalContractDetailService.updateBatchById(capitalDeductList);
        }
        Map<String, SettleContractDetailVO> settleVoMap = new HashMap<>();
        // 合同提前结清扣减
        BigDecimal earlyNum = BigDecimal.ZERO;
        if (settlement != null) {
            earlyNum = settlement.getAdvanceResul();
            if (CollUtil.isNotEmpty(settlement.getSettleVoList())) {
                settleVoMap = settlement.getSettleVoList().stream().collect(Collectors.toMap(SettleContractDetailVO::getContractNo, k -> k));
            }
            List<WriteOffContractDetailManage> hisManageDetails = settlement.getHisDetails();
            if (zjdFlag) {
                List<WriteOffContractDetailManage> curManageDetails = settlement.getCurDetails();
                if (hisManageDetails != null && hisManageDetails.size() > 0) {
                    BigDecimal reduce = BigDecimal.ZERO;
                    List<WriteOffContractDetailManage> manages = hisManageDetails.stream().filter(k -> !capitalReceivedMap.containsKey(k.getContractNo())).toList();
                    if (manages.size() > 0) {
                        reduce = notReceivedCapitalDeduct(manages, ContractStatusEnum.advanceSettle, settleVoMap, null);
                    }
                    log.info("经销商{}，历史期未对账的提前结清扣减金额{}", key, reduce);
                    earlyNum = earlyNum.subtract(reduce);
                }
                if (curManageDetails != null && curManageDetails.size() > 0) {
                    BigDecimal reduce = BigDecimal.ZERO;
                    List<WriteOffContractDetailManage> manages = curManageDetails.stream().filter(k -> !capitalReceivedMap.containsKey(k.getContractNo())).toList();
                    if (manages.size() > 0) {
                        reduce = notReceivedCapitalDeduct(manages, ContractStatusEnum.advanceSettle, settleVoMap, null);
                    }
                    log.info("经销商{}，本期未对账的提前结清扣减金额{}", key, reduce);
                    earlyNum = earlyNum.subtract(reduce);
                }
            }
            List<WriteOffContractDetailManage> hisReceivedManages = null;
            if (zjdFlag) {
                if (CollUtil.isNotEmpty(hisManageDetails)) {
                    hisReceivedManages = hisManageDetails.stream().filter(k -> capitalReceivedMap.containsKey(k.getContractNo())).toList();
                }
            }else {
                hisReceivedManages = hisManageDetails;
            }
            if (CollUtil.isNotEmpty(hisReceivedManages)) {
                Map<String, WriteOffContractDetailManage> hisSettleMap = hisReceivedManages.stream().collect(Collectors.toMap(WriteOffContractDetailManage::getContractNo, k -> k));
                List<WriteOffContractDetailManage> hisDetailManageList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                        .in(WriteOffContractDetailManage::getContractNo, hisSettleMap.keySet()));
                for (WriteOffContractDetailManage hisDetail : hisDetailManageList) {
                    WriteOffContractDetailManage hisSettleVo = hisSettleMap.get(hisDetail.getContractNo());
                    SettleContractDetailVO settleDetailVO = settleVoMap.get(hisDetail.getContractNo());
                    hisDetail.setContractStatus(ContractStatusEnum.advanceSettle);
                    hisDetail.setAdvanceSettleFlag(Boolean.TRUE);
                    hisDetail.setAdvanceSettleAmount(hisSettleVo.getAdvanceSettleAmount());
                    hisDetail.setDeductRate(hisSettleVo.getDeductRate());
                    hisDetail.setAdvanceSettleMonth(billingPeriod);
                    WriteOffDeductDetail deductDetail = new WriteOffDeductDetail();
                    deductDetail.setApplyNo(hisDetail.getApplyNo());
                    deductDetail.setContractNo(hisDetail.getContractNo());
                    if (zjdFlag){
                        deductDetail.setLoanDate(hisDetail.getCapitalReturnTime());
                    }else {
                        deductDetail.setLoanDate(hisDetail.getLoanDate());
                    }
                    deductDetail.setBaseInfoApply(generateApply);
                    deductDetail.setContractStatus(ContractStatusEnum.advanceSettle);
                    deductDetail.setChannelFullName(hisDetail.getChannelFullName());
                    deductDetail.setProductName(hisDetail.getProductName());
                    deductDetail.setProductGroup(settleDetailVO.getProductGroup());
                    deductDetail.setPaidTermNo(settleDetailVO.getTermNo());
                    deductDetail.setSettleDate(settleDetailVO.getSettleDate());
                    deductDetail.setEarlyDeductRate(hisSettleVo.getDeductRate());
                    deductDetail.setIntoFirstDate(settleDetailVO.getIntoFirstDate());
                    deductDetail.setDeductAmount(hisDetail.getAdvanceSettleAmount());
                    addDeductDetailList.add(deductDetail);
                }
                writeOffContractDetailManageService.updateBatchById(hisDetailManageList);
            }
        }
        // 合同取消扣减
        BigDecimal cancelNum = BigDecimal.ZERO;
        //查询上期的核销项信息
        WriteOffBaseInfo lastBase = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getOrganId, key)
                .orderByDesc(WriteOffBaseInfo::getCreateTime)
                .last("limit 1"));
        //设置上月剩余扣罚金额
        if (lastBase != null) {
            //合同取消逻辑
            WriteOffBaseInfo lastTypeBase = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .eq(WriteOffBaseInfo::getOrganId, key)
                    .eq(WriteOffBaseInfo::getWriteOffType, writeOffType.getCode())
                    .orderByDesc(WriteOffBaseInfo::getCreateTime)
                    .last("limit 1"));
            //只扣减相同业务模式的合同
            List<WriteOffContractDetailManage> manageList = null;
            if (lastTypeBase != null) {
                manageList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                        .eq(WriteOffContractDetailManage::getBaseInfoApply, lastTypeBase.getApplyNo()));
            }
            List<BasicContractInfoVo> cancelContracts = null;
            if (manageList != null && manageList.size() > 0) {
                List<String> detailConList = manageList.stream().map(WriteOffContractDetailManage::getContractNo).toList();
                ContractDetailManageSearchDTO searchDTO = new ContractDetailManageSearchDTO();
                searchDTO.setContractNoList(detailConList);
                IResponse<List<BasicContractInfoVo>> response = writeOffFeign.queryWriteCancel(searchDTO);
                Assert.isTrue("0000".equals(response.getCode()), "合同取消查询异常");
                cancelContracts = response.getData();
            }
            if (CollectionUtil.isNotEmpty(cancelContracts)) {
                //上月核销项中存在合同取消的单子
                Map<String, Date> cancelMap = cancelContracts.stream().collect(Collectors.toMap(BasicContractInfoVo::getContractNo, BasicContractInfoVo::getCancelDate));
                Set<String> cancelNos = cancelMap.keySet();
                List<WriteOffContractDetailManage> cancelDetails = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                        .in(WriteOffContractDetailManage::getContractNo, cancelNos));
                if (zjdFlag){
                    List<WriteOffContractDetailManage> manages = cancelDetails.stream().filter(k -> !capitalReceivedMap.containsKey(k.getContractNo())).toList();
                    if (manages.size() > 0) {
                        BigDecimal reduce = notReceivedCapitalDeduct(manages, ContractStatusEnum.contractCancel, null, cancelMap);
                        log.info("经销商{}，未对账的合同取消扣减金额{}", key, reduce);
                    }
                    cancelDetails = cancelDetails.stream().filter(k -> capitalReceivedMap.containsKey(k.getContractNo())).toList();
                }
                if (cancelDetails.size() > 0) {
                    //合同取消金额改为奖惩前金额
                    cancelNum = cancelDetails.stream().map(WriteOffContractDetailManage::getBeforeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    for (WriteOffContractDetailManage cancelDetail : cancelDetails) {
                        cancelDetail.setContractStatus(ContractStatusEnum.contractCancel);
                        WriteOffDeductDetail deductDetail = new WriteOffDeductDetail();
                        deductDetail.setApplyNo(cancelDetail.getApplyNo());
                        deductDetail.setContractNo(cancelDetail.getContractNo());
                        if (zjdFlag){
                            deductDetail.setLoanDate(cancelDetail.getCapitalReturnTime());
                        }else {
                            deductDetail.setLoanDate(cancelDetail.getLoanDate());
                        }
                        deductDetail.setCancelDate(cancelMap.get(cancelDetail.getContractNo()));
                        deductDetail.setBaseInfoApply(generateApply);
                        deductDetail.setContractStatus(ContractStatusEnum.contractCancel);
                        deductDetail.setChannelFullName(cancelDetail.getChannelFullName());
                        deductDetail.setDeductAmount(cancelDetail.getBeforeAmount());
                        addDeductDetailList.add(deductDetail);
                    }
                    writeOffContractDetailManageService.updateBatchById(cancelDetails);
                }
            } else {
                if (value.size() <= 0 && addDeductDetailList.size() <= 0) {
                    log.info("经销商：{}，本月无服务费，并且无提前结清扣罚和合同取消扣罚", key);
                    return;
                }
            }

            //上月留下的合同取消扣罚
            BigDecimal lastRemainCancelPunishAmount = lastBase.getNextToCancelPunishAmount() == null ? BigDecimal.ZERO : lastBase.getNextToCancelPunishAmount();
            //上月留下的提前结清扣罚
            BigDecimal lastRemainEarlySettleAmount = lastBase.getNextToEarlySettleAmount() == null ? BigDecimal.ZERO : lastBase.getNextToEarlySettleAmount();
            //上月留下的其他扣罚
            BigDecimal lastRemainOtherAmount = lastBase.getNextToOtherPunishAmount() == null ? BigDecimal.ZERO : lastBase.getNextToOtherPunishAmount();
            baseInfo.setLastToCancelPunishAmount(lastRemainCancelPunishAmount);
            baseInfo.setLastToEarlySettleAmount(lastRemainEarlySettleAmount);
            baseInfo.setLastToOtherPunishAmount(lastRemainOtherAmount);
            BigDecimal totalLastPunish = lastBase.getNextToPunishAmount().add(cancelNum).add(earlyNum);
            if (totalLastPunish.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal subtract = serviceCharge.subtract(totalLastPunish);
                if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                    baseInfo.setInvoiceAmount(BigDecimal.ZERO);
                    baseInfo.setAmountToBeInvoiced(BigDecimal.ZERO);
                    baseInfo.setLastToPunishAmount(serviceCharge);
                    baseInfo.setNextToPunishAmount(totalLastPunish.subtract(serviceCharge));
                    BigDecimal enableAmount = serviceCharge;
                    //1、合同取消扣减(先上月)
                    BigDecimal totalCancel = lastRemainCancelPunishAmount.add(cancelNum);
                    if (totalCancel.compareTo(serviceCharge) > 0) {
                        baseInfo.setNextToCancelPunishAmount(totalCancel.subtract(serviceCharge));
                        baseInfo.setActualCancelPunishAmount(serviceCharge);
                        enableAmount = BigDecimal.ZERO;
                    } else {
                        baseInfo.setNextToCancelPunishAmount(BigDecimal.ZERO);
                        baseInfo.setActualCancelPunishAmount(totalCancel);
                        enableAmount = enableAmount.subtract(totalCancel);
                    }
                    //2、提前结清扣减(先上月)
                    BigDecimal totalEarly = lastRemainEarlySettleAmount.add(earlyNum);
                    if (enableAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (totalEarly.compareTo(enableAmount) > 0) {
                            baseInfo.setNextToEarlySettleAmount(totalEarly.subtract(enableAmount));
                            baseInfo.setActualEarlySettleAmount(enableAmount);
                            enableAmount = BigDecimal.ZERO;
                        } else {
                            baseInfo.setNextToEarlySettleAmount(BigDecimal.ZERO);
                            baseInfo.setActualEarlySettleAmount(totalEarly);
                            enableAmount = enableAmount.subtract(totalEarly);
                        }
                    } else {
                        baseInfo.setNextToEarlySettleAmount(totalEarly);
                        baseInfo.setActualEarlySettleAmount(BigDecimal.ZERO);
                    }
                    //3、其他扣罚(先上月)
                    if (enableAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (lastRemainOtherAmount.compareTo(enableAmount) > 0) {
                            baseInfo.setNextToOtherPunishAmount(lastRemainOtherAmount.subtract(enableAmount));
                            baseInfo.setActualOtherPunishAmount(enableAmount);
                            enableAmount = BigDecimal.ZERO;
                        } else {
                            baseInfo.setNextToOtherPunishAmount(BigDecimal.ZERO);
                            baseInfo.setActualOtherPunishAmount(lastRemainOtherAmount);
                            enableAmount = enableAmount.subtract(lastRemainOtherAmount);
                        }
                    } else {
                        baseInfo.setNextToOtherPunishAmount(lastRemainOtherAmount);
                        baseInfo.setActualOtherPunishAmount(BigDecimal.ZERO);
                    }
                } else {
                    baseInfo.setInvoiceAmount(subtract);
                    baseInfo.setAmountToBeInvoiced(subtract);
                    baseInfo.setLastToPunishAmount(totalLastPunish);
                    baseInfo.setNextToPunishAmount(BigDecimal.ZERO);
                    baseInfo.setActualCancelPunishAmount(lastRemainCancelPunishAmount.add(cancelNum));
                    baseInfo.setActualEarlySettleAmount(lastRemainEarlySettleAmount.add(earlyNum));
                    baseInfo.setActualOtherPunishAmount(lastRemainOtherAmount);
                }
            } else {
                baseInfo.setInvoiceAmount(serviceCharge);
                baseInfo.setAmountToBeInvoiced(serviceCharge);
                baseInfo.setLastToPunishAmount(BigDecimal.ZERO);
                baseInfo.setNextToPunishAmount(BigDecimal.ZERO);
            }
        } else {
            baseInfo.setLastToPunishAmount(BigDecimal.ZERO);
            baseInfo.setNextToPunishAmount(BigDecimal.ZERO);
            if (earlyNum.compareTo(serviceCharge) > 0) {
                baseInfo.setInvoiceAmount(BigDecimal.ZERO);
                baseInfo.setAmountToBeInvoiced(BigDecimal.ZERO);
                baseInfo.setNextToEarlySettleAmount(earlyNum.subtract(serviceCharge));
                baseInfo.setActualEarlySettleAmount(serviceCharge);
                baseInfo.setNextToPunishAmount(earlyNum.subtract(serviceCharge));
            } else {
                baseInfo.setInvoiceAmount(serviceCharge.subtract(earlyNum));
                baseInfo.setAmountToBeInvoiced(serviceCharge.subtract(earlyNum));
                baseInfo.setActualEarlySettleAmount(earlyNum);
            }
        }
        if (value.size() <= 0 && addDeductDetailList.size() <= 0) {
            log.info("经销商：{}，本月无服务费，并且无提前结清扣罚和合同取消扣罚", key);
            return;
        }
        baseInfo.setBeforeAmount(serviceCharge);
        baseInfo.setCancelPunishAmount(cancelNum);
        baseInfo.setEarlySettlementAmount(earlyNum);
        baseInfo.setApplyNo(generateApply);
        //延续上月的冰冻状态
        if (lastBase != null && lastBase.getFrozenStatus() == FrozenStatusEnum.FROZEN) {
            baseInfo.setFrozenStatus(FrozenStatusEnum.FROZEN);
        } else {
            baseInfo.setFrozenStatus(FrozenStatusEnum.NORMAL);
        }
        //延续回司超期冻结状态
        if (lastBase != null && StrUtil.equals(lastBase.getReturnOverdueStatus(), OverdueStatusEnum.FROZEN.getCode())) {
            baseInfo.setReturnOverdueStatus(OverdueStatusEnum.FROZEN.getCode());
            baseInfo.setReturnOverdueAppNo(lastBase.getReturnOverdueAppNo());
        }
        // 服务费发票上传金额税率
        Map<String, String> configuration = DicHelper.getDicMaps("invoiceUploadAmountTaxRate")
                .values()
                .stream()
                .flatMap(List::stream).collect(Collectors.toMap(DicDataDto::getTitle, DicDataDto::getValue));
        BigDecimal tax = new BigDecimal(configuration.get("税率")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        if (value.size() > 0) {
            baseInfo.setWriteOffStatus(WriteOffStatusEnum.DRAFT_APPROVAL);
            baseInfo.setOrganName(value.get(0).getChannelFullName());
            baseInfo.setChannelBelong(value.get(0).getChannelBelong());
        } else {
            baseInfo.setWriteOffStatus(WriteOffStatusEnum.PUNISH_WAIT);
            baseInfo.setOrganName(channelFullName);
            baseInfo.setChannelBelong(channelBelong);
            baseInfo.setWriteOffExplain("生成待扣罚项成功");
        }
        baseInfo.setOrganId(key);
        baseInfo.setWriteOffMonth(billingPeriod);
        baseInfo.setAmountBeingWrittenOff(BigDecimal.ZERO);
        baseInfo.setAmountLoaned(BigDecimal.ZERO);
        baseInfo.setAmountToBeReleased(BigDecimal.ZERO);
        baseInfo.setPrizeAmount(BigDecimal.ZERO);
        baseInfo.setPunishAmount(BigDecimal.ZERO);
        baseInfo.setInvoiceAmountBegin(baseInfo.getInvoiceAmount());
        baseInfo.setNextToPunishAmountBegin(baseInfo.getNextToPunishAmount());
        baseInfo.setNextToOtherPunishAmountBegin(baseInfo.getNextToOtherPunishAmount());
        baseInfo.setNextToCancelPunishAmountBegin(baseInfo.getNextToCancelPunishAmount());
        baseInfo.setNextToEarlySettleAmountBegin(baseInfo.getNextToEarlySettleAmount());
        baseInfo.setActualCancelPunishAmountBegin(baseInfo.getActualCancelPunishAmount());
        baseInfo.setActualEarlySettleAmountBegin(baseInfo.getActualEarlySettleAmount());
        baseInfo.setActualOtherPunishAmountBegin(baseInfo.getActualOtherPunishAmount());
        //未含税金额=总金额/1.06
        baseInfo.setExcludeTaxAmount(baseInfo.getInvoiceAmount().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
        baseInfo.setTaxAmount(baseInfo.getInvoiceAmount().subtract(baseInfo.getExcludeTaxAmount()));
        writeOffBaseInfoService.save(baseInfo);

        //保存核销项合同信息
        WriteOffContractDetailManage detail = null;
        BigDecimal totalDetailPrize = BigDecimal.ZERO;
        BigDecimal lastAmount = BigDecimal.ZERO;
        BigDecimal baseTotalPunish = baseInfo.getBeforeAmount().subtract(baseInfo.getInvoiceAmount());
        for (int i = 0; i < value.size(); i++) {
            detail = value.get(i);
            if (baseInfo.getBeforeAmount().compareTo(BigDecimal.ZERO) != 0) {
                lastAmount = baseTotalPunish.multiply(detail.getServiceCharge()).divide(baseInfo.getBeforeAmount(), 2, RoundingMode.HALF_UP);
            }
            if (i == value.size() - 1) {
                //最后一项,特殊处理,扣罚金额=上月总扣罚金额-其他合同扣罚总额
                lastAmount = baseTotalPunish.subtract(totalDetailPrize);
                detail.setBaseInfoApply(generateApply);
                detail.setWriteOffMonth(billingPeriod);
                detail.setErrMsg(null);
                detail.setBeforeAmount(detail.getServiceCharge());
                detail.setPrizeOrPunishAmount(BigDecimal.ZERO);
                //上月总扣罚金额-其他合同扣罚总额
                detail.setLastToPunishAmount(lastAmount);
                detail.setServiceCharge(detail.getServiceCharge().subtract(lastAmount));
                detail.setExcludeTaxAmount(detail.getServiceCharge().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
                detail.setTaxAmount(detail.getServiceCharge().subtract(detail.getExcludeTaxAmount()));
                detail.setApportionAmount(detail.getExcludeTaxAmount().divide(new BigDecimal(detail.getLoanTerm()), 2, RoundingMode.HALF_UP));
            } else {
                detail.setBaseInfoApply(generateApply);
                detail.setWriteOffMonth(billingPeriod);
                detail.setErrMsg(null);
                detail.setBeforeAmount(detail.getServiceCharge());
                detail.setPrizeOrPunishAmount(BigDecimal.ZERO);
                detail.setLastToPunishAmount(lastAmount);
                detail.setServiceCharge(detail.getServiceCharge().subtract(lastAmount));
                detail.setExcludeTaxAmount(detail.getServiceCharge().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
                detail.setTaxAmount(detail.getServiceCharge().subtract(detail.getExcludeTaxAmount()));
                detail.setApportionAmount(detail.getExcludeTaxAmount().divide(new BigDecimal(detail.getLoanTerm()), 2, RoundingMode.HALF_UP));
                totalDetailPrize = totalDetailPrize.add(lastAmount);
            }
            detail.setServiceChargeBegin(detail.getServiceCharge());
            detail.setResidueApportionAmount(detail.getExcludeTaxAmount());
            detail.setApportionedAmount(BigDecimal.ZERO);
            detail.setWriteOffFlag("1");
            lastAmount = BigDecimal.ZERO;
        }
        if (value.size() > 0) {
            //保存合同信息
            writeOffContractDetailManageService.saveBatch(value);
        }
        if (settlement != null && CollUtil.isNotEmpty(settlement.getCurDetails())) {
            List<WriteOffContractDetailManage> receivedDetails = settlement.getCurDetails().stream().filter(k -> capitalReceivedMap.containsKey(k.getContractNo())).toList();
            for (WriteOffContractDetailManage curManage : receivedDetails) {
                SettleContractDetailVO settleDetailVO = settleVoMap.get(curManage.getContractNo());
                WriteOffDeductDetail deductDetail = new WriteOffDeductDetail();
                deductDetail.setApplyNo(curManage.getApplyNo());
                deductDetail.setContractNo(curManage.getContractNo());
                if (zjdFlag){
                    deductDetail.setLoanDate(curManage.getCapitalReturnTime());
                }else {
                    deductDetail.setLoanDate(curManage.getLoanDate());
                }
                deductDetail.setBaseInfoApply(generateApply);
                deductDetail.setContractStatus(ContractStatusEnum.advanceSettle);
                deductDetail.setChannelFullName(curManage.getChannelFullName());
                deductDetail.setProductName(curManage.getProductName());
                deductDetail.setProductGroup(settleDetailVO.getProductGroup());
                deductDetail.setPaidTermNo(settleDetailVO.getTermNo());
                deductDetail.setSettleDate(settleDetailVO.getSettleDate());
                deductDetail.setEarlyDeductRate(curManage.getDeductRate());
                deductDetail.setIntoFirstDate(settleDetailVO.getIntoFirstDate());
                deductDetail.setDeductAmount(curManage.getAdvanceSettleAmount());
                addDeductDetailList.add(deductDetail);
            }
        }
        //合同扣减明细计算
        BigDecimal totalNext = baseInfo.getNextToCancelPunishAmount().add(baseInfo.getNextToEarlySettleAmount()).add(baseInfo.getNextToOtherPunishAmount());
        log.info("核销项信息：{}", JSON.toJSONString(baseInfo));
        Assert.isTrue(baseInfo.getNextToPunishAmount().compareTo(totalNext) == 0, "经销商" + key + "扣罚明细计算异常");
        //本月金额足够，上月有留扣，本月直接新增上月的(只有上月)
        List<WriteOffDeductDetail> lastToAdd = new ArrayList<>();
        //本月金额足够，不用下月留扣，本月直接新增(只有本月)
        List<WriteOffDeductDetail> thisToAdd = new ArrayList<>();
        //本月金额不够，本月实扣为0，直接流转到下月(上月和本月)
        List<WriteOffDeductDetail> nextToAdd = new ArrayList<>();
        //本月金额不够，本月实扣不为0，下月还有留扣，流转到下月(上月和本月)
        List<WriteOffDeductDetail> nextOtherAdd = new ArrayList<>();
        List<WriteOffDeductDetail> lastRemainList = new ArrayList<>();
        if (lastBase != null) {
            lastRemainList = writeOffDeductDetailService.list(Wrappers.<WriteOffDeductDetail>lambdaQuery()
                    .eq(WriteOffDeductDetail::getBaseInfoApply, lastBase.getApplyNo())
                    .gt(WriteOffDeductDetail::getNextDeductAmount, BigDecimal.ZERO));
        }
        if (baseInfo.getNextToPunishAmount().compareTo(BigDecimal.ZERO) > 0) {
            List<WriteOffDeductDetail> thisCancels = addDeductDetailList.stream().filter(k -> k.getContractStatus() == ContractStatusEnum.contractCancel).toList();
            List<WriteOffDeductDetail> thisEarlys = addDeductDetailList.stream().filter(k -> k.getContractStatus() == ContractStatusEnum.advanceSettle).toList();
            List<WriteOffDeductDetail> lastCancels = lastRemainList.stream().filter(k -> k.getContractStatus() == ContractStatusEnum.contractCancel).toList();
            List<WriteOffDeductDetail> lastEarlys = lastRemainList.stream().filter(k -> k.getContractStatus() == ContractStatusEnum.advanceSettle).toList();
            //先合同取消
            if (baseInfo.getNextToCancelPunishAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal enableCancel = baseInfo.getActualCancelPunishAmount();
                if (enableCancel.compareTo(BigDecimal.ZERO) <= 0) {
                    nextToAdd.addAll(lastCancels);
                    nextToAdd.addAll(thisCancels);
                } else {
                    //先扣上月的
                    for (WriteOffDeductDetail lastCancel : lastCancels) {
                        if (enableCancel.compareTo(lastCancel.getNextDeductAmount()) >= 0) {
                            lastToAdd.add(lastCancel);
                            enableCancel = enableCancel.subtract(lastCancel.getNextDeductAmount());
                        } else {
                            lastCancel.setLastDeductAmount(lastCancel.getNextDeductAmount());
                            if (enableCancel.compareTo(BigDecimal.ZERO) > 0) {
                                lastCancel.setActualDeductAmount(enableCancel);
                                lastCancel.setNextDeductAmount(lastCancel.getNextDeductAmount().subtract(enableCancel));
                                nextOtherAdd.add(lastCancel);
                                enableCancel = BigDecimal.ZERO;
                            } else {
                                nextToAdd.add(lastCancel);
                            }
                        }
                    }
                    //再扣本月的
                    for (WriteOffDeductDetail thisCancel : thisCancels) {
                        if (enableCancel.compareTo(thisCancel.getDeductAmount()) >= 0) {
                            thisToAdd.add(thisCancel);
                            enableCancel = enableCancel.subtract(thisCancel.getDeductAmount());
                        } else {
                            thisCancel.setLastDeductAmount(BigDecimal.ZERO);
                            if (enableCancel.compareTo(BigDecimal.ZERO) > 0) {
                                thisCancel.setActualDeductAmount(enableCancel);
                                thisCancel.setNextDeductAmount(thisCancel.getDeductAmount().subtract(enableCancel));
                                nextOtherAdd.add(thisCancel);
                                enableCancel = BigDecimal.ZERO;
                            } else {
                                nextToAdd.add(thisCancel);
                            }
                        }
                    }
                }
            } else {
                lastToAdd.addAll(lastCancels);
                thisToAdd.addAll(thisCancels);
            }
            //再提前结清
            if (baseInfo.getNextToEarlySettleAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal enableEarly = baseInfo.getActualEarlySettleAmount();
                if (enableEarly.compareTo(BigDecimal.ZERO) <= 0) {
                    nextToAdd.addAll(lastEarlys);
                    nextToAdd.addAll(thisEarlys);
                } else {
                    //先扣上月的
                    for (WriteOffDeductDetail lastEarly : lastEarlys) {
                        if (enableEarly.compareTo(lastEarly.getNextDeductAmount()) >= 0) {
                            lastToAdd.add(lastEarly);
                            enableEarly = enableEarly.subtract(lastEarly.getNextDeductAmount());
                        } else {
                            lastEarly.setLastDeductAmount(lastEarly.getNextDeductAmount());
                            if (enableEarly.compareTo(BigDecimal.ZERO) > 0) {
                                lastEarly.setActualDeductAmount(enableEarly);
                                lastEarly.setNextDeductAmount(lastEarly.getNextDeductAmount().subtract(enableEarly));
                                nextOtherAdd.add(lastEarly);
                                enableEarly = BigDecimal.ZERO;
                            } else {
                                nextToAdd.add(lastEarly);
                            }
                        }
                    }
                    //再扣本月的
                    for (WriteOffDeductDetail thisEarly : thisEarlys) {
                        if (enableEarly.compareTo(thisEarly.getDeductAmount()) >= 0) {
                            thisToAdd.add(thisEarly);
                            enableEarly = enableEarly.subtract(thisEarly.getDeductAmount());
                        } else {
                            thisEarly.setLastDeductAmount(BigDecimal.ZERO);
                            if (enableEarly.compareTo(BigDecimal.ZERO) > 0) {
                                thisEarly.setActualDeductAmount(enableEarly);
                                thisEarly.setNextDeductAmount(thisEarly.getDeductAmount().subtract(enableEarly));
                                nextOtherAdd.add(thisEarly);
                                enableEarly = BigDecimal.ZERO;
                            } else {
                                nextToAdd.add(thisEarly);
                            }
                        }
                    }
                }
            } else {
                lastToAdd.addAll(lastEarlys);
                thisToAdd.addAll(thisEarlys);
            }
        } else {
            thisToAdd.addAll(addDeductDetailList);
            lastToAdd.addAll(lastRemainList);
        }
        for (WriteOffDeductDetail deductDetail : thisToAdd) {
            deductDetail.setLastDeductAmount(deductDetail.getLastDeductAmount() == null ? BigDecimal.ZERO : deductDetail.getLastDeductAmount());
            deductDetail.setActualDeductAmount(deductDetail.getActualDeductAmount() == null ? deductDetail.getDeductAmount() : deductDetail.getActualDeductAmount());
            deductDetail.setNextDeductAmount(BigDecimal.ZERO);
        }
        for (WriteOffDeductDetail nextDetail : lastToAdd) {
            nextDetail.setLastDeductAmount(nextDetail.getNextDeductAmount());
            nextDetail.setActualDeductAmount(nextDetail.getNextDeductAmount());
            nextDetail.setNextDeductAmount(BigDecimal.ZERO);
        }
        for (WriteOffDeductDetail deductDetail : nextToAdd) {
            deductDetail.setLastDeductAmount(deductDetail.getNextDeductAmount() == null ? BigDecimal.ZERO : deductDetail.getNextDeductAmount());
            deductDetail.setActualDeductAmount(BigDecimal.ZERO);
            deductDetail.setNextDeductAmount(deductDetail.getNextDeductAmount() == null ? deductDetail.getDeductAmount() : deductDetail.getNextDeductAmount());
        }
        //保存合同扣减明细
        List<WriteOffDeductDetail> resList = new ArrayList<>();
        resList.addAll(thisToAdd);
        resList.addAll(lastToAdd);
        resList.addAll(nextToAdd);
        resList.addAll(nextOtherAdd);
        for (WriteOffDeductDetail deductDetail : resList) {
            deductDetail.setId(null);
            deductDetail.setUpdateTime(null);
            deductDetail.setBaseInfoApply(generateApply);
            deductDetail.setActualDeductAmountBegin(deductDetail.getActualDeductAmount());
            deductDetail.setNextDeductAmountBegin(deductDetail.getNextDeductAmount());
        }
        log.info("经销商编号：{}，合同扣减计算成功：{}", key, JSON.toJSONString(resList));
        writeOffDeductDetailService.saveBatch(resList);
        log.info("【服务费计算】成功，经销商编号：{}", key);
    }


    /**
     * 未对账的资方合同扣减处理
     */
    public BigDecimal notReceivedCapitalDeduct(List<WriteOffContractDetailManage> manages, ContractStatusEnum contractStatusEnum,
                                               Map<String, SettleContractDetailVO> settleVoMap, Map<String, Date> cancelMap) {
        BigDecimal reduce = BigDecimal.ZERO;
        Map<String, BigDecimal> decimalMap = null;
        if (ContractStatusEnum.advanceSettle == contractStatusEnum) {
            reduce = manages.stream().map(WriteOffContractDetailManage::getAdvanceSettleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            decimalMap = manages.stream().collect(Collectors.toMap(WriteOffContractDetailManage::getContractNo, WriteOffContractDetailManage::getAdvanceSettleAmount));
        } else if (ContractStatusEnum.contractCancel == contractStatusEnum) {
            reduce = manages.stream().map(WriteOffContractDetailManage::getBeforeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            decimalMap = manages.stream().collect(Collectors.toMap(WriteOffContractDetailManage::getContractNo, WriteOffContractDetailManage::getBeforeAmount));
        } else {
            throw new RuntimeException("不正确的合同状态");
        }
        List<WriteOffCapitalContractDetail> capitalContractDetails = writeOffCapitalContractDetailService.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                .select(WriteOffCapitalContractDetail::getId, WriteOffCapitalContractDetail::getContractNo)
                .in(WriteOffCapitalContractDetail::getContractNo, decimalMap.keySet()));
        List<String> oldCapitalConList = new ArrayList<>();
        if (capitalContractDetails.size() > 0) {
            for (WriteOffCapitalContractDetail capitalContractDetail : capitalContractDetails) {
                oldCapitalConList.add(capitalContractDetail.getContractNo());
                capitalContractDetail.setContractStatus(contractStatusEnum);
                capitalContractDetail.setDeductAmt(decimalMap.get(capitalContractDetail.getContractNo()));
            }
            writeOffCapitalContractDetailService.updateBatchById(capitalContractDetails);
        }
        List<WriteOffContractDetailManage> newManageDetails = manages.stream().filter(k -> !oldCapitalConList.contains(k.getContractNo())).toList();
        List<WriteOffCapitalContractDetail> newCapitalDetails = new ArrayList<>();
        for (WriteOffContractDetailManage manage : newManageDetails) {
            WriteOffCapitalContractDetail capitalContractDetail = new WriteOffCapitalContractDetail();
            capitalContractDetail.setApplyNo(manage.getApplyNo());
            capitalContractDetail.setContractNo(manage.getContractNo());
            capitalContractDetail.setContractStatus(contractStatusEnum);
            capitalContractDetail.setBelongingCapital(manage.getBelongingCapital());
            capitalContractDetail.setCapitalReturnTime(manage.getCapitalReturnTime());
            capitalContractDetail.setChannelFullName(manage.getChannelFullName());
            capitalContractDetail.setProductName(manage.getProductName());
            capitalContractDetail.setWriteOffFlag("0");
            capitalContractDetail.setReceiveFinishFlag("0");
            capitalContractDetail.setDeductAmt(decimalMap.get(manage.getContractNo()));
            if (ContractStatusEnum.advanceSettle == contractStatusEnum) {
                capitalContractDetail.setPaidTermNo(settleVoMap.get(manage.getContractNo()).getTermNo());
                capitalContractDetail.setSettleDate(settleVoMap.get(manage.getContractNo()).getSettleDate());
            }
            if (ContractStatusEnum.contractCancel == contractStatusEnum) {
                capitalContractDetail.setCancelDate(cancelMap.get(manage.getContractNo()));
            }
            newCapitalDetails.add(capitalContractDetail);
        }
        if (newCapitalDetails.size() > 0) {
            writeOffCapitalContractDetailService.saveBatch(newCapitalDetails);
        }
        return reduce;
    }


    @Override
    public IResponse writeOffAdvanceRuleCal(List<WriteOffContractDetailManage> currentDetail,
        List<SettleContractDetailVO> settleData, String billingPeriod) {
        return IResponse.success(getEarlySettlementOfDeductionAmount(currentDetail, settleData, billingPeriod, false));
    }

    /**
     * 计算经销商本期服务费扣减金额
     *
     * @param currentDetail 当期合同详情信息
     * @param settleData    提前结清合同信息
     * @param billingPeriod 当期账期
     * @param zjdFlag       是否租金贷
     * @return 本期服务费扣减金额
     */
    private AdvanceResultDTO getEarlySettlementOfDeductionAmount(List<WriteOffContractDetailManage> currentDetail, List<SettleContractDetailVO> settleData,
                                                                 String billingPeriod, boolean zjdFlag) {
        if (CollUtil.isEmpty(settleData)) {
            return null;
        }
        AdvanceResultDTO resultDTO = new AdvanceResultDTO();
        List<SettleContractDetailVO> settleVoList = new ArrayList<>();
        BigDecimal earlyPay = new BigDecimal(0);
        // 数据分类， 放款日期在本期还是历史期   2024-08-06   本期:2024-07，上期:2024-06  查询时间段2024-07-01 00-00-00<----->2024-08-01 00-00-00
        Map<Boolean, List<SettleContractDetailVO>> map = null;
        if (zjdFlag) {
            List<String> curContractNos = currentDetail.stream().map(WriteOffContractDetailManage::getContractNo).toList();
            map = settleData.stream().collect(Collectors.groupingBy(detailVO -> {
                return curContractNos.contains(detailVO.getContractNo());
            }));
        } else {
            YearMonth targetMonth = YearMonth.parse(billingPeriod, DateTimeFormatter.ofPattern("yyyy-MM"));
            map = settleData.stream().collect(Collectors.groupingBy(detailVO -> {
                YearMonth dateMonth = YearMonth.from(
                        detailVO.getLoanDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                return dateMonth.equals(targetMonth);
            }));
        }

        // 历史期数据
        List<SettleContractDetailVO> hisSettleData = map.get(Boolean.FALSE);
        if (CollUtil.isNotEmpty(hisSettleData)) {
            List<WriteOffContractDetailManage> managesList = new ArrayList<>();
            List<String> earlyNos = hisSettleData.stream().map(SettleContractDetailVO::getContractNo).distinct().toList();

            List<WriteOffContractDetailManage> hisDetails = writeOffContractDetailManageService.lambdaQuery()
                    .select(WriteOffContractDetailManage::getContractNo,
                            WriteOffContractDetailManage::getWriteOffMonth,
                            WriteOffContractDetailManage::getBeforeAmount)
                    .in(WriteOffContractDetailManage::getContractNo, earlyNos)
                    .eq(WriteOffContractDetailManage::getAdvanceSettleFlag, Boolean.FALSE)
                    .list();

            log.info("提前结清扣减计算-历史期服务费,合同号{}", JSON.toJSONString(earlyNos));

            // 待扣减账期合同信息
            Map<String, List<WriteOffContractDetailManage>> monthDetail = hisDetails.stream()
                    .collect(Collectors.groupingBy(WriteOffContractDetailManage::getContractNo));

            Map<String, SettleContractDetailVO> contractDetails = hisSettleData.stream()
                    .collect(Collectors.toMap(SettleContractDetailVO::getContractNo, Function.identity(),
                            // 存在两个提前结清还款计划项,取旧数据的期数 新的期数是为了处理问题期数+1
                            (existingValue, newValue) -> existingValue.getTermNo() < newValue.getTermNo()
                                    ? existingValue
                                    : newValue));

            BigDecimal hisEarly = new BigDecimal(0);
            for (SettleContractDetailVO contractDetail : contractDetails.values()) {
                List<WriteOffContractDetailManage> detailManages = monthDetail.get(contractDetail.getContractNo());

                // 部分订单存在提前结清信息，不存在服务费合同详情信息【服务费计算未经过本系统，跳过】
                if (CollUtil.isEmpty(detailManages)) {
                    log.info("{}提前结清扣减计算未匹配到服务费合同明细", contractDetail.getContractNo());
                    continue;
                }
                if (detailManages.size() > 1) {
                    log.error("{}提前结清扣减计算匹配到多个服务费合同明细", contractDetail.getContractNo());
                    throw new AfsBaseException(contractDetail.getContractNo() + "提前结清扣减计算匹配到多个服务费合同明细");
                }
                WriteOffContractDetailManage detailManage = detailManages.get(0);
                contractDetail.setServiceFee(detailManage.getBeforeAmount());
                contractDetail.setBelongingCapital(detailManage.getBelongingCapital());
                // 扣减金额计算
                BigDecimal contractFee = calculateTheDeductionAmount(contractDetail);
                settleVoList.add(contractDetail);
                if (contractFee != null) {
                    hisEarly = hisEarly.add(contractFee);
                    WriteOffContractDetailManage manage = new WriteOffContractDetailManage();
                    manage.setAdvanceSettleAmount(contractFee);
                    manage.setContractNo(detailManage.getContractNo());
                    manage.setDeductRate(contractDetail.getDeductRate());
                    managesList.add(manage);
                }
            }
            if (hisEarly.compareTo(new BigDecimal(0)) != 0) {
                resultDTO.setHisDetails(managesList);
                resultDTO.setHisAdvanceResul(hisEarly);
                earlyPay = earlyPay.add(hisEarly);
            }
        }

        // 本期提前结清合同
        List<SettleContractDetailVO> currentData = map.get(Boolean.TRUE);
        if (CollUtil.isNotEmpty(currentData)) {
            if (currentDetail.size() <= 0) {
                throw new RuntimeException("数据异常，本期合同为空，本期不能有提前结清");
            }
            // 过滤当期提前结清的数据
            List<String> contractNos = currentData.stream().map(SettleContractDetailVO::getContractNo).distinct().toList();
            List<WriteOffContractDetailManage> list = currentDetail.stream()
                    .filter(current -> contractNos.contains(current.getContractNo()))
                    .toList();

            if (contractNos.size() != list.size()) {
                log.info("本期提前结清的合同和计算服务费的合同数量不一致,提前结清合同号{},计算中的合同号{}",
                        contractNos, list.stream().map(WriteOffContractDetailManage::getContractNo).toList());
            }
            log.info("本期提前结清扣减计算-服务费合同信息,合同号{}", JSON.toJSONString(contractNos));

            Map<String, SettleContractDetailVO> curDetails = currentData.stream()
                    .collect(Collectors.toMap(SettleContractDetailVO::getContractNo, Function.identity(),
                            // 存在两个提前结清还款计划项,取旧数据的期数
                            (existingValue, newValue) -> existingValue.getTermNo() < newValue.getTermNo()
                                    ? existingValue
                                    : newValue));

            Map<String, WriteOffContractDetailManage> collect = list.stream()
                    .collect(Collectors.toMap(WriteOffContractDetailManage::getContractNo, Function.identity()));

            List<WriteOffContractDetailManage> curManages = new ArrayList<>();
            BigDecimal currEarly = new BigDecimal(0);
            for (SettleContractDetailVO contractDetail : curDetails.values()) {
                WriteOffContractDetailManage detailManage = collect.get(contractDetail.getContractNo());
                if (detailManage == null) {
                    log.info("本期{}提前结清扣减计算未匹配到服务费合同明细", contractDetail.getContractNo());
                    continue;
                }
                contractDetail.setServiceFee(detailManage.getServiceCharge());
                contractDetail.setBelongingCapital(detailManage.getBelongingCapital());
                // 扣减金额计算
                BigDecimal contractFee = calculateTheDeductionAmount(contractDetail);
                settleVoList.add(contractDetail);
                if (contractFee != null) {
                    currEarly = currEarly.add(contractFee);
                    // 设置当期提起结清数据
                    detailManage.setContractStatus(ContractStatusEnum.advanceSettle);
                    detailManage.setAdvanceSettleFlag(Boolean.TRUE);
                    detailManage.setAdvanceSettleAmount(contractFee);
                    detailManage.setDeductRate(contractDetail.getDeductRate());
                    detailManage.setAdvanceSettleMonth(billingPeriod);
                    curManages.add(detailManage);
                }
            }
            if (currEarly.compareTo(new BigDecimal(0)) != 0) {
                resultDTO.setCurDetails(curManages);
                resultDTO.setCurAdvanceResul(currEarly);
                earlyPay = earlyPay.add(currEarly);
            }
        }
        resultDTO.setSettleVoList(settleVoList);
        if (earlyPay.compareTo(new BigDecimal(0)) != 0) {
            resultDTO.setAdvanceResul(earlyPay);
            log.info("提前结清扣减汇总结果{}", JSON.toJSONString(resultDTO));
            return resultDTO;
        }
        return null;
    }

    private BigDecimal calculateTheDeductionAmount(SettleContractDetailVO infoVo) {
        if (infoVo.getServiceFee() == null) {
            log.info("服务费为空，未开始计算{}",JSON.toJSONString(infoVo));
            return null;
        }

        Assert.isTrue(StrUtil.isNotBlank(infoVo.getBelongingCapital()), "合同号{" + infoVo.getContractNo() + "}所属资方为空");
        List<WriteOffRule> earlyRuleList = this.list(Wrappers.<WriteOffRule>lambdaQuery()
                .eq(WriteOffRule::getStatus, StatusEnum.YES.getCode())
                .eq(WriteOffRule::getType, CalRuleEnum.EARLY_PAY.getCode())).stream().filter(k ->
                Arrays.asList(k.getBelongingCapital().split(",")).contains(infoVo.getBelongingCapital())).toList();
        Assert.isTrue(earlyRuleList.size() == 1, "合同号{" + infoVo.getContractNo() + "}提前结清规则不存在");
        WriteOffRule writeOffRule = earlyRuleList.get(0);
        // 规则原子参数入参
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(WriteOffConstant.SETTLE_DATE, parseDateToInt(infoVo.getSettleDate()));
        jsonObject.put(WriteOffConstant.INTO_FIRST_DATE, parseDateToInt(infoVo.getIntoFirstDate()));
        jsonObject.put(WriteOffConstant.LOAN_DATE, parseDateToInt(infoVo.getLoanDate()));
        // 已偿期数计算 DATEDIF(激活日期,结清日期,"m")
        jsonObject.put(WriteOffConstant.PAID_NUM, infoVo.getTermNo());
        jsonObject.put(WriteOffConstant.FINANCE_TYPE, infoVo.getProductNum());
        log.info("合同号码：{},计算提前结清扣减规则入参：{}", infoVo.getContractNo(), jsonObject);

        RuleRunResult ruleRunResult = RuleHelper.runByGroup(jsonObject, "WriteOffAdvanceRuleAtom", false,
            RuleRunEnum.PARALLEL, false);
        boolean isHit = ruleRunResult.getHit();
        log.info("合同号码：{},计算提前结清扣减规则出参：{}", infoVo.getContractNo(), isHit);
        if (isHit) {
            List<Long> ruleNoList = ruleRunResult.getResults()
                .stream()
                .map(k -> Long.valueOf(k.getRuleNo()))
                .collect(Collectors.toList());
            log.info("合同号码：{}，提前结清扣减规则命中ruleNos：{}", infoVo.getContractNo(), ruleNoList);

            List<WriteOffRuleDetail> ruleDetailList = writeOffRuleDetailService.listByIds(ruleNoList).stream()
                    .filter(k-> Objects.equals(k.getWriteOffRuleId(), writeOffRule.getId())).toList();

            if (CollUtil.isEmpty(ruleDetailList)) {
                log.info("合同号码{},未找到满足的提前结清扣减规则跳过", infoVo.getContractNo());
            } else if (ruleDetailList.size() > 1) {
                log.error("合同号{},命中多条提前结清扣减规则{}", infoVo.getContractNo(),
                    JSON.toJSONString(ruleDetailList));
                throw new AfsBaseException("合同号码:{" + infoVo.getContractNo() + "}," + "命中多条提前结清扣减规则");
            } else {
                log.error("合同号{},提前结清扣减规则命中{},服务费{}", infoVo.getContractNo(),
                    JSON.toJSONString(ruleDetailList), infoVo.getServiceFee());
                // 扣减费用计算
                Integer deductRate = ruleDetailList.get(0).getDeductRate();
                infoVo.setDeductRate(deductRate);
                BigDecimal settle = new BigDecimal(deductRate).divide(new BigDecimal(100), 5,
                    RoundingMode.HALF_UP).multiply(infoVo.getServiceFee()).setScale(2, RoundingMode.HALF_UP);
                log.info("合同号{},结清扣减计算成功{}", infoVo.getContractNo(), settle);
                return settle;
            }
        } else {
            log.info("合同号码:{},提前结清扣减规则未命中", infoVo.getContractNo());
        }
        return null;
    }

    public static Integer parseDateToInt(Date loanDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = sdf.format(loanDate);
        return Integer.parseInt(formattedDate);
    }

    /**
     * 组装计算规则原子
     *
     * @param detail
     * @return
     */
    private JSONObject packageRuleAtom(WriteOffContractDetailManage detail, Map<Long, String> productMap, Map<String, List<WriteOffChannelGroupDetail>> channelGroupMap) {
        ChannelRiskInfo riskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>lambdaQuery()
                .select(ChannelRiskInfo::getQualityGrade)
                .eq(ChannelRiskInfo::getChannelId, detail.getChannelId())
                .eq(ChannelRiskInfo::getBusinessType, detail.getBusinessType()));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("channelInfo.channelCity", detail.getChannelCity());
        jsonObject.put("channelGrade", riskInfo.getQualityGrade());
        jsonObject.put("financeType", productMap.get(Long.parseLong(detail.getProductId())));
        jsonObject.put("discountType", detail.getDiscountType());
        jsonObject.put("loanTerm", detail.getLoanTerm());
        jsonObject.put("custRate", detail.getCustRate());
        jsonObject.put("channelCode", detail.getChannelCode());
        List<WriteOffChannelGroupDetail> groupDetails = channelGroupMap.get(detail.getChannelCode());
        if (groupDetails != null) {
            for (WriteOffChannelGroupDetail groupDetail : groupDetails) {
                jsonObject.put(groupDetail.getGroupType(), groupDetail.getGroupId().toString());
            }
        }
        detail.setQualityGrade(riskInfo.getQualityGrade());
        return jsonObject;
    }

    /**
     * 运行服务费计算周期规则
     */
    private boolean runMonthRule(WriteOffContractDetailManage detail, Map<Long, String> productMap, Long ruleGroup) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("financeType", productMap.get(Long.parseLong(detail.getProductId())));
        jsonObject.put("belongingCapital", detail.getBelongingCapital());
        log.info("合同号码：{},周期服务费规则报文：{}", detail.getContractNo(), jsonObject);
        RuleRunResult ruleRunResult = RuleHelper.runByGroup(jsonObject, ruleGroup.toString(), false, RuleRunEnum.PARALLEL, false);
        log.info("合同号码：{},计算周期：{},命中结果：{}", detail.getContractNo(), ruleGroup, ruleRunResult.getHit());
        return ruleRunResult.getHit();
    }

    /**
     * 生成服务费核销项编号
     * @return
     */
    public String generateApply() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        Long seqNo = afsSequenceGenerator.genNext(CaseConstants.WRITE_OFF_APPLY_NO, dateFormat.format(Calendar.getInstance().getTime()));
        String seqStr = StringUtils.leftPad(seqNo.toString(), 6, "0");
        String flawNo = CaseConstants.WRITE_OFF_APPLY_NO + "-"
                + dateFormat.format(Calendar.getInstance().getTime()) + "-" + seqStr;
        return flawNo;
    }


    /**
     * 服务费分摊数据埋点
     */
    private void saveServiceFeeData(String channelCode, Long channelId, String voucher, String channelName) {
        log.info("{}服务费分摊发布埋点数据{}", channelCode, voucher);
        VoucherFlowInfoDto voucherFlowInfoDto = new VoucherFlowInfoDto();

        voucherFlowInfoDto.setBuriedPointNo(VoucherBuriedPointNo.writeOffApportion);
        voucherFlowInfoDto.setTransNo(voucher);
        voucherFlowInfoDto.setKeepAccountDate(new Date());//发布成功时间
        voucherFlowInfoDto.setContractNo(channelCode);//合同号
        voucherFlowInfoDto.setDealerName(channelName);
        voucherFlowInfoDto.setChannelId(String.valueOf(channelId));
        voucherFlowInfoDto.setCustNo(String.valueOf(channelId));

        log.info("服务费分摊发布埋点成功,开始保存流水{}", voucherFlowInfoDto);
        mqMessageQueueLogService.saveMqMessage(voucherFlowInfoDto);
    }

    /**
     * 服务费计算规则导出
     * @param response
     * @return
     */
    @Override
    public void writeOffRuleExport(HttpServletResponse response) {
        List<WriteOffRuleExportVo> exportVoList = new ArrayList<>();
        List<WriteOffRule> writeOffRuleList = this.list();
        for (WriteOffRule writeOffRule : writeOffRuleList) {
            String typeName = writeOffRule.getTypeName();
            String status = "1".equals(writeOffRule.getStatus()) ? "有效" : "无效";
            List<WriteOffRuleDetail> ruleDetails = writeOffRuleDetailService.list(Wrappers.<WriteOffRuleDetail>lambdaQuery().eq(WriteOffRuleDetail::getWriteOffRuleId, writeOffRule.getId()));
            for (WriteOffRuleDetail ruleDetail : ruleDetails) {
                WriteOffRuleExportVo exportVo = new WriteOffRuleExportVo();
                exportVo.setTypeName(typeName);
                exportVo.setStatus(status);
                exportVo.setRuleName(ruleDetail.getRuleName());
                exportVo.setRate(ruleDetail.getBackRate() != null ? ruleDetail.getBackRate() : ruleDetail.getServeRate());
                exportVo.setRuleExpress(ruleDetail.getRuleExpress());
                exportVoList.add(exportVo);
            }
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("服务费计算规则导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), WriteOffRuleExportVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, "服务费计算规则").build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }

}
