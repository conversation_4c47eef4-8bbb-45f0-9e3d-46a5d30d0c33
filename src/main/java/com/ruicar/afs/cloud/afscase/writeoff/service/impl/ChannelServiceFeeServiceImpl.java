package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffBaseAndTaskInfo;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ChannelServiceFeeDTO;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ChannelServiceFeeVo;
import com.ruicar.afs.cloud.afscase.writeoff.dto.DealerUpdateAmount;
import com.ruicar.afs.cloud.afscase.writeoff.dto.PermissionResultDto;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ServiceFeeWorkInfoResp;
import com.ruicar.afs.cloud.afscase.writeoff.entity.ChannelServiceFee;
import com.ruicar.afs.cloud.afscase.writeoff.entity.ServiceFeeWorkInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.OverdueStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.ChannelServiceFeeMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInvoiceRelMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.ChannelServiceFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPermissionService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.ChannelServiceFeeExportVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeSubmitVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffRelExportVo;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.uid.AfsSequenceGenerator;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.PaymentStatusEnum;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPayStatusReqDTO;
import com.ruicar.afs.cloud.interfaces.cbs.dto.resp.CbsPayRespStatusDTO;
import com.ruicar.afs.cloud.interfaces.cbs.enums.CBSRPPayStatusEnum;
import com.ruicar.afs.cloud.interfaces.cbs.service.CbsService;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Service
public class ChannelServiceFeeServiceImpl extends ServiceImpl<ChannelServiceFeeMapper, ChannelServiceFee> implements ChannelServiceFeeService {

    private WorkflowHelper workflowHelper;
    private WorkflowWrapperService workflowWrapperService;
    private CaseApproveRecordService recordService;
    private WorkflowTaskInfoService taskInfoService;
    private AfsSequenceGenerator afsSequenceGenerator;
    private ChannelServiceFeeMapper channelServiceFeeMapper;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private final UserDetailsInfoFeign userDetailsInfoFeign;
    private WriteOffBaseInfoMapper baseInfoMapper;
    private static final String LOCK_CBS_PAY = "l:cbs:p:r";
    private WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final CbsService cbsService;
    private final StringRedisTemplate redisTemplate;
    @Resource
    private WriteOffBaseInvoiceRelMapper writeOffBaseInvoiceRelMapper;
    private final WriteOffPermissionService writeOffPermissionService;
    private final WriteOffPayRecordService writeOffPayRecordService;

    @Override
    public IPage<ChannelServiceFeeVo> queryTaskList(Page page, ChannelServiceFeeDTO condition) {
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        if (!resultDto.getIsAll()) {
            condition.setHitCodeList(resultDto.getHitCodeList());
        }
        IPage<ChannelServiceFeeVo> channelServiceFeeList = baseMapper.getDataList(page, condition);
        log.info("查询所有经销商服务费提取信息{}", channelServiceFeeList);
        channelServiceFeeList.getRecords().forEach(channelServiceFee -> {
            BigDecimal amount = ObjectUtils.isEmpty(channelServiceFee.getAmount()) ? BigDecimal.ZERO : channelServiceFee.getAmount();
            BigDecimal notExtractedAmount = ObjectUtils.isEmpty(channelServiceFee.getNotExtractedAmount()) ? BigDecimal.ZERO : channelServiceFee.getNotExtractedAmount();
            BigDecimal extractedAmount = ObjectUtils.isEmpty(channelServiceFee.getExtractedAmount()) ? BigDecimal.ZERO : channelServiceFee.getExtractedAmount();
            BigDecimal paySuccessAmount = ObjectUtils.isEmpty(channelServiceFee.getPaySuccessAmount()) ? BigDecimal.ZERO : channelServiceFee.getPaySuccessAmount();

            channelServiceFee.setAmount(amount);
            channelServiceFee.setNotExtractedAmount(notExtractedAmount);
            channelServiceFee.setExtractedAmount(extractedAmount);
            channelServiceFee.setPaySuccessAmount(paySuccessAmount);

            List<String> statusList = new ArrayList<>();
            statusList.add(ChannelServiceFeeEnum.STATUS_1.code);//审核中状态
            statusList.add(ChannelServiceFeeEnum.STATUS_2.code);//退回状态

            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                    .eq(WriteOffBaseInvoiceRel::getDealerCode, channelServiceFee.getChannelCode())
                    .in(WriteOffBaseInvoiceRel::getApproveStatus, statusList));

            if (ObjectUtils.isNotEmpty(writeOffBaseInvoiceRels)) {
                BigDecimal onApproveAmount = BigDecimal.ZERO;
                for (WriteOffBaseInvoiceRel writeOffBaseInvoiceRel : writeOffBaseInvoiceRels) {
                    BigDecimal amt = ObjectUtils.isEmpty(writeOffBaseInvoiceRel.getInvoiceAmount()) ? BigDecimal.ZERO : writeOffBaseInvoiceRel.getInvoiceAmount();
                    onApproveAmount = onApproveAmount.add(amt);
                }

                channelServiceFee.setOnApproveAmount(onApproveAmount);
            } else {
                channelServiceFee.setOnApproveAmount(BigDecimal.ZERO);
            }
        });
        log.info("查询所有经销商服务费提取信息最终结果{}", channelServiceFeeList);

        return channelServiceFeeList;
    }

    @Override
    public void exportTaskList(ChannelServiceFeeDTO condition, HttpServletRequest request, HttpServletResponse response) {
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        if (!resultDto.getIsAll()) {
            condition.setHitCodeList(resultDto.getHitCodeList());
        }
        List<ChannelServiceFeeVo> channelServiceFeeList = baseMapper.getDataList(condition);
        log.info("查询所有经销商服务费提取信息{}", channelServiceFeeList);
        channelServiceFeeList.forEach(channelServiceFee -> {
            BigDecimal amount = ObjectUtils.isEmpty(channelServiceFee.getAmount()) ? BigDecimal.ZERO : channelServiceFee.getAmount();
            BigDecimal notExtractedAmount = ObjectUtils.isEmpty(channelServiceFee.getNotExtractedAmount()) ? BigDecimal.ZERO : channelServiceFee.getNotExtractedAmount();
            BigDecimal extractedAmount = ObjectUtils.isEmpty(channelServiceFee.getExtractedAmount()) ? BigDecimal.ZERO : channelServiceFee.getExtractedAmount();
            BigDecimal paySuccessAmount = ObjectUtils.isEmpty(channelServiceFee.getPaySuccessAmount()) ? BigDecimal.ZERO : channelServiceFee.getPaySuccessAmount();

            channelServiceFee.setAmount(amount);
            channelServiceFee.setNotExtractedAmount(notExtractedAmount);
            channelServiceFee.setExtractedAmount(extractedAmount);
            channelServiceFee.setPaySuccessAmount(paySuccessAmount);

            List<String> statusList = new ArrayList<>();
            statusList.add(ChannelServiceFeeEnum.STATUS_1.code);//审核中状态
            statusList.add(ChannelServiceFeeEnum.STATUS_2.code);//退回状态

            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                    .eq(WriteOffBaseInvoiceRel::getDealerCode, channelServiceFee.getChannelCode())
                    .in(WriteOffBaseInvoiceRel::getApproveStatus, statusList));

            if (ObjectUtils.isNotEmpty(writeOffBaseInvoiceRels)) {
                BigDecimal onApproveAmount = BigDecimal.ZERO;
                for (WriteOffBaseInvoiceRel writeOffBaseInvoiceRel : writeOffBaseInvoiceRels) {
                    BigDecimal amt = ObjectUtils.isEmpty(writeOffBaseInvoiceRel.getInvoiceAmount()) ? BigDecimal.ZERO : writeOffBaseInvoiceRel.getInvoiceAmount();
                    onApproveAmount = onApproveAmount.add(amt);
                }

                channelServiceFee.setOnApproveAmount(onApproveAmount);
            } else {
                channelServiceFee.setOnApproveAmount(BigDecimal.ZERO);
            }
        });
        log.info("查询所有经销商服务费提取信息最终结果{}", channelServiceFeeList);

        List<ChannelServiceFeeExportVo> collect = channelServiceFeeList.stream().map(item -> {
            ChannelServiceFeeExportVo inputTaxExportVo = new ChannelServiceFeeExportVo();
            BeanUtils.copyProperties(item, inputTaxExportVo);
            return inputTaxExportVo;
        }).collect(Collectors.toList());

        try{
            String fileName = URLEncoder.encode("服务费结算_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ChannelServiceFeeExportVo.class).sheet("服务费结算").doWrite(collect);

        }catch (Exception e){
            log.error("生成导出文件名异常， ",  e);
        }

    }

    @Override
    public void addOrUpdateData(ChannelServiceFee channelServiceFee) {
        log.info("经销商服务费提取入参数据：{}", channelServiceFee);

        ChannelServiceFee channelServiceFeeData = channelServiceFeeMapper.selectOne(Wrappers.<ChannelServiceFee>lambdaQuery()
                .eq(ChannelServiceFee::getChannelCode, channelServiceFee.getChannelCode()));
        if (ObjectUtils.isEmpty(channelServiceFeeData)) {
            channelServiceFee.setAmount(ObjectUtils.isEmpty(channelServiceFee.getAmount()) ? BigDecimal.ZERO : channelServiceFee.getAmount());
            channelServiceFee.setExtractedAmount(ObjectUtils.isEmpty(channelServiceFee.getExtractedAmount()) ? BigDecimal.ZERO : channelServiceFee.getExtractedAmount());
            //初次进入设置未提取金额
            channelServiceFee.setNotExtractedAmount(ObjectUtils.isEmpty(channelServiceFee.getAmount()) ? BigDecimal.ZERO : channelServiceFee.getAmount());

            channelServiceFeeMapper.insert(channelServiceFee);
        } else {
            //计算总服务提取费
            BigDecimal newAmount = ObjectUtils.isEmpty(channelServiceFee.getAmount()) ? BigDecimal.ZERO : channelServiceFee.getAmount();
            BigDecimal oldAmount = ObjectUtils.isEmpty(channelServiceFeeData.getAmount()) ? BigDecimal.ZERO : channelServiceFeeData.getAmount();
            channelServiceFeeData.setAmount(newAmount.add(oldAmount));

            //计算总未提取费用
            BigDecimal notExtractedAmount = ObjectUtils.isEmpty(channelServiceFeeData.getNotExtractedAmount()) ? BigDecimal.ZERO : channelServiceFeeData.getNotExtractedAmount();
            channelServiceFeeData.setNotExtractedAmount(newAmount.add(notExtractedAmount));

            channelServiceFeeMapper.updateById(channelServiceFeeData);
        }
    }


    /**
     * 经销商提取服务费金额回滚
     * @param channelCode
     * @param amount
     */
    @Override
    public void reverseChannelAmount(String channelCode, BigDecimal amount) {
        log.info("经销商服务费回滚数据：{}--->{}", channelCode, amount);
        ChannelServiceFee channelServiceFeeData = channelServiceFeeMapper.selectOne(Wrappers.<ChannelServiceFee>lambdaQuery()
                .eq(ChannelServiceFee::getChannelCode, channelCode));
        Assert.isTrue(channelServiceFeeData != null, "数据异常！");
        channelServiceFeeData.setAmount(channelServiceFeeData.getAmount().subtract(amount));
        channelServiceFeeData.setNotExtractedAmount(channelServiceFeeData.getNotExtractedAmount().subtract(amount));
        channelServiceFeeMapper.updateById(channelServiceFeeData);
    }

    @Override
    public IResponse submitApprove(String channelCode) {
        IResponse response = null;
        IResponse backResponse = null;
        StartFlowRequestBo requestBo = new StartFlowRequestBo();

        String applyNo = "";
        requestBo.setPackageId("write-off-application");
        requestBo.setTemplateId("service-charge-withdrawal");

        //退回数据
        List<WriteOffBaseInvoiceRel> writeList1 = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                .eq(WriteOffBaseInvoiceRel::getDealerCode, channelCode)
                .eq(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_2.code));//退回状态

        //未审核数据
        List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                .eq(WriteOffBaseInvoiceRel::getDealerCode, channelCode)
                .eq(WriteOffBaseInvoiceRel::getStatus, ChannelServiceFeeEnum.STATUS_0.code)//未提取
                .in(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_0.code)
                .ne(WriteOffBaseInvoiceRel::getInvoiceAmount, BigDecimal.ZERO)
                .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));

        //现根据批次号去重，同一批次号内根据核销项编号去重
        List<WriteOffBaseInvoiceRel> writeList2 = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(writeOffBaseInvoiceRels)) {
            List<WriteOffBaseInvoiceRel> batchNoList = writeOffBaseInvoiceRels.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(WriteOffBaseInvoiceRel::getCurrentBatchNo))), ArrayList::new));
            if (ObjectUtils.isNotEmpty(batchNoList)) {
                for (WriteOffBaseInvoiceRel batchNo : batchNoList) {
                    List<WriteOffBaseInvoiceRel> relList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                            .eq(WriteOffBaseInvoiceRel::getCurrentBatchNo, batchNo.getCurrentBatchNo())
                            .eq(WriteOffBaseInvoiceRel::getStatus, ChannelServiceFeeEnum.STATUS_0.code)
                            .eq(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_0.code)
                            .ne(WriteOffBaseInvoiceRel::getInvoiceAmount, BigDecimal.ZERO));

                    if (ObjectUtils.isNotEmpty(relList)) {
                        List<WriteOffBaseInvoiceRel> invoiceRels = relList.stream().collect(Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(
                                        Comparator.comparing(WriteOffBaseInvoiceRel::getApplyNo))), ArrayList::new));

                        for (WriteOffBaseInvoiceRel writeOffBaseInvoiceRel : invoiceRels) {
                            writeList2.add(writeOffBaseInvoiceRel);
                        }
                    }
                }
            }
        }

        log.info("服务费提取工作流退回数据{}未审核数据{}", JSON.toJSONString(writeList1), JSON.toJSONString(writeList2));

        if (ObjectUtils.isNotEmpty(writeList1) && ObjectUtils.isEmpty(writeList2)) {
            log.info("发起退回流程。。。。。。");
            List<WriteOffBaseInvoiceRel> infoCollect = writeList1.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(WriteOffBaseInvoiceRel::getApproveId))), ArrayList::new));

            for (WriteOffBaseInvoiceRel baseInfo : infoCollect) {
                String approveId = baseInfo.getApproveId();//退回数据申请编号
                log.info("重新发起服务费提取工作流{}", approveId);

                WorkflowTaskInfo list = taskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo, approveId).last("limit 1"));
                backResponse = workflowHelper.resumeProcess(list.getProcessInstanceId());

                if (ObjectUtils.isNotEmpty(backResponse)) {
                    if("0000".equals(backResponse.getCode())) {
                        //日志保存
                        this.buildParamAgain(approveId);
                        //更新周期服务费状态
                        List<WriteOffBaseInvoiceRel> writeOffList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                                .eq(WriteOffBaseInvoiceRel::getApproveId, approveId));
                        for (WriteOffBaseInvoiceRel writeOff : writeOffList) {
                            writeOff.setApproveStatus(ChannelServiceFeeEnum.STATUS_1.code);//重新设置审核中
                            writeOff.setApproveId(approveId);

                            writeOffBaseInvoiceRelService.updateById(writeOff);
                        }
                    } else {
                        log.info("订单工作流发起失败{}",backResponse.getMsg());
                        return IResponse.fail("发起工作流失败");
                    }
                }
            }
        } else if (ObjectUtils.isEmpty(writeList1) && ObjectUtils.isNotEmpty(writeList2)) {
            applyNo = this.getApplyNo();
            requestBo = this.buildParam(applyNo, requestBo);
            log.info("{}发起服务费提取工作流{}", applyNo, requestBo);

            response = workflowHelper.startFlow(requestBo, UseSceneEnum.SERVICE_CHARGE_WITHDRAWAL);
        } else if (ObjectUtils.isNotEmpty(writeList1) && ObjectUtils.isNotEmpty(writeList2)) {
            log.info("服务费新流程和退回流程一起发起。。。。。");

            List<WriteOffBaseInvoiceRel> infoCollect = writeList1.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(WriteOffBaseInvoiceRel::getApproveId))), ArrayList::new));

            for (WriteOffBaseInvoiceRel baseInfo : infoCollect) {
                String approveId = baseInfo.getApproveId();//退回数据申请编号
                log.info("重新发起服务费提取工作流{}", approveId);

                WorkflowTaskInfo list = taskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo, approveId).last("limit 1"));
                backResponse = workflowHelper.resumeProcess(list.getProcessInstanceId());

                if (ObjectUtils.isNotEmpty(backResponse)) {
                    if("0000".equals(backResponse.getCode())) {
                        //日志保存
                        this.buildParamAgain(approveId);
                        //更新周期服务费状态
                        List<WriteOffBaseInvoiceRel> relList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                                .eq(WriteOffBaseInvoiceRel::getApproveId, approveId));
                        for (WriteOffBaseInvoiceRel rel : relList) {
                            rel.setApproveStatus(ChannelServiceFeeEnum.STATUS_1.code);//重新设置审核中
                            rel.setApproveId(approveId);

                            writeOffBaseInvoiceRelService.updateById(rel);
                        }
                    } else {
                        log.info("订单工作流发起失败{}",backResponse.getMsg());
                        return IResponse.fail("发起工作流失败");
                    }
                }

            }

            //新流程
            applyNo = this.getApplyNo();
            requestBo = this.buildParam(applyNo, requestBo);
            log.info("{}发起服务费提取新流程工作流{}", applyNo, requestBo);

            response = workflowHelper.startFlow(requestBo, UseSceneEnum.SERVICE_CHARGE_WITHDRAWAL);

        } else {
            return IResponse.fail("没有可发起的任务");
        }

        if (ObjectUtils.isNotEmpty(response)) {
            if("0000".equals(response.getCode())) {
                //服务费提取状态
                for (WriteOffBaseInvoiceRel invoiceRel : writeOffBaseInvoiceRels) {
                                        
                    if(writeList2.stream().anyMatch(x -> { return x.getId().compareTo(invoiceRel.getId()) == 0;})){
                             invoiceRel.setApproveId(applyNo);
                             invoiceRel.setApproveStatus("1");//审核中     
                    }
                    writeOffBaseInvoiceRelService.updateById(invoiceRel);
                }


            } else {
                log.info("订单工作流发起失败{}",response.getMsg());
                return IResponse.fail("发起工作流失败");
            }
        }

        return IResponse.success("发起工作流成功");
    }

    @Override
    public IResponse<String> submitApproveNormal(FeeSubmitVo submitVo) {
        List<Long> relIdList = submitVo.getRelIdList();
        if (CollectionUtil.isEmpty(relIdList)) {
            throw new AfsBaseException("勾选数据不能为空");
        }
        StartFlowRequestBo requestBo = new StartFlowRequestBo();
        String applyNo = "";
        requestBo.setPackageId("write-off-application");
        requestBo.setTemplateId("service-charge-withdrawal");
        //未提取数据
        List<String> statusList = new ArrayList<>();
        statusList.add(ChannelServiceFeeEnum.STATUS_0.code);
        statusList.add(ChannelServiceFeeEnum.STATUS_2.code);
        List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                .in(WriteOffBaseInvoiceRel::getId, relIdList)
                .in(WriteOffBaseInvoiceRel::getApproveStatus, statusList)
                .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));

        Assert.isTrue(relIdList.size() == writeOffBaseInvoiceRels.size(), "请选择未提取或已撤回的数据发起!");
        // 校验数据
        List<String> applyNoList = writeOffBaseInvoiceRels.stream().map(k -> WriteOffTypeEnum.ZJD.getCode().equals(k.getWriteOffType()) ? k.getApplyNo().substring(0, k.getApplyNo().lastIndexOf("_")) : k.getApplyNo()).toList();
        List<WriteOffBaseInfo> frozenList = baseInfoMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .in(WriteOffBaseInfo::getApplyNo, applyNoList)
                .in(WriteOffBaseInfo::getFrozenStatus, List.of(FrozenStatusEnum.FROZEN,FrozenStatusEnum.SUSPEND_PAYMENT)));
        if (CollUtil.isNotEmpty(frozenList)) {
            throw new AfsBaseException("编号为：" + frozenList.get(0).getApplyNo() + "的核销项已冻结或专项管理，不可发起流程！");
        }

        List<WriteOffBaseInfo> reFrozenList = baseInfoMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery()
            .in(WriteOffBaseInfo::getApplyNo, applyNoList)
            .eq(WriteOffBaseInfo::getReturnOverdueStatus, OverdueStatusEnum.FROZEN.getCode()));
        if (!reFrozenList.isEmpty()) {
            throw new AfsBaseException("编号为：" + reFrozenList.get(0).getApplyNo() + "的核销项处于回司超期冻结状态，不可发起流程！");
        }
        Map<String, List<WriteOffBaseInvoiceRel>> batchNoRelMap = writeOffBaseInvoiceRels.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getCurrentBatchNo));
        for (Map.Entry<String, List<WriteOffBaseInvoiceRel>> stringListEntry : batchNoRelMap.entrySet()) {
            List<WriteOffBaseInvoiceRel> value = stringListEntry.getValue();
            if (!WriteOffTypeEnum.ZJD.getCode().equals(value.get(0).getWriteOffType())) {
                String batchNo = stringListEntry.getKey();
                List<WriteOffBaseInvoiceRel> realList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                        .select(WriteOffBaseInvoiceRel::getDealerName)
                        .eq(WriteOffBaseInvoiceRel::getCurrentBatchNo, batchNo));
                Assert.isTrue(realList.size() == value.size(), "["+realList.get(0).getDealerName() + "]同批次下不能漏选数据！");
            }
        }

        applyNo = this.getApplyNo();
        requestBo = this.buildParam(applyNo, requestBo);
        log.info("{}发起服务费提取工作流", applyNoList);
        IResponse response = workflowHelper.startFlow(requestBo, UseSceneEnum.SERVICE_CHARGE_WITHDRAWAL);
        if ("0000".equals(response.getCode())) {
            //服务费提取状态
            for (WriteOffBaseInvoiceRel invoiceRel : writeOffBaseInvoiceRels) {
                invoiceRel.setApproveId(applyNo);
                invoiceRel.setFileBusiNo(submitVo.getBusiNo());
                invoiceRel.setApproveStatus(ChannelServiceFeeEnum.STATUS_1.code);//审核中
                invoiceRel.setSubmitDate(new Date());
                invoiceRel.setPayStatus(PaymentStatusEnum.waitPayment);
            }
            writeOffBaseInvoiceRelService.updateBatchById(writeOffBaseInvoiceRels);
        } else {
            log.info("服务费提取工作流发起失败{}", response.getMsg());
            return IResponse.fail("发起流程失败");
        }
        return IResponse.success("发起流程成功");
    }

    @Override
    public void updatePaySuccessAmount(List<DealerUpdateAmount> dto) {
        for (DealerUpdateAmount dealerUpdateAmount : dto) {
            LambdaQueryWrapper<ChannelServiceFee> query = Wrappers.lambdaQuery();
            query.eq(ChannelServiceFee::getChannelCode,dealerUpdateAmount.getDealerCode());
            ChannelServiceFee one = this.getOne(query);
            if (null != one) {
                BigDecimal old = null == one.getPaySuccessAmount() ? BigDecimal.ZERO : one.getPaySuccessAmount();
                BigDecimal add = null == dealerUpdateAmount.getPaySuccessAmount() ? BigDecimal.ZERO : dealerUpdateAmount.getPaySuccessAmount();
                one.setPaySuccessAmount(old.add(add));
            }
            this.updateById(one);
        }
    }

    /**
     * 查询cbs付款结果，激活等待节点
     * @param relIdList
     * @return
     */
    @Override
    public IResponse<String> getCbsPayResult(List<Long> relIdList) {
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(LOCK_CBS_PAY, "lock", 2, TimeUnit.HOURS);
        if (Boolean.FALSE.equals(lock)) {
            throw new AfsBaseException("在查询中，请勿重复点击");
        }
        List<WriteOffBaseInvoiceRel> payingRelList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                .eq(WriteOffBaseInvoiceRel::getPayStatus, PaymentStatusEnum.paying));
        if (payingRelList.size() <= 0) {
            log.info("不存在付款中的服务费");
            return IResponse.success("不存在付款中的服务费");
        }
        //根据流程分组
        Map<String, List<WriteOffBaseInvoiceRel>> collect = payingRelList.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getApproveId));
        for (Map.Entry<String, List<WriteOffBaseInvoiceRel>> entry : collect.entrySet()) {
            try {
                boolean passFlag = true;
                String businessNo = entry.getKey();
                List<WriteOffBaseInvoiceRel> relList = entry.getValue();
                //根据经销商分组
                Map<String, List<WriteOffBaseInvoiceRel>> channelRelList = relList.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getDealerCode));
                for (Map.Entry<String, List<WriteOffBaseInvoiceRel>> channelEntry : channelRelList.entrySet()) {
                    String dealerCode = channelEntry.getKey();
                    List<WriteOffBaseInvoiceRel> channelRels = channelEntry.getValue();
                    String cbsBusNbr = channelRels.get(0).getCbsBusNbr();
                    String cbsRefNbr = channelRels.get(0).getCbsRefNbr();
                    if (StrUtil.isBlank(cbsBusNbr)) {
                        log.error("[服务费查询cbs结果]异常，cbs返回流水号为空，流程编号:{}，经销商编号:{}", businessNo, dealerCode);
                        passFlag = false;
                    } else {
                        CbsPayStatusReqDTO req = new CbsPayStatusReqDTO();
                        req.setBusNbr(cbsBusNbr);
                        req.setRefNbr(cbsRefNbr);
                        IResponse<CbsPayRespStatusDTO> resp = cbsService.getPayResult(req);
                        log.info("流程编号:{}，经销商编号:{}，付款结果查询响应：{}", businessNo, dealerCode, JSON.toJSONString(resp));
                        if(CommonConstants.SUCCESS.equals(resp.getCode())){
                            CbsPayRespStatusDTO respBody = resp.getData();
                            if (respBody.getStatus() == CBSRPPayStatusEnum.SUCCESS) {
                                for (WriteOffBaseInvoiceRel channelRel : channelRels) {
                                    channelRel.setPayStatus(PaymentStatusEnum.successPayment);
                                }
                                log.info("付款成功，流程编号:{}，经销商编号:{}", businessNo, dealerCode);
                            }else if (respBody.getStatus() == CBSRPPayStatusEnum.FAILED || CBSRPPayStatusEnum.SEND_BACK == respBody.getStatus()) {
                                for (WriteOffBaseInvoiceRel channelRel : channelRels) {
                                    channelRel.setPayStatus(PaymentStatusEnum.failPayment);
                                    channelRel.setPayFailReason(respBody.getRemark());
                                }
                                log.warn("付款失败，流程编号:{}，经销商编号:{}", businessNo, dealerCode);
                                passFlag = false;
                            }else if (respBody.getStatus() == CBSRPPayStatusEnum.UN_DONE || CBSRPPayStatusEnum.UN_KNOW == respBody.getStatus()) {
                                log.warn("暂无付款结果，流程编号:{}，经销商编号:{}", businessNo, dealerCode);
                                passFlag = false;
                            }else {
                                passFlag = false;
                            }
                            writeOffBaseInvoiceRelService.updateBatchById(channelRels);
                        }else{
                            log.warn("经销商编号:{},付款结果查询接口调用失败：{}", dealerCode, resp.getMsg());
                            passFlag = false;
                        }
                    }
                }
                //如果该流程所有经销商都付款成功，才能激活等待节点
                List<WriteOffBaseInvoiceRel> invoiceRelList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                        .eq(WriteOffBaseInvoiceRel::getApproveId, businessNo));
                for (WriteOffBaseInvoiceRel invoiceRel : invoiceRelList) {
                    if (PaymentStatusEnum.successPayment != invoiceRel.getPayStatus()) {
                        passFlag = false;
                    }
                }
                if (passFlag) {
                    WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                            .eq(WorkflowTaskInfo::getBusinessNo, businessNo).orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1"));
                    IResponse response = workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
                    if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                        log.error("[服务费提取]激活等待节点失败，流程编号:{}", businessNo);
                        throw new AfsBaseException("流程发起失败!");
                    }
                    log.info("[服务费提取]激活等待节点成功，流程编号:{}", businessNo);
                }
            } catch (Exception e) {
                log.error("[服务费提取查询付款结果]出现异常:{},流程编号:{}", e.getMessage(), entry.getKey());
            }
        }
        redisTemplate.delete(LOCK_CBS_PAY);
        return IResponse.success("查询成功");
    }

    /**
     * 人工付款，激活等待节点
     * @param relIdList
     * @return
     */
    @Override
    public IResponse<String> artificialPay(List<Long> relIdList) {
        if (CollectionUtil.isEmpty(relIdList)) {
            throw new AfsBaseException("勾选数据不能为空");
        }
        log.info("服务费人工付款接收到请求参数：{}", relIdList);
        //付款失败
        List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                .in(WriteOffBaseInvoiceRel::getId, relIdList)
                .eq(WriteOffBaseInvoiceRel::getPayStatus, PaymentStatusEnum.failPayment)
                .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));

        Assert.isTrue(relIdList.size() == writeOffBaseInvoiceRels.size(), "请选择付款失败的数据!");
        List<String> busiList = new ArrayList<>();
        for (WriteOffBaseInvoiceRel invoiceRel : writeOffBaseInvoiceRels) {
            invoiceRel.setPayStatus(PaymentStatusEnum.successPayment);
            busiList.add(invoiceRel.getApproveId());
        }
        writeOffBaseInvoiceRelService.updateBatchById(writeOffBaseInvoiceRels);
        //如果该流程所有数据都付款成功，激活等待节点
        List<WriteOffBaseInvoiceRel> list = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery().in(WriteOffBaseInvoiceRel::getApproveId, busiList));
        Map<String, List<WriteOffBaseInvoiceRel>> collect = list.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getApproveId));
        for (Map.Entry<String, List<WriteOffBaseInvoiceRel>> entry : collect.entrySet()) {
            String businessNo = entry.getKey();
            List<WriteOffBaseInvoiceRel> rels = entry.getValue();
            List<WriteOffBaseInvoiceRel> paySuccessList = rels.stream().filter(k -> k.getPayStatus() == PaymentStatusEnum.successPayment).toList();
            if (paySuccessList.size() == rels.size()) {
                //激活等待节点
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo, businessNo).orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1"));
                IResponse response = workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
                if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                    log.error("[服务费提取]激活等待节点失败，流程编号:{}", businessNo);
                    throw new AfsBaseException("流程发起失败!");
                }
                log.info("[服务费提取]激活等待节点成功，流程编号:{}", businessNo);
            }
        }
        return IResponse.success("操作成功");
    }

    @Override
    public IResponse<String> failedRecordInitiatesRepay(List<Long> relIdList) {
        if (CollUtil.isEmpty(relIdList)) {
            throw new AfsBaseException("勾选数据不能为空");
        }
        log.info("服务费失败流程发起再次付款流程接收到请求参数：{}", relIdList);
        //付款失败
        List<WriteOffBaseInvoiceRel> records = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
            .in(WriteOffBaseInvoiceRel::getId, relIdList)
            .eq(WriteOffBaseInvoiceRel::getPayStatus, PaymentStatusEnum.failPayment)
            .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));
        Assert.isTrue(relIdList.size() == records.size(), "只能选择付款失败的数据!");
        // 以流程编号分组
        Map<String, List<WriteOffBaseInvoiceRel>> listMap = records.stream()
            .collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getApproveId));

        listMap.forEach((businessNo, v) -> {
            List<WriteOffBaseInvoiceRel> writeOffRel = writeOffBaseInvoiceRelService.list(
                Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                    .eq(WriteOffBaseInvoiceRel::getApproveId, businessNo)
                    .eq(WriteOffBaseInvoiceRel::getPayStatus, PaymentStatusEnum.failPayment)
                    .eq(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_1.code));
            if (CollUtil.isNotEmpty(writeOffRel)) {
                // 服务费失败重新支付key
                String lockPrefix = "S:F:F:Repay" + businessNo;
                if (Boolean.TRUE.equals(
                    redisTemplate.opsForValue().setIfAbsent(lockPrefix, "lock", 20, TimeUnit.SECONDS))) {
                    // 发起再次付款
                    writeOffPayRecordService.relCbsPay(writeOffRel, false, businessNo);
                    redisTemplate.delete(lockPrefix);
                }
            }
        });
        return IResponse.success("操作成功");
    }

    /**
     * 提取流程批量撤销
     * @param busiNoList
     * @return
     */
    @Override
    public IResponse recallBatch(List<String> busiNoList) {
        Assert.isTrue(CollectionUtil.isNotEmpty(busiNoList), "流程编号不能为空");
        List<WorkflowTaskInfo> list = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getStatus, FlowStatusEnum.ACTIVE.getCode())
                .in(WorkflowTaskInfo::getBusinessNo, busiNoList));
        Assert.isTrue(list.size() == busiNoList.size(), "存在无法撤回的流程，请选择状态正常的流程");
        for (WorkflowTaskInfo workflowTaskInfo : list) {
            IResponse<Boolean> response = workflowHelper.cancelFlow(workflowTaskInfo.getProcessInstanceId());
            log.info("服务费提取工作流撤回返回参数{}", response);
            if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                throw new AfsBaseException("服务费提取工作流撤回失败!");
            }
            //更改状态为结束
            workflowTaskInfo.setStatus(FlowStatusEnum.END.getCode());
            //存撤回记录日志
            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(workflowTaskInfo.getBusinessNo());
            record.setDisposeStaff(SecurityUtils.getUser().getUserRealName());
            record.setDisposeNodeName(workflowTaskInfo.getTaskNodeName());
            record.setApproveSuggestName("撤回");
            record.setApproveEndTime(new Date());
            record.setApproveRemark("服务费提取流程撤回");
            recordService.save(record);
        }
        workflowTaskInfoService.updateBatchById(list);
        //更改状态为已撤回
        writeOffBaseInvoiceRelService.update(Wrappers.<WriteOffBaseInvoiceRel>lambdaUpdate()
                .in(WriteOffBaseInvoiceRel::getApproveId, busiNoList)
                .set(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_2.code));
        return IResponse.success("操作成功");
    }

    /**
     * 服务费提取项按条件导出
     * @param response
     * @param dto
     */
    @Override
    public void exportByCondition(HttpServletResponse response, ChannelServiceFeeDTO dto) {
        List<String> statusList = new ArrayList<>();
        //未提取状态
        statusList.add(ChannelServiceFeeEnum.STATUS_0.code);
        //提取完成状态
        statusList.add(ChannelServiceFeeEnum.STATUS_1.code);
        if (StrUtil.isNotBlank(dto.getApproveId())) {
            dto.setBusinessNo(dto.getApproveId());
        }
        if (StrUtil.isNotBlank(dto.getWriteOffMonth())) {
            if (dto.getWriteOffMonth().contains(" ")) {
                dto.setWriteOffMonthList(List.of(dto.getWriteOffMonth().split(" ")));
            } else {
                dto.setWriteOffMonthList(Collections.singletonList(dto.getWriteOffMonth()));
            }
        }
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        if (!resultDto.getIsAll()) {
            dto.setHitCodeList(resultDto.getHitCodeList());
        }
        List<WriteOffBaseInvoiceRel> list = writeOffBaseInvoiceRelMapper.getList(dto);

        List<DicDataDto> payStatusList = DicHelper.getDicMaps("paymentStatusQuery")
                .getOrDefault("paymentStatusQuery", new ArrayList<>());
        Map<String, String> dicMap = new HashMap<>();
        for (DicDataDto dicDataDto : payStatusList) {
            dicMap.put(dicDataDto.getValue(),dicDataDto.getTitle());
        }
        List<WriteOffRelExportVo> exportVos = list.stream().map(rel -> {
            WriteOffRelExportVo exportVo = new WriteOffRelExportVo();
            exportVo.setApproveId(rel.getApproveId());
            exportVo.setApplyNo(rel.getApplyNo());
            exportVo.setDealerCode(rel.getDealerCode());
            exportVo.setDealerName(rel.getDealerName());
            exportVo.setPayReceiveAccount(rel.getPayReceiveAccount());
            exportVo.setWriteOffMonth(rel.getWriteOffMonth());
            exportVo.setInvoiceAmount(rel.getInvoiceAmount());
            if (ChannelServiceFeeEnum.STATUS_0.code.equals(rel.getApproveStatus())) {
                exportVo.setApproveStatus("未提取");
            } else if (ChannelServiceFeeEnum.STATUS_1.code.equals(rel.getApproveStatus())) {
                exportVo.setApproveStatus("提取中");
            } else if (ChannelServiceFeeEnum.STATUS_2.code.equals(rel.getApproveStatus())) {
                exportVo.setApproveStatus("已撤回");
            } else if (ChannelServiceFeeEnum.STATUS_3.code.equals(rel.getApproveStatus())) {
                exportVo.setApproveStatus("提取完成");
            } else {
                exportVo.setApproveStatus("异常");
            }
            if (rel.getPayStatus() != null) {
                exportVo.setPayStatus(dicMap.get(rel.getPayStatus().toString()));
            }
            exportVo.setWriteOffType(WriteOffTypeEnum.createTypeEnum(rel.getWriteOffType()).getDesc());
            return exportVo;
        }).collect(Collectors.toList());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("核销项提取") + ".xlsx");
            ExcelWriter excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), WriteOffRelExportVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0).includeColumnFiledNames(null).build();
            excelWriterBuilder.write(exportVos, htSheetWrite);
            excelWriterBuilder.finish();
        } catch (Exception e) {
            e.printStackTrace();
            throw new AfsBaseException("下载失败");
        }
    }

    private StartFlowRequestBo buildParam(String applyNo, StartFlowRequestBo requestBo) {
        requestBo.setBusinessNo(applyNo);
        requestBo.setSubject("经销商服务费提取流程发起" + applyNo);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FlowConstant.BUSINESS_NO, applyNo);
        jsonObject.put(FlowConstant.APPROVAL_USER, SecurityUtils.getUsername());
        jsonObject.put(FlowConstant.APPROVAL_OPINION, "发起");
        jsonObject.put(FlowConstant.TASK_NODE_NAME, "流程发起");
        jsonObject.put("periodFlag", "0");
        requestBo.setParams(jsonObject);

        return requestBo;
    }

    private void buildParamAgain(String applyNo) {
        CaseApproveRecord record = new CaseApproveRecord();
        record.setApplyNo(applyNo);
        record.setApproveSuggest("initiate");
        record.setApproveSuggestName("重新发起审批");
        record.setUseScene(UseSceneEnum.SERVICE_CHARGE_WITHDRAWAL.getValue());
        record.setDisposeStaff(SecurityUtils.getUser() == null ? "系统" : SecurityUtils.getUser().getUserRealName());
        record.setDisposeNodeName("开始节点");
        record.setApproveEndTime(new Date());

        recordService.save(record);
    }


    private String getApplyNo() {
        String letter = "serviceDraw";
        Long seqno = afsSequenceGenerator.genNext(letter, DateUtil.year(DateUtil.date())+"");
        String tempNo = "FWFTQ" + DateUtil.year(DateUtil.date()) + StringUtils.leftPad(String.valueOf(seqno), 6, '0');// 申请编号
        return tempNo;
    }


    /**
     * 提交任务(审批) 包括：同意/拒绝/退回
     *
     * @param param
     * @return
     */
    @Override
    public IResponse<Boolean> submitTask(SubmitTaskParam param, UseSceneEnum useSceneEnum) {
        final WorkflowTaskInfo taskInfo = workflowWrapperService.getTaskInfo(param.getTaskId());
        // record
        final CaseApproveRecord record = workflowWrapperService.assembleRecord(param, taskInfo, useSceneEnum);
        recordService.save(record);

        taskInfoService.saveTaskBeforeSubmit(param, taskInfo.getId());
        IResponse<Boolean> response = null;

        // 标记当前操作  回调中使用
        JSONObject jsonObject = Optional.ofNullable(param.getExtendParams())
                .orElse(new JSONObject());
        jsonObject.put(FlowConstant.BUSINESS_NO, taskInfo.getBusinessNo());
        jsonObject.put(FlowConstant.LAST_OPERATION, param.getOperationType().toUpperCase());
        jsonObject.put(FlowConstant.LAST_APPROVE_REASON, param.getApproveSuggest());
        jsonObject.put(FlowConstant.LAST_APPROVE_REMARK, param.getRemark());
        jsonObject.put(FlowConstant.BIZ_OPERATION_TYPE, param.getOperationType());
        jsonObject.put(FlowConstant.LAST_OPERATOR_LOGIN_NAME, SecurityUtils.getUsername());
        param.setExtendParams(jsonObject);

        FlowTaskOperationEnum operationEnum = null;
        try {
            operationEnum = FlowTaskOperationEnum.valueOf(param.getOperationType().toUpperCase());
            switch (operationEnum) {
                case REFUSE:
                    response = workflowWrapperService.refuseTask(param, response);
                    break;
                case BACK:
                    response = workflowWrapperService.backTask(param, response);
                    break;
                case SUBMIT:
                    if (StringUtils.isEmpty(param.getRemark())) {
                        param.setRemark("通过");
                    }
                    response = workflowWrapperService.agreeTask(param, response);
                    break;
                default:
                    log.warn("no match workflow operation.");
            }
        } catch (Throwable throwable) {
            log.error("invoke flow {} error.", param.getOperationType(), throwable);
            return IResponse.fail(throwable.getMessage());
        } finally {
            if (response == null) {
                record.setApproveSuggestName(record.getApproveSuggestName());
                recordService.updateById(record);
            } else if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
                // 结束本地任务
                final WorkflowTaskInfo newTaskInfo = new WorkflowTaskInfo();
                newTaskInfo.setStatus(FlowStatusEnum.END.getCode());
                newTaskInfo.setId(taskInfo.getId());
                taskInfoService.updateById(newTaskInfo);
            } else {
                recordService.removeById(record.getId());
                log.warn("invoke flow {} error. code: {} msg: {}", param.getOperationType(), response.getCode(), response.getMsg());
            }
        }

        return response;
    }

    @Override
    public IPage<ServiceFeeWorkInfo> getServiceFeeTask(Page page, ChannelServiceFeeDTO condition) {
        //查询当前登录人的待办任务
        String name = SecurityUtils.getUsername();
        IPage<ServiceFeeWorkInfo> list = baseMapper.queryTaskList(page, condition, name);
        list.getRecords().forEach(task->{
            final List<WorkflowTaskInfo> tmps = taskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getBusinessNo, task.getBusinessNo())
                    .orderByDesc(BaseEntity::getCreateTime));
            //申请时间
            task.setApplyDate(tmps.get(0).getCreateTime());
        });
        return list;
    }

    @Override
    public IPage<ServiceFeeWorkInfoResp> getServiceFeeTask2(Page page, ChannelServiceFeeDTO condition) {
        //查询当前登录人的待办任务
        String name = SecurityUtils.getUsername();
        IPage<ServiceFeeWorkInfoResp> list = baseMapper.queryTaskList2(page, condition, name);
        list.getRecords().forEach(task->{
            //申请时间
            task.setApplyDate(task.getCreateTime());
            task.setApproveId(task.getBusinessNo());
            List<WriteOffPayRecord> payRecordList = writeOffPayRecordService.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                    .select(WriteOffPayRecord::getId, WriteOffPayRecord::getChannelCode)
                    .eq(WriteOffPayRecord::getBusinessNo, task.getBusinessNo()));
            int dealerCount = 0;
            if (payRecordList.size() > 0) {
                Map<String, List<WriteOffPayRecord>> collect = payRecordList.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getChannelCode));
                dealerCount = collect.keySet().size();
            } else {
                List<WriteOffBaseInvoiceRel> relList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                        .select(WriteOffBaseInvoiceRel::getId, WriteOffBaseInvoiceRel::getDealerCode)
                        .eq(WriteOffBaseInvoiceRel::getApproveId, task.getBusinessNo()));
                if (relList.size() > 0) {
                    Map<String, List<WriteOffBaseInvoiceRel>> map = relList.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getDealerCode));
                    dealerCount = map.keySet().size();
                }
            }
            task.setDealerCount(String.valueOf(dealerCount));
        });
        return list;
    }
    @Override
    public IPage<WriteOffBaseAndTaskInfo> approveDetail(Page page,String organId) {

        IPage<WriteOffBaseAndTaskInfo> detail = baseInfoMapper.getApproveDetail(page, organId);
        List<WriteOffBaseAndTaskInfo> collect = detail.getRecords();
        List<WriteOffBaseAndTaskInfo> arrayList = new ArrayList<>();
        for (WriteOffBaseAndTaskInfo writeOffBaseInfo : collect) {

            WriteOffBaseAndTaskInfo taskInfo = new WriteOffBaseAndTaskInfo();
            BeanUtils.copyProperties(writeOffBaseInfo,taskInfo);
            List<WorkflowTaskInfo> infos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getBusinessNo, writeOffBaseInfo.getApproveId())
                    .orderByDesc(WorkflowTaskInfo::getCreateTime));
            if (!Objects.isNull(infos)) {
                taskInfo.setStatus(FlowStatusEnum.getDescByCode(infos.get(0).getStatus()));
                taskInfo.setTaskNodeName(infos.get(0).getTaskNodeName());
                taskInfo.setAssign(infos.get(0).getAssign());
                if (!Objects.isNull(infos.get(0).getAssign())) {
                    IResponse info = userDetailsInfoFeign.info(infos.get(0).getAssign());
                    log.info("{}查询中文名结果{}", infos.get(0).getAssign(), info);
                    Object data = info.getData();
                    JSONObject jsonObject = (JSONObject) JSON.toJSON(data);
                    Object syUser = jsonObject.get("sysUser");
                    JSONObject object = (JSONObject) JSON.toJSON(syUser);
                    String userRealName = object.getString("userRealName");
                    taskInfo.setAssign(userRealName);
                }
                arrayList.add(taskInfo);
            }
        }
        detail.setRecords(arrayList);
        return detail;
    }


}
