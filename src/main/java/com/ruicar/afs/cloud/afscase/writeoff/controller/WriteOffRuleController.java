package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffMonthConfig;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffRule;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffRuleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.enums.RuleTypeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.CalRuleEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffMonthConfigService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.AdvanceVO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.GroupSimpleVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffRuleQueryVo;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleResult;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 服务费计算规则
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffRule")
public class WriteOffRuleController {
    private final WriteOffRuleService writeOffRuleService;
    private final WriteOffRuleDetailService writeOffRuleDetailService;
    private final AfsRuleFeign afsRuleInfoService;
    private final StringRedisTemplate stringRedisTemplate;
    private final WriteOffMonthConfigService writeOffMonthConfigService;
    private static final String SERVICE_FEE_RULE_EXPORT = "service:fee:rule:export";
    private static final String SERVICE_FEE_CAL = "service:fee:rule:cal";

    @GetMapping("/queryList")
    @ApiOperation(value = "查询服务费计算规则数据")
    public IResponse queryList() {
        List<WriteOffRule> list = writeOffRuleService.list();
        for (WriteOffRule offRule : list) {
            StringBuilder builder = new StringBuilder();
            String[] split = offRule.getBelongingCapital().split(",");
            for (int i = 0; i < split.length; i++) {
                if (split.length - 1 == i) {
                    builder.append(BelongingCapitalEnum.getDescByCode(split[i]));
                } else {
                    builder.append(BelongingCapitalEnum.getDescByCode(split[i])).append(",");
                }
            }
            offRule.setBelongCapitalName(builder.toString());
            offRule.setBelongCapitalList(Arrays.asList(split));
        }
        return IResponse.success(list);
    }

    @PostMapping("/saveData")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "保存服务费计算规则数据")
    public IResponse saveData(@RequestBody WriteOffRule writeOffRule) {
        List<String> capitalList = writeOffRule.getBelongCapitalList();
        Assert.isTrue(StrUtil.isNotBlank(writeOffRule.getType()) && CollUtil.isNotEmpty(capitalList), "参数异常");
        //是否复制操作
        boolean copyFlag = "1".equals(writeOffRule.getCopyFlag());
        Long copyId = null;
        List<WriteOffRule> list = null;
        if (writeOffRule.getId() != null && !copyFlag) {
            WriteOffRule one = writeOffRuleService.getById(writeOffRule.getId());
            Assert.isTrue(one != null && StatusEnum.NO.getCode().equals(one.getStatus()), "规则不存在或规则不是无效,无法更新");
            writeOffRule.setId(one.getId());
            list = writeOffRuleService.list(Wrappers.<WriteOffRule>lambdaQuery()
                    .eq(WriteOffRule::getType, writeOffRule.getType())
                    .ne(WriteOffRule::getId, writeOffRule.getId()));
        } else {
            if (copyFlag) {
                copyId = writeOffRule.getId();
                Assert.isTrue(copyId != null, "复制操作，参数异常");
                WriteOffRule oldOne = writeOffRuleService.getById(copyId);
                Assert.isTrue(oldOne != null && oldOne.getType().equals(writeOffRule.getType()), "只能复制同类型的计算规则");
            }
            writeOffRule.setId(null);
            list = writeOffRuleService.list(Wrappers.<WriteOffRule>lambdaQuery().eq(WriteOffRule::getType, writeOffRule.getType()));
        }
        for (WriteOffRule offRule : list) {
            String[] split = offRule.getBelongingCapital().split(",");
            for (String s : split) {
                Assert.isTrue(!capitalList.contains(s), "保存失败,[" + offRule.getTypeName() + "]已存在[" + BelongingCapitalEnum.getDescByCode(s)+"]");
            }
        }
        writeOffRule.setStatus(StatusEnum.NO.getCode());
        writeOffRule.setBelongingCapital(String.join(",", capitalList));
        writeOffRuleService.saveOrUpdate(writeOffRule);
        if (copyFlag) {
            //复制操作
            Long newId = writeOffRule.getId();
            List<WriteOffRuleDetail> detailList = writeOffRuleDetailService.list(Wrappers.<WriteOffRuleDetail>lambdaQuery()
                    .eq(WriteOffRuleDetail::getWriteOffRuleId, copyId));
            for (WriteOffRuleDetail detail : detailList) {
                detail.setId(null);
                detail.setWriteOffRuleId(newId);
                detail.setCreateBy(null);
                detail.setCreateTime(null);
                detail.setUpdateBy(null);
                detail.setUpdateTime(null);
                writeOffRuleDetailService.save(detail);
                if (detail.getRuleId() != null) {
                    //复制规则详情
                    String ruleGroup = writeOffRule.getType().equals(CalRuleEnum.EARLY_PAY.getCode()) ? "WriteOffAdvanceRuleAtom" : "WriteOffCalRuleAtom";
                    Long newRuleId = afsRuleInfoService.copyRule(detail.getRuleId(), detail.getId().toString(), detail.getRuleName(), ruleGroup).getData();
                    if (newRuleId != null) {
                        writeOffRuleDetailService.update(Wrappers.<WriteOffRuleDetail>lambdaUpdate()
                                .eq(WriteOffRuleDetail::getId, detail.getId())
                                .set(WriteOffRuleDetail::getRuleId, newRuleId));
                    } else {
                        throw new AfsBaseException(detail.getRuleName() + "规则复制失败");
                    }
                }
            }
            return IResponse.success("复制成功");
        }
        return IResponse.success("保存成功");
    }

    @PostMapping("/queryDetailList")
    @ApiOperation(value = "查询服务费计算规则详情数据")
    public IResponse queryDetailList(@RequestBody WriteOffRuleQueryVo queryVo) {
        return IResponse.success(writeOffRuleDetailService.page(new Page(queryVo.getPageNumber(), queryVo.getPageSize()), Wrappers.<WriteOffRuleDetail>lambdaQuery()
                .eq(WriteOffRuleDetail::getWriteOffRuleId, queryVo.getWriteOffRuleId())
                .orderByDesc(WriteOffRuleDetail::getCreateTime)));
    }

    @PostMapping("/addDetail")
    @ApiOperation(value = "新增服务费计算规则详情数据")
    public IResponse addDetail(@RequestBody WriteOffRuleDetail writeOffRuleDetail) {
        writeOffRuleDetail.setId(null);
        if (writeOffRuleDetail.getDeductRate() != null) {
            if (writeOffRuleDetail.getDeductRate() > 100 || writeOffRuleDetail.getDeductRate() < 0) {
                throw new AfsBaseException("扣减比例不能大于100或小于0");
            }
        }
        writeOffRuleDetailService.save(writeOffRuleDetail);
        return IResponse.success(null);
    }

    @PostMapping("/editDetail")
    @ApiOperation(value = "修改服务费计算规则详情数据")
    public IResponse editDetail(@RequestBody WriteOffRuleDetail writeOffRuleDetail) {
        if (writeOffRuleDetail.getDeductRate() != null) {
            if (writeOffRuleDetail.getDeductRate() > 100 || writeOffRuleDetail.getDeductRate() < 0) {
                throw new AfsBaseException("扣减比例不能大于100或小于0");
            }
        }
        writeOffRuleDetailService.updateById(writeOffRuleDetail);
        return IResponse.success(null);
    }

    @PostMapping(value = "/updateStatus")
    @ApiOperation(value = "修改生效状态")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateStatus(@RequestParam Long id) {
        WriteOffRule writeOffRule = writeOffRuleService.getById(id);
        Assert.isTrue(writeOffRule != null, "id不正确");
        List<WriteOffRuleDetail> detailList = writeOffRuleDetailService.list(Wrappers.<WriteOffRuleDetail>lambdaQuery()
                .eq(WriteOffRuleDetail::getWriteOffRuleId, writeOffRule.getId()));
        if (StatusEnum.YES.getCode().equals(writeOffRule.getStatus())) {
            // 生效改无效
            for (WriteOffRuleDetail detail : detailList) {
                afsRuleInfoService.deActiveRuleByRuleId(detail.getRuleId());
            }
            writeOffRule.setStatus(StatusEnum.NO.getCode());
        } else {
            // 无效改生效
            Assert.isTrue(detailList.size() > 0, "没有配置计算规则，不可生效！");
            for (WriteOffRuleDetail detail : detailList) {
                Assert.isTrue(detail.getRuleId() != null, "【" + detail.getRuleName() + "】规则未配置！");
                afsRuleInfoService.activeRuleByRuleId(detail.getRuleId());
            }
            writeOffRule.setStatus(StatusEnum.YES.getCode());
        }
        writeOffRuleService.updateById(writeOffRule);
        return IResponse.success(null);
    }

    @PostMapping(value = "/delById")
    @ApiOperation(value = "通过id删除计算规则")
    @Transactional(rollbackFor = Exception.class)
    public IResponse deleteById(@RequestParam Long id) {
        WriteOffRule writeOffRule = writeOffRuleService.getById(id);
        Assert.isTrue(StatusEnum.NO.getCode().equals(writeOffRule.getStatus()), "生效的计算规则不可删除");
        List<WriteOffRuleDetail> detailList = writeOffRuleDetailService.list(Wrappers.<WriteOffRuleDetail>lambdaQuery()
                .select(WriteOffRuleDetail::getId, WriteOffRuleDetail::getRuleId)
                .eq(WriteOffRuleDetail::getWriteOffRuleId, writeOffRule.getId()));
        for (WriteOffRuleDetail detail : detailList) {
            if (detail.getRuleId() != null) {
                afsRuleInfoService.deleteRule(detail.getRuleId());
            }
        }
        writeOffRuleDetailService.removeBatchByIds(detailList);
        writeOffRuleService.removeById(id);
        return IResponse.success(null);
    }

    @PostMapping("/writeOffAdvanceRuleCal")
    @ApiOperation(value = "提前结清扣减计算")
    public IResponse writeOffAdvanceRuleCal(@RequestBody AdvanceVO advanceVO) {
        return writeOffRuleService.writeOffAdvanceRuleCal(advanceVO.getCurrentDetail(), advanceVO.getSettleData(),
            advanceVO.getBillingPeriod());
    }

    @PostMapping(value = "/delDetailById")
    @ApiOperation(value = "删除规则")
    @Transactional(rollbackFor = Exception.class)
    public IResponse delDetailById(@RequestParam Long id) {
        WriteOffRuleDetail detail = writeOffRuleDetailService.getById(id);
        if (detail.getRuleId() != null) {
            afsRuleInfoService.deleteRule(detail.getRuleId());
        }
        writeOffRuleDetailService.removeById(id);
        return IResponse.success(null);
    }

    @PostMapping("/writeOffRuleCal")
    @ApiOperation(value = "服务费计算")
    public IResponse writeOffRuleCal() {
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(SERVICE_FEE_CAL, "lock", 5, TimeUnit.HOURS);
        if (lock != null && lock) {
            try {
                return writeOffRuleService.writeOffCalRuleJob(null, false, null, null, null);
            } finally {
                stringRedisTemplate.delete(SERVICE_FEE_CAL);
            }
        } else {
            throw new AfsBaseException("服务费正在计算中，请不要重复操作！");
        }
    }

    @PostMapping("/zjdWriteOffCal")
    @ApiOperation(value = "租金贷服务费计算")
    public IResponse zjdWriteOffCal() {
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(SERVICE_FEE_CAL, "lock", 5, TimeUnit.HOURS);
        if (lock != null && lock) {
            try {
                List<WriteOffMonthConfig> monthConfigs = writeOffMonthConfigService.list(Wrappers.<WriteOffMonthConfig>lambdaQuery()
                        .eq(WriteOffMonthConfig::getStatus, StatusEnum.YES.getCode()));
                Assert.isTrue(monthConfigs.size() > 0, "没有生效的周期配置");
                int dayOfMonth = DateUtil.dayOfMonth(new Date());
                Date zjdStartTime = null;
                Date zjdEndTime = null;
                for (WriteOffMonthConfig monthConfig : monthConfigs) {
                    Calendar startCalendar = Calendar.getInstance();
                    Calendar endCalendar = Calendar.getInstance();
                    if (monthConfig.getNextEndDay() != null) {
                        if (dayOfMonth > monthConfig.getNextEndDay()) {
                            startCalendar.add(Calendar.MONTH, -1);
                            startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                            endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getNextEndDay() + 1);
                        } else {
                            startCalendar.add(Calendar.MONTH, -2);
                            startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                            endCalendar.add(Calendar.MONTH, -1);
                            endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getNextEndDay() + 1);
                        }
                    } else {
                        Assert.isTrue(monthConfig.getThisEndDay() != null, "[" + monthConfig.getRuleName() + "]周期配置的时间异常");
                        if (dayOfMonth > monthConfig.getThisEndDay()) {
                            startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                            endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getThisEndDay() + 1);
                        } else {
                            startCalendar.add(Calendar.MONTH, -1);
                            startCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getStartDay());
                            endCalendar.add(Calendar.MONTH, -1);
                            endCalendar.set(Calendar.DAY_OF_MONTH, monthConfig.getThisEndDay() + 1);
                        }
                    }
                    zjdStartTime = DateUtil.beginOfDay(startCalendar.getTime());
                    zjdEndTime = DateUtil.beginOfDay(endCalendar.getTime());
                    Assert.isTrue(DateUtil.betweenDay(zjdStartTime, zjdEndTime, true) >= 27, "[" + monthConfig.getRuleName() + "]周期配置的时间不满足一个月");
                    writeOffRuleService.writeOffCalRuleJob(null, true, zjdStartTime, zjdEndTime, monthConfig.getId());
                }
                return IResponse.success("计算完成");
            } finally {
                stringRedisTemplate.delete(SERVICE_FEE_CAL);
            }
        } else {
            throw new AfsBaseException("服务费正在计算中，请不要重复操作！");
        }
    }

    @PostMapping("/writeOffRuleExport")
    @ApiOperation(value = "服务费计算规则导出")
    public void writeOffRuleExport(HttpServletResponse response) {

        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(SERVICE_FEE_RULE_EXPORT, "lock", 10, TimeUnit.SECONDS);
        if (lock != null && lock) {
            try {
                writeOffRuleService.writeOffRuleExport(response);
            } finally {
                stringRedisTemplate.delete(SERVICE_FEE_RULE_EXPORT);
            }
        } else {
            throw new AfsBaseException("服务费计算规则导出中，请不要重复操作！");
        }
    }

    @GetMapping("/queryInvoiceRule")
    @ApiOperation(value = "查询服务费发票规则")
    public IResponse queryInvoiceRule() {
        return IResponse.success(writeOffRuleDetailService.list(Wrappers.<WriteOffRuleDetail>lambdaQuery().eq(WriteOffRuleDetail::getRuleType, RuleTypeEnum.INVOICE.getCode())));
    }

    @PostMapping("/addInvoiceDetail")
    @ApiOperation(value = "新增发票规则详情数据")
    public IResponse addInvoiceDetail() {
        WriteOffRuleDetail detail = new WriteOffRuleDetail();
        detail.setRuleType(RuleTypeEnum.INVOICE.getCode());
        detail.setRuleName(RuleTypeEnum.INVOICE.getDesc());
        detail.setStatus(StatusEnum.NO.getCode());
        writeOffRuleDetailService.save(detail);
        return IResponse.success("新增成功");
    }

    @PostMapping("/editInvoiceDetail")
    @ApiOperation(value = "修改发票规则详情数据")
    public IResponse editInvoiceDetail(@RequestBody WriteOffRuleDetail detail) {
        Assert.isTrue(detail.getId() != null && StatusEnum.NO.getCode().equals(detail.getStatus()) && RuleTypeEnum.INVOICE.getCode().equals(detail.getRuleType()), "数据异常");
        writeOffRuleDetailService.updateById(detail);
        return IResponse.success(null);
    }

    @PostMapping(value = "/updateInvoiceStatus")
    @ApiOperation(value = "修改发票规则生效状态")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateInvoiceStatus(@RequestParam Long id) {
        WriteOffRuleDetail invoiceRule = writeOffRuleDetailService.getById(id);
        Assert.isTrue(invoiceRule != null, "id不正确");
        if (StatusEnum.YES.getCode().equals(invoiceRule.getStatus())) {
            // 生效改无效
            afsRuleInfoService.deActiveRuleByRuleId(invoiceRule.getRuleId());
            invoiceRule.setStatus(StatusEnum.NO.getCode());
        } else {
            // 无效改生效
            Assert.isTrue(invoiceRule.getRuleId() != null, "【" + invoiceRule.getRuleName() + "】规则未配置！");
            afsRuleInfoService.activeRuleByRuleId(invoiceRule.getRuleId());
            invoiceRule.setStatus(StatusEnum.YES.getCode());
        }
        writeOffRuleDetailService.updateById(invoiceRule);
        return IResponse.success(null);
    }

    @PostMapping("/runInvoiceRule")
    @ApiOperation(value = "运行服务费发票校验规则")
    public String runInvoiceRule(@RequestBody JSONObject jsonObject) {
        log.info("服务费发票校验规则参数：{}", jsonObject);
        RuleRunResult ruleRunResult = RuleHelper.runByGroup(jsonObject, "WriteOffInvoiceRule", false, RuleRunEnum.PARALLEL, true);
        if (!ruleRunResult.getHit()) {
            return "【发票校验规则全部不满足】";
        }
        StringBuilder builder = new StringBuilder();
        boolean flag = false;
        for (RuleResult ruleResult : ruleRunResult.getResults()) {
            if (ruleResult.getResult()==null || "false".equals(ruleResult.getResult().toString())) {
                WriteOffRuleDetail detail = writeOffRuleDetailService.getOne(Wrappers.<WriteOffRuleDetail>lambdaQuery()
                        .eq(WriteOffRuleDetail::getId, Long.valueOf(ruleResult.getRuleNo()))
                        .eq(WriteOffRuleDetail::getStatus, StatusEnum.YES.getCode()));
                if (detail != null) {
                    flag = true;
                    builder.append("[").append(detail.getRuleExpress()).append("]");
                }
            }
        }
        return flag ? builder.toString() : null;
    }

    @GetMapping("/getOptionalCalByType")
    @ApiOperation(value = "根据type获取可选的服务费计算规则")
    public IResponse getOptionalCalByType(@RequestParam String calType) {
        List<WriteOffRule> list = writeOffRuleService.list(Wrappers.<WriteOffRule>lambdaQuery()
                .eq(WriteOffRule::getType, calType)
                .eq(WriteOffRule::getStatus, StatusEnum.NO.getCode()));
        List<GroupSimpleVo> simpleVoList = list.stream().map(k -> {
            StringBuilder builder = new StringBuilder();
            String[] split = k.getBelongingCapital().split(",");
            for (int i = 0; i < split.length; i++) {
                if (split.length - 1 == i) {
                    builder.append(BelongingCapitalEnum.getDescByCode(split[i]));
                } else {
                    builder.append(BelongingCapitalEnum.getDescByCode(split[i])).append(",");
                }
            }
            GroupSimpleVo simpleVo = new GroupSimpleVo();
            simpleVo.setValue(k.getId().toString());
            simpleVo.setTitle(k.getTypeName() + " --> " + builder.toString());
            return simpleVo;
        }).toList();
        return IResponse.success(simpleVoList);
    }

    @PostMapping("/copyRuleDetail")
    @ApiOperation(value = "复制服务费计算规则详情")
    public IResponse copyRuleDetail(@RequestParam Long detailId, @RequestParam Long infoId) {
        WriteOffRule offRule = writeOffRuleService.getById(infoId);
        Assert.isTrue(offRule != null && StatusEnum.NO.getCode().equals(offRule.getStatus()), "只能复制不是生效的计算规则");
        WriteOffRuleDetail detail = writeOffRuleDetailService.getById(detailId);
        Assert.isTrue(detail != null, "参数异常");
        detail.setId(null);
        detail.setWriteOffRuleId(infoId);
        detail.setCreateBy(null);
        detail.setCreateTime(null);
        detail.setUpdateBy(null);
        detail.setUpdateTime(null);
        writeOffRuleDetailService.save(detail);
        if (detail.getRuleId() != null) {
            //复制规则详情
            String ruleGroup = offRule.getType().equals(CalRuleEnum.EARLY_PAY.getCode()) ? "WriteOffAdvanceRuleAtom" : "WriteOffCalRuleAtom";
            Long newRuleId = afsRuleInfoService.copyRule(detail.getRuleId(), detail.getId().toString(), detail.getRuleName(), ruleGroup).getData();
            if (newRuleId != null) {
                writeOffRuleDetailService.update(Wrappers.<WriteOffRuleDetail>lambdaUpdate()
                        .eq(WriteOffRuleDetail::getId, detail.getId())
                        .set(WriteOffRuleDetail::getRuleId, newRuleId));
            } else {
                throw new AfsBaseException(detail.getRuleName() + "规则复制失败");
            }
        }
        return IResponse.success("复制成功");
    }
}
