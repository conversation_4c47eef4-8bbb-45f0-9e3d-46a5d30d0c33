package com.ruicar.afs.cloud.afscase.loanactivatepool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPool;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseContractLockInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.archive.dto.ArchiveApiDto;
import com.ruicar.afs.cloud.afscase.archive.service.ArchiveApiService;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.StepParam;
import com.ruicar.afs.cloud.afscase.autoaudit.lock.LoanActiveLock;
import com.ruicar.afs.cloud.afscase.autoaudit.util.ActiveStepUtil;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsApply;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsApplyService;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelQuotaInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.loanactivatepool.condition.LoanActivateCondition;
import com.ruicar.afs.cloud.afscase.loanactivatepool.condition.SkipCondition;
import com.ruicar.afs.cloud.afscase.loanactivatepool.entity.LoanActivatePool;
import com.ruicar.afs.cloud.afscase.loanactivatepool.mapper.LoanActivatePoolMapper;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.IContractActiveProcess;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.LoanActivateService;
import com.ruicar.afs.cloud.afscase.loanactivatepool.vo.LoanActivatePoolVO;
import com.ruicar.afs.cloud.afscase.loanactivatepool.vo.LoanTaskRevokeVO;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanWorkflowService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveLoanInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.postloan.condition.PostLoanCondition;
import com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum;
import com.ruicar.afs.cloud.afscase.risk.service.CaseTortoiseService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ActTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ActivateStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStageEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.FlowNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.GpsInstallTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.GpsStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.QuotaStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.StatusDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanDiscardDTO;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 放款待激活池实现类
 * @date 2020/5/16 17:24
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class LoanActivateServiceImpl  extends ServiceImpl<LoanActivatePoolMapper,LoanActivatePool> implements LoanActivateService {

    private CaseContractInfoService caseContractInfoService;
    private CaseCustInfoService caseCustInfoService;
    private CaseChannelInfoService caseChannelInfoService;
    private final CaseConfParamService caseConfParamService;
    private WorkTaskPoolService workTaskPoolService;
    private WorkProcessScheduleInfoService workProcessScheduleInfoService;
    /**
     * MQservice
     */
    private final ApproveLoanInfoService approveLoanInfoService;

    /**
     * 工作流
     */
    private final LoanWorkflowService loanWorkflowService;

    private ApplyCostDetailsService applyCostDetailsService;

    private ChannelQuotaInfoService channelQuotaInfoService;

    private ChannelBaseInfoService channelBaseInfoService;

    private CaseBaseInfoService caseBaseInfoService;

    private IContractActiveProcess contractActiveProcess;

    private CaseTortoiseService caseTortoiseService;

    private CaseApproveRecordService caseApproveRecordService;

    private ComAttachmentFileService comAttachmentFileService;

    private CarGpsApplyService carGpsApplyService;

    private ArchiveApiService archiveApiService;

    private CaseMainInfoService caseMainInfoService;

    private final FlowConfigProperties flowConfigProperties;

    private final CaseApproveRecordService approveRecordService;
    private final CaseContractLockInfoService caseContractLockInfoService;
    private final ApplyServiceFeign applyServiceFeign;
    private WorkflowTaskInfoService workflowTaskInfoService;
    /**
     * 激活  推送信息到合同系统
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doActivate(Long id,String actType) {
        LoanActivatePool info = this.getById(id);
        if (null != info) {
            log.info("{}合同激活开始执行",info.getContractNo());
            StepParam stepParams=new StepParam();
            stepParams.setContractNo(info.getContractNo());
            stepParams.setActType(actType);
            stepParams.setApplyNo(info.getApplyNo());
            ActiveStepUtil.prevSteps(stepParams);
        }
    }

    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:19
     */
    @Override
    public void loanActivateAutoAudit() {
        CaseConfParam confParam = caseConfParamService
                .getOne(Wrappers.<CaseConfParam>query().lambda().eq(CaseConfParam::getParamType, "CONTRACT_AUTO_ACTIVE"));
        boolean flag = confParam.getParamValue().equals(WhetherEnum.YES.getCode())&&confParam.getParamStatus().equals(WhetherEnum.YES.getCode());
        if (flag) {
            log.info("自动激活合同开始");
            List<LoanActivatePool> list= this.list(Wrappers.<LoanActivatePool>lambdaQuery()
                    .eq(LoanActivatePool::getActStatus, ActivateStatusEnum.UN_ACTIVATE.getStatus())
                    .eq(LoanActivatePool::getLoanApproveStatus, ApplyStatusEnum.LOAN_APPROVE_DONE.getState()));
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            for (LoanActivatePool pool : list) {
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                        .eq(CaseContractInfo::getContractNo, pool.getContractNo()));
                if ("yes".equals(caseContractInfo.getIsLock())) {
                    log.info("{}此订单已锁定,请先人工解锁", pool.getContractNo());
                } else {
                    try {
                        log.info("{}合同自动激活--start", pool.getContractNo());
                        SpringContextHolder.getBean(LoanActivateService.class).doActivate(pool.getId(), ActTypeEnum.AUTO.getCode());
                        log.info("{}合同自动激活完成", pool.getContractNo());
                    } catch (Exception e) {
                        log.error("{}合同激活失败:{}", pool.getContractNo(), e.getMessage());
                    }
                }
            }
        } else {
            log.info("自动激活合同关闭");
        }
    }

    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:19
     * @param id
     * @param discardReason
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDiscardData(Long id, String discardReason) {
        LoanActivatePool pool = this.getById(id);
        if (null != pool && null != pool.getContractActivateFlag()) {
            //如果收到合同激活落库通知则不提交流程
            throw new AfsBaseException("已激活数据不可执行废弃操作...");
        }
        pool.setLoanApproveStatus(ApplyStatusEnum.LOAN_DISCARD.getState());
        pool.setActStatus(ActivateStatusEnum.DISCARD.getStatus());
        this.updateById(pool);

        try{
            //修改合同状态
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getContractNo,pool.getContractNo()));
            if(!ObjectUtils.isEmpty(caseContractInfo)) {
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo,caseContractInfo.getApplyNo()));
                //判断额度占用时释放额度
                if(caseContractInfo.getQuotaStatus()!=null && caseContractInfo.getQuotaStatus().equals(QuotaStatusEnum.occupation)){
                    ArchiveApiDto dto = new ArchiveApiDto();
                    dto.setContractNo(caseContractInfo.getContractNo());
                    dto.setContractId(caseContractInfo.getId().toString());
                    archiveApiService.releaseQuota(dto);
                }
                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_DISCARD.getState());
                caseContractInfo.setBusinessStage(BusinessStageEnum.LOAN_DISCARD.getCode());
                caseContractInfo.setContractStatus(ContractStatusEnum.contractCancel);
                caseContractInfoService.updateById(caseContractInfo);
                caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
                caseBaseInfoService.updateById(caseBaseInfo);
                //记录合同取消日志
                List<WorkTaskPool> pools = workTaskPoolService.list(Wrappers.<WorkTaskPool>query().lambda()
                        .eq(WorkTaskPool::getContractNo,pool.getContractNo()));
                CaseApproveRecord lockOprRecord = new CaseApproveRecord();
                lockOprRecord.setApplyNo(pool.getApplyNo());
                lockOprRecord.setContractNo(pool.getContractNo());
                lockOprRecord.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                lockOprRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
                lockOprRecord.setApproveSuggestName("合同取消");
                lockOprRecord.setApproveStartTime(new Date());
                lockOprRecord.setApproveEndTime(new Date());
                lockOprRecord.setDisposeNode(pools.get(0).getTaskNodeId());
                lockOprRecord.setDisposeNodeName(pools.get(0).getTaskNodeName());
                lockOprRecord.setApproveRemark(discardReason);
                lockOprRecord.setDisposeStaff(SecurityUtils.getUser().getUserRealName());
                lockOprRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
                lockOprRecord.setStageId(pools.get(0).getStageId());
                caseApproveRecordService.save(lockOprRecord);

                //废弃信息通知进件系统
                approveLoanInfoService.sendToApplyNotic(pool.getContractNo(),ApplyStatusEnum.LOAN_DISCARD);

                //结束工作流
                loanWorkflowService.giveUpWorkflow(pool.getContractNo(), LoanProcessTypeEnum.GENERAL_LOAN.getCode());
            }
        }catch (Exception e){
            log.info("执行废弃操作失败！",e);
            throw new AfsBaseException(pool.getContractNo()+":执行废弃操作失败");
        }
    }

    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:19
     * @param loanDiscardDTO
     * @return com.ruicar.afs.cloud.common.core.util.IResponse
     */
    @Override
    public IResponse discardCase(LoanDiscardDTO loanDiscardDTO) {
            try {
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                        .eq(CaseContractInfo::getContractNo,loanDiscardDTO.getContractNo()),false);
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo,loanDiscardDTO.getApplyNo()));
                //主状态信息
                CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
                StatusDTO caseStatusDTO = new StatusDTO();
                caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
                caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
                if(!ObjectUtils.isEmpty(caseContractInfo)){
                    log.info("{}合同取消开始！",loanDiscardDTO.getContractNo());
                    //修改合同状态
                    caseContractInfo.setBusinessStage(BusinessStageEnum.LOAN_DISCARD.getCode());
                    caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_DISCARD.getState());
                    caseContractInfo.setContractStatus(ContractStatusEnum.contractCancel);
                    caseContractInfoService.updateById(caseContractInfo);
                    //记录合同取消日志
                    List<WorkProcessScheduleInfo> info = workProcessScheduleInfoService.list(Wrappers.<WorkProcessScheduleInfo>query().lambda()
                            .eq(WorkProcessScheduleInfo::getContractNo,loanDiscardDTO.getContractNo()));
                    String disposeStaff ="";
                    CaseApproveRecord lockOprRecord = new CaseApproveRecord();
                    lockOprRecord.setContractNo(loanDiscardDTO.getContractNo());
                    lockOprRecord.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                    lockOprRecord.setApproveRemark(loanDiscardDTO.getCancelContent());
                    AfsUser user=SecurityUtils.getUser();
                    if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(user)){
                        disposeStaff=user.getUserRealName();
                    } else {
                        disposeStaff="合作商";
                    }
                    lockOprRecord.setDisposeStaff(disposeStaff);
                    lockOprRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
                    lockOprRecord.setApproveSuggestName("合同取消");
                    lockOprRecord.setApproveStartTime(new Date());
                    lockOprRecord.setApproveEndTime(new Date());
                    if(CollectionUtils.isNotEmpty(info)){
                        lockOprRecord.setDisposeNode(info.get(0).getCurrentNodeId());
                        lockOprRecord.setDisposeNodeName(info.get(0).getCurrentNodeName());
                        lockOprRecord.setStageId(info.get(0).getId().toString());
                    }
                    lockOprRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
                    lockOprRecord.setFlowNode(caseContractInfo.getFlowNode());
                    caseApproveRecordService.save(lockOprRecord);

                    //结束工作流
                    loanWorkflowService.giveOverWorkflow(loanDiscardDTO.getContractNo(), LoanProcessTypeEnum.GENERAL_LOAN.getCode());
                }
                if(AfsEnumUtil.key(BusinessStateInEnum.APPROVED).equals(caseBaseInfo.getBusinessStateIn()) ||
                        AfsEnumUtil.key(BusinessStateInEnum.CONDITIONAL_APPROVE).equals(caseBaseInfo.getBusinessStateIn())){
                    caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
                    caseBaseInfoService.updateById(caseBaseInfo);
                    CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                            .eq(CaseChannelInfo::getApplyNo, loanDiscardDTO.getApplyNo()));
                    //合同取消记录日志
                    String disposeStaff ="";
                    if("1".equals(loanDiscardDTO.getInitType())){
                        //管理员主动取消的
                        AfsUser user=SecurityUtils.getUser();
                        if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(user)){
                            disposeStaff=user.getUserRealName();
                        } else {
                            disposeStaff="管理员";
                        }
                    }else if (!ObjectUtils.isEmpty(caseChannelInfo)) {
                        disposeStaff =caseChannelInfo.getSaleAdvisor();
                    }
                    CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                    caseApproveRecord.setApplyNo(loanDiscardDTO.getApplyNo());
                    caseApproveRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
                    caseApproveRecord.setDisposeStaff(disposeStaff);
                    caseApproveRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
                    caseApproveRecord.setApproveSuggestName("取消");
                    if(StringUtils.isNotEmpty(loanDiscardDTO.getInitType()) && "1".equals(loanDiscardDTO.getInitType())){
                        if(StringUtils.isNotEmpty(loanDiscardDTO.getCancelContent())){
                            caseApproveRecord.setApproveRemark(loanDiscardDTO.getCancelContent());
                        }else{
                            caseApproveRecord.setApproveRemark("管理人员主动取消当前案件");
                        }
                    }else{
                        caseApproveRecord.setApproveRemark(Optional.ofNullable(loanDiscardDTO.getCancelContent()).orElse("合作商主动取消当前案件!"));
                    }

                    caseApproveRecord.setApproveStartTime(new Date());
                    caseApproveRecord.setApproveEndTime(new Date());
                    caseApproveRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
                    caseApproveRecordService.save(caseApproveRecord);

                    //修改主状态
                    caseStatusDTO.setStatusCode(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
                    caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(BusinessStateInEnum.CANCEL));
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4050);
                    caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
                    caseMainInfoService.updateCaseMain(caseMainUpdateCondition);

                    //结束工作流
                    loanWorkflowService.giveOverWorkflow(loanDiscardDTO.getContractNo(), UseSceneEnum.APPROVE.getValue());
                }
                log.info("{}合同取消成功！",loanDiscardDTO.getContractNo());
                return IResponse.success("合同取消成功!");
            }catch (Exception e){
                log.info("{}合同取消失败！",loanDiscardDTO.getContractNo(),e);
                return IResponse.fail("合同取消失败!");
            }
    }

    /**
     * @param contractNo
     * @param status
     * @Description 合作商撤回合同操作
     * <AUTHOR>
     * @Date 2020/08/31
     */
    @Override
    public IResponse backCase(String contractNo, String status) {
            return IResponse.fail("未找到需要撤回的合同!");

    }

    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:19
     * @param caseContractInfo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void backContract(CaseContractInfo caseContractInfo){
        try{
            //结束工作流
            loanWorkflowService.giveOverWorkflow(caseContractInfo.getContractNo(), LoanProcessTypeEnum.GENERAL_LOAN.getCode());
            //删除工作流程
            List<WorkProcessScheduleInfo> workProcessScheduleInfoList = workProcessScheduleInfoService.list(Wrappers.<WorkProcessScheduleInfo>query().lambda()
                    .eq(WorkProcessScheduleInfo::getContractNo,caseContractInfo.getContractNo()));
            if(CollectionUtil.isNotEmpty(workProcessScheduleInfoList)) {
                workProcessScheduleInfoList.forEach(workProcessScheduleInfo -> {
                    workProcessScheduleInfoService.removeById(workProcessScheduleInfo);
                });
            }
            /**物理删除文件*/
            comAttachmentFileService.deleteFileByBusiNo(caseContractInfo.getContractNo(),caseContractInfo.getContractNo());
            //修改合同状态
            caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_SUBMIT.getState());
            caseContractInfo.setBusinessStage(BusinessStageEnum.LOAN_APPLY.getCode());
            caseContractInfo.setWorkflowPrevStep(0);
            caseContractInfoService.updateById(caseContractInfo);
        }catch (Exception e){
            log.error("{}撤回操作失败：{}",caseContractInfo.getContractNo(),e);
            throw new AfsBaseException("执行撤回失败");
        }
    }


    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:18
     * @param ids
     * @return java.lang.String
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String doActivateOfArtificial(Long[] ids) {
        if(!inAutoActiveTime()){
            throw new AfsBaseException("当前时间处于不可激活时间段内");
        }
        for (Long id : ids) {
            LoanActivatePool info = this.getById(id);
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getContractNo, info.getContractNo()));
            if ("yes".equals(caseContractInfo.getIsLock())) {
                return info.getContractNo()+"此订单已锁定,请先人工解锁";
            }
            SpringContextHolder.getBean(LoanActivateService.class).doActivate(id, ActTypeEnum.MANUAL.getCode());
        }
        return "发送成功";
    }


    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:18
     * @return boolean
     */
    public boolean inAutoActiveTime(){
        CaseConfParam confParam = caseConfParamService
                .getOne(Wrappers.<CaseConfParam>query().lambda().eq(CaseConfParam::getParamType, "CONTRACT_AUTO_ACTIVE_TIME"));
        // 未配置或不启时不校验
        if(confParam==null || StringUtils.equals(YesOrNoEnum.no.name(),confParam.getParamStatus())){
            return true;
        }
        Date date = new Date();
        String configDateBetweenStr = confParam.getParamValue();
        String startTime = configDateBetweenStr.split("-")[0];
        String endTime = configDateBetweenStr.split("-")[1];
        String nowDateStr = DateUtil.format(date,"HHmm");
        return nowDateStr.compareTo(endTime) <= 0 && nowDateStr.compareTo(startTime) >=0;
    }

    /**
     * 代码规范
     * <AUTHOR>
     * @date 2023/8/17 18:18
     * @param skipCondition
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doSingleActivateOfArtificial(SkipCondition skipCondition) {
        if(!inAutoActiveTime()){
            throw new AfsBaseException("当前时间处于不可激活时间段内");
        }
        LoanActivatePool info = this.getById(skipCondition.getId());
        if (null != info) {
            log.info("{}合同激活开始执行",info.getContractNo());
            StepParam stepParams=new StepParam();
            stepParams.setContractNo(info.getContractNo());
            stepParams.setSkipOverdue(skipCondition.isSkipOverdue());
            stepParams.setSkipBlackList(skipCondition.isSkipBlackList());
            stepParams.setSkipOverTime(skipCondition.isSkipOverTime());
            stepParams.setSkipSuspend(skipCondition.isSkipSuspend());
            stepParams.setActType(ActTypeEnum.MANUAL.getCode());
            ActiveStepUtil.prevSteps(stepParams);
        }
    }

    /**
     * 代码规范注释
     * <AUTHOR>
     * @date 2023/8/17 18:19
     * @param id
     * @param backReason
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitBackLoan(Long id,String backReason) {
        LoanActivatePool info = this.getById(id);
        //如果收到合同通知 则不允许退回放款
        Assert.isTrue(StringUtils.isBlank(info.getContractActivateFlag()),"已激活数据不可执行退回...");

        //修改放款审核状态
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,info.getContractNo()));
        //放款审核通过状态无退回
        Boolean caseStates = StringUtils.isNotBlank(caseContractInfo.getApplyStatus()) && caseContractInfo.getApplyStatus().equals(ApplyStatusEnum.LOAN_APPROVE_DONE.getState());
        Assert.isTrue(!caseStates,"已经放款审核通过不可执行退回...");
        if(WhetherEnum.YES.getCode().equals(caseContractInfo.getIsFastLoan())){
            caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                    .set(CaseContractInfo::getApplyStatus,ApplyStatusEnum.LOAN_WAIT_APPROVE.getState())
                    .set(CaseContractInfo::getFlowNode, FlowNodeEnum.PRIMARY.getCode())
                    .eq(CaseContractInfo::getId,caseContractInfo.getId()));
            log.info("*****************" + info.getContractNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_APPROVE.getState() + "*****************");
        }else {
            caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                    .set(CaseContractInfo::getApplyStatus,ApplyStatusEnum.LOAN_WAIT_CONFIRM.getState())
                    .set(CaseContractInfo::getFlowNode,FlowNodeEnum.REVIEW.getCode())
                    .eq(CaseContractInfo::getId,caseContractInfo.getId()));
            log.info("*****************" + info.getContractNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_CONFIRM.getState() + "*****************");
        }
        //删除历史激活池数据
        this.removeById(info);
        //工作流提交流程  保存操作记录
        LoanApproveCondition condition = new LoanApproveCondition();
        condition.setAutoFlag(Boolean.TRUE);
        condition.setContractNo(info.getContractNo());
        condition.setApplyNo(info.getApplyNo());
        condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        condition.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.BACK));
        condition.setFlowNode(FlowNodeEnum.AUTO.getCode());
        condition.setApproveRemark(backReason);
        loanWorkflowService.submitWorkflowByScheduleInfo(condition);
    }

    /**
     * @param
     * @description 结束工作流
     * <AUTHOR>
     * @date 2020/7/31
     */
    public void submitWorkflow(Long id) {
        LoanActivatePool info = this.getById(id);
        if (null != info) {
            LoanApproveCondition condition = new LoanApproveCondition();
            condition.setAutoFlag(Boolean.TRUE);
            condition.setContractNo(info.getContractNo());
            condition.setApplyNo(info.getApplyNo());
            condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
            condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
            condition.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.SUBMIT));
            condition.setFlowNode(FlowNodeEnum.AUTO.getCode());
            //工作流流程结束  保存操作记录
            WorkTaskPool pool = workTaskPoolService.getOne(Wrappers.<WorkTaskPool>lambdaQuery()
                    .eq(WorkTaskPool::getContractNo, info.getContractNo())
                    .eq( WorkTaskPool::getProcessType, condition.getUseScene()), false);
            if(!ObjectUtils.isEmpty(pool)) {
                loanWorkflowService.submitWorkflowByScheduleInfo(condition);
            }
        }
    }

    /**
     * 代码规范注释
     * <AUTHOR>
     * @date 2023/8/17 18:20
     * @param pool
     */
    @Transactional(rollbackFor = Exception.class)
    public void doLoanActivate(LoanActivatePool pool) {
        //设为已激活状态
        pool.setActStatus(ActivateStatusEnum.ACTIVATE.getStatus());
        pool.setActTime(new Date());
        this.updateById(pool);
        //通过MQ推送信息到合同系统
        approveLoanInfoService.sendLoanMsgToContract(pool.getContractNo(),pool.getApplyNo());
    }

    /**
     * 只有GPS审核状态、额度审核状态都通过后，才自动会激活合同
     *
     * @param pool
     * @return
     */
    public boolean canActivateContract(LoanActivatePool pool) {
        Boolean canActivate = Boolean.FALSE;
        if (WhetherEnum.YES.getCode().equals(pool.getGpsApproveStatus())
                && WhetherEnum.YES.getCode().equals(pool.getLimitApproveStatus())) {
            canActivate = Boolean.TRUE;
        }
        return canActivate;
    }

    /**
     * 批量更新放款激活状态
     *
     * @param id
     */
    @Override
    public Boolean updateActStatusById(Long id, String state) {
        LoanActivatePool pool = this.getById(id);
        if (null != pool) {
            //如果审核状态有一项不通过
            Assert.isTrue(canActivateContract(pool), "【GPS审核/额度审核】未通过，推送数据失败");
            //设为已激活状态
            pool.setActStatus(state);
            pool.setActTime(new Date());
            //放款审核通过
            pool.setLoanApproveStatus(ApplyStatusEnum.LOAN_APPROVE_DONE.getState());
            pool.setLoanApproveTime(new Date());
            this.updateById(pool);
        }
        return Boolean.TRUE;
    }

    /**
     * 联表查询待放款池数据
     *
     * @param page
     * @param loanActivateCondition
     * @return
     */
    @Override
    public IPage<LoanActivatePoolVO> getLoanActivateInfoList(Page page, LoanActivateCondition loanActivateCondition) {
        return baseMapper.getLoanActivateInfoList(page, loanActivateCondition);
    }

    /**
     * 联表查询已激活合同
     * @param page
     * @param postLoanCondition
     * @return
     */
    @Override
    public IPage<LoanActivatePoolVO> getActiveContractList(Page page, PostLoanCondition postLoanCondition){
        return baseMapper.getActiveContractList(page, postLoanCondition);
    }

    /**
     * 联表查询待放款池数据
     *
     * @param
     * @return
     */
    @Override
    public List<LoanActivatePoolVO> getLoanActivateInfoLists() {
        return baseMapper.getLoanActivateInfoLists();
    }

    /**
     * 代码规则注释
     * <AUTHOR>
     * @date 2023/8/17 18:20
     * @param applyNo
     */
    @Override
    public void addLoanActivateInfo(String applyNo) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(StringUtils.isNotEmpty(applyNo), CaseContractInfo::getApplyNo, applyNo), false);
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(applyNo), CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()), false);
        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(applyNo), CaseChannelInfo::getApplyNo, applyNo));

        //待激活池插入数据
        LoanActivatePool loanActivatePool = this.getOne(Wrappers.<LoanActivatePool>lambdaQuery()
                .eq(StringUtils.isNotEmpty(applyNo), LoanActivatePool::getApplyNo, applyNo));
        if (null == loanActivatePool) {
            loanActivatePool = new LoanActivatePool();
        }

        loanActivatePool.setApplyNo(applyNo);
        loanActivatePool.setContractNo(null != caseContractInfo ? caseContractInfo.getContractNo() : "");
        loanActivatePool.setCustId(null != caseCustInfo ? caseCustInfo.getId().toString() : "");
        loanActivatePool.setDealerNo(null != caseChannelInfo ? caseChannelInfo.getDealerNo() : "");
        loanActivatePool.setActStatus(ActivateStatusEnum.UN_ACTIVATE.getStatus());
        CarGpsApply carGpsApply= carGpsApplyService.getOne(Wrappers.<CarGpsApply>lambdaQuery().eq(CarGpsApply::getContractNo,caseContractInfo.getContractNo()),false);
        /**如果不安装GPS或者已经审核通过，直接审核通过*/
        if(ObjectUtil.isNotEmpty(carGpsApply)&&ObjectUtil.isNotNull(carGpsApply)){
            if(AfsEnumUtil.key(GpsInstallTypeEnum.noInstall).equals(carGpsApply.getGpsType())
                    ||StringUtils.isBlank(carGpsApply.getGpsType())
                    ||AfsEnumUtil.key(GpsStatusEnum.PASS_AUDIT).equals(carGpsApply.getApplyStatus())){
                loanActivatePool.setGpsApproveStatus(WhetherEnum.YES.getCode());
                if(carGpsApply.getExamineTime() != null){
                    loanActivatePool.setGpsApproveTime(carGpsApply.getExamineTime());
                }
            }else {
                if(StringUtils.isNotBlank(carGpsApply.getApplyStatus()) && carGpsApply.getApplyStatus().equals(AfsEnumUtil.key(GpsStatusEnum.PASS_AUDIT))){
                    loanActivatePool.setGpsApproveStatus(WhetherEnum.YES.getCode());
                    if(carGpsApply.getExamineTime() != null){
                        loanActivatePool.setGpsApproveTime(carGpsApply.getExamineTime());
                    }
                } else {
                    loanActivatePool.setGpsApproveStatus(WhetherEnum.NO.getCode());
                }
            }
        }else {
            loanActivatePool.setGpsApproveStatus(WhetherEnum.NO.getCode());
        }

        loanActivatePool.setLimitApproveStatus(WhetherEnum.NO.getCode());
        loanActivatePool.setLoanApproveStatus(ApplyStatusEnum.LOAN_PRE_APPROVE.getState());
        this.saveOrUpdate(loanActivatePool);
    }

    /**
     * 激活池未激活数据自动取消，案件处理
     * <AUTHOR>
     * @date 2023/8/10 15:09
     * @param id
     * @return com.ruicar.afs.cloud.common.core.util.IResponse
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IResponse cancelContractPool(Long id) {
        LoanActivatePool pool = this.getById(id);
        if (null != pool && null != pool.getContractActivateFlag()) {
            //如果收到合同激活落库通知则不提交流程
            throw new AfsBaseException("已激活数据不可执行废弃操作...");
        }
        pool.setLoanApproveStatus(ApplyStatusEnum.LOAN_DISCARD.getState());
        pool.setActStatus(ActivateStatusEnum.DISCARD.getStatus());
        this.updateById(pool);
        try {
            //修改合同状态
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getContractNo, pool.getContractNo()));
            if (!ObjectUtils.isEmpty(caseContractInfo)) {
                //判断额度占用时释放额度
                if (caseContractInfo.getQuotaStatus() != null && caseContractInfo.getQuotaStatus().equals(QuotaStatusEnum.occupation)) {
                    ArchiveApiDto dto = new ArchiveApiDto();
                    dto.setContractNo(caseContractInfo.getContractNo());
                    dto.setContractId(caseContractInfo.getId().toString());
                    archiveApiService.releaseQuota(dto);
                }
                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_DISCARD.getState());
                caseContractInfo.setBusinessStage(BusinessStageEnum.LOAN_DISCARD.getCode());
                caseContractInfo.setContractStatus(ContractStatusEnum.contractCancel);
                caseContractInfoService.updateById(caseContractInfo);
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()));
                if(ObjectUtil.isNotEmpty(caseBaseInfo)){
                    caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
                    caseBaseInfoService.updateById(caseBaseInfo);
                }
                //废弃信息通知进件系统
                approveLoanInfoService.sendToApplyNotic(pool.getContractNo(),ApplyStatusEnum.LOAN_DISCARD);
                CaseApproveRecord record = new CaseApproveRecord();
                record.setApplyNo(caseContractInfo.getApplyNo());
                record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                record.setApproveSuggest("自动合同取消");
                record.setApproveSuggestName("自动合同取消");
                record.setApproveEndTime(new Date());
                record.setApproveType(ApproveTypeEnum.CANAEL.getValue());
                record.setApproveRemark("自动合同取消");
                record.setDisposeNodeName("自动合同取消");
                record.setDisposeStaff("系统");
                approveRecordService.save(record);
            }
            log.info("{}激活池合同取消成功！",pool.getContractNo());
            return IResponse.success("激活池合同取消成功!");
        }catch (Exception e){
            log.info("{}激活池合同取消失败！",pool.getContractNo(),e);
            return IResponse.fail("激活池合同取消失败!");
        }
    }

    @Override
    public IResponse revokeFlow(LoanTaskRevokeVO loanTaskRevokeVO) {
        String applyNo = loanTaskRevokeVO.getApplyNo();
        String contractNo = loanTaskRevokeVO.getContractNo();
        LoanActiveLock.LockOperate lockOperate = LoanActiveLock.lockOperate(contractNo);
        try {
            if (!lockOperate.isLocked()) {
                return IResponse.fail("撤回失败，当前合同已处于自动激活处理中");
            }
            CaseContractInfo caseContractInfo = caseContractInfoService.getContractByContractNo(contractNo);
            if(!ApplyStatusEnum.REVOCABLE_WAITING.getState().equals(caseContractInfo.getApplyStatus())){
                return IResponse.fail("当前合同状态已变更，请刷新后重试");
            }
            WorkflowProcessBusinessRefInfo flowRefInfo = SpringContextHolder.getBean(WorkflowProcessBusinessRefInfoService.class)
                    .getLastRef(applyNo, flowConfigProperties.getLoanPackageId(),flowConfigProperties.getLoanTemplateId());
            if(flowRefInfo == null){
                return IResponse.fail("未获取到当前合同流程实例信息");
            }
            FlowVariable flowVariable = new FlowVariable();
            flowVariable.setFlowInstanceId(flowRefInfo.getProcessInstanceId());
            flowVariable.setName("bizOperationType");
            flowVariable.setValue("back");
            IResponse response = SpringContextHolder.getBean(FlowRunFeign.class).setFlowVariableByFlowInstance(flowVariable);
            if(!CommonConstants.SUCCESS.equals(response.getCode())){
                log.info("审批撤回操作设置流程变量响应结果：{}",response);
                return IResponse.fail("操作失败，流程变量设置异常");
            }
            IResponse<Boolean> signResp = SpringContextHolder.getBean(FlowRunFeign.class)
                    .signalInstance(flowRefInfo.getProcessInstanceId());
            if(CommonConstants.SUCCESS.equals(signResp.getCode())) {
                CaseApproveRecord record = new CaseApproveRecord();
                record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                record.setApproveEndTime(new Date());
                record.setApproveStartTime(new Date());
                record.setDisposeStaff("系统");

                // 事件个性字段
                record.setApplyNo(loanTaskRevokeVO.getApplyNo());
                record.setStageId(flowVariable.getFlowInstanceId());
                record.setApproveSuggest(AfsEnumUtil.key(ApplyStatusEnum.LOAN_CALL_BACK));
                record.setApproveSuggestName("审核撤回");
                record.setApproveReason(loanTaskRevokeVO.getReason());
                record.setApproveRemark(loanTaskRevokeVO.getReason());
                if (SecurityUtils.getUser() != null) {
                    record.setDisposeStaff(SecurityUtils.getUser().getUserRealName());
                }
                caseApproveRecordService.save(record);

                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
                caseContractInfoService.updateById(caseContractInfo);

                workflowTaskInfoService.update(Wrappers.<WorkflowTaskInfo>lambdaUpdate()
                        .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getLoanPackageId())
                        .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getLoanTemplateId())
                        .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                        .set(WorkflowTaskInfo::getIsRevoked, AfsEnumUtil.key(com.ruicar.afs.cloud.enums.common.YesOrNoEnum.YES))
                );


                return IResponse.success(null);
            }
        }catch (Exception e){
            log.error("流程撤销操作失败：{}",contractNo,e);
            return IResponse.fail(String.format("撤回失败：%s",e.getMessage()));
        }finally {
            lockOperate.free();
        }
        return IResponse.success(null);
    }

    @Override
    public void saveLoanCancel(LoanDiscardDTO loanDiscardDTO) {

        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getContractNo, loanDiscardDTO.getContractNo()), false);
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, loanDiscardDTO.getApplyNo()));
        //主状态信息
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
        if (!ObjectUtils.isEmpty(caseContractInfo)) {
            log.info("{}合同取消信息保存！", loanDiscardDTO.getContractNo());
            //修改合同状态
            caseContractInfo.setBusinessStage(BusinessStageEnum.LOAN_DISCARD.getCode());
            caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_CANCEL_WAIT_APPROVE.getState());
            caseContractInfo.setContractStatus(ContractStatusEnum.contractCancel);
            caseContractInfoService.updateById(caseContractInfo);
            //记录合同取消日志
            List<WorkProcessScheduleInfo> info = workProcessScheduleInfoService.list(Wrappers.<WorkProcessScheduleInfo>query().lambda()
                    .eq(WorkProcessScheduleInfo::getContractNo, loanDiscardDTO.getContractNo()));
            String disposeStaff = "";
            CaseApproveRecord lockOprRecord = new CaseApproveRecord();
            lockOprRecord.setContractNo(loanDiscardDTO.getContractNo());
            lockOprRecord.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
            lockOprRecord.setApproveRemark(loanDiscardDTO.getCancelContent());
            AfsUser user = SecurityUtils.getUser();
            if (com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(user)) {
                disposeStaff = user.getUserRealName();
            } else {
                disposeStaff = "合作商";
            }
            lockOprRecord.setDisposeStaff(disposeStaff);
            lockOprRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
            lockOprRecord.setApproveSuggestName("合同取消");
            lockOprRecord.setApproveStartTime(new Date());
            if (CollectionUtils.isNotEmpty(info)) {
                lockOprRecord.setDisposeNode(info.get(0).getCurrentNodeId());
                lockOprRecord.setDisposeNodeName(info.get(0).getCurrentNodeName());
                lockOprRecord.setStageId(info.get(0).getId().toString());
            }
            lockOprRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
            lockOprRecord.setFlowNode(caseContractInfo.getFlowNode());
            caseApproveRecordService.save(lockOprRecord);

        }
        if (AfsEnumUtil.key(BusinessStateInEnum.APPROVED).equals(caseBaseInfo.getBusinessStateIn()) ||
                AfsEnumUtil.key(BusinessStateInEnum.CONDITIONAL_APPROVE).equals(caseBaseInfo.getBusinessStateIn())) {
            caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
            caseBaseInfoService.updateById(caseBaseInfo);
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, loanDiscardDTO.getApplyNo()));
            //合同取消记录日志
            String disposeStaff = "";
            if (!ObjectUtils.isEmpty(caseChannelInfo)) {
                disposeStaff = caseChannelInfo.getSaleAdvisor();
            }
            CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
            caseApproveRecord.setApplyNo(loanDiscardDTO.getApplyNo());
            caseApproveRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
            caseApproveRecord.setDisposeStaff(disposeStaff);
            caseApproveRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
            caseApproveRecord.setApproveSuggestName("取消");

            caseApproveRecord.setApproveRemark(Optional.ofNullable(loanDiscardDTO.getCancelContent()).orElse("合作商主动取消当前案件!"));


            caseApproveRecord.setApproveStartTime(new Date());

            caseApproveRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
            caseApproveRecordService.save(caseApproveRecord);

        }
    }

}
