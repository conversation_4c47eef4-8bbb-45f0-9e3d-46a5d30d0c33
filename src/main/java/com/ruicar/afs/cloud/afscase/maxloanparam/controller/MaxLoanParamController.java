package com.ruicar.afs.cloud.afscase.maxloanparam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.maxloanparam.condition.MaxLoanParamCondition;
import com.ruicar.afs.cloud.afscase.maxloanparam.entity.MaxLoanParam;
import com.ruicar.afs.cloud.afscase.maxloanparam.service.MaxLoanParamService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 理论最大贷额接口
 * <AUTHOR>
 * @created 2020/8/28 20:53
 * @version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("理论最大贷额接口")
@RequestMapping("/maxLoanParam")

public class MaxLoanParamController {

    private MaxLoanParamService maxLoanParamService;
  

    @PostMapping(value = "/queryMaxLoanList")
    @ApiOperation(value = "多条件分页获取信审理论贷额参数信息数据")
    public IResponse<IPage<MaxLoanParam>> queryMaxLoanList(@ModelAttribute MaxLoanParamCondition condition) {
        Page<CaseConfParam> page = maxLoanParamService.page(new Page(condition.getPageNumber(), condition.getPageSize()), Wrappers.<MaxLoanParam>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getParamStatus()), MaxLoanParam::getParamStatus, condition.getParamStatus())
                .like(StringUtils.isNotEmpty(condition.getParamLogo()), MaxLoanParam::getParamLogo, condition.getParamLogo()));
        return IResponse.success(page);
    }



    @RequestMapping(value = "/deleteMaxLoanParam/{id}", method = {RequestMethod.POST,RequestMethod.DELETE})
    @ApiOperation(value = "通过id删除")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("删除理论贷额参数")
    public IResponse<Boolean> deleteMaxLoanParam(@PathVariable String id) {
        MaxLoanParam maxLoanParam = maxLoanParamService.getById(id);
        if (maxLoanParam == null) {
            return new IResponse<Boolean>().setMsg("通过id删除数据成功");
        }
        maxLoanParamService.removeById(id);
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }

    @PostMapping(value = "/addMaxLoanParam")
    @ApiOperation(value = "新增理论贷额参数")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("新增理论贷额参数")
    public IResponse<Boolean> addMaxLoanParam(@RequestBody MaxLoanParam maxLoanParam) {
        String paramLogo = maxLoanParam.getParamLogo();
        List<MaxLoanParam> list = maxLoanParamService.list(Wrappers.<MaxLoanParam>query().lambda()
                .eq(MaxLoanParam::getParamLogo, paramLogo));
        if (list != null && list.size() > 0) {
            return new IResponse<Boolean>().setMsg("场景标识:" + paramLogo + "重复,请重新提交！").setCode("0001");
        } else {
            maxLoanParamService.save(maxLoanParam);
            return new IResponse<Boolean>().setMsg("新增理论贷额参数成功");
        }
    }

    @PostMapping(value = "/editMaxLoanParam")
    @ApiOperation(value = "修改理论贷额参数")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("修改理论贷额参数")
    public IResponse<Boolean> editMaxLoanParam(@ModelAttribute MaxLoanParam maxLoanParam) {
        maxLoanParamService.updateById(maxLoanParam);
        return new IResponse<Boolean>().setMsg("修改理论贷额参数成功");
    }
}