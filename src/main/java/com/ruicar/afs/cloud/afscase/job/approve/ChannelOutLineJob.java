package com.ruicar.afs.cloud.afscase.job.approve;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.dealeroutline.entity.ChannelOutLine;
import com.ruicar.afs.cloud.afscase.dealeroutline.enums.ConstantEnum;
import com.ruicar.afs.cloud.afscase.dealeroutline.service.ChannelOutLineService;
import com.ruicar.afs.cloud.common.job.core.biz.model.ReturnT;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJob;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 渠道预退网状态变更
 * <AUTHOR>
 * @Date 2022/05/07 13:13
 */
@AfsJob
@Component
@AllArgsConstructor
@Slf4j
public class ChannelOutLineJob {

    private ChannelBaseInfoService channelBaseInfoService;
    private ChannelOutLineService channelOutLineService;

    @AfsJobHandler("channelOutStatusChange")
    public ReturnT caseEffectTimeMonitor(String params) {
        try {
            /**
             * 1.根据预退网日期查询小于当前日期的渠道信息
             * 2.将信息状态改为待退网
             */
            List<ChannelBaseInfo> channelBaseInfos = channelBaseInfoService.list(Wrappers.<ChannelBaseInfo>lambdaQuery()
                    .lt(ChannelBaseInfo::getPrepareOutlineDate, new Date())
                    .eq(ChannelBaseInfo::getDealerStatus, ConstantEnum.PRE_WITHDRAWAL_NETWORK.getCode()));
            List<String> businessNoList = new ArrayList<>();
            if (channelBaseInfos != null && channelBaseInfos.size() > 0){
                for (ChannelBaseInfo c:channelBaseInfos){
                    businessNoList.add(c.getId() + "");
                }
                channelBaseInfoService.update(Wrappers.<ChannelBaseInfo>update().lambda()
                        .in(ChannelBaseInfo::getChannelId,businessNoList)
                        .set(ChannelBaseInfo::getDealerStatus, ConstantEnum.NETWORK_TORETURNED.getCode()));
                channelOutLineService.update(Wrappers.<ChannelOutLine>update().lambda()
                        .in(ChannelOutLine::getChannelId,businessNoList)
                        .set(ChannelOutLine::getOutLineType, ConstantEnum.OUT_TYPE_2.getCode()));
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("渠道预退网状态变更执行失败", e);
            return new ReturnT(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}
