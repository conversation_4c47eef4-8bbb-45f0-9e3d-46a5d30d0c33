package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.TaskTransferParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffAllStatusCondition;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffBaseCondition;
import com.ruicar.afs.cloud.afscase.writeoff.dto.WriteOffFreezeExcel;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffBatchFrozenVO;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffExtensionVO;
import com.ruicar.afs.cloud.afscase.writeoff.dto.BdRequestDataDTO;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffAccountCycleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.dto.PermissionResultDto;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ReturnDataVO;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ReturnDataWorkInfoResp;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffDeductDetail;
import com.ruicar.afs.cloud.afscase.writeoff.enums.AccountCycleEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ApportionEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.OverdueStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.feign.ApplyWriteOffFeign;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInvoiceRelMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mq.WriteOffSender;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffAccountCycleDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffContractDetailManageService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffDeductDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPermissionService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.LaunchWorkVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.OverdueVO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.ServiceFeeTotalVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffBaseDraftExcelVO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffBaseExcelVO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffBaseInfoApproveVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffBaseInfoVo;
import com.ruicar.afs.cloud.applyservice.api.request.Request;
import com.ruicar.afs.cloud.bizcommon.voucher.service.MqMessageQueueLogService;
import com.ruicar.afs.cloud.channel.dto.ChannelBaseInfoApiDTO;
import com.ruicar.afs.cloud.channel.service.ChannelApiService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.uid.AfsSequenceGenerator;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.ProcessStatus;
import com.ruicar.afs.cloud.common.modules.constant.VoucherBuriedPointNo;
import com.ruicar.afs.cloud.common.modules.dto.mq.voucher.VoucherFlowInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffBaseInfoBatchDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffBaseInfoDto;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import com.ruicar.afs.cloud.enums.common.InvoiceExtenApplicateStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class WriteOffBaseInfoServiceImpl extends ServiceImpl<WriteOffBaseInfoMapper, WriteOffBaseInfo> implements WriteOffBaseInfoService {
    private final AfsSequenceGenerator afsSequenceGenerator;
    private final WriteOffBaseInfoMapper writeOffBaseInfoMapper;
    private final WriteOffSender writeOffSender;
    private final WriteOffBaseInvoiceRelMapper baseInvoiceRelMapper;
    private final ApplyConfig applyConfig;
    private ChannelApiService channelApiService;
    private final FlowConfigProperties flowConfigProperties;
    private final WorkflowHelper workflowHelper;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private CaseApproveRecordService caseApproveRecordService;
    private final WriteOffContractDetailManageService writeOffContractDetailManageService;
    private final CaseContractInfoService caseContractInfoService;
    private final WriteOffAccountCycleDetailService writeOffAccountCycleDetailService;
    private final WorkflowWrapperService workflowWrapperService;
    private final WriteOffPermissionService writeOffPermissionService;
    private final ApplyWriteOffFeign applyWriteOffFeign;
    private final MqMessageQueueLogService mqMessageQueueLogService;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final WriteOffDeductDetailService writeOffDeductDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> importList(MultipartFile file) {
        List<WriteOffBaseDraftExcelVO> excelVOList = new ArrayList<>();
        try {
            EasyExcelFactory.read(file.getInputStream(), WriteOffBaseDraftExcelVO.class, new AnalysisEventListener<WriteOffBaseDraftExcelVO>() {
                @Override
                public void invoke(WriteOffBaseDraftExcelVO excelVO, AnalysisContext analysisContext) {
                    excelVOList.add(excelVO);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    log.info("所有数据解析完成！数据长度为{}", excelVOList.size());
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AfsBaseException("表格数据读取异常");
        }
        BigDecimal zero = BigDecimal.ZERO;
        List<WriteOffBaseInfo> baseInfoList = new ArrayList<>();

        // 服务费发票上传金额税率
        Map<String, String> configuration = DicHelper.getDicMaps("invoiceUploadAmountTaxRate")
            .values()
            .stream()
            .flatMap(List::stream).collect(Collectors.toMap(DicDataDto::getTitle, DicDataDto::getValue));
        BigDecimal tax = new BigDecimal(configuration.get("税率")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        //解析数据
        for (int i = 0; i < excelVOList.size(); i++) {
            WriteOffBaseDraftExcelVO excelVO = excelVOList.get(i);
            //获取到导入的核销项编号
            String applyNo = excelVO.getApplyNo();
            Assert.isTrue(StrUtil.isNotBlank(applyNo), "第" + (i + 1) + "行的核销项编号为空！");
            WriteOffBaseInfo baseInfo = this.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .eq(WriteOffBaseInfo::getApplyNo, applyNo));
            Assert.isTrue(baseInfo != null, "第" + (i + 1) + "行的核销项编号不正确！");
            Assert.isTrue(WriteOffStatusEnum.DRAFT_APPROVAL == baseInfo.getWriteOffStatus() || WriteOffStatusEnum.DELETED == baseInfo.getWriteOffStatus(), "第" + (i + 1) + "行的核销项不是草稿或撤回状态");
            if (excelVO.getPrizeAmount() == null) {
                excelVO.setPrizeAmount(zero);
            }
            if (excelVO.getImportPunishAmount() == null) {
                excelVO.setImportPunishAmount(zero);
            }
            BigDecimal prize = excelVO.getPrizeAmount();
            BigDecimal punish = excelVO.getImportPunishAmount();
            Assert.isTrue(prize.compareTo(zero) >= 0 && punish.compareTo(zero) >= 0, "第" + (i + 1) + "行的奖惩金额不能小于0！");
            baseInfo.setPrizeAmount(prize);
            baseInfo.setPunishAmount(punish);
            baseInfo.setPrizeRemark(excelVO.getPrizeRemark());
            baseInfo.setPunishRemark(excelVO.getPunishRemark());
            baseInfo.setInvoiceAmount(baseInfo.getInvoiceAmountBegin());
            baseInfo.setNextToPunishAmount(baseInfo.getNextToPunishAmountBegin());
            baseInfo.setNextToOtherPunishAmount(baseInfo.getNextToOtherPunishAmountBegin());
            baseInfo.setNextToCancelPunishAmount(baseInfo.getNextToCancelPunishAmountBegin());
            baseInfo.setNextToEarlySettleAmount(baseInfo.getNextToEarlySettleAmountBegin());
            baseInfo.setActualCancelPunishAmount(baseInfo.getActualCancelPunishAmountBegin());
            baseInfo.setActualEarlySettleAmount(baseInfo.getActualEarlySettleAmountBegin());
            baseInfo.setActualOtherPunishAmount(baseInfo.getActualOtherPunishAmountBegin());
            //总体
            if (prize.compareTo(punish) > 0) {
                BigDecimal subtract = prize.subtract(punish);
                if (baseInfo.getNextToPunishAmount().compareTo(subtract) > 0) {
                    baseInfo.setNextToPunishAmount(baseInfo.getNextToPunishAmount().subtract(subtract));
                } else {
                    baseInfo.setInvoiceAmount(baseInfo.getInvoiceAmount().add(subtract).subtract(baseInfo.getNextToPunishAmount()));
                    baseInfo.setNextToPunishAmount(zero);
                }
            } else if (prize.compareTo(punish) < 0) {
                BigDecimal subtract = punish.subtract(prize);
                if (baseInfo.getInvoiceAmount().compareTo(subtract) > 0) {
                    baseInfo.setInvoiceAmount(baseInfo.getInvoiceAmount().subtract(subtract));
                } else {
                    baseInfo.setNextToPunishAmount(baseInfo.getNextToPunishAmount().add(subtract).subtract(baseInfo.getInvoiceAmount()));
                    baseInfo.setInvoiceAmount(zero);
                }
            } else {
            }
            //合同扣减明细
            List<WriteOffDeductDetail> allDeductList = writeOffDeductDetailService.list(Wrappers.<WriteOffDeductDetail>lambdaQuery()
                    .eq(WriteOffDeductDetail::getBaseInfoApply, applyNo)
                    .orderByDesc(WriteOffDeductDetail::getActualDeductAmountBegin));
            for (WriteOffDeductDetail deductDetail : allDeductList) {
                deductDetail.setActualDeductAmount(deductDetail.getActualDeductAmountBegin());
                deductDetail.setNextDeductAmount(deductDetail.getNextDeductAmountBegin());
            }
            //奖励金额大于0
            if (prize.compareTo(BigDecimal.ZERO) > 0) {
                List<WriteOffDeductDetail> cancelList = allDeductList.stream()
                        .filter(k -> k.getContractStatus() == ContractStatusEnum.contractCancel &&
                                k.getNextDeductAmountBegin().compareTo(BigDecimal.ZERO) > 0).toList();
                List<WriteOffDeductDetail> earlyList = allDeductList.stream()
                        .filter(k -> k.getContractStatus() == ContractStatusEnum.advanceSettle &&
                                k.getNextDeductAmountBegin().compareTo(BigDecimal.ZERO) > 0).toList();
                //先扣取消，再扣结清和其他
                BigDecimal nextToCancelPunishAmount = baseInfo.getNextToCancelPunishAmount();
                if (nextToCancelPunishAmount != null && nextToCancelPunishAmount.compareTo(BigDecimal.ZERO) > 0) {
                    Assert.isTrue(cancelList.size() > 0, "合同取消扣罚计算，数据异常");
                    if (prize.compareTo(nextToCancelPunishAmount) >= 0) {
                        baseInfo.setNextToCancelPunishAmount(BigDecimal.ZERO);
                        baseInfo.setActualCancelPunishAmount(baseInfo.getActualCancelPunishAmount().add(nextToCancelPunishAmount));
                        prize = prize.subtract(nextToCancelPunishAmount);
                        for (WriteOffDeductDetail detail : cancelList) {
                            detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(detail.getNextDeductAmountBegin()));
                            detail.setNextDeductAmount(BigDecimal.ZERO);
                        }
                    } else {
                        baseInfo.setNextToCancelPunishAmount(nextToCancelPunishAmount.subtract(prize));
                        baseInfo.setActualCancelPunishAmount(baseInfo.getActualCancelPunishAmount().add(prize));
                        for (WriteOffDeductDetail detail : cancelList) {
                            if (prize.compareTo(detail.getNextDeductAmountBegin()) >= 0) {
                                detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(detail.getNextDeductAmountBegin()));
                                detail.setNextDeductAmount(BigDecimal.ZERO);
                                prize = prize.subtract(detail.getNextDeductAmountBegin());
                            } else {
                                if (prize.compareTo(BigDecimal.ZERO) > 0) {
                                    detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(prize));
                                    detail.setNextDeductAmount(detail.getNextDeductAmountBegin().subtract(prize));
                                    prize = BigDecimal.ZERO;
                                } else {
                                    detail.setActualDeductAmount(detail.getActualDeductAmountBegin());
                                    detail.setNextDeductAmount(detail.getNextDeductAmountBegin());
                                }
                            }
                        }
                        Assert.isTrue(prize.compareTo(BigDecimal.ZERO) == 0, "合同取消扣罚计算，数据异常！");
                    }
                }
                //提前结清扣减
                BigDecimal nextToEarlySettleAmount = baseInfo.getNextToEarlySettleAmount();
                if (prize.compareTo(BigDecimal.ZERO) > 0 && nextToEarlySettleAmount != null && nextToEarlySettleAmount.compareTo(BigDecimal.ZERO) > 0) {
                    Assert.isTrue(earlyList.size() > 0, "合同提前结清扣罚计算，数据异常");
                    if (prize.compareTo(nextToEarlySettleAmount) >= 0) {
                        baseInfo.setNextToEarlySettleAmount(BigDecimal.ZERO);
                        baseInfo.setActualEarlySettleAmount(baseInfo.getActualEarlySettleAmount().add(nextToEarlySettleAmount));
                        prize = prize.subtract(nextToEarlySettleAmount);
                        for (WriteOffDeductDetail detail : earlyList) {
                            detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(detail.getNextDeductAmountBegin()));
                            detail.setNextDeductAmount(BigDecimal.ZERO);
                        }
                    } else {
                        baseInfo.setNextToEarlySettleAmount(nextToEarlySettleAmount.subtract(prize));
                        baseInfo.setActualEarlySettleAmount(baseInfo.getActualEarlySettleAmount().add(prize));
                        for (WriteOffDeductDetail detail : earlyList) {
                            if (prize.compareTo(detail.getNextDeductAmountBegin()) >= 0) {
                                detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(detail.getNextDeductAmountBegin()));
                                detail.setNextDeductAmount(BigDecimal.ZERO);
                                prize = prize.subtract(detail.getNextDeductAmountBegin());
                            } else {
                                if (prize.compareTo(BigDecimal.ZERO) > 0) {
                                    detail.setActualDeductAmount(detail.getActualDeductAmountBegin().add(prize));
                                    detail.setNextDeductAmount(detail.getNextDeductAmountBegin().subtract(prize));
                                    prize = BigDecimal.ZERO;
                                } else {
                                    detail.setActualDeductAmount(detail.getActualDeductAmountBegin());
                                    detail.setNextDeductAmount(detail.getNextDeductAmountBegin());
                                }
                            }
                        }
                        Assert.isTrue(prize.compareTo(BigDecimal.ZERO) == 0, "合同提前结清扣罚计算，数据异常！");
                    }
                }
            }
            //更新合同扣减明细
            writeOffDeductDetailService.updateBatchById(allDeductList);
            //其他扣减
            BigDecimal nextToOtherPunishAmount = baseInfo.getNextToOtherPunishAmount() == null ? BigDecimal.ZERO : baseInfo.getNextToOtherPunishAmount();
            BigDecimal nextCancel = baseInfo.getNextToCancelPunishAmount() == null ? BigDecimal.ZERO : baseInfo.getNextToCancelPunishAmount();
            BigDecimal nextEarly = baseInfo.getNextToEarlySettleAmount() == null ? BigDecimal.ZERO : baseInfo.getNextToEarlySettleAmount();
            BigDecimal nextCancelAndEarly = nextCancel.add(nextEarly);
            BigDecimal nextToPunishAmount = baseInfo.getNextToPunishAmount();
            if (nextCancelAndEarly.compareTo(BigDecimal.ZERO) > 0) {
                baseInfo.setNextToOtherPunishAmount(nextToOtherPunishAmount.add(punish));
            }else {
                if (nextToPunishAmount.compareTo(BigDecimal.ZERO) > 0) {
                    baseInfo.setNextToOtherPunishAmount(nextToPunishAmount);
                    baseInfo.setActualOtherPunishAmount(baseInfo.getLastToOtherPunishAmount().add(punish).subtract(nextToPunishAmount));
                } else {
                    BigDecimal totalPunish = punish.add(nextToOtherPunishAmount);
                    baseInfo.setNextToOtherPunishAmount(BigDecimal.ZERO);
                    baseInfo.setActualOtherPunishAmount(baseInfo.getActualOtherPunishAmount().add(totalPunish));
                }
            }
            BigDecimal nextToOther2 = baseInfo.getNextToOtherPunishAmount();
            BigDecimal totalNext = nextCancelAndEarly.add(nextToOther2);
            if (nextToPunishAmount.compareTo(totalNext) != 0) {
                log.error("{}奖惩项出现异常，prize:{},nextCancel:{},nextEarly:{},nextOther:{},total:{}", applyNo, prize, nextCancel, nextEarly, nextToOther2, nextToPunishAmount);
                throw new RuntimeException(applyNo + "计算奖惩项出现异常");
            }
            baseInfo.setAmountToBeInvoiced(baseInfo.getInvoiceAmount());
            //未含税金额=总金额/1.06
            baseInfo.setExcludeTaxAmount(baseInfo.getInvoiceAmount().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
            baseInfo.setTaxAmount(baseInfo.getInvoiceAmount().subtract(baseInfo.getExcludeTaxAmount()));
            baseInfoList.add(baseInfo);
            BigDecimal totalPrize = baseInfo.getInvoiceAmount().subtract(baseInfo.getInvoiceAmountBegin());
            List<WriteOffContractDetailManage> detailList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                    .eq(WriteOffContractDetailManage::getBaseInfoApply, applyNo));
            int size = detailList.size();
            if (size >= 1) {
                //加权平均分配奖惩金额
                WriteOffContractDetailManage detail = null;
                BigDecimal totalDetailPrize = BigDecimal.ZERO;
                BigDecimal puAmount = BigDecimal.ZERO;
                for (int j = 0; j < detailList.size(); j++) {
                    detail = detailList.get(j);
                    //如果服务费为0，奖励金额就按订单平均分配
                    if (baseInfo.getBeforeAmount().compareTo(BigDecimal.ZERO) == 0) {
                        puAmount = totalPrize.divide(new BigDecimal(detailList.size()), 2, RoundingMode.HALF_UP);
                    } else {
                        puAmount=totalPrize.multiply(detail.getBeforeAmount()).divide(baseInfo.getBeforeAmount(), 2, RoundingMode.HALF_UP);
                    }
                    if (j == detailList.size() - 1) {
                        //最后一项,特殊处理,扣罚金额=总扣罚金额-其他合同扣罚总额
                        puAmount = totalPrize.subtract(totalDetailPrize);
                        detail.setPrizeOrPunishAmount(puAmount);
                        detail.setServiceCharge(detail.getServiceChargeBegin().add(puAmount));
                        detail.setExcludeTaxAmount(detail.getServiceCharge().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
                        detail.setTaxAmount(detail.getServiceCharge().subtract(detail.getExcludeTaxAmount()));
                        detail.setApportionAmount(detail.getExcludeTaxAmount().divide(new BigDecimal(detail.getLoanTerm()), 2, RoundingMode.HALF_UP));
                    } else {
                        detail.setPrizeOrPunishAmount(puAmount);
                        detail.setServiceCharge(detail.getServiceChargeBegin().add(puAmount));
                        detail.setExcludeTaxAmount(detail.getServiceCharge().divide(new BigDecimal("1").add(tax), 2, RoundingMode.HALF_UP));
                        detail.setTaxAmount(detail.getServiceCharge().subtract(detail.getExcludeTaxAmount()));
                        detail.setApportionAmount(detail.getExcludeTaxAmount().divide(new BigDecimal(detail.getLoanTerm()), 2, RoundingMode.HALF_UP));
                        totalDetailPrize = totalDetailPrize.add(puAmount);
                    }
                    puAmount = BigDecimal.ZERO;
                    detail.setResidueApportionAmount(detail.getExcludeTaxAmount());
                }
                writeOffContractDetailManageService.updateBatchById(detailList);
            }
        }
        this.updateBatchById(baseInfoList);
        return IResponse.success("导入成功");
    }
    @Override
    public void exportTemplent(HttpServletResponse response) {
        Set<String> includeColumnFiledNames = new HashSet<String>();
        includeColumnFiledNames.add("organId");
        includeColumnFiledNames.add("region");
        includeColumnFiledNames.add("manage");
        includeColumnFiledNames.add("writeOffType");
        includeColumnFiledNames.add("writeOffName");
        includeColumnFiledNames.add("writeOffMonth");
        includeColumnFiledNames.add("invoiceAmount");
        this.commExport(response, new ArrayList<>(),"服务费模板",WriteOffBaseInfo.class,includeColumnFiledNames);
    }

    public void commExport(HttpServletResponse response, List writeOffBaseInfoList, String fileName,Class classType,Set<String> includeColumnFiledNames) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode(fileName) + ".xlsx");
            ExcelWriter excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), classType).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0).includeColumnFiledNames(includeColumnFiledNames).build();
            excelWriterBuilder.write(writeOffBaseInfoList, htSheetWrite);
            excelWriterBuilder.finish();
        } catch (Exception e) {
            log.error("下载异常" + e);
            throw new AfsBaseException("下载失败");
        }
    }

    @Override
    public IResponse getWriteOffList(QueryCondition<WriteOffBaseCondition> queryCondition) {
        WriteOffBaseCondition condition = queryCondition.getCondition();
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        Page<WriteOffBaseInfo> page = this.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffBaseInfo::getOrganId, resultDto.getHitCodeList())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffBaseInfo::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getOrganId()), WriteOffBaseInfo::getOrganId, condition.getOrganId())
                .like(StrUtil.isNotBlank(condition.getOrganName()), WriteOffBaseInfo::getOrganName, condition.getOrganName())
                .eq(condition.getWriteOffStatus() != null, WriteOffBaseInfo::getWriteOffStatus, condition.getWriteOffStatus())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffBaseInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .eq(StrUtil.isNotBlank(condition.getDraftBatchCode()), WriteOffBaseInfo::getDraftBatchCode, condition.getDraftBatchCode())
                .eq(StrUtil.isNotBlank(condition.getChannelBelong()), WriteOffBaseInfo::getChannelBelong, condition.getChannelBelong())
                .eq(StrUtil.isNotBlank(condition.getFrozenStatus()), WriteOffBaseInfo::getFrozenStatus, condition.getFrozenStatus())
                .eq(StrUtil.isNotBlank(condition.getWriteOffType()), WriteOffBaseInfo::getWriteOffType, condition.getWriteOffType())
                .orderByDesc(WriteOffBaseInfo::getCreateTime));
        return IResponse.success(page);
    }

    @Override
    public IResponse draftItemApproval(String[] id, String type, String remark) {
        return null;
    }

    @Override
    public void exportWriteOffDate(HttpServletResponse response, WriteOffBaseCondition condition) {
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        List<WriteOffStatusEnum> statusEnumList = new ArrayList<>();
        statusEnumList.add(WriteOffStatusEnum.DRAFT_APPROVAL);
        statusEnumList.add(WriteOffStatusEnum.DELETED);
        List<WriteOffBaseInfo> baseInfoList = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffBaseInfo::getOrganId, resultDto.getHitCodeList())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffBaseInfo::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getOrganId()), WriteOffBaseInfo::getOrganId, condition.getOrganId())
                .like(StrUtil.isNotBlank(condition.getOrganName()), WriteOffBaseInfo::getOrganName, condition.getOrganName())
                .in(WriteOffBaseInfo::getWriteOffStatus, statusEnumList)
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffBaseInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .orderByDesc(WriteOffBaseInfo::getCreateTime));
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<WriteOffBaseDraftExcelVO> feesVOList = baseInfoList.stream().map(base -> {
            WriteOffBaseDraftExcelVO feesVO = new WriteOffBaseDraftExcelVO();
            BeanUtil.copyProperties(base, feesVO);
            feesVO.setImportPunishAmount(base.getPunishAmount());
            if (WriteOffStatusEnum.DELETED == base.getWriteOffStatus()) {
                feesVO.setStatusName("已撤回");
            } else {
                feesVO.setStatusName("草稿项审批");
            }
            if (FrozenStatusEnum.FROZEN == base.getFrozenStatus()) {
                feesVO.setFreezeStatus("已冻结");
            } else if (FrozenStatusEnum.NORMAL == base.getFrozenStatus()) {
                feesVO.setFreezeStatus("正常");
            }else {
                if (base.getFrozenStatus() != null) {
                    feesVO.setFreezeStatus(AfsEnumUtil.desc(base.getFrozenStatus()));
                }
            }
            if (base.getReceiveElectronicFlag() != null && !base.getReceiveElectronicFlag().equals("")){
                feesVO.setReceiveElectronicFlag("1".equals(base.getReceiveElectronicFlag()) ? "电子发票" : "纸质发票");
            }
            if (base.getInvoiceUploadDeadline() != null) {
                feesVO.setInvoiceUploadDeadline(dateFormat.format(base.getInvoiceUploadDeadline()));
            }
            if (base.getReturningCompanyDeadline() != null) {
                feesVO.setReturningCompanyDeadline(dateFormat.format(base.getReturningCompanyDeadline()));
            }
            if (base.getTimeSyncFlag() != null) {
                feesVO.setTimeSyncFlag("1".equals(base.getTimeSyncFlag()) ? "已同步" : "未同步");
            }
            feesVO.setWriteOffType(WriteOffTypeEnum.createTypeEnum(base.getWriteOffType()).getDesc());
            return feesVO;
        }).collect(Collectors.toList());
        this.commExport(response, feesVOList,"核销项草稿明细", WriteOffBaseDraftExcelVO.class,null);
    }

    @Override
    public IResponse changeFrozenType(WriteOffAllStatusCondition condition) {
        List<String> applyNos = condition.getApplyNos();
        //需要变成的状态
        FrozenStatusEnum frozenStatus = condition.getFrozenStatus();
        Assert.isTrue(CollectionUtil.isNotEmpty(applyNos) && frozenStatus != null, "参数错误");
        List<WriteOffBaseInfo> baseInfoList = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .in(WriteOffBaseInfo::getApplyNo, applyNos));
        for (WriteOffBaseInfo baseInfo : baseInfoList) {
            if (baseInfo.getFrozenStatus() == frozenStatus) {
                return IResponse.fail("核销项编号：" + baseInfo.getApplyNo() + "状态异常");
            }
            baseInfo.setFrozenStatus(frozenStatus);
            baseInfo.setFrozenBusiNo(condition.getFrozenBusiNo());
            baseInfo.setThawBusiNo(condition.getThawBusiNo());
        }
        this.updateBatchById(baseInfoList);
        return IResponse.success("操作成功");
    }

    /**
     * 导入自动冻结
     * @param file
     * @return
     */
    @Override
    public IResponse importAutoFrozen(MultipartFile file, String frozenStatus) {

        if (file.isEmpty()) {
            return IResponse.fail("请上传正确文件").setCode(ProcessStatus.ILLEGAL.getCode()).setMsg("请上传正确文件").setData(JSONUtil.parseArray("[{\"msg\":\"请上传正确文件\"}]"));
        }
        /**  文件格式 by ZC.GUO  **/
        String fileType = FileUtil.extName(file.getOriginalFilename());
        String rightFileType = "xlsx";
        if (!rightFileType.equals(fileType)) {
            return IResponse.fail("请上传与模板格式一致的excel文件").setCode(ProcessStatus.ILLEGAL.getCode()).setMsg("请上传与模板格式一致的excel文件").setData(JSONUtil.parseArray("[{\"msg\":\"请上传与模板格式一致的excel文件\"}]"));
        }

        if (frozenStatus == null || frozenStatus == ""){
            return IResponse.fail("执行冻结解冻状态为空");
        }

        /**  文件解析 by ZC.GUO  **/
        try {
            log.info("importAutoFrozen---执行人{},file name {},执行状态{}",SecurityUtils.getUsername(),file.getOriginalFilename(),frozenStatus);
            this.handleExcelImport(file, frozenStatus);
        } catch (Exception e) {
            log.info(e.getMessage(),e);
            //手动开启事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return IResponse.fail(e.getMessage()).setCode(ProcessStatus.ILLEGAL.getCode()).setMsg(e.getMessage()).setData(JSONUtil.parseArray("[{\"msg\":\"" + e.getMessage() + "\"}]"));
        }
        return IResponse.success("").setMsg("操作成功！");
    }

    private void handleExcelImport (MultipartFile file, String frozenStatus) throws Exception {
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file.getInputStream());
        /**  解析excel**/
        Sheet sheet = xssfWorkbook.getSheetAt(0);

        int end = sheet.getLastRowNum();
        for (int i = 1; i <= end; i++) {
            /**  逐行解析 by ZC.GUO  **/
            WriteOffFreezeExcel writeOffFreezeExcel = this.extractRow(sheet.getRow(i), i);
            log.info("importAutoFrozen--handleExcelImport,，第" + (i+1) + "行excel数据为{}",JSONObject.toJSONString(writeOffFreezeExcel));

            List<WriteOffBaseInfo> baseInfoList = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .eq(WriteOffBaseInfo::getOrganName, writeOffFreezeExcel.getOrganName())
                    .eq(WriteOffBaseInfo::getInvoiceAmount, writeOffFreezeExcel.getInvoiceAmount())
                    .eq(WriteOffBaseInfo::getWriteOffMonth, writeOffFreezeExcel.getWriteOffMonth())
            );

            int a = i+1 ;

            if (baseInfoList != null && baseInfoList.size() > 0) {
                for(WriteOffBaseInfo baseInfo : baseInfoList) {
                    if (ObjectUtils.isNotEmpty(baseInfo)){
                        if (frozenStatus.equals(baseInfo.getFrozenStatus().key())) {
                            throw new AfsBaseException("核销项编号：" + baseInfo.getApplyNo() + "冻结/解冻状态异常,或导入数据重复，请检查");
                        }
                        if("1".equals(frozenStatus)) {
                            baseInfo.setFrozenStatus(FrozenStatusEnum.FROZEN);
                        }else if ("0".equals(frozenStatus)){
                            baseInfo.setFrozenStatus(FrozenStatusEnum.NORMAL);
                        }
                        log.info("importAutoFrozen--handleExcelImport,冻结状态变更核销项编号为{}",baseInfo.getApplyNo());
                    }else {
                        log.info("handleExcelImport，经销商名称为: {}，应开票金额为:{},核销期数为: {}" + "服务费核销项为空",
                                writeOffFreezeExcel.getOrganName(), writeOffFreezeExcel.getInvoiceAmount(), writeOffFreezeExcel.getWriteOffMonth());
                        throw new AfsBaseException("第" + a + "行的服务费核销项为空!");
                    }
                }
                this.updateBatchById(baseInfoList);
            } else {
                log.info("WriteOffBaseInfoServiceImpl--handleExcelImport，经销商名称为: {},应开票金额为:{},核销期数为: {},在核销项中未找到",
                        writeOffFreezeExcel.getOrganName(), writeOffFreezeExcel.getInvoiceAmount(), writeOffFreezeExcel.getWriteOffMonth());
                throw new AfsBaseException("第" + a + "行，未找到对应的服务费核销项，请检查导入信息!");
            }
        }
    }

    private WriteOffFreezeExcel extractRow(Row row, int i) throws Exception{
        WriteOffFreezeExcel writeOffFreezeExcel = new WriteOffFreezeExcel();
        int j= 0;
        /** 经销商名称 **/
        String organName;
        Cell cell = row.getCell(j);
        if(EmptyUtils.isEmpty(cell)) {
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列格为空，请检查");
        }
        organName = getValueByType(cell).toString();
        if (EmptyUtils.isEmpty(organName)){
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列为空，请检查");
        }
        writeOffFreezeExcel.setOrganName(organName);
        j++;

        /**  应开票金额 **/
        BigDecimal invoiceAmount;
        Cell cell1 = row.getCell(j);
        if(EmptyUtils.isEmpty(getValueByType(cell1))) {
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列格为空，请检查");
        }
        invoiceAmount = new BigDecimal(getValueByType(cell1).toString());
        if (EmptyUtils.isEmpty(invoiceAmount)){
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列为空，请检查");
        }
        writeOffFreezeExcel.setInvoiceAmount(invoiceAmount);
        j++;

        /**  核销期数  **/
        String writeOffMonth;
        Cell cell2 = row.getCell(j);
        if(EmptyUtils.isEmpty(cell2)) {
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列格为空，请检查");
        }
        writeOffMonth = getValueByType(cell2).toString();
        if (EmptyUtils.isEmpty(writeOffMonth)){
            throw new AfsBaseException("第"+(i+1)+"行第"+(j+1)+"列为空，请检查");
        }
        writeOffFreezeExcel.setWriteOffMonth(writeOffMonth);
        j++;

        return writeOffFreezeExcel;
    }

    private static Object getValueByType(Cell cell) {
        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            default:
                return null;
        }
    }

    @Override
    public IResponse batchUpdateFrozen(WriteOffBatchFrozenVO frozenVO) {
        Assert.isTrue(StrUtil.isNotEmpty(frozenVO.getApplyNo()), "核销项编号为空");
        Assert.isTrue(List.of(FrozenStatusEnum.SUSPEND_PAYMENT, FrozenStatusEnum.NORMAL)
                .contains(frozenVO.getFrozenStatus()), "冻结状态有误");
        // 获取对账周期
        Assert.isTrue(frozenVO.getOrganId() != null, "渠道信息不可为空！");
        WriteOffAccountCycleDetail cycleDetail = writeOffAccountCycleDetailService.getOne(Wrappers.<WriteOffAccountCycleDetail>lambdaQuery()
            .eq(WriteOffAccountCycleDetail::getChannelCode, frozenVO.getOrganId()));
        Assert.isTrue(cycleDetail != null, "该经销商对账周期不存在");
        String accountCycle = cycleDetail.getType();

        WriteOffBaseInfo baseInfo = this.getOne(
            Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getApplyNo, frozenVO.getApplyNo()));
        Assert.isTrue(baseInfo.getFrozenStatus() == frozenVO.getFrozenStatus(), "数据已更新，刷新一下再次尝试");
        if (ObjectUtil.isNotEmpty(baseInfo)) {
            Set<Long> idList = new HashSet<>();
            if (AccountCycleEnum.SEASON.getCode().equals(accountCycle)) {
                List<String> quarterMonths = getQuarterMonths(baseInfo.getWriteOffMonth());
                List<WriteOffBaseInfo> writeOffBaseInfos = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .in(WriteOffBaseInfo::getWriteOffMonth, quarterMonths)
                    .eq(WriteOffBaseInfo::getOrganId, baseInfo.getOrganId()));

                // 当前月份大于核销项季度尾月 数据完整
                LocalDate currentDate = LocalDate.now();
                int monthValue = currentDate.getMonthValue();
                Assert.isTrue(monthValue > Integer.parseInt(quarterMonths.get(2).split("-")[1]),
                    "季度数据不完整,不可以专项管理");
                for (WriteOffBaseInfo writeOffBaseInfo : writeOffBaseInfos) {
                    idList.add(writeOffBaseInfo.getId());
                }
            } else {
                idList.add(baseInfo.getId());
            }
            this.lambdaUpdate()
                .set(WriteOffBaseInfo::getFrozenStatus, frozenVO.getFrozenStatus() == FrozenStatusEnum.NORMAL
                    ? FrozenStatusEnum.SUSPEND_PAYMENT
                    : FrozenStatusEnum.NORMAL)
                .in(WriteOffBaseInfo::getId, idList).update();
        } else {
            throw new AfsBaseException("核销项信息为空");
        }
        return IResponse.success("操作成功");
    }

    public static List<String> getQuarterMonths(String date) {
        List<String> months = new ArrayList<>();
        // 解析输入的年月
        String[] parts = date.split("-");
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);
        // 根据月份确定季度的起始月份
        int startMonth;
        if (month >= 1 && month <= 3) {
            startMonth = 1;
        } else if (month >= 4 && month <= 6) {
            startMonth = 4;
        } else if (month >= 7 && month <= 9) {
            startMonth = 7;
        } else {
            startMonth = 10;
        }
        // 添加季度内的所有月份
        for (int i = 0; i < 3; i++) {
            int currentMonth = startMonth + i;
            months.add(String.format("%d-%02d", year, currentMonth));
        }
        return months;
    }

    @Override
    public IResponse recallBatch(List<String> draftBatchCodeList) {
        Assert.isTrue(CollectionUtil.isNotEmpty(draftBatchCodeList), "流程编号不能为空");
        List<WorkflowTaskInfo> list = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getStatus, FlowStatusEnum.ACTIVE.getCode())
                .in(WorkflowTaskInfo::getBusinessNo, draftBatchCodeList));
        Assert.isTrue(list.size() == draftBatchCodeList.size(), "存在无法撤回的流程，请选择状态正常的流程");
        for (WorkflowTaskInfo workflowTaskInfo : list) {
            IResponse<Boolean> response = workflowHelper.cancelFlow(workflowTaskInfo.getProcessInstanceId());
            log.info("发起草稿预审批工作流撤回参数{}", response);
            if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                throw new AfsBaseException("发起草稿预审批撤回工作流失败!");
            }
            //更改状态为结束
            workflowTaskInfo.setStatus(FlowStatusEnum.END.getCode());
            //存撤回记录日志
            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(workflowTaskInfo.getBusinessNo());
            record.setDisposeStaff(SecurityUtils.getUser().getUserRealName());
            record.setDisposeNodeName(workflowTaskInfo.getTaskNodeName());
            record.setApproveSuggestName("撤回");
            record.setApproveEndTime(new Date());
            record.setApproveRemark("草稿预审批撤回");
            caseApproveRecordService.save(record);
        }
        workflowTaskInfoService.updateBatchById(list);
        //更改状态为已撤回
        List<WriteOffBaseInfo> writeOffBaseInfoList = this.list(Wrappers.<WriteOffBaseInfo>query().lambda()
                .in(WriteOffBaseInfo::getDraftBatchCode, draftBatchCodeList));
        for (WriteOffBaseInfo writeOffBaseInfo : writeOffBaseInfoList) {
            writeOffBaseInfo.setWriteOffStatus(WriteOffStatusEnum.DELETED);
        }
        this.updateBatchById(writeOffBaseInfoList);

        return IResponse.success("撤回成功");
    }

    @Override
    public IResponse offSetChange(WriteOffAllStatusCondition condition) {
        return IResponse.success("冲抵成功");
    }

    @Override
    public IResponse submitApprove(WriteOffAllStatusCondition condition) {
        log.info("接收到草稿审批数据{}",condition);
        List<String> applyNos = condition.getApplyNos();
        Assert.isTrue(CollectionUtil.isNotEmpty(applyNos), "请至少选择一条数据");
        List<WriteOffStatusEnum> statusEnumList = new ArrayList<>();
        statusEnumList.add(WriteOffStatusEnum.DRAFT_APPROVAL);
        statusEnumList.add(WriteOffStatusEnum.DELETED);
        List<WriteOffBaseInfo> baseInfo = this.list(Wrappers.<WriteOffBaseInfo>query().lambda()
                .in(WriteOffBaseInfo::getApplyNo, applyNos)
                .in(WriteOffBaseInfo::getWriteOffStatus, statusEnumList)
        );
        Assert.isTrue(applyNos.size() == baseInfo.size(), "只能发起草稿状态和撤回状态的核销项");
        String writeOffMonth = baseInfo.get(0).getWriteOffMonth();
        for (WriteOffBaseInfo info : baseInfo) {
            if (!writeOffMonth.equals(info.getWriteOffMonth())) {
                throw new AfsBaseException("只能发起单月的核销项");
            }
        }
        //生成批次号
        String uuid= UUID.randomUUID().toString().replace("-","");
        if (ObjectUtils.isNotEmpty(baseInfo) && baseInfo.size()==applyNos.size()) {
            if (WriteOffStatusEnum.BE_RELEASED.equals(condition.getWriteOffStatus())) {
                baseInfo.forEach(i->{
                    i.setWriteOffStatus(WriteOffStatusEnum.DRAFT_UNDER_APPROVAL);
                    i.setRemark(condition.getRemark());
                    i.setWriteOffExplain(condition.getWriteOffExplain());
                    i.setDraftBatchCode(uuid);
                });
                //发起审批流程
                StartFlowRequestBo startFlowRequestBo = new StartFlowRequestBo();
                startFlowRequestBo.setBusinessNo(uuid);
                //这两个ID为指定工作流的id，后面可以写在配置文件里面。
                startFlowRequestBo.setPackageId(flowConfigProperties.getWriteOffDraftApprovalPackageId());
                startFlowRequestBo.setTemplateId(flowConfigProperties.getWriteOffDraftApprovalTemplateId());
                startFlowRequestBo.setSubject("服务费草稿批次号:" +uuid);
                // 植入业务参数
                if (startFlowRequestBo.getParams() == null) {
                    startFlowRequestBo.setParams(new JSONObject());
                }
                startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_NO, uuid);
                startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_USER, SecurityUtils.getUser().getUserRealName());
                startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_OPINION, "创建");
                startFlowRequestBo.getParams().put(FlowConstant.TASK_NODE_NAME, "流程发起");
                startFlowRequestBo.getParams().put(FlowConstant.FLOW_START_USER, SecurityUtils.getUser().getUsername());
                // 设置工作流需要的参数
                final IResponse response = workflowHelper.startFlow(startFlowRequestBo, UseSceneEnum.INITIATE_WRITE);
                log.info("发起草稿预审批工作流返回参数{}",response);
                if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                    throw new AfsBaseException("发起草稿预审批工作流失败!");
                }
            } else {
                baseInfo.forEach(i->{
                    i.setWriteOffStatus(WriteOffStatusEnum.DELETED);
                });
            }
            this.updateBatchById(baseInfo);
        } else {
            throw new AfsBaseException("当前状态无法提交");
        }
        return IResponse.success("提交成功");
    }

    @Override
    public IResponse deleteWriteOff(WriteOffAllStatusCondition condition) {
        List<String> applyNos = condition.getApplyNos();
        Assert.isTrue(CollectionUtil.isNotEmpty(applyNos), "请至少选择一条数据");
        List<WriteOffStatusEnum> statusEnumList = new ArrayList<>();
        statusEnumList.add(WriteOffStatusEnum.DRAFT_APPROVAL);
        statusEnumList.add(WriteOffStatusEnum.DELETED);
        List<WriteOffBaseInfo> baseInfoList = this.list(Wrappers.<WriteOffBaseInfo>query().lambda()
                .in(WriteOffBaseInfo::getApplyNo, applyNos)
                .in(WriteOffBaseInfo::getWriteOffStatus, statusEnumList)
        );
        Assert.isTrue(applyNos.size() == baseInfoList.size(), "只能删除草稿状态和撤回状态的核销项");
        //删除核销项信息
        this.removeBatchByIds(baseInfoList);
        //删除合同信息
        List<WriteOffContractDetailManage> detailManageList = writeOffContractDetailManageService.list(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .select(WriteOffContractDetailManage::getId, WriteOffContractDetailManage::getContractNo)
                .in(WriteOffContractDetailManage::getBaseInfoApply, applyNos));
        List<String> contractNos = detailManageList.stream().map(WriteOffContractDetailManage::getContractNo).toList();
        writeOffContractDetailManageService.removeBatchByIds(detailManageList);
        //删除合同扣减信息
        List<WriteOffDeductDetail> deductDetailList = writeOffDeductDetailService.list(Wrappers.<WriteOffDeductDetail>lambdaQuery()
                .select(WriteOffDeductDetail::getId, WriteOffDeductDetail::getContractNo, WriteOffDeductDetail::getContractStatus)
                .in(WriteOffDeductDetail::getBaseInfoApply, applyNos));
        writeOffDeductDetailService.removeBatchByIds(deductDetailList);
        //修改合同状态
        caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                .in(CaseContractInfo::getContractNo, contractNos)
                .set(CaseContractInfo::getServiceFeeCheck, "0"));
        //还原提前结清标识
        List<String> settleContractNos = deductDetailList.stream().filter(k -> k.getContractStatus() == ContractStatusEnum.advanceSettle).map(WriteOffDeductDetail::getContractNo).toList();
        Date date = new Date();
        if (settleContractNos.size() > 0) {
            writeOffContractDetailManageService.update(Wrappers.<WriteOffContractDetailManage>lambdaUpdate()
                    .in(WriteOffContractDetailManage::getContractNo, settleContractNos)
                    .set(WriteOffContractDetailManage::getAdvanceSettleFlag, Boolean.FALSE)
                    .set(WriteOffContractDetailManage::getAdvanceSettleMonth, null)
                    .set(WriteOffContractDetailManage::getAdvanceSettleAmount, null)
                    .set(WriteOffContractDetailManage::getDeductRate, null)
                    .set(WriteOffContractDetailManage::getUpdateTime, date));
        }
        return IResponse.success("删除成功");
    }

    @Override
    public IResponse<IPage<WriteOffBaseInfoVo>> getWriteOffApproveList(QueryCondition<WriteOffBaseCondition> condition) {
        condition.getCondition().setWriteOffStatus(WriteOffStatusEnum.BE_APPROVAL);
        IPage<WriteOffBaseInfoVo> writeOffBaseInfoVoPage = writeOffBaseInfoMapper.getWriteOffApproveList(new Page<>(condition.getPageNumber(), condition.getPageSize()), condition.getCondition());
        return IResponse.success(writeOffBaseInfoVoPage);
    }

    @Override
    public IResponse writeOffRelease(WriteOffBaseCondition condition) {
        WriteOffBaseInfo baseInfo = this.getOne(Wrappers.<WriteOffBaseInfo>query().lambda()
                .eq(WriteOffBaseInfo::getApplyNo, condition.getApplyNo())
                .in(WriteOffBaseInfo::getWriteOffStatus, WriteOffStatusEnum.BE_RELEASED, WriteOffStatusEnum.COST_CONTROL_RELEASED,WriteOffStatusEnum.FINANCE_CONTROL_RELEASED)
        );
        if (ObjectUtils.isEmpty(baseInfo)) {
            throw new AfsBaseException("当前状态不支持发布,请检查当前状态");
        }
        baseInfo.setWriteOffStatus(WriteOffStatusEnum.BE_WRITE_OFF);
        WriteOffBaseInfoDto dto = new WriteOffBaseInfoDto();
        BeanUtils.copyProperties(baseInfo, dto, "id");
        dto.setWriteOffStatus(baseInfo.getWriteOffStatus());
        dto.setFrozenStatus(null);
        dto.setTime(new Date());
        baseInfo.setReleaseTime(dto.getTime());
        this.updateById(baseInfo);
        WriteOffBaseInfoBatchDto writeOffBaseInfoBatchDto = new WriteOffBaseInfoBatchDto();
        AfsTransEntity<WriteOffBaseInfoBatchDto> afsTransEntity = new AfsTransEntity();
        writeOffBaseInfoBatchDto.setWriteOffBaseInfoDtos(Arrays.asList(dto));
        afsTransEntity.setData(writeOffBaseInfoBatchDto);
        afsTransEntity.setTransCode(MqTransCode.AFS_WRITE_OFF_CASE_APPLY_NOTICE);
        writeOffSender.sendWriteOffToApply(afsTransEntity);
        return IResponse.success("发布成功");
    }

    @Override
    public IResponse getWriteOffApplyNo() {
        return IResponse.success(this.generateApply());
    }

    @Override
    public IResponse writeOffApproveList(QueryCondition<WriteOffBaseCondition> condition) {
        String applyNo = condition.getCondition().getApplyNo();
        WriteOffBaseInvoiceRel invoiceRel = baseInvoiceRelMapper.selectOne(Wrappers.<WriteOffBaseInvoiceRel>query().lambda().eq(WriteOffBaseInvoiceRel::getApplyNo, applyNo));
        List<WriteOffBaseInvoiceRel> relList = baseInvoiceRelMapper.selectList(Wrappers.<WriteOffBaseInvoiceRel>query().lambda().eq(WriteOffBaseInvoiceRel::getBusinessNo, invoiceRel.getBusinessNo()));
        List<String> applyNoList = relList.stream().map(WriteOffBaseInvoiceRel::getApplyNo).collect(Collectors.toList());
        IPage<WriteOffBaseInfoApproveVo> page = writeOffBaseInfoMapper.getApproveInfo(new Page<>(condition.getPageNumber(), condition.getPageSize()), applyNoList);
        return IResponse.success(page);
    }

    /**
     * 获取大区列表
     *
     * @return
     */
    @Override
    public IResponse<List<ChannelBaseInfoApiDTO>> getRegionList() {
        return channelApiService.getChannelRegionlist();
    }

    /**
     * 根据核销项编号查询核销项信息
     * @param condition
     * @return
     */
    @Override
    public IResponse selectByApplyNo(QueryCondition<WriteOffBaseCondition> condition) {
        String applyNo = condition.getCondition().getApplyNo();
        Page<WriteOffBaseInfo> writeOffBaseInfoPage = writeOffBaseInfoMapper.selectPage(new Page<>(condition.getPageNumber(), condition.getPageSize()), Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getCaseNo, applyNo));
        return IResponse.success(writeOffBaseInfoPage);
    }

    public String generateApply() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        Long seqNo = afsSequenceGenerator.genNext(CaseConstants.WRITE_OFF_APPLY_NO, dateFormat.format(Calendar.getInstance().getTime()));
        String seqStr = StringUtils.leftPad(seqNo.toString(), 5, "0");
        String flawNo = CaseConstants.WRITE_OFF_APPLY_NO + "-"
                + dateFormat.format(Calendar.getInstance().getTime()) + "-" + seqStr;
        return flawNo;
    }

    /**
     * 获取进件请求头
     *
     * @return
     */
    public Map makeApplyHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }


    /**
     * 宝德汇总信息查询
     * @param condition 入参.
     * @return 汇总信息
     */
    @Override
    public IResponse getBdWriteOffList(Request<BdRequestDataDTO> condition) {
        log.info("WriteOffBaseInfoServiceImpl----getBDWriteOffList,宝德汇总信息查询入参：{}", JSONUtil.toJsonStr(condition));
        List<WriteOffBaseInfo> writeOffBaseInfos = this.baseMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getOrganId, condition.getChannelCode())
                .eq(ObjectUtils.isNotNull(condition.getReqData().getWriteOffStatus()), WriteOffBaseInfo::getWriteOffStatus, condition.getReqData().getWriteOffStatus())
                .eq(ObjectUtils.isNotNull(condition.getReqData().getWriteOffMonth()), WriteOffBaseInfo::getWriteOffMonth, condition.getReqData().getWriteOffMonth())
        );
        List<WriteOffBaseExcelVO> responseResult = writeOffBaseInfos.stream().map(item -> {
            WriteOffBaseExcelVO vo = new WriteOffBaseExcelVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return IResponse.success(responseResult);
    }

    @Override
    public IResponse writeOffDraftApprovalList(QueryCondition<LaunchWorkVo> vo) {
        Page page = new Page(vo.getPageNumber(), vo.getPageSize());
        LaunchWorkVo condition = vo.getCondition();
        condition.setAssign(SecurityUtils.getUsername());
        condition.setFlowPackageId(flowConfigProperties.getWriteOffDraftApprovalPackageId());
        condition.setFlowTemplateId(flowConfigProperties.getWriteOffDraftApprovalTemplateId());
        return  IResponse.success( this.writeOffBaseInfoMapper.writeOffDraftApprovalList(page,condition));
    }

    /**
     * 服务费总金额
     */
    @Override
    public IResponse<ServiceFeeTotalVo> serviceFeeTotal() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        //上月的服务费总额
        Calendar last = Calendar.getInstance();
        last.add(Calendar.MONTH, -1);
        String time = dateFormat.format(last.getTime());

        //根据当前年月查询所需数据
        List<WriteOffBaseInfo> writeOffBaseInfo = writeOffBaseInfoMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getWriteOffMonth, time));

        //服务费金额
        BigDecimal systemName = BigDecimal.ZERO;

        ServiceFeeTotalVo serviceFeeTotalVo = new ServiceFeeTotalVo();
        //求服务费总金额
        for (WriteOffBaseInfo offBaseInfo : writeOffBaseInfo) {
            systemName = systemName.add(offBaseInfo.getInvoiceAmount());
        }
        serviceFeeTotalVo.setSystemName(systemName);
        return IResponse.success(serviceFeeTotalVo);
    }

    /**
     * 判断是否有当前经销商是否有正在提取的单子
     * @param distributorCode
     * @return
     */
    @Override
    public IResponse extractingOrNot(String distributorCode) {
        log.info("接收到经销商编号 {}",distributorCode);
        List<WriteOffBaseInvoiceRel> writeOffBaseInfos = baseInvoiceRelMapper.selectList(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                .eq(WriteOffBaseInvoiceRel::getDealerCode, distributorCode)
                .in(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_1.code,ChannelServiceFeeEnum.STATUS_2.code));
        log.info("通过经销商查询到信息 {}",writeOffBaseInfos);
        if(writeOffBaseInfos.size()>0){
            //有正在提取的数据
            return IResponse.success(true);
        }
        return IResponse.success(false);
    }

    /**
     * 计算服务费总额
     * @param businessNo
     * @return
     */
    @Override
    public IResponse getWriteOffTotalInfo(String businessNo) {
        List<WriteOffBaseInfo> list = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .select(WriteOffBaseInfo::getInvoiceAmount, WriteOffBaseInfo::getExcludeTaxAmount, WriteOffBaseInfo::getTaxAmount)
                .eq(WriteOffBaseInfo::getDraftBatchCode, businessNo));
        //服务费金额
        Map<String, BigDecimal> resultMap = new HashMap<>();
        BigDecimal baseAmtNum = list.stream().map(WriteOffBaseInfo::getInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal baseExcludeNum = list.stream().map(WriteOffBaseInfo::getExcludeTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal baseTaxNum = list.stream().map(WriteOffBaseInfo::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultMap.put("baseAmtNum", baseAmtNum);
        resultMap.put("baseExcludeNum", baseExcludeNum);
        resultMap.put("baseTaxNum", baseTaxNum);
        return IResponse.success(resultMap);
    }

    /**
     * 核销项按条件导出
     * @param response
     * @param condition
     */
    @Override
    public void exportByCondition(HttpServletResponse response, WriteOffBaseCondition condition) {
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        List<WriteOffBaseInfo> list = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffBaseInfo::getOrganId, resultDto.getHitCodeList())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffBaseInfo::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getOrganId()), WriteOffBaseInfo::getOrganId, condition.getOrganId())
                .like(StrUtil.isNotBlank(condition.getOrganName()), WriteOffBaseInfo::getOrganName, condition.getOrganName())
                .eq(condition.getWriteOffStatus() != null, WriteOffBaseInfo::getWriteOffStatus, condition.getWriteOffStatus())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffBaseInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .eq(StrUtil.isNotBlank(condition.getDraftBatchCode()), WriteOffBaseInfo::getDraftBatchCode, condition.getDraftBatchCode())
                .eq(StrUtil.isNotBlank(condition.getFrozenStatus()), WriteOffBaseInfo::getFrozenStatus, condition.getFrozenStatus())
                .eq(StrUtil.isNotBlank(condition.getWriteOffType()), WriteOffBaseInfo::getWriteOffType, condition.getWriteOffType())
                .orderByDesc(WriteOffBaseInfo::getCreateTime));
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<WriteOffBaseExcelVO> feesVOList = list.stream().map(base -> {
            WriteOffBaseExcelVO feesVO = new WriteOffBaseExcelVO();
            BeanUtil.copyProperties(base, feesVO);
            if (WriteOffStatusEnum.DELETED == base.getWriteOffStatus()) {
                feesVO.setStatusName("已撤回");
            } else if (WriteOffStatusEnum.DRAFT_APPROVAL == base.getWriteOffStatus()) {
                feesVO.setStatusName("草稿项审批");
            } else if (WriteOffStatusEnum.DRAFT_UNDER_APPROVAL == base.getWriteOffStatus()) {
                feesVO.setStatusName("草稿项审批中");
            } else {
                feesVO.setStatusName(AfsEnumUtil.desc(base.getWriteOffStatus()));
            }
            if (FrozenStatusEnum.FROZEN == base.getFrozenStatus()) {
                feesVO.setFreezeStatus("已冻结");
            } else if (FrozenStatusEnum.NORMAL == base.getFrozenStatus()) {
                feesVO.setFreezeStatus("正常");
            }else {
                if (base.getFrozenStatus() != null) {
                    feesVO.setFreezeStatus(AfsEnumUtil.desc(base.getFrozenStatus()));
                }
            }
            if (base.getReceiveElectronicFlag() != null && !base.getReceiveElectronicFlag().equals("")){
                feesVO.setReceiveElectronicFlag("1".equals(base.getReceiveElectronicFlag()) ? "电子发票" : "纸质发票");
            }
            if (base.getInvoiceUploadDeadline() != null) {
                feesVO.setInvoiceUploadDeadline(dateFormat.format(base.getInvoiceUploadDeadline()));
            }
            if (base.getReturningCompanyDeadline() != null) {
                feesVO.setReturningCompanyDeadline(dateFormat.format(base.getReturningCompanyDeadline()));
            }
            if (base.getTimeSyncFlag() != null) {
                feesVO.setTimeSyncFlag("1".equals(base.getTimeSyncFlag()) ? "已同步" : "未同步");
            }
            feesVO.setWriteOffType(WriteOffTypeEnum.createTypeEnum(base.getWriteOffType()).getDesc());
            return feesVO;
        }).collect(Collectors.toList());
        this.commExport(response, feesVOList,"核销项明细", WriteOffBaseExcelVO.class,null);
    }

    /**
     * 服务费发票工作流改派临时定时任务
     * @param param
     */
    @Override
    public void tempWriteOffTransferJob(String param) {
        String packageId = flowConfigProperties.getInitiateWriteOffPackageId();
        String templateId = flowConfigProperties.getInitiateWriteOffTemplateId();
        if (StrUtil.isNotBlank(param) && "province".equals(param)) {
            //郭丽丽299422账号下：按省份划转至下方用户。
            List<WorkflowTaskInfo> taskInfoList = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getFlowPackageId, packageId)
                    .eq(WorkflowTaskInfo::getFlowTemplateId, templateId)
                    .eq(WorkflowTaskInfo::getAssign, "299422")
                    .eq(WorkflowTaskInfo::getStatus, "active"));
            //('浙江省','陕西省','甘肃省','宁夏回族自治区','新疆维吾尔自治区','青海省','江苏省','上海市');
            Set<String> liSet = Set.of("310000", "320000", "330000", "610000", "620000", "630000", "640000", "650000");
            //('山西省','安徽省','北京市','天津市','河北省','山东省','辽宁省','黑龙江省','吉林省');
            Set<String> weiSet = Set.of("110000", "120000", "130000", "140000", "210000", "220000", "230000", "340000", "370000");
            //('海南省','广东省','河南省','内蒙古自治区')
            Set<String> jieSet = Set.of("150000", "410000", "440000", "460000");
            //('江西省','福建省','重庆市','湖南省','湖北省','四川省','云南省','贵州省','广西壮族自治区','西藏自治区')
            Set<String> zhouSet = Set.of("350000", "360000", "420000", "430000", "450000", "500000", "510000", "520000", "530000", "540000");
            log.info("李瀛萍5264864包含的省份：{}", Arrays.toString(liSet.toArray()));
            log.info("魏树昊5889581-改为-赵瑞雪2577639包含的省份：{}", Arrays.toString(weiSet.toArray()));
            log.info("李洁5199596包含的省份：{}", Arrays.toString(jieSet.toArray()));
            log.info("周文龙5234166-改为-杨庆3237368包含的省份：{}", Arrays.toString(zhouSet.toArray()));
            for (WorkflowTaskInfo taskInfo : taskInfoList) {
                List<WriteOffBaseInfo> baseInfoList = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getCaseNo, taskInfo.getBusinessNo()));
                Assert.isTrue(baseInfoList.size() > 0, "流程对应核销项为空，业务编号：" + taskInfo.getBusinessNo());
                WriteOffContractDetailManage detailManage = writeOffContractDetailManageService.getOne(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                        .select(WriteOffContractDetailManage::getChannelProvince)
                        .eq(WriteOffContractDetailManage::getBaseInfoApply, baseInfoList.get(0).getApplyNo())
                        .last("limit 1"));
                if (liSet.contains(detailManage.getChannelProvince())) {
                    TaskTransferParam taskTransferParam = new TaskTransferParam();
                    taskTransferParam.setTaskId(taskInfo.getTaskId());
                    taskTransferParam.setTargetUserId("5264864");
                    taskTransferParam.setUserRealNameOld(taskInfo.getAssign());
                    taskTransferParam.setUserRealNameNew("5264864");
                    taskTransferParam.setTaskNodeName(taskInfo.getTaskNodeName());
                    taskTransferParam.setRemark("郭丽丽299422账号下,任务划转至李瀛萍5264864");
                    IResponse response = workflowWrapperService.transfer(taskTransferParam, true, UseSceneEnum.INITIATE_WRITE);
                    if(response == null || !"0000".equals(response.getCode())){
                        log.error("改派失败：{}，业务编号：{}", response, taskInfo.getBusinessNo());
                        throw new RuntimeException("改派失败！");
                    }
                } else if (weiSet.contains(detailManage.getChannelProvince())) {
                    TaskTransferParam taskTransferParam = new TaskTransferParam();
                    taskTransferParam.setTaskId(taskInfo.getTaskId());
                    taskTransferParam.setTargetUserId("2577639");
                    taskTransferParam.setUserRealNameOld(taskInfo.getAssign());
                    taskTransferParam.setUserRealNameNew("2577639");
                    taskTransferParam.setTaskNodeName(taskInfo.getTaskNodeName());
                    taskTransferParam.setRemark("郭丽丽299422账号下,任务划转至赵瑞雪2577639");
                    IResponse response = workflowWrapperService.transfer(taskTransferParam, true, UseSceneEnum.INITIATE_WRITE);
                    if (response == null || !"0000".equals(response.getCode())) {
                        log.error("改派失败：{}，业务编号：{}", response, taskInfo.getBusinessNo());
                        throw new RuntimeException("改派失败！");
                    }
                } else if (jieSet.contains(detailManage.getChannelProvince())) {
                    TaskTransferParam taskTransferParam = new TaskTransferParam();
                    taskTransferParam.setTaskId(taskInfo.getTaskId());
                    taskTransferParam.setTargetUserId("5199596");
                    taskTransferParam.setUserRealNameOld(taskInfo.getAssign());
                    taskTransferParam.setUserRealNameNew("5199596");
                    taskTransferParam.setTaskNodeName(taskInfo.getTaskNodeName());
                    taskTransferParam.setRemark("郭丽丽299422账号下,任务划转至李洁5199596");
                    IResponse response = workflowWrapperService.transfer(taskTransferParam, true, UseSceneEnum.INITIATE_WRITE);
                    if (response == null || !"0000".equals(response.getCode())) {
                        log.error("改派失败：{}，业务编号：{}", response, taskInfo.getBusinessNo());
                        throw new RuntimeException("改派失败！");
                    }
                } else if (zhouSet.contains(detailManage.getChannelProvince())) {
                    TaskTransferParam taskTransferParam = new TaskTransferParam();
                    taskTransferParam.setTaskId(taskInfo.getTaskId());
                    taskTransferParam.setTargetUserId("3237368");
                    taskTransferParam.setUserRealNameOld(taskInfo.getAssign());
                    taskTransferParam.setUserRealNameNew("3237368");
                    taskTransferParam.setTaskNodeName(taskInfo.getTaskNodeName());
                    taskTransferParam.setRemark("郭丽丽299422账号下,任务划转至杨庆3237368");
                    IResponse response = workflowWrapperService.transfer(taskTransferParam, true, UseSceneEnum.INITIATE_WRITE);
                    if (response == null || !"0000".equals(response.getCode())) {
                        log.error("改派失败：{}，业务编号：{}", response, taskInfo.getBusinessNo());
                        throw new RuntimeException("改派失败！");
                    }
                } else {
                    log.warn("省份没匹配到人员，业务编号：{}，省份：{}", taskInfo.getBusinessNo(), detailManage.getChannelProvince());
                }
            }
        } else {
            //邓娟5199591账号下,全部任务划转至郭丽丽299422
            List<WorkflowTaskInfo> taskInfoList = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                    .eq(WorkflowTaskInfo::getFlowPackageId, packageId)
                    .eq(WorkflowTaskInfo::getFlowTemplateId, templateId)
                    .eq(WorkflowTaskInfo::getAssign, "5199591")
                    .eq(WorkflowTaskInfo::getStatus, "active"));
            for (WorkflowTaskInfo taskInfo : taskInfoList) {
                TaskTransferParam taskTransferParam = new TaskTransferParam();
                taskTransferParam.setTaskId(taskInfo.getTaskId());
                taskTransferParam.setTargetUserId("299422");
                taskTransferParam.setUserRealNameOld(taskInfo.getAssign());
                taskTransferParam.setUserRealNameNew("299422");
                taskTransferParam.setTaskNodeName(taskInfo.getTaskNodeName());
                taskTransferParam.setRemark("邓娟5199591账号下,任务划转至郭丽丽299422");
                IResponse response = workflowWrapperService.transfer(taskTransferParam, true, UseSceneEnum.INITIATE_WRITE);
                if(response == null || !"0000".equals(response.getCode())){
                    log.error("改派失败：{}，业务编号：{}", response, taskInfo.getBusinessNo());
                    throw new RuntimeException("改派失败！");
                }
            }
        }
    }

    @Override
    public IPage<ReturnDataWorkInfoResp> pageReturnData(Page page, ReturnDataVO condition) {
        //查询当前登录人的待办任务
        IPage<ReturnDataWorkInfoResp> list = baseMapper.pageReturnData(page, condition, SecurityUtils.getUsername());
        if (CollUtil.isEmpty(list.getRecords())) {
            return list;
        }
        List<String> noList = list.getRecords().stream().map(s -> s.getBusinessNo().substring(6)).toList();
        List<WriteOffBaseInfo> baseInfos = this.lambdaQuery()
            .select(WriteOffBaseInfo::getApplyNo,
                WriteOffBaseInfo::getWriteOffMonth,
                WriteOffBaseInfo::getOrganName,
                WriteOffBaseInfo::getReleaseTime,
                WriteOffBaseInfo::getInvoiceAmount,
                WriteOffBaseInfo::getInvoiceUploadDeadline,
                WriteOffBaseInfo::getReturningCompanyDeadline)
            .in(WriteOffBaseInfo::getApplyNo, noList)
            .list();

        Map<String, WriteOffBaseInfo> infoMap = baseInfos.stream()
            .collect(Collectors.toMap(WriteOffBaseInfo::getApplyNo, Function.identity()));

        list.getRecords().forEach(task -> {
            WriteOffBaseInfo writeOffBaseInfo = infoMap.get(task.getBusinessNo().substring(6));
            task.setWriteOffMonth(writeOffBaseInfo.getWriteOffMonth());
            task.setOrganName(writeOffBaseInfo.getOrganName());
            task.setInvoiceAmount(writeOffBaseInfo.getInvoiceAmount().toString());
            task.setReleaseTime(writeOffBaseInfo.getReleaseTime());
            task.setInvoiceUploadDeadline(writeOffBaseInfo.getInvoiceUploadDeadline());
            task.setReturningCompanyDeadline(writeOffBaseInfo.getReturningCompanyDeadline());
        });
        return list;
    }

    @Override
    public void extensionApplication(WriteOffExtensionVO extensionVO) {
        log.info("经销商发票延期申请信息{}", JSONObject.toJSONString(extensionVO));
        // 校验核销项信息
        List<WriteOffBaseInfo> writeOffBaseInfos = this.lambdaQuery()
            .in(WriteOffBaseInfo::getApplyNo, extensionVO.getAppNos())
            .eq(WriteOffBaseInfo::getOrganId, extensionVO.getOrganId())
            .list();
        if(writeOffBaseInfos.size()!=extensionVO.getAppNos().size()){
            throw new AfsBaseException("数据不合法，匹配数量不对应");
        }
        StartFlowRequestBo startFlowBO = new StartFlowRequestBo();
        startFlowBO.setBusinessNo(extensionVO.getProcessNo());
        startFlowBO.setPackageId("write-off-application");
        if (StrUtil.equals("return", extensionVO.getType())) {
            startFlowBO.setTemplateId("write-off-extension");
        } else {
            startFlowBO.setTemplateId("write-off-extension-up");
        }
        String msg = StrUtil.equals("return", extensionVO.getType()) ? "发票回司延期申请" : "发票上传延期申请";
        startFlowBO.setSubject(msg + extensionVO.getProcessNo());
        JSONObject param = new JSONObject();
        // 核销编号
        param.put("app_no", String.join(",", extensionVO.getAppNos()));
        // 周期
        param.put("cycle",extensionVO.getCycle());
        // 经销商编码
        param.put("organ_id",extensionVO.getOrganId());
        // 操作人
        param.put("rel_name",extensionVO.getUserRealName());
        // 申请编号
        param.put("process_no",extensionVO.getProcessNo());
        startFlowBO.setParams(param);
        IResponse response = workflowHelper.startFlow(startFlowBO, StrUtil.equals("return", extensionVO.getType())
            ? UseSceneEnum.WRITE_OFF_EXTENSION
            : UseSceneEnum.WRITE_OFF_EXTENSION_UP);
        log.info("发起回调后返回数据 {}", response);
        if (!CommonConstants.SUCCESS.equals(response.getCode())) {
            throw new AfsBaseException("发起核销流程发起失败!");
        }

        // 保存状态
        boolean upload = StrUtil.equals(extensionVO.getType(), "upload");
        this.lambdaUpdate()
            .set(upload, WriteOffBaseInfo::getUpExtStatus, InvoiceExtenApplicateStatusEnum.ASKING.key())
            .set(upload, WriteOffBaseInfo::getUpExtNo, extensionVO.getProcessNo())
            .set(!upload, WriteOffBaseInfo::getExtStatus, InvoiceExtenApplicateStatusEnum.ASKING.key())
            .set(!upload, WriteOffBaseInfo::getExtNo, extensionVO.getProcessNo())
            .in(WriteOffBaseInfo::getApplyNo, extensionVO.getAppNos())
            .update();
    }


    /**
     * 服务费回司超期自动冻结定时任务
     *
     * @param param
     */
    @Override
    public void writeOffReturnExpireFrozenJob(String param) {
        // 1.0 查询新增的回司过期的核销项
        List<WriteOffBaseInfo> list = this.lambdaQuery()
            .select(WriteOffBaseInfo::getId, WriteOffBaseInfo::getOrganId, WriteOffBaseInfo::getApplyNo)
            .in(WriteOffBaseInfo::getWriteOffStatus,
                List.of(WriteOffStatusEnum.BE_WRITE_OFF, WriteOffStatusEnum.BE_APPROVAL, WriteOffStatusEnum.DELETED))
            .eq(WriteOffBaseInfo::getTimeSyncFlag, StatusEnum.YES.getCode())
            .eq(WriteOffBaseInfo::getReturnOverdueStatus, OverdueStatusEnum.NORMAL.getCode())
            .lt(WriteOffBaseInfo::getReturningCompanyDeadline, new Date())
            .list();

        if(CollUtil.isEmpty(list)){
            log.info("没有回司过期的核销项");
            return;
        }

        // 2.0 按照经销商分组处理 超期冻结 记录引起冻结的核销项
        Map<String, List<WriteOffBaseInfo>> baseMap = list.stream()
            .collect(Collectors.groupingBy(WriteOffBaseInfo::getOrganId));

        baseMap.forEach((organId, baseInfos) -> {

            // 本次引起冻结的核销项
            List<String> appList = baseInfos.stream().map(WriteOffBaseInfo::getApplyNo).distinct().toList();

            List<WriteOffBaseInfo> freezeList = this.lambdaQuery()
                .eq(WriteOffBaseInfo::getOrganId, organId)
                .list();

            HashMap<String, List<String>> info = new HashMap<>();

            for (WriteOffBaseInfo writeOffBaseInfo : freezeList) {
                if (StrUtil.equals(writeOffBaseInfo.getReturnOverdueStatus(), OverdueStatusEnum.FROZEN.getCode())) {
                    // 冻结状态附加冻结核销项
                    List<String> apps = JSONUtil.toList(writeOffBaseInfo.getReturnOverdueAppNo(), String.class);
                    apps.addAll(appList);
                    List<String> dis = apps.stream().distinct().toList();
                    this.lambdaUpdate()
                        .eq(WriteOffBaseInfo::getId, writeOffBaseInfo.getId())
                        .set(WriteOffBaseInfo::getReturnOverdueAppNo, JSONUtil.toJsonStr(dis))
                        .update();
                    info.put(writeOffBaseInfo.getApplyNo(),dis);
                } else {
                    // 非冻结状态设置冻结核销项
                    this.lambdaUpdate()
                        .eq(WriteOffBaseInfo::getId, writeOffBaseInfo.getId())
                        .set(WriteOffBaseInfo::getReturnOverdueStatus, OverdueStatusEnum.FROZEN.getCode())
                        .set(WriteOffBaseInfo::getReturnOverdueAppNo, JSONUtil.toJsonStr(appList))
                        .update();
                    info.put(writeOffBaseInfo.getApplyNo(),appList);
                }

                // 同步进件状态
                IResponse<?> response = applyWriteOffFeign.updateReturnOverdueStatus(new OverdueVO(info));
                Assert.isTrue("0000".equals(response.getCode()),"同步进件回司状态失败");
            }
        });
    }

    /**
     * 服务费超期自动取消定时任务
     * @param param
     */
    @Override
    public void writeOffDueCancelJob(String param) {
        //按照经销商分组
        Map<String, List<WriteOffBaseInfo>> collect = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                        .eq(WriteOffBaseInfo::getWriteOffStatus, WriteOffStatusEnum.BE_WRITE_OFF)
                        .eq(WriteOffBaseInfo::getTimeSyncFlag, StatusEnum.YES.getCode())
                        .isNotNull(WriteOffBaseInfo::getInvoiceUploadDeadline))
                .stream().collect(Collectors.groupingBy(WriteOffBaseInfo::getOrganId));
        Date now = new Date();
        List<String> all = new ArrayList<>();
        for (Map.Entry<String, List<WriteOffBaseInfo>> entry : collect.entrySet()) {
            String channelCode = entry.getKey();
            List<WriteOffBaseInfo> baseInfoList = entry.getValue();
            ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>lambdaQuery()
                    .select(ChannelBaseInfo::getChannelId)
                    .eq(ChannelBaseInfo::getChannelCode, channelCode));
            //按照时间期限分组
            Map<Date, List<WriteOffBaseInfo>> timeMap = baseInfoList.stream().collect(Collectors.groupingBy(WriteOffBaseInfo::getInvoiceUploadDeadline));
            ArrayList<String> appList = new ArrayList<>();
            for (Map.Entry<Date, List<WriteOffBaseInfo>> dateEntry : timeMap.entrySet()) {
                Date uploadDeadline = dateEntry.getKey();
                List<WriteOffBaseInfo> value = dateEntry.getValue();
                if (uploadDeadline.before(now)) {
                    //已超时
                    for (WriteOffBaseInfo offBaseInfo : value) {
                        try {
                            writeOffContractDetailManageService.update(Wrappers.<WriteOffContractDetailManage>lambdaUpdate()
                                    .eq(WriteOffContractDetailManage::getBaseInfoApply, offBaseInfo.getApplyNo())
                                    .set(WriteOffContractDetailManage::getApportionFlag, ApportionEnum.APPORTIONCANCEL.getCode()));
                            //确认经销商费用总额-红冲凭证埋点
                            saveServiceFeeData(VoucherBuriedPointNo.serviceFeeRed, channelCode, channelBaseInfo.getChannelId(), offBaseInfo.getVoucher(), offBaseInfo.getOrganName());
                            //按期分摊佣金-红冲凭证埋点
                            saveServiceFeeData(VoucherBuriedPointNo.writeOffApportionRed, channelCode, channelBaseInfo.getChannelId(), offBaseInfo.getApplyNo(), offBaseInfo.getOrganName());
                            log.info("经销商编号:{},服务费账期:{},上传发票已超期,款项取消成功", channelCode, offBaseInfo.getWriteOffMonth());
                            appList.add(offBaseInfo.getApplyNo());
                        } catch (Exception e) {
                            log.error("经销商编号:{},服务费账期:{},上传发票已超期,款项取消异常:{}", channelCode, offBaseInfo.getWriteOffMonth(), e);
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(appList)) {
                // 状态设置为款项取消
                this.lambdaUpdate().set(WriteOffBaseInfo::getWriteOffStatus, WriteOffStatusEnum.OVERDUE_CANCEL)
                        .in(WriteOffBaseInfo::getApplyNo, appList).update();
                all.addAll(appList);
            }
        }
        if (CollUtil.isNotEmpty(all)) {
            // 状态同步进件
            IResponse<Boolean> response = applyWriteOffFeign.updateOverdueCancelStatus(all);
            log.info("服务费超期自动取消-同步款项取消状态给进件,{}", response);
        }
    }

    /**
     * 服务费时间同步定时任务
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void writeOffTimeSyncJob(String param) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar lastMonth = Calendar.getInstance();
        lastMonth.add(Calendar.MONTH, -1);
        String billingPeriod = dateFormat.format(lastMonth.getTime());
        long count = this.count(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getWriteOffMonth, billingPeriod));
        if (count <= 0) {
            log.info(billingPeriod + "账期，还未计算服务费");
            return;
        }
        List<WriteOffStatusEnum> statusEnumList = new ArrayList<>();
        statusEnumList.add(WriteOffStatusEnum.DRAFT_APPROVAL);
        statusEnumList.add(WriteOffStatusEnum.DELETED);
        statusEnumList.add(WriteOffStatusEnum.DRAFT_UNDER_APPROVAL);
        long count2 = this.count(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getWriteOffMonth, billingPeriod)
                .in(WriteOffBaseInfo::getWriteOffStatus, statusEnumList));
        if (count2 >= 1) {
            log.info(billingPeriod + "账期，服务费还未审批完");
            return;
        }
        List<WriteOffBaseInfo> list = this.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .eq(WriteOffBaseInfo::getTimeSyncFlag, StatusEnum.NO.getCode())
                .orderByDesc(WriteOffBaseInfo::getReleaseTime));
        if (list.size() <= 0) {
            log.info("没有未同步时间的服务费");
            return;
        }
        // 服务费草稿审批会在同一天内审批完成
        Date releaseTime = list.get(0).getReleaseTime();
        // 获取服务费期限字典
        Map<String, String> configuration = DicHelper.getDicMaps("ServiceFeeInvoiceTimeConfiguration")
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(DicDataDto::getValue, DicDataDto::getTitle));
        // 回司时间期限
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(releaseTime);
        calendar.add(Calendar.DAY_OF_MONTH, Integer.parseInt(configuration.get("发票回司时间期限")));
        // 上传时间期限
        Calendar cal = Calendar.getInstance();
        cal.setTime(releaseTime);
        cal.add(Calendar.DAY_OF_MONTH, Integer.parseInt(configuration.get("发票上传时间期限")));
        //精确到日内结束
        Date returningCompanyDeadline = DateUtil.endOfDay(calendar.getTime());
        Date invoiceUploadDeadline = DateUtil.endOfDay(cal.getTime());
        //是否季度末月
        boolean seasonEndFlag = false;
        int month = DateUtil.month(lastMonth.getTime()) + 1;
        if (month == 3 || month == 6 || month == 9 || month == 12) {
            seasonEndFlag = true;
        }
        //按经销商分组
        Map<String, List<WriteOffBaseInfo>> map = list.stream().collect(Collectors.groupingBy(WriteOffBaseInfo::getOrganId));
        for (Map.Entry<String, List<WriteOffBaseInfo>> entry : map.entrySet()) {
            String channelCode = entry.getKey();
            List<WriteOffBaseInfo> baseInfoList = entry.getValue();
            WriteOffAccountCycleDetail cycleDetail = writeOffAccountCycleDetailService.getOne(Wrappers.<WriteOffAccountCycleDetail>lambdaQuery()
                    .eq(WriteOffAccountCycleDetail::getChannelCode, channelCode));
            if (cycleDetail == null) {
                log.error("经销商对账周期不存在，经销商编号：{}", channelCode);
                continue;
            }
            //推送进件数据
            List<WriteOffBaseInfoDto> dtoList = new ArrayList<>();
            if (AccountCycleEnum.SEASON.getCode().equals(cycleDetail.getType())) {
                //季度对账
                if (seasonEndFlag) {
                    for (WriteOffBaseInfo baseInfo : baseInfoList) {
                        baseInfo.setReturningCompanyDeadline(returningCompanyDeadline);
                        baseInfo.setInvoiceUploadDeadline(invoiceUploadDeadline);
                        baseInfo.setTimeSyncFlag(StatusEnum.YES.getCode());
                        WriteOffBaseInfoDto dto = new WriteOffBaseInfoDto();
                        dto.setApplyNo(baseInfo.getApplyNo());
                        dto.setReturningCompanyDeadline(returningCompanyDeadline);
                        dto.setInvoiceUploadDeadline(invoiceUploadDeadline);
                        dtoList.add(dto);
                    }
                    this.updateBatchById(baseInfoList);
                } else {
                    //没到季度末月，跳过，不执行
                    continue;
                }
            } else {
                //月度
                for (WriteOffBaseInfo baseInfo : baseInfoList) {
                    baseInfo.setReturningCompanyDeadline(returningCompanyDeadline);
                    baseInfo.setInvoiceUploadDeadline(invoiceUploadDeadline);
                    baseInfo.setTimeSyncFlag(StatusEnum.YES.getCode());
                    WriteOffBaseInfoDto dto = new WriteOffBaseInfoDto();
                    dto.setApplyNo(baseInfo.getApplyNo());
                    dto.setReturningCompanyDeadline(returningCompanyDeadline);
                    dto.setInvoiceUploadDeadline(invoiceUploadDeadline);
                    dtoList.add(dto);
                }
                this.updateBatchById(baseInfoList);
            }
            //推送进件
            IResponse response = applyWriteOffFeign.syncWriteOffTime(dtoList);
            log.info("{}===推送结果{}",channelCode,response);
            Assert.isTrue(CommonConstants.SUCCESS.equals(response.getCode()), "服务费时间同步进件失败！");
        }
    }

    /**
     * 服务费数据埋点
     */
    private void saveServiceFeeData(String pointNo, String channelCode, Long channelId, String voucher, String channelName) {
        VoucherFlowInfoDto voucherFlowInfoDto = new VoucherFlowInfoDto();
        voucherFlowInfoDto.setBuriedPointNo(pointNo);
        voucherFlowInfoDto.setTransNo(voucher);
        voucherFlowInfoDto.setKeepAccountDate(new Date());
        voucherFlowInfoDto.setContractNo(channelCode);
        voucherFlowInfoDto.setDealerName(channelName);
        voucherFlowInfoDto.setChannelId(String.valueOf(channelId));
        voucherFlowInfoDto.setCustNo(String.valueOf(channelId));
        mqMessageQueueLogService.saveMqMessage(voucherFlowInfoDto);
    }
}
