package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.vo.TaskHistoryContent;
import com.ruicar.afs.cloud.afscase.approvetask.vo.TaskHistoryVO;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.writeoff.condition.WriteOffBaseAndTaskInfo;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ChannelServiceFeeCheckDetail;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ChannelServiceFeeDTO;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ChannelServiceFeeVo;
import com.ruicar.afs.cloud.afscase.writeoff.dto.DealerUpdateAmount;
import com.ruicar.afs.cloud.afscase.writeoff.dto.PermissionResultDto;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ServiceFeeWorkInfoResp;
import com.ruicar.afs.cloud.afscase.writeoff.entity.ServiceFeeWorkInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInvoiceRelMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.ChannelServiceFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPermissionService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeRecallVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeSubmitVo;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author： guoliang.jin
 * @date： 2022-11-28
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/channelServiceFee")
public class ChannelServiceFeeController {

    private ChannelServiceFeeService channelServiceFeeService;
    private CaseApproveRecordService caseApproveRecordService;
    private WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final WriteOffBaseInvoiceRelMapper writeOffBaseInvoiceRelMapper;
    private final WriteOffPermissionService writeOffPermissionService;
    private final WriteOffPayRecordService writeOffPayRecordService;

    @PostMapping("/getData")
    @ApiOperation(value = "查询表数据")
    public IResponse getData(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO){
        IPage<ChannelServiceFeeVo> pageResult = channelServiceFeeService.queryTaskList(new Page(channelServiceFeeDTO.getPageNumber(),
                channelServiceFeeDTO.getPageSize()), channelServiceFeeDTO);
        return IResponse.success(pageResult);
    }

    @PostMapping("/exportData")
    @ApiOperation(value = "导出表数据")
    public void exportData(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO, HttpServletRequest request, HttpServletResponse response){
        channelServiceFeeService.exportTaskList(channelServiceFeeDTO, request, response);
    }

    @PostMapping("/updatePaySuccessAmount")
    public IResponse updatePaySuccessAmount(@RequestBody List<DealerUpdateAmount> dealerUpdateAmountList){
        log.info("updatePaySuccessAmount,接收到推送的经销商成功付款金额：{}", JSON.toJSONString(dealerUpdateAmountList));
        channelServiceFeeService.updatePaySuccessAmount(dealerUpdateAmountList);
        return IResponse.success(null);
    }

    @PostMapping("/getServiceFeeList")
    @ApiOperation(value = "查询经销商周期服务费数据")
    public IResponse<IPage<WriteOffBaseInvoiceRel>> getServiceFeeList(@RequestBody ChannelServiceFeeDTO dto) {
        if (StrUtil.isNotBlank(dto.getWriteOffMonth())) {
            if (dto.getWriteOffMonth().contains(" ")) {
                dto.setWriteOffMonthList(List.of(dto.getWriteOffMonth().split(" ")));
            } else {
                dto.setWriteOffMonthList(Collections.singletonList(dto.getWriteOffMonth()));
            }
        }
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        if (!resultDto.getIsAll()) {
            dto.setHitCodeList(resultDto.getHitCodeList());
        }
        IPage<WriteOffBaseInvoiceRel> page = writeOffBaseInvoiceRelMapper.getServiceFeeList(new Page<>(dto.getPageNumber(), dto.getPageSize()),dto);
        return new IResponse().setData(page);
    }

    /**
     * 手动分页
     * @param dataList
     * @param pageSize
     * @param currentPage
     * @return
     */
    public static List<WriteOffBaseInvoiceRel> dealPage( List<WriteOffBaseInvoiceRel> dataList, int pageSize,int currentPage) {
        List<WriteOffBaseInvoiceRel> currentPageList = new ArrayList<>();
        if (dataList != null && dataList.size() > 0) {
            int currIdx = (currentPage > 1 ? (currentPage - 1) * pageSize : 0);
            for (int i = 0; i < pageSize && i < dataList.size() - currIdx; i++) {
                WriteOffBaseInvoiceRel data = dataList.get(currIdx + i);
                currentPageList.add(data);
            }
        }
        return currentPageList;
    }


    /**
     * 多条件分页获取待审核任务
     * @param condition
     * @return
     */
    @ApiOperation(value = "多条件分页获取待审核任务--仅展示当前登录用户的待审核任务")
    public IResponse<IPage<ServiceFeeWorkInfo>> getServiceFeeTask(@RequestBody ChannelServiceFeeDTO condition) {
        Assert.hasLength(condition.getPackageId(), "流程包id不能为空");
        Assert.hasLength(condition.getTemplateId(), "流程模版id不能为空");
        Assert.hasLength(condition.getUserDefinedIndex(), "流程节点自定义key不能为空");
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        return IResponse.success(channelServiceFeeService.getServiceFeeTask(page,condition));
    }

    @PostMapping(value = "/getServiceFeeTask")
    @ApiOperation(value = "多条件分页获取待审核任务--仅展示当前登录用户的待审核任务")
    public IResponse<IPage<ServiceFeeWorkInfoResp>> getServiceFeeTask22(@RequestBody ChannelServiceFeeDTO condition) {
        Assert.hasLength(condition.getPackageId(), "流程包id不能为空");
        Assert.hasLength(condition.getTemplateId(), "流程模版id不能为空");
        Assert.hasLength(condition.getUserDefinedIndex(), "流程节点自定义key不能为空");
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        return IResponse.success(channelServiceFeeService.getServiceFeeTask2(page,condition));
    }

    @PostMapping(value = "/getTaskInfo")
    @ApiOperation(value = "查看日志")
    public IResponse getTaskInfo(@RequestParam("applyNo") String applyNo)  {
        List<CaseApproveRecord> result = caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                        .eq(CaseApproveRecord::getApplyNo, applyNo)
                        .orderByAsc(CaseApproveRecord::getId));
        return IResponse.success(result);
    }


    @PostMapping(value = "/getTaskData")
    @ApiOperation(value = "审批详情")
    public IResponse getTaskData(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO)  {
        List<ChannelServiceFeeCheckDetail> list = new ArrayList<>();
        List<WriteOffPayRecord> payRecordList = writeOffPayRecordService.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                .eq(WriteOffPayRecord::getBusinessNo, channelServiceFeeDTO.getApplyNo()));
        if (payRecordList.size() > 0) {
            Map<String, List<WriteOffPayRecord>> channelMap = payRecordList.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getChannelCode));
            for (Map.Entry<String, List<WriteOffPayRecord>> entry : channelMap.entrySet()) {
                List<WriteOffPayRecord> records = entry.getValue();
                BigDecimal totalAmount = BigDecimal.ZERO;
                List<WriteOffBaseInvoiceRel> relList = new ArrayList<>();
                for (WriteOffPayRecord record : records) {
                    totalAmount = totalAmount.add(null == record.getPayAmount() ? BigDecimal.ZERO : record.getPayAmount());
                    WriteOffBaseInvoiceRel invoiceRel = new WriteOffBaseInvoiceRel();
                    invoiceRel.setDealerName(record.getChannelFullName());
                    invoiceRel.setWriteOffMonth(record.getWriteOffMonth());
                    invoiceRel.setInvoiceAmount(record.getPayAmount());
                    relList.add(invoiceRel);
                }
                ChannelServiceFeeCheckDetail detail = new ChannelServiceFeeCheckDetail();
                detail.setDealerCode(entry.getKey());
                detail.setDealerName(records.get(0).getChannelFullName());
                detail.setWriteOffBaseInfo(relList);
                detail.setListSize(String.valueOf(records.size()));
                detail.setTotalAmount(totalAmount);
                detail.setFileBusiNo(records.get(0).getFileBusiNo());
                list.add(detail);
            }
        } else {
            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                    .eq(WriteOffBaseInvoiceRel::getApproveId, channelServiceFeeDTO.getApplyNo())
                    .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));
            Map<String, List<WriteOffBaseInvoiceRel>> dealerMap = writeOffBaseInvoiceRels.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getDealerCode));
            for (String code : dealerMap.keySet()) {
                ChannelServiceFeeCheckDetail detail = new ChannelServiceFeeCheckDetail();
                detail.setDealerCode(code);
                List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels1 = dealerMap.get(code);
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (WriteOffBaseInvoiceRel rel : writeOffBaseInvoiceRels1) {
                    totalAmount = totalAmount.add(null == rel.getInvoiceAmount() ? BigDecimal.ZERO : rel.getInvoiceAmount());
                }
                detail.setDealerName(writeOffBaseInvoiceRels1.get(0).getDealerName());
                detail.setWriteOffBaseInfo(writeOffBaseInvoiceRels1);
                detail.setListSize(String.valueOf(writeOffBaseInvoiceRels1.size()));
                detail.setTotalAmount(totalAmount);
                detail.setFileBusiNo(writeOffBaseInvoiceRels.get(0).getFileBusiNo());
                list.add(detail);
            }
        }
        return new IResponse().setData(list);
    }


    /**
     * 流程发起
     * @param channelServiceFeeDTO
     * @return
     */
    @ApiOperation(value = "流程发起")
    public IResponse<String> submitApprove(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO) {
        return channelServiceFeeService.submitApprove(channelServiceFeeDTO.getChannelCode());
    }

    /**
     * 正常发起流程
     * @param submitVo
     * @return 响应
     */
    @PostMapping("/submitApprove")
    @ApiOperation(value = "流程发起_正常发起")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> submitApproveNormal(@RequestBody FeeSubmitVo submitVo) {
        return channelServiceFeeService.submitApproveNormal(submitVo);
    }

    @PostMapping("/submitTask")
    @ApiOperation(value = "流程审批：通过，驳回")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> submitTask(@RequestBody SubmitTaskParam submitTaskParam) {
        if("back".equals(submitTaskParam.getOperationType())){
            submitTaskParam.setApproveSuggest("驳回");
            submitTaskParam.setApproveSuggestName("驳回");
        }
        if (StrUtil.isNotBlank(submitTaskParam.getRemark())){
            submitTaskParam.setRemark(submitTaskParam.getRemark());
        }else {
            submitTaskParam.setRemark("无");
        }
        final IResponse<Boolean> response = channelServiceFeeService.submitTask(submitTaskParam, null);
        return IResponse.success(response);
    }

    @PostMapping("/batchApproveSubmit")
    @ApiOperation(value = "流程审批：批量通过")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> batchApproveSubmit(@RequestBody List<SubmitTaskParam> submitTaskParamList) {
        log.info("批量通过开始：", JSON.toJSONString(submitTaskParamList));
        IResponse<Boolean> response = new IResponse<>();
        for (SubmitTaskParam submitTaskParam : submitTaskParamList) {
            submitTaskParam.setApproveSuggest("通过");
            submitTaskParam.setApproveSuggestName("通过");
            submitTaskParam.setRemark("通过");
            submitTaskParam.setOperationType("submit");

            response = channelServiceFeeService.submitTask(submitTaskParam, null);
        }

        return IResponse.success(response);
    }

    @PostMapping("/batchRefuseSubmit")
    @ApiOperation(value = "流程审批：批量驳回")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> batchRefuseSubmit(@RequestBody List<SubmitTaskParam> submitTaskParamList) {
        log.info("批量驳回开始：", JSON.toJSONString(submitTaskParamList));
        IResponse<Boolean> response = new IResponse<>();
        for (SubmitTaskParam submitTaskParam : submitTaskParamList) {
            submitTaskParam.setApproveSuggest("驳回");
            submitTaskParam.setApproveSuggestName("驳回");
            submitTaskParam.setRemark("驳回");
            submitTaskParam.setOperationType("refuse");

            response = channelServiceFeeService.submitTask(submitTaskParam, null);
        }

        return IResponse.success(response);
    }

    @PostMapping(value = "/listHistoryContent")
    @ApiOperation(value = "审批页面审批记录")
    public IResponse listHistoryContent(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO)  {
        TaskHistoryVO taskHistoryVO = new TaskHistoryVO();
        List<TaskHistoryContent> contentsList = new ArrayList();

        List<CaseApproveRecord> resultList = caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                        .eq(CaseApproveRecord::getApplyNo, channelServiceFeeDTO.getApplyNo())
                        .orderByDesc(CaseApproveRecord::getCreateTime));

        if (CollectionUtil.isNotEmpty(resultList)) {
            for (CaseApproveRecord result : resultList) {
                TaskHistoryContent content = new TaskHistoryContent();

                content.setApproveSuggest(result.getApproveSuggest());

                String format = DateUtil.format(result.getApproveEndTime(), "yyyy-MM-dd HH:mm:ss");
                Date date = DateUtil.parse(format);
                result.setApproveEndTime(date);

                if (StringUtils.isEmpty(result.getDisposeNodeName())) {
                    content.setTitle("操作【"+ result.getApproveSuggestName() +"】,操作人【"+ result.getDisposeStaff() +"】,操作时间【"+ result.getApproveEndTime() +"】");
                } else {
                    content.setTitle(result.getDisposeNodeName()+"--审批意见【"+ result.getApproveSuggestName() +"】,审批人【"+ result.getDisposeStaff() +"】,审批时间【"+ result.getApproveEndTime() +"】");
                }

                if (StringUtils.isEmpty(result.getApproveRemark())) {
                    content.setApproveRemark("审批备注:无");
                } else {
                    content.setApproveRemark("审批备注:" + result.getApproveRemark());
                }

                contentsList.add(content);
            }
            taskHistoryVO.setApproveEnd(true);
            taskHistoryVO.setTaskHistories(contentsList);
        } else {
            taskHistoryVO.setApproveEnd(false);

        }
        return IResponse.success(taskHistoryVO);
    }


    @PostMapping("/approveDetail")
    public IResponse approveDetail(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO) {
        Page page = new Page(channelServiceFeeDTO.getPageNumber(), channelServiceFeeDTO.getPageSize());
        IPage<WriteOffBaseAndTaskInfo> iPage = channelServiceFeeService.approveDetail(page, channelServiceFeeDTO.getChannelCode());

        return IResponse.success(iPage);
    }


    @PostMapping("/approveRecord")
    public IResponse approveRecord(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO) {

        List<CaseApproveRecord> list = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getApplyNo, channelServiceFeeDTO.getApplyNo()));

        return IResponse.success(list);
    }

    @PostMapping("/approveData")
    public IResponse approveData(@RequestBody ChannelServiceFeeDTO channelServiceFeeDTO) {
        List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                .eq(WriteOffBaseInvoiceRel::getApproveId, channelServiceFeeDTO.getApplyNo())
                .orderByAsc(WriteOffBaseInvoiceRel::getCreateTime));

        BigDecimal amount = BigDecimal.ZERO;
        for (WriteOffBaseInvoiceRel writeOffBaseInvoiceRel : writeOffBaseInvoiceRels) {
            //服务类型转换
            if ("0".equals(writeOffBaseInvoiceRel.getServiceType())) {
                writeOffBaseInvoiceRel.setServiceType("直租");
            } else if ("1".equals(writeOffBaseInvoiceRel.getServiceType())) {
                writeOffBaseInvoiceRel.setServiceType("回租");
            }

            amount = amount.add(writeOffBaseInvoiceRel.getInvoiceAmount());
        }
        log.info("根据申请编号查询到的提取总金额{}", amount);

        //方便前端取值
        writeOffBaseInvoiceRels.get(0).setTotalAmount(amount);
        return IResponse.success(writeOffBaseInvoiceRels);
    }

    /**
     * 查询cbs付款结果，激活等待节点
     */
    @PostMapping("/getCbsPayResult")
    @ApiOperation(value = "查询cbs付款结果")
    public IResponse<String> getCbsPayResult() {
        return channelServiceFeeService.getCbsPayResult(new ArrayList<>());
    }

    /**
     * 人工付款，激活等待节点
     */
    @PostMapping("/artificialPay")
    @ApiOperation(value = "人工付款")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> artificialPay(@RequestBody List<Long> relIdList) {
        return channelServiceFeeService.artificialPay(relIdList);
    }

    /**
     * 失败记录发起重新支付
     */
    @PostMapping("/failedRecordInitiatesRepay")
    @ApiOperation(value = "失败记录发起重新支付")
    public IResponse<String> initiatesRepay(@RequestBody List<Long> relIdList) {
        return channelServiceFeeService.failedRecordInitiatesRepay(relIdList);
    }

    @PostMapping("/feeRecallBatch")
    @ApiOperation("提取流程批量撤销")
    @Transactional(rollbackFor = Exception.class)
    public IResponse recallBatch(@RequestBody FeeRecallVo recallVo) {
        return channelServiceFeeService.recallBatch(recallVo.getBusiNoList());
    }

    @PostMapping("/exportServiceFeeDate")
    @ApiOperation(value = "服务费提取项按条件导出")
    public void exportServiceFeeDate(HttpServletResponse response, @RequestBody ChannelServiceFeeDTO condition){
        channelServiceFeeService.exportByCondition(response, condition);
    }

    @GetMapping("/getWriteOffTqFile")
    @ApiOperation(value = "获取服务费提取影像件management")
    public IResponse getWriteOffTqFile(){
        ComAttachmentManagement management = comAttachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getBusiNode, "writeOff")
                .eq(ComAttachmentManagement::getUniqueCode, "writeOffTq"));
        return IResponse.success(management);
    }
}
