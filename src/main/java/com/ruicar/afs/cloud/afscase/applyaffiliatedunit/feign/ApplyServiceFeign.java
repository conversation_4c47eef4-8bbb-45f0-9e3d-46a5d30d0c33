package com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign;

import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.dto.ChannelGroupCheckVO;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.afscase.approvetask.vo.HistoryInfoEntity;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.apply.dto.AffiliatedDetailsDto;
import com.ruicar.afs.cloud.loan.sdk.dto.VehicleAndCapitalTypeDTO;
import com.ruicar.afs.cloud.parameter.commom.entity.FaceReviewNotice;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * The interface Apply feign.
 *
 * @description:
 * @author: wjy
 * @date: 2023 /10/09
 */
@FeignClient(contextId = "ApplyServiceFeign", value = "${com.ruicar.service-names.apply-service:apply-service}")
public interface ApplyServiceFeign {
    /**
     * 获取挂靠相关信息
     *
     * @param applyNo
     * @param headers  the headers
     * @return the sys user temp
     */
    @ApiOperation("查询用户部门信息")
    @GetMapping(value = "/order/searchAffiliatedInfo")
    IResponse<AffiliatedDetailsDto> searchAffiliatedInfo(@RequestParam("applyNo") String applyNo, @RequestHeader Map<String, String> headers);

    /**
     * 通过申请编号保存需要锁定的合同目录信息
     * @param applyNo 申请编号
     * @param scene 场景
     * @return the sys user temp
     */
    @ApiOperation("通过申请编号保存需要锁定的合同目录信息")
    @GetMapping(value = "/applyContractLockInfo/saveLockInfoByApplyNo")
    void saveLockInfoByApplyNo(@RequestParam("applyNo") String applyNo, @RequestParam("scene") String scene);

    /**
     * 锁定签约合同目录，不允许修改
     * @param applyNo 申请编号
     * @param scene 场景
     * @return the sys user temp
     */
    @ApiOperation("锁定合同目录")
    @GetMapping(value = "/applySignRelation/lockCatalogName")
    boolean lockCatalogName(@RequestParam("applyNo") String applyNo, @RequestParam("scene") String scene);

    /**
     * 检查渠道组员
     * @param checkVO checkVO
     * @return Boolean
     */
    @PostMapping("/channelInvoice/checkChannelGroup")
    @ApiOperation("检查经销商发票和渠道是否同一个分组")
    IResponse<ChannelGroupCheckVO> checkChannelGroup(@RequestBody ChannelGroupCheckVO checkVO);

    /**
     * 获取挂靠相关信息
     *
     * @param applyNo
     * @return the sys user temp
     */
    @ApiOperation("获取合同签约方式")
    @PostMapping(value = "/contract/getAuthorizeWay")
    IResponse getAuthorizeWay(@RequestParam("applyNo") String applyNo);

    /**
     * 获取该订单的签署方式
     * @param applyNo 申请编号
     * @return 签署方式
     */
    @PostMapping("/applyContractInfo/getAuthorizeWayByApplyNo")
    @ApiOperation("获取该订单的签署方式")
    IResponse<String> getAuthorizeWayByApplyNo(@RequestParam String applyNo);


    /**
     * 获取挂靠信息
     * @param applyNo
     * @return
     */
    @PostMapping("/applyAffiliatedUnit/getAffiliatedUnit")
    @ApiOperation("获取挂靠信息")
    IResponse<ApplyAffiliatedUnit> getAffiliatedUnit(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通知进件需视频面审
     * @param params
     * @return 处理结果
     */
    @PostMapping("/faceReview/faceReviewNotice")
    IResponse<?> faceReviewNotice(@RequestBody FaceReviewNotice params);

    /**
     * 获取是否带牌车
     * @param applyNo 申请编号
     * @return 处理结果
     */
    @PostMapping("/nbgcCarInfo/getLicensedVehicleByApplyNo")
    IResponse<String> getLicensedVehicleByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通过申请编号获取对应的车辆类型
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/creatContract/getVehicleTypeEnumByApplyNo")
    @ApiOperation("通过申请编号获取对应的车辆类型")
    IResponse<VehicleAndCapitalTypeDTO> getVehicleTypeEnumByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通过申请编号列表获取对应的车辆类型Map
     * @param applyNoList 申请编号列表
     * @return 返回结果
     */
    @PostMapping("/creatContract/getVehicleTypeEnumMapByApplyNoList")
    @ApiOperation("通过申请编号列表获取对应的车辆类型Map")
    IResponse<Map<String, VehicleAndCapitalTypeDTO>> getVehicleTypeEnumMapByApplyNoList(@RequestParam(name = "applyNoList" ) List<String> applyNoList);

    /**
     * 通过申请编号获取资料是否开启
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/applyContractInfo/getIsDataPostStatusByApplyNo")
    @ApiOperation("通过申请编号获取资料是否开启")
    IResponse<String> getIsDataPostStatusByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);


    /**
     * 通过申请编号获取资料是否开启
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/channelCaseInfo/getChannelCodeByApplyNo")
    @ApiOperation("通过申请编号获取经销商编码")
    IResponse<String> getChannelCodeByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通过申请编号获取接收到的资方签约状态
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/applyContractInfo/getCapitalOrderStatusByApplyNo")
    @ApiOperation("通过申请编号获取接收到的资方签约状态")
    Map<String,String> getCapitalOrderStatusByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通过申请编号获取免抵信息
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/applyOrderInfo/getDecisionMortgageFlagByApplyNo")
    @ApiOperation("通过申请编号获取免抵信息")
    IResponse<String> getDecisionMortgageFlagByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 查询挂靠公司历史匹配
     * @param applyNo
     * @return
     */
    @PostMapping("/order/queryHistoryInfoListByCompanyName")
    @ApiOperation("查询挂靠公司历史匹配")
    IResponse<List<HistoryInfoEntity>> queryHistoryInfoListByCompanyName(@RequestParam(name = "applyNo" ) String applyNo);


    /**
     * 通过申请编号获取进件和预审批城市编码
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/applyBestSignRecord/getCityCode")
    IResponse<Map<String,String>> getCityCode(@RequestParam(name = "applyNo" ) String applyNo);

    /**
     * 通过申请编号获取免抵信息
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @PostMapping("/applyOrderInfo/getDecisionMortgageResultByApplyNo")
    @ApiOperation("通过申请编号获取免抵信息")
    IResponse<String> getDecisionMortgageResultByApplyNo(@RequestParam(name = "applyNo" ) String applyNo);
}
