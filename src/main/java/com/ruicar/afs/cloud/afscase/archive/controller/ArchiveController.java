package com.ruicar.afs.cloud.afscase.archive.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.ApproveMakeLabel;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.ApproveMakeLabelService;
import com.ruicar.afs.cloud.afscase.approveprev.entity.PreCaseCustInfo;
import com.ruicar.afs.cloud.afscase.approveprev.entity.PreCaseOrderInfo;
import com.ruicar.afs.cloud.afscase.approveprev.service.PreCaseCustInfoService;
import com.ruicar.afs.cloud.afscase.approveprev.service.PreCaseOrderInfoService;
import com.ruicar.afs.cloud.afscase.archive.dto.ArchiveCsaeSubmitDTO;
import com.ruicar.afs.cloud.afscase.archive.dto.ArchiveElectronAssetsHistory;
import com.ruicar.afs.cloud.afscase.archive.dto.ArchiveOnlineSubmitDTO;
import com.ruicar.afs.cloud.afscase.archive.dto.CustInfoDto;
import com.ruicar.afs.cloud.afscase.archive.dto.FileListDTO;
import com.ruicar.afs.cloud.afscase.archive.service.ArchiveApiService;
import com.ruicar.afs.cloud.afscase.archive.service.CustInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAffiliatedUnits;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAffiliatedUnitsService;
import com.ruicar.afs.cloud.afscase.grade.constant.Constants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInsuranceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInsuranceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindCondition;
import com.ruicar.afs.cloud.afscase.remind.entity.CaseRemindDetail;
import com.ruicar.afs.cloud.afscase.remind.service.RemindService;
import com.ruicar.afs.cloud.afscase.remind.vo.RemindVo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.enums.ApplyCarInvoiceEnum;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CancelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.InsuranceTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindTypeEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseBaseInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseChannelInfoDto;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.parameter.commom.enums.InsuranceTypeEnums;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum.APPROVE;
import static com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum.GENERAL_LOAN;

/**
 * <AUTHOR>
 * @time 2021/10/21 14:22 周四
 * @description: 归档信息查询
 */
@Api("案件归档接口")
@RestController
@RequestMapping("/archive")
@AllArgsConstructor
@Slf4j
public class ArchiveController {
    private final CarInvoiceInfoService carInvoiceInfoService;//发票
    private final CarInsuranceInfoService carInsuranceInfoService;//保险
    private final CaseCustContactService caseCustContactService;
    private final CustInfoService custInfoService;
    private final CaseCarInfoService caseCarInfoService;
    private final ApproveMakeLabelService approveMakeLabelService;
    private final CaseCustInfoService caseCustInfoService;
    private final PreCaseCustInfoService preCaseCustInfoService;
    private final ComAttachmentFileService comAttachmentFileService;
    private RemindService remindService;
    private CaseBaseInfoService caseBaseInfoService;
    private CaseContractInfoService caseContractInfoService;
    private final ApplyCostDetailsService applyCostDetailsService;
    private final CaseChannelInfoService caseChannelInfoService;
    private final ArchiveApiService archiveApiService;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final CaseChannelUniteInfoService caseChannelUniteInfoService;
    private final PreCaseOrderInfoService preCaseOrderInfoService;
    private final CaseEnterpriseCustomerDetailsService enterpriseCustomerDetailsService;
    private final ChannelAffiliatedUnitsService channelAffiliatedUnitsService;
    private final CaseCostInfoService caseCostInfoService;

    private final CaseUseApplyServiceFeign applyServiceFeign;
    private final ApplyConfig applyConfig;
    /**
     * 获取购车发票信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getInvoiceInfo/{applyNo}")
    public IResponse<CarInvoiceInfo>  getInvoiceInfo(@PathVariable("applyNo")String applyNo) {
        CarInvoiceInfo carInvoiceInfo = carInvoiceInfoService.getOne(
                Wrappers.<CarInvoiceInfo>lambdaQuery().eq(CarInvoiceInfo::getApplyNo, applyNo)
                        .eq(CarInvoiceInfo::getInvoiceType, ApplyCarInvoiceEnum.INVOICETYPE_BUYCAR.getIndex())
                        .eq(CarInvoiceInfo::getDelFlag, Constants.DEL_FLAG_NO));
        return IResponse.success(carInvoiceInfo);
    }
    /**
     * 查询精品装潢发票信息
     * @param applyNo 申请编号
     * @return
     */
    @RequestMapping("/getDecorationInvoice/{applyNo}")
    public IResponse  getDecorationInvoice(@PathVariable("applyNo")String applyNo) {
        CarInvoiceInfo carInvoiceInfo = carInvoiceInfoService.getOne(
                Wrappers.<CarInvoiceInfo>lambdaQuery().eq(CarInvoiceInfo::getApplyNo, applyNo)
                        .eq(CarInvoiceInfo::getInvoiceType, ApplyCarInvoiceEnum.INVOICETYPE_DECORATION.getIndex())
                        .eq(CarInvoiceInfo::getDelFlag, Constants.DEL_FLAG_NO));
        return IResponse.success(carInvoiceInfo);
    }

    /**
     * 查询商业保单信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCommercialPolicy/{applyNo}")
    public IResponse  getCommercialPolicy(@PathVariable("applyNo")String applyNo) {
        CarInsuranceInfo insuranceInfoList = carInsuranceInfoService.getOne(Wrappers.<CarInsuranceInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(applyNo), CarInsuranceInfo::getApplyNo, applyNo)
                .eq(CarInsuranceInfo::getInsuranceType, InsuranceTypeEnums.BUSINESS.getCode()));
        return IResponse.success(insuranceInfoList);
    }

    /**
     * 查询交强险保单信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getStrongInsurancePolicyInfo/{applyNo}")
    public IResponse  getStrongInsurancePolicyInfo(@PathVariable("applyNo")String applyNo) {
        CarInsuranceInfo insuranceInfoList = carInsuranceInfoService.getOne(Wrappers.<CarInsuranceInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(applyNo), CarInsuranceInfo::getApplyNo, applyNo)
                .eq(CarInsuranceInfo::getInsuranceType, InsuranceTypeEnums.COMPULSORY.getCode()));
        return IResponse.success(insuranceInfoList);
    }

    /**
     * 查询订单联系人列表
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCaseCustContactList/{applyNo}")
    public IResponse  getCaseCustContactList(@PathVariable("applyNo")String applyNo) {
        List<CustInfoDto> contactList = custInfoService.getContact(applyNo);
        return IResponse.success(contactList);
    }
    /**
     * 查询概要信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getSummaryInformation/{applyNo}")
    public IResponse  getSummaryInformation(@PathVariable("applyNo")String applyNo) {
        return archiveApiService.getSummaryInformation(applyNo);

    }

    /**
     * 查询车辆信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCarDetail/{applyNo}")
    public IResponse<CaseCarInfo> getCarDetail(@PathVariable("applyNo")String applyNo) {
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, applyNo));
        return IResponse.success(caseCarInfo);
    }

    /**
     * 查询车辆信息
     * @param applyNos
     * @return
     */
    @RequestMapping("/getCarDetails")
    public IResponse<List<CaseCarInfo>> getCarDetails(@RequestBody List<String> applyNos) {
        if(CollUtil.isEmpty(applyNos)) {
            return IResponse.success(CollUtil.newArrayList());
        }

        List<CaseCarInfo> list = caseCarInfoService.list(Wrappers.<CaseCarInfo>query().lambda()
                .in(CaseCarInfo::getApplyNo, applyNos));
        return IResponse.success(list);
    }

    /**
     * 查询签约时间  签约方式
     * @param applyNo
     * @return
     */
    @RequestMapping("/getAgencyNo/{applyNo}")
    public IResponse<PreCaseCustInfo> getAgencyNo(@PathVariable("applyNo")String applyNo) {
        //根据申请编号查询orderId
        CaseBaseInfo one = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, applyNo));

        PreCaseCustInfo preCaseCustInfo = preCaseCustInfoService.getOne(Wrappers.<PreCaseCustInfo>query().lambda()
                .eq(PreCaseCustInfo::getOrderId, one.getOrderId()));
        return IResponse.success(preCaseCustInfo);
    }

    /**
     * 查询签约时间  签约方式 (入参 orderId)
     * @param orderId
     * @return
     */
    @RequestMapping("/getOrder/{orderId}")
    public IResponse<PreCaseCustInfo> getOrder(@PathVariable("orderId")String orderId) {
        PreCaseCustInfo preCaseCustInfo = preCaseCustInfoService.getOne(Wrappers.<PreCaseCustInfo>query().lambda()
                .eq(PreCaseCustInfo::getOrderId, orderId));
        return IResponse.success(preCaseCustInfo);
    }

    /**
     * 查询征信来源  （根据申请编号）
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCredit/{applyNo}")
    public IResponse<CaseChannelUniteInfo> getCredit(@PathVariable("applyNo")String applyNo) {
        //根据申请编号查询
        CaseChannelUniteInfo one = caseChannelUniteInfoService.getOne(Wrappers.<CaseChannelUniteInfo>query().lambda()
                .eq(CaseChannelUniteInfo::getApplyNo,applyNo));
        return IResponse.success(one);
    }

    /**
     * 查询经销商code  （根据申编号编号case_channel_info）
     * @param applyNo
     * @return
     */
    @RequestMapping("/getChannelCode/{applyNo}")
    public IResponse<CaseChannelInfoDto> getChannelCode(@PathVariable("applyNo")String applyNo) {
        CaseChannelInfo one = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(CaseChannelInfo::getApplyNo, applyNo));
        return IResponse.success(one);
    }

    /**
     * 查询案件合同信息表
     * @param applyNo
     * @return
     */
    @RequestMapping("/getContractInfo/{applyNo}")
    public IResponse<CaseContractInfo> getContractInfo(@PathVariable("applyNo")String applyNo) {
        CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo, applyNo));
        return IResponse.success(contractInfo);
    }

    /**
     * 查询车辆信息
     * @param applyNos
     * @return
     */
    @RequestMapping("/getContractInfos")
    public IResponse<List<CaseContractInfo>> getContractInfos(@RequestBody List<String> applyNos) {
        if(CollUtil.isEmpty(applyNos)) {
            return IResponse.success(CollUtil.newArrayList());
        }

        List<CaseContractInfo> list = caseContractInfoService.list(Wrappers.<CaseContractInfo>query().lambda()
                .in(CaseContractInfo::getApplyNo, applyNos));
        return IResponse.success(list);
    }


    /**
     * 根据预审编号查询数据
     * @param orderId
     * @return
     */
    @RequestMapping("/getOrderId/{orderId}")
    public IResponse<PreCaseOrderInfo> getOrderId(@PathVariable("orderId")String orderId) {
        //根据预审编号查询pre_case_order_info
        PreCaseOrderInfo  preCaseOrderInfo = preCaseOrderInfoService.getOne(Wrappers.<PreCaseOrderInfo>query().lambda()
                .eq(PreCaseOrderInfo::getOrderId, orderId));
        return IResponse.success(preCaseOrderInfo);
    }

    /**
     * 根据申请编号查询数据
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCaseInfo/{applyNo}")
    public IResponse<CaseBaseInfoDto> getCaseInfo(@PathVariable("applyNo")String applyNo) {
        //根据申请编号查询案件基本信息表数据
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, applyNo));
        return IResponse.success(caseBaseInfo);
    }

    /**
     * 根据申请编号查询数据
     * @param applyNos
     * @return
     */
    @PostMapping("/getCaseInfoByApplyNos")
    public IResponse<CaseBaseInfoDto> getCaseInfoByApplyNos(@RequestBody List<String> applyNos) {
        if(CollUtil.isEmpty(applyNos)) {
            return IResponse.success(CollUtil.newArrayList());
        }

        //根据申请编号查询案件基本信息表数据
        List<CaseBaseInfo> caseBaseInfos = caseBaseInfoService.list(Wrappers.<CaseBaseInfo>query().lambda()
                .in(CaseBaseInfo::getApplyNo, applyNos));
        return IResponse.success(caseBaseInfos);
    }

    /**
     * 根据预审编号查询数据部门id
     * @param orderId
     * @return
     */
    @RequestMapping("/getDeptId/{orderId}")
    public IResponse<CaseBaseInfoDto> getDeptId(@PathVariable("orderId")String orderId) {
        //根据预审编号查询数据部门id
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getOrderId, orderId));
        return IResponse.success(caseBaseInfo);
    }


    /**
     * 查询客户信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getCustList")
    public IResponse<CaseCustInfo> getCustList(@RequestParam("applyNo")String applyNo) {
        List<CaseCustInfo>custList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo));
        return IResponse.success(custList);
    }

    /**
     * 查询客户信息
     * @param applyNos
     * @return
     */
    @PostMapping(value = "/getCustListByApplyNos")
    public IResponse<List<CaseCustInfo>> getCustListByApplyNos(@RequestBody List<String> applyNos) {
        if(CollUtil.isEmpty(applyNos)) {
            return IResponse.success(CollUtil.newArrayList());
        }
        List<CaseCustInfo> caseCustInfos = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .in(CaseCustInfo::getApplyNo, applyNos));
        return IResponse.success(caseCustInfos);
    }

    /**
     * 查询企业信息
     * @param custId
     * @return
     */
    @RequestMapping("/getEnterpriseName")
    public IResponse<CaseEnterpriseCustomerDetails> getEnterpriseName(@RequestParam("custId")String custId) {
        CaseEnterpriseCustomerDetails customerDetails = enterpriseCustomerDetailsService.getOne(new LambdaQueryWrapper<CaseEnterpriseCustomerDetails>()
                .eq(CaseEnterpriseCustomerDetails::getCustId,custId));

        return IResponse.success(customerDetails);
    }

    /**
     * 查询企业信息
     * @param custIds
     * @return
     */
    @PostMapping(value = "/getEnterpriseDetails")
    public IResponse<List<CaseEnterpriseCustomerDetails>> getEnterpriseDetails(@RequestBody List<Long> custIds) {
        List<CaseEnterpriseCustomerDetails> customerDetails = enterpriseCustomerDetailsService.list(new LambdaQueryWrapper<CaseEnterpriseCustomerDetails>()
                .in(CaseEnterpriseCustomerDetails::getCustId,custIds));
        return IResponse.success(customerDetails);
    }


    /**
     * 查询挂靠查询挂靠企业信息
     * @param applyNo
     * @return
     */
    @RequestMapping("/getAffiliatedUnit")
    public IResponse<ChannelAffiliatedUnits> getAffiliatedUnit(@RequestParam("applyNo")String applyNo) {
        ChannelAffiliatedUnits affiliatedUnit = channelAffiliatedUnitsService.findByApplyNo(applyNo);

        return IResponse.success(affiliatedUnit);
    }

    /**
     * 获取风险提示
     * @param applyNo
     * @return
     */
    @RequestMapping("/getApproveMakeLabel")
    public IResponse<List<ApproveMakeLabel>>  getApproveMakeLabel(@RequestParam("applyNo") String applyNo) {
        /**风控看板  标签信息 **/
        List<ApproveMakeLabel> labelList = approveMakeLabelService.list(Wrappers.<ApproveMakeLabel>query().lambda()
                .eq(ApproveMakeLabel::getApplyNo, applyNo)
                .eq(ApproveMakeLabel::getLabelLocation, LabelPositionEnum.CREDITANALYSIS.getCode()));
        return IResponse.success(labelList);
    }
    /**
     * 获取文件列表
     * @return
     */
    @PostMapping("/getFileList")
    public IResponse<List<ComAttachmentFile>> getFileList(@RequestBody FileListDTO fileListDTO) {
        /**文件信息 **/
        List<ComAttachmentFile> fileList = comAttachmentFileService.getFileList(fileListDTO.getContractNo(),fileListDTO.getStrList());
        return IResponse.success(fileList);
    }

    /**
     * 查询对应合同类型的文件列表
     * @return
     */
    @PostMapping("/getFileListByContractNo")
    public IResponse<List<ComAttachmentFile>> getFileListByContractNo(@RequestBody FileListDTO fileListDTO) {
        /**文件信息 **/
        List<ComAttachmentFile> fileList = comAttachmentFileService.getFileListByContractNo(fileListDTO.getContractNo(),fileListDTO.getStrList());
        return IResponse.success(fileList);
    }


    /**
     * 获取文件列表
     * @return
     */
    @PostMapping("/queryArchivedImageInformation")
    public IResponse<List<ComAttachmentFile>> queryArchivedImageInformation(@RequestBody FileListDTO fileListDTO) {
        /**文件信息 **/
        List<ComAttachmentFile> fileList = comAttachmentFileService
                .queryArchivedImageInformation(fileListDTO.getContractNo(),fileListDTO.getApplyNo(), fileListDTO.getBusiNodeList());
        return IResponse.success(fileList);
    }


    /**
     *
     * <p>Description: 根据条件查询提醒列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryRemindListByApplyNo")
    @ApiOperation(value = "根据条件查询留言/提醒列表")
    public IResponse<List<RemindVo>> queryRemindListByApplyNo(@RequestBody RemindCondition remindCondition) {
        List<RemindVo> remindVoList = new ArrayList<>();
        List<CaseRemindDetail> caseRemindDetailList =  remindService.list(Wrappers.<CaseRemindDetail>query().lambda()
                .eq(ObjectUtils.isNotEmpty(remindCondition.getApplyNo()),CaseRemindDetail::getApplyNo,remindCondition.getApplyNo())
                .eq(CaseRemindDetail::getStatus, CancelStatusEnum.EFFECTIVE.getCode())
                .in(ObjectUtils.isNotEmpty(remindCondition.getUseScene()),CaseRemindDetail::getUseScene,GENERAL_LOAN.getValue(),APPROVE.getValue())
        );
        boolean caseNormalFlowEnd = caseBaseInfoService.checkNormalFlowEnd(remindCondition.getApplyNo(),remindCondition.getStageId());
        if(CollectionUtils.isNotEmpty(caseRemindDetailList)){
            // 过滤掉内部留言且内容为空的留言数据
            caseRemindDetailList = caseRemindDetailList.stream()
                    .filter(remind ->
                            !(RemindTypeEnum.INNER.getValue().equals(remind.getRemindType())
                                    && StrUtil.isBlank(remind.getRemindContent())))
                    .collect(Collectors.toList());
            caseRemindDetailList.forEach(caseRemindDetail->{
                RemindVo remindVo = new RemindVo();
                BeanUtils.copyProperties(caseRemindDetail,remindVo);
                /**  撤销操作权限  **/
                if(caseNormalFlowEnd){ // 审批结束后不允许撤销
                    remindVo.setCancel(false);
                }else {
                    String id = SecurityUtils.getUser().getId().toString();
                    if (id.equals(remindVo.getDisposeUserId())) {
                        remindVo.setCancel(true);
                    } else {
                        remindVo.setCancel(false);
                    }
                }
                remindVoList.add(remindVo);
            });
        }
        return IResponse.success(remindVoList);
    }


    @PostMapping(value = "/synchronizeCaseData")
    @ApiOperation(value = "同步案件数据")
    @Transactional(rollbackFor = Exception.class)
    public IResponse   synchronizeCaseData(@RequestBody ArchiveCsaeSubmitDTO dto){
        log.info("同步数据入参{}"+ JSON.toJSONString(dto));
        //车辆信息 新
        if(null != dto.getNewCar()){
            CaseCarInfo assets = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>lambdaQuery()
                    .eq(CaseCarInfo::getApplyNo, dto.getApplyNo()));
            if (ObjectUtils.isEmpty(assets)){
                throw new  ClassCastException("车辆信息不存在无法同步");
            }
            assets.setBrandName(dto.getNewCar().getBrandName());
            assets.setSeriesName(dto.getNewCar().getSeriesName());
            assets.setModelName(dto.getNewCar().getModelName());
            assets.setEngineNo(dto.getNewCar().getEngineNo());
            assets.setSalePrice(dto.getNewCar().getSalePrice());
            assets.setGuidePrice(dto.getNewCar().getGuidePrice());
            assets.setVehicleMadeDate(dto.getNewCar().getVehicleMadeDate());
            assets.setGbCode(dto.getNewCar().getGbCode());
            assets.setLicensePlate(dto.getNewCar().getCarNumber());
            assets.setCarVin(dto.getNewCar().getCarVin());
            assets.setCertificateNo(dto.getNewCar().getCertificateNo());
            assets.setMortgageDate(dto.getNewCar().getMortgageDate());
            assets.setLicensePlateProvinceCode(dto.getNewCar().getLicensePlateProvinceCode());
            assets.setLicensePlateProvince(dto.getNewCar().getLicensePlateProvince());
            assets.setLicensePlateCityCode(dto.getNewCar().getLicensePlateCityCode());
            assets.setLicensePlateCity(dto.getNewCar().getLicensePlateCity());
            assets.setActualMortgagor(dto.getNewCar().getActualMortgagor());
            assets.setMortgageStatus(dto.getNewCar().getMortgageStatus());
                caseCarInfoService.updateById(assets);

        }
        //车辆发票信息 新/
        if(null != dto.getNewCarInvoice() ) {

            CarInvoiceInfo archiveInvoiceTemp = carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>lambdaQuery()
                    .eq(CarInvoiceInfo::getApplyNo, dto.getApplyNo())
                    .eq(CarInvoiceInfo::getInvoiceType,ApplyCarInvoiceEnum.INVOICETYPE_BUYCAR.getIndex()));
            if (ObjectUtils.isNotEmpty(archiveInvoiceTemp)){
                archiveInvoiceTemp.setMakeInvoiceUnit(dto.getNewCarInvoice().getMakeInvoiceUnit());
                archiveInvoiceTemp.setInvoiceCode(dto.getNewCarInvoice().getInvoiceCode());
                archiveInvoiceTemp.setInvoiceNumber(dto.getNewCarInvoice().getInvoiceNumber());
                archiveInvoiceTemp.setInvoiceDate(dto.getNewCarInvoice().getInvoiceDate());
                archiveInvoiceTemp.setBuyerName(dto.getNewCarInvoice().getBuyerName());
                archiveInvoiceTemp.setBuyerIdcardNo(dto.getNewCarInvoice().getBuyerIdcardNo());
                archiveInvoiceTemp.setBuyerPurchaserTaxNo(dto.getNewCarInvoice().getBuyerPurchaserTaxNo());
                archiveInvoiceTemp.setCarVin(dto.getNewCarInvoice().getCarVin());
                archiveInvoiceTemp.setEngineNo(dto.getNewCarInvoice().getEngineNo());
                archiveInvoiceTemp.setInvoiceAmt(dto.getNewZhInvoice().getInvoiceAmt());
                archiveInvoiceTemp.setInvoiceTaxAmt(dto.getNewCarInvoice().getInvoiceTaxAmt());
                archiveInvoiceTemp.setExcludingTaxAmt(dto.getNewCarInvoice().getExcludingTaxAmt());
                archiveInvoiceTemp.setInvoiceRate(dto.getNewCarInvoice().getInvoiceRate());
                carInvoiceInfoService.updateById(archiveInvoiceTemp);
            }


        }
        //装潢发票信息 新/
        if(null != dto.getNewZhInvoice() ) {
            CarInvoiceInfo zhInvoiceTemp = carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>lambdaQuery()
                    .eq(CarInvoiceInfo::getApplyNo, dto.getApplyNo())
                    .eq(CarInvoiceInfo::getInvoiceType,ApplyCarInvoiceEnum.INVOICETYPE_DECORATION.getIndex()));
            if (ObjectUtils.isNotEmpty(zhInvoiceTemp)){
                zhInvoiceTemp.setMakeInvoiceUnit(dto.getNewZhInvoice().getMakeInvoiceUnit());
                zhInvoiceTemp.setInvoiceCode(dto.getNewZhInvoice().getInvoiceCode());
                zhInvoiceTemp.setInvoiceNumber(dto.getNewZhInvoice().getInvoiceNumber());
                zhInvoiceTemp.setInvoiceDate(dto.getNewZhInvoice().getInvoiceDate());
                zhInvoiceTemp.setInvoiceRate(dto.getNewZhInvoice().getInvoiceRate());
                zhInvoiceTemp.setExcludingTaxAmt(dto.getNewZhInvoice().getExcludingTaxAmt());
                zhInvoiceTemp.setInvoiceAmt(dto.getNewZhInvoice().getInvoiceAmt());
                zhInvoiceTemp.setInvoiceTaxAmt(dto.getNewCarInvoice().getInvoiceTaxAmt());
                carInvoiceInfoService.updateById(zhInvoiceTemp);
            }

        }
        //商业险信息 新/旧
        if(null != dto.getNewBusiInsurance() ) {
            CarInsuranceInfo buInsurance = carInsuranceInfoService.getOne(Wrappers.<CarInsuranceInfo>lambdaQuery()
                            .eq(CarInsuranceInfo::getApplyNo, dto.getApplyNo())
                            .eq(CarInsuranceInfo::getInsuranceType, InsuranceTypeEnum.BUSINESS.getCode()));
            if (ObjectUtils.isNotEmpty(buInsurance)){
                buInsurance.setInsuranceMode(dto.getNewBusiInsurance().getInsuranceMode());
                buInsurance.setInsuranceNo(dto.getNewBusiInsurance().getInsuranceNo());
                buInsurance.setInsuranceAmt(dto.getNewBusiInsurance().getInsuranceAmt());
                buInsurance.setInsuranceMoney(dto.getNewBusiInsurance().getInsuranceMoney());
                buInsurance.setInsuranceStartTime(dto.getNewBusiInsurance().getInsuranceStartTime());
                buInsurance.setInsuranceEndTime(dto.getNewBusiInsurance().getInsuranceEndTime());
                buInsurance.setThirdInsurance(dto.getNewBusiInsurance().getThirdInsurance());
                carInsuranceInfoService.updateById(buInsurance);
            }


        }
        //交强险信息 新/旧
        if(null != dto.getNewJqInsurance() ) {
            CarInsuranceInfo buInsurance = carInsuranceInfoService.getOne(Wrappers.<CarInsuranceInfo>lambdaQuery()
                    .eq(CarInsuranceInfo::getApplyNo, dto.getApplyNo())
                    .eq(CarInsuranceInfo::getInsuranceType, InsuranceTypeEnum.COMPULSORY.getCode()));
            if (ObjectUtils.isNotEmpty(buInsurance)){
                buInsurance.setApplyNo(dto.getNewJqInsurance().getApplyNo());
                buInsurance.setInsuranceMode(dto.getNewJqInsurance().getInsuranceMode());
                buInsurance.setInsuranceNo(dto.getNewJqInsurance().getInsuranceNo());
                buInsurance.setInsuranceAmt(dto.getNewJqInsurance().getInsuranceAmt());
                buInsurance.setInsuranceMoney(dto.getNewJqInsurance().getInsuranceMoney());
                buInsurance.setInsuranceStartTime(dto.getNewJqInsurance().getInsuranceStartTime());
                buInsurance.setInsuranceEndTime(dto.getNewJqInsurance().getInsuranceEndTime());
                buInsurance.setThirdInsurance(dto.getNewJqInsurance().getThirdInsurance());
                carInsuranceInfoService.updateById(buInsurance);
            }

            }
        return IResponse.success("同步完成");
    }


    @PostMapping(value = "/queryAttachment")
    @ApiOperation(value = "同步影像件文件")
    @Transactional(rollbackFor = Exception.class)
    public IResponse queryAttachment(@RequestBody ArchiveOnlineSubmitDTO dto){
        log.info("影像件同步数据入参{}"+ JSON.toJSONString(dto));
        //查询影像
        String busiNode = "postLoan";
        if ("01".equals(dto.getChannelBelong())){
            busiNode = "postLoanZY";
        }
        List<ComAttachmentManagement> comAttachmentManagements = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                        .eq(ComAttachmentManagement::getBusiNode,busiNode)
        );
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo,dto.getApplyNo())
        );
        Assert.isTrue(caseContractInfo != null,"合同信息不允许为空");

        List<String> attachmentCodeList =  comAttachmentManagements.stream().map(s -> {
        	return s.getId().toString();
        }).collect(Collectors.toList());

        //查询影像文件
        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                .in(ComAttachmentFile::getAttachmentCode, attachmentCodeList)
                .eq(ComAttachmentFile::getBusiNo, caseContractInfo.getContractNo())
        );
        //影像件文件不为空，删除
        if (comAttachmentFiles.size()>0){
            for (int i = 0;i< comAttachmentFiles.size();i++) {
                comAttachmentFileService.removeById(comAttachmentFiles.get(i).getId());
            }
        }
        if(!dto.getAttachmentFile().isEmpty()){
            for (int i = 0;i< dto.getAttachmentFile().size();i++) {
                dto.getAttachmentFile().get(i).setId(null);
            }
        }
        //批量保存
        comAttachmentFileService.saveBatch(dto.getAttachmentFile());
        return IResponse.success("提交成功");
    }

    @PostMapping(value = "/saveAttachment")
    @ApiOperation(value = "保存影像件文件")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveAttachment(@RequestBody ArchiveOnlineSubmitDTO dto){
        log.info("影像件同步数据入参{}", JSON.toJSONString(dto));
        Assert.isTrue(dto != null,"缺少必要参数,请检查");
        Assert.isTrue(CollUtil.isNotEmpty(dto.getFileVos()),"缺少必要参数,请检查");
        //查询影像
        List<ComAttachmentManagement> comAttachmentManagements = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                .in(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.CERTIFICATE_INFO.getCode(), AttachmentUniqueCodeEnum.CERTIFICATE_REG.getCode())
        );
        log.info("影像件同步数据入参comAttachmentManagements{}", JSONUtil.parse(comAttachmentManagements));
        Assert.isTrue(comAttachmentManagements != null,"影像件不允许为空");
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo,dto.getApplyNo())
        );
        Assert.isTrue(caseContractInfo != null,"合同信息不允许为空");

        List<String> attachmentCodeList =  comAttachmentManagements.stream().map(s -> s.getId().toString()).collect(Collectors.toList());
        log.info("影像件同步数据入参attachmentCodeList{}", JSON.toJSONString(attachmentCodeList));
        //查询影像文件
        IResponse<List<ComAttachmentFile>> comAttachmentFilesIResponse = applyServiceFeign.getAttachmentFiles(attachmentCodeList, caseContractInfo.getContractNo(), getApplyHeader());
        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFilesIResponse.getData();
        log.info("影像件同步数据入参comAttachmentFiles{}", JSON.toJSONString(comAttachmentFiles));
        //删除
        if (CollUtil.isNotEmpty(comAttachmentFiles)) {
            applyServiceFeign.removeAttachmentFiles(attachmentCodeList, caseContractInfo.getContractNo(), getApplyHeader());
        }

        Map<String, ComAttachmentManagement> attachmentUniqueCodeMap = comAttachmentManagements.stream()
                .collect(Collectors.toMap(
                        ComAttachmentManagement::getUniqueCode,
                        comAttachmentManagement -> comAttachmentManagement
                ));
        log.info("影像件同步数据入参attachmentUniqueCodeMap{}", JSON.toJSONString(attachmentUniqueCodeMap));
        List<ArchiveOnlineSubmitDTO.FileVo> fileVos = dto.getFileVos();
        List<ComAttachmentFile> attachmentFileList = new ArrayList<>();
        for (ArchiveOnlineSubmitDTO.FileVo fileVo : fileVos) {
            ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
            comAttachmentFile.setId(null);
            comAttachmentFile.setCreateBy(SecurityUtils.getUsername());
            comAttachmentFile.setUpdateBy(SecurityUtils.getUsername());
            comAttachmentFile.setCreateTime(DateUtil.date());
            comAttachmentFile.setUpdateTime(DateUtil.date());
            comAttachmentFile.setRemake(fileVo.getType());
            comAttachmentFile.setBusiNo(caseContractInfo.getContractNo());
            comAttachmentFile.setBelongNo(caseContractInfo.getContractNo());
            comAttachmentFile.setAttachmentCode(String.valueOf(attachmentUniqueCodeMap.get(fileVo.getType()).getId()));
            comAttachmentFile.setAttachmentName(attachmentUniqueCodeMap.get(fileVo.getType()).getAttachmentName());
            comAttachmentFile.setFileStatus(FileStatusEnum.STANDARD.getCode());
            Assert.isTrue(StringUtils.isNotEmpty(fileVo.getMd5()),"上传影像资料MD5值不允许为空");
            comAttachmentFile.setFileId(fileVo.getMd5());
            comAttachmentFile.setHistoryVersion(fileVo.getVersion());
            comAttachmentFile.setIsElectronic("0");
            comAttachmentFile.setFileName(fileVo.getFileName());
            comAttachmentFile.setUploadTime(DateUtil.date());
            comAttachmentFile.setCreateBy(SecurityUtils.getUsername());
            comAttachmentFile.setFileSource("com_attachment_management");
            log.info("影像件同步数据入参comAttachmentFile{}", JSON.toJSONString(comAttachmentFile));
            attachmentFileList.add(comAttachmentFile);
        }

        log.info("影像件同步数据入参attachmentFileList{}", JSON.toJSONString(attachmentFileList));
        // 批量保存
        IResponse saveAttachmentFileIResponse = applyServiceFeign.saveAttachmentFiles(attachmentFileList, getApplyHeader());
        Assert.isTrue(StrUtil.equals(saveAttachmentFileIResponse.getCode(), CommonConstants.SUCCESS),"保存机动车证书图片失败");
        return saveAttachmentFileIResponse;
    }

    /**
     *  影像件保存
     * @param assets
     * @param headers
     * @return  CaseCutsInfoDto
     */
    @PostMapping(value = "/saveAttachmentFile")
    @ApiOperation("影像件同步案件数据")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveAttachmentFile(@RequestBody ArchiveElectronAssetsHistory assets, @RequestHeader Map<String, String> headers) {
        log.info("影像件同步数据入参{}", JSON.toJSONString(assets));
        Assert.isTrue(assets != null,"缺少必要参数,请检查");
        List<ComAttachmentFile> attachmentFile = assets.getAttachmentFile();
        Assert.isTrue(CollUtil.isNotEmpty(attachmentFile),"缺少必要参数,请检查");
        //查询影像
        List<ComAttachmentManagement> comAttachmentManagements = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                .in(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.CERTIFICATE_INFO.getCode(), AttachmentUniqueCodeEnum.CERTIFICATE_REG.getCode())
        );
        log.info("影像件同步数据入参comAttachmentManagements{}", JSONUtil.parse(comAttachmentManagements));
        Assert.isTrue(comAttachmentManagements != null,"影像件不允许为空");
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo, assets.getApplyNo())
        );
        Assert.isTrue(caseContractInfo != null,"合同信息不允许为空");

        List<String> attachmentCodeList =  comAttachmentManagements.stream().map(s -> s.getId().toString()).collect(Collectors.toList());
        log.info("影像件同步数据入参attachmentCodeList{}", JSON.toJSONString(attachmentCodeList));
        //查询影像文件
        IResponse<List<ComAttachmentFile>> comAttachmentFilesIResponse = applyServiceFeign.getAttachmentFiles(attachmentCodeList, caseContractInfo.getContractNo(), getApplyHeader());
        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFilesIResponse.getData();
        log.info("影像件同步数据入参comAttachmentFiles{}", JSON.toJSONString(comAttachmentFiles));
        //删除
        if (CollUtil.isNotEmpty(comAttachmentFiles)) {
            applyServiceFeign.removeAttachmentFiles(attachmentCodeList, caseContractInfo.getContractNo(), getApplyHeader());
        }

        Map<String, ComAttachmentManagement> attachmentUniqueCodeMap = comAttachmentManagements.stream()
                .collect(Collectors.toMap(
                        ComAttachmentManagement::getUniqueCode,
                        Function.identity()
                ));
        log.info("影像件同步数据入参attachmentUniqueCodeMap{}", JSON.toJSONString(attachmentUniqueCodeMap));
        List<ComAttachmentFile> attachmentFileList = new ArrayList<>();
        for (ComAttachmentFile file : attachmentFile) {
            ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
            comAttachmentFile.setId(null);
            comAttachmentFile.setCreateBy(SecurityUtils.getUsername());
            comAttachmentFile.setUpdateBy(SecurityUtils.getUsername());
            comAttachmentFile.setCreateTime(DateUtil.date());
            comAttachmentFile.setUpdateTime(DateUtil.date());
            String remake = file.getRemake();
            Assert.isTrue(attachmentUniqueCodeMap.containsKey(remake),"上传机动车登记证书影像资料唯一标识(remake)不允许为空!");
            comAttachmentFile.setRemake(remake);
            comAttachmentFile.setBusiNo(caseContractInfo.getContractNo());
            comAttachmentFile.setBelongNo(caseContractInfo.getContractNo());
            comAttachmentFile.setAttachmentCode(String.valueOf(attachmentUniqueCodeMap.get(remake).getId()));
            comAttachmentFile.setAttachmentName(attachmentUniqueCodeMap.get(remake).getAttachmentName());
            comAttachmentFile.setFileStatus(FileStatusEnum.STANDARD.getCode());
            Assert.isTrue(StringUtils.isNotEmpty(file.getFileId()),"上传影像资料文件ID值(FileId)不允许为空");
            comAttachmentFile.setFileId(file.getFileId());
            comAttachmentFile.setHistoryVersion(file.getHistoryVersion());
            comAttachmentFile.setIsElectronic(CommonConstants.COMMON_NO);
            comAttachmentFile.setFileName(file.getFileName());
            comAttachmentFile.setUploadTime(DateUtil.date());
            comAttachmentFile.setCreateBy(SecurityUtils.getUsername());
            comAttachmentFile.setFileSource("com_attachment_management");
            log.info("影像件同步数据入参comAttachmentFile{}", JSON.toJSONString(comAttachmentFile));
            attachmentFileList.add(comAttachmentFile);
        }

        log.info("影像件同步数据入参attachmentFileList{}", JSON.toJSONString(attachmentFileList));
        // 批量保存
        IResponse saveAttachmentFileIResponse = applyServiceFeign.saveAttachmentFiles(attachmentFileList, getApplyHeader());
        Assert.isTrue(StrUtil.equals(saveAttachmentFileIResponse.getCode(), CommonConstants.SUCCESS),"保存机动车证书图片失败");
        return saveAttachmentFileIResponse;
    }

    /**
     * 获取 header
     * @return
     */
    private Map<String, String> getApplyHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }

    @PostMapping(value = "/getAttachmentFileApp")
    @ApiOperation(value = "app查询合同影像件文件")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<List<ComAttachmentFile>> getAttachmentFileApp(@RequestParam("applyNo")String applyNo){
        List<ComAttachmentManagement> comAttachmentManagements = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                .in(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.CERTIFICATE_INFO.getCode(), AttachmentUniqueCodeEnum.CERTIFICATE_REG.getCode())
        );
        log.info("影像件同步数据入参comAttachmentManagements{}", JSONUtil.parse(comAttachmentManagements));
        Assert.isTrue(comAttachmentManagements != null,"影像件不允许为空");
        List<String> attachmentCodeList =  comAttachmentManagements.stream().map(s -> s.getId().toString()).collect(Collectors.toList());
        log.info("影像件同步数据入参attachmentCodeList{}", JSONUtil.parse(attachmentCodeList));
        // 查询合同信息
        CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo, applyNo));
        Assert.isTrue(contractInfo != null,"合同信息不允许为空");
        log.info("影像件同步数据入参contractInfo{}", JSON.toJSONString(contractInfo));
        // 查询影像文件
        IResponse<List<ComAttachmentFile>> comAttachmentFilesIResponse = applyServiceFeign.getAttachmentFiles(attachmentCodeList, contractInfo.getContractNo(), getApplyHeader());
        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFilesIResponse.getData();
        log.info("影像件同步数据入参comAttachmentFiles{}", JSON.toJSONString(comAttachmentFiles));
        return IResponse.success(comAttachmentFiles);
    }

    @PostMapping(value = "/getAttachmentFile")
    @ApiOperation(value = "查询合同影像件文件")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<List<ComAttachmentFile>> getAttachmentFile(@RequestParam("contractNo")String contractNo,@RequestBody List<String> strList){
        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFileService.getFileListByContractNoAndAttachCode(contractNo,strList);
        return IResponse.success(comAttachmentFiles);
    }

    /**
     * 保存外部留言
     * @return
     */
    @PostMapping("/saveCaseRemindDetail")
    public IResponse saveCaseRemindDetail(@RequestBody CaseRemindDetail caseRemindDetail) {
        remindService.save(caseRemindDetail);
        return IResponse.success("ok");
    }



    @PostMapping(value = "/queryCaseCostInfos")
    @ApiOperation(value = "根据申请编号查询融资费用")
    public IResponse<?> queryCaseCostInfos(List<String> applyNos) {
        List<FinCostDetails> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyNos)) {
            list = caseCostInfoService.list(Wrappers.<FinCostDetails>lambdaQuery().in(FinCostDetails::getApplyNo, applyNos));
        }
        return IResponse.success(list);
    }
}
