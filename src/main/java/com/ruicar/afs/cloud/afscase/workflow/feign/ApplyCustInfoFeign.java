package com.ruicar.afs.cloud.afscase.workflow.feign;

import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffPeriodLoanDto;
import com.ruicar.afs.cloud.parameter.commom.vo.ApplyCustBaseInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * The interface Apply cust info feign.
 */
@FeignClient(name = "${com.ruicar.service-names.apply-server:afs-apply-biz}",contextId = "ApplyCustInfoFeign")
public interface ApplyCustInfoFeign {

    /**
     * Gets cus and guarantor by apply no.
     *
     * @param params the params
     * @return the cus and guarantor by apply no
     */
    @PostMapping("/cust/getCusAndGuarantorByApplyNo")
    IResponse getCusAndGuarantorByApplyNo(@RequestBody ApplyCustBaseInfoVo params);

    /**
     * 服务费提取成功，同步进件信息
     *
     * @param applyNos the apply nos
     * @return the response
     */
    @PostMapping("/writeOffBaseInfo/syncApplyWriteOffLoanInfo")
    IResponse syncApplyWriteOffLoanInfo(@RequestBody List<String> applyNos);

    /**
     * 服务费分期提取成功，同步进件信息
     *
     * @param periodLoanDtos
     * @return the response
     */
    @PostMapping("/writeOffBaseInfo/syncApplyWriteOffPeriod")
    IResponse syncApplyWriteOffPeriod(@RequestBody List<WriteOffPeriodLoanDto> periodLoanDtos);
}
