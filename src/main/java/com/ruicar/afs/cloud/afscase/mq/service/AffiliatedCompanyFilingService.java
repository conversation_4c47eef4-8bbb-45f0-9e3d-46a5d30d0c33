package com.ruicar.afs.cloud.afscase.mq.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.mq.condition.AffiliatedCompanyFilingCondition;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.vo.AffiliatedCompanyFilingVo;
import java.util.List;


public interface AffiliatedCompanyFilingService extends IService<AffiliatedCompanyFiling> {
    /**
     * 获取备案任务信息列表
     * @param page
     * @param companyFilingCondition
     * @return
     */
    IPage<List<AffiliatedCompanyFilingVo>> queryApproveTaskLaunchList(Page page, AffiliatedCompanyFilingCondition companyFilingCondition);
}
