package com.ruicar.afs.cloud.afscase.writeoff.dto;

import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 合同明细查询实体
 * @author： qinghao
 * @date： 2022-11-24
 */
@Data
public class ContractDetailManageSearchDTO implements Serializable {
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 核销项汇总核销项编号
     */
    private String baseInfoApply;
    /**
     * 承租人姓名
     */
    private String custNameRepeat;
    /**
     * 账期
     */
    private String writeOffMonth;
    /**
     * 结清账期
     */
    private String advanceSettleMonth;
    /**
     * 付款起始时间
     */
    private Date activationStartTime;
    /**
     *  付款结束时间
     */
    private Date activationEndTime;
    /**
     * 经销商名称
     */
    private String channelFullName;
    /**
     * 合同号list
     */
    private List<String> contractNoList;
    /**
     * 放款时间起
     */
    private Date loanStart;
    /**
     * 放款时间止
     */
    private Date loanEnd;
    /**
     * 合同状态
     */
    private ContractStatusEnum contractStatus;
    /**
     * 所属资方
     */
    private String belongingCapital;
    /**
     * 是否生成了核销项(1是，0否)
     */
    private String writeOffFlag;
    /**
     * 是否生成了提取项(1是，0否)
     */
    private String serverTqFlag;
}
