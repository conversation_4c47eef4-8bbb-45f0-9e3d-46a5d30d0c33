package com.ruicar.afs.cloud.afscase.caseocr.dto;


import com.ruicar.afs.cloud.deepseek.entity.CaseFacePhotoInfo;
import com.ruicar.afs.cloud.deepseek.entity.DeepSeekDrivingInfo;
import com.ruicar.afs.cloud.deepseek.entity.StatementAutomaticRecognition;
import lombok.Data;

import java.util.List;

@Data
public class DsResultVo {
	private String applyNo;
	private List<StatementAutomaticRecognition> statementAutomaticRecognitionList;
	private List<CaseFacePhotoInfo> caseFacePhotoInfoList;
	private List<DeepSeekDrivingInfo> deepSeekDrivingInfoList;
}
