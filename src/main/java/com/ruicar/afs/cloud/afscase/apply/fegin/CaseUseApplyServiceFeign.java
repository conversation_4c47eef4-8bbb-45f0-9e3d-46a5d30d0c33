package com.ruicar.afs.cloud.afscase.apply.fegin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.apply.condition.ApplyBusinessConfCondition;
import com.ruicar.afs.cloud.afscase.apply.condition.ApplySignCondition;
import com.ruicar.afs.cloud.afscase.apply.entity.ApplyBusinessConfigRule;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.carrierpigeon.controller.vo.SingleIdVO;
import com.ruicar.afs.cloud.afscase.mq.entity.CommonFailMqInfo;
import com.ruicar.afs.cloud.afscase.report.condition.ReportMonitorCondition;
import com.ruicar.afs.cloud.afscase.risk.vo.PersonInfoCondition;
import com.ruicar.afs.cloud.afscase.writeoff.dto.SubmitOrBackResultDTO;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApplyResultInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CasePreApplyCustInfoDto;
import com.ruicar.afs.cloud.parameter.commom.vo.ApplyBankCardVo;
import com.ruicar.afs.cloud.parameter.commom.vo.ApplyBankTypeCiccVo;
import com.ruicar.afs.cloud.parameter.commom.vo.ApplyCarInvoiceVo;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanDiscardDTO;
import com.ruicar.afs.cloud.zhengxin.condition.ZxReportCondition;
import com.ruicar.afs.cloud.zhengxin.entity.ComReportFile;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;
import java.util.Map;

/**
 * 调用进件fegin
 *
 * <AUTHOR>
 */
@FeignClient(name = "${com.ruicar.service-names.apply-server:afs-apply-biz}")
public interface CaseUseApplyServiceFeign {

    /**
     * 查询
     *
     * @param condition the condition
     * @param headers   the headers
     * @return business conf info
     */
    @ApiOperation("查询")
    @PostMapping("businessConf/getBusinessConfInfo")
    IResponse getBusinessConfInfo(@RequestBody QueryCondition<ApplyBusinessConfCondition> condition, @RequestHeader Map<String, String> headers);

    /**
     * 查询单个
     *
     * @param condition the condition
     * @param headers   the headers
     * @return info
     */
    @PostMapping("businessConf/getInfo")
    IResponse getInfo(@RequestBody ApplyBusinessConfCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * 更新
     *
     * @param condition the condition
     * @param headers   the headers
     * @return response
     */
    @ApiOperation("更新")
    @PostMapping("businessConf/updateBusinessConfInfo")
    IResponse updateBusinessConfInfo(@RequestBody ApplyBusinessConfCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * 删除
     *
     * @param condition the condition
     * @param headers   the headers
     * @return response
     */
    @ApiOperation("删除")
    @PostMapping("businessConf/deleteBusinessConfInfo")
    IResponse deleteBusinessConfInfo(@RequestBody ApplyBusinessConfCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * 新增
     *
     * @param condition the condition
     * @param headers   the headers
     * @return response
     */
    @ApiOperation("新增")
    @PostMapping("businessConf/saveBusinessConfInfo")
    IResponse saveBusinessConfInfo(@RequestBody ApplyBusinessConfCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * Resend archive mq response.
     *
     * @param commonFailMqInfo the common fail mq info
     * @param headers          the headers
     * @return the response
     */
    @ApiOperation("重新发送档案的MQ")
    @PostMapping("archivedData/resendArchiveMQ")
    IResponse resendArchiveMq(@RequestBody CommonFailMqInfo commonFailMqInfo, @RequestHeader Map<String, String> headers);

    /**
     * Gets apply business config rule.
     *
     * @param condition the condition
     * @param headers   the headers
     * @return the apply business config rule
     */
    @ApiOperation("根据业务id获取规则信息")
    @PostMapping("businessConf/getApplyBusinessConfigRule")
    IResponse getApplyBusinessConfigRule(@RequestBody ApplyBusinessConfCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * Gets rule by business rule.
     *
     * @param condition the condition
     * @param headers   the headers
     * @return the rule by business rule
     */
    @ApiOperation("根据业务id以及业务类型获取规则信息")
    @PostMapping("businessConf/getRuleByBusinessRule")
    IResponse getRuleByBusinessRule(@RequestBody ApplyBusinessConfigRule condition, @RequestHeader Map<String, String> headers);

    /**
     * Upd apply business config rule response.
     *
     * @param condition the condition
     * @param headers   the headers
     * @return the response
     */
    @ApiOperation("修改规则信息")
    @PostMapping("businessConf/updApplyBusinessConfigRule")
    IResponse updApplyBusinessConfigRule(@RequestBody ApplyBusinessConfigRule condition, @RequestHeader Map<String, String> headers);

    /**
     * Gets order channel list.
     *
     * @param headers the headers
     * @return the order channel list
     */
    @ApiOperation("获取系统中渠道车行开票方关系数据")
    @PostMapping("orderLeading/getOrderChannelList")
    IResponse getOrderChannelList(@RequestHeader Map<String, String> headers);

    /**
     * Gets anti fraud info.
     *
     * @param condition the condition
     * @param headers   the headers
     * @return the anti fraud info
     */
    @ApiOperation("获取系统中存量校验数据")
    @PostMapping("antiFraudMng/getAntiFraudInfo")
    IResponse getAntiFraudInfo(@RequestBody PersonInfoCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * Gets sign address.
     *
     * @param condition the condition
     * @param headers   the headers
     * @return the sign address
     */
    @ApiOperation("查询承租人电子签约地址信息")
    @PostMapping("antiFraudMng/getSignAddress")
    IResponse getSignAddress(@RequestBody ApplySignCondition condition, @RequestHeader Map<String, String> headers);

    /**
     * Gets customer info by id.
     *
     * @param custId  the cust id
     * @param headers the headers
     * @return the customer info by id
     */
    @ApiOperation("通过Id获取客户数据")
    @GetMapping("cust/getCustomerInfoById")
    IResponse getCustomerInfoById(@RequestParam(value = "custId") String custId,@RequestHeader Map<String, String> headers);

    /**
     * Gets pre customer info by id.
     *
     * @param custId  the cust id
     * @param headers the headers
     * @return the pre customer info by id
     */
    @ApiOperation("通过Id获取预审批客户数据")
    @GetMapping("cust/getPreCustomerInfoById")
    IResponse getPreCustomerInfoById(@RequestParam(value = "custId") String custId,@RequestHeader Map<String, String> headers);

    /**
     * 核销项审批通过或退回
     *
     * @param submitOrBackResultDTO the submit or back result dto
     * @param headers               the headers
     * @return response
     */
    @ApiOperation("核销项审批通过或退回")
    @PostMapping("writeOffApprovalInfo/submitOrBackResult")
    IResponse submitOrBackResult(@RequestBody SubmitOrBackResultDTO submitOrBackResultDTO, @RequestHeader Map<String, String> headers);

    /**
     * 待核销核销项撤回
     *
     * @param applyNos the apply nos
     * @param headers  the headers
     * @return response
     */
    @ApiOperation("待核销核销项撤回")
    @GetMapping("writeOffBaseInvoiceRel/callBack")
    IResponse writeOffCallBack(@RequestParam("applyNos") List<String> applyNos, @RequestHeader Map<String, String> headers);

    /**
     * Gets cust details by cust id.
     *
     * @param custId  the cust id
     * @param headers the headers
     * @return the cust details by cust id
     */
    @ApiOperation("通过客户编号获取客户详情信息")
    @GetMapping("cust/getCustDetailsByCustId")
    IResponse getCustDetailsByCustId(@RequestParam(value = "custId") String custId, @RequestHeader Map<String, String> headers);

    /**
     * 银行卡申请表增加
     *
     * @param applyBankCard the apply bank card
     * @return response
     */
    @ApiOperation("银行卡申请表增加")
    @PostMapping("/applyBankCard/addApplyBankCard")
    IResponse addApplyBankCard(@RequestBody ApplyBankCardVo applyBankCard);

    /**
     * 银行卡申请表删除
     *
     * @param applyBankCard the apply bank card
     * @return response
     */
    @ApiOperation("银行卡申请表删除")
    @PostMapping("/applyBankCard/deleteApplyBankCard")
    IResponse deleteApplyBankCard(@RequestBody ApplyBankCardVo applyBankCard);

    /**
     * 银行卡申请表修改
     *
     * @param applyBankCard the apply bank card
     * @return response
     */
    @ApiOperation("银行卡申请表修改")
    @PostMapping ("/applyBankCard/editApplyBankCard")
    IResponse editApplyBankCard(@RequestBody ApplyBankCardVo applyBankCard);

    /**
     * 银行卡申请表查询
     *
     * @param condition the condition
     * @return response
     */
    @ApiOperation("银行卡申请表查询")
    @PostMapping("/applyBankCard/queryApplyBankCard")
    IResponse queryApplyBankCard(@RequestBody QueryCondition<ApplyBankCardVo> condition);


    /**
     * 银行卡类型表(bin码)新增
     *
     * @param applyBankTypeCicc the apply bank type cicc
     * @return the response
     */
    @ApiOperation("银行卡类型表(bin码)新增")
    @PostMapping("/applyBankTypeCicc/addApplyBankTypeCicc")
    IResponse addApplyBankTypeCicc(@RequestBody ApplyBankTypeCiccVo applyBankTypeCicc);

    /**
     * 银行卡类型表(bin码)删除
     *
     * @param applyBankCard the apply bank card
     * @return the response
     */
    @ApiOperation("银行卡类型表(bin码)删除")
    @PostMapping("applyBankTypeCicc/deleteApplyBankTypeCicc")
    IResponse deleteApplyBankTypeCicc(@RequestBody ApplyBankTypeCiccVo applyBankCard);

    /**
     * 银行卡类型表(bin码)编辑
     *
     * @param applyBankTypeCiccVo the apply bank card
     * @return the response
     */
    @ApiOperation("银行卡类型表(bin码)编辑")
    @PostMapping("applyBankTypeCicc/editApplyBankTypeCicc")
    IResponse editApplyBankTypeCicc(@RequestBody ApplyBankTypeCiccVo applyBankTypeCiccVo);

    /**
     * 银行卡类型表(bin码)查询
     *
     * @param condition the condition
     * @return the response
     */
    @ApiOperation("银行卡类型表(bin码)查询")
    @PostMapping("applyBankTypeCicc/queryApplyBankTypeCicc")
    IResponse queryApplyBankTypeCicc(@RequestBody QueryCondition<ApplyBankTypeCiccVo> condition);

    /**
     * 车辆发票申请表修改
     * @param applyCarInvoice
     * @return
     */
    @ApiOperation("车辆发票申请表修改")
    @PostMapping("/invoice/editInvoice")
    IResponse editApplyCarInvoice(@RequestBody ApplyCarInvoiceVo applyCarInvoice);

    /**
     * 车辆发票申请表删除
     *
     * @param applyCarInvoice the apply car invoice
     * @return response
     */
    @ApiOperation("车辆发票申请表删除")
    @PostMapping("/invoice/deleteInvoice")
    IResponse deleteApplyCarInvoice(@RequestBody ApplyCarInvoiceVo applyCarInvoice);

    /**
     * 车辆发票申请表查询
     *
     * @param condition the condition
     * @return response
     */
    @ApiOperation("车辆发票申请表查询")
    @PostMapping("/invoice/queryInvoice")
    IResponse queryApplyCarInvoice(@RequestBody QueryCondition<ApplyCarInvoiceVo> condition);

    /**
     * 根据合同号获取融资租赁合同承租人签约时间
     * @param contractNo
     * @return
     */
    @PostMapping("/applyAppSign/getSignDateByBusinessNo")
    @ApiOperation("根据合同号获取融资租赁合同承租人签约时间")
    IResponse getSignDateByBusinessNo(@RequestBody String contractNo);

    /**
     * 通过申请编号获取资产变更判断结果
     *
     * @param applyNo the submit or back result dto
     * @param headers               the headers
     * @return response
     */
    @ApiOperation("通过申请编号获取资产变更判断结果")
    @GetMapping("/opr/searchAssertChangeReturnByApplyNo")
    IResponse searchAssertChangeReturnByApplyNo(@RequestParam(value = "applyNo") String applyNo, @RequestHeader Map<String, String> headers);

    /**
     * 放款申请-案件端-取消进件放款
     *
     * @param loanDiscardDTO the submit or back result dto
     * @param headers               the headers
     * @return response
     */
    @ApiOperation("放款申请-案件端-取消进件放款")
    @PostMapping("/loan/caseCancelOrder")
    IResponse caseCancelNew(@RequestBody LoanDiscardDTO loanDiscardDTO, @RequestHeader Map<String, String> headers);


    /**
     * 通过申请编号获取是否人证后置
     * @param applyNo 申请编号
     * @param headers 请求头
     * @return 是否人证后置
     */
    @ApiOperation("通过申请编号获取是否人证后置")
    @PostMapping("/applyOrderInfo/getRetentionOfwitnessesFlag")
    String getRetentionOfwitnessesFlag(@RequestBody String applyNo, @RequestHeader Map<String, String> headers);

    /**
     * 进件合同号查询预审批appNo
     *
     * @param id the submit or back result dto
     * @return response
     */
    @ApiOperation("发起信鸽服务-查询预审批id")
    @PostMapping("/preApprove/getPreIdByAppNo")
    SingleIdVO getPreIdByAppNo(@RequestBody SingleIdVO id);

    /**
     * 查询征信报告
     * @param zxReportCondition zxReportCondition
     * @param headers 请求头
     * @return 征信报告
     */
    @ApiOperation("查询征信报告")
    @PostMapping("/applyReport/zxReportQuery")
    IResponse<Page<ComReportFile>> zxReportQuery(@RequestBody ZxReportCondition zxReportCondition, @RequestHeader Map<String, String> headers);
    /**
     * 查询征信报告详情
     * @param zxReportCondition zxReportCondition
     * @param headers 请求头
     * @return 征信报告
     */
    @ApiOperation("查询征信报告")
    @PostMapping("/applyReport/zxReportQueryDetails")
    IResponse<ComReportFile> zxReportQueryDetails(@RequestBody ZxReportCondition zxReportCondition, @RequestHeader Map<String, String> headers);
    /**
     * 查询预审批id
     * @param zxReportCondition zxReportCondition
     * @param headers 请求头
     * @return 查询预审批id
     */
    @ApiOperation("查询预审批详情")
    @PostMapping("/applyReport/zxReportQueryPreId")
    String zxReportQueryPreId(@RequestBody ZxReportCondition zxReportCondition, @RequestHeader Map<String, String> headers);
    /**
     * 征信查询异常监控
     * @param reportMonitorCondition
     * @return
     */
    @ApiOperation("征信查询异常监控")
    @PostMapping("/zxReport/zxReportMonitor")
    Map<String,Object> zxReportMonitor(@RequestBody ReportMonitorCondition reportMonitorCondition);

    /**
     * 获取附件文件
     * @param attachmentCodeList 附件编码s
     * @param contractNo 合同号
     * @param headers 请求头
     * @return 附件文件
     */
    @ApiOperation("获取附件文件")
    @PostMapping("/applyOrderInfo/getAttachmentFiles")
    IResponse<List<ComAttachmentFile>> getAttachmentFiles(@RequestBody List<String> attachmentCodeList, @RequestParam String contractNo, @RequestHeader Map<String, String> headers);

    /**
     * 保存附件文件
     * @param attachmentFileList 附件s
     * @param applyHeader 请求头
     * @return 附件文件
     */
    @ApiOperation("保存附件文件")
    @PostMapping("/applyOrderInfo/saveAttachmentFiles")
    IResponse saveAttachmentFiles(@RequestBody List<ComAttachmentFile> attachmentFileList, @RequestHeader Map<String, String> applyHeader);

    /**
     * 删除附件文件
     * @param attachmentCodeList 附件编码s
     * @param contractNo 合同号
     * @param headers 请求头
     * @return 附件文件
     */
    @ApiOperation("删除附件文件")
    @PostMapping("/applyOrderInfo/removeAttachmentFiles")
    IResponse removeAttachmentFiles(@RequestBody List<String> attachmentCodeList, @RequestParam String contractNo, @RequestHeader Map<String, String> headers);

    /**
     * 修改征信阈值
     * @param threshold
     * @return
     */
    @ApiOperation("修改征信阈值")
    @PostMapping("/zxReport/editthreshold")
    IResponse editthreshold(@RequestParam("threshold") String threshold);
    /**
     * 征信报告路径
     * @param comReportFile comReportFile
     * @param headers 请求头
     * @return 征信报告路径
     */
    @ApiOperation("查询征信报告")
    @PostMapping("/applyReport/downReport")
    String downReport(@RequestBody ComReportFile comReportFile, @RequestHeader Map<String, String> headers);

    /**
     * 通过预审批id获取订单信息
     * @param preId 预审批id
     * @param headers 请求头
     * @return 订单信息
     */
    @ApiOperation("通过申请编号获取订单信息")
    @PostMapping("/preApprove/getApproveInfoByPreId")
    IResponse<CasePreApplyCustInfoDto> getApproveInfoByPreId(@RequestBody String preId, @RequestHeader Map<String, String> headers);

    /**
     * 通过申请编号查询预审批信息
     * @param applyNo
     * @return
     */
    @ApiOperation("通过申请编号查询预审批信息")
    @PostMapping("/preApprove/findApproveByApplyNo")
    IResponse findApproveByApplyNo(@RequestParam("applyNo") String applyNo);

    /**
     * 通过申请编号修改订单状态
     * @param record
     */
    @ApiOperation("通过申请编号修改订单状态")
    @PostMapping("/contract/updateOrderStatus")
    void updateOrderStatus(@RequestBody CaseApproveRecord record);

    /**
     * 更新备案审批
     * @param id
     * @param status
     * @return
     */
    @ApiOperation("更新备案审批")
    @PostMapping(value = "/affiliated/updateFilingStatus")
    IResponse updateFilingStatus(@RequestParam String id,@RequestParam String status);
    /**
     * 通过申请编号修改订单是否抵押
     * @param applyNo applyNo
     * @param isMortgage  isMortgage
     */
    @ApiOperation("通过申请编号修改订单是否抵押")
    @PostMapping("/applyOrderInfo/updateOrderIsMortgage")
    void updateOrderIsMortgage(@RequestParam("applyNo") String applyNo, @RequestParam("isMortgage") String isMortgage);

    /**
     * 通过申请编号修改订单信息
     * @param applyResultInfoDto  applyResultInfoDto
     */
    @ApiOperation("通过申请编号修改订单信息")
    @PostMapping("/applyOrderInfo/updateOrderInfoByResultInfoDto")
    void updateOrderInfoByResultInfoDto(@RequestBody ApplyResultInfoDto applyResultInfoDto);

}
