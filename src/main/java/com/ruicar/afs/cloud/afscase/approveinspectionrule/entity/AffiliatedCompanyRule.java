package com.ruicar.afs.cloud.afscase.approveinspectionrule.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;
import java.math.BigDecimal;

@Data
@TableName("affiliated_company_rule")
public class AffiliatedCompanyRule extends BaseEntity<AffiliatedCompanyRule> {
    /**
     * 经销商省份
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String channelProvince;
    /**
     * 经销商城市
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String channelCity;
    /**
     * 经销商名称
     */
    private String channelName;
    /**
     * 挂靠公司名称
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String affiliatedName;
    /**
     * 统一社会信用代码
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String socUniCrtCode;
    /**
     * 产品ID
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String productId;
    /**
     * 产品名称
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String productName;
    /**
     * 挂靠台数
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String attachmentNum;
    /**
     * 融资金额
     */
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private BigDecimal financingAmount;
    /**
     * 规则状态
     */
    private String ruleState;

    @TableField(exist = false)
    private String[] addressinfo;

    @TableField(exist = false)
    private String channelProvinceName;

    @TableField(exist = false)
    private String channelCityName;

    @TableField(exist = false)
    private String[] productIds;
}
