package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRule;

import java.util.List;

public interface WriteOffPayRuleService extends IService<WriteOffPayRule> {

    /**
     * 生成服务费支付账单
     *
     * @param batchNo
     * @param baseInfoList
     */
    void createPayBill(String batchNo, List<WriteOffBaseInfo> baseInfoList);
}
