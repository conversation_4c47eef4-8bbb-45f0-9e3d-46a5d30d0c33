package com.ruicar.afs.cloud.afscase.infomanagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.approvetask.condition.WorkTaskErrorCondition;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPoolHistory;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolHistoryService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.approvetask.vo.ChannelRiskVo;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.PropertyLicenseAiRecognition;
import com.ruicar.afs.cloud.afscase.autoaudit.service.PropertyLicenseAiRecognitionService;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseBusinessLicenseInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekDrivingInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekOperatorInfo;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseBusinessLicenseInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekDrivingInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekOperatorInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAuthorizeRegion;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.mapper.ChannelAuthorizeRegionMapper;
import com.ruicar.afs.cloud.afscase.channel.mapper.ChannelBusiInfoMapper;
import com.ruicar.afs.cloud.afscase.channel.mapper.ChannelRiskInfoMapper;
import com.ruicar.afs.cloud.afscase.common.service.AggregateService;
import com.ruicar.afs.cloud.afscase.common.step.StepParam;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.ApproveAssetsChangeInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBusinessLicense;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarStyleDetail;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseFacePhotoInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.SealRecognitionInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.enums.DeepSeekFileType;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CarStyleDetailInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseBusinessLicenseMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseCarInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseChannelInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseContractInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseCostInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.ApproveAssetsChangeService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBusinessLicenseService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.FinCostDetailsService;
import com.ruicar.afs.cloud.afscase.workflow.enums.WorkflowNodeEnum;
import com.ruicar.afs.cloud.afscase.workflow.mapper.WorkflowTaskInfoMapper;
import com.ruicar.afs.cloud.aggregate.dto.CertNoDto;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AddressTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ConfCaseParamTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsAssignedEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TopEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.apply.dto.ApplyAffiliatedUnitDto;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustIndividualDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustInfoDto;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.dto.AddrQueryDto;
import com.ruicar.afs.cloud.config.api.address.feign.AddressFeign;
import com.ruicar.afs.cloud.deepseek.config.DeepSeekProperties;
import com.ruicar.afs.cloud.deepseek.dto.AttorneyAutomaticRecognitionDto;
import com.ruicar.afs.cloud.deepseek.dto.CertificateAutomaticRecognitionDto;
import com.ruicar.afs.cloud.deepseek.entity.AttorneyAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.BusinessAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.CertificateAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.StatementAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.enums.CheckTypeEnum;
import com.ruicar.afs.cloud.deepseek.enums.IntelligentStatusEnum;
import com.ruicar.afs.cloud.deepseek.service.AttorneyAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.BusinessAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.CertificateAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.DeepseekIntelligentResultsService;
import com.ruicar.afs.cloud.deepseek.service.StatementAutomaticRecognitionService;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.filecenter.FileCenterHelper;
import com.ruicar.afs.cloud.filecenter.FileType;
import com.ruicar.afs.cloud.image.config.FileProperties;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.enums.FileTypeEnum;
import com.ruicar.afs.cloud.image.feign.FileCenterFeign;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.interfaces.ocr.dto.OcrReqData;
import com.ruicar.afs.cloud.interfaces.ocr.dto.OcrResData;
import com.ruicar.afs.cloud.interfaces.ocr.enums.OCRType;
import com.ruicar.afs.cloud.interfaces.ocr.service.OcrService;
import com.ruicar.afs.cloud.interfaces.wlease.ocr.dto.SinoOcrResp;
import com.ruicar.afs.cloud.interfaces.wlease.ocr.dto.TencentBizLicenseResp;
import com.ruicar.afs.cloud.interfaces.wlease.ocr.dto.TencentOcrCommonResp;
import com.ruicar.afs.cloud.parameter.commom.enums.CustRelationEnums;
import com.ruicar.afs.cloud.seats.entity.RegularValue;
import com.ruicar.afs.cloud.seats.service.RegularValueService;
import com.ruicar.afs.cloud.third.system.invoke.dto.Request;
import com.ruicar.afs.cloud.third.system.invoke.dto.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-13 15:20
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class CaseBaseInfoServiceImpl extends ServiceImpl<CaseBaseInfoMapper, CaseBaseInfo> implements CaseBaseInfoService {

    private ApproveAssetsChangeService approveAssetsChangeService;
    private WorkTaskPoolService workTaskPoolService;
    private WorkTaskPoolHistoryService workTaskPoolHistoryService;
    private CaseCustInfoService caseCustInfoService;
    private AggregateService aggregateService;
    private WorkflowTaskInfoMapper workflowTaskInfoMapper;
    private CaseChannelInfoMapper caseChannelInfoMapper;
    private ChannelBusiInfoMapper channelBusiInfoMapper;
    private ChannelAuthorizeRegionMapper channelAuthorizeRegionMapper;
    private ChannelRiskInfoMapper channelRiskInfoMapper;
    private CaseCarInfoMapper caseCarInfoMapper;
    private CarStyleDetailInfoMapper carStyleDetailInfoMapper;
    private CaseCostInfoMapper caseCostInfoMapper;
    private CaseContractInfoMapper caseContractInfoMapper;
    @Autowired
    private RegularValueService valueService;
    private ComAttachmentManagementService attachmentManagementService;
    private ComAttachmentFileService comAttachmentFileService;
    private DeepSeekProperties deepSeekProperties;
    private DeepseekIntelligentResultsService deepseekIntelligentResultsService;
    private StatementAutomaticRecognitionService statementAutomaticRecognitionService;
    private FileProperties fileProperties;
    private ApplyContractFeign applyContractFeign;
    private final ApplyConfig applyConfig;
    private final CaseFacePhotoInfoService facePhotoInfoService;
    private BusinessAutomaticRecognitionService businessAutomaticRecognitionService;
    private final DeepSeekDrivingInfoService deepSeekDrivingInfoService;
    private final DeepSeekOperatorInfoService deepSeekOperatorInfoService;
    private final FileCenterFeign fileCenterFeign;
    private final PropertyLicenseAiRecognitionService propertyLicenseAiRecognitionService;
    private final OcrService ocrService;
    private final CaseBusinessLicenseService caseBusinessLicenseService;
    private final StringRedisTemplate stringRedisTemplate;
    private final CaseCustAddressService caseCustAddressService;
    private final AddressFeign addressFeign;
    private final CaseBusinessLicenseInfoService caseBusinessLicenseInfoService;
    private final CaseBusinessLicenseMapper caseBusinessLicenseMapper;
    private final CaseCustContactService caseCustContactService;
    private CaseConfParamService caseConfParamService;
    private AttorneyAutomaticRecognitionService attorneyAutomaticRecognitionService;
    private CertificateAutomaticRecognitionService certificateAutomaticRecognitionService;
    private final FinCostDetailsService finCostDetailsService;

    private static final long MB_20 = 20L * 1024 * 1024;
    /**
     * 影像类型判断
     */
    private final List<String> FILE_TYPE= Arrays.asList("jpg","png","jpeg","pdf","PDF");

    private static final List<String> INVOICE_KEY_LIST = List.of("发票名称", "购方名称", "货物名称", "金额合计", "发票监制章", "销售方章");
    private static final String EXEMPT_MARRIAGE  = "exemptMarriage";
    private static final String FILE_TYPE_JUDGE  = "fileTypeJudge";
    private static final String ADDR_LEVEL_PROVINCE = "1";
    private static final String ADDR_LEVEL_CITY     = "2";
    private static final String ADDR_LEVEL_COUNTY   = "3";
    private static final float DEFAULT_SCALE = 0.8f;
    private static final int DEFAULT_SIZE = 7;

    @Override
    public CaseBaseInfo getCaseByApplyNo(String applyNo){
        return this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,applyNo),false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public CaseBaseInfo getCaseBaseInfoByApplyNoInTran(String applyNo) {
        return this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,applyNo),false);
    }

    @Override
    public boolean checkNormalFlowEnd(String applyNo,String stageId) {

        BusinessStateInEnum caseStatusEnum = null;

        //资产变更的案件状态取资产变更表中的案件状态
        List<WorkTaskPoolHistory> workTaskPoolHisList = workTaskPoolHistoryService.list(Wrappers.<WorkTaskPoolHistory>lambdaQuery()
                .eq(WorkTaskPoolHistory::getApplyNo, applyNo)
                .eq(WorkTaskPoolHistory::getStageId, stageId)
                .orderByDesc(WorkTaskPoolHistory::getCreateTime));
        if (CollectionUtils.isNotEmpty(workTaskPoolHisList)) {
            WorkTaskPoolHistory workTaskPoolHistory = workTaskPoolHisList.get(0);
            if (ProcessTypeEnum.CHANGE_ASSETS.getCode().equals(workTaskPoolHistory.getProcessType())) {
                ApproveAssetsChangeInfo changeInfo = approveAssetsChangeService.getOne(Wrappers.<ApproveAssetsChangeInfo>lambdaQuery()
                        .eq(ApproveAssetsChangeInfo::getApplyNo,applyNo)
                        .eq(ApproveAssetsChangeInfo::getStageId,stageId));
                if (ObjectUtils.isNotEmpty(changeInfo)) {
                    caseStatusEnum = (BusinessStateInEnum) AfsEnumUtil.getEnum(changeInfo.getStatus(),BusinessStateInEnum.class);
                }
            }else {
                CaseBaseInfo baseInfo = Optional.ofNullable(getOne(
                        Wrappers.<CaseBaseInfo>lambdaQuery()
                                .eq(CaseBaseInfo::getApplyNo,applyNo)
                )).orElse(new CaseBaseInfo());
                caseStatusEnum = Objects.isNull(baseInfo.getBusinessStateIn())
                        ? null
                        : (BusinessStateInEnum) AfsEnumUtil.getEnum(baseInfo.getBusinessStateIn(),BusinessStateInEnum.class);
            }
        }
        Set<BusinessStateInEnum> resultBusinessStatus = new HashSet<>();
        resultBusinessStatus.add(BusinessStateInEnum.REPEAL);
        resultBusinessStatus.add(BusinessStateInEnum.REJECT);
        resultBusinessStatus.add(BusinessStateInEnum.APPROVED);
        resultBusinessStatus.add(BusinessStateInEnum.CONDITIONAL_APPROVE);
        resultBusinessStatus.add(BusinessStateInEnum.CANCEL_ADVICE_CONDITIONAL);
        return resultBusinessStatus.contains(caseStatusEnum);
    }

    @Override
    public boolean checkNormalFlowEnd(String applyNo) {
        CaseBaseInfo baseInfo = Optional.ofNullable(getOne(
                Wrappers.<CaseBaseInfo>lambdaQuery()
                        .eq(CaseBaseInfo::getApplyNo,applyNo)
        )).orElse(new CaseBaseInfo());
        BusinessStateInEnum caseStatusEnum = Objects.isNull(baseInfo.getBusinessStateIn())
                ? null
                : (BusinessStateInEnum) AfsEnumUtil.getEnum(baseInfo.getBusinessStateIn(),BusinessStateInEnum.class);
        Set<BusinessStateInEnum> resultBusinessStatus = new HashSet<>();
        resultBusinessStatus.add(BusinessStateInEnum.REPEAL);
        resultBusinessStatus.add(BusinessStateInEnum.REJECT);
        resultBusinessStatus.add(BusinessStateInEnum.APPROVED);
        resultBusinessStatus.add(BusinessStateInEnum.CONDITIONAL_APPROVE);
        resultBusinessStatus.add(BusinessStateInEnum.CANCEL_ADVICE_CONDITIONAL);
        resultBusinessStatus.add(BusinessStateInEnum.CANCEL);
        return resultBusinessStatus.contains(caseStatusEnum);
    }

    @Override
    public boolean updateAddAmtFromApi(String applyNo) {
        try {
            Assert.isTrue(StrUtil.isNotBlank(applyNo), "累加贷额更新 : 申请编号不可为空");
            CaseBaseInfo caseBaseInfo = this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));
            Assert.notNull(caseBaseInfo, String.format("累加贷额更新：%s 当前案件不存在", applyNo));
            List<String> customerRoleList = new ArrayList<>();
            customerRoleList.add(CustRoleEnum.MIANCUST.getCode());
            customerRoleList.add(CustRoleEnum.COMMONCSUT.getCode());
            Map<String, String> customerRoleToCertNoMap = caseCustInfoService.list(
                    Wrappers.<CaseCustInfo>lambdaQuery()
                            .eq(CaseCustInfo::getApplyNo, applyNo)
                            .in(CaseCustInfo::getCustRole, customerRoleList)
            ).stream().collect(Collectors.toMap(CaseCustInfo::getCustRole, CaseCustInfo::getCertNo));

            CertNoDto queryDto = new CertNoDto();
            queryDto.setApplicationNo(applyNo);
            queryDto.setBorrowerCertno(customerRoleToCertNoMap.get(CustRoleEnum.MIANCUST.getCode()));
            queryDto.setCoborrowerCertno(customerRoleToCertNoMap.get(CustRoleEnum.COMMONCSUT.getCode()));
            BigDecimal addAmt = aggregateService.getLoanhistory(queryDto);
            log.info("累加贷额更新: {},原累加贷额:{},当前获取到的累加贷额：{}", applyNo, caseBaseInfo.getTotalLoanAmt(), addAmt);
            // 检查当前案件状态是否为拒绝或者取消附条件核准
            if (Objects.nonNull(addAmt) && addAmt.compareTo(Convert.toBigDecimal(caseBaseInfo.getTotalLoanAmt(), BigDecimal.ZERO)) != 0) {
                // 获取到的累加贷额为0时，将当前单贷款金额设置为当前案件的累加贷额
                if(addAmt.compareTo(BigDecimal.ZERO) == 0){
                    caseBaseInfo.setTotalLoanAmt(caseBaseInfo.getLoanAmtRepeat());
                    this.updateById(caseBaseInfo);
                }else {
                    caseBaseInfo.setTotalLoanAmt(addAmt);
                    this.updateById(caseBaseInfo);
                }
            }
            return true;
        } catch (Exception e) {
            log.warn("累加贷额更新失败: {}", applyNo, e);
            return false;
        }
    }

    /**
     * 锁定放款带提交案件
     * @param applyNo
     * @param reason
     * @param isLock
     */
    @Override
    public void setLoanLock(String applyNo,String reason,String isLock){
        CaseBaseInfo caseBaseInfo=this.getCaseByApplyNo(applyNo);
        caseBaseInfo.setLoanSubmitLock(isLock);
        caseBaseInfo.setLoanLockReason(reason);
        this.updateById(caseBaseInfo);
    }

    @Override
    public ChannelRiskVo getChannelRiskInfo(String businessType) {
        return baseMapper.getChannelRiskInfo(businessType);
    }



    @Override
    public void suspendCase(String applyNo) {
        CaseBaseInfo caseBaseInfo=this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,applyNo));
        caseBaseInfo.setSuspendStatus(WhetherEnum.YES.getCode());
        caseBaseInfo.setSuspendStart(new Date());
        if (ObjectUtils.isNull(caseBaseInfo.getSuspendTimes())) {
            caseBaseInfo.setSuspendTimes("once");//记录是否首次挂起
        } else {
            caseBaseInfo.setSuspendTimes("again");//记录是否多次挂起
        }
        this.updateById(caseBaseInfo);
    }

    @Override
    public void cancelSuspendCase(String applyNo) {
        CaseBaseInfo caseBaseInfo=this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,applyNo));
        caseBaseInfo.setSuspendStatus(WhetherEnum.NO.getCode());
        caseBaseInfo.setSuspendEnd(new Date());
        this.updateById(caseBaseInfo);
    }

    @Override
    public String queryHasHiredDriverByApplyNo(String applyNo) throws AfsBaseException {
        CaseBaseInfo caseBaseInfo = this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo), false);
        if(caseBaseInfo == null){
            String message = "未查询到案件基本信息";
            throw new AfsBaseException(message);
        }
        // 历史数据会有为null的情况，所以需要在这里处理成默认值：0（否）
        if(caseBaseInfo.getHasHiredDriver() == null){
            return "0";
        }
        return caseBaseInfo.getHasHiredDriver();
    }

    @Override
    public List<CaseBaseInfo> selectCaseBaseListByCertNo(String certNo, String carType){
        List<CaseBaseInfo> list = this.baseMapper.selectCaseBaseListByCertNo(certNo, carType);
        return list;
    }

    @Override
    public List<CaseBaseInfo> selectCaseBaseListBySocUniCrtCode(String socUniCrtCode, String carType) {
        List<CaseBaseInfo> list = this.baseMapper.selectCaseBaseListBySocUniCrtCode(socUniCrtCode, carType);
        return list;
    }

    @Override
    public List<CaseBaseInfo> queryCaseBaseInfoByUserDefinedIndex(String userDefinedIndex,Integer limit,String paramer) throws AfsBaseException {
        return this.baseMapper.queryCaseBaseInfoByUserDefinedIndex(userDefinedIndex,limit,paramer);
    }

    @Override
    public IPage<CaseBaseInfo> queryCaseInfoTaskErrorList(WorkTaskErrorCondition condition) throws AfsBaseException {
        List<String> applyNos = new ArrayList<>();
        if(StringUtil.isNotEmpty(condition.getNumb()) || StringUtil.isNotEmpty(condition.getTimeUnit())){
            if(StringUtil.isEmpty(condition.getNumb())){
                condition.setNumb("1");
            }
            if(StringUtil.isEmpty(condition.getTimeUnit())){
                condition.setTimeUnit("2");
            }
            List<String> list = workflowTaskInfoMapper.queryApproveUnallocated("1",AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR),
                    condition.getNumb(),condition.getTimeUnit());
            if(list.size() > 0){
                applyNos.addAll(list);
            }
        }else{
            List<String> list = this.baseMapper.queryCaseInfoTaskErrorList(condition);
            applyNos.addAll(list);
            if(StringUtil.isNotEmpty(condition.getApplyNo())){
                if(applyNos.contains(condition.getApplyNo())){
                    applyNos.clear();
                    applyNos.add(condition.getApplyNo());
                }else{
                    applyNos.clear();
                }
            }
        }
        //防止applyNos是为空
        applyNos.add("BL-AP");
        IPage<CaseBaseInfo> list = this.baseMapper.selectPage(new Page<>(condition.getPageNumber(), condition.getPageSize()),Wrappers.<CaseBaseInfo>query().lambda()
                .in(CaseBaseInfo::getApplyNo, applyNos)
                .like(StringUtil.isNotEmpty(condition.getCustName()),CaseBaseInfo::getCustNameRepeat,condition.getCustName())
                .orderByAsc(CaseBaseInfo::getCreateTime));
        return list;
    }

    @Override
    public List<CaseBaseInfo> queryCaseBaseInfoReconsider() throws AfsBaseException {
        List<CaseBaseInfo> list = this.baseMapper.queryCaseBaseInfoReconsider();
        return list;
    }

    @Override
    public void setUpTopCase(StepParam stepParam) {
        CaseBaseInfo baseInfo = this.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo,stepParam.getApplyNo()),false);
        if(StringUtil.isEmpty(baseInfo.getIsStick())){
            baseInfo.setIsStick(TopEnum.NO.getCode());
        }
        List valuesList = valueService.list(Wrappers.<RegularValue>lambdaQuery().eq(RegularValue::getRuleType, "approveTop")
                .eq(RegularValue::getRuleState,"2"));
        if(valuesList.size() == 0){
            this.updateById(baseInfo);
            log.info("信审置顶规则没有生效或者创建:{}",stepParam.getApplyNo());
            return;
        }
        if (ObjectUtil.isNotNull(baseInfo)) {
            JSONObject jsonObject = new JSONObject();
            /**业务类型*/
            jsonObject.put("businessType", baseInfo.getBusinessType());
            /**车辆类型*/
            jsonObject.put("carType", baseInfo.getCarType());
            /**车辆属性*/
            jsonObject.put("carNature", baseInfo.getCarNature());
            /**运营方式*/
            jsonObject.put("operateWay", baseInfo.getOperateWay());
            /**挂靠方式*/
            jsonObject.put("affiliatedWay", baseInfo.getAffiliatedWay());
            /**贷款总额*/
            jsonObject.put("loanAmount", baseInfo.getLoanAmtRepeat());
            /**金融产品*/
            jsonObject.put("productId", String.valueOf(baseInfo.getProductId()));
            /**是否加急*/
            String isUrgent =  WhetherEnum.NO.getCode();
            if(StringUtils.isNotEmpty(stepParam.getIsUrgent())){
                isUrgent = stepParam.getIsUrgent();
            }
            jsonObject.put("isUrgent", isUrgent);
            CaseChannelInfo caseChannelInfo = caseChannelInfoMapper.selectOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, baseInfo.getApplyNo()));
            if (ObjectUtil.isNotNull(caseChannelInfo)) {
                /**经销商名称*/
                jsonObject.put("dealerName", caseChannelInfo.getDealerName());
                ChannelBaseInfo channelBaseInfo = channelBusiInfoMapper.selectOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
                if (ObjectUtil.isNotNull(channelBaseInfo)) {
                    /**经销商省份*/
                    jsonObject.put("channelProvince", channelBaseInfo.getChannelProvince());
                    /**经销商城市*/
                    jsonObject.put("channelCity", channelBaseInfo.getChannelCity());
                    /**渠道归属*/
                    jsonObject.put("channelBelong", channelBaseInfo.getChannelBelong());
                    List<ChannelAuthorizeRegion> list = channelAuthorizeRegionMapper.selectList(Wrappers.<ChannelAuthorizeRegion>query().lambda().eq(ChannelAuthorizeRegion::getChannelId, channelBaseInfo.getChannelId()));
                    if (list != null && !list.isEmpty()) {
                        StringBuilder practicesProvince = new StringBuilder();
                        StringBuilder practicesCity = new StringBuilder();
                        for (ChannelAuthorizeRegion region : list) {
                            if (IsAssignedEnum.YES.getCode().equals(region.getParentId())
                                    && IsAssignedEnum.YES.getCode().equals(region.getIsParent())) {
                                practicesProvince.append(region.getCode()).append(",");
                            }
                            if (IsAssignedEnum.NO.getCode().equals(region.getIsParent())) {
                                practicesCity.append(region.getCode())
                                        .append(",");
                            }
                        }
                        /** 展业省份 */
                        jsonObject.put("practicesProvince",
                                !practicesProvince.isEmpty()?practicesProvince.substring(0, practicesProvince.length() - 1):"");
                        /** 展业城市 */
                        jsonObject.put("practicesCity", !practicesCity.isEmpty()?practicesCity.substring(0, practicesCity.length() - 1):"");
                    }
                    ChannelRiskInfo channelRiskInfo = channelRiskInfoMapper.selectOne(Wrappers.<ChannelRiskInfo>query().lambda()
                            .eq(StringUtils.isNotBlank(caseChannelInfo.getDealerNo()), ChannelRiskInfo::getChannelId, channelBaseInfo.getChannelId())
                            .eq(StringUtils.isNotBlank(channelBaseInfo.getBusinessType()), ChannelRiskInfo::getBusinessType, baseInfo.getBusinessType()));
                    /**优质登记*/
                    if (ObjectUtils.isNotEmpty(channelRiskInfo)) {
                        jsonObject.put("qualityGrade", channelRiskInfo.getQualityGrade());
                    }
                }
            }

            CaseCarInfo caseCarInfo = caseCarInfoMapper.selectOne(Wrappers.<CaseCarInfo>query().lambda().eq(CaseCarInfo::getApplyNo, baseInfo.getApplyNo()));
            if (ObjectUtil.isNotNull(caseCarInfo)) {
                /**车辆级别*/
                jsonObject.put("carBodyClass", caseCarInfo.getCarBodyClass());
                /**车型*/
                jsonObject.put("modelCode", caseCarInfo.getModelCode());
                /**购车地省份*/
                jsonObject.put("purchaseProvince", caseCarInfo.getPurchaseCity());
                /**购车地城市*/
                jsonObject.put("purchaseCity", caseCarInfo.getPurchaseCity());
                /**上牌地省份*/
                jsonObject.put("licenseProvince", caseCarInfo.getLicenseProvince());
                /**上牌地城市*/
                jsonObject.put("licenseCity", caseCarInfo.getLicenseCity());
                /**车辆品牌*/
                jsonObject.put("brandCode",caseCarInfo.getBrandCode());
                /**车系*/
                jsonObject.put("seriesName",caseCarInfo.getSeriesName());

                List<CaseCarStyleDetail> cartyleDetailInfo = carStyleDetailInfoMapper.selectList(Wrappers.<CaseCarStyleDetail>query().lambda().eq(CaseCarStyleDetail::getCarId, caseCarInfo.getId()));
                if (cartyleDetailInfo != null && !cartyleDetailInfo.isEmpty()) {
                    /**是否新能源*/
                    jsonObject.put("isGreen", cartyleDetailInfo.get(0).getIsGreen());
                    /** 资产主类*/
                    jsonObject.put("assetsClass", cartyleDetailInfo.get(0).getCarType());
                    /** 资产子类*/
                    jsonObject.put("carTypeDetail", cartyleDetailInfo.get(0).getCarTypeDetail());
                }
            }
            /**是否网约车*/
            if (StringUtil.isNotEmpty(baseInfo.getAffiliatedWay()) && AffiliatedWayEnum.NETWORK_CAR_AFFILIATED.getCode().equals(baseInfo.getAffiliatedWay())) {
                jsonObject.put("onlineCar", WhetherEnum.YES.getCode());
            } else {
                jsonObject.put("onlineCar", WhetherEnum.NO.getCode());
            }
            /**附加贷金额*/
            FinCostDetails carAmtCostInfoAdd = caseCostInfoMapper.selectOne(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo,  baseInfo.getApplyNo()).eq(FinCostDetails::getCostType, CostTypeEnum.AFFIXAMT.getCode()));
            if (ObjectUtil.isNotNull(carAmtCostInfoAdd)) {
                jsonObject.put("loanAmt", carAmtCostInfoAdd.getLoanAmt());
            }
            /**反欺诈评分*/
            jsonObject.put("antiFraudGrade", 100);
            /**决策引擎评分*/
            jsonObject.put("decisionGrade", 100);
            BigDecimal contAmt = BigDecimal.ZERO;
            BigDecimal tailPayAmt = BigDecimal.ZERO;
            BigDecimal subsidyTotalAmt = BigDecimal.ZERO;
            FinCostDetails carAmtCostInfoCar = caseCostInfoMapper.selectOne(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, baseInfo.getApplyNo()).eq(FinCostDetails::getCostType, CostTypeEnum.CARAMT.getCode()));
            if (ObjectUtil.isNotNull(carAmtCostInfoCar)) {
                /**首付比例*/
                BigDecimal downPayScale = carAmtCostInfoCar.getDownPayScale();
                jsonObject.put("downPayScale", downPayScale);
                /**还款方式*/
                jsonObject.put("repayMode", carAmtCostInfoCar.getAlgorithmType());
                contAmt = contAmt.add(Convert.toBigDecimal(carAmtCostInfoCar.getLoanAmt(), BigDecimal.ZERO));
                tailPayAmt = contAmt.add(Convert.toBigDecimal(carAmtCostInfoCar.getTailPayAmt(), BigDecimal.ZERO));
                subsidyTotalAmt = contAmt.add(Convert.toBigDecimal(carAmtCostInfoCar.getDiscountAmt(), BigDecimal.ZERO));
            }
            /**贴息金额*/
            jsonObject.put("tailPayAmt", tailPayAmt);
            /**尾款金额*/
            jsonObject.put("subsidyTotalAmt", subsidyTotalAmt);
            try {
                log.info("分单前置顶规则jsonObject: {}", jsonObject);
                //调用置顶规则
                RuleRunResult result = RuleHelper.runRule(jsonObject, "approveTop", true, RuleRunEnum.SERIAL);
                log.info("分单前置顶规则result: {}", result);
                if(result.getHit()){
                    baseInfo.setIsStick(TopEnum.YES.getCode());
                    baseInfo.setStickTime(DateUtil.date());
                    baseInfo.setIsUrgent(TopEnum.YES.getCode());
                    baseInfo.setUrgentTime(DateUtil.date());
                }
            }catch (Exception e){
                log.error("{}:置顶规则处理失败:{}", stepParam.getApplyNo(),e);
            }
            this.updateById(baseInfo);
        }
    }

    /**
     * 查询案件订单信息
     * @return
     */
    @Override
    public IResponse<List<?>> syncCaseBaseInfoData() {
        //获取客户的客户姓名，车系、LTV首付比例、融资期限
        List<String> contractStatus= new ArrayList<>();
        contractStatus.add(ContractStatusEnum.contractEffective.key());
        contractStatus.add(ContractStatusEnum.advanceSettle.key());
        return IResponse.success(caseContractInfoMapper.syncCaseBaseInfoData(contractStatus));
    }

    @Async("dSTaskExecutor")
    @Override
    public void facePhotoAutomaticRecognition(String applyNo, ComAttachmentFile comAttachmentFile, String prompt) {
        log.info("进件提交开始，进行ds面签照自动识别，申请编号：{},文件id：{}", applyNo, comAttachmentFile.getId());
        JSONArray array = generateJson(comAttachmentFile, applyNo,false);
        if (array.size() > 0) {
            CaseFacePhotoInfo one = facePhotoInfoService.lambdaQuery()
                .eq(CaseFacePhotoInfo::getApplyNo, applyNo)
                .eq(CaseFacePhotoInfo::getType, "ds")
                .eq(CaseFacePhotoInfo::getFileId, String.valueOf(comAttachmentFile.getId()))
                .one();
            CaseFacePhotoInfo face = new CaseFacePhotoInfo();
            face.setApplyNo(applyNo);
            face.setType("ds");
            face.setFileId(comAttachmentFile.getId().toString());
            face.setIdentifyDate(new Date());
            face.setCreateBy("dsExecutor");
            face.setUpdateBy("dsExecutor");
            face.setPhotoDateTime("");
            face.setPlace("");
            face.setCity("");
            if(one != null){
                face.setId(one.getId());
            }
            try {
                String facePreface = deepSeekProperties.getFacePreface();
                String result = deepseekIntelligentResultsService.callDsRead(StrUtil.isNotBlank(prompt)?prompt:facePreface, array.toString());
                log.info("进行ds面签照自动识别结果{}", result);
                if (StrUtil.isNotBlank(result)) {
                    String s = extractPureJson(result);
                    JSONObject json = getJson(s);
                    if (json != null) {
                        face.setResult(result);
                        face.setDsResult(Boolean.TRUE);
                        face.setPhotoDateTime(json.getString("datetime"));
                        face.setPlace(json.getString("place"));
                        face.setCity(json.getString("city"));
                        log.info("进行ds面签照自动识别success{}", applyNo);
                        if (StrUtil.isBlank(face.getPhotoDateTime()) || StrUtil.isBlank(face.getPlace())
                            || StrUtil.isBlank(face.getCity())) {
                            face.setDsResult(Boolean.FALSE);
                            log.info("进行ds面签照自动识别为空{}", applyNo);
                        }
                    } else {
                        face.setDsResult(Boolean.FALSE);
                        face.setResult(result);
                        log.info("进行ds面签照自动识别失败{}", applyNo);
                    }
                } else {
                    face.setDsResult(Boolean.FALSE);
                    log.info("进行ds面签照自动识别fail{}", applyNo);
                }
                facePhotoInfoService.saveOrUpdate(face);
            } catch (Exception e) {
                face.setDsResult(Boolean.FALSE);
                facePhotoInfoService.saveOrUpdate(face);
                log.error("进行ds面签照自动识别失败{}", applyNo, e);
            }
        }
    }

    public static JSONObject getJson(String json) {
        try {
            return JSONObject.parseObject(json);
        } catch (Exception e) {
            return null;
        }
    }

    @Async("dSTaskExecutor")
    @Override
    public void turnoverAutomaticRecognition(String applyNo,CaseBaseInfo caseBaseInfo,CaseCustInfo custInfo,ComAttachmentFile comAttachmentFile,String bankCardNo,CaseCustInfo custInfo1) {
        log.info("进件提交开始，进行流水自动识别，申请编号：{},文件id：{}",applyNo,comAttachmentFile.getId());
        try{
            StringBuilder sb = new StringBuilder();
            List<DicDataDto> dtos = DicHelper.getDicMaps("flowVerification").get("flowVerification");
            for (DicDataDto dto : dtos){
                sb.append(dto.getValue());
            }
            // 下载文件
            String savePath = null;
            String result = null;
            String content = deepSeekProperties.getReadPreface() + sb;
            if (AfsEnumUtil.desc(AfsEnumUtil.getEnum(FileTypeEnum.image.getCode(), FileTypeEnum.class)).toLowerCase().contains(comAttachmentFile.getFileType())){
                JSONArray jsonArray = generateJson(comAttachmentFile,applyNo,false);
                result = deepseekIntelligentResultsService.callDsRead(content,jsonArray.toString());
                savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + "." + comAttachmentFile.getFileType();
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000) // 设置超时时间20秒
                        .execute()
                        .writeBody(FileUtil.file(savePath));
            }else {
                savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".pdf";
                String decryptPath = fileProperties.getTempDir() + "new" + comAttachmentFile.getFileId() + ".pdf";
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000) // 设置超时时间20秒
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                if (StrUtil.isNotBlank(comAttachmentFile.getSecretCode())){
                    decryptLargePDF(savePath,decryptPath,comAttachmentFile.getSecretCode());
                    FileUtil.del(savePath);
                    savePath = decryptPath;
                }
                int pages = Integer.valueOf(deepSeekProperties.getPages());
                if (isPdfPageCountGreaterThan50(savePath,pages)){
                    String newPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + comAttachmentFile.getFileId() + ".pdf";
                    cutPdfTo50Pages(savePath,newPath,pages);
                    result = deepseekIntelligentResultsService.callDsImags(content,new File(newPath));
                } else {
                    //获取文件url
                    result = deepseekIntelligentResultsService.callDsImags(content,new File(savePath));
                }
            }
            int seal = 0;
            if (StrUtil.isNotBlank(result)){
                String pureJson = extractPureJson(result);
                //判断是否需要做印章校验
                if (StrUtil.equals(comAttachmentFile.getFileType(),"jpg") || StrUtil.equals(comAttachmentFile.getFileType(),"png")
                        || StrUtil.equals(comAttachmentFile.getFileType(),"jpeg") || StrUtil.equals(comAttachmentFile.getFileType(),"pdf")
                        || StrUtil.equals(comAttachmentFile.getFileType(),"PDF")){
                    try {
                        JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getId()),applyNo,FileUtil.file(savePath));
                        if (ObjectUtil.isNotEmpty(jsonObject)){
                            boolean falg = jsonObject.getBoolean("error");
                            JSONArray array = jsonObject.getJSONArray("seal_result");
                            boolean isExist = areAllDetectBoxesNotEmpty(array);
                            if (!falg && isExist){
                                seal = 1;
                            }
                        }
                    }catch (Exception e){
                        log.error("印章识别失败:{}",e);
                    }finally {
                        FileUtil.del(savePath);
                    }
                }
                //系统判断DP返回信息
                StatementAutomaticRecognition statementAutomaticRecognition = new StatementAutomaticRecognition();
                String resultJson = makeData(pureJson,seal,statementAutomaticRecognition,caseBaseInfo,custInfo,bankCardNo,comAttachmentFile.getSecretCode(),custInfo1);
                StatementAutomaticRecognition recognition = statementAutomaticRecognitionService.getOne(Wrappers.<StatementAutomaticRecognition>lambdaQuery()
                        .eq(StatementAutomaticRecognition::getApplyNo,applyNo)
                        .eq(StatementAutomaticRecognition::getCheckType,CheckTypeEnum.STATEMENT.getCode())
                        .eq(StatementAutomaticRecognition::getFileId,String.valueOf(comAttachmentFile.getId())));
                if (ObjectUtil.isNotEmpty(recognition)){
                    statementAutomaticRecognition.setId(recognition.getId());
                }
                statementAutomaticRecognition.setApplyNo(applyNo);
                statementAutomaticRecognition.setFileType(comAttachmentFile.getFileType());
                statementAutomaticRecognition.setStatus("0");
                statementAutomaticRecognition.setCheckType(CheckTypeEnum.STATEMENT.getCode());
                statementAutomaticRecognition.setResult(resultJson);
                statementAutomaticRecognition.setParam(content);
                statementAutomaticRecognition.setAutoTime(new Date());
                statementAutomaticRecognition.setFileId(String.valueOf(comAttachmentFile.getId()));
                statementAutomaticRecognitionService.saveOrUpdate(statementAutomaticRecognition);
            }else {
                log.info("deepseek流水自动识别失败");
            }
        }catch (Exception e){
            log.error("deepseek流水自动识别失败:{}",e);
        }
    }

    public static boolean decryptLargePDF(String inputPath,
                                          String outputPath,
                                          String password) {
        try {
            // 使用内存映射方式处理大文件
            PDDocument document = PDDocument.load(
                    new File(inputPath),
                    password,
                    MemoryUsageSetting.setupTempFileOnly()
            );

            if (!document.isEncrypted()) {
                document.close();
                return false;
            }

            document.setAllSecurityToBeRemoved(true);
            document.save(outputPath);
            document.close();

            return true;
        } catch (IOException e) {
            log.error("解密大文件出错: {}",e);
            return false;
        }
    }

    public static boolean isPdfPageCountGreaterThan50(String filePath,int pages) {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            int pageCount = document.getNumberOfPages();
            return pageCount > pages;
        } catch (IOException e) {
            log.error("读取 PDF 文件失败:{}",e);
            return false; // 或抛出异常
        }
    }

    public static void cutPdfTo50Pages(String inputPath, String outputPath,int pages) {
        try (PDDocument document = PDDocument.load(new File(inputPath))) {
            int totalPages = document.getNumberOfPages();

            if (totalPages > pages) {
                // 使用 Splitter 分割前 50 页
                Splitter splitter = new Splitter();
                splitter.setStartPage(1);
                splitter.setEndPage(pages);
                splitter.setSplitAtPage(pages);

                List<PDDocument> splitDocuments = splitter.split(document);
                PDDocument first50Pages = splitDocuments.get(0);

                // 保存裁剪后的 PDF
                first50Pages.save(outputPath);
                first50Pages.close();
            }
        } catch (IOException e) {
            log.error("处理 PDF 失败:{},",e);
        }
    }

    /**
     * 流水校验(指定影像类型)
     * @param applyNo
     * @param fileList
     * @param personDetail
     */
    @Async("dSTaskExecutor")
    @Override
    public void imageAutomaticRecognition(String applyNo, List<ComAttachmentFile> fileList, CaseCustIndividualDto personDetail,CaseBaseInfo caseBaseInfo) {
        log.info("营业执照公章识别start");
        String yes=AfsEnumUtil.key(YesOrNoEnum.YES);
        //异步解析流水
        for (ComAttachmentFile comAttachmentFile : fileList) {
            if (!FILE_TYPE.contains(comAttachmentFile.getFileType())) {
                log.info("当前影像类型={}，无需识别",comAttachmentFile.getFileType());
                return;
            }
            // 下载文件
            String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId();
            try {
                //获取订单挂靠信息
                IResponse<ApplyAffiliatedUnitDto> affiliatedUnitRes = applyContractFeign.getAffiliatedUnit(applyNo);
                if (!CommonConstants.SUCCESS.equals(affiliatedUnitRes.getCode())) {
                    log.error(affiliatedUnitRes.getMsg());
                    continue;
                }
                //文件下载及识别智能信息对比项
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000)
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                File file = FileUtil.file(savePath);
                //获取营业执照OCR结果
                CaseBusinessLicenseInfo businessLicenseInfo = ocrHandler(file,affiliatedUnitRes.getData(),personDetail,caseBaseInfo);
                //获取印章识别结果
                CaseBusinessLicense caseBusinessLicense = caseBusinessLicenseMapper.selectOne(Wrappers.<CaseBusinessLicense>lambdaQuery().eq(CaseBusinessLicense::getApplyNo, applyNo));
                if (caseBusinessLicense==null) {
                    caseBusinessLicense = new CaseBusinessLicense();
                }
                JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getId()), applyNo, file);
                if (ObjectUtil.isNotEmpty(jsonObject)) {
                    JSONArray sealResult = Optional.ofNullable(jsonObject.getJSONArray("seal_result")).orElse(new JSONArray());
                    //判断是否加盖公章（存在第二个章即可）
                    //判断公章返回的文字是否与申请人的单位名称，挂靠单位名称一致
                    //判断是否原件/复印件（存在第二个章即可）
                    if (sealResult.size()>1) {
                        caseBusinessLicense.setOfficialSeal(yes);
                        caseBusinessLicense.setCopyOrScan(yes);
                    }
                    for (Object o : sealResult) {
                        SealRecognitionInfo sealRecognitionInfo = JSONObject.parseObject(JSONObject.toJSONString(o), SealRecognitionInfo.class);
                        List<String> recTxt = sealRecognitionInfo.getRecTxt();
                        if (recTxt.contains(affiliatedUnitRes.getData().getAffiliatedName())) {
                            caseBusinessLicense.setOfficialSealLetteringComparison(yes);
                            break;
                        }
                    }
                }
                //保存识别结果
                caseBusinessLicense.setApplyNo(applyNo);
                caseBusinessLicense.setUnitName(personDetail.getUnitName());
                caseBusinessLicense.setAffiliatedName(affiliatedUnitRes.getData().getAffiliatedName());
                caseBusinessLicense.setSocUniCrtCode(affiliatedUnitRes.getData().getSocUniCrtCode());
                caseBusinessLicense.setAffiliatedNameComparison(businessLicenseInfo.getAffiliatedNameIsSame());
                caseBusinessLicense.setUnitNameComparison(businessLicenseInfo.getWorkNameIsSame());
                caseBusinessLicense.setValidityDateComparison(businessLicenseInfo.getPeriodIsSame());
                caseBusinessLicense.setIsEstablishmentTime(businessLicenseInfo.getIsEstablishmentTime());
                caseBusinessLicenseService.saveOrUpdate(caseBusinessLicense);
                //多文件情况下，如果某一个全部符合则直接跳过，无需识别其他
                if (Objects.equals(yes, caseBusinessLicense.getUnitNameComparison())
                        && Objects.equals(yes, caseBusinessLicense.getAffiliatedNameComparison())
                        && Objects.equals(yes, caseBusinessLicense.getValidityDateComparison())
                        && Objects.equals(yes, caseBusinessLicense.getOfficialSeal())
                        && Objects.equals(yes, caseBusinessLicense.getOfficialSealLetteringComparison())
                        && Objects.equals(yes, caseBusinessLicense.getCopyOrScan())
                        && ObjectUtil.equals(yes,caseBusinessLicense.getIsEstablishmentTime())) {
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                FileUtil.del(savePath);
            }
        }
    }

    @Async("dSTaskExecutor")
    @Override
    public void businessAutomaticRecognition(String applyNo, CaseBaseInfo caseBaseInfo, List<ComAttachmentFile> fileList, CaseCustIndividualDto personDetail) {
        log.info("经营许可证智能识别开始:{}",applyNo);
        for (ComAttachmentFile comAttachmentFile : fileList){
            if (!FILE_TYPE.contains(comAttachmentFile.getFileType())) {
                log.info("当前影像类型={}，无需识别",comAttachmentFile.getFileType());
                return;
            }
            String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId();
            try {
                StringBuilder sb = new StringBuilder();
                List<DicDataDto> dtos = DicHelper.getDicMaps("businessVerification").get("businessVerification");
                for (DicDataDto dto : dtos){
                    sb.append(dto.getValue());
                }
                //获取文件url
                JSONArray jsonArray = generateJson(comAttachmentFile,applyNo,false);
                String content = deepSeekProperties.getReadBusiness() + sb;
                String result = deepseekIntelligentResultsService.callDsRead(content,jsonArray.toString());
                int seal = 0;
                if (StrUtil.isNotBlank(result)){
                    String pureJson = extractPureJson(result);
                    HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                            .timeout(20000) // 设置超时时间20秒
                            .execute()
                            .writeBody(FileUtil.file(savePath));
                    JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getId()),applyNo,FileUtil.file(savePath));
                    if (ObjectUtil.isNotEmpty(jsonObject)){
                        boolean falg = jsonObject.getBoolean("error");
                        JSONArray array = jsonObject.getJSONArray("seal_result");
                        boolean isExist = areAllDetectBoxesNotEmpty(array);
                        if (!falg && isExist){
                            seal = 1;
                        }
                    }
                    //获取订单挂靠信息
                    IResponse<ApplyAffiliatedUnitDto> affiliatedUnitRes = applyContractFeign.getAffiliatedUnit(applyNo);
                    if (!CommonConstants.SUCCESS.equals(affiliatedUnitRes.getCode())) {
                        log.error(affiliatedUnitRes.getMsg());
                        continue;
                    }
                    BusinessAutomaticRecognition businessAutomaticRecognition = new BusinessAutomaticRecognition();
                    String resultJson = makeBusinessData(pureJson,seal,businessAutomaticRecognition,caseBaseInfo,affiliatedUnitRes.getData(),personDetail);
                    BusinessAutomaticRecognition recognition = businessAutomaticRecognitionService.getOne(Wrappers.<BusinessAutomaticRecognition>lambdaQuery()
                            .eq(BusinessAutomaticRecognition::getApplyNo,applyNo)
                            .eq(BusinessAutomaticRecognition::getFileId,String.valueOf(comAttachmentFile.getId())));
                    if (ObjectUtil.isNotEmpty(recognition)){
                        businessAutomaticRecognition.setId(recognition.getId());
                    }
                    businessAutomaticRecognition.setApplyNo(applyNo);
                    businessAutomaticRecognition.setResult(resultJson);
                    businessAutomaticRecognition.setParam(content);
                    businessAutomaticRecognition.setAutoTime(new Date());
                    businessAutomaticRecognition.setFileId(String.valueOf(comAttachmentFile.getId()));
                    businessAutomaticRecognitionService.saveOrUpdate(businessAutomaticRecognition);
                }
            }catch (Exception e){
                log.error("经营许可证智能识别失败:{}",e);
            }finally {
                FileUtil.del(savePath);
            }
        }
    }

    private String makeBusinessData(String pureJson, int seal, BusinessAutomaticRecognition businessAutomaticRecognition, CaseBaseInfo caseBaseInfo, ApplyAffiliatedUnitDto dto,CaseCustIndividualDto personDetail) {
        JSONObject newApply = new JSONObject();
        JSONObject newApprove = new JSONObject();

        JSONObject data = JSONObject.parseObject(pureJson);
        String name = data.getString("业户名称").trim();
        String type = data.getString("类型");
        newApply.put("业户名称",name);
        newApply.put("起始日期",data.getString("开始日期"));
        newApply.put("结束日期",data.getString("结束日期"));

        Date startDate = null;
        Date endDate = null;
        if (StrUtil.isNotBlank(data.getString("开始日期"))){
            startDate = DateUtil.parse(data.getString("开始日期"));
        }
        if (StrUtil.isNotBlank(data.getString("结束日期"))){
            endDate = DateUtil.parse(data.getString("结束日期"));
        }

        int affiliatedNameComparison = 0;
        if (ObjectUtil.isNotEmpty(dto)){
            affiliatedNameComparison = StrUtil.equals(dto.getAffiliatedName(),name) ? 1 : 0;
        }
        newApprove.put("挂靠公司名称比对",affiliatedNameComparison);
        businessAutomaticRecognition.setAffiliatedNameComparison(affiliatedNameComparison);

        int unitNameComparison = 0;
        if (ObjectUtil.isNotEmpty(personDetail)){
            unitNameComparison = StrUtil.equals(personDetail.getUnitName(),name) ? 1 : 0;
        }
        CaseConfParam caseConfParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                .eq(CaseConfParam::getParamType, Const.ATTACHMENT_RULES_UNIT)
                .eq(CaseConfParam::getParamStatus, com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum.yes.key()));
        if (ObjectUtil.isNotEmpty(caseConfParam)){
            if (caseConfParam.getParamValue().contains(caseBaseInfo.getProductName())){
                unitNameComparison = 1;
            }
        }
        newApprove.put("单位名称比对",unitNameComparison);
        businessAutomaticRecognition.setUnitNameComparison(unitNameComparison);

        int businessNameComparison = 0;
        CaseBusinessLicenseInfo caseBusinessLicenseInfo = caseBusinessLicenseInfoService.getOne(Wrappers.<CaseBusinessLicenseInfo>lambdaQuery()
                .eq(CaseBusinessLicenseInfo::getApplyNo,caseBaseInfo.getApplyNo())
                .orderByDesc(CaseBusinessLicenseInfo::getCreateTime)
                .last("limit 1"));
        log.info("caseBusinessLicenseInfo:{}",caseBusinessLicenseInfo);
        if (ObjectUtil.isNotEmpty(caseBusinessLicenseInfo)){
            businessNameComparison = StrUtil.equals(caseBusinessLicenseInfo.getAffiliatedName(),name) ? 1 : 0;
        }
        newApprove.put("营业执照名称比对",businessNameComparison);
        businessAutomaticRecognition.setBusinessNameComparison(businessNameComparison);

        int validityDateComparison = 0;
        if (endDate != null){
            validityDateComparison = DateUtil.compare(caseBaseInfo.getPassFirstDate(),endDate) < 0 ? 1 : 0;
        }
        newApprove.put("是否有效期内",validityDateComparison);
        businessAutomaticRecognition.setValidityDateComparison(validityDateComparison);

        int establishmentTime = 0;
        if (startDate != null){
            long day = DateUtil.betweenDay(startDate,new Date(),true);
            establishmentTime = day > 180 ? 1 : 0;
        }else {
            establishmentTime = 1;
        }
        newApprove.put("成立大于半年",establishmentTime);
        businessAutomaticRecognition.setEstablishmentTime(establishmentTime);

        int typeMeets = 0;
        if (StrUtil.isNotEmpty(type)){
            typeMeets = type.contains("经营许可") ? 1 : 0;
        }
        newApprove.put("类型符合性",typeMeets);
        businessAutomaticRecognition.setTypeMeets(typeMeets);

        newApprove.put("印章",seal);
        businessAutomaticRecognition.setIsSeal(seal);

        if (seal == 0 || typeMeets == 0 || establishmentTime == 0 || businessNameComparison == 0 || unitNameComparison == 0
            || affiliatedNameComparison ==  0 || validityDateComparison == 0){
            businessAutomaticRecognition.setOverallJudgment(0);
        }else {
            businessAutomaticRecognition.setOverallJudgment(1);
        }

        JSONObject json = new JSONObject();
        json.put("apply",newApply);
        json.put("approve",newApprove);
        return json.toJSONString();
    }

    @Override
    @Async("dSTaskExecutor")
    public void turnoverAutomaticRecognitionByImag(String applyNo, CaseBaseInfo caseBaseInfo, CaseCustInfo custInfo, List<ComAttachmentFile> comAttachmentFileList, String bankCardNo,CaseCustInfo custInfo1) {
        log.info("进行流水图片自动识别，申请编号：{}",applyNo);
        try{
            StringBuilder sb = new StringBuilder();
            List<DicDataDto> dtos = DicHelper.getDicMaps("flowVerification").get("flowVerification");
            for (DicDataDto dto : dtos){
                sb.append(dto.getValue());
            }
            String content = deepSeekProperties.getReadPreface() + sb;
            //将图片合成pdf
            List<String> imgList = new ArrayList<>();
            List<String> list = new ArrayList<>();
            String tmpPath = fileProperties.getTempDir() + applyNo + ".pdf";
            for (ComAttachmentFile comAttachmentFile : comAttachmentFileList){
                String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + "." + comAttachmentFile.getFileType();
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000)
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                imgList.add(savePath);
                list.add(String.valueOf(comAttachmentFile.getId()));
            }
            convertImagesToPdf(imgList,tmpPath);
            File file = new File(tmpPath);
            if (file.exists()){
                String result = deepseekIntelligentResultsService.callDsImags(content,new File(tmpPath));
                int seal = 0;
                if (StrUtil.isNotBlank(result)){
                    String pureJson = extractPureJson(result);
                    try {
                        JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(null,null,FileUtil.file(tmpPath));
                        if (ObjectUtil.isNotEmpty(jsonObject)){
                            boolean falg = jsonObject.getBoolean("error");
                            JSONArray array = jsonObject.getJSONArray("seal_result");
                            boolean isExist = areAllDetectBoxesNotEmpty(array);
                            if (!falg && isExist){
                                seal = 1;
                            }
                        }
                    }catch (Exception e){
                        log.error("印章识别失败:{}",e);
                    }finally {
                        FileUtil.del(tmpPath);
                    }
                    StatementAutomaticRecognition statementAutomaticRecognition = new StatementAutomaticRecognition();
                    String resultJson = makeData(pureJson,seal,statementAutomaticRecognition,caseBaseInfo,custInfo,bankCardNo,null,custInfo1);
                    for (ComAttachmentFile comAttachmentFile : comAttachmentFileList){
                        StatementAutomaticRecognition recognition = statementAutomaticRecognitionService.getOne(Wrappers.<StatementAutomaticRecognition>lambdaQuery()
                                .eq(StatementAutomaticRecognition::getApplyNo,applyNo)
                                .eq(StatementAutomaticRecognition::getCheckType,CheckTypeEnum.STATEMENT.getCode())
                                .eq(StatementAutomaticRecognition::getFileId,String.valueOf(comAttachmentFile.getId())));
                        if (ObjectUtil.isNotEmpty(recognition)){
                            statementAutomaticRecognition.setId(recognition.getId());
                        }
                        statementAutomaticRecognition.setApplyNo(applyNo);
                        statementAutomaticRecognition.setFileType(comAttachmentFile.getFileType());
                        statementAutomaticRecognition.setStatus("0");
                        statementAutomaticRecognition.setCheckType(CheckTypeEnum.STATEMENT.getCode());
                        statementAutomaticRecognition.setResult(resultJson);
                        statementAutomaticRecognition.setParam(content);
                        statementAutomaticRecognition.setAutoTime(new Date());
                        statementAutomaticRecognition.setFileId(String.valueOf(comAttachmentFile.getId()));
                        statementAutomaticRecognitionService.saveOrUpdate(statementAutomaticRecognition);
                    }
                }
            }
        }catch (Exception e){
            log.error("流水图片识别失败:{}",e);
        }
    }

    @Async("dSTaskExecutor")
    @Override
    public void attorneyAutomaticRecognition(String applyNo, CaseBaseInfo caseBaseInfo, List<ComAttachmentFile> fileList,CaseCustIndividualDto personDetail,CaseCustInfo caseCustInfo,String now) {
        log.info("进行授权委托书自动识别，申请编号：{}",applyNo);
        CaseConfParam caseConfParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>lambdaQuery()
                .eq(CaseConfParam::getParamType, ConfCaseParamTypeEnum.POWER_ATTORNEY.getParamType()));
        for (ComAttachmentFile comAttachmentFile : fileList){
            String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + "." + comAttachmentFile.getFileType();
            AttorneyAutomaticRecognitionDto recognitionDto = new AttorneyAutomaticRecognitionDto();
            AttorneyAutomaticRecognition attorneyAutomaticRecognition = new AttorneyAutomaticRecognition();
            AttorneyAutomaticRecognition automaticRecognition = attorneyAutomaticRecognitionService.getOne(Wrappers.<AttorneyAutomaticRecognition>lambdaQuery()
                    .eq(AttorneyAutomaticRecognition::getApplyNo,applyNo)
                    .eq(AttorneyAutomaticRecognition::getFileId,comAttachmentFile.getId()));
            if (ObjectUtil.isNotEmpty(automaticRecognition)){
                attorneyAutomaticRecognition.setId(automaticRecognition.getId());
                Map<String, String> mapping = new HashMap<>();
                mapping.put("id","id");
                mapping.put("complianceCheckManual", "complianceCheckManual");
                mapping.put("authorizationValidityManual", "authorizationValidityManual");
                mapping.put("sealCheckManual","sealCheckManual");
                mapping.put("companyNameCheckManual","companyNameCheckManual");
                mapping.put("customerInfoConsistencyManual","customerInfoConsistencyManual");
                BeanUtil.copyProperties(automaticRecognition,attorneyAutomaticRecognition, CopyOptions.create()
                        .setFieldMapping(mapping)
                        .setIgnoreCase(true)
                        .setIgnoreNullValue(true)
                        .setOverride(false));
            }
            try {
                attorneyAutomaticRecognition.setFileId(String.valueOf(comAttachmentFile.getId()));
                attorneyAutomaticRecognition.setApplyNo(applyNo);
                JSONArray jsonArray = generateJson(comAttachmentFile,applyNo,false);
                String content = caseConfParam.getParamValue();
                String result = deepseekIntelligentResultsService.callDsRead(content,jsonArray.toString());
                if (StrUtil.isNotEmpty(result)){
                    String pureJson = extractPureJson(result);
                    JSONObject jsonObject = JSON.parseObject(pureJson);
                    attorneyAutomaticRecognition.setDsResult(jsonObject.toJSONString());
                    makeAttorneyData(jsonObject,attorneyAutomaticRecognition,recognitionDto);
                }else {
                    attorneyAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.EXCEPTION.getCode());
                    attorneyAutomaticRecognition.setIntelligentApprovalRemark("timeout");
                }
                int seal = 0;
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000) // 设置超时时间20秒
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getId()),applyNo,FileUtil.file(savePath));
                if (ObjectUtil.isNotEmpty(jsonObject)){
                    attorneyAutomaticRecognition.setSealResult(jsonObject.toJSONString());
                    boolean falg = jsonObject.getBoolean("error");
                    JSONArray array = jsonObject.getJSONArray("seal_result");
                    boolean isExist = areAllDetectBoxesNotEmpty(array);
                    if (!falg && isExist && array.size()>1){
                        seal = 1;
                    }else {
                        attorneyAutomaticRecognition.setSealCheckRemark("印章检测缺失");
                    }
                }else {
                    attorneyAutomaticRecognition.setSealCheckRemark("印章检测缺失");
                }
                attorneyAutomaticRecognition.setHasSealIntelligent(seal);
                checkAttorney(caseBaseInfo,recognitionDto,attorneyAutomaticRecognition,personDetail,caseCustInfo,now);
                if (attorneyAutomaticRecognition.getComplianceCheckIntelligent() == 1 && attorneyAutomaticRecognition.getAuthorizationValidityIntelligent() == 1
                    && attorneyAutomaticRecognition.getHasSealIntelligent() == 1 && attorneyAutomaticRecognition.getCompanyNameCheckIntelligent() == 1
                    && attorneyAutomaticRecognition.getCustomerInfoConsistencyIntelligent() == 1){
                    attorneyAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.SUCCESS.getCode());
                }else {
                    attorneyAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.FAIL.getCode());
                }
            }catch (Exception e){
                log.error("{}-{}授权委托书识别失败:{}",applyNo,comAttachmentFile.getFileId(),e);
                attorneyAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.EXCEPTION.getCode());
                attorneyAutomaticRecognition.setIntelligentApprovalRemark(e.getMessage());
            }finally {
                attorneyAutomaticRecognitionService.saveOrUpdate(attorneyAutomaticRecognition);
                FileUtil.del(savePath);
            }
        }
        }

    @Async("dSTaskExecutor")
    @Override
    public void certificateAutomaticRecognition(String applyNo, CaseBaseInfo caseBaseInfo, List<ComAttachmentFile> fileList,CaseCustIndividualDto personDetail,String now) {
        log.info("进行证明书自动识别，申请编号：{}",applyNo);
        CaseConfParam caseConfParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>lambdaQuery()
                .eq(CaseConfParam::getParamType, ConfCaseParamTypeEnum.APPLY_CERTIFICATE.getParamType()));
        for (ComAttachmentFile comAttachmentFile : fileList){
            String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + "." + comAttachmentFile.getFileType();
            CertificateAutomaticRecognitionDto certificateAutomaticRecognitionDto = new CertificateAutomaticRecognitionDto();
            CertificateAutomaticRecognition certificateAutomaticRecognition = new CertificateAutomaticRecognition();
            CertificateAutomaticRecognition automaticRecognition = certificateAutomaticRecognitionService.getOne(Wrappers.<CertificateAutomaticRecognition>lambdaQuery()
                    .eq(CertificateAutomaticRecognition::getApplyNo,applyNo)
                    .eq(CertificateAutomaticRecognition::getFileId,String.valueOf(comAttachmentFile.getId())));
            if (ObjectUtil.isNotEmpty(automaticRecognition)){
                certificateAutomaticRecognition.setId(automaticRecognition.getId());
                Map<String, String> map = new HashMap<>();
                map.put("id","id");
                map.put("complianceCheckManual", "complianceCheckManual");
                map.put("signingTimeManual", "signingTimeManual");
                map.put("sealCheckManual","sealCheckManual");
                map.put("companyNameCheckManual","companyNameCheckManual");
                map.put("customerInfoConsistencyManual","customerInfoConsistencyManual");
                map.put("incomeManual","incomeManual");
                BeanUtil.copyProperties(automaticRecognition,certificateAutomaticRecognition, CopyOptions.create()
                        .setFieldMapping(map)
                        .setIgnoreCase(true)
                        .setIgnoreNullValue(true)
                        .setOverride(false));
            }
            try{
                certificateAutomaticRecognition.setFileId(String.valueOf(comAttachmentFile.getId()));
                certificateAutomaticRecognition.setApplyNo(applyNo);
                JSONArray jsonArray = generateJson(comAttachmentFile,applyNo,false);
                String content = caseConfParam.getParamValue();
                String result = deepseekIntelligentResultsService.callDsRead(content,jsonArray.toString());
                if (StrUtil.isNotEmpty(result)){
                    String pureJson = extractPureJson(result);
                    JSONObject jsonObject = JSON.parseObject(pureJson);
                    certificateAutomaticRecognition.setDsResult(jsonObject.toJSONString());
                    makeCertificateData(jsonObject,certificateAutomaticRecognition,certificateAutomaticRecognitionDto);
                }else {
                    certificateAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.EXCEPTION.getCode());
                    certificateAutomaticRecognition.setIntelligentApprovalRemark("timeout");
                }
                int seal = 0;
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000) // 设置超时时间20秒
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                JSONObject jsonObject = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getId()),applyNo,FileUtil.file(savePath));
                if (ObjectUtil.isNotEmpty(jsonObject)){
                    certificateAutomaticRecognition.setSealResult(jsonObject.toJSONString());
                    boolean falg = jsonObject.getBoolean("error");
                    JSONArray array = jsonObject.getJSONArray("seal_result");
                    boolean isExist = areAllDetectBoxesNotEmpty(array);
                    if (!falg && isExist){
                        seal = 1;
                    }else {
                        certificateAutomaticRecognition.setSealCheckRemark("印章检测缺失");
                    }
                }else {
                    certificateAutomaticRecognition.setSealCheckRemark("印章检测缺失");
                }
                certificateAutomaticRecognition.setHasSealIntelligent(seal);
                checkCertificate(caseBaseInfo,certificateAutomaticRecognitionDto,certificateAutomaticRecognition,personDetail,now);
                if (certificateAutomaticRecognition.getComplianceCheckIntelligent() == 1 && certificateAutomaticRecognition.getSigningTimeIntelligent() == 1
                    && certificateAutomaticRecognition.getHasSealIntelligent() == 1 && certificateAutomaticRecognition.getCompanyNameCheckIntelligent() == 1
                    && certificateAutomaticRecognition.getCustomerInfoConsistencyIntelligent() == 1 && certificateAutomaticRecognition.getIncomeIntelligent() == 1){
                    certificateAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.SUCCESS.getCode());
                }else {
                    certificateAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.FAIL.getCode());
                }
            }catch (Exception e){
                log.error("{}-{}证明书识别失败:{}",applyNo,comAttachmentFile.getFileId(),e);
                certificateAutomaticRecognition.setIntelligentApprovalResult(IntelligentStatusEnum.EXCEPTION.getCode());
                certificateAutomaticRecognition.setIntelligentApprovalRemark(e.getMessage());
            }finally {
                certificateAutomaticRecognitionService.saveOrUpdate(certificateAutomaticRecognition);
                FileUtil.del(savePath);
            }
        }

    }

    private void checkCertificate(CaseBaseInfo caseBaseInfo, CertificateAutomaticRecognitionDto certificateAutomaticRecognitionDto, CertificateAutomaticRecognition certificateAutomaticRecognition, CaseCustIndividualDto personDetail,String now) {
        //获取订单挂靠信息
        JSONObject apply = new JSONObject();
        apply.put("客户姓名",caseBaseInfo.getCustNameRepeat());
        apply.put("身份证号码",caseBaseInfo.getCertNoRepeat());
        apply.put("职务", PositionEnum.getDesc(personDetail.getPosition()));
        apply.put("月收入",personDetail.getMonthlyIncome());
        apply.put("工作单位",personDetail.getUnitName());
        apply.put("订单最新提交时间",now);
        certificateAutomaticRecognition.setOrderInfo(apply.toJSONString());

        if (ObjectUtil.isNotEmpty(certificateAutomaticRecognitionDto) && StrUtil.isNotBlank(certificateAutomaticRecognitionDto.getSignDate())){
            Date signDate = DateUtil.parse(certificateAutomaticRecognitionDto.getSignDate());
            int result = DateUtil.compare(signDate, DateUtil.parse(DateUtil.today()));
            if (result <= 0){
                long day = DateUtil.betweenDay(signDate,DateUtil.parse(DateUtil.today()),true);
                if (day <= 15){
                    certificateAutomaticRecognition.setSigningTimeIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
                }else {
                    certificateAutomaticRecognition.setSigningTimeIntelligent(IntelligentStatusEnum.FAIL.getCode());
                    certificateAutomaticRecognition.setSigningTimeRemark("日期差大于15天");
                }
            }else {
                certificateAutomaticRecognition.setSigningTimeIntelligent(IntelligentStatusEnum.FAIL.getCode());
                certificateAutomaticRecognition.setSigningTimeRemark("签署日期大于最近一次提交信审时间");
            }
        }else {
            certificateAutomaticRecognition.setSigningTimeIntelligent(IntelligentStatusEnum.FAIL.getCode());
            certificateAutomaticRecognition.setSigningTimeRemark("未识别出签署时间");
        }

        if (certificateAutomaticRecognition.getHasSealIntelligent() == 1){
            boolean sealCheck = false;
            JSONObject jsonObject = JSON.parseObject(certificateAutomaticRecognition.getSealResult());
            JSONArray jsonArray = jsonObject.getJSONArray("seal_result");
            for (Object item : jsonArray){
                JSONObject obj = (JSONObject)item;
                List<String> list = obj.getJSONArray("rec_txt").toJavaList(String.class);
                for (String s : list){
                    if (StrUtil.equals(s,personDetail.getUnitName())){
                        sealCheck = true;
                    }
                }
            }
            if (sealCheck){
                certificateAutomaticRecognition.setCompanyNameCheckIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
            }else {
                certificateAutomaticRecognition.setCompanyNameCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
                certificateAutomaticRecognition.setCompanyNameCheckRemark("公章信息不一致");
            }
        }

        boolean isName = true;
        boolean isCertNo = true;
        StringBuilder stringBuilder = new StringBuilder();
        if (!StrUtil.equals(certificateAutomaticRecognitionDto.getAuthorizerName(),caseBaseInfo.getCustNameRepeat())){
            isName = false;
            stringBuilder.append("姓名不一致");
            stringBuilder.append("/n");
        }
        if (!StrUtil.equals(certificateAutomaticRecognitionDto.getAuthorizerIdcard(),caseBaseInfo.getCertNoRepeat())){
            isCertNo = false;
            stringBuilder.append("身份证号码不一致");
            stringBuilder.append("/n");
        }
        if (isName && isCertNo){
            certificateAutomaticRecognition.setCustomerInfoConsistencyIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            certificateAutomaticRecognition.setCustomerInfoConsistencyIntelligent(IntelligentStatusEnum.FAIL.getCode());
            certificateAutomaticRecognition.setCustomerInfoConsistencyRemark(stringBuilder.toString());
        }

        if (NumberUtil.equals(personDetail.getMonthlyIncome(),NumberUtil.toBigDecimal(certificateAutomaticRecognitionDto.getMonthlyIncome()))){
            certificateAutomaticRecognition.setIncomeIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            certificateAutomaticRecognition.setIncomeIntelligent(IntelligentStatusEnum.FAIL.getCode());
            certificateAutomaticRecognition.setIncomeRemark("月收入不一致");
        }

    }

    private void makeCertificateData(JSONObject jsonObject, CertificateAutomaticRecognition certificateAutomaticRecognition, CertificateAutomaticRecognitionDto certificateAutomaticRecognitionDto) {
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotBlank(jsonObject.getString("company_name"))){
            certificateAutomaticRecognitionDto.setAuthorizerName(jsonObject.getString("company_name"));
        }else {
            sb.append("证明书抬头对象缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_name"))){
            certificateAutomaticRecognitionDto.setAuthorizerName(jsonObject.getString("authorizer_name"));
        }else {
            sb.append("被证明人缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_idcard"))){
            certificateAutomaticRecognitionDto.setAuthorizerIdcard(jsonObject.getString("authorizer_idcard"));
        }else {
            sb.append("被证明人身份证号码缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_duty"))){
            certificateAutomaticRecognitionDto.setAuthorizerDuty(jsonObject.getString("authorizer_duty"));
        }else {
            sb.append("被证明人职务缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("service_years"))){
            certificateAutomaticRecognitionDto.setServiceYears(jsonObject.getString("service_years"));
        }else {
            sb.append("单位工作年限缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("monthly_income"))){
            certificateAutomaticRecognitionDto.setMonthlyIncome(jsonObject.getString("monthly_income"));
        }else {
            sb.append("月收入缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("sign_date"))){
            certificateAutomaticRecognitionDto.setSignDate(jsonObject.getString("sign_date"));
        }else {
            sb.append("签署日期缺失");
            sb.append("/n");
        }

        if (sb.isEmpty()){
            certificateAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            certificateAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
            certificateAutomaticRecognition.setComplianceCheckRemark(sb.toString());
        }

    }

    private void checkAttorney(CaseBaseInfo caseBaseInfo, AttorneyAutomaticRecognitionDto recognitionDto, AttorneyAutomaticRecognition attorneyAutomaticRecognition,CaseCustIndividualDto personDetail,CaseCustInfo caseCustInfo,String now) {
        //获取订单挂靠信息
        JSONObject apply = new JSONObject();
        apply.put("客户姓名",caseBaseInfo.getCustNameRepeat());
        apply.put("身份证号码",caseBaseInfo.getCertNoRepeat());
        apply.put("职务", PositionEnum.getDesc(personDetail.getPosition()));
        apply.put("电话",caseCustInfo.getTelPhone());
        apply.put("工作单位",personDetail.getUnitName());

        String affiliatedName = null;
        IResponse<ApplyAffiliatedUnitDto> affiliatedUnitRes = applyContractFeign.getAffiliatedUnit(caseBaseInfo.getApplyNo());
        if (CommonConstants.SUCCESS.equals(affiliatedUnitRes.getCode()) && ObjectUtil.isNotEmpty(affiliatedUnitRes.getData())){
            affiliatedName = affiliatedUnitRes.getData().getAffiliatedName();
            apply.put("挂靠单位",affiliatedName);
        }
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotEmpty(attorneyAutomaticRecognition.getComplianceCheckRemark())){
            sb = new StringBuilder(attorneyAutomaticRecognition.getComplianceCheckRemark());
        }
        if (attorneyAutomaticRecognition.getHasSealIntelligent() == 1){
            boolean sealCheck = false;
            JSONObject jsonObject = JSON.parseObject(attorneyAutomaticRecognition.getSealResult());
            JSONArray jsonArray = jsonObject.getJSONArray("seal_result");
            outerLoop:
            for (Object item : jsonArray){
                JSONObject obj = (JSONObject)item;
                List<String> list = obj.getJSONArray("rec_txt").toJavaList(String.class);
                for (String s : list){
                    if (StrUtil.equals(s,affiliatedName)){
                        sealCheck = true;
                        break outerLoop;
                    }
                }
            }
            if (!sealCheck){
                attorneyAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
                sb.append("公章信息不一致");
                sb.append("/n");
            }
        }else {
            sb.append("公章信息不一致");
            sb.append("/n");
        }

        if (ObjectUtil.isNotEmpty(recognitionDto) && StrUtil.isNotBlank(recognitionDto.getAuthorizerDate())){
            Date authorizerDate = DateUtil.parse(recognitionDto.getAuthorizerDate());
            int result = DateUtil.compare(authorizerDate, caseBaseInfo.getPassFirstDate());
            if (result > 0){
                attorneyAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
                sb.append("签署日期异常");
                sb.append("/n");
            }
        }else {
            attorneyAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
            sb.append("签署日期异常");
            sb.append("/n");
        }
        attorneyAutomaticRecognition.setComplianceCheckRemark(sb.toString());


        //授权有效期
        try {
            if (StrUtil.isNotBlank(recognitionDto.getAuthorizerBegin()) && StrUtil.isNotBlank(recognitionDto.getAuthorizerEnd())){
                Date authorizerBegin = DateUtil.parse(recognitionDto.getAuthorizerBegin());
                Date authorizerEnd = DateUtil.parse(recognitionDto.getAuthorizerEnd());
                long day = DateUtil.betweenDay(authorizerBegin,authorizerEnd,true);
                if (day >= 364 && day <= 366){
                    attorneyAutomaticRecognition.setAuthorizationValidityIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
                }else {
                    attorneyAutomaticRecognition.setAuthorizationValidityIntelligent(IntelligentStatusEnum.FAIL.getCode());
                    attorneyAutomaticRecognition.setAuthorizationValidityRemark("授权周期非1年");
                }
            }else {
                attorneyAutomaticRecognition.setAuthorizationValidityIntelligent(IntelligentStatusEnum.FAIL.getCode());
                attorneyAutomaticRecognition.setAuthorizationValidityRemark("授权周期非1年");
            }
        }catch (Exception e){
            attorneyAutomaticRecognition.setAuthorizationValidityIntelligent(IntelligentStatusEnum.FAIL.getCode());
            attorneyAutomaticRecognition.setAuthorizationValidityRemark("授权周期非1年");
            log.error("有效期校验报错:{}",e);
        }

        String licenseName = null;
        CaseBusinessLicenseInfo caseBusinessLicenseInfo = caseBusinessLicenseInfoService.getOne(Wrappers.<CaseBusinessLicenseInfo>lambdaQuery()
                .eq(CaseBusinessLicenseInfo::getApplyNo,caseBaseInfo.getApplyNo())
                .orderByDesc(CaseBusinessLicenseInfo::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(caseBusinessLicenseInfo)){
            licenseName = caseBusinessLicenseInfo.getAffiliatedName();
            apply.put("营业执照OCR单位名称",licenseName);
        }
        //单位名称校验
        if (StrUtil.equals(recognitionDto.getAffiliatedName(),personDetail.getUnitName()) && StrUtil.equals(recognitionDto.getAffiliatedName(),affiliatedName)
                && StrUtil.equals(recognitionDto.getAffiliatedName(),licenseName)){
            attorneyAutomaticRecognition.setCompanyNameCheckIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            attorneyAutomaticRecognition.setCompanyNameCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
            attorneyAutomaticRecognition.setCompanyNameCheckRemark("公司名称一致性校验不通过");
        }

        //客户信息是否一致
        boolean isName = true;
        boolean isCertNo = true;
        StringBuilder stringBuilder = new StringBuilder();
        if (!StrUtil.equals(recognitionDto.getAuthorizerName(),caseBaseInfo.getCustNameRepeat())){
            isName = false;
            stringBuilder.append("姓名不一致");
            stringBuilder.append("/n");
        }
        if (!StrUtil.equals(recognitionDto.getAuthorizerIdcard(),caseBaseInfo.getCertNoRepeat())){
            isCertNo = false;
            stringBuilder.append("身份证号码不一致");
            stringBuilder.append("/n");
        }

        if (isName && isCertNo){
            attorneyAutomaticRecognition.setCustomerInfoConsistencyIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            attorneyAutomaticRecognition.setCustomerInfoConsistencyIntelligent(IntelligentStatusEnum.FAIL.getCode());
            attorneyAutomaticRecognition.setCustomerInfoConsistencyRemark(stringBuilder.toString());
        }
        apply.put("订单最新提交时间",now);
        attorneyAutomaticRecognition.setOrderInfo(apply.toJSONString());
    }

    private void makeAttorneyData(JSONObject jsonObject, AttorneyAutomaticRecognition attorneyAutomaticRecognition, AttorneyAutomaticRecognitionDto recognitionDto) {
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotBlank(jsonObject.getString("affiliated_name"))){
            recognitionDto.setAffiliatedName(jsonObject.getString("affiliated_name"));
        }else {
            sb.append("授权公司缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("soc_uni_crt_code"))){
            recognitionDto.setSocUniCrtCode(jsonObject.getString("soc_uni_crt_code"));
        }else {
            sb.append("授权公司统一社会信用代码缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_name"))){
            recognitionDto.setAuthorizerName(jsonObject.getString("authorizer_name"));
        }else {
            sb.append("被授权人姓名缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_idcard"))){
            recognitionDto.setAuthorizerIdcard(jsonObject.getString("authorizer_idcard"));
        }else {
            sb.append("被授权人证件号缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_duty"))){
            recognitionDto.setAuthorizerDuty(jsonObject.getString("authorizer_duty"));
        }else {
            sb.append("职位信息缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_phone"))){
            recognitionDto.setAuthorizerPhone(jsonObject.getString("authorizer_phone"));
        }else {
            sb.append("被授权人电话缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_begin"))){
            recognitionDto.setAuthorizerBegin(jsonObject.getString("authorizer_begin"));
        }else {
            sb.append("授权有效期起缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_end"))){
            recognitionDto.setAuthorizerEnd(jsonObject.getString("authorizer_end"));
        }else {
            sb.append("授权有效期止缺失");
            sb.append("/n");
        }

        if (StrUtil.isNotBlank(jsonObject.getString("authorizer_date"))){
            recognitionDto.setAuthorizerDate(jsonObject.getString("authorizer_date"));
        }else {
            sb.append("授权有效期止缺失");
            sb.append("/n");
        }

        if (sb.isEmpty()){
            attorneyAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.SUCCESS.getCode());
        }else {
            attorneyAutomaticRecognition.setComplianceCheckIntelligent(IntelligentStatusEnum.FAIL.getCode());
            attorneyAutomaticRecognition.setComplianceCheckRemark(sb.toString());
        }
    }


    public static void convertImagesToPdf(List<String> imagePaths, String outputPdfPath){
        List<String> newPaths = new ArrayList<>();
        //先压缩图片
        for (String path : imagePaths){
            String code = compressToBase64Str(new File(path),DEFAULT_SCALE,"jpg");
            String newPath =  SpringContextHolder.getBean(FileProperties.class).genTempFilePath() + File.separator + UUID.randomUUID() + ".jpg";
            base64ToFile(code,new File(newPath));
            newPaths.add(newPath);
        }
        try (PDDocument document = new PDDocument()) {
            for (String imagePath : newPaths) {
                // 创建A4大小的页面
                PDPage page = new PDPage(PDRectangle.A4);
                document.addPage(page);

                // 加载图片
                PDImageXObject pdImage = PDImageXObject.createFromFile(imagePath, document);

                // 计算缩放比例，保持宽高比
                float pageWidth = page.getMediaBox().getWidth();
                float pageHeight = page.getMediaBox().getHeight();
                float imageWidth = pdImage.getWidth();
                float imageHeight = pdImage.getHeight();

                float ratio = Math.min(pageWidth / imageWidth, pageHeight / imageHeight);
                float scaledWidth = imageWidth * ratio;
                float scaledHeight = imageHeight * ratio;

                // 计算居中位置
                float x = (pageWidth - scaledWidth) / 2;
                float y = (pageHeight - scaledHeight) / 2;

                try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                    contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
                }
            }
            document.save(outputPdfPath);
        }catch (Exception e){
            log.error("图片合成失败:{}",e);
        }finally {
            for (String path : newPaths){
                FileUtil.del(path);
            }
        }
    }

    public static void base64ToFile(String base64String, File outputFile){
        // 1. 去除Base64字符串中的前缀（如果有）
        String base64Data = base64String.split(",").length > 1 ?
                base64String.split(",")[1] :
                base64String;

        // 2. 解码Base64字符串为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(base64Data);

        // 3. 将字节数组写入文件
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(decodedBytes);
        }catch (Exception e){
            log.error("文件写入失败:{}",e);
        }
    }

    private static String compressToBase64Str(File file, float scale, String suffix) {
        try {
            BufferedImage templateImage = ImageIO.read(file);
            //原始图片的长度和宽度
            int height = templateImage.getHeight();
            int width = templateImage.getWidth();
            //压缩之后的长度和宽度
            int doWithHeight = (int) (scale * height);
            int dowithWidth = (int) (scale * width);
            BufferedImage finalImage = new BufferedImage(dowithWidth, doWithHeight, BufferedImage.TYPE_INT_RGB);
            finalImage.getGraphics()
                    .drawImage(templateImage.getScaledInstance(dowithWidth, doWithHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
            //写入bos
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(finalImage, suffix, bos);
            String encodeStr = Base64Utils.encodeToString(bos.toByteArray());
            int strSize = encodeStr.length();
            log.info("压缩比例={}，压缩后大小={}", scale, strSize);
            if (strSize < 1 * 1024 * 1024 || scale < 0.7f) {
                return encodeStr;
            }
            compressToBase64Str(file, scale - 0.1f, suffix);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AfsBaseException("压缩失败");
        } finally {
            //删除临时文件
            FileUtil.del(file);
        }
        return null;
    }

    /**
     * ocr识别
     * @param file
     * @param affiliatedUnitDto
     * @param personDetail
     * @return
     */
    @Nullable
    private CaseBusinessLicenseInfo ocrHandler(File file,ApplyAffiliatedUnitDto affiliatedUnitDto, CaseCustIndividualDto personDetail,CaseBaseInfo caseBaseInfo) throws Exception {
        String yes=AfsEnumUtil.key(YesOrNoEnum.YES);
        String no=AfsEnumUtil.key(YesOrNoEnum.NO);
        //OCR识别项
        CaseBusinessLicenseInfo businessLicenseInfo=new CaseBusinessLicenseInfo();
        businessLicenseInfo.setAffiliatedNameIsSame(no);
        businessLicenseInfo.setWorkNameIsSame(no);
        businessLicenseInfo.setSocUniCrtCodeIsSame(no);
        //没有返回营业期限字段，默认就是有效的2025-05-27业务需求
        businessLicenseInfo.setPeriodIsSame(yes);
        FileInputStream os=null;
        try {
            os=new FileInputStream(file);
            String imageBaseStr = Base64Utils.encodeToString(os.readAllBytes());
            OcrReqData ocrReqData = new OcrReqData(imageBaseStr);
            Response<OcrResData> ocrRes = ocrService.callThirdSystem(new Request<>(null, OCRType.BIZ_LICENSE_OCR, ocrReqData));
            if (!ocrRes.isSuccess()||ocrRes.getData()==null) {
                throw new AfsBaseException("营业执照OCR异常");
            }
            JSONObject respData = JSONObject.parseObject(JSONObject.toJSONString(ocrRes.getData()));
            JSONObject dataS = respData.getJSONObject("respData");
            if (dataS.isEmpty()) {
                throw new AfsBaseException("营业执照OCR结果为空");
            }
            TencentBizLicenseResp tencentBizLicenseResp = dataS.toJavaObject(TencentBizLicenseResp.class);
            if (affiliatedUnitDto.getAffiliatedName().equals(tencentBizLicenseResp.getName())) {
                businessLicenseInfo.setAffiliatedNameIsSame(yes);
            }
            if (personDetail.getUnitName().equals(tencentBizLicenseResp.getName())) {
                businessLicenseInfo.setWorkNameIsSame(yes);
            }
            CaseConfParam caseConfParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                    .eq(CaseConfParam::getParamType, Const.ATTACHMENT_RULES_UNIT)
                    .eq(CaseConfParam::getParamStatus, com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum.yes.key()));
            if (ObjectUtil.isNotEmpty(caseConfParam)){
                if (caseConfParam.getParamValue().contains(caseBaseInfo.getProductName())){
                    businessLicenseInfo.setWorkNameIsSame(yes);
                }
            }
            if (StringUtils.isNotBlank(tencentBizLicenseResp.getPeriod())) {
                if (tencentBizLicenseResp.getPeriod().contains("至")) {
                    String time = tencentBizLicenseResp.getPeriod().split("至")[1];
                    if (time.contains("年")) {
                        time = time.replace("年", "-").replace("月", "-").replace("日", "");
                        Long ms = DateUtil.parse(time, "yyyy-MM-dd").getTime() - System.currentTimeMillis();
                        if (ms < 0) {
                            businessLicenseInfo.setPeriodIsSame(no);
                        }
                    } else {
                        if (!time.equals("长期")) {
                            Long ms = DateUtil.parse(time, "yyyy-MM-dd").getTime() - System.currentTimeMillis();
                            if (ms < 0) {
                                businessLicenseInfo.setPeriodIsSame(no);
                            }
                        }
                    }
                }
            }
            if (StrUtil.isNotBlank(tencentBizLicenseResp.getSetDate())){
                if (tencentBizLicenseResp.getSetDate().contains("年")){
                    tencentBizLicenseResp.setSetDate(tencentBizLicenseResp.getSetDate().replace("年","-").replace("月","-").replace("日",""));
                }
                Date establishmentTime = DateUtil.parse(tencentBizLicenseResp.getSetDate(),"yyyy-MM-dd");
                Long months = DateUtil.betweenDay(establishmentTime,new Date(),true);
                CaseConfParam confParam = caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                        .eq(CaseConfParam::getParamType, Const.ATTACHMENT_RULES_HALF_YEAR)
                        .eq(CaseConfParam::getParamStatus, com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum.yes.key()));
                CaseConfParam confParam1 = caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                        .eq(CaseConfParam::getParamType, Const.ATTACHMENT_RULES_ONE_YEAR)
                        .eq(CaseConfParam::getParamStatus, com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum.yes.key()));
                if (confParam.getParamValue().contains(caseBaseInfo.getProductName())){
                    businessLicenseInfo.setIsEstablishmentTime(months >= 175 ? "1" : "0");
                } else if (confParam1.getParamValue().equals(caseBaseInfo.getProductName())) {
                    businessLicenseInfo.setIsEstablishmentTime(months >= 360 ? "1" : "0");
                }else {
                    businessLicenseInfo.setIsEstablishmentTime("1");
                }
            }

        } catch (Exception e) {
            throw e;
        }finally {
            if (os!=null) {
                os.close();
            }
        }
        return businessLicenseInfo;
    }

    private String makeData(String pureJson,int seal,StatementAutomaticRecognition statementAutomaticRecognition,CaseBaseInfo caseBaseInfo,CaseCustInfo custInfo,String bankCardNo,String code,CaseCustInfo custInfo1) {
        JSONObject newApply = new JSONObject();
        JSONObject newApprove = new JSONObject();

        newApply.put("客户姓名",custInfo.getCustName());
        newApply.put("身份证号",custInfo.getCertNo());
        newApply.put("卡号",bankCardNo);
        if (StrUtil.isNotBlank(code)){
            newApply.put("密码",code);
        }
        statementAutomaticRecognition.setCustName(custInfo.getCustName());
        statementAutomaticRecognition.setCertNo(custInfo.getCertNo());
        statementAutomaticRecognition.setBankCardNo(bankCardNo);

        //解析JSON
        JSONObject data = JSONObject.parseObject(pureJson);

        String flowType = data.getString("流水类型");
        statementAutomaticRecognition.setFlowType(data.getString("流水类型"));

        int isNameSame = 1;
        int isCertNoSame = 1;
        if (StrUtil.equals("银行卡",flowType)){
            String name = null;
            if (StrUtil.isNotEmpty(data.getString("户名"))){
                name = data.getString("户名");
            }else {
                name = data.getString("姓名");
            }
            isNameSame = StrUtil.equals(name,custInfo.getCustName()) ? 1 : 0;
            newApprove.put("与本人姓名比对",isNameSame);
            newApprove.put("与本人证件号比对",1);
            statementAutomaticRecognition.setIsCertNoSame(1);
            statementAutomaticRecognition.setIsNameSame(isNameSame);
        }else if (StrUtil.equals("支付宝",flowType) || StrUtil.equals("微信",flowType)){
            isCertNoSame = StrUtil.equals(data.getString("身份证号").toLowerCase(),custInfo.getCertNo().toLowerCase()) ? 1 : 0;
            isNameSame = StrUtil.equals(data.getString("姓名"),custInfo.getCustName()) ? 1 : 0;
            newApprove.put("与本人姓名比对",isNameSame);
            newApprove.put("与本人证件号比对",isCertNoSame);
            statementAutomaticRecognition.setIsCertNoSame(isCertNoSame);
            statementAutomaticRecognition.setIsNameSame(isNameSame);
        }else {
            isCertNoSame = 0;
            isNameSame = 0;
            newApprove.put("与本人姓名比对",isNameSame);
            newApprove.put("与本人证件号比对",isCertNoSame);
            statementAutomaticRecognition.setIsCertNoSame(isCertNoSame);
            statementAutomaticRecognition.setIsNameSame(isNameSame);
        }

        if (ObjectUtil.isNotEmpty(custInfo1)){
            if (isNameSame == 0){
                isNameSame = StrUtil.equals(data.getString("姓名"),custInfo1.getCustName()) ||
                        StrUtil.equals(data.getString("户名"),custInfo1.getCustName()) ? 1 : 0;
                newApprove.put("与本人姓名比对",isNameSame);
                statementAutomaticRecognition.setIsNameSame(isNameSame);
            }
            if (isCertNoSame == 0){
                isCertNoSame = StrUtil.equals(data.getString("身份证号").toLowerCase(),custInfo1.getCertNo()) ? 1 : 0;
                newApprove.put("与本人证件号比对",isCertNoSame);
                statementAutomaticRecognition.setIsCertNoSame(isCertNoSame);
            }
        }


        int incomeExpenditureTypes = data.getInteger("收/支类型");
        int tradingPartner = data.getInteger("交易对象");
        int differenceVerification = data.getInteger("差额核对");
        newApprove.put("收/支类型",incomeExpenditureTypes);
        newApprove.put("交易对象",tradingPartner);
        newApprove.put("差额核对",differenceVerification);
        statementAutomaticRecognition.setIncomeExpenditureTypes(incomeExpenditureTypes);
        statementAutomaticRecognition.setTradingPartner(tradingPartner);
        statementAutomaticRecognition.setDifferenceVerification(differenceVerification);

        Date startDate = null;
        Date endDate = null;
        if (StrUtil.isNotBlank(data.getString("起始日期"))){
            startDate = DateUtil.parse(data.getString("起始日期"));
        }
        if (StrUtil.isNotBlank(data.getString("结束日期"))){
            endDate = DateUtil.parse(data.getString("结束日期"));
        }

        int isFifteenDays = 1;
        int isSixMonths = 1;
        if (endDate != null){
            long days = DateUtil.betweenDay(endDate,caseBaseInfo.getPassFirstDate(),true);
            isFifteenDays = days < 15 ? 1 : 0;
            statementAutomaticRecognition.setIsFifteenDays(isFifteenDays);
            newApprove.put("截止日15天内", isFifteenDays);
        }else {
            isFifteenDays = 0;
            statementAutomaticRecognition.setIsFifteenDays(isFifteenDays);
            newApprove.put("截止日15天内",isFifteenDays);
        }
        if (endDate != null && startDate != null){
            long days = DateUtil.betweenDay(startDate,endDate,true);
            long day = DateUtil.betweenDay(endDate,caseBaseInfo.getPassFirstDate(),true);
            isSixMonths = (days >= 175 && day < 180) ? 1 : 0;
            newApprove.put("六个月内",isSixMonths);
            statementAutomaticRecognition.setIsSixMonths(isSixMonths);
        }else {
            isSixMonths = 0;
            statementAutomaticRecognition.setIsSixMonths(isSixMonths);
            newApprove.put("六个月内",isSixMonths);
        }

        newApprove.put("印章",seal);
        statementAutomaticRecognition.setIsSeal(seal);
        if (isNameSame == 0 || isCertNoSame == 0 || isFifteenDays == 0 || isSixMonths == 0 || differenceVerification == 0
                || incomeExpenditureTypes == 0 || tradingPartner == 0 || seal == 0){
            statementAutomaticRecognition.setOverallJudgment(0);
        }else {
            statementAutomaticRecognition.setOverallJudgment(1);
        }
        JSONObject json = new JSONObject();
        json.put("apply",newApply);
        json.put("approve",newApprove);
        return json.toJSONString();
    }

    /**
     * 同步解析驾驶证信息
     * @param applyNo
     * @param comAttachmentFile
     */
    @Override
    public void driverRecognitionSame(String applyNo, ComAttachmentFile comAttachmentFile) {
        log.info("进行驾驶证自动识别，申请编号：{},文件id：{}", applyNo, comAttachmentFile.getId());
        driverAnalySis(applyNo, comAttachmentFile, null, null, null, null);
    }

    /**
     * 异步解析驾驶证信息
     *
     * @param applyNo
     * @param comAttachmentFile
     * @param caseBaseInfo
     * @param caseCustInfoList
     * @param faceList
     * @param garFaceList
     */
    @Async("dSTaskExecutor")
    @Override
    public void driverRecognitionAsync(String applyNo, ComAttachmentFile comAttachmentFile, CaseBaseInfo caseBaseInfo, ArrayList<CaseCustInfoDto> caseCustInfoList, List<ComAttachmentFile> faceList, List<ComAttachmentFile> garFaceList) {
        log.info("异步进行驾驶证自动识别，申请编号：{},文件id：{}", applyNo, comAttachmentFile.getId());
        driverAnalySis(applyNo, comAttachmentFile, caseBaseInfo, caseCustInfoList, faceList, garFaceList);
    }

    /**
     * 营运人证ds解析（同步）
     *
     * @param applyNo
     * @param comAttachmentFile
     */
    @Override
    public void operatorDsRecognitionSame(String applyNo, ComAttachmentFile comAttachmentFile) {
        log.info("同步进行营运人证ds解析，申请编号：{},文件id：{}", applyNo, comAttachmentFile.getId());
        operatorDsAnalysis(applyNo, comAttachmentFile, null, null, null);
    }

    /**
     * 营运人证ds解析（异步）
     *
     * @param applyNo
     * @param comAttachmentFile
     * @param caseBaseInfo
     * @param custInfo
     * @param faceList
     */
    @Async("dSTaskExecutor")
    @Override
    public void operatorDsRecognitionAsync(String applyNo, ComAttachmentFile comAttachmentFile, CaseBaseInfo caseBaseInfo, CaseCustInfo custInfo, List<ComAttachmentFile> faceList) {
        log.info("异步进行营运人证ds解析，申请编号：{},文件id：{}", applyNo, comAttachmentFile.getId());
        operatorDsAnalysis(applyNo, comAttachmentFile, caseBaseInfo, custInfo, faceList);
    }

    private void driverAnalySis(String applyNo, ComAttachmentFile comAttachmentFile, CaseBaseInfo caseBaseInfo, ArrayList<CaseCustInfoDto> caseCustInfoList, List<ComAttachmentFile> faceList, List<ComAttachmentFile> garFaceList) {
        try {
            Map<String, JSONObject> map = deepSeekDrivingInfoService.ocrDriver(applyNo, comAttachmentFile, generateUrl(comAttachmentFile.getFileId()), caseBaseInfo, caseCustInfoList, faceList, garFaceList);
            JSONObject applyJson = map.get("apply");
            JSONObject approveJson = map.get("approve");
            JSONObject res = new JSONObject();
            res.put("apply", applyJson);
            res.put("approve", approveJson);
            DeepSeekDrivingInfo one = deepSeekDrivingInfoService.getOne(Wrappers.<DeepSeekDrivingInfo>lambdaQuery()
                    .eq(DeepSeekDrivingInfo::getApplyNo, applyNo)
                    .eq(DeepSeekDrivingInfo::getFileId, comAttachmentFile.getId()));
            DeepSeekDrivingInfo seekDrivingInfo = new DeepSeekDrivingInfo();
            //驾驶证正页，驾驶证副页是纸质
            String driverType = applyJson.getString("证件类型");
            if (!"驾驶证正页".equals(driverType) && !"驾驶证副页".equals(driverType) && !"纸质驾驶证".equals(driverType)) {
                //非纸质，调用ds
                log.info("开始deepseek解析");
                seekDrivingInfo.setAnalysisType("ds");
                seekDrivingInfo.setOcrResult(res.toJSONString());
                JSONArray jsonArray = generateJson(comAttachmentFile, applyNo, true);
                String content = deepSeekProperties.getDriverContent();
                String result = deepseekIntelligentResultsService.callDsRead(content, jsonArray.toString(), deepSeekProperties.getDriverReadKey());
                if (StrUtil.isNotBlank(result)) {
                    String pureJson = extractPureJson(result);
                    seekDrivingInfo.setDsParam(content);
                    JSONObject dsRes = JSONObject.parseObject(pureJson);
                    res.put("apply", dsRes);
                    approveJson.put("DS真假(电)", "真".equals(dsRes.get("DS真假判断建议")) ? "1" : "0");
                    if ("学习驾驶证明".equals(dsRes.getString("文件类型"))) {
                        deepSeekDrivingInfoService.checkLearningCertificate(applyNo, approveJson, dsRes, map.get("other"), caseBaseInfo, caseCustInfoList, comAttachmentFile, faceList, garFaceList);
                    }else {
                        if (applyJson.get("ocr识别异常") != null) {
                            deepSeekDrivingInfoService.checkOther(applyNo, approveJson, dsRes, caseBaseInfo, caseCustInfoList, comAttachmentFile, faceList, garFaceList, map.get("other"));
                        }
                    }
                    seekDrivingInfo.setDsResult(res.toJSONString());
                } else {
                    log.error("deepseek驾驶证自动解析失败");
                }
            } else {
                log.info("开始ocr解析");
                seekDrivingInfo.setAnalysisType("ocr");
                seekDrivingInfo.setOcrResult(res.toJSONString());
            }
            if (one != null) {
                seekDrivingInfo.setId(one.getId());
            }
            seekDrivingInfo.setApplyNo(applyNo);
            seekDrivingInfo.setDsTime(new Date());
            seekDrivingInfo.setFileId(comAttachmentFile.getId());
            deepSeekDrivingInfoService.saveOrUpdate(seekDrivingInfo);
        } catch (Exception e) {
            log.error("驾驶证自动解析失败", e);
        }
    }

    private void operatorDsAnalysis(String applyNo, ComAttachmentFile comAttachmentFile, CaseBaseInfo caseBaseInfo, CaseCustInfo custInfo, List<ComAttachmentFile> faceList) {
        try {
            DeepSeekOperatorInfo one = deepSeekOperatorInfoService.getOne(Wrappers.<DeepSeekOperatorInfo>lambdaQuery()
                    .eq(DeepSeekOperatorInfo::getApplyNo, applyNo)
                    .eq(DeepSeekOperatorInfo::getFileId, comAttachmentFile.getId()));
            DeepSeekOperatorInfo seekOperatorInfo = new DeepSeekOperatorInfo();
            JSONObject res = new JSONObject();
            JSONArray jsonArray = generateJson(comAttachmentFile, applyNo, true);
            String curStr = "当前时间为" + DateUtil.format(new Date(), DatePattern.NORM_DATE_FORMAT) + ",";
            String content = curStr + deepSeekProperties.getOperatorContent();
            String result = deepseekIntelligentResultsService.callDsRead(content, jsonArray.toString(), deepSeekProperties.getOperatorReadKey());
            if (StrUtil.isNotBlank(result)) {
                String pureJson = extractPureJson(result);
                seekOperatorInfo.setDsParam(content);
                JSONObject dsRes = JSONObject.parseObject(pureJson);
                deepSeekOperatorInfoService.checkApproveResult(res, dsRes, applyNo, comAttachmentFile, generateUrl(comAttachmentFile.getFileId()), caseBaseInfo, custInfo, faceList);
                JSONObject approveJson = (JSONObject) res.get("approve");
                List<String> noveList = List.of("其他", "纸质营运人证-1", "纸质营运人证-2");
                String fileType = dsRes.getString("文件类型");
                if (!noveList.contains(fileType)) {
                    Object seal = res.get("seal");
                    boolean sFlag = false;
                    if (seal != null) {
                        JSONObject sealJson = (JSONObject) seal;
                        if (!sealJson.isEmpty()) {
                            boolean falg = sealJson.getBoolean("error");
                            JSONArray array = sealJson.getJSONArray("seal_result");
                            boolean isExist = areAllDetectBoxesNotEmpty(array);
                            if (!falg && isExist) {
                                sFlag = true;
                            }
                        }
                    }
                    approveJson.put("印章", sFlag ? "1" : "0");
                }
                res.put("approve", approveJson);
                seekOperatorInfo.setDsResult(res.toJSONString());
            } else {
                log.error("deepseek营运人证自动解析失败");
            }
            if (one != null) {
                seekOperatorInfo.setId(one.getId());
            }
            seekOperatorInfo.setApplyNo(applyNo);
            seekOperatorInfo.setDsTime(new Date());
            seekOperatorInfo.setFileId(comAttachmentFile.getId());
            deepSeekOperatorInfoService.saveOrUpdate(seekOperatorInfo);
        } catch (Exception e) {
            log.error("营运人证ds解析失败", e);
        }
    }


    private JSONArray generateJson(ComAttachmentFile comAttachmentFile, String applyNo, boolean ossFlag) {
        JSONArray jsonArray = new JSONArray();
        JSONObject object = new JSONObject();
        if (ossFlag) {
            String ossUrl = generateOssUrl(comAttachmentFile.getFileId());
            object.put("file_url", "http://"+ossUrl);
            object.put("file_name", ossUrl.substring(ossUrl.lastIndexOf("/") + 1));
        } else {
            object.put("file_url", generateUrl(comAttachmentFile.getFileId()));
            object.put("file_name", comAttachmentFile.getFileName());
        }
        jsonArray.add(object);
        return jsonArray;
    }

    public static boolean areAllDetectBoxesNotEmpty(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            return false;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject obj = jsonArray.getJSONObject(i);
            // 检查detect_box是否存在且不为空
            if (!obj.containsKey("detect_box") || obj.get("detect_box") == null) {
                return false;
            }

            // 获取detect_box数组
            JSONArray boxArray = obj.getJSONArray("detect_box");
            if (boxArray == null || boxArray.isEmpty()) {
                return false;
            }

            // 检查detect_box中的每个元素都不为null
            for (int j = 0; j < boxArray.size(); j++) {
                if (boxArray.get(j) == null) {
                    return false;
                }
            }
        }
        return true;
    }

    private String generateUrl(String fileId) {
        Long date = System.currentTimeMillis();
        String verifyCode= DigestUtils.md5Hex(fileId+deepSeekProperties.getSalt()+date);
        String url = String.format(deepSeekProperties.getFilePrefix(),fileId,date,verifyCode);
        return url;
    }

    /**
     * 获取文件oss访问路径
     * @param accessKey
     * @return
     */
    private String generateOssUrl(String accessKey) {
        IResponse<String> response = fileCenterFeign.getFileOssUrl(accessKey);
        Assert.isTrue(CommonConstants.SUCCESS.equals(response.getCode()), "获取文件oss访问路径失败");
        return response.getData();
    }

    private static String extractPureJson(String str) {
        JSONObject jsonObject = JSON.parseObject(str);
        String content = jsonObject.getString("content");
        // 找到第一个{和最后一个}的位置
        int start = content.indexOf("{");
        int end = content.lastIndexOf("}");

        if (start >= 0 && end > start) {
            return content.substring(start, end + 1).replace("```","").replace("json","");
        }
        return content; // 如果没有找到，返回原内容
    }
    @Async("dSTaskExecutor")
    @Override
    public void titleDeedVerification(String applyNo, List<ComAttachmentFile> fileList, String productName) {
        log.info("进件提交开始，进行不动产自动识别，申请编号：{}", applyNo);
        AtomicReference<DeepSeekFileType> fileType = new AtomicReference<>();
        String content = "";
        StringBuilder stringBuilder = new StringBuilder();
        JSONObject jsonObject = new JSONObject();
        AtomicBoolean hasHomePage = new AtomicBoolean(false);
        AtomicBoolean hasAppend = new AtomicBoolean(false);
        AtomicBoolean hasSeal = new AtomicBoolean(false);
        boolean found = fileList.stream().anyMatch(comAttachmentFile -> {
            JSONObject resultJson = null;
            byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
            String imageBaseStr = Base64Utils.encodeToString(tData);
            OcrReqData ocrReqData = new OcrReqData(imageBaseStr);
            String chnResult = "";
            try  {
                Response<OcrResData> response = ocrService.callThirdSystem(new Request<>(null, OCRType.REAL_ESTATE_CERTIFICATE, ocrReqData));
                SinoOcrResp data = (SinoOcrResp) response.getData();
                resultJson = data.getRespData().getJSONObject(0).getJSONArray("Result")
                        .getJSONObject(0).getJSONArray("ResultList").getJSONObject(0);
                 chnResult = resultJson.getString("cls_chn_result");
            } catch (Exception e) {
                log.error("不动产识别失败", e);
            }
            if (EmptyUtils.isNotEmpty(chnResult) &&  "不动产权证首页".equals(chnResult)) {
                fileType.set(DeepSeekFileType.REAL_ESTATE_CERTIFICATE);
                hasHomePage.set(true);
                return true;
            }
            if(EmptyUtils.isNotEmpty(chnResult) && (chnResult.contains("不动产权") || chnResult.contains("房产证"))){
                JSONArray fieldList = resultJson.getJSONArray("FieldList");
                fileType.set(DeepSeekFileType.REAL_ESTATE_CERTIFICATE);
                String obligee = fieldList.stream()
                        .map(field -> (JSONObject) field) // 转换为 JSONObject
                        .filter(fieldObj -> "权利人".equals(fieldObj.getString("chn_key"))) // 筛选目标键
                        .map(fieldObj -> fieldObj.getString("value")) // 提取值
                        .filter(StringUtils::isNotBlank) // 确保值不为空
                        .findFirst().orElse(null);

                String commonSituation = fieldList.stream()
                        .map(field -> (JSONObject) field) // 转换为 JSONObject
                        .filter(fieldObj -> "共有情况".equals(fieldObj.getString("chn_key"))) // 筛选目标键
                        .map(fieldObj -> fieldObj.getString("value")) // 提取值
                        .filter(StringUtils::isNotBlank) // 确保值不为空
                        .findFirst().orElse(null);

                if(EmptyUtils.isNotEmpty(commonSituation) && EmptyUtils.isEmpty(obligee) && !commonSituation.contains("有")){
                    fileType.set(DeepSeekFileType.PURCHASE_CONTRACT);
                    return true;
                }
            }
            return false;
        });
        log.info("当前文件类型是{},found是{}",  fileType.get(),found);
        for (ComAttachmentFile comAttachmentFile : fileList) {
            byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
            String imageBaseStr = Base64Utils.encodeToString(tData);
            OcrReqData ocrReqData = new OcrReqData(imageBaseStr);
            Response<OcrResData> response = null;
            if (EmptyUtils.isNotEmpty(fileType.get()) && StrUtil.equals(fileType.get().getCode(), DeepSeekFileType.REAL_ESTATE_CERTIFICATE.getCode())) {
                response = ocrService.callThirdSystem(new Request<>(null, OCRType.REAL_ESTATE_CERTIFICATE, ocrReqData));
                SinoOcrResp data1 = (SinoOcrResp) response.getData();
                JSONObject resultJson = new JSONObject();
                log.info("调用房产证 OCR response:{}", data1);
                try {
                    resultJson = data1.getRespData().getJSONObject(0).getJSONArray("Result")
                            .getJSONObject(0).getJSONArray("ResultList").getJSONObject(0);
                } catch (Exception e) {
                    log.error("调用房产证 OCR error:{}", e.getMessage());
                }
                JSONArray fieldList = resultJson.getJSONArray("FieldList");
                if (EmptyUtils.isNotEmpty(fieldList) && !fieldList.isEmpty()) {
                    fieldList.forEach(field -> {
                        JSONObject fieldObj = (JSONObject) field;
                        String chnKey = fieldObj.getString("chn_key");
                        String value = fieldObj.getString("value");
                        if (StringUtils.isNotBlank(chnKey) && StringUtils.isNotBlank(value)) {
                            jsonObject.put(chnKey, value);
                        }
                        if ("附记".equals(chnKey) && EmptyUtils.isNotEmpty(value)) {
                            hasAppend.set(true);
                        }
                    });
                    content = jsonObject.toString();
                    log.info("不动产自动识别成功，content：{}", content);
                } else {
                    String normalOcr = normalOcr(ocrReqData);
                    if (EmptyUtils.isNotEmpty(normalOcr) && normalOcr.contains("附记")) {
                        hasAppend.set(true);
                    }
                    content = content + normalOcr;
                }
                String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId();
                //文件下载及识别智能信息对比项
                HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                        .timeout(20000)
                        .execute()
                        .writeBody(FileUtil.file(savePath));
                JSONObject sealRecognition = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getFileId()), applyNo, FileUtil.file(savePath));
                if (ObjectUtil.isNotEmpty(sealRecognition)) {
                    JSONArray sealResult = Optional.ofNullable(sealRecognition.getJSONArray("seal_result")).orElse(new JSONArray());
                    if (EmptyUtils.isNotEmpty(sealResult)) {
                        hasSeal.set(true);
                    }
                }
            } else {
                String extracted = extracted(applyNo, comAttachmentFile, hasSeal, fileType);
                 ocrReqData = new OcrReqData(extracted);
                String normalOcr = normalOcr(ocrReqData);
                try {
                    if (normalOcr.contains("票") || normalOcr.contains("税")) {
                        response = ocrService.callThirdSystem(
                                new Request<>(null, OCRType.VAT_INVOICE_OCR, ocrReqData));
                        TencentOcrCommonResp invoiceOcr = (TencentOcrCommonResp) response.getData();
                        JSONObject invoiceJson = invoiceOcr.getRespData().getJSONObject("data").getJSONArray("PageInfo").getJSONObject(0)
                                .getJSONArray("Result").getJSONObject(0)
                                .getJSONArray("ResultList").getJSONObject(0);


                        invoiceJson.getJSONArray("FieldList").forEach(field -> {
                            JSONObject fieldObj = (JSONObject) field;
                            if (INVOICE_KEY_LIST.contains(fieldObj.getString("chn_key"))) {
                                jsonObject.put(fieldObj.getString("chn_key"), fieldObj.getString("value"));
                            }
                        });

                        invoiceJson.getJSONArray("DetailList").getJSONObject(0).getJSONArray("FieldList").forEach(field -> {
                            JSONObject fieldObj = (JSONObject) field;
                            if (INVOICE_KEY_LIST.contains(fieldObj.getString("chn_key"))) {
                                jsonObject.put(fieldObj.getString("chn_key"), fieldObj.getString("value"));
                            }
                        });
                    }
                } catch (Exception e) {
                    log.error("调用发票 OCR error:{}", e.getMessage());
                }
                stringBuilder.append(normalOcr);
                content = stringBuilder.toString();
            }

        }
        if(EmptyUtils.isEmpty(fileType.get())|| (EmptyUtils.isNotEmpty(fileType.get()) && !StrUtil.equals(fileType.get().getCode(),DeepSeekFileType.REAL_ESTATE_CERTIFICATE.getCode()))) {
            try {
                StringBuilder sb = new StringBuilder();
                List<DicDataDto> dicDataDtos = DicHelper.getDicMaps(FILE_TYPE_JUDGE).get(FILE_TYPE_JUDGE);
                for (DicDataDto dto : dicDataDtos) {
                    sb.append(dto.getValue());
                }
                String diction = combinatorialScript(content, java.lang.String.valueOf(sb));
                String result = deepseekIntelligentResultsService.callDeepSeek(diction, deepSeekProperties.getApiKey());
                String pureJson = extractPureJson(result);
                JSONObject judgeObject = JSONObject.parseObject(pureJson);
                String fileTypeResult = judgeObject.getString("合同类型");
                DeepSeekFileType seekFileType = DeepSeekFileType.getByDesc(fileTypeResult);
                fileType.set(seekFileType);
            } catch (Exception e) {
                log.error("进件提交开始，进件失败，content：{}", content);
            }
        }
        log.info("所识别的文件类型，fileType:{}",fileType);
        if(fileType.get() == DeepSeekFileType.REAL_ESTATE_CERTIFICATE){
            jsonObject.put("附页",stringBuilder);
        }
        log.info("进件提交开始，进行不动产自动识别，JSONArray:{}", content);
        if(EmptyUtils.isNotEmpty(fileType.get())){
            parseOcrTextContent(hasHomePage.get(), hasAppend.get(),hasSeal.get(),content,jsonObject, applyNo, fileType.get(),productName);
        }
    }

    private String extracted(String applyNo, ComAttachmentFile comAttachmentFile, AtomicBoolean hasSeal, AtomicReference<DeepSeekFileType> fileType) {
        String savePath = fileProperties.getTempDir() + comAttachmentFile.getFileId();
        //文件下载及识别智能信息对比项
        HttpRequest.get(generateUrl(comAttachmentFile.getFileId()))
                .timeout(20000)
                .execute()
                .writeBody(FileUtil.file(savePath));
        JSONObject sealRecognition = deepseekIntelligentResultsService.callSealRecognition(String.valueOf(comAttachmentFile.getFileId()), applyNo, FileUtil.file(savePath));
        if (ObjectUtil.isNotEmpty(sealRecognition)) {
            JSONArray sealResult = Optional.ofNullable(sealRecognition.getJSONArray("seal_result")).orElse(new JSONArray());
            if (EmptyUtils.isNotEmpty(sealResult)) {
                hasSeal.set(true);
            }
        }
        if ( StrUtil.equals(comAttachmentFile.getFileType().toLowerCase(), "pdf")) {
            String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileName() + "." + fileType;
            FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL,tempPath);
            try (PDDocument document = PDDocument.load(new File(tempPath), "123456")) {
                // 检查文档是否需要密码
                if (document.isEncrypted()) {
                    System.out.println("PDF已成功解密！");
                    // 移除密码保护
                    document.setAllSecurityToBeRemoved(true);
                    // 将PDF转换为字节数组
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    document.save(baos);
                    byte[] pdfBytes = baos.toByteArray();
                    // 转换为Base64字符串
                    return Base64.getEncoder().encodeToString(pdfBytes);
                } else {
                    log.info("PDF未加密，无需解密。");
                    byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
                    return Base64Utils.encodeToString(tData);
                }
            } catch (IOException e) {
                log.error("解密失败：{}", e.getMessage());
            }
        }
        byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
        return Base64Utils.encodeToString(tData);
    }

    private String normalOcr(OcrReqData ocrReqData) {
        Response<OcrResData> response;
        response = ocrService.callThirdSystem(new Request<>(null, OCRType.Normal, ocrReqData));
        TencentOcrCommonResp data = (TencentOcrCommonResp) response.getData();
        JSONArray jsonArray = data.getRespData().getJSONObject("data")
                .getJSONArray("PageInfo").getJSONObject(0).getJSONArray("Result")
                .getJSONObject(0).getJSONObject("UniversalData")
                .getJSONArray("PageData").getJSONObject(0).getJSONArray("RowData");

        return jsonArray.stream()
                .map(item -> ((JSONObject) item).getString("word"))
                .filter(Objects::nonNull)
                .collect(Collectors.joining());
    }

    private void  parseOcrTextContent(boolean hasHomePage,boolean hasAppend,boolean hasSeal,String ocrTextContent, JSONObject invoiceJson, String applyNo ,DeepSeekFileType fileType,String productName) {

        try {
            StringBuilder sb = new StringBuilder();
            List<DicDataDto> dicDataDtos = DicHelper.getDicMaps(fileType.getCode()).get(fileType.getCode());
            for (DicDataDto dto : dicDataDtos){
                sb.append(dto.getValue());
            }
            log.info("进件提交开始，进行不动产自动识别，content:{},invoiceJson:{},fileType:{}",ocrTextContent,invoiceJson,fileType);
            String diction = combinatorialScript(ocrTextContent, java.lang.String.valueOf(sb));
            String result = deepseekIntelligentResultsService.callDeepSeek(diction,deepSeekProperties.getApiKey());
            ChannelBaseInfo channelBaseInfo = caseChannelInfoMapper.getChannelBaseInfo(applyNo);
            if (StrUtil.isNotBlank(result)) {
                String pureJson = extractPureJson(result);
                PropertyLicenseAiRecognition propertyLicenseAiRecognition = new PropertyLicenseAiRecognition();
                PropertyLicenseAiRecognition recognition = propertyLicenseAiRecognitionService.getOne(Wrappers.<PropertyLicenseAiRecognition>lambdaQuery()
                        .eq(PropertyLicenseAiRecognition::getApplyNo, applyNo)
                        .eq(PropertyLicenseAiRecognition::getAuditType, "intelligent"));
                if (ObjectUtil.isNotEmpty(recognition)) {
                    propertyLicenseAiRecognition.setId(recognition.getId());
                }
                propertyLicenseAiRecognition.setFileType(fileType.getDesc());
                if(hasSeal){
                    propertyLicenseAiRecognition.setHasSeal(1);
                }
                List<DicDataDto> exemptMarriageDtos = DicHelper.getDicMaps(EXEMPT_MARRIAGE).get(EXEMPT_MARRIAGE);
                log.info("进件提交开始，进行婚姻产品判断，applyNo:{},exemptMarriageDtos:{}",applyNo,JSON.toJSONString(exemptMarriageDtos));
                log.info("进件提交开始，进行婚姻产品判断，applyNo:{},productName:{}",applyNo,productName);
                propertyLicenseAiRecognition.setExemptMarriageCertificate(
                        exemptMarriageDtos.stream()
                                .anyMatch(dto -> StrUtil.equals(dto.getValue(), productName)) ? 0 : 1);
                JSONObject jsonObject = JSONObject.parseObject(pureJson);
                if(fileType == DeepSeekFileType.REAL_ESTATE_CERTIFICATE) {
                    String obligee = jsonObject.getString("权利人");
                    String located = jsonObject.getString("坐落");
                    String realEstateUnitNo = jsonObject.getString("不动产单元号");
                    List<CaseCustInfo> custInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                            .eq(CaseCustInfo::getApplyNo, applyNo));
                    CaseCustInfo caseCustInfo = custInfoList.stream()
                            .filter(info -> CustRoleEnum.MIANCUST.getCode().equals(info.getCustRole()))
                            .findFirst()
                            .orElse(new CaseCustInfo());
                    if (EmptyUtils.isNotEmpty(obligee) && EmptyUtils.isNotEmpty(located) && EmptyUtils.isNotEmpty(realEstateUnitNo)) {
                        propertyLicenseAiRecognition.setHasInfoPage(1);
                    }
                    List<CaseCustAddress> custAddresses = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                            .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
                    Map<String, CaseCustAddress> caseCustAddressMap = custAddresses.stream().collect(Collectors.toMap(CaseCustAddress::getAddressType, Function.identity()));
                    checkAndPutAddress(propertyLicenseAiRecognition, located, caseCustAddressMap, AddressTypeEnum.CENSUS);
                    checkAndPutAddress(propertyLicenseAiRecognition, located, caseCustAddressMap, AddressTypeEnum.LIVING);
                    checkAndPutCustName(propertyLicenseAiRecognition, obligee, applyNo);
                    if (hasHomePage) {
                        propertyLicenseAiRecognition.setHasHomePage(1);
                    }
                    if (hasAppend) {
                        propertyLicenseAiRecognition.setHasAppendPage(1);
                    }
                    String address = jsonObject.getString("权属地") + jsonObject.getString("坐落");
                    String addressDiction = combinatorialScript(address, deepSeekProperties.getAddressContent());
                    String called = deepseekIntelligentResultsService.callDeepSeek(addressDiction, deepSeekProperties.getApiKey());
                    String extracted = extractPureJson(called);
                    if (StrUtil.equals(extracted, channelBaseInfo.getChannelProvince())) {
                        propertyLicenseAiRecognition.setDealerAddress(1);
                    }
                    propertyLicenseAiRecognition.setResult(pureJson);
                } else if (fileType == DeepSeekFileType.PURCHASE_CONTRACT) {
                    analysisPurchaseContract(invoiceJson, jsonObject, propertyLicenseAiRecognition, channelBaseInfo);
                } else if (fileType == DeepSeekFileType.LOAN_CONTRACT) {
                    analysisPurchaseContract(invoiceJson, jsonObject, propertyLicenseAiRecognition, channelBaseInfo);
                    String loanRepaymentStatement = jsonObject.getString("还贷流水");
                    propertyLicenseAiRecognition.setHasRepaymentTransaction(0);
                    if (StrUtil.equals(loanRepaymentStatement, "有")) {
                        propertyLicenseAiRecognition.setHasRepaymentTransaction(1);
                    }
                }
                propertyLicenseAiRecognition.setApplyNo(applyNo);
                propertyLicenseAiRecognition.setParam(String.valueOf(sb));
                log.info("deepseek不动产自动识别成功{}", JSON.toJSONString(propertyLicenseAiRecognition));
                propertyLicenseAiRecognitionService.saveOrUpdate(propertyLicenseAiRecognition);
            } else {
                log.info("deepseek不动产自动识别失败");
            }
        }catch (Exception e) {
            log.error("deepseek不动产自动识别失败:{}", e.getMessage());
        }
    }

    private void analysisPurchaseContract(JSONObject invoiceJson, JSONObject jsonObject, PropertyLicenseAiRecognition propertyLicenseAiRecognition, ChannelBaseInfo channelBaseInfo) throws Exception {
        String nameOfBuyer = jsonObject.getString("买受人");
        String propertyUse = jsonObject.getString("商品房用途");
        String locationAddress = jsonObject.getString("坐落地址");
        String invoiceLocate = invoiceJson.getString("货物名称");

        propertyLicenseAiRecognition.setIsContractComplete(0);
        propertyLicenseAiRecognition.setIsInvoiceSealPresent(0);
        propertyLicenseAiRecognition.setIsTotalSealPresent(0);
        propertyLicenseAiRecognition.setIsInvoiceAddrMatch(0);
        if(EmptyUtils.isNotEmpty(nameOfBuyer) && EmptyUtils.isNotEmpty(propertyUse) && EmptyUtils.isNotEmpty(locationAddress)){
            propertyLicenseAiRecognition.setIsContractComplete(1);
        }
        if("1".equals(invoiceJson.getString("发票监制章")) ){
            propertyLicenseAiRecognition.setIsInvoiceSealPresent(1);
        }
        if("1".equals(invoiceJson.getString("销售方章"))){
            propertyLicenseAiRecognition.setIsTotalSealPresent(1);
        }
        if(EmptyUtils.isNotEmpty(locationAddress) && EmptyUtils.isNotEmpty(invoiceLocate) && (locationAddress.contains(invoiceLocate) || invoiceLocate.contains(locationAddress))){
            propertyLicenseAiRecognition.setIsInvoiceAddrMatch(1);
        }
        JSONObject orderedJson = new JSONObject(new LinkedHashMap<>());
        // 2. 复制原始字段（假设原始 JSON 对象为 jsonObject）
        orderedJson.putAll(jsonObject.getInnerMap());
        orderedJson.put("发票名称", invoiceJson.getString("发票名称"));
        orderedJson.put("购方名称", invoiceJson.getString("购方名称"));
        orderedJson.put("货物名称", invoiceJson.getString("货物名称"));
        orderedJson.put("金额合计", invoiceJson.getString("金额合计"));
        String address = jsonObject.getString("发票名称") + jsonObject.getString("坐落地址");
        String addressDiction = combinatorialScript(address,deepSeekProperties.getAddressContent());
        String called = deepseekIntelligentResultsService.callDeepSeek(addressDiction, deepSeekProperties.getApiKey());
        String extracted = extractPureJson(called);
        if(StrUtil.equals(extracted, channelBaseInfo.getChannelProvince() )){
            propertyLicenseAiRecognition.setDealerAddress(1);
        }
        orderedJson.remove("还贷流水");
        propertyLicenseAiRecognition.setResult(String.valueOf(orderedJson));
    }

    private String combinatorialScript(String content, String dialogue) {

        JSONArray messages = new JSONArray();
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content",content);

        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", dialogue);

        messages.add(systemMessage);
        messages.add(userMessage);

        return messages.toString();
    }


    private void checkAndPutAddress(PropertyLicenseAiRecognition propertyLicenseAiRecognition, String address, Map<String, CaseCustAddress> caseCustAddressMap, AddressTypeEnum addressTypeEnum) {
        CaseCustAddress custAddress = caseCustAddressMap.get(addressTypeEnum.getCode());
        if (custAddress == null) {
            return;
        }
        List<String> codeList = List.of(custAddress.getProvince(), custAddress.getCity(), custAddress.getCounty());
        IResponse<List<AddrQueryDto>> response;
        try {
            response = addressFeign.getAllLocationCodeInfoByCodes(codeList);
        } catch (Exception e) {
            throw new RuntimeException("Feign调用失败: " + addressTypeEnum, e);
        }

        List<AddrQueryDto> locationInfo = response.getData();
        if (locationInfo == null) {
            return;
        }

        // 根据地址类型选择字段设置方式
        String detailAddress = custAddress.getDetailAddress();
        boolean b = (EmptyUtils.isNotEmpty(detailAddress) && address.contains(detailAddress) || detailAddress.contains(address));
        switch (addressTypeEnum) {
            case LIVING:
                // 居住地址字段
                locationInfo.forEach(item -> {
                    if (address.contains(item.getLabel())) {
                        switch (item.getAddrLevel()) {
                            case ADDR_LEVEL_PROVINCE: propertyLicenseAiRecognition.setResProv(1); break;
                            case ADDR_LEVEL_CITY:     propertyLicenseAiRecognition.setResCity(1); break;
                            case ADDR_LEVEL_COUNTY:   propertyLicenseAiRecognition.setResCounty(1); break;
                            default: break;
                        }
                    }
                });
                    if (b) {
                        propertyLicenseAiRecognition.setResCommunity(1);
                    }

                break;

            case CENSUS:
                // 户籍地址字段
                locationInfo.forEach(item -> {
                    if (address.contains(item.getLabel())) {
                        switch (item.getAddrLevel()) {
                            case ADDR_LEVEL_PROVINCE: propertyLicenseAiRecognition.setAddrProv(1); break;
                            case ADDR_LEVEL_CITY:     propertyLicenseAiRecognition.setAddrCity(1); break;
                            case ADDR_LEVEL_COUNTY:   propertyLicenseAiRecognition.setAddrCounty(1); break;
                            default: break;
                        }
                    }
                });
                if (b) {
                    propertyLicenseAiRecognition.setAddrCommunity(1);
                }
                break;
            default: break;
            }
        }


    private void checkAndPutCustName(PropertyLicenseAiRecognition propertyLicenseAiRecognition,String obligee, String applyNo) {
        // 1. 初始化默认值
        propertyLicenseAiRecognition.setNameApplicant(0);
        propertyLicenseAiRecognition.setNameSpouse(0);
        propertyLicenseAiRecognition.setNameGuarantor(0);

        // 3. 构建角色映射关系
        Map<String, String> roleMapping = new HashMap<>();
        roleMapping.put(CustRoleEnum.MIANCUST.getCode(), "nameApplicant");
        roleMapping.put(CustRoleEnum.GUARANTOR.getCode(), "nameGuarantor");

        CaseCustInfo  custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        CaseCustContact spouse = caseCustContactService.getOne(Wrappers.<CaseCustContact>query().lambda()
                .eq(CaseCustContact::getApplyNo, applyNo)
                .eq(CaseCustContact::getCustRelation, CustRelationEnums.spouse.getIndex()));

        CaseCustInfo  guarantorInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));

        checkAndSetProperty(propertyLicenseAiRecognition, custInfo, obligee,roleMapping);
        checkAndSetProperty(propertyLicenseAiRecognition, guarantorInfo, obligee,roleMapping);

        if  (EmptyUtils.isNotEmpty(spouse) && obligee.contains(spouse.getCustName())) {
                propertyLicenseAiRecognition.setNameSpouse(1);
        }

        int[] values = {
                propertyLicenseAiRecognition.getNameApplicant(),
                propertyLicenseAiRecognition.getNameSpouse(),
                propertyLicenseAiRecognition.getNameGuarantor()
        };
        if (Arrays.stream(values).anyMatch(v -> v == 1)) {
            propertyLicenseAiRecognition.setNameComparison(1);
        }

    }

    private void checkAndSetProperty(PropertyLicenseAiRecognition property, CaseCustInfo custInfo, String obligee, Map<String, String> roleMap) {
        if (custInfo != null) {
            String role = custInfo.getCustRole();
            String field = roleMap.get(role);
            if (field != null && obligee.contains(custInfo.getCustName())) {
                try {
                    BeanUtil.setProperty(property, field, "1");
                } catch (Exception e) {
                    log.warn("字段赋值失败: {}", field);
                }
            }
        }
    }

}

