package com.ruicar.afs.cloud.afscase.approveinspectionrule.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.condition.ApproveInspectionRuleCondition;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.entity.ApproveInspectionRule;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.service.ApproveInspectionRuleService;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.common.utils.SequenceUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 质检规则控制层
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/approveInspectionRule")
@Api("信审质检规则配置")
public class ApproveInspectionRuleController {

    private ApproveInspectionRuleService approveInspectionRuleService;
    private AfsRuleFeign afsRuleInfoService;


    /**
     * 获取配置规则数据
     * @param condition
     * @return
     */
    @PostMapping("/getInspectionRuleList")
    @ApiOperation("多条件分页获取质检规则配置数据")
    public IResponse<ApproveInspectionRule> getInspectionRuleList(@RequestBody ApproveInspectionRuleCondition condition) {
        IPage<ApproveInspectionRule> approveInspectionRuleIPage = approveInspectionRuleService.page(new Page(condition.getPageNumber(),condition.getPageSize()), Wrappers.<ApproveInspectionRule>lambdaQuery()
        .eq(StringUtils.isNotBlank(condition.getRuleName()),ApproveInspectionRule::getRuleName,condition.getRuleName())
        .eq(StringUtils.isNotBlank(condition.getRuleNo()),ApproveInspectionRule::getRuleNo,condition.getRuleNo())
        );

        return IResponse.success(approveInspectionRuleIPage);
    }

    /**
     * 新增质检规则
     * @param approveInspectionRule
     * @return
     */
    @PostMapping("/addRule")
    @ApiOperation("新增质检规则配置数据")
    public IResponse<Boolean> addRule(@RequestBody ApproveInspectionRule approveInspectionRule) {
        //生成规则编号
        approveInspectionRule.setRuleNo(SequenceUtil.getSeq(Const.INSPECTION_NO));
        approveInspectionRuleService.save(approveInspectionRule);
        return new IResponse<Boolean>().setMsg("新增质检规则成功");
    }

    /**
     * 编辑质检规则
     * @param approveInspectionRule
     * @return
     */
    @PostMapping("/editRule")
    @ApiOperation("编辑质检规则")
    public IResponse<Boolean> editRule(@RequestBody ApproveInspectionRule approveInspectionRule) {
        approveInspectionRuleService.updateById(approveInspectionRule);
        return new IResponse<Boolean>().setMsg("编辑质检规则成功");
    }

    /**
     * 批量删除规则
     * @patram ids
     * @reurn
     */
    @PostMapping(value = "/delRuleByIds/{ids}")
    @ApiOperation(value = "批量删除质检规则")
    public IResponse<Object> delRule(@PathVariable String[] ids) {
        //使规则信息表数据失效
        approveInspectionRuleService.deActiveRuleByRuleNo(ids);
        //删除超时效规则数据
        approveInspectionRuleService.removeByIds(Arrays.asList(ids));
        return new IResponse<Object>().setMsg("删除规则成功！");
    }

    /**
     * 更新规则表业务id
     * @param approveInspectionRule
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新质检规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody ApproveInspectionRule approveInspectionRule) {
        //更新状态为启用
        approveInspectionRule.setIsEnable(WhetherEnum.NO.getCode());
        approveInspectionRuleService.updateById(approveInspectionRule);
        return new IResponse<Boolean>().setMsg("保存成功！");
    }

    /**
     * 启用超时效规则数据
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用质检规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable String id) {
        approveInspectionRuleService.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用规则成功！");
    }

    /**
     * 停用超时效规则数据
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用质检规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable String id) {
        approveInspectionRuleService.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用规则成功！");
    }

    /**
     * 失效规则
     * @param ruleId
     * @return
     */
    @PostMapping("/deActiveRuleById/{ruleId}")
    public IResponse deActiveRuleById(@PathVariable("ruleId") String ruleId) {
        //先反激活再失效
        afsRuleInfoService.deActiveRuleByRuleId(Long.valueOf(ruleId));
        return IResponse.success("失效成功");
    }
}
