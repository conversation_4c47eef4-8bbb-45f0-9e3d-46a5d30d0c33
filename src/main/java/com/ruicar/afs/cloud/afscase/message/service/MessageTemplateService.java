package com.ruicar.afs.cloud.afscase.message.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.message.entity.CaseSmsSendRecord;
import com.ruicar.afs.cloud.afscase.message.entity.CaseSmsTemplate;
import com.ruicar.afs.cloud.afscase.message.entity.MessageTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * The interface Message template service.
 *
 * <AUTHOR>
 * @version 1.0
 * @created 2020 /5/19 11:40
 */
public interface MessageTemplateService extends IService<MessageTemplate> {
    /**
     * Assembly information message template.
     *
     * @param applyNo         the apply no
     * @param name            the name
     * @param messageTemplate the message template
     * @return the message template
     */
    public MessageTemplate assemblyInformation(String applyNo, String name, MessageTemplate messageTemplate);

    /**
     * 审批通过 ‘核准’ 短信发送，考虑后期新增短信发送，‘templateType’给予类型单笔逐一发送
     * TemplateTypeEnum 枚举，目前审批结果设定： APPROVE_PERSON("07", "审批通过-信贷专员"),APPROVE_MUSTER("08", "审批通知-客户");
     *
     * @param applyNo      the apply no
     * @param templateType the template type
     */
    public void sendInformation(String applyNo, String templateType);

    /**
     * 审批通过 ‘核准’ 短信发送
     *
     * @param caseApprovedSmsWaitSendInfoList the case approved sms wait send info list
     * @param templateType                    the template type
     */
    public void sendInformation(List<CaseSmsSendRecord> caseApprovedSmsWaitSendInfoList, List<CaseSmsTemplate> templateType);

    /**
     * 发送短信
     *
     * @param applyNo
     * @param templateType
     * @param contractNo
     */
    @Transactional(rollbackFor = Exception.class)
    void sendInformation(String applyNo, String templateType, String contractNo);
}
