package com.ruicar.afs.cloud.afscase.risk.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.risk.entity.CaseTortoiseFraudPush;
import com.ruicar.afs.cloud.afscase.risk.service.CaseTortoiseFraudPushService;
import com.ruicar.afs.cloud.afscase.risk.vo.AntiFraudPushRecordParam;
import com.ruicar.afs.cloud.afscase.risk.vo.PushAntiFraudParam;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.risk.api.enums.tortoise.GwtTortoiseAntiFraudCallStep;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR> Jinbo
 * @Date 2020/08/10 22:49
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/antiFraud")
public class AntiFraudPushController {

    /**
     * Case tortoise fraud push service
     */
    private CaseTortoiseFraudPushService caseTortoiseFraudPushService;

    /**
     * Author Peng Jinbo
     * List anti fraud response
     *
     * @param param param
     * @return the response
     */
    @GetMapping("/list")
    public IResponse listAntiFraud(@ModelAttribute AntiFraudPushRecordParam param) {
        if (Objects.nonNull(param)) {
            List<CaseTortoiseFraudPush> result = caseTortoiseFraudPushService.list(
                    Wrappers.<CaseTortoiseFraudPush>lambdaQuery()
                            .eq(StrUtil.isNotBlank(param.getApplyNo()), CaseTortoiseFraudPush::getApplyNo, param.getApplyNo())
            );
            if(CollectionUtil.isNotEmpty(result)){
                for (CaseTortoiseFraudPush fraudPush : result) {
                    if(Objects.nonNull(fraudPush) && Objects.nonNull(fraudPush.getCallStep())){
                        fraudPush.setCallStepDesc(AfsEnumUtil.desc(fraudPush.getCallStep()));
                    }
                }
            }
            return IResponse.success(result);
        } else {
            return IResponse.success(caseTortoiseFraudPushService.list());
        }
    }

    /**
    * @Description 根据id删除
    * <AUTHOR>
    * @Date 2020/9/1 11:21
    */
    @PostMapping("/remove/{id}")
    public IResponse removeById(@PathVariable("id")String id){
        if(Objects.nonNull(id)){
            caseTortoiseFraudPushService.removeById(id);
        }
        return IResponse.success("操作成功!");
    }

    /**
     * @Description 发现欺诈，保存欺诈信息
     * <AUTHOR>
     * @Date 2020/8/10 19:59
     */
    @PostMapping("/pushAntiFraud")
    public IResponse pushAntiFraud(@RequestBody PushAntiFraudParam param) {
        if (Objects.nonNull(param)) {
            Assert.isTrue(StrUtil.isNotBlank(param.getApplyNo()),"申请编号不可为空");
            Assert.isTrue(StrUtil.isNotBlank(param.getApplyNo()),"调用步骤不可为空");
            GwtTortoiseAntiFraudCallStep callStep = (GwtTortoiseAntiFraudCallStep) AfsEnumUtil.getEnum(param.getCallStep(),GwtTortoiseAntiFraudCallStep.class);
            Assert.notNull(callStep,String.format("非法调用场景: %s",param.getCallStep()));

            CaseTortoiseFraudPush fraudPush = new CaseTortoiseFraudPush();
            fraudPush.setApplyNo(param.getApplyNo());
            fraudPush.setOperateDate(new Date());
            fraudPush.setOperateStaff(SecurityUtils.getUsername());
            fraudPush.setFraudType(param.getFraudTypeTitle());
            fraudPush.setDealerJoin(param.getDealerJoinTitle());
            fraudPush.setDetailMessage(param.getFraudDetailMsg());
            fraudPush.setCallStep(callStep);
            fraudPush.setOperateStaffName(SecurityUtils.getUser().getUserRealName());
            caseTortoiseFraudPushService.save(fraudPush);
            return IResponse.success("操作成功");
        } else {
            return IResponse.fail("操作失败，申请编号为空");
        }
    }


}
