package com.ruicar.afs.cloud.afscase.loanactivaterules.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.loanactivatepool.vo.LoanActivatePoolVO;
import com.ruicar.afs.cloud.afscase.loanactivaterules.condition.LoanActivateRulesCondition;
import com.ruicar.afs.cloud.afscase.loanactivaterules.entity.LoanActivateRules;
import com.ruicar.afs.cloud.afscase.loanactivaterules.service.LoanActivateRulesService;
import com.ruicar.afs.cloud.afscase.loanactivaterules.vo.LoanActivateTrialVO;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 激活规则控制层
 * @date 2020/5/18 17:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/loanActivateRules")
@Api("激活规则配置")
public class LoanActivateRulesController {
    private final LoanActivateRulesService service;

    /**
     * 获取激活规则配置数据
     *
     * @param queryCondition
     * @return
     */
    @PostMapping(value = "/getActivateRulesList")
    @ApiOperation(value = "多条件分页获取激活规则配置数据")
    public IResponse<IPage<LoanActivateRules>> getActivateRulesList(@RequestBody QueryCondition<LoanActivateRulesCondition> queryCondition) {
        LoanActivateRulesCondition condition = queryCondition.getCondition();
        return IResponse.success(service.page(new Page(condition.getPageNumber(), condition.getPageSize()), Wrappers.<LoanActivateRules>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getIsOpen()), LoanActivateRules::getIsOpen, condition.getIsOpen())
                .eq(StringUtils.isNotEmpty(condition.getRulesName()), LoanActivateRules::getRulesName, condition.getRulesName())
                .eq(StringUtils.isNotEmpty(condition.getRulesDesc()), LoanActivateRules::getRulesDesc, condition.getRulesDesc())
                .eq(ObjectUtils.isNotEmpty(condition.getEffectTime()), LoanActivateRules::getEffectTime, condition.getEffectTime())
                .eq(ObjectUtils.isNotEmpty(condition.getRulesName()), LoanActivateRules::getRulesName, condition.getRulesName())));
    }

    /**
     * 新增放款激活规则配置数据
     *
     * @param loanActivateRules
     * @return
     */
    @PostMapping(value = "/addRule")
    @ApiOperation(value = "新增放款激活规则配置数据")
    public IResponse<Boolean> addActivateRules(@RequestBody LoanActivateRules loanActivateRules) {
        service.save(loanActivateRules);
        return new IResponse<Boolean>().setMsg("新增放款激活规则成功！");
    }

    /**
     * 更新规则表业务id
     *
     * @param loanActivateRules
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody LoanActivateRules loanActivateRules) {
        service.updateById(loanActivateRules);
        return new IResponse<Boolean>().setMsg("编辑放款激活规则配置数据！");
    }

    /**
     * 编辑放款激活规则配置数据
     *
     * @param loanActivateRules
     * @return
     */
    @PostMapping(value = "/editRule")
    @ApiOperation(value = "编辑放款激活规则数据")
    public IResponse<Boolean> edit(@RequestBody LoanActivateRules loanActivateRules) {
        service.updateById(loanActivateRules);
        return new IResponse<Boolean>().setMsg("修改放款激活规则成功！");
    }

    /**
     * 批量删除放款激活规则
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/delByIds/{ids}")
    @ApiOperation(value = "批量删除放款激活规则")
    public IResponse<Boolean> del(@PathVariable String[] ids) {
        //使规则信息表数据失效
        service.deActiveRuleByRuleNo(ids);
        //删除放款规则数据
        service.removeByIds(Arrays.asList(ids));
        return new IResponse<Boolean>().setMsg("删除放款激活规则成功！");
    }


    /**
     * 启用放款激活规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用放款激活规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable String id) {
        service.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用放款激活规则成功！");
    }


    /**
     * 停用放款激活规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用放款激活规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable String id) {
        service.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用放款激活规则成功！");
    }


    /**
     * 根据规则匹配待激活数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/getTrialResultData/{id}")
    @ApiOperation(value = "多条件分页获取激活规则配置数据")
    public IResponse<IPage<LoanActivateTrialVO>> getTrialResultData(@PathVariable String id) {
        log.info("*****************begin:组装命中结果数据，业务规则id【" + id + "】**************");
        List<LoanActivateTrialVO> loanActivateTrialVOList = service.dealLoanActivatePoolByRule(id);
        log.info("*******************end:组装命中结果数据，业务规则id【" + id + "】**************");
        return IResponse.success(loanActivateTrialVOList);
    }

    /**
     * 根据规则匹配待激活数据
     *
     * @param loanActivateRulesCondition
     * @return
     */
    @PostMapping(value = "/getTrialResultDatas")
    @ApiOperation(value = "多条件分页获取激活规则配置数据")
    public IResponse getTrialResultDatas(@RequestBody LoanActivateRulesCondition loanActivateRulesCondition) {
        log.info("*****************begin:组装命中结果数据**************");
        IPage<LoanActivatePoolVO> LoanActivatePoolVOList = service.newDealLoanActivatePoolByRule(loanActivateRulesCondition.getPageSize(),loanActivateRulesCondition.getPageNumber());
        log.info("*******************end:组装命中结果数据**************");
        return IResponse.success(LoanActivatePoolVOList);
    }
}
