package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.event.ChannelServiceFeeEvent;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.parameter.commom.enums.AProveBusinessTypeEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@AllArgsConstructor
@Component
public class ChannelServiceFeeCallback implements CommonAdapter {

    private ApplicationEventPublisher eventPublisher;
    private CaseApproveRecordService caseApproveRecordService;

    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
                                       String extParam, Map<String, String> flowVariables) {

        log.info("flow : {} variables : {}", flowInstanceId, flowVariables);
        CaseApproveRecord record = null;
        String applyNo = flowVariables.get(FlowConstant.BUSINESS_NO);
        String periodFlag = flowVariables.get("periodFlag");

        switch (FlowTaskOperationEnum.valueOf(flowVariables.get(FlowConstant.LAST_OPERATION))) {
            case SUBMIT:
                // 正常通过
                eventPublisher.publishEvent(new ChannelServiceFeeEvent(this, applyNo, AProveBusinessTypeEnum.APPROVAL, periodFlag));
                break;
            case REFUSE:
                // 拒绝
                record.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SUGGEST_REJECT_FINAL));
                record.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SUGGEST_REJECT_FINAL));
                eventPublisher.publishEvent(new ChannelServiceFeeEvent(this, applyNo, AProveBusinessTypeEnum.REJECTION, periodFlag));
                break;
            default:
                log.info("not support operation : {}", flowVariables.get(FlowConstant.LAST_OPERATION));
        }

        if (record != null) {
            caseApproveRecordService.save(record);
        }

        return Collections.singletonMap("LoanProcessEndCallback", "ok");
    }
}
