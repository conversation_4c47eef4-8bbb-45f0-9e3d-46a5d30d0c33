package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 租金贷核销项金额确认
 */
@TableName(value = "write_off_capital_fee")
@Data
public class WriteOffCapitalFee extends BaseEntity<WriteOffCapitalFee> {

    /**
     * 核销项编号
     */
    private String applyNo;
    /**
     * 核销项账期
     */
    private String writeOffMonth;
    /**
     * 业务模式
     */
    private String writeOffType;
    /**
     * 经销商编号
     */
    private String channelCode;
    /**
     * 经销商名称
     */
    private String channelFullName;
    /**
     * 已到票金额
     */
    private BigDecimal receiveInvoiceAmt;
    /**
     * 历史已确认金额
     */
    private BigDecimal hisConfirmAmt;
    /**
     *本次确认金额
     */
    private BigDecimal thisConfirmAmt;
    /**
     * 累计已确认金额
     */
    private BigDecimal totalConfirmAmt;
    /**
     * 未确认金额
     */
    private BigDecimal waitConfirmAmt;

    /**
     * 最小未确认金额(查询条件，前端使用)
     */
    @TableField(exist = false)
    private BigDecimal waitAmtMin;
    /**
     * 最大未确认金额(查询条件，前端使用)
     */
    @TableField(exist = false)
    private BigDecimal waitAmtMax;
}
