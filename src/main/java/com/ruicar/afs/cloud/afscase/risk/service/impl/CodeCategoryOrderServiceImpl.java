package com.ruicar.afs.cloud.afscase.risk.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.mapper.CodeCategoryOrderMapper;
import com.ruicar.afs.cloud.afscase.risk.service.CodeCategoryOrderService;
import com.ruicar.afs.cloud.afscase.risk.vo.CodeCategoryOrderCondition;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 规则码分类排序表实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class CodeCategoryOrderServiceImpl extends ServiceImpl<CodeCategoryOrderMapper, CodeCategoryOrder> implements CodeCategoryOrderService {

    /**
     * 分页查询所有的规则码分类排序表信息
     * @param condition 参数
     * @return 返回结果
     */
    @Override
    public IResponse queryByPage(CodeCategoryOrderCondition condition) {
        Page<CodeCategoryOrder> page = new Page<>(condition.getPageNumber(), condition.getPageSize());
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        commonQueryWrapBuild(condition, queryWrapper);
        Page<CodeCategoryOrder> codeCategoryOrderPage = this.baseMapper.selectPage(page, queryWrapper);
        return IResponse.success(codeCategoryOrderPage);
    }

    /**
     * 根据条件查询规则码分类排序表信息
     * @param condition 参数
     * @return
     */
    @Override
    public List<CodeCategoryOrder> queryByCondition(CodeCategoryOrderCondition condition) {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = new LambdaQueryWrapper<>();
        commonQueryWrapBuild(condition, queryWrapper);
        return this.list(queryWrapper);
    }

    public void commonQueryWrapBuild(CodeCategoryOrderCondition condition, LambdaQueryWrapper<CodeCategoryOrder> queryWrapper) {
        queryWrapper.eq(CodeCategoryOrder::getRuleCode, condition.getRuleCode());
        queryWrapper.eq(CodeCategoryOrder::getCategory, condition.getCategory());
        queryWrapper.eq(CodeCategoryOrder::getHiddenFlag, condition.getHiddenFlag());
        queryWrapper.eq(CodeCategoryOrder::getSortOrder, condition.getSortOrder());
        queryWrapper.orderByDesc(CodeCategoryOrder::getUpdateTime);
    }

    /**
     * 根据规则码获取分类
     * @param ruleCode 规则码
     * @return 分类名称
     */
    public String getCategoryByCode(String ruleCode) {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CodeCategoryOrder::getRuleCode, ruleCode)
                .select(CodeCategoryOrder::getCategory);
        CodeCategoryOrder order = this.getOne(queryWrapper);
        return order != null ? order.getCategory() : null;
    }

    /**
     * 根据规则码获取规则记录
     * @param ruleCode 规则码
     * @return 规则记录
     */
    public CodeCategoryOrder getByCode(String ruleCode) {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CodeCategoryOrder::getRuleCode, ruleCode);
        return this.getOne(queryWrapper);
    }

    /**
     * 返回所有规则码code、category、hiddenFlag、order的列表 并且是按order排序的
     * @return List<Map<String, String>>
     */
    @Override
    public List<Map<String, String>> getAllRuleCodes() {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(CodeCategoryOrder::getSortOrder);

        return this.list(queryWrapper).stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());
    }

    /**
     * 返回所有规则码code、category、hiddenFlag="0"、order的列表 并且是按order排序的
     * @return List<Map<String, String>>
     */
    public List<Map<String, String>> getAllShowRuleCodes() {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CodeCategoryOrder::getHiddenFlag, CommonConstants.COMMON_NO)
                .orderByAsc(CodeCategoryOrder::getSortOrder);

        return this.list(queryWrapper).stream()
                .map(this::convertToMap)
                .collect(Collectors.toList());
    }

    /**
     * 保存或更新规则码分类排序表
     *
     * @param codeCategoryOrder
     * @return
     */
    @Override
    public IResponse saveOrUpdateCco(CodeCategoryOrder codeCategoryOrder) {
        // 必要参数校验
        if (StrUtil.isBlank(codeCategoryOrder.getRuleCode())) {
            throw new AfsBaseException("规则码不能为空");
        }
        if (StrUtil.isBlank(codeCategoryOrder.getCategory())) {
            throw new AfsBaseException("分类不能为空");
        }
        if (StrUtil.isBlank(codeCategoryOrder.getHiddenFlag())) {
            codeCategoryOrder.setHiddenFlag(CommonConstants.COMMON_NO);
        }

        if (ObjectUtil.isNotEmpty(codeCategoryOrder.getId())) {
            // 更新时候需要校验规则码是否重复
            LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(CodeCategoryOrder::getRuleCode, codeCategoryOrder.getRuleCode())
                    .ne(CodeCategoryOrder::getId, codeCategoryOrder.getId());
            if (this.count(queryWrapper) > 0) {
                throw new AfsBaseException("规则码重复, 请重新输入");
            }
        }
        boolean saveOrUpdate = this.saveOrUpdate(codeCategoryOrder);
        log.info("保存或更新规则码分类排序表信息，结果[{}]", saveOrUpdate);
        return IResponse.success("");
    }

    /**
     * 获取所有规则码的集合
     * @return Set<String> 规则码集合
     */
    public Set<String> getAllCodeSet() {
        LambdaQueryWrapper<CodeCategoryOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(CodeCategoryOrder::getRuleCode);
        return this.list(queryWrapper).stream()
                .map(CodeCategoryOrder::getRuleCode)
                .collect(Collectors.toSet());
    }

    /**
     * 将CodeCategoryOrder对象转换为Map
     * @param order 规则码分类排序对象
     * @return Map<String, String>
     */
    private Map<String, String> convertToMap(CodeCategoryOrder order) {
        Map<String, String> map = new HashMap<>();
        map.put("code", order.getRuleCode());
        map.put("ruleName", order.getCategory() + "-" + order.getRuleCode());
        map.put("category", order.getCategory());
        map.put("hiddenFlag", order.getHiddenFlag());
        map.put("order", String.valueOf(order.getSortOrder()));
        return map;
    }
}