package com.ruicar.afs.cloud.afscase.channel.enums;


import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *渠道归属
 */
@Getter
public enum ChannelBelongEnum {


    /**
     * Sp channel belong enum.
     * 社会经销商
     */
    SP( "00","社会经销商"),

    /**
     * Direct channel belong enum.
     * 直营经销商
     */
    DIRECT("03","直营经销商");

    private String key;
    private String desc;

    ChannelBelongEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    private static Map<String, ChannelBelongEnum> map = new HashMap<>(ChannelBelongEnum.values().length);

    static {
        for (ChannelBelongEnum value : ChannelBelongEnum.values()) {
            map.put(value.getKey(), value);
        }
    }

    /**
     * Create ChannelBelongEnum.
     */
    public static ChannelBelongEnum create(String key) {
        return map.get(key);
    }
}




