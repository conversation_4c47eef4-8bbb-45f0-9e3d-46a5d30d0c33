package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务费批次详情ExcelVo
 */
@Data
public class BatchInfoExcelVo implements Serializable {

    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号", index = 0)
    private String batchNo;
    /**
     * 核销项编号
     */
    @ExcelProperty(value = "核销项编号", index = 1)
    private String applyNos;
    /**
     * 核销期数
     */
    @ExcelProperty(value = "核销期数", index = 2)
    private String writeOffMonth;
    /**
     * 经销商code
     */
    @ExcelProperty(value = "经销商代码", index = 3)
    private String channelCode;
    /**
     * 经销商name
     */
    @ExcelProperty(value = "经销商名称", index = 4)
    private String channelFullName;
    /**
     * 批次总金额
     */
    @ExcelProperty(value = "批次总金额", index = 5)
    private BigDecimal batchAmount;
    /**
     * 已提取金额
     */
    @ExcelProperty(value = "已提取金额", index = 6)
    private BigDecimal paidAmount;
    /**
     * 剩余提取金额
     */
    @ExcelProperty(value = "剩余提取金额", index = 7)
    private BigDecimal residueAmount;
    /**
     * 发起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "发起时间", index = 8)
    private Date submitTime;
}
