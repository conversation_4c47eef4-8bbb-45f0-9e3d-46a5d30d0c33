package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同明细导出数据
 *
 * @author： qinghao
 * @date： 2022-11-24
 */
@Data
public class ContractDetailManageExcelVO implements Serializable {
    /**
     * 申请编号
     */
    @ExcelProperty(value = "申请编号")
    private String applyNo;
    /**
     * 合同号
     */
    @ExcelProperty(value = "合同号")
    private String contractNo;
    /**
     * 核销项编号
     */
    @ExcelProperty(value = "核销项编号")
    private String baseInfoApply;
    /**
     * 经销商代码
     */
    @ExcelProperty(value = "经销商代码")
    private String channelCode;
    /**
     * 经销商名称
     */
    @ExcelProperty(value = "经销商名称")
    private String channelFullName;
    /**
     * 账期
     */
    @ExcelProperty(value = "账期")
    private String writeOffMonth;
    /**
     * 所属资方
     */
    @ExcelProperty(value = "所属资方")
    private String belongingCapital;
    /**
     * 资方回款时间
     */
    @ExcelProperty(value = "回款时间")
    private Date capitalReturnTime;
    /**
     * 是否生成了核销项(1是，0否)
     */
    @ExcelProperty(value = "是否生成核销项")
    private String writeOffFlag;
    /**
     * 应收对账确认时间
     */
    @ExcelProperty(value = "应收对账确认时间")
    private String receiveConfirmTime;
    /**
     * 是否生成提取项(1是，0否)
     */
    @ExcelProperty(value = "是否生成提取项")
    private String serverTqFlag;
    /**
     * 应收支付确认(1已确认，0未确认)
     */
    @ExcelProperty(value = "应收支付确认")
    private String payConfirmFlag;
    /**
     * 应收支付确认时间
     */
    @ExcelProperty(value = "应收支付确认时间")
    private Date payConfirmTime;
    /**
     * 服务费
     */
    @ExcelProperty(value = "服务费")
    private BigDecimal serviceCharge;
    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String custNameRepeat;
    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 融资总额
     */
    @ExcelProperty(value = "融资总额")
    private BigDecimal loanAmt;
    /**
     * 期数
     */
    @ExcelProperty(value = "期数")
    private String loanTerm;
    /**
     * 客户利率
     */
    @ExcelProperty(value = "客户利率")
    private BigDecimal custRate;
    /**
     * 贴息方式
     */
    @ExcelProperty(value = "贴息类别")
    private String discountType;
    /**
     * 放款时间
     */
    @ExcelProperty(value = "放款时间")
    private Date loanDate;
    /**
     * 合同到期日期
     */
    @ExcelProperty(value = "合同到期日期")
    private Date endDate;
    /**
     * 经销商省份
     */
    @ExcelProperty(value = "经销商省份")
    private String channelProvince;
    /**
     * 经销商城市
     */
    @ExcelProperty(value = "经销商城市")
    private String channelCity;
    /**
     * SAP供应商代码
     */
    @ExcelProperty(value = "SAP供应商代码")
    private String spaSupplierCode;
    /**
     * 城市服务系数（计算规则）
     */
    @ExcelProperty(value = "城市服务系数%")
    private BigDecimal cityServeRate;
    /**
     * 经销商服务系数（计算规则）
     */
    @ExcelProperty(value = "经销商服务系数%")
    private BigDecimal channelServeRate;
    /**
     * 支付标准（计算规则）
     */
    @ExcelProperty(value = "支付标准%")
    private BigDecimal backRate;
    /**
     * 经销商服务瑕疵评价系数（计算规则）
     */
    @ExcelProperty(value = "经销商服务瑕疵评价系数%")
    private BigDecimal flawRate;
    /**
     * 奖惩前金额
     */
    @ExcelProperty(value = "奖惩前金额")
    private BigDecimal beforeAmount;
    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;
    /**
     * 未含税金额
     */
    @ExcelProperty(value = "未含税金额")
    private BigDecimal excludeTaxAmount;
    /**
     * 分摊金额
     */
    @ExcelProperty(value = "分摊金额")
    private BigDecimal apportionAmount;
    /**
     * 已分摊金额
     */
    @ExcelProperty(value = "已分摊金额")
    private BigDecimal apportionedAmount;
    /**
     * 剩余分摊金额
     */
    @ExcelProperty(value = "剩余分摊金额")
    private BigDecimal residueApportionAmount;
    /**
     * 合同状态
     */
    @ExcelProperty(value = "合同状态")
    private String contractStatus;

    /**
     * 提前结清扣罚金额
     */
    @ExcelProperty(value = "提前结清扣罚金额")
    private BigDecimal advanceSettleAmount;

    @ExcelProperty(value = "扣减比例")
    private Integer deductRate;

    @ExcelProperty(value = "提前结清账期")
    private String advanceSettleMonth;

    /**
     * 分摊标识
     */
    @ExcelProperty(value = "分摊标识")
    private String apportionFlag;
    /**
     * 服务费计算错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errMsg;
}
