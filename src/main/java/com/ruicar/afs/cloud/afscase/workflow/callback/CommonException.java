package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseDataPostInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseLoanCannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseLoanCannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseDataPostInfoService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.job.core.util.DateUtil;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStageEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.ExceptionProcessor;
import com.ruicar.afs.cloud.workflow.sdk.dto.notice.ExceptionData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * #<AUTHOR>
 * #@Date 2022/8/1 11:32
 **/
@Slf4j
@Component
@AllArgsConstructor
public class CommonException implements ExceptionProcessor {
    private CaseBaseInfoService caseBaseInfoService;
    private FlowConfigProperties flowConfigProperties;
    private CaseContractInfoService caseContractInfoService;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private CaseDataPostInfoService caseDataPostInfoService;
    private CaseLoanCannelInfoService caseLoanCannelInfoService;
    /**
     * 全局流程异常信息接收
     * 现在只接收信审 有需要后续加
     */
    @Override
    public Boolean onFlowException(ExceptionData exceptionData) {
        log.info("流程异常接收报文{}",JSON.toJSONString(exceptionData));
        // FlowServerExceptionType.TASK_ASSIGN_NO_ONLINE_USER 没有找到处理人, FlowServerExceptionType.TASK_ASSIGN_TASK_OVERLOAD(这个好像是超出权重了)
        if (flowConfigProperties.getApprovePackageId().equals(exceptionData.getFlowPackageId())
                &&flowConfigProperties.getApproveTemplateId().equals(exceptionData.getFlowTemplateId())
        ){
            log.info("信审异常单子：{}",JSON.toJSONString(exceptionData));
            String businessNo = exceptionData.getFlowParams().get("business_no").toString();
            log.info("编号：{}，时间：{}",businessNo, DateUtil.format(new Date(),"yyyy-MM-dd"));
            String exceptionId = exceptionData.getExceptionId();

            List<WorkflowTaskInfo> works = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getTaskId,exceptionData.getTaskId()));
            log.info("编号：{}",works);
            String businessStateIn = AfsEnumUtil.key(BusinessStateInEnum.EXCEPTIONALLOCATION);
            if(works.size() > 0){
                WorkflowTaskInfo workflowTaskInfo = works.get(0);
                if(StringUtils.isNotEmpty(workflowTaskInfo.getAssign())){
                    businessStateIn = AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK);
                }
            }
            log.info("信审异常单子需要修改的单号:"+businessNo+",状态:"+businessStateIn);
            caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                    .set(CaseBaseInfo::getBusinessStateIn,businessStateIn)
                    .set(CaseBaseInfo::getFlowParseExceptionId, exceptionId)
                    .set(CaseBaseInfo::getIsSubmittingApproval,"0")
                    .eq(CaseBaseInfo::getApplyNo, businessNo)
            );
        }
        if (flowConfigProperties.getLoanPackageId().equals(exceptionData.getFlowPackageId())
                &&flowConfigProperties.getLoanTemplateId().equals(exceptionData.getFlowTemplateId())
        ){
            log.info("放款异常单子：{}",JSON.toJSONString(exceptionData));
            String businessNo = exceptionData.getFlowParams().getString("business_no");
            if(StringUtils.isEmpty(businessNo)){
                List<WorkflowTaskInfo> works = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .eq(WorkflowTaskInfo::getTaskId,exceptionData.getTaskId()));
                if(works.size() > 0){
                    WorkflowTaskInfo workflowTaskInfo = works.get(0);
                    businessNo = workflowTaskInfo.getBusinessNo();
                }
            }
            String exceptionId = exceptionData.getExceptionId();
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(new LambdaQueryWrapper<CaseContractInfo>().eq(CaseContractInfo::getApplyNo,businessNo).orderByDesc(CaseContractInfo::getCreateTime).last("limit 1"));
            caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                    .set(CaseContractInfo::getApplyStatus, AfsEnumUtil.key(ApplyStatusEnum.WAIT_ASSIGN))
                    .set(CaseContractInfo::getBusinessStage, BusinessStageEnum.WAIT_ASSIGN.getCode())
                    .set(CaseContractInfo::getWorkExceptionId, exceptionId)
                    .set(CaseContractInfo::getWorkflowPrevStep,100)
                    .eq(CaseContractInfo::getApplyNo, businessNo)
            );
            CaseContractInfo caseContractInfoNew = caseContractInfoService.getOne(new LambdaQueryWrapper<CaseContractInfo>().eq(CaseContractInfo::getApplyNo,businessNo).orderByDesc(CaseContractInfo::getCreateTime).last("limit 1"));
            if(caseContractInfoNew != null){
                log.info("放款修改后数据："+caseContractInfoNew.getApplyStatus()+"----"+caseContractInfo.getBusinessStage());
            }
            log.info("放款审核工作流异常切换applyStatus状态由[{}]切换为[{}]",caseContractInfo == null ? "": caseContractInfo.getApplyStatus(),AfsEnumUtil.key(ApplyStatusEnum.LOAN_QUEUE));
        }
        if (flowConfigProperties.getReconPackageId().equals(exceptionData.getFlowPackageId())
                &&flowConfigProperties.getReconTemplateId().equals(exceptionData.getFlowTemplateId())
        ){
            log.info("信审复议异常单子：{}",JSON.toJSONString(exceptionData));
            String businessNo = "";
            log.info("复议编号：{}，时间：{}",businessNo, DateUtil.format(new Date(),"yyyy-MM-dd"));
            String exceptionId = exceptionData.getExceptionId();

            List<WorkflowTaskInfo> works = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getTaskId,exceptionData.getTaskId()));
            log.info("复议工作流：{}",works);
            if(StringUtils.isEmpty(businessNo)){
                if(works.size() > 0){
                    WorkflowTaskInfo workflowTaskInfo = works.get(0);
                    businessNo = workflowTaskInfo.getBusinessNo();
                }
            }
            if(works.size() > 0){
                WorkflowTaskInfo workflowTaskInfo = works.get(0);
                if(StringUtils.isEmpty(workflowTaskInfo.getAssign())){
                    caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                            .set(CaseBaseInfo::getFlowParseExceptionId, exceptionId)
                            .eq(CaseBaseInfo::getApplyNo, businessNo)
                    );
                }
            }
        }if (flowConfigProperties.getLoanCancelPackageId().equals(exceptionData.getFlowPackageId())
                &&flowConfigProperties.getLoanCancelTemplateId().equals(exceptionData.getFlowTemplateId())
        ){
            log.info("取消放款异常单子：{}",JSON.toJSONString(exceptionData));
            String businessNo = "";
            log.info("取消放款编号：{}，时间：{}",businessNo, DateUtil.format(new Date(),"yyyy-MM-dd"));
            String exceptionId = exceptionData.getExceptionId();

            List<WorkflowTaskInfo> works = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getTaskId,exceptionData.getTaskId()));
            log.info("取消放款工作流：{}",works);
            if(StringUtils.isEmpty(businessNo)){
                if(works.size() > 0){
                    WorkflowTaskInfo workflowTaskInfo = works.get(0);
                    businessNo = workflowTaskInfo.getBusinessNo();
                }
            }
            if(works.size() > 0){
                WorkflowTaskInfo workflowTaskInfo = works.get(0);
                if(StringUtils.isEmpty(workflowTaskInfo.getAssign())){
                    caseLoanCannelInfoService.update(Wrappers.<CaseLoanCannelInfo>lambdaUpdate()
                            .set(CaseLoanCannelInfo::getFlowParseExceptionId, exceptionId)
                            .eq(CaseLoanCannelInfo::getSubmitNo, businessNo)
                    );
                }
            }
        }
        if (flowConfigProperties.getLoanPackageId().equals(exceptionData.getFlowPackageId())
                &&flowConfigProperties.getLoanDataPostNewTemplateId().equals(exceptionData.getFlowTemplateId())
        ){
            log.info("放款资料后置异常单子：{}",JSON.toJSONString(exceptionData));
            String businessNo = "";
            log.info("放款后置编号：{}，时间：{}",businessNo, DateUtil.format(new Date(),"yyyy-MM-dd"));
            String exceptionId = exceptionData.getExceptionId();

            List<WorkflowTaskInfo> works = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                    .eq(WorkflowTaskInfo::getTaskId,exceptionData.getTaskId()));
            log.info("放款资料后置工作流：{}",works);
            if(StringUtils.isEmpty(businessNo)){
                if(works.size() > 0){
                    WorkflowTaskInfo workflowTaskInfo = works.get(0);
                    businessNo = workflowTaskInfo.getBusinessNo();
                }
            }
            if(works.size() > 0){
                WorkflowTaskInfo workflowTaskInfo = works.get(0);
                if(StringUtils.isEmpty(workflowTaskInfo.getAssign())){
                    caseDataPostInfoService.update(Wrappers.<CaseDataPostInfo>lambdaUpdate()
                            .set(CaseDataPostInfo::getFlowParseExceptionId, exceptionId)
                            .set(CaseDataPostInfo::getDataPostStatus, CaseConstants.DATA_POST_EXCEPTION_ALLOCATION)
                            .eq(CaseDataPostInfo::getApplyNo, businessNo)
                    );
                }
            }
        }
        return true;
    }
}
