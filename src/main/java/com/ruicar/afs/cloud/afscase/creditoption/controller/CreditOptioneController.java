package com.ruicar.afs.cloud.afscase.creditoption.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.creditoption.condition.CreditOptionCondition;
import com.ruicar.afs.cloud.afscase.creditoption.entity.CreditOption;
import com.ruicar.afs.cloud.afscase.creditoption.service.CreditOptionService;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 参数配置功能
 * @created 2020/5/19 11:40
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("参数配置接口")
@RequestMapping("/creditOption")
public class CreditOptioneController {

    private CreditOptionService ceditOptionService;

    @PostMapping(value = "/queryOptionList")
    @ApiOperation(value = "多条件分页获取信审参数配置信息数据")
    public IResponse<IPage<CreditOption>> queryOptionList(@RequestBody QueryCondition<CreditOptionCondition> creditOption) {
        return IResponse.success(ceditOptionService.page(new Page(creditOption.getPageNumber(), creditOption.getPageSize()), Wrappers.<CreditOption>query().lambda()
                .like(StringUtils.isNotEmpty(creditOption.getCondition().getParameterName()), CreditOption::getParameterName, creditOption.getCondition().getParameterName())
                .eq(StringUtils.isNotEmpty(creditOption.getCondition().getStatus()), CreditOption::getStatus, creditOption.getCondition().getStatus())


        ));
    }

    @RequestMapping(value = "/deleteOption/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "通过id删除")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("删除参数配置")
    public IResponse<Boolean> deleteOption(@PathVariable String id) {
        CreditOption creditOption = ceditOptionService.getById(id);
        if (creditOption == null) {
            return new IResponse<Boolean>().setMsg("通过id删除数据成功");
        }
        ceditOptionService.removeById(id);
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }

    @PostMapping(value = "/addOption")
    @ApiOperation(value = "新增参数配置")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("新增参数配置")
    public IResponse<Boolean> addOption(@Valid @RequestBody CreditOption creditOption) {
        String parameterName = creditOption.getParameterName();
        List<CreditOption> list = ceditOptionService.list(Wrappers.<CreditOption>lambdaQuery()
        .eq(CreditOption::getParameterName,parameterName));
        if (list != null && list.size() > 0) {
            return new IResponse<Boolean>().setMsg("参数名称:" + parameterName + "重复,请重新提交！").setCode("0001");
        }else {
            ceditOptionService.save(creditOption);
        }
        return new IResponse<Boolean>().setMsg("新增参数配置成功");
    }

    @PostMapping(value = "/editOption")
    @ApiOperation(value = "修改参数配置")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("修改参数配置")
    public IResponse<Boolean> editOption(@RequestBody CreditOption creditOption) {
        String parameterName = creditOption.getParameterName();
        List<CreditOption> list = ceditOptionService.list(Wrappers.<CreditOption>lambdaQuery()
                .eq(CreditOption::getParameterName,parameterName));
        if (list != null && list.size() > 0) {
            if (list.get(0).getId().equals(creditOption.getId())) {
                ceditOptionService.updateById(creditOption);
            }else {
                return new IResponse<Boolean>().setMsg("参数名称:" + parameterName + "重复,请重新提交！").setCode("0001");
            }
        }else {
            ceditOptionService.updateById(creditOption);
        }
        return new IResponse<Boolean>().setMsg("修改参数配置成功");
    }
}