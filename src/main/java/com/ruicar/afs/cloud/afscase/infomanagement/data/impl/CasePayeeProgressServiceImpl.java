package com.ruicar.afs.cloud.afscase.infomanagement.data.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.infomanagement.data.CasePayeeProgressService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CasePayeeProgress;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CasePayeeProgressMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 2022/1/5 22:09
 * @Description:
 */
@Slf4j
@Service
@AllArgsConstructor
public class CasePayeeProgressServiceImpl extends ServiceImpl<CasePayeeProgressMapper, CasePayeeProgress> implements CasePayeeProgressService {
}
