package com.ruicar.afs.cloud.afscase.mq.receiver.processor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.StepParam;
import com.ruicar.afs.cloud.afscase.autoaudit.util.ActiveStepUtil;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.channel.utils.CaseConfig;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanactivatepool.entity.LoanActivatePool;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.LoanActivateService;
import com.ruicar.afs.cloud.afscase.message.condition.MessageTemplateCondition;
import com.ruicar.afs.cloud.afscase.message.service.MessageTemplateService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.ArchieLoanMoneySender;
import com.ruicar.afs.cloud.afscase.risk.service.CaseTortoiseService;
import com.ruicar.afs.cloud.channel.dto.AffiliatedRealtimeDataDto;
import com.ruicar.afs.cloud.channel.dto.QuotaUseInfoDto;
import com.ruicar.afs.cloud.channel.feign.ChannelApiFeign;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ActivateStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ArchiveMQNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TemplateTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractBusinessEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.archive.ApplyCreditPool;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.LoanActivateReceiveNoticeDTO;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.listener.AfsMqBizProcessor;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.product.sdk.service.feign.ProductFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Description 合同激活通知
 * <AUTHOR>
 * @Date 2020/08/12
 */
@AllArgsConstructor
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.ruicar.afs.cloud.mq.rabbit",name="enable")
public class ContractActicateNoticeProcessor implements AfsMqBizProcessor<LoanActivateReceiveNoticeDTO> {

    private final LoanActivateService loanActivateService;
    private final CaseContractInfoService caseContractInfoService;
    private final CaseApproveRecordService caseApproveRecordService;
    private final CaseBaseInfoService caseBaseInfoService;
    private final CaseTortoiseService caseTortoiseService;
    private final ArchieLoanMoneySender archieLoanMoneySender;
    private final ChannelApiFeign channelApiFeign;
    private final CaseConfig caseConfig;
    private final CaseCarInfoService caseCarInfoService;
    private  final CaseMainInfoService caseMainInfoService;
    private final MessageTemplateService messageTemplateService;
    private final ProductFeign productFeign;

    @Override
    public boolean processMessage(LoanActivateReceiveNoticeDTO loanActivateReceiveNoticeDTO) throws Exception {
        try {
            log.info(">>>>>>>>>>接收合同激活通知 START>>>>>>>>>>>>", loanActivateReceiveNoticeDTO);
            this.dealContractNotice(loanActivateReceiveNoticeDTO);
            log.info(">>>>>>>>>>接收合同激活通知 END>>>>>>>>>>>>", loanActivateReceiveNoticeDTO);
        } catch (Exception e) {
            log.error("接收合同激活通知失败", loanActivateReceiveNoticeDTO, e.getMessage());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public MqTransCode getCode() {
        return MqTransCode.AFS_CONTRACT_BASIC_CASE_CTM_ACTIVATION_STATUS;
    }

    /**
     * @Description 解析GPS申请信息
     * <AUTHOR>
     * @Date 2020/08/10
     */
    public void dealContractNotice(LoanActivateReceiveNoticeDTO loanActivateReceiveNoticeDTO) {
        Assert.isTrue(null!=loanActivateReceiveNoticeDTO,"合同推送通知信息异常，接口参数不存在...");
        Assert.isTrue(null!=loanActivateReceiveNoticeDTO.getContractNo(),"合同系统推送合同号不存在...");
        log.info("收到合同系统回传数据：{}",JSONObject.toJSONString(loanActivateReceiveNoticeDTO));
        ContractBusinessEnum contractBusinessEnum = loanActivateReceiveNoticeDTO.getCaseType();
        switch (contractBusinessEnum){
            case activationSuccess:
                doActivationSuccess(loanActivateReceiveNoticeDTO);
                break;
            case activationFail:
                doActivationFail(loanActivateReceiveNoticeDTO);
                break;
            case cancelContract:
                doCancelContract(loanActivateReceiveNoticeDTO);
                break;
            case normalSettle:
                doNormalSettle(loanActivateReceiveNoticeDTO);
                break;
            case advanceSettle:
                doAdvanceSettle(loanActivateReceiveNoticeDTO);
                break;
            case closeContract:
                doCloseContract(loanActivateReceiveNoticeDTO);
                break;
            case revokeHandle:
                doRevokeHandle(loanActivateReceiveNoticeDTO);
            default:
                log.error(loanActivateReceiveNoticeDTO.getContractNo()+"收到合同系统推送数据，未找到相应命令："+contractBusinessEnum);
                break;
        }
    }
    public void doActivationSuccess(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同激活成功保存业务数据开始=======");
        LoanActivatePool pool=loanActivateService.getOne(Wrappers.<LoanActivatePool>lambdaQuery().eq(LoanActivatePool::getContractNo,dto.getContractNo()));
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        if(pool.getActStatus().equals(ActivateStatusEnum.ACTIVATING.getStatus())||pool.getActStatus().equals(ActivateStatusEnum.UN_ACTIVATE.getStatus())){
            pool.setActStatus(ActivateStatusEnum.ACTIVATE.getStatus());
            pool.setActTime(dto.getCaseDate());
            pool.setContractActivateFlag(WhetherEnum.YES.getCode());//默认落库成功
            loanActivateService.updateById(pool);

            caseContractInfo.setStartDate(dto.getCaseDate());
            caseContractInfo.setEndDate(dto.getContractEndDate());
            caseContractInfo.setContractStatus(ContractStatusEnum.contractEffective);
            caseContractInfo.setLoanDate(dto.getCaseDate());
            caseContractInfoService.updateById(caseContractInfo);
            log.info(dto.getContractNo()+"======合同激活成功保存业务数据结束=======");
            //修改主状态
            CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
            caseMainUpdateCondition.setApplyNo(caseContractInfo.getApplyNo());
            caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_6600);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);

            /**继续执行剩下的步骤*/
            StepParam stepParams=new StepParam();
            stepParams.setContractNo(caseContractInfo.getContractNo());
            stepParams.setSkip(true);
            ActiveStepUtil.prevSteps(stepParams);

            //合同激活，推送归档系统
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()));
            try{
                log.info("======推送归档系统Begin===========");
                CaseContractInfo caseContract = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                        .eq(CaseContractInfo::getContractNo, dto.getContractNo()));
                log.info("======applyNo======{}",caseContractInfo.getApplyNo());
                log.info("======caseChannelInfo======{}", JSON.toJSONString(caseContractInfo));
                ApplyCreditPool applyCreditPool = new ApplyCreditPool();
                applyCreditPool.setOrderType(caseBaseInfo.getOrderType());
                applyCreditPool.setCreateBy(caseBaseInfo.getCreateBy());
                applyCreditPool.setContractNo(dto.getContractNo());
                applyCreditPool.setApplyNo(caseContractInfo.getApplyNo());
                applyCreditPool.setNode(AfsEnumUtil.key(ArchiveMQNodeEnum.LOAN));
                applyCreditPool.setChannelId(caseContractInfo.getDealerNo());
                applyCreditPool.setChannelName(caseContractInfo.getDealerName());
                applyCreditPool.setDeptId(caseBaseInfo.getDeptId());
                applyCreditPool.setChannelCode(caseContract.getDealerNo());
                applyCreditPool.setChannelBelong(caseBaseInfo.getChannelBelong());
                applyCreditPool.setRentType(caseBaseInfo.getRentType());
                applyCreditPool.setProductName(caseContractInfo.getProductName());
                applyCreditPool.setVehicleTag(caseBaseInfo.getVehicleTag());
                IResponse productResp = productFeign.getProductById(caseContract.getProductId().toString());
                log.info("======订单号{}的产品信息为======{}",caseContractInfo.getApplyNo(), JSON.toJSONString(productResp));
                String productNumber = "";
                if (CommonConstants.SUCCESS.equals(productResp.getCode())) {
                    JSONObject productPlan = Optional.ofNullable(productResp.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
                    productNumber = productPlan.getString("productNumber");
                }
                applyCreditPool.setProductNumber(productNumber);
                log.info("======applyCreditPool======{}",JSON.toJSONString(applyCreditPool));
                AfsTransEntity<ApplyCreditPool> transEntity = new AfsTransEntity<>();
                transEntity.setTransCode(MqTransCode.AFS_ARCHIVE_CREDIT_POOL);
                transEntity.setData(applyCreditPool);
                archieLoanMoneySender.sendCreditPool(transEntity);
                log.info("推送成功-----,{}", JSON.toJSONString(transEntity));

                //信审记录合同激活日志
                CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                caseApproveRecord.setApplyNo(caseContractInfo.getApplyNo());
                caseApproveRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
                caseApproveRecord.setApproveSuggest("合同激活");
                caseApproveRecord.setApproveSuggestName("合同激活");
                caseApproveRecord.setApproveStartTime(new Date());
                caseApproveRecord.setApproveEndTime(new Date());
                caseApproveRecord.setApproveRemark("合同激活");
                caseApproveRecordService.save(caseApproveRecord);
            }catch (Exception e){
                log.error("推送归档系统异常：{},异常原因", dto.getContractNo(), e);
            } finally {
                //清除要设定服务，不清除会导致自己服务出现问题
                AfsEnumUtil.clear();
                log.info("==============归档清除设定服务==============");
            }

            CaseCarInfo caseCarInfo = null;
            try{
                //合同生效后，占用额度，插入实时数据
                Map<String, String> requestHeader = new HashMap<>();
                requestHeader.put("clientId", caseConfig.getChannelClientId());
                requestHeader.put("clientSecret", caseConfig.getChannelClientSecret());

                caseCarInfo =  caseCarInfoService.queryInfoByApplyNo(caseContractInfo.getApplyNo());
                log.info("####挂靠公司额度使用记录->applyNo={},AffCompanyId={}",new Object[]{caseCarInfo.getApplyNo(),caseCarInfo.getAffCompanyId()});
                if(StrUtil.isNotBlank(caseCarInfo.getAffCompanyId())){
                    QuotaUseInfoDto useInfoVo = new QuotaUseInfoDto();
                    useInfoVo.setAgencyNo(caseCarInfo.getAffCompanyId());
                    useInfoVo.setTradeType("0002");
                    useInfoVo.setTradeDate(DateUtil.date());
                    useInfoVo.setBusinessType("01");
                    useInfoVo.setBusinessCategory("1");
                    useInfoVo.setTradeUnits(Integer.parseInt("0"));
                    useInfoVo.setTradeAmount(caseBaseInfo.getLoanAmtRepeat());
                    //保存月度额度使用记录
                    IResponse ir1 = channelApiFeign.quotaUsageRecord(useInfoVo,requestHeader);
                    log.info("####挂靠公司月度额度使用记录->code={},msg={}",new Object[]{ir1.getCode(),ir1.getMsg()});

                    useInfoVo.setBusinessCategory("2");
                    useInfoVo.setTradeUnits(Integer.parseInt("0"));
                    useInfoVo.setTradeAmount(caseBaseInfo.getLoanAmtRepeat());
                    //保存总额度使用记录
                    IResponse ir2 = channelApiFeign.quotaUsageRecord(useInfoVo,requestHeader);
                    log.info("####挂靠公司总额度使用记录->code={},msg={}",new Object[]{ir2.getCode(),ir2.getMsg()});
                    useInfoVo.setBusinessCategory("3");
                    useInfoVo.setTradeUnits(Integer.parseInt("1"));
                    useInfoVo.setTradeAmount(BigDecimal.ZERO);
                    //保存台数使用记录
                    IResponse ir3 = channelApiFeign.quotaUsageRecord(useInfoVo,requestHeader);
                    log.info("####挂靠公司月度台数使用记录->code={},msg={}",new Object[]{ir3.getCode(),ir3.getMsg()});

                    AffiliatedRealtimeDataDto dataDto = new AffiliatedRealtimeDataDto();
                    dataDto.setAffiliatedNo(caseCarInfo.getAffCompanyId());
                    dataDto.setApplyNo(caseContractInfo.getApplyNo());
                    dataDto.setApplyDate(caseContractInfo.getLoanDate());
                    dataDto.setNodeType("3");
                    dataDto.setPassNum(Integer.parseInt("1"));
                    dataDto.setPassMoney(caseBaseInfo.getLoanAmtRepeat());
                    dataDto.setApplyDate(DateUtil.date());
                    //挂靠实时数据调用
                    IResponse ir4 = channelApiFeign.realtimeData(dataDto,requestHeader);
                    log.info("####挂靠实时数据调用->code={},msg={}",new Object[]{ir4.getCode(),ir4.getMsg()});
                }

            }catch (Exception e){
                log.error("挂靠公司额度使用：{},异常原因", caseCarInfo.getAffCompanyId(), e);
            }




        }else {
            log.info(dto.getContractNo()+"合同状态为:{}，跳过修改",pool.getActStatus());
        }
    }

    public void doActivationFail(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同激活失败保存业务数据开始=======");
        LoanActivatePool pool=loanActivateService.getOne(Wrappers.<LoanActivatePool>lambdaQuery().eq(LoanActivatePool::getContractNo,dto.getContractNo()));
        if(pool.getActStatus().equals(ActivateStatusEnum.ACTIVATING.getStatus())||pool.getActStatus().equals(ActivateStatusEnum.UN_ACTIVATE.getStatus())){
            pool.setContractActivateFlag(WhetherEnum.NO.getCode());
            pool.setActStatus(ActivateStatusEnum.UN_ACTIVATE.getStatus());
            loanActivateService.updateById(pool);
            log.info(dto.getContractNo()+"======合同激活失败保存业务数据结束=======");
        }else {
            log.info(dto.getContractNo()+"合同状态为:{}，跳过修改",pool.getActStatus());
        }
    }
    public void doCancelContract(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同取消保存业务数据开始=======");
        //修改放款状态
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        caseContractInfo.setContractStatus(ContractStatusEnum.flatCancel);
        caseContractInfo.setApplyStatus(ApplyStatusEnum.FLAT_CANCEL.getState());
        caseContractInfo.setUpdateTime(dto.getCaseDate());
        caseContractInfoService.updateById(caseContractInfo);
        //修改信审状态
        if(!ObjectUtils.isEmpty(caseContractInfo)){
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo,caseContractInfo.getApplyNo()));
            caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.CANCEL));
            caseBaseInfoService.updateById(caseBaseInfo);
        }
        //放款记录合同激活后取消日志
        CaseApproveRecord lockOprRecord = new CaseApproveRecord();
        lockOprRecord.setContractNo(caseContractInfo.getContractNo());
        lockOprRecord.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        lockOprRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
        lockOprRecord.setApproveSuggestName("合同取消");
        lockOprRecord.setApproveStartTime(new Date());
        lockOprRecord.setApproveEndTime(new Date());
        lockOprRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
        lockOprRecord.setFlowNode(caseContractInfo.getFlowNode());
        caseApproveRecordService.save(lockOprRecord);
        //信审记录合同激活后取消日志
        CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
        caseApproveRecord.setApplyNo(caseContractInfo.getApplyNo());
        caseApproveRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
        caseApproveRecord.setApproveSuggest(ApplyStatusEnum.LOAN_DISCARD.getState());
        caseApproveRecord.setApproveSuggestName("合同取消");
        caseApproveRecord.setApproveStartTime(new Date());
        caseApproveRecord.setApproveEndTime(new Date());
        caseApproveRecord.setApproveRemark("租后合同取消");
        caseApproveRecord.setApproveType(ApproveTypeEnum.CANAEL.getValue());
        caseApproveRecordService.save(caseApproveRecord);
        log.info(dto.getContractNo()+"======合同取消保存业务数据结束=======");
    }
    public void doNormalSettle(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同正常结清保存业务数据开始=======");
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        if(caseContractInfo.getContractStatus().equals(ContractStatusEnum.contractEffective)) {
            caseContractInfo.setContractStatus(ContractStatusEnum.normalSettle);
            caseContractInfo.setSettleDate(dto.getCaseDate());
            caseContractInfo.setSettleType(AfsEnumUtil.key(ContractStatusEnum.normalSettle));
            caseContractInfoService.updateById(caseContractInfo);
            try{
            log.info(dto.getContractNo() + "======合同正常结清保存业务数据结束=======");
            MessageTemplateCondition messageTemplateCondition = new MessageTemplateCondition();
            messageTemplateCondition.setContractNo(dto.getContractNo());
            messageTemplateService.sendInformation(caseContractInfo.getApplyNo(),AfsEnumUtil.key(TemplateTypeEnum.CONTRACT_SETTLE),caseContractInfo.getContractNo());
            }catch (Exception e){
                log.error("合同正常结清发送消息异常",e);
            }
        }else{
            log.info(dto.getContractNo() + "合同状态为非生效状态：{}，跳过修改！",caseContractInfo.getContractStatus());
        }
    }
    public void doAdvanceSettle(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同提前结清保存业务数据开始=======");
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        if(caseContractInfo.getContractStatus().equals(ContractStatusEnum.contractEffective)) {
            caseContractInfo.setContractStatus(ContractStatusEnum.advanceSettle);
            caseContractInfo.setSettleDate(dto.getCaseDate());
            caseContractInfo.setSettleType(AfsEnumUtil.key(ContractStatusEnum.advanceSettle));
            caseContractInfoService.updateById(caseContractInfo);
            log.info(dto.getContractNo() + "======合同提前结清保存业务数据结束=======");
            try {
                MessageTemplateCondition messageTemplateCondition = new MessageTemplateCondition();
                messageTemplateCondition.setContractNo(dto.getContractNo());
                messageTemplateService.sendInformation(caseContractInfo.getApplyNo(),AfsEnumUtil.key(TemplateTypeEnum.CONTRACT_SETTLE),caseContractInfo.getContractNo());
            }catch (Exception e){
                log.error("合同提前结清发送消息异常",e);
            }
        }else{
            log.info(dto.getContractNo() + "合同状态为非生效状态：{}，跳过修改！",caseContractInfo.getContractStatus());
        }
    }
    public void doCloseContract(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同关闭保存业务数据开始=======");
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        caseContractInfo.setContractStatus(ContractStatusEnum.close);
        caseContractInfo.setSettleType(AfsEnumUtil.key(dto.getContractStatus()));
        caseContractInfoService.updateById(caseContractInfo);
        log.info(dto.getContractNo() + "======合同关闭保存业务数据结束=======");
    }
    public void doRevokeHandle(LoanActivateReceiveNoticeDTO dto){
        log.info(dto.getContractNo()+"======合同生效保存业务数据开始=======");
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,dto.getContractNo()));
        caseContractInfo.setContractStatus(ContractStatusEnum.contractEffective);
        caseContractInfo.setSettleType("");
        caseContractInfoService.updateById(caseContractInfo);
        log.info(dto.getContractNo() + "======合同生效保存业务数据结束=======");
    }
}
