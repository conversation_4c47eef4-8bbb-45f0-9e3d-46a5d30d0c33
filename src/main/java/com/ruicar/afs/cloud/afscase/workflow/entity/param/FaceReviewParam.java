package com.ruicar.afs.cloud.afscase.workflow.entity.param;

import com.ruicar.afs.cloud.afscase.workflow.enums.FaceReviewEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 功能说明:
 * <AUTHOR>
 */
@Data
public class FaceReviewParam {
    /**
     * 进件编号
     */
    private String applyNo;
    /**
     * {@link FaceReviewEnum}
     */
    private String faceReviewResult;
    /**
     * 面审视频地址
     */
    private String spUrl;
    /**
     * 客户面审时经度
     */
    private String longitude;
    /**
     * 客户面审时纬度
     */
    private String latitude;
    /**
     * 客户地址
     */
    private String address;
    /**
     * 审批备注
     */
    private String remark;
    /**
     * 坐席名称
     */
    private String seatName;
    /**
     * 工单状态
     */
    private String status;
    /**
     * 办理时间
     */
    private Date processingTime;
    /**
     * 影像件信息
     */
    private List<String> imageUrls;
}
