package com.ruicar.afs.cloud.afscase.autoaudit.service;

import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;


/**
 * The interface Loan risk tips rule service.
 *
 * @Description: 放款风险提示规则
 * @Author: tiankai
 * @Date: 2020 /7/18 20:06
 */
public interface LoanAutoApproveSaveService {

    /**
     * 保存放款自动审核后需要修改的数据
     * @param contractInfo 合同信息
     * @param flag 标识符 1-首次提交，2-非首次提交
     */
    void loanAutoApprovedLogic(CaseContractInfo contractInfo, String flag);

    /**
     * 发送短信
     * @param applyNo 入参
     */
    void sendMessage(String applyNo);
}
