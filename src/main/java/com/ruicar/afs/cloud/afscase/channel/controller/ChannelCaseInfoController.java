package com.ruicar.afs.cloud.afscase.channel.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.approvetask.entity.DebitPayMentVO;
import com.ruicar.afs.cloud.afscase.channel.common.Constants;
import com.ruicar.afs.cloud.afscase.channel.condition.CarDealerCondition;
import com.ruicar.afs.cloud.afscase.channel.condition.ChannelBaseInfoCondition;
import com.ruicar.afs.cloud.afscase.channel.condition.ChannelCustManageDto;
import com.ruicar.afs.cloud.afscase.channel.condition.ChannelReceivablesAccountCondition;
import com.ruicar.afs.cloud.afscase.channel.condition.DistanceDTO;
import com.ruicar.afs.cloud.afscase.channel.dto.ChannelCaseInfoDTO;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAuthorizeRegion;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAuthorizeVehicle;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelCoopeCardealer;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelQuotaInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelReceivablesAccount;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelVoucherInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.CommonCarDealer;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAccountInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAuthorizeRegionService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAuthorizeVehicleService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelCoopeCardealerService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelQuotaInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelVoucherInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.CommonCarDealerService;
import com.ruicar.afs.cloud.afscase.channel.utils.Common;
import com.ruicar.afs.cloud.afscase.channel.utils.DistanceUtil;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelBaseInfoVO;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelCaseInfoVo;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelContractNumberVO;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelInfoModifyVO;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelLimitTakeUpOrLeaseVo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.bizcommon.print.entity.ComPrintFormManage;
import com.ruicar.afs.cloud.bizcommon.print.enums.ServiceClientTypeEnum;
import com.ruicar.afs.cloud.bizcommon.print.service.ComPrintFormManageService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.common.modules.enums.BusinessType;
import com.ruicar.afs.cloud.common.modules.enums.FeeTypeEnum;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.dto.AddrQueryDto;
import com.ruicar.afs.cloud.config.api.address.feign.AddressFeign;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.afscase.channel.common.Constants.BUSINESS_TYPE_NEW_CAR;
import static com.ruicar.afs.cloud.afscase.channel.common.Constants.BUSINESS_TYPE_OLD_CAR;

/**
 * @ClassName:ChannelCaseInfoController
 * @Description: 同步渠道信息案件服务-controller
 * @Author:mingzhi.li
 * @Date:2020/6/9 14:06
 * @Version: V1.0
 **/

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/channelCaseInfo")
@Api(value = "channelCaseInfo", description = "同步进件上线申请信息")
public class ChannelCaseInfoController {
    /**
     * 同步渠道信息保存
     */
    @Autowired
    private ChannelBaseInfoService channelBusiInfoService;

    /**
     * 同步风控信息
     */
    @Autowired
    private ChannelRiskInfoService channelRiskInfoService;

    /**
     * 同步收款账号信息
     */
    @Autowired
    private ChannelAccountInfoService channelAccountInfoService;

    /**
     * 同步保证金信息
     */
    @Autowired
    private ChannelQuotaInfoService channelQuotaInfoService;

    /**
     * 同步授权区域信息
     */
    @Autowired
    private ChannelAuthorizeRegionService channelAuthorizeRegionService;

    /**
     * 同步授权车型信息
     */
    @Autowired
    private ChannelAuthorizeVehicleService channelAuthorizeVehicleService;

    @Autowired
    private ChannelCoopeCardealerService channelCoopeCardealerService;

    private final ChannelVoucherInfoService channelVoucherInfoService;

    private final CommonCarDealerService commonCarDealerService;
    private final CaseChannelInfoService caseChannelInfoService;
    private final CaseContractInfoService caseContractInfoService;
    private final ComPrintFormManageService comPrintFormManageService;
    private final CaseBaseInfoService caseBaseInfoService;

    private final AddressFeign addressFeign;
    /**
     * 获取收款账户列表信息
     * @param channelId 申请编号
     * <AUTHOR>
     * @date 2021年7月6日
     * */
    @ApiOperation(value = "获取收款账户列表信息")
    @PostMapping(value = "/listChannelReceivableByChannelId")
    public IResponse<List<ChannelReceivablesAccount>> listChannelReceivableByChannelId(@RequestParam("channelId") String channelId,@RequestParam("carPayment") String carPayment,@RequestParam("carType") String carType){
        IResponse<List<ChannelReceivablesAccount>> iResponse = new IResponse();
        List<ChannelReceivablesAccount> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                .eq(ChannelReceivablesAccount::getChannelId, channelId)
                .eq(ChannelReceivablesAccount::getCollectionType, carPayment)
                .eq(StringUtils.isNotEmpty(carType), ChannelReceivablesAccount::getAccountAttribute, carType));
        iResponse.setData(accountList);
        return iResponse;
    }


    /**
     * 放款申请 根据渠道ID获取收款账户列表信息（直营分页查询）
     * @param condition 查询条件
     * <AUTHOR>
     * @date 2021年12月22日
     * */
    @ApiOperation(value = "根据渠道ID获取收款账户列表信息(直营)")
    @PostMapping(value = "/queryListChannelReceivable")
    public IResponse queryListChannelReceivable(@RequestBody ChannelReceivablesAccountCondition condition){
        log.info("根据渠道ID获取收款账户列表信息[{}],",JSONObject.toJSONString(condition));
        if(StringUtils.isNotBlank(condition.getApplyNo())){
            CaseBaseInfo caseByApplyNo = caseBaseInfoService.getCaseByApplyNo(condition.getApplyNo());
            //SP和总对总 并且 银行账户和 银行名称 都为空的 情况下 默认匹配订单关联车商和合作商的账户信息
            if(caseByApplyNo != null && StringUtils.isBlank(condition.getReceivingName()) && StringUtils.isBlank(condition.getReceivingAccount())
                    && ( AfsEnumUtil.key(ChannelBelongEnum.SP).equals(caseByApplyNo.getChannelBelong()) ||
                    AfsEnumUtil.key(ChannelBelongEnum.ALL_TO_ALL).equals(caseByApplyNo.getChannelBelong()))){
                List<String> channelNameList =  new ArrayList<>();
                if(StringUtils.isNotBlank(caseByApplyNo.getDealerName())){ channelNameList.add(caseByApplyNo.getDealerName());}
                if(StringUtils.isNotBlank(caseByApplyNo.getChannelFullName())){ channelNameList.add(caseByApplyNo.getChannelFullName());}
                if(channelNameList.size()>0) {
                    IPage<ChannelReceivablesAccount> list = channelAccountInfoService.page(new Page(condition.getPageNumber(), condition.getPageSize())
                            , Wrappers.<ChannelReceivablesAccount>query().lambda()
                                    .eq(ChannelReceivablesAccount::getChannelId, condition.getChannelId())
                                    // todo : yekaixuan 2022-09-30 宝德不区分二手车新车，暂时去掉
//                                    .eq(ChannelReceivablesAccount::getAccountAttribute, condition.getCarType())
                                    .eq(StringUtils.isNotEmpty(condition.getCollectionType()), ChannelReceivablesAccount::getCollectionType, condition.getCollectionType())
                                    .eq(condition.getChannelId() != null, ChannelReceivablesAccount::getChannelId, condition.getChannelId())
                                    .eq(ChannelReceivablesAccount::getStatus, Constants.ENABLED)
                                    .in(ChannelReceivablesAccount::getReceivingName, channelNameList));
                    return IResponse.success(list);
                }
            }
        }
        IPage<ChannelReceivablesAccount> accountList = channelAccountInfoService.page(new Page(condition.getPageNumber(),condition.getPageSize())
                ,Wrappers.<ChannelReceivablesAccount>query().lambda()
                 .eq(ChannelReceivablesAccount::getChannelId, condition.getChannelId())
//                 .eq(ChannelReceivablesAccount::getAccountAttribute, condition.getCarType())
                .eq(StringUtils.isNotEmpty(condition.getCollectionType()), ChannelReceivablesAccount::getCollectionType, condition.getCollectionType())
                .eq(condition.getChannelId() != null, ChannelReceivablesAccount::getChannelId, condition.getChannelId())
                  .eq(ChannelReceivablesAccount::getStatus,Constants.ENABLED)
                        .like(StringUtils.isNotEmpty(condition.getReceivingName()), ChannelReceivablesAccount::getReceivingName, condition.getReceivingName())
                .like(StringUtils.isNotEmpty(condition.getReceivingAccount()), ChannelReceivablesAccount::getReceivingAccount, condition.getReceivingAccount()));
        return IResponse.success(accountList);
    }

    @PostMapping(value = "/saveOrUpdate")
    @ApiOperation(value = "保存渠道信息")
    @SysLog("保存渠道请信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveOrUpdate(@RequestBody ChannelCaseInfoVo vo) {
        if (vo.getChannelBaseInfo().getId() == null) {
            return IResponse.fail("保存失败");
        }
        //业务类型：新车二手车
        String businessType = vo.getBusinessType();
        //基本信息同步
        ChannelBaseInfo channelBaseInfo = vo.getChannelBaseInfo();
        if (channelBaseInfo != null) {
            ChannelBaseInfo baseInfo = channelBusiInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getId, channelBaseInfo.getId()));
            //保存前先删除数据
            if (baseInfo != null) {
                channelBusiInfoService.updateById(channelBaseInfo);
            } else {
                channelBaseInfo.setChannelId(channelBaseInfo.getId());
                channelBaseInfo.setId(channelBaseInfo.getId());
                channelBusiInfoService.save(channelBaseInfo);
            }

            //渠道风控信息同步
            List<ChannelRiskInfo> channelRiskInfoList = vo.getRisk();
            List<ChannelRiskInfo> channelRiskInfoTempList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>query().lambda()
                    .eq(ChannelRiskInfo::getChannelId, channelBaseInfo.getId())
                    .eq(ChannelRiskInfo::getBusinessType, businessType));
            //同步信息前删除数据
            if (channelRiskInfoTempList != null && channelRiskInfoTempList.size() > 0) {
                for (ChannelRiskInfo risk : channelRiskInfoTempList) {
                    channelRiskInfoService.removeById(risk.getId());
                }
            }
            if (channelRiskInfoList != null && channelRiskInfoList.size() > 0) {
                channelRiskInfoService.saveBatch(channelRiskInfoList);
            }

            //渠道同步收款账号表
            List<ChannelReceivablesAccount> channelReceivablesAccountList = vo.getAcount();
            List<ChannelReceivablesAccount> accountList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                    .eq(ChannelReceivablesAccount::getChannelId, channelBaseInfo.getId().toString())
                    .eq(ChannelReceivablesAccount::getAccountAttribute, businessType));
            if (channelReceivablesAccountList != null && channelReceivablesAccountList.size() > 0) {
                if (accountList != null && accountList.size() > 0) {
                    //同步信息前删除数据
                    for (ChannelReceivablesAccount account : accountList) {
                        channelAccountInfoService.removeById(account.getId());
                    }
                }
                if (channelReceivablesAccountList != null && channelReceivablesAccountList.size() > 0) {
                    channelAccountInfoService.saveBatch(channelReceivablesAccountList);
                }
            }

            //渠道同步额度信息表
            List<ChannelQuotaInfo> channelQuotaInfoList = vo.getQuota();
            List<ChannelQuotaInfo> quotaInfoList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfo>query().lambda()
                    .eq(ChannelQuotaInfo::getChannelId, channelBaseInfo.getId())
                    .eq(ChannelQuotaInfo::getBusinessType, businessType));
            if (quotaInfoList != null && quotaInfoList.size() > 0) {
                //同步信息前删除数据
                for (ChannelQuotaInfo quotaInfo : quotaInfoList) {
                    channelQuotaInfoService.removeById(quotaInfo.getId());
                }
            }
            if (channelQuotaInfoList != null && channelQuotaInfoList.size() > 0) {
                channelQuotaInfoService.saveBatch(channelQuotaInfoList);
            }

            //渠道同步授权区域信息表
            List<ChannelAuthorizeRegion> channelAuthorizeRegionList = vo.getRegion();
            channelAuthorizeRegionService.remove(Wrappers.<ChannelAuthorizeRegion>query().lambda()
                    .eq(ChannelAuthorizeRegion::getChannelId, channelBaseInfo.getId())
                    .eq(ChannelAuthorizeRegion::getBusinessType, businessType));
            if (channelAuthorizeRegionList != null && channelAuthorizeRegionList.size() > 0) {
                int size = channelAuthorizeRegionList.size();
                if (size <= 1000) {
                    channelAuthorizeRegionService.saveOrUpdateBatch(channelAuthorizeRegionList);
                } else {
                    for (int i = 0; i < size / 1000; i++) {
                        if (size / 1000 - i > 1) {
                            channelAuthorizeRegionService.saveOrUpdateBatch(channelAuthorizeRegionList.subList(1000 * i, 1000 * i + 999));
                        } else {
                            channelAuthorizeRegionService.saveOrUpdateBatch(channelAuthorizeRegionList.subList(1000 * i, size - 1));
                        }
                    }
                }
            }

            //渠道同步授权车型
            List<ChannelAuthorizeVehicle> authorizeVehicleList = vo.getVehicle();
            channelAuthorizeVehicleService.remove(Wrappers.<ChannelAuthorizeVehicle>query().lambda()
                    .eq(ChannelAuthorizeVehicle::getChannelId, channelBaseInfo.getId())
                    .eq(ChannelAuthorizeVehicle::getBusinessType, businessType));
            if (authorizeVehicleList != null && authorizeVehicleList.size() > 0) {
                int size = authorizeVehicleList.size();
                if (size <= 1000) {
                    channelAuthorizeVehicleService.saveBatch(authorizeVehicleList);
                } else {
                    for (int i = 0; i < size / 1000; i++) {
                        if (size / 1000 - i > 1) {
                            channelAuthorizeVehicleService.saveBatch(authorizeVehicleList.subList(1000 * i, 1000 * i + 999));
                        } else {
                            channelAuthorizeVehicleService.saveBatch(authorizeVehicleList.subList(1000 * i, size - 1));
                        }
                    }
                }
            }

                //判断是否保证金且保证金金额不为零
                if (vo.getRisk() != null && vo.getRisk().size() > 0 && vo.getRisk().get(0).getChannelDepositPaidIn() != null && vo.getRisk().get(0).getChannelDepositPaidIn().compareTo(BigDecimal.ZERO) != 0) {
                    //凭证信息保存并推送数据
                    List<ChannelVoucherInfo> list = new ArrayList<>();
                    //保证金实收
                    BigDecimal channelDepositPaidIn = vo.getRisk().get(0).getChannelDepositPaidIn();
                    ChannelVoucherInfo info = new ChannelVoucherInfo();
                    info.setChannelId(channelBaseInfo.getChannelId());
                    info.setFeeType( FeeTypeEnum.BOND);
                    info.setAmount(channelDepositPaidIn);
                    info.setEventId(channelBaseInfo.getEventId());
                    // 业务类型
                    if (Constants.BUSINESS_TYPE_NEW_CAR.equals(businessType)) { //新车
                        info.setBusinessType(BusinessType.NEW_CAR);
                    } else if (Constants.BUSINESS_TYPE_OLD_CAR.equals(businessType)) {//新车
                        info.setBusinessType(BusinessType.OLD_CAR);
                    } else {
                        Assert.isTrue(false, MessageFormat.format("业务类型:{0},暂不支持！", businessType));
                    }
                    list.add(info);
                    channelVoucherInfoService.saveVoucher(list, channelBaseInfo.getChannelFullName());
                }

        }
        return IResponse.success("保存成功");
    }

    @PostMapping(value = "/updateGrade")
    @ApiOperation(value = "更新合作商评级")
    @SysLog("更新合作商评级")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateGrade(@RequestBody ChannelRiskInfo channelRiskInfo) {
        if (channelRiskInfo.getChannelId() == null) {
            return IResponse.fail("更新合作商评级失败，必传参数id为空");
        }
        try {
            ChannelRiskInfo channelRiskInfo1 = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                    .eq(ChannelRiskInfo::getChannelId, channelRiskInfo.getChannelId())
                    .eq(ChannelRiskInfo::getBusinessType, channelRiskInfo.getBusinessType()));
            if (channelRiskInfo1 != null) {
                channelRiskInfo1.setChannelGrade(channelRiskInfo.getChannelGrade());
                channelRiskInfoService.updateById(channelRiskInfo1);
            }
        } catch (Exception e) {
            log.error("更新合作商评级失败,事务回滚");
            //手动开启事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return IResponse.fail("更新合作商评级失败，请联系管理员！");
        }
        return IResponse.success("更新合作商评级成功！");
    }

    @PostMapping(value = "/limitTakeUpOrLease")
    @ApiOperation(value = "渠道额度占用与释放")
    @SysLog("渠道额度占用与释放")
    @Transactional(rollbackFor = Exception.class)
    public IResponse limitTakeUpOrLease(@RequestBody ChannelLimitTakeUpOrLeaseVo vo) {
        if (StrUtil.isBlank(vo.getChannelId())) {
            return IResponse.fail("渠道id不能为空！");
        }
        if (StrUtil.isBlank(vo.getQuotaType())) {
            return IResponse.fail("额度类型不能为空！");
        }
        if (StrUtil.isBlank(vo.getOperType())) {
            return IResponse.fail("额度操作类型(占用或释放)不能为空！");
        }
        if (StrUtil.isBlank(vo.getBusinessType())) {
            return IResponse.fail("业务类型不能为空！");
        }
        BigDecimal quotaAmount = vo.getQuotaAmount() == null ? BigDecimal.ZERO : vo.getQuotaAmount();
        if (quotaAmount.compareTo(BigDecimal.ZERO) == 0 || quotaAmount.compareTo(BigDecimal.ZERO) == -1) {
            return IResponse.fail("额度必须大于0!");
        }
        List<ChannelQuotaInfo> quotaInfoList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfo>query().lambda()
                .eq(ChannelQuotaInfo::getChannelId, vo.getChannelId())
                .eq(ChannelQuotaInfo::getQuotaType, vo.getQuotaType())
                .eq(ChannelQuotaInfo::getBusinessType, vo.getBusinessType()));
        if (quotaInfoList == null && quotaInfoList.size() == 0) {
            return IResponse.fail("渠道不存在或额度类型不存在或业务类型不存在!");
        }
        channelQuotaInfoService.updateQuotaLimit(vo, quotaInfoList);

        return IResponse.success("操作成功");
    }

    @PostMapping(value = "/upStatusByChannel")
    @ApiOperation(value = "黑名单根据id停用合作商或者直营车商")
    public IResponse upStatusByChannel(@RequestBody List<Long> ids) {
        //截取字符串
        if (ids.size() > 0) {
            for (int j = 0; j < ids.size(); j++) {
                ChannelBaseInfo channel = channelBusiInfoService.getById(ids.get(j));
                if (channel != null) {
                    if (channel.getChannelStatus() != null) {
                        channel.setChannelStatus(Common.STATUS_STOP);
                    }
                    if (channel.getChannelStatusOldCar() != null) {
                        channel.setChannelStatusOldCar(Common.STATUS_STOP);
                    }
                    channel.setUpdateTime(new Date(System.currentTimeMillis()));
                    channel.setUpdateBy(SecurityUtils.getUsername());
                    boolean result = channelBusiInfoService.updateById(channel);

                    //渠道类型为直营车商的 同步停用中间关联表
                    if (Common.DIRECT_CAR_DEALER.equals(channel.getChannelType())) {
                        List<ChannelCoopeCardealer> cardealerTemps = channelCoopeCardealerService.list(Wrappers.<ChannelCoopeCardealer>query().lambda()
                                .eq(ChannelCoopeCardealer::getCardealerId, ids.get(j))
                                .eq(ChannelCoopeCardealer::getCardealerType, Common.DIRECT_CAR_DEALER)
                        );
                        if (cardealerTemps.size() > 0) {
                            for (ChannelCoopeCardealer temp : cardealerTemps) {
                                temp.setStatus(Common.STATUS_STOP);
                                channelCoopeCardealerService.updateById(temp);
                            }
                        }
                    }

                    if (!result) {
                        return IResponse.fail("状态更新失败！");
                    }
                }
            }
        }
        return new IResponse<Boolean>().setMsg("状态更新成功");
    }

    @PostMapping(value = "/upStatusByChannelPass")
    @ApiOperation(value = "黑名单根据id启用合作商或者直营车商")
    public IResponse upStatusByChannelPass(@RequestBody List<Long> ids) {
        if (ids.size() > 0) {
            for (int j = 0; j < ids.size(); j++) {
                ChannelBaseInfo channel = channelBusiInfoService.getById(ids.get(j));
                if (channel != null) {
                    channel.setChannelStatus(Common.STATUS_ENABLE);
                    channel.setChannelStatusOldCar(Common.STATUS_ENABLE);
                    channel.setUpdateTime(new Date(System.currentTimeMillis()));
                    channel.setUpdateBy(SecurityUtils.getUsername());
                    boolean result = channelBusiInfoService.updateById(channel);
                    if (!result) {
                        return IResponse.fail("状态更新失败！");
                    }
                }
            }
        }
        return new IResponse<Boolean>().setMsg("状态更新成功");
    }

    /**
     * 获取合作商/直营车商信息
     *
     * @param condition
     * @return
     */
    @PostMapping(value = "/channelInfoForUserName")
    @ApiOperation("获取合作商/直营车商信息")
    public IResponse<ChannelCaseInfoVo> getAffiliatedUnit(@RequestBody ChannelBaseInfoCondition condition) {

        ChannelBaseInfo temp = channelBusiInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                .eq(ChannelBaseInfo::getId, condition.getChannelId()));

        ChannelCaseInfoVo vo = new ChannelCaseInfoVo();
        if (temp != null) {
            //返回整个渠道信息
            vo.setChannelBaseInfo(temp);

            //查询风控信息
            List<ChannelRiskInfo> channelRiskInfoTempList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>query().lambda()
                    .eq(ChannelRiskInfo::getChannelId, temp.getId().toString())
                    .eq(StringUtils.isNotBlank(condition.getAssociatedChannelId()),ChannelRiskInfo::getAssociatedChannelId, condition.getAssociatedChannelId())
            );
            if (channelRiskInfoTempList != null && channelRiskInfoTempList.size() > 0) {
                //返回风控数据
                vo.setRisk(channelRiskInfoTempList);
            }
            //查询渠道保证金信息
            List<ChannelQuotaInfo> channelQuotaInfoTempList = channelQuotaInfoService.list(Wrappers.<ChannelQuotaInfo>query().lambda()
                    .eq(ChannelQuotaInfo::getChannelId, temp.getId().toString())
                    .eq(StringUtils.isNotBlank(condition.getAssociatedChannelId()),ChannelQuotaInfo::getAssociatedChannelId, condition.getAssociatedChannelId())
            );
            if (channelQuotaInfoTempList != null && channelQuotaInfoTempList.size() > 0) {
                //返回渠道保证金信息
                vo.setQuota(channelQuotaInfoTempList);
            }

            //查询渠道授权区域信息
            List<ChannelAuthorizeRegion> regionTempList = channelAuthorizeRegionService.list(Wrappers.<ChannelAuthorizeRegion>query().lambda()
                    .eq(ChannelAuthorizeRegion::getChannelId, temp.getId().toString())
                    .eq(StringUtils.isNotBlank(condition.getAssociatedChannelId()),ChannelAuthorizeRegion::getAssociatedChannelId, condition.getAssociatedChannelId())
            );
            if (regionTempList != null && regionTempList.size() > 0) {
                //返回授权区域信息
                vo.setRegion(regionTempList);
            }

            List<ChannelReceivablesAccount> accountTempList = channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                    .eq(ChannelReceivablesAccount::getChannelId, temp.getId().toString())
                    .eq(ChannelReceivablesAccount::getStatus,"00")//查询已经启用的
                    .eq(StringUtils.isNotBlank(condition.getAssociatedChannelId()),ChannelReceivablesAccount::getAssociatedChannelId, condition.getAssociatedChannelId())
            );
            if (accountTempList != null && accountTempList.size() > 0) {
                //返回收款账号信息
                vo.setAcount(accountTempList);
            }

            //查询授权车型
            List<ChannelAuthorizeVehicle> vehicleTempList = channelAuthorizeVehicleService.list(Wrappers.<ChannelAuthorizeVehicle>query().lambda()
                    .eq(ChannelAuthorizeVehicle::getChannelId, temp.getId().toString())
                    .eq(StringUtils.isNotBlank(condition.getAssociatedChannelId()),ChannelAuthorizeVehicle::getAssociatedChannelId, condition.getAssociatedChannelId())
            );
            if (vehicleTempList != null && vehicleTempList.size() > 0) {
                //返回授权车型
                vo.setVehicle(vehicleTempList);
            }
        }
        return IResponse.success(vo);
    }

    /**
     * 批量修改经纬度范围同步案件服务
     *
     * @param list
     * @return
     */
    @PostMapping(value = "/saveLlRangeToCase")
    @ApiOperation("批量修改经纬度范围同步案件服务")
    public IResponse<ChannelCaseInfoVo> saveLlRangeToCase(@RequestBody List<ChannelBaseInfo> list) {
        if (list != null && list.size() > 0) {
            channelBusiInfoService.saveOrUpdateBatch(list);
        }
        return IResponse.success("修改成功");
    }

    /**
     * 批量修改区域经理同步案件服务
     *
     * @param condition
     * @return
     */
    @PostMapping(value = "/saveManageToCase")
    @ApiOperation("批量修改经纬度范围同步案件服务")
    public IResponse saveManageToCase(@RequestBody ChannelBaseInfoCondition condition) {
        List<ChannelRiskInfo> riskInfoTempList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>query().lambda().in(ChannelRiskInfo::getChannelId, Arrays.asList(condition.getChannelIds())));
        if (riskInfoTempList != null && riskInfoTempList.size() > 0) {
            riskInfoTempList.forEach(temp -> {
                if (BUSINESS_TYPE_NEW_CAR.equals(temp.getBusinessType())) {
                    if (!StrUtil.isBlank(condition.getCustomerManagerNew())) {
                        temp.setCustomerManager(condition.getCustomerManagerNew());
                    }
                }
                if (BUSINESS_TYPE_OLD_CAR.equals(temp.getBusinessType())) {
                    if (!StrUtil.isBlank(condition.getCustomerManagerOld())) {
                        temp.setCustomerManager(condition.getCustomerManagerOld());
                    }
                }
            });
            channelRiskInfoService.saveOrUpdateBatch(riskInfoTempList);
        }
        return IResponse.success("修改成功");
    }

    @PostMapping(value = "/modifyManageToCase")
    @ApiOperation("批量修改区域经理同步案件服务")
    public IResponse modifyManageToCase(@RequestBody @Validated ChannelCustManageDto condition) {

        channelRiskInfoService.lambdaUpdate().set(ChannelRiskInfo::getCustomerManager,condition.getCustomerManage())
                .set(ChannelRiskInfo::getCustManageId,condition.getCustId()).in(ChannelRiskInfo::getChannelId,condition.getChannelIds()).update();

        return IResponse.success("修改成功");
    }


    /**
     * 查询车辆类型
     *
     * @param condition
     * @return
     */
    @PostMapping(value = "/queryCarType")
    @ApiOperation("查询车辆类型")
    public IResponse queryCarType(@RequestBody ChannelBaseInfoCondition condition) {
        ChannelRiskInfo riskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                .eq(ChannelRiskInfo::getChannelId, condition.getChannelId())
                .eq(ChannelRiskInfo::getBusinessType, condition.getBusinessType()));
        if (riskInfo != null) {
            return IResponse.success(riskInfo.getCarType());
        } else {
            return IResponse.fail("查无数据");
        }
    }

    /**
     * 查询渠道账号
     *
     * @param channelId
     * @param businessType
     * @return
     */
    @RequestMapping(value = "/queryAccountInfo")
    @ApiOperation("查询渠道账号")
    public IResponse queryAccountInfo(@RequestParam("channelId") Long channelId, @RequestParam("businessType") String businessType) {

        ChannelReceivablesAccount channelReceivablesAccount = channelAccountInfoService.getOne(Wrappers.<ChannelReceivablesAccount>query().lambda()
                .eq(ChannelReceivablesAccount::getChannelId, channelId)
                /**  业务类型 by ZC.GUO  **/
                .eq(ChannelReceivablesAccount::getAccountAttribute, businessType)
                /**  车款账号 by ZC.GUO  **/
                .eq(ChannelReceivablesAccount::getCollectionType, "1")
                .eq(ChannelReceivablesAccount::getStatus, "00")
        );
        if (ObjectUtil.isNotNull(channelReceivablesAccount)) {
            return IResponse.success(channelReceivablesAccount);
        } else {
            return IResponse.fail("获取账号信息失败");
        }
    }

    /**
     * 获取地区信息
     *
     * @param condition
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/getLocationFromCase")
    @ApiOperation(value = "获取数据")
    public IResponse<List<AddrQueryDto>> getLocationFromCase(@RequestBody AddrQueryDto condition) {
        return addressFeign.getAddressListByUpercodeAndLevel(condition.getUpperCode(),condition.getLevel());
    }

    /**
     * 获取地区信息
     *
     * @param condition
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/getAddressList")
    @ApiOperation(value = "获取数据")
    public IResponse<List<AddrQueryDto>> getAddressList(@RequestBody AddrQueryDto condition) {
        // 先查询所有合作商省的code
        Set<String> pCode = new HashSet<>();
        List<ChannelBaseInfo> baseInfos = channelBusiInfoService.list();
        if ("1".equals(condition.getLevel())) {
            // 说明需要查询所有省的
            if (baseInfos.size() > 0) {
                for (ChannelBaseInfo b : baseInfos) {
                    if (b.getChannelProvince() != null) {
                        pCode.add(b.getChannelProvince());
                    }
                }
            }
        } else {
            // 说明查询的是市
            if (baseInfos.size() > 0) {
                for (ChannelBaseInfo b : baseInfos) {
                    if (b.getChannelCity() != null) {
                        pCode.add(b.getChannelCity());
                    }
                }
            }
        }
        if (pCode.size() <= 0) {
            // 说明没有渠道合作商省
            throw new AfsBaseException("暂无合作商注册！");
        }


        IResponse<List<AddrQueryDto>> rtn = addressFeign.getAddressListByUpercodeAndLevel(condition.getUpperCode(),condition.getLevel());

        if(StringUtils.equals(rtn.getCode(), CommonConstants.SUCCESS)){
            return  IResponse.success(rtn.getData().stream().filter(va->pCode.contains(va.getValue())).collect(Collectors.toList()));
        }else {
            throw new AfsBaseException("地址数据获取失败");
        }
    }

    @RequestMapping(value = "/getCaseBaseInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取渠道基本信息")
    public IResponse getCaseBaseInfo(@RequestBody ChannelCaseInfoDTO dto) {
        List<ChannelBaseInfo> baseList = channelBusiInfoService.list(Wrappers.<ChannelBaseInfo>query().lambda()
                .like(!ObjectUtils.isEmpty(dto.getChannelFullName()),ChannelBaseInfo::getChannelFullName, dto.getChannelFullName()));
        List<ChannelCaseInfoDTO> dtoList = new ArrayList<>();
        if(baseList!=null && baseList.size()>0){
            for(ChannelBaseInfo temp:baseList){
                ChannelCaseInfoDTO infoDTO = new ChannelCaseInfoDTO();
                infoDTO.setChannelCode(temp.getChannelCode());
                infoDTO.setChannelFullName(temp.getChannelFullName());
                dtoList.add(infoDTO);
            }
        }
        return IResponse.success(dtoList);
    }

    @RequestMapping(value = "/getAllCaseBaseInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取渠道基本信息")
    public IResponse getAllCaseBaseInfo(@RequestBody CarDealerCondition condition) {

        IPage<ChannelBaseInfo> baseList = channelBusiInfoService.page(new Page(condition.getPageNumber(), condition.getPageSize()),Wrappers.<ChannelBaseInfo>query().lambda()
                .like(!ObjectUtils.isEmpty(condition.getSearchName()),ChannelBaseInfo::getChannelFullName, condition.getSearchName()));

        for(ChannelBaseInfo list:baseList.getRecords()){
            list.setLabel(list.getChannelFullName());
            list.setValue(list.getId().toString());
        }
        return IResponse.success(baseList);
    }

    @PostMapping("/getDistanceBychannelId")
    @ApiOperation(value = "通过合作商id和经纬度计算记录")
    public IResponse getDistanceBychannelId(@RequestBody DistanceDTO dto) {
        // 声明一个校验距离
        Double checkDistance =Double.valueOf(0);

        Double distance = Double.valueOf(0);
        log.info("传递过来参数" + dto.toString());
        // 根据渠道id 查询数据信息
        ChannelBaseInfo temp = channelBusiInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                .eq(ChannelBaseInfo::getId, dto.getChannelId()));

        // 看看传递过来的业务类型
        if (Constants.BUSINESS_TYPE_NEW_CAR.equals(dto.getBusinessType())) {
            // 再看看合作商是否控制车商
            if ("0".equals(temp.getChoiceCardealerSwitch())) {
                // 说是不选车商，那么去匹配合作商信息
                log.info("我是新车" + temp.getLongitudeLatitudeSwitch());

                // 说明是新车，取看看查询新车是否控制距离
                if ("0".equals(temp.getLongitudeLatitudeSwitch())) {
                    log.info("新车合作商不控制经纬度！");
                    // 说明新车不需要控制
                    return new IResponse<Boolean>().setData(true).setMsg("成功！");
                } else if ("1".equals(temp.getLongitudeLatitudeSwitch())) {
                    log.info("合作商经度：" + Double.valueOf(temp.getLongitude()).doubleValue());
                    log.info("合作商纬度：" + Double.valueOf(temp.getLatitude()).doubleValue());
                    log.info("传递过来的经度" + dto.getLongitude());
                    log.info("传递过来的纬度" + dto.getLatitude());
                    distance = DistanceUtil.getDistance(Double.valueOf(temp.getLongitude()).doubleValue(), Double.valueOf(temp.getLatitude()).doubleValue(), dto.getLongitude(), dto.getLatitude());

                    // 赋值校验距离
                    checkDistance= Double.valueOf(temp.getLongitudeLatitudeRange());
                    log.info("我计算的距离" + distance);
                    log.info("新车合作商控制经纬度！");
                }
            } else {
                // 说明去选择车商，那么根据车商的信息去匹配数据,判断是直营车商还是普通车商
                if (Common.CHANNEL_TYPE_CAR_COMMON.equals(dto.getDealerType())) {
                    // 说明是普通车商那么去查普通车商的数据
                    CommonCarDealer cardealer = commonCarDealerService.getById(dto.getCardealerId());
                    if (cardealer != null) {
                        // 再去看车商是否控制经纬度
                        if ("0".equals(cardealer.getLongitudeLatitudeSwitch())) {
                            log.info("新车普通车商不控制经纬度！");
                            return new IResponse<Boolean>().setData(true).setMsg("成功！");
                        } else {
                            log.info("新车普通车商控制经纬度！");
                            // 说明需要控制，那么就需要去处理经纬度问题
                            String number = cardealer.getLongitudeLatitude();
                            String[] result = number.split(",");
                            if (result[0] != null && result[0] != "" && result[1] != null && result[1] != "") {
                                distance = DistanceUtil.getDistance(Double.valueOf(result[0]).doubleValue(), Double.valueOf(result[1]).doubleValue(), dto.getLongitude(), dto.getLatitude());

                                // 赋值校验距离
                                checkDistance= Double.valueOf(cardealer.getLongitudeLatitudeRange());
                            }else {
                                log.info("新车普通车商控制经纬度,值获取不正确！");
                            }
                        }
                    } else {
                        log.info("获取新车普通车商数据失败！" + dto.getCardealerId());
                        throw new AfsBaseException("获取车商信息失败!");
                    }
                }else {
                    // 说明是直营车商
                    ChannelBaseInfo dir=channelBusiInfoService.getById(dto.getCardealerId());
                    if(dir!=null){
                        log.info("经纬度新车商名称"+dir.getChannelFullName());
                        log.info("经纬度新车"+dir.getLongitudeLatitudeSwitchOld());
                        if("0".equals(dir.getLongitudeLatitudeSwitch())){
                            log.info("新车直营车商不控制经纬度！");
                            // 说明新车不需要控制
                            return new IResponse<Boolean>().setData(true).setMsg("成功！");
                        }else {
                            // 再看直营车商 经纬度控制
                            distance = DistanceUtil.getDistance(Double.valueOf(dir.getLongitude()).doubleValue(), Double.valueOf(dir.getLatitude()).doubleValue(), dto.getLongitude(), dto.getLatitude());
                            log.info("新车直营车商控制经纬度！");
                            // 赋值校验距离
                            checkDistance= Double.valueOf(dir.getLongitudeLatitudeRange());
                        }
                    }else {
                        log.info("获取新车直营车商数据失败！" + dto.getCardealerId());
                        throw new AfsBaseException("获取车商信息失败!");
                    }
                }

            }

        } else {
            log.info("我是二手车" + temp.getLongitudeLatitudeSwitchOld());
            // 先判断当前属性是否控制车商
            if("0".equals(temp.getChoiceCardealerSwitchOld())){
                // 说明不选择车商，那么就去匹配合作商的数据
                if ("0".equals(temp.getLongitudeLatitudeSwitchOld())) {
                    log.info("二手车合作商不控制经纬度！");
                    // 说明二手车车不需要控制
                    return new IResponse<Boolean>().setData(true).setMsg("成功！");
                } else if ("1".equals(temp.getLongitudeLatitudeSwitchOld())) {
                    // 说明需要控制
                    log.info("合作商经度：" + Double.valueOf(temp.getLongitude()).doubleValue());
                    log.info("合作商纬度：" + Double.valueOf(temp.getLatitude()).doubleValue());
                    log.info("传递过来的经度" + dto.getLongitude());
                    log.info("传递过来的纬度" + dto.getLatitude());
                    distance = DistanceUtil.getDistance(Double.valueOf(temp.getLongitude()).doubleValue(), Double.valueOf(temp.getLatitude()).doubleValue(), dto.getLongitude(), dto.getLatitude());
                    log.info("我计算的距离" + distance);
                    log.info("二手车合作商控制经纬度！");
                    // 赋值校验距离
                    checkDistance= Double.valueOf(temp.getLongitudeLatitudeRange());
                }
            }else {
                // 说明去选择车商，那么根据车商的信息去匹配数据,判断是直营车商还是普通车商
                if (Common.CHANNEL_TYPE_CAR_COMMON.equals(dto.getDealerType())) {
                    // 说明是普通车商那么去查普通车商的数据
                    CommonCarDealer cardealer = commonCarDealerService.getById(dto.getCardealerId());
                    if (cardealer != null) {
                        // 再去看车商是否控制经纬度
                        if ("0".equals(cardealer.getLongitudeLatitudeSwitch())) {
                            log.info("二手车普通车商不控制经纬度！");
                            return new IResponse<Boolean>().setData(true).setMsg("成功！");
                        } else {
                            log.info("二手车普通车商控制经纬度！");
                            // 说明需要控制，那么就需要去处理经纬度问题
                            String number = cardealer.getLongitudeLatitude();
                            String[] result = number.split(",");
                            if (result[0] != null && result[0] != "" && result[1] != null && result[1] != "") {
                                distance = DistanceUtil.getDistance(Double.valueOf(result[0]).doubleValue(), Double.valueOf(result[1]).doubleValue(), dto.getLongitude(), dto.getLatitude());
                                // 赋值校验距离
                                checkDistance= Double.valueOf(cardealer.getLongitudeLatitudeRange());
                            }else {
                                log.info("二手车普通车商控制经纬度,但是值获取不正确！");
                            }
                        }
                    } else {
                        log.info("获取新车普通车商数据失败！" + dto.getCardealerId());
                        throw new AfsBaseException("获取车商信息失败!");
                    }
                }else {
                    // 说明是直营车商
                    ChannelBaseInfo dir=channelBusiInfoService.getById(dto.getCardealerId());
                    if(dir!=null){
                        log.info("经纬度二手车商名称"+dir.getChannelFullName());
                        log.info("经纬度二手车"+dir.getLongitudeLatitudeSwitchOld());
                        if("0".equals(dir.getLongitudeLatitudeSwitchOld())){
                            log.info("二手车直营商不控制经纬度！");
                            // 说明新车不需要控制
                            return new IResponse<Boolean>().setData(true).setMsg("成功！");
                        }else {
                            log.info("二手车直营商控制经纬度！");
                            // 再看直营车商 经纬度控制
                            distance = DistanceUtil.getDistance(Double.valueOf(dir.getLongitude()).doubleValue(), Double.valueOf(dir.getLatitude()).doubleValue(), dto.getLongitude(), dto.getLatitude());
                            // 赋值校验距离
                            checkDistance= Double.valueOf(dir.getLongitudeLatitudeRange());


                        }
                    }else {
                        log.info("获取新车直营车商数据失败！" + dto.getCardealerId());
                        throw new AfsBaseException("获取车商信息失败!");
                    }
                }
            }

        }
        log.info("我要对比的距离" + distance);
        // 取对比距离
        if (distance >= Double.valueOf(0)) {
            if ((checkDistance * 1000) >= distance) {
                return new IResponse<Boolean>().setData(true).setMsg("成功！距离" + distance);
            } else {
                return new IResponse<Boolean>().setData(false).setMsg("距离不足！距离" + distance);
            }
        } else {
            if ((checkDistance * 1000 + distance) >= 0) {
                return new IResponse<Boolean>().setData(true).setMsg("成功！距离" + distance);
            } else {
                return new IResponse<Boolean>().setData(false).setMsg("距离不足！距离" + distance);
            }
        }

    }

    @PostMapping("/getContractNumberByChannelCode")
    @ApiOperation(value = "根据合作商代码查询关联的合同数量")
    public IResponse<List<ChannelContractNumberVO>> getContractNumberByChannelCode(@RequestBody ChannelBaseInfoCondition condition) {
        List<String> channelCodeList = condition.getChannelCodeList();
        List<ChannelContractNumberVO> vos = new ArrayList<>();
        List<CaseChannelInfo> caseChannelInfoList = caseChannelInfoService.list(Wrappers.<CaseChannelInfo>query().lambda()
                .in(CaseChannelInfo::getDealerNo, channelCodeList));
        if (CollectionUtil.isNotEmpty(caseChannelInfoList)) {
            channelCodeList.forEach(channelCode -> {
                ChannelContractNumberVO vo = new ChannelContractNumberVO();
                List<String> applyNos = new ArrayList<>();
                vo.setChannelCode(channelCode);
                caseChannelInfoList.forEach(info -> {
                    if (channelCode.equals(info.getDealerNo())) {
                        applyNos.add(info.getApplyNo());
                    }
                });
                vo.setApplyNos(applyNos);
                //根据查询到的申请编号查询申请的合同数量
                if (CollectionUtil.isNotEmpty(applyNos)) {
                    List<CaseContractInfo> caseContractInfos = caseContractInfoService.list(Wrappers.<CaseContractInfo>query().lambda()
                            .in(CaseContractInfo::getApplyNo, applyNos));
                    vo.setContractNum(caseContractInfos.size());
                }
                vos.add(vo);
            });
        }
        return IResponse.success(vos);
    }

    @GetMapping("/{applyNo}/channelBaseInfo")
    @ApiModelProperty("根据申请单号查询渠道信息")
    public IResponse<ChannelBaseInfoVO> channelBaseInfo(@PathVariable("applyNo") String applyNo) {

        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(CaseChannelInfo::getApplyNo,applyNo));
        ChannelBaseInfo channelBaseInfo = caseChannelInfoService.getChannelBaseInfo(applyNo);

        ChannelBaseInfoVO channelBaseInfoVO = new ChannelBaseInfoVO();
        channelBaseInfoVO.setCaseChannelInfo(caseChannelInfo);
        channelBaseInfoVO.setChannelBaseInfo(channelBaseInfo);

        return IResponse.success(channelBaseInfoVO);
    }

    @GetMapping("/channelBaseInfo")
    @ApiModelProperty("根据申请单号查询渠道信息")
    public IResponse<List<ChannelBaseInfoVO>> channelBaseInfo(@RequestBody List<String> applyNoList) {

        List<CaseChannelInfo> caseChannelInfoList = caseChannelInfoService.list(Wrappers.<CaseChannelInfo>query().lambda().in(CaseChannelInfo::getApplyNo,applyNoList));
        List<String> applyList = caseChannelInfoList.stream().map(CaseChannelInfo::getApplyNo).collect(Collectors.toList());

        List<ChannelBaseInfo> channelBaseInfoList = caseChannelInfoService.getChannelBaseInfo(applyList);
        Map<String, ChannelBaseInfo> codeChannelBaseMap = channelBaseInfoList.stream().collect(Collectors.toMap(ChannelBaseInfo::getChannelCode, item -> item));

        List<ChannelBaseInfoVO> channelBaseInfoVOList = caseChannelInfoList.stream().map(item -> convert(item, codeChannelBaseMap.get(item.getDealerNo()))).collect(Collectors.toList());

        return IResponse.success(channelBaseInfoVOList);
    }

    private ChannelBaseInfoVO convert(CaseChannelInfo caseChannelInfo, ChannelBaseInfo channelBaseInfo) {
        ChannelBaseInfoVO channelBaseInfoVO = new ChannelBaseInfoVO();
        channelBaseInfoVO.setCaseChannelInfo(caseChannelInfo);
        channelBaseInfoVO.setChannelBaseInfo(channelBaseInfo);
        return channelBaseInfoVO;
    }

    /**
     * 付款通知函
     *
     * @param
     * @param
     */
    @PostMapping("/getDeptPayComAttachmentFileList")
    @Transactional(rollbackFor = Exception.class)
    public List<ComAttachmentFile> getDeptPayComAttachmentFileList(@RequestBody List<DebitPayMentVO> payMentVOList) {
        log.info("debitPayBatchPrint--step6.5 -> {}",payMentVOList.size());
        //获取模板
        ComPrintFormManage debitForm = comPrintFormManageService.getPrintFormManageByUniqueCode("debitPayDirect");
        com.ruicar.afs.cloud.common.modules.utils.Assert.isTrue(debitForm == null).throwMessage("放款通知函模板不存在");
        log.info("debitPayBatchPrint--step7 -> {}",debitForm.getId()+"~~~"+debitForm.toString());
        List<ComAttachmentFile> printDebitPay = new ArrayList<>();
        //根据银行code获取银行名称
        Map<String, List<DicDataDto>> listMap = DicHelper.getDicMaps("bankCode");
        List<DicDataDto> bankCode = listMap.get("bankCode");

        payMentVOList.forEach(debitPayMentVO ->{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy 年 MM 月 dd 日");
            String startDate = debitPayMentVO.getStartDate();
            Date parse = null;
            try {
                parse = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startDate);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            String startDateStr = sdf.format(parse);
            debitPayMentVO.setStartDateStr(startDateStr);
            Date activationTime = debitPayMentVO.getActivationTime();
            String activationTimeStr = sdf.format(activationTime);
            debitPayMentVO.setActivationTimeStr(activationTimeStr);
            //银行名称
            for(DicDataDto dic : bankCode){
                if(debitPayMentVO.getReceiptBankCode().equals(dic.getValue())){
                    debitPayMentVO.setReceiptBankCode(dic.getTitle());
                }
            }

            JSONObject debitPayMentJson = (JSONObject) JSONObject.toJSON(debitPayMentVO);
            log.info("debitPayBatchPrint--step7.5 -> {}",debitPayMentJson.toJSONString());
            ComAttachmentFile comAttachmentFileByXys = this.setSinglePrintByRule(debitForm,debitPayMentVO.getContractNo(), AfsEnumUtil.key(ServiceClientTypeEnum.PC), debitPayMentJson);
            comAttachmentFileByXys.setAttachmentName(debitPayMentVO.getContractNo()+"-"+debitPayMentVO.getCustName());
            printDebitPay.add(comAttachmentFileByXys);
        });
        log.info("debitPayBatchPrint--step8 -> {}",printDebitPay.toString());
        log.info("debitPayBatchPrint--step9 -> {}",printDebitPay.size());
        return printDebitPay;
    }


    /**
     *
     * @param file 打印模板管理实体类
     * @param contractNo 合同号码
     * @param serviceClientType pc端/app端
     * @param templateVO 模板参数
     * @return 返回单个打印模板
     */
    private ComAttachmentFile setSinglePrintByRule(ComPrintFormManage file,String contractNo,String serviceClientType,JSONObject templateVO){
        if ("1".equals(file.getIsEnable())){
            JSONObject mainCustJson = (JSONObject) JSONObject.toJSON(templateVO);
            ComAttachmentFile attachmentFile = comPrintFormManageService.print(mainCustJson, String.valueOf(file.getId()), contractNo, serviceClientType);
            if (attachmentFile != null) {
                return attachmentFile;
            }
        }
        return null;
    }

    @RequestMapping(value = "/getCaseBaseInfoList", method = RequestMethod.POST)
    @ApiOperation(value = "获取渠道基本信息")
    public IResponse getCaseBaseInfoList() {
        List<ChannelBaseInfo> baseList = channelBusiInfoService.list();
        return new IResponse<List<ChannelBaseInfo>>().setData(baseList);
    }

    /**
     * 根据经销商代码去查询经销商id
     * @param channelCode
     * @return
     */
    @RequestMapping(value = "/queryChannelIdByChannelCode", method = RequestMethod.GET)
    @ApiOperation(value = "根据经销商代码去查询经销商id")
    public String queryChannelIdByChannelCode(@RequestParam String channelCode) {
        return channelBusiInfoService.getChannelIdByChannelCode(channelCode);
    }


    /**
     * 执行更新更新评级
     * @param channelGrade 更新评级
     * @param channelId 渠道id
     * @return
     */
    @PostMapping(value = "/updateChannelGradeCri")
    @ApiOperation(value = "执行更新更新评级")
    public IResponse updateChannelGradeCri(@RequestParam String channelGrade, @RequestParam Long channelId){
        caseChannelInfoService.updateChannelGradeCri(channelGrade, channelId);
        return IResponse.success("ok");
    }

    /**
     * 执行更新更新评级
     * @param qualityGrade 更新评级
     * @param channelGrade 更新评级
     * @return
     */
    @PostMapping(value = "/updateQualityGradeCri")
    @ApiOperation(value = "执行更新更新评级")
    public IResponse updateQualityGradeCri(@RequestParam String qualityGrade, @RequestParam String channelGrade){
        caseChannelInfoService.updateQualityGradeCri(qualityGrade, channelGrade);
        return IResponse.success("ok");
    }

    /**
     * 执行更新更新评级
     * @param qualityGrade 更新评级
     * @param channelGrade 更新评级
     * @return
     */
    @PostMapping(value = "/updateQualityGradeCci")
    @ApiOperation(value = "执行更新更新评级")
    public IResponse updateQualityGradeCci(@RequestParam String qualityGrade, @RequestParam String channelGrade){
        caseChannelInfoService.updateQualityGradeCci(qualityGrade, channelGrade);
        return IResponse.success("ok");
    }


    /**
     * 执行更新是否先放后抵
     * @param isMortgage 是否先放后抵
     * @param channelId 渠道id
     * @return
     */
    @PostMapping(value = "/updateIsMortgageCri")
    @ApiOperation(value = "执行更新是否先放后抵")
    public IResponse updateIsMortgageCri(@RequestParam String isMortgage, @RequestParam Long channelId){
        caseChannelInfoService.updateIsMortgageCri(isMortgage, channelId);
        return IResponse.success("ok");
    }

    /**
     * 案例渠道基础信息更新 leasing_case.CASE_CHANNEL_BASE_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例渠道基础信息更新")
    @PostMapping(value = "/updateCaseChannelBaseInfo")
    public IResponse<?> updateCaseChannelBaseInfo(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseChannelBaseInfo(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销分摊详细信息更新 leasing_case.CASE_WRITE_OFF_APPORTION_DETAIL
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销分摊详细信息更新")
    @PostMapping(value = "/updateCaseWriteOffApportionDetail")
    public IResponse<?> updateCaseWriteOffApportionDetail(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffApportionDetail(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销合同管理详细信息更新 leasing_case.CASE_WRITE_OFF_CONTRACT_DETAIL_MANAGE
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销合同管理详细信息更新")
    @PostMapping(value = "/updateCaseWriteOffContractDetailManage")
    public IResponse<?> updateCaseWriteOffContractDetailManage(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffContractDetailManage(modify);
        return IResponse.success(true);
    }

    /**
     * 案例渠道服务费信息更新 leasing_case.CASE_CHANNEL_SERVICE_FEE
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例渠道服务费信息更新")
    @PostMapping(value = "/updateCaseChannelServiceFee")
    public IResponse<?> updateCaseChannelServiceFee(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseChannelServiceFee(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销账期详细信息更新 leasing_case.CASE_WRITE_OFF_ACCOUNT_CYCLE_DETAIL
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销账期详细信息更新")
    @PostMapping(value = "/updateCaseWriteOffAccountCycleDetail")
    public IResponse<?> updateCaseWriteOffAccountCycleDetail(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffAccountCycleDetail(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销分摊信息更新 leasing_case.CASE_WRITE_OFF_APPORTION_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销分摊信息更新")
    @PostMapping(value = "/updateCaseWriteOffApportionInfo")
    public IResponse<?> updateCaseWriteOffApportionInfo(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffApportionInfo(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销基础信息更新 leasing_case.CASE_WRITE_OFF_BASE_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销基础信息更新")
    @PostMapping(value = "/updateCaseWriteOffBaseInfo")
    public IResponse<?> updateCaseWriteOffBaseInfo(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffBaseInfo(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销基础发票关联信息更新 leasing_case.CASE_WRITE_OFF_BASE_INVOICE_REL
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销基础发票关联信息更新")
    @PostMapping(value = "/updateCaseWriteOffBaseInvoiceRel")
    public IResponse<?> updateCaseWriteOffBaseInvoiceRel(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffBaseInvoiceRel(modify);
        return IResponse.success(true);
    }

    /**
     * 案例核销发票信息更新 leasing_case.CASE_WRITE_OFF_INVOICE_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例核销发票信息更新")
    @PostMapping(value = "/updateCaseWriteOffInvoiceInfo")
    public IResponse<?> updateCaseWriteOffInvoiceInfo(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseWriteOffInvoiceInfo(modify);
        return IResponse.success(true);
    }

    /**
     * 案例渠道鸽子详细信息更新 leasing_case.CASE_CHANNEL_PIGEON_DETAIL
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案例渠道鸽子详细信息更新")
    @PostMapping(value = "/updateCaseChannelPigeonDetail")
    public IResponse<?> updateCaseChannelPigeonDetail(@RequestBody ChannelInfoModifyVO modify) {
        caseChannelInfoService.updateCaseChannelPigeonDetail(modify);
        return IResponse.success(true);
    }

    /**
     * 案件收款账户信息更新 leasing_case.CASE_CHANNEL_RECEIVABLES_ACCOUNT
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案件收款账户更新")
    @PostMapping(value = "/updateCaseChannelReceivablesAccount")
    public IResponse<Boolean> updateCaseChannelReceivablesAccount(@RequestBody ChannelInfoModifyVO modify){
        caseChannelInfoService.updateCaseChannelReceivablesAccount(modify);
        return IResponse.success(true);
    }

    /**
     * 案件基础信息更新 leasing_case.CASE_BASE_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案件基础信息更新")
    @PostMapping(value = "/updateCaseBaseInfo")
    public IResponse<Boolean> updateCaseBaseInfo(@RequestBody ChannelInfoModifyVO modify){
        caseChannelInfoService.updateCaseBaseInfo(modify);
        return IResponse.success(true);
    }

    /**
     * 案件渠道信息更新 leasing_case.CASE_CHANNEL_INFO
     *
     * @param modify modify
     * @return response
     */
    @ApiOperation("案件渠道信息更新")
    @PostMapping(value = "/updateCaseChannelInfo")
    public IResponse<Boolean> updateCaseChannelInfo(@RequestBody ChannelInfoModifyVO modify){
        caseChannelInfoService.updateCaseChannelInfo(modify);
        return IResponse.success(true);
    }
}
