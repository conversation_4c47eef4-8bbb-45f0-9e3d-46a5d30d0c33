package com.ruicar.afs.cloud.afscase.channel.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.admin.api.dto.UserSimpleInfoDto;
import com.ruicar.afs.cloud.admin.api.feign.AfsUserFeign;
import com.ruicar.afs.cloud.afscase.channel.condition.AffiliationStatusCondition;
import com.ruicar.afs.cloud.afscase.channel.condition.BaseInfoTypeDTO;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.mapper.ChannelBusiInfoMapper;
import com.ruicar.afs.cloud.afscase.channel.mapper.ChannelRiskInfoMapper;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.dealeroutline.condition.ChannelOntlineCondition;
import com.ruicar.afs.cloud.afscase.dealeroutline.entity.ChannelOutLine;
import com.ruicar.afs.cloud.afscase.dealeroutline.enums.ConstantEnum;
import com.ruicar.afs.cloud.afscase.dealeroutline.mapper.ChannelOutLineMapper;
import com.ruicar.afs.cloud.afscase.dealeroutline.vo.ChannelBaseInfoVo;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.config.api.address.feign.AddressFeign;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-6-9 14:17
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class ChannelBaseInfoServiceImpl extends ServiceImpl<ChannelBusiInfoMapper, ChannelBaseInfo> implements ChannelBaseInfoService {

    private ChannelRiskInfoMapper channelRiskInfoMapper;

    private AddressFeign addressFeign;

    private final WorkflowTaskInfoService workflowTaskInfoService;

    private ChannelOutLineMapper channelOutLineMapper;

    private AfsUserFeign feign;

    private FlowConfigProperties flowConfigProperties;

    @Override
    public List<ChannelBaseInfo> getBaseInfo(AffiliationStatusCondition affiliationStatusCondition) {
        return this.baseMapper.getChannelInfo(affiliationStatusCondition);
    }

    @Override
    public IPage<List<ChannelBaseInfo>> getBaseInfoPage(Page page, AffiliationStatusCondition affiliationStatusCondition) {
        return this.baseMapper.getChannelInfoPage(page, affiliationStatusCondition);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByType(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByType(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatus(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatus(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOtherAll(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOtherAll(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOwnAll(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOwnAll(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOnlyOwnAll(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOnlyOwnAll(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOnlyOwnType(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOnlyOwnType(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOnlyOtherAll(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOnlyOtherAll(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOnlyOtherType(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOnlyOtherType(dto);
    }

    @Override
    public List<ChannelBaseInfo> getBaseInfoByStatusOtherOnlyOwnOnly(BaseInfoTypeDTO dto) {
        return this.baseMapper.getBaseInfoByStatusOtherOnlyOwnOnly(dto);
    }

    @Override
    public IPage<ChannelBaseInfoVo> getChannelBaseInfoVo(ChannelOntlineCondition channelOntlineCondition) {
        Page page = new Page(channelOntlineCondition.getPageNumber(), channelOntlineCondition.getPageSize());
        if (ConstantEnum.ENABLE.getCode().equals(channelOntlineCondition.getDealerStatus())){
            channelOntlineCondition.setDealerType(ConstantEnum.ENABLE.getCode());
            channelOntlineCondition.setDealerStatus(null);

        }
        IPage<ChannelBaseInfoVo> channelBaseInfoVoPage = this.baseMapper.getChannelBaseInfoVo(page, channelOntlineCondition);

        for(ChannelBaseInfoVo u : channelBaseInfoVoPage.getRecords()){
            List<ChannelRiskInfo> riskInfoTempList =channelRiskInfoMapper.selectList(Wrappers.<ChannelRiskInfo>query().lambda().eq(ChannelRiskInfo::getChannelId,u.getId())
                    .eq(ChannelRiskInfo::getDelFlag,"0").orderByAsc(ChannelRiskInfo::getBusinessType));

            if (riskInfoTempList != null && riskInfoTempList.size() > 0){
                u.setCustomerManager(riskInfoTempList.get(0).getCustomerManager());
                u.setChannelDeposit(riskInfoTempList.get(0).getChannelDeposit());
            }

            if(u.getChannelCity()!=null&& !"".equals(u.getChannelCity())){
                IResponse<String> param  = addressFeign.getLabelByValue(u.getChannelCity());
                if(StringUtils.equals(param.getCode(), CommonConstants.SUCCESS)) {
                    u.setChannelCity(param.getData());
                }
                if(StringUtils.isEmpty(u.getChannelCity())){
                    u.setChannelCity("");
                }
            }
            if(u.getChannelProvince()!=null&& !"".equals(u.getChannelProvince())){
                IResponse<String> param  = addressFeign.getLabelByValue(u.getChannelProvince()+"0000");


                if(StringUtils.equals(param.getCode(), CommonConstants.SUCCESS)) {
                    u.setChannelProvince(param.getData());
                }
                if(StringUtils.isEmpty(u.getChannelProvince())){
                    u.setChannelProvince("");
                }
            }
            if(StringUtils.isBlank(u.getDealerStatus())&&(
                    "00".equals(u.getChannelStatus())|| "00".equals(u.getChannelStatusOldCar()))){
                u.setDealerStatus(ConstantEnum.ENABLE.getCode());
            }

            ChannelOutLine channelOutLine = channelOutLineMapper.selectOne(Wrappers.<ChannelOutLine>lambdaQuery()
                    .eq(ChannelOutLine::getChannelId, u.getId()));
            if (channelOutLine != null){
                Long bizDataId = channelOutLine.getId();
                //查找下一审批人节点
                List<WorkflowTaskInfo> list = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo, bizDataId)
                        .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getChannelOutlineTemplateId())
                        .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getChannelOutlinePackageId())
                        .eq(WorkflowTaskInfo::getStatus, FlowStatusEnum.ACTIVE.getCode()));

                if(!org.springframework.util.CollectionUtils.isEmpty(list)){
                    //使用审批意见来存下一处理人
                    String assign = list.stream().map(f->getUserByLoginName(f.getAssign())).collect(Collectors.joining("-"));
                    if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(assign)){
                        u.setOutlineDealLoginNo(assign);
                    }
                }
            }
        }

        return channelBaseInfoVoPage;
    }

    private String getUserByLoginName(String loginName) {
        IResponse<List<UserSimpleInfoDto>> simpleInfoByUserLoginNames = feign.getSimpleInfoByUserLoginNames(Arrays.asList(new String[]{loginName}));
        if("0000".equals(simpleInfoByUserLoginNames.getCode())){
            UserSimpleInfoDto userSimpleInfoDto = simpleInfoByUserLoginNames.getData().get(0);
            return userSimpleInfoDto.getUserRealName();
        }
        throw new RuntimeException("查询用户信息异常");
    }

    @Override
    public ChannelBaseInfo queryApproveBasic(Long id) {
        return this.baseMapper.queryApproveBasic(id);
    }

    /**
     * 根据客户经理name获取渠道code列表
     * @param username
     * @return
     */
    @Override
    public List<String> getChannelCodeByManager(String username) {
        return baseMapper.getChannelCodeByManager(username);
    }

    @Override
    public ChannelBaseInfo getChannelByDealerId(String dealerId) {
        return baseMapper.selectOne(Wrappers.<ChannelBaseInfo>lambdaQuery().eq(ChannelBaseInfo::getChannelCode, dealerId).eq(ChannelBaseInfo::getDelFlag, "0").last("limit 1"));
    }
    @Override
    public ChannelBaseInfo queryChannelByChannelId(Long channelId) {
        return baseMapper.selectOne(Wrappers.<ChannelBaseInfo>lambdaQuery().eq(ChannelBaseInfo::getChannelId, channelId).eq(ChannelBaseInfo::getDelFlag, "0").last("limit 1"));
    }

    @Override
    public String getChannelIdByChannelCode(String channelCode) {
        return baseMapper.getChannelIdByChannelCode(channelCode);
    }

    /**
     * 根据渠道id获取渠道风险信息
     *
     * @param channelId    渠道id
     * @param businessType 业务类型
     * @return channel risk info
     */
    @Override
    public ChannelRiskInfo getChannelRiskInfo(Long channelId, String businessType) {
        ChannelRiskInfo channelRiskInfo = channelRiskInfoMapper.selectOne(
                Wrappers.<ChannelRiskInfo>lambdaQuery()
                        .eq(ChannelRiskInfo::getChannelId, String.valueOf(channelId))
                        .eq(ChannelRiskInfo::getBusinessType, businessType)
                        .orderByDesc(ChannelRiskInfo::getUpdateTime)
                        .last("limit 1")
        );
        return channelRiskInfo;
    }

}
