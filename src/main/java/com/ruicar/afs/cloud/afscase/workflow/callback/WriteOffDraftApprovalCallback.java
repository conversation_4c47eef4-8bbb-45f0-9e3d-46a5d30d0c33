package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffClearInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ApportionEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ClearStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mq.WriteOffSender;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffClearInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffContractDetailManageService;
import com.ruicar.afs.cloud.bizcommon.voucher.service.MqMessageQueueLogService;
import com.ruicar.afs.cloud.common.modules.constant.VoucherBuriedPointNo;
import com.ruicar.afs.cloud.common.modules.dto.mq.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.voucher.VoucherFlowInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffBaseInfoBatchDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffBaseInfoDto;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.enums.common.WriteOffStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 服务费草稿审批回调
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Component
public class WriteOffDraftApprovalCallback implements CommonAdapter {
    private WriteOffBaseInfoMapper writeOffBaseInfoMapper;
    private WriteOffBaseInfoService writeOffBaseInfoService;
    private final WriteOffSender writeOffSender;
    private WriteOffContractDetailManageService writeOffContractDetailManageService;
    private ChannelBaseInfoService channelBaseInfoService;
    private final MqMessageQueueLogService mqMessageQueueLogService;
    private final WriteOffClearInfoService writeOffClearInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
                                       String extParam, Map<String, String> flowVariables) {
        //拿申请编号查询变更记录
        String applyNo = flowVariables.get(FlowConstant.BUSINESS_NO);
        List<WriteOffBaseInfo> writeOffBaseInfo = writeOffBaseInfoMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getDraftBatchCode, applyNo));
        Assert.isTrue(writeOffBaseInfo.size() > 0, "服务费草稿审批数据异常");
        //审批通过
        WriteOffBaseInfoBatchDto writeOffBaseInfoBatchDto=new WriteOffBaseInfoBatchDto();
        ArrayList<WriteOffBaseInfoDto> writeOffBaseInfoDtos = new ArrayList<>();
        Date date = new Date();
        switch (FlowTaskOperationEnum.valueOf(flowVariables.get(FlowConstant.LAST_OPERATION))) {
            case SUBMIT:
                if (ObjectUtils.isNotEmpty(writeOffBaseInfo)) {
                    writeOffBaseInfo.forEach(i->{
                        i.setWriteOffStatus(WriteOffStatusEnum.BE_WRITE_OFF);
                        i.setReleaseTime(date);
                        i.setTimeSyncFlag(StatusEnum.NO.getCode());
                        //推送数据到进件
                        WriteOffBaseInfoDto dto = new WriteOffBaseInfoDto();
                        BeanUtils.copyProperties(i, dto, "id");
                        dto.setWriteOffStatus(i.getWriteOffStatus());
                        dto.setFrozenStatus(null);
                        dto.setTime(date);
                        if (StrUtil.isNotBlank(dto.getReturnOverdueAppNo())) {
                            List<String> appNos = JSONUtil.toList(dto.getReturnOverdueAppNo(), String.class);
                            dto.setReturnOverdueAppNo(String.join(",", appNos));
                        }
                        writeOffBaseInfoDtos.add(dto);
                    });
                    writeOffBaseInfoBatchDto.setWriteOffBaseInfoDtos(writeOffBaseInfoDtos);
                    AfsTransEntity<WriteOffBaseInfoBatchDto> afsTransEntity = new AfsTransEntity();
                    afsTransEntity.setData(writeOffBaseInfoBatchDto);
                    afsTransEntity.setTransCode(MqTransCode.AFS_WRITE_OFF_CASE_APPLY_NOTICE);
                    writeOffSender.sendWriteOffToApply(afsTransEntity);
                }

                //凭证埋点
                for (WriteOffBaseInfo baseInfo : writeOffBaseInfo) {
                    baseInfo.setVoucher(baseInfo.getApplyNo());
                    //埋点
                    this.saveServiceFeeData(baseInfo);
                }

                //添加分摊标识
                List<String> applyNoList = writeOffBaseInfo.stream().map(WriteOffBaseInfo::getApplyNo).toList();
                writeOffContractDetailManageService.update(Wrappers.<WriteOffContractDetailManage>lambdaUpdate()
                        .in(WriteOffContractDetailManage::getBaseInfoApply, applyNoList)
                        .set(WriteOffContractDetailManage::getApportionFlag, ApportionEnum.APPORTIONWAIT.getCode()));
                //添加清账数据
                List<WriteOffClearInfo> clearInfoList = writeOffBaseInfo.stream().map(base -> {
                    WriteOffClearInfo clearInfo = new WriteOffClearInfo();
                    clearInfo.setApplyNo(base.getApplyNo());
                    clearInfo.setWriteOffType(base.getWriteOffType());
                    clearInfo.setWriteOffMonth(base.getWriteOffMonth());
                    clearInfo.setChannelCode(base.getOrganId());
                    clearInfo.setChannelFullName(base.getOrganName());
                    clearInfo.setServiceCharge(base.getInvoiceAmount());
                    clearInfo.setFeeClearStatus(ClearStatusEnum.WAIT_CLEAR.getCode());
                    clearInfo.setPayClearStatus(ClearStatusEnum.WAIT_CLEAR.getCode());
                    return clearInfo;
                }).toList();
                writeOffClearInfoService.saveBatch(clearInfoList);
                break;
            case REFUSE:
                if (ObjectUtils.isNotEmpty(writeOffBaseInfo)) {
                    writeOffBaseInfo.forEach(i->{
                        i.setWriteOffStatus(WriteOffStatusEnum.DELETED);
                    });
                }
                break;
            default:
                log.info("not support operation : {}", flowVariables);
        }
        writeOffBaseInfoService.updateBatchById(writeOffBaseInfo);
        log.info("服务费草稿审批结束回调:{}", flowVariables);
        return flowVariables;
    }


    private void saveServiceFeeData(WriteOffBaseInfo baseInfo) {
        log.info("服务费发布埋点数据:" + baseInfo.getApplyNo());
        VoucherFlowInfoDto voucherFlowInfoDto = new VoucherFlowInfoDto();
        voucherFlowInfoDto.setBuriedPointNo(WriteOffTypeEnum.ZJD.getCode().equals(baseInfo.getWriteOffType()) ? VoucherBuriedPointNo.rentLoanServiceFee : VoucherBuriedPointNo.serviceFee);
        voucherFlowInfoDto.setTransNo(baseInfo.getVoucher());
        voucherFlowInfoDto.setKeepAccountDate(new Date());
        voucherFlowInfoDto.setContractNo(baseInfo.getOrganId());
        voucherFlowInfoDto.setDealerName(baseInfo.getOrganName());

        //根据经销商code获取Channel
        ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getChannelByDealerId(baseInfo.getOrganId());
        if (ObjectUtils.isNotEmpty(channelBaseInfo)) {
            voucherFlowInfoDto.setChannelId(String.valueOf(channelBaseInfo.getChannelId()));
            voucherFlowInfoDto.setCustNo(String.valueOf(channelBaseInfo.getChannelId()));
        }
        log.info("服务费发布埋点成功,开始保存流水{}", voucherFlowInfoDto);
        mqMessageQueueLogService.saveMqMessage(voucherFlowInfoDto);
    }
}
