package com.ruicar.afs.cloud.afscase.risk.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONAware;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.common.utils.DecisionStrategySorter;
import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.entity.ManualReviewInformation;
import com.ruicar.afs.cloud.afscase.risk.entity.RemindVerificationRule;
import com.ruicar.afs.cloud.afscase.risk.entity.RemindVerificationRuleMapping;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.afscase.risk.mapper.ThirdDataMapper;
import com.ruicar.afs.cloud.afscase.risk.service.CodeCategoryOrderService;
import com.ruicar.afs.cloud.afscase.risk.service.ManualReviewInformationService;
import com.ruicar.afs.cloud.afscase.risk.service.RemindVerificationRuleMappingService;
import com.ruicar.afs.cloud.afscase.risk.service.RemindVerificationRuleService;
import com.ruicar.afs.cloud.afscase.risk.service.ThirdDataService;
import com.ruicar.afs.cloud.afscase.risk.util.Constant;
import com.ruicar.afs.cloud.afscase.risk.vo.BhCheatScore;
import com.ruicar.afs.cloud.afscase.risk.vo.BhRiskPorList;
import com.ruicar.afs.cloud.afscase.risk.vo.BhSpecialList;
import com.ruicar.afs.cloud.afscase.risk.vo.Custom;
import com.ruicar.afs.cloud.afscase.risk.vo.CustomInfo;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionResult;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionStrategyFirst;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionStrategySecond;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionStrategyThird;
import com.ruicar.afs.cloud.afscase.risk.vo.Ensure;
import com.ruicar.afs.cloud.afscase.risk.vo.EnsureInfo;
import com.ruicar.afs.cloud.afscase.risk.vo.HitRuleResultDetail;
import com.ruicar.afs.cloud.afscase.risk.vo.HitRuleResults;
import com.ruicar.afs.cloud.afscase.risk.vo.IdTwoz;
import com.ruicar.afs.cloud.afscase.risk.vo.Request;
import com.ruicar.afs.cloud.afscase.risk.vo.Response;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleApplyLoanAutofin;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleExecution;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleExecutionCustom;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleInfoRelationAutofinC;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleInfoRelationAutofincEnsure;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleSetResult;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleSpecialListC;
import com.ruicar.afs.cloud.afscase.risk.vo.RuleSpecialListcCustom;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledApplyLoanUsury;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledApplyLoanUsuryCustom;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledDebtRepayStress;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledDebtRepayStressEnsure;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledFraudRelationG;
import com.ruicar.afs.cloud.afscase.risk.vo.RuledFraudRelationgCustom;
import com.ruicar.afs.cloud.afscase.risk.vo.ScoreCust;
import com.ruicar.afs.cloud.afscase.risk.vo.ScoreCustCustom;
import com.ruicar.afs.cloud.afscase.risk.vo.TelPeriod;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.risk.api.config.RiskControlServerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ThirdDataServiceImpl implements ThirdDataService {

    @Autowired
    private ThirdDataMapper thirdDataMapper;
    @Autowired
    private RiskControlServerConfig riskControlServerConfig;
    @Autowired
    private ManualReviewInformationService manualReviewInformationService;
    @Autowired
    private RemindVerificationRuleService remindVerificationRuleService;
    @Autowired
    private RemindVerificationRuleMappingService remindVerificationRuleMappingService;

    private final String SUCCESS = "通过";
    private final List<String> riskRules = Arrays.asList("L028","L029","L030","L031","L032","L033","L034","L035","L036","L037");
    private final List<String> conclusionRules = Arrays.asList("T284");
    private final List<String> labelRules = Arrays.asList("T299","T062","T024","NH20","NH21","NH22","NH23","NH24","NH25","NH26"
            ,"NH33","L006","L007","L008","L009","L010","L011","L012","L013","L014","L015","L016","L017","L018","L019","L020","L021"
            ,"L022","L023","L024","L025","L026","L027","NH27","NH28","NH29","NH30","NH31","C003","C004","C005","C006","C007","C008"
            ,"C009","C010","C011","C012","C013","C015","C016","C017","C018","C019","C020","C021","T291","T295","T296","T304","T305"
            ,"Q001","Q002","L038");
	@Autowired
	private CodeCategoryOrderService codeCategoryOrderService;

    @Override
    public void save(ThirdData thirdData) {
        //需要判断是新增还是修改
        ThirdData selectData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApplyId, thirdData.getApplyId())
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (selectData != null) {
            thirdData.setCreateTime(selectData.getCreateTime());
            thirdData.setCreateBy(selectData.getCreateBy());
            thirdData.setId(selectData.getId());
            thirdData.setUpdateTime(new Date());
            thirdData.setUpdateBy(selectData.getCreateBy());
            thirdDataMapper.updateById(thirdData);
        } else {
            thirdDataMapper.insert(thirdData);
        }
    }

    @Override
    public IResponse<String> queryThirdUrlByApplyNo(String applyNo) {
        ThirdData thirdData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, applyNo)
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (thirdData != null) {
            JSONObject jsonObject = JSONObject.parseObject(thirdData.getResponse());
            String token;
            token = Optional.ofNullable(jsonObject).map(json -> json.getString("token")).orElse(null);
            if (StringUtils.isNotBlank(token)) {
                return IResponse.success(riskControlServerConfig.getReportUrl() + token);
            }
        }
        return IResponse.fail("获取地址失败！");
    }


    @Override
    public IResponse queryThirdDataByApplyNo(String applyNo) {
        List<ThirdData> list = thirdDataMapper.selectList(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, applyNo)
                .orderByDesc(ThirdData::getCreateTime)
                .last("limit 2"));
        if (list != null && list.get(0) != null) {
            JSONObject jsonReq = JSONObject.parseObject(list.get(0).getRequest());
            JSONObject jsonRsp = JSONObject.parseObject(list.get(0).getResponse());
            Request request = null;
            Response response = null;
            if (EmptyUtils.isNotEmpty(jsonReq)) {
                request = jsonReq.toJavaObject(Request.class);
            }
            if (EmptyUtils.isNotEmpty(jsonRsp)) {
                response = jsonRsp.toJavaObject(Response.class);
            }
            //组装决策结果对象信息
            DecisionResult decisionResult = new DecisionResult();
            decisionResult.setApproveId(applyNo);
            if (EmptyUtils.isNotEmpty(response)) {
                if ("0".equals(response.getReasonCode())) {
                    decisionResult.setFinalDealTypeName(response.getFinalDealTypeName());
                } else {
                    decisionResult.setFinalDealTypeName(response.getReasonDesc());
                }
                decisionResult.setSeqId(response.getSeqId());
            }
            if (EmptyUtils.isNotEmpty(request)) {
                decisionResult.setEntryoccurtime(request.getEntryoccurtime());

            }


            //历史数据对比
            List<DecisionStrategySecond> oldList = new ArrayList<>();
            if (list.size() > 1){
                JSONObject rsp = JSONObject.parseObject(list.get(1).getResponse());
                if (EmptyUtils.isNotEmpty(rsp)){
                    Response res = rsp.toJavaObject(Response.class);
                    if (EmptyUtils.isNotEmpty(res) && EmptyUtils.isNotEmpty(res.getDecisionDetailedResults())){
                        List<RuleSetResult> ruleSetResults = res.getDecisionDetailedResults().getRuleSetResults();
                        for (RuleSetResult ruleSetResult : ruleSetResults){
                            List<HitRuleResults> hitRuleResults = ruleSetResult.getHitRuleResults();
                            if (EmptyUtils.isNotEmpty(hitRuleResults) && hitRuleResults.size() > 0){
                                for (HitRuleResults hitRuleResult : hitRuleResults){
                                    DecisionStrategySecond decisionStrategy2 = new DecisionStrategySecond();
                                    if (EmptyUtils.isNotEmpty(hitRuleResult) && StrUtil.isNotEmpty(hitRuleResult.getCustomId())
                                            && hitRuleResult.getCustomId().indexOf(Constant.NH) == -1
                                            && !hitRuleResult.getCustomId().startsWith(Constant.Z)
                                    ){
                                        String name = hitRuleResult.getName();
                                        if (StrUtil.isNotEmpty(name)){
                                            decisionStrategy2.setName(name);
                                        }
                                        List<HitRuleResultDetail> hitRuleResultDetails = hitRuleResult.getHitRuleResultDetails();
                                        if (CollUtil.isNotEmpty(hitRuleResultDetails)) {
                                            List<DecisionStrategyThird> decisionStrategy3ListList = new ArrayList<>();
                                            for (HitRuleResultDetail hitRuleResultDetail : hitRuleResultDetails) {
                                                DecisionStrategyThird decisionStrategy3 = new DecisionStrategyThird();
                                                decisionStrategy3.setData(hitRuleResultDetail.getData());
                                                decisionStrategy3.setDescription(hitRuleResultDetail.getDescription());
                                                decisionStrategy3ListList.add(decisionStrategy3);
                                            }
                                            decisionStrategy2.setDecisionStrategy3(decisionStrategy3ListList);
                                        }
                                        oldList.add(decisionStrategy2);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            log.info("oldList:{}",oldList);
            //组装决策策略对象信息
            List<DecisionStrategyFirst> decisionStrategy1List = new ArrayList<>();
            List<CodeCategoryOrder> codeCategoryOrderList = codeCategoryOrderService.list();
            Map<String, CodeCategoryOrder> codeCategoryOrderMap = codeCategoryOrderList.stream()
                    .collect(Collectors.toMap(CodeCategoryOrder::getRuleCode, Function.identity()));
            if (EmptyUtils.isNotEmpty(response) && EmptyUtils.isNotEmpty(response.getDecisionDetailedResults())) {
                // 先查询历史是否有
                LambdaQueryWrapper<RemindVerificationRuleMapping> queryWrapperMapping = Wrappers.lambdaQuery(RemindVerificationRuleMapping.class)
                        .eq(RemindVerificationRuleMapping::getApplyNo, applyNo).eq(RemindVerificationRuleMapping::getActiveFlag, CommonConstants.COMMON_YES);
                List<RemindVerificationRuleMapping> remindVerificationRuleMappingList = remindVerificationRuleMappingService.list(queryWrapperMapping);
                LambdaQueryWrapper<RemindVerificationRule> queryWrapper = Wrappers.lambdaQuery(RemindVerificationRule.class)
                        .eq(RemindVerificationRule::getActiveFlag, CommonConstants.COMMON_YES);
                List<RemindVerificationRule> remindVerificationRuleList;
                AtomicBoolean historyFlag = new AtomicBoolean(false);
                List<RemindVerificationRule> hitRules = new ArrayList<>();
                if (CollUtil.isNotEmpty(remindVerificationRuleMappingList)) {
                    log.info("从历史中查询={}", JSONUtil.parse(remindVerificationRuleMappingList));
                    historyFlag.set(true);
                    remindVerificationRuleList = remindVerificationRuleMappingList.stream()
                            .map(m -> {
                                RemindVerificationRule rule = new RemindVerificationRule();
                                rule.setRuleCode(m.getRuleCode());
                                rule.setCategory(m.getCategory());
                                rule.setDescription(m.getDescription());
                                rule.setVerificationGesture(m.getVerificationGesture());
                                rule.setManualCheckContent(m.getManualCheckContent());
                                rule.setActiveFlag(CommonConstants.COMMON_YES);
                                return rule;
                            }).collect(Collectors.toList());
                } else {
                    log.info("从规则表中查询");
                    remindVerificationRuleList = remindVerificationRuleService.list(queryWrapper);
                }

                // 1、将提醒核实规则按规则码分组
                Map<String, List<RemindVerificationRule>> ruleMap = Optional.ofNullable(remindVerificationRuleList).orElse(Collections.emptyList()).stream()
                            .collect(Collectors.groupingBy(RemindVerificationRule::getRuleCode));
                log.info("ruleMap={}", JSONUtil.parse(ruleMap));

                List<RuleSetResult> ruleSetResults = response.getDecisionDetailedResults().getRuleSetResults();
                for (RuleSetResult ruleSetResult : ruleSetResults) {
                    DecisionStrategyFirst decisionStrategy1 = new DecisionStrategyFirst();
                    List<HitRuleResults> hitRuleResults = ruleSetResult.getHitRuleResults();
                    if (EmptyUtils.isNotEmpty(hitRuleResults) && hitRuleResults.size() > 0) {
                        decisionStrategy1.setDealTypeName(ruleSetResult.getDealTypeName());
                        List<DecisionStrategySecond> decisionStrategy2List = new ArrayList<>();
                        for (HitRuleResults hitRuleResult : hitRuleResults) {
                            log.info("hitRuleResult={}", JSONUtil.parse(hitRuleResult));
                            DecisionStrategySecond decisionStrategy2 = new DecisionStrategySecond();
                            // 2、设置决策策略二的分类(根据规则码分类)、提醒核实手势和人工核查内容，以下用的是同一个规则码，相当于大类
                            List<RemindVerificationRule> groupedRulesByRuleCode;
                            if (EmptyUtils.isNotEmpty(hitRuleResult) && StrUtil.isNotEmpty(hitRuleResult.getCustomId()) && CollUtil.isNotEmpty(ruleMap)) {
                                String customId = hitRuleResult.getCustomId();
                                decisionStrategy2.setRuleCode(customId);
                                CodeCategoryOrder codeCategoryOrder = codeCategoryOrderMap.get(customId);
                                decisionStrategy2.setOrder(EmptyUtils.isNotEmpty(codeCategoryOrder) ? codeCategoryOrder.getSortOrder() : 99);
                                decisionStrategy2.setCategory(EmptyUtils.isNotEmpty(codeCategoryOrder) ? codeCategoryOrder.getCategory() : "未分类");
                                decisionStrategy2.setHiddenFlag(EmptyUtils.isNotEmpty(codeCategoryOrder) ? codeCategoryOrder.getHiddenFlag() : "0");
                                groupedRulesByRuleCode = ruleMap.get(customId);
                                log.info("groupedRulesByRuleCode={}", JSONUtil.parse(groupedRulesByRuleCode));
                                if (EmptyUtils.isNotEmpty(groupedRulesByRuleCode)) {
                                    RemindVerificationRule remindVerificationRule = groupedRulesByRuleCode.get(0);
                                    log.info("remindVerificationRule={}", JSONUtil.parse(remindVerificationRule));
                                    // set 大类提示语
                                    decisionStrategy2.setVerificationGestureTip(remindVerificationRule.getVerificationGesture());
                                    decisionStrategy2.setManualCheckContentTip(remindVerificationRule.getManualCheckContent());
                                    // 如果首次 && 命中(进到这里说明是命中规则)，则添加到hitRules
                                    if (!historyFlag.get()) {
                                        log.info("首次命中规则，添加到hitRules");
                                        hitRules.add(remindVerificationRule);
                                    }
                                }
                            }
                            decisionStrategy2.setChange(false);
                            if (EmptyUtils.isNotEmpty(hitRuleResult) && StrUtil.isNotEmpty(hitRuleResult.getCustomId()) &&
                                    hitRuleResult.getCustomId().indexOf(Constant.NH) == -1
                                    && !hitRuleResult.getCustomId().startsWith(Constant.Z)
                            ) {
                                String name = hitRuleResult.getName();
                                if (StrUtil.isNotEmpty(name)) {
                                    decisionStrategy2.setName(name);
                                    if (oldList.size() > 0){
                                        decisionStrategy2.setChange(!oldList.stream().filter(m -> m.getName().equals(name)).findAny().isPresent());
                                    }
                                }
                                List<HitRuleResultDetail> hitRuleResultDetails = hitRuleResult.getHitRuleResultDetails();
                                if (CollUtil.isNotEmpty(hitRuleResultDetails)) {
                                    List<DecisionStrategyThird> decisionStrategy3ListList = new ArrayList<>();
                                    for (HitRuleResultDetail hitRuleResultDetail : hitRuleResultDetails) {
                                        DecisionStrategyThird decisionStrategy3 = new DecisionStrategyThird();
                                        decisionStrategy3.setData(hitRuleResultDetail.getData());
                                        decisionStrategy3.setDescription(hitRuleResultDetail.getDescription());
                                        decisionStrategy3ListList.add(decisionStrategy3);
                                    }
                                    decisionStrategy2.setDecisionStrategy3(decisionStrategy3ListList);
                                }
                                decisionStrategy2List.add(decisionStrategy2);
                            }
                        }
                        decisionStrategy1.setDecisionStrategy2(decisionStrategy2List);
                    }
                    if (EmptyUtils.isNotEmpty(decisionStrategy1.getDealTypeName())) {
                        decisionStrategy1List.add(decisionStrategy1);
                    }
                }
                // 如果是首次 && hitRules 不为空
                if (!historyFlag.get() && CollUtil.isNotEmpty(hitRules)) {
                    List<RemindVerificationRuleMapping> historyRules = buildRvrMappingHistoryList(applyNo, hitRules);
                    // 保存提醒核实规则核查内容订单绑定表
                    log.info("保存提醒核实规则核查内容订单绑定表，条数={}", historyRules.size());
                    remindVerificationRuleMappingService.saveBatch(historyRules);
                }
            }
            CustomInfo customInfo = new CustomInfo();
            EnsureInfo ensureInfo = new EnsureInfo();
            if (EmptyUtils.isNotEmpty(response) && EmptyUtils.isNotEmpty((response.getOutputVariable()))) {
                if (EmptyUtils.isNotEmpty(response.getOutputVariable().getCustom())) {
                    //组装评分人信息
                    IdTwoz idTwoz = response.getOutputVariable().getCustom().getIdTwoz();
                    if (EmptyUtils.isNotEmpty(idTwoz)) {
                        customInfo.setCsIdtwozresultCustom(idTwoz.getCsIdtwozresultCustom());
                        customInfo.setCsFlagidtwozCustom(idTwoz.getCsFlagidtwozCustom());
                    }
                    RuledDebtRepayStress ruledDebtRepayStress = response.getOutputVariable().getCustom().getRuledDebtRepayStress();
                    if (EmptyUtils.isNotEmpty(ruledDebtRepayStress)) {
                        customInfo.setCsDebtrepaystressscoreCustom(ruledDebtRepayStress.getCsAebtrepaystressscoreCustom());
                        customInfo.setCsFlagruleddebtrepaystressCustom(ruledDebtRepayStress.getCsFlagruleddebtrepaystressCustom());
                    }
                    RuleInfoRelationAutofinC ruleInfoRelationAutofinc = response.getOutputVariable().getCustom().getRuleInfoRelationAutofinc();
                    if (EmptyUtils.isNotEmpty(ruleInfoRelationAutofinc)) {
                        customInfo.setCsRuleinforelationautofinscoreCustom(ruleInfoRelationAutofinc.getCsRuleinforelationautofinscoreCustom());
                        customInfo.setCsIrm12idxcellcntCustom(ruleInfoRelationAutofinc.getCsIrm12idxcellcntCustom());
                        customInfo.setCsFlaginforelationCustom(ruleInfoRelationAutofinc.getCsFlaginforelationCustom());
                        customInfo.setCsFlagruleinforelationautofincCustom(ruleInfoRelationAutofinc.getCsFlagruleinforelationautofincCustom());
                    }
                    RuleApplyLoanAutofin ruleApplyLoanAutofin = response.getOutputVariable().getCustom().getRuleApplyLoanAutofin();
                    if (EmptyUtils.isNotEmpty(ruleApplyLoanAutofin)) {
                        customInfo.setCsAlsm1idaforgnumCustom(ruleApplyLoanAutofin.getCsAlsm1idaforgnumCustom());
                        customInfo.setCsAlsm1idnbankfinleaorgnumCustom(ruleApplyLoanAutofin.getCsAlsm1idnbankfinleaorgnumCustom());
                        customInfo.setCsFlagapplyloanstrCustom(ruleApplyLoanAutofin.getCsFlagapplyloanstrCustom());
                        customInfo.setCsAlsm6idnbankorgnumCustom(ruleApplyLoanAutofin.getCsAlsm6idnbankorgnumCustom());
                        customInfo.setCsAlsm6idnbankfinleaorgnumCustom(ruleApplyLoanAutofin.getCsAlsm6idnbankfinleaorgnumCustom());
                        customInfo.setCsRuleapplyloanautofinscoreCustom(ruleApplyLoanAutofin.getCsRuleapplyloanautofinscoreCustom());
                        customInfo.setCsAlsm6idaforgnumCustom(ruleApplyLoanAutofin.getCsAlsm6idaforgnumCustom());
                        customInfo.setCsAlsm12idnbankorgnumCustom(ruleApplyLoanAutofin.getCsAlsm12idnbankorgnumCustom());
                        customInfo.setCsFlagruleapplyloanautofinCustom(ruleApplyLoanAutofin.getCsFlagruleapplyloanautofinCustom());
                    }
                    RuledApplyLoanUsuryCustom ruledApplyLoanUsury = response.getOutputVariable().getCustom().getRuledApplyLoanUsury();
                    if (EmptyUtils.isNotEmpty(ruledApplyLoanUsury)) {
                        customInfo.setCsApplyloanusuryscoreCustom(ruledApplyLoanUsury.getCsApplyloanusuryscoreCustom());
                        customInfo.setCsFlagruledapplyloanusuryCustom(ruledApplyLoanUsury.getCsFlagruledapplyloanusuryCustom());
                    }
                    RuleSpecialListcCustom ruleSpecialListc = response.getOutputVariable().getCustom().getRuleSpecialListc();
                    if (EmptyUtils.isNotEmpty(ruleSpecialListc)) {
                        customInfo.setCsSlidcourtexecutedCustom(ruleSpecialListc.getCsSlidcourtexecutedCustom());
                        customInfo.setCsRulespeciallistscoreCustom(ruleSpecialListc.getCsRulespeciallistscoreCustom());
                        customInfo.setCsFlagspeciallistcCustom(ruleSpecialListc.getCsFlagspeciallistcCustom());
                        customInfo.setCsFlagrulespeciallistcCustom(ruleSpecialListc.getCsFlagrulespeciallistcCustom());
                        customInfo.setCsSlidcourtbadCustom(ruleSpecialListc.getCsSlidcourtbadCustom());
                    }
                    RuleExecutionCustom ruleExecution = response.getOutputVariable().getCustom().getRuleExecution();
                    if (EmptyUtils.isNotEmpty(ruleExecution)) {
                        customInfo.setCsRuleexecutionscoreCustom(ruleExecution.getCsRuleexecutionscoreCustom());
                        customInfo.setCsFlagruleexecutionCustom(ruleExecution.getCsFlagruleexecutionCustom());
                    }
                    RuledFraudRelationgCustom ruledFraudRelationg = response.getOutputVariable().getCustom().getRuledFraudRelationg();
                    if (EmptyUtils.isNotEmpty(ruledFraudRelationg)) {
                        customInfo.setCsFraudrelationscoreCustom(ruledFraudRelationg.getCsFraudrelationscoreCustom());
                        customInfo.setCsFlagruledfraudrelationgCustom(ruledFraudRelationg.getCsFlagruledfraudrelationgCustom());
                    }
                    TelPeriod telPeriod = response.getOutputVariable().getCustom().getTelPeriod();
                    if (EmptyUtils.isNotEmpty(telPeriod)) {
                        customInfo.setCsTelperiodoperationCustom(telPeriod.getCsTelperiodoperationCustom());
                        customInfo.setCsFlagtelperiodCustom(telPeriod.getCsFlagtelperiodCustom());
                        customInfo.setCsTelperiodvalueCustom(telPeriod.getCsTelperiodvalueCustom());
                    }
                    ScoreCustCustom scoreCust = response.getOutputVariable().getCustom().getScoreCust();
                    if (EmptyUtils.isNotEmpty(response.getOutputVariable().getCustom().getScoreCust())) {
                        customInfo.setCsScorecustscoreCustom(scoreCust.getCsScorecustscoreCustom());
                        customInfo.setCsFlagscoreCustom(scoreCust.getCsFlagscoreCustom());
                    }
                    BhCheatScore customInfoBhCheatScore = response.getOutputVariable().getCustom().getBhCheatScore();
                    if (EmptyUtils.isNotEmpty(customInfoBhCheatScore)) {
                        customInfo.setPersonCreditIndices(String.valueOf(customInfoBhCheatScore.getCnPersonCreditIndices()));
                        customInfo.setPersonCheatIndices(String.valueOf(customInfoBhCheatScore.getCnPersonCheatIndices()));
                        customInfo.setLoanActiveIndices(String.valueOf(customInfoBhCheatScore.getCnLoanActiveIndices()));
                        customInfo.setBreakActiveIndices(String.valueOf(customInfoBhCheatScore.getCnBreakActiveIndices()));
                        customInfo.setGangCheatIndices(String.valueOf(customInfoBhCheatScore.getCnGangCheatIndices()));
                        customInfo.setGangScaleIndices(String.valueOf(customInfoBhCheatScore.getCnGangScaleIndices()));
                        customInfo.setCheatNameHit(customInfoBhCheatScore.getCsCheatNameHit());
                        customInfo.setRiskScore(String.valueOf(customInfoBhCheatScore.getCnRiskScore()));
                    }
                    Custom.BhIncluseScore customBhIncluseScore = response.getOutputVariable().getCustom().getBhIncluseScore();
                    if (EmptyUtils.isNotEmpty(customBhIncluseScore)) {
                        customInfo.setScore(customBhIncluseScore.getCsScore());
                    }
                    BhSpecialList customInfoBhSpecialList = response.getOutputVariable().getCustom().getBhSpecialList();
                    if (EmptyUtils.isNotEmpty(customInfoBhSpecialList)) {
                        customInfo.setP2pEscapeDebtStatus(customInfoBhSpecialList.getCsP2pescapedebtstatus());
                        customInfo.setMaxOverDueStatus(customInfoBhSpecialList.getCsMaxoverduestatus());
                        customInfo.setOverdue30d24m(customInfoBhSpecialList.getCsOverdue30d24m());
                        customInfo.setOverdue15d12m(customInfoBhSpecialList.getCsOverdue15d12m());
                        customInfo.setFirstOverdue7d24m(customInfoBhSpecialList.getCsFirstoverdue7d24m());
                        customInfo.setLargeAmtOverDue30d36m(customInfoBhSpecialList.getCsLargeamtoverdue30d36m());
                        customInfo.setSupremeCourtExecutedStatus(customInfoBhSpecialList.getCsSupremecourtexecutedstatus());
                    }
                    BhRiskPorList customInfoBhRiskPorList = response.getOutputVariable().getCustom().getBhRiskPorList();
                    if (EmptyUtils.isNotEmpty(customInfoBhRiskPorList)) {
                        customInfo.setLabNumbers(String.valueOf(customInfoBhRiskPorList.getCnLabnumbers()));
                        customInfo.setLongEndLendingGradeLow(customInfoBhRiskPorList.getCsLongendlendingGradelow());
                        customInfo.setFraudulentLoanGradeLow(customInfoBhRiskPorList.getCsFraudulentloanGradelow());
                        customInfo.setLastPerformanceRiskGradeLow(customInfoBhRiskPorList.getCsLastperformanceriskGradelow());
                        customInfo.setMultiInstitutionalLoansGradeMiddle(customInfoBhRiskPorList.getCsMultiinstitutionalloansGrademiddle());
                        customInfo.setFraudulentLoanGrade(customInfoBhRiskPorList.getCsFraudulentloanGrade());
                        customInfo.setIdRiskIndexHighT(customInfoBhRiskPorList.getCsIdriskIndexhight());
                        customInfo.setHistoryFirstExRiskGradeHighT(customInfoBhRiskPorList.getCsHistoryfirstexriskGradehight());
                        customInfo.setLastPerformanceRisk(customInfoBhRiskPorList.getCsLastperformancerisk());
                        customInfo.setHistoryPerForRiskGradeLow(customInfoBhRiskPorList.getCsHistoryperforriskGradelow());
                        customInfo.setHistoryFirstExRiskGradeLow(customInfoBhRiskPorList.getCsHistoryfirstexriskGradelow());
                        customInfo.setViewValue(customInfoBhRiskPorList.getCsViewvalue());
                    }
                }
                if (EmptyUtils.isNotEmpty(response.getOutputVariable().getEnsure())) {
                    RuledApplyLoanUsury ruledApplyLoanUsury = response.getOutputVariable().getEnsure().getRuledApplyLoanUsury();
                    if (EmptyUtils.isNotEmpty(ruledApplyLoanUsury)) {
                        ensureInfo.setCsApplyloanusuryscoreEnsure(ruledApplyLoanUsury.getCsApplyloanusuryscoreEnsure());
                        ensureInfo.setCsFlagruledapplyloanusuryEnsure(ruledApplyLoanUsury.getCsFlagruledapplyloanusuryEnsure());
                    }
                    RuledDebtRepayStressEnsure ruledDebtRepayStress = response.getOutputVariable().getEnsure().getRuledDebtRepayStress();
                    if (EmptyUtils.isNotEmpty(ruledDebtRepayStress)) {
                        ensureInfo.setCsDebtrepaystressscoreEnsure(ruledDebtRepayStress.getCsDebtrepaystressscoreEnsure());
                        ensureInfo.setCsFlagruleddebtrepaystressEnsure(ruledDebtRepayStress.getCsFlagruleddebtrepaystressEnsure());
                    }
                    RuleSpecialListC ruleSpecialListc = response.getOutputVariable().getEnsure().getRuleSpecialListc();
                    if (EmptyUtils.isNotEmpty(ruleSpecialListc)) {
                        ensureInfo.setCsFlagspeciallistcEnsure(ruleSpecialListc.getCsFlagspeciallistcEnsure());
                        ensureInfo.setCsRulespeciallistscoreEnsure(ruleSpecialListc.getCsRulespeciallistscoreEnsure());
                        ensureInfo.setCsFlagrulespeciallistcEnsure(ruleSpecialListc.getCsFlagrulespeciallistcEnsure());
                        ensureInfo.setCsSlidcourtexecutedEnsure(ruleSpecialListc.getCsSlidcourtexecutedEnsure());
                        ensureInfo.setCsSlidcourtbadEnsure(ruleSpecialListc.getCsSlidcourtbadEnsure());
                    }
                    RuleExecution ruleExecution = response.getOutputVariable().getEnsure().getRuleExecution();
                    if (EmptyUtils.isNotEmpty(ruleExecution)) {
                        ensureInfo.setCsRuleexecutionscoreEnsure(ruleExecution.getCsRuleexecutionscoreEnsure());
                        ensureInfo.setCsFlagruleexecutionEnsure(ruleExecution.getCsFlagruleexecutionEnsure());
                    }
                    RuledFraudRelationG ruledFraudRelationg = response.getOutputVariable().getEnsure().getRuledFraudRelationg();
                    if (EmptyUtils.isNotEmpty(ruledFraudRelationg)) {
                        ensureInfo.setCsFlagruledfraudrelationgEnsure(ruledFraudRelationg.getCsFlagruledfraudrelationgEnsure());
                        ensureInfo.setCsFraudrelationscoreEnsure(ruledFraudRelationg.getCsFraudrelationscoreEnsure());
                    }
                    ScoreCust scoreCust = response.getOutputVariable().getEnsure().getScoreCust();
                    if (EmptyUtils.isNotEmpty(scoreCust)) {
                        ensureInfo.setCsScorecustscoreEnsure(scoreCust.getCsScorecustscoreEnsure());
                        ensureInfo.setCsFlagscoreEnsure(scoreCust.getCsFlagscoreEnsure());
                    }
                    RuleInfoRelationAutofincEnsure ruleInfoRelationAutofinc = response.getOutputVariable().getEnsure().getRuleInfoRelationAutofinc();
                    if (EmptyUtils.isNotEmpty(ruleInfoRelationAutofinc)) {
                        ensureInfo.setCsRuleinforelationautofinscoreEnsure(ruleInfoRelationAutofinc.getCsRuleinforelationautofinscoreEnsure());
                        ensureInfo.setCsFlagruleinforelationautofincEnsure(ruleInfoRelationAutofinc.getCsFlagruleinforelationautofincEnsure());
                    }
                    BhCheatScore ensureInfoBhCheatScore = response.getOutputVariable().getEnsure().getBhCheatScore();
                    if (EmptyUtils.isNotEmpty(ensureInfoBhCheatScore)) {
                        ensureInfo.setPersonCreditIndices(String.valueOf(ensureInfoBhCheatScore.getCnPersonCreditIndices()));
                        ensureInfo.setPersonCheatIndices(String.valueOf(ensureInfoBhCheatScore.getCnPersonCheatIndices()));
                        ensureInfo.setLoanActiveIndices(String.valueOf(ensureInfoBhCheatScore.getCnLoanActiveIndices()));
                        ensureInfo.setBreakActiveIndices(String.valueOf(ensureInfoBhCheatScore.getCnBreakActiveIndices()));
                        ensureInfo.setGangCheatIndices(String.valueOf(ensureInfoBhCheatScore.getCnGangCheatIndices()));
                        ensureInfo.setGangScaleIndices(String.valueOf(ensureInfoBhCheatScore.getCnGangScaleIndices()));
                        ensureInfo.setCheatNameHit(ensureInfoBhCheatScore.getCsCheatNameHit());
                        ensureInfo.setRiskScore(String.valueOf(ensureInfoBhCheatScore.getCnRiskScore()));
                    }
                    Ensure.BhIncluseScore customBhIncluseScore = response.getOutputVariable().getEnsure().getBhIncluseScore();
                    if (EmptyUtils.isNotEmpty(customBhIncluseScore)) {
                        ensureInfo.setScore(customBhIncluseScore.getCsScore());
                    }
                    BhSpecialList ensureInfoBhSpecialList = response.getOutputVariable().getEnsure().getBhSpecialList();
                    if (EmptyUtils.isNotEmpty(ensureInfoBhSpecialList)) {
                        ensureInfo.setP2pEscapeDebtStatus(ensureInfoBhSpecialList.getCsP2pescapedebtstatus());
                        ensureInfo.setMaxOverDueStatus(ensureInfoBhSpecialList.getCsMaxoverduestatus());
                        ensureInfo.setOverdue30d24m(ensureInfoBhSpecialList.getCsOverdue30d24m());
                        ensureInfo.setOverdue15d12m(ensureInfoBhSpecialList.getCsOverdue15d12m());
                        ensureInfo.setFirstOverdue7d24m(ensureInfoBhSpecialList.getCsFirstoverdue7d24m());
                        ensureInfo.setLargeAmtOverDue30d36m(ensureInfoBhSpecialList.getCsLargeamtoverdue30d36m());
                        ensureInfo.setSupremeCourtExecutedStatus(ensureInfoBhSpecialList.getCsSupremecourtexecutedstatus());
                    }
                    BhRiskPorList ensureInfoBhRiskPorList = response.getOutputVariable().getEnsure().getBhRiskPorList();
                    if (EmptyUtils.isNotEmpty(ensureInfoBhRiskPorList)) {
                        ensureInfo.setLabNumbers(ensureInfoBhRiskPorList.getCnLabnumbers());
                        ensureInfo.setLongEndLendingGradeLow(ensureInfoBhRiskPorList.getCsLongendlendingGradelow());
                        ensureInfo.setFraudulentLoanGradeLow(ensureInfoBhRiskPorList.getCsFraudulentloanGradelow());
                        ensureInfo.setLastPerformanceRiskGradeLow(ensureInfoBhRiskPorList.getCsLastperformanceriskGradelow());
                        ensureInfo.setMultiInstitutionalLoansGradeMiddle(ensureInfoBhRiskPorList.getCsMultiinstitutionalloansGrademiddle());
                        ensureInfo.setFraudulentLoanGrade(ensureInfoBhRiskPorList.getCsFraudulentloanGrade());
                        ensureInfo.setIdRiskIndexHighT(ensureInfoBhRiskPorList.getCsIdriskIndexhight());
                        ensureInfo.setHistoryFirstExRiskGradeHighT(ensureInfoBhRiskPorList.getCsHistoryfirstexriskGradehight());
                        ensureInfo.setLastPerformanceRisk(ensureInfoBhRiskPorList.getCsLastperformancerisk());
                        ensureInfo.setHistoryPerForRiskGradeLow(ensureInfoBhRiskPorList.getCsHistoryperforriskGradelow());
                        ensureInfo.setHistoryFirstExRiskGradeLow(ensureInfoBhRiskPorList.getCsHistoryfirstexriskGradelow());
                        ensureInfo.setViewValue(ensureInfoBhRiskPorList.getCsViewvalue());
                    }
                }
            }
            JSONObject res = new JSONObject();
            res.put("decisionResult", Collections.singletonList(decisionResult));
            DecisionStrategySorter.categorizeAndSort(decisionStrategy1List, codeCategoryOrderMap);
            res.put("decisionStrategy", decisionStrategy1List);
            res.put("customInfo", customInfo);
            res.put("ensureInfo", ensureInfo);
            return IResponse.success(res);
        }
        return IResponse.fail("未获取到决策结果！");
    }

    /**
     * 获取提醒核实规则核查内容订单绑定表
     * @param applyNo
     * @param hitRules
     * @return
     */
    private List<RemindVerificationRuleMapping> buildRvrMappingHistoryList(String applyNo, List<RemindVerificationRule> hitRules) {
        List<RemindVerificationRuleMapping> historyRules = new ArrayList<>();
        for (RemindVerificationRule hitRule : hitRules) {
            RemindVerificationRuleMapping ruleMapping = new RemindVerificationRuleMapping();
            ruleMapping.setApplyNo(applyNo);
            ruleMapping.setRuleCode(hitRule.getRuleCode());
            ruleMapping.setCategory(hitRule.getCategory());
            ruleMapping.setDescription(hitRule.getDescription());
            ruleMapping.setVerificationGesture(hitRule.getVerificationGesture());
            ruleMapping.setManualCheckContent(hitRule.getManualCheckContent());
            ruleMapping.setActiveFlag(CommonConstants.COMMON_YES);
            historyRules.add(ruleMapping);
        }
        return historyRules;
    }


    public void setDecisionList(HitRuleResults hitRuleResult,List<DecisionStrategySecond> list,List<DecisionStrategySecond> oldList){
        DecisionStrategySecond decisionStrategy2 = new DecisionStrategySecond();
        decisionStrategy2.setChange(false);
        String name = hitRuleResult.getName();
        if (StrUtil.isNotEmpty(name)) {
            decisionStrategy2.setName(name);
            if (oldList.size() > 0){
                decisionStrategy2.setChange(!oldList.stream().filter(m -> m.getName().equals(name)).findAny().isPresent());
            }
            List<HitRuleResultDetail> hitRuleResultDetails = hitRuleResult.getHitRuleResultDetails();
            if (CollUtil.isNotEmpty(hitRuleResultDetails)) {
                List<DecisionStrategyThird> decisionStrategy3ListList = new ArrayList<>();
                for (HitRuleResultDetail hitRuleResultDetail : hitRuleResultDetails) {
                    DecisionStrategyThird decisionStrategy3 = new DecisionStrategyThird();
                    decisionStrategy3.setData(hitRuleResultDetail.getData());
                    decisionStrategy3.setDescription(hitRuleResultDetail.getDescription());
                    decisionStrategy3ListList.add(decisionStrategy3);
                }
                decisionStrategy2.setDecisionStrategy3(decisionStrategy3ListList);
            }
            list.add(decisionStrategy2);
        }
    }

    @Override
    public boolean isTopChannelByApplyNo(String applyNo) {

        List<ThirdData> list = thirdDataMapper.selectList(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, applyNo)
                .orderByDesc(ThirdData::getCreateTime)
                .last("limit 1"));
        if (list != null && !list.isEmpty()) {

            JSONObject rsp = JSONObject.parseObject(list.get(0).getResponse());
            if (EmptyUtils.isNotEmpty(rsp)){
                Response res = rsp.toJavaObject(Response.class);
                if (EmptyUtils.isNotEmpty(res) && EmptyUtils.isNotEmpty(res.getDecisionDetailedResults())){

                    List<String> ruleCustomIds = res.getDecisionDetailedResults().getRuleCustomIds();

                    if(ruleCustomIds.contains("T304")){
                        return true;
                    }
                }
            }

        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void saveApproveThirdData(String applyNo, IResponse<JSONObject> iResponse, JSONObject reqBody) {
        //设置默认属性
        ThirdData thirdData = new ThirdData();
        thirdData.setApproveId(applyNo);
        thirdData.setRequest(reqBody.toJSONString());
        thirdData.setResponse(Optional.ofNullable(iResponse.getData()).map(JSONAware::toJSONString).orElse(null));
        thirdData.setStatus((short) 0);
        thirdDataMapper.insert(thirdData);
    }

    @Override
    public IResponse queryThirdDataLoanByApplyNo(String applyNo) {
        ThirdData thirdData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getLoanId, applyNo)
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));

        if (thirdData != null) {
            JSONObject jsonReq = JSONObject.parseObject(thirdData.getRequest());
            JSONObject jsonRsp = JSONObject.parseObject(thirdData.getResponse());
            Request request = null;
            Response response = null;
            if (EmptyUtils.isNotEmpty(jsonReq)) {
                request = jsonReq.toJavaObject(Request.class);
            }
            if (EmptyUtils.isNotEmpty(jsonRsp)) {
                response = jsonRsp.toJavaObject(Response.class);
            }

            //组装决策结果对象信息
            DecisionResult decisionResult = new DecisionResult();
            decisionResult.setApproveId(applyNo);
            if (EmptyUtils.isNotEmpty(response)) {
                if ("0".equals(response.getReasonCode())) {
                    decisionResult.setFinalDealTypeName(response.getFinalDealTypeName());
                } else {
                    decisionResult.setFinalDealTypeName(response.getReasonDesc());
                }
                decisionResult.setSeqId(response.getSeqId());
            }
            if (EmptyUtils.isNotEmpty(request)) {
                decisionResult.setEntryoccurtime(request.getEntryoccurtime());
            }
            //组装决策策略对象信息
            List<DecisionStrategyFirst> decisionStrategy1List = new ArrayList<>();
            if (EmptyUtils.isNotEmpty(response) && EmptyUtils.isNotEmpty(response.getDecisionDetailedResults())) {
                List<RuleSetResult> ruleSetResults = response.getDecisionDetailedResults().getRuleSetResults();
                for (RuleSetResult ruleSetResult : ruleSetResults) {
                    DecisionStrategyFirst decisionStrategy1 = new DecisionStrategyFirst();
                    List<HitRuleResults> hitRuleResults = ruleSetResult.getHitRuleResults();
                    if (EmptyUtils.isNotEmpty(hitRuleResults) && hitRuleResults.size() > 0) {
                        decisionStrategy1.setDealTypeName(ruleSetResult.getDealTypeName());
                        List<DecisionStrategySecond> decisionStrategy2List = new ArrayList<>();
                        for (HitRuleResults hitRuleResult : hitRuleResults) {
                            DecisionStrategySecond decisionStrategy2 = new DecisionStrategySecond();
                            if (EmptyUtils.isNotEmpty(hitRuleResult) && StrUtil.isNotEmpty(hitRuleResult.getCustomId()) &&
                                    hitRuleResult.getCustomId().indexOf(Constant.NH) == -1
                                    && !hitRuleResult.getCustomId().startsWith(Constant.Z)
                            ) {
                                String name = hitRuleResult.getName();
                                if (StrUtil.isNotEmpty(name)) {
                                    decisionStrategy2.setName(name);
                                }
                                List<HitRuleResultDetail> hitRuleResultDetails = hitRuleResult.getHitRuleResultDetails();
                                if (CollUtil.isNotEmpty(hitRuleResultDetails)) {
                                    List<DecisionStrategyThird> decisionStrategy3ListList = new ArrayList<>();
                                    for (HitRuleResultDetail hitRuleResultDetail : hitRuleResultDetails) {
                                        DecisionStrategyThird decisionStrategy3 = new DecisionStrategyThird();
                                        decisionStrategy3.setData(hitRuleResultDetail.getData());
                                        decisionStrategy3.setDescription(hitRuleResultDetail.getDescription());
                                        decisionStrategy3ListList.add(decisionStrategy3);
                                    }
                                    decisionStrategy2.setDecisionStrategy3(decisionStrategy3ListList);
                                }
                                decisionStrategy2List.add(decisionStrategy2);
                            }
                        }
                        decisionStrategy1.setDecisionStrategy2(decisionStrategy2List);
                    }
                    if (EmptyUtils.isNotEmpty(decisionStrategy1.getDealTypeName())) {
                        decisionStrategy1List.add(decisionStrategy1);
                    }
                }
            }
            JSONObject res = new JSONObject();

            if (EmptyUtils.isNotEmpty(jsonRsp)) {
                JSONObject outputVariableJson = jsonRsp.getJSONObject("outputVariable");
                if (EmptyUtils.isNotEmpty(outputVariableJson)) {
                    outputVariableJson.remove("SYSTEM");
                    res.put("outputVariable", outputVariableJson);
                }
            }

            res.put("decisionResult", Collections.singletonList(decisionResult));
            res.put("decisionStrategy", decisionStrategy1List);
            return IResponse.success(res);
        }

        return IResponse.fail("未获取到决策结果！");
    }

    @Override
    public IResponse<String> queryLoanThirdUrlByApplyNo(String applyNo) {
        ThirdData thirdData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getLoanId, applyNo)
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (thirdData != null) {
            JSONObject jsonObject = JSONObject.parseObject(thirdData.getResponse());
            String token;
            token = Optional.ofNullable(jsonObject).map(json -> json.getString("token")).orElse(null);
            if (StringUtils.isNotBlank(token)) {
                return IResponse.success(riskControlServerConfig.getReportUrl() + token);
            }
        }
        return IResponse.fail("获取地址失败！");
    }

    @Override
    public List<String> queryDecisionEnginePre(String applyId) {
        List<String> resultList = new ArrayList<>();
        ThirdData thirdData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApplyId, applyId)
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (thirdData != null){
            String response = thirdData.getResponse();
            if (EmptyUtils.isNotEmpty(response)) {
                JSONObject jsonObject = JSONObject.parseObject(response);
                String finalDealTypeName = jsonObject.getString("finalDealTypeName");
                if (SUCCESS.equals(finalDealTypeName)) {
                    resultList.add(finalDealTypeName);
                } else {
                    JSONObject decisionDetailedResults = jsonObject.getJSONObject("decisionDetailedResults");
                    if (EmptyUtils.isNotEmpty(decisionDetailedResults)) {
                        resultList = decisionDetailedResults.getJSONArray("ruleCustomIds").toJavaList(String.class);
                    }
                }
            }
        }
        log.info("决策引擎的结果是{}",resultList);
        return resultList;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void saveadaitInformation(String applyNo, IResponse<JSONObject> iResponse) {
        log.info("开始解析决策引擎人工审核项");
        try {
            Response response = iResponse.getData().toJavaObject(Response.class);
            List<ManualReviewInformation> list = new ArrayList<>();
            if (EmptyUtils.isNotEmpty(response) && EmptyUtils.isNotEmpty(response.getDecisionDetailedResults())) {
                List<RuleSetResult> ruleSetResults = response.getDecisionDetailedResults().getRuleSetResults();
                for (RuleSetResult ruleSetResult : ruleSetResults) {
                    List<HitRuleResults> hitRuleResults = ruleSetResult.getHitRuleResults();
                    if (EmptyUtils.isNotEmpty(hitRuleResults) && hitRuleResults.size() > 0){
                        if (StrUtil.equals("人工审核",ruleSetResult.getDealTypeName()) &&  !StrUtil.equals("转人工客群",ruleSetResult.getName())){
                            for (HitRuleResults hitRuleResult : hitRuleResults) {
                                if (EmptyUtils.isNotEmpty(hitRuleResult) && StrUtil.isNotEmpty(hitRuleResult.getCustomId()) &&
                                        hitRuleResult.getCustomId().indexOf(Constant.NH) == -1
                                        && !hitRuleResult.getCustomId().startsWith(Constant.Z)
                                ) {
                                    String name = hitRuleResult.getName();
                                    if (StrUtil.isNotEmpty(name)){
                                        ManualReviewInformation manualReviewInformation = new ManualReviewInformation();
                                        manualReviewInformation.setAdaitInformation(name);
                                        manualReviewInformation.setApplyNo(applyNo);
                                        list.add(manualReviewInformation);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (list.size() > 0){
                manualReviewInformationService.remove(Wrappers.<ManualReviewInformation>lambdaQuery().eq(ManualReviewInformation::getApplyNo,applyNo));
                manualReviewInformationService.saveBatch(list);
            }
        }catch (Exception e){
            log.error("决策引擎人工审核信息保存失败:{}",e);
        }
    }
}
