package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeSubmitVo;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface WriteOffPayRecordService extends IService<WriteOffPayRecord> {

    /**
     * 按条件查询
     * @param queryCondition
     * @return
     */
    IResponse queryByCondition(QueryCondition<WriteOffPayRecord> queryCondition);
    /**
     * 数据导出
     * @param condition
     * @param response
     */
    void exportData(WriteOffPayRecord condition, HttpServletResponse response);

    /**
     * 发起提取流程
     * @param submitVo
     * @return
     */
    IResponse submitApprove(FeeSubmitVo submitVo);

    /**
     * 查询cbs付款结果，激活等待节点
     * @return
     */
    IResponse getCbsPayResult();

    /**
     * 人工付款，激活等待节点
     * @param idList
     * @return
     */
    IResponse artificialPay(List<Long> idList);

    /**
     * 失败记录发起重新支付
     * @param idList
     * @return
     */
    IResponse failedRecordInitiatesRepay(List<Long> idList);

    /**
     * 老页面的提取支付
     *
     * @param writeOffBaseInvoiceRels
     * @param flowFlag
     * @param businessNo
     */
    public void relCbsPay(List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels, Boolean flowFlag, String businessNo);

    /**
     * 新页面的分期提取支付
     *
     * @param payRecordList
     * @param flowFlag
     * @param businessNo
     */
    public void recordCbsPay(List<WriteOffPayRecord> payRecordList, Boolean flowFlag, String businessNo);
}
