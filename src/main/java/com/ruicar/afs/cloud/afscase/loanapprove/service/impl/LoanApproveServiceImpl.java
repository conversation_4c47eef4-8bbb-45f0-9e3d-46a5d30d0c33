package com.ruicar.afs.cloud.afscase.loanapprove.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.ApproveTaskServiceImpl;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.StepParam;
import com.ruicar.afs.cloud.afscase.autoaudit.service.IRuleAtomDataService;
import com.ruicar.afs.cloud.afscase.autoaudit.util.ActiveStepUtil;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.entity.CaseBackToPartnersInfo;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.service.CaseBackToPartnersInfoService;
import com.ruicar.afs.cloud.afscase.casemaininfo.entity.CaseMainInfo;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAffiliatedUnits;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelWitnessInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAffiliatedUnitsService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelWitnessInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustHistory;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustHistoryService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFinancingItemsService;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.LoanActivateService;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.InvoiceOcrCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.AddedFinancingItems;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInsuranceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.LoanBankCardInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.service.AddedFinancingItemsService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInsuranceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityChangeService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityRecordService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanApproveService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanBankCardInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanWorkflowService;
import com.ruicar.afs.cloud.afscase.loanapprove.task.condition.CarInsuranceInfoCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.AddedFinancingItemsVO;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.CarInsuranceInfoVO;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.CaseCostInfoVO;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.CaseCustHistoyVO;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.FinFinancingItemsVO;
import com.ruicar.afs.cloud.afscase.loanapprove.vo.InvoiceCheckVo;
import com.ruicar.afs.cloud.afscase.loandealerfirstmortgage.entity.LoanDealerFirstMortgage;
import com.ruicar.afs.cloud.afscase.loandealerfirstmortgage.service.LoanDealerFirstMortgageService;
import com.ruicar.afs.cloud.afscase.loanexamination.service.LoanExaminationInfoService;
import com.ruicar.afs.cloud.afscase.loanflawfix.entity.CaseManageAssetChange;
import com.ruicar.afs.cloud.afscase.loanflawfix.service.CaseManageAssetChangeService;
import com.ruicar.afs.cloud.afscase.loanflawfix.service.LoanFlawFixDetailService;
import com.ruicar.afs.cloud.afscase.loanflawfix.vo.CaseManageAssetChangeVo;
import com.ruicar.afs.cloud.afscase.loanflawfix.vo.LoanFlawFixDetailVo;
import com.ruicar.afs.cloud.afscase.loaninvoicecheckrecord.service.LoanInvoiceCheckRecordService;
import com.ruicar.afs.cloud.afscase.loanmoderuleinfo.entity.LoanModeRuleInfo;
import com.ruicar.afs.cloud.afscase.loanmoderuleinfo.service.LoanModeRuleInfoService;
import com.ruicar.afs.cloud.afscase.loanovertimerule.entity.LoanOverTimeRule;
import com.ruicar.afs.cloud.afscase.loanovertimerule.service.LoanOverTimeRuleService;
import com.ruicar.afs.cloud.afscase.loansecondaryfraudinfo.entity.LoanSecondaryFraudInfo;
import com.ruicar.afs.cloud.afscase.loansecondaryfraudinfo.service.LoanSecondaryFraudInfoService;
import com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.entity.LoanSpecialBusinessInfo;
import com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.service.LoanSpecialBusinessInfoService;
import com.ruicar.afs.cloud.afscase.loansuspendrule.entity.LoanSuspendRule;
import com.ruicar.afs.cloud.afscase.loansuspendrule.service.LoanSuspendRuleService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.PushDataForPos;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveLoanInfoService;
import com.ruicar.afs.cloud.afscase.mq.entity.CaseMqCompareInfo;
import com.ruicar.afs.cloud.afscase.mq.service.CaseMqAcceptRecordService;
import com.ruicar.afs.cloud.afscase.mq.service.CaseMqCompareInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum;
import com.ruicar.afs.cloud.afscase.vehicle.service.VehicleInfoService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarFunctionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.FlowNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.InsuranceModeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LendingModeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LockDegreeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LockTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PaymentObjectEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PriorityEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.StatusDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.SendToApplyContractInfoDTO;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.BusiNodeEnum;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.parameter.commom.enums.InsuranceTypeEnums;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.service.UserCollocationService;
import com.ruicar.afs.cloud.vehicle.vo.VehicleModelNewVO;
import com.ruicar.afs.cloud.websocket.utils.WebSocketUtils;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUnit.DAY;


/**
 * <AUTHOR>
 * @Date 2020/7/27
 * @description
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class LoanApproveServiceImpl implements LoanApproveService {
    /**
     * 二次反欺诈
     */
    private final LoanSecondaryFraudInfoService loanSecondaryFraudInfoService;
    /**
     * 贷中特殊业务申请
     */
    private final LoanSpecialBusinessInfoService loanSpecialBusinessInfoService;
    /**
     * 退件记录
     */
    private final CaseBackToPartnersInfoService caseBackToPartnersInfoService;
    /**
     * 基本信息
     */
    private final CaseBaseInfoService caseBaseInfoService;
    /**
     * 合同信息
     */
    private CaseContractInfoService caseContractInfoService;
    /**
     * 渠道与申请件关联信息
     */
    private final CaseChannelInfoService caseChannelInfoService;
    /**
     * 渠道信息
     */
    private final ChannelBaseInfoService channelBaseInfoService;
    /**
     * 渠道见证人
     */
    private final ChannelWitnessInfoService channelWitnessInfoService;
    /**
     * 客户信息
     */
    private final CaseCustInfoService caseCustInfoService;
    /**
     * 车辆信息
     */
    private final CaseCarInfoService caseCarInfoService;
    /**
     * 车型信息
     */
    private final VehicleInfoService vehicleInfoService;
    /**
     * 渠道挂靠信息
     */
    private final ChannelAffiliatedUnitsService channelAffiliatedUnitsService;
    /**
     * 费用信息
     */
    private final CaseCostInfoService caseCostInfoService;
    /**
     * 费用信息  --车款
     */
    private final CaseFinancingItemsService caseFinancingItemsService;
    /**
     * 增容项
     */
    private final AddedFinancingItemsService addedFinancingItemsService;
    /**
     * 保险信息
     */
    private final CarInsuranceInfoService carInsuranceInfoService;
    /**
     * 发票信息
     */
    private final CarInvoiceInfoService carInvoiceInfoService;
    /**
     * 还款信息（银行卡申请信息）
     */
    private final LoanBankCardInfoService loanBankCardInfoService;
    /**
     * 工作流
     */
    private final LoanWorkflowService loanWorkflowService;
    /**
     * MQ通知
     */
    private final ApproveLoanInfoService approveLoanInfoService;
    /**
     * 放款待激活池
     */
    private final LoanActivateService loanActivateService;

    /**
     * 规则报文组装
     */
    private IRuleAtomDataService ruleAtomDataService;

    /**优先级历史记录*/
    private CasePriorityRecordService casePriorityRecordService;

    /**审批记录*/
    private CaseApproveRecordService caseApproveRecordService;



    private WorkTaskPoolService workTaskPoolService;

    private CaseMqCompareInfoService caseMqCompareInfoService;


    private LoanModeRuleInfoService loanModeRuleInfoService;

    private LoanDealerFirstMortgageService loanDealerFirstMortgageService;

    private ChannelRiskInfoService channelRiskInfoService;

    private LoanSuspendRuleService loanSuspendRuleService;

    private CaseCustHistoryService caseCustHistoryService;

    private CasePriorityChangeService casePriorityChangeService;

    private CaseMqAcceptRecordService caseMqAcceptRecordService;

    private CaseManageAssetChangeService caseManageAssetChangeService;

    private LoanFlawFixDetailService loanFlawFixDetailService;

    private LoanOverTimeRuleService loanOverTimeRuleService;

    private LoanInvoiceCheckRecordService loanInvoiceCheckRecordService;

    private CaseConfParamService caseConfParamService;

    private ComAttachmentFileService comAttachmentFileService;

    private ComAttachmentManagementService comAttachmentManagementService;

    private LoanExaminationInfoService loanExaminationInfoService;

    private ApproveTaskServiceImpl approveTaskService;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private WorkflowHelper workflowHelper;
    private WorkflowWrapperService workflowWrapperService;
    private UserCollocationService userCollocationService;
    private final FlowConfigProperties flowConfigProperties;
    private WorkflowProcessBusinessRefInfoService processBusinessRefInfoService;
    private CaseMainInfoService caseMainInfoService;
    private final PushDataForPos infoSender;
    /**
     * 通过申请编号获取二次反欺诈信息
     *
     * @param condition
     * @return
     */
    @Override
    public List<LoanSecondaryFraudInfo> getSecondaryFraudInfoList(LoanApproveCondition condition) {
        log.info("*****************二次反欺诈信息【" + isNull(condition.getApplyNo()) + "】**************");
        List<LoanSecondaryFraudInfo> info = loanSecondaryFraudInfoService.list(Wrappers.<LoanSecondaryFraudInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), LoanSecondaryFraudInfo::getApplyNo, condition.getApplyNo()));
        return info;
    }

    /**
     * 通过合同号获取贷中特殊业务申请
     *
     * @param condition
     * @return
     */
    @Override
    public List<LoanSpecialBusinessInfo> getSpecialBusinessInfoList(LoanApproveCondition condition) {
        log.info("*****************贷中特殊业务申请【" + isNull(condition.getApplyNo()) + "】**************");
        List<LoanSpecialBusinessInfo> info = loanSpecialBusinessInfoService.list(Wrappers.<LoanSpecialBusinessInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), LoanSpecialBusinessInfo::getApplyNo, condition.getApplyNo()));
        return info;
    }

    /**
     * 通过申请编号查询退件记录
     *
     * @param condition
     * @return
     */
    @Override
    public List<CaseBackToPartnersInfo> getBackToPartnersInfoList(LoanApproveCondition condition) {
        log.info("****************查询退件记录【" + isNull(condition.getContractNo()) + "】**************");
        if(StrUtil.isBlank(condition.getContractNo())){
            return new ArrayList<>();
        }else{
            return caseBackToPartnersInfoService.list(Wrappers.<CaseBackToPartnersInfo>query().lambda()
                    .eq(StringUtils.isNotEmpty(condition.getContractNo()), CaseBackToPartnersInfo::getContractNo, condition.getContractNo())
                    .isNotNull(CaseBackToPartnersInfo::getBackTime)
                    .orderByAsc(CaseBackToPartnersInfo::getCreateTime));
        }
    }

    @Override
    public List<CaseCustHistoyVO> getCaseCustHistory(LoanApproveCondition condition) {
        log.info("****************查询客户身份证信息变更记录【" + isNull(condition.getApplyNo()) + "】**************");
        List<CaseCustHistoyVO> caseCustHistoyVOList = new ArrayList<>();
        List<CaseCustHistory> caseCustHistoryList = caseCustHistoryService.list(Wrappers.<CaseCustHistory>query().lambda()
                .eq(CaseCustHistory::getApplyNo,condition.getApplyNo()));
        if(!CollectionUtils.isEmpty(caseCustHistoryList)){
            caseCustHistoryList.forEach(history -> {
                CaseCustHistoyVO caseCustHistoyVO=new CaseCustHistoyVO();

                if(WhetherEnum.YES.getCode().equals(history.getIsLongTerm())){
                    caseCustHistoyVO.setCertEndDate("长期");
                }else {
                    caseCustHistoyVO.setCertEndDate(history.getCertEndDate());
                }
                if(WhetherEnum.YES.getCode().equals(history.getNewIsLongTerm())){
                    caseCustHistoyVO.setNewCertEndDate("长期");
                }else {
                    caseCustHistoyVO.setNewCertEndDate(history.getNewCertEndDate());
                }
                if(!caseCustHistoyVO.getCertEndDate().equals(caseCustHistoyVO.getNewCertEndDate())) {
                    caseCustHistoyVO.setApplyNo(history.getApplyNo());
                    caseCustHistoyVO.setCertNo(history.getCertNo());
                    caseCustHistoyVO.setCertType(history.getCertType());
                    caseCustHistoyVO.setCustId(history.getCustId());
                    caseCustHistoyVO.setCustName(history.getCustName());
                    caseCustHistoyVO.setCustRole(history.getCustRole());
                    caseCustHistoyVOList.add(caseCustHistoyVO);
                }
            });
        }

        return caseCustHistoyVOList;
    }
    /**
     * 通过申请编号查询基本信息
     *
     * @param condition
     * @return
     */
    @Override
    public CaseBaseInfo getCaseBaseInfoList(LoanApproveCondition condition) {
        log.info("*****************查询案件基本信息【" + isNull(condition.getApplyNo()) + "】**************");
        CaseBaseInfo info = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseBaseInfo::getApplyNo, condition.getApplyNo()), false);
        return info;
    }

    /**
     * 通过申请编号查询合同信息
     *
     * @param condition
     * @return
     */
    @Override
    public CaseContractInfo getCaseContractInfoList(LoanApproveCondition condition) {
        log.info("*****************查询合同基本信息【" + isNull(condition.getApplyNo()) + "】**************");
        CaseContractInfo info = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseContractInfo::getApplyNo, condition.getApplyNo()), false);
        return Objects.nonNull(info) ? info : new CaseContractInfo();
    }

    /**
     * 通过申请编号查询渠道基础信息
     *
     * @param condition
     * @return
     */
    @Override
    public ChannelBaseInfo getChannelBaseInfoList(LoanApproveCondition condition) {
        CaseChannelInfo caseChannelInfo = getCaseChannelInfo(isNull(condition.getApplyNo()));
        if (null != caseChannelInfo) {
            log.info("*****************查询渠道基本信息【" + isNull(condition.getApplyNo()) + "】*****************");
            ChannelBaseInfo info = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                    .eq(StringUtils.isNotEmpty(caseChannelInfo.getDealerNo()), ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()), false);
            if(AfsEnumUtil.key(ChannelBelongEnum.DIRECT).equals(info.getChannelBelong())&&AfsEnumUtil.key(PaymentObjectEnum.car_dealer).equals(info.getPaymentObject())){
                if(StrUtil.isNotBlank(caseChannelInfo.getCarDealersId())){
                    info=channelBaseInfoService.getById(Long.valueOf(caseChannelInfo.getCarDealersId()));
                }
            }
            if(StringUtils.isBlank(info.getChannelBelong())){
                info.setChannelBelong(AfsEnumUtil.key(ChannelBelongEnum.DIRECT));
            }
            return info;
        }
        return null;
    }


    /**
     * 通过申请编号查询渠道见证人信息
     * 放款审核页面使用合同表中绑定的见证人信息
     *
     * @param condition
     * @return
     */
    @Override
    public ChannelWitnessInfo getChannelWitnessInfoList(LoanApproveCondition condition) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseContractInfo::getContractNo, condition.getContractNo()), false);
        if (null != caseContractInfo&&StringUtils.isNotBlank(caseContractInfo.getWitnessId())) {
            ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                    .eq(StringUtils.isNotEmpty(caseContractInfo.getDealerNo()),ChannelBaseInfo::getChannelCode,caseContractInfo.getDealerNo()),false);
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo,caseContractInfo.getApplyNo()),false);
            if(!ObjectUtils.isEmpty(caseBaseInfo) && StringUtils.isNotBlank(caseBaseInfo.getBusinessType()) && !ObjectUtils.isEmpty(channelBaseInfo)){
                if(caseBaseInfo.getBusinessType().equals(BusinessTypeEnum.NEW_CAR.getCode())){
                    log.info("*****************查询渠道见证人信息【" + isNull(condition.getApplyNo()) + "】*****************");
                    ChannelWitnessInfo info = channelWitnessInfoService.getOne(Wrappers.<ChannelWitnessInfo>query().lambda()
                            .eq(ChannelWitnessInfo::getBusinessAttributes,"0")
                            .eq(ChannelWitnessInfo::getChannelId,channelBaseInfo.getId().toString())
                            .eq(ChannelWitnessInfo::getWitnessIdCard, caseContractInfo.getWitnessId()),false);
                    return info;
                } else {
                    log.info("*****************查询渠道见证人信息【" + isNull(condition.getApplyNo()) + "】*****************");
                    ChannelWitnessInfo info = channelWitnessInfoService.getOne(Wrappers.<ChannelWitnessInfo>query().lambda()
                            .eq(ChannelWitnessInfo::getBusinessAttributes,"1")
                            .eq(ChannelWitnessInfo::getChannelId,channelBaseInfo.getId().toString())
                            .eq(ChannelWitnessInfo::getWitnessIdCard, caseContractInfo.getWitnessId()),false);
                    return info;
                }

            }
        }
        return new ChannelWitnessInfo();
    }

    /**
     * 获取渠道评分信息
     *
     * @param condition
     * @return
     */
    @Override
    public ChannelRiskInfo getChannelRiskInfo(LoanApproveCondition condition) {
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseBaseInfo::getApplyNo, condition.getApplyNo()), false);
        if (null != caseBaseInfo) {
            CaseChannelInfo caseChannelInfo=caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>lambdaQuery().eq(CaseChannelInfo::getApplyNo,caseBaseInfo.getApplyNo()));
            log.info("*****************查询渠道评分信息【" + isNull(condition.getApplyNo()) + "】*****************");
            ChannelBaseInfo channelBaseInfo=channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>lambdaQuery().eq(ChannelBaseInfo::getChannelCode,caseChannelInfo.getDealerNo()));
            if(null != channelBaseInfo){
                ChannelRiskInfo channelRiskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                        .eq(ChannelRiskInfo::getChannelId,channelBaseInfo.getChannelId())
                        .eq(ChannelRiskInfo::getBusinessType,caseBaseInfo.getBusinessType()));
                return channelRiskInfo;
            }
        }
        return null;
    }

    /**
     * 通过申请编号查询客户信息
     *
     * @param condition
     * @return
     */
    @Override
    public List<CaseCustInfo> getCaseCustInfoList(LoanApproveCondition condition) {
        log.info("*****************查询客户主借人信息【" + isNull(condition.getApplyNo()) + "】*****************");
        List<CaseCustInfo> infoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseCustInfo::getApplyNo, condition.getApplyNo()));
        log.info("*****************end:查询客户主借人信息【" + isNull(condition.getApplyNo()) + "】*****************");
        /**查找变更值*/
        infoList.forEach(info->{
            List<CaseMqCompareInfo> caseMqCompareInfos= caseMqCompareInfoService.list(Wrappers.<CaseMqCompareInfo>lambdaQuery()
                    .eq(CaseMqCompareInfo::getDataId,info.getId().toString())
                    .orderByDesc(CaseMqCompareInfo::getCreateTime));
            if(!CollectionUtils.isEmpty(caseMqCompareInfos)){
                Map map = JSON.parseObject(caseMqCompareInfos.get(0).getDifferentData(), HashMap.class);
                info.setChangeMap(map);
            }
        });
        return infoList;
    }

    /**
     * 通过申请编号查询车辆信息
     *
     * @param condition
     * @return
     */
    @Override
    public CaseCarInfo getCaseCarInfoList(LoanApproveCondition condition) {
        log.info("*****************查询车辆信息【" + isNull(condition.getContractNo()) + "】*****************");
        CaseCarInfo caseCarInfo = new CaseCarInfo();
        if(StrUtil.isBlank(condition.getContractNo()) && StrUtil.isNotBlank(condition.getApplyNo())){
            caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                    .eq(CaseCarInfo::getApplyNo, condition.getApplyNo()), false);
        }else{
            caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                    .eq(CaseCarInfo::getContractNo, condition.getContractNo()), false);
        }
        List<CaseMqCompareInfo> caseMqCompareInfoList = caseMqCompareInfoService.list(Wrappers.<CaseMqCompareInfo>query().lambda()
                .eq(CaseMqCompareInfo::getContractNo,condition.getContractNo())
                .ne(CaseMqCompareInfo::getDataId,"null"));
        if(!CollectionUtils.isEmpty(caseMqCompareInfoList)){
            Map resultMap = new HashMap();
            caseMqCompareInfoList.forEach(caseMqCompareInfo -> {
                Map paramsMap = JSON.parseObject(caseMqCompareInfo.getDifferentData(), HashMap.class);
                Iterator it = paramsMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry entry = (Map.Entry) it.next();
                    Object key = entry.getKey();
                    resultMap.put(key, paramsMap.get(key) != null ? paramsMap.get(key) : "");
                }
            });
            caseCarInfo.setChangeMap(resultMap);
        }
        return caseCarInfo;
    }

    /**
     * 通过申请编号查询车型信息
     *
     * @param condition
     * @return
     */
    @Override
    public VehicleModelNewVO getVehicleModelList(LoanApproveCondition condition){
        log.info("*****************查询车型信息【" + isNull(condition.getApplyNo()) + "】*****************");
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, condition.getApplyNo()), false);

        VehicleModelNewVO vo = new VehicleModelNewVO();
        if(caseCarInfo != null){
            List<VehicleModelNewVO> vehicleModelNewVOList = vehicleInfoService.queryModelInfo(caseCarInfo.getModelId());

            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(vehicleModelNewVOList)){
                vo.setLocalTrxPrice(vehicleModelNewVOList.get(0).getLocalTrxPrice());
                vo.setVehicleLevel(vehicleModelNewVOList.get(0).getVehicleLevel());
                vo.setIsUnpopular(vehicleModelNewVOList.get(0).getIsUnpopular());
            }
        }

        return vo;
    }

    /**
     * 通过申请编号查询渠道挂靠信息
     *
     * @param condition
     * @return
     */
    @Override
    public ChannelAffiliatedUnits getChannelAffiliatedUnitsList(LoanApproveCondition condition) {
        CaseCarInfo caseCarInfo = getCaseCarInfoList(condition);
        if (null != caseCarInfo) {
            ChannelAffiliatedUnits info = new ChannelAffiliatedUnits();
            if (StringUtils.isNotEmpty(caseCarInfo.getAffCompanyId())) {
                log.info("*****************查询渠道挂靠信息【" + isNull(condition.getApplyNo()) + "】*****************");
                info = channelAffiliatedUnitsService.getAffiliatedCompany(caseCarInfo.getAffCompanyId());
            } else {
                log.info("*****************查询个体工商挂靠信息【" + isNull(condition.getApplyNo()) + "】*****************");
                info.setAffiliatedName(caseCarInfo.getIndBusinessName());
                info.setSocUniCrtCode(caseCarInfo.getIndBusinessUsci());
            }
            return info;
        }
        return null;
    }

    @Override
    public CaseManageAssetChangeVo getCaseManageAssetChange(LoanApproveCondition condition) {
        CaseManageAssetChangeVo caseManageAssetChangeVo = new CaseManageAssetChangeVo();
        if(StringUtils.isNotBlank(condition.getFlawNo())){
            CaseManageAssetChange caseManageAssetChange = caseManageAssetChangeService.getOne(Wrappers.<CaseManageAssetChange>query().lambda()
                    .eq(CaseManageAssetChange::getFlawNo,condition.getFlawNo()),false);
            if(!ObjectUtils.isEmpty(caseManageAssetChange)){
                BeanUtils.copyProperties(caseManageAssetChange, caseManageAssetChangeVo);
                List<LoanFlawFixDetailVo> loanFlawFixDetailList = loanFlawFixDetailService.getLoanFlawFixDetailList(condition);
                caseManageAssetChangeVo.setLoanFlawFixDetailVoList(loanFlawFixDetailList);
            }
        }
        return caseManageAssetChangeVo;
    }

    /**
     * 通过申请编号查询保险信息
     *
     * @param condition
     * @return
     */
    @Override
    public CarInsuranceInfoVO getCarInsuranceInfoList(LoanApproveCondition condition) {
        log.info("*****************查询纸质保险信息【" + isNull(condition.getApplyNo()) + "】*****************");
        List<CarInsuranceInfo> insuranceInfoList = carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CarInsuranceInfo::getApplyNo, condition.getApplyNo())
                .eq(CarInsuranceInfo::getInsuranceType, InsuranceTypeEnums.BUSINESS.getCode()));
        CarInsuranceInfoVO insuranceInfoVO = new CarInsuranceInfoVO();
        for (CarInsuranceInfo info : insuranceInfoList) {
            insuranceInfoVO.setInsuranceMode(info.getInsuranceMode());
            insuranceInfoVO.setInsuranceStartTime(info.getInsuranceStartTime());
            insuranceInfoVO.setInsuranceEndTime(info.getInsuranceEndTime());
        }
        return insuranceInfoVO;
    }

    /**
     * @param
     * @Description 通过申请编号查询电子保费信息
     * <AUTHOR>
     * @Date 2020/08/21
     */
    @Override
    public List<CarInsuranceInfoVO> getElectronicsInsuranceInfo(LoanApproveCondition condition) {
        log.info("*****************查询电子保单保险信息【" + isNull(condition.getApplyNo()) + "】*****************");
        List<CarInsuranceInfoVO> infoList = new ArrayList<>();
        List<CarInsuranceInfo> insuranceInfoList = carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CarInsuranceInfo::getApplyNo, condition.getApplyNo()));
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CaseCustInfo::getApplyNo, condition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()), false);
        FinFinancingItems itemsInfo = caseFinancingItemsService.getOne(Wrappers.<FinFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), FinFinancingItems::getApplyNo, condition.getApplyNo())
                .eq(FinFinancingItems::getFinanceItemCode, "F002"));
        CaseCarInfo carInfo = getCaseCarInfoList(condition);
        CaseContractInfo contractInfo = getCaseContractInfoList(condition);
        CarInsuranceInfoVO insuranceInfoVO = new CarInsuranceInfoVO();
        if(!CollectionUtils.isEmpty(insuranceInfoList)){
            for (CarInsuranceInfo info : insuranceInfoList) {
                insuranceInfoVO.setInsuranceCompany(info.getInsuranceCompany());
                insuranceInfoVO.setInsuranceMode(info.getInsuranceMode());
                //车架号是否一致
                insuranceInfoVO.setCarVin(info.getCarVin()==null?"":info.getCarVin().equals(carInfo.getCarVin()) ? "一致" : "不一致");
                if (ObjectUtil.isNotNull(info.getPurpose()) && !"".equals(info.getPurpose())) {
                    insuranceInfoVO.setPurpose(AfsEnumUtil.desc(AfsEnumUtil.getEnum(info.getPurpose(), CarFunctionEnum.class)));
                }
                insuranceInfoVO.setInsuranceStartTime(info.getInsuranceStartTime());
                insuranceInfoVO.setInsuranceEndTime(info.getInsuranceEndTime());
                //计算保险是否有效
                if (null != info.getInsuranceEndTime() && null != contractInfo.getLendingFirstDate()) {
                    Date lendingFirstDateAddNine = DateUtil.offsetMonth(contractInfo.getLendingFirstDate(), 9);
                    insuranceInfoVO.setIsExpirationDate(between(info.getInsuranceEndTime(), lendingFirstDateAddNine) ? "是" : "否");
                }
                if (InsuranceTypeEnums.BUSINESS.getCode().equals(info.getInsuranceType())) {
                    //商业险被保险人是否一致
                    insuranceInfoVO.setBusinessName(info.getInsuranceCustName()==null?"":info.getInsuranceCustName().equals(custInfo.getCustName()) ? "一致" : "不一致");
                    insuranceInfoVO.setBusinessAmt(info.getInsuranceAmt());
                } else if (InsuranceTypeEnums.COMPULSORY.getCode().equals(info.getInsuranceType())) {
                    //交强险险人是否一致
                    insuranceInfoVO.setCompulsoryName(info.getInsuranceCustName()==null?"":info.getInsuranceCustName().equals(custInfo.getCustName()) ? "一致" : "不一致");
                    insuranceInfoVO.setCompulsoryCustNo(info.getInsuranceCustNo()==null?"":info.getInsuranceCustNo().equals(custInfo.getCertNo()) ? "一致" : "不一致");
                    insuranceInfoVO.setCompulsoryAmt(info.getInsuranceAmt());
                } else if (InsuranceTypeEnums.VEHICLE_LOSS.getCode().equals(info.getInsuranceType())) {
                    insuranceInfoVO.setVehicleLossAmt(info.getInsuranceAmt());
                } else if (InsuranceTypeEnums.THEFT.getCode().equals(info.getInsuranceType())) {
                    insuranceInfoVO.setTheftAmt(info.getInsuranceAmt());
                } else if (InsuranceTypeEnums.THIRD_PARTY.getCode().equals(info.getInsuranceType())) {
                    insuranceInfoVO.setThirdPartyAmt(info.getInsuranceAmt());
                } else if (InsuranceTypeEnums.VEHICLE_VESSEL_TAX.getCode().equals(info.getInsuranceType())) {
                    insuranceInfoVO.setVehicleVesselTaxAmt(info.getInsuranceAmt());
                }
            }
            //交强险合计
            insuranceInfoVO.setCompulsoryTotal((null != insuranceInfoVO.getCompulsoryAmt() ? insuranceInfoVO.getCompulsoryAmt() : BigDecimal.ZERO)
                    .add(null != insuranceInfoVO.getVehicleVesselTaxAmt() ? insuranceInfoVO.getVehicleVesselTaxAmt() : BigDecimal.ZERO));
            //总合计费用
            insuranceInfoVO.setTotal((null != insuranceInfoVO.getCompulsoryTotal() ? insuranceInfoVO.getCompulsoryTotal() : BigDecimal.ZERO)
                    .add(null != insuranceInfoVO.getBusinessAmt() ? insuranceInfoVO.getBusinessAmt() : BigDecimal.ZERO));
            if(!ObjectUtils.isEmpty(itemsInfo)){
                insuranceInfoVO.setPremiumIsMeet(insuranceInfoVO.getTotal()
                        .compareTo(null != itemsInfo.getFinanceItemAmt() ? itemsInfo.getFinanceItemAmt() : BigDecimal.ZERO) == -1 ? "不满足" : "满足");
            }
            infoList.add(insuranceInfoVO);
        }


        return infoList;
    }

    /**
     * @param startDate
     * @param endDate
     * @Description 判断startDate是否小于endDate
     * <AUTHOR>
     * @Date 2020/08/21
     */
    public Boolean between(Date startDate, Date endDate) {
        Long day = DateUtil.between(startDate,endDate,DAY,false);
        return day>0;
    }


    /**
     * 通过申请编号查询费用信息
     *
     * @param condition
     * @return
     */
    @Override
    public List<FinCostDetails> getCaseCostInfoList(LoanApproveCondition condition) {
        log.info("*****************查询费用信息【" + isNull(condition.getApplyNo()) + "】*****************");
        List<FinCostDetails> info = caseCostInfoService.list(Wrappers.<FinCostDetails>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), FinCostDetails::getApplyNo, condition.getApplyNo()));
        return info;
    }

    /**
     * 通过申请编号查询总费用信息
     *
     * @param condition
     * @return
     */
    @Override
    public CaseCostInfoVO getCostTotalInfo(LoanApproveCondition condition) {
        log.info("*****************查询费用总信息【" + isNull(condition.getApplyNo()) + "】*****************");
        return caseCostInfoService.costTotal(condition.getApplyNo());
    }

    /**
     * 通过合同号查询发票信息
     *
     * @param condition
     * @return
     */
    @Override
    public CarInvoiceInfo getCarInvoiceInfo(LoanApproveCondition condition) {
        log.info("*****************发票信息【" + isNull(condition.getContractNo()) + "】*****************");
        if(StrUtil.isNotBlank(condition.getApplyNo()) && StrUtil.isBlank(condition.getContractNo())){
            return carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(StringUtils.isNotEmpty(condition.getApplyNo()), CarInvoiceInfo::getContractNo, condition.getApplyNo()));
        }else{
            return carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(StringUtils.isNotEmpty(condition.getContractNo()), CarInvoiceInfo::getContractNo, condition.getContractNo()));
        }
    }

    /**
     * 通过申请编号查询融资项目信息
     * 表结构搞成这样我也是服了 表设计是真的******#¥%&%¥#¥%&
     * @param condition
     * @return
     */
    @Override
    public List<FinFinancingItemsVO> getCaseFinancingItemsList(LoanApproveCondition condition) {
        log.info("*****************通过申请编号查询融资项目信息【" + isNull(condition.getApplyNo()) + "】*****************");
        List<String> itemCodes = new ArrayList<>();
        itemCodes.add("F001");//购置税

        //融资金额  排除-购置税-
        List<FinFinancingItems> itemsList = caseFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), FinFinancingItems::getApplyNo, condition.getApplyNo())
                .notIn(FinFinancingItems::getFinanceItemCode,itemCodes));

        //实际成交价格
        List<AddedFinancingItems>  addItemsList = addedFinancingItemsService.list(Wrappers.<AddedFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), AddedFinancingItems::getApplyNo, condition.getApplyNo()));
        //取出父节点所有数据
        List<FinFinancingItems> isParentList = itemsList.stream().filter(item->null==item.getUpperId()).collect(Collectors.toList());
        List<AddedFinancingItems> addIsParentList = addItemsList.stream().filter(item->null==item.getUpperId()).collect(Collectors.toList());

        //取出子节点所有数据
        List<FinFinancingItems> isSonList = itemsList.stream().filter(item->null!=item.getUpperId()).collect(Collectors.toList());
        List<AddedFinancingItems> addIsSonList = addItemsList.stream().filter(item->null!=item.getUpperId()).collect(Collectors.toList());

        //组装附加项目'融资金额'
        List<FinFinancingItemsVO> itemsVOS = new ArrayList<>();
        for(FinFinancingItems items:isParentList){
            FinFinancingItemsVO vo = new FinFinancingItemsVO();
            vo.setFinanceItemName(items.getFinanceItemName());
            vo.setFinanceItemAmt(items.getFinanceItemAmt());

            //添加实际成交价格
            for(AddedFinancingItems addItem : addIsParentList){
                if(items.getId().equals(addItem.getUpperId())){
                    vo.setRealFinanceItemAmt(addItem.getAddFinanceAmt());
                }
            }
            if(ObjectUtil.isNull(vo.getRealFinanceItemAmt())){
                vo.setRealFinanceItemAmt(vo.getFinanceItemAmt());
            }
            //组装增融项
            List<FinFinancingItemsVO> children = new ArrayList<>();
            for(FinFinancingItems sonItems:isSonList){
                //组装当前父节点项目包含的子项目
                if(items.getId().equals(sonItems.getUpperId())){
                    FinFinancingItemsVO sonVo = new FinFinancingItemsVO();
                    sonVo.setFinanceItemName(sonItems.getFinanceItemName());
                    sonVo.setFinanceItemAmt(sonItems.getFinanceItemAmt());

                    //添加实际成交价格
                    for(AddedFinancingItems addItem : addIsSonList){
                        if(items.getId().equals(addItem.getUpperId())){
                            sonVo.setRealFinanceItemAmt(addItem.getAddFinanceAmt());
                        }
                    }
                    if(ObjectUtil.isNull(vo.getRealFinanceItemAmt())){
                        vo.setRealFinanceItemAmt(vo.getFinanceItemAmt());
                    }
                    children.add(sonVo);
                }
            }
            vo.setChildren(children);
            itemsVOS.add(vo);
        }

        return itemsVOS;
    }

    /**
     * 获取首单抵押详情
     * <AUTHOR>
     * @param
     * @return true 不是首单，false是首单
     */
    @Override
    public Boolean getIsFirstMortgage(LoanApproveCondition condition){

        List<CaseCarInfo> caseCarInfoList = caseCarInfoService.list(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, condition.getApplyNo()));
        String businessType = "";
        if(StringUtils.isNotBlank(condition.getContractNo())){
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getContractNo,condition.getContractNo()));
            if(!ObjectUtils.isEmpty(caseContractInfo) && StringUtils.isNotBlank(caseContractInfo.getBusinessType())){
                businessType = caseContractInfo.getBusinessType();
            } else {
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo,condition.getApplyNo()));
                if(!ObjectUtils.isEmpty(caseBaseInfo) && StringUtils.isNotBlank(caseBaseInfo.getBusinessType())){
                    businessType = caseBaseInfo.getBusinessType();
                }
            }
        } else {
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo,condition.getApplyNo()));
            if(!ObjectUtils.isEmpty(caseBaseInfo) && StringUtils.isNotBlank(caseBaseInfo.getBusinessType())){
                businessType = caseBaseInfo.getBusinessType();
            }
        }
        Boolean result = true;
        if (!CollectionUtils.isEmpty(caseCarInfoList)) {
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, caseCarInfoList.get(0).getApplyNo()));
            if(!ObjectUtils.isEmpty(caseChannelInfo) && StringUtils.isNotBlank(caseChannelInfo.getDealerNo())){
                ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                        .eq(ChannelBaseInfo::getChannelCode,caseChannelInfo.getDealerNo()));
                if(!ObjectUtils.isEmpty(channelBaseInfo)){
                    log.info("合同{}:首单抵押规则执行开始...",condition.getApplyNo());
                    List<LoanModeRuleInfo> afsRuleInfoList = loanModeRuleInfoService.list(Wrappers.<LoanModeRuleInfo>query().lambda()
                            .eq(LoanModeRuleInfo::getIsEnable,WhetherEnum.YES.getCode()));
                    if(!CollectionUtils.isEmpty(afsRuleInfoList)){
                        List<String> list=afsRuleInfoList.stream().map(loanModeRuleInfo->loanModeRuleInfo.getId().toString()).collect(Collectors.toList());
                        JSONObject runParam=ruleAtomDataService.getCommonAtomRunParams(condition.getApplyNo());
                        RuleRunResult ruleResult = RuleHelper.runRule(runParam,list,false,RuleRunEnum.SERIAL );
                        if (ruleResult.getHit()) {
                            List<String> lendingModeList = loanModeRuleInfoService.list(Wrappers.<LoanModeRuleInfo>query().lambda()
                                    .in(LoanModeRuleInfo::getId,list)).stream().map(s->s.getLendingMode()).collect(Collectors.toList());
                            int j=0;
                            for(int i=0;i<lendingModeList.size();i++){
                                if(lendingModeList.get(i).equals(LendingModeEnum.CHANNEL.getCode())){
                                    List<LoanDealerFirstMortgage> loanDealerFirstMortgage = loanDealerFirstMortgageService.list(Wrappers.<LoanDealerFirstMortgage>query().lambda()
                                            .eq(LoanDealerFirstMortgage::getDealerNo, channelBaseInfo.getId().toString())
                                            .eq(StringUtils.isNoneBlank(businessType),LoanDealerFirstMortgage::getBusinessType,businessType)
                                            .eq(LoanDealerFirstMortgage::getIsFirstMortgage, WhetherEnum.YES.getCode()));
                                    if (CollectionUtils.isEmpty(loanDealerFirstMortgage)) {
                                        j++;
                                    }
                                }
                                if(lendingModeList.get(i).equals(LendingModeEnum.CHANNEL_AND_PROVINCE.getCode())){
                                    List<LoanDealerFirstMortgage> loanDealerFirstMortgage = loanDealerFirstMortgageService.list(Wrappers.<LoanDealerFirstMortgage>query().lambda()
                                            .eq(LoanDealerFirstMortgage::getDealerNo, channelBaseInfo.getId().toString())
                                            .eq(StringUtils.isNoneBlank(businessType),LoanDealerFirstMortgage::getBusinessType,businessType)
                                            .eq(LoanDealerFirstMortgage::getIsFirstMortgage, WhetherEnum.YES.getCode())
                                            .in(LoanDealerFirstMortgage::getProvinceCode, caseCarInfoList.stream().map(s->s.getPurchaseProvince()).collect(Collectors.toList())));
                                    if (CollectionUtils.isEmpty(loanDealerFirstMortgage)) {
                                        j++;
                                    }
                                }
                                if(lendingModeList.get(i).equals(LendingModeEnum.CHANNEL_PROVINCE_CITY.getCode())){
                                    List<LoanDealerFirstMortgage> loanDealerFirstMortgage = loanDealerFirstMortgageService.list(Wrappers.<LoanDealerFirstMortgage>query().lambda()
                                            .eq(LoanDealerFirstMortgage::getDealerNo, channelBaseInfo.getId().toString())
                                            .eq(StringUtils.isNoneBlank(businessType),LoanDealerFirstMortgage::getBusinessType,businessType)
                                            .eq(LoanDealerFirstMortgage::getIsFirstMortgage, WhetherEnum.YES.getCode())
                                            .in(LoanDealerFirstMortgage::getProvinceCode, caseCarInfoList.stream().map(s->s.getPurchaseProvince()).collect(Collectors.toList()))
                                            .in(LoanDealerFirstMortgage::getCityCode, caseCarInfoList.stream().map(s->s.getPurchaseCity()).collect(Collectors.toList())));
                                    if (CollectionUtils.isEmpty(loanDealerFirstMortgage)) {
                                        j++;
                                    }
                                }
                            }
                            if(j>0){
                                result = false;
                            }
                        }
                        log.info("合同{}:首单抵押规则执行结束...",condition.getApplyNo());
                    } else {
                        log.info("合同{}:首单抵押规则执行结束...",condition.getApplyNo());
                    }
                } else {
                    log.info("合同{}:首单抵押规则执行结束...",condition.getApplyNo());
                }
            }
        }
        return result;
    }

    /**
     * 通过申请编号查询融资项目信息
     * 表结构搞成这样我也是服了 表设计是真的******#¥%&%¥#¥%&
     * @param condition
     * @return
     */
    @Override
    public List<FinFinancingItemsVO> getCaseAddFinancingItemsList(LoanApproveCondition condition) {
        log.info("*****************通过申请编号查询融资项目信息【" + isNull(condition.getApplyNo()) + "】*****************");

        List<FinFinancingItemsVO> financingItemsList=new ArrayList<>();
        //实际成交价格
        List<AddedFinancingItems>  addItemsList = addedFinancingItemsService.list(Wrappers.<AddedFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), AddedFinancingItems::getApplyNo, condition.getApplyNo())
                .eq(AddedFinancingItems::getDelFlag,"0"));
        addItemsList.stream().collect(Collectors.toMap(AddedFinancingItems::getId, f -> f));


        if(!CollectionUtils.isEmpty(addItemsList)){
            addItemsList.forEach(addedFinancingItems -> {
                FinFinancingItemsVO finFinancingItemsVO = new FinFinancingItemsVO();
                FinFinancingItems finFinancingItems = caseFinancingItemsService.getOne(Wrappers.<FinFinancingItems>lambdaQuery()
                        .eq(FinFinancingItems::getApplyNo, addedFinancingItems.getApplyNo())
                        .eq(FinFinancingItems::getFinanceItemCode,addedFinancingItems.getFinanceItemCode()));
                BeanUtils.copyProperties(finFinancingItems, finFinancingItemsVO);
                finFinancingItemsVO.setRealFinanceItemAmt(addedFinancingItems.getAddFinanceAmt());
                finFinancingItemsVO.checkStatus();
                financingItemsList.add(finFinancingItemsVO);
            });

            Map<Long, FinFinancingItemsVO> voMap = financingItemsList.stream().collect(Collectors.toMap(FinFinancingItemsVO::getId, f -> f));
            List<FinFinancingItemsVO> childrenList = financingItemsList.stream().filter(item -> null != item.getUpperId()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(childrenList)) {
                Map<Long, List<FinFinancingItemsVO>> childrenMap = childrenList.stream().collect(Collectors.groupingBy(FinFinancingItemsVO::getUpperId));
                if (voMap.size() > 0) {
                    childrenMap.forEach((key, value) -> {
                        FinFinancingItemsVO financingItemsVO = voMap.get(key);
                        for(FinFinancingItemsVO vo:value){
                            if(!vo.isQualified()){
                                financingItemsVO.setQualified(false);
                            }
                        }
                        financingItemsVO.setChildren(value);
                        financingItemsList.removeAll(value);
                    });
                }
            }
        }
        return financingItemsList;
    }

    /**
     * 通过申请编号查询融资项目信息
     * 表结构搞成这样我也是服了 表设计是真的******#¥%&%¥#¥%& :)同意
     * @param condition
     * @return
     */
    @Override
    public List<AddedFinancingItemsVO> getAddFinancingItemsList(LoanApproveCondition condition) {
        log.info("*****************通过申请编号查询融资项目信息【" + isNull(condition.getApplyNo()) + "】*****************");

        List<AddedFinancingItemsVO> financingItemsList=new ArrayList<>();
        /**查询未显示的融资项目*/
        List<FinFinancingItems>  finFinancingItemsList = caseFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), FinFinancingItems::getApplyNo, condition.getApplyNo())
                .isNull(FinFinancingItems::getIsView).ne(FinFinancingItems::getFinanceItemAmt,BigDecimal.ZERO));
        if(!CollectionUtils.isEmpty(finFinancingItemsList)){
            finFinancingItemsList.forEach(finFinancingItems -> {
                AddedFinancingItemsVO addedItemsVO=new AddedFinancingItemsVO();
                addedItemsVO.setId(finFinancingItems.getId());
                addedItemsVO.setApplyFinanceItemAmt(finFinancingItems.getFinanceItemAmt());
                addedItemsVO.setFinanceItemName(finFinancingItems.getFinanceItemName());
                addedItemsVO.setFinanceItemCode(finFinancingItems.getFinanceItemCode());
                addedItemsVO.setQualified(false);
                financingItemsList.add(addedItemsVO);
            });
        }
        //实际成交价格
        List<AddedFinancingItems>  addItemsList = addedFinancingItemsService.list(Wrappers.<AddedFinancingItems>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), AddedFinancingItems::getApplyNo, condition.getApplyNo())
                .eq(AddedFinancingItems::getDelFlag,"0"));
        addItemsList.forEach(addedFinancingItems -> {
            AddedFinancingItemsVO addedFinancingItemsVO=new AddedFinancingItemsVO();
            BeanUtils.copyProperties(addedFinancingItems, addedFinancingItemsVO);
            FinFinancingItems finFinancingItems = caseFinancingItemsService.getOne(Wrappers.<FinFinancingItems>lambdaQuery()
                    .eq(FinFinancingItems::getApplyNo, addedFinancingItems.getApplyNo())
                    .eq(FinFinancingItems::getFinanceItemCode,addedFinancingItems.getFinanceItemCode()));
            if(!ObjectUtils.isEmpty(finFinancingItems)){
                addedFinancingItemsVO.setApplyFinanceItemAmt(finFinancingItems.getFinanceItemAmt());
                addedFinancingItemsVO.checkStatus();
            }
            financingItemsList.add(addedFinancingItemsVO);
        });
        Map<Long, AddedFinancingItemsVO> voMap = financingItemsList.stream().collect(Collectors.toMap(AddedFinancingItemsVO::getId, f -> f));
        List<AddedFinancingItemsVO> childrenList = financingItemsList.stream().filter(item -> null != item.getUpperId()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(childrenList)) {
            Map<Long, List<AddedFinancingItemsVO>> childrenMap = childrenList.stream().collect(Collectors.groupingBy(AddedFinancingItemsVO::getUpperId));
            if (voMap.size() > 0) {
                childrenMap.forEach((key, value) -> {
                    AddedFinancingItemsVO financingItemsVO = voMap.get(key);
                    for(AddedFinancingItemsVO vo:value){
                        if(!vo.isQualified()){
                            financingItemsVO.setQualified(false);
                        }
                    }
                    financingItemsVO.setChildren(value);
                    value.forEach(v->{
                        financingItemsList.removeIf(addedFinancingItemsVO -> addedFinancingItemsVO.getId().equals(v.getId()));
                    });
                });
            }
        }
        return financingItemsList;
    }
    /**
     * 通过申请编号查询客户银行信息
     *
     * @param condition
     * @return
     */
    @Override
    public LoanBankCardInfo getLoanBankCardInfoList(LoanApproveCondition condition) {
        log.info("*****************客户银行信息【" + isNull(condition.getApplyNo()) + "】*****************");
        LoanBankCardInfo info = loanBankCardInfoService.getOne(Wrappers.<LoanBankCardInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getApplyNo()), LoanBankCardInfo::getApplyNo, condition.getApplyNo()), false);
        if(Objects.nonNull(info)) {
            List<CaseMqCompareInfo> caseMqCompareInfos = caseMqCompareInfoService.list(Wrappers.<CaseMqCompareInfo>lambdaQuery()
                    .eq(CaseMqCompareInfo::getDataId, info.getId().toString())
                    .orderByDesc(CaseMqCompareInfo::getCreateTime));
            if (!CollectionUtils.isEmpty(caseMqCompareInfos)) {
                Map map = JSON.parseObject(caseMqCompareInfos.get(0).getDifferentData(), HashMap.class);
                info.setChangeMap(map);
            }
        }
        return info;
    }

    /**
     * 获取渠道关联申请件信息
     *
     * @param applyNo
     * @return
     */
    private CaseChannelInfo getCaseChannelInfo(String applyNo) {
        return caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(applyNo), CaseChannelInfo::getApplyNo, applyNo));
    }

    /**
     * 判空
     *
     * @param str
     * @return
     */
    private String isNull(String str) {
        return StringUtils.isNotEmpty(str) ? str : null;
    }

    @Override
    public void auditSubmit(LoanApproveSubmitVO condition) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(condition.getContractNo()), CaseContractInfo::getContractNo, condition.getContractNo()));
        SpringContextHolder.getBean(LoanApproveService.class).normalLoanApprove(caseContractInfo, condition);
    }

    @Override
    public void auditAutoSubmit(String contractNo) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(contractNo), CaseContractInfo::getContractNo, contractNo));
        SpringContextHolder.getBean(LoanApproveService.class).normalAutoLoanApprove(caseContractInfo);
    }

    /**
     * @Description 保存首单抵押单号信息
     * <AUTHOR>
     * @Date 2020/09/28
     */
    @Override
    public void saveIsFirstMortgage(String contractNo) {
        try {
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getContractNo, contractNo));
            if (!ObjectUtils.isEmpty(caseContractInfo)) {
                //获取合作商信息
                caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo,caseContractInfo.getApplyNo()));
                CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                        .eq(CaseChannelInfo::getApplyNo, caseContractInfo.getApplyNo()));
                ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                        .eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
                //获取购车城市信息
                if(StringUtils.isNotBlank(channelBaseInfo.getChannelBelong())){
                    CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                            .eq(CaseCarInfo::getApplyNo, caseContractInfo.getApplyNo())
                            .eq(CaseCarInfo::getContractNo, caseContractInfo.getContractNo()));
                    if (!ObjectUtils.isEmpty(caseCarInfo) && !ObjectUtils.isEmpty(channelBaseInfo)) {
                        LoanDealerFirstMortgage loanDealerFirstMortgage = loanDealerFirstMortgageService.getOne(Wrappers.<LoanDealerFirstMortgage>query().lambda()
                                .eq(LoanDealerFirstMortgage::getDealerNo, channelBaseInfo.getId().toString())
                                .eq(LoanDealerFirstMortgage::getBusinessType, caseContractInfo.getBusinessType())
                                .eq(LoanDealerFirstMortgage::getProvinceCode, caseCarInfo.getPurchaseProvince())
                                .eq(LoanDealerFirstMortgage::getCityCode, caseCarInfo.getPurchaseCity()));
                        if (!ObjectUtils.isEmpty(loanDealerFirstMortgage)) {
                            loanDealerFirstMortgage.setIsFirstMortgage(WhetherEnum.YES.getCode());
                            loanDealerFirstMortgage.setFirstCaseNo(caseContractInfo.getContractNo());
                            loanDealerFirstMortgageService.updateById(loanDealerFirstMortgage);
                        } else {
                            LoanDealerFirstMortgage loanDealerFirstMortgage1 = new LoanDealerFirstMortgage();
                            loanDealerFirstMortgage1.setDealerNo(channelBaseInfo.getId().toString());
                            loanDealerFirstMortgage1.setDealerName(channelBaseInfo.getChannelFullName());
                            loanDealerFirstMortgage1.setChannelType(channelBaseInfo.getChannelBelong());
                            loanDealerFirstMortgage1.setBusinessType(caseContractInfo.getBusinessType());
                            loanDealerFirstMortgage1.setIsFirstMortgage(WhetherEnum.YES.getCode());
                            loanDealerFirstMortgage1.setProvinceCode(caseCarInfo.getPurchaseProvince());
                            loanDealerFirstMortgage1.setCityCode(caseCarInfo.getPurchaseCity());
                            loanDealerFirstMortgage1.setFirstCaseNo(caseContractInfo.getContractNo());
                            loanDealerFirstMortgageService.save(loanDealerFirstMortgage1);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("{}首单落库失败--",contractNo,e);
        }
    }

    /**
     * 获取OCR验证结果
     * <AUTHOR>
     * @Date 2020/8/12
     * @param invoiceOcrCondition
     * @return
     */
    @Override
    public InvoiceCheckVo getInvoiceCheck(InvoiceOcrCondition invoiceOcrCondition){
       return new InvoiceCheckVo();
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void backToDealer(LoanApproveSubmitVO condition) {
        //清楚合同表执行步骤
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getContractNo,condition.getContractNo()));
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
        status.add(ApplyStatusEnum.LOAN_REPAIR.getState());
        status.add(ApplyStatusEnum.LOAN_REVIEW_RETURN.getState());
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());
        Assert.isTrue(checkApplyStatus(caseContractInfo,status),"合同审核状态异常!");

        List<CaseBackToPartnersInfo> caseBackToPartnersInfoList = caseBackToPartnersInfoService.list(Wrappers.<CaseBackToPartnersInfo>query().lambda()
                .eq(CaseBackToPartnersInfo::getContractNo,condition.getContractNo()));
        if(!CollectionUtils.isEmpty(caseBackToPartnersInfoList)){
            caseBackToPartnersInfoList.forEach(caseBackToPartnersInfo -> {
                if(caseBackToPartnersInfo.getBackTime()==null){
                    caseBackToPartnersInfo.setBackTime(new Date());
                    caseBackToPartnersInfoService.updateById(caseBackToPartnersInfo);
                }
            });
        }

        //修改放款审核状态
        if(!ObjectUtils.isEmpty(caseContractInfo)){
            caseContractInfo.setWorkflowPrevStep(1);
            caseContractInfo.setFlowNode(FlowNodeEnum.CHANNEL.getCode());
            caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_RETURN.getState());
            caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
            caseContractInfoService.updateById(caseContractInfo);
        }
        log.info("*****************" + condition.getContractNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_RETURN.getState() + "*****************");

        //工作流提交流程  保存操作记录
        final IResponse<Boolean> response = submitWorkflow(condition);
        if (!CaseConstants.CODE_SUCCESS.equals(response.getCode())){
            // TODO 异常处理
            throw new RuntimeException("工作流异常 - 待处理");
        }else {
            approveLoanInfoService.backToPartnersNotic(condition.getContractNo(), condition.getApproveRecord().getStageId(),
                condition.getApproveRecord().getTaskId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reviewSubmit(LoanApproveSubmitVO condition) { //清楚合同表执行步骤

        log.info("开始校验合同审核状态是否正常...");
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getContractNo,condition.getContractNo()));
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());
        status.add(ApplyStatusEnum.WAIT_XC.getState());
        status.add(ApplyStatusEnum.REVOCABLE_WAITING.getState());
        status.add(ApplyStatusEnum.WAIT_BANK_STATUS.getState());
        Boolean aBoolean = checkApplyStatus(caseContractInfo, status);
        Assert.isTrue(aBoolean,"合同审核状态异常!");


        log.info("开始校验合同审核状态为预审通过...");
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_PRE_APPROVE.getState());
        caseContractInfo.setApproveCompleteTime(new Date());
        log.info("*****************" + condition.getApproveRecord().getApplyNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_PRE_APPROVE.getState() + "*****************");
        //更新放款通过时间
        caseContractInfo.setFlowNode(FlowNodeEnum.AUTO.getCode());
        AfsUser user = SecurityUtils.getUser();
        if(!ObjectUtils.isEmpty(user) && StringUtils.isNotBlank(user.getUsername())){
            caseContractInfo.setLoanReviewer(user.getUsername());
        } else {
            caseContractInfo.setLoanReviewer("auto");
        }

        //判断是否一次审批通过
        List<WorkflowTaskInfo> workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo, caseContractInfo.getApplyNo())
                .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getLoanPackageId())
                .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId()));
        caseContractInfo.setIsFirstSubmitApproved("1");
        for(WorkflowTaskInfo workflowTaskInfo : workflowTaskInfos){
            if(!FlowTaskOperationEnum.SUBMIT.getCode().equals(workflowTaskInfo.getOperationType())){
                caseContractInfo.setIsFirstSubmitApproved("0");
                break;
            }
        }
        caseContractInfoService.updateById(caseContractInfo);

        //修改主状态
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(caseContractInfo.getApplyNo()), CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()), false);
        //修改主状态信息
        List<CaseMainInfo> caseMainInfos = caseMainInfoService.list(Wrappers.<CaseMainInfo>query().lambda()
                .eq(CaseMainInfo::getApplyNo, caseBaseInfo.getApplyNo())
                .eq(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(caseBaseInfo.getOrderId()),CaseMainInfo::getPreApplyNo,caseBaseInfo.getOrderId())
                .orderByDesc(CaseMainInfo::getUpdateTime)
        );
        if (CollectionUtil.isNotEmpty(caseMainInfos)){
            caseMainInfos.get(0);
            CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
            StatusDTO caseStatusDTO = new StatusDTO();
            caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
            caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4910);
            caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
            caseStatusDTO.setStatusCode(AfsEnumUtil.key(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        /**复审完成 添加合作商展业城市首单记录*/
        //复审完成  待激活池插入数据
        loanActivateService.addLoanActivateInfo(condition.getApproveRecord().getApplyNo());
        log.info("*****************复核完成待激活池插入待激活数据*****************");
        log.info("*****************放款通过推送信审到结束节点开始*****************");
        WorkflowProcessBusinessRefInfo workflowTaskInfo=null;
        List<WorkflowProcessBusinessRefInfo> refInfoList = processBusinessRefInfoService.list(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, condition.getApproveRecord().getApplyNo())
                .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getApprovePackageId())
                .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId())
                .orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime)
        );
        log.info("查询workflow_process_business_ref_info表的数据，其中入参BusinessNo={}，FlowPackageId={}，FlowTemplateId={}，查询结果={}",
                condition.getApproveRecord().getApplyNo(),flowConfigProperties.getApprovePackageId(),flowConfigProperties.getApproveTemplateId(),JSON.toJSONString(refInfoList));
        if(CollectionUtil.isNotEmpty(refInfoList)){
            if(refInfoList.size()>0){
                workflowTaskInfo=refInfoList.get(0);
            }
        }
        log.info("workflowTaskInfo赋值后的数据={}",JSON.toJSONString(workflowTaskInfo));
        if(ObjectUtil.isNotNull(workflowTaskInfo)){
            FlowVariable flowVariable=new FlowVariable();
            flowVariable.setFlowInstanceId(workflowTaskInfo.getProcessInstanceId());
            flowVariable.setName(FlowConstant.BIZ_OPERATION_TYPE);
            flowVariable.setValue(FlowTaskOperationEnum.FINAL6APPROVED.getCode());

            //修改变量信息
            workflowHelper.setFlowVariableByFlowInstance(flowVariable);
            //恢复流程
            log.info("开始恢复流程！");
            workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
            //保存记录
            log.info("开始保存记录！");
            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(condition.getApproveRecord().getApplyNo());
            record.setUseScene(UseSceneEnum.APPROVE.getValue());
            record.setApproveSuggest(FlowTaskOperationEnum.FINAL6APPROVED.getCode());
            record.setApproveSuggestName(FlowTaskOperationEnum.FINAL6APPROVED.getDesc());
            record.setApproveEndTime(new Date());
            record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
            record.setApproveRemark(AfsEnumUtil.desc(FlowTaskOperationEnum.FINAL6APPROVED));
            caseApproveRecordService.save(record);
        }
        log.info("*****************放款通过推送信审到结束节点结束*****************");
        //通知进件系统
        approveLoanInfoService.sendLoanToApplyNotic(condition.getContractNo(), ApplyStatusEnum.LOAN_PRE_APPROVE, condition.getMortgageClaim());

        ThreadUtil.execute(()->{
            try {
                /**等待0.5秒执行*/
                TimeUnit.MILLISECONDS.sleep(500);
                doApproveDone(condition.getContractNo());
            }catch (Throwable e){
                log.error("复审通过后置事件完成,运行结果",e);
            }
        });

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newReviewSubmit(LoanApproveSubmitVO condition,CaseContractInfo caseContractInfo) { //清楚合同表执行步骤

        log.info("开始校验合同审核状态是否正常...");
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());
        status.add(ApplyStatusEnum.WAIT_XC.getState());
        status.add(ApplyStatusEnum.WAIT_BANK_STATUS.getState());
        Boolean aBoolean = checkApplyStatus(caseContractInfo, status);
        Assert.isTrue(aBoolean,"合同审核状态异常!");


        log.info("开始校验合同审核状态为预审通过...");
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_PRE_APPROVE.getState());
        caseContractInfo.setApproveCompleteTime(new Date());
        log.info("*****************" + condition.getApproveRecord().getApplyNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_PRE_APPROVE.getState() + "*****************");
        //更新放款通过时间
        caseContractInfo.setFlowNode(FlowNodeEnum.AUTO.getCode());
        AfsUser user = SecurityUtils.getUser();
        if(!ObjectUtils.isEmpty(user) && StringUtils.isNotBlank(user.getUsername())){
            caseContractInfo.setLoanReviewer(user.getUsername());
        } else {
            caseContractInfo.setLoanReviewer("auto");
        }

        //判断是否一次审批通过
        List<WorkflowTaskInfo> workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo, caseContractInfo.getApplyNo())
                .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getLoanPackageId())
                .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId()));
        caseContractInfo.setIsFirstSubmitApproved("1");
        for(WorkflowTaskInfo workflowTaskInfo : workflowTaskInfos){
            if(!FlowTaskOperationEnum.SUBMIT.getCode().equals(workflowTaskInfo.getOperationType())){
                caseContractInfo.setIsFirstSubmitApproved("0");
                break;
            }
        }

        caseContractInfoService.updateById(caseContractInfo);

        //修改主状态
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(caseContractInfo.getApplyNo()), CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()), false);
        //修改主状态信息
        List<CaseMainInfo> caseMainInfos = caseMainInfoService.list(Wrappers.<CaseMainInfo>query().lambda()
                .eq(CaseMainInfo::getApplyNo, caseBaseInfo.getApplyNo())
                .eq(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(caseBaseInfo.getOrderId()),CaseMainInfo::getPreApplyNo,caseBaseInfo.getOrderId())
                .orderByDesc(CaseMainInfo::getUpdateTime)
        );
        if (CollectionUtil.isNotEmpty(caseMainInfos)){
            caseMainInfos.get(0);
            CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
            StatusDTO caseStatusDTO = new StatusDTO();
            caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
            caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4910);
            caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
            caseStatusDTO.setStatusCode(AfsEnumUtil.key(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        /**复审完成 添加合作商展业城市首单记录*/
        this.saveIsFirstMortgage(condition.getContractNo());
        //复审完成  待激活池插入数据
        loanActivateService.addLoanActivateInfo(condition.getApproveRecord().getApplyNo());
        log.info("*****************复核完成待激活池插入待激活数据*****************");
        log.info("*****************放款通过推送信审到结束节点开始*****************");
        WorkflowProcessBusinessRefInfo workflowTaskInfo=null;
        List<WorkflowProcessBusinessRefInfo> refInfoList = processBusinessRefInfoService.list(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, condition.getApproveRecord().getApplyNo())
                .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getApprovePackageId())
                .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId())
                .orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime)
        );
        log.info("查询workflow_process_business_ref_info表的数据，其中入参BusinessNo={}，FlowPackageId={}，FlowTemplateId={}，查询结果={}",
                condition.getApproveRecord().getApplyNo(),flowConfigProperties.getApprovePackageId(),flowConfigProperties.getApproveTemplateId(),JSON.toJSONString(refInfoList));
        if(CollectionUtil.isNotEmpty(refInfoList)){
            if(refInfoList.size()>0){
                workflowTaskInfo=refInfoList.get(0);
            }
        }
        log.info("workflowTaskInfo赋值后的数据={}",JSON.toJSONString(workflowTaskInfo));
        if(ObjectUtil.isNotNull(workflowTaskInfo)){
            FlowVariable flowVariable=new FlowVariable();
            flowVariable.setFlowInstanceId(workflowTaskInfo.getProcessInstanceId());
            flowVariable.setName(FlowConstant.BIZ_OPERATION_TYPE);
            flowVariable.setValue(FlowTaskOperationEnum.FINAL6APPROVED.getCode());

            //修改变量信息
            workflowHelper.setFlowVariableByFlowInstance(flowVariable);
            //恢复流程
            log.info("开始恢复流程！");
            workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
        }

        //保存记录
        log.info("开始保存放款自动审核日志记录！");

        List<CaseApproveRecord> records = new ArrayList<>();

        // 保存记录 - 放款自动审核记录
        CaseApproveRecord record = new CaseApproveRecord();
        record.setApplyNo(condition.getApproveRecord().getApplyNo());
        record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record.setApproveSuggest(FlowTaskOperationEnum.AUTOLOANAPPROVED.getCode());
        record.setApproveSuggestName(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
        record.setApproveEndTime(new Date());
        record.setApproveStartTime(new Date());
        record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record.setApproveRemark(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
        record.setDisposeNodeName("放款自动审核");
        record.setDisposeStaff("系统");
        records.add(record);

        // 保存记录 - 放款结束节点
        CaseApproveRecord record2 = new CaseApproveRecord();
        record2.setApplyNo(condition.getApproveRecord().getApplyNo());
        record2.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record2.setApproveSuggest(FlowTaskOperationEnum.SUBMIT.getCode());
        record2.setApproveSuggestName(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setApproveEndTime(new Date());
        record2.setApproveStartTime(new Date());
        record2.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record2.setApproveRemark(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setDisposeNodeName("请款结束节点");
        record2.setDisposeStaff("系统");
        records.add(record2);

        // 信审结束
        CaseApproveRecord record3 = new CaseApproveRecord();
        record3.setApplyNo(condition.getApproveRecord().getApplyNo());
        record3.setUseScene(UseSceneEnum.APPROVE.getValue());
        record3.setApproveSuggest(FlowTaskOperationEnum.FINAL6APPROVED.getCode());
        record3.setApproveSuggestName(FlowTaskOperationEnum.FINAL6APPROVED.getDesc());
        record3.setApproveEndTime(new Date());
        record3.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record3.setApproveRemark(AfsEnumUtil.desc(FlowTaskOperationEnum.FINAL6APPROVED));
        records.add(record3);

        caseApproveRecordService.saveBatch(records);

        log.info("*****************放款通过推送信审到结束节点结束*****************");
        //通知进件系统
        approveLoanInfoService.sendLoanToApplyNotic(condition.getContractNo(), ApplyStatusEnum.LOAN_PRE_APPROVE, caseContractInfo.getMortgageClaim());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newReviewSubmit(LoanApproveSubmitVO condition,CaseContractInfo caseContractInfo, String approveTime, Date date) { //清楚合同表执行步骤

        log.info("开始校验合同审核状态是否正常1...");
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());
        status.add(ApplyStatusEnum.WAIT_XC.getState());
        status.add(ApplyStatusEnum.WAIT_BANK_STATUS.getState());
        Boolean aBoolean = checkApplyStatus(caseContractInfo, status);
        Assert.isTrue(aBoolean,"合同审核状态异常!");


        log.info("开始校验合同审核状态为预审通过1...");
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_PRE_APPROVE.getState());
        caseContractInfo.setApproveCompleteTime(new Date());
        log.info("*****************" + condition.getApproveRecord().getApplyNo() + " 放款审核状态1change:" + ApplyStatusEnum.LOAN_PRE_APPROVE.getState() + "*****************");
        //更新放款通过时间
        caseContractInfo.setFlowNode(FlowNodeEnum.AUTO.getCode());
        AfsUser user = SecurityUtils.getUser();
        if(!ObjectUtils.isEmpty(user) && StringUtils.isNotBlank(user.getUsername())){
            caseContractInfo.setLoanReviewer(user.getUsername());
        } else {
            caseContractInfo.setLoanReviewer("auto");
        }

        //判断是否一次审批通过
        List<WorkflowTaskInfo> workflowTaskInfos = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo, caseContractInfo.getApplyNo())
                .eq(WorkflowTaskInfo::getFlowPackageId,flowConfigProperties.getLoanPackageId())
                .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getLoanTemplateId()));
        caseContractInfo.setIsFirstSubmitApproved("1");
        for(WorkflowTaskInfo workflowTaskInfo : workflowTaskInfos){
            if(!FlowTaskOperationEnum.SUBMIT.getCode().equals(workflowTaskInfo.getOperationType())){
                caseContractInfo.setIsFirstSubmitApproved("0");
                break;
            }
        }

        caseContractInfoService.updateById(caseContractInfo);

        //修改主状态
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(caseContractInfo.getApplyNo()), CaseBaseInfo::getApplyNo, caseContractInfo.getApplyNo()), false);
        //修改主状态信息
        List<CaseMainInfo> caseMainInfos = caseMainInfoService.list(Wrappers.<CaseMainInfo>query().lambda()
                .eq(CaseMainInfo::getApplyNo, caseBaseInfo.getApplyNo())
                .eq(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(caseBaseInfo.getOrderId()),CaseMainInfo::getPreApplyNo,caseBaseInfo.getOrderId())
                .orderByDesc(CaseMainInfo::getUpdateTime)
        );
        if (CollectionUtil.isNotEmpty(caseMainInfos)){
            caseMainInfos.get(0);
            CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
            StatusDTO caseStatusDTO = new StatusDTO();
            caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
            caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4910);
            caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
            caseStatusDTO.setStatusCode(AfsEnumUtil.key(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(ApplyStatusEnum.LOAN_PRE_APPROVE));
            caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        /**复审完成 添加合作商展业城市首单记录*/
        this.saveIsFirstMortgage(condition.getContractNo());
        //复审完成  待激活池插入数据
        loanActivateService.addLoanActivateInfo(condition.getApproveRecord().getApplyNo());
        log.info("*****************复核完成待激活池插入待激活数据1*****************");
        log.info("*****************放款通过推送信审到结束节点开始1*****************");
        WorkflowProcessBusinessRefInfo workflowTaskInfo=null;
        List<WorkflowProcessBusinessRefInfo> refInfoList = processBusinessRefInfoService.list(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, condition.getApproveRecord().getApplyNo())
                .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, flowConfigProperties.getApprovePackageId())
                .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, flowConfigProperties.getApproveTemplateId())
                .orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime)
        );
        log.info("查询workflow_process_business_ref_info表的数据1，其中入参BusinessNo={}，FlowPackageId={}，FlowTemplateId={}，查询结果={}",
                condition.getApproveRecord().getApplyNo(),flowConfigProperties.getApprovePackageId(),flowConfigProperties.getApproveTemplateId(),JSON.toJSONString(refInfoList));
        if(CollectionUtil.isNotEmpty(refInfoList)){
            if(refInfoList.size()>0){
                workflowTaskInfo=refInfoList.get(0);
            }
        }
        log.info("workflowTaskInfo赋值后的数据1={}",JSON.toJSONString(workflowTaskInfo));
        if(ObjectUtil.isNotNull(workflowTaskInfo)){
            FlowVariable flowVariable=new FlowVariable();
            flowVariable.setFlowInstanceId(workflowTaskInfo.getProcessInstanceId());
            flowVariable.setName(FlowConstant.BIZ_OPERATION_TYPE);
            flowVariable.setValue(FlowTaskOperationEnum.FINAL6APPROVED.getCode());

            //修改变量信息
            workflowHelper.setFlowVariableByFlowInstance(flowVariable);
            //恢复流程
            log.info("开始恢复流程1！");
            workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
        }

        //保存记录
        log.info("开始保存放款自动审核日志记录1！");

        List<CaseApproveRecord> records = new ArrayList<>();

        // 定义日期格式
        String pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);

        // 保存记录 - 放款资方自动审核记录
        CaseApproveRecord record4 = new CaseApproveRecord();
        record4.setApplyNo(condition.getApproveRecord().getApplyNo());
        record4.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record4.setApproveSuggest(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getCode());
        record4.setApproveSuggestName(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getDesc());
        try {
            // 执行转换
            Date tDate = sdf.parse(approveTime);
            record4.setApproveEndTime(tDate);
            record4.setApproveStartTime(tDate);
        } catch (ParseException e) {
            log.error("资方审核时间转换失败，使用当前时间!");
            record4.setApproveEndTime(new Date());
            record4.setApproveStartTime(new Date());
        }

        record4.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record4.setApproveRemark(FlowTaskOperationEnum.AWAITBANKLOANSTATUSPASS.getDesc());
        record4.setDisposeNodeName("银行审批");
        record4.setDisposeStaff("系统");
        records.add(record4);

        // 保存记录 - 放款结束节点
        CaseApproveRecord record2 = new CaseApproveRecord();
        record2.setApplyNo(condition.getApproveRecord().getApplyNo());
        record2.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record2.setApproveSuggest(FlowTaskOperationEnum.SUBMIT.getCode());
        record2.setApproveSuggestName(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setApproveEndTime(new Date());
        record2.setApproveStartTime(new Date());
        record2.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record2.setApproveRemark(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setDisposeNodeName("请款结束节点");
        record2.setDisposeStaff("系统");
        records.add(record2);

        // 信审结束
        CaseApproveRecord record3 = new CaseApproveRecord();
        record3.setApplyNo(condition.getApproveRecord().getApplyNo());
        record3.setUseScene(UseSceneEnum.APPROVE.getValue());
        record3.setApproveSuggest(FlowTaskOperationEnum.FINAL6APPROVED.getCode());
        record3.setApproveSuggestName(FlowTaskOperationEnum.FINAL6APPROVED.getDesc());
        record3.setApproveEndTime(new Date());
        record3.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record3.setApproveRemark(AfsEnumUtil.desc(FlowTaskOperationEnum.FINAL6APPROVED));
        records.add(record3);

        caseApproveRecordService.saveBatch(records);

        log.info("*****************放款通过推送信审到结束节点结束1*****************");
        //通知进件系统
        approveLoanInfoService.sendToApplyNotic(condition.getContractNo(), ApplyStatusEnum.LOAN_PRE_APPROVE);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newReviewBackSubmit(LoanApproveSubmitVO condition) { //清楚合同表执行步骤

        //保存记录
        log.info("开始保存放款自动审核日志记录！");

        List<CaseApproveRecord> records = new ArrayList<>();

        // 保存记录 - 放款自动审核记录
        CaseApproveRecord record = new CaseApproveRecord();
        record.setApplyNo(condition.getApproveRecord().getApplyNo());
        record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record.setApproveSuggest(FlowTaskOperationEnum.AUTOLOANAPPROVEDBACK.getCode());
        record.setApproveSuggestName(FlowTaskOperationEnum.AUTOLOANAPPROVEDBACK.getDesc());
        record.setApproveEndTime(new Date());
        record.setApproveStartTime(new Date());
        record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record.setApproveRemark(FlowTaskOperationEnum.AUTOLOANAPPROVEDBACK.getDesc());
        record.setDisposeNodeName("放款自动审核");
        record.setDisposeStaff("系统");
        records.add(record);

        // 保存记录 - 放款结束节点
        CaseApproveRecord record2 = new CaseApproveRecord();
        record2.setApplyNo(condition.getApproveRecord().getApplyNo());
        record2.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        record2.setApproveSuggest(FlowTaskOperationEnum.SUBMIT.getCode());
        record2.setApproveSuggestName(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setApproveEndTime(new Date());
        record2.setApproveStartTime(new Date());
        record2.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record2.setApproveRemark(FlowTaskOperationEnum.SUBMIT.getDesc());
        record2.setDisposeNodeName("请款结束节点");
        record2.setDisposeStaff("系统");
        records.add(record2);

        // 信审结束
        CaseApproveRecord record3 = new CaseApproveRecord();
        record3.setApplyNo(condition.getApproveRecord().getApplyNo());
        record3.setUseScene(UseSceneEnum.APPROVE.getValue());
        record3.setApproveSuggest(FlowTaskOperationEnum.FINAL6APPROVED.getCode());
        record3.setApproveSuggestName(FlowTaskOperationEnum.FINAL6APPROVED.getDesc());
        record3.setApproveEndTime(new Date());
        record3.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record3.setApproveRemark(AfsEnumUtil.desc(FlowTaskOperationEnum.FINAL6APPROVED));
        records.add(record3);

        caseApproveRecordService.saveBatch(records);

        log.info("*****************放款通过推送信审到结束节点结束*****************");
        //通知进件系统
        SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                .approveType(ApplyStatusEnum.LOAN_RETURN.getState())
                .contractNo(condition.getContractNo()).build();
        ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();

        infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                .data(approveSubmitInfo).build());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void backToAudit(LoanApproveCondition condition) {
        //清除必复核标记
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getContractNo,condition.getContractNo()));
        caseContractInfo.setNeedReview(WhetherEnum.NO.getCode());
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_REVIEW_RETURN.getState());
        log.info("*****************" + condition.getApplyNo() + "放款审核状态change:" + ApplyStatusEnum.LOAN_REVIEW_RETURN.getState() + "*****************");
        caseContractInfo.setFlowNode(FlowNodeEnum.PRIMARY.getCode());
        caseContractInfoService.updateById(caseContractInfo);
        //工作流提交流程  保存操作记录
        condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        condition.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.BACK_AUDIT));
        condition.setFlowNode(FlowNodeEnum.REVIEW.getCode());
        loanWorkflowService.submitWorkflowByScheduleInfo(condition);

        //通知进件系统
        approveLoanInfoService.sendToApplyNotic(condition.getContractNo(), ApplyStatusEnum.LOAN_WAIT_APPROVE);
    }

    /**
     * 复核规则
     */
    @Override
    public void setReviewFlag(String contractNo) {
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getContractNo, contractNo));
        if (!ObjectUtils.isEmpty(caseContractInfo)) {
            JSONObject runParams = ruleAtomDataService.getCommonAtomRunParamsLoan(contractNo);
            log.info("{}复核规则报文：{}", contractNo, runParams);
            try {
                RuleRunResult result = RuleHelper.runRule(runParams, "reviewRuleGroup", false, RuleRunEnum.SERIAL);
                if (result.getHit()) {
                    caseContractInfo.setNeedReview(WhetherEnum.YES.getCode());
                    caseContractInfoService.updateById(caseContractInfo);
                } else {
                    log.info("合同号{}复核规则未命中！", contractNo);
                }
            } catch (Exception e) {
                log.error("合同号{}复核规则调用失败！{}", contractNo, e);
            }
        } else {
            log.error("复核规则：合同号{}未找到相应合同信息", contractNo);
        }
    }

    @Override
    public void savePaperInfo(CarInsuranceInfoCondition info) {
        List<CarInsuranceInfo> infoList = carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>lambdaQuery()
                .eq(StringUtils.isNoneBlank(info.getContractNo()), CarInsuranceInfo::getContractNo, info.getContractNo()));
        for(CarInsuranceInfo insuranceInfo:infoList){
            insuranceInfo.setInsuranceStartTime(info.getInsuranceStartTime());
            insuranceInfo.setInsuranceEndTime(info.getInsuranceEndTime());
            carInsuranceInfoService.saveOrUpdate(insuranceInfo);
        }
    }

    @Override
    public void lockContract(String contractNo,String remarks){
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,contractNo));
        caseContractInfo.setLockDegree(LockDegreeEnum.MEDIUM.getValue());
        caseContractInfo.setLockType(LockTypeEnum.ARTIFICIAL.getState());
        caseContractInfo.setIsLock(WhetherEnum.YES.getCode());
        caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
        caseContractInfoService.updateById(caseContractInfo);
    }

    @Override
    public void suspendContract(String contractNo,String suspendRemarks){
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,contractNo));
        caseContractInfo.setSuspendStatus(WhetherEnum.YES.getCode());
        caseContractInfo.setSuspendStart(new Date());
        if (ObjectUtils.isEmpty(caseContractInfo.getSuspendTimes())) {
            caseContractInfo.setSuspendTimes("once");//记录是否首次挂起
        } else {
            caseContractInfo.setSuspendTimes("again");//记录是否多次挂起
        }
        caseContractInfoService.updateById(caseContractInfo);
    }

    @Override
    public void cancelSuspendContract(String contractNo,String suspendRemarks){
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,contractNo));
        caseContractInfo.setSuspendStatus(WhetherEnum.NO.getCode());
        caseContractInfo.setSuspendEnd(new Date());
        caseContractInfoService.updateById(caseContractInfo);
    }

    @Override
    public void unLockContract(String contractNo,String remarks){
        CaseContractInfo caseContractInfo=caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo,contractNo));
        CaseBaseInfo baseInfo= caseBaseInfoService.getCaseByApplyNo(caseContractInfo.getApplyNo());
        if(WhetherEnum.YES.getCode().equals(baseInfo.getIsLock())) {
            log.info("合同:{}审批锁定，放款阶段无法进行解锁操作！",contractNo);
            throw new AfsBaseException("案件由审批锁定，必须由审批解锁");
        } else {
            caseContractInfo.setLoanSuspendRuleId("");
            caseContractInfo.setLockType(LockTypeEnum.ARTIFICIAL.getState());
            caseContractInfo.setLockDegree(LockDegreeEnum.NORMAL.getValue());
            caseContractInfo.setIsLock(WhetherEnum.NO.getCode());
            caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
            caseContractInfoService.updateById(caseContractInfo);
            if(WhetherEnum.YES.getCode().equals(baseInfo.getLoanSubmitLock())){
                baseInfo.setLoanSubmitLock(WhetherEnum.NO.getCode());
                baseInfo.setLoanLockReason("");
                caseBaseInfoService.updateById(baseInfo);
            }
        }
    }

    /**
     * add by rongji.zhang
     * 暂停拦截
     * result: true(未锁定),false(锁定)
     */
    @Override
    public Boolean setSuspend(CaseContractInfo contractInfo) {
        log.info("合同{}:拦截暂停检测开始执行...", contractInfo.getContractNo());
        CaseBaseInfo baseInfo= caseBaseInfoService.getCaseByApplyNo(contractInfo.getApplyNo());
        if(WhetherEnum.YES.getCode().equals(baseInfo.getIsLock())) {
            /**如果主表被锁状态，则锁定合同*/
            log.info("合同{}:案件被锁定", contractInfo.getContractNo());
            contractInfo.setLockDegree(LockDegreeEnum.MEDIUM.getValue());
            contractInfo.setIsLock(WhetherEnum.YES.getCode());
            contractInfo.setLockType(LockTypeEnum.ARTIFICIAL.getState());
            contractInfo.setPriority(casePriorityChangeService.priorityChange(contractInfo));
            contractInfo.setRemarks("审批锁定");
            caseContractInfoService.updateById(contractInfo);
            return false;
        }else {
            if(WhetherEnum.YES.getCode().equals(baseInfo.getLoanSubmitLock())){
                /**如果主表被锁状态，则锁定合同*/
                log.info("合同{}:放款申请提交被锁定", contractInfo.getContractNo());
                contractInfo.setLockDegree(LockDegreeEnum.MEDIUM.getValue());
                contractInfo.setIsLock(WhetherEnum.YES.getCode());
                contractInfo.setLockType(LockTypeEnum.AUTOMATIC.getState());
                contractInfo.setPriority(casePriorityChangeService.priorityChange(contractInfo));
                contractInfo.setRemarks(baseInfo.getLoanLockReason());
                caseContractInfoService.updateById(contractInfo);
                return false;
            }else {
                List<LoanSuspendRule> activeLoanSuspendRules = loanSuspendRuleService.list(Wrappers.<LoanSuspendRule>lambdaQuery().eq(LoanSuspendRule::getIsEnable, WhetherEnum.YES.getCode()));
                if (CollectionUtils.isEmpty(activeLoanSuspendRules)) {
                    log.info("合同{}:未找到已启用的拦截暂停规则，跳过执行", contractInfo.getContractNo());
                    return true;
                } else {
                    JSONObject runParam = ruleAtomDataService.getCommonAtomRunParamsLoan(contractInfo.getContractNo());
                    log.info("合同{}:拦截暂停规则报文：{}", contractInfo.getContractNo(), runParam);
                    RuleRunResult result = RuleHelper.runRule(runParam, "suspendRuleGroup", false, RuleRunEnum.SERIAL);
                    if (result.getHit()) {
                        List listNo = result.getResults().stream().map(s -> {
                            return s.getRuleNo();
                        }).collect(Collectors.toList());
                        if (listNo.isEmpty()) {
                            log.info("未找到规则编号");
                            return true;
                        } else {
                            log.info("命中规则编号：{}", listNo);
                            List<LoanSuspendRule> loanSuspendRules = loanSuspendRuleService.listByIds(listNo);
                            StringBuilder lockReason = new StringBuilder();
                            for (int i = 0; i < loanSuspendRules.size(); i++) {
                                lockReason.append(loanSuspendRules.get(i).getLockReason()).append(";");
                            }
                            if (!CollectionUtils.isEmpty(loanSuspendRules)) {
                                Optional<LoanSuspendRule> loanSuspendRule = loanSuspendRules.stream().max(Comparator.comparing(LoanSuspendRule::getLockDegree));
                                contractInfo.setLockDegree(loanSuspendRule.get().getLockDegree());
                                contractInfo.setLockType(LockTypeEnum.AUTOMATIC.getState());
                                contractInfo.setIsLock(WhetherEnum.YES.getCode());
                                contractInfo.setIsTop(WhetherEnum.NO.getCode());
                                contractInfo.setPriority(casePriorityChangeService.priorityChange(contractInfo));
                                contractInfo.setRemarks(lockReason.toString());
                                caseContractInfoService.updateById(contractInfo);
                                return false;
                            } else {
                                log.info("合同{}:未找到相应规则", contractInfo.getContractNo(), result.getHit());
                                return true;
                            }
                        }
                    } else {
                        log.info("合同{}:拦截暂停规则未命中：{}", contractInfo.getContractNo(), result.getHit());
                        return true;
                    }
                }
            }
        }
    }

    /**
     * add by rongji.zhang
     * 审批时效
     *
     * @param contractInfo
     */
    @Override
    public Boolean setOvertime(CaseContractInfo contractInfo) {
        List<LoanOverTimeRule> loanOverTimeRuleList = loanOverTimeRuleService.list(Wrappers.<LoanOverTimeRule>lambdaQuery().eq(LoanOverTimeRule::getIsEnable, WhetherEnum.YES.getCode()));
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(loanOverTimeRuleList)) {
            log.info("合同{}:超时效检测开始执行...", contractInfo.getContractNo() );
            JSONObject runParam = ruleAtomDataService.getCommonAtomRunParamsOverTime(contractInfo.getApplyNo());
            log.info("合同{}:超时效暂停规则报文：{}", contractInfo.getContractNo(), runParam);
            RuleRunResult result = RuleHelper.runRule(runParam, "overTimeRuleGroup", false, RuleRunEnum.SERIAL);
            if (result.getHit()) {
                String ruleRemarks = "案件已超审批时效;";
                if(StrUtil.isNotBlank(contractInfo.getRemarks())&&!contractInfo.getRemarks().contains("案件已超审批时效")){
                    ruleRemarks = contractInfo.getRemarks()+ruleRemarks;
                }
                contractInfo.setIsOvertime(WhetherEnum.YES.getCode());
                contractInfo.setPriority(casePriorityChangeService.priorityChange(contractInfo));
                contractInfo.setRemarks(ruleRemarks);
                caseContractInfoService.updateById(contractInfo);
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    @Override
    public void lockCase(String applyNo){
        List<CaseContractInfo> caseContractInfos=caseContractInfoService.list(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getApplyNo,applyNo));
        caseContractInfos.forEach(caseContractInfo->{
            caseContractInfo.setIsLock(WhetherEnum.YES.getCode());
            caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
            caseContractInfo.setRemarks("疑似欺诈");
            caseContractInfo.setLockDegree(LockDegreeEnum.MEDIUM.getValue());
            caseContractInfoService.updateById(caseContractInfo);
        });
    }

    @Override
    public void lockChannel(String channelCode){
        List<CaseChannelInfo> caseChannelInfos=caseChannelInfoService.list(Wrappers.<CaseChannelInfo>lambdaQuery().eq(CaseChannelInfo::getDealerNo,channelCode));
        caseChannelInfos.forEach(caseChannelInfo->{
            List<CaseContractInfo> caseContractInfos=caseContractInfoService.list(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getApplyNo,caseChannelInfo.getApplyNo()));
            caseContractInfos.forEach(caseContractInfo->{
                caseContractInfo.setIsLock(WhetherEnum.YES.getCode());
                caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
                caseContractInfo.setRemarks("疑似欺诈");
                caseContractInfo.setLockDegree(LockDegreeEnum.MEDIUM.getValue());
                caseContractInfoService.updateById(caseContractInfo);
            });
        });
    }

    private Boolean checkApplyStatus(CaseContractInfo contractInfo,List<String> submitStatus){
        Boolean result=true;
        if(!submitStatus.contains(contractInfo.getApplyStatus())){
            result=false;
        }
        return result;
    }

    public void doApproveDone(String contractNo){
        log.info("{}合同复核提交之后执行放款审核通过操作开始",contractNo);
        StepParam stepParams=new StepParam();
        stepParams.setContractNo(contractNo);
        stepParams.setContractInfo(caseContractInfoService.getContractByContractNo(contractNo));
        stepParams.setReview(true);
        ActiveStepUtil.prevSteps(stepParams);
    }

    /**
     * 复审自动通过
     * @param contractNo
     */
    @Override
    public void reviewAutoApprove(String contractNo){
        CaseConfParam caseConfParam=caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                .eq(CaseConfParam::getParamType, Const.REVIEW_AUTO_APPROVE_SWITCH));
        if (ObjectUtil.isNull(caseConfParam)){
            log.warn("未配置放款复审自动审核参数！");
            return;
        }
        if(AfsEnumUtil.key(YesOrNoEnum.yes).equals(caseConfParam.getParamStatus())&&WhetherEnum.YES.getCode().equals(caseConfParam.getParamValue())){
            log.info("{}合同开始执行复审自动审核任务!",contractNo);
            Boolean isTrue=true;
            if(isTrue){
                isTrue =this.checkEnableAutoReview(contractNo);
                log.info("复审自动审核任务-{}合同状态校验结果：{}",contractNo,isTrue);
            }

            if(isTrue){
                isTrue=this.checkContractSuspend(contractNo);
                log.info("复审自动审核任务-{}合同暂停规则校验结果：{}",contractNo,isTrue);
            }
            if(isTrue){
                isTrue=this.checkContractInsuranceData(contractNo);
                log.info("复审自动审核任务-{}合同保单生效日期校验结果：{}",contractNo,isTrue);
            }
            if(isTrue){
                isTrue=this.checkFileStatus(contractNo);
                log.info("复审自动审核任务-{}合同文件状态校验结果：{}",contractNo,isTrue);
            }
            if(isTrue){
                isTrue=this.checkOverTime(contractNo);
                log.info("复审自动审核任务-{}合同审批时效校验结果：{}",contractNo,isTrue);
            }
            if(isTrue){
                log.info("{}合同满足复审自动通过条件。",contractNo);
                CaseContractInfo info=caseContractInfoService.getContractByContractNo(contractNo);
                LoanApproveCondition condition=new LoanApproveCondition();
                condition.setApplyNo(info.getApplyNo());
                condition.setContractNo(info.getContractNo());
            }
        }else {
            log.info("{}已禁用，取消执行任务",caseConfParam.getParamExplain());
            return;
        }
    }

    @Override
    public Boolean checkEnableAutoReview(String contractNo){
        Boolean result=false;
        CaseContractInfo info=caseContractInfoService.getContractByContractNo(contractNo);
        if((ApplyStatusEnum.LOAN_WAIT_CONFIRM.getState().equals(info.getApplyStatus()))
                &&(!WhetherEnum.YES.getCode().equals(info.getNeedReview()))
                &&FlowNodeEnum.REVIEW.getCode().equals(info.getFlowNode())){
            result=true;
        }
        return result;
    }

    @Override
    public Boolean checkContractSuspend(String contractNo){
        CaseContractInfo info=caseContractInfoService.getContractByContractNo(contractNo);
        if(StringUtils.isNotBlank(info.getIsLock()) && StringUtils.isNotBlank(info.getLockType()) && info.getIsLock().equals(WhetherEnum.NO.getCode()) && info.getLockType().equals(LockTypeEnum.ARTIFICIAL.getState())){
            return true;
        } else {
            return this.setSuspend(info);
        }
    }

    @Override
    public Boolean checkContractInsuranceData(String contractNo){
        boolean result=true;
        List<CarInsuranceInfo> insuranceInfos=carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>lambdaQuery().eq(CarInsuranceInfo::getContractNo,contractNo));
        for (CarInsuranceInfo insuranceInfo:insuranceInfos){
            if(InsuranceModeEnum.PAPER.getCode().equals(insuranceInfo.getInsuranceMode())){
                Boolean isAfter = DateUtil.endOfDay(insuranceInfo.getInsuranceStartTime()).after(DateUtil.offsetDay(DateUtil.endOfDay(new Date()),1));
                if(isAfter){
                    result = false;
                    break;
                }
            }else {
                result=false;
                break;
            }
        }
        return result;
    }

    @Override
    public Boolean checkFileStatus(String contractNo){
        boolean result=true;
        List<ComAttachmentFile> comAttachmentFileList=new ArrayList<>();
        List<ComAttachmentManagement> comAttachmentManagementList=comAttachmentManagementService.getAllManagementInfo().stream().filter(m-> BusiNodeEnum.LOAN_APPLY.getCode().equals(m.getBusiNode())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(comAttachmentManagementList)){
            List<String> ids=comAttachmentManagementList.stream().map(m->String.valueOf(m.getId())).collect(Collectors.toList());
            comAttachmentFileList=comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getBusiNo, contractNo)
                    .eq(ComAttachmentFile::getBelongNo,contractNo)
                    .eq(ComAttachmentFile::getHistoryVersion,"0")
                    .in(ComAttachmentFile::getAttachmentCode,ids)
                    .eq(ComAttachmentFile::getFileSource,"com_attachment_management"));
        }
        if(comAttachmentFileList.size()>0){
            for (ComAttachmentFile file:comAttachmentFileList){
                if(file.getFileStatus().equals(FileStatusEnum.NOTSTANDARD.getCode()) || file.getFileStatus().equals(FileStatusEnum.WAITAPPROVE.getCode())){
                    result=false;
                    break;
                }
            }
        }else {
            result=false;
        }
        return result;
    }

    @Override
    public Boolean checkOverTime(String contractNo){
        Boolean result=true;
        CaseContractInfo contractInfo= caseContractInfoService.getContractByContractNo(contractNo);
        List<LoanOverTimeRule> loanOverTimeRuleList = loanOverTimeRuleService.list(Wrappers.<LoanOverTimeRule>lambdaQuery().eq(LoanOverTimeRule::getIsEnable, WhetherEnum.YES.getCode()));
        if (CollectionUtil.isNotEmpty(loanOverTimeRuleList)) {
            log.info("合同{}:超时效检测开始执行...", contractInfo.getContractNo() );
            JSONObject runParam = ruleAtomDataService.getCommonAtomRunParamsOverTime(contractInfo.getApplyNo());
            log.info("合同{}:超时效暂停规则报文：{}", contractInfo.getContractNo(), runParam);
            RuleRunResult ruleRunResult = RuleHelper.runRule(runParam, "overTimeRuleGroup", false, RuleRunEnum.SERIAL);
            if (ruleRunResult.getHit()) {
                String ruleRemarks = "案件已超审批时效;";
                if(StrUtil.isNotBlank(contractInfo.getRemarks())&&!contractInfo.getRemarks().contains("案件已超审批时效")){
                    ruleRemarks = contractInfo.getRemarks()+ruleRemarks;
                }
                contractInfo.setIsOvertime(WhetherEnum.YES.getCode());
                contractInfo.setPriority(casePriorityChangeService.priorityChange(contractInfo));
                contractInfo.setRemarks(ruleRemarks);
                caseContractInfoService.updateById(contractInfo);
                result = false;
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void normalLoanApprove(CaseContractInfo caseContractInfo,LoanApproveSubmitVO condition){
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());//放款待审核
        status.add(ApplyStatusEnum.LOAN_REPAIR.getState());//补件
        status.add(ApplyStatusEnum.LOAN_REVIEW_RETURN.getState());//复核退回
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());//暂停等待
        Assert.isTrue(checkApplyStatus(caseContractInfo,status),"合同审核状态异常!");
        /**初审提交至复审，案件优先级大于等于5，统一变成5，小于5则不变*/
        int priorityValue=Integer.valueOf(caseContractInfo.getPriority());
        if(priorityValue>=Integer.valueOf(PriorityEnum.ORDINARY.getCode())){
            caseContractInfo.setPriority(PriorityEnum.ORDINARY.getCode());
        }
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());//放款待审核
        log.info("*****************" + condition.getContractNo() + "放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_CONFIRM.getState() + "*****************");
        log.info("-------condition-------{}", condition);
        if (LoanProcessTypeEnum.AUTO_SUBMIT.getCode().equals(condition.getPassType())) {//区分是否自动审批
            caseContractInfo.setLoanAuditor(condition.getApproveRecord().getDisposeStaff());
        } else {
            caseContractInfo.setLoanAuditor(SecurityUtils.getUsername());
        }
        caseContractInfo.setFlowNode(FlowNodeEnum.REVIEW.getCode());
        caseContractInfoService.updateLoanSubmit(caseContractInfo);
        log.info("放款阶段合同号:{}开始向客户端{}发送websocket消息",caseContractInfo.getContractNo(),SecurityUtils.getUsername());
        WebSocketUtils.sendOneMessage(SecurityUtils.getUsername(),"");
        //工作流提交流程  保存操作记录
        final IResponse<Boolean> response = submitWorkflow(condition);
        log.info(">>>>>>>>>>>>>>>工作流提交流程--->>>>>>>", JSONObject.toJSONString(response));
        if (!CaseConstants.CODE_SUCCESS.equals(response.getCode())){
            // TODO 异常处理
            throw new RuntimeException("工作流异常 - 待处理");
        }

        //通知进件系统
        approveLoanInfoService.sendToApplyNotic(condition.getContractNo()
                , ApplyStatusEnum.LOAN_WAIT_CONFIRM);

    }

    /**
     * 修改合同状态表数据
     * @param caseContractInfo 合同信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void normalAutoLoanApprove(CaseContractInfo caseContractInfo){

        /**初审提交至复审，案件优先级大于等于5，统一变成5，小于5则不变*/
        int priorityValue=Integer.valueOf(caseContractInfo.getPriority());
        if(priorityValue>=Integer.valueOf(PriorityEnum.ORDINARY.getCode())){
            caseContractInfo.setPriority(PriorityEnum.ORDINARY.getCode());
        }
        //修改放款审核状态
        caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());//放款待审核
        log.info("*****************" + caseContractInfo.getContractNo() + "放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_CONFIRM.getState() + "*****************");
        caseContractInfo.setLoanAuditor("admin");
        caseContractInfoService.updateLoanSubmit(caseContractInfo);

    }

    public IResponse<Boolean> submitWorkflow(LoanApproveSubmitVO condition) {
        final CaseApproveRecord caseApproveRecord = condition.getApproveRecord();
        WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
            .eq(WorkflowTaskInfo::getBusinessNo, caseApproveRecord.getApplyNo())
            .eq(WorkflowTaskInfo::getTaskId, caseApproveRecord.getTaskId())
            .eq(WorkflowTaskInfo::getProcessInstanceId, caseApproveRecord.getStageId()));

        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
            .eq(CaseBaseInfo::getApplyNo, caseApproveRecord.getApplyNo()));

        final SubmitTaskParam submitTaskParam = approveTaskService.buildSubmitTaskParam(condition, caseApproveRecord, workflowTaskInfo);
        buildFlowVariable(condition.getContractNo(), workflowTaskInfo, caseBaseInfo, submitTaskParam);
        WorkflowTaskInfo lastWorkflowTaskInfo = workflowTaskInfoService.getOne(new LambdaQueryWrapper<WorkflowTaskInfo>().eq(WorkflowTaskInfo::getTaskId,workflowTaskInfo.getPreviousTaskId()).last("limit 1"));
        submitTaskParam.getExtendParams().put("lastLoanApprovalNode",lastWorkflowTaskInfo == null ? "" : lastWorkflowTaskInfo.getUserDefinedIndex());
        submitTaskParam.setPassType(condition.getPassType());
        submitTaskParam.setPassReason(condition.getPassReason());
        submitTaskParam.setContractNo(condition.getContractNo());
        submitTaskParam.setUseName(condition.getUseName());
        submitTaskParam.setTotalLoanAmt(caseBaseInfo.getTotalLoanAmt());
        final IResponse<Boolean> response = workflowHelper.submitTask(submitTaskParam, UseSceneEnum.GENERAL_LOAN);
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void fastLoanApprove(CaseContractInfo caseContractInfo,LoanApproveCondition condition){
        List<String> status=new ArrayList<>();
        status.add(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
        status.add(ApplyStatusEnum.SUSPEND_WAIT.getState());
        Assert.isTrue(checkApplyStatus(caseContractInfo,status),"合同审核状态异常!");
        //修改放款审核状态
        log.info("*****************" + condition.getApplyNo() + " 放款审核状态change:" + ApplyStatusEnum.LOAN_PRE_APPROVE.getState() + "*****************");
        caseContractInfoService.update(Wrappers.<CaseContractInfo>lambdaUpdate()
                .set(CaseContractInfo::getApplyStatus,ApplyStatusEnum.LOAN_PRE_APPROVE.getState())
                .set(CaseContractInfo::getFlowNode,FlowNodeEnum.AUTO.getCode())
                .eq(CaseContractInfo::getId,caseContractInfo.getId()));
        /**复审完成 添加合作商展业城市首单记录*/
        //复审完成  待激活池插入数据
        loanActivateService.addLoanActivateInfo(condition.getApplyNo());
        log.info("*****************复核完成待激活池插入待激活数据*****************");

        //工作流提交流程  保存操作记录
        condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
        condition.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.SUBMIT));
        condition.setFlowNode(FlowNodeEnum.REVIEW.getCode());
        loanWorkflowService.submitWorkflow(condition);

        //通知进件系统
        approveLoanInfoService.sendToApplyNotic(condition.getContractNo(), ApplyStatusEnum.LOAN_PRE_APPROVE);

        ThreadUtil.execute(()->{
            try {
                /**等待0.5秒执行*/
                TimeUnit.MILLISECONDS.sleep(500);
                doApproveDone(condition.getContractNo());
            }catch (Throwable e){
                log.error("复审通过后置事件完成,运行结果",e);
            }
        });
    }



    private void buildFlowVariable(String contractNo, WorkflowTaskInfo workflowTaskInfo, CaseBaseInfo caseBaseInfo,
        SubmitTaskParam submitTaskParam) {
        final UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>lambdaQuery()
            .eq(UserCollocation::getLoginName, workflowTaskInfo.getAssign())
        );

        final JSONObject extendParam = new JSONObject();
        extendParam.put(FlowConstant.CONTRACT_NO, contractNo);
        extendParam.put(FlowConstant.LOAN_AMT, caseBaseInfo.getLoanAmtRepeat());
        extendParam.put(FlowConstant.IS_REGULAR, userCollocation.getIsRegular());
        extendParam.put(FlowConstant.CREDIT_AMT_LIMIT, userCollocation.getApprovedAmount());
        submitTaskParam.setExtendParams(extendParam);

        workflowWrapperService.busiParamAdvice(submitTaskParam);
    }
}
