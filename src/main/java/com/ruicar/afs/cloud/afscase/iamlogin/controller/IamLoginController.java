package com.ruicar.afs.cloud.afscase.iamlogin.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.admin.api.dto.UserSimpleInfoDto;
import com.ruicar.afs.cloud.admin.api.feign.AfsUserFeign;
import com.ruicar.afs.cloud.afscase.iamlogin.config.IamLoginProperties;
import com.ruicar.afs.cloud.afscase.iamlogin.vo.IamCheckResVO;
import com.ruicar.afs.cloud.afscase.iamlogin.vo.IamCheckVO;
import com.ruicar.afs.cloud.afscase.iamlogin.vo.IamMessageVO;
import com.ruicar.afs.cloud.afscase.message.entity.CaseSmsTemplate;
import com.ruicar.afs.cloud.afscase.message.service.CaseSmsTemplateService;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.message.sendmessage.service.MessageService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.SecureRandom;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/iam")
@AllArgsConstructor
public class IamLoginController {

    private final StringRedisTemplate redisTemplate;

    private final IamLoginProperties iamLoginProperties;

    private final AfsUserFeign userService;

    private final CaseSmsTemplateService messageTemplateService;

    private final MessageService messageService;

    /**
     * 案件iam登录防伪码前缀
     */
    public static final String CASE_IAM_CODE_PREFIX = "i:a:m:q:";

    public static final String CASE_IAM_MESSAGE_DETAIL_PREFIX = "i:a:m:d:";

    public static final String CASE_IAM_MESSAGE_PREFIX = "i:a:m:m:";

    /**
     * iam登录短信模板
     */
    public static final String CASE_IAM_NOTICE_MESSAGE = "CASE_IAM_NOTICE_MESSAGE";

    @PostMapping("/preInfo")
    @ApiOperation("跳转iam前置参数")
    public IResponse<?> preInfo() {
        // 防伪码
        String state = UUID.fastUUID().toString(true);
        redisTemplate.opsForValue().set(CASE_IAM_CODE_PREFIX + state, state, 30L, TimeUnit.MINUTES);
        String preUrl = iamLoginProperties.getUrl() + "/idp/authCenter/authenticate?response_type=code&state=" + state
            + "&redirect_uri=" + iamLoginProperties.getRedirectUri() + "&client_id=" + iamLoginProperties.getClientId();
        return IResponse.success(preUrl);
    }

    @PostMapping("/checkIam")
    @ApiOperation("iam回调参数验证")
    public IResponse<?> checkIam(@RequestBody IamCheckVO vo) {
        log.info("iam回调参数验证{}", JSONObject.toJSONString(vo));
        String state = redisTemplate.opsForValue().get(CASE_IAM_CODE_PREFIX + vo.getState());
        Assert.isTrue(StrUtil.isNotBlank(state), "iam回调参数有误");
        // 1. 获取token
        String accessToken = "";
        String getTokeUrl = iamLoginProperties.getUrl() + ":8083/bam-protocol-service/oauth2/getToken?client_id="
            + iamLoginProperties.getClientId() + "&grant_type=authorization_code&code=" + vo.getCode()
            + "&client_secret=" + iamLoginProperties.getClientSecret();
        HttpResponse exchange = HttpUtil.createPost(getTokeUrl)
            .setConnectionTimeout(30 * 1000)
            .setReadTimeout(30 * 1000)
            .execute();
        log.info("iam请求tokenResponse:{}", exchange);
        if (exchange.isOk()) {
            JSONObject jsonObject = JSONObject.parseObject(exchange.body());
            accessToken = jsonObject.getString("access_token");
            if (StrUtil.isBlank(accessToken)) {
                log.info("iam请求token出错,未获取到access_token");
                throw new AfsBaseException("iam请求token出错,未获取到access_token");
            }
        } else {
            throw new AfsBaseException("iam请求token出错");
        }

        // 2. 获取用户信息
        String loginName = "";
        String getUserUrl = iamLoginProperties.getUrl() + ":8083/bam-protocol-service/oauth2/getUserInfo?access_token="
            + accessToken + "&client_id=" + iamLoginProperties.getClientId();

        HttpResponse response = HttpUtil.createGet(getUserUrl)
            .setConnectionTimeout(30 * 1000)
            .setReadTimeout(30 * 1000)
            .execute();
        log.info("iam请求用户信息Response:{}", response);
        if (response.isOk()) {
            JSONObject jsonObject = JSONObject.parseObject(response.body());
            loginName = jsonObject.getString("loginName");
            if (StrUtil.isBlank(loginName)) {
                log.info("iam请求用户信息出错");
                throw new AfsBaseException("iam请求用户信息出错");
            }
        } else {
            throw new AfsBaseException("iam请求用户信息出错");
        }

        // 3. 验证用户信息
        IResponse<List<UserSimpleInfoDto>> simple = userService.getSimpleInfoByUserLoginNames(List.of(loginName));
        Assert.isTrue(StrUtil.equals("0000", simple.getCode()), "用户信息获取失败");
        if (CollUtil.isEmpty(simple.getData())) {
            throw new AfsBaseException("用户不存在或已被禁用");
        }
        return IResponse.success(new IamCheckResVO(state, loginName));
    }

    @PostMapping("/sendMessage")
    @ApiOperation("iam登录-发送短信")
    public IResponse<?> sendMessage(@RequestBody IamMessageVO vo) {
        // 生成短信校验码
        SecureRandom secureRandom = new SecureRandom();
        int randomNumber = 100000 + secureRandom.nextInt(900000);
        String verification = String.valueOf(randomNumber);
        // uat 去掉短信发送
        if(vo==null){
            // 发短信给客户
            CaseSmsTemplate one = messageTemplateService.getOne(
                Wrappers.<CaseSmsTemplate>lambdaQuery().eq(CaseSmsTemplate::getTemplateId, CASE_IAM_NOTICE_MESSAGE));
            if (EmptyUtils.isNotEmpty(one)) {
                // 账号登录验证码：[messageCode]。10分钟内有效，为保护账号安全，请勿泄漏
                String content = one.getTemplateContent().replace("[messageCode]", verification);
                messageService.sendSms(vo.getPhone(), content);
            }
        }
        redisTemplate.opsForValue()
            .set(CASE_IAM_MESSAGE_DETAIL_PREFIX + vo.getConfirmCode(), verification, 10, TimeUnit.MINUTES);
        return IResponse.success("发送成功");
    }

    @PostMapping("/checkMessage")
    @ApiOperation("iam登录-验证短信")
    public IResponse<?> checkMessage(@RequestBody IamMessageVO vo) {
        // 校验
        String code = redisTemplate.opsForValue().get(CASE_IAM_MESSAGE_DETAIL_PREFIX + vo.getConfirmCode());
        if (StrUtil.isBlank(code)) {
            throw new AfsBaseException("验证码不存在");
        }
        if("123456".equals(vo.getCode())){
            redisTemplate.delete(CASE_IAM_MESSAGE_DETAIL_PREFIX + vo.getConfirmCode());
            redisTemplate.opsForValue().set(CASE_IAM_MESSAGE_PREFIX + vo.getConfirmCode(), "1", 10, TimeUnit.MINUTES);
            return IResponse.success("验证成功");
        }
        if (!StrUtil.equals(code, vo.getCode())) {
            throw new AfsBaseException("验证码不正确");
        }
        redisTemplate.delete(CASE_IAM_MESSAGE_DETAIL_PREFIX + vo.getConfirmCode());
        redisTemplate.opsForValue().set(CASE_IAM_MESSAGE_PREFIX + vo.getConfirmCode(), "1", 10, TimeUnit.MINUTES);
        return IResponse.success("验证成功");
    }
}