package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffClearInfo;

import javax.servlet.http.HttpServletResponse;

public interface WriteOffClearInfoService extends IService<WriteOffClearInfo> {

    /**
     * 导出核销项清账明细
     * @param condition
     * @param response
     */
    void exportData(WriteOffClearInfo condition, HttpServletResponse response);

    /**
     * 服务费清账定时任务
     * @param param
     */
    void writeOffClearJob(String param);
}
