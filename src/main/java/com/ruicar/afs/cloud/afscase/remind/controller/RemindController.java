package com.ruicar.afs.cloud.afscase.remind.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPool;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveInformInfoService;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindCondition;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindCountCondition;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindData;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindPageCondition;
import com.ruicar.afs.cloud.afscase.remind.entity.ApplyRemindDetails;
import com.ruicar.afs.cloud.afscase.remind.entity.CaseRemindDetail;
import com.ruicar.afs.cloud.afscase.remind.feign.ApplyAdminFeign;
import com.ruicar.afs.cloud.afscase.remind.feign.ApplyRemindFeign;
import com.ruicar.afs.cloud.afscase.remind.service.RemindService;
import com.ruicar.afs.cloud.afscase.remind.vo.RemindVo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CancelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemainKeyEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindOperateTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindPowerEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.websocket.utils.WebSocketUtils;
import com.ruicar.afs.cloud.wx.message.service.WxMessageService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum.APPROVE;
import static com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum.GENERAL_LOAN;
import static com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum.LOAN_DATA_POST_NEW;


/**
 * @Description 留言、提醒
 * <AUTHOR>
 * @Date 2020/05/30 10:31
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/remind")
public class RemindController {

    private RemindService remindService;

    private CaseApproveRecordService caseApproveRecordService;

    private WorkTaskPoolService workTaskPoolService;

    private ApproveInformInfoService approveInformInfoService;

    private CaseBaseInfoService caseBaseInfoService;

    private WorkProcessScheduleInfoService workProcessScheduleInfoService;

    private WorkflowTaskInfoService workflowTaskInfoService;

    private ApplyRemindFeign remindFeign;

    private final WxMessageService wxMessageService;

    private final ApplyAdminFeign adminFeign;

    /**
     * @Description 归档保存留言
     * <AUTHOR>
     * @Date 2020/6/1 10:33
     */
    @PostMapping("/archiveRemind")
    @Transactional(rollbackFor = Exception.class)
    public IResponse archiveRemind(@RequestBody RemindData remindData) {
        AfsUser user = SecurityUtils.getUser();

        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        String once = uuid + System.currentTimeMillis();

        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, remindData.getRecord().getApplyNo()));
        String caseStatus = caseBaseInfo.getBusinessStateIn();
        //审批人员打开案件 此时案件已经回收
        if (AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(caseStatus)
                || AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGNING).equals(caseStatus)) {
            throw new AfsBaseException("该案件已不在当前队列，无法操作!");
        }
        remindData.getRemind().setDisposeUser(user.getUserRealName());
        remindData.getRemind().setDisposeUserId(user.getId().toString());
        remindData.getRemind().setStatus(CancelStatusEnum.EFFECTIVE.getCode());
        remindData.getRemind().setDisposeTime(new Date());
        remindData.getRemind().setUseScene(remindData.getRecord().getUseScene());
        remindData.getRemind().setOperationType(remindData.getRemindOprType());
        remindData.getRemind().setMindId(once);
        // 内部留言默认 经销商不可见
        if (RemindTypeEnum.INNER.getValue().equals(remindData.getRemind().getRemindType())) {
            remindData.getRemind().setRemindPowers(RemindPowerEnum.UN_VISIBLE.getValue());
        } else {
            remindData.getRemind().setRemindPowers(RemindPowerEnum.VISIBLE.getValue());
        }

        remindService.save(remindData.getRemind());
        return IResponse.success("ok");
    }

    /**
     * @Description 保存留言
     * <AUTHOR>
     * @Date 2020/6/1 10:33
     */
    @PostMapping("/remind")
    @Transactional(rollbackFor = Exception.class)
    public IResponse createRemind(@RequestBody RemindData remindData) {
        AfsUser user = SecurityUtils.getUser();

        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        String once = uuid + System.currentTimeMillis();

        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, remindData.getRecord().getApplyNo()));
        String caseStatus = caseBaseInfo.getBusinessStateIn();
        //审批人员打开案件 此时案件已经回收
        if (AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(caseStatus)
                || AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGNING).equals(caseStatus)) {
            throw new AfsBaseException("该案件已不在当前队列，无法操作!");
        }

        //当前登陆用户
        String useName = SecurityUtils.getUsername();
        WorkflowTaskInfo workTaskPool = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo, remindData.getRecord().getApplyNo())
                .eq(WorkflowTaskInfo::getStatus, FlowStatusEnum.ACTIVE.getCode())
                .eq(WorkflowTaskInfo::getProcessInstanceId, remindData.getRecord().getStageId()));
        if (ObjectUtils.isNotEmpty(workTaskPool)) {
            if (useName.equals(workTaskPool.getAssign()) || AfsEnumUtil.key(YesOrNoEnum.yes).equals(remindData.getRecord().getDealWithFlag())) {
                // 1.留言记录信息
                remindData.getRemind().setDisposeUser(user.getUserRealName());
                remindData.getRemind().setDisposeUserId(user.getId().toString());
                remindData.getRemind().setStatus(CancelStatusEnum.EFFECTIVE.getCode());
                remindData.getRemind().setDisposeTime(new Date());
                remindData.getRemind().setUseScene(remindData.getRecord().getUseScene());
                remindData.getRemind().setOperationType(remindData.getRemindOprType());
                remindData.getRemind().setMindId(once);
                // 内部留言默认 经销商不可见
                if (RemindTypeEnum.INNER.getValue().equals(remindData.getRemind().getRemindType())) {
                    remindData.getRemind().setRemindPowers(RemindPowerEnum.UN_VISIBLE.getValue());
                } else {
                    remindData.getRemind().setRemindPowers(RemindPowerEnum.VISIBLE.getValue());
                }

                // 公共日志参数
                remindData.getRecord().setApplyNo(remindData.getRemind().getApplyNo());
                remindData.getRecord().setUseScene(UseSceneEnum.APPROVE.getValue());
                remindData.getRecord().setDisposeStaff(user.getUserRealName());
                remindData.getRecord().setApproveEndTime(new Date());

                // 2.留言日志
                CaseApproveRecord remindRecord = new CaseApproveRecord();
                BeanUtil.copyProperties(remindData.getRecord(), remindRecord);

                remindRecord.setApproveType(ApproveTypeEnum.REMIND.getValue());
                remindRecord.setApproveSuggestName(RemindTypeEnum.getNameByValue(remindData.getRemind().getRemindType()));
                remindRecord.setApproveSuggest(remindData.getRemind().getRemindType());
                remindRecord.setApproveReason(remindData.getRemind().getRemindReason());
                remindRecord.setApproveRemark(remindData.getRemind().getRemindContent());
                remindRecord.setMindId(once);


                if (RemindOperateTypeEnum.REJECT.getValue().equals(remindData.getRemindOprType())
                        || RemindOperateTypeEnum.PARSE.getValue().equals(remindData.getRemindOprType())) {
                    // 暂停时，保留暂停前案件状态
                    if (RemindOperateTypeEnum.PARSE.getValue().equals(remindData.getRemindOprType())) {
                        caseBaseInfo.setPauseStatus(caseStatus);
                        caseBaseInfoService.updateById(caseBaseInfo);
                    }
                    WorkTaskPool taskPool = workTaskPoolService.getOne(
                            Wrappers.<WorkTaskPool>lambdaQuery().eq(WorkTaskPool::getStageId, remindData.getRecord().getStageId()));

                    remindData.getRecord().setApproveStartTime(taskPool.getStartTime());

                    remindRecord.setDisposeNode(taskPool.getTaskNodeId());
                    remindRecord.setDisposeNodeName(taskPool.getTaskNodeName());
                    remindRecord.setApproveStartTime(taskPool.getStartTime());

                    // 3.流程日志
                    CaseApproveRecord approveRecord = new CaseApproveRecord();
                    BeanUtil.copyProperties(remindData.getRecord(), approveRecord);
                    approveRecord.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                    approveRecord.setApproveSuggestName(RemindOperateTypeEnum.getNameByValue(remindData.getRemindOprType()));
                    approveRecord.setApproveSuggest(remindData.getRemindOprType());
                    approveRecord.setApproveRemark(remindData.getRecord().getApproveRemark());
                    approveRecord.setApproveStartTime(approveRecord.getApproveStartTime());

                    if (RemindOperateTypeEnum.REJECT.getValue().equals(remindData.getRemindOprType())) {
                        approveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SEND_BACK));
                        approveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SEND_BACK));
                        CaseApproveRecord noticeRecord = new CaseApproveRecord();
                        BeanUtil.copyProperties(approveRecord, noticeRecord);
                        CaseApproveRecord flowRecord = approveRecord;
                        if (JSON.isValid(taskPool.getRemainData())) {
                            JSONObject remainData = JSONObject.parseObject(taskPool.getRemainData());
                            String remainRecordKey = AfsEnumUtil.key(RemainKeyEnum.RECORD);
                            if (remainData.containsKey(remainRecordKey) && JSON.isValid(remainData.getString(remainRecordKey))) {
                                flowRecord = remainData.getJSONObject(remainRecordKey).toJavaObject(CaseApproveRecord.class);
                                flowRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SEND_BACK));
                                flowRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SEND_BACK));
                                flowRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
                                flowRecord.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                            }
                        }
                        //内部留言 不退送进件端
                        if (!RemindTypeEnum.INNER.getValue().equals(remindData.getRemind().getRemindType())) {
                            noticeRecord.setApproveMessage(remindData.getRemind().getRemindContent());
                            noticeRecord.setApproveReason(remindData.getRemind().getRemindType());
                            noticeRecord.setApproveRemark(remindData.getRemind().getRemindReason());
                        }
                    } else {
                        approveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.PARSE));
                        approveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.PARSE));
                    }
                } else if (RemindPowerEnum.VISIBLE.getValue().equals(remindData.getRemind().getRemindPowers())) {
                    //经我处理任务池中，业务状态为修订-暂停的案件，追加留言通知进件端
                    if (AfsEnumUtil.key(BusinessStateInEnum.REVISE_PARSE).equals(remindData.getRecord().getBusinessStateIn())) {
                        approveInformInfoService.submitDealWithMessage(remindData.getRemind());
                    } else {
                        // 通知进件系统
                        approveInformInfoService.submitLeaveMessage(remindData.getRemind());
                    }
                }
                // 内部留言，留言内容为空 不做处理
                if (RemindTypeEnum.INNER.getValue().equals(remindData.getRemind().getRemindType())
                        && StrUtil.isBlank(remindData.getRemind().getRemindContent())) {
                } else {
                    caseApproveRecordService.save(remindRecord);
                }
                remindService.save(remindData.getRemind());
                return IResponse.success("ok");
            } else {
                return new IResponse().setCode("0001").setMsg("该案件已不在当前队列，无法操作");
            }
        } else {
            return IResponse.success("");
        }
    }

    /**
     * @Description 保存留言
     * 综合查询——》案件查询特殊处理
     */
    @PostMapping("/specialRemind")
    @Transactional(rollbackFor = Exception.class)
    public IResponse specialRemind(@RequestBody RemindData remindData) {
        AfsUser user = SecurityUtils.getUser();

        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        String once = uuid + System.currentTimeMillis();

        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, remindData.getRecord().getApplyNo()));
        String caseStatus = caseBaseInfo.getBusinessStateIn();
        // 审批人员打开案件 此时案件已经回收
        if (AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(caseStatus)
                || AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGNING).equals(caseStatus)) {
            throw new AfsBaseException("该案件已不在当前队列，无法操作!");
        }
        remindData.getRemind().setDisposeUser(user.getUserRealName());
        remindData.getRemind().setDisposeUserId(user.getId().toString());
        remindData.getRemind().setStatus(CancelStatusEnum.EFFECTIVE.getCode());
        remindData.getRemind().setDisposeTime(new Date());
        remindData.getRemind().setUseScene(remindData.getRecord().getUseScene());
        remindData.getRemind().setOperationType(remindData.getRemindOprType());
        remindData.getRemind().setMindId(once);
        // 内部留言默认 经销商不可见
        if (RemindTypeEnum.INNER.getValue().equals(remindData.getRemind().getRemindType())) {
            remindData.getRemind().setRemindPowers(RemindPowerEnum.UN_VISIBLE.getValue());
        } else {
            remindData.getRemind().setRemindPowers(RemindPowerEnum.VISIBLE.getValue());
        }
        remindService.save(remindData.getRemind());
        if (RemindPowerEnum.VISIBLE.getValue().equals(remindData.getRemind().getRemindPowers())) {
            // 通知进件系统
            approveInformInfoService.submitLeaveMessage(remindData.getRemind());
        }
        return IResponse.success("ok");
    }

    /**
     * queryRemindListByApplyNo
     * <p>Description: 根据条件查询留言/提醒列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryRemindListByApplyNo")
    @ApiOperation(value = "根据条件查询留言/提醒列表")
    public IResponse<List<RemindVo>> queryRemindListByApplyNo(@ModelAttribute RemindCondition remindCondition) {
        List<RemindVo> remindVoList = new ArrayList<>();
        List<CaseRemindDetail> caseRemindDetailList = remindService.list(Wrappers.<CaseRemindDetail>query().lambda()
                .eq(ObjectUtils.isNotEmpty(remindCondition.getApplyNo()), CaseRemindDetail::getApplyNo, remindCondition.getApplyNo())
                .eq(CaseRemindDetail::getStatus, CancelStatusEnum.EFFECTIVE.getCode())
                .in(ObjectUtils.isNotEmpty(remindCondition.getUseScene()), CaseRemindDetail::getUseScene, GENERAL_LOAN.getValue(), APPROVE.getValue(), LOAN_DATA_POST_NEW.getValue())
        );
        boolean caseNormalFlowEnd = caseBaseInfoService.checkNormalFlowEnd(remindCondition.getApplyNo(), remindCondition.getStageId());
        if (CollectionUtils.isNotEmpty(caseRemindDetailList)) {
            // 过滤掉内部留言且内容为空的留言数据
            caseRemindDetailList = caseRemindDetailList.stream()
                    .filter(remind ->
                            !(RemindTypeEnum.INNER.getValue().equals(remind.getRemindType())
                                    && StrUtil.isBlank(remind.getRemindContent())))
                    .collect(Collectors.toList());
            caseRemindDetailList.forEach(caseRemindDetail -> {
                RemindVo remindVo = new RemindVo();
                BeanUtils.copyProperties(caseRemindDetail, remindVo);
                /**  撤销操作权限  **/
                if (caseNormalFlowEnd) { // 审批结束后不允许撤销
                    remindVo.setCancel(false);
                } else {
                    String id = SecurityUtils.getUser().getId().toString();
                    if (id.equals(remindVo.getDisposeUserId())) {
                        remindVo.setCancel(true);
                    } else {
                        remindVo.setCancel(false);
                    }
                }
                remindVoList.add(remindVo);
            });
        }
        return IResponse.success(remindVoList);
    }

    /**
     * cancelRemindtById
     * <p>Description: 撤销留言</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/cancelRemindtById")
    @ApiOperation(value = "撤销留言")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<List<RemindVo>> cancelRemindtById(@RequestBody RemindCondition remindCondition) {
        //删除日志表中的留言记录
        CaseRemindDetail remind = remindService.getOne(Wrappers.<CaseRemindDetail>lambdaQuery()
                .eq(CaseRemindDetail::getId, remindCondition.getRemindId()));
        CaseApproveRecord caseApproveRecord = caseApproveRecordService.getOne(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getMindId, remind.getMindId()));
        if (ObjectUtils.isNotEmpty(caseApproveRecord)) {
            caseApproveRecordService.removeById(caseApproveRecord);
        }
        //撤销留言表的留言
        if (!StringUtils.isEmpty(remindCondition.getRemindId())) {
            remindService.update(Wrappers.<CaseRemindDetail>update().lambda()
                    .eq(CaseRemindDetail::getId, remindCondition.getRemindId())
                    .set(CaseRemindDetail::getStatus, CancelStatusEnum.CANCELED.getCode())
            );
        }
        return IResponse.success("success");
    }


    /**
     * cancelRemindtById
     * <p>Description: 修改留言</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/modifyRemindtById")
    @ApiOperation(value = "修改留言")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<List<RemindVo>> modifyRemindtById(@RequestBody RemindData remindData) {
        //修改日志表中的留言记录
        CaseRemindDetail remind = remindService.getOne(Wrappers.<CaseRemindDetail>lambdaQuery()
                .eq(CaseRemindDetail::getId, remindData.getRemind().getId()));
        remind.setRemindContent(remindData.getRemind().getRemindContent());
        remind.setDisposeTime(remindData.getRemind().getDisposeTime());
        if (ObjectUtils.isNotEmpty(remind)) {
            if (!remind.getDisposeUserId().equals(remindData.getRemind().getDisposeUserId())) {
                return IResponse.fail("修改失败,请找添加该条留言人员进行修改");
            }
            remindService.updateById(remind);
            return IResponse.success("修改成功");
        } else {
            return IResponse.fail("修改失败");
        }

    }


    @PostMapping("/queryCode")
    public IResponse<WorkProcessScheduleInfo> queryCode(@RequestBody RemindCondition remindCondition) {
        WorkProcessScheduleInfo info = workProcessScheduleInfoService.getOne(Wrappers.<WorkProcessScheduleInfo>lambdaQuery()
                .eq(WorkProcessScheduleInfo::getApplyNo, remindCondition.getApplyNo())
                .eq(WorkProcessScheduleInfo::getId, remindCondition.getStageId()));
        return IResponse.success(info);
    }

    @PostMapping("/saveRemind")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveRemind(@RequestBody ApplyRemindDetails applyRemindDetails) {
        if(EmptyUtils.isNotEmpty(applyRemindDetails)){
            remindService.saveRemind(applyRemindDetails);
        }
        return IResponse.success("同步成功");
    }

    /**
     * 查询所有的留言信息
     *
     * @return
     */
    @PostMapping(value = "/queryAllRemindList")
    @ApiOperation(value = "查询所有的留言信息")
    public IResponse<IPage<RemindVo>> queryAllRemindList(@ModelAttribute RemindPageCondition condition) {

        Integer pageSize = condition.getPageSize();
        Integer pageNumber = condition.getPageNumber();
        String applyNo = condition.getApplyNo();
        String remindType = condition.getRemindType();

        return remindService.queryAllRemindList(new Page(pageNumber, pageSize), applyNo, remindType);

    }


    @PostMapping(value = "/queryUnreadCount/{userName}")
    @ApiOperation(value = "查询所有的留言信息")
    public IResponse queryUnreadCount(@PathVariable("userName") String userName) {
        Map<String, Long> filteredMap = new HashMap<>();
        if(EmptyUtils.isNotEmpty(userName)){
            Map<String, Long> hashMap = remindService.queryUnreadCount(userName);
            filteredMap = hashMap.entrySet().stream()
                    .filter(entry -> entry.getValue() != 0)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        }
        return IResponse.success(filteredMap);
    }

        @PostMapping("/markAsRead")
        @Transactional(rollbackFor = Exception.class)
        public IResponse markReminderAsRead(@RequestBody RemindCountCondition condition) {
            log.info("markAsRead:{}", JSONObject.toJSONString(condition));
            remindService.update(Wrappers.<CaseRemindDetail>update().lambda()
                    .eq(EmptyUtils.isNotEmpty(condition.getApplyNo()),CaseRemindDetail::getApplyNo, condition.getApplyNo())
                    .eq(Optional.of(condition.getRemindId()).filter(id -> id != 0).isPresent(),CaseRemindDetail::getId, condition.getRemindId())
                    .set(CaseRemindDetail::getReadFlag, "read")
            );
            // 发送消息
            WebSocketUtils.sendOneMessage(condition.getUserName(),"");
            return IResponse.success("操作成功");
        }



 }


