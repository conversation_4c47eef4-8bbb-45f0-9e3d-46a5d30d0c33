package com.ruicar.afs.cloud.afscase.workflow.event;

import com.alibaba.fastjson.JSONObject;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.entity.AffiliatedCompanyRule;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.service.AffiliatedCompanyRuleService;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.enums.FilingStatusEnum;
import com.ruicar.afs.cloud.afscase.mq.service.AffiliatedCompanyFilingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component
public class AffiliatedFilingEventListener implements ApplicationListener<AffiliatedFilingEvent> {

    private AffiliatedCompanyFilingService affiliatedCompanyFilingService;
    private CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private AffiliatedCompanyRuleService affiliatedCompanyRuleService;

    /**
     * 推送流程进度到业务系统
     *
     * @param event
     */
    @Override
    public void onApplicationEvent(AffiliatedFilingEvent event) {
        Object o = JSONObject.toJSON(event);
        log.info("内容"+o);
        AffiliatedCompanyFiling affiliatedCompanyFiling = affiliatedCompanyFilingService.getById(event.getApplyNo());
        switch(event.getNormalSubmitType()){
            case SEND_BACK_TO_DEALER:
                affiliatedCompanyFiling.setStatus(FilingStatusEnum.SUSPEND.key());
                affiliatedCompanyFilingService.updateById(affiliatedCompanyFiling);
                caseUseApplyServiceFeign.updateFilingStatus(String.valueOf(affiliatedCompanyFiling.getId()),FilingStatusEnum.SUSPEND.key());
                break;
            case SUGGEST_REJECT_FINAL:
                affiliatedCompanyFiling.setStatus(FilingStatusEnum.REFUSE.key());
                affiliatedCompanyFilingService.updateById(affiliatedCompanyFiling);
                caseUseApplyServiceFeign.updateFilingStatus(String.valueOf(affiliatedCompanyFiling.getId()),FilingStatusEnum.REFUSE.key());
                break;
            case SUGGEST_CHECK_FINAL:
                affiliatedCompanyFiling.setStatus(FilingStatusEnum.PASS.key());
                affiliatedCompanyFilingService.updateById(affiliatedCompanyFiling);
                AffiliatedCompanyRule affiliatedCompanyRule = new AffiliatedCompanyRule();
                affiliatedCompanyRule.setChannelName(affiliatedCompanyFiling.getChannelName());
                affiliatedCompanyRule.setAffiliatedName(affiliatedCompanyFiling.getAffiliatedName());
                affiliatedCompanyRule.setSocUniCrtCode(affiliatedCompanyFiling.getSocUniCrtCode());
                affiliatedCompanyRule.setRuleState("1");
                affiliatedCompanyRuleService.save(affiliatedCompanyRule);
                caseUseApplyServiceFeign.updateFilingStatus(String.valueOf(affiliatedCompanyFiling.getId()),FilingStatusEnum.PASS.key());
                break;
            default:
                break;
        }
    }
}
