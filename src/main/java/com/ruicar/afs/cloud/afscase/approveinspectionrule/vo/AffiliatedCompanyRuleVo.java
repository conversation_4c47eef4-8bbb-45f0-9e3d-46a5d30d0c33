package com.ruicar.afs.cloud.afscase.approveinspectionrule.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class AffiliatedCompanyRuleVo {

    @ExcelProperty(value = "经销商省份", index = 0)
    private String channelProvince;

    @ExcelProperty(value = "经销商城市", index = 1)
    private String channelCity;

    @ExcelProperty(value = "经销商名称", index = 2)
    private String channelName;

    @ExcelProperty(value = "挂靠公司名称", index = 3)
    private String affiliatedName;

    @ExcelProperty(value = "统一社会信用代码", index = 4)
    private String socUniCrtCode;

    @ExcelProperty(value = "产品名称", index = 5)
    private String productName;

    @ExcelProperty(value = "挂靠台数", index = 6)
    private String attachmentNum;

    @ExcelProperty(value = "融资金额", index = 7)
    private BigDecimal financingAmount;

    @ExcelProperty(value = "规则状态", index = 8)
    private String ruleState;

}
