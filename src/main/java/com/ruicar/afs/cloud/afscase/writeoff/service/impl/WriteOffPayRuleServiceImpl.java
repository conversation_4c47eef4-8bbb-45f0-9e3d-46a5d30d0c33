package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBatchInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffChannelGroupDetail;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRule;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRuleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffPayRuleMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBatchInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffChannelGroupDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffContractDetailManageService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleService;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleResult;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务费分期支付规则
 */
@Slf4j
@AllArgsConstructor
@Service
public class WriteOffPayRuleServiceImpl extends ServiceImpl<WriteOffPayRuleMapper, WriteOffPayRule> implements WriteOffPayRuleService {
    private final WriteOffPayRecordService writeOffPayRecordService;
    private final WriteOffPayRuleDetailService writeOffPayRuleDetailService;
    private final WriteOffBatchInfoService writeOffBatchInfoService;
    private final WriteOffChannelGroupDetailService writeOffChannelGroupDetailService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final WriteOffContractDetailManageService writeOffContractDetailManageService;

    /**
     * 生成服务费支付账单
     *
     * @param batchNo
     * @param baseInfoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPayBill(String batchNo, List<WriteOffBaseInfo> baseInfoList) {
        WriteOffBatchInfo batchInfo = new WriteOffBatchInfo();
        try {
            batchInfo = writeOffBatchInfoService.getOne(Wrappers.<WriteOffBatchInfo>lambdaQuery().eq(WriteOffBatchInfo::getBatchNo, batchNo));
            //服务费经销商分组信息
            List<WriteOffChannelGroupDetail> groupDetailList = writeOffChannelGroupDetailService.list(Wrappers.<WriteOffChannelGroupDetail>lambdaQuery()
                    .select(WriteOffChannelGroupDetail::getGroupId, WriteOffChannelGroupDetail::getGroupType, WriteOffChannelGroupDetail::getChannelCode)
                    .eq(WriteOffChannelGroupDetail::getStatus, StatusEnum.YES.getCode()));
            Map<String, List<WriteOffChannelGroupDetail>> channelGroupMap = groupDetailList.stream().collect(Collectors.groupingBy(WriteOffChannelGroupDetail::getChannelCode));
            JSONObject jsonObject = this.packageRuleAtom(batchInfo.getCaseNo(), channelGroupMap);
            log.info("批次号：{},服务费付款规则报文：{}", batchNo, jsonObject);
            RuleRunResult ruleRunResult = RuleHelper.runByGroup(jsonObject, "WriteOffPayRuleAtom", false, RuleRunEnum.PARALLEL, false);
            boolean isHit = ruleRunResult.getHit();
            log.info("服务费付款规则组：{}=>执行结束，结果：{}", "WriteOffPayRuleAtom", isHit);
            Assert.isTrue(isHit, "所有付款规则都未命中");
            List<RuleResult> ruleResults = ruleRunResult.getResults();
            Assert.isTrue(ruleResults.size() > 0, "所有付款规则都未命中");
            List<Long> ruleNoList = ruleResults.stream().map(k -> Long.valueOf(k.getRuleNo())).collect(Collectors.toList());
            log.info("批次号：{}，服务费付款命中规则ruleNos：{}", batchNo, ruleNoList);
            Assert.isTrue(ruleNoList.size() == 1, "付款规则命中多条规则");
            WriteOffPayRule payRule = this.getById(ruleNoList.get(0));
            List<WriteOffPayRuleDetail> detailList = writeOffPayRuleDetailService.list(Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                    .eq(WriteOffPayRuleDetail::getPayRuleId, payRule.getId())
                    .orderByAsc(WriteOffPayRuleDetail::getTermNo));
            //计算服务费每期付款金额
            BigDecimal totalAmount = batchInfo.getBatchAmount();
            BigDecimal sum = BigDecimal.ZERO;
            List<WriteOffPayRecord> recordList = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            //取最后一月的核销期数
            String[] split = batchInfo.getWriteOffMonth().split(",");
            Arrays.sort(split);
            DateTime beginOfMonth = DateUtil.beginOfMonth(dateFormat.parse(split[split.length - 1]));
            Map<Long, BigDecimal> map = new HashMap<>();
            for (int i = 0; i < detailList.size(); i++) {
                WriteOffPayRuleDetail ruleDetail = detailList.get(i);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(beginOfMonth);
                calendar.add(Calendar.MONTH, ruleDetail.getDelayMonth());
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                WriteOffPayRecord record = new WriteOffPayRecord();
                StringBuilder basePeriodAmount = new StringBuilder();
                if (i == detailList.size() - 1) {
                    //最后一期
                    record.setPayAmount(totalAmount.subtract(sum));
                    for (int k = 0; k < baseInfoList.size(); k++) {
                        WriteOffBaseInfo baseInfo = baseInfoList.get(k);
                        BigDecimal periodAmt = map.get(baseInfo.getId()) == null ? baseInfo.getInvoiceAmount() : baseInfo.getInvoiceAmount().subtract(map.get(baseInfo.getId()));
                        if (k == baseInfoList.size() - 1) {
                            basePeriodAmount.append(baseInfo.getApplyNo()).append(":");
                            basePeriodAmount.append(periodAmt);
                        } else {
                            basePeriodAmount.append(baseInfo.getApplyNo()).append(":");
                            basePeriodAmount.append(periodAmt);
                            basePeriodAmount.append(";");
                        }
                    }
                } else {
                    record.setPayAmount(totalAmount.multiply(ruleDetail.getPayRate()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                    sum = sum.add(record.getPayAmount());
                    for (int k = 0; k < baseInfoList.size(); k++) {
                        WriteOffBaseInfo baseInfo = baseInfoList.get(k);
                        BigDecimal periodAmt = baseInfo.getInvoiceAmount().multiply(ruleDetail.getPayRate()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                        if (k == baseInfoList.size() - 1) {
                            basePeriodAmount.append(baseInfo.getApplyNo()).append(":");
                            basePeriodAmount.append(periodAmt);
                        } else {
                            basePeriodAmount.append(baseInfo.getApplyNo()).append(":");
                            basePeriodAmount.append(periodAmt);
                            basePeriodAmount.append(";");
                        }
                        if (map.containsKey(baseInfo.getId())) {
                            map.put(baseInfo.getId(), map.get(baseInfo.getId()).add(periodAmt));
                        } else {
                            map.put(baseInfo.getId(), periodAmt);
                        }
                    }
                }
                record.setBatchNo(batchNo);
                record.setWriteOffMonth(batchInfo.getWriteOffMonth());
                record.setCaseNo(batchInfo.getCaseNo());
                record.setStatus(ChannelServiceFeeEnum.STATUS_0.code);
                record.setBatchAmount(totalAmount);
                record.setChannelCode(batchInfo.getChannelCode());
                record.setChannelFullName(batchInfo.getChannelFullName());
                record.setPayRate(ruleDetail.getPayRate());
                record.setTermNo(ruleDetail.getTermNo());
                record.setWaitPayTime(calendar.getTime());
                record.setBasePeriodAmt(basePeriodAmount.toString());
                record.setCbsRefNbr(null);
                record.setPayStatus(null);
                record.setErrorMsg(null);
                recordList.add(record);
            }
            writeOffPayRecordService.remove(Wrappers.<WriteOffPayRecord>lambdaQuery().eq(WriteOffPayRecord::getBatchNo, batchNo));
            writeOffPayRecordService.saveBatch(recordList);
            log.error("生成服务费付款账单成功,批次号:{}", batchNo);
        } catch (Exception e) {
            WriteOffPayRecord errorRecord = new WriteOffPayRecord();
            errorRecord.setBatchNo(batchNo);
            errorRecord.setChannelCode(batchInfo.getChannelCode());
            errorRecord.setChannelFullName(batchInfo.getChannelFullName());
            errorRecord.setCaseNo(batchInfo.getCaseNo());
            errorRecord.setStatus(ChannelServiceFeeEnum.STATUS_4.code);
            errorRecord.setErrorMsg(e.getMessage());
            writeOffPayRecordService.save(errorRecord);
            log.error("生成服务费付款账单失败,批次号:{},异常:{}", batchNo, e);
        }
    }

    /**
     * 组装服务费分期支付规则原子
     * @param caseNo
     * @param channelGroupMap
     * @return
     */
    private JSONObject packageRuleAtom(String caseNo, Map<String, List<WriteOffChannelGroupDetail>> channelGroupMap) {
        List<WriteOffBaseInfo> baseInfoList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .select(WriteOffBaseInfo::getApplyNo)
                .eq(WriteOffBaseInfo::getCaseNo, caseNo));
        Assert.isTrue(baseInfoList.size() > 0, "数据异常，核销项数据为空");
        String hxxNo = baseInfoList.get(0).getApplyNo();
        WriteOffContractDetailManage detail = writeOffContractDetailManageService.getOne(Wrappers.<WriteOffContractDetailManage>lambdaQuery()
                .eq(WriteOffContractDetailManage::getBaseInfoApply, hxxNo).last("limit 1"));
        Assert.isTrue(detail != null, "核销项" + hxxNo + "的合同数据为空");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("channelInfo.channelCity", detail.getChannelCity());
        jsonObject.put("channelProvince", detail.getChannelProvince());
        jsonObject.put("channelGrade", detail.getQualityGrade());
        jsonObject.put("channelCode", detail.getChannelCode());
        jsonObject.put("channelBelong", detail.getChannelBelong());
        List<WriteOffChannelGroupDetail> groupDetails = channelGroupMap.get(detail.getChannelCode());
        if (groupDetails != null) {
            for (WriteOffChannelGroupDetail groupDetail : groupDetails) {
                jsonObject.put(groupDetail.getGroupType(), groupDetail.getGroupId().toString());
            }
        }
        return jsonObject;
    }
}
