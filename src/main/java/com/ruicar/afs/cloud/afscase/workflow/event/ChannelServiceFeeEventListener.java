package com.ruicar.afs.cloud.afscase.workflow.event;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.workflow.feign.ApplyCustInfoFeign;
import com.ruicar.afs.cloud.afscase.writeoff.entity.ChannelServiceFee;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.ChannelServiceFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.bizcommon.voucher.service.MqMessageQueueLogService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.constant.VoucherBuriedPointNo;
import com.ruicar.afs.cloud.common.modules.dto.mq.voucher.VoucherFlowInfoDto;
import com.ruicar.afs.cloud.enums.common.WriteOffExtractEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Slf4j
@Component
public class ChannelServiceFeeEventListener implements ApplicationListener<ChannelServiceFeeEvent> {
    private WriteOffBaseInfoService writeOffBaseInfoService;
    private ChannelServiceFeeService channelServiceFeeService;
    private WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final ApplyCustInfoFeign applyCustInfoFeign;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final MqMessageQueueLogService mqMessageQueueLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(ChannelServiceFeeEvent channelServiceFeeEvent) {
        Object o = JSONObject.toJSON(channelServiceFeeEvent);
        log.info("服务费提取回调"+o);

        List<WriteOffBaseInvoiceRel> infoList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                .eq(WriteOffBaseInvoiceRel::getApproveId, channelServiceFeeEvent.getApplyNo()));

        switch (channelServiceFeeEvent.getAProveBusinessTypeEnum()){
            case APPROVAL:
                log.info("服务费提取通过,流程编号{}", channelServiceFeeEvent.getApplyNo());
                /**
                 * 审批通过
                 */
                if (ObjectUtils.isNotEmpty(infoList)) {
                    List<String> applyNos = new ArrayList<>();
                    Map<String, List<WriteOffBaseInvoiceRel>> dealerMap = infoList.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getDealerCode));
                    if (CollectionUtil.isNotEmpty(dealerMap)) {
                        for (String dealerCode : dealerMap.keySet()) {
                            List<WriteOffBaseInvoiceRel> relList = dealerMap.get(dealerCode);
                            BigDecimal amount = BigDecimal.ZERO;
                            for (WriteOffBaseInvoiceRel info : relList) {
                                amount = amount.add(info.getInvoiceAmount());//累加提取金额
                                //更新任务状态
                                info.setApproveStatus(ChannelServiceFeeEnum.STATUS_3.code);//审核结束
                                info.setPassDate(new Date());
                                info.setStatus(AfsEnumUtil.key(WriteOffExtractEnum.EXTRACTED));
                            }
                            writeOffBaseInvoiceRelService.updateBatchById(relList);

                            log.info("经销商code：{}，本次流程需要提取的服务费金额：{}", dealerCode, amount);
                            ChannelServiceFee channelServiceFee = channelServiceFeeService.getOne(Wrappers.<ChannelServiceFee>query().lambda()
                                    .eq(ChannelServiceFee::getChannelCode, dealerCode));
                            channelServiceFee.setExtractedAmount(channelServiceFee.getExtractedAmount().add(amount));
                            channelServiceFee.setNotExtractedAmount(channelServiceFee.getNotExtractedAmount().subtract(amount));
                            if (channelServiceFee.getPaySuccessAmount() == null) {
                                channelServiceFee.setPaySuccessAmount(amount);
                            } else {
                                channelServiceFee.setPaySuccessAmount(channelServiceFee.getPaySuccessAmount().add(amount));
                            }
                            log.info("经销商code：{}，流程最后累计的服务费数据{}", dealerCode, JSON.toJSONString(channelServiceFee));

                            channelServiceFeeService.saveOrUpdate(channelServiceFee);

                            //根据批次号分组
                            Map<String, List<WriteOffBaseInvoiceRel>> listMap = relList.stream().collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getCurrentBatchNo));
                            for (Map.Entry<String, List<WriteOffBaseInvoiceRel>> entry : listMap.entrySet()) {
                                List<WriteOffBaseInvoiceRel> invoiceRels = entry.getValue();
                                List<String> applyNoList = invoiceRels.stream().map(k -> WriteOffTypeEnum.ZJD.getCode().equals(k.getWriteOffType()) ? k.getApplyNo().substring(0, k.getApplyNo().lastIndexOf("_")) : k.getApplyNo()).toList();
                                log.info("提取核销项编号：{}", applyNoList);
                                List<WriteOffBaseInfo> baseInfos = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery().in(WriteOffBaseInfo::getApplyNo, applyNoList));
                                //计算批次付款总金额
                                BigDecimal loanSum = invoiceRels.stream().map(WriteOffBaseInvoiceRel::getInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                WriteOffBaseInvoiceRel relOne = invoiceRels.get(0);
                                if (WriteOffTypeEnum.ZJD.getCode().equals(relOne.getWriteOffType())) {
                                    for (WriteOffBaseInvoiceRel invoiceRel : invoiceRels) {
                                        for (WriteOffBaseInfo baseInfo : baseInfos) {
                                            if (invoiceRel.getApplyNo().startsWith(baseInfo.getApplyNo())){
                                                baseInfo.setAmountLoaned(baseInfo.getAmountLoaned().add(invoiceRel.getInvoiceAmount()));
                                                baseInfo.setAmountToBeReleased(baseInfo.getAmountToBeReleased().subtract(invoiceRel.getInvoiceAmount()));
                                            }
                                        }
                                    }
                                    //租金贷凭证埋点
                                    writeOffBaseInvoiceRelService.update(Wrappers.<WriteOffBaseInvoiceRel>lambdaUpdate()
                                            .eq(WriteOffBaseInvoiceRel::getId, relOne.getId())
                                            .set(WriteOffBaseInvoiceRel::getZjdPayAmount, loanSum));
                                    saveServiceFeeData(relOne.getDealerCode(), relOne.getApplyNo(), relOne.getDealerName(), VoucherBuriedPointNo.rentLoanServicePay);
                                }else {
                                    for (WriteOffBaseInfo baseInfo : baseInfos) {
                                        baseInfo.setAmountLoaned(baseInfo.getInvoiceAmount());
                                        baseInfo.setAmountToBeReleased(BigDecimal.ZERO);
                                        baseInfo.setLoanedSum(loanSum);
                                    }
                                    //凭证埋点
                                    WriteOffBaseInfo baseInfo1 = baseInfos.get(0);
                                    saveServiceFeeData(baseInfo1.getOrganId(), baseInfo1.getCaseNo(), baseInfo1.getOrganName(), VoucherBuriedPointNo.writeOffCbsPay);
                                }
                                writeOffBaseInfoService.updateBatchById(baseInfos);
                                applyNos.addAll(applyNoList);
                            }
                        }

                        IResponse response = applyCustInfoFeign.syncApplyWriteOffLoanInfo(applyNos);
                        if ("0000".equals(response.getCode())) {
                            log.info("服务费提取成功，同步进件成功，流程编号：{}", channelServiceFeeEvent.getApplyNo());
                        } else {
                            log.info("服务费提取成功，同步进件失败，流程编号：{}", channelServiceFeeEvent.getApplyNo());
                            throw new AfsBaseException("服务费提取同步进件异常");
                        }
                    }
                }

                break;
            case REJECTION:
                log.info("审批拒绝{}", JSON.toJSONString(channelServiceFeeEvent));
                break;
            case BACK:
                log.info("审批退回");
                break;
            default:
                log.warn("操作类型异常");
                break;
        }
        log.info("服务费提取begin，ApplyNo{}", channelServiceFeeEvent);
    }

    /**
     * 数据埋点
     *
     * @param dealerCode
     * @param voucher
     * @param organName
     */
    private void saveServiceFeeData(String dealerCode, String voucher, String organName, String pointNo) {
        log.info("服务费发布埋点数据:" + voucher);
        VoucherFlowInfoDto voucherFlowInfoDto = new VoucherFlowInfoDto();

        voucherFlowInfoDto.setBuriedPointNo(pointNo);
        voucherFlowInfoDto.setTransNo(voucher);
        voucherFlowInfoDto.setKeepAccountDate(new Date());//发布成功时间
        voucherFlowInfoDto.setContractNo(dealerCode);//合同号
        voucherFlowInfoDto.setDealerName(organName);

        //根据经销商code获取Channel
        ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getChannelByDealerId(dealerCode);
        if (ObjectUtils.isNotEmpty(channelBaseInfo)) {
            voucherFlowInfoDto.setChannelId(String.valueOf(channelBaseInfo.getChannelId()));
            voucherFlowInfoDto.setCustNo(String.valueOf(channelBaseInfo.getChannelId()));
        }

        log.info("服务费发布埋点成功,开始保存流水{}", voucherFlowInfoDto);
        mqMessageQueueLogService.saveMqMessage(voucherFlowInfoDto);
    }

}