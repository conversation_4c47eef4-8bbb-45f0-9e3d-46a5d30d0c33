package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.common.feign.CaseToAccountFeign;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffClearInfo;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ClearStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffClearInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffClearInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.ClearInfoExcelVo;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@AllArgsConstructor
@Service
public class WriteOffClearInfoServiceImpl extends ServiceImpl<WriteOffClearInfoMapper, WriteOffClearInfo> implements WriteOffClearInfoService {

    private final StringRedisTemplate stringRedisTemplate;
    private static final String CLEAR_KEY = "service:fee:clear";
    private final CaseToAccountFeign caseToAccountFeign;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;

    @Override
    public void exportData(WriteOffClearInfo condition, HttpServletResponse response) {
        List<WriteOffClearInfo> list = this.list(Wrappers.<WriteOffClearInfo>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffClearInfo::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffClearInfo::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffClearInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .eq(StrUtil.isNotBlank(condition.getWriteOffType()), WriteOffClearInfo::getWriteOffType, condition.getWriteOffType())
                .eq(StrUtil.isNotBlank(condition.getFeeClearStatus()), WriteOffClearInfo::getFeeClearStatus, condition.getFeeClearStatus())
                .eq(StrUtil.isNotBlank(condition.getPayClearStatus()), WriteOffClearInfo::getPayClearStatus, condition.getPayClearStatus())
                .orderByDesc(WriteOffClearInfo::getCreateTime));
        List<ClearInfoExcelVo> exportVoList = list.stream().map(info -> {
            ClearInfoExcelVo excelVo = new ClearInfoExcelVo();
            BeanUtil.copyProperties(info, excelVo);
            excelVo.setWriteOffType(WriteOffTypeEnum.createTypeEnum(info.getWriteOffType()).getDesc());
            excelVo.setFeeClearStatus(ClearStatusEnum.create(info.getFeeClearStatus()).getDesc());
            excelVo.setPayClearStatus(ClearStatusEnum.create(info.getPayClearStatus()).getDesc());
            return excelVo;
        }).toList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("核销项清账明细导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), ClearInfoExcelVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, "核销项清账明细").build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }

    @Override
    public void writeOffClearJob(String param) {
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(CLEAR_KEY, "lock", 2, TimeUnit.HOURS);
        if (lock != null && lock) {
            try {
                this.serviceFeeClear(param);
            } finally {
                stringRedisTemplate.delete(CLEAR_KEY);
            }
        } else {
            log.error("服务费正在清账中，不重复执行！");
        }
    }

    /**
     * 服务费清账
     * @param param
     */
    private void serviceFeeClear(String param) {
        List<WriteOffClearInfo> feeList = this.list(Wrappers.<WriteOffClearInfo>lambdaQuery()
                .eq(WriteOffClearInfo::getFeeClearStatus, ClearStatusEnum.WAIT_CLEAR.getCode()));
        List<WriteOffClearInfo> payList = this.list(Wrappers.<WriteOffClearInfo>lambdaQuery()
                .eq(WriteOffClearInfo::getPayClearStatus, ClearStatusEnum.WAIT_CLEAR.getCode()));
        log.info("暂估清账size：" + feeList.size());
        log.info("应付清账size：" + payList.size());
        for (WriteOffClearInfo clearInfo : feeList) {
            try {
                boolean zjdFlag = WriteOffTypeEnum.ZJD.getCode().equals(clearInfo.getWriteOffType());
                boolean feeEnableFlag = true;
                boolean payEnableFlag = true;
                WriteOffBaseInfo baseInfo = writeOffBaseInfoService.getOne(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getApplyNo, clearInfo.getApplyNo()));
                Assert.isTrue(baseInfo != null, "数据异常：" + clearInfo.getApplyNo());
                if (clearInfo.getFeeVoucherNo() == null) {
                    IResponse<String> iResponse = caseToAccountFeign.getVoucherNoByTransNo(clearInfo.getApplyNo(), zjdFlag ? "13001" : "40701");
                    clearInfo.setFeeVoucherNo("0000".equals(iResponse.getCode()) ? iResponse.getData() : null);
                    feeEnableFlag = "0000".equals(iResponse.getCode());
                }
                if (clearInfo.getInvoiceVoucherNo() == null) {
                    IResponse<String> iResponse = caseToAccountFeign.getVoucherNoByTransNo(baseInfo.getCaseNo(), zjdFlag ? "13002" : "40703");
                    clearInfo.setInvoiceVoucherNo("0000".equals(iResponse.getCode()) ? iResponse.getData() : null);
                    feeEnableFlag = "0000".equals(iResponse.getCode());
                    payEnableFlag = "0000".equals(iResponse.getCode());
                }
                if (zjdFlag) {
                    List<WriteOffBaseInvoiceRel> relList = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                            .select(WriteOffBaseInvoiceRel::getApplyNo)
                            .likeRight(WriteOffBaseInvoiceRel::getApplyNo, clearInfo.getApplyNo())
                            .eq(WriteOffBaseInvoiceRel::getCaseNo, baseInfo.getCaseNo())
                            .eq(WriteOffBaseInvoiceRel::getApproveStatus, ChannelServiceFeeEnum.STATUS_3.code)
                            .orderByAsc(WriteOffBaseInvoiceRel::getUpdateTime));
                    if (relList.size() > 0) {
                        if (clearInfo.getPayVoucherNo() == null || clearInfo.getPayVoucherNo().split(",").length != relList.size()) {
                            List<String> voucherNos = new ArrayList<>();
                            for (WriteOffBaseInvoiceRel rel : relList) {
                                IResponse<String> iResponse = caseToAccountFeign.getVoucherNoByTransNo(rel.getApplyNo(), "13003");
                                if ("0000".equals(iResponse.getCode()) && iResponse.getData() != null) {
                                    voucherNos.add(iResponse.getData());
                                }
                            }
                            clearInfo.setPayVoucherNo(voucherNos.size() > 0 ? String.join(",", voucherNos) : null);
                            payEnableFlag = relList.size() == voucherNos.size();
                        }
                    }
                }else {
                    if (clearInfo.getPayVoucherNo() == null) {
                        IResponse<String> iResponse = caseToAccountFeign.getVoucherNoByTransNo(baseInfo.getCaseNo(), "40705");
                        clearInfo.setPayVoucherNo("0000".equals(iResponse.getCode()) ? iResponse.getData() : null);
                        payEnableFlag = "0000".equals(iResponse.getCode());
                    }
                }
                //尾差处理
                if (clearInfo.getTailVoucherNo() == null && baseInfo.getWaitExcludeSum() != null && baseInfo.getWaitExcludeSum().compareTo(baseInfo.getExcludeTaxAmount()) != 0) {
                    IResponse<String> iResponse = caseToAccountFeign.getVoucherNoByTransNo(baseInfo.getId().toString(), "40706");
                    clearInfo.setTailVoucherNo("0000".equals(iResponse.getCode()) ? iResponse.getData() : null);
                    feeEnableFlag = "0000".equals(iResponse.getCode());
                }
                //清账
                if (feeEnableFlag) {
                    //暂估可清账
                }
                if (payEnableFlag && baseInfo.getAmountLoaned().compareTo(baseInfo.getInvoiceAmount()) == 0) {
                    //应付可清账
                }
            } catch (Exception e) {
                log.error("清账出现异常：" + clearInfo.getApplyNo());
            }
        }
    }
}
