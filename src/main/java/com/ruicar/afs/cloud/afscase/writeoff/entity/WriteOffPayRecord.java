package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.modules.contract.enums.PaymentStatusEnum;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: zhangjin
 * @description 服务费支付详情记录
 * @date: 2024/8/6 14:05
 */
@Data
@TableName(value = "write_off_pay_record", autoResultMap = true)
public class WriteOffPayRecord extends BaseEntity<WriteOffPayRecord> {
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 核销期数
     */
    private String writeOffMonth;
    /**
     * 流程编号
     */
    private String businessNo;
    /**
     * 案件编号
     */
    private String caseNo;
    /**
     * 提取状态
     */
    private String status;
    /**
     * 批次总金额
     */
    private BigDecimal batchAmount;
    /**
     * 经销商code
     */
    private String channelCode;
    /**
     * 经销商name
     */
    private String channelFullName;
    /**
     * 期数
     */
    private Integer termNo;
    /**
     * 应支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private Date waitPayTime;
    /**
     * 实际支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date successPayTime;
    /**
     * 发起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    /**
     * 支付比例
     */
    private BigDecimal payRate;
    /**
     * 本期需支付金额
     */
    private BigDecimal payAmount;
    /**
     * 核销项分期支付金额
     */
    private String basePeriodAmt;
    /**
     * cbs发送，请求流水号
     */
    private String cbsRefNbr;
    /**
     * cbs发送，对方接口返回流水号
     */
    private String cbsBusNbr;
    /**
     * 付款状态
     */
    private PaymentStatusEnum payStatus;
    /**
     * 付款账号
     */
    private String payAccount;
    /**
     * 收款账号
     */
    private String payReceiveAccount;
    /**
     * 付款失败原因
     */
    private String payFailReason;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 影像件busiNo
     */
    private String fileBusiNo;

    @TableField(exist = false)
    private FrozenStatusEnum frozenStatus;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date payTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date payTimeStop;
    /**
     * 查询条件，是否全部案件
     */
    @TableField(exist = false)
    private boolean caseAll;
}
