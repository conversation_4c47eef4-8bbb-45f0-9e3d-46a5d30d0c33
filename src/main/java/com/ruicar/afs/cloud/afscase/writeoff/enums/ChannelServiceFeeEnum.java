package com.ruicar.afs.cloud.afscase.writeoff.enums;

/**
 * The enum Channel service fee enum.
 */
public enum ChannelServiceFeeEnum {
    /**
     * 未提取
     */
    STATUS_0("0","未审核"),
    /**
     * 提取中
     */
    STATUS_1("1","审核中"),
    /**
     * 已撤回
     */
    STATUS_2("2","退回"),
    /**
     * 提取完成
     */
    STATUS_3("3","审核完成"),
    /**
     * 异常
     */
    STATUS_4("4", "异常");

    /**
     * The Code.
     */
    public String code;
    /**
     * The Desc.
     */
    public String desc;

    private ChannelServiceFeeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
