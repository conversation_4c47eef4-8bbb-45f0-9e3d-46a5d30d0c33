package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffMonthConfig;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffMonthConfigMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffMonthConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@AllArgsConstructor
@Service
public class WriteOffMonthConfigServiceImpl extends ServiceImpl<WriteOffMonthConfigMapper, WriteOffMonthConfig> implements WriteOffMonthConfigService {

}
