package com.ruicar.afs.cloud.afscase.infomanagement.config;

import lombok.Data;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
public class BaiduMapConfiguration {

    /** 服务地址 */
    @Value("${com.ruicar.afs.cloud.third-api-config.baidu-map.serviceUrl:https://api.map.baidu.com}")
    private String serviceUrl;
    /** 客户端id */
    @Value("${com.ruicar.afs.cloud.third-api-config.baidu-map.ak:TaQjzrjUUHMTY3TqkJTQjsU4xiiYJbjx}")
    private String ak;
}