package com.ruicar.afs.cloud.afscase.writeoff.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRule;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRuleDetail;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.WriteOffRuleQueryVo;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: zhangjin
 * @description 服务费支付规则前端控制器
 * @date: 2024/8/6 11:22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffPayRule")
public class WriteOffPayRuleController {
    private final WriteOffPayRuleService writeOffPayRuleService;
    private final WriteOffPayRuleDetailService writeOffPayRuleDetailService;
    private final AfsRuleFeign afsRuleInfoService;

    @GetMapping("/queryList")
    @ApiOperation(value = "查询数据")
    public IResponse queryList() {
        return IResponse.success(writeOffPayRuleService.list());
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存数据")
    @Transactional(rollbackFor = Exception.class)
    public IResponse saveData(@RequestBody WriteOffPayRule payRule) {
        payRule.setId(null);
        payRule.setStatus(StatusEnum.NO.getCode());
        writeOffPayRuleService.save(payRule);
        List<WriteOffPayRuleDetail> detailList = new ArrayList<>();
        for (int i = 0; i < payRule.getPeriod(); i++) {
            WriteOffPayRuleDetail detail = new WriteOffPayRuleDetail();
            detail.setId(null);
            detail.setPayRuleId(payRule.getId());
            detail.setTermNo(i + 1);
            detailList.add(detail);
        }
        writeOffPayRuleDetailService.saveBatch(detailList);
        return IResponse.success("保存成功");
    }

    @PostMapping("/editData")
    @ApiOperation(value = "修改数据")
    @Transactional(rollbackFor = Exception.class)
    public IResponse editData(@RequestBody WriteOffPayRule payRule) {
        Assert.isTrue(payRule.getId() != null && payRule.getPeriod() != null, "参数错误！");
        WriteOffPayRule byId = writeOffPayRuleService.getById(payRule.getId());
        if (!byId.getPeriod().equals(payRule.getPeriod())) {
            //期数被修改
            writeOffPayRuleDetailService.remove(Wrappers.<WriteOffPayRuleDetail>lambdaQuery().eq(WriteOffPayRuleDetail::getPayRuleId, byId.getId()));
            List<WriteOffPayRuleDetail> detailList = new ArrayList<>();
            for (int i = 0; i < payRule.getPeriod(); i++) {
                WriteOffPayRuleDetail detail = new WriteOffPayRuleDetail();
                detail.setId(null);
                detail.setPayRuleId(payRule.getId());
                detail.setTermNo(i + 1);
                detailList.add(detail);
            }
            writeOffPayRuleDetailService.saveBatch(detailList);
        }
        writeOffPayRuleService.updateById(payRule);
        return IResponse.success("修改成功");
    }

    @PostMapping("/queryDetailList")
    @ApiOperation(value = "查询规则详情数据")
    public IResponse queryDetailList(@RequestBody WriteOffRuleQueryVo queryVo) {
        return IResponse.success(writeOffPayRuleDetailService.page(new Page(queryVo.getPageNumber(), queryVo.getPageSize()), Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                .eq(WriteOffPayRuleDetail::getPayRuleId, queryVo.getPayRuleId())
                .orderByAsc(WriteOffPayRuleDetail::getTermNo)));
    }

    @PostMapping("/addDetail")
    @ApiOperation(value = "新增规则详情数据")
    public IResponse addDetail(@RequestBody WriteOffPayRuleDetail payRuleDetail) {
        Assert.isTrue(payRuleDetail.getPayRuleId() != null && payRuleDetail.getTermNo() != null, "参数错误！");
        long count = writeOffPayRuleDetailService.count(Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                .eq(WriteOffPayRuleDetail::getPayRuleId, payRuleDetail.getPayRuleId())
                .eq(WriteOffPayRuleDetail::getTermNo, payRuleDetail.getTermNo()));
        Assert.isTrue(count == 0, "期数不能重复！");
        payRuleDetail.setId(null);
        writeOffPayRuleDetailService.save(payRuleDetail);
        return IResponse.success(null);
    }

    @PostMapping("/editDetail")
    @ApiOperation(value = "修改规则详情数据")
    public IResponse editDetail(@RequestBody WriteOffPayRuleDetail detail) {
        Assert.isTrue(detail.getId() != null && detail.getPayRuleId() != null && detail.getTermNo() != null, "参数错误！");
        long count = writeOffPayRuleDetailService.count(Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                .eq(WriteOffPayRuleDetail::getPayRuleId, detail.getPayRuleId())
                .eq(WriteOffPayRuleDetail::getTermNo, detail.getTermNo())
                .ne(WriteOffPayRuleDetail::getId, detail.getId()));
        Assert.isTrue(count == 0, "期数不能重复！");
        writeOffPayRuleDetailService.updateById(detail);
        return IResponse.success(null);
    }

    @PostMapping(value = "/updateStatus")
    @ApiOperation(value = "修改生效状态")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateStatus(@RequestParam Long id) {
        WriteOffPayRule payRule = writeOffPayRuleService.getById(id);
        Assert.isTrue(payRule != null, "id不正确");
        if (StatusEnum.YES.getCode().equals(payRule.getStatus())) {
            // 生效改无效
            afsRuleInfoService.deActiveRuleByRuleId(payRule.getRuleId());
            payRule.setStatus(StatusEnum.NO.getCode());
        } else {
            // 无效改生效
            List<WriteOffPayRuleDetail> detailList = writeOffPayRuleDetailService.list(Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                    .eq(WriteOffPayRuleDetail::getPayRuleId, payRule.getId()));
            Assert.isTrue(detailList.size() > 0, "没有配置分期支付规则，不可生效！");
            Assert.isTrue(payRule.getRuleId() != null, "规则未配置，不可生效！");
            BigDecimal rateSum = detailList.stream().map(WriteOffPayRuleDetail::getPayRate).reduce(BigDecimal.ZERO, BigDecimal::add);
            Assert.isTrue(new BigDecimal("100").compareTo(rateSum) == 0, "支付比例之和不等于100%，不可生效！");
            afsRuleInfoService.activeRuleByRuleId(payRule.getRuleId());
            payRule.setStatus(StatusEnum.YES.getCode());
        }
        writeOffPayRuleService.updateById(payRule);
        return IResponse.success(null);
    }

    @PostMapping(value = "/delById")
    @ApiOperation(value = "通过id删除")
    @Transactional(rollbackFor = Exception.class)
    public IResponse deleteById(@RequestParam Long id) {
        WriteOffPayRule payRule = writeOffPayRuleService.getById(id);
        Assert.isTrue(StatusEnum.NO.getCode().equals(payRule.getStatus()), "生效的规则不可删除");
        if (payRule.getRuleId() != null) {
            afsRuleInfoService.deleteRule(payRule.getRuleId());
        }
        writeOffPayRuleDetailService.remove(Wrappers.<WriteOffPayRuleDetail>lambdaQuery()
                .eq(WriteOffPayRuleDetail::getPayRuleId, payRule.getId()));
        writeOffPayRuleService.removeById(id);
        return IResponse.success(null);
    }

    @PostMapping(value = "/delDetailById")
    @ApiOperation(value = "删除规则详情数据")
    @Transactional(rollbackFor = Exception.class)
    public IResponse delDetailById(@RequestParam Long id) {
        WriteOffPayRuleDetail detail = writeOffPayRuleDetailService.getById(id);
        Assert.isTrue(detail != null, "数据不存在！");
        writeOffPayRuleDetailService.removeById(id);
        return IResponse.success(null);
    }
}
