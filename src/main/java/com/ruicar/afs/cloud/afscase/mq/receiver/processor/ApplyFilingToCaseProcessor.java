package com.ruicar.afs.cloud.afscase.mq.receiver.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.enums.FilingStatusEnum;
import com.ruicar.afs.cloud.afscase.mq.service.AffiliatedCompanyFilingService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ComAttachmentFileDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CompanyFilingSubmitInfo;
import com.ruicar.afs.cloud.common.mq.rabbit.listener.AfsMqBizProcessor;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Slf4j
@Component
public class ApplyFilingToCaseProcessor implements AfsMqBizProcessor<CompanyFilingSubmitInfo> {

    private AffiliatedCompanyFilingService affiliatedCompanyFilingService;
    private ComAttachmentFileService comAttachmentFileService;
    private WorkflowHelper workflowHelper;
    private FlowConfigProperties flowConfigProperties;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private CaseUseApplyServiceFeign caseUseApplyServiceFeign;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processMessage(CompanyFilingSubmitInfo entity) {
        AffiliatedCompanyFiling affiliatedCompanyFiling = new AffiliatedCompanyFiling();
        BeanUtil.copyProperties(entity,affiliatedCompanyFiling);
        affiliatedCompanyFiling.setStatus(FilingStatusEnum.APPROVE.key());
        affiliatedCompanyFiling.setId(Long.valueOf(affiliatedCompanyFiling.getBusiNo()));
        affiliatedCompanyFilingService.saveOrUpdate(affiliatedCompanyFiling);
        if (StrUtil.equals(entity.getStatus(), FilingStatusEnum.SUBMIT_WAIT.key())){
            //备案申请
            this.startWorkFlow(affiliatedCompanyFiling);
        } else if (StrUtil.equals(entity.getStatus(),FilingStatusEnum.SUSPEND.key())) {
            //退回
            this.resumeProcess(affiliatedCompanyFiling.getId());
        }else {
            return false;
        }
        ArrayList<ComAttachmentFile> comAttachmentFileArrayList = this.parseComAttachmentFile(entity);
        if(!ObjectUtils.isEmpty(comAttachmentFileArrayList)){
            comAttachmentFileService.saveBatch(comAttachmentFileArrayList,comAttachmentFileArrayList.size());
        }
        log.info("影像文件add成功！！！");
        return true;
    }

    private void resumeProcess(Long id) {
        //如果是退回状态，恢复流程
        List<WorkflowTaskInfo> list = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                .eq(WorkflowTaskInfo::getFlowPackageId, flowConfigProperties.getFilingPackageId())
                .eq(WorkflowTaskInfo::getFlowTemplateId, flowConfigProperties.getFilingTemplateId())
                .eq(WorkflowTaskInfo::getBusinessNo, String.valueOf(id))
                .orderByDesc(WorkflowTaskInfo::getCreateTime));
        FlowVariable flowVariable=new FlowVariable();
        flowVariable.setFlowInstanceId(list.get(0).getProcessInstanceId());
        flowVariable.setName(FlowConstant.BACK_NODE_ID);
        flowVariable.setValue(list.get(0).getUserDefinedIndex());
        workflowHelper.setFlowVariableByFlowInstance(flowVariable);
        FlowVariable flowVariable1=new FlowVariable();
        flowVariable1.setFlowInstanceId(list.get(0).getProcessInstanceId());
        flowVariable1.setName(FlowConstant.FLOW_BACK_OPTION);
        flowVariable1.setValue("DIRECT");
        workflowHelper.setFlowVariableByFlowInstance(flowVariable1);
        IResponse iResponse = workflowHelper.resumeProcess(list.get(0).getProcessInstanceId());
        if(!CommonConstants.SUCCESS.equals(iResponse.getCode())){
            caseUseApplyServiceFeign.updateFilingStatus(String.valueOf(id),FilingStatusEnum.SUSPEND.key());
            throw new AfsBaseException("恢复流程异常");
        }
    }


    private void startWorkFlow(AffiliatedCompanyFiling affiliatedCompanyFiling) {
        //发起挂靠公司备案审批流程
        StartFlowRequestBo startFlowRequestBo = new StartFlowRequestBo();
        startFlowRequestBo.setBusinessNo(affiliatedCompanyFiling.getBusiNo());
        startFlowRequestBo.setPackageId(flowConfigProperties.getFilingPackageId());
        startFlowRequestBo.setTemplateId(flowConfigProperties.getFilingTemplateId());
        String type =  dicData(affiliatedCompanyFiling.getFilingType(),"filingType");
        startFlowRequestBo.setSubject(affiliatedCompanyFiling.getChannelName()+"挂靠公司备案申请:" + affiliatedCompanyFiling.getAffiliatedName() + "-" + type + DateUtil.date());
        // 植入业务参数
        if (startFlowRequestBo.getParams() == null) {
            startFlowRequestBo.setParams(new JSONObject());
        }
        startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_ID, affiliatedCompanyFiling.getBusiNo());
        startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_NO, affiliatedCompanyFiling.getBusiNo());
        startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_OPINION, FlowStatusEnum.CREATE.getDesc());
        startFlowRequestBo.getParams().put(FlowConstant.TASK_NODE_NAME, "流程发起");
        startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_TYPE, type);
        startFlowRequestBo.getParams().put(FlowConstant.APPROVAL_USER, affiliatedCompanyFiling.getOperatorRealName());
        startFlowRequestBo.getParams().put(FlowConstant.FILING_TYPE, affiliatedCompanyFiling.getFilingType());
        final IResponse response = workflowHelper.startFlow(startFlowRequestBo, UseSceneEnum.AFFILIATED_COMPANY_FILING_NEW);
        if (!CommonConstants.SUCCESS.equals(response.getCode())) {
            caseUseApplyServiceFeign.updateFilingStatus(String.valueOf(affiliatedCompanyFiling.getBusiNo()),FilingStatusEnum.SUBMIT_WAIT.key());
            throw new AfsBaseException("挂靠公司备案申请流程异常!");
        }
    }

    private ArrayList<ComAttachmentFile> parseComAttachmentFile(CompanyFilingSubmitInfo entity) {
        ArrayList<ComAttachmentFile> comAttachmentFileArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entity.getComAttachmentFileListDto())) {
            List<ComAttachmentFile> attachmentFiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo,entity.getBusiNo())
                    .eq(ComAttachmentFile::getBelongNo,entity.getBusiNo()));
            List<Long> ids = new ArrayList<>();
            for (ComAttachmentFile file : attachmentFiles){
                ids.add(file.getId());
            }
            for (ComAttachmentFileDto dto : entity.getComAttachmentFileListDto()){
                if (!ids.contains(dto.getId())){
                    ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
                    BeanUtils.copyProperties(dto,comAttachmentFile);
                    comAttachmentFileArrayList.add(comAttachmentFile);
                }
            }
        }
        return comAttachmentFileArrayList;
    }

    @Override
    public MqTransCode getCode() {
        return MqTransCode.APPLY_TO_CASE_COMPANY_FILING;
    }

    public static String dicData(String param,String dicType){
        String result = "";
        Map<String, List<DicDataDto>> listMap = DicHelper.getDicMaps(dicType);
        List<DicDataDto> list=listMap.get(dicType);
        log.info("字典数据:{}",list.size());
        for(DicDataDto dicDataDto : list){
            if(dicDataDto.getValue().equals(param)){
                result = dicDataDto.getTitle();
                break;
            }
        }
        return result;

    }

}
