package com.ruicar.afs.cloud.afscase.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.badge.entity.CaseElectronBadgeInfo;
import com.ruicar.afs.cloud.afscase.badge.service.ElectronBadgeInfoService;
import com.ruicar.afs.cloud.afscase.carrierpigeon.controller.vo.DecisionEngineVO;
import com.ruicar.afs.cloud.afscase.carrierpigeon.enums.CarrierPigeonSiteEnums;
import com.ruicar.afs.cloud.afscase.carrierpigeon.service.CarrierPigeonService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.common.constants.RiskResConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.FinCostDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.impl.CaseCustContactServiceImpl;
import com.ruicar.afs.cloud.afscase.infomanagement.service.impl.CaseCustInfoServiceImpl;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.afscase.risk.mapper.ThirdDataMapper;
import com.ruicar.afs.cloud.afscase.risk.service.ThirdDataService;
import com.ruicar.afs.cloud.afscase.workflow.enums.RiskErrorEnum;
import com.ruicar.afs.cloud.afscase.workflow.feign.ConfigServiceFeign;
import com.ruicar.afs.cloud.afscase.workflow.feign.ProducePlanFeign;
import com.ruicar.afs.cloud.afscase.workflow.feign.ProductPlanRateFeign;
import com.ruicar.afs.cloud.afscase.workflow.feign.TsysParamConfigFeign;
import com.ruicar.afs.cloud.badge.condition.BadgeReqCondition;
import com.ruicar.afs.cloud.badge.service.ElectronBadgeService;
import com.ruicar.afs.cloud.badge.vo.BadgeResDataVo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.service.FinMainInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.risk.api.enums.RiskStatus;
import com.ruicar.afs.cloud.risk.api.enums.TencentControlRequestPath;
import com.ruicar.afs.cloud.risk.api.service.DecisionEngineService;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 功能说明: 决策引擎工具类
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class RiskCheckUtils {

    final CaseBaseInfoService baseInfoService;
    final TsysParamConfigFeign tsysParamConfigFeign;
    final DecisionEngineService decisionEngineService;
    final ThirdDataService thirdDataService;
    final CaseBaseInfoService caseBaseInfoService;
    final ProducePlanFeign producePlanFeign;
    final ProductPlanRateFeign productPlanRateFeign;
    final ConfigServiceFeign configServiceFeign;
    final CaseCustInfoServiceImpl caseCustInfoService;
    final CaseCustContactServiceImpl caseCustContactService;
    final CaseCarInfoService caseCarInfoService;
    final FinCostDetailsService finCostDetailsService;
    final FinMainInfoService finMainInfoService;
    final CaseChannelInfoService caseChannelInfoService;
    final ChannelBaseInfoService channelBaeInfoService;
    final CarrierPigeonService carrierPigeonService;
    final ThirdDataMapper thirdDataMapper;
    final CaseApproveRecordService caseApproveRecordService;
    final ElectronBadgeService electronBadgeService;
    final ElectronBadgeInfoService electronBadgeInfoService;

    static {
        baseInfoService = SpringContextHolder.getBean(CaseBaseInfoService.class);
        tsysParamConfigFeign = SpringContextHolder.getBean(TsysParamConfigFeign.class);
        decisionEngineService = SpringContextHolder.getBean(DecisionEngineService.class);
        thirdDataService = SpringContextHolder.getBean(ThirdDataService.class);
        caseBaseInfoService = SpringContextHolder.getBean(CaseBaseInfoService.class);
        producePlanFeign = SpringContextHolder.getBean(ProducePlanFeign.class);
        productPlanRateFeign = SpringContextHolder.getBean(ProductPlanRateFeign.class);
        configServiceFeign = SpringContextHolder.getBean(ConfigServiceFeign.class);
        caseCustInfoService = SpringContextHolder.getBean(CaseCustInfoServiceImpl.class);
        caseCustContactService = SpringContextHolder.getBean(CaseCustContactServiceImpl.class);
        caseCarInfoService = SpringContextHolder.getBean(CaseCarInfoService.class);
        finCostDetailsService = SpringContextHolder.getBean(FinCostDetailsService.class);
        finMainInfoService = SpringContextHolder.getBean(FinMainInfoService.class);
        caseChannelInfoService = SpringContextHolder.getBean(CaseChannelInfoService.class);
        channelBaeInfoService = SpringContextHolder.getBean(ChannelBaseInfoService.class);
        carrierPigeonService = SpringContextHolder.getBean(CarrierPigeonService.class);
        thirdDataMapper = SpringContextHolder.getBean(ThirdDataMapper.class);
        caseApproveRecordService = SpringContextHolder.getBean(CaseApproveRecordService.class);
        electronBadgeService = SpringContextHolder.getBean(ElectronBadgeService.class);
        electronBadgeInfoService = SpringContextHolder.getBean(ElectronBadgeInfoService.class);
    }

    /**
     * 调用决策引擎
     *
     * @param applyNo   进件编号
     * @param inputType 个人/企业
     * @param firstSubmit 首次提交【0否，1是】
     * @return
     */
    public IResponse<JSONObject> riskCheckExecute(String applyNo, String inputType,String firstSubmit,String equeryid,String cqueryid) {
        JSONObject jsonRes = new JSONObject();
        //添加决策引擎风控开关
        boolean onCheck = getOnCheck();
        //添加决策引擎风控（默认人工）
        String finalDealTypeCode = AfsEnumUtil.key(RiskStatus.RISK_RES_REVIEW);
        //决策错误原因
        StringBuilder riskErrorMsg = new StringBuilder("");
        if (onCheck && Objects.equals(CaseConstants.PERSONAL, inputType)) {
            //1.根据applyNo调用apply服务的apply_cust_base_info表，获取到进件申请中用户和担保人的信息
            JSONObject riskBody = getAndSetParams(applyNo,firstSubmit,equeryid,cqueryid);
            //调用决策引擎接口进行风险验证
            IResponse<JSONObject> iResponse = decisionEngineService.DecisionEngine(riskBody, TencentControlRequestPath.PRE_EXAMINATION);
            log.info("调用决策引擎结果：{}", iResponse);
            //获取决策状态
            if (Objects.equals(CommonConstants.SUCCESS, iResponse.getCode())) {
                //对应状态类RiskStatus
                String code = Optional.ofNullable(iResponse.getData()).map(json -> json.getString(RiskResConstants.FINAL_DEAL_TYPE_CODE)).filter(str->!Objects.equals(str,AfsEnumUtil.key(RiskStatus.RISK_RES_NONE))).orElse("");
                if (StrUtil.isNotBlank(code)) {
                    finalDealTypeCode = code;
                }
                JSONArray codeList = Optional.ofNullable(iResponse.getData()).map(data -> data.getJSONObject("decisionDetailedResults")).map(res -> res.getJSONArray(RiskResConstants.RULE_CUSTOM_IDS)).orElse(new JSONArray());
                jsonRes.put(RiskResConstants.RULE_CUSTOM_IDS,codeList);
                log.info("决策结果={}命中的规则={}", finalDealTypeCode, codeList.toJSONString());
                for (Object o : codeList) {
                    if (o != null) {
                        log.info("错误编码={}", o);
                        Enum anEnum = AfsEnumUtil.getEnum(o.toString(), RiskErrorEnum.class);
                        if (anEnum != null) {
                            String desc = AfsEnumUtil.desc(anEnum);
                            log.info("错误描述信息={}", desc);
                            if (StrUtil.isNotBlank(desc) && riskErrorMsg.indexOf(desc) == -1) {
                                riskErrorMsg.append(desc).append(",");
                            }
                        }
                    }
                }
                log.info("最终的错误信息={}", riskErrorMsg);
                JSONObject outputVariable = Optional.ofNullable(iResponse.getData())
                        .map(data -> data.getJSONObject(RiskResConstants.OUT_PUT_VARIABLE)).orElse(new JSONObject());
                jsonRes.put(RiskResConstants.OUT_PUT_VARIABLE, outputVariable);
            }
            //保存正审数据，查询决策引擎报告会使用
            thirdDataService.saveApproveThirdData(applyNo, iResponse, riskBody);
        }
        jsonRes.put(RiskResConstants.RISK_ERROR_MSG, riskErrorMsg.toString());
        jsonRes.put(RiskResConstants.FINAL_DEAL_TYPE_CODE, finalDealTypeCode);
        log.info("返回的决策data={}", jsonRes);
        return IResponse.success(jsonRes);
    }

    /**
     * 获取参数配置
     *
     * @return
     */
    private boolean getOnCheck() {
        JSONObject queryVo = new JSONObject();
        queryVo.put("paramNo", "approveRisk");
        queryVo.put("magicNo", "riskSwitch");
        IResponse<String> paramValue = tsysParamConfigFeign.getParamValue(queryVo);
        log.info("获取到的进件申请参数开关={}", paramValue);
        String val = Optional.ofNullable(paramValue).filter(iRes -> "0000".equals(iRes.getCode())).map(IResponse::getData).orElse("1");
        return Objects.equals("1", val);
    }

    /**
     * 地址code转label
     *
     * @param addressJson
     * @return
     */
    private CaseCustAddress addressCodeTransferLabel(JSONObject addressJson) {
        CaseCustAddress caseCustAddress = new CaseCustAddress();
        log.info("transfer address code to label req={}", addressJson);
        IResponse res = configServiceFeign.getAddressByCodes(addressJson);
        log.info("transfer address code to label resp={}", res);
        if (Objects.equals("0000", res.getCode())) {
            JSONObject addressLabels = JSONObject.parseObject(JSONObject.toJSONString(res.getData()));
            caseCustAddress.setProvince(addressLabels.getString("province"));
            caseCustAddress.setCity(addressLabels.getString("city"));
            caseCustAddress.setCounty(addressLabels.getString("county"));
        }
        return caseCustAddress;
    }

    /**
     * @param dicKey
     * @param val    title或者value的值
     * @param type   返回的字典值：【1:title  2:value】
     * @return
     */
    private String getDicTitleOrValue(String dicKey, String val, int type) {
        Map<String, List<DicDataDto>> sexDic = DicHelper.getDicMaps(dicKey);
        List<DicDataDto> typeData = sexDic.get(dicKey);
        for (DicDataDto typeDatum : typeData) {
            if (Objects.equals(type == 1 ? typeDatum.getValue() : typeDatum.getTitle(), val)) {
                return type == 1 ? typeDatum.getTitle() : typeDatum.getValue();
            }
        }
        return null;
    }

    /**
     * 设置进件业务信息
     *
     * @param applyNo
     */
    public JSONObject getAndSetParams(String applyNo,String firstSubmit,String equeryid,String cqueryid) {
        JSONObject riskBody = new JSONObject();
        SimpleDateFormat format = new SimpleDateFormat("yyyy.MM.dd");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
        //特殊地址信息处理
        List<String> specialAddressList = List.of("北京市", "上海市", "天津市", "重庆市", "香港特别行政区", "澳门特别行政区");
        /*======================================================业务数据相关========================================================*/
        riskBody.put("entryid", applyNo);
        riskBody.put("isenergycar", "是");//是否新能源车
        //查询业务基础信息
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getBaseMapper().selectOne(Wrappers.<CaseBaseInfo>query().lambda().eq(CaseBaseInfo::getApplyNo, applyNo));
        //查询渠道商名称
        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getBaseMapper().selectOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, applyNo));
        //获取渠道商所有信息
        ChannelBaseInfo channelBaseInfo = channelBaeInfoService.getBaseMapper().selectOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
        riskBody.put("socialunifiedcreditcode", channelBaseInfo.getSocUniCrtCode());//统一社会信用代码
        riskBody.put("byddealercode", channelBaseInfo.getChannelCode());//经销商代码
        riskBody.put("storesapcode", channelBaseInfo.getSpaCode());//SAP代码
        //查询车辆信息
        CaseCarInfo caseCarInfo = caseCarInfoService.getBaseMapper().selectOne(Wrappers.<CaseCarInfo>query().lambda().eq(CaseCarInfo::getApplyNo, applyNo));
        riskBody.put("vehicleprice", Optional.ofNullable(caseCarInfo.getSalePrice()).map(price -> price.setScale(2)).orElse(null));//车辆价格,保留两位小数
        riskBody.put("vehiclesemod", caseCarInfo.getSeriesName() + " " + caseCarInfo.getModelName());//车辆型号,2023-09-05按需求调整为：车系+" "+车型
        riskBody.put("carName", caseCarInfo.getSeriesName());//车辆型号,2023-09-05按需求调整为：车系+" "+车型
        riskBody.put("carbrand",caseCarInfo.getBrandName());
        IResponse<ApplyAffiliatedUnit> unitIResponse = SpringContextHolder.getBean(ApplyServiceFeign.class).getAffiliatedUnit(applyNo);
        if ("0000".equals(unitIResponse.getCode())&& EmptyUtils.isNotEmpty(unitIResponse.getData())) {
            riskBody.put("attachcompany", unitIResponse.getData().getAffiliatedName());
        }
        //城市编码转label
        JSONObject cityJson = new JSONObject();
        cityJson.put("province", caseCarInfo.getPurchaseProvince());
        cityJson.put("city", caseCarInfo.getPurchaseCity());
        cityJson.put("county", caseCarInfo.getPurchaseCounty());
        CaseCustAddress cityRes = addressCodeTransferLabel(cityJson);
        String buyCity = specialAddressList.contains(cityRes.getProvince()) ? cityRes.getProvince() : cityRes.getCity();
        riskBody.put("applyprovince",cityRes.getProvince());
        riskBody.put("applycity", buyCity);//申请城市,2023-09-05业务需求更改
        riskBody.put("applystore", caseChannelInfo.getDealerName());//申请门店

        ChannelRiskInfo channelRiskInfo = channelBaeInfoService.getChannelRiskInfo(channelBaseInfo.getChannelId(), caseBaseInfo.getBusinessType());
        // 门店资质等级
        riskBody.put("storeqlevel", channelRiskInfo.getChannelGrade());
        riskBody.put("istsproduct", caseCarInfo.getBrandName().contains("腾势"));//是否腾势产品->正确做法应该是根据品牌id查询车型库获取品牌=/vehicle/queryBrand
        riskBody.put("purchasepurpose", getDicTitleOrValue("carPurpose", caseBaseInfo.getCarPurpose(), 1));//购置用途
        //查询金融产品信息
        FinCostDetails finCostDetails = finCostDetailsService.getFinCostDetails(applyNo);
        riskBody.put("carType", caseBaseInfo.getCarType());
        riskBody.put("firstpayment", finCostDetails.getDownPayAmt());//首付金额
        riskBody.put("financingamount", finCostDetails.getLoanAmt());//融资金额
        riskBody.put("productname", finCostDetails.getProductName());//租赁产品名称
        riskBody.put("vehicletotalprice", caseCarInfo.getSalePrice().add(finCostDetails.getAddAmt()));//车辆总价款=裸车价+附加融金额
        riskBody.put("leaseterm", finCostDetails.getLoanTerm());//融资期限
        BigDecimal downPayScale = Optional.ofNullable(finCostDetails.getDownPayScale()).orElse(new BigDecimal(0));
        riskBody.put("firstpaymentrates", downPayScale.divide(new BigDecimal("100"), 4, RoundingMode.DOWN));//首付比例
        riskBody.put("balancepayment", finCostDetails.getTailPayAmt());//尾款金额
        riskBody.put("interestrate",Optional.ofNullable(finCostDetails.getCustRate().setScale(3,RoundingMode.DOWN)).orElse(new BigDecimal(0)));
        //融资费用主表
        FinMainInfo mainInfo = finMainInfoService.getOne(Wrappers.<FinMainInfo>query().lambda()
                .eq(FinMainInfo::getApplyNo, applyNo));
        riskBody.put("ltvfirstpaymentrates",Optional.ofNullable(mainInfo.getLtvScale()).map(d->d.divide(new BigDecimal(100))).orElse(new BigDecimal(0)));
        //根据产品id查询产品表，
        IResponse productResp = producePlanFeign.getProductById(finCostDetails.getProductId());
        if (Objects.equals("0000", productResp.getCode())) {
            JSONObject productPlan = Optional.ofNullable(productResp.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
            String discountPlanId = productPlan.getString("discountPlanId");
            String extrasPlanId = productPlan.getString("extrasPlanId");
            if (StrUtil.isBlank(discountPlanId)) {
                //无贴息产品
                riskBody.put("typeofproduction", "标准融产品组");//产品的类型，根据是否贴息字段判断,字典{贴息融产品组,标准融产品组}
            } else {
                //无贴息产品
                riskBody.put("typeofproduction", "贴息融产品组");//产品的类型，根据是否贴息字段判断,字典{贴息融产品组,标准融产品组}
            }
            if (StrUtil.isBlank(extrasPlanId)) {
                riskBody.put("additionalproduct", "否");//附加品，有附加融就传递是，反之否
            } else {
                riskBody.put("additionalproduct", "是");//附加品，有附加融就传递是，反之否
            }
            //根据产品id查询关联的产品规则，获取对应的规则
            JSONObject reqBody = new JSONObject();
            reqBody.put("id", finCostDetails.getProductId());
            log.info("获取产品利率参数={}", reqBody);
            IResponse planRateList = productPlanRateFeign.getProductPlanRateList(reqBody);
            log.info("获取产品利率结果={}", planRateList);
            if (Objects.equals("0000", planRateList.getCode())) {
                JSONArray arr = Optional.ofNullable(planRateList.getData()).map(JSONArray::toJSONString).map(JSONArray::parseArray).orElse(new JSONArray());
                for (Object o : arr) {
                    JSONObject planRate = JSONObject.parseObject(JSONObject.toJSONString(o));
                    riskBody.put("limitAmt", planRate.getBigDecimal("limitAmt"));//租赁产品最高融资额
                    riskBody.put("minPaymentRatio", Optional.ofNullable(planRate.getBigDecimal("minPaymentRatio")).map(bd -> bd.divide(new BigDecimal("100"), 2, RoundingMode.DOWN)).orElse(null));//租赁产品最低首付比例
                }
            }
        }
        /*===================================================用户信息相关=========================================================*/
        // 查询签约时的城市位置
        IResponse<Map<String, String>> cityCode = SpringContextHolder.getBean(ApplyServiceFeign.class).getCityCode(applyNo);
        //1.查询进件客户基础信息
        List<CaseCustInfo> caseCustInfos = caseCustInfoService.getBaseMapper().selectList(Wrappers.<CaseCustInfo>query().lambda().eq(CaseCustInfo::getApplyNo, applyNo));
        String telPhone = null;
        for (CaseCustInfo caseCustInfo : caseCustInfos) {
            //2.查询客户明细信息
            CaseCustIndividual custIndividual = caseCustInfoService.getCaseCustIndividualService().getBaseMapper().selectOne(Wrappers.<CaseCustIndividual>query().lambda().eq(CaseCustIndividual::getCustId, caseCustInfo.getId()));
            //3.查询客户地址信息
            List<CaseCustAddress> custAddressList = caseCustInfoService.getCaseCustAddressService().getBaseMapper().selectList(Wrappers.<CaseCustAddress>query().lambda().eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
            //4.查询联系人信息
            List<CaseCustContact> caseCustContacts = caseCustContactService.getBaseMapper().selectList(Wrappers.<CaseCustContact>query().lambda().eq(CaseCustContact::getApplyNo, applyNo));

            if (Objects.equals(caseCustInfo.getCustRole(), "01")) {

                if("0000".equals(cityCode.getCode()) && null != cityCode.getData()){
                    riskBody.put("cauthorizecity",cityCode.getData().get("01"));
                    riskBody.put("eauthorizecity",cityCode.getData().get("00"));
                }

                telPhone = caseCustInfo.getTelPhone();
                riskBody.put("employeeno", caseCustInfo.getEmployNo());
                //承租人
                riskBody.put("customname", caseCustInfo.getCustName());//承租人姓名
                riskBody.put("customidnumber", caseCustInfo.getCertNo());//承租人证件号码
                riskBody.put("orderphone", caseCustInfo.getTelPhone());//承租人联系电话
                riskBody.put("childrennumber", custIndividual.getNumberOffspring());//承租人子女人数
                riskBody.put("customgender", getDicTitleOrValue("sex", custIndividual.getSex(), 1));//承租人性别
                //有效期=证件开始时间+证件结束时间
                Date certEndDate = caseCustInfo.getCertEndDate();
                Date certStartDate = caseCustInfo.getCertStartDate();
                if (certEndDate != null && certStartDate != null) {
                    riskBody.put("customidend", format.format(certStartDate) + "-" + format.format(certEndDate));//承租人身份证有效期
                }
                riskBody.put("familysize", custIndividual.getFamilyNumbers());//承租人家庭人数
                riskBody.put("wechatnumber", custIndividual.getWechatQq());//承租人微信或QQ
                riskBody.put("educationlevel", getDicTitleOrValue("highestEducation", custIndividual.getHighestEducation(), 1));//承租人教育程度
                riskBody.put("maritalstatus", getDicTitleOrValue("maritalStatus", custIndividual.getMaritalStatus(), 1));//承租人婚姻状况
                riskBody.put("registrationtype", getDicTitleOrValue("registeredResidenceType", custIndividual.getRegisteredResidenceType(), 1));//承租人户籍类型
                riskBody.put("cusemail", custIndividual.getEmail());//承租人邮箱
                riskBody.put("companyphone", custIndividual.getUnitTelPhone());//承租人单位联系电话
                riskBody.put("companyname", custIndividual.getUnitName());//承租人工作单位名称
                riskBody.put("companytype", getDicTitleOrValue("unitNature", custIndividual.getUnitType(), 1));//承租人单位类型
                riskBody.put("position", getDicTitleOrValue("position", custIndividual.getPosition(), 1));//承租人职务
                riskBody.put("mouthincome", custIndividual.getMonthlyIncome());//承租人月收入
                riskBody.put("cusage", custIndividual.getAge());//承租人年龄
                //地址信息
                for (CaseCustAddress caseCustAddress : custAddressList) {
                    JSONObject addressJson = new JSONObject();
                    addressJson.put("province", caseCustAddress.getProvince());
                    addressJson.put("city", caseCustAddress.getCity());
                    addressJson.put("county", caseCustAddress.getCounty());
                    CaseCustAddress transferLabel = addressCodeTransferLabel(addressJson);
                    String permanentAddress = transferLabel.getProvince() + transferLabel.getCity() + transferLabel.getCounty() + caseCustAddress.getDetailAddress();
                    //2023-09-05业务需求，如果是specialAddressList数据中的省份，城市值=省名称
                    String city = specialAddressList.contains(transferLabel.getProvince()) ? transferLabel.getProvince() : transferLabel.getCity();
                    switch (caseCustAddress.getAddressType()) {
                        case "1": {
                            //户籍地址
                            riskBody.put("liveprovince",transferLabel.getProvince());
                            riskBody.put("residenceaddress", permanentAddress);//承租人户籍地址
                            riskBody.put("registered", city);//承租人户籍城市
                            break;
                        }
                        case "2": {
                            //居住地址
                            riskBody.put("nativeprovince",transferLabel.getProvince());
                            riskBody.put("currentresidenceaddress", permanentAddress);//承租人现居住地地址
                            riskBody.put("residentialcity", city);//承租人居住城市
                            riskBody.put("currentresidencetype", getDicTitleOrValue("houseType", caseCustAddress.getHouseType(), 1));//承租人现居住地房产类型
                            if (caseCustAddress.getHouseStartDate() != null) {
                                riskBody.put("currentresidencestartdate", format2.format(caseCustAddress.getHouseStartDate()));//承租人居住地开始居住时间
                            }
                            break;
                        }
                        case "3": {
                            //单位地址
                            riskBody.put("companyaddress", permanentAddress);//承租人工作单位地址
                            riskBody.put("workprovince",transferLabel.getProvince());
                            riskBody.put("workplace", city);//承租人工作城市
                            break;
                        }
                        default: {

                        }
                    }
                }
                //联系人信息
                for (CaseCustContact caseCustContact : caseCustContacts) {
                    JSONObject addressJson = new JSONObject();
                    addressJson.put("province", caseCustContact.getLivingProvince());
                    addressJson.put("city", caseCustContact.getLivingCity());
                    addressJson.put("county", caseCustContact.getLivingCounty());
                    CaseCustAddress transferLabel = addressCodeTransferLabel(addressJson);
                    String permanentAddress = transferLabel.getProvince() + transferLabel.getCity() + transferLabel.getCounty() + caseCustContact.getDetailAddress();
                    if (Objects.equals("1", caseCustContact.getFirstFlag())) {
                        //第一联系人信息
                        riskBody.put("customname1", caseCustContact.getCustName());//第一联系人姓名
                        //应byd人员要求，单独对这几个值进行映射
                        switch (caseCustContact.getCustRelation()) {
                            case "00006": {
                                //夫-妻
                                riskBody.put("relationship1", "配偶");//第一联系人与承租人关系
                                break;
                            }
                            case "00008": {
                                //直系亲属-父母
                                riskBody.put("relationship1", "父母");//第一联系人与承租人关系
                                break;
                            }
                            case "00009": {
                                //直系亲属-子女
                                riskBody.put("relationship1", "子女");//第一联系人与承租人关系
                                break;
                            }
                            default: {
                                riskBody.put("relationship1", getDicTitleOrValue("custRelation", caseCustContact.getCustRelation(), 1));//第一联系人与承租人关系
                            }
                        }
                        riskBody.put("idnumber1", caseCustContact.getCertNo());//第一联系人身份证号码
                        riskBody.put("phone1", Optional.ofNullable(caseCustContact.getTelPhone()).orElse(""));//第一联系人联系电话
                        riskBody.put("currentresidenceaddress1", permanentAddress);//第一联系人联系地址
                        riskBody.put("companyname1", caseCustContact.getCompanyName());//第一联系人单位名称
                    } else {
                        //紧急联系人信息
                        riskBody.put("customname2", caseCustContact.getCustName());//紧急联系人姓名
                        //应byd人员要求，单独对这几个值进行映射
                        switch (caseCustContact.getCustRelation()) {
                            case "00006": {
                                //夫-妻
                                riskBody.put("relationship2", "配偶");//紧急联系人与承租人关系
                                break;
                            }
                            case "00008": {
                                //直系亲属-父母
                                riskBody.put("relationship2", "父母");//紧急联系人与承租人关系
                                break;
                            }
                            case "00009": {
                                //直系亲属-子女
                                riskBody.put("relationship2", "子女");//紧急联系人与承租人关系
                                break;
                            }
                            default: {
                                riskBody.put("relationship2", getDicTitleOrValue("custRelation", caseCustContact.getCustRelation(), 1));//紧急联系人与承租人关系
                            }
                        }
                        riskBody.put("idnumber2", caseCustContact.getCertNo());//紧急联系人身份证号码
                        riskBody.put("phone2", Optional.ofNullable(caseCustContact.getTelPhone()).orElse(""));//紧急联系人联系电话
                        riskBody.put("currentresidenceaddress2", permanentAddress);//紧急联系人联系地址
                        riskBody.put("companyname2", caseCustContact.getCompanyName());//紧急联系人单位名称
                    }
                }
            }
            if (Objects.equals("1", caseCustInfo.getIsFirstGuarantor())) {
                //第一担保人
                riskBody.put("ensurecustomname1", caseCustInfo.getCustName());//保证人姓名
                riskBody.put("ensureidnumber1", caseCustInfo.getCertNo());//保证人证件号码
                riskBody.put("ensurephone1", Optional.ofNullable(caseCustInfo.getTelPhone()).orElse(""));//保证人联系电话
                riskBody.put("ensuregender1", getDicTitleOrValue("sex", custIndividual.getSex(), 1));//保证人性别
                //应byd人员要求，单独对这几个值进行映射
                switch (caseCustInfo.getCustRelation()) {
                    case "00006": {
                        //夫-妻
                        riskBody.put("relationshipName1", "配偶");//保证人与承租人关系
                        break;
                    }
                    case "00008": {
                        //直系亲属-父母
                        riskBody.put("relationshipName1", "父母");//保证人与承租人关系
                        break;
                    }
                    case "00009": {
                        //直系亲属-子女
                        riskBody.put("relationshipName1", "子女");//保证人与承租人关系
                        break;
                    }
                    default: {
                        riskBody.put("relationshipName1", Optional.ofNullable(getDicTitleOrValue("custRelation", caseCustInfo.getCustRelation(), 1)).orElse(""));//保证人与承租人关系
                    }
                }
                riskBody.put("ensurewechatnumber1", custIndividual.getWechatQq());//保证人微信或QQ
                //有效期=证件开始时间+证件结束时间
                Date certEndDate = caseCustInfo.getCertEndDate();
                Date certStartDate = caseCustInfo.getCertStartDate();
                if (certEndDate != null && certStartDate != null) {
                    riskBody.put("ensureidend", format.format(certStartDate) + "-" + format.format(certEndDate));//保证人身份证有效期
                }
                riskBody.put("ensureemail1", custIndividual.getEmail());//保证人邮箱
                riskBody.put("ensureeducationlevel1", getDicTitleOrValue("highestEducation", custIndividual.getHighestEducation(), 1));//保证人教育程度
                riskBody.put("ensurecompanyname1", custIndividual.getUnitName());//保证人工作单位名称
                riskBody.put("ensurecompanyphone1", custIndividual.getUnitTelPhone());//保证人单位固话
                riskBody.put("ensurecompanytype1", getDicTitleOrValue("unitNature", custIndividual.getUnitType(), 1));//保证人单位类型
                riskBody.put("ensureposition1", getDicTitleOrValue("position", custIndividual.getPosition(), 1));//保证人职位
                riskBody.put("ensuremouthincome1", custIndividual.getMonthlyIncome());//保证人月收入
                riskBody.put("ensurefamilysize1", custIndividual.getFamilyNumbers());//保证人家庭人数
                riskBody.put("ensurechildrennumber1", custIndividual.getNumberOffspring());//保证人子女人数
                riskBody.put("ensuremaritalstatus1", getDicTitleOrValue("maritalStatus", custIndividual.getMaritalStatus(), 1));//保证人婚姻状况
                riskBody.put("spousename1", custIndividual.getSpouseName());//保证人配偶姓名
                riskBody.put("spousephone1", custIndividual.getSpousePhone());//保证人配偶电话
                riskBody.put("ensureage", custIndividual.getAge());//保证人年龄
                //riskBody.put("ensureworkyearsdate1",);//保证人本单位工作年限（当前无此字段）
                riskBody.put("workyearsdate", custIndividual.getWorkAge());//保证人现工作年限
                //地址信息
                for (CaseCustAddress caseCustAddress : custAddressList) {
                    JSONObject addressJson = new JSONObject();
                    addressJson.put("province", caseCustAddress.getProvince());
                    addressJson.put("city", caseCustAddress.getCity());
                    addressJson.put("county", caseCustAddress.getCounty());
                    CaseCustAddress transferLabel = addressCodeTransferLabel(addressJson);
                    String permanentAddress = transferLabel.getProvince() + transferLabel.getCity() + transferLabel.getCounty() + caseCustAddress.getDetailAddress();
                    //2023-09-05业务需求，如果是specialAddressList数据中的省份，城市值=省名称
                    String city = specialAddressList.contains(transferLabel.getProvince()) ? transferLabel.getProvince() : transferLabel.getCity();
                    switch (caseCustAddress.getAddressType()) {
                        case "1": {
                            //户籍地址
                            riskBody.put("ensureregistered", city);//保证人户籍城市
                            riskBody.put("ensureresidenceaddress1", permanentAddress);//保证人户籍地址
                            break;
                        }
                        case "2": {
                            //居住地址
                            riskBody.put("ensureresidentialcity", city);//保证人居住城市
                            riskBody.put("ensurecurrentresidenceaddress1", permanentAddress);//保证人现居住地址
                            riskBody.put("ensurecurrentresidencetype1", getDicTitleOrValue("houseType", caseCustAddress.getHouseType(), 1));//保证人居住地房产类型
                            if (caseCustAddress.getHouseStartDate() != null) {
                                riskBody.put("ensurecurrentresidencestartdate1", format2.format(caseCustAddress.getHouseStartDate()));//保证人居住地开始居住时间
                            }
                            break;
                        }
                        case "3": {
                            //单位地址
                            riskBody.put("ensurecompanyaddress1", permanentAddress);//保证人单位地址
                            riskBody.put("ensurecompanycity1", city);//保证人单位城市
                            break;
                        }
                        default: {

                        }
                    }
                }
            }
        }
        //应业务需求如下字段为空时设置空字符串：phone1（第一联系人联系电话）、phone2（紧急联系人联系电话）、ensurephone1（保证人联系电话）、relationshipName1（保证人与承租人关系）
        riskBody.put("phone1", Optional.ofNullable(riskBody.getString("phone1")).orElse(""));
        riskBody.put("phone2", Optional.ofNullable(riskBody.getString("phone2")).orElse(""));
        riskBody.put("ensurephone1", Optional.ofNullable(riskBody.getString("ensurephone1")).orElse(""));
        //2023-10-13应业务需求把字段名relationshipname1改为relationshipName1
        riskBody.put("relationshipName1", Optional.ofNullable(riskBody.getString("relationshipName1")).orElse(""));
        //2023-12-14应业务需求把字段“保证人单位固话”设置默认值
        riskBody.put("ensurecompanyphone1",Optional.ofNullable(riskBody.getString("ensurecompanyphone1")).orElse(""));
        //是否首次提交（2024-07-22需求）
        ThirdData selectData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, applyNo)
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        String riskCode = Optional.ofNullable(selectData).map(ThirdData::getResponse).map(JSONObject::parseObject).map(json -> json.getString(RiskResConstants.FINAL_DEAL_TYPE_CODE)).orElse("");
        if (Objects.equals(AfsEnumUtil.key(YesOrNoEnum.YES),firstSubmit)
                ||AfsEnumUtil.key(RiskStatus.RISK_RES_ACCEPT).equals(riskCode)) {
            firstSubmit=AfsEnumUtil.key(YesOrNoEnum.YES);
        }
        riskBody.put("repeatedsubmit",firstSubmit);
        riskBody.put("store_nature", ChannelBelongEnum.create(channelBaseInfo.getChannelBelong()).getDesc());
        riskBody.put("equeryid",equeryid);
        riskBody.put("cqueryid",cqueryid);
        //设置金融专员手机号（2025-05-08，需求编号813）
        riskBody.put("userphone",caseBaseInfo.getSellerPhone());
        /*===================================================信鸽信息相关=========================================================*/
        DecisionEngineVO engineVO = carrierPigeonService.getDecisionEngineParameter(riskBody.getString("customidnumber"),applyNo,true);
        if (CollUtil.isNotEmpty(engineVO.getSites())) {
            engineVO.getSites().forEach(site -> {
                CarrierPigeonSiteEnums siteEnum = CarrierPigeonSiteEnums.getSiteEnum(site);
                switch (siteEnum) {
                    case APP_GJZWFW_DZSB:
                        setIfPresent(riskBody, "slcompanyname", engineVO.getSlCompanyName());
                        setIfPresent(riskBody, "slaccountstatus", engineVO.getSlAccountStatus());
                        setIfPresent(riskBody, "slindbase", engineVO.getSlInBase());
                        break;
                    case APP_GJZWFW_GJJ, CHROME_GJJ_ANY:
                        setIfPresent(riskBody, "pafcompanyname", engineVO.getPafCompanyName());
                        setIfPresent(riskBody, "pafaccountstatus", engineVO.getPafAccountStatus());
                        setIfPresent(riskBody, "pafindbase", engineVO.getPafInBase());
                        break;
                    case APP_TAX_INCOME:
                        setIfPresent(riskBody, "taxappcompanyname", engineVO.getTaxCompanyName());
                        setIfPresent(riskBody, "taxappincome", engineVO.getTaxIncome());
                        break;
                    case BEEHIVE_ALIPAY:
                        setIfPresent(riskBody, "alipay_monthstartdate", engineVO.getAlipayMonthStartDate());
                        setIfPresent(riskBody, "alipay_monthenddate", engineVO.getAlipayMonthEndDate());
                        setIfPresent(riskBody, "alipaysum_ttl_cnt_12m", engineVO.getAlipaySumTtlCnt12m());
                        setIfPresent(riskBody, "alipaycredit_limit_max", engineVO.getAlipayCreditLimitMax());
                        break;
                    case BEEHIVE_WECHATPAY:
                        setIfPresent(riskBody, "consumer_monthstartdate", engineVO.getConsumerMonthStartDate());
                        setIfPresent(riskBody, "consumer_monthenddate", engineVO.getConsumerMonthEndDate());
                        setIfPresent(riskBody, "consumersum_ttl_cnt_12m", engineVO.getConsumerSumTtlCnt12m());
                        setIfPresent(riskBody, "consumercredit_limit_max", engineVO.getConsumerCreditLimitMax());
                        break;
                    default:
                        // 银行流水
                        if (siteEnum.getType() == 3) {
                            setIfPresent(riskBody, "bank_headercnt_revenue", engineVO.getBankHeaderCntRevenue());
                            setIfPresent(riskBody, "bank_headercnt_expense", engineVO.getBankHeaderCntExpense());
                            setIfPresent(riskBody, "bank_headeranalysis_end", engineVO.getBankHeaderAnalysisEnd());
                            setIfPresent(riskBody, "bank_headeranalysis_begin", engineVO.getBankHeaderAnalysisBegin());
                            setIfPresent(riskBody, "bankcredit_limit_max", engineVO.getBankCreditLimitMax());
                        }
                        break;
                }
            });
        }
        try {
            // 品牌为腾势才调用电子工牌
            if (CaseConstants.BADGE_BRAND_NAME.equals(caseCarInfo.getBrandName())) {
                executeBadgeQuery(riskBody, telPhone, applyNo);
            }
        } catch (Exception e) {
            log.error("【电子工牌】查询异常", e);
        }
        log.warn("申请编号={}的决策引擎入参={}",applyNo,JSONObject.toJSON(riskBody));
        return riskBody;
    }

    /**
     * 调用电子工牌
     *
     * @param riskBody
     * @param telPhone
     */
    public void executeBadgeQuery(JSONObject riskBody, String telPhone, String applyNo) {
        // 调用电子工牌
        BadgeResDataVo badgeResDataVo = electronBadgeService.executeBadgeQuery(new BadgeReqCondition(telPhone));
        Assert.isTrue(CollectionUtil.isNotEmpty(badgeResDataVo.getData()), "查询电子工牌数据为空！");
        // 保存电子工牌
        List<CaseElectronBadgeInfo> electronBadgeInfoList = electronBadgeInfoService.saveElectronBadgeInfo(badgeResDataVo, applyNo);
        Assert.isTrue(CollectionUtil.isNotEmpty(electronBadgeInfoList), "电子工牌数据为空！");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        electronBadgeInfoList.sort(Comparator.comparing(
                info -> LocalDateTime.parse(info.getStartTime(), formatter)
        ));
        CaseElectronBadgeInfo caseElectronBadgeInfo = electronBadgeInfoList.get(0);
        // 基础信息
        riskBody.put("customerNameD", caseElectronBadgeInfo.getCustomerName());
        riskBody.put("customerPhoneD", caseElectronBadgeInfo.getCustomerPhone());
        riskBody.put("salesPhone", caseElectronBadgeInfo.getSalesPhone());
        riskBody.put("salesName", caseElectronBadgeInfo.getSalesName());
        riskBody.put("salesShopName", caseElectronBadgeInfo.getSalesShopName());
        riskBody.put("voiceName", caseElectronBadgeInfo.getVoiceName());
        riskBody.put("startTime", caseElectronBadgeInfo.getStartTime());
        riskBody.put("totalTime", caseElectronBadgeInfo.getTotalTime());
        // 正面标签
        riskBody.put("vehicle_specifications", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getVehicleSpecifications));
        riskBody.put("version_differences", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getVersionDifferences));
        riskBody.put("operating_costs", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getOperatingCosts));
        riskBody.put("color_preferences", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getColorPreferences));
        riskBody.put("financial_policies", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getFinancialPolicies));
        riskBody.put("loan_details", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getLoanDetails));
        riskBody.put("schedule_a_test_drive", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getScheduleTestDrive));
        riskBody.put("compare_quotes", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getCompareQuotes));
        riskBody.put("gifts", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getGifts));
        riskBody.put("purpose_of_purchase", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getPurposeOfPurchase));
        riskBody.put("vehicle_space", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getVehicleSpace));
        riskBody.put("delivery_information", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getDeliveryInformation));
        riskBody.put("refund_inquiries", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getRefundInquiries));
        riskBody.put("trade_in", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getTradeIn));
        riskBody.put("competitor_comparisons", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getCompetitorComparisons));
        riskBody.put("competitor_reviews", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getCompetitorReviews));
        // 负面标签
        riskBody.put("fictitious_invoices", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getFictitiousInvoices));
        riskBody.put("proxy_purchasing_behavior", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getProxyPurchasingBehavior));
        riskBody.put("fictitious_fund_flows", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getFictitiousFundFlows));
        riskBody.put("inadequate_down_payment", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getInadequateDownPayment));
        riskBody.put("debt_issues", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getDebtIssues));
        riskBody.put("overdue", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getOverdue));
        riskBody.put("business_operation", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getBusinessOperation));
        riskBody.put("marital_status", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getMaritalStatus));
        riskBody.put("driver_license", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getDriverLicense));
        riskBody.put("source_of_down_payment", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getSourceOfDownPayment));
        riskBody.put("first_time_loan", getFinalValue(electronBadgeInfoList, CaseElectronBadgeInfo::getFirstTimeLoan));
    }
    /**
     * 0-1，处理器
     * @param badgeInfoList
     * @param getter
     * @return
     */
    private String getFinalValue(List<CaseElectronBadgeInfo> badgeInfoList, Function<CaseElectronBadgeInfo, String> getter) {
        if (badgeInfoList == null || badgeInfoList.isEmpty()) {
            return "0";
        }
        return badgeInfoList.stream()
                .anyMatch(info -> "1".equals(getter.apply(info))) ? "1" : "0";
    }
    private void setIfPresent(JSONObject riskBody, String key, String field) {
        if (StrUtil.isNotEmpty(field)) {
            riskBody.put(key, field);
        }
    }
}
