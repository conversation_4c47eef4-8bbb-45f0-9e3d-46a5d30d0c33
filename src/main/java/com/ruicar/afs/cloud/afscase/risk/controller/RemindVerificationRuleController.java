package com.ruicar.afs.cloud.afscase.risk.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.entity.RemindVerificationRule;
import com.ruicar.afs.cloud.afscase.risk.service.CodeCategoryOrderService;
import com.ruicar.afs.cloud.afscase.risk.service.RemindVerificationRuleService;
import com.ruicar.afs.cloud.afscase.risk.vo.RemindVerificationRuleCondition;
import com.ruicar.afs.cloud.afscase.risk.vo.RemindVerificationRuleImportVo;
import com.ruicar.afs.cloud.afscase.vehicle.condition.MultipartFileCondition;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 提醒核实规则核查内容表表
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/remindVerificationRule")
public class RemindVerificationRuleController {
	private final RemindVerificationRuleService remindVerificationRuleService;
	private final CodeCategoryOrderService codeCategoryOrderService;

	@PostMapping("/saveOrUpdate")
	@ApiOperation("保存修改提醒核实规则核查内容表信息")
	public IResponse saveOrUpdate(@RequestBody RemindVerificationRule remindVerificationRule) {
		log.info("保存修改提醒核实规则核查内容表信息,处理人={}, 入参=[{}]", SecurityUtils.getUsername(), JSONUtil.parse(remindVerificationRule));
		return remindVerificationRuleService.saveOrUpdateRvr(remindVerificationRule);
	}

	@PostMapping("/getById")
	@ApiOperation("查询提醒核实规则核查内容表信息")
	public IResponse getById(@RequestBody RemindVerificationRule remindVerificationRule) {
		Assert.notNull(remindVerificationRule.getId(), "查询提醒核实规则核查内容表信息，id参数不能为空！");
		return IResponse.success(remindVerificationRuleService.getById(remindVerificationRule.getId()));
	}

	@PostMapping("/deleteById")
	@ApiOperation("删除提醒核实规则核查内容表信息")
	public IResponse deleteById(@RequestBody RemindVerificationRule remindVerificationRule) {
		log.info("删除提醒核实规则核查内容表信息，入参[{}]", JSONUtil.parse(remindVerificationRule));
		Assert.notNull(remindVerificationRule.getId(), "删除提醒核实规则核查内容表信息，id参数不能为空！");
		return remindVerificationRuleService.remove(Wrappers.<RemindVerificationRule>lambdaQuery().eq(RemindVerificationRule::getId, remindVerificationRule.getId())) ? IResponse.success("删除成功！") : IResponse.fail("删除失败！");
	}

	@PostMapping("/queryByPage")
	@ApiOperation("分页查询所有的提醒核实规则核查内容表信息")
	public IResponse queryByPage(@RequestBody RemindVerificationRuleCondition condition) {
		return remindVerificationRuleService.queryByPage(condition);
	}

	@PostMapping("/queryAllVerificationRule")
	@ApiOperation("分页查询所有的提醒核实规则核查内容表信息")
	public IResponse queryAllVerificationRule() {
		return remindVerificationRuleService.getAllShowRuleCodes();
	}

	/**
	 * 导出提醒核实规则核查内容表信息
	 *
	 * @param condition 查询条件
	 * @param response  HTTP响应对象，用于写出Excel文件
	 */
	@PostMapping("/export")
	@ApiOperation("导出提醒核实规则核查内容表信息")
	public void exportRemindVerificationRule(@RequestBody RemindVerificationRuleCondition condition, HttpServletResponse response) {
		ExcelWriter excelWriter = null;
		try {
			List<RemindVerificationRule> remindVerificationRuleList = remindVerificationRuleService.queryByCondition(condition);
			log.info("导出提醒核实规则核查内容表信息，共{}条数据", remindVerificationRuleList.size());
			if (remindVerificationRuleList.isEmpty()) {
				throw new AfsBaseException("没有数据可导出！");
			}

			response.setContentType("application/vnd.ms-excel");
			String fileName = URLEncoder.encode("提醒核实规则核查内容表信息", StandardCharsets.UTF_8);
			response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

			// 创建Excel写入器并设置工作表
			excelWriter = EasyExcel.write(response.getOutputStream())
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.build();

			List<String> excludeColumns = Arrays.asList("id", "delFlag", "createBy", "updateBy", "createTime", "updateTime");
			WriteSheet writeSheet = EasyExcel.writerSheet("提醒核实规则核查内容表信息")
					.head(RemindVerificationRule.class)
					.excludeColumnFiledNames(excludeColumns) // 排除不需要的字段
					.build();

			// 写入数据到Excel文件
			excelWriter.write(remindVerificationRuleList, writeSheet);
		} catch (IOException e) {
			log.error("导出失败", e);
			throw new AfsBaseException("导出失败！");
		} finally {
			// 手动调用finish()来关闭ExcelWriter并释放资源
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
	}

	/**
	 * 导入提醒核实规则核查内容表信息
	 *
	 * @param condition 上传的Excel文件
	 * @return 导入结果的响应
	 */
	@PostMapping("/import")
	@ApiOperation("导入提醒核实规则核查内容表信息")
	@Transactional(rollbackFor = Exception.class)
	public IResponse importRemindVerificationRule(@ModelAttribute MultipartFileCondition condition) {
		MultipartFile file = condition.getUploadFile();
		Assert.notNull(file, "文件不能为空！");
		log.info("导入文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

		try (InputStream inputStream = file.getInputStream()) {
			// 使用监听器方式读取Excel，更适合大数据量导入
			RemindVerificationRuleImportListener listener = new RemindVerificationRuleImportListener();

			EasyExcel.read(inputStream)
					.head(RemindVerificationRuleImportVo.class)  // 使用正确的VO类
					.sheet()
					.registerReadListener(listener)
					.doRead();
			if (listener.hasErrors()) {
				return IResponse.fail("导入失败，错误信息: " +
						String.join("；", listener.getErrorMessages()));
			}
			List<RemindVerificationRuleImportVo> dataList = listener.getDataList();
			if (CollUtil.isEmpty(dataList)) {
				return IResponse.fail("导入失败，文件内容为空！");
			}

			log.info("成功读取 {} 条数据", dataList.size());

			List<CodeCategoryOrder> codeCategoryOrderList = codeCategoryOrderService.list();
			Map<String, CodeCategoryOrder> codeCategoryOrderMap = codeCategoryOrderList.stream()
					.collect(Collectors.toMap(CodeCategoryOrder::getRuleCode, Function.identity()));
			// 转换VO为实体并批量保存
			List<RemindVerificationRule> entities = dataList.stream()
					.map(vo -> convertToEntity(vo, codeCategoryOrderMap))
					.collect(Collectors.toList());
			ruleCodeNecessityCheck(entities);
			remindVerificationRuleService.saveBatch(entities);
			return IResponse.success("成功导入 " + entities.size() + " 条数据");
		} catch (IOException e) {
			log.error("导入文件处理异常", e);
			throw new AfsBaseException("导入失败: " + e.getMessage());
		} catch (Exception e) {
			log.error("导入过程发生异常", e);
			throw new AfsBaseException("导入处理失败: " + e.getMessage());
		}
	}

	/**
	 * 规则码必要性检查
	 * @param entities 导入的VO对象
	 */
	public void ruleCodeNecessityCheck(List<RemindVerificationRule> entities) {
		// 将导入的VO对象转换为实体对象
		List<String> ruleCodeList = entities.stream()
				.map(RemindVerificationRule::getRuleCode)
				.collect(Collectors.toList());

		// 统计规则码出现的次数
		Map<String, Long> codeCountMap = ruleCodeList.stream()
				.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
		// 获取出现次数大于1的规则码
		List<String> duplicateCodes = codeCountMap.entrySet().stream()
				.filter(entry -> entry.getValue() > 1)
				.map(Map.Entry::getKey)
				.collect(Collectors.toList());
		// 如果存在重复的规则码，抛出异常
		if (!duplicateCodes.isEmpty()) {
			throw new AfsBaseException("导入失败，规则码不能重复！重复的规则码: " + duplicateCodes);
		}

		// 校验库里是否已存在相同的规则码
		List<RemindVerificationRule> existingRules = remindVerificationRuleService.list(
				Wrappers.<RemindVerificationRule>lambdaQuery()
						.in(RemindVerificationRule::getRuleCode, ruleCodeList)
		);
		// 如果存在相同的规则码，抛出异常
		if (CollUtil.isNotEmpty(existingRules)) {
			String existingRuleCodes = existingRules.stream()
					.map(RemindVerificationRule::getRuleCode)
					.collect(Collectors.joining(", "));
			throw new AfsBaseException("导入失败，以下规则码下的人工核查内容已存在: " + existingRuleCodes);
		}
	}

	/**
	 * 导入模板下载
	 */
	@GetMapping("/downloadTemplate")
	@ApiOperation("提醒核实规则导入模板下载")
	public void downloadTemplate(HttpServletResponse response) {
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());

		String fileName = URLEncoder.encode("提醒核实规则导入模板", StandardCharsets.UTF_8)
				.replaceAll("\\+", "%20");
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

		try {
			// 创建带示例数据的模板
			List<RemindVerificationRuleImportVo> templateData = Arrays.asList(
					new RemindVerificationRuleImportVo("L028", "决策引擎提示项1", "排查风险1"),
					new RemindVerificationRuleImportVo("L029", "决策引擎提示项2", "排查风险2")
			);

			EasyExcel.write(response.getOutputStream())
					.head(RemindVerificationRuleImportVo.class)
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.sheet("模板")
					.doWrite(templateData);
		} catch (IOException e) {
			log.error("模板下载失败", e);
			throw new AfsBaseException("模板下载失败: " + e.getMessage());
		}
	}

	/**
	 * VO转Entity转换方法
	 */
	private RemindVerificationRule convertToEntity(RemindVerificationRuleImportVo vo, Map<String, CodeCategoryOrder> codeCategoryOrderMap) {
		RemindVerificationRule entity = new RemindVerificationRule();
		BeanUtils.copyProperties(vo, entity);

		// 设置默认值或特殊处理
		if (StrUtil.isBlank(entity.getManualCheckContent())) {
			entity.setManualCheckContent("排查风险");
		}

		if (StrUtil.isBlank(entity.getActiveFlag())) {
			entity.setActiveFlag(CommonConstants.COMMON_YES);
		}

		// 从枚举获取分类
		CodeCategoryOrder codeCategoryOrder = codeCategoryOrderMap.get(vo.getRuleCode());
		if (ObjectUtil.isEmpty(codeCategoryOrder)) {
			throw new AfsBaseException("规则码 " + vo.getRuleCode() + " 不存在！");
		}
		entity.setCategory(codeCategoryOrder.getCategory());

		return entity;
	}

	/**
	 * 导入监听器（带行号提示）
	 */
	@Getter
	public static class RemindVerificationRuleImportListener extends AnalysisEventListener<RemindVerificationRuleImportVo> {
		private final List<RemindVerificationRuleImportVo> dataList = new ArrayList<>();
		private final List<String> errorMessages = new ArrayList<>();

		@Override
		public void invoke(RemindVerificationRuleImportVo data, AnalysisContext context) {
			// 获取当前行号（注意：EasyExcel的行号从0开始，通常需要+1显示给用户）
			int rowIndex = context.readRowHolder().getRowIndex() + 1;

			try {
				// 数据校验
				if (StrUtil.isBlank(data.getRuleCode())) {
					throw new ExcelAnalysisException("规则码不能为空！行数为：" + rowIndex);
				}
				if (StrUtil.isBlank(data.getManualCheckContent())) {
					throw new ExcelAnalysisException("人工核查内容不能为空！行数为：" + rowIndex);
				}

				dataList.add(data);
			} catch (ExcelAnalysisException e) {
				// 捕获异常并记录错误信息（带行号）
				errorMessages.add(String.format("第%d行数据错误: %s", rowIndex, e.getMessage()));
				throw e; // 继续抛出以停止处理
			}
		}

		@Override
		public void doAfterAllAnalysed(AnalysisContext context) {
			if (!errorMessages.isEmpty()) {
				log.error("导入过程中发现错误: {}", errorMessages);
			}
			log.info("Excel解析完成共读取{}条有效数据", dataList.size());
		}

		public boolean hasErrors() {
			return !errorMessages.isEmpty();
		}
	}
}
