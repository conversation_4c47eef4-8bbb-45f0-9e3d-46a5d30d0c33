package com.ruicar.afs.cloud.afscase.risk.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.service.CodeCategoryOrderService;
import com.ruicar.afs.cloud.afscase.risk.vo.CodeCategoryOrderCondition;
import com.ruicar.afs.cloud.afscase.vehicle.condition.MultipartFileCondition;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * 规则码分类排序表表
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/codeCategoryOrder")
public class CodeCategoryOrderController {
    private final CodeCategoryOrderService codeCategoryOrderService;

    @PostMapping("/saveOrUpdate")
    @ApiOperation("修改规则码分类排序表信息")
    public IResponse saveOrUpdate(@RequestBody CodeCategoryOrder codeCategoryOrder) {
        log.info("修改规则码分类排序表信息，入参[{}]", JSONUtil.parse(codeCategoryOrder));
        return codeCategoryOrderService.saveOrUpdateCco(codeCategoryOrder);
    }

    @PostMapping("/getById")
    @ApiOperation("查询规则码分类排序表信息")
    public IResponse getById(@RequestBody CodeCategoryOrder codeCategoryOrder) {
        Assert.notNull(codeCategoryOrder.getId(), "查询规则码分类排序表信息，id参数不能为空！");
        return IResponse.success(codeCategoryOrderService.getById(codeCategoryOrder.getId()));
    }

    @PostMapping("/deleteById")
    @ApiOperation("删除规则码分类排序表信息")
    public IResponse deleteById(@RequestBody CodeCategoryOrder codeCategoryOrder) {
        log.info("删除规则码分类排序表信息，入参[{}]", JSONUtil.parse(codeCategoryOrder));
        Assert.notNull(codeCategoryOrder.getId(), "删除规则码分类排序表信息，id参数不能为空！");
        return codeCategoryOrderService.remove(Wrappers.<CodeCategoryOrder>lambdaQuery().eq(CodeCategoryOrder::getId, codeCategoryOrder.getId())) ? IResponse.success("删除成功！") : IResponse.fail("删除失败！");
    }

    @PostMapping("/queryByPage")
    @ApiOperation("分页查询所有的规则码分类排序表信息")
    public IResponse queryByPage(@RequestBody CodeCategoryOrderCondition condition) {
        return codeCategoryOrderService.queryByPage(condition);
    }

    /**
     * 导出规则码分类排序表信息
     * @param condition 查询条件
     * @param response HTTP响应对象，用于写出Excel文件
     */
    @PostMapping("/export")
    @ApiOperation("导出规则码分类排序表信息")
    public void exportCodeCategoryOrder(@RequestBody CodeCategoryOrderCondition condition, HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            List<CodeCategoryOrder> codeCategoryOrderList = codeCategoryOrderService.queryByCondition(condition);
            log.info("导出规则码分类排序表信息，共{}条数据", codeCategoryOrderList.size());
            if (codeCategoryOrderList.isEmpty()) {
                throw new AfsBaseException("没有数据可导出！");
            }

            response.setContentType("application/vnd.ms-excel");
            String fileName = URLEncoder.encode("规则码分类排序表信息", StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建Excel写入器并设置工作表
            excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();

            List<String> excludeColumns = Arrays.asList("id", "delFlag", "createBy", "updateBy", "createTime", "updateTime");
            WriteSheet writeSheet = EasyExcel.writerSheet("规则码分类排序表信息")
                    .head(CodeCategoryOrder.class)
                    .excludeColumnFiledNames(excludeColumns) // 排除不需要的字段
                    .build();

            // 写入数据到Excel文件
            excelWriter.write(codeCategoryOrderList, writeSheet);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new AfsBaseException("导出失败！");
        } finally {
            // 手动调用finish()来关闭ExcelWriter并释放资源
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 导入规则码分类排序表信息
     * @param condition 上传的Excel文件
     * @return 导入结果的响应
     */
    @PostMapping("/import")
    @ApiOperation("导入规则码分类排序表信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse importCodeCategoryOrder(@ModelAttribute MultipartFileCondition condition) {
        MultipartFile file = condition.getUploadFile();
        Assert.notNull(file, "文件不能为空！");
        log.info("文件名,{},文件大小:{}", file.getOriginalFilename(), file.getSize());

        try {
            // 使用EasyExcel读取上传的文件并转换为实体对象列表
            List<CodeCategoryOrder> dataList = EasyExcel.read(file.getInputStream())
                    .head(CodeCategoryOrder.class)
                    .sheet()
                    .doReadSync();

            if (dataList.isEmpty()) {
                return IResponse.fail("导入失败，文件内容为空！");
            }
            log.info("读取到的数据：{}", JSONUtil.parse(dataList));

            codeCategoryOrderService.saveBatch(dataList);
            return IResponse.success("导入成功！");
        } catch (IOException e) {
            log.error("导入失败", e);
            throw new AfsBaseException("导入失败！");
        }
    }

    /**
     * 导入规则码分类排序表信息模板下载
     * @param response HttpServletResponse
     * @return 导入结果的响应
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation("导入规则码分类排序表信息模板下载")
    public void downloadTemplate(HttpServletResponse response) {
        // 准备模板数据（这里可以放一些示例数据）
        List<CodeCategoryOrder> templateData = Collections.singletonList(new CodeCategoryOrder());

        // 设置响应头，告诉浏览器这是一个下载文件
        response.setContentType("application/vnd.ms-excel");
        String fileName = URLEncoder.encode("规则码分类排序表信息导入模板", StandardCharsets.UTF_8);
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        ExcelWriter excelWriter = null;
		try (OutputStream outputStream = response.getOutputStream()) {
			// 创建Excel写入器并设置工作表
			excelWriter = EasyExcel.write(outputStream)
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.build();
			List<String> excludeColumns = Arrays.asList("id", "delFlag", "createBy", "updateBy", "createTime", "updateTime");
			// 写入表头和模板数据
			WriteSheet writeSheet = EasyExcel.writerSheet("规则码分类排序表信息")
					.head(CodeCategoryOrder.class)  // 使用实体类生成表头
					.excludeColumnFiledNames(excludeColumns) // 排除不需要的字段
					.build();

			// 写入模板数据 去掉部分字段
			excelWriter.write(templateData, writeSheet);
		} catch (IOException e) {
			log.error("下载模板失败", e);
			throw new AfsBaseException("下载模板失败！");
		} finally {
			// 手动调用finish()来关闭ExcelWriter并释放资源
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
    }
}
