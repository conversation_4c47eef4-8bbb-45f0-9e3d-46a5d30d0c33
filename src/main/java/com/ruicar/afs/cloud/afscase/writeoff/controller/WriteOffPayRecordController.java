package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.ContractQueryVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeSubmitVo;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.writeoff.WriteOffPeriodLoanDto;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @author: zhangjin
 * @description 服务费支付详情记录
 * @date: 2024/8/7 17:53
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffPayRecord")
public class WriteOffPayRecordController {
    private final WriteOffPayRecordService writeOffPayRecordService;
    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final WriteOffPayRuleService writeOffPayRuleService;
    private final StringRedisTemplate redisTemplate;

    @PostMapping("/queryByCondition")
    @ApiOperation(value = "条件查询")
    public IResponse queryByCondition(@RequestBody QueryCondition<WriteOffPayRecord> queryCondition) {
        return writeOffPayRecordService.queryByCondition(queryCondition);
    }

    @PostMapping("/exportData")
    @ApiOperation("数据导出")
    public void exportData(@RequestBody WriteOffPayRecord queryCondition, HttpServletResponse httpServletResponse){
        writeOffPayRecordService.exportData(queryCondition, httpServletResponse);
    }

    @PostMapping("/submitData")
    @ApiOperation("发起提取流程")
    @Transactional(rollbackFor = Exception.class)
    public IResponse submitData(@RequestBody FeeSubmitVo submitVo) {
        return writeOffPayRecordService.submitApprove(submitVo);
    }

    /**
     * 查询cbs付款结果，激活等待节点
     */
    @PostMapping("/getCbsPayResult")
    @ApiOperation(value = "查询cbs付款结果")
    public IResponse getCbsPayResult() {
        return writeOffPayRecordService.getCbsPayResult();
    }

    /**
     * 人工付款，激活等待节点
     */
    @PostMapping("/artificialPay")
    @ApiOperation(value = "人工付款")
    @Transactional(rollbackFor = Exception.class)
    public IResponse artificialPay(@RequestBody List<Long> idList) {
        return writeOffPayRecordService.artificialPay(idList);
    }

    /**
     * 失败记录发起重新支付
     */
    @PostMapping("/failedRecordInitiatesRepay")
    @ApiOperation(value = "失败记录发起重新支付")
    public IResponse initiatesRepay(@RequestBody List<Long> idList) {
        return writeOffPayRecordService.failedRecordInitiatesRepay(idList);
    }

    /**
     * 失败支付账单重新生成
     */
    @PostMapping("/reCreatePayBill")
    @ApiOperation(value = "失败支付账单重新生成")
    public IResponse reCreatePayBill() {
        String lockKey = "fee:bill:re";
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "lock", 2, TimeUnit.HOURS);
        if (Boolean.FALSE.equals(lock)) {
            throw new AfsBaseException("在生成中，请勿重复点击");
        }
        Map<String, List<WriteOffPayRecord>> collect = writeOffPayRecordService.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                        .select(WriteOffPayRecord::getBatchNo, WriteOffPayRecord::getCaseNo)
                        .eq(WriteOffPayRecord::getStatus, ChannelServiceFeeEnum.STATUS_4.code))
                .stream().collect(Collectors.groupingBy(WriteOffPayRecord::getBatchNo));
        Assert.isTrue(collect.size() > 0, "没有需要生成的账单！");
        for (String batchNo : collect.keySet()) {
            String caseNo = collect.get(batchNo).get(0).getCaseNo();
            List<WriteOffBaseInfo> list = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getCaseNo, caseNo));
            writeOffPayRuleService.createPayBill(batchNo, list);
        }
        redisTemplate.delete(lockKey);
        return IResponse.success("操作成功");
    }

    @ApiOperation("核销项查询付款明细")
    @PostMapping(value = "/queryPayList")
    public IResponse queryPayList(@RequestBody QueryCondition<ContractQueryVo> queryCondition) {
        List<String> applyNoList = queryCondition.getCondition().getApplyNoList();
        Assert.isTrue(CollUtil.isNotEmpty(applyNoList), "参数异常，核销项编号不能为空");
        WriteOffBaseInvoiceRel invoiceRel = writeOffBaseInvoiceRelService.getOne(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery()
                .select(WriteOffBaseInvoiceRel::getCurrentBatchNo)
                .eq(WriteOffBaseInvoiceRel::getApplyNo, applyNoList.get(0)));
        if (invoiceRel == null) {
            return IResponse.success(new Page<>());
        }
        String batchNo = invoiceRel.getCurrentBatchNo();
        List<WriteOffPayRecord> records = writeOffPayRecordService.list(Wrappers.<WriteOffPayRecord>lambdaQuery().eq(WriteOffPayRecord::getBatchNo, batchNo));
        List<WriteOffPeriodLoanDto> dtoList = new ArrayList<>();
        for (WriteOffPayRecord record : records) {
            for (String periodAmt : record.getBasePeriodAmt().split(";")) {
                String[] split = periodAmt.split(":");
                WriteOffPeriodLoanDto dto = new WriteOffPeriodLoanDto();
                dto.setApplyNo(split[0]);
                dto.setTermNo(record.getTermNo());
                dto.setPayRate(record.getPayRate());
                dto.setPayAmount(new BigDecimal(split[1]));
                dto.setWaitPayTime(record.getWaitPayTime());
                dto.setPayStatus(record.getPayStatus());
                dtoList.add(dto);
            }
        }
        Long pageNumber = queryCondition.getPageNumber();
        Long pageSize = queryCondition.getPageSize();
        Page<WriteOffPeriodLoanDto> page = new Page<>(pageNumber, pageSize);
        List<WriteOffPeriodLoanDto> currentPageList = new ArrayList<>();
        if (dtoList.size() > 0) {
            long currIdx = (pageNumber - 1) * pageSize;
            for (long i = 0; i < pageSize && i < dtoList.size() - currIdx; i++) {
                WriteOffPeriodLoanDto dto = dtoList.get((int) (currIdx + i));
                currentPageList.add(dto);
            }
        }
        page.setRecords(currentPageList);
        page.setTotal(dtoList.size());
        return IResponse.success(page);
    }
}
