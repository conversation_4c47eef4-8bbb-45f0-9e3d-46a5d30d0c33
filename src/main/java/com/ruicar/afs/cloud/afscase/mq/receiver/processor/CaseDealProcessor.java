package com.ruicar.afs.cloud.afscase.mq.receiver.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseSubmitRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkTaskPool;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseSubmitRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.autoaudit.condition.CardDetectRecordChangeCondition;
import com.ruicar.afs.cloud.afscase.autoaudit.service.CardDetectRecordChangeService;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.common.constants.RiskResConstants;
import com.ruicar.afs.cloud.afscase.common.constants.RskRuleConstants;
import com.ruicar.afs.cloud.afscase.common.dto.Car300DTO;
import com.ruicar.afs.cloud.afscase.common.dto.CarCreeperDTO;
import com.ruicar.afs.cloud.afscase.common.enums.InterfaceIdentifyEnum;
import com.ruicar.afs.cloud.afscase.common.service.Car300Service;
import com.ruicar.afs.cloud.afscase.common.service.CarCreeperService;
import com.ruicar.afs.cloud.afscase.common.step.ServiceBox;
import com.ruicar.afs.cloud.afscase.common.step.StepParam;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.CompareFieldsUtil;
import com.ruicar.afs.cloud.afscase.common.utils.RiskCheckUtils;
import com.ruicar.afs.cloud.afscase.common.utils.StepUtil;
import com.ruicar.afs.cloud.afscase.effecttime.entity.CaseEffectRecord;
import com.ruicar.afs.cloud.afscase.effecttime.enums.EffectOpTypeEnum;
import com.ruicar.afs.cloud.afscase.effecttime.enums.EffectTimeTypeEnum;
import com.ruicar.afs.cloud.afscase.effecttime.service.CaseEffectRecordService;
import com.ruicar.afs.cloud.afscase.effecttime.vo.BaseEffectParam;
import com.ruicar.afs.cloud.afscase.fincostdetailslog.constants.FinUpdateNodeEnum;
import com.ruicar.afs.cloud.afscase.fincostdetailslog.service.FinCostDetailsLogService;
import com.ruicar.afs.cloud.afscase.handling.entity.HandlingInfo;
import com.ruicar.afs.cloud.afscase.handling.service.business.HandlingInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarStyleDetail;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseRedundantInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseRemarkRecord;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarStyleDetailService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustIndividualService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseDiscountDetailService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFinMainInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFinRentAdjustDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFinancingItemsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseRedundantInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseRemarkRecordService;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.entity.DecisionEngineResultsInfo;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.service.DecisionEngineResultsInfoService;
import com.ruicar.afs.cloud.afscase.margin.entity.MarginInfo;
import com.ruicar.afs.cloud.afscase.margin.service.MarginInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveInformInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.vo.CaseLogInfo;
import com.ruicar.afs.cloud.afscase.option.entity.CaseCreditOption;
import com.ruicar.afs.cloud.afscase.option.entity.CaseCreditOptionConf;
import com.ruicar.afs.cloud.afscase.option.service.CaseCreditOptionConfService;
import com.ruicar.afs.cloud.afscase.option.service.CaseCreditOptionService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.processor.enums.WorkflowType;
import com.ruicar.afs.cloud.afscase.record.service.business.BusinessChangeRecordService;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.afscase.risk.mapper.ThirdDataMapper;
import com.ruicar.afs.cloud.afscase.step.entity.CaseStepParam;
import com.ruicar.afs.cloud.afscase.step.service.CaseStepParamService;
import com.ruicar.afs.cloud.afscase.workflow.callback.ApproveProcessEndCallback;
import com.ruicar.afs.cloud.afscase.workflow.config.CaseLoanRentProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.CaseRiskCustInfoService;
import com.ruicar.afs.cloud.bizcommon.business.dto.FinDiscountPlanDTO;
import com.ruicar.afs.cloud.bizcommon.business.dto.HandlingInfoDto;
import com.ruicar.afs.cloud.bizcommon.business.dto.MarginInfoDto;
import com.ruicar.afs.cloud.bizcommon.business.dto.PlanRateDTO;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetailsLog;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountCost;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinPlanRate;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRentAdjustDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRepaymentPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinTermsDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyRentAdjustDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinDiscountCostServer;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinDiscountPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinPlanRateService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinRepaymentPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinTermsDetailsService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.EffectiveStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsTypeNumEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ResultBooleanEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.StepSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TortoiseApplyExecuteEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ValidStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.apply.enums.StatusEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.StatusDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApplyResultInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseBaseInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCarInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCarStyleDetailDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseChannelInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseChannelUniteInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCreditInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustAddressDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustContactDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustIndividualDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseEnterpriseCustomerDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ComAttachmentFileDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.OrderSubmitInfo;
import com.ruicar.afs.cloud.common.modules.enums.ApplySceneEnum;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.common.modules.enums.OprTypeEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.listener.AfsMqBizProcessor;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.BusiNodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.risk.api.enums.RiskStatus;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @description:进件-->案件MQ
 * <AUTHOR>
 * @created 2020/7/28 12:57
 * @version 1.0
 */
@AllArgsConstructor
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.ruicar.afs.cloud.mq.rabbit", name = "enable")
public class CaseDealProcessor implements AfsMqBizProcessor<OrderSubmitInfo> {

    private CaseBaseInfoService caseBaseInfoService;

    private final CaseFacePhotoInfoService facePhotoInfoService;

    private CaseCustContactService caseCustContactService;

    private CaseApproveRecordService caseApproveRecordService;

    private CaseCarInfoService caseCarInfoService;

    private CaseCostInfoService caseCostInfoService;

    private CaseCustAddressService caseCustAddressService;

    private CaseCustIndividualService caseCustIndividualService;

    private CaseCustInfoService caseCustInfoService;

    private CaseDiscountDetailService caseDiscountDetailService;

    private CaseFinancingItemsService caseFinancingItemsService;

    private CaseChannelInfoService caseChannelInfoService;

    private CaseCarStyleDetailService caseCarStyleDetailService;

    private ApproveInformInfoService approveInformInfoService;

    private ApplyRentAdjustDetailsService applyRentAdjustDetailsService;

    private ComAttachmentFileService comAttachmentFileService;

    private CaseRedundantInfoService redundantInfoService;

    private CaseStepParamService caseStepParamService;

    private final WorkProcessScheduleInfoService workProcessScheduleInfoService;

    private CaseFinRentAdjustDetailsService rentAdjustDetailsService;

    private Car300Service    car300Service;

    private CaseEffectRecordService caseEffectRecordService;
    private CarCreeperService carCreeperService;
    private AddressService addressService;

    private ChannelBaseInfoService channelBaseInfoService;
    private CaseConfParamService caseConfParamService;

    private ComAttachmentManagementService comAttachmentManagementService;

    private WorkTaskPoolService workTaskPoolService;

    private CaseCreditOptionConfService caseCreditOptionConfService;

    private CaseCreditOptionService caseCreditOptionService;

    private CaseEnterpriseCustomerDetailsService caseEnterpriseCustomerDetailsService;

    private CaseChannelUniteInfoService caseChannelUniteInfoService;
    private MarginInfoService marginInfoService;

    private HandlingInfoService handlingInfoService;
    /** 贴息明细及客户费率 */
    private FinDiscountPlanService finDiscountPlanService;
    private FinPlanRateService finPlanRateService;
    private FinDiscountCostServer finDiscountCostServer;
    /** 订单期数明细及还款计划明细 */
    private FinTermsDetailsService finTermsDetailsService;
    private FinRepaymentPlanService finRepaymentPlanService;

    private FinCostDetailsLogService finCostDetailsLogService;
    private CaseEnterpriseCustomerDetailsService enterpriseCustomerDetailsService;

    private final BusinessChangeRecordService businessChangeRecordService;
    private CaseRemarkRecordService remarkRecordService;

    private CaseMainInfoService caseMainInfoService;

    private final CaseFinMainInfoService caseFinMainInfoService;

    private ApproveProcessEndCallback approveProcessEndCallback;
    private ThirdDataMapper thirdDataMapper;
    final ServiceBox serviceBox;
    private CaseLoanRentProperties caseLoanRentProperties;
    private final CaseRiskCustInfoService riskCustInfoService;
    private final CardDetectRecordChangeService cardDetectRecordChangeService;
    private final DecisionEngineResultsInfoService decisionEngineResultsInfoService;

    private ApplyContractFeign applyContractFeign;
    private ApplyConfig applyConfig;

    /**
     * 进件提交记录接口
     */
    private final CaseSubmitRecordService caseSubmitRecordService;
    private final CaseUseApplyServiceFeign caseUseApplyServiceFeign;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processMessage(OrderSubmitInfo entity) {
        /**  2.1申请编号  **/
        String applyNo = entity.getApplyNo();
        CaseLogInfo logInfo = new CaseLogInfo();
        logInfo.setApplyNo(applyNo);
        logInfo.setResult(ResultBooleanEnum.TRUE.getCode());
        logInfo.setDecisionMortgageFlag(CommonConstants.COMMON_NO);
        Throwable throwable = null;
        //决策错误信息
        String riskErrorMsg =null;
        //最新决策结果(决策通关依旧要判断是否开启流程，如有Q开头规则，Z001规则)
        String finalDealTypeCode= null;
        // 是否住房类资料减免
        String isHousingReductionCode = "0";
        // 是否住房类资料减免是否更新过标志
        boolean isHousingReductionCodeUpdateFlag = false;
        //资方是否为弗迪，如为银行，决策通过依旧需要进入流程
        boolean rentIsFD=true;
        BelongingCapitalEnum rentType=BelongingCapitalEnum.FD;
        //是否为租金贷产品
        boolean isRentLoanPro = false;
        //是否包含租金贷规则
        boolean isRentLoanRule = false;
        try {
            /**申请场景-常规审批*/
            if (ApplySceneEnum.GENERALAPPROVAL.getCode().equals(entity.getApplyScene())) {
                this.receiveSubmitCaseInfo(entity);
            }
            /**正式复议*/
            if(ApplySceneEnum.FORMALREVIEW.getCode().equals(entity.getApplyScene())){
                //正式复议时更新风险提示
                CaseBaseInfo caseBaseInfo=caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                        .eq(CaseBaseInfo::getApplyNo,entity.getApplyNo()));
                caseBaseInfo.setApplyRiskTips("");
                caseBaseInfo.setReconsiderationReview(entity.getApplyScene());
                caseBaseInfo.setPassFirstDate(new Date());
                caseBaseInfoService.updateById(caseBaseInfo);
                this.receiveSubmitCaseInfo(entity);
            }

            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                    .eq(CaseBaseInfo::getApplyNo,applyNo));
            List<DicDataDto> list = DicHelper.getDicMaps("rentLoanProducts").get("rentLoanProducts");
            //租金贷产品组
            List<String> strings = list.stream().map(DicDataDto::getTitle).toList();
            //是否为租金贷产品
            isRentLoanPro = strings.contains(caseBaseInfo.getProductName());
            //首次提单且当前为影像后置，执行决策引擎
            if (OprTypeEnum.OPRADD.getCode().equals(entity.getOprType())) {
                caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                        .set(CaseBaseInfo::getIsCarrierPigeon, ResultBooleanEnum.FALSE.getCode())
                        .eq(CaseBaseInfo::getApplyNo,applyNo));
                //获取决策引擎调用记录，如果有值，表示已经做过决策，无需再次调用
                ThirdData selectData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                        .eq(ThirdData::getApproveId, applyNo)
                        .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
                if (selectData==null||Objects.equals(entity.getEditFlag(), AfsEnumUtil.key(YesOrNoEnum.YES))) {
                    //调用决策引擎
                    IResponse<com.alibaba.fastjson.JSONObject> riskRes = RiskCheckUtils.riskCheckExecute(applyNo,entity.getCaseBaseInfo().getInputType(), AfsEnumUtil.key(YesOrNoEnum.YES),entity.getEqueryid(),entity.getCqueryid());
                    log.info("最终决策结果={}",riskRes);
                    com.alibaba.fastjson.JSONObject riskData = riskRes.getData();
                    //决策异常信息
                    riskErrorMsg = riskData.getString(RiskResConstants.RISK_ERROR_MSG);
                    //决策结果
                    finalDealTypeCode = riskData.getString(RiskResConstants.FINAL_DEAL_TYPE_CODE);
                    //决策规则
                    com.alibaba.fastjson.JSONArray codeList = riskData.getJSONArray(RiskResConstants.RULE_CUSTOM_IDS);
                    // pboc客户信息保存
                    riskCustInfoService.pbocCustInfo(riskRes.getData(), applyNo);
                    //决策规则解析
                    if (Objects.equals(AfsEnumUtil.key(RiskStatus.RISK_RES_ACCEPT),finalDealTypeCode)) {
                        if (codeList!=null) {
                            for (Object o : codeList) {
                                String code=(String)o;
                                if (code.startsWith(RskRuleConstants.POST_IMAGE_PRE)) {
                                    //决策通过，且规则包含Q开头的，需补充资料进行单岗审批
                                    finalDealTypeCode=AfsEnumUtil.key(RiskStatus.RISK_RES_REVIEW);
                                }
                            }
                        }
                    }
                    if (codeList!=null) {
                        for (Object o : codeList) {
                            String code=(String)o;
                            if (StrUtil.equals(RskRuleConstants.RENT_LOAN_PRE,code) || StrUtil.equals(RskRuleConstants.RENT_LOAN_SH_PRE,code)){
                                isRentLoanRule = true;
                            }
                        }
                    }
                    //执行决策引擎，如果转人工，且影像后置，执行流程前置代码，进行异常流程
                    if (AfsEnumUtil.key(RiskStatus.RISK_RES_REVIEW).equals(finalDealTypeCode)&&Objects.equals(AfsEnumUtil.key(YesOrNoEnum.YES),entity.getCaseBaseInfo().getPostImage())) {
                        logInfo.setResult(ResultBooleanEnum.FALSE.getCode());
                        logInfo.setPostImage(AfsEnumUtil.key(YesOrNoEnum.NO));
                    }
                    // 无论是否成功都要保存决策结果: 是否住房类资料减免:资料待提交规则要用
                    if (codeList != null && !codeList.isEmpty()) {
                        isHousingReductionCode = codeList.stream()
                                .anyMatch(codeObj -> RskRuleConstants.IS_HOUSING_REDUCTION.equals(codeObj.toString()))
                                ? "1" : "0";
                        ApplyResultInfoDto applyResultInfoDto = new ApplyResultInfoDto();
                        applyResultInfoDto.setApplyNo(applyNo);
                        applyResultInfoDto.setHousingReductionFlag(isHousingReductionCode);
                        caseUseApplyServiceFeign.updateOrderInfoByResultInfoDto(applyResultInfoDto);
                        isHousingReductionCodeUpdateFlag = true;
                    }
                    if (isRentLoanPro && !isRentLoanRule && StrUtil.isEmpty(entity.getCaseBaseInfo().getRentLoanBack())){
                        logInfo = new CaseLogInfo();
                        logInfo.setResult(ResultBooleanEnum.FALSE.getCode());
                        logInfo.setRentLoanBack(AfsEnumUtil.key(YesOrNoEnum.YES));
                        logInfo.setApplyNo(applyNo);
                        logInfo.setDecisionMortgageFlag(CommonConstants.COMMON_NO);
                    }
                }
            }
        } catch (Throwable e) {
            throwable = e;
            logInfo.setResult(ResultBooleanEnum.FALSE.getCode());
            log.error("异常原因：{}", e.getMessage(),e);
        }
        approveInformInfoService.incomingApplyResult(logInfo);
        log.info("MQ日志保存成功-结果：{}", logInfo.getResult());
        log.info("报文信息：{},{}", entity.getApplyNo(),entity.toString());
        if (ResultBooleanEnum.FALSE.getCode().equals(logInfo.getResult())) {
            throw new AfsBaseException("", "保存失败!", throwable);
        }
        ThirdData data = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, applyNo).orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (data != null && data.getResponse() != null){
            com.alibaba.fastjson.JSONObject riskData= com.alibaba.fastjson.JSONObject.parseObject(data.getResponse());
            com.alibaba.fastjson.JSONArray codeList = Optional.ofNullable(riskData.getJSONObject("decisionDetailedResults")).map(res -> res.getJSONArray("ruleCustomIds")).orElse(null);
            if (codeList != null){
                String lesseeGrade = "0";
                String isSecondHand = "0";
                String isMortgage = "0";
                String enforceableNotarizationFlag1 = "0";
                String enforceableNotarizationFlag2 = "0";
                for (Object o : codeList) {

                    String code = o.toString();
                    if(code.contains(RskRuleConstants.ENFORCEABLE_NOTARIZATION2)){
                        enforceableNotarizationFlag2 = "1";
                    }

                    switch (code) {
                        case "L028"://承租人低风险
                            lesseeGrade = "0";
                            break;
                        case "L029"://承租人中低风险
                            lesseeGrade = "1";
                            break;
                        case "L030"://承租人中风险
                            lesseeGrade = "2";
                            break;
                        case "L031"://承租人中高风险
                            lesseeGrade = "3";
                            break;
                        case "L032"://承租人高风险
                            lesseeGrade = "4";
                            break;
                        case "NH33"://二手单
                            isSecondHand = "1";
                            break;
                        case RskRuleConstants.RENT_LOAN_PRE:
                            //决策通过，且包含租金贷规则，流程发起，进入租金贷等待节点
                            if (caseLoanRentProperties.getEnableInvestors().contains(BelongingCapitalEnum.BANK.getCode())) {
                                rentIsFD = false;
                                rentType=BelongingCapitalEnum.BANK;
                            }
                            break;
                        case RskRuleConstants.RENT_LOAN_SH_PRE:
                            //决策通过，且包含租金贷规则，流程发起，进入租金贷等待节点
                            if (caseLoanRentProperties.getEnableInvestors().contains(BelongingCapitalEnum.BANK_SH.getCode())) {
                                rentIsFD = false;
                                rentType=BelongingCapitalEnum.BANK_SH;
                            }
                            break;
                        case RskRuleConstants.IS_MORTGAGE:
                            // 是否抵押
                            isMortgage = "1";
                            break;
                        case RskRuleConstants.ENFORCEABLE_NOTARIZATION1:
                            // 是否赋强公证
                            enforceableNotarizationFlag1 = "1";
                            break;
                        case RskRuleConstants.IS_HOUSING_REDUCTION:
                            // 是否住房类资料减免
                            isHousingReductionCode = "1";
                            break;
                        default:
                            break;
                    }
                }
                caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                        .set(StrUtil.isNotEmpty(lesseeGrade),CaseBaseInfo::getLesseeGrade,lesseeGrade)
                        .set(CaseBaseInfo::getIsSecondHand,isSecondHand)
                        .set(CaseBaseInfo::getBelongingCapital,rentType.getCode())
                        .eq(CaseBaseInfo::getApplyNo,applyNo));
                caseUseApplyServiceFeign.updateOrderIsMortgage(applyNo, isMortgage);
                if (!isHousingReductionCodeUpdateFlag) {
                    ApplyResultInfoDto applyResultInfoDto = new ApplyResultInfoDto();
                    applyResultInfoDto.setApplyNo(applyNo);
                    applyResultInfoDto.setHousingReductionFlag(isHousingReductionCode);
                    caseUseApplyServiceFeign.updateOrderInfoByResultInfoDto(applyResultInfoDto);
                }

                // 是否赋强公证
                // 决策引擎规则编码
                String flag = "1".equals(enforceableNotarizationFlag1) && "1".equals(enforceableNotarizationFlag2) ? "1" : "0";
                List<DecisionEngineResultsInfo> decisionEngineResultsInfoList = decisionEngineResultsInfoService.list(Wrappers.<DecisionEngineResultsInfo>query().lambda()
                        .eq(DecisionEngineResultsInfo::getApplyNo, applyNo)
                        .orderByDesc(DecisionEngineResultsInfo::getCreateTime));
                if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(decisionEngineResultsInfoList)) {

                    // 是否赋强公证
                    decisionEngineResultsInfoService.update(Wrappers.<DecisionEngineResultsInfo>lambdaUpdate()
                            .set(DecisionEngineResultsInfo::getEnforceableNotarizationStatus,flag)
                            .eq(DecisionEngineResultsInfo::getApplyNo, applyNo)
                            .eq(DecisionEngineResultsInfo::getId,decisionEngineResultsInfoList.get(0).getId()));
                } else {

                    DecisionEngineResultsInfo info = new DecisionEngineResultsInfo();
                    info.setEnforceableNotarizationStatus(flag);
                    info.setApplyNo(applyNo);
                    info.setMortgageClaim(isMortgage);
                    decisionEngineResultsInfoService.save(info);
                }
            }
        }

        // 分单前置逻辑
        if (ResultBooleanEnum.TRUE.getCode().equals(logInfo.getResult())) {
            CaseStepParam caseStepParam = caseStepParamService.getLast(applyNo, StepSceneEnum.APPROVE);
            StepParam param = StepParam.builder().applyNo(applyNo).build();
            param.setIsUrgent(entity.getIsUrgent());
            if (ApplySceneEnum.GENERALAPPROVAL.getCode().equals(entity.getApplyScene())
                    && OprTypeEnum.OPRUPDATE.getCode().equals(entity.getOprType())) {
                // 流程中退回重提
                if(StrUtil.isNotBlank(entity.getDataId()) && ObjectUtil.isNull(entity.getIsCallBackSubmit())) {
                    param.setStageId(entity.getDataId());
                    param.setReturnSubmit(true);
                    param.setProcessTypeEnum(ProcessTypeEnum.GENERA_APPROVE);

                    caseStepParam.setStageId(entity.getDataId());
                    caseStepParam.setFlowType(ProcessTypeEnum.GENERA_APPROVE);
                    caseStepParam.setReturnSubmit(WhetherEnum.YES.getCode());
                    log.info("流程中退回重提{}", entity.getDataId());
                }else if(StringUtils.isEmpty(entity.getDataId()) && WhetherEnum.YES.getCode().equals(entity.getIsLoanCallBackSubmit())){
                    param.setLoanCallBackSubmit(true);
                    param.setProcessTypeEnum(ProcessTypeEnum.GENERA_APPROVE);
                    caseStepParam.setStageId(entity.getDataId());
                    caseStepParam.setFlowType(ProcessTypeEnum.GENERA_APPROVE);
                    log.info("流程中撤回重提，申请编号{}", entity.getApplyNo());
                    // 撤回重提记录时效
                    caseEffectRecordService.insertEffectOther(
                            new BaseEffectParam()
                                    .setEffect(WhetherEnum.NO)
                                    .setApplyNo(applyNo)
                                    .setEndTime(new Date())
                                    .setOpType(EffectOpTypeEnum.CALLBACK)
                                    .setEnd(true)
                    );
                }
                // 流程中撤回重提 add by maxueyu
                else if(WhetherEnum.YES.getCode().equals(entity.getIsCallBackSubmit())) {
                    param.setStageId(entity.getDataId());
                    param.setCallBackSubmit(true);
                    param.setProcessTypeEnum(ProcessTypeEnum.GENERA_APPROVE);
                    caseStepParam.setStageId(entity.getDataId());
                    caseStepParam.setFlowType(ProcessTypeEnum.GENERA_APPROVE);
                    log.info("流程中撤回重提，申请编号{}", entity.getApplyNo());

                    // 撤回重提记录时效
                    caseEffectRecordService.insertEffectOther(
                            new BaseEffectParam()
                                    .setEffect(WhetherEnum.NO)
                                    .setApplyNo(applyNo)
                                    .setEndTime(new Date())
                                    .setOpType(EffectOpTypeEnum.CALLBACK)
                                    .setEnd(true)
                    );
                }else if (StrUtil.isBlank(entity.getDataId())){
                    // 流程发起前的撤回、ocr退回重提操作
                    Optional<CaseEffectRecord> otherOpt = caseEffectRecordService.list(
                            Wrappers.<CaseEffectRecord>lambdaQuery()
                                    .eq(CaseEffectRecord::getApplyNo, applyNo)
                                    .orderByDesc(CaseEffectRecord::getCreateTime)
                    ).stream().findFirst();
                    CaseEffectRecord otherRecord = otherOpt.orElseGet( () -> new CaseEffectRecord());
                    if(EffectTimeTypeEnum.OTHER == otherRecord.getEffectType() && Objects.isNull(otherRecord.getEndTime())){
                        caseEffectRecordService.insertEffectOther(
                                new BaseEffectParam()
                                        .setEffect(WhetherEnum.NO)
                                        .setApplyNo(applyNo)
                                        .setEndTime(new Date())
                                        .setOpType(otherRecord.getOperateType())
                                        .setEnd(true)
                        );
                    }
                }
            }else if(ApplySceneEnum.FORMALREVIEW.getCode().equals(entity.getApplyScene())) {
                caseStepParam.setFlowType(ProcessTypeEnum.FORMAL_REVIEW);
                param.setProcessTypeEnum(ProcessTypeEnum.FORMAL_REVIEW);
                if (OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(entity.getOprType())) {
                    caseStepParam.setStageId(entity.getDataId());
                    caseStepParam.setReturnSubmit(WhetherEnum.YES.getCode());

                    param.setReturnSubmit(true);
                    param.setStageId(entity.getDataId());
                    log.info("正式复议修改流程{}", entity.getDataId());
                }
            }
            /**
             * 修订回复留言
             */
            if(StringUtils.isNotEmpty(entity.getLeaveMessage())){
                caseStepParam.setLeaveMessage(entity.getLeaveMessage());
                param.setLeaveMessage(entity.getLeaveMessage());
            }
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                    .eq(CaseBaseInfo::getApplyNo,applyNo));
            CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo,applyNo)
                    .eq(CaseCustInfo::getCustRole,"01"));
            CaseCustInfo custInfo1 = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo,applyNo)
                    .eq(CaseCustInfo::getCustRole,"03"));
            //人脸信息
            List<ComAttachmentFile> faceList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo, applyNo)
                    .eq(ComAttachmentFile::getAttachmentName, "承租人身份证正面")
                    .orderByDesc(ComAttachmentFile::getCreateTime));
            List<ComAttachmentFile> garFaceList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo, applyNo)
                    .eq(ComAttachmentFile::getAttachmentName, "担保人身份证正面")
                    .orderByDesc(ComAttachmentFile::getCreateTime));
            String bankCardNo = applyContractFeign.getBankCardNo(applyNo,getHeader());
            caseStepParam.setScene(StepSceneEnum.APPROVE);
            caseStepParam.setApplyNo(applyNo);
            caseStepParamService.saveOrUpdate(caseStepParam);
            List<CaseConfParam> caseConfParamList = caseConfParamService.list(Wrappers.<CaseConfParam>lambdaQuery()
                    .in(CaseConfParam::getParamLogo, CaseConstants.CREDIT_AUTO_APPROVES)
                    .eq(CaseConfParam::getParamStatus, "yes"));
            if(CollectionUtil.isNotEmpty(caseConfParamList)){
                List<CaseConfParam> caseConfParamNotList = caseConfParamService.list(Wrappers.<CaseConfParam>lambdaQuery()
                        .eq(CaseConfParam::getParamLogo, CaseConstants.CREDIT_AUTO_APPROVES_NOT)
                        .eq(CaseConfParam::getParamValue,"true")
                        .eq(CaseConfParam::getParamStatus, "yes"));
                List<String> code = caseConfParamList.stream().map(CaseConfParam::getParamType).toList();
                boolean facePhoto = code.stream().anyMatch(str -> str.equals(InterfaceIdentifyEnum.INTERFACE_FACE_PHOTO.getCode()));
                List<String> codeNot = caseConfParamNotList.stream().map(CaseConfParam::getParamType).toList();
                List<ComAttachmentManagement> managementList = comAttachmentManagementService.list(
                    Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .in(ComAttachmentManagement::getUniqueCode, code));
                if (facePhoto) {
                    List<ComAttachmentManagement> list = comAttachmentManagementService.list(
                        Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                            .eq(ComAttachmentManagement::getBusiNode, "orderApply"));
                    managementList.addAll(list);
                }
                List<ComAttachmentManagement> managementNotList = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .in(ComAttachmentManagement::getAttachmentName, codeNot));

                List<String> managementIdList = managementList.stream().map(management -> String.valueOf(management.getId())).collect(Collectors.toList());
                List<String> managementIdNotList = managementNotList.stream().map(management -> String.valueOf(management.getId())).collect(Collectors.toList());

                List<ComAttachmentFile> attachmentFiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .notIn(ComAttachmentFile::getAttachmentCode, managementIdList)
                        .notIn(ComAttachmentFile::getAttachmentCode, managementIdNotList)
                        .eq(ComAttachmentFile::getBusiNo, applyNo)
                        .eq(ComAttachmentFile::getBelongNo,applyNo));
                log.info("{}开始智能初审判断:{}",entity.getApplyNo(), CollectionUtil.isEmpty(attachmentFiles)
                    ? "符合"
                    : "不符合，文件" + JSON.toJSONString(attachmentFiles));
                // 判断返回节点是初审
                String userDefinedIndex = "";
                if(StrUtil.isNotBlank(caseStepParam.getStageId())){
                    IResponse<List<FlowVariable>> listVariablesResponse = serviceBox.getWorkflowHelper()
                        .listVariables(caseStepParam.getStageId());
                    List<FlowVariable> listVariables = listVariablesResponse.getData();
                    log.info("{}流程变量列表{}",entity.getApplyNo(),JSON.toJSONString(listVariablesResponse));
                    for(FlowVariable flowVariable : listVariables){
                        if("backNodeId".equals(flowVariable.getName())){
                            userDefinedIndex  = flowVariable.getValue();
                            break;
                        }
                    }
                }
                if (CollectionUtil.isEmpty(attachmentFiles)) {
                    // 初步审核通过，分单那边第二部通过后不需要初审流程直接到复审
                    if (StrUtil.isBlank(userDefinedIndex) || StrUtil.equals("NONE", userDefinedIndex) || StrUtil.equals(
                        userDefinedIndex, "FIRST_NODE")) {
                        // 通过拒绝单不需要智能初审
                        if ((Objects.equals(finalDealTypeCode, AfsEnumUtil.key(RiskStatus.RISK_RES_ACCEPT)))
                            || Objects.equals(finalDealTypeCode, AfsEnumUtil.key(RiskStatus.RISK_RES_REJECT))) {
                            caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                                .set(CaseBaseInfo::getApplyJointStatus, AfsEnumUtil.key(YesOrNoEnum.NO))
                                .eq(CaseBaseInfo::getApplyNo, applyNo));
                        } else {
                            caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                                .set(CaseBaseInfo::getApplyJointStatus, AfsEnumUtil.key(YesOrNoEnum.YES))
                                .eq(CaseBaseInfo::getApplyNo, applyNo));
                        }
                    } else {
                        caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                            .set(CaseBaseInfo::getApplyJointStatus, AfsEnumUtil.key(YesOrNoEnum.NO))
                            .eq(CaseBaseInfo::getApplyNo, applyNo));
                    }
                } else {
                    caseBaseInfoService.update(Wrappers.<CaseBaseInfo>lambdaUpdate()
                            .set(CaseBaseInfo::getApplyJointStatus,AfsEnumUtil.key(YesOrNoEnum.NO))
                            .eq(CaseBaseInfo::getApplyNo,applyNo));
                }
            }
            CaseCustIndividualDto personDetail=null;
            for (CaseCustInfoDto caseCustInfoDto : entity.getCaseCustInfoList()) {
                if (CustRoleEnum.MIANCUST.getCode().equals(caseCustInfoDto.getCustRole())) {
                    for (CaseCustIndividualDto caseCustIndividualDto : entity.getCaseCustIndividualList()) {
                        if (Objects.equals(caseCustInfoDto.getId(),caseCustIndividualDto.getCustId())) {
                            personDetail=caseCustIndividualDto;
                        }
                    }
                }
            }
            //营业执照类材料公章识别（由于修改基础信息也会影响识别的结果，所以每次提交都需要重新比对结果）
            List<ComAttachmentManagement> blManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BUSINESS_LICENSE.getCode()));
            if (blManage!=null && blManage.size() > 0) {
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(blManage.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo, applyNo));
                if (fileList!=null && fileList.size() > 0) {
                    caseBaseInfoService.imageAutomaticRecognition(applyNo,fileList,personDetail,caseBaseInfo);
                }
            }
            //经营许可证
            List<ComAttachmentManagement> opManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.OPERATING_LICENSE.getCode()));
            if (opManage != null && opManage.size() > 0){
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(opManage.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo, applyNo));
                if (ObjectUtil.isNotEmpty(fileList) && fileList.size() > 0) {
                    //异步解析经营许可证
                    caseBaseInfoService.businessAutomaticRecognition(applyNo,caseBaseInfo,fileList,personDetail);
                }
            }
            //证明书
            String now = DateUtil.now();
            List<ComAttachmentManagement> acManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.APPLY_CERTIFICATE.getCode()));
            if (CollectionUtil.isNotEmpty(acManage)){
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(acManage.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo,applyNo));
                if (CollectionUtil.isNotEmpty(fileList)){
                    //异步解析证明书
                    caseBaseInfoService.certificateAutomaticRecognition(applyNo,caseBaseInfo,fileList,personDetail,now);
                }
            }

            //授权委托书
            List<ComAttachmentManagement> paManage = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.POWER_ATTORNEY.getCode()));
            if (CollectionUtil.isNotEmpty(paManage)){
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(paManage.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo,applyNo));
                if (CollectionUtil.isNotEmpty(fileList)){
                    //异步解析授权委托书
                    caseBaseInfoService.attorneyAutomaticRecognition(applyNo,caseBaseInfo,fileList,personDetail,custInfo,now);
                }
            }


            List<ComAttachmentManagement> applyResidenceList = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.APPLY_RESIDENCE.getCode()));

            List<ComAttachmentFile> applyResidenceFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(applyResidenceList.get(0).getId()))
                    .eq(ComAttachmentFile::getBusiNo, applyNo)
                    .eq(ComAttachmentFile::getFileStatus, StatusEnum.STANDARD.getValue())
                    .orderByAsc(ComAttachmentFile::getId));

            if(!applyResidenceFileList.isEmpty()) {
                List<FinCostDetails> caseCostInfoList = entity.getCaseCostInfoList();
                AtomicReference<String> productName = new AtomicReference<>("");
                caseCostInfoList.stream().findFirst().ifPresent(costDetails -> {
                    productName.set(costDetails.getProductName());
                });
                caseBaseInfoService.titleDeedVerification(applyNo, applyResidenceFileList,productName.get());
            }

            if (OprTypeEnum.OPRADD.getCode().equals(entity.getOprType())) {
                log.info("信审消息处理器进入新增逻辑");
                //查询是否包含收入类材料
                List<ComAttachmentManagement> list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode()));
                if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
                    List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                            .eq(ComAttachmentFile::getBusiNo, applyNo));
                    if (CollectionUtil.isNotEmpty(fileList)) {
                        //异步解析流水
                        for (ComAttachmentFile comAttachmentFile : fileList){
                            caseBaseInfoService.turnoverAutomaticRecognition(applyNo,caseBaseInfo,custInfo,comAttachmentFile,bankCardNo,custInfo1);
                        }
                    }
                }
                //驾驶证解析
                List<ComAttachmentManagement> driverManages = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));
                if (driverManages.size() > 0) {
                    List<ComAttachmentFile> driverFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(driverManages.get(0).getId()))
                            .eq(ComAttachmentFile::getBusiNo, applyNo));
                    if (driverFileList.size() > 0) {
                        //异步解析驾驶证信息
                        for (ComAttachmentFile comAttachmentFile : driverFileList) {
                            caseBaseInfoService.driverRecognitionAsync(applyNo, comAttachmentFile, caseBaseInfo, entity.getCaseCustInfoList(), faceList, garFaceList);
                        }
                    }
                }
                // 新单面签照解析
                List<ComAttachmentManagement> facePhotoList = comAttachmentManagementService.list(
                    Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getBusiNode, "orderApply")
                        .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                );
                if (CollUtil.isNotEmpty(facePhotoList)) {
                    List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(facePhotoList.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo, applyNo)
                        .orderByDesc(ComAttachmentFile::getCreateTime));
                    if (CollUtil.isNotEmpty(fileList) && CollUtil.isNotEmpty(faceList)) {
                        for (ComAttachmentFile file : fileList) {
                            log.info("{}面签照识别处理:{}", applyNo, file.getId());
                            // ds解析面签信息
                            caseBaseInfoService.facePhotoAutomaticRecognition(applyNo, file, "");
                            // 人脸识别对比
                            facePhotoInfoService.facePhotoidentifyAndCompare(file, faceList.get(0), applyNo);
                        }
                    }
                }

                //营运人证解析
                List<ComAttachmentManagement> operatorManages = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.OPERATOR_CERTIFICATE.getCode()));
                if (operatorManages.size() > 0) {
                    List<ComAttachmentFile> operatorFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(operatorManages.get(0).getId()))
                            .eq(ComAttachmentFile::getBusiNo, applyNo));
                    if (operatorFileList.size() > 0) {
                        for (ComAttachmentFile comAttachmentFile : operatorFileList) {
                            caseBaseInfoService.operatorDsRecognitionAsync(applyNo, comAttachmentFile, caseBaseInfo, custInfo, faceList);
                        }
                    }
                }
                //决策引擎调用结果处理
                CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                if ((Objects.equals(finalDealTypeCode,AfsEnumUtil.key(RiskStatus.RISK_RES_ACCEPT))&&rentIsFD)||
                        Objects.equals(finalDealTypeCode,AfsEnumUtil.key(RiskStatus.RISK_RES_REJECT))) {
                    //调用流程结束方法
                    Map<String,String> flowVariables=new HashMap<>();
                    flowVariables.put(FlowConstant.BUSINESS_NO,applyNo);
                    if (Objects.equals(AfsEnumUtil.key(RiskStatus.RISK_RES_ACCEPT),finalDealTypeCode)) {
                        flowVariables.put(FlowConstant.LAST_OPERATION,FlowTaskOperationEnum.SUBMIT.name());
                        caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SUGGEST_CHECK_FINAL));
                        caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SUGGEST_CHECK_FINAL));
                        caseApproveRecord.setApproveRemark("决策通过");
                    }else {
                        flowVariables.put(FlowConstant.LAST_OPERATION,FlowTaskOperationEnum.REFUSE.name());
                        caseApproveRecord.setApproveReason("自动拒绝");
                        //拒绝原因设置
                        if (StrUtil.isNotBlank(riskErrorMsg)) {
                            //去掉最后的逗号
                            caseApproveRecord.setApproveRemark(riskErrorMsg.substring(0, riskErrorMsg.length() - 1));
                        }else {
                            caseApproveRecord.setApproveRemark("系统拒绝");
                        }
                        caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.SUGGEST_REJECT_FINAL));
                        caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.SUGGEST_REJECT_FINAL));
                        //自动拒绝留言
                        flowVariables.put(FlowConstant.LAST_APPROVE_REASON,"J05");
                    }
                    caseApproveRecord.setApplyNo(applyNo);
                    caseApproveRecord.setApproveEndTime(new Date());
                    caseApproveRecord.setDisposeStaff("系统");
                    caseApproveRecordService.save(caseApproveRecord);
                    approveProcessEndCallback.execute(null,null,null,null,flowVariables);
                    //自动打标签
                    title(param);
                }else {
                    //解决退回重启工作流出现当前事务未提交，决策引擎获取到旧的订单数据
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            StepUtil.prevSteps(param);
                        }
                    });
                }
            }else {
                //解决退回重启工作流出现当前事务未提交，决策引擎获取到旧的订单数据
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        StepUtil.prevSteps(param);
                    }
                });
            }
            //进件端提交申请时，记录提交时间(包含退回重提等场景)
            CaseSubmitRecord caseSubmitRecord = new CaseSubmitRecord();
            caseSubmitRecord.setApplyNo(applyNo);
            caseSubmitRecord.setSubmitDate(new Date());
            caseSubmitRecordService.save(caseSubmitRecord);
        }
        return true;
    }

    @Override
    public MqTransCode getCode() {
        return MqTransCode.AFS_POS_APPLY_CASE_CTM_NEW_ORDER;
    }

    /**
     * title
     * @param stepParam
     */
    public void title(StepParam stepParam){
        try {
            log.info("{}:决策引擎自动打标签开始执行...", stepParam.getApplyNo());
            serviceBox.getManualLabelService().transmitDataForLabel(stepParam);
        } catch (Exception e) {
            log.error("决策引擎自动打标签执行失败.", e);
        }
    }


    /**
     * receiveSubmitCaseInfo
     * <p>Description: The Listener of /order/submitCaseInfo </p>
     *
     * @param orderSubmitInfo Serializable Object
     * @return
     */
    public void receiveSubmitCaseInfo(OrderSubmitInfo orderSubmitInfo) throws ParseException {
        /**新增操作附条件核准冗余数据*/
        JSONObject object=new JSONObject();
        /**新增操作退回留痕冗余数据*/
        JSONObject jsonObject=new JSONObject();
        if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
            log.info("------------------------------常规审批流程新增操作start----------------------------");
            /**  2.2解析客户联系方式CaseCustContact  **/
            ArrayList<CaseCustContact> caseCustContactArrayList = this.parseCaseCustContact(orderSubmitInfo);
            caseCustContactService.saveBatch(caseCustContactArrayList, caseCustContactArrayList.size());
            log.info("客户联系方式add成功！！！");


            /**  2.3解析车辆信息CaseCarInfo  **/
            ArrayList<CaseCarInfo> caseCarInfoArrayList = this.parseCaseCarInfo(orderSubmitInfo);
            caseCarInfoService.saveBatch(caseCarInfoArrayList, caseCarInfoArrayList.size());
            log.info("车辆信息add成功！！！");


            /**  2.4解析融资信息CaseCostInfo  **/
            ArrayList<FinCostDetails> finCostInfoArrayList = this.parseCaseCostInfo(orderSubmitInfo);
            caseCostInfoService.saveBatch(finCostInfoArrayList, finCostInfoArrayList.size());
            log.info("融资信息add成功！！！");

            /**  2.4.1解析融资费用主表finMainInfo  **/
            ArrayList<FinMainInfo> finMainInfoArrayList = this.parseFinMainInfo(orderSubmitInfo);
            caseFinMainInfoService.saveBatch(finMainInfoArrayList, finMainInfoArrayList.size());
            log.info("融资费用主表add成功！！！");


            /**  2.5解析客户地址明细CaseCustAddress  **/
            ArrayList<CaseCustAddress> caseCustAddressArrayList = this.parseCaseCustAddress(orderSubmitInfo);
            caseCustAddressService.saveBatch(caseCustAddressArrayList, caseCustAddressArrayList.size());
            log.info("客户地址明细add成功！！！");


            /**  2.6解析企业客户明细信息CaseCustCompany  **/
            ArrayList<CaseEnterpriseCustomerDetails> caseEnterpriseCustomerDetailsArrayList = this.parseCaseCustCompany(orderSubmitInfo);
            if(CollectionUtil.isNotEmpty(caseEnterpriseCustomerDetailsArrayList)) {
            	caseEnterpriseCustomerDetailsService.saveBatch(caseEnterpriseCustomerDetailsArrayList, caseEnterpriseCustomerDetailsArrayList.size());
            	log.info("企业客户明细信息add成功！！！");
            }

            /**  2.7解析个人客户明细信息CaseCustIndividual  **/
            ArrayList<CaseCustIndividual> caseCustIndividualArrayList = this.parseCaseCustIndividual(orderSubmitInfo);
            if(CollectionUtil.isNotEmpty(caseCustIndividualArrayList)) {
            caseCustIndividualService.saveBatch(caseCustIndividualArrayList, caseCustIndividualArrayList.size());
            log.info("个人客户明细信息add成功！！！");
            }

            /**  2.8解析客户信息CaseCustInfo  **/
            ArrayList<CaseCustInfo> caseCustInfoArrayList = this.parseCaseCustInfo(orderSubmitInfo);
            caseCustInfoService.saveBatch(caseCustInfoArrayList, caseCustInfoArrayList.size());
            log.info("客户信息add成功！！！");


            /**  2.9解析贴息明细信息CaseDiscountDetail  **/
            ArrayList<FinDiscountDetails> caseDiscountDetailArrayList = this.parseCaseDiscountDetail(orderSubmitInfo);
            if(CollectionUtil.isNotEmpty(caseDiscountDetailArrayList)) {
            	 caseDiscountDetailService.saveBatch(caseDiscountDetailArrayList, caseDiscountDetailArrayList.size());
                 log.info("贴息明细信息add成功！！！");
            }

            ArrayList<FinDiscountCost> finDiscountCostList = orderSubmitInfo.getFinDiscountCostList();
            if (CollectionUtil.isNotEmpty(finDiscountCostList)){
                finDiscountCostServer.saveBatch(finDiscountCostList,finDiscountCostList.size());
            }


            /**  2.10解析融资项目信息CaseFinancingItems  **/
            ArrayList<FinFinancingItems> finFinancingItemsArrayList = this.parseCaseFinancingItems(orderSubmitInfo);
            if(!CollectionUtil.isEmpty(finFinancingItemsArrayList)) {
            	caseFinancingItemsService.saveBatch(finFinancingItemsArrayList, finFinancingItemsArrayList.size());
            	log.info("融资项目信息add成功！！！");
            }

            /**  2.11解析案件渠道信息CaseChannelInfo  **/
            CaseChannelInfo caseChannelInfo = this.parseCaseChannelInfo(orderSubmitInfo);
            //获取渠道类型字段
            if(!ObjectUtils.isEmpty(caseChannelInfo) && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(caseChannelInfo.getDealerNo())){
                ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                        .eq(ChannelBaseInfo::getChannelCode,caseChannelInfo.getDealerNo()));
                if(!ObjectUtils.isEmpty(channelBaseInfo)){
                    caseChannelInfo.setChannelType(channelBaseInfo.getChannelType());
                    caseChannelInfo.setChannelBelong(channelBaseInfo.getChannelBelong());
                }
                caseChannelInfoService.save(caseChannelInfo);
                log.info("案件渠道信息add成功！！！");
            }


            /**  2.12解析车辆信息CaseCarStyleDetail  **/
            ArrayList<CaseCarStyleDetail> caseCarStyleDetailArrayList = this.parseCaseCarStyleDetail(orderSubmitInfo);
            caseCarStyleDetailService.saveBatch(caseCarStyleDetailArrayList, caseCarStyleDetailArrayList.size());
            log.info("车辆信息add成功！！！");

            /**  2.13解析租金调整ApplyRentAdjustDetails  **/
            ArrayList<FinRentAdjustDetails> finRentAdjustDetailsArrayList = this.parseApplyRentAdjustDetails(orderSubmitInfo);
            if(!ObjectUtils.isEmpty(finRentAdjustDetailsArrayList)) {
            	applyRentAdjustDetailsService.saveBatch(finRentAdjustDetailsArrayList, finRentAdjustDetailsArrayList.size());
            	log.info("租金调整add成功！！！");
            }

            /**  2.14解析影像文件ComAttachmentFile  **/
            ArrayList<ComAttachmentFile> comAttachmentFileArrayList = this.parseComAttachmentFile(orderSubmitInfo);
            if(!ObjectUtils.isEmpty(comAttachmentFileArrayList)){
            	comAttachmentFileService.saveBatch(comAttachmentFileArrayList,comAttachmentFileArrayList.size());
            }
            log.info("影像文件add成功！！！");

            /**  2.15解析联合方信息CaseChannelUniteInfo  **/
            ArrayList<CaseChannelUniteInfo> caseCaseChannelUniteInfoList = this.parseCaseChannelUniteInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCaseChannelUniteInfoList)) {
                caseChannelUniteInfoService.saveBatch(caseCaseChannelUniteInfoList, caseCaseChannelUniteInfoList.size());
            }
            log.info("联合方信息add成功！！！");

            /** 解析保证金信息MarginInfo  **/
            ArrayList<MarginInfo> marginInfoList = this.parseMarginInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(marginInfoList)) {
                marginInfoService.saveBatch(marginInfoList, marginInfoList.size());
                log.info("保证金信息add成功！！！");
                JSONArray arrayMargin=new JSONArray();
                arrayMargin.addAll(marginInfoList);
                jsonObject.put("marginInfoList",marginInfoList);
                log.info("保证金信息冗余成功！！！");
            }

            /**  2.16解析案件基本信息CaseBaseInfo  **/
            CaseBaseInfo caseBaseInfo = this.parseCaseBaseInfo(orderSubmitInfo, finCostInfoArrayList, caseCustInfoArrayList, caseCarInfoArrayList);
            caseBaseInfo.setPassFirstDate(new Date());
            caseBaseInfo.setEffectTimeReckon(0L);
            caseBaseInfo.setIsApprovedInvalid("0");
            caseBaseInfo.setIsFirstSubmit(1);
            log.info("联合方信息add成功！！！");
            /**  2.17解析手续费信息handlingInfo  **/
            ArrayList<HandlingInfo> handlingInfoList = this.parseHandlingInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(handlingInfoList)) {
                handlingInfoService.saveBatch(handlingInfoList, handlingInfoList.size());
                log.info("服务费用信息add成功！！！");
                JSONArray arrayHandling=new JSONArray();
                arrayHandling.addAll(arrayHandling);
                jsonObject.put("handlingInfoList",handlingInfoList);
                log.info("服务费用信息冗余成功！！！");
            }
            /** 贴息明细和产品利率明细 */
            try {
                copyParseFinDiscountPlan(orderSubmitInfo,jsonObject);
                copyParseFinPlanRate(orderSubmitInfo,jsonObject);
            } catch (Exception e) {
                log.error("保存贴息明细和产品利率失败!orderSubmitInfo:{} 错误信息:", JSON.toJSONString(orderSubmitInfo),e);
            }
            /** 订单期数/还款计划保存明细 */
            try {
                saveFintermsOrRepaymentPlans(orderSubmitInfo,jsonObject);
            } catch (Exception e) {
                log.error("保存订单期数/还款计划保存明细失败!orderSubmitInfo:{} 错误信息:", JSON.toJSONString(orderSubmitInfo),e);
            }
            // 玄武进件调用默认未发送
            caseBaseInfo.setTortoiseApplyExecute(AfsEnumUtil.key(TortoiseApplyExecuteEnum.NOT_SEND));
            //是否同意附条件核准默认值
            caseBaseInfo.setIsConditional(WhetherEnum.NO.getCode());
            JSONArray arrayContact=new JSONArray();
            arrayContact.addAll(caseCustContactArrayList);
            jsonObject.put("caseContactList",arrayContact);
            log.info("客户联系方式冗余成功！！！");

            JSONArray carInfoArray=new JSONArray();
            carInfoArray.addAll(caseCarInfoArrayList);
            jsonObject.put("carInfoList",carInfoArray);
            log.info("车辆信息冗余成功！！！");


            JSONArray custAddressArray=new JSONArray();
            custAddressArray.addAll(caseCustAddressArrayList);
            jsonObject.put("custAddressList",custAddressArray);
            log.info("客户地址明细冗余成功！！！");

            JSONArray caseCustIndividualArray=new JSONArray();
            caseCustIndividualArray.addAll(caseCustIndividualArrayList);
            jsonObject.put("caseCustIndividualList",caseCustIndividualArray);
            log.info("个人客户明细信息冗余成功！！！");

            JSONArray caseEnterpriseCustomerDetailsArray=new JSONArray();
            caseEnterpriseCustomerDetailsArray.addAll(caseEnterpriseCustomerDetailsArrayList);
            jsonObject.put("caseEnterpriseCustomerDetailsList",caseEnterpriseCustomerDetailsArray);
            log.info("企业客户明细信息冗余成功！！！");

            JSONArray caseCustInfoArray=new JSONArray();
            caseCustInfoArray.addAll(caseCustInfoArrayList);
            jsonObject.put("caseCustInfoList",caseCustInfoArray);
            log.info("客户信息冗余成功！！！");


            JSONArray arrayCost=new JSONArray();
            arrayCost.addAll(finCostInfoArrayList);
            object.put("finCostDetailsList",arrayCost);
            log.info("附条件融资信息冗余成功！！！");



            JSONArray arrayDiscount=new JSONArray();
            arrayDiscount.addAll(caseDiscountDetailArrayList);
            object.put("finDiscountDetailsList",arrayDiscount);
            log.info("附条件贴息冗余成功！！！");

            JSONArray arrayDiscountCost = new JSONArray();
            arrayDiscountCost.addAll(finDiscountCostList);
            object.put("finDiscountCostList",arrayDiscountCost);


            JSONArray arrayFinancing=new JSONArray();
            arrayFinancing.addAll(finFinancingItemsArrayList);
            object.put("finFinancingItemsList",arrayFinancing);
            log.info("附条件融资项冗余成功！！！");


            JSONArray arrayRentAdjust=new JSONArray();
            arrayRentAdjust.addAll(finRentAdjustDetailsArrayList);
            object.put("finRentAdjustDetailsList",arrayRentAdjust);
            log.info("附条件租金冗余成功！！！");



            //附条件核准冗余字段
            this.saveRedundantInfo(orderSubmitInfo,object,jsonObject);
            caseBaseInfoService.save(caseBaseInfo);

            log.info("------------------------------常规审批流程新增操作end----------------------------");

            /**  3.记录操作记录CaseApproveRecord  **/
            CaseApproveRecord caseApproveRecord = this.addCaseApproveRecord(orderSubmitInfo);
            caseApproveRecordService.save(caseApproveRecord);
            log.info("进件新增数据接收成功，申请编号：{}", caseBaseInfo.getApplyNo());

            // 案件时效
            caseEffectRecordService.insertEffectCase(new BaseEffectParam()
                    .setApplyNo(caseBaseInfo.getApplyNo())
                    .setEffect(WhetherEnum.YES)
                    .setEnd(false)
                    .setStartTime(caseBaseInfo.getPassFirstDate()));


            //信贷选项保存

            saveCreditOption(orderSubmitInfo);
        }
        /**修改操作*/
        else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            log.info("------------------------------常规审批流程修改操作start----------------------------");

            //通过进件重新提交的信息判断是否发生变化并添加内部信息
            this.buildMessageByUpdate(orderSubmitInfo);
            businessChangeRecordService.handleMessage(orderSubmitInfo);

            /**  2.2解析客户联系方式CaseCustContact  **/
            ArrayList<CaseCustContact> caseCustContactArrayList = this.parseCaseCustContact(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCustContactArrayList)) {
                caseCustContactService.saveBatch(caseCustContactArrayList, caseCustContactArrayList.size());
                log.info("客户联系方式update成功！！！");
            }

            /**  2.3解析车辆信息CaseCarInfo  **/
            ArrayList<CaseCarInfo> caseCarInfoArrayList = this.parseCaseCarInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCarInfoArrayList)) {
                caseCarInfoService.saveBatch(caseCarInfoArrayList, caseCarInfoArrayList.size());
                log.info("车辆信息update成功！！！");
            }

            /**  2.4解析融资信息CaseCostInfo  **/
            ArrayList<FinCostDetails> finCostInfoArrayList = this.parseCaseCostInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(finCostInfoArrayList)) {
                caseCostInfoService.saveBatch(finCostInfoArrayList, finCostInfoArrayList.size());

                log.info("融资信息update成功！！！");
                JSONArray arrayCost=new JSONArray();
                arrayCost.addAll(finCostInfoArrayList);
                object.put("finCostDetailsList",arrayCost);
                log.info("附条件融资信息冗余成功！！！");
            }

            /**  2.4.1融资费用主表finMainInfo  **/
            ArrayList<FinMainInfo> finMainInfoArrayList = this.parseFinMainInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(finMainInfoArrayList)) {
                caseFinMainInfoService.saveBatch(finMainInfoArrayList,finMainInfoArrayList.size());
            }

            /**  2.5解析客户地址明细CaseCustAddress  **/
            ArrayList<CaseCustAddress> caseCustAddressArrayList = this.parseCaseCustAddress(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCustAddressArrayList)) {
                caseCustAddressService.saveBatch(caseCustAddressArrayList, caseCustAddressArrayList.size());
                log.info("客户地址明细update成功！！！");
            }

            /**  2.6解析企业客户明细信息CaseCustCompany  **/
            ArrayList<CaseEnterpriseCustomerDetails> caseEnterpriseCustomerDetailsArrayList = this.parseCaseCustCompany(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseEnterpriseCustomerDetailsArrayList)) {
                caseEnterpriseCustomerDetailsService.saveBatch(caseEnterpriseCustomerDetailsArrayList, caseEnterpriseCustomerDetailsArrayList.size());
                log.info("企业客户明细信息update成功！！！");
            }


            /**  2.7解析个人客户明细信息CaseCustIndividual  **/
            ArrayList<CaseCustIndividual> caseCustIndividualArrayList = this.parseCaseCustIndividual(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCustIndividualArrayList)) {
                caseCustIndividualService.saveBatch(caseCustIndividualArrayList, caseCustIndividualArrayList.size());
                log.info("个人客户明细明细信息update成功！！！");
            }

            /**  2.8解析客户信息CaseCustInfo  **/
            ArrayList<CaseCustInfo> caseCustInfoArrayList = this.parseCaseCustInfo(orderSubmitInfo);
            Set<Long> longs = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda().eq(CaseCustInfo::getApplyNo, orderSubmitInfo.getApplyNo()))
                    .stream().map(CaseCustInfo::getId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(caseCustInfoArrayList)) {
                for (CaseCustInfo custInfo : caseCustInfoArrayList) {
                        if(Objects.isNull(caseCustInfoService.getById(custInfo.getId()))){
                        caseCustInfoService.save(custInfo);
                        log.info("客户信息add成功！！！");
                    }else{
                        caseCustInfoService.updateById(custInfo);
                        log.info("客户信息update成功！！！");
                    }
                    longs.remove(custInfo.getId());
                }
            }
            if(longs.size()>0) {
                caseCustInfoService.removeByIds(Arrays.asList(longs.toArray()));
            }

            /**  2.9解析贴息明细信息CaseDiscountDetail  **/
            ArrayList<FinDiscountDetails> caseDiscountDetailArrayList = this.parseCaseDiscountDetail(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseDiscountDetailArrayList)) {
                caseDiscountDetailService.saveBatch(caseDiscountDetailArrayList, caseDiscountDetailArrayList.size());
                log.info("贴息明细add成功！！！");
                JSONArray arrayDiscount=new JSONArray();
                arrayDiscount.addAll(caseDiscountDetailArrayList);
                object.put("finDiscountDetailsList",arrayDiscount);
                log.info("附条件贴息冗余成功！！！");
            }else {
                //modify by xiongjie.guan 如果是修改提交，没有贴息信息，需要清空历史的案件贴息信息
                caseDiscountDetailService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
                log.info(orderSubmitInfo.getApplyNo()+"进件修改为无贴息后重提，清空历史案件贴息信息完成。");
            }

            ArrayList<FinDiscountCost> finDiscountCostList = orderSubmitInfo.getFinDiscountCostList();
            if (CollectionUtil.isNotEmpty(finDiscountCostList)){
                finDiscountCostServer.updateBatchById(finDiscountCostList);
                JSONArray arrayDiscountCost = new JSONArray();
                arrayDiscountCost.addAll(finDiscountCostList);
                object.put("finDiscountCostList",arrayDiscountCost);
            }

            /** 更改 贴息/期数明细/还款计划/利率表信息 */
            try{
                updateOrderFinInfos(orderSubmitInfo,object);
            }catch (Exception e){
                log.error("更新贴息/期数明细/还款计划/利率表信息 orderSubmitInfo:{} 失败错误信息:",JSON.toJSONString(orderSubmitInfo),e);
            }

            /**  2.10解析融资项目信息CaseFinancingItems  **/
            ArrayList<FinFinancingItems> finFinancingItemsArrayList = this.parseCaseFinancingItems(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(finFinancingItemsArrayList)) {
                caseFinancingItemsService.saveBatch(finFinancingItemsArrayList, finFinancingItemsArrayList.size());
                log.info("融资项目信息add成功！！！");
                JSONArray arrayFinancing=new JSONArray();
                arrayFinancing.addAll(finFinancingItemsArrayList);
                object.put("finFinancingItemsList",arrayFinancing);
                log.info("附条件融资项冗余成功！！！");
            }

            /**  2.11解析案件渠道信息CaseChannelInfo  **/
            CaseChannelInfo caseChannelInfo = this.parseCaseChannelInfo(orderSubmitInfo);
            if (!ObjectUtils.isEmpty(caseChannelInfo)) {
                caseChannelInfoService.updateById(caseChannelInfo);
                log.info("案件渠道信息update成功！！！");
            }

            /**  2.12解析车辆信息CaseCarStyleDetail  **/
            ArrayList<CaseCarStyleDetail> caseCarStyleDetailArrayList = this.parseCaseCarStyleDetail(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCarStyleDetailArrayList)) {
                caseCarStyleDetailService.saveBatch(caseCarStyleDetailArrayList, caseCarStyleDetailArrayList.size());
                log.info("车辆信息update成功！！！");
            }

            /**  2.13解析租金调整ApplyRentAdjustDetails  **/
            ArrayList<FinRentAdjustDetails> finRentAdjustDetailsArrayList = this.parseApplyRentAdjustDetails(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(finRentAdjustDetailsArrayList)){
                applyRentAdjustDetailsService.updateBatchById(finRentAdjustDetailsArrayList, finRentAdjustDetailsArrayList.size());
                log.info("租金调整update成功！！！");
                JSONArray arrayRentAdjust=new JSONArray();
                arrayRentAdjust.addAll(finRentAdjustDetailsArrayList);
                object.put("finRentAdjustDetailsList",arrayRentAdjust);
                log.info("附条件租金冗余成功！！！");

            }

            /**  2.14解析联合方信息CaseChannelUniteInfo  **/
            ArrayList<CaseChannelUniteInfo> caseCaseChannelUniteInfoList = this.parseCaseChannelUniteInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(caseCaseChannelUniteInfoList)) {
                caseChannelUniteInfoService.saveBatch(caseCaseChannelUniteInfoList, caseCaseChannelUniteInfoList.size());
            }
            log.info("联合方信息update成功！！！");

            /**  2.15解析影像文件ComAttachmentFile  **/
            ArrayList<ComAttachmentFile> comAttachmentFileArrayList = this.parseComAttachmentFile(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(comAttachmentFileArrayList)){
                comAttachmentFileService.saveOrUpdateBatch(comAttachmentFileArrayList,comAttachmentFileArrayList.size());
                log.info("影像资料update成功！！！");
            }

            log.info("联合方信息add成功！！！");
            JSONArray arrayContact=new JSONArray();
            arrayContact.addAll(caseCustContactArrayList);
            jsonObject.put("caseContactList",arrayContact);
            log.info("客户联系方式冗余成功！！！");

            JSONArray carInfoArray=new JSONArray();
            carInfoArray.addAll(caseCarInfoArrayList);
            jsonObject.put("carInfoList",carInfoArray);
            log.info("车辆信息冗余成功！！！");


            JSONArray custAddressArray=new JSONArray();
            custAddressArray.addAll(caseCustAddressArrayList);
            jsonObject.put("custAddressList",custAddressArray);
            log.info("客户地址明细冗余成功！！！");

            JSONArray caseCustIndividualArray=new JSONArray();
            caseCustIndividualArray.addAll(caseCustIndividualArrayList);
            jsonObject.put("caseCustIndividualList",caseCustIndividualArray);
            log.info("个人客户明细信息冗余成功！！！");

            JSONArray caseEnterpriseCustomerDetailsArray=new JSONArray();
            caseEnterpriseCustomerDetailsArray.addAll(caseEnterpriseCustomerDetailsArrayList);
            jsonObject.put("caseEnterpriseCustomerDetailsList",caseEnterpriseCustomerDetailsArray);
            log.info("企业客户明细信息冗余成功！！！");

            JSONArray caseCustInfoArray=new JSONArray();
            caseCustInfoArray.addAll(caseCustInfoArrayList);
            jsonObject.put("caseCustInfoList",caseCustInfoArray);
            log.info("客户信息冗余成功！！！");
            /**  2.16解析手续费信息handlingInfo  **/
            ArrayList<HandlingInfo> handlingInfoList = this.parseHandlingInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(handlingInfoList)) {
                handlingInfoService.saveBatch(handlingInfoList, handlingInfoList.size());
                log.info("手续费信息add成功！！！");
                JSONArray arrayHandling=new JSONArray();
                arrayHandling.addAll(arrayHandling);
                jsonObject.put("handlingInfoList",handlingInfoList);
                log.info("手续费信息冗余成功！！！");
            }


            /** 解析保证金信息MarginInfo  **/
            ArrayList<MarginInfo> marginInfoList = this.parseMarginInfo(orderSubmitInfo);
            if (CollectionUtil.isNotEmpty(marginInfoList)) {
                marginInfoService.saveBatch(marginInfoList, marginInfoList.size());
                log.info("保证金信息add成功！！！");
                JSONArray arrayMargin=new JSONArray();
                arrayMargin.addAll(marginInfoList);
                jsonObject.put("marginInfoList",marginInfoList);
                log.info("保证金信息冗余成功！！！");
            }
            //附条件核准冗余字段
            this.saveRedundantInfo(orderSubmitInfo,object,jsonObject);
            /**  2.15解析案件基本信息CaseBaseInfo  **/
            CaseBaseInfo caseBaseInfo = this.parseCaseBaseInfoForUpdate(orderSubmitInfo, finCostInfoArrayList, caseCustInfoArrayList,
                    caseCarInfoArrayList,orderSubmitInfo.getApplyScene());
            caseBaseInfo.setIsFirstSubmit(0);
            caseBaseInfoService.updateById(caseBaseInfo);
            if(!WhetherEnum.YES.getCode().equals(orderSubmitInfo.getIsCallBackSubmit())
                    &&StringUtils.isNotEmpty(orderSubmitInfo.getDataId())){
                //修订回复日志
                saveBackRecordInfo(orderSubmitInfo);
            }
            log.info("------------------------------常规审批流程修改操作end----------------------------");
        }
        //主状态修改
        ((CaseDealProcessor)AopContext.currentProxy()).updateCaseMainStatus(orderSubmitInfo.getCaseBaseInfo());
    }

    /**
     * 贴息/期数明细/还款计划/利率表信息
     * @param orderSubmitInfo
     */
    private void updateOrderFinInfos(OrderSubmitInfo orderSubmitInfo,JSONObject jsonObject) {
        // 利率
        if(CollectionUtil.isNotEmpty(orderSubmitInfo.getFinPlanRateDtos())){
            ArrayList<FinPlanRate> finPlanRates = new ArrayList<>();
            for (PlanRateDTO dto : orderSubmitInfo.getFinPlanRateDtos()) {
                FinPlanRate finPlanRate = new FinPlanRate();
                BeanUtils.copyProperties(dto, finPlanRate);
                finPlanRates.add(finPlanRate);
            }
            finPlanRateService.updateBatchById(finPlanRates);
            JSONArray jsonArray=new JSONArray();
            jsonArray.addAll(finPlanRates);
            jsonObject.put("finPlanRateList",jsonArray);
        }

        // 贴息明细
        if(CollectionUtil.isNotEmpty(orderSubmitInfo.getFinDiscountPlanDtos())){
            ArrayList<FinDiscountPlan> finDiscountPlans = new ArrayList<>();
            for (FinDiscountPlanDTO dto : orderSubmitInfo.getFinDiscountPlanDtos()) {
                FinDiscountPlan finDiscountPlan = new FinDiscountPlan();
                BeanUtils.copyProperties(dto, finDiscountPlan);
                finDiscountPlans.add(finDiscountPlan);
            }
            finDiscountPlanService.updateBatchById(finDiscountPlans);
            JSONArray jsonArray=new JSONArray();
            jsonArray.addAll(finDiscountPlans);
            jsonObject.put("finDiscountPlanList",jsonArray);
        }

        // 期数明细
        if(CollectionUtil.isNotEmpty(orderSubmitInfo.getFinTermsDetailsList())){
            finTermsDetailsService.updateBatchById(orderSubmitInfo.getFinTermsDetailsList());
            JSONArray jsonArray=new JSONArray();
            jsonArray.addAll(orderSubmitInfo.getFinTermsDetailsList());
            jsonObject.put("finTermsDetails",jsonArray);
        }

        // 还款计划
        if(CollectionUtil.isNotEmpty(orderSubmitInfo.getFinRepaymentPlanList())){
            finRepaymentPlanService.updateBatchById(orderSubmitInfo.getFinRepaymentPlanList());
            JSONArray jsonArray=new JSONArray();
            jsonArray.addAll(orderSubmitInfo.getFinRepaymentPlanList());
            jsonObject.put("finRepaymentPlans",jsonArray);
        }

    }

    private void copyParseFinPlanRate(OrderSubmitInfo orderSubmitInfo,JSONObject jsonObject) {

        log.info("开始保存产品费率表:orderSubmitInfo {}",JSON.toJSONString(orderSubmitInfo.getFinPlanRateDtos()));
        ArrayList<FinPlanRate> finPlanRates = new ArrayList<>();
        List<PlanRateDTO>  planRateDTOList = orderSubmitInfo.getFinPlanRateDtos();
        if (CollectionUtils.isEmpty(planRateDTOList)) {
            log.info("产品费率表为空!");
            return;
        }
        //修改时,需要删除有效的记录
        if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            finPlanRateService.remove(Wrappers.<FinPlanRate>lambdaQuery()
                    .eq(FinPlanRate::getApplyNo,orderSubmitInfo.getApplyNo())
            );
        }

        for (PlanRateDTO dto : planRateDTOList) {
            FinPlanRate finPlanRate = new FinPlanRate();
            BeanUtils.copyProperties(dto, finPlanRate);
            finPlanRates.add(finPlanRate);
        }
        finPlanRateService.saveBatch(finPlanRates, finPlanRates.size());
        JSONArray finRateArray=new JSONArray();
        finRateArray.addAll(finPlanRates);
        jsonObject.put("finPlanRateList",finRateArray);
        log.info("保存产品费率成功! finRateArray:{}",JSON.toJSONString(finRateArray));
    }

    private void saveFintermsOrRepaymentPlans(OrderSubmitInfo orderSubmitInfo,JSONObject jsonObject){

        if (!CollectionUtils.isEmpty(orderSubmitInfo.getFinTermsDetailsList())) {
            //修改时,需要删除有效的记录
            if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                finTermsDetailsService.remove(Wrappers.<FinTermsDetails>lambdaQuery()
                        .eq(FinTermsDetails::getApplyNo,orderSubmitInfo.getApplyNo()));
            }

            finTermsDetailsService.saveBatch(orderSubmitInfo.getFinTermsDetailsList(), orderSubmitInfo.getFinTermsDetailsList().size());
            JSONArray finTermsDetails=new JSONArray();
            finTermsDetails.addAll(orderSubmitInfo.getFinTermsDetailsList());
            jsonObject.put("finTermsDetails",finTermsDetails);
            log.info("保存期数明细表成功! finTermsDetails:{}",JSON.toJSONString(finTermsDetails));
        }

        if (!CollectionUtils.isEmpty(orderSubmitInfo.getFinRepaymentPlanList())) {
            //修改时,需要删除有效的记录
            if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                finRepaymentPlanService.remove(Wrappers.<FinRepaymentPlan>lambdaQuery()
                        .eq(FinRepaymentPlan::getApplyNo,orderSubmitInfo.getApplyNo()));
            }

            finRepaymentPlanService.saveBatch(orderSubmitInfo.getFinRepaymentPlanList(), orderSubmitInfo.getFinRepaymentPlanList().size());
            JSONArray finRepaymentPlans=new JSONArray();
            finRepaymentPlans.addAll(orderSubmitInfo.getFinRepaymentPlanList());
            jsonObject.put("finRepaymentPlans",finRepaymentPlans);
            log.info("保存还款计划明细表成功! finTermsDetails:{}",JSON.toJSONString(finRepaymentPlans));
        }



    }
    private void copyParseFinDiscountPlan(OrderSubmitInfo orderSubmitInfo,JSONObject jsonObject) {
        log.info("开始保存贴息明细表:orderSubmitInfo {}",JSON.toJSONString(orderSubmitInfo.getFinDiscountPlanDtos()));
        ArrayList<FinDiscountPlan> finDiscountPlanArrayList = new ArrayList<>();
        List<FinDiscountPlanDTO>  finDiscountPlanDTOList = orderSubmitInfo.getFinDiscountPlanDtos();
        if (CollectionUtils.isEmpty(finDiscountPlanDTOList)) {
            log.info("贴息明细表为空!");
            return;
        }
        //修改时,需要删除有效的记录
        if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            finDiscountPlanService.remove(Wrappers.<FinDiscountPlan>lambdaQuery()
                    .eq(FinDiscountPlan::getApplyNo,orderSubmitInfo.getApplyNo())
            );
        }

        for (FinDiscountPlanDTO dto : finDiscountPlanDTOList) {
            FinDiscountPlan finDiscountPlan = new FinDiscountPlan();
            BeanUtils.copyProperties(dto, finDiscountPlan);
            finDiscountPlanArrayList.add(finDiscountPlan);
        }
        finDiscountPlanService.saveBatch(finDiscountPlanArrayList, finDiscountPlanArrayList.size());
        JSONArray finDiscountArray=new JSONArray();
        finDiscountArray.addAll(finDiscountPlanArrayList);
        jsonObject.put("finDiscountPlanList",finDiscountArray);
        log.info("保存贴息明细表成功! finRateArray:{}",JSON.toJSONString(finDiscountArray));

    }

    /**
     * parse1CaseBaseInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return CaseBaseInfo
     */
    private CaseBaseInfo parseCaseBaseInfo(OrderSubmitInfo orderSubmitInfo, List<FinCostDetails> finCostInfoArrayList, List<CaseCustInfo> caseCustInfoArrayList, List<CaseCarInfo> caseCarInfoArrayList) {
        if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseBaseInfo())) {
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseBaseInfo不能为空");
        } else {
            if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseBaseInfo().getApplyNo())) {
                throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",案件基本信息,applyNo不能为空");
            }
        }
        /**冗余经销商名称*/
        CaseBaseInfo caseBaseInfo = new CaseBaseInfo();
        BeanUtils.copyProperties(orderSubmitInfo.getCaseBaseInfo(), caseBaseInfo);
        CaseChannelInfoDto caseChannelInfo= orderSubmitInfo.getCaseChannelInfo();
        /**直营车商名称不为null**/
        if(StringUtils.isNotEmpty(caseChannelInfo.getCarDealersName())){
            caseBaseInfo.setDealerName(caseChannelInfo.getCarDealersName());
        }else{
            caseBaseInfo.setDealerName(caseChannelInfo.getDealerName());
        }
        caseBaseInfo.setChannelFullName(caseChannelInfo.getDealerName());
        BigDecimal loanAmt=BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(finCostInfoArrayList)){
            for(FinCostDetails finCostDetails:finCostInfoArrayList){
                loanAmt=loanAmt.add(finCostDetails.getLoanAmt());
                if (CostTypeEnum.CARAMT.getCode().equals(finCostDetails.getCostType())) {
                    caseBaseInfo.setDownPayScaleRepeat(finCostDetails.getDownPayScale());
                    caseBaseInfo.setLoanTermsRepeat(finCostDetails.getLoanTerm());
                    caseBaseInfo.setProductName(finCostDetails.getProductName());
                    caseBaseInfo.setProductId(Convert.toLong(finCostDetails.getProductId()));
                }
            }
            caseBaseInfo.setLoanAmtRepeat(loanAmt);
        }
        if (!CollectionUtils.isEmpty(caseCustInfoArrayList)) {
            caseCustInfoArrayList.forEach(caseCustInfo -> {
                if (CustRoleEnum.MIANCUST.getCode().equals(caseCustInfo.getCustRole())) {
                    caseBaseInfo.setCustNameRepeat(caseCustInfo.getCustName());
                    caseBaseInfo.setCertNoRepeat(caseCustInfo.getCertNo());
                } else {
                    caseBaseInfo.setBondManName(caseCustInfo.getCustName());
                }
            });
        }
        if (!CollectionUtils.isEmpty(caseCarInfoArrayList)) {
            caseCarInfoArrayList.forEach(caseCarInfo -> {
                caseBaseInfo.setSalePrice(caseCarInfo.getSalePrice());
            });
        }
        /**
         *是否锁定给默认值
         */
        caseBaseInfo.setIsLock(WhetherEnum.NO.getCode());
        //业务状态 已提交
        caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.SUBMITTED));
        return caseBaseInfo;
    }

    /**
     * @description: 基础主表更新
     * <AUTHOR>
     * @created 2020/7/7 18:20
     * @version 1.0
     */
    private CaseBaseInfo parseCaseBaseInfoForUpdate(OrderSubmitInfo orderSubmitInfo, List<FinCostDetails> finCostInfoArrayList,
                                                    List<CaseCustInfo> caseCustInfoArrayList, List<CaseCarInfo> caseCarInfoArrayList,String applyScence) {
        if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseBaseInfo())) {
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseBaseInfo不能为空");
        } else {
            if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseBaseInfo().getApplyNo())) {
                throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",案件基本信息,applyNo不能为空");
            }
        }
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, orderSubmitInfo.getApplyNo()));
        if(StringUtils.isNotBlank(caseBaseInfo.getRemarks())){
            CaseRemarkRecord caseRemarkRecord=new CaseRemarkRecord();
            caseRemarkRecord.setOldRemarks(caseBaseInfo.getRemarks());
            caseRemarkRecord.setApplyNo(caseBaseInfo.getApplyNo());
            if(ObjectUtils.isEmpty(caseBaseInfo.getUpdateTime())){
                caseRemarkRecord.setSubmitTime(caseBaseInfo.getCreateTime());
            }else {
                caseRemarkRecord.setSubmitTime(caseBaseInfo.getUpdateTime());
            }
            remarkRecordService.save(caseRemarkRecord);
        }
        /**
         * 修订回复时累加贷额不做更新
         */
        BeanUtils.copyProperties(orderSubmitInfo.getCaseBaseInfo(), caseBaseInfo);
        BigDecimal loanAmt=BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(finCostInfoArrayList)){
            for(FinCostDetails finCostDetails:finCostInfoArrayList){
                loanAmt=loanAmt.add(finCostDetails.getLoanAmt());
                if (CostTypeEnum.CARAMT.getCode().equals(finCostDetails.getCostType())) {
                    caseBaseInfo.setDownPayScaleRepeat(finCostDetails.getDownPayScale());
                    caseBaseInfo.setLoanTermsRepeat(finCostDetails.getLoanTerm());
                    caseBaseInfo.setProductName(finCostDetails.getProductName());
                    caseBaseInfo.setProductId(Convert.toLong(finCostDetails.getProductId()));
                }
            }
        }
        caseBaseInfo.setLoanAmtRepeat(loanAmt);
        if (!CollectionUtils.isEmpty(caseCustInfoArrayList)) {
            caseCustInfoArrayList.forEach(caseCustInfo -> {
                if (CustRoleEnum.MIANCUST.getCode().equals(caseCustInfo.getCustRole())) {
                    caseBaseInfo.setCustNameRepeat(caseCustInfo.getCustName());
                    caseBaseInfo.setCertNoRepeat(caseCustInfo.getCertNo());
                } else {
                    caseBaseInfo.setBondManName(caseCustInfo.getCustName());
                }
            });
        }
        if (!CollectionUtils.isEmpty(caseCarInfoArrayList)) {
            caseCarInfoArrayList.forEach(caseCarInfo -> {
                caseBaseInfo.setSalePrice(caseCarInfo.getSalePrice());
            });
        }
        //判断是否为经销商撤回后重新提交
        if(ApplySceneEnum.GENERALAPPROVAL.getCode().equals(applyScence)
                &&ObjectUtil.isNotNull(orderSubmitInfo.getIsCallBackSubmit())){
            List<String> normalWorkflowTypes = new ArrayList<>();
            normalWorkflowTypes.add(WorkflowType.NORMAL_NEW.getAfsFlowKey());
            normalWorkflowTypes.add(WorkflowType.NORMAL_OLD.getAfsFlowKey());
            List<WorkProcessScheduleInfo> workProcessScheduleList = workProcessScheduleInfoService.list(
                    Wrappers.<WorkProcessScheduleInfo>lambdaQuery()
                            .in(WorkProcessScheduleInfo::getAfsFlowKey,normalWorkflowTypes)
                            .eq(WorkProcessScheduleInfo::getApplyNo,caseBaseInfo.getApplyNo())
                            .orderByDesc(WorkProcessScheduleInfo::getCreateTime));
            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(caseBaseInfo.getApplyNo());
            if(CollectionUtil.isNotEmpty(workProcessScheduleList)){
                record.setStageId(workProcessScheduleList.get(0).getStageId());
            }
            record.setUseScene(UseSceneEnum.APPROVE.getValue());
            record.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.SUBMIT));
            record.setApproveSuggestName("经销商重新提交");
            record.setApproveEndTime(new Date());
            record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
            record.setApproveRemark("【经销商重新提交】");
            record.setDisposeNodeName("经销商重新提交");
            record.setDisposeStaff(orderSubmitInfo.getOperatorRealName());
            caseApproveRecordService.save(record);
        }
        //冗余车商合作商字段
        CaseChannelInfoDto caseChannelInfo= orderSubmitInfo.getCaseChannelInfo();
        /**直营车商名称不为null**/
        if(StringUtils.isNotEmpty(caseChannelInfo.getCarDealersName())){
            caseBaseInfo.setDealerName(caseChannelInfo.getCarDealersName());
        }else{
            caseBaseInfo.setDealerName(caseChannelInfo.getDealerName());
        }
        caseBaseInfo.setChannelFullName(caseChannelInfo.getDealerName());
        //业务状态 已提交
        caseBaseInfo.setBusinessStateIn(AfsEnumUtil.key(BusinessStateInEnum.SUBMITTED));
        return caseBaseInfo;
    }

    /**
     * parse1CaseBaseInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return CaseChannelInfo
     */
    private CaseChannelInfo parseCaseChannelInfo(OrderSubmitInfo orderSubmitInfo) {
        if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseChannelInfo())) {
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseChannelInfo不能为空");
        } else {
            if (ObjectUtils.isEmpty(orderSubmitInfo.getCaseChannelInfo().getApplyNo())) {
                throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",案件渠道信息,applyNo不能为空");
            }
        }
        CaseChannelInfo caseChannelInfo = new CaseChannelInfo();
        if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
            BeanUtils.copyProperties(orderSubmitInfo.getCaseChannelInfo(), caseChannelInfo);
        }
        else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            caseChannelInfo =caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, orderSubmitInfo.getApplyNo()));
            BeanUtils.copyProperties(orderSubmitInfo.getCaseChannelInfo(), caseChannelInfo);
        }
        return caseChannelInfo;
    }

    /**
     * 通过进件重新提交的信息判断是否发生变化并添加内部信息
     * @param submitInfo 提交信息
     */
    private void buildMessageByUpdate(OrderSubmitInfo submitInfo){
        //获取提交融资信息 客户信息 客户详细信息 客户地址信息 联系人信息 车辆信息
        ArrayList<FinCostDetails> caseCostInfoList = submitInfo.getCaseCostInfoList();
        ArrayList<CaseCustInfoDto> newCustInfoList = submitInfo.getCaseCustInfoList();
        ArrayList<CaseCustIndividualDto> caseCustIndividualList = submitInfo.getCaseCustIndividualList();
        ArrayList<CaseCustAddressDto> newCustAddressList = submitInfo.getCaseCustAddressList();
        ArrayList<CaseCustContactDto> newCustContactList = submitInfo.getCaesCustContactList();
        ArrayList<CaseEnterpriseCustomerDetailsDTO> newEnterpriseList = submitInfo.getCaseEnterpriseCustomerDetailsDTOArrayList();
        ArrayList<CaseCarInfoDto> caseCarInfoList = submitInfo.getCaseCarInfoList();
        CaseBaseInfoDto caseBaseInfoDto = submitInfo.getCaseBaseInfo();
        //获取当前信息
        String applyNo = submitInfo.getApplyNo();
        StringBuffer remindMessage = new StringBuffer();

        //基本信息对比
        CaseBaseInfo oldCaseBaseInfo = caseBaseInfoService.getOne((Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, applyNo)));
        CaseBaseInfo newCaseBaseInfo = new CaseBaseInfo();
        BeanUtils.copyProperties(caseBaseInfoDto,newCaseBaseInfo);
        approveInformInfoService.changeByCaseBaseInfo(oldCaseBaseInfo,newCaseBaseInfo,new StringBuffer(),remindMessage);

        //融资信息对比
        FinCostDetails oldCostDetails = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, applyNo)
                .eq(FinCostDetails::getCostType,CostTypeEnum.CARAMT.getCode()));
        for (FinCostDetails newCostDetails : caseCostInfoList) {
            if (CostTypeEnum.CARAMT.getCode().equals(newCostDetails.getCostType())){
                approveInformInfoService.changeByFinCost(oldCostDetails,newCostDetails,new StringBuffer(),remindMessage,null,null);
            }
        }
        //客户信息对比
        List<CaseCustInfo> oldCustInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo));

        ArrayList<Long> custIds = new ArrayList<>();
        HashMap<Long, CaseCustInfo> idCustMap = new HashMap<>();
        for (CaseCustInfo caseCustInfo : oldCustInfoList) {
            idCustMap.put(caseCustInfo.getId(),caseCustInfo);
            custIds.add(caseCustInfo.getId());
        }
        HashMap<Long, CaseCustInfo> newidCustMap = new HashMap<>();
        for (CaseCustInfoDto newCustInfo : newCustInfoList) {
            if (!ObjectUtils.isEmpty(idCustMap.get(newCustInfo.getId()))){
                CaseCustInfo newCaseCustInfo = new CaseCustInfo();
                BeanUtils.copyProperties(newCustInfo,newCaseCustInfo);
                newidCustMap.put(newCustInfo.getId(),newCaseCustInfo);
                approveInformInfoService.changeByCustInfo(idCustMap.get(newCustInfo.getId()),new StringBuffer(),remindMessage,newidCustMap,null);
            }
        }
        //客户详细信息对比
        for (CaseCustIndividualDto newIndividualDto : caseCustIndividualList) {
            CaseCustIndividual oldIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery().eq(CaseCustIndividual::getCustId, newIndividualDto.getCustId()));
           if (!ObjectUtils.isEmpty(oldIndividual)){
               CaseCustIndividual newIndividual = new CaseCustIndividual();
               BeanUtils.copyProperties(newIndividualDto,newIndividual);
               approveInformInfoService.changeByCustIndividual(oldIndividual,new StringBuffer(),remindMessage,idCustMap,null);
           }
        }
        //企业信息对比
        if (CollectionUtil.isNotEmpty(newEnterpriseList)){
            for (CaseEnterpriseCustomerDetailsDTO newEnterpriseDTO : newEnterpriseList) {
                CaseEnterpriseCustomerDetails oldEnterprise = enterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, newEnterpriseDTO.getCustId()));
                if (ObjectUtil.isNotNull(oldEnterprise)){
                    CaseEnterpriseCustomerDetails newEnterprise = new CaseEnterpriseCustomerDetails();
                    BeanUtils.copyProperties(newEnterpriseDTO,newEnterprise);
                    approveInformInfoService.changeByEnterprise(oldEnterprise,newEnterprise,new StringBuffer(),remindMessage,idCustMap,null);
                }
            }
        }

        //客户地址信息对比
        List<CaseCustAddress> oldCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                .in(CaseCustAddress::getCustId, custIds));
        for (CaseCustAddressDto newCustAddressDto : newCustAddressList) {
            for (CaseCustAddress oldCustAddress : oldCustAddressList) {
                CaseCustInfo caseCustInfo = idCustMap.get(oldCustAddress.getCustId());
                //查询客户Id和地址类型都相同的是否有变化
                if (newCustAddressDto.getCustId().equals(oldCustAddress.getCustId()) && newCustAddressDto.getAddressType().equals(oldCustAddress.getAddressType())){
                    CaseCustAddress newCustAddress = new CaseCustAddress();
                    BeanUtils.copyProperties(newCustAddressDto,newCustAddress);
                    approveInformInfoService.changeByCustAddress(oldCustAddress,newCustAddress,caseCustInfo,new StringBuffer(),remindMessage,null);
                }
            }
        }
        //联系人信息对比
        List<CaseCustContact> oldCustContactList = caseCustContactService.list(Wrappers.<CaseCustContact>lambdaQuery().eq(CaseCustContact::getApplyNo, applyNo));
        HashMap<Long, CaseCustContact> idContactMap = new HashMap<>();
        for (CaseCustContact oldCustContact : oldCustContactList) {
            idContactMap.put(oldCustContact.getId(),oldCustContact);
        }
        for (CaseCustContactDto newCustContactDto : newCustContactList) {
            CaseCustContact oldCustContact = idContactMap.get(newCustContactDto.getId());
            if (!ObjectUtils.isEmpty(oldCustContact)){
                CaseCustContact newCustContact = new CaseCustContact();
                BeanUtils.copyProperties(newCustContactDto,newCustContact);
                approveInformInfoService.changeByCustContact(oldCustContact,newCustContact,new StringBuffer(),remindMessage,null);
            }

        }

        //车辆信息对比
        List<CaseCarInfo> oldCaseCarInfoList = caseCarInfoService.list(Wrappers.<CaseCarInfo>lambdaQuery().eq(CaseCarInfo::getApplyNo, applyNo));
        HashMap<Long, CaseCarInfo> idCaseCarInfoMap = new HashMap<>();
        for (CaseCarInfo oldCaseCarInfo : oldCaseCarInfoList) {
            idCaseCarInfoMap.put(oldCaseCarInfo.getId(),oldCaseCarInfo);
        }
        for (CaseCarInfoDto newCaseCarInfoDto : caseCarInfoList) {
            CaseCarInfo oldCaseCarInfo = idCaseCarInfoMap.get(newCaseCarInfoDto.getId());
            if (!ObjectUtils.isEmpty(oldCaseCarInfo)){
                CaseCarInfo newCaseCarInfo = new CaseCarInfo();
                BeanUtils.copyProperties(newCaseCarInfoDto,newCaseCarInfo);
                approveInformInfoService.changeByCaseCarInfo(oldCaseCarInfo,newCaseCarInfo,new StringBuffer(),remindMessage,null);
            }

        }
        //添加内部留言
        if (!ObjectUtils.isEmpty(remindMessage) && !"".equals(remindMessage.toString())){
            CaseChannelInfoDto caseChannelInfo = submitInfo.getCaseChannelInfo();
            approveInformInfoService.insertRemindByChange(applyNo,remindMessage,caseChannelInfo.getSaleAdvisor(),null,FinUpdateNodeEnum.CREDIT_APPLY.getCode());
        }
    }

    /**
     * parseCaseCustContact
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCustContact>
     */
    private ArrayList<CaseCustContact> parseCaseCustContact(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseCustContact> caseCustContactArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaesCustContactList())) {
            //新增
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseCustContactDto caseCustContactDto : orderSubmitInfo.getCaesCustContactList()) {
                    if (ObjectUtils.isEmpty(caseCustContactDto.getApplyNo()) || ObjectUtils.isEmpty(caseCustContactDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户联系人信息,applyNo、custId不能为空");
                    }
                    CaseCustContact caseCustContact = new CaseCustContact();
                    BeanUtils.copyProperties(caseCustContactDto, caseCustContact);
                    caseCustContact.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());

                    caseCustContactArrayList.add(caseCustContact);
                }
            }
            //修改
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {

                for (CaseCustContactDto caseCustContactDto : orderSubmitInfo.getCaesCustContactList()) {
                    if (ObjectUtils.isEmpty(caseCustContactDto.getApplyNo()) || ObjectUtils.isEmpty(caseCustContactDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户联系人信息,applyNo、custId不能为空");
                    }
                    CaseCustContact caseCustContact = new CaseCustContact();
                    BeanUtils.copyProperties(caseCustContactDto, caseCustContact);
                    caseCustContact.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());
                    caseCustContactArrayList.add(caseCustContact);
                }
                caseCustContactService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
            }
        }
        return caseCustContactArrayList;
    }

    /**
     * addCaseApproveRecord
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return CaseApproveRecord
     */
    private CaseApproveRecord addCaseApproveRecord(OrderSubmitInfo orderSubmitInfo) {

        CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
        caseApproveRecord.setApplyNo(orderSubmitInfo.getCaseBaseInfo().getApplyNo());
        caseApproveRecord.setUseScene(UseSceneEnum.APPROVE.getValue());
        caseApproveRecord.setApproveRemark("进件子系统提交案件信息");
        caseApproveRecord.setApproveStartTime(Calendar.getInstance().getTime());
        caseApproveRecord.setApproveEndTime(Calendar.getInstance().getTime());
        return caseApproveRecord;
    }

    /**
     * parseCaseCarInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCarInfo>
     */
    private ArrayList<CaseCarInfo> parseCaseCarInfo(OrderSubmitInfo orderSubmitInfo) throws ParseException {
        ArrayList<CaseCarInfo> caseCarInfoArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCarInfoList())) {
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseCarInfoDto caseCarInfoDto : orderSubmitInfo.getCaseCarInfoList()) {
                    if (ObjectUtils.isEmpty(caseCarInfoDto.getId()) || ObjectUtils.isEmpty(caseCarInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",车辆信息,id、applyNo不能为空");
                    }
                    CaseCarInfo caseCarInfo = new CaseCarInfo();
                    BeanUtils.copyProperties(caseCarInfoDto, caseCarInfo);
                    caseCarInfo.setId(caseCarInfoDto.getId());
                    caseCarInfoArrayList.add(caseCarInfo);
                }

            } else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseCarInfoService.remove(Wrappers.<CaseCarInfo>query().lambda()
                        .eq(CaseCarInfo::getApplyNo, orderSubmitInfo.getApplyNo()));
                for (CaseCarInfoDto caseCarInfoDto : orderSubmitInfo.getCaseCarInfoList()) {
                    if (ObjectUtils.isEmpty(caseCarInfoDto.getId()) || ObjectUtils.isEmpty(caseCarInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",车辆信息,id、applyNo不能为空");
                    }
                    CaseCarInfo caseCarInfo = new CaseCarInfo();
                    BeanUtils.copyProperties(caseCarInfoDto, caseCarInfo);
                    caseCarInfo.setId(caseCarInfoDto.getId());
                    caseCarInfoArrayList.add(caseCarInfo);
                }
            }

        } else {
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCarInfoList不可为空");
        }
        return caseCarInfoArrayList;
    }

    /**
     * parseCaseCarStyleDetail
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCarStyleDetail>
     */
    private ArrayList<CaseCarStyleDetail> parseCaseCarStyleDetail(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseCarStyleDetail> caseCarStyleDetailArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCarStyleDetailList())) {

            for (CaseCarStyleDetailDto caseCarStyleDetailDto : orderSubmitInfo.getCaseCarStyleDetailList()) {
                if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                        ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                    caseCarStyleDetailService.remove(Wrappers.<CaseCarStyleDetail>query().lambda()
                            .eq(CaseCarStyleDetail::getCarId,caseCarStyleDetailDto.getCarId()));
                }
                /**获取车300modelId*/
                CaseCarStyleDetail caseCarStyleDetail = new CaseCarStyleDetail();
                String carModelId=caseCarStyleDetailDto.getCarModelId();
                if(StringUtils.isNotEmpty(carModelId)){
                    Car300DTO car300DTO= new Car300DTO();
                    car300DTO.setModelId(carModelId);
                    /**调取车300接口获取车辆配置详情*/
                    log.info(orderSubmitInfo.getApplyNo()+"车300请求参数,{}",car300DTO);
                    CaseCarStyleDetailDto styleDetailDto=car300Service.queryCar300Info(car300DTO);
                    if(!ObjectUtils.isEmpty(styleDetailDto)){
                        styleDetailDto.setCarId(caseCarStyleDetailDto.getCarId());
                        styleDetailDto.setIsGreen(caseCarStyleDetailDto.getIsGreen());
                        styleDetailDto.setCarType(caseCarStyleDetailDto.getCarType());
                        styleDetailDto.setCarModelId(caseCarStyleDetailDto.getCarModelId());
                        styleDetailDto.setCarTypeDetail(caseCarStyleDetailDto.getCarTypeDetail());
                        styleDetailDto.setTripartEvaluatePrice(caseCarStyleDetailDto.getTripartEvaluatePrice());
                        BeanUtils.copyProperties(styleDetailDto, caseCarStyleDetail);
                    }else{
                        BeanUtils.copyProperties(caseCarStyleDetailDto, caseCarStyleDetail);
                    }
                }else{
                    BeanUtils.copyProperties(caseCarStyleDetailDto, caseCarStyleDetail);
                }
                /**爬虫获取车价信息*/
                //新能源 并且存在modelId
                if(IsTypeNumEnum.YES.getCode().equals(caseCarStyleDetailDto.getIsGreen())
                        &&StringUtils.isNotEmpty(carModelId)){
                    log.info(orderSubmitInfo.getApplyNo()+"确认新能源爬取车价，modelId，{}",carModelId);
                    CarCreeperDTO carCreeperDTO= new CarCreeperDTO();
                    //业务编号 申请编号
                    carCreeperDTO.setCaseCode(orderSubmitInfo.getApplyNo());
                    //是否新能源(1：是，2：否)目前业务只针对新能源调用 默认给‘1’
                    carCreeperDTO.setIsNewEnergy("1");
                    //id
                    carCreeperDTO.setModelId(carModelId);
                    //车型名称
                    carCreeperDTO.setVehicleType(orderSubmitInfo.getCaseCarInfoList().get(0).getModelName());
                    //指导价格
                    carCreeperDTO.setGuidePrice(orderSubmitInfo.getCaseCarInfoList().get(0).getGuidePrice().setScale(0, BigDecimal.ROUND_DOWN).toString());
                    //浮動价格
                    carCreeperDTO.setFloatPrice(gainCarCreeperParam());
                    //年份
                    carCreeperDTO.setCarYear(getCarYear(orderSubmitInfo.getCaseCarInfoList().get(0).getYearStyle())!=null?getCarYear(orderSubmitInfo.getCaseCarInfoList().get(0).getYearStyle()):"");
                    //购车地址
                    String cityName=orderSubmitInfo.getCaseCarInfoList().get(0).getPurchaseCity();

                    carCreeperDTO.setCityName(addressService.getLabelByCode(cityName));

                    carCreeperService.queryCarCreeperInfo(carCreeperDTO);
                }
                caseCarStyleDetailArrayList.add(caseCarStyleDetail);
            }
        }
        return caseCarStyleDetailArrayList;
    }

    /**
     * 影像资料解析
     * @param orderSubmitInfo
     * @return
     */
    private ArrayList<ComAttachmentFile> parseComAttachmentFile(OrderSubmitInfo orderSubmitInfo){
        //补充次数
        int num = 0;
        List<Long> idList = new ArrayList<>();
        List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBusiNo,orderSubmitInfo.getApplyNo())
                .orderByDesc(ComAttachmentFile::getCreateTime));
        if (CollectionUtil.isNotEmpty(fileList)){
            num = fileList.get(0).getTimes() != null ?  fileList.get(0).getTimes() + 1 : 1;
            idList = fileList.stream().map(l -> l.getId()).collect(Collectors.toList());
        }
        ArrayList<ComAttachmentFile> comAttachmentFileArrayList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(orderSubmitInfo.getComAttachmentFileListDto())) {
           /**删除不在传输文件中的原始文件*/
            List<ComAttachmentManagement> list= new ArrayList<>();
            comAttachmentManagementService.getAllManagementInfo().forEach(manageInfo ->{
                if(BusiNodeEnum.ORDER_APPLY.getCode().equals(manageInfo.getBusiNode())){
                    list.add(manageInfo);
                }
            });
            List<String> stringList=list.stream().map(m->String.valueOf(m.getId())).collect(Collectors.toList());
            List<Long> ids=orderSubmitInfo.getComAttachmentFileListDto().stream().map(s->s.getId()).collect(Collectors.toList());
            List<Long> longs = new ArrayList<>(ids);
            longs.removeAll(idList);

            List<ComAttachmentFile> notExitFiles=comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo,orderSubmitInfo.getApplyNo())
                    .eq(ComAttachmentFile::getBelongNo,orderSubmitInfo.getApplyNo())
                    .in(ComAttachmentFile::getAttachmentCode,stringList)
                    .notIn(ComAttachmentFile::getId,ids)
            );
            comAttachmentFileService.removeBatchByIds(notExitFiles);
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                    .eq(CaseBaseInfo::getApplyNo,orderSubmitInfo.getApplyNo()));
            CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo,orderSubmitInfo.getApplyNo())
                    .eq(CaseCustInfo::getCustRole,"01"));
            CaseCustInfo custInfo1 = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo,orderSubmitInfo.getApplyNo())
                    .eq(CaseCustInfo::getCustRole,"03"));
            //人脸信息
            List<ComAttachmentFile> faceList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo, orderSubmitInfo.getApplyNo())
                    .eq(ComAttachmentFile::getAttachmentName, "承租人身份证正面")
                    .orderByDesc(ComAttachmentFile::getCreateTime));
            List<ComAttachmentFile> garFaceList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo, orderSubmitInfo.getApplyNo())
                    .eq(ComAttachmentFile::getAttachmentName, "担保人身份证正面")
                    .orderByDesc(ComAttachmentFile::getCreateTime));
            String bankCardNo = applyContractFeign.getBankCardNo(orderSubmitInfo.getApplyNo(),getHeader());
            for (ComAttachmentFileDto attachmentFileDto : orderSubmitInfo.getComAttachmentFileListDto()) {
                if (ObjectUtils.isEmpty(attachmentFileDto.getBusiNo()) || ObjectUtils.isEmpty(attachmentFileDto.getBelongNo())) {
                    throw new AfsBaseException("业务编号:"+attachmentFileDto.getBusiNo() + ",所属业务编号:" + attachmentFileDto.getBelongNo()+"不能为空");
                }
                ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
                BeanUtils.copyProperties(attachmentFileDto,comAttachmentFile);
                if (longs.size() > 0 && longs.contains(comAttachmentFile.getId()) && num > 0){
                    comAttachmentFile.setTimes(num);
                    List<ComAttachmentManagement> list1 = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode()));
                    if (StrUtil.equals(String.valueOf(list1.get(0).getId()),comAttachmentFile.getAttachmentCode())){
                        //有补充流水则重新调用
                       caseBaseInfoService.turnoverAutomaticRecognition(orderSubmitInfo.getApplyNo(),caseBaseInfo,custInfo,comAttachmentFile,bankCardNo,custInfo1);
                    }
                    List<ComAttachmentManagement> driverList = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));
                    if (StrUtil.equals(String.valueOf(driverList.get(0).getId()),comAttachmentFile.getAttachmentCode())){
                        //有补充驾驶证则重新调用
                        caseBaseInfoService.driverRecognitionAsync(orderSubmitInfo.getApplyNo(), comAttachmentFile, caseBaseInfo, orderSubmitInfo.getCaseCustInfoList(), faceList, garFaceList);
                    }
                    List<ComAttachmentManagement> facePhotoList = comAttachmentManagementService.list(
                        Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getBusiNode, "orderApply")
                            .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                    );
                    if (StrUtil.equals(String.valueOf(facePhotoList.get(0).getId()),comAttachmentFile.getAttachmentCode())){
                        //有补充面签照则重新调用
                        log.info("{}补充面签照处理:{}", orderSubmitInfo.getApplyNo(), comAttachmentFile.getId());
                        // 异步ds解析面签信息
                        caseBaseInfoService.facePhotoAutomaticRecognition(orderSubmitInfo.getApplyNo(),
                            comAttachmentFile, "");
                        // 人脸识别对比
                        facePhotoInfoService.facePhotoidentifyAndCompare(comAttachmentFile, faceList.get(0),
                            orderSubmitInfo.getApplyNo());
                    }
                    List<ComAttachmentManagement> operatorManages = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.OPERATOR_CERTIFICATE.getCode()));
                    if (operatorManages.size() > 0 && StrUtil.equals(String.valueOf(operatorManages.get(0).getId()), comAttachmentFile.getAttachmentCode())) {
                        //有补充营运人证则重新调用
                        caseBaseInfoService.operatorDsRecognitionAsync(orderSubmitInfo.getApplyNo(), comAttachmentFile, caseBaseInfo, custInfo, faceList);
                    }
                    //重新上传身份证相关资料的重新进行智能识别
                    List<ComAttachmentManagement> certManages = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                            .eq(ComAttachmentManagement::getBusiNode, "orderApply")
                            .in(ComAttachmentManagement::getUniqueCode, Arrays.asList(AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode(),
                                    AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode(),
                                    AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode(),
                                    AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode())));
                    log.info("身份证影像管理列表={}",JSON.toJSONString(certManages));
                    for (ComAttachmentManagement certManage : certManages) {
                        log.info("当前影像attachmentCode={}，影像管理id={}",comAttachmentFile.getAttachmentCode(),certManage.getId());
                        if (StrUtil.equals(String.valueOf(certManage.getId()), comAttachmentFile.getAttachmentCode())) {
                            log.info("触发重新识别身份证信息");
                            //有补充身份证相关证件则重新调用
                            CardDetectRecordChangeCondition cardDetectRecordChangeCondition = new CardDetectRecordChangeCondition();
                            cardDetectRecordChangeCondition.setAttachmentCode(String.valueOf(certManage.getId()));
                            cardDetectRecordChangeCondition.setApplyNo(orderSubmitInfo.getApplyNo());
                            cardDetectRecordChangeCondition.setRefreshOcr(AfsEnumUtil.key(YesOrNoEnum.YES));
                            cardDetectRecordChangeCondition.setMd5(comAttachmentFile.getFileId());
                            cardDetectRecordChangeService.getByConditionAsync(cardDetectRecordChangeCondition);
                        }
                    }
                }
                comAttachmentFileArrayList.add(comAttachmentFile);
            }
        }
        return comAttachmentFileArrayList;
    }
    /**
     * <p>Description: </p>
     * @param orderSubmitInfo
     * @return ArrayList<ApplyRentAdjustDetails>
     */
    private ArrayList<FinRentAdjustDetails> parseApplyRentAdjustDetails(OrderSubmitInfo orderSubmitInfo) {
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getApplyRentAdjustDetailsList())) {
            if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                rentAdjustDetailsService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
            }
        }
        ArrayList<FinRentAdjustDetails> adjustDetailsArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getApplyRentAdjustDetailsList())) {
            for (FinRentAdjustDetails finRentAdjustDetailsDto : orderSubmitInfo.getApplyRentAdjustDetailsList()) {
                FinRentAdjustDetails finRentAdjustDetails = new FinRentAdjustDetails();
                BeanUtils.copyProperties(finRentAdjustDetailsDto, finRentAdjustDetails);
                adjustDetailsArrayList.add(finRentAdjustDetails);
            }
        }
        return adjustDetailsArrayList;
    }

    /**
     * parseCaseCostInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCostInfo>
     */
    private ArrayList<FinCostDetails> parseCaseCostInfo(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<FinCostDetails> finCostInfoArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCostInfoList())) {
            //新增
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (FinCostDetails finCostInfoDto : orderSubmitInfo.getCaseCostInfoList()) {
                    if (ObjectUtils.isEmpty(finCostInfoDto.getId()) || ObjectUtils.isEmpty(finCostInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资信息，id、applyNo");
                    }
                    FinCostDetails finCostInfo = new FinCostDetails();
                    BeanUtils.copyProperties(finCostInfoDto, finCostInfo);
                    finCostInfoArrayList.add(finCostInfo);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseCostInfoService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
                for (FinCostDetails finCostInfoDto : orderSubmitInfo.getCaseCostInfoList()) {
                    if (ObjectUtils.isEmpty(finCostInfoDto.getId()) || ObjectUtils.isEmpty(finCostInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资信息，id、applyNo");
                    }
                    FinCostDetails finCostInfo = new FinCostDetails();
                    BeanUtils.copyProperties(finCostInfoDto, finCostInfo);
                    finCostInfoArrayList.add(finCostInfo);
                }
            }


        } else {
            log.info("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCostInfoList不可为空！");
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCostInfoList不可为空！");
        }
        return finCostInfoArrayList;
    }


    /**
     * parseCaseCostInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<FinMainInfo>
     */
    private ArrayList<FinMainInfo> parseFinMainInfo(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<FinMainInfo> finMainInfoArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseFinMainInfo())) {
            //新增
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (FinMainInfo finMainInfoDto : orderSubmitInfo.getCaseFinMainInfo()) {
                    if (ObjectUtils.isEmpty(finMainInfoDto.getId()) || ObjectUtils.isEmpty(finMainInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资信息，id、applyNo");
                    }
                    FinMainInfo finMainInfo = new FinMainInfo();
                    BeanUtils.copyProperties(finMainInfoDto, finMainInfo);
                    finMainInfoArrayList.add(finMainInfo);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseFinMainInfoService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
                for (FinMainInfo finMainInfoDto : orderSubmitInfo.getCaseFinMainInfo()) {
                    if (ObjectUtils.isEmpty(finMainInfoDto.getId()) || ObjectUtils.isEmpty(finMainInfoDto.getApplyNo())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资信息，id、applyNo");
                    }
                    FinMainInfo finMainInfo = new FinMainInfo();
                    BeanUtils.copyProperties(finMainInfoDto, finMainInfo);
                    finMainInfoArrayList.add(finMainInfo);
                }
            }


        } else {
            log.info("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCostInfoList不可为空！");
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCostInfoList不可为空！");
        }
        return finMainInfoArrayList;
    }

    /**
     * parseCaseCustAddress
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCustAddress>
     */
    private ArrayList<CaseCustAddress> parseCaseCustAddress(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseCustAddress> caseCustAddressArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCustAddressList())) {
            //新增
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseCustAddressDto caseCustAddressDto : orderSubmitInfo.getCaseCustAddressList()) {
                    if (ObjectUtils.isEmpty(caseCustAddressDto.getApplyNo()) || ObjectUtils.isEmpty(caseCustAddressDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户地址信息,applyNo、custId不能为空");
                    }
                    CaseCustAddress caseCustAddress = new CaseCustAddress();
                    BeanUtils.copyProperties(caseCustAddressDto, caseCustAddress);
                    caseCustAddress.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());
                    caseCustAddressArrayList.add(caseCustAddress);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseCustAddressService.remove(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getApplyNo, orderSubmitInfo.getApplyNo()));
                for (CaseCustAddressDto caseCustAddressDto : orderSubmitInfo.getCaseCustAddressList()) {
                    if (ObjectUtils.isEmpty(caseCustAddressDto.getApplyNo()) || ObjectUtils.isEmpty(caseCustAddressDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户地址信息,applyNo、custId不能为空");
                    }
                    CaseCustAddress caseCustAddress = new CaseCustAddress();
                    BeanUtils.copyProperties(caseCustAddressDto, caseCustAddress);
                    caseCustAddress.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());
                    caseCustAddressArrayList.add(caseCustAddress);
                }
            }
        }
        return caseCustAddressArrayList;
    }

    /**
     * parseCaseCustCompany
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCustCompany>
     */
    private ArrayList<CaseEnterpriseCustomerDetails> parseCaseCustCompany(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseEnterpriseCustomerDetails> caseEnterpriseCustomerDetailsArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseEnterpriseCustomerDetailsDTOArrayList())) {
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseEnterpriseCustomerDetailsDTO caseEnterpriseCustomerDetailsDTO : orderSubmitInfo.getCaseEnterpriseCustomerDetailsDTOArrayList()) {
                    if (ObjectUtils.isEmpty(caseEnterpriseCustomerDetailsDTO.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",企业客户信息，custId不能为空");
                    }
                    CaseEnterpriseCustomerDetails details = new CaseEnterpriseCustomerDetails();
                    BeanUtils.copyProperties(caseEnterpriseCustomerDetailsDTO, details);
                    caseEnterpriseCustomerDetailsArrayList.add(details);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseEnterpriseCustomerDetailsDTO enterpriseCustomerDetailsDTO : orderSubmitInfo.getCaseEnterpriseCustomerDetailsDTOArrayList()) {
                    if (ObjectUtils.isEmpty(enterpriseCustomerDetailsDTO.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",个人客户信息，custId不能为空");
                    }
                    caseEnterpriseCustomerDetailsService.remove(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                            .eq(CaseEnterpriseCustomerDetails::getCustId, enterpriseCustomerDetailsDTO.getCustId()));
                    CaseEnterpriseCustomerDetails customerDetails = new CaseEnterpriseCustomerDetails();
                    BeanUtils.copyProperties(enterpriseCustomerDetailsDTO, customerDetails);
                    caseEnterpriseCustomerDetailsArrayList.add(customerDetails);
                }
            }
        }
        return caseEnterpriseCustomerDetailsArrayList;
    }

    /**
     * 联合方信息
     * @param orderSubmitInfo
     * @return
     */
    private ArrayList<CaseChannelUniteInfo> parseCaseChannelUniteInfo(OrderSubmitInfo orderSubmitInfo){
        ArrayList<CaseChannelUniteInfo> caseChannelUniteInfoList = new ArrayList<>();

        ArrayList<CaseChannelUniteInfoDto> caseChannelUniteInfoDtoList = orderSubmitInfo.getCaseChannelUniteInfoDto();
        if (!CollectionUtils.isEmpty(caseChannelUniteInfoDtoList)) {
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseChannelUniteInfoDto dto : caseChannelUniteInfoDtoList) {
                    CaseChannelUniteInfo caseChannelUniteInfo = new CaseChannelUniteInfo();
                    BeanUtils.copyProperties(dto, caseChannelUniteInfo);
                    caseChannelUniteInfoList.add(caseChannelUniteInfo);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseChannelUniteInfoDto dto : caseChannelUniteInfoDtoList) {
                    caseChannelUniteInfoService.remove(Wrappers.<CaseChannelUniteInfo>lambdaQuery().eq(CaseChannelUniteInfo::getApplyNo, dto.getApplyNo()).eq(CaseChannelUniteInfo::getDelFlag,"0"));
                    CaseChannelUniteInfo caseChannelUniteInfo = new CaseChannelUniteInfo();
                    BeanUtils.copyProperties(dto, caseChannelUniteInfo);
                    caseChannelUniteInfoList.add(caseChannelUniteInfo);
                }
            }
        }
        return caseChannelUniteInfoList;
    }

    /**
     * parseCaseCustIndividual
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCustIndividual>
     */
    private ArrayList<CaseCustIndividual> parseCaseCustIndividual(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseCustIndividual> caseCustIndividualArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCustIndividualList())) {
            if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseCustIndividualDto caseCustIndividualDto : orderSubmitInfo.getCaseCustIndividualList()) {
                    if (ObjectUtils.isEmpty(caseCustIndividualDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",个人客户信息，custId不能为空");
                    }
                    CaseCustIndividual caseCustIndividual = new CaseCustIndividual();
                    BeanUtils.copyProperties(caseCustIndividualDto, caseCustIndividual);
                    caseCustIndividualArrayList.add(caseCustIndividual);
                }
            }
            else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                for (CaseCustIndividualDto caseCustIndividualDto : orderSubmitInfo.getCaseCustIndividualList()) {
                    if (ObjectUtils.isEmpty(caseCustIndividualDto.getCustId())) {
                        throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",个人客户信息，custId不能为空");
                    }
                    caseCustIndividualService.remove(Wrappers.<CaseCustIndividual>query().lambda()
                            .eq(CaseCustIndividual::getCustId, caseCustIndividualDto.getCustId()));
                    CaseCustIndividual caseCustIndividual = new CaseCustIndividual();
                    BeanUtils.copyProperties(caseCustIndividualDto, caseCustIndividual);
                    caseCustIndividualArrayList.add(caseCustIndividual);
                }
            }
        }
        return caseCustIndividualArrayList;
    }

    /**
     * parseCaseCustInfo
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseCustInfo>
     */
    private ArrayList<CaseCustInfo> parseCaseCustInfo(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<CaseCustInfo> caseCustInfoArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseCustInfoList()) && orderSubmitInfo.getCaseCustInfoList().size() > 0) {
            for (CaseCustInfoDto caseCustInfoDto : orderSubmitInfo.getCaseCustInfoList()) {
                if (ObjectUtils.isEmpty(caseCustInfoDto.getId()) || ObjectUtils.isEmpty(caseCustInfoDto.getApplyNo())) {
                    log.info("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户信息,id、applyNo不能为空");
                    throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",客户信息,id、applyNo不能为空");
                }
                CaseCustInfo caseCustInfo = new CaseCustInfo();
                BeanUtils.copyProperties(caseCustInfoDto, caseCustInfo);
                caseCustInfoArrayList.add(caseCustInfo);
            }
        } else {
            throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",caseCustInfoList不能为空");
        }
        return caseCustInfoArrayList;
    }

    /**
     * parseCaseDiscountDetail
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseDiscountDetail>
     */
    private ArrayList<FinDiscountDetails> parseCaseDiscountDetail(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<FinDiscountDetails> caseDiscountDetailArrayList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseDiscountDetailList()) && orderSubmitInfo.getCaseDiscountDetailList().size() > 0) {
            if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseDiscountDetailService.deleteByApplyNo(orderSubmitInfo.getCaseDiscountDetailList().get(0).getApplyNo());
            }
            for (FinDiscountDetails finDiscountDetailsDto : orderSubmitInfo.getCaseDiscountDetailList()) {
                if (ObjectUtils.isEmpty(finDiscountDetailsDto.getApplyNo()) || ObjectUtils.isEmpty(finDiscountDetailsDto.getCostId())) {
                    throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",贴息信息，applyNo和costId不能为空");
                }
                FinDiscountDetails caseDiscountDetail = new FinDiscountDetails();
                BeanUtils.copyProperties(finDiscountDetailsDto, caseDiscountDetail);
                caseDiscountDetailArrayList.add(caseDiscountDetail);
            }
        }
        return caseDiscountDetailArrayList;
    }

    /**
     * parseCaseFinancingItems
     * <p>Description: </p>
     *
     * @param orderSubmitInfo orderSubmitInfo
     * @return ArrayList<CaseFinancingItems>
     */
    private ArrayList<FinFinancingItems> parseCaseFinancingItems(OrderSubmitInfo orderSubmitInfo) {

        ArrayList<FinFinancingItems> finFinancingItemsArrayList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(orderSubmitInfo.getCaseFinancingItemList()) && orderSubmitInfo.getCaseFinancingItemList().size() > 0) {
            if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                    ||OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
                caseFinancingItemsService.deleteByApplyNo(orderSubmitInfo.getCaseBaseInfo().getApplyNo());
            }
            for (FinFinancingItems finFinancingItemsDto : orderSubmitInfo.getCaseFinancingItemList()) {
                if (ObjectUtils.isEmpty(finFinancingItemsDto.getId()) || ObjectUtils.isEmpty(finFinancingItemsDto.getApplyNo())) {
                    log.info("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资项目信息，id、applyNo不能为空");
                    throw new AfsBaseException("申请编号:" + orderSubmitInfo.getCaseBaseInfo().getApplyNo() + ",融资项目信息，id、applyNo不能为空");
                }
                FinFinancingItems finFinancingItems = new FinFinancingItems();
                BeanUtils.copyProperties(finFinancingItemsDto, finFinancingItems);
                finFinancingItemsArrayList.add(finFinancingItems);
            }
//        }

        return finFinancingItemsArrayList;
    }
    /**
     * @description: 数据冗余
     * <AUTHOR>
     * @created 2020/8/15 14:57
     * @version 1.0
     */
    private void saveRedundantInfo(OrderSubmitInfo submitInfo,JSONObject object,JSONObject jsonObject){
        CaseRedundantInfo redundantInfo= redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(submitInfo.getApplyNo()),CaseRedundantInfo::getApplyNo,submitInfo.getApplyNo()));

        //常规审批
        if(ApplySceneEnum.GENERALAPPROVAL.getCode().equals(submitInfo.getApplyScene())){
           if(OprTypeEnum.OPRADD.getCode().equals(submitInfo.getOprType())){
                   CaseRedundantInfo caseRedundantInfo= new CaseRedundantInfo();
                   caseRedundantInfo.setBackSign(WhetherEnum.NO.getCode());
                   caseRedundantInfo.setApplyNo(submitInfo.getApplyNo());
                   caseRedundantInfo.setReconsiderSign(WhetherEnum.NO.getCode());
                   caseRedundantInfo.setBackEvidence(jsonObject.toString());
                   caseRedundantInfo.setConditionalApproval(object.toString());
                   redundantInfoService.save(caseRedundantInfo);
           }else{
               if(!ObjectUtils.isEmpty(redundantInfo)){
                   redundantInfo.setBackSign(WhetherEnum.YES.getCode());
                   redundantInfo.setReconsiderSign(WhetherEnum.NO.getCode());
                   redundantInfo.setConditionalApproval(object.toString());
                   redundantInfoService.updateById(redundantInfo);
               }
           }
        }

        //正式复议
        if(ApplySceneEnum.FORMALREVIEW.getCode().equals(submitInfo.getApplyScene())){
            if(OprTypeEnum.OPRUPDATE.getCode().equals(submitInfo.getOprType())){
                if(!ObjectUtils.isEmpty(redundantInfo)){
                    redundantInfo.setReconsiderEvidence(jsonObject.toString());
                    redundantInfo.setConditionalApproval(object.toString());
                    redundantInfoService.updateById(redundantInfo);
                }
            }else{
                if(!ObjectUtils.isEmpty(redundantInfo)){
                    redundantInfo.setBackSign(WhetherEnum.NO.getCode());
                    redundantInfo.setReconsiderSign(WhetherEnum.YES.getCode());
                    redundantInfo.setConditionalApproval(object.toString());
                    redundantInfoService.updateById(redundantInfo);
                }
            }
        }
        //判断数据是否更改，如果更改，则更新融资信息记录表
        JSONArray jsonArray = object.getJSONArray("finCostDetailsList");
        List<FinCostDetailsLog> finCostDetails = jsonArray.toList(FinCostDetailsLog.class);
        FinCostDetailsLog newLogs = new FinCostDetailsLog();
        if (CollectionUtil.isNotEmpty(finCostDetails)){
            for (FinCostDetailsLog finCostDetail : finCostDetails) {
                if (CostTypeEnum.CARAMT.getCode().equals(finCostDetail.getCostType())){
                    BeanUtils.copyProperties(finCostDetail,newLogs);
                }
            }
        }
        if (!ObjectUtils.isEmpty(newLogs)){
            DecimalFormat decimalFormat = new DecimalFormat("0.00#");
            newLogs.setDownPayScale(new BigDecimal(decimalFormat.format(newLogs.getDownPayScale() ==null ? BigDecimal.ZERO : newLogs.getDownPayScale())));
            newLogs.setDownPayAmt(new BigDecimal(decimalFormat.format(newLogs.getDownPayAmt()  ==null ? BigDecimal.ZERO : newLogs.getDownPayAmt())));
            newLogs.setCustMarginAmt(new BigDecimal(decimalFormat.format(newLogs.getCustMarginAmt() == null ? BigDecimal.ZERO : newLogs.getCustMarginAmt())));
            newLogs.setCustMarginAmt(new BigDecimal(decimalFormat.format(newLogs.getCustMarginAmt()== null ? BigDecimal.ZERO : newLogs.getCustMarginAmt())));
            newLogs.setMonthPayAmt(new BigDecimal(decimalFormat.format(newLogs.getMonthPayAmt()  ==null ? BigDecimal.ZERO : newLogs.getMonthPayAmt())));
            newLogs.setTotalInterest(new BigDecimal(decimalFormat.format(newLogs.getTotalInterest()  ==null ? BigDecimal.ZERO : newLogs.getTotalInterest())));
            newLogs.setLoanAmt(new BigDecimal(decimalFormat.format(newLogs.getLoanAmt()  ==null ? BigDecimal.ZERO : newLogs.getLoanAmt())));

            newLogs.setId(null);
            newLogs.setUpdateNode(FinUpdateNodeEnum.CREDIT_APPLY.getCode());
            //获取记录表最新数据
            List<FinCostDetailsLog> costDetailsLogList = finCostDetailsLogService.list(Wrappers.<FinCostDetailsLog>query().lambda()
                    .eq(FinCostDetailsLog::getApplyNo, submitInfo.getApplyNo())
                    .eq(FinCostDetailsLog::getCostType,CostTypeEnum.CARAMT.getCode())
                    .orderByDesc(FinCostDetailsLog::getCreateTime));

            if (CollectionUtil.isNotEmpty(costDetailsLogList)){
                //有变化就新增，没有就不变
                HashMap<String, String> messageMap = new HashMap<>();
                messageMap.put("loanAmt","融资总额");
                messageMap.put("downPayScale","首付比例");
                messageMap.put("downPayAmt","金额");
                messageMap.put("custMarginRatio","客户保证金比率");
                messageMap.put("custMarginAmt","客户保证金金额");
                messageMap.put("firmMarginRatio","厂商保证金比率");
                messageMap.put("firmMarginAmt","厂商保证金金额");
                messageMap.put("monthPayAmt","首期月供总额");
                messageMap.put("totalInterest","利息总额");
                messageMap.put("updateNode","操作节点");
                List<String> modifyMessages = CompareFieldsUtil.buildUpdateCostMessage(costDetailsLogList.get(0), newLogs, messageMap);
                if (CollectionUtil.isNotEmpty(modifyMessages)){
                   finCostDetailsLogService.save(newLogs);
                }
            }else {
                finCostDetailsLogService.save(newLogs);
            }
        }


    }
    /**
     * @description: 获取年款
     * <AUTHOR>
     * @created 2020/12/23 18:43
     * @version 1.0
     */
    public String getCarYear(String yearCode){
        String  carYear=dicData(yearCode,"yearStyle");
        log.info("车型年款对应关系解析结果：{}",carYear);
        if(StringUtils.isNotEmpty(carYear)){
            return carYear.replace("款","");
        }else{
            return  null;
        }
    }
    /**
     * @description: 获取浮动车价参数
     * <AUTHOR>
     * @created 2020/12/23 17:38
     * @version 1.0
     */
    public String gainCarCreeperParam() {
        CaseConfParam caseConfParam=caseConfParamService.getOne(Wrappers.<CaseConfParam>query().lambda()
                .eq(CaseConfParam::getParamType,"CAR_CREEPER_INFO"));
        if(!ObjectUtils.isEmpty(caseConfParam)){
            return caseConfParam.getParamValue();
        }else{
            return null;
        }
    }
    /**
     * @description: 字典翻译
     * <AUTHOR>
     * @created 2020/12/23 18:36
     * @version 1.0
     */
    public static String dicData(String param,String dicType){
        String result = "";
        Map<String, List<DicDataDto>> listMap = DicHelper.getDicMaps(dicType);
        List<DicDataDto> list=listMap.get(dicType);
        for(DicDataDto dicDataDto:list){
            if(dicDataDto.getValue().equals(param)){
                result = dicDataDto.getTitle();
            }
        }
        return result;
    }
    /**
     * @description: 用于常规、正式复议修订回复
     * <AUTHOR>
     * @created 2021/3/2 12:13
     * @version 1.0
     */
    public  void saveBackRecordInfo(OrderSubmitInfo entity){
        WorkTaskPool pool = workTaskPoolService.getOne(
                Wrappers.<WorkTaskPool>lambdaQuery()
                        .eq(WorkTaskPool::getStageId, entity.getDataId())
        );
        if(!ObjectUtils.isEmpty(pool)){
            CaseApproveRecord record = new CaseApproveRecord();
            record.setStageId(entity.getDataId());
            record.setApplyNo(entity.getApplyNo());
            record.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.MODIFY_SUBMIT));
            record.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.MODIFY_SUBMIT));
            record.setDisposeNode(pool.getTaskNodeId());
            record.setDisposeNodeName(pool.getTaskNodeName());
            record.setApproveStartTime(pool.getStartTime());
            record.setApproveEndTime(new Date());
            record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
            record.setDisposeStaff(pool.getApproveStaffName());
            record.setApproveRemark(entity.getLeaveMessage());
            caseApproveRecordService.save(record);
        }
    }
    /**
     * @description: 用于信贷选项
     * <AUTHOR>
     * @created 2021/8/16 12:13
     * @version 1.0
     */
    public  void saveCreditOption(OrderSubmitInfo entity){
        //规则需要匹配的数据
        CaseBaseInfo baseInfo= caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo,entity.getApplyNo()));
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, entity.getApplyNo()));
        CaseCreditInfoDto creditInfoDto= new CaseCreditInfoDto();
        creditInfoDto.setBusinessType(baseInfo.getBusinessType());
        creditInfoDto.setCarType(baseInfo.getCarType());
        creditInfoDto.setIsLcv(caseCarInfo.getIsLcv());

        String applyNo = entity.getApplyNo();
        //逻辑删除;
        List<CaseCreditOption> optionList = new ArrayList<>();
        List<CaseCreditOptionConf> creditOptionConfList = caseCreditOptionConfService.list(
                Wrappers.<CaseCreditOptionConf>lambdaQuery()
                        .eq(CaseCreditOptionConf::getOptionStatus,AfsEnumUtil.key(EffectiveStatusEnum.ENABLE))
        );
        List<Long> ids= new ArrayList<>();
        if(CollectionUtil.isNotEmpty(creditOptionConfList)){
            creditOptionConfList.forEach(optionConf -> {
                //查看是否已经维护数据
                CaseCreditOption option=   caseCreditOptionService.getOne(Wrappers.<CaseCreditOption>query().lambda()
                        .eq(CaseCreditOption::getApplyNo,applyNo)
                        .eq(CaseCreditOption::getOptionNo,optionConf.getOptionNo()));
                //规则有效
                if(optionConf.getStatus().equals(AfsEnumUtil.key(EffectiveStatusEnum.ENABLE))){
                    boolean ruleRunResult;
                    try {
                        ruleRunResult = RuleHelper.runRule(JSON.parseObject(JSON.toJSONString(creditInfoDto)),optionConf.getId().toString()).getHit();
                    } catch (Exception e) {
                        ruleRunResult = false;
                        log.info("调用信贷选项规则失败异常原因：{}", e.getMessage());
                    }
                    if(ruleRunResult){
                        if(!ObjectUtils.isEmpty(option)&&!org.springframework.util.StringUtils.isEmpty(option.getValue())){
                            ids.add(option.getId());
                            log.info("常规生效且已经存在完成选择的：{},{}",entity.getApplyNo(), optionConf.getOptionName());
                        }else{
                            CaseCreditOption optionInfo = new CaseCreditOption();
                            optionInfo.setApplyNo(applyNo);
                            optionInfo.setOptionDicKey(optionConf.getOptionDicKey());
                            optionInfo.setOptionName(optionConf.getOptionName());
                            optionInfo.setStatus(optionConf.getStatus());
                            optionInfo.setOptionType(optionConf.getOptionType());
                            optionInfo.setSpecialTypeKey(optionConf.getSpecialTypeKey());
                            optionInfo.setOptionStatus(optionConf.getOptionStatus());
                            optionInfo.setOptionNo(optionConf.getOptionNo());
                            optionList.add(optionInfo);
                            log.info("常规生效信贷选项输出：{},{}",applyNo, optionConf.getOptionName());
                        }

                    }

                }else{
                    if(!ObjectUtils.isEmpty(option)&&!org.springframework.util.StringUtils.isEmpty(option.getValue())){
                        ids.add(option.getId());
                        log.info("常规未生效且已经存在完成选择的：{},{}",applyNo, optionConf.getOptionName());
                    }else{
                        CaseCreditOption optionInfo = new CaseCreditOption();
                        optionInfo.setApplyNo(applyNo);
                        optionInfo.setOptionDicKey(optionConf.getOptionDicKey());
                        optionInfo.setOptionName(optionConf.getOptionName());
                        optionInfo.setStatus(optionConf.getStatus());
                        optionInfo.setOptionType(optionConf.getOptionType());
                        optionInfo.setSpecialTypeKey(optionConf.getSpecialTypeKey());
                        optionInfo.setOptionStatus(optionConf.getOptionStatus());
                        optionInfo.setOptionNo(optionConf.getOptionNo());
                        optionList.add(optionInfo);
                        log.info("常规未生效信贷选项输出：{},{}",applyNo, optionConf.getOptionName());
                    }
                }
            });
        }
        //删除常规的
        caseCreditOptionService.remove(Wrappers.<CaseCreditOption>query().lambda().eq(CaseCreditOption::getApplyNo,applyNo)
                .notIn(CollectionUtil.isNotEmpty(ids),CaseCreditOption::getId,ids));

        if(CollectionUtil.isNotEmpty(optionList)){
            caseCreditOptionService.saveBatch(optionList);
        }

    }
    /**
     * 手续费信息
     * @param orderSubmitInfo
     * @return
     */
    private ArrayList<HandlingInfo> parseHandlingInfo(OrderSubmitInfo orderSubmitInfo){
        ArrayList<HandlingInfo> handlingInfoList = new ArrayList<>();
        List<HandlingInfoDto> handlingInfoDtoList = orderSubmitInfo.getHandlingInfoDtos();
        if (CollectionUtils.isEmpty(handlingInfoDtoList)) {
            return handlingInfoList;
        }
        if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
            for (HandlingInfoDto dto : handlingInfoDtoList) {
                HandlingInfo handlingInfo = new HandlingInfo();
                BeanUtils.copyProperties(dto, handlingInfo);
                handlingInfoList.add(handlingInfo);
            }
        } else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            handlingInfoService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
            for (HandlingInfoDto dto : handlingInfoDtoList) {
                HandlingInfo handlingInfo = new HandlingInfo();
                BeanUtils.copyProperties(dto,handlingInfo);
                handlingInfoList.add(handlingInfo);
            }
        }
        return handlingInfoList;
    }
    /**
     * 保证金信息
     * @param orderSubmitInfo
     * @return
     */
    private ArrayList<MarginInfo> parseMarginInfo(OrderSubmitInfo orderSubmitInfo){
        ArrayList<MarginInfo> marginInfoList = new ArrayList<>();
        List<MarginInfoDto> marginInfoDtoList = orderSubmitInfo.getMarginInfoDtos();
        if (OprTypeEnum.OPRADD.getCode().equals(orderSubmitInfo.getOprType())) {
            if (CollectionUtils.isEmpty(marginInfoDtoList)) {
                return marginInfoList;
            }
            for (MarginInfoDto dto : marginInfoDtoList) {
                MarginInfo marginInfo = new MarginInfo();
                BeanUtils.copyProperties(dto, marginInfo);
                marginInfoList.add(marginInfo);
            }
        } else if (OprTypeEnum.OPRUPDATE.getCode().equals(orderSubmitInfo.getOprType())
                || OprTypeEnum.UPDATE_RECONSIDER.getCode().equals(orderSubmitInfo.getOprType())) {
            marginInfoService.deleteByApplyNo(orderSubmitInfo.getApplyNo());
            if (CollectionUtils.isEmpty(marginInfoDtoList)) {
                return marginInfoList;
            }
            for (MarginInfoDto dto : marginInfoDtoList) {
                MarginInfo marginInfo = new MarginInfo();
                BeanUtils.copyProperties(dto, marginInfo);
                marginInfoList.add(marginInfo);
            }
        }
        return marginInfoList;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public void updateCaseMainStatus(CaseBaseInfoDto caseBaseInfo){
        //主状态修改
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        StatusDTO caseStatusDTO = new StatusDTO();
        caseStatusDTO.setStatusCode(AfsEnumUtil.key(BusinessStateInEnum.SUBMITTED));
        caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(BusinessStateInEnum.SUBMITTED));
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_2100);
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
        caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
        caseMainInfoService.updateCaseMain(caseMainUpdateCondition);

    }

    /**
     * 获取 header
     * @return
     */
    private Map<String, String> getHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }

}
