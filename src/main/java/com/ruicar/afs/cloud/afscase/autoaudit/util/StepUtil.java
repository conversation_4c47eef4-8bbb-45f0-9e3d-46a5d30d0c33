package com.ruicar.afs.cloud.afscase.autoaudit.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.enums.RealTimeDataTypeEnum;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.LoanLabelInfo;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.autoapprove.condition.LoanAutoApproveBackRuleCondition;
import com.ruicar.afs.cloud.afscase.autoapprove.condition.LoanAutoApproveRuleCondition;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.LoanServiceBox;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.StepInvoker;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.StepParam;
import com.ruicar.afs.cloud.afscase.autoaudit.loan.Steps;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.entity.CaseBackToPartnersInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAuthorizeRegion;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelReceivablesAccount;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelReceivableAccountService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.execute.enums.ExecuteBusinessTypeEnum;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseLoanAwaitInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveCondition;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.manuallabel.entity.ManualLabel;
import com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum;
import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.afscase.risk.vo.DecisionResult;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.RiskErrorEnum;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.FlowNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsAssignedEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanModelEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PriorityEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.StepSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TopEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PriorityEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.enums.common.CapitalOrderStatusEnum;
import com.ruicar.afs.cloud.enums.common.LoanAutoApproveResultEnum;
import com.ruicar.afs.cloud.enums.common.SubmissionTypeEnum;
import com.ruicar.afs.cloud.filecenter.ftp.dto.UploadInputDto;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.loan.sdk.dto.LoanOrderSubmitReqDTO;
import com.ruicar.afs.cloud.loan.sdk.dto.Req;
import com.ruicar.afs.cloud.loan.sdk.dto.RequestBody;
import com.ruicar.afs.cloud.loan.sdk.dto.Resp;
import com.ruicar.afs.cloud.loan.sdk.dto.StatusInfoReqDTO;
import com.ruicar.afs.cloud.loan.sdk.dto.StatusInfoRespDTO;
import com.ruicar.afs.cloud.loan.sdk.dto.VehicleAndCapitalTypeDTO;
import com.ruicar.afs.cloud.loan.sdk.enums.AttachmentCodeEnum;
import com.ruicar.afs.cloud.risk.api.enums.RiskStatus;
import com.ruicar.afs.cloud.risk.api.enums.TencentControlRequestPath;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ruicar.afs.cloud.afscase.processor.enums.LoanSubmitEnum.REPAIR_REPLY;

/**
 * @Description
 * <AUTHOR>
 * @Date
 */

@UtilityClass
@Slf4j
public class StepUtil {

    final LoanServiceBox serviceBox;

    static {
        serviceBox = SpringContextHolder.getBean(LoanServiceBox.class);
    }

    /**
     * @Description 分单前置步骤执行，归档超期 -> 影像件ocr识别 -> 电子签名比对 -> 玄武大数据 -> 暂停拦截 -> 审批超时 -> 自动打标签
     * <AUTHOR>
     * @Date
     */
    public void prevSteps(StepParam stepParams) {
        CaseBaseInfoService caseBaseInfoService = serviceBox.getCaseBaseInfoService();
        CaseContractInfoService caseContractInfoService = serviceBox.getCaseContractInfoService();
        if (Objects.isNull(stepParams.getBaseInfo())) {
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, stepParams.getApplyNo()), false);
            CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, stepParams.getContractNo()), false);
            stepParams.setBaseInfo(caseBaseInfo);
            stepParams.setContractInfo(contractInfo);
        }
        if(!serviceBox.getIExecuteWorkTask().executeWork(ExecuteBusinessTypeEnum.loan,"prevSteps",stepParams)) {
            Steps.create()
//                    .then(overdue())
//                    .then(ocr())
//                    .then(sign())
//                    .then(credit())
                    .then(suspend())
                    .then(overtime())
                    .then(title())
                    .then(setUpTopLoan())
                    .err(prevStepErr())
                    .end(prevStepEnd())
                    .doStep(stepParams, Convert.toInt(stepParams.getContractInfo().getWorkflowPrevStep(), 0));
        }
    }


    /**
     * @Description 自动打标签
     * <AUTHOR>
     * @Date 2020/7/14 17:30
     */
    private static StepInvoker title() {
        return (stepParam, currentStep) -> {
            try {
                log.info("合同{}:自动打标签开始执行...", stepParam.getContractNo());
                serviceBox.getManualLabelService().loanForLabel(stepParam.getContractNo());
            } catch (Exception e) {
                log.error("自动打标签执行失败.", e);
            }finally {
                log.info("合同{}:自动打标签执行结束...", stepParam.getContractNo());
                stepParam.getContractInfo().setWorkflowPrevStep(currentStep);
                serviceBox.getCaseContractInfoService().updateById(stepParam.getContractInfo());
            }
            return true;
        };
    }


    /**
     * @Description 拦截暂停检测
     * <AUTHOR>
     * @Date 2020/7/30
     */
    private static StepInvoker suspend() {
        return (stepParam, currentStep) -> {
            CaseContractInfo contractInfo = stepParam.getContractInfo();
            try {
                serviceBox.getLoanApproveService().setSuspend(contractInfo);
            } catch (Exception e) {
                log.error("合同{}:拦截暂停检测执行失败.", e);
            }finally {
                contractInfo.setWorkflowPrevStep(currentStep);
                serviceBox.getCaseContractInfoService().updateById(contractInfo);
            }
            return true;
        };
    }

    /**
     * @Description 超审批时效
     * <AUTHOR>
     * @Date 2020/12/15
     */
    private static StepInvoker overtime() {
        return (stepParam, currentStep) -> {
            CaseContractInfo contractInfo = stepParam.getContractInfo();
            try {
                serviceBox.getLoanApproveService().setOvertime(contractInfo);
            } catch (Exception e) {
                log.error("合同{}:拦截暂停检测执行失败.", e);
            }finally {
                contractInfo.setWorkflowPrevStep(currentStep);
                serviceBox.getCaseContractInfoService().updateById(contractInfo);
            }
            return true;
        };
    }



    /**
     * @Description 分单前置错误处理
     * <AUTHOR>
     * @Date 2020/7/14 17:30
     */
    private static StepInvoker prevStepErr() {
        return (stepParam, currentStep) -> {
            log.info("合同:{}=>执行错误，当前步骤执行至 " + currentStep, stepParam.getContractNo());
            CaseContractInfo contractInfo = stepParam.getContractInfo();
            if(contractInfo.getApplyStatus().equals(ApplyStatusEnum.LOAN_PRE_PROCESS.getState())){
                contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_QUEUE.getState());
            }
            contractInfo.setWorkflowPrevStep(currentStep);
            serviceBox.getCaseContractInfoService().updateById(contractInfo);
            return true;
        };
    }

    /**
     * @Description 正常结束处理
     * <AUTHOR>
     * @Date 2020/7/8 17:31
     */
    private static StepInvoker prevStepEnd() {
        return (stepParam, endStep) -> {
            log.info("{}:自动分单前置逻辑执行完毕...", stepParam.getContractNo());
            // 流程退回时修改提交处理
            CaseContractInfo contractInfo = stepParam.getContractInfo();
            if (contractInfo.getApplyStatus().equals(ApplyStatusEnum.LOAN_RETURN.getState())) {

                try {

                    // TODO 根据回调参数操作，不用业务号反查
                    WorkflowProcessBusinessRefInfo refInfo = serviceBox.getWorkflowProcessBusinessRefInfoService().getOne(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                            .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, contractInfo.getApplyNo())
                            .eq(WorkflowProcessBusinessRefInfo::getFlowPackageId, serviceBox.getFlowConfigProperties().getLoanPackageId())
                            .eq(WorkflowProcessBusinessRefInfo::getFlowTemplateId, serviceBox.getFlowConfigProperties().getLoanTemplateId())
                    );

                    if(ObjectUtils.isNotEmpty(refInfo)){

                        log.warn("放款自动审核，非首次提交操作，之前进入过流程！");

                        //更新合同审核状态:补件回复
                        contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_REPAIR.getState());

                        contractInfo.setFlowNode(FlowNodeEnum.PRIMARY.getCode());
                        contractInfo.setPriority(serviceBox.getCasePriorityChangeService().priorityChange(contractInfo));
                        serviceBox.getCaseContractInfoService().updateById(contractInfo);

                        //工作流提交流程  保存操作记录
                        LoanApproveCondition condition = new LoanApproveCondition();
                        condition.setApplyNo(contractInfo.getApplyNo());
                        condition.setContractNo(contractInfo.getContractNo());
                        condition.setAutoFlag(Boolean.FALSE);
                        condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                        condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                        condition.setApproveSuggest(AfsEnumUtil.key(REPAIR_REPLY));
                        condition.setFlowNode(FlowNodeEnum.CHANNEL.getCode());

                        // 保存放款自动审核结果信息
                        if(saveLoanAutoApproveRuleResult(contractInfo,AfsEnumUtil.key(SubmissionTypeEnum.SECOND_TIME),stepParam.getBelongingCapital())){

                            serviceBox.getLoanWorkflowService().newSubmitWorkflowByScheduleInfo(condition,contractInfo.getAutoApproveFlag());
                            serviceBox.getCaseStepParamService().clear(contractInfo.getApplyNo(), StepSceneEnum.LOAN);
                            return true;
                        }
                    }else{

                        log.warn("放款自动审核，首次提交操作，之前没有进入过流程！");

                        contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_QUEUE.getState());
                        contractInfo.setWorkflowPrevStep(endStep);
                        serviceBox.getCaseContractInfoService().updateById(contractInfo);
                        serviceBox.getCaseStepParamService().clear(contractInfo.getApplyNo(),StepSceneEnum.LOAN);
                        // 首次提交
                        saveLoanAutoApproveRuleResult(contractInfo,AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME),stepParam.getBelongingCapital());
                    }

                } catch (Exception e) {
                    log.error("合同：{}=>{}流程提交失败：{}", stepParam.getContractNo(), AfsEnumUtil.desc(REPAIR_REPLY), e);
                    return false;
                }

                return true;
            } else if (contractInfo.getApplyStatus().equals(ApplyStatusEnum.LOAN_CALL_BACK.getState())) {
                //更新合同审核状态:补件回复
                contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());
                contractInfo.setFlowNode(FlowNodeEnum.PRIMARY.getCode());
                contractInfo.setPriority(serviceBox.getCasePriorityChangeService().priorityChange(contractInfo));
                serviceBox.getCaseContractInfoService().updateById(contractInfo);

                //工作流提交流程  保存操作记录
                LoanApproveCondition condition = new LoanApproveCondition();
                condition.setApplyNo(contractInfo.getApplyNo());
                condition.setContractNo(contractInfo.getContractNo());
                condition.setAutoFlag(Boolean.FALSE);
                condition.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                condition.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                condition.setApproveSuggest(AfsEnumUtil.key(LoanSubmitEnum.BACK_DEALER));
                condition.setFlowNode(FlowNodeEnum.CHANNEL.getCode());
                try {
                    serviceBox.getLoanWorkflowService().submitWorkflowByScheduleInfo(condition);
                    serviceBox.getCaseStepParamService().clear(contractInfo.getApplyNo(),StepSceneEnum.LOAN);
                    return true;
                } catch (Exception e) {
                    log.error("合同：{}=>{}流程提交失败：{}", stepParam.getContractNo(), AfsEnumUtil.desc(REPAIR_REPLY), e);
                    return false;
                }
            }else {
                if(contractInfo.getApplyStatus().equals(ApplyStatusEnum.LOAN_PRE_PROCESS.getState())
                        ||contractInfo.getApplyStatus().equals(ApplyStatusEnum.CREDIT_CONFIRM.getState())){
                    contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_QUEUE.getState());
                }
                contractInfo.setWorkflowPrevStep(endStep);
                serviceBox.getCaseContractInfoService().updateById(contractInfo);
                serviceBox.getCaseStepParamService().clear(contractInfo.getApplyNo(),StepSceneEnum.LOAN);
                // 首次提交
                saveLoanAutoApproveRuleResult(contractInfo,AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME),stepParam.getBelongingCapital());

                return true;
            }
        };
    }

    /**
     * 判断结果是转人工还是自动退回
     * @param listMap 自动审核结果相关数据
     * @param contractInfo 合同信息
     * @return true - 自动退回，false - 转人工
     */
    public static boolean checkResult(Map<String,List<String>> listMap, CaseContractInfo contractInfo){

        log.info("开始判断放款自动结果是转人工还是自动退回！，其中入参listMap={}，contractInfo={}", listMap, contractInfo);

        // 判断挡板，1-直接走人工，0-走自动审核逻辑
        String autoBackSwitch = "1";
        try {
            autoBackSwitch = autoBackRuleSwitch();
        } catch (Exception e){
            log.error("获取放款自动退回挡板结果失败，默认设置为1-直接走人工，获取挡板失败原因="+e+"！");
            autoBackSwitch = "1";
        }

        if("1".equals(autoBackSwitch)){
            return false;
        }

        // 判断是否为工作日，如果为工作日，则转人工，如果为放假日，则进行自动退回判断
        boolean currentSysHolidayInfo = serviceBox.getCaseAutoBackHolidayService().getCurrentSysHolidayInfo();

        // 转人工
        if(currentSysHolidayInfo){
            if(listMap.get("2") == null){
                log.info("开始判断放款自动结果是转人工还是自动退回！其中结果是转人工，原因为自动审核不通过！");
            }else{
                // 判断自动退回逻辑
                LoanAutoApproveBackRuleCondition loanAutoApproveBackRuleCondition = new LoanAutoApproveBackRuleCondition();
                loanAutoApproveBackRuleCondition.setApplyNo(contractInfo.getApplyNo());
                String result = serviceBox.getLoanAutoApproveBackService().executeLoanAutoApproveBackRule(loanAutoApproveBackRuleCondition,listMap.get("3"), contractInfo);
                if(CaseConstants.AUTO_BACK.equals(result)){
                    log.info("开始判断放款自动结果是转人工还是自动退回！其中结果是自动退回！");
                    return true;
                }else{
                    log.info("开始判断放款自动结果是转人工还是自动退回！其中结果是转人工，原因为自动退回规则不通过！");
                }
            }
        }else{
            log.warn("查询到的节假日信息为非节假日！");
        }

        return false;

    }

    /**
     * 保存自动审核结果
     * @param contractInfo 合同信息
     */
    public static boolean saveLoanAutoApproveRuleResult(CaseContractInfo contractInfo,String flag,String belongingCapital){

        log.warn("放款自动审核，入参flag={}！",flag);

        // 设置基础审核信息
        contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.NO_APPROVE));

        LoanAutoApproveRuleCondition condition = new LoanAutoApproveRuleCondition();
        condition.setApplyNo(contractInfo.getApplyNo());

        List<String> strings = new ArrayList<>();
        Map<String,List<String>> listMap = new HashMap<>();
        try{
            listMap = serviceBox.getLoanAutoApproveService().executeLoanAutoApproveRule(condition);
        } catch (Exception e){
            log.error("放款自动审核抛出了异常！具体问题="+e);
            if(checkResult(listMap,contractInfo)){
                // 自动退回逻辑-需补充
                autoBack(contractInfo.getApplyNo(),contractInfo,listMap.get("1"));
                return false;
            }else{
                contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_FAILURE));
            }

        }

        /**
         * 如果为true的话，则表示关闭挡板
         */
        String onCheck = "0";
        try {
            onCheck = getOnCheck();
        } catch (Exception e){
            log.error("获取放款自动审核挡板结果失败，默认设置为开启挡板，获取挡板失败原因="+e+"！");
            onCheck = "0";
        }

        //进行放款决策引擎校验
        boolean loanDecision = loanDecisionResult(contractInfo.getApplyNo(), contractInfo);
        log.info("申请编号={}的订单，放款决策引擎结果为：{}", condition.getApplyNo() ,loanDecision? "决策通过" : "转人工");

        // 关闭挡板
        if("0".equals(onCheck)){
            //决策引擎结果通过，走自动放款逻辑
            if (loanDecision) {
                log.info("申请编号={}的订单，放款自动审核挡板开关已关闭，所有的放款提交，开始进行逻辑判断，判断结果影响订单是由人工审核还是自动审核！", condition.getApplyNo());
                // 放款外部审核不通过，直接退回经销商
                if (ObjectUtil.isNotEmpty(listMap.get("0")) && listMap.get("0").size() > 0) {
                    // 匹配到了不允许自动审核的规则
                    log.warn("放款自动审核审核不通过，原因={}！", JSON.toJSONString(listMap.get("0")));
                    if(checkResult(listMap,contractInfo)){
                        // 自动退回逻辑-需补充
                        autoBack(contractInfo.getApplyNo(),contractInfo,listMap.get("1"));
                        return false;
                    }else{
                        contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_FAILURE));
                    }
                    // 修改自动放款结果修饰符
                    serviceBox.getCaseContractInfoService().updateById(contractInfo);

                } else {

                    // 外部审核通过，判断内部审核合Ocr审核结果
                    strings = listMap.get("1");
                    // 放款自动审核内部审核或者Ocr审核不通过，转入人工审核
                    if (ObjectUtil.isNotEmpty(strings) && strings.size() > 0) {
                        // 匹配到了不允许自动审核的规则
                        log.warn("放款自动审核审核不通过，原因={}！", JSON.toJSONString(strings));
                        if(checkResult(listMap,contractInfo)){
                            // 自动退回逻辑-需补充
                            autoBack(contractInfo.getApplyNo(),contractInfo,listMap.get("1"));
                            return false;
                        }else{
                            contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_FAILURE));
                        }
                        // 修改自动放款结果修饰符
                        serviceBox.getCaseContractInfoService().updateById(contractInfo);
                    } else {
                        log.info("放款自动审核审核正常通过，原因={}！", JSON.toJSONString(strings));
                        contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_SUCCESS));
                        // 获取所属资方，如果所属资方=FD弗迪，则保持旧逻辑，如果所属资方=BANK银行，则进入等待状态
                        if(CaseConstants.BELONGING_CAPITAL_FD.equals(belongingCapital)){
                            loanAutoApprovedLogic(contractInfo, flag);
                        }else{
                            loanAutoApprovedLogicAwait(contractInfo, flag);
                        }
                    }
                }
            }else {
                    //决策引擎结果为转人工，直接走人工审核逻辑
                    if(checkResult(listMap,contractInfo)){
                        // 自动退回逻辑-需补充，使用mq退回相关逻辑
                        autoBack(contractInfo.getApplyNo(),contractInfo,listMap.get("1"));
                        return false;
                    }else{
                        contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_FAILURE));
                    }
                    serviceBox.getCaseContractInfoService().updateById(contractInfo);
                }
        }else if("2".equals(onCheck)){
            log.info("申请编号={}的订单，放款自动审核挡板开关已打开，所有的放款提交，只进行数据模拟，模拟结果不再影响是否进行自动审核，统一使用自动审核！",condition.getApplyNo());
            contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_SUCCESS));
            // 获取所属资方，如果所属资方=FD弗迪，则保持旧逻辑，如果所属资方=BANK银行，则进入等待状态
            if(CaseConstants.BELONGING_CAPITAL_FD.equals(belongingCapital)){
                loanAutoApprovedLogic(contractInfo, flag);
            }else{
                loanAutoApprovedLogicAwait(contractInfo, flag);
            }
            // 修改自动放款结果修饰符
            serviceBox.getCaseContractInfoService().updateById(contractInfo);
        }else{
            log.info("申请编号={}的订单，放款自动审核挡板开关已打开，所有的放款提交，只进行数据模拟，模拟结果不再影响是否进行自动审核，统一使用人工审核！",condition.getApplyNo());
            if(checkResult(listMap,contractInfo)){
                // 自动退回逻辑-需补
                autoBack(contractInfo.getApplyNo(),contractInfo,listMap.get("1"));
                return false;
            }else{
                contractInfo.setAutoApproveFlag(AfsEnumUtil.key(LoanAutoApproveResultEnum.APPROVE_FAILURE));
            }
            // 修改自动放款结果修饰符
            serviceBox.getCaseContractInfoService().updateById(contractInfo);
        }

        return true;

    }

    /**
     * 自动退回 - 自动退回规则引起的自动退回逻辑
     * @param applyNo 申请编号
     * @param caseContractInfo 合同信息
     * @param list 原因list
     */
    private void autoBack(String applyNo,CaseContractInfo caseContractInfo,List<String> list){

        // 保存日志记录
        CaseApproveRecord record = new CaseApproveRecord();
        record.setApplyNo(applyNo);
        record.setUseScene(UseSceneEnum.APPROVE.getValue());
        record.setApproveSuggest(FlowTaskOperationEnum.AUTOLOANAUTOBACK.getCode());
        record.setApproveSuggestName(FlowTaskOperationEnum.AUTOLOANAUTOBACK.getDesc());
        record.setApproveEndTime(new Date());
        record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record.setApproveRemark(FlowTaskOperationEnum.AUTOLOANAUTOBACK.getDesc());
        record.setDisposeNodeName("放款自动审核");
        record.setDisposeStaff("系统");
        serviceBox.getCaseApproveRecordService().save(record);

        // 退回原因
        StringBuilder sb = new StringBuilder();
        if(ObjectUtil.isNotEmpty(list) && list.size() > 0){
            for(String str : list){
                sb.append(str).append("\r\n");
            }
        }else{
            log.info("========================= 放款自动审核通过 =========================");
            if(!com.ruicar.afs.cloud.afscase.channel.enums.WhetherEnum.YES.getCode().equals(serviceBox.getBydDirectDealerProperties().getRuleFlag())){
                sb.append("内部审核校验不通过").append("\r\n");
            }
            if(!com.ruicar.afs.cloud.afscase.channel.enums.WhetherEnum.YES.getCode().equals(serviceBox.getBydDirectDealerProperties().getExternalFlag())){
                sb.append("外部审核校验不通过").append("\r\n");
            }
            if(!com.ruicar.afs.cloud.afscase.channel.enums.WhetherEnum.YES.getCode().equals(serviceBox.getBydDirectDealerProperties().getOcrFlag())){
                sb.append("OCR审核校验不通过").append("\r\n");
            }
        }

        CaseBackToPartnersInfo info = new CaseBackToPartnersInfo();
        info.setApplyNo(applyNo);
        info.setContractNo(caseContractInfo.getContractNo());
        info.setBackTime(new Date());
        info.setTaskId(123456L);
        info.setBackDesc(sb.toString());
        List<CaseBackToPartnersInfo> infoList = new ArrayList<>();
        infoList.add(info);

        if (caseContractInfo.getBackTimes() == null) {
            caseContractInfo.setBackTimes(1);
        } else {
            caseContractInfo.setBackTimes(caseContractInfo.getBackTimes()+1);
        }

        serviceBox.getCaseLoanApproveResultService().updateResultVerifyFlagByApplyNo(applyNo, WhetherEnum.NO.getKey());
        serviceBox.getCaseContractInfoService().updateById(caseContractInfo);
        serviceBox.getApproveLoanInfoService().autoBackToApplyNotic(infoList);
    }

    /**
     * 放款自动审核通过后逻辑
     * @param contractInfo 合同信息
     */
    private static void loanAutoApprovedLogic(CaseContractInfo contractInfo,String flag){

        log.info("第{}次放款逻辑提交-弗迪订单...",flag);

        int priorityValue=Integer.valueOf(contractInfo.getPriority());
        if(priorityValue>=Integer.valueOf(PriorityEnum.ORDINARY.getCode())){
            contractInfo.setPriority(PriorityEnum.ORDINARY.getCode());
        }
        //修改放款审核状态（修改为预审批状态）
        contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());//放款待确认
        log.info("*****************" + contractInfo.getContractNo() + "放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_APPROVE.getState() + "*****************");
        contractInfo.setLoanAuditor("admin");
        serviceBox.getCaseContractInfoService().updateById(contractInfo);

        if(AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME).equals(flag)){

            log.warn("放款审核一次直接通过，准备参数。。。");
            LoanApproveSubmitVO submitVO = new LoanApproveSubmitVO();
            submitVO.setContractNo(contractInfo.getContractNo());
            CaseApproveRecord approveRecord = new CaseApproveRecord();
            approveRecord.setApplyNo(contractInfo.getApplyNo());
            submitVO.setApproveRecord(approveRecord);

            // TOP20经销商信息
            boolean topChannelByApplyNo = serviceBox.getThirdDataService().isTopChannelByApplyNo(contractInfo.getApplyNo());
            if(topChannelByApplyNo){
                contractInfo.setIsTopChannel(CaseConstants.YES);
            }else{
                contractInfo.setIsTopChannel(CaseConstants.NO);
            }

            // 抵押要求信息
            log.warn("抵押要求，是否先放后抵={}",JSON.toJSONString(contractInfo));
            if(AfsEnumUtil.key(LoanModelEnum.afterMortgage).equals(contractInfo.getLendingMode()) && CaseConstants.YES.equals(contractInfo.getIsTopChannel())){
                // 免抵
                contractInfo.setMortgageClaim(CaseConstants.YES);
            } else {
                contractInfo.setMortgageClaim(CaseConstants.NO);
            }

            String businessType = contractInfo.getBusinessType();
            // 查询渠道编码
            CaseChannelInfo caseChannelInfo = serviceBox.getCaseChannelInfoService().getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, contractInfo.getApplyNo()));
            // 做非空校验
            if(null != caseChannelInfo && null != caseChannelInfo.getDealerNo() && !"".equals(caseChannelInfo.getDealerNo())) {
                // 查询渠道ID
                ChannelBaseInfo channelBaseInfo =
                        serviceBox.getChannelBaseInfoService().getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
                // 进行非空校验
                if(null != channelBaseInfo && null != channelBaseInfo.getChannelId() && !"".equals(channelBaseInfo.getChannelId())) {
                    List<ChannelRiskInfo> tChannelRiskInfoList = serviceBox.getChannelRiskInfoService().list(Wrappers.<ChannelRiskInfo>query().lambda()
                            .eq(ChannelRiskInfo::getChannelId, channelBaseInfo.getChannelId())
                            .eq(ChannelRiskInfo::getBusinessType, businessType));

                    // 查询是否先放后抵
                    if(ObjectUtils.isNotEmpty(tChannelRiskInfoList) && tChannelRiskInfoList.size() > 0) {
                        // 如果满足条件，则保存免抵
                        if(CaseConstants.YES.equals(contractInfo.getIsTopChannel()) &&  tChannelRiskInfoList.get(0).getIsMortgage().equals(CaseConstants.YES)){
                            contractInfo.setMortgageClaim(CaseConstants.YES);
                        }
                    }
                }
            }

            // 更新状态 & 通知进件
            log.warn("放款审核一次直接通过，开始更新状态并且通知进件。。。");
            serviceBox.getLoanApproveService().newReviewSubmit(submitVO,contractInfo);
            //草稿状态改为合格状态
            log.warn("放款审核一次直接通过，将草稿状态修改为合格状态。。。");
            serviceBox.getComAttachmentFileService().updateFileStatusByBusiNo(contractInfo.getContractNo());
            //modify by likang 插入挂靠实时数据
            log.warn("放款审核一次直接通过，插入挂靠实时数据。。。");
            serviceBox.getCaseChannelInfoService().insertAffiliatedRealtimeData(contractInfo.getApplyNo(), RealTimeDataTypeEnum.CONTRACT.getIndex());

            // 激活合同
            ThreadUtil.execute(()->{
                try {
                    /**等待0.5秒执行*/
                    TimeUnit.MILLISECONDS.sleep(500);
                    serviceBox.getLoanApproveService().doApproveDone(submitVO.getContractNo());
                }catch (Throwable e){
                    log.error("复审通过后置事件完成,运行结果",e);
                }
            });

            // 锁定合同目录
            try{
                serviceBox.getApplyServiceFeign().saveLockInfoByApplyNo(contractInfo.getApplyNo(),CaseConstants.MESSAGE_TYPE_TWO);
            } catch (Exception e){
                log.error("保存锁定合同目录信息失败！");
            }

            //自动放款审核通过发送短信
            serviceBox.getLoanAutoApproveSaveService().sendMessage(contractInfo.getApplyNo());
        }

    }

    /**
     * 放款自动审核通过后进入等待状态
     * @param contractInfo 合同信息
     */
    private static void loanAutoApprovedLogicAwait(CaseContractInfo contractInfo,String flag){

        log.info("第{}次放款逻辑提交-资方订单...",flag);

        if(AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME).equals(flag)){

            // 查询数据库中是否存在
            List<String> awaitStatusList = new ArrayList<>();
            awaitStatusList.add(CaseConstants.ORDER_AWAIT_STATUS_UNKNOWN);
            awaitStatusList.add(CaseConstants.BANK_LOAN_STATUS_AWAIT);
            awaitStatusList.add(CaseConstants.BANK_LOAN_STATUS_PASS);
            List<CaseLoanAwaitInfo> list = serviceBox.getCaseLoanAwaitInfoService().list(Wrappers.<CaseLoanAwaitInfo>lambdaQuery()
                    .eq(CaseLoanAwaitInfo::getContractNo, contractInfo.getContractNo())
                    .in(CaseLoanAwaitInfo::getOrderAwaitStatus, awaitStatusList));

            if(ObjectUtil.isEmpty(list) || list.isEmpty()){

                // 保存等待信息信息
                CaseLoanAwaitInfo caseLoanAwaitInfo = new CaseLoanAwaitInfo();
                caseLoanAwaitInfo.setContractNo(contractInfo.getContractNo());
                caseLoanAwaitInfo.setApplyNo(contractInfo.getApplyNo());
                caseLoanAwaitInfo.setLoanTimes(flag);
                caseLoanAwaitInfo.setAwaitType(CaseConstants.AWAIT_TYPE_AUTO);
                caseLoanAwaitInfo.setOrderAwaitStatus(CaseConstants.ORDER_AWAIT_STATUS_UNKNOWN);
                serviceBox.getCaseLoanAwaitInfoService().save(caseLoanAwaitInfo);

                CaseApproveRecord record = new CaseApproveRecord();
                record.setApplyNo(contractInfo.getApplyNo());
                record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                record.setApproveSuggest(FlowTaskOperationEnum.AUTOLOANAPPROVED.getCode());
                record.setApproveSuggestName(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
                record.setApproveEndTime(new Date());
                record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                record.setApproveRemark(FlowTaskOperationEnum.AUTOLOANAPPROVED.getDesc());
                record.setDisposeNodeName("放款自动审核");
                record.setDisposeStaff("系统");
                serviceBox.getCaseApproveRecordService().save(record);

            }

        }

        int priorityValue=Integer.valueOf(contractInfo.getPriority());
        if(priorityValue>=Integer.valueOf(PriorityEnum.ORDINARY.getCode())){
            contractInfo.setPriority(PriorityEnum.ORDINARY.getCode());
        }
        //修改放款审核状态（修改为等待银行端放款状态）
        contractInfo.setApplyStatus(ApplyStatusEnum.WAIT_BANK_STATUS.getState());//等待银行端放款状态
        log.info("*****************" + contractInfo.getContractNo() + "资方 - 放款审核状态change:" + ApplyStatusEnum.WAIT_BANK_STATUS.getState() + "*****************");
        contractInfo.setLoanAuditor("admin");
        serviceBox.getCaseContractInfoService().updateById(contractInfo);

        if(AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME).equals(flag)){
            String result = getOnCheck3();
            if("1".equals(result)){

                // 获取提交状态
                RequestBody requestBody = new RequestBody();
                Req req = new Req();
                Map map = new HashMap(){{
                    put("applyNo", contractInfo.getApplyNo());
                }};
                req.setTxComm(map);
                requestBody.setReq(req);

                // 通过申请编号获取车辆类型和资方类型
                IResponse<VehicleAndCapitalTypeDTO> vehicleTypeEnumByApplyNo = serviceBox.getApplyServiceFeign().getVehicleTypeEnumByApplyNo(contractInfo.getApplyNo());

                if(CaseConstants.CODE_SUCCESS.equals(vehicleTypeEnumByApplyNo.getCode())){

                    VehicleAndCapitalTypeDTO vehicleAndCapitalTypeDTO = vehicleTypeEnumByApplyNo.getData();

                    // 获取当前订单的资方状态
                    IResponse<StatusInfoRespDTO> statusResult = serviceBox.getRentLoansService().processEDITransaction(requestBody, StatusInfoReqDTO.class, StatusInfoRespDTO.class, vehicleAndCapitalTypeDTO.getCapitalEnum(), vehicleAndCapitalTypeDTO.getVehicleTypeEnum());

                    if(CaseConstants.CODE_SUCCESS.equals(statusResult.getCode())){
                        StatusInfoRespDTO data = statusResult.getData();
                        String tResult = data.getStatus();
                        // 如果是银行未放款，处于放款等待中的状态
                        if(AfsEnumUtil.key(CapitalOrderStatusEnum.CONTRACT_SIGNING_COMPLETE).equals(tResult)){

                            // 同步文件
                            // 获取当前订单逻辑
                            RequestBody tRequestBody = new RequestBody();
                            Req tReq = new Req();

                            List<String> uniqueCodeLsit = new ArrayList<>();
                            // 首付款凭证
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_SFKPZ.getFdCode());
                            // 购车合同或协议
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_GCHT.getFdCode());
                            // 汽车融资租赁合同
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_ZLHT.getFdCode());
                            // 车辆保险单
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_BXD.getFdCode());
                            // 购车发票
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_GCFP.getFdCode());
                            // 收入声明
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_SRZM.getFdCode());
                            // 车辆抵押合同
                            uniqueCodeLsit.add(AttachmentCodeEnum.ATT_LOAN_BK_DYHT.getFdCode());

                            List<ComAttachmentManagement> list =
                                    serviceBox.getComAttaManageService().list(Wrappers.<ComAttachmentManagement>lambdaQuery().in(ComAttachmentManagement::getUniqueCode, uniqueCodeLsit));

                            if(ObjectUtil.isNotEmpty(list) && list.size()>0){
                                Map<String,String> idAndUniqueCodeMap = new HashMap<>();

                                List<String> idList = new ArrayList<>();
                                for(ComAttachmentManagement comAttachmentManagement : list){
                                    idList.add(String.valueOf(comAttachmentManagement.getId()));
                                    idAndUniqueCodeMap.put(String.valueOf(comAttachmentManagement.getId()),comAttachmentManagement.getUniqueCode());
                                }

                                if(ObjectUtil.isNotEmpty(idList) && idList.size()>0){

                                    // 初始化
                                    List<ComAttachmentFile> comAttachmentFileList = serviceBox.getComAttachmentFileService().list(Wrappers.<ComAttachmentFile>lambdaQuery()
                                            .eq(ComAttachmentFile::getBusiNo, contractInfo.getContractNo())
                                            .in(ComAttachmentFile::getAttachmentCode, idList));
                                    for (ComAttachmentFile comAttachmentFile : comAttachmentFileList) {
                                        UploadInputDto uploadInputDto = getUploadInputDto(comAttachmentFile);
                                        comAttachmentFile.setAttachmentName(comAttachmentFile.getFileName());
                                        comAttachmentFile.setAttachmentCode(AttachmentCodeEnum.getBankCodeByFdCode(idAndUniqueCodeMap.get(comAttachmentFile.getAttachmentCode())));
                                        String fileFullPath = serviceBox.getDisposeInfoCapitalFileService().uploadFilesByFtp(vehicleAndCapitalTypeDTO.getCapitalEnum(),contractInfo.getApplyNo(), uploadInputDto);
                                        fileFullPath = fileFullPath.substring(0, fileFullPath.lastIndexOf("/"));
                                        comAttachmentFile.setFileName(fileFullPath);
                                    }

                                    CarInvoiceInfo carInvoiceInfo = serviceBox.getCarInvoiceInfoService().getOne(Wrappers.<CarInvoiceInfo>lambdaQuery()
                                            .select(CarInvoiceInfo::getCarVin)
                                            .eq(CarInvoiceInfo::getApplyNo, contractInfo.getApplyNo()));
                                    ChannelReceivableAccountService receivableAccountService = SpringContextHolder.getBean(ChannelReceivableAccountService.class);
                                    ChannelReceivablesAccount receivablesAccount = receivableAccountService.getOne(Wrappers.<ChannelReceivablesAccount>lambdaQuery().eq(ChannelReceivablesAccount::getId, contractInfo.getReceivingId()));

                                    Map tMap = new HashMap(){{
                                        put("applyNo", contractInfo.getApplyNo());
                                        put("carVin", carInvoiceInfo.getCarVin());
                                        put("receiptBank", receivablesAccount.getReceivingBank());
                                        put("receiptAccount",receivablesAccount.getReceivingAccount());
                                        put("receiptName",receivablesAccount.getReceivingName());
                                    }};

                                    tMap.put("comAttachmentFileList",comAttachmentFileList);

                                    if(ObjectUtil.isNotEmpty(comAttachmentFileList) && comAttachmentFileList.size() > 0){

                                        tReq.setTxComm(tMap);
                                        tRequestBody.setReq(tReq);
                                        IResponse<Resp<StatusInfoRespDTO>> iResponse = serviceBox.getRentLoansService().processEDITransaction(tRequestBody, LoanOrderSubmitReqDTO.class, Void.class, vehicleAndCapitalTypeDTO.getCapitalEnum(), vehicleAndCapitalTypeDTO.getVehicleTypeEnum());
                                        log.info("推送订单信息={}", JSON.toJSONString(iResponse));
                                    }
                                }
                            }

                        }else if(CaseConstants.BANK_LOAN_STATUS_AWAIT.equals(tResult)){

                            log.warn("银行资方状态处于等待审核状态，不再进行放款资方信息提交！");

                        }else if(CaseConstants.BANK_LOAN_STATUS_PASS.equals(tResult)){

                            log.warn("银行资方状态处于已放款状态，不再进行放款资方信息提交！");

                        }else if(CaseConstants.BANK_LOAN_STATUS_BACK.equals(tResult)){

                            log.warn("银行资方状态处于放款退回状态，不再进行放款资方信息提交！");

                        }else{
                            log.error("申请编号={}, 错误的资方状态，请联系管理员，状态={}！", contractInfo.getApplyNo(), tResult);
                            throw new AfsBaseException("错误的资方状态，请联系管理员，状态={}！", tResult);
                        }
                    } else {
                        log.error("申请编号={}, 银行资方状态获取失败，无法判断是否进行资方信息提交操作！", contractInfo.getApplyNo());
                        throw new AfsBaseException("银行资方状态获取失败，无法判断是否进行资方信息提交操作！");
                    }

                } else {
                    log.error("申请编号={}, 通过申请编号获取车辆类型和资方类型接口调用失败！", contractInfo.getApplyNo());
                    throw new AfsBaseException("通过申请编号获取车辆类型和资方类型接口调用失败！");
                }
            }
        }
    }

    /**
     * 获取推送订单信息 - 文件 - ftp推送
     * @param attachmentFile
     * @return
     */
    public UploadInputDto getUploadInputDto(ComAttachmentFile attachmentFile) {
        UploadInputDto uploadInputDto = new UploadInputDto();
        uploadInputDto.setAttachmentId(String.valueOf(attachmentFile.getId()));
        uploadInputDto.setAttachmentName(attachmentFile.getAttachmentName());
        uploadInputDto.setFileType(attachmentFile.getFileType());
        uploadInputDto.setFileName(attachmentFile.getFileName());
        uploadInputDto.setBusiNo(attachmentFile.getBusiNo());
        uploadInputDto.setBelongNo(attachmentFile.getBelongNo());
        uploadInputDto.setFileMd5(attachmentFile.getFileId());
        uploadInputDto.setIsElectronic(attachmentFile.getIsElectronic());
        uploadInputDto.setAttachmentCode(attachmentFile.getAttachmentCode());
        uploadInputDto.setFileSource(attachmentFile.getFileSource());
        uploadInputDto.setRemake(attachmentFile.getRemake());
        return uploadInputDto;
    }

    /**
     * 查询挡板信息
     * @return true - 表示挡板已关闭，审核订单可能会自动通过，false - 表示挡板已开启，审核订单只会走人工
     */
    private static String getOnCheck() {
        JSONObject queryVo = new JSONObject();
        queryVo.put("paramNo","loanAutoApproveRisk");
        queryVo.put("magicNo","loanAutoApproveSwitch");
        IResponse<String> paramValue = serviceBox.getTsysParamConfigFeign().getParamValue(queryVo);
        log.info("获取到的进件申请参数开关={}",paramValue);
        String val = Optional.ofNullable(paramValue).filter(iRes -> "0000".equals(iRes.getCode())).map(IResponse::getData).orElse("1");
        return val;
    }

    /**
     * 自动退回规则挡板，默认0-走自动退回规则，1-不走自动退回规则
     * @return true - 表示挡板已关闭，审核订单可能会自动通过，false - 表示挡板已开启，审核订单只会走人工
     */
    private static String autoBackRuleSwitch() {
        JSONObject queryVo = new JSONObject();
        queryVo.put("paramNo","autoBackRuleSwitch");
        queryVo.put("magicNo","autoBackRuleSwitch");
        IResponse<String> paramValue = serviceBox.getTsysParamConfigFeign().getParamValue(queryVo);
        log.info("自动退回规则挡板={}",JSON.toJSONString(paramValue));
        String val = Optional.ofNullable(paramValue).filter(iRes -> "0000".equals(iRes.getCode())).map(IResponse::getData).orElse("1");
        return val;
    }

    /**
     * 放款审核流程调用决策引擎结果
     * @param applyNo
     * @param contractInfo
     * @return true - 表示决策通过，走自动放款规则，false - 表示决策未通过，走人工审核
     */
    private boolean loanDecisionResult(String applyNo, CaseContractInfo contractInfo) {
        //默认情况下流程下一步转人工，后面根据决策引擎结果进行更改
        boolean result = false;
        try {
            log.info("-------开始放款决策引擎判断-------" + applyNo);
            CaseBaseInfo caseBaseInfo = serviceBox.getCaseBaseInfoService().getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));

            //获取信审决策引擎结果
            String creditApproveResult = null;
            IResponse<JSONObject> response = serviceBox.getThirdDataService().queryThirdDataByApplyNo(applyNo);
            if (response.getData() != null) {
                List<DecisionResult> decisionResultList = (List<DecisionResult>) response.getData().get("decisionResult");
                if (CollectionUtils.isNotEmpty(decisionResultList)) {
                    creditApproveResult = decisionResultList.get(0).getFinalDealTypeName();
                    log.info("信审决策结果={}", creditApproveResult);
                }
            }

            //如果信审决策结果为"通过"，才走放款决策引擎
            if (AfsEnumUtil.desc(RiskStatus.RISK_RES_ACCEPT).equals(creditApproveResult)) {
                //添加决策引擎风控
                String finalDealTypeCode = "";// 决策结果
                //决策错误原因
                StringBuilder riskErrorMsg = new StringBuilder("");
                //1.根据applyNo调用apply服务的apply_cust_base_info表，获取到进件申请中用户和担保人的信息
                JSONObject riskBody = serviceBox.getDecisionEngineLoanService().getAndSetParams(applyNo);
                //调用决策引擎接口进行放款风险验证
                IResponse<JSONObject> iResponse = serviceBox.getDecisionEngineService().DecisionEngine(riskBody, TencentControlRequestPath.LOAN_EXAMINATION);
                log.info("调用决策引擎结果：{}", iResponse);
                //获取决策状态
                if (Objects.equals("0000", iResponse.getCode())) {
                    finalDealTypeCode = Optional.ofNullable(iResponse.getData()).map(json -> json.getString("finalDealTypeCode")).orElse("");//对应状态类RiskStatus
                    JSONArray codeList = Optional.ofNullable(iResponse.getData()).map(data -> data.getJSONObject("decisionDetailedResults")).map(res -> res.getJSONArray("ruleCustomIds")).orElse(new JSONArray());
                    log.info("决策结果={}命中的规则={}", finalDealTypeCode, codeList.toJSONString());
                    for (Object o : codeList) {
                        if (o != null) {
                            log.info("错误编码={}", o);
                            Enum anEnum = AfsEnumUtil.getEnum(o.toString(), RiskErrorEnum.class);
                            if (anEnum != null) {
                                String desc = AfsEnumUtil.desc(anEnum);
                                log.info("错误描述信息={}", desc);
                                if (StrUtil.isNotBlank(desc) && riskErrorMsg.indexOf(desc) == -1) {
                                    riskErrorMsg.append(desc).append(",");
                                }
                            }
                        }
                    }
                    log.info("最终的错误信息={}", riskErrorMsg);
                }else {
                    log.info("编号为{}的订单放款决策引擎无结果返回", applyNo);
                    //无决策结果返回时按决策未命中规则处理
                    finalDealTypeCode = RiskStatus.RISK_RES_ACCEPT.key();
                }
                //保存正审数据，查询决策引擎报告会使用
                serviceBox.getDecisionEngineLoanService().saveThirdData(applyNo, iResponse, riskBody);

                Map<RiskStatus, NormalSubmitType> decidedResultTranslateMap = new HashMap<>();
                decidedResultTranslateMap.put(RiskStatus.RISK_RES_ACCEPT, NormalSubmitType.DECISION_PASSED);
                decidedResultTranslateMap.put(RiskStatus.RISK_RES_REVIEW, NormalSubmitType.ARTIFICIAL);

                RiskStatus riskStatus = (RiskStatus) AfsEnumUtil.getEnum(finalDealTypeCode, RiskStatus.class);
                NormalSubmitType finalType = decidedResultTranslateMap
                        .getOrDefault(riskStatus, NormalSubmitType.DECISION_PASSED);

                CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                if (finalType == NormalSubmitType.DECISION_PASSED) {
                    //自动通过时，添加审批日志
                    caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.DECISION_PASSED));
                    caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.DECISION_PASSED));
                    caseApproveRecord.setApproveRemark("决策通过");
                    //未命中规则，走自动放款规则
                    result = true;
                } else {
                    caseApproveRecord.setApproveSuggest(AfsEnumUtil.key(NormalSubmitType.ARTIFICIAL));
                    caseApproveRecord.setApproveSuggestName(AfsEnumUtil.desc(NormalSubmitType.ARTIFICIAL));
                    caseApproveRecord.setApproveRemark(String.format("决策转人工，决策输出结果：%s-%s",
                            finalDealTypeCode, riskStatus != null ? AfsEnumUtil.desc(riskStatus) : "无"));
                    //打人工电核标签和资料后置标签
                    autoLabel(contractInfo.getContractNo(), caseBaseInfo.getApplyNo());
                    //命中规则，转人工
                    result = false;
                }
                caseApproveRecord.setApplyNo(caseBaseInfo.getApplyNo());
                caseApproveRecord.setApproveEndTime(new Date());
                caseApproveRecord.setApproveType(ApproveTypeEnum.PROCESS.getValue());
                caseApproveRecord.setDisposeStaff("系统");
                caseApproveRecord.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
                serviceBox.getCaseApproveRecordService().save(caseApproveRecord);
                log.info("决策自动审批状态{},审批状态{}", caseBaseInfo, AfsEnumUtil.key(finalType));
            } else {
                //非系统核准订单走自动放款规则
                result = true;
            }
        }catch (Exception e) {
            log.error("申请编号={}的订单，放款决策引擎校验过程发生错误，错误原因：{}", applyNo, e.getMessage(), e);
            result = false;
        }
        //获取放款决策引擎开关
        if (!getLoanDecisionOnCheck()) {
            //挡板开启时，无论决策结果如何，走自动放款规则
            result = true;
        }
        return result;
    }

    /**
     * 查询放款决策引擎挡板信息
     * @return true - 表示挡板已关闭，审核订单安装正常流程执行，false - 表示挡板已开启，审核订单无论决策结果如何都进入自动放款规则
     */
    private static boolean getLoanDecisionOnCheck() {
        JSONObject queryVo = new JSONObject();
        queryVo.put("paramNo","loanDecisionEngine");
        queryVo.put("magicNo","loanDecisionEngineSwitch");
        IResponse<String> paramValue = serviceBox.getTsysParamConfigFeign().getParamValue(queryVo);
        log.info("获取到的进件申请参数开关={}",paramValue);
        String val = Optional.ofNullable(paramValue).filter(iRes -> "0000".equals(iRes.getCode())).map(IResponse::getData).orElse("1");
        return Objects.equals("0",val);
    }

    /**
     * 打"人工电核"标签
     * @param contractNo
     * @param applyNo
     */
    private void autoLabel(String contractNo, String applyNo){
        CaseContractInfo caseContractInfo= serviceBox.getCaseContractInfoService().getOne(Wrappers.<CaseContractInfo>query()
                .lambda().eq(CaseContractInfo::getContractNo,contractNo));
        JSONObject params = new JSONObject();
        //设置规则参数
        params.put("hasAutoPass", AfsEnumUtil.key(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO));
        // 获取TOP20经销商原子
        if(serviceBox.getThirdDataService().isTopChannelByApplyNo(applyNo)){
            params.put("isTopChannel",CaseConstants.YES);
        }else{
            params.put("isTopChannel",CaseConstants.NO);
        }
        log.warn("自动标签原子设置入参={},结果={}",applyNo,JSON.toJSONString(params));
        List<ManualLabel> manualLabelList = serviceBox.getManualLabelService().list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getStatus, LabelStatusEnum.TAKE_EFFECT.getCode())
                .eq(ManualLabel::getLabel, LabelWayEnum.AUTOMATIC.getCode())
                .eq(ManualLabel::getLabelPhase, LabelPhaseEnum.LOANS.getCode())
                .isNotNull(ManualLabel::getRuleId)
        );
        for (ManualLabel label : manualLabelList) {
            boolean RuleRunResult;
            try {
                RuleRunResult = RuleHelper.runRule(params, label.getId().toString()).getHit();
            } catch (Exception e) {
                RuleRunResult = false;
                log.info("调用规则失败异常原因：{}", e.getMessage());
            }
            if (RuleRunResult) {
                log.info("------------------自动标签规则已命中编号:" + label.getId().toString() + "--------------------");
                LoanLabelInfo loanLabelInfo = new LoanLabelInfo();
                ManualLabel manualLabel = serviceBox.getManualLabelService().queryAllLabelListById(label.getId());
                //验证是否已存在人工电核标签
                List<LoanLabelInfo> loanLabelInfos = serviceBox.getLoanLabelInfoService().list(Wrappers.<LoanLabelInfo>lambdaQuery()
                        .eq(LoanLabelInfo::getApplyNo,applyNo)
                        .eq(LoanLabelInfo::getLabelId, manualLabel.getId())
                        .eq(LoanLabelInfo::getDelFlag, 0)
                );
                loanLabelInfo.setApplyNo(applyNo);
                loanLabelInfo.setContractNo(contractNo);
                loanLabelInfo.setLabelId(manualLabel.getId());
                loanLabelInfo.setLabelName(manualLabel.getLabelName());
                loanLabelInfo.setLabelPhase(manualLabel.getLabelPhase());
                loanLabelInfo.setLabelColor(manualLabel.getLabelColor());
                loanLabelInfo.setLabelType(manualLabel.getLabelType());
                loanLabelInfo.setLabelLocation(manualLabel.getLabelLocation());
                loanLabelInfo.setLabelSort(manualLabel.getLabelSort());
                loanLabelInfo.setLabelPattern(manualLabel.getLabel());
                loanLabelInfo.setLabelClassification(manualLabel.getLabelClassification());
                loanLabelInfo.setIsArchive(manualLabel.getIsArchive());
                if (CollectionUtils.isEmpty(loanLabelInfos)) {
                    serviceBox.getLoanLabelInfoService().save(loanLabelInfo);
                }else {
                    loanLabelInfo.setId(loanLabelInfos.get(0).getId());
                    serviceBox.getLoanLabelInfoService().updateById(loanLabelInfo);
                }
                //处理原因:涉及到自动标签、手动标签共同查询
                String labelIdRepeat = caseContractInfo.getLabelIdRepeat();
                String[] oldStr = null;
                if (StringUtils.isNotBlank(labelIdRepeat)) {
                    oldStr = labelIdRepeat.split(",");
                }
                String newStr = "";
                if (oldStr != null && oldStr.length > 0) {
                    for (int i = 0; i < oldStr.length; i++) {
                        newStr += oldStr[i] + ",";
                    }
                }
                newStr += manualLabel.getId() + ",";
                caseContractInfo.setLabelIdRepeat(newStr);
                serviceBox.getCaseContractInfoService().updateById(caseContractInfo);
            }
        }
    }

    public StepInvoker overdue(){
        return (stepParam, currentStep) -> {
            try {
                if(serviceBox.getCaseConfParamService().checkSwitch(Const.ARCHIVE_OVERDUE_SWITCH)) {
                        Assert.isTrue(null != stepParam && null != stepParam.getContractInfo(), ">>>>>执行超期异常步骤异常...");
                        return serviceBox.getArchiveApiService().setOverdue(stepParam.getContractInfo());
                }else {
                    log.info("归档超期开关未启用，跳过判断！");
                }
            } catch (Exception e) {
                log.error("合同：{}=>超期判断ERROR...", stepParam.getContractNo(), e);
            }finally {
                log.info("合同{}超期判断执行完成",stepParam.getContractNo());
                stepParam.getContractInfo().setWorkflowPrevStep(currentStep);
                serviceBox.getCaseContractInfoService().updateById(stepParam.getContractInfo());
            }
            return true;
        };
    }

    /**
     * @Description 置顶放款
     * <AUTHOR>
     * @Date 2020/7/30
     */
    private static StepInvoker setUpTopLoan() {
        return (stepParam, currentStep) -> {
            CaseContractInfo contractInfo = stepParam.getContractInfo();
            CaseContractInfo contractInfoNew = serviceBox.getCaseContractInfoService().getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getApplyNo, contractInfo.getApplyNo()));
            try {
                CaseBaseInfo baseInfo = serviceBox.getCaseBaseInfoService().getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, contractInfoNew.getApplyNo()));
                JSONObject jsonObject = new JSONObject();
                /**是否秒贷*/
                jsonObject.put("isFastLoan", StrUtil.isBlank(contractInfo.getIsFastLoan())?"no":contractInfo.getIsFastLoan());
                /**业务类型*/
                jsonObject.put("businessType", baseInfo.getBusinessType());
                /**车辆类型*/
                jsonObject.put("carType", baseInfo.getCarType());
                /**车辆属性*/
                jsonObject.put("carNature", baseInfo.getCarNature());
                /**运营方式*/
                jsonObject.put("operateWay", baseInfo.getOperateWay());
                /**挂靠方式*/
                jsonObject.put("affiliatedWay", baseInfo.getAffiliatedWay());
                /**贷款总额*/
                jsonObject.put("loanAmount", baseInfo.getLoanAmtRepeat());
                /**金融产品*/
                jsonObject.put("productId", String.valueOf(baseInfo.getProductId()));
                CaseChannelInfo caseChannelInfo = serviceBox.getCaseChannelInfoService().getOne(Wrappers.<CaseChannelInfo>query().lambda().eq(CaseChannelInfo::getApplyNo, contractInfoNew.getApplyNo()));
                if (ObjectUtil.isNotNull(caseChannelInfo)) {
                    /**经销商名称*/
                    jsonObject.put("dealerName", caseChannelInfo.getDealerName());
                    ChannelBaseInfo channelBaseInfo = serviceBox.getChannelBaseInfoService().getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
                    if (ObjectUtil.isNotNull(channelBaseInfo)) {
                        /**经销商省份*/
                        jsonObject.put("channelProvince", channelBaseInfo.getChannelProvince());
                        /**经销商城市*/
                        jsonObject.put("channelCity", channelBaseInfo.getChannelCity());
                        /**渠道归属*/
                        jsonObject.put("channelBelong", channelBaseInfo.getChannelBelong());
                        List<ChannelAuthorizeRegion> list = serviceBox.getRegionService().list(Wrappers.<ChannelAuthorizeRegion>query().lambda().eq(ChannelAuthorizeRegion::getChannelId, channelBaseInfo.getChannelId()));
                        if (list != null && !list.isEmpty()) {
                            StringBuilder practicesProvince = new StringBuilder();
                            StringBuilder practicesCity = new StringBuilder();
                            for (ChannelAuthorizeRegion region : list) {
                                if (IsAssignedEnum.YES.getCode().equals(region.getParentId())
                                        && IsAssignedEnum.YES.getCode().equals(region.getIsParent())) {
                                    practicesProvince.append(region.getCode()).append(",");
                                }
                                if (IsAssignedEnum.NO.getCode().equals(region.getIsParent())) {
                                    practicesCity.append(region.getCode())
                                            .append(",");
                                }
                            }
                            /** 展业省份 */
                            jsonObject.put("practicesProvince",
                                    !practicesProvince.isEmpty()?practicesProvince.substring(0, practicesProvince.length() - 1):"");
                            /** 展业城市 */
                            jsonObject.put("practicesCity", !practicesCity.isEmpty()?practicesCity.substring(0, practicesCity.length() - 1):"");
                        }
                    }
                }
                CaseCarInfo caseCarInfo = serviceBox.getCaseCarInfoService().getOne(Wrappers.<CaseCarInfo>query().lambda().eq(CaseCarInfo::getApplyNo, contractInfoNew.getApplyNo()));
                if (ObjectUtil.isNotNull(caseCarInfo)) {
                    /**车辆级别*/
                    jsonObject.put("carBodyClass", caseCarInfo.getCarBodyClass());
                    /**车型*/
                    jsonObject.put("modelCode", caseCarInfo.getModelCode());
                    /**购车地省份*/
                    jsonObject.put("purchaseProvince", caseCarInfo.getPurchaseCity());
                    /**购车地城市*/
                    jsonObject.put("purchaseCity", caseCarInfo.getPurchaseCity());
                    /**上牌地省份*/
                    jsonObject.put("licenseProvince", caseCarInfo.getLicenseProvince());
                    /**上牌地城市*/
                    jsonObject.put("licenseCity", caseCarInfo.getLicenseCity());
                }
                /**是否网约车*/
                if (StringUtil.isNotEmpty(baseInfo.getAffiliatedWay()) && AffiliatedWayEnum.NETWORK_CAR_AFFILIATED.getCode().equals(baseInfo.getAffiliatedWay())) {
                    jsonObject.put("onlineCar", WhetherEnum.YES.getCode());
                } else {
                    jsonObject.put("onlineCar", WhetherEnum.NO.getCode());
                }
                /**是否归档超期/是否暂停放款*/
                if(WhetherEnum.YES.getCode().equals(contractInfoNew.getIsOverdue())){
                    jsonObject.put("isOverdue", WhetherEnum.YES.getCode());
                }else {
                    jsonObject.put("isOverdue", WhetherEnum.NO.getCode());
                }
                if(WhetherEnum.YES.getCode().equals(contractInfoNew.getIsLock())){
                    jsonObject.put("isLock", WhetherEnum.YES.getCode());
                }else {
                    jsonObject.put("isLock", WhetherEnum.NO.getCode());
                }
                log.info("分单前放款置顶规则jsonObject: {}", jsonObject);
                //调用置顶规则
                RuleRunResult result = RuleHelper.runRule(jsonObject, "loanTop", true, RuleRunEnum.SERIAL);
                log.info("分单前放款置顶规则result: {}", result);
                if(result.getHit()){
                    contractInfo.setIsTop(TopEnum.YES.getCode());
                    contractInfo.setTopTime(DateUtil.date());
                }
                serviceBox.getCaseContractInfoService().updateById(contractInfo);
            } catch (Exception e) {
                log.error("合同{}:置顶放款执行失败.", e);
            }finally {
                contractInfo.setWorkflowPrevStep(currentStep);
                serviceBox.getCaseContractInfoService().updateById(contractInfo);
            }
            return true;
        };
    }

    private String getOnCheck3() {
        JSONObject queryVo = new JSONObject();
        queryVo.put("paramNo","loanAutoApproveRisk3");
        queryVo.put("magicNo","loanAutoApproveSwitch3");
        IResponse<String> paramValue = serviceBox.getTsysParamConfigFeign().getParamValue(queryVo);
        log.info("获取到的进件申请参数开关={}",paramValue);
        String val = Optional.ofNullable(paramValue).filter(iRes -> "0000".equals(iRes.getCode())).map(IResponse::getData).orElse("1");
        return val;
    }
}
