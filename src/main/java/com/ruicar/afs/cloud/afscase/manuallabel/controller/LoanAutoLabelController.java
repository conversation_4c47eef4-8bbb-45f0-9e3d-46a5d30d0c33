package com.ruicar.afs.cloud.afscase.manuallabel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.manuallabel.condition.ManualLabelCondition;
import com.ruicar.afs.cloud.afscase.manuallabel.entity.ManualLabel;
import com.ruicar.afs.cloud.afscase.manuallabel.service.ManualLabelService;
import com.ruicar.afs.cloud.common.core.log.annotation.SysLog;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelStatusEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 放款自动标签库管理功能
 * @created 2020/10/21 11:38
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("放款自动标签管理功能接口")
@RequestMapping("/loanAutoLabel")

public class LoanAutoLabelController {

    private ManualLabelService manualLabelService;
    private AfsRuleFeign afsRuleInfoService;

    @PostMapping(value = "/queryLoanLabelList")
    @ApiOperation(value = "多条件分页获取放款自动标签库信息数据")
    public IResponse<IPage<ManualLabel>> queryLoanLabelList(@RequestBody QueryCondition<ManualLabelCondition> loanAutoLabel) {
        return IResponse.success(manualLabelService.page(new Page(loanAutoLabel.getPageNumber(), loanAutoLabel.getPageSize()), Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelPhase,LabelPhaseEnum.LOANS.getCode())
                .like(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getLabelName()), ManualLabel::getLabelName, loanAutoLabel.getCondition().getLabelName())
                .eq(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getLabelType()), ManualLabel::getLabelType, loanAutoLabel.getCondition().getLabelType())
                .eq(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getLabelLocation()), ManualLabel::getLabelLocation, loanAutoLabel.getCondition().getLabelLocation())
                .eq(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getLabelClassification()), ManualLabel::getLabelClassification, loanAutoLabel.getCondition().getLabelClassification())
                .eq(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getStatus()), ManualLabel::getStatus, loanAutoLabel.getCondition().getStatus())
                .eq(StringUtils.isNotEmpty(loanAutoLabel.getCondition().getLabel()), ManualLabel::getLabel, loanAutoLabel.getCondition().getLabel())
                .orderByAsc(ManualLabel::getLabelSort).orderByAsc(ManualLabel::getLabelNo)
        ));
    }

    @RequestMapping(value = "/deleteLabel/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "通过id删除")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("删除标签库")
    public IResponse<Boolean> deleteLabel(@PathVariable String id) {
        ManualLabel manualLabel = manualLabelService.getById(id);
        if (manualLabel == null) {
            return new IResponse<Boolean>().setMsg("通过id删除数据成功");
        }
        manualLabelService.removeById(id);
        return new IResponse<Boolean>().setMsg("通过id删除数据成功");
    }

    @PostMapping(value = "/saveOrUpdateLoanLabel")
    @ApiOperation(value = "新增或者更新放款标签库")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("新增或者更新放款标签库")
    public IResponse saveOrUpdateLoanLabel(@RequestBody ManualLabel loanLabel) {
        String labelNo = loanLabel.getLabelNo();
        String labelName = loanLabel.getLabelName();
        List<ManualLabel> listName = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelName, labelName)
                .eq(ManualLabel::getLabelPhase,LabelPhaseEnum.LOANS.getCode()).ne(loanLabel.getId()!=null,ManualLabel::getId,loanLabel.getId()));
        List<ManualLabel> listNo = manualLabelService.list(Wrappers.<ManualLabel>query().lambda()
                .eq(ManualLabel::getLabelNo, labelNo)
                .eq(ManualLabel::getLabelPhase,LabelPhaseEnum.LOANS.getCode()).ne(loanLabel.getId()!=null,ManualLabel::getId,loanLabel.getId()));
        if (CollectionUtils.isNotEmpty(listName)) {
            return IResponse.fail("标签名称:" + labelName + "重复,请重新提交！");
        } else if (CollectionUtils.isNotEmpty(listNo)) {
            return IResponse.fail("标签编号:" + labelNo + "重复,请重新提交！");
        } else {
            if(loanLabel.getId()!=null){
                manualLabelService.updateById(loanLabel);
                if(loanLabel.getRuleId()==null) {
                    manualLabelService.checkLabelRule(loanLabel);
                }
            }else{
                /**新增默认失效*/
                loanLabel.setStatus(LabelStatusEnum.LOSE_EFFICACY.getCode());
                manualLabelService.save(loanLabel);
            }
            return IResponse.success("保存成功");
        }
    }

    @PostMapping(value = "/saveLabelRuleId")
    @ApiOperation(value = "保存规则id")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("保存规则id")
    public IResponse<Boolean> saveLabelRuleId(@RequestParam("id") String id,@RequestParam("ruleId") String ruleId) {
        ManualLabel lableInfo = manualLabelService.getById(Long.valueOf(id));
        if(ObjectUtils.isNotEmpty(lableInfo)){
            lableInfo.setRuleId(Long.valueOf(ruleId));
            manualLabelService.updateById(lableInfo);
            return IResponse.success("更新成功");
        }else {
            return IResponse.fail("未找到相应标签："+id);
        }
    }

    @PostMapping(value = "/activateOrExpiredTheRules/{flag}")
    @ApiOperation(value = "激活/失效")
    @Transactional(rollbackFor = Exception.class)
    @SysLog("激活/失效规则")
    public IResponse<Boolean> activateOrExpiredTheRules(@RequestBody ManualLabel manualLabel, @PathVariable String flag) {
        if (StringUtils.isNotEmpty(flag) && !"null".equals(String.valueOf(manualLabel.getRuleId()))) {
            if ("activate".equals(flag)) {//激活规则
                afsRuleInfoService.activeRuleByRuleId(manualLabel.getRuleId());
            } else {                      //失效规则
                afsRuleInfoService.deActiveRule(manualLabel.getRuleId());
                afsRuleInfoService.expiredRule(manualLabel.getRuleId());
            }
        }
        manualLabelService.updateById(manualLabel);
        return new IResponse<Boolean>().setMsg("操作成功");

    }

    /**
     * 失效规则
     *
     * @param ruleId
     * @return
     */
    @PostMapping("/deActiveRuleById/{ruleId}")
    public IResponse deActiveRuleById(@PathVariable("ruleId") String ruleId) {
        //先反激活再失效
        afsRuleInfoService.deActiveRuleByRuleId(Long.valueOf(ruleId));
        return IResponse.success("失效成功");
    }
}
