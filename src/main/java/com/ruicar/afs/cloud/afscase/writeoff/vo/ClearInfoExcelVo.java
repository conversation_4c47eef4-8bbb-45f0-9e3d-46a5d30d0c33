package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 服务费清账数据ExcelVo
 */
@Data
public class ClearInfoExcelVo implements Serializable {

    /**
     * 核销项编号
     */
    @ExcelProperty(value = "核销项编号")
    private String applyNo;
    /**
     * 核销项账期
     */
    @ExcelProperty(value = "核销项月份")
    private String writeOffMonth;
    /**
     * 业务模式
     */
    @ExcelProperty(value = "业务模式")
    private String writeOffType;
    /**
     * 经销商名称
     */
    @ExcelProperty(value = "经销商名称")
    private String channelFullName;
    /**
     * 总服务费
     */
    @ExcelProperty(value = "总服务费")
    private BigDecimal serviceCharge;
    /**
     * 暂估凭证号
     */
    @ExcelProperty(value = "暂估凭证号")
    private String feeVoucherNo;
    /**
     * 暂估转应付凭证号
     */
    @ExcelProperty(value = "暂估转应付凭证号")
    private String invoiceVoucherNo;
    /**
     * 付款凭证号
     */
    @ExcelProperty(value = "付款凭证号")
    private String payVoucherNo;
    /**
     * 尾差凭证号
     */
    @ExcelProperty(value = "尾差凭证号")
    private String tailVoucherNo;
    /**
     * 暂估清账状态
     */
    @ExcelProperty(value = "暂估清账状态")
    private String feeClearStatus;
    /**
     * 应付清账状态
     */
    @ExcelProperty(value = "应付清账状态")
    private String payClearStatus;
}
