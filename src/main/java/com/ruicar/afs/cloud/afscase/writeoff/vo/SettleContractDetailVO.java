package com.ruicar.afs.cloud.afscase.writeoff.vo;

import lombok.Data;

import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提前结清数据vo
 */
@Data
public class SettleContractDetailVO {

    /**
     * 扣减比例
     */
    private Integer deductRate;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 结清日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleDate;

    /**
     * 提单日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date intoFirstDate;

    /**
     * 放款日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loanDate;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品num
     */
    private String productNum;

    /**
     * 产品分组
     */
    private String productGroup;

    /**
     * 经销商名称
     */
    private String channelName;

    /**
     * 还款计划表期数
     */
    private Integer termNo;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 所属资方
     */
    private String belongingCapital;
}
