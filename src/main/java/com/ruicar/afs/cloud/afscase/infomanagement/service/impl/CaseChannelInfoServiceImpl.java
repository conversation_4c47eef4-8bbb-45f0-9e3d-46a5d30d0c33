package com.ruicar.afs.cloud.afscase.infomanagement.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelInfoModifyVO;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseChannelInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.channel.dto.AffiliatedRealtimeDataDto;
import com.ruicar.afs.cloud.channel.feign.ChannelApiFeign;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-13 15:20
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class CaseChannelInfoServiceImpl extends ServiceImpl<CaseChannelInfoMapper, CaseChannelInfo> implements CaseChannelInfoService {
    private final ApplyContractFeign applyContractFeign;
    private CaseCostInfoService costInfoService;
    private CaseCarInfoService carInfoService;
    private ApplyConfig applyConfig;
    private ChannelApiFeign channelApiFeign;
    private CaseBaseInfoService caseBaseInfoService;
    private ChannelRiskInfoService channelRiskInfoService;

    /**
     * 通过申请编号查询渠道主表
     */
    @Override
    public ChannelBaseInfo getChannelBaseInfo(String applyNo){
        return this.baseMapper.getChannelBaseInfo(applyNo);
    }

    /**
     * 通过申请编号查询渠道主表
     */
    @Override
    public List<ChannelBaseInfo> getChannelBaseInfo(List<String> applyNoList){
        return this.baseMapper.getChannelBaseInfoList(applyNoList);
    }

    @Override
    public List<ChannelBaseInfo> getChannelBaseInfoListDistinct(List<String> applyNoList){
        return this.baseMapper.getChannelBaseInfoListDistinct(applyNoList);
    }

    @Override
    public void insertAffiliatedRealtimeData(String applyNo,String busiType) {

        AffiliatedRealtimeDataDto realtimeDataDto = new AffiliatedRealtimeDataDto();
        CaseCarInfo carInfo = carInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, applyNo));
        FinCostDetails finCostDetails = costInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, applyNo));
        //判断是否挂靠
        if(StringUtil.isNotBlank(carInfo.getAffCompanyId())) {
            log.info("########挂靠公司实时数据{}插入begin######## ",busiType);
            realtimeDataDto.setAffiliatedNo(carInfo.getAffCompanyId());
            realtimeDataDto.setApplyNo(applyNo);
            realtimeDataDto.setNodeType(busiType);
            realtimeDataDto.setPassNum(1);
            realtimeDataDto.setPassMoney(finCostDetails.getLoanAmt());
            realtimeDataDto.setApplyDate(DateUtil.date());
            //调用插入挂靠公司实时数据接口
            IResponse ir = channelApiFeign.realtimeData(realtimeDataDto, getHeaders());
            log.info("挂靠公司实时数据插入返回->code={},msg={}",new Object[]{ir.getCode(),ir.getMsg()});

        }

    }

    @Override
    public void deleteAffiliatedRealtimeData(String applyNo) {
        AffiliatedRealtimeDataDto realtimeDataDto = new AffiliatedRealtimeDataDto();
        realtimeDataDto.setApplyNo(applyNo);
        IResponse ir = channelApiFeign.removeRealtimeData(realtimeDataDto, getHeaders());
        log.info("挂靠公司实时数据插入返回->code={},msg={}",new Object[]{ir.getCode(),ir.getMsg()});
    }

    private Map<String, String> getHeaders(){
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getChannelClientId());
        headers.put("clientSecret", applyConfig.getChannelClientSecret());
        return headers;
    }

    /**
     * 通过申请编号，查询该订单是否属于先放后抵
     * @param applyNo 申请编号
     * @return 返回结果 yes - 是，no - 否
     */
    public String queryIsLoanAfterMortgage(String applyNo){

        log.warn("查询先放后抵的参数的相关接口，其中入参applyNo={}",applyNo);
        ChannelBaseInfo channelBaseInfo = this.baseMapper.getChannelBaseInfo(applyNo);
        log.warn("查询先放后抵的参数的相关接口，查询到的数据channelBaseInfo={}", JSON.toJSONString(channelBaseInfo));
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getCaseByApplyNo(applyNo);
        log.warn("查询先放后抵的参数的相关接口，查询到的数据caseBaseInfo={}", JSON.toJSONString(caseBaseInfo));
        //经销商- 抵押后放款
        List<ChannelRiskInfo> risks = channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>query().lambda().eq(ChannelRiskInfo::getChannelId, channelBaseInfo.getChannelId()));
        log.warn("查询先放后抵的参数的相关接口，查询到的数据ChannelRiskInfo={}", JSON.toJSONString(risks));
        for(ChannelRiskInfo riskInfo : risks){
            if(riskInfo != null && StringUtils.isNotEmpty(riskInfo.getBusinessType()) && caseBaseInfo.getBusinessType().equals(riskInfo.getBusinessType())){
                if(riskInfo.getIsMortgage().equals(AfsEnumUtil.key(YesOrNoEnum.YES))){
                    return "yes";
                } else {
                    return "no";
                }
            }else{
                log.warn("先放后抵数据对比失败：riskInfo={}",JSON.toJSONString(riskInfo));
            }
        }
        log.error("通过进件端订单表查询到的业务类型与查询到的经销商信息中的业务类型匹配失败！");
        throw new AfsBaseException("是否先放后抵判断处理异常！请联系管理员处理！");

    }

    /**
     * 通过申请编号，查询该订单是否属于先放后抵
     * @param channelId 渠道id
     * @param carNature 融资类型 01-新车
     * @return 返回结果 yes - 是，no - 否
     */
    public String queryNewIsLoanAfterMortgage(String channelId,String carNature){

        //经销商- 抵押后放款
        ChannelRiskInfo riskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda().eq(ChannelRiskInfo::getChannelId, channelId)
                .eq(ChannelRiskInfo::getBusinessType,carNature));
        if(ObjectUtil.isNotNull(riskInfo)){
            if(riskInfo.getIsMortgage().equals(AfsEnumUtil.key(YesOrNoEnum.YES))){
                return "yes";
            } else {
                return "no";
            }
        }
        return null;

    }

    /**
     * 执行更新更新评级
     *
     * @param channelGrade 更新评级
     * @param channelId    渠道id
     * @return
     */
    @Override
    public void updateChannelGradeCri(String channelGrade, Long channelId) {
        this.baseMapper.updateChannelGradeCri(channelGrade, channelId);
    }

    /**
     * 执行更新更新评级
     *
     * @param qualityGrade 更新评级
     * @param channelGrade 更新评级
     * @return
     */
    @Override
    public void updateQualityGradeCri(String qualityGrade, String channelGrade) {
        this.baseMapper.updateQualityGradeCri(qualityGrade, channelGrade);
    }

    /**
     * 执行更新更新评级
     *
     * @param qualityGrade 更新评级
     * @param channelGrade 更新评级
     * @return
     */
    @Override
    public void updateQualityGradeCci(String qualityGrade, String channelGrade) {
        this.baseMapper.updateQualityGradeCci(qualityGrade, channelGrade);
    }

    /**
     * 执行更新是否先放后抵
     *
     * @param isMortgage 先放后抵
     * @param channelId  渠道id
     * @return
     */
    @Override
    public void updateIsMortgageCri(String isMortgage, Long channelId) {
        this.baseMapper.updateIsMortgageCri(isMortgage, channelId);
    }

    @Override
    public void updateCaseChannelBaseInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseChannelBaseInfo(modify);
    }

    @Override
    public void updateCaseWriteOffApportionDetail(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffApportionDetail(modify);
    }

    @Override
    public void updateCaseWriteOffContractDetailManage(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffContractDetailManage(modify);
    }

    @Override
    public void updateCaseChannelServiceFee(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseChannelServiceFee(modify);
    }

    @Override
    public void updateCaseWriteOffAccountCycleDetail(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffAccountCycleDetail(modify);
    }

    @Override
    public void updateCaseWriteOffApportionInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffApportionInfo(modify);
    }

    @Override
    public void updateCaseWriteOffBaseInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffBaseInfo(modify);
    }

    @Override
    public void updateCaseWriteOffBaseInvoiceRel(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffBaseInvoiceRel(modify);
    }

    @Override
    public void updateCaseWriteOffInvoiceInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseWriteOffInvoiceInfo(modify);
    }

    @Override
    public void updateCaseChannelPigeonDetail(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseChannelPigeonDetail(modify);
    }

    @Override
    public void updateCaseChannelReceivablesAccount(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseChannelReceivablesAccount(modify);
    }

    @Override
    public void updateCaseBaseInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseBaseInfo(modify);
    }

    @Override
    public void updateCaseChannelInfo(ChannelInfoModifyVO modify) {
        this.baseMapper.updateCaseChannelInfo(modify);
    }
}
