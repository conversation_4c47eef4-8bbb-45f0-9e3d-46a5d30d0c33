package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBatchInfo;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import javax.servlet.http.HttpServletResponse;

public interface WriteOffBatchInfoService extends IService<WriteOffBatchInfo> {

    /**
     * 条件查询
     * @param queryCondition
     * @return
     */
    IResponse queryByCondition(QueryCondition<WriteOffBatchInfo> queryCondition);

    /**
     * 数据导出
     * @param condition
     * @param response
     */
    void exportData(WriteOffBatchInfo condition, HttpServletResponse response);
}
