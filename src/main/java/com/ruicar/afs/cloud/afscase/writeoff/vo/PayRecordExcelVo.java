package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务费支付详情记录ExcelVo
 */
@Data
public class PayRecordExcelVo implements Serializable {

    /**
     * 流程编号
     */
    @ExcelProperty(value = "流程编号", index = 0)
    private String businessNo;
    /**
     * 批次号
     */
    @ExcelProperty(value = "批次号", index = 1)
    private String batchNo;
    /**
     * 经销商code
     */
    @ExcelProperty(value = "经销商代码", index = 2)
    private String channelCode;
    /**
     * 经销商name
     */
    @ExcelProperty(value = "经销商名称", index = 3)
    private String channelFullName;
    /**
     * 批次总金额
     */
    @ExcelProperty(value = "批次总金额", index = 4)
    private BigDecimal batchAmount;
    /**
     * 核销期数
     */
    @ExcelProperty(value = "核销期数", index = 5)
    private String writeOffMonth;
    /**
     * 提取状态
     */
    @ExcelProperty(value = "提取状态", index = 6)
    private String status;
    /**
     * 支付期数
     */
    @ExcelProperty(value = "支付期数", index = 7)
    private Integer termNo;
    /**
     * 支付比例
     */
    @ExcelProperty(value = "支付比例%", index = 8)
    private BigDecimal payRate;
    /**
     * 应支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "应支付时间", index = 9)
    private Date waitPayTime;
    /**
     * 本期需支付金额
     */
    @ExcelProperty(value = "支付金额", index = 10)
    private BigDecimal payAmount;
    /**
     * 付款状态
     */
    @ExcelProperty(value = "支付状态", index = 11)
    private String payStatus;
    /**
     * 实际支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "实际支付时间", index = 12)
    private Date successPayTime;
    /**
     * 命中规则名称
     */
    @ExcelProperty(value = "规则名称", index = 13)
    private String ruleName;
    /**
     * cbs发送，请求流水号
     */
    @ExcelProperty(value = "付款流水号", index = 14)
    private String cbsRefNbr;
    /**
     * 付款账号
     */
    @ExcelProperty(value = "付款账号", index = 15)
    private String payAccount;
    /**
     * 收款账号
     */
    @ExcelProperty(value = "收款账号", index = 16)
    private String payReceiveAccount;
    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", index = 17)
    private String errorMsg;
}
