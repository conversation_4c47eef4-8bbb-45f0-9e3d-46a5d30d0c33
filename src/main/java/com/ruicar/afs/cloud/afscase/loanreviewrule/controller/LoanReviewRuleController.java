package com.ruicar.afs.cloud.afscase.loanreviewrule.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.common.utils.SequenceUtil;
import com.ruicar.afs.cloud.afscase.loanreviewrule.condition.LoanReviewRuleCondition;
import com.ruicar.afs.cloud.afscase.loanreviewrule.entity.LoanReviewRule;
import com.ruicar.afs.cloud.afscase.loanreviewrule.service.LoanReviewRuleService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 复核放款规则控制层
 * @date 2020/5/18 17:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/loanReviewRule")
@Api("复核规则配置")
public class LoanReviewRuleController {

    private final LoanReviewRuleService service;
    private final AfsRuleFeign afsRuleInfoService;

    /**
     * 获取复核规则配置数据
     *
     * @param loanReviewRuleCondition
     * @return
     */
    @PostMapping(value = "/getReviewRuleList")
    @ApiOperation(value = "多条件分页获取复核规则配置数据")
    public IResponse<IPage<LoanReviewRule>> getReviewRulesList(@RequestBody QueryCondition<LoanReviewRuleCondition> loanReviewRuleCondition) {
        LoanReviewRuleCondition condition = loanReviewRuleCondition.getCondition();
        return IResponse.success(service.page(new Page(loanReviewRuleCondition.getPageNumber(), loanReviewRuleCondition.getPageSize()), Wrappers.<LoanReviewRule>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getIsEnable()), LoanReviewRule::getIsEnable, condition.getIsEnable())
                .like(StringUtils.isNotEmpty(condition.getReviewName()), LoanReviewRule::getReviewName, condition.getReviewName())
                .like(StringUtils.isNotEmpty(condition.getRuleDesc()), LoanReviewRule::getRuleDesc, condition.getRuleDesc())
                .eq(null != condition.getEffectTime(), LoanReviewRule::getEffectTime, condition.getEffectTime())
                .eq(null != condition.getFailureTime(), LoanReviewRule::getFailureTime, condition.getFailureTime())));
    }

    /**
     * 新增放款复核规则配置数据
     *
     * @param loanReviewRule
     * @return
     */
    @PostMapping(value = "/addRule")
    @ApiOperation(value = "新增放款暂复核则成功")
    public IResponse<Boolean> addReviewRules(@RequestBody LoanReviewRule loanReviewRule) {
        //按规则生成id
        loanReviewRule.setReviewNo(SequenceUtil.getSeq(Const.REVIEW_NO));
        service.save(loanReviewRule);
        return new IResponse<Boolean>().setMsg("新增放款复核规则成功！");
    }

    /**
     * 更新规则表业务id
     *
     * @param loanReviewRule
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody LoanReviewRule loanReviewRule) {
        //更新状态为启用
        loanReviewRule.setIsEnable(WhetherEnum.NO.getCode());
        service.updateById(loanReviewRule);
        if (null != loanReviewRule.getRuleId()) {
            //默认生效
            afsRuleInfoService.deActiveRule(loanReviewRule.getRuleId());
        }
        return new IResponse<Boolean>().setMsg("保存成功，规则已生效！");
    }


    /**
     * 编辑放款复核规则配置数据
     *
     * @param loanReviewRule
     * @return
     */
    @PostMapping(value = "/editRule")
    @ApiOperation(value = "修改放款复核规则成功")
    public IResponse<Boolean> edit(@RequestBody LoanReviewRule loanReviewRule) {
        service.updateById(loanReviewRule);
        return new IResponse<Boolean>().setMsg("修改放款复核规则成功！");
    }

    /**
     * 批量删除放款复核规则成功
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/delByIds/{ids}")
    @ApiOperation(value = "批量删除放款复核规则成功")
    public IResponse<Boolean> del(@PathVariable String[] ids) {
        //使规则信息表数据失效
        service.deActiveRuleByRuleNo(ids);
        //删除复核规则数据
        service.removeByIds(Arrays.asList(ids));
        return new IResponse<Boolean>().setMsg("删除放款复核规则成功！");
    }

    /**
     * 启用放款复核规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用放款复核规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable String id) {
        service.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用复核规则成功！");
    }

    /**
     * 停用放款复核规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用放款复核规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable String id) {
        service.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用放款复核规则成功！");
    }
    /**设置停用中*/
    @PostMapping(value = "/setUnEnableIng/{id}")
    @ApiOperation(value = "设置停用中")
    public IResponse<Boolean> setUnEnableIng(@PathVariable String id) {
        service.setUnEnableIng(id);
        return new IResponse<Boolean>().setMsg("停用放款暂停规则成功！");
    }
}
