package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;


/**
 * 服务费账期配置
 */
@Data
@TableName(value = "write_off_month_config", autoResultMap = true)
public class WriteOffMonthConfig extends BaseEntity<WriteOffMonthConfig> {

    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 开始日期-天
     */
    private Integer startDay;
    /**
     * 本月结束日期-天
     */
    private Integer thisEndDay;
    /**
     * 次月结束日期-天
     */
    private Integer nextEndDay;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 规则展示
     */
    private String ruleExpress;
    /**
     * 状态;0-未生效；1-生效
     */
    private String status;
}
