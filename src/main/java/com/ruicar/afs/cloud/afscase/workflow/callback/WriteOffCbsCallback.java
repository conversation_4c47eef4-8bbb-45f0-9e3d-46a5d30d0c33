package com.ruicar.afs.cloud.afscase.workflow.callback;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 服务费cbs付款自动节点
 */
@Slf4j
@AllArgsConstructor
@Component
public class WriteOffCbsCallback implements CommonAdapter {

    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final WriteOffPayRecordService writeOffPayRecordService;

    @Override
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
        String extParam, Map<String, String> flowVariables) {
        log.info("[服务费cbs付款]流程回调,flowInstanceId:{}，variables:{}", flowInstanceId, flowVariables);
        String businessNo = flowVariables.get(FlowConstant.BUSINESS_NO);
        String periodFlag = flowVariables.get("periodFlag");
        if ("1".equals(periodFlag)) {
            //新页面的分期提取
            List<WriteOffPayRecord> payRecordList = writeOffPayRecordService.list(Wrappers.<WriteOffPayRecord>lambdaQuery().eq(WriteOffPayRecord::getBusinessNo, businessNo));
            writeOffPayRecordService.recordCbsPay(payRecordList, Boolean.TRUE, businessNo);
        } else {
            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelService.list(Wrappers.<WriteOffBaseInvoiceRel>query().lambda()
                    .eq(WriteOffBaseInvoiceRel::getApproveId, businessNo));
            writeOffPayRecordService.relCbsPay(writeOffBaseInvoiceRels, Boolean.TRUE, businessNo);
        }
        return flowVariables;
    }
}
