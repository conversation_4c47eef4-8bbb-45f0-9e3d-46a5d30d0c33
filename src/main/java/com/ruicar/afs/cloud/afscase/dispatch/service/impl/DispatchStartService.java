package com.ruicar.afs.cloud.afscase.dispatch.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseSubmitRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfoTemp;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseSubmitRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoTempService;
import com.ruicar.afs.cloud.afscase.autoaudit.service.CardDetectRecordChangeService;
import com.ruicar.afs.cloud.afscase.casemaininfo.entity.CaseMainInfo;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekDrivingInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekOperatorInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.dispatch.DispatchContext;
import com.ruicar.afs.cloud.afscase.dispatch.annotation.Dispatch;
import com.ruicar.afs.cloud.afscase.dispatch.enums.DispatchTypeEnum;
import com.ruicar.afs.cloud.afscase.dispatch.service.BaseDispatchService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.processor.util.WorkflowTypeUtil;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.StatusDTO;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.deepseek.service.StatementAutomaticRecognitionService;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.seats.entity.RegularValue;
import com.ruicar.afs.cloud.seats.service.RegularValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @date Jun 16, 2020
 */

@Slf4j
@Service
@Dispatch(name = "启动信审审批流程", dependsOn = "dispatchTimeBeforService", group = DispatchTypeEnum.CREDIT)
public class DispatchStartService extends BaseDispatchService {
    @Autowired
    private RegularValueService valueService;

    @Autowired
    private WorkProcessScheduleInfoTempService workProcessScheduleInfoTempService;

    @Autowired
    private WorkflowHelper workflowHelper;

    @Autowired
    private CaseBaseInfoService caseBaseInfoService;

    @Autowired
    private FlowConfigProperties flowConfigProperties;

    @Autowired
    private WorkflowWrapperService workflowWrapperService;

    @Autowired
    private CardDetectRecordChangeService cardDetectRecordChangeService;

    @Autowired
    private CaseMainInfoService caseMainInfoService;

    @Autowired
    private ComAttachmentManagementService comAttachmentManagementService;

    @Autowired
    private CaseConfParamService caseConfParamService;

    @Autowired
    private CaseSubmitRecordService caseSubmitRecordService;

    @Autowired
    private CaseCustInfoService caseCustInfoService;
    @Autowired
    private ComAttachmentFileService comAttachmentFileService;
    @Autowired
    private StatementAutomaticRecognitionService statementAutomaticRecognitionService;
    @Autowired
    private DeepSeekDrivingInfoService deepSeekDrivingInfoService;
    @Autowired
    private DeepSeekOperatorInfoService deepSeekOperatorInfoService;
    @Autowired
    private CaseFacePhotoInfoService facePhotoInfoService;

    @Override
    public void process(DispatchContext context) throws Exception {
        List<CaseBaseInfo> caseInfoList = (List) (Optional.ofNullable(context.get(KEY)).orElse(new ArrayList()));
        if (caseInfoList.isEmpty()) {
            return;
        }
        // 优先级排序
        // 离岗回收 -》 时效监测回收 -》 常规案件
        List<CaseBaseInfo> recycleCaseList = caseInfoList.stream().filter(caseInfo -> WhetherEnum.YES.getCode().equals(caseInfo.getFlowRecycleStatus())).collect(Collectors.toList());
        List<CaseBaseInfo> effectRecycleCaseList = caseInfoList.stream().filter(
                caseInfo ->
                        WhetherEnum.YES.getCode().equals(caseInfo.getEffectRecycleFlag()) && !WhetherEnum.YES.getCode().equals(caseInfo.getFlowRecycleStatus())
        ).collect(Collectors.toList());
        List<CaseBaseInfo> normalCaseList = caseInfoList.stream().filter(
                caseInfo ->
                        !recycleCaseList.contains(caseInfo) && !effectRecycleCaseList.contains(caseInfo))
                .collect(Collectors.toList());

        // 置顶 -> 优先级排序
        Comparator<CaseBaseInfo> stickComparator = (first, second) -> {
            Integer firstNumber = WhetherEnum.YES.getCode().equals(first.getIsStick()) ? Integer.MAX_VALUE : Convert.toInt(first.getPriority(), -1);
            Integer secondNumber = WhetherEnum.YES.getCode().equals(second.getIsStick()) ? Integer.MAX_VALUE : Convert.toInt(first.getPriority(), -1);
            return secondNumber.compareTo(firstNumber);
        };

        Comparator<CaseBaseInfo> submitTimeComparator = Comparator.nullsLast(Comparator.comparing(CaseBaseInfo::getPassFirstDate));

        // 离岗回收案件
        List<CaseBaseInfo> list = recycleCaseList.stream().sorted(stickComparator.thenComparing(submitTimeComparator)).collect(Collectors.toList());

        // 时效监测回收案件
        list.addAll(effectRecycleCaseList.stream().sorted(stickComparator.thenComparing(submitTimeComparator)).collect(Collectors.toList()));

        // 常规案件
        list.addAll(normalCaseList.stream().sorted(stickComparator.thenComparing(submitTimeComparator)).collect(Collectors.toList()));

        log.info("信审分单前start_caseList:{}"+list);
        for (CaseBaseInfo caseInfo : list) {
            JSONObject jsonObject = (JSONObject) context.get(caseInfo.getId());
            RuleRunResult result = RuleHelper.runRule(jsonObject, "approveProcedure", false, RuleRunEnum.SERIAL);
            log.info("DispatchStartService.RuleRunResult:{},{},{}", caseInfo.getApplyNo(), caseInfo.getId(), result);
            if (result.getHit()) {
                List<String> listNo = result.getResults().stream().map(s -> s.getRuleNo()).collect(Collectors.toList());
                if (listNo.isEmpty()) {
                    return;
                }
                List<RegularValue> valueList = valueService.listByIds(listNo);
                if (valueList == null || valueList.isEmpty()) {
                    return;
                }
                String[] value = valueList.stream().map(s -> {
                    String[] str = s.getValue().split("#");
                    return str;
                }).max(Comparator.comparing(s -> s[0])).get();
                String afsFlowKey = value[1];
                String errMsgType = "";
                try {
                    log.info("流程匹配命中结果[{}],流程编号为[{}]", JSON.toJSONString(listNo), afsFlowKey);
                    if (StrUtil.isNotEmpty(caseInfo.getApplyJointStatus()) && caseInfo.getApplyJointStatus()
                        .equals(AfsEnumUtil.key(YesOrNoEnum.YES))) {
                        List<CaseSubmitRecord> submitRecords = caseSubmitRecordService.list(
                            Wrappers.<CaseSubmitRecord>lambdaQuery()
                                .eq(CaseSubmitRecord::getApplyNo, caseInfo.getApplyNo())
                                .orderByDesc(CaseSubmitRecord::getSubmitDate));
                        Date targetDate;
                        if (CollectionUtil.isNotEmpty(submitRecords)) {
                            targetDate = submitRecords.get(0).getSubmitDate();
                        } else {
                            targetDate = caseInfo.getPassFirstDate();
                        }
                        LocalDateTime targetTime = targetDate.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                        LocalDateTime now = LocalDateTime.now();
                        // 计算时间差（取绝对值，避免顺序影响）
                        Duration duration = Duration.between(targetTime, now).abs();
                        Map<String, List<DicDataDto>> sexDic = DicHelper.getDicMaps("intelligentApproveFirst");
                        String waitTime = sexDic.get("intelligentApproveFirst").get(0).getValue();
                        if (duration.toMinutes() < Integer.parseInt(waitTime)) {
                            log.error("【智能识别】申请编号：{},等待至{}分钟！", caseInfo.getApplyNo(),waitTime);
                            continue;
                        }
                    }
                    if (flowBeforeCheck(caseInfo, BusinessStateInEnum.ALLOCATION) || flowBeforeCheck(caseInfo, BusinessStateInEnum.EXCEPTIONALLOCATION)) {
                        errMsgType = "流程发起";
                        log.info("流程发起：{}-{}", caseInfo.getApplyNo(), afsFlowKey);
                        createLocalWorkProcessor(caseInfo.getApplyNo(), value[1]);

                        StartFlowRequestBo startFlowRequestBo = new StartFlowRequestBo();
                        startFlowRequestBo.setBusinessNo(caseInfo.getApplyNo());
                        //这两个ID为指定工作流的id，后面可以写在配置文件里面。
                        startFlowRequestBo.setPackageId(flowConfigProperties.getApprovePackageId());
                        startFlowRequestBo.setTemplateId(flowConfigProperties.getApproveTemplateId());
                        startFlowRequestBo.setSubject("案件审批" + caseInfo.getApplyNo());
                        JSONObject object = startFlowRequestBo.getParams();
                        if (object == null) {
                            object = new JSONObject();
                        }


                        startFlowRequestBo.setParams(object);
                        workflowWrapperService.busiParamAdvice(startFlowRequestBo);
                        log.info("startFlowRequestBo:{}",startFlowRequestBo);
                        IResponse response = null;
                        log.info("执行提交任务{}"+caseInfo.getFlowParseExceptionId()+"----申请编号："+caseInfo.getApplyNo()+"---业务状态（内）："+caseInfo.getBusinessStateIn());
                        if(StringUtils.isNotEmpty(caseInfo.getFlowParseExceptionId()) &&
                                (StringUtils.isNotEmpty(caseInfo.getBusinessStateIn()) &&
                                        AfsEnumUtil.key(BusinessStateInEnum.EXCEPTIONALLOCATION).equals(caseInfo.getBusinessStateIn())) ) {
                            log.info("执行异常待提交任务{}"+caseInfo.getFlowParseExceptionId());
                            response = workflowHelper.exceptionRetry(caseInfo.getFlowParseExceptionId());
                        }else {
                            if (AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(caseInfo.getBusinessStateIn())){
                                response = workflowHelper.startFlow(startFlowRequestBo, UseSceneEnum.APPROVE);
                            }
                        }
                        log.info("分单工作流接口回调结果:{}"+response);
                        if (ObjectUtil.isNotNull(response) && CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
                            //修改主状态信息
                            List<CaseMainInfo> caseMainInfos = caseMainInfoService.list(Wrappers.<CaseMainInfo>query().lambda()
                                    .eq(CaseMainInfo::getApplyNo, caseInfo.getApplyNo())
                                    .eq(ObjectUtils.isNotEmpty(caseInfo.getOrderId()), CaseMainInfo::getPreApplyNo, caseInfo.getOrderId())
                                    .orderByDesc(CaseMainInfo::getUpdateTime)
                            );
                            if (CollectionUtils.isNotEmpty(caseMainInfos)) {
                                CaseMainInfo caseMainInfo = caseMainInfos.get(0);
                                CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
                                StatusDTO caseStatusDTO = new StatusDTO();
                                caseMainUpdateCondition.setApplyNo(caseInfo.getApplyNo());
                                caseMainUpdateCondition.setStatus((CaseCodeEnum) AfsEnumUtil.getEnum(caseMainInfo.getStatusCode(), CaseCodeEnum.class));
                                caseMainUpdateCondition.setPreApplyNo(caseInfo.getOrderId());
                                caseStatusDTO.setStatusCode(AfsEnumUtil.key(BusinessStateInEnum.WAIT_CHECK));
                                caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(BusinessStateInEnum.WAIT_CHECK));
                                caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
                                caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
                            }
                        }

                    } else if (flowBeforeCheck(caseInfo, BusinessStateInEnum.TASK_ASSIGN)
                            && WhetherEnum.YES.getCode().equals(caseInfo.getFlowParseFlag())) {
                        errMsgType = "流程恢复";
                        log.info("流程恢复：{}", caseInfo.getApplyNo());
                        //TODO
//                        processFlowException(caseInfo);
                    }
                } catch (Exception e) {
                    log.error("{}失败，申请编号{} : {}", errMsgType, caseInfo.getApplyNo(), e.getMessage(), e);
                }
            }
        }
    }
    /**
     * @Description 待分配任务池中可能对案件进行指定处理人操作，此时业务状态可能已经改变
     * <AUTHOR>
     * @Date 2020/8/28 19:12
     */

    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public boolean flowBeforeCheck(CaseBaseInfo caseBaseInfo, BusinessStateInEnum businessStateInEnum) {
        return caseBaseInfoService.count(
                Wrappers.<CaseBaseInfo>lambdaQuery()
                        .eq(CaseBaseInfo::getApplyNo, caseBaseInfo.getApplyNo())
                        .eq(CaseBaseInfo::getBusinessStateIn, AfsEnumUtil.key(businessStateInEnum))
        ) > 0;
    }

    /**
     * @Description 创建流程实例
     * <AUTHOR>
     * @Date 2020/6/17 14:11
     */
    public WorkProcessScheduleInfoTemp createLocalWorkProcessor(String applyNo, String afsFlowKey) {
        // 保存工作流实例
        WorkProcessScheduleInfoTemp info = new WorkProcessScheduleInfoTemp();
        info.setAfsFlowKey(afsFlowKey);
        info.setApplyNo(applyNo);
        info.setProcessName(WorkflowTypeUtil.getApproveTypeName(afsFlowKey));
        info.setStartTime(new Date());
        workProcessScheduleInfoTempService.saveWithNewTransactional(info);
        return info;
    }


//    public void processFlowException(CaseBaseInfo caseInfo){
//        WorkFlowResponse response = workflowService.resumeFlow(caseInfo.getFlowParseId(),caseInfo.getFlowParseExceptionId());
//        if (!(Const.WORKFLOW_RESPONSE_SUCCESS == response.getSuccess())) {
//            log.warn("流程恢复失败，stageId:{},exceptionId:{}.message:{}",caseInfo.getFlowParseId(),caseInfo.getFlowParseExceptionId(),response.getMessage());
//        }
//
//    }

}
