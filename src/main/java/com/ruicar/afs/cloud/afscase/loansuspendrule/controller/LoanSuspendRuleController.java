package com.ruicar.afs.cloud.afscase.loansuspendrule.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.common.utils.SequenceUtil;
import com.ruicar.afs.cloud.afscase.loansuspendrule.condition.LoanSuspendRuleCondition;
import com.ruicar.afs.cloud.afscase.loansuspendrule.entity.LoanSuspendRule;
import com.ruicar.afs.cloud.afscase.loansuspendrule.service.LoanSuspendRuleService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 暂停放款规则控制层
 * @date 2020/5/18 17:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/loanSuspendRule")
@Api("暂停规则配置")
public class LoanSuspendRuleController {

    private final LoanSuspendRuleService service;
    private final AfsRuleFeign afsRuleInfoService;

    /**
     * 获取暂停规则配置数据
     *
     * @param condition
     * @return
     */
    @PostMapping(value = "/getSuspendRuleList")
    @ApiOperation(value = "多条件分页获取暂停规则配置数据")
    public IResponse<IPage<LoanSuspendRule>> getSuspendRulesList(@RequestBody QueryCondition<LoanSuspendRuleCondition> condition) {
        return IResponse.success(service.page(new Page(condition.getPageNumber(), condition.getPageSize()), Wrappers.<LoanSuspendRule>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getCondition().getIsEnable()), LoanSuspendRule::getIsEnable, condition.getCondition().getIsEnable())
                .eq(StringUtils.isNotEmpty(condition.getCondition().getReviewNo()), LoanSuspendRule::getReviewNo, condition.getCondition().getReviewNo())
                .eq(StringUtils.isNotEmpty(condition.getCondition().getLockDegree()), LoanSuspendRule::getLockDegree, condition.getCondition().getLockDegree())
                .like(StringUtils.isNotEmpty(condition.getCondition().getReviewName()), LoanSuspendRule::getReviewName, condition.getCondition().getReviewName())
                .like(StringUtils.isNotEmpty(condition.getCondition().getLockReason()), LoanSuspendRule::getLockReason, condition.getCondition().getLockReason())
                .like(StringUtils.isNotEmpty(condition.getCondition().getRuleDesc()), LoanSuspendRule::getRuleDesc, condition.getCondition().getRuleDesc())));
    }

    /**
     * 新增放款暂停规则配置数据
     *
     * @param loanSuspendRule
     * @return
     */
    @PostMapping(value = "/addRule")
    @ApiOperation(value = "新增放款暂停规则配置数据")
    public IResponse<Boolean> addSuspendRules(@RequestBody LoanSuspendRule loanSuspendRule) {
        //按规则生成id
        loanSuspendRule.setReviewNo(SequenceUtil.getSeq(Const.SUSPEND_NO));
        service.save(loanSuspendRule);
        return new IResponse<Boolean>().setMsg("新增放款暂停规则成功！");
    }

    /**
     * 更新规则表业务id
     *
     * @param loanSuspendRule
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody LoanSuspendRule loanSuspendRule) {
        //更新状态为启用
        loanSuspendRule.setIsEnable(WhetherEnum.NO.getCode());
        service.updateById(loanSuspendRule);
        return new IResponse<Boolean>().setMsg("保存成功！");
    }

    /**
     * 编辑放款暂停规则配置数据
     *
     * @param loanSuspendRule
     * @return
     */
    @PostMapping(value = "/editRule")
    @ApiOperation(value = "编辑放款暂停规则数据")
    public IResponse<Boolean> edit(@RequestBody LoanSuspendRule loanSuspendRule) {
        service.updateById(loanSuspendRule);
        return new IResponse<Boolean>().setMsg("修改放款暂停规则成功！");
    }

    /**
     * 批量删除放款暂停规则
     *
     * @patram ids
     * @reurn
     */
    @PostMapping(value = "/delByIds/{ids}")
    @ApiOperation(value = "批量删除放款暂停规则")
    public IResponse<Object> del(@PathVariable String[] ids) {
        //使规则信息表数据失效
        service.deActiveRuleByRuleNo(ids);
        //删除暂停规则数据
        service.removeByIds(Arrays.asList(ids));
        return new IResponse<Object>().setMsg("删除放款暂停规则成功！");
    }

    /**
     * 启用暂停暂停规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用放款暂停规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable String id) {
        service.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用放款暂停规则成功！");
    }

    /**
     * 停用放款暂停规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用放款暂停规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable String id) {
        service.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用放款暂停规则成功！");
    }

    /**
     * 失效规则
     *
     * @param ruleId
     * @return
     */
    @PostMapping("/deActiveRuleById/{ruleId}")
    public IResponse deActiveRuleById(@PathVariable("ruleId") String ruleId) {
        //先反激活再失效
        afsRuleInfoService.deActiveRuleByRuleId(Long.valueOf(ruleId));
        return IResponse.success("失效成功");
    }

    /**批量锁定合同*/
    @PostMapping("/lockContractByRule/{id}")
    public IResponse lockContractByRule(@PathVariable("id") String id) {
        service.lockContractByRule(id);
        return IResponse.success("批量锁定成功");
    }
    /**批量锁定合同*/
    @PostMapping("/removeLockContractByRule/{id}")
    public IResponse removeLockContractByRule(@PathVariable("id") String id) {
        try {
            service.removeLockContractByRule(id);
        } catch (Exception e){
            log.error("暂停规则停用失败！",e);
        }
        return IResponse.success("批量解锁成功");
    }

    /**设置停用中*/
    @PostMapping(value = "/setUnEnableIng/{id}")
    @ApiOperation(value = "设置停用中")
    public IResponse<Boolean> setUnEnableIng(@PathVariable String id) {
        service.setUnEnableIng(id);
        return new IResponse<Boolean>().setMsg("停用放款暂停规则成功！");
    }
}
