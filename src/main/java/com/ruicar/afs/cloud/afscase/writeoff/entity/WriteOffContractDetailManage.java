package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.core.mask.annotation.ContentMask;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import lombok.Data;

/**
 * 合同明细表
 */
@TableName(value ="write_off_contract_detail_manage")
@Data
public class WriteOffContractDetailManage extends BaseEntity<WriteOffContractDetailManage> implements Serializable {
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 核销项汇总核销项编号
     */
    private String baseInfoApply;
    /**
     * 所属资方
     */
    private String belongingCapital;
    /**
     * 资方回款时间
     */
    private Date capitalReturnTime;
    /**
     * 是否生成了核销项(1是，0否，2计算异常)
     */
    private String writeOffFlag;
    /**
     * 应收对账确认时间
     */
    private Date receiveConfirmTime;
    /**
     * 账期
     */
    private String writeOffMonth;
    /**
     * 是否生成了提取项(1是，0否)
     */
    private String serverTqFlag;
    /**
     * 应收支付确认(1已确认，0未确认)
     */
    private String payConfirmFlag;
    /**
     * 应收支付确认时间
     */
    private Date payConfirmTime;
    /**
     * 承租人姓名（脱敏）
     */
    @ContentMask(startPos = 1, endPos = 2)
    private String custNameRepeat;
    /**
     * 业务类型，新车、二手车
     */
    private String businessType;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 融资总额
     */
    private BigDecimal loanAmt;
    /**
     * 融资期限
     */
    private Integer loanTerm;
    /**
     * 客户利率
     */
    private BigDecimal custRate;
    /**
     * 贴息方式
     */
    private String discountType;
    /**
     * 放款时间
     */
    private Date loanDate;
    /**
     * 合同到期日期
     */
    private Date endDate;
    /**
     * 合同状态
     */
    private ContractStatusEnum contractStatus;
    /**
     * 渠道id
     */
    private Long channelId;
    /**
     * 经销商代码
     */
    private String channelCode;
    /**
     * 经销商名称
     */
    private String channelFullName;
    /**
     * 渠道归属case.ChannelBelongEnum
     */
    private String channelBelong;
    /**
     * 经销商省份
     */
    private String channelProvince;
    /**
     * 经销商城市
     */
    private String channelCity;
    /**
     * SAP供应商代码
     */
    private String spaSupplierCode;
    /**
     * 城市服务系数（计算规则）
     */
    private BigDecimal cityServeRate;
    /**
     * 经销商服务系数（计算规则）
     */
    private BigDecimal channelServeRate;
    /**
     * 支付标准（计算规则）
     */
    private BigDecimal backRate;
    /**
     * 经销商服务瑕疵评价系数（计算规则）
     */
    private BigDecimal flawRate;
    /**
     * 服务费
     */
    private BigDecimal serviceCharge;
    /**
     * 服务费初始状态
     */
    private BigDecimal serviceChargeBegin;
    /**
     * 奖惩前金额
     */
    private BigDecimal beforeAmount;
    /**
     * 奖惩金额
     */
    private BigDecimal prizeOrPunishAmount;
    /**
     * 上月扣罚金额
     */
    private BigDecimal lastToPunishAmount;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 未含税金额
     */
    private BigDecimal excludeTaxAmount;
    /**
     * 分摊金额
     */
    private BigDecimal apportionAmount;
    /**
     * 已分摊金额
     */
    private BigDecimal apportionedAmount;
    /**
     * 剩余分摊金额
     */
    private BigDecimal residueApportionAmount;
    /**
     * 服务费计算错误信息
     */
    private String errMsg;

    /**
     * 分摊标识(0:未分摊,1:分摊中,2:分摊结束)
     */
    private String apportionFlag;

    /**
     * 提前结清标识(0:不是提前结清订单或未提前结清，1 已提前结清 )
     */
    private Boolean advanceSettleFlag;

    /**
     * 提前结清扣罚金额
     */
    private BigDecimal advanceSettleAmount;

    /**
     * 扣减比例
     */
    private Integer deductRate;

    /**
     * 提前结清账期
     */
    private String advanceSettleMonth;

    /**
     * 当前分摊期数
     */
    private Integer apportionNum;
    /**
     * 经销商优质等级
     */
    private String qualityGrade;
}
