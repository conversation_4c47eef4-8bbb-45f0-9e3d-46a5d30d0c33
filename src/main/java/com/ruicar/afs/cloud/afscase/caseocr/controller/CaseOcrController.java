package com.ruicar.afs.cloud.afscase.caseocr.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.caseocr.dto.CaseApplyResidenceDto;
import com.ruicar.afs.cloud.afscase.caseocr.dto.CaseIntelligentResultVO;
import com.ruicar.afs.cloud.afscase.caseocr.dto.CertificateDto;
import com.ruicar.afs.cloud.afscase.caseocr.dto.DsResultVo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseApplyResidence;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseBusinessLicenseInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseCertificateInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseDrivingLicenceInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseIntelligentResult;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekDrivingInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekOperatorInfo;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseApplyResidenceService;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseBusinessLicenseInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseCertificateInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseDrivingLicenceInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseIntelligentResultService;
import com.ruicar.afs.cloud.afscase.common.enums.InterfaceIdentifyEnum;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.config.FaceIdentifyConfig;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustIndividualService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.workflow.callback.AutomaticCallback;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustIndividualDto;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.deepseek.entity.CaseFacePhotoInfo;
import com.ruicar.afs.cloud.deepseek.entity.StatementAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.service.StatementAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.utils.GenericVoConverter;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.parameter.commom.enums.CustRelationEnums;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *  OCR功能
 */
@RestController
@RequestMapping("/caseOcr")
@AllArgsConstructor
@Slf4j
public class CaseOcrController {

    private final CaseDrivingLicenceInfoService caseDrivingLicenceInfoService;
    private final CaseBusinessLicenseInfoService caseBusinessLicenseInfoService;
    private final CaseApplyResidenceService caseApplyResidenceService;
    private final CaseCustContactService caseCustContactService;
    private final CaseCertificateInfoService caseCertificateInfoService;
    private final CaseBaseInfoService caseBaseInfoService;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final StringRedisTemplate redisTemplate;
    private final CaseCustInfoService caseCustInfoService;
    private final ApplyContractFeign applyContractFeign;
    private final CaseIntelligentResultService intelligentResultService;
    private final AutomaticCallback automaticCallback;
    private final CaseConfParamService caseConfParamService;
    private final ApplyConfig applyConfig;
    private final CaseFacePhotoInfoService facePhotoInfoService;
    private final FaceIdentifyConfig faceIdentifyConfig;
    private CaseCustIndividualService caseCustIndividualService;
    private StatementAutomaticRecognitionService statementAutomaticRecognitionService;


    @ApiOperation("驾驶证OCR识别保存")
    @PostMapping("/driverLicenseInfoOCR")
    public IResponse driverLicenseInfoOcr(@RequestBody CaseDrivingLicenceInfo info){

        if(ObjectUtil.isNull(info)){
            throw new AfsBaseException("驾驶证Ocr结果不允许为空！");
        }

        if(StringUtils.isBlank(info.getApplyNo())){
            throw new AfsBaseException("驾驶证Ocr结果中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseDrivingLicenceInfo> list = caseDrivingLicenceInfoService.list(Wrappers.<CaseDrivingLicenceInfo>lambdaQuery().eq(CaseDrivingLicenceInfo::getApplyNo, info.getApplyNo()));
        if(ObjectUtil.isNotNull(list) && list.size() > 0){
            caseDrivingLicenceInfoService.remove(Wrappers.<CaseDrivingLicenceInfo>lambdaQuery().eq(CaseDrivingLicenceInfo::getApplyNo, info.getApplyNo()));
        }
        boolean save = caseDrivingLicenceInfoService.save(info);

        if(save){
            return IResponse.success("");
        }

        return IResponse.fail("");
    }

    @ApiOperation("删除驾驶证信息")
    @PostMapping("/deleteDriverLicenseInfoOCR")
    public IResponse deleteDriverLicenseInfoOcr(@RequestBody CaseDrivingLicenceInfo info){

        if(ObjectUtil.isNull(info)){
            throw new AfsBaseException("驾驶证信息不允许为空！");
        }

        if(StringUtils.isBlank(info.getApplyNo())){
            throw new AfsBaseException("驾驶证信息中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseDrivingLicenceInfo> list = caseDrivingLicenceInfoService.list(Wrappers.<CaseDrivingLicenceInfo>lambdaQuery().eq(CaseDrivingLicenceInfo::getApplyNo, info.getApplyNo()));

        if(ObjectUtil.isNotNull(list) && list.size() > 0){
            caseDrivingLicenceInfoService.removeBatchByIds(list);
        }

        return IResponse.success("");
    }


    @ApiOperation("删除营业执照信息")
    @PostMapping("/deleteBusinessLicenseInfoOcr")
    public IResponse deleteBusinessLicenseInfoOcr(@RequestBody CaseBusinessLicenseInfo info){

        if(ObjectUtil.isNull(info)){
            throw new AfsBaseException("营业执照信息不允许为空！");
        }

        if(StringUtils.isBlank(info.getApplyNo())){
            throw new AfsBaseException("营业执照信息中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseBusinessLicenseInfo> list = caseBusinessLicenseInfoService.list(Wrappers.<CaseBusinessLicenseInfo>lambdaQuery().eq(CaseBusinessLicenseInfo::getApplyNo,info.getApplyNo()));

        if(ObjectUtil.isNotNull(list) && list.size() > 0){
            caseBusinessLicenseInfoService.removeBatchByIds(list);
        }

        return IResponse.success("");
    }

    @ApiOperation("营业执照OCR识别保存")
    @PostMapping("/businessLicenseOcr")
    public IResponse businessLicenseOcr(@RequestBody CaseBusinessLicenseInfo info){

        if(ObjectUtil.isNull(info)){
            throw new AfsBaseException("营业执照Ocr结果不允许为空！");
        }

        if(StringUtils.isBlank(info.getApplyNo())){
            throw new AfsBaseException("营业执照Ocr结果中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseBusinessLicenseInfo> list = caseBusinessLicenseInfoService.list(Wrappers.<CaseBusinessLicenseInfo>lambdaQuery().eq(CaseBusinessLicenseInfo::getApplyNo, info.getApplyNo()));
        if(ObjectUtil.isNotNull(list) && list.size() > 0){
            caseBusinessLicenseInfoService.remove(Wrappers.<CaseBusinessLicenseInfo>lambdaQuery().eq(CaseBusinessLicenseInfo::getApplyNo, info.getApplyNo()));
        }
        boolean save = caseBusinessLicenseInfoService.save(info);

        if(save){
            return IResponse.success("");
        }

        return IResponse.fail("");
    }
    /**
     * Delete ApplyResidence ocr response.
     *
     * @param caseApplyResidenceDto the ApplyResidence dto
     * @return the response
     */
    @ApiOperation("删除房产证信息")
    @PostMapping(value = "/deleteApplyResidenceOcr")
    public IResponse<String> deleteApplyResidenceOcr(@RequestBody CaseApplyResidenceDto caseApplyResidenceDto) {
        log.info("删除房产证信息caseApplyResidenceDto :{}", caseApplyResidenceDto);
        if(ObjectUtil.isNull(caseApplyResidenceDto)){
            throw new AfsBaseException("房产证信息不允许为空！");
        }

        if(StringUtils.isBlank(caseApplyResidenceDto.getApplyNo())){
            throw new AfsBaseException("房产证信息中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseApplyResidence> list = caseApplyResidenceService.list(Wrappers.<CaseApplyResidence>lambdaQuery().eq(CaseApplyResidence::getApplyNo, caseApplyResidenceDto.getApplyNo()));

        if(ObjectUtil.isNotNull(list) && !list.isEmpty()){
            caseApplyResidenceService.removeBatchByIds(list);
        }

        return IResponse.success("");
    }

    /**
     * Delete ApplyResidence ocr response.
     *
     * @param certificateDto the ApplyResidence dto
     * @return the response
     */
    @ApiOperation("删除机动车登记证书信息")
    @PostMapping(value = "/deleteCertificateOcr")
    public IResponse<String> deleteCertificateOcr(@RequestBody CertificateDto certificateDto) {
        log.info("删除机动车登记证书信息certificateDto :{}", JSONUtil.parse(certificateDto));
        if(ObjectUtil.isNull(certificateDto)){
            throw new AfsBaseException("机动车登记证书信息不允许为空！");
        }

        if(StrUtil.isBlank(certificateDto.getApplyNo())){
            throw new AfsBaseException("机动车登记证书信息中的申请编号不允许为空!");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseCertificateInfo> certificateInfos = caseCertificateInfoService.list(Wrappers.<CaseCertificateInfo>lambdaQuery()
                .eq(CaseCertificateInfo::getApplyNo, certificateDto.getApplyNo()));

        if(CollectionUtil.isNotEmpty(certificateInfos)){
            caseCertificateInfoService.removeBatchByIds(certificateInfos);
        }

        return IResponse.success("");
    }

    /**
     * 根据申请编号查询房产证信息
     * @param applyNo
     * @return
     */
    @ApiOperation("根据申请编号查询房产证信息")
    @PostMapping(value = "/getResidenceInfo")
    public IResponse<CaseApplyResidence> getResidenceInfo(@RequestParam("applyNo") String applyNo){
        CaseApplyResidence residenceInfo = caseApplyResidenceService.getOne(Wrappers.<CaseApplyResidence>lambdaQuery()
                .eq(CaseApplyResidence::getApplyNo,applyNo));
        return IResponse.success(residenceInfo);
    }

    /**
     * 根据申请编号查询机动车登记证书信息
     * @param applyNo
     * @return
     */
    @ApiOperation("根据申请编号查询机动车登记证书信息")
    @PostMapping(value = "/getCertificateInfo")
    public IResponse<CaseCertificateInfo> getCertificateInfo(@RequestParam("applyNo") String applyNo){
        CaseCertificateInfo caseCertificateInfo = caseCertificateInfoService.getOne(Wrappers.<CaseCertificateInfo>lambdaQuery()
                .eq(CaseCertificateInfo::getApplyNo, applyNo));
        return IResponse.success(caseCertificateInfo);
    }


    /**
     * 查询不动产登记号历史存量个数
     * @param caseApplyResidenceDto CaseApplyResidence
     * @return
     */
    @ApiOperation("查询不动产登记号历史存量个数")
    @PostMapping(value = "/countRegistrationNumber")
    public IResponse<Integer> countRegistrationNumber(@RequestBody CaseApplyResidenceDto caseApplyResidenceDto){
        log.info("查询不动产登记号历史存量个数caseApplyResidenceDto :{}", caseApplyResidenceDto);
        long count = caseApplyResidenceService.count(Wrappers.<CaseApplyResidence>lambdaQuery().eq(CaseApplyResidence::getRegistrationNumber, caseApplyResidenceDto.getRegistrationNumber()));
        return IResponse.success(count);
    }

    /**
     * residence ocr response.
     *
     * @param caseApplyResidenceDto the case driving licence dto
     * @return the response
     */
    @ApiOperation("保存调用OCR识别房产证信息")
    @PostMapping(value = "/residenceOcr")
    public IResponse<String> residenceOcr(@RequestBody CaseApplyResidenceDto caseApplyResidenceDto) {
        if (ObjectUtil.isNull(caseApplyResidenceDto)) {
            throw new AfsBaseException("房产证Ocr结果不允许为空！");
        }

        if (StringUtils.isBlank(caseApplyResidenceDto.getApplyNo())) {
            throw new AfsBaseException("房产证Ocr结果中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseApplyResidence> list = caseApplyResidenceService.list(Wrappers.<CaseApplyResidence>lambdaQuery().eq(CaseApplyResidence::getApplyNo, caseApplyResidenceDto.getApplyNo()));
        if (ObjectUtil.isNotNull(list) && !list.isEmpty()) {
            caseApplyResidenceService.remove(Wrappers.<CaseApplyResidence>lambdaQuery().eq(CaseApplyResidence::getApplyNo, caseApplyResidenceDto.getApplyNo()));
        }

        CaseApplyResidence caseApplyResidence = new CaseApplyResidence();
        log.info("复制前的caseApplyResidenceDto: {}", caseApplyResidenceDto);
        BeanUtils.copyProperties(caseApplyResidenceDto, caseApplyResidence);
        log.info("保存前的caseApplyResidence: {}", caseApplyResidence);
        boolean save = caseApplyResidenceService.save(caseApplyResidence);

        return save ? IResponse.success("") : IResponse.fail("");
    }

    /**
     * residence ocr response.
     *
     * @param certificateDto the case driving licence dto
     * @return the response
     */
    @ApiOperation("保存调用OCR识别机动车登记证书信息")
    @PostMapping(value = "/certificateOcr")
    public IResponse<String> certificateOcr(@RequestBody CertificateDto certificateDto) {
        if (ObjectUtil.isNull(certificateDto)) {
            throw new AfsBaseException("机动车登记证书Ocr结果不允许为空！");
        }

        if (StrUtil.isBlank(certificateDto.getApplyNo())) {
            throw new AfsBaseException("机动车登记证书Ocr结果中的申请编号不允许为空");
        }

        // 判断原来表中是否存在对应的数据，如果存在的话，需要先删除旧的数据
        List<CaseCertificateInfo> list = caseCertificateInfoService.list(Wrappers.<CaseCertificateInfo>lambdaQuery().eq(CaseCertificateInfo::getApplyNo, certificateDto.getApplyNo()));
        if (CollectionUtil.isNotEmpty(list)) {
            caseCertificateInfoService.remove(Wrappers.<CaseCertificateInfo>lambdaQuery().eq(CaseCertificateInfo::getApplyNo, certificateDto.getApplyNo()));
        }

        CaseCertificateInfo caseCertificateInfo = new CaseCertificateInfo();
        log.info("复制前的certificateDto: {}", certificateDto);
        BeanUtils.copyProperties(certificateDto, caseCertificateInfo, "id");
        log.info("保存前的caseCertificateInfo: {}", caseCertificateInfo);
        boolean save = caseCertificateInfoService.save(caseCertificateInfo);

        return save ? IResponse.success("") : IResponse.fail("");
    }

    @PostMapping("facePhotoAutomaticRecognition")
    @ApiOperation("面签照识别")
    public IResponse<?> facePhotoAutomaticRecognition(@RequestBody StatementAutomaticRecognition state){
        log.info("面签照识别测试入参:{}", JSON.toJSONString(state));
        // 查询是否包含面签照
        List<ComAttachmentManagement> Facelist = comAttachmentManagementService.list(
            Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getBusiNode, "orderApply")
                .eq(ComAttachmentManagement::getAttachmentName, "面签照")
        );
        List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(Facelist.get(0).getId()))
            .eq(ComAttachmentFile::getBusiNo, state.getApplyNo())
            .orderByDesc(ComAttachmentFile::getCreateTime)
        );
        List<ComAttachmentFile> idCarFile = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
            .eq(ComAttachmentFile::getBusiNo,state.getApplyNo())
            .eq(ComAttachmentFile::getAttachmentName, "承租人身份证正面")
            .orderByDesc(ComAttachmentFile::getCreateTime));
        if (CollUtil.isNotEmpty(fileList) && CollUtil.isNotEmpty(idCarFile)) {
            // 异步ds解析面签信息
            ComAttachmentFile file = fileList.get(0);
            caseBaseInfoService.facePhotoAutomaticRecognition(state.getApplyNo(), file,state.getParam());
            // 人脸对比
            facePhotoInfoService.facePhotoidentifyAndCompare(file,idCarFile.get(0),state.getApplyNo());
        }
        return IResponse.success("执行成功");
    }

    @ApiOperation("测试人脸检测")
    @PostMapping("/testFaceLocation")
    public IResponse<?> testFaceLocation(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return IResponse.fail("文件不能为空");
        }

        log.info("人脸检测测试，文件名：{}, 大小：{}", file.getOriginalFilename(), file.getSize());

        try {
            // 将MultipartFile转换为临时文件
            String tempDir = System.getProperty("java.io.tmpdir");
            File tempFile = new File(tempDir, System.currentTimeMillis() + "_" + file.getOriginalFilename());
            FileUtils.writeByteArrayToFile(tempFile, file.getBytes());

            String host = faceIdentifyConfig.getLocateUrl();
            HttpRequest request = HttpUtil.createPost(host);
            request.header("Authorization", "Bearer " + faceIdentifyConfig.getLocateKey());
            request.form("file", tempFile);
            request.setConnectionTimeout(30 * 1000);
            request.setReadTimeout(30 * 1000);

            try (HttpResponse exchange = request.execute()) {
                log.info("faceLocation-Resp={}", exchange);
                if (exchange.isOk()) {
                    JSONObject entries = JSONUtil.parseObj(exchange.body());
                    if (StrUtil.equals("200", entries.get("code").toString())) {
                        return IResponse.success(entries);
                    }
                    return IResponse.fail("人脸检测失败：" + entries.toString());
                } else {
                    log.info("faceLocation-Resp-fail");
                    return IResponse.fail("人脸检测请求失败");
                }
            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        } catch (Exception e) {
            log.error("人脸检测异常", e);
            return IResponse.fail("人脸检测异常：" + e.getMessage());
        }
    }

    @ApiOperation("根据申请编号获取直系亲属以及配偶名称")
    @PostMapping("/getImmediateFamilyMembers")
    public IResponse<List<String>> getImmediateFamilyMembers(@RequestParam String conditionApplyNo) {
        List<CaseCustContact> caseCustContacts = caseCustContactService.getBaseMapper().selectList(Wrappers.<CaseCustContact>query().lambda()
                .eq(CaseCustContact::getApplyNo, conditionApplyNo)
                .in(CaseCustContact::getCustRelation, CustRelationEnums.spouse.getIndex(),CustRelationEnums.children.getIndex(),CustRelationEnums.parent.getIndex())
                .select(CaseCustContact::getCustName));
        if (ObjectUtil.isEmpty(caseCustContacts) || caseCustContacts.isEmpty()) {
            return IResponse.success(Collections.emptyList());
        }
        List<String> nameList = caseCustContacts.stream()
                .map(CaseCustContact::getCustName)
                .toList();
        log.info("房产证查询到的直系亲属/配偶名称:{}",nameList);
        return IResponse.success(nameList);
    }

    @PostMapping("callAgainDsByStatement")
    public IResponse callAgainDsByStatement(@RequestBody StatementAutomaticRecognition statementAutomaticRecognition){
        //查询收入类材料
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo,statementAutomaticRecognition.getApplyNo()));
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                .eq(CaseCustInfo::getApplyNo,statementAutomaticRecognition.getApplyNo())
                .eq(CaseCustInfo::getCustRole,"01"));
        CaseCustInfo custInfo1 = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                .eq(CaseCustInfo::getApplyNo,statementAutomaticRecognition.getApplyNo())
                .eq(CaseCustInfo::getCustRole,"03"));
        String bankCardNo = applyContractFeign.getBankCardNo(statementAutomaticRecognition.getApplyNo(),getHeader());
        List<ComAttachmentManagement> list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode()));
        if (CollectionUtil.isNotEmpty(list)) {
            List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                    .eq(ComAttachmentFile::getBusiNo, statementAutomaticRecognition.getApplyNo()));
            if (CollectionUtil.isNotEmpty(fileList)) {
                //异步解析流水
                log.info("手动重新调用流水校验");
                for (ComAttachmentFile comAttachmentFile : fileList){
                    caseBaseInfoService.turnoverAutomaticRecognition(statementAutomaticRecognition.getApplyNo(),caseBaseInfo,custInfo,comAttachmentFile,bankCardNo,custInfo1);
                }
            }
        }
        return IResponse.success("");
    }

    @PostMapping("callAgainDsByOperating")
    public IResponse callAgainDsByOperating(@RequestBody StatementAutomaticRecognition statementAutomaticRecognition){
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo,statementAutomaticRecognition.getApplyNo()));
        List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBusiNo,statementAutomaticRecognition.getApplyNo())
                .eq(ComAttachmentFile::getId,Long.valueOf(statementAutomaticRecognition.getFileId())));
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                .eq(CaseCustInfo::getApplyNo,statementAutomaticRecognition.getApplyNo())
                .eq(CaseCustInfo::getCustRole,"01"));
        CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>lambdaQuery()
                .eq(CaseCustIndividual::getCustId,custInfo.getId()));
        CaseCustIndividualDto personDetail = new CaseCustIndividualDto();
        BeanUtils.copyProperties(caseCustIndividual,personDetail);
        log.info("手动重新调用经营许可证校验");
        caseBaseInfoService.businessAutomaticRecognition(statementAutomaticRecognition.getApplyNo(),caseBaseInfo,fileList,personDetail);
        return IResponse.success("");
    }


    @ApiOperation("手动重新调用驾驶证ds解析")
    @PostMapping("/callAgainDsByDriver")
    public IResponse callAgainDsByDriver(@RequestBody DeepSeekDrivingInfo condition){
        String key = "ds:try:driver:" + condition.getApplyNo();
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, "lock", 20, TimeUnit.MINUTES);
        if (lock != null && lock) {
            try {
                //驾驶证解析
                List<ComAttachmentManagement> list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));
                if (list.size() > 0) {
                    List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                            .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo()));
                    if (fileList.size() > 0) {
                        log.info("手动重新调用驾驶证ds解析");
                        for (ComAttachmentFile comAttachmentFile : fileList) {
                            caseBaseInfoService.driverRecognitionSame(condition.getApplyNo(), comAttachmentFile);
                        }
                    }
                }
            } finally {
                redisTemplate.delete(key);
            }
        } else {
            return IResponse.fail("正在执行中，请不要重复调用");
        }
        return IResponse.success("");
    }

    @ApiOperation("手动重新调用营运人证ds解析")
    @PostMapping("/callAgainDsByOperator")
    public IResponse callAgainDsByOperator(@RequestBody DeepSeekOperatorInfo condition){
        String key = "ds:try:operator:" + condition.getApplyNo();
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, "lock", 20, TimeUnit.MINUTES);
        if (lock != null && lock) {
            try {
                //营运人证解析
                List<ComAttachmentManagement> list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.OPERATOR_CERTIFICATE.getCode()));
                if (list.size() > 0) {
                    List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                            .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo()));
                    if (fileList.size() > 0) {
                        log.info("手动重新调用营运人证ds解析");
                        for (ComAttachmentFile comAttachmentFile : fileList) {
                            caseBaseInfoService.operatorDsRecognitionSame(condition.getApplyNo(), comAttachmentFile);
                        }
                    }
                }
            } finally {
                redisTemplate.delete(key);
            }
        } else {
            return IResponse.fail("正在执行中，请不要重复调用");
        }
        return IResponse.success("");
    }

    @ApiOperation("初审智能化识别结果查询")
    @PostMapping("/listApproveIntelligentResult")
    public IResponse listApproveIntelligentResult(@RequestBody CaseIntelligentResultVO dto) {
        List<CaseIntelligentResult> list = intelligentResultService.lambdaQuery()
            .eq(CaseIntelligentResult::getApplyNo, dto.getApplyNo())
            .orderByDesc(CaseIntelligentResult::getCreateTime)
            .list();
        if (CollUtil.isEmpty(list)) {
            return IResponse.success("");
        }
        Set<String> codeList = new HashSet<>();
        CaseIntelligentResult intelligent = list.get(0);
        // 如果资料不完整，展示实时的资料状态
        if (ObjectUtil.equals(intelligent.getFacePhotoComplete(), false) ||
            ObjectUtil.equals(intelligent.getBusinessLicenseComplete(), false) ||
            ObjectUtil.equals(intelligent.getOperatingLicenseComplete(), false) ||
            ObjectUtil.equals(intelligent.getDriverLicenseComplete(), false) ||
            ObjectUtil.equals(intelligent.getBankStatementComplete(), false) ||
            ObjectUtil.equals(intelligent.getOperatorCertificateComplete(), false)) {
            long startTime = System.currentTimeMillis();
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo, dto.getApplyNo()));
            CaseIntelligentResult caseIntelligentResult = automaticCallback.intelligentAttachmentRecognition(caseBaseInfo,
                new AtomicBoolean(true), false,false);
            long endTime = System.currentTimeMillis();
            log.info("初审智能化识别结果耗时:{}", endTime - startTime);
            intelligent = caseIntelligentResult;
        }
        List<CaseIntelligentResultVO> resultVOS = new ArrayList<>();
        // 智能识别附件类型
        List<CaseConfParam> caseConfParamList = caseConfParamService.list(Wrappers.<CaseConfParam>lambdaQuery()
            .eq(CaseConfParam::getParamLogo, CaseConstants.CREDIT_AUTO_APPROVES)
            .eq(CaseConfParam::getParamStatus, "yes"));
        Map<String, List<CaseConfParam>> stringListMap = caseConfParamList.stream()
            .collect(Collectors.groupingBy(CaseConfParam::getParamValue));
        CaseIntelligentResult finalIntelligent = intelligent;
        stringListMap.forEach((key, val) -> {
            List<String> code = val.stream().map(CaseConfParam::getParamType).toList();
            boolean anyMatchFacePhoto = code.stream()
                .anyMatch(str -> str.equals(InterfaceIdentifyEnum.INTERFACE_FACE_PHOTO.getCode()));
            List<ComAttachmentManagement> managementList = comAttachmentManagementService.list(
                Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .in(ComAttachmentManagement::getUniqueCode, code));
            if (anyMatchFacePhoto) {
                List<ComAttachmentManagement> management = comAttachmentManagementService.list(
                    Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                        .eq(ComAttachmentManagement::getBusiNode, "orderApply"));
                managementList.addAll(management);
            }
            InterfaceIdentifyEnum interfaceIdentifyEnum = InterfaceIdentifyEnum.create(key);
            // 身份证
            if (interfaceIdentifyEnum == InterfaceIdentifyEnum.INTERFACE_IDENTITY_CARD) {
                if (finalIntelligent.getGuarantorIdCard() != null) {
                    List<ComAttachmentManagement> guarantorIdCard = managementList.stream()
                        .filter(s -> StrUtil.equals(s.getUniqueCode(),
                            AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode()))
                        .toList();
                    CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
                    intelligentResultVO.setAttachmentCode(String.valueOf(guarantorIdCard.get(0).getId()));
                    codeList.add(String.valueOf(guarantorIdCard.get(0).getId()));
                    Boolean result = finalIntelligent.getGuarantorIdCard();
                    intelligentResultVO.setResult(result ? "1" : "0");
                    resultVOS.add(intelligentResultVO);
                }
                if (finalIntelligent.getGuarantorIdCardBack() != null) {
                    List<ComAttachmentManagement> guarantorIdCard = managementList.stream()
                        .filter(s -> StrUtil.equals(s.getUniqueCode(),
                            AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode()))
                        .toList();
                    CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
                    intelligentResultVO.setAttachmentCode(String.valueOf(guarantorIdCard.get(0).getId()));
                    codeList.add(String.valueOf(guarantorIdCard.get(0).getId()));
                    Boolean result = finalIntelligent.getGuarantorIdCardBack();
                    intelligentResultVO.setResult(result ? "1" : "0");
                    resultVOS.add(intelligentResultVO);
                }
                List<ComAttachmentManagement> mainIdCard = managementList.stream()
                    .filter(s -> StrUtil.equals(s.getUniqueCode(),
                        AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode()))
                    .toList();
                CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
                intelligentResultVO.setAttachmentCode(String.valueOf(mainIdCard.get(0).getId()));
                codeList.add(String.valueOf(mainIdCard.get(0).getId()));
                Boolean result = finalIntelligent.getIdCard();
                intelligentResultVO.setResult(result ? "1" : "0");
                resultVOS.add(intelligentResultVO);

                List<ComAttachmentManagement> mainIdCardBack = managementList.stream()
                    .filter(s -> StrUtil.equals(s.getUniqueCode(),
                        AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode()))
                    .toList();
                CaseIntelligentResultVO intelligentResultBack = new CaseIntelligentResultVO();
                intelligentResultBack.setAttachmentCode(String.valueOf(mainIdCardBack.get(0).getId()));
                codeList.add(String.valueOf(mainIdCardBack.get(0).getId()));
                Boolean resultBack = finalIntelligent.getIdCardBack();
                if (resultBack == null) {
                    // 历史订单没有区分，取正面
                    resultBack = result;
                }
                intelligentResultBack.setResult(resultBack ? "1" : "0");
                resultVOS.add(intelligentResultBack);
            } else {
                for (ComAttachmentManagement management : managementList) {
                    CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
                    intelligentResultVO.setAttachmentCode(String.valueOf(management.getId()));
                    String result = null;
                    switch (interfaceIdentifyEnum) {
                        case INTERFACE_CURRENT_ACCOUNT:
                            result = finalIntelligent.getBankStatement();
                            break;
                        case INTERFACE_DRIVER_LICENSE:
                            result = finalIntelligent.getDriverLicense();
                            break;
                        case INTERFACE_OPERATOR_CERTIFICATE:
                            result = finalIntelligent.getOperatorCertificate();
                            break;
                        case INTERFACE_FACE_PHOTO:
                            result = finalIntelligent.getFacePhoto();
                            break;
                        case BUSINESS_LICENSE:
                            result = finalIntelligent.getBusinessLicense();
                            break;
                        case OPERATING_LICENSE:
                            result = finalIntelligent.getOperatingLicense();
                            break;
                        default:
                    }
                    if (result != null) {
                        // 配置项存在，有识别结果才排除，历史单没有识别结果取x
                        // 之前只有1 0 直接展示结果 现在有2不合格跳过 3合格跳过
                        codeList.add(String.valueOf(management.getId()));
                        intelligentResultVO.setResult(List.of("1","3").contains(result) ? "1" : "0");
                        resultVOS.add(intelligentResultVO);
                    }
                }
            }
        });

        // 排除文件默认正确
        List<CaseConfParam> caseConfParamNotList = caseConfParamService.list(Wrappers.<CaseConfParam>lambdaQuery()
            .eq(CaseConfParam::getParamLogo, CaseConstants.CREDIT_AUTO_APPROVES_NOT)
            .eq(CaseConfParam::getParamValue, "true")
            .eq(CaseConfParam::getParamStatus, "yes"));
        List<String> codeNot = caseConfParamNotList.stream().map(CaseConfParam::getParamType).toList();
        List<ComAttachmentManagement> managementNotList = comAttachmentManagementService.list(
            Wrappers.<ComAttachmentManagement>lambdaQuery().in(ComAttachmentManagement::getAttachmentName, codeNot));
        for (ComAttachmentManagement notId : managementNotList) {
            CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
            String attachmentCode = String.valueOf(notId.getId());
            intelligentResultVO.setAttachmentCode(attachmentCode);
            codeList.add(attachmentCode);
            intelligentResultVO.setResult("1");
            resultVOS.add(intelligentResultVO);
        }
        // 不涉及的文件需要人工检查
        List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
            .select(ComAttachmentFile::getId, ComAttachmentFile::getAttachmentCode)
            .eq(ComAttachmentFile::getBusiNo, dto.getApplyNo()).eq(ComAttachmentFile::getBelongNo, dto.getApplyNo()));
        List<String> attachmentCodeList = fileList.stream()
            .map(ComAttachmentFile::getAttachmentCode)
            .distinct()
            .filter(s -> !codeList.contains(s))
            .toList();
        if(CollUtil.isNotEmpty(attachmentCodeList)){
            for (String code : attachmentCodeList) {
                CaseIntelligentResultVO intelligentResultVO = new CaseIntelligentResultVO();
                intelligentResultVO.setAttachmentCode(code);
                intelligentResultVO.setResult("0");
                resultVOS.add(intelligentResultVO);
            }
        }
        return IResponse.success(resultVOS);
    }
    /**
     * 推送ds识别结果
     * @param dsResultVo
     */
    @ApiOperation("接收ds识别结果")
    @PostMapping(value = "/pushDsResult2Case")
    public void pushDsResult2Case(@RequestBody DsResultVo dsResultVo) {
        log.info("接收ds识别结果:{}", dsResultVo);
        String applyNo = dsResultVo.getApplyNo();
        if (StrUtil.isBlank(applyNo)) {
            throw new AfsBaseException("申请编号不允许为空！");
        }
        List<CaseFacePhotoInfo> caseFacePhotoInfoList = dsResultVo.getCaseFacePhotoInfoList();
        if (CollUtil.isNotEmpty(caseFacePhotoInfoList)) {
            boolean saveOrUpdateBatch = facePhotoInfoService.saveOrUpdateBatch(GenericVoConverter.convertList(caseFacePhotoInfoList, com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseFacePhotoInfo::new));
            log.info("保存人脸识别结果:{}", saveOrUpdateBatch);
        }
        List<com.ruicar.afs.cloud.deepseek.entity.DeepSeekDrivingInfo> deepSeekDrivingInfoList = dsResultVo.getDeepSeekDrivingInfoList();
        if (CollUtil.isNotEmpty(deepSeekDrivingInfoList)) {
            boolean saveOrUpdateBatch = caseDrivingLicenceInfoService.saveOrUpdateBatch(GenericVoConverter.convertList(deepSeekDrivingInfoList, CaseDrivingLicenceInfo::new));
            log.info("保存驾驶证识别结果:{}", saveOrUpdateBatch);
        }
        List<StatementAutomaticRecognition> statementAutomaticRecognitionList = dsResultVo.getStatementAutomaticRecognitionList();
        if (CollUtil.isNotEmpty(statementAutomaticRecognitionList)) {
            boolean saveOrUpdateBatch = statementAutomaticRecognitionService.saveOrUpdateBatch(GenericVoConverter.convertList(statementAutomaticRecognitionList, StatementAutomaticRecognition::new));
            log.info("保存流水识别结果:{}", saveOrUpdateBatch);
        }
    }

    /**
     * 获取字典
     * @param dicKey
     * @return
     */
    @ApiOperation("获取字典")
    @PostMapping(value = "/getDicMapsByDicKey")
    public IResponse getDicMaps(@RequestParam(value = "dicKey") String dicKey) {
        return IResponse.success(DicHelper.getDicMaps(dicKey).get(dicKey));
    }
    /**
     * 获取 header
     * @return
     */
    private Map<String, String> getHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }
}
