package com.ruicar.afs.cloud.afscase.mq.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.mq.condition.AffiliatedCompanyFilingCondition;
import com.ruicar.afs.cloud.afscase.mq.entity.AffiliatedCompanyFiling;
import com.ruicar.afs.cloud.afscase.mq.mapper.AffiliatedCompanyFilingMapper;
import com.ruicar.afs.cloud.afscase.mq.service.AffiliatedCompanyFilingService;
import com.ruicar.afs.cloud.afscase.mq.vo.AffiliatedCompanyFilingVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class AffiliatedCompanyFilingServiceImpl extends ServiceImpl<AffiliatedCompanyFilingMapper, AffiliatedCompanyFiling> implements AffiliatedCompanyFilingService {
    @Override
    public IPage<List<AffiliatedCompanyFilingVo>> queryApproveTaskLaunchList(Page page, AffiliatedCompanyFilingCondition companyFilingCondition) {
        return this.baseMapper.queryApproveTaskLaunchList(page,companyFilingCondition);
    }
}
