package com.ruicar.afs.cloud.afscase.mq.sender;

import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CallSubmitInfo;
import com.ruicar.afs.cloud.common.mq.rabbit.anno.send.AfsRabbitMqClient;
import com.ruicar.afs.cloud.common.mq.rabbit.anno.send.AfsRabbitMqSender;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;

@AfsRabbitMqClient
public interface CaseToModelSender {
    /**
     * 推送通话记录到质检系统
     * @param entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.model}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendCaseToModelCall(AfsTransEntity<CallSubmitInfo> entity);
}
