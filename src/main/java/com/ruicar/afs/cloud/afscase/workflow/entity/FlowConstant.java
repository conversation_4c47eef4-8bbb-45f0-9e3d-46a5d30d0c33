package com.ruicar.afs.cloud.afscase.workflow.entity;

public class FlowConstant {

    public static String BUSINESS_NO = "business_no";

    public static String CONTRACT_NO = "contract_no";

    public static String LAST_OPERATION = "last_operation";

    public static String LAST_APPROVE_REASON = "last_approve_reason";

    public static String LAST_APPROVE_REMARK = "last_approve_remark";

    public static String LAST_OPERATOR_LOGIN_NAME = "last_operator_login_name";


    /**
     * 原子属性
     */
    public static String BIZ_OPERATION_TYPE = "bizOperationType";

    /**
     * 放款自动审核 - 原子属性
     */
    public static String LOAN_AUTO_APPROVE_TYPE = "loanAutoApproveType";

    /**
     * 银行放款状态
     */
    public static String BANK_LOAN_STATUS = "bankLoanStatus";

    public static String LOAN_AMT = "loanAmt";

    public static String IS_REGULAR = "isRegular";

    public static String CREDIT_AMT_LIMIT = "creditAmtLimit";

    /**
     * 审核人
     */
    public static String APPROVAL_USER ="approval_user";
    /**
     * 审核意见
     */
    public static String APPROVAL_OPINION = "approval_opinion";
    /**
     * 节点名称
     */
    public static String TASK_NODE_NAME = "task_node_name";
    /**
     * 发起人
     */
    public static String FLOW_START_USER = "planFlowStartUser";
    /**
     * 客户经理ID
     */
    public static String FLOW_SALES_USER_ID = "flow_sales_user_id";

    public static String FLOW_SALES_TEAM_FINISH = "flow_sales_team_finish";
    /**
     * 放款补件对应订单id
     */
    public static String PASS_ID = "pass_id";

    /**
     * 团队id (内部使用)
     */
    public static String FLOW_SALES_TEAM_ID = "flow_sales_team_id";

    /**
     * 金额是否符合提交
     */
    public static String IS_PASS = "isPass";

    /**
     * 是否开启先付后票
     */
    public static String IS_ADVANCE_PAY = "isAdvancePay";

    /**
     * 需要视频面审流程变量
     */
    public static final String RISK_CHECK="riskCheck";

    /**
     * 所属资方
     */
    public static final String BELONGING_CAPITAL="belongingCapital";
    /**
     * 是否信鸽退回，true自动退回经销商
     */
    public static final String IS_CARRIER_PIGEON_BACK="isCarrierPigeonBack";

    public static String FILING_TYPE = "filingType";

    public static String BACK_NODE_ID = "backNodeId";

    public static String FLOW_BACK_OPTION = "flowBackOption";

    /**
     * 业务id
     */
    public static String BUSINESS_ID ="business_id";

    /**
     * 业务类型
     */
    public static String BUSINESS_TYPE ="business_type";

}
