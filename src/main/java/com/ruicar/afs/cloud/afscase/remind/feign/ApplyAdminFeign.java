package com.ruicar.afs.cloud.afscase.remind.feign;


import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.wx.message.vo.UserWechatDeviceVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "${com.ruicar.service-names.apply-admin-server}",contextId = "wechat-feign")
public interface ApplyAdminFeign {


     /**
      * 根据unionId查询用户信息
      * @param userName
      * @return
      */
     @ApiOperation("查询（生成）签约人列表")
     @PostMapping("/autoLogin/getUserWechatDevice")
     IResponse<UserWechatDeviceVo> getUserWechatDevice(@RequestParam("userName") String userName);
}
