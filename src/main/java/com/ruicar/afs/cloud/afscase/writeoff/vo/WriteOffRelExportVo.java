package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WriteOffRelExportVo {

    @ExcelProperty(value = "流程编号")
    private String approveId;
    @ExcelProperty(value = "服务费核销项编号")
    private String applyNo;
    @ExcelProperty(value = "业务模式")
    private String writeOffType;
    /**
     * 账期
     */
    @ExcelProperty(value = "核销期数")
    private String writeOffMonth;
    /**
     * 经销商编号
     */
    @ExcelProperty(value = "经销商代码")
    private String dealerCode;
    /**
     * 经销商名称
     */
    @ExcelProperty(value = "经销商名称")
    private String dealerName;
    /**
     * 收款账号
     */
    @ExcelProperty(value = "收款账号")
    private String payReceiveAccount;
    /**
     * 提取金额
     */
    @ExcelProperty(value = "提取金额")
    private BigDecimal invoiceAmount;
    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态")
    private String approveStatus;
    /**
     * 付款状态
     */
    @ExcelProperty(value = "付款状态")
    private String payStatus;
}
