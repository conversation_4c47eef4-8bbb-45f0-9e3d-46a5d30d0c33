package com.ruicar.afs.cloud.afscase.approvetask.enums;

public enum FilingNodeEnum {
    /**
     * First node approve node enum. 信审岗
     */
    FIRST_NODE("FIRST_NODE", "BA001"),
    /**
     * Second node approve node enum.放款岗
     */
    SECOND_NODE("SECOND_NODE", "BA002"),
    /**
     * end node approve node enum.租后岗
     */
    THIRD_NODE("THIRD_NODE", "BA003"),
    /**
     * funds node approve node enum.档案岗
     */
    FOURTH_NODE("FOURTH_NODE", "BA004")
    ;

    FilingNodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    /**
     * Gets code.
     *
     * @return the code
     */
    public String getCode() {
        return code;
    }

    /**
     * Gets desc.
     *
     * @return the desc
     */
    public String getDesc() {
        return desc;
    }

    public static FilingNodeEnum fromString(String codeString) {
        for (FilingNodeEnum enums : FilingNodeEnum.values()) {
            if (enums.code.equalsIgnoreCase(codeString)) {
                return enums;
            }
        }
        return null;
    }
}
