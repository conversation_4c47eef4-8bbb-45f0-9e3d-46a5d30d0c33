package com.ruicar.afs.cloud.afscase.loanpreliminaryreview.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.dto.ChannelGroupCheckVO;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.ApproveMakeLabel;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.ApproveMakeLabelService;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.CaseLiangfuApply;
import com.ruicar.afs.cloud.afscase.autoaudit.service.CaseLiangfuApplyService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsAddress;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsApply;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsDevice;
import com.ruicar.afs.cloud.afscase.cargpsmanage.mapper.CarGpsApplyMapper;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsAddressService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsApplyService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsDeviceService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.vo.CarGpsApplyVO;
import com.ruicar.afs.cloud.afscase.channel.common.Constants;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAffiliatedUnits;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAffiliatedUnitsService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CaseInfoQueryCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.ApplyGuarantee;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarStyleDetail;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseReceiptInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseRedundantInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseRemarkRecord;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarStyleDetailService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustIndividualService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseReceiptInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseRedundantInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseRemarkRecordService;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CarInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.MainBaseInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.RedundantSignVo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInsuranceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.LoanBankCardInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInsuranceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanBankCardInfoService;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.entity.DecisionEngineResultsInfo;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.enums.GpsSuppEnum;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.service.DecisionEngineResultsInfoService;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.service.LoanSummaryInfoService;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.vo.ExamineAssetPageInfoVo;
import com.ruicar.afs.cloud.afscase.loanpreliminaryreview.vo.OutlineVo;
import com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.entity.LoanSpecialBusinessInfo;
import com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.service.LoanSpecialBusinessInfoService;
import com.ruicar.afs.cloud.afscase.option.entity.CaseCreditOption;
import com.ruicar.afs.cloud.afscase.option.service.CaseCreditOptionService;
import com.ruicar.afs.cloud.afscase.remind.condition.RemindData;
import com.ruicar.afs.cloud.afscase.remind.entity.CaseRemindDetail;
import com.ruicar.afs.cloud.afscase.remind.service.RemindService;
import com.ruicar.afs.cloud.afscase.risk.enums.RemindEditEnum;
import com.ruicar.afs.cloud.afscase.risk.service.ThirdDataService;
import com.ruicar.afs.cloud.afscase.vehicle.service.VehicleInfoService;
import com.ruicar.afs.cloud.basic.api.dto.ApplyNoDTO;
import com.ruicar.afs.cloud.basic.api.dto.CarTypeVo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyFinancingItemsService;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.dto.ResponseCarModelScreeningDTO;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.dto.ResponseInvoiceVerificationDTO;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.enums.ApplyCarInvoiceEnum;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.enums.IsEqualEnum;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.service.InvoiceService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AddressTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BankCardSigningTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CancelStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsDefaultDeductCardEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanModelEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindPowerEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RemindTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractFinancingItemsEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.VerStatusEnum;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.parameter.commom.enums.CostType;
import com.ruicar.afs.cloud.parameter.commom.enums.CustType;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import com.ruicar.afs.cloud.seats.service.UserCollocationService;
import com.ruicar.afs.cloud.seats.util.DateUtil;
import com.ruicar.afs.cloud.vehicle.vo.VehicleModelNewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.parameter.commom.enums.InsuranceTypeEnums.BUSINESS;


/**
 * <p>Description:放款审核待办任务相关</p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @CreateDate 2020-5-29
 */

@Slf4j
@RestController
@AllArgsConstructor
@Api("放款初审明细信息")
@RequestMapping("/loanSummaryInfo")
public class LoanSummaryInfoController {

    private CaseBaseInfoService caseBaseInfoService;
    private LoanBankCardInfoService loanBankCardInfoService;

    private CaseCarInfoService caseCarInfoService;

    private CaseCostInfoService caseCostInfoService;

    private ChannelRiskInfoService channelRiskInfoService;

    private ChannelAffiliatedUnitsService channelAffiliatedUnitsService;

    private CaseChannelInfoService caseChannelInfoService;

    private ChannelBaseInfoService channelBaseInfoService;

    private AddressService addressService;

    private final CaseCustInfoService caseCustInfoService;

    private LoanSummaryInfoService loanSummaryInfoService;
    private CaseLiangfuApplyService caseLiangfuApplyService;

    private ApproveMakeLabelService approveMakeLabelService;
    private final CaseRedundantInfoService redundantInfoService;
    private final CaseEnterpriseCustomerDetailsService caseEnterpriseCustomerDetailsService;
    private final CaseCustIndividualService caseCustIndividualService;
    private final CaseCustAddressService caseCustAddressService;


    private CarGpsApplyMapper carGpsApplyMapper;
    private CarGpsApplyService carGpsApplyService;

    private final InvoiceService invoiceService;
    private final CarInvoiceInfoService carInvoiceInfoService;

    private ApplyFinancingItemsService applyFinancingItemsService;
    private CaseContractInfoService caseContractInfoService;
    private CaseCustContactService caseCustContactService;
    private CaseChannelUniteInfoService caseChannelUniteInfoService;
    private CarInsuranceInfoService carInsuranceInfoService;
    private CarGpsAddressService carGpsAddressService;
    private CaseReceiptInfoService caseReceiptInfoService;
    private final CaseCreditOptionService caseCreditOptionService;
    private final LoanSpecialBusinessInfoService specialBusinessInfoService;
    private final CaseRemarkRecordService caseRemarkRecordService;
    private final VehicleInfoService vehicleInfoService;
    private CaseCarStyleDetailService carStyleDetailInfoService;
    private final CarGpsDeviceService carGpsDeviceService;
    @Autowired
    private UserDetailsInfoFeign userDetailsInfoFeign;
    private RemindService remindService;
    private ApplyServiceFeign applyServiceFeign;
    private ApplyContractFeign applyContractFeign;
    private final ApplyConfig applyConfig;
    private UserCollocationService userCollocationService;
    private CaseApproveRecordService caseApproveRecordService;
    private final ThirdDataService thirdDataService;

    private DecisionEngineResultsInfoService decisionEngineResultsInfoService;

    /**
     * queryAssetSummaryInfo
     * <p>Description: </p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryExamineAssetSummaryInfo")
    @ApiOperation(value = "根据申请编号查询资产基本信息和挂靠信息")
    public IResponse<ExamineAssetPageInfoVo> queryExamineAssetSummaryInfo(@RequestParam("applyNo") String applyNo) {
        IResponse iResponse = new IResponse();
        ExamineAssetPageInfoVo examineAssetPageInfoVo = new ExamineAssetPageInfoVo();
        OutlineVo outlineVo = new OutlineVo();
        //概要信息开始
        outlineVo.setApplyNo(applyNo);
        FinCostDetails finCostInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, applyNo)
                .eq(FinCostDetails::getCostType, CostTypeEnum.CARAMT.getCode())
        );
        //首付金额
        examineAssetPageInfoVo.setDownPayAmt(null != finCostInfo ? finCostInfo.getDownPayAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setFirmMarginAmount(null != finCostInfo ? finCostInfo.getFirmMarginAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setCustMarginAmount(null != finCostInfo ? finCostInfo.getCustMarginAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setFirmHandlingAmount(null != finCostInfo ? finCostInfo.getFirmHandlingAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setCustHandlingAmount(null != finCostInfo ? finCostInfo.getCustHandlingAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setFirmServiceAmount(null != finCostInfo ? finCostInfo.getFirmServiceAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setCustServiceAmount(null != finCostInfo ? finCostInfo.getCustServiceAmt() : BigDecimal.ZERO);
        examineAssetPageInfoVo.setDiscountAmount(null != finCostInfo ? finCostInfo.getDiscountAmt() : BigDecimal.ZERO);
        //融资额
        outlineVo.setLoanAmt(null != finCostInfo ? finCostInfo.getLoanAmt() : BigDecimal.ZERO);
        outlineVo.setLoanTerm(null != finCostInfo ? finCostInfo.getLoanTerm() : 0);
        outlineVo.setDownPayScale(null != finCostInfo ? finCostInfo.getDownPayScale() : BigDecimal.valueOf(0));
        outlineVo.setTotalRent(null != finCostInfo ? finCostInfo.getMonthPayAmt() : BigDecimal.valueOf(0));
        //风险融资额
        examineAssetPageInfoVo.setRiskLoanAmt(null != finCostInfo && (ObjectUtils.isNotEmpty(finCostInfo.getRiskLoanAmt())) ? finCostInfo.getRiskLoanAmt() : BigDecimal.valueOf(0));
        //保证金
        examineAssetPageInfoVo.setMarginAmount(null != finCostInfo && (ObjectUtils.isNotEmpty(finCostInfo.getMarginAmount())) ? finCostInfo.getMarginAmount() : BigDecimal.ZERO);
        //手续费
        examineAssetPageInfoVo.setHandlingFeeAmount(null != finCostInfo && (ObjectUtils.isNotEmpty(finCostInfo.getHandlingFeeAmount())) ? finCostInfo.getHandlingFeeAmount() : BigDecimal.valueOf(0));

        //购置税
        FinFinancingItems finFinancingItems = applyFinancingItemsService.getOne(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo)
                .eq(FinFinancingItems::getFinanceItemCode, AfsEnumUtil.key(ContractFinancingItemsEnum.PURCHASE_TAX)));
        if (null != finFinancingItems) {
            examineAssetPageInfoVo.setFinanceItemAmt(null != finFinancingItems ? finFinancingItems.getFinanceItemAmt() : BigDecimal.ZERO);
        }
        FinFinancingItems items = new FinFinancingItems();
        // 商业险融资额
        FinFinancingItems bisnessAmtItems = applyFinancingItemsService.getOne(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo)
                .eq(FinFinancingItems::getFinanceItemCode, CostType.SYXAMT.getIndex()));
        items.setBisnessAmt(BigDecimal.ZERO);
        items.setCompulsoryAmt(BigDecimal.ZERO);
        if (bisnessAmtItems != null) {
            items.setBisnessAmt(bisnessAmtItems == null ? BigDecimal.ZERO : bisnessAmtItems.getFinanceItemAmt());
        }
        // 交强险融资额
        FinFinancingItems compulsoryAmtItems = applyFinancingItemsService.getOne(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo)
                .eq(FinFinancingItems::getFinanceItemCode, CostType.JQXAMT.getIndex()));
        if (compulsoryAmtItems != null) {
            items.setCompulsoryAmt(compulsoryAmtItems == null ? BigDecimal.ZERO : compulsoryAmtItems.getFinanceItemAmt());
        }
        examineAssetPageInfoVo.setFinanceItem(items);
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        /** 获取合同号*/
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                .eq(CaseContractInfo::getApplyNo, applyNo));
        outlineVo.setIsDataPostStatus(caseContractInfo.getIsDataPostStatus());
        outlineVo.setLastGenerationDate(caseContractInfo != null ? caseContractInfo.getLastGenerationDate() : null);
        outlineVo.setContractNo(null != caseContractInfo ? caseContractInfo.getContractNo() : "");
        outlineVo.setLoanRemarks(null != caseContractInfo && null != caseContractInfo.getRemarks() ? caseContractInfo.getRemarks() : "");
        /**是否先抵押后放款 **/
        outlineVo.setLendingMode("否");
        outlineVo.setSignType(StringUtils.isNotBlank(caseContractInfo.getSignType()) ? caseContractInfo.getSignType() : null);
        List<CaseRemarkRecord> loanList = caseRemarkRecordService.list(Wrappers.<CaseRemarkRecord>query().lambda()
                .eq(ObjectUtils.isNotEmpty(caseContractInfo), CaseRemarkRecord::getApplyNo, caseContractInfo.getContractNo()));
        List<String> collect = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(loanList)) {
            if (loanList.size() > 0) {
                collect = loanList.stream().map(s -> "【备注】 " + s.getOldRemarks() + " 【提交时间】 " + (ObjectUtils.isNotEmpty(s.getSubmitTime()) ? DateUtil.formatDate(s.getSubmitTime()) : "")).collect(Collectors.toList());
            }
        }
        outlineVo.setLoanRemarkList(collect);
        if (null != caseContractInfo && null != caseContractInfo.getLendingMode()) {
            if (caseContractInfo.getLendingMode().equals(AfsEnumUtil.key(LoanModelEnum.afterMortgage))) {
                outlineVo.setLendingMode("是");
            }
        }
        /**特殊业务*/
        List<LoanSpecialBusinessInfo> specialBusinessInfoList = specialBusinessInfoService.list(Wrappers.<LoanSpecialBusinessInfo>query().lambda()
                .eq(LoanSpecialBusinessInfo::getApplyNo, applyNo)
        );
        outlineVo.setSpecialBusinessInfoList(specialBusinessInfoList);
        /**是否锁定*/
        outlineVo.setIsLock(caseContractInfo.getIsLock());
        /**联合方信息 **/
        CaseChannelUniteInfo caseChannelUniteInfo =
                caseChannelUniteInfoService.getOne(Wrappers.<CaseChannelUniteInfo>lambdaQuery().eq(CaseChannelUniteInfo::getApplyNo, applyNo));
        outlineVo.setUniteName(null != caseChannelUniteInfo ? caseChannelUniteInfo.getUniteName() : "");
        boolean affixed = true;
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, applyNo));

        outlineVo.setRemarks(null != caseBaseInfo && null != caseBaseInfo.getRemarks() ? caseBaseInfo.getRemarks() : "");
        outlineVo.setAutoPass(caseBaseInfo.getAutoPass().equals(AfsEnumUtil.key(WhetherEnum.NO)) ? "否" : "是");
        List<CaseRemarkRecord> remarklist = caseRemarkRecordService.list(Wrappers.<CaseRemarkRecord>query().lambda()
                .eq(ObjectUtils.isNotEmpty(caseBaseInfo), CaseRemarkRecord::getApplyNo, caseBaseInfo.getApplyNo()));
        List<String> remarkString = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(remarklist)) {
            if (remarklist.size() > 0) {
                remarkString = remarklist.stream().map(s -> "【备注】 " + s.getOldRemarks() + " 【提交时间】 " + (ObjectUtils.isNotEmpty(s.getSubmitTime()) ? DateUtil.formatDate(s.getSubmitTime()) : "")).collect(Collectors.toList());
            }
        }
        outlineVo.setRemarkList(remarkString);

        // 决策引擎规则编码
        List<DecisionEngineResultsInfo> decisionEngineResultsInfoList = decisionEngineResultsInfoService.list(Wrappers.<DecisionEngineResultsInfo>query().lambda()
                .eq(DecisionEngineResultsInfo::getApplyNo, applyNo)
                .orderByDesc(DecisionEngineResultsInfo::getCreateTime));
        if(ObjectUtils.isNotEmpty(decisionEngineResultsInfoList)) {

            // 是否赋强公证
            String enforceableNotarizationStatus = "1".equals(decisionEngineResultsInfoList.get(0).getEnforceableNotarizationStatus()) ? "是" :
                    ("0".equals(decisionEngineResultsInfoList.get(0).getEnforceableNotarizationStatus()) ? "否" : "");
            outlineVo.setEnforceableNotarizationStatus(enforceableNotarizationStatus);
        }


        //如果是企业单
        if (caseBaseInfo != null) {
            outlineVo.setPassFirstDate(caseBaseInfo.getPassFirstDate());
            if (caseBaseInfo.getInputType().equals(CustType.COMPANY.getIndex())) {
                CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda().eq(CaseEnterpriseCustomerDetails::getCustId, caseCustInfo.getId()));
                outlineVo.setCustName(null != details ? details.getEnterpriseName() : "");
            } else {
                outlineVo.setCustName(null != caseCustInfo ? caseCustInfo.getCustName() : "");
            }
        }
        List<CaseCarInfo> list = caseCarInfoService.list(Wrappers.<CaseCarInfo>lambdaQuery()
                .eq(CaseCarInfo::getApplyNo, applyNo));
        if (caseBaseInfo != null) {
            outlineVo.setIfJointLease(caseBaseInfo.getInputType().equals(CustType.COMPANY.getIndex()) || Constants.IF_PERSONAL_TO_ENTERPRISE.equals(caseBaseInfo.getIfPersonalToEnterprise()) ? "否" : (caseBaseInfo.getInputType().equals(CustType.PERSON.getIndex()) ? "是" : ""));
        }

        // TOP20经销商
        if(StringUtils.isNotEmpty(caseContractInfo.getIsTopChannel())){
            outlineVo.setIsTopChannel(caseContractInfo.getIsTopChannel());
            if(CaseConstants.YES.equals(caseContractInfo.getIsTopChannel())){
                outlineVo.setIsTopChannelName("是");
            }else{
                outlineVo.setIsTopChannelName("否");
            }
        }else{
            boolean topChannelByApplyNo = thirdDataService.isTopChannelByApplyNo(applyNo);
            if(topChannelByApplyNo){
                outlineVo.setIsTopChannel(CaseConstants.YES);
                outlineVo.setIsTopChannelName("是");
            }else{
                outlineVo.setIsTopChannel(CaseConstants.NO);
                outlineVo.setIsTopChannelName("否");
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            /**  上牌地  **/
            String province = addressService.getLabelByCode(list.get(0).getLicenseProvince());
            String city = addressService.getLabelByCode(list.get(0).getLicenseCity());
            outlineVo.setPlateAddress(province + city);
            CaseCarInfo caseCarInfo = list.get(0);
            examineAssetPageInfoVo.setCooperPlat(caseCarInfo.getCooperPlat());
            examineAssetPageInfoVo.setIsNominal(caseCarInfo.getIsNominal());

            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, applyNo));
            /**  封装响应数据1  **/
            if (ObjectUtils.isNotEmpty(caseBaseInfo) && ObjectUtils.isNotEmpty(caseCarInfo) && ObjectUtils.isNotEmpty(caseChannelInfo)) {
                outlineVo.setCarType(caseBaseInfo.getCarType());
                outlineVo.setProductName(caseBaseInfo.getProductName());
                outlineVo.setSubjectName(caseChannelInfo.getSubjectName());
                outlineVo.setSaleAdvisor(caseChannelInfo.getSaleAdvisor());
                outlineVo.setSaleAdvisorPhone(caseChannelInfo.getSalePhone());
                /**是否经销商担保 **/
                outlineVo.setIfDealerGuarantee(caseBaseInfo.getIfDealerGuarantee());
                ChannelBaseInfo channelInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                        .eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo())
                );
                if (ObjectUtils.isNotEmpty(channelInfo)) {
                    outlineVo.setChannelFullName(channelInfo.getChannelFullName());
                    /**渠道归属*/
                    outlineVo.setChannelBelong(channelInfo.getChannelBelong());
                    //经销商省市
                    String channelProvince =addressService.getLabelByCode(channelInfo.getChannelProvince());
                    String channelCity =addressService.getLabelByCode(channelInfo.getChannelCity());
                    outlineVo.setChannelAddress(channelProvince + channelCity);
                }
                /**查询授权区域*/
                String titleByApply = "";
                titleByApply = caseCarInfoService.getTitleByApply(applyNo, caseBaseInfo.getBusinessType());
                outlineVo.setTitle(titleByApply);
                //查询起租备注
                CaseCreditOption option = caseCreditOptionService.getOne(Wrappers.<CaseCreditOption>query().lambda()
                        .eq(CaseCreditOption::getApplyNo, applyNo)
                        .eq(CaseCreditOption::getOptionNo, "10016"));
                if (ObjectUtils.isNotEmpty(option)) {
                    outlineVo.setRentRemark(option.getValue());
                }
                //概要信息赋值
                examineAssetPageInfoVo.setOutlineVo(outlineVo);
                if (AffiliatedWayEnum.NO.getCode().equals(caseBaseInfo.getAffiliatedWay())) {
                    affixed = false;
                }
                /**  挂靠单位信息开始  **/
                ChannelAffiliatedUnits affiliateInfo = new ChannelAffiliatedUnits();
                if (StringUtils.isNotBlank(caseCarInfo.getAffCompanyId())) {
                    //modify by likang 修改挂靠信息
                    affiliateInfo = channelAffiliatedUnitsService.getAffiliatedCompany(caseCarInfo.getAffCompanyId());
                    /*.getOne(Wrappers.<ChannelAffiliatedUnits>query().lambda().eq(ChannelAffiliatedUnits::getId, Long.parseLong(caseCarInfo.getAffCompanyId())));*/
                }
                examineAssetPageInfoVo.setAffiliateInfo(affiliateInfo);

                // 如果满足条件，则保存免抵
                IResponse<String> decisionMortgageResultByApplyNo = applyServiceFeign.getDecisionMortgageResultByApplyNo(applyNo);

                if(CaseConstants.CODE_SUCCESS.equals(decisionMortgageResultByApplyNo.getCode())){
                    String data = decisionMortgageResultByApplyNo.getData();
                    if(StringUtil.isNotEmpty(data) && data.equals(CaseConstants.YES)){
                        outlineVo.setMortgageClaim(CaseConstants.YES);
                        outlineVo.setMortgageClaimName("免抵");
                    }else if(StringUtil.isNotEmpty(data) && data.equals(CaseConstants.NO)){
                        outlineVo.setMortgageClaim(CaseConstants.NO);
                        outlineVo.setMortgageClaimName("抵押");
                    }
                }

                /**  挂靠单位信息结束  **/
                if (ObjectUtils.isNotEmpty(channelInfo)) {
                    if (channelInfo.getChannelBelong().equals(AfsEnumUtil.key(ChannelBelongEnum.DIRECT))) {
                        ChannelRiskInfo channelRiskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                                .eq(ChannelRiskInfo::getChannelId, caseChannelInfo.getCarDealersId())
                                .eq(ChannelRiskInfo::getBusinessType, caseBaseInfo.getBusinessType()));
                        if (ObjectUtils.isNotEmpty(channelRiskInfo)) {
                            outlineVo.setChannelGrade(channelRiskInfo.getChannelGrade());
                            outlineVo.setLendingMode(AfsEnumUtil.key(WhetherEnum.YES).equals(channelRiskInfo.getIsMortgage()) ? AfsEnumUtil.desc(WhetherEnum.YES) : AfsEnumUtil.desc(WhetherEnum.NO));
                            outlineVo.setMortgageStatus(channelRiskInfo.getMortgageStatus());
                            outlineVo.setQualityLevel(channelRiskInfo.getQualityGrade());

                            //获取用户姓名，手机号
                            this.getUserInfo(channelRiskInfo, outlineVo);

                            // 如果满足条件，则保存免抵
                            if(CarTypeEnum.TEST_CAR.equals(outlineVo.getCarType())){
                                if(CaseConstants.YES.equals(outlineVo.getIsTopChannel()) &&  channelRiskInfo.getIsMortgage().equals(CaseConstants.YES)){
                                    outlineVo.setMortgageClaim(CaseConstants.YES);
                                    outlineVo.setMortgageClaimName("免抵");
                                }else{
                                    outlineVo.setMortgageClaim(CaseConstants.NO);
                                    outlineVo.setMortgageClaimName("抵押");
                                }
                            }

                        }

                    } else {
                        ChannelRiskInfo channelRiskInfo = channelRiskInfoService.getOne(Wrappers.<ChannelRiskInfo>query().lambda()
                                .eq(ChannelRiskInfo::getChannelId, channelInfo.getChannelId())
                                .eq(ChannelRiskInfo::getBusinessType, caseBaseInfo.getBusinessType()));
                        if (ObjectUtils.isNotEmpty(channelRiskInfo)) {
                            outlineVo.setLendingMode(AfsEnumUtil.key(WhetherEnum.YES).equals(channelRiskInfo.getIsMortgage()) ? AfsEnumUtil.desc(WhetherEnum.YES) : AfsEnumUtil.desc(WhetherEnum.NO));
                            outlineVo.setChannelGrade(channelRiskInfo.getChannelGrade());
                            outlineVo.setQualityLevel(channelRiskInfo.getQualityGrade());
                            outlineVo.setMortgageStatus(channelRiskInfo.getMortgageStatus());

                            //获取用户姓名，手机号
                            this.getUserInfo(channelRiskInfo, outlineVo);

                            // 如果满足条件，则保存免抵
                            if(CarTypeEnum.TEST_CAR.equals(outlineVo.getCarType())){
                                if(CaseConstants.YES.equals(outlineVo.getIsTopChannel()) &&  channelRiskInfo.getIsMortgage().equals(CaseConstants.YES)){
                                    outlineVo.setMortgageClaim(CaseConstants.YES);
                                    outlineVo.setMortgageClaimName("免抵");
                                }else{
                                    outlineVo.setMortgageClaim(CaseConstants.NO);
                                    outlineVo.setMortgageClaimName("抵押");
                                }
                            }
                        }
                    }

                }
                //概要信息赋值
                examineAssetPageInfoVo.setOutlineVo(outlineVo);
                /**风控看板  标签信息 **/
                List<ApproveMakeLabel> labelList = approveMakeLabelService.list(Wrappers.<ApproveMakeLabel>query().lambda()
                        .eq(ApproveMakeLabel::getApplyNo, applyNo)
                        .eq(ApproveMakeLabel::getLabelLocation, LabelPositionEnum.CREDITANALYSIS.getCode())
                );
                examineAssetPageInfoVo.setLabelList(labelList);
                //内部留言信息和外部留言信息接口在remindcontroller.queryRemindListByApplyNo

                //融资信息
                BigDecimal loanAmtNew = BigDecimal.ZERO;
                List<FinCostDetails> finCostInfoArrayList = caseCostInfoService.list(Wrappers.<FinCostDetails>query().lambda()
                        .eq(FinCostDetails::getApplyNo, applyNo));
                if (!org.springframework.util.CollectionUtils.isEmpty(finCostInfoArrayList)) {
                    for (FinCostDetails finCostDetails : finCostInfoArrayList) {
                        loanAmtNew = loanAmtNew.add(finCostDetails.getLoanAmt());
                        if (CostTypeEnum.CARAMT.getCode().equals(finCostDetails.getCostType())) {
                            examineAssetPageInfoVo.setContractAmt(finCostDetails.getContractAmt());
                        }
                    }
                }
                examineAssetPageInfoVo.setLoanAmtRepeat(loanAmtNew);
                //todo 购置税还没赋值
                //客户信息与企业信息 casecustinfocontroller.queryApproveTaskList

                //担保人信息
                List<CaseCustInfo> caseInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                        .eq(CaseCustInfo::getApplyNo, applyNo)
                        .eq(CaseCustInfo::getCustRole, CaseConstants.GUARANTOR)
                        .orderByAsc(CaseCustInfo::getFirstGuarantor));

                //返回结果加入融资期限
                FinCostDetails finInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                        .eq(FinCostDetails::getApplyNo, applyNo)
                );
                //担保人信息
                if (CollectionUtils.isNotEmpty(caseInfoList)) {
                    //获取年龄和业务年龄
                    for (CaseCustInfo caseInfo : caseInfoList) {
                        int age = this.getAge(caseInfo.getCertNo());
                        String ageTerm = this.getAgeTrem(age, finInfo.getLoanTerm());

                        caseInfo.setAge(String.valueOf(age));
                        caseInfo.setAgeTerm(ageTerm);
                    }
                    log.info("担保人信息list{}", caseInfoList);
                    caseInfoList.forEach(t -> {
                        if (t.getCustType().equals(CaseConstants.ENTERPRISE)) {
                            CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda().eq(CaseEnterpriseCustomerDetails::getCustId, t.getId()));
                            t.setCustName(null != details ? details.getEnterpriseName() : "");
                        }
                    });
                    examineAssetPageInfoVo.setCaseinfoList(caseInfoList);
                }

                //银行卡鉴权

                //车辆信息表、审批历史记录、客户信息表、车辆发票信息表、保险信息
                loanSummaryInfoService.getSummaryList(examineAssetPageInfoVo, applyNo);
                //调用车型服务接口查询车300车辆类型
                try {
                    CarInfoVo carInfoVo = new CarInfoVo();
                    List<VehicleModelNewVO> vehicleModelNewVOList = vehicleInfoService.queryModelInfo(caseCarInfo.getModelId());
                    if (CollectionUtils.isNotEmpty(vehicleModelNewVOList)) {
                        VehicleModelNewVO vehicleModelNewVO = vehicleModelNewVOList.get(0);
                        carInfoVo.setCarType(vehicleModelNewVO.getOwnParentTypeDesc());
                        carInfoVo.setOwnSubTypeDesc(vehicleModelNewVO.getOwnSubTypeDesc());
                        carInfoVo.setVehicleTypeDesc(vehicleModelNewVO.getVehicleTypeDesc());
                        carInfoVo.setLevelType(vehicleModelNewVO.getLevelType());

                        String isParallel = vehicleModelNewVO.getIsParallel();
                        if (StringUtils.isNotBlank(isParallel)) {
                            carInfoVo.setIsParallel("0".equals(isParallel) ? "否" : "是");
                        }
                        carInfoVo.setFuelType(vehicleModelNewVO.getFuelType());
                        //新增中远评估价
                        carInfoVo.setOwnPrice(vehicleModelNewVO.getOwnPrice());
                        //车型分级
                        carInfoVo.setVehicleLevel(vehicleModelNewVO.getVehicleLevel());

                        //是否停售为空，展示为空
                        if (jodd.util.StringUtil.isNotEmpty(caseCarInfo.getIsStopSale())) {
                            carInfoVo.setIsStopSale("0".equals(caseCarInfo.getIsStopSale()) ? "否" : "是");
                        }
                        //是否新能源
                        String isGreen = "";
                        List<CaseCarStyleDetail> cartyleDetailInfoList = carStyleDetailInfoService.list(Wrappers.<CaseCarStyleDetail>query().lambda()
                                .eq(CaseCarStyleDetail::getCarId, caseCarInfo.getId())
                                .eq(CaseCarStyleDetail::getDelFlag, "0")
                                .orderByDesc(CaseCarStyleDetail::getCreateTime));
                        if (CollectionUtil.isNotEmpty(cartyleDetailInfoList)) {
                            isGreen = cartyleDetailInfoList.get(0).getIsGreen();
                            if (StringUtils.isNotBlank(isGreen)) {
                                isGreen = "0".equals(isGreen) ? "否" : "是";
                            } else {
                                if (StrUtil.isNotBlank(vehicleModelNewVO.getIsGreen())) {
                                    isGreen = "0".equals(vehicleModelNewVO.getIsGreen()) ? "否" : "是";
                                }
                            }
                        } else if (StringUtils.isNotBlank(vehicleModelNewVO.getIsGreen())) {
                            isGreen = "0".equals(vehicleModelNewVO.getIsGreen()) ? "否" : "是";
                        }

                        if (StringUtils.isNotBlank(isGreen)) {
                            carInfoVo.setIsGreen(isGreen);
                        } else {
                            carInfoVo.setIsGreen("否");
                        }
                        carInfoVo.setCarPurpose(caseBaseInfo.getCarPurpose());
                        examineAssetPageInfoVo.setCarInfoVo(carInfoVo);
                    }
                } catch (Exception e) {
                    log.error("调用车型服务获取车型信息异常！[{}]", e.getMessage());
                }

            } else {
                examineAssetPageInfoVo.setAffiliateInfo(new ChannelAffiliatedUnits());
            }
        }
        examineAssetPageInfoVo.setAffixed(affixed);
        //车300信息
        IResponse<List<ResponseCarModelScreeningDTO>> carThree = caseCarInfoService.carModelScreening(applyNo);
        examineAssetPageInfoVo.setCarModelScreenList(carThree.getData());
        //客户等级
        CaseLiangfuApply caseLiangfuApply = caseLiangfuApplyService.getCustLevel(applyNo);
        if (null != caseLiangfuApply) {
            examineAssetPageInfoVo.setCustLevel(caseLiangfuApply.getCusRank());
        }
        //承租人发票信息
        CaseReceiptInfo caseReceiptInfo = caseReceiptInfoService.getCaseReceiptInfoByApplyNo(applyNo);
        examineAssetPageInfoVo.setCaseReceiptInfo(caseReceiptInfo);

        examineAssetPageInfoVo.setCarType(caseBaseInfo.getCarType());
        examineAssetPageInfoVo.setBusinessType(caseBaseInfo.getBusinessType());
        examineAssetPageInfoVo.setOrderType(caseBaseInfo.getOrderType());
        iResponse.setData(examineAssetPageInfoVo);
        iResponse.setMsg(carThree.getMsg());
        return iResponse;
    }

    /**
     * 获取年龄
     * @param certNo
     * @return
     */
    private int getAge(String certNo) {
        String brithday = certNo.substring(6, 14);
        Date brithDate = null;

        try {
            brithDate = new SimpleDateFormat("yyyyMMdd").parse(brithday);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        GregorianCalendar currentDay = new GregorianCalendar();
        currentDay.setTime(brithDate);
        int birYear = currentDay.get(Calendar.YEAR);

        //获取年龄
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        String thisYear = simpleDateFormat.format(new Date());
        int age = Integer.valueOf(thisYear) - birYear;

        return age;
    }

    /**
     * 获取保证人业务年龄
     * @param age
     * @param loanTerm
     * @return
     */
    private String getAgeTrem(Integer age, Integer loanTerm) {
        DecimalFormat df = new DecimalFormat("0.0");
        float num = (float) loanTerm / 12;
        String loanTermAge = df.format(num);
        BigDecimal ageTerm = (new BigDecimal(age)).add(new BigDecimal(loanTermAge));
        log.info("保证人业务年龄为：" + ageTerm);
        return ageTerm.toString();
    }

    @PostMapping(value = "/BankCardAuthentication")
    @ApiOperation(value = "展示银行卡信息")
    public IResponse loanBankCardInfo(@RequestParam("applyNo") String applyNo) {
        IResponse iResponse = new IResponse();
        ExamineAssetPageInfoVo examineAssetPageInfoVo = new ExamineAssetPageInfoVo();
        try {
            //银行卡信息
            List<LoanBankCardInfo> loanBankCardInfos = loanBankCardInfoService.list(Wrappers.<LoanBankCardInfo>query().lambda()
                    .eq(LoanBankCardInfo::getApplyNo, applyNo).eq(LoanBankCardInfo::getIsDefaultDeductCard, AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT)));
            log.info("银行卡信息 loanBankCardInfo --> {}", JSONObject.toJSONString(loanBankCardInfos));
            for (LoanBankCardInfo loanBankCardInfo : loanBankCardInfos) {

                loanBankCardInfo.setVerStatus(AfsEnumUtil.desc(VerStatusEnum.valueOf(loanBankCardInfo.getVerStatus())));
                loanBankCardInfo.setVerChannelBelong(AfsEnumUtil.desc(AfsEnumUtil.getEnum(loanBankCardInfo.getVerChannelBelong(), BankCardSigningTypeEnum.class)));
                examineAssetPageInfoVo.setLoanBankCardInfo(loanBankCardInfo);
            }
            iResponse.setData(examineAssetPageInfoVo);
        } catch (Exception e) {
            log.error("获取银行卡信息失败", e);
            return IResponse.fail("获取银行卡信息失败");
        }
        return iResponse;
    }


    @PostMapping(value = "/saveInvoiceInfo")
    @ApiOperation(value = "保存发票信息")
    public IResponse saveInvoiceInfo(@RequestBody RemindData remindData) {
        CarInvoiceInfo carInvoiceInfo = remindData.getCarInvoiceInfo();
        CarInvoiceInfo oldInvoice = carInvoiceInfoService.getById(carInvoiceInfo.getId());
        if (null == oldInvoice) {
            return IResponse.fail("发票信息不存在");
        }
        AfsUser user = SecurityUtils.getUser();
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        StringBuffer stringBuffer = new StringBuffer();
        Boolean excludingTaxAmt = false;
        Boolean invoiceCode = false;
        Boolean invoiceNumber = false;
        Boolean invoiceDate = false;
        if(ObjectUtils.isNotEmpty(oldInvoice)) {
            // 原增值税额不等于新增值税额
            excludingTaxAmt = remindData.getCarInvoiceInfo().getExcludingTaxAmt().compareTo(oldInvoice.getExcludingTaxAmt()) != 0;
            // 原发票代码不等于新发票代码
            invoiceCode = !remindData.getCarInvoiceInfo().getInvoiceCode().equals(oldInvoice.getInvoiceCode());
            // 原发票号码不等于新发票号码
            invoiceNumber = !remindData.getCarInvoiceInfo().getInvoiceNumber().equals(oldInvoice.getInvoiceNumber());
            // 原开票日期不等于新开票日期
            invoiceDate = !remindData.getCarInvoiceInfo().getInvoiceDate().equals(oldInvoice.getInvoiceDate());
        }
        if(excludingTaxAmt){
            stringBuffer.append("不含税价：[" + oldInvoice.getExcludingTaxAmt() +"]，改为：[" +remindData.getCarInvoiceInfo().getExcludingTaxAmt() + "]  ");
        }
        if(invoiceCode){
            stringBuffer.append("发票代码：[" + oldInvoice.getInvoiceCode() + "]，修改为：[" + remindData.getCarInvoiceInfo().getInvoiceCode() + "]");
        }
        if(invoiceNumber){
            stringBuffer.append("发票号码：[" + oldInvoice.getInvoiceNumber() + "]，修改为：[" + remindData.getCarInvoiceInfo().getInvoiceNumber() + "]");
        }
        if(invoiceDate){
            stringBuffer.append("开票日期：[" + cn.hutool.core.date.DateUtil.format(oldInvoice.getInvoiceDate(),"yyyy-MM-dd HH:mm:ss") + "]，修改为：[" + remindData.getCarInvoiceInfo().getInvoiceDate() + "]");
        }
        if(excludingTaxAmt || invoiceCode || invoiceNumber || invoiceDate){
            CaseRemindDetail caseRemindDetail = new CaseRemindDetail();
            String once = uuid + System.currentTimeMillis();
            //当前登陆用户
            SecurityUtils.getUsername();
            caseRemindDetail.setDisposeUser(user.getUserRealName());
            caseRemindDetail.setDisposeUserId(user.getId().toString());
            caseRemindDetail.setStatus(CancelStatusEnum.EFFECTIVE.getCode());
            caseRemindDetail.setDisposeTime(new Date());
            caseRemindDetail.setUseScene(remindData.getRecord().getUseScene());
            caseRemindDetail.setOperationType(remindData.getRemindOprType());
            caseRemindDetail.setMindId(once);
            caseRemindDetail.setApplyNo(remindData.getCarInvoiceInfo().getApplyNo());
            caseRemindDetail.setRemindType(RemindTypeEnum.INNER.getValue());
            caseRemindDetail.setOperationType("save");
            // 内部留言默认 经销商不可见
            caseRemindDetail.setRemindPowers(RemindPowerEnum.UN_VISIBLE.getValue());
            caseRemindDetail.setEditFlag(RemindEditEnum.UN_EDIT.getValue());
            caseRemindDetail.setRemindContent(stringBuffer.toString());
            remindService.save(caseRemindDetail);
        }


        if (!carInvoiceInfoService.updateById(carInvoiceInfo)) {
            return IResponse.fail("发票信息保存失败");
        }
        return IResponse.success("保存成功");
    }

    @PostMapping(value = "/saveInsurance")
    @ApiOperation(value = "保存首保信息")
    public IResponse saveInsurance(@RequestBody List<CarInsuranceInfo> carInsuranceInfos) {

        if (carInsuranceInfos.size() > 0) {
            for (CarInsuranceInfo carInsuranceInfo : carInsuranceInfos) {
                if (null == carInsuranceInfoService.getById(carInsuranceInfo.getId())) {
                    return IResponse.fail("保险信息不存在");
                }
                if (!carInsuranceInfoService.updateById(carInsuranceInfo)) {
                    if (BUSINESS.getCode().equals(carInsuranceInfo.getInsuranceNo())) {
                        return IResponse.fail("商业保险信息保存失败");
                    } else {
                        return IResponse.fail("交强保险信息保存失败");
                    }
                }
            }
        } else {
            return IResponse.fail("保存失败,无数据传入");
        }
        return IResponse.success("保存成功");
    }

    @PostMapping(value = "/saveDecorationInvoiceInfo")
    @ApiOperation(value = "保存精品装潢发票信息")
    public IResponse saveDecorationInvoiceInfo(@RequestBody CarInvoiceInfo carInvoiceInfo) {
        if (StringUtils.isBlank(carInvoiceInfo.getInvoiceCode())) {
            return IResponse.fail("发票代码不能为空");
        }
        if (StringUtils.isBlank(carInvoiceInfo.getInvoiceNumber())) {
            return IResponse.fail("发票号码不能为空");
        }
        if (ObjectUtils.isNull(carInvoiceInfo.getInvoiceAmt())) {
            return IResponse.fail("发票金额不能为空");
        }
        if (ObjectUtils.isNull(carInvoiceInfo.getInvoiceTaxAmt())) {
            return IResponse.fail("不含税价不能为空");
        }
        carInvoiceInfoService.updateById(carInvoiceInfo);
        return IResponse.success("保存成功");
    }

    @PostMapping(value = "/carModelScreening")
    @ApiOperation(value = "车型甄别")
    public IResponse<List<ResponseCarModelScreeningDTO>> carModelScreening(@RequestParam("applyNo") String applyNo) {
        //车300信息
        return caseCarInfoService.carModelScreening(applyNo);
    }

    /**
     * queryApproveTaskList
     * <p>Description:主借人基本信息查询 </p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryPersionOrEnterpriseInfo")
    @ApiOperation(value = "按照申请编号查询主借人基本信息")
    public IResponse<MainBaseInfoVo> queryPersionOrEnterpriseInfo(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        MainBaseInfoVo mainBaseInfoVo = new MainBaseInfoVo();
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, caseInfoQueryCondition.getApplyNo()));
        mainBaseInfoVo.setInputType(caseBaseInfo.getInputType());
        String capital = Optional.ofNullable(caseBaseInfo.getBelongingCapital())
                .filter(c -> StrUtil.startWithIgnoreCase(c, CaseConstants.CAPITAL_BANK_PREFIX))
                .orElse(BelongingCapitalEnum.FD.getCode());
        String capitalName = BelongingCapitalEnum.getDescByCode(capital);
        mainBaseInfoVo.setBelongingCapital(capitalName);
        //是否个人转个体户
        String ifPersonalToEnterprise = caseBaseInfo.getIfPersonalToEnterprise();
        mainBaseInfoVo.setIfPersonalToEnterprise(ifPersonalToEnterprise);

        //返回结果加入融资期限
        FinCostDetails finCostInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, caseInfoQueryCondition.getApplyNo())
        );
        mainBaseInfoVo.setLoanTerm(null != finCostInfo ? finCostInfo.getLoanTerm() : 0);

        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            BeanUtils.copyProperties(caseCustInfo, mainBaseInfoVo);
            Long custId = caseCustInfo.getId();
            mainBaseInfoVo.setId(custId.toString());

            if (Constants.IF_PERSONAL_TO_ENTERPRISE.equals(ifPersonalToEnterprise)) {
                processEnterpriseCustInfo(mainBaseInfoVo, custId, ifPersonalToEnterprise);
                return IResponse.success(mainBaseInfoVo);
            } else {

                if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)) {
                    /**  个人客户信息  **/
                    CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                            .eq(CaseCustIndividual::getCustId, custId));
                    if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                        BeanUtils.copyProperties(caseCustIndividual, mainBaseInfoVo);
                    }
                    /**  居住地址  **/
                    List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                            .eq(CaseCustAddress::getCustId, custId).eq(CaseCustAddress::getAddressType, AddressTypeEnum.LIVING.getCode()).eq(CaseCustAddress::getDelFlag, "0"));
                    if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                        mainBaseInfoVo.setProvince(caseCustAddressList.get(0).getProvince());
                        mainBaseInfoVo.setCity(caseCustAddressList.get(0).getCity());
                        mainBaseInfoVo.setCounty(caseCustAddressList.get(0).getCounty());
                        mainBaseInfoVo.setStreet((caseCustAddressList.get(0).getStreet() != null && !Objects.equals(caseCustAddressList.get(0).getStreet(), "99999999"))? caseCustAddressList.get(0).getStreet() : "");
                        mainBaseInfoVo.setTown((caseCustAddressList.get(0).getTown() != null && !Objects.equals(caseCustAddressList.get(0).getTown(), "99999999"))? caseCustAddressList.get(0).getTown() : "");
                        mainBaseInfoVo.setDetailAddress((caseCustAddressList.get(0).getDetailAddress() != null && !Objects.equals(caseCustAddressList.get(0).getDetailAddress(), "99999999"))? caseCustAddressList.get(0).getDetailAddress() : "");
                    }
                    List list = this.getEvidenceIndividual(caseInfoQueryCondition.getApplyNo(), caseCustInfo);
                    if (CollectionUtils.isNotEmpty(list)) {
                        RedundantSignVo signVo = JSONObject.parseObject(JSONObject.toJSONString(list.get(0)), RedundantSignVo.class);
                        CaseCustInfo custInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(1)), CaseCustInfo.class);
                        CaseCustIndividual individual = JSONObject.parseObject(JSONObject.toJSONString(list.get(2)), CaseCustIndividual.class);
                        if (ObjectUtils.isNotEmpty(custInfo)) {
                            mainBaseInfoVo.setTelPhoneOld(custInfo.getTelPhone());
                        }
                        if (ObjectUtils.isNotEmpty(individual)) {
                            mainBaseInfoVo.setMaritalStatusOld(individual.getMaritalStatus());
                        }
                        mainBaseInfoVo.setBackSign(signVo.getBackSign());
                        mainBaseInfoVo.setReconsiderSign(signVo.getReconsiderSign());

                    }
                    return IResponse.success(mainBaseInfoVo);
                } else if (caseCustInfo.getCustType().equals(CaseConstants.ENTERPRISE)) {
                    processEnterpriseCustInfo(mainBaseInfoVo, custId, ifPersonalToEnterprise);
                    return IResponse.success(mainBaseInfoVo);
                }
            }
        }


        return IResponse.fail("数据错误9999");
    }

    private void processEnterpriseCustInfo(MainBaseInfoVo mainBaseInfoVo, Long custId, String ifPersonalToEnterprise) {
        log.info("get CaseEnterpriseCustomerDetails custId,{}", custId);
        CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                .eq(CaseEnterpriseCustomerDetails::getCustId, custId));
        if (ObjectUtils.isNotEmpty(details)) {
            BeanUtils.copyProperties(details, mainBaseInfoVo);
            log.info("get CaseEnterpriseCustomerDetails result,{},{}", JSON.toJSONString(details), JSON.toJSONString(mainBaseInfoVo));
        }
        if (Constants.IF_PERSONAL_TO_ENTERPRISE.equals(ifPersonalToEnterprise)) {
            //法人保证人也是主借人的地址
            CaseCustInfo guarantor = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                    .eq(CaseCustInfo::getApplyNo, details.getApplyNo())
                    .eq(CaseCustInfo::getCustRelation, Constants.LEGAL_PERSON)
                    .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));
            //居住地址
            List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, guarantor.getId()).eq(CaseCustAddress::getAddressType, AddressTypeEnum.LIVING.getCode()).eq(CaseCustAddress::getDelFlag, "0"));
            if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                // 企业类型时该字段是企业经营地址，个人转企业和个人是居住地址
                mainBaseInfoVo.setProvince(caseCustAddressList.get(0).getProvince());
                mainBaseInfoVo.setCity(caseCustAddressList.get(0).getCity());
                mainBaseInfoVo.setCounty(caseCustAddressList.get(0).getCounty());
                mainBaseInfoVo.setStreet(caseCustAddressList.get(0).getStreet());
                mainBaseInfoVo.setTown(caseCustAddressList.get(0).getTown());
                mainBaseInfoVo.setDetailAddress(caseCustAddressList.get(0).getDetailAddress() != null ? caseCustAddressList.get(0).getDetailAddress() : "");

            }
            /**  企业营业执照地  **/
            List<CaseCustAddress> businessAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, custId).eq(CaseCustAddress::getAddressType, AddressTypeEnum.BUSINESS_LICENSE.getCode()).eq(CaseCustAddress::getDelFlag, "0"));
            if (CollectionUtils.isNotEmpty(businessAddressList)) {
                mainBaseInfoVo.setBusinessLicenseAddress(businessAddressList.get(0));
            }

        } else {
            /**  企业经营地  **/
            List<CaseCustAddress> enterpriseAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, custId).eq(CaseCustAddress::getAddressType, AddressTypeEnum.BUSINESS_PLACE.getCode()).eq(CaseCustAddress::getDelFlag, "0"));
            // 企业类型时该字段是企业经营地址，个人转企业和个人是居住地址
            if (CollectionUtils.isNotEmpty(enterpriseAddressList)) {
                mainBaseInfoVo.setProvince(enterpriseAddressList.get(0).getProvince());
                mainBaseInfoVo.setCity(enterpriseAddressList.get(0).getCity());
                mainBaseInfoVo.setCounty(enterpriseAddressList.get(0).getCounty());
                mainBaseInfoVo.setStreet(enterpriseAddressList.get(0).getStreet());
                mainBaseInfoVo.setTown(enterpriseAddressList.get(0).getTown());
                mainBaseInfoVo.setDetailAddress(enterpriseAddressList.get(0).getDetailAddress() != null ? enterpriseAddressList.get(0).getDetailAddress() : "");

            }
        }

    }

    /**
     * @description: 获取冗余数据
     * <AUTHOR>
     * @created 2020/8/16 21:42
     * @version 1.0
     */
    public List getEvidenceIndividual(String applyNo, CaseCustInfo caseCustInfo) {

        RedundantSignVo signVo = new RedundantSignVo();
        signVo.setBackSign(WhetherEnum.NO.getCode());
        signVo.setReconsiderSign(WhetherEnum.NO.getCode());
        CaseCustInfo caseCust = null;
        CaseCustIndividual custIndividual = null;
        List list = new ArrayList();
        CaseRedundantInfo redundantInfo = redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(applyNo), CaseRedundantInfo::getApplyNo, applyNo));
        if (ObjectUtils.isNotEmpty(redundantInfo)) {
            String backSign = redundantInfo.getBackSign();
            if (WhetherEnum.YES.getCode().equals(backSign)) {
                String backEvidence = redundantInfo.getBackEvidence();
                if (StringUtils.isNotBlank(backEvidence)) {
                    caseCust = this.getEvidenceForCustInfo(backEvidence, caseCustInfo.getId());
                    custIndividual = this.getEvidenceForCustIndividual(backEvidence, caseCustInfo.getId());
                    signVo.setBackSign(WhetherEnum.YES.getCode());
                    signVo.setReconsiderSign(WhetherEnum.NO.getCode());
                }
            }
            String reconsiderSign = redundantInfo.getReconsiderSign();
            if (WhetherEnum.YES.getCode().equals(reconsiderSign)) {
                String reconsiderEvidence = redundantInfo.getReconsiderEvidence();
                if (StringUtils.isNotBlank(reconsiderEvidence)) {
                    caseCust = this.getEvidenceForCustInfo(reconsiderEvidence, caseCustInfo.getId());
                    custIndividual = this.getEvidenceForCustIndividual(reconsiderEvidence, caseCustInfo.getId());
                    signVo.setBackSign(WhetherEnum.NO.getCode());
                    signVo.setReconsiderSign(WhetherEnum.YES.getCode());
                }
            }
        }
        list.add(signVo);
        list.add(caseCust);
        list.add(custIndividual);
        return list;
    }

    /**
     * @description: 解析客户信息详情冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public CaseCustInfo getEvidenceForCustInfo(String jsonStr, Long id) {
        CaseCustInfo caseCust = new CaseCustInfo();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseCustList = jsonObject.getJSONArray("caseCustInfoList");
            if (ObjectUtils.isNotEmpty(caseCustList) && caseCustList.size() > 0) {
                caseCustList.toJavaList(CaseCustInfo.class).forEach(caseCustInfo -> {
                    if (caseCustInfo.getId().equals(id)) {
                        BeanUtils.copyProperties(caseCustInfo, caseCust);
                    }
                });
            }
        }
        return caseCust;
    }

    /**
     * @description: 解析客户信息详情冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public CaseCustIndividual getEvidenceForCustIndividual(String jsonStr, Long id) {
        CaseCustIndividual caseCust = new CaseCustIndividual();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseCustIndividualList = jsonObject.getJSONArray("caseCustIndividualList");
            if (ObjectUtils.isNotEmpty(caseCustIndividualList) && caseCustIndividualList.size() > 0) {
                caseCustIndividualList.toJavaList(CaseCustIndividual.class).forEach(caseCustIndividual -> {
                    if (caseCustIndividual.getCustId().equals(id)) {
                        BeanUtils.copyProperties(caseCustIndividual, caseCust);
                    }
                });
            }
        }
        return caseCust;
    }

    /**
     * GPS定位
     * insert lsh  2021-08-16 新增GPS定位
     *
     * @param gpsInfo
     * @return
     */
    @PostMapping("/locationbute")
    @ApiOperation(value = "GPS定位")
    public IResponse locationbute(@RequestBody CarGpsApply gpsInfo) {
        //一期不对接gps定位接口，执行数据查询
        List<CarGpsDevice> gpsLists = carGpsDeviceService.list(Wrappers.<CarGpsDevice>query().lambda().eq(CarGpsDevice::getApplyNo, gpsInfo.getApplyNo()));

        if (ObjectUtils.isEmpty(gpsLists)) {//针对PC端进件数据特别处理,后续做修改
            CarGpsDevice carGpsDevice = new CarGpsDevice();
            CarGpsApply carGpsApply = carGpsApplyMapper.selectOne(Wrappers.<CarGpsApply>query().lambda()
                    .eq(CarGpsApply::getApplyNo, gpsInfo.getApplyNo()));
            if (ObjectUtils.isNotEmpty(carGpsApply)) {
                carGpsDevice.setGpsSupplier(GpsSuppEnum.getName(carGpsApply.getGpsSupplier()));

                String addr = carGpsApply.getInstallAddress() != null ? carGpsApply.getInstallAddress() : "";
                String pro = addressService.getLabelByCode(carGpsApply.getInstallProvince());
                String cityAddr =addressService.getLabelByCode(carGpsApply.getInstallCity());
                String county = addressService.getLabelByCode(carGpsApply.getInstallCounty());
                String street = addressService.getLabelByCode(carGpsApply.getInstallStreet());
                carGpsDevice.setCurLocationAdd(pro + cityAddr + county + street + addr);
            }
            gpsLists.add(carGpsDevice);
        } else {
            for (CarGpsDevice carGpsDevice : gpsLists) {
                carGpsDevice.setGpsSupplier(GpsSuppEnum.getName(carGpsDevice.getGpsSupplier()));
            }
        }
        return IResponse.success(gpsLists);

    }

    /**
     * 发票查验
     * insert lsh  2021-08-18
     *
     * @param invoiceInfo
     * @return
     */
    @PostMapping("/invoiceCheck")
    @ApiOperation(value = "发票查验")
    public IResponse invoiceCheck(@RequestBody CarInvoiceInfo invoiceInfo) {
        SimpleDateFormat sf = new SimpleDateFormat("yyy-MM-dd");
        CarInvoiceInfo carInvoiceInfo = carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>lambdaQuery().eq(CarInvoiceInfo::getApplyNo, invoiceInfo.getApplyNo())
                .eq(CarInvoiceInfo::getInvoiceType, ApplyCarInvoiceEnum.INVOICETYPE_BUYCAR.getIndex()));
        if (null == carInvoiceInfo) {
            return IResponse.fail("发票信息不存在");
        }
        if (carInvoiceInfoService.updateById(invoiceInfo) == false) {
            return IResponse.fail("发票信息保存失败");
        }
        IResponse<List<ResponseInvoiceVerificationDTO>> iResponse = invoiceService.invoiceCheck(
                invoiceInfo.getInvoiceNumber(), invoiceInfo.getInvoiceCode(), sf.format(invoiceInfo.getInvoiceDate()), invoiceInfo.getCheckCode(), invoiceInfo.getInvoiceTaxAmt().toString(), invoiceInfo.getApplyNo());
        if ("9999".equals(iResponse.getCode())) {
            int index = iResponse.getMsg().indexOf(",");
            if (index > 0) {
                iResponse.setMsg(iResponse.getMsg().substring(index + 1, iResponse.getMsg().length()));
                return iResponse;
            } else {
                return iResponse;
            }
        }
        ResponseInvoiceVerificationDTO invoice = iResponse.getData().get(0);
        String isPass = IsEqualEnum.PASS.getValue();
        //购方名称
        if (StringUtil.isNotEmpty(carInvoiceInfo.getBuyerName())) {
            invoice.setPurchaserName(carInvoiceInfo.getBuyerName().equals(invoice.getPurchaserName()) ?
                    invoice.getPurchaserName() + IsEqualEnum.BRACKETS_EQUAL.getValue() : invoice.getPurchaserName() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        } else {
            isPass = IsEqualEnum.FAIL.getValue();
            carInvoiceInfo.setBuyerName(invoice.getPurchaserName());
            invoice.setPurchaserName(invoice.getPurchaserName() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        }
        //购方证件号
        if (StringUtil.isNotEmpty(carInvoiceInfo.getBuyerIdcardNo())) {
            invoice.setIDCardNo(carInvoiceInfo.getBuyerIdcardNo().equals(invoice.getIDCardNo()) ?
                    invoice.getIDCardNo() + IsEqualEnum.BRACKETS_EQUAL.getValue() : invoice.getIDCardNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        } else {
            isPass = IsEqualEnum.FAIL.getValue();
            carInvoiceInfo.setBuyerIdcardNo(invoice.getIDCardNo());
            invoice.setIDCardNo(invoice.getIDCardNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        }
        //车架号
        if (StringUtil.isNotEmpty(carInvoiceInfo.getCarVin())) {
            invoice.setVehicleNo(carInvoiceInfo.getCarVin().equals(invoice.getVehicleNo()) ?
                    invoice.getVehicleNo() + IsEqualEnum.BRACKETS_EQUAL.getValue() : invoice.getVehicleNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        } else {
            isPass = IsEqualEnum.FAIL.getValue();
            carInvoiceInfo.setCarVin(invoice.getVehicleNo());
            invoice.setVehicleNo(invoice.getVehicleNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        }
        //发动机号
        if (StringUtil.isNotEmpty(carInvoiceInfo.getEngineNo())) {
            invoice.setEngineNo(carInvoiceInfo.getEngineNo().equals(invoice.getEngineNo()) ?
                    invoice.getEngineNo() + IsEqualEnum.BRACKETS_EQUAL.getValue() : invoice.getEngineNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        } else {
            isPass = IsEqualEnum.FAIL.getValue();
            carInvoiceInfo.setEngineNo(invoice.getEngineNo());
            invoice.setEngineNo(invoice.getEngineNo() + IsEqualEnum.BRACKETS_NOEQUAL.getValue());
        }
        invoice.setIsPass(isPass);
        iResponse.getData().clear();
        iResponse.getData().add(invoice);
        iResponse.setMsg("查验成功");
        return iResponse;
    }

    /**
     * 查询客户联系人信息
     * 2021-08-20
     *
     * @param applyNo
     * @return
     */
    @PostMapping("/queryCaseCustContactList")
    @ApiOperation(value = "客户联系人")
    public IResponse queryCaseCustContactList(@RequestParam("applyNo") String applyNo) {
        List<CaseCustContact> caseCustContactList = caseCustContactService.queryCaseCustContactList(applyNo);
        return IResponse.success("查验成功").setData(caseCustContactList);
    }

    /**
     * 功能描述: 判断该订单是否挂起<br>
     * 〈〉
     *
     * @Param: [applyNo]
     * @Return: com.ruicar.afs.cloud.common.core.util.IResponse
     * @Date: 2022/4/20 18:53
     * @Author: fwp
     */
    @PostMapping("/queryLoanSuspendStatus")
    @ApiOperation(value = "查询放款审核的案件挂起状态")
    public IResponse queryLoanSuspendStatus(@RequestBody CaseContractInfo caseContractInfo) {
        CaseContractInfo result = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo, caseContractInfo.getApplyNo()));
        return IResponse.success(result.getSuspendStatus());
    }

    /**
     * 获取GPS定位
     *
     * @return
     */
    @GetMapping("/getGpsAddressInfo")
    @ApiOperation(value = "获取GPS定位")
    public IResponse getGpsAddressInfo(@RequestParam("applyNo") String applyNo, @RequestParam(defaultValue = "1", required = false) int page,
                                       @RequestParam(defaultValue = "10", required = false) int size) {
        QueryWrapper<CarGpsAddress> resultDTOS = new QueryWrapper<>();
        resultDTOS.eq("apply_no", applyNo);
        resultDTOS.orderByDesc("create_time");
        Page<CarGpsAddress> addressPage = new Page<>(page, size);
        IPage<CarGpsAddress> carGpsAddresses = carGpsAddressService.page(addressPage, resultDTOS);
        return IResponse.success(carGpsAddresses);
    }

    /**
     * 获取GPS是否履约信息
     *
     * @return
     */
    @PostMapping("/queryGpsPerform")
    @ApiOperation(value = "获取GPS是否履约信息")
    public IResponse queryGpsPerform(@RequestBody CarGpsApplyVO gpsInfo) {
        CarGpsApply carGpsApply = carGpsApplyService.getOne(Wrappers.<CarGpsApply>query().lambda()
                .eq(CarGpsApply::getApplyNo, gpsInfo.getApplyNo()));
        return IResponse.success(carGpsApply);
    }

    /**
     * 保存GPS是否履约信息
     *
     * @return
     */
    @PostMapping("/saveGpsPerform")
    @ApiOperation(value = "保存GPS是否履约信息")
    public IResponse saveGpsPerform(@RequestBody RemindData remindData) {
        CarGpsApply carGpsApply = carGpsApplyService.getOne(Wrappers.<CarGpsApply>query().lambda()
                .eq(CarGpsApply::getApplyNo, remindData.getGpsInfo().getApplyNo()));
        AfsUser user = SecurityUtils.getUser();
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16);
        StringBuffer stringBuffer = new StringBuffer();
        Boolean gpsPerform = false;
        Boolean gpsSupplier = false;
        if (ObjectUtils.isEmpty(remindData.getGpsInfo())) {
            return IResponse.fail("GPS信息为空");
        }
        // 原履约不等于新履约
        gpsPerform = !remindData.getGpsInfo().getGpsPerform().equals(carGpsApply.getGpsPerform());
        // 原GPS厂商不等于新GPS厂商
        gpsSupplier = !remindData.getGpsInfo().getGpsSupplier().equals(carGpsApply.getGpsSupplier());
        if (gpsPerform) {
            stringBuffer.append("是否履约：[" + ("1".equals(carGpsApply.getGpsPerform()) ? "是" : "否") + "]，改为：[" + ("1".equals(remindData.getGpsInfo().getGpsPerform()) ? "是" : "否") + "]  ");
        }
        if (gpsSupplier) {
            String oldGpsSupplier = GpsSuppEnum.getName(carGpsApply.getGpsSupplier()) != null ? GpsSuppEnum.getName(carGpsApply.getGpsSupplier()) : carGpsApply.getGpsSupplier();
            String newGpsSupplier = GpsSuppEnum.getName(remindData.getGpsInfo().getGpsSupplier()) != null ? GpsSuppEnum.getName(remindData.getGpsInfo().getGpsSupplier()) : remindData.getGpsInfo().getGpsSupplier();
            stringBuffer.append("GPS厂商：[" + oldGpsSupplier + "]，修改为：[" + newGpsSupplier + "]");
        }
        if (gpsPerform || gpsSupplier) {
            CaseRemindDetail caseRemindDetail = new CaseRemindDetail();

            String once = uuid + System.currentTimeMillis();
            //当前登陆用户
            SecurityUtils.getUsername();
            caseRemindDetail.setDisposeUser(user.getUserRealName());
            caseRemindDetail.setDisposeUserId(user.getId().toString());
            caseRemindDetail.setStatus(CancelStatusEnum.EFFECTIVE.getCode());
            caseRemindDetail.setDisposeTime(new Date());
            caseRemindDetail.setUseScene(remindData.getRecord().getUseScene());
            caseRemindDetail.setOperationType(remindData.getRemindOprType());
            caseRemindDetail.setMindId(once);
            caseRemindDetail.setApplyNo(remindData.getGpsInfo().getApplyNo());
            caseRemindDetail.setRemindType(RemindTypeEnum.INNER.getValue());
            caseRemindDetail.setOperationType("save");
            // 内部留言默认 经销商不可见
            caseRemindDetail.setRemindPowers(RemindPowerEnum.UN_VISIBLE.getValue());
            caseRemindDetail.setEditFlag(RemindEditEnum.UN_EDIT.getValue());
            caseRemindDetail.setRemindContent(stringBuffer.toString());
            remindService.save(caseRemindDetail);
        }
        carGpsApply.setGpsSupplier(remindData.getGpsInfo().getGpsSupplier());
        carGpsApply.setGpsPerform(remindData.getGpsInfo().getGpsPerform());
        carGpsApplyService.saveOrUpdate(carGpsApply);

        return IResponse.success("保存成功");
    }


    /**
     * 校验发票查验项
     *
     * @return
     */
    @PostMapping("/checkInvoiceInfo")
    @ApiOperation(value = "校验发票查验项")
    public IResponse checkInvoiceInfo(@RequestBody CarInvoiceInfo info) {
        //发票信息
        CarInvoiceInfo carInvoiceInfo = carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>query().lambda()
                .eq(CarInvoiceInfo::getApplyNo, info.getApplyNo()).eq(CarInvoiceInfo::getInvoiceType, ApplyCarInvoiceEnum.INVOICETYPE_MOTOR.getIndex()));
        boolean buyerName = false;
        boolean buyerIdCardNo = false;
        boolean carVin = false;
        boolean invoiceAmt = false;

        StringBuffer sb = new StringBuffer();//ocr识别项校验
        StringBuffer sb1 = new StringBuffer();//验真项校验

        if (ObjectUtils.isNotEmpty(carInvoiceInfo)) {
            //车辆信息表
            CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda().eq(CaseCarInfo::getApplyNo, info.getApplyNo()));

            CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                    .eq(CaseCustInfo::getApplyNo, info.getApplyNo())
                    .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            //客户信息表
            if (ObjectUtils.isNotEmpty(caseCustInfo)) {
                // 挂靠
                if (StrUtil.equals(caseCarInfo.getAffiliatedType(), "04")) {
                    if (ObjectUtils.isNotEmpty(carInvoiceInfo.getBuyerName())) {
                        if (!carInvoiceInfo.getBuyerName().equals(caseCarInfo.getIndBusinessName())) {
                            sb.append("购买方名称与客户挂靠公司名称不一致；");
                            buyerName = true;
                        }

                    } else {
                        //不比较默认不一致
                        buyerName = true;
                    }
                    if (ObjectUtils.isNotEmpty(carInvoiceInfo.getBuyerIdcardNo())) {
                        if (!carInvoiceInfo.getBuyerIdcardNo().equals(caseCarInfo.getIndBusinessUsci())) {
                            sb.append("客户挂靠公司社会信用代码与发票购买方不一致；");
                            buyerIdCardNo = true;
                        }
                    } else {
                        //不比较默认不一致
                        buyerIdCardNo = true;
                    }
                } else {
                    if (ObjectUtils.isNotEmpty(carInvoiceInfo.getBuyerName())) {
                        if (!carInvoiceInfo.getBuyerName().equals(caseCustInfo.getCustName())) {
                            sb.append("承租人姓名不一致；");
                            buyerName = true;
                        }

                    } else {
                        //不比较默认不一致
                        buyerName = true;
                    }
                    if (ObjectUtils.isNotEmpty(carInvoiceInfo.getBuyerIdcardNo())) {
                        if (!carInvoiceInfo.getBuyerIdcardNo().equals(caseCustInfo.getCertNo())) {
                            sb.append("承租人证件号不一致；");
                            buyerIdCardNo = true;
                        }
                    } else {
                        //不比较默认不一致
                        buyerIdCardNo = true;
                    }
                }

            } else {
                //不比较默认不一致
                buyerName = true;
                buyerIdCardNo = true;
            }

            if (ObjectUtils.isNotEmpty(caseCarInfo)) {
                if (ObjectUtils.isNotEmpty(carInvoiceInfo.getCarVin())) {
                    if (!carInvoiceInfo.getCarVin().equals(caseCarInfo.getCarVin())) {
                        sb.append("车架号不一致；");
                        carVin = true;
                    }
                } else {
                    //不比较默认不一致
                    carVin = true;
                }
                if (ObjectUtils.isNotEmpty(carInvoiceInfo.getEngineNo())) {
                    if (!carInvoiceInfo.getEngineNo().equals(caseCarInfo.getEngineNo())) {
                        sb.append("发动机号不一致；");
                        carVin = true;
                    }
                } else {
                    //不比较默认不一致
                    carVin = true;
                }
                if (ObjectUtils.isNotEmpty(carInvoiceInfo.getInvoiceAmt())) {
                    if (!(carInvoiceInfo.getInvoiceAmt().compareTo(caseCarInfo.getSalePrice()) == 0)) {
                        sb.append("购置价不一致；");
                        invoiceAmt = true;
                    }
                } else {
                    //不比较默认不一致
                    invoiceAmt = true;
                }

            } else {
                //不比较默认不一致
                carVin = true;
                invoiceAmt = true;
            }
            if (buyerName && buyerIdCardNo && carVin && invoiceAmt) {
                sb = new StringBuffer();
                sb.append("发票非本人；");
            }
            //渠道信息
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, info.getApplyNo()));
            if (ObjectUtils.isNotEmpty(caseChannelInfo)) {
                if (ObjectUtils.isNotEmpty(carInvoiceInfo.getMakeInvoiceUnit())) {
                    if (!carInvoiceInfo.getMakeInvoiceUnit().equals(caseChannelInfo.getDealerName())) {
                        if (!applyServiceFeign.checkChannelGroup(
                                new ChannelGroupCheckVO(info.getApplyNo(),carInvoiceInfo.getMakeInvoiceUnit(),
                                        caseChannelInfo.getDealerName())).getData().getExist()) {
                            sb.append("经销商不一致；");
                        }
                    }
                }
            }
            //合同信息
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                    .eq(CaseContractInfo::getApplyNo, info.getApplyNo()));
            if (ObjectUtils.isNotEmpty(caseContractInfo)) {
                if (ObjectUtils.isNotEmpty(carInvoiceInfo.getInvoiceDate())) {
                    if (this.dateDay(carInvoiceInfo.getInvoiceDate(), caseContractInfo.getFinalSubmitTime()) > 30) {
                        sb.append("发票日期超期");
                    }
                }
            }
        }

        List list = new ArrayList();
        if (!ObjectUtils.isNotEmpty(sb)) {
            sb.append("一致！");
        }
        list.add(sb);

        if (!ObjectUtils.isNotEmpty(sb1)) {
            sb1.append("一致！");
        }
        list.add(sb1);

        return new IResponse().setData(list);
    }

    /**
     * 判断相差天数
     * @param startDate
     * @param endDate
     * @return
     */
    private int dateDay(Date startDate, Date endDate) {
        Calendar calendarStartDate = Calendar.getInstance();
        Calendar calendarEndDate = Calendar.getInstance();
        calendarStartDate.setTime(startDate);
        calendarEndDate.setTime(endDate);
        if (startDate.after(endDate)) {
            Calendar swap = calendarStartDate;
            calendarStartDate = calendarEndDate;
            calendarEndDate = swap;
        }

        int days = calendarEndDate.get(6) - calendarStartDate.get(6);
        int y2 = calendarEndDate.get(1);

        while (calendarStartDate.get(1) < y2) {
            days += calendarStartDate.getActualMaximum(6);
            calendarStartDate.add(1, 1);
        }

        return days;
    }

    /**
     * 获取用户信息
     */
    private void getUserInfo(ChannelRiskInfo channelRiskInfo, OutlineVo outlineVo) {
        IResponse info = userDetailsInfoFeign.info(channelRiskInfo.getCustomerManager());
        log.info("获取{}用户信息{}", channelRiskInfo.getCustomerManager(), JSONObject.toJSONString(info));
        if (info.getData() != null) {
            Object data = info.getData();
            log.info("查询用户详细信息{}", JSONObject.toJSONString(data));

            JSONObject jsonObject = (JSONObject) JSON.toJSON(data);
            Object sysUser = jsonObject.get("sysUser");
            JSONObject object = (JSONObject) JSON.toJSON(sysUser);
            String userRealName = object.getString("userRealName");
            String phone = object.getString("phone");

            outlineVo.setManageRealName(userRealName);
            outlineVo.setManagePhone(phone);
        }
    }
    private Map<String, String> applyCaseHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }
    @PostMapping("/saveGuaranteeInfo")
    @ApiOperation(value = "保存保单信息")
    public IResponse saveGuaranteeInfo(@RequestBody ApplyGuarantee guaranteeInfo) {
        UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda().eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
        guaranteeInfo.setUserRealName(userCollocation.getUserRealName());
        applyContractFeign.saveGuaranteeInfo(guaranteeInfo,applyCaseHeader());

        return IResponse.success("保存成功");
    }



    /**
     * 第一次放款审批-记录开始审核时间
     *
     * @return
     */
    @PostMapping("/openApprove")
    @ApiOperation(value = "第一次放款审批-记录开始审核时间")
    public IResponse openApprove(@RequestParam("applyNo") String applyNo) {
        //合同信息
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo, applyNo));
        if (ObjectUtils.isNotEmpty(caseContractInfo) &&
                (StringUtils.isEmpty(caseContractInfo.getIsFirstApproved()) || "0".equals(caseContractInfo.getIsFirstApproved()))
                && (ApplyStatusEnum.LOAN_WAIT_APPROVE.getState().equals(caseContractInfo.getApplyStatus()) ||
                ApplyStatusEnum.LOAN_REPAIR.getState().equals(caseContractInfo.getApplyStatus()) ||
                ApplyStatusEnum.LOAN_RETURN.getState().equals(caseContractInfo.getApplyStatus())
        )) {
            caseContractInfo.setIsFirstApproved("1");
            caseContractInfoService.saveOrUpdate(caseContractInfo);
            AfsUser user = SecurityUtils.getUser();

            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(applyNo);
            record.setUseScene(UseSceneEnum.GENERAL_LOAN.getValue());
            record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
            record.setApproveSuggest("initiate");
            record.setDisposeNodeName("开始审批");
            record.setDisposeStaff(user.getUserRealName());
            record.setApproveEndTime(new Date());
            record.setCreateBy(user.getUsername());
            record.setCreateTime(new Date());
            record.setUpdateBy(user.getUsername());
            record.setUpdateTime(new Date());
            record.setDelFlag("0");
            record.setApproveSuggestName("开始审批");
            caseApproveRecordService.save(record);

        }
        return IResponse.success(true);
    }


    @ApiOperation("通过申请编号查询对应的车辆类型和抵押要求")
    @PostMapping(value = "/getCarTypeByApplyNo")
    public IResponse<CarTypeVo> getCarTypeByApplyNo(@RequestBody ApplyNoDTO applyNoDTO){

        // 查询车辆类型
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .select(CaseBaseInfo::getCarType)
                .eq(CaseBaseInfo::getApplyNo, applyNoDTO.getApplyNo()));

        if(caseBaseInfo != null && caseBaseInfo.getCarType() != null && !caseBaseInfo.getCarType().isEmpty()){

            CarTypeVo carTypeVo = new CarTypeVo();
            carTypeVo.setCarType(caseBaseInfo.getCarType());
            CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery()
                    .select(CaseContractInfo::getMortgageClaim,CaseContractInfo::getLendingMode,CaseContractInfo::getIsTopChannel)
                    .eq(CaseContractInfo::getApplyNo, applyNoDTO.getApplyNo()));
            if(caseContractInfo.getLendingMode() != null && !caseContractInfo.getLendingMode().isEmpty()){
                carTypeVo.setLendingMode(caseContractInfo.getLendingMode());
            }
            if(caseContractInfo.getIsTopChannel() != null && !caseContractInfo.getIsTopChannel().isEmpty()){
                carTypeVo.setIsTopChannel(caseContractInfo.getIsTopChannel());
            }
            if(caseContractInfo.getMortgageClaim() != null && !caseContractInfo.getMortgageClaim().isEmpty()){
                carTypeVo.setMortgageClaim(caseContractInfo.getMortgageClaim());
            }
            return IResponse.success(carTypeVo);

        }else{
            return IResponse.fail("");
        }

    }

}
