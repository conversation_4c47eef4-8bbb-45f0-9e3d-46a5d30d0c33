package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffMonthConfig;
import com.ruicar.afs.cloud.afscase.writeoff.enums.StatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffMonthConfigService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 服务费账期配置
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffMonth")
public class WriteOffMonthController {

    private final WriteOffMonthConfigService writeOffMonthConfigService;
    private final AfsRuleFeign afsRuleInfoService;

    @PostMapping("/queryList")
    @ApiOperation(value = "查询服务费账期配置数据")
    public IResponse queryList(@RequestBody QueryCondition<WriteOffMonthConfig> queryCondition) {
        WriteOffMonthConfig condition = queryCondition.getCondition();
        Page<WriteOffMonthConfig> page = writeOffMonthConfigService.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffMonthConfig>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getRuleName()), WriteOffMonthConfig::getRuleName, condition.getRuleName())
                .orderByDesc(WriteOffMonthConfig::getUpdateTime));
        return IResponse.success(page);
    }

    @PostMapping("/saveData")
    @ApiOperation(value = "保存服务费账期数据")
    public IResponse saveData(@RequestBody WriteOffMonthConfig writeOffMonthConfig) {
        Assert.isTrue(StrUtil.isNotBlank(writeOffMonthConfig.getRuleName()), "规则名称不能为空");
        Integer startDay = writeOffMonthConfig.getStartDay();
        Assert.isTrue(startDay != null && startDay > 0 && startDay < 32, "开始日不符合要求");
        Integer thisEndDay = writeOffMonthConfig.getThisEndDay();
        Integer nextEndDay = writeOffMonthConfig.getNextEndDay();
        if (thisEndDay == null && nextEndDay != null) {
            Assert.isTrue(nextEndDay > 0 && nextEndDay < 32, "结束日不符合要求");
        } else if (thisEndDay != null && nextEndDay == null) {
            Assert.isTrue(thisEndDay > 0 && thisEndDay < 32, "结束日不符合要求");
        } else {
            throw new RuntimeException("结束日不符合要求");
        }
        writeOffMonthConfig.setId(null);
        writeOffMonthConfig.setStatus(StatusEnum.NO.getCode());
        writeOffMonthConfigService.save(writeOffMonthConfig);
        return IResponse.success("保存成功");
    }

    @PostMapping("/editData")
    @ApiOperation(value = "修改服务费账期数据")
    public IResponse editData(@RequestBody WriteOffMonthConfig writeOffMonthConfig) {
        WriteOffMonthConfig monthConfig = writeOffMonthConfigService.getById(writeOffMonthConfig.getId());
        Assert.isTrue(monthConfig != null && StatusEnum.NO.getCode().equals(monthConfig.getStatus()), "数据异常，不能修改");
        Assert.isTrue(StrUtil.isNotBlank(writeOffMonthConfig.getRuleName()), "规则名称不能为空");
        Integer startDay = writeOffMonthConfig.getStartDay();
        Assert.isTrue(startDay != null && startDay > 0 && startDay < 32, "开始日不符合要求");
        Integer thisEndDay = writeOffMonthConfig.getThisEndDay();
        Integer nextEndDay = writeOffMonthConfig.getNextEndDay();
        if (thisEndDay == null && nextEndDay != null) {
            Assert.isTrue(nextEndDay > 0 && nextEndDay < 32, "结束日不符合要求");
        } else if (thisEndDay != null && nextEndDay == null) {
            Assert.isTrue(thisEndDay > 0 && thisEndDay < 32, "结束日不符合要求");
        } else {
            throw new RuntimeException("结束日不符合要求");
        }
        writeOffMonthConfigService.updateById(writeOffMonthConfig);
        return IResponse.success("修改成功");
    }

    @PostMapping(value = "/updateStatus")
    @ApiOperation(value = "修改生效状态")
    @Transactional(rollbackFor = Exception.class)
    public IResponse updateStatus(@RequestParam Long id) {
        WriteOffMonthConfig monthConfig = writeOffMonthConfigService.getById(id);
        Assert.isTrue(monthConfig != null, "id不正确");
        if (StatusEnum.YES.getCode().equals(monthConfig.getStatus())) {
            // 生效改无效
            afsRuleInfoService.deActiveRuleByRuleId(monthConfig.getRuleId());
            monthConfig.setStatus(StatusEnum.NO.getCode());
        } else {
            // 无效改生效
            Assert.isTrue(monthConfig.getRuleId() != null, "【" + monthConfig.getRuleName() + "】规则未配置！");
            afsRuleInfoService.activeRuleByRuleId(monthConfig.getRuleId());
            monthConfig.setStatus(StatusEnum.YES.getCode());
        }
        writeOffMonthConfigService.updateById(monthConfig);
        return IResponse.success(null);
    }

    @PostMapping(value = "/delById")
    @ApiOperation(value = "通过id删除账期规则")
    @Transactional(rollbackFor = Exception.class)
    public IResponse deleteById(@RequestParam Long id) {
        WriteOffMonthConfig monthConfig = writeOffMonthConfigService.getById(id);
        Assert.isTrue(StatusEnum.NO.getCode().equals(monthConfig.getStatus()), "生效的账期规则不可删除");
        if (monthConfig.getRuleId() != null) {
            afsRuleInfoService.deleteRule(monthConfig.getRuleId());
        }
        writeOffMonthConfigService.removeById(id);
        return IResponse.success(null);
    }

}
