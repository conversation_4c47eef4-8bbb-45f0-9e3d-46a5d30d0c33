package com.ruicar.afs.cloud.afscase.workflow.event;

import com.ruicar.afs.cloud.afscase.processor.enums.NormalSubmitType;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class AffiliatedFilingEvent extends ApplicationEvent {
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 流程实例ID
     */
    private String stageId;

    /**
     * 审批建议;审批建议、复议建议
     */
    private String approveSuggest;
    /**
     * 审批建议翻译值
     */
    private String approveSuggestName;
    /**
     * 审批原因;审批原因、复议要求
     */
    private String approveReason;
    /**
     * 审批备注;审批备注、复议意见
     */
    private String approveRemark;
    /**
     * 处理人员
     */
    private String disposeStaff;

    private Boolean withLog;

    private NormalSubmitType normalSubmitType;

    public AffiliatedFilingEvent(Object source, String applyNo, String stageId, String approveSuggest,
                             String approveSuggestName, String approveReason, String approveRemark, String disposeStaff, Boolean withLog) {
        super(source);
        this.applyNo = applyNo;
        this.stageId = stageId;
        this.approveSuggest = approveSuggest;
        this.approveSuggestName = approveSuggestName;
        this.approveReason = approveReason;
        this.approveRemark = approveRemark;
        this.disposeStaff = disposeStaff;
        this.withLog = withLog;
    }

    public AffiliatedFilingEvent(Object source, String applyNo, String stageId, NormalSubmitType submitType,
                             String disposeStaff, String approveReason, String approveRemark) {
        this(source, applyNo, stageId, AfsEnumUtil.key(submitType), AfsEnumUtil.desc(submitType), null, null, disposeStaff, false);
        this.normalSubmitType = submitType;
        this.approveRemark = approveRemark;
        this.approveReason = approveReason;
    }


}
