package com.ruicar.afs.cloud.afscase.workflow;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CasePassRecord;
import com.ruicar.afs.cloud.afscase.approvetask.enums.FlowBackOption;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.CasePassRecordService;
import com.ruicar.afs.cloud.afscase.casemaininfo.service.CaseMainInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.message.service.CaseSmsSendRecordService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkTaskApproveRecord;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.TaskTakenParam;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.TaskTransferParam;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.MainEventTypeEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.WorkflowNodeEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkTaskApproveRecordService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.impl.WorkflowWrapperService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanProcessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CaseMainUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.StatusDTO;
import com.ruicar.afs.cloud.common.modules.enums.CaseCodeEnum;
import com.ruicar.afs.cloud.seats.entity.MainEventLog;
import com.ruicar.afs.cloud.seats.service.MainEventLogService;
import com.ruicar.afs.cloud.workflow.sdk.dto.group.UserTaskStatistics;
import com.ruicar.afs.cloud.workflow.sdk.dto.group.request.RequestUser;
import com.ruicar.afs.cloud.workflow.sdk.dto.group.request.ResponseUser;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.FlowVariable;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.ProcessInstanceDto;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.StartFlowRequest;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.TakenTask;
import com.ruicar.afs.cloud.workflow.sdk.dto.run.TaskOperation;
import com.ruicar.afs.cloud.workflow.sdk.enums.CallBackProtocol;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowGroupManaFeign;
import com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WorkflowHelper {

    private FlowRunFeign flowRunFeign;

    private WorkflowProcessBusinessRefInfoService processBusinessRefInfoService;

    private WorkflowTaskInfoService taskInfoService;

    private CaseApproveRecordService recordService;

    private WorkflowWrapperService workflowWrapperService;

    @Autowired
    private MainEventLogService mainEventLogService;
    @Autowired
    private CaseMainInfoService caseMainInfoService;
    @Autowired
    private CaseBaseInfoService caseBaseInfoService;
    @Autowired
    private CaseContractInfoService caseContractInfoService;
    @Autowired
    private WorkTaskApproveRecordService workTaskApproveRecordService;

    @Autowired
    private CaseSmsSendRecordService caseSmsSendRecordService;

    @Value("${spring.application.name}")
    private String appName;

    @Value("${com.ruicar.afs.systemCode}")
    private String sysCode;
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private FlowGroupManaFeign flowGroupManaFeign;

    public WorkflowHelper(FlowRunFeign flowRunFeign,
        WorkflowWrapperService workflowWrapperService,
        WorkflowProcessBusinessRefInfoService processBusinessRefInfoService,
        WorkflowTaskInfoService taskInfoService,
        CaseApproveRecordService service,
        StringRedisTemplate stringRedisTemplate) {
        this.flowRunFeign = flowRunFeign;
        this.workflowWrapperService = workflowWrapperService;
        this.processBusinessRefInfoService = processBusinessRefInfoService;
        this.taskInfoService = taskInfoService;
        this.recordService = service;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 发起
     *
     * @param requestBo
     * @return
     */
    public IResponse startFlow(@Validated StartFlowRequestBo requestBo, UseSceneEnum useSceneEnum) {

        // record
        final CaseApproveRecord record = workflowWrapperService.assembleRecord(requestBo, useSceneEnum);
        recordService.save(record);

        // 暂存发起记录
        final WorkflowProcessBusinessRefInfo refInfo = processBusinessRefInfoService.saveBusinessInfo(requestBo);

        // 发起流程
        final StartFlowRequest request = new StartFlowRequest();
        BeanUtils.copyProperties(requestBo, request);

        request.setProtocol(CallBackProtocol.LB);
        request.setCallBackPath(appName);
        request.setSystemCode(sysCode);

        // 植入业务单号
        if (request.getParams() == null) {
            request.setParams(new JSONObject());
        }
        request.getParams().put(FlowConstant.BUSINESS_NO, requestBo.getBusinessNo());

        IResponse<ProcessInstanceDto> response = null;
        try {
            if (stringRedisTemplate.opsForValue().setIfAbsent("flow_biz_" + requestBo.getBusinessNo() , useSceneEnum.getValue(),10, TimeUnit.SECONDS)) {
                log.info("startFlow_request:"+request);
                response = flowRunFeign.startFlow(request);
            }else {
                response = IResponse.fail("发起中，请稍后查看");
            }
        } catch (Throwable throwable) {
            log.error("invoke process start error.", throwable);
            return IResponse.fail(throwable.getMessage());
        } finally {
            if (response == null) {

            } else if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
                final ProcessInstanceDto data = response.getData();
                refInfo.setProcessInstanceId(data.getFlowInstanceId());
                refInfo.setVersion(data.getTemplateVersion());

                processBusinessRefInfoService.updateById(refInfo);

                if (UseSceneEnum.AFFILIATED_COMPANY_FILING_NEW.equals(useSceneEnum)){
                    WorkTaskApproveRecord approveRecord = new WorkTaskApproveRecord();
                    BeanUtils.copyProperties(refInfo, approveRecord);
                    approveRecord.setBizDataId(requestBo.getBusinessNo());
                    approveRecord.setBusinessId(requestBo.getParams().get(FlowConstant.BUSINESS_ID).toString());
                    approveRecord.setBusinessType(requestBo.getParams().get(FlowConstant.BUSINESS_TYPE).toString());
                    approveRecord.setApprovalUser(requestBo.getParams().get(FlowConstant.APPROVAL_USER).toString());
                    approveRecord.setApprovalOpinion(requestBo.getParams().get(FlowConstant.APPROVAL_OPINION).toString());
                    approveRecord.setTaskNodeName(requestBo.getParams().get(FlowConstant.TASK_NODE_NAME).toString());
                    workTaskApproveRecordService.save(approveRecord);
                }else {
                    // record
                    record.setStageId(data.getFlowInstanceId());
                    recordService.updateById(record);
                }
            } else {
                // remove record
                recordService.removeById(record.getId());
                log.warn("invoke process start error. code: {} msg: {}", response.getCode(), response.getMsg());
            }
        }

        return response;
    }

    /**
     * 提交任务(审批) 包括：同意/拒绝/退回
     *
     * @param param
     * @return
     */
    public IResponse<Boolean> submitTask(SubmitTaskParam param, UseSceneEnum useSceneEnum) {
        final WorkflowTaskInfo taskInfo = workflowWrapperService.getTaskInfo(param.getTaskId());
        CaseBaseInfo caseBaseInfo = null;

        if (AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR).equals(taskInfo.getFlowTemplateId())
                ||AfsEnumUtil.key(WorkflowNodeEnum.LOAN).equals(taskInfo.getFlowTemplateId())
                ||SpringContextHolder.getBean(FlowConfigProperties.class).getReconTemplateId().equals(taskInfo.getFlowTemplateId())){
            //获取申请编号
            caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo, taskInfo.getBusinessNo()));
            // 获取合同信息
            caseContractInfoService.getBaseMapper().selectOne(new QueryWrapper<CaseContractInfo>().eq("apply_no",taskInfo.getBusinessNo()));
        }

        // 案件审批记录信息
        final CaseApproveRecord record = workflowWrapperService.assembleRecord(param, taskInfo, useSceneEnum);
        if(ObjectUtils.isNotEmpty(record.getId())){
            recordService.updateById(record);
        }else {
            recordService.save(record);
        }
        CasePassRecord casePassRecord = new CasePassRecord();


        if (AfsEnumUtil.key(WorkflowNodeEnum.LOAN).equals(taskInfo.getFlowTemplateId())) {
            BeanUtils.copyProperties(record, casePassRecord);
            casePassRecord.setSubmitState(WhetherEnum.NO.getCode());
            casePassRecord.setPassType(param.getPassType());
            casePassRecord.setPassReason(param.getPassReason());
            casePassRecord.setContractNo(param.getContractNo());
            casePassRecord.setTaskId(taskInfo.getTaskId());
            casePassRecord.setApproveRemark(param.getRemark());
            casePassRecord.setPreviousTaskId(taskInfo.getPreviousTaskId());
            log.info("case_approve_record审批记录数据{}", JSONObject.toJSON(record));
            SpringContextHolder.getBean(CasePassRecordService.class).save(casePassRecord);
        }
        if (ObjectUtils.isNotEmpty(param.getBackOption()) && StringUtils.isNotEmpty(param.getBackOption().name())) {
            taskInfo.setFlowBackOption(param.getBackOption() == FlowBackOption.DIRECT? AfsEnumUtil.key(FlowBackOption.DIRECT):AfsEnumUtil.key(FlowBackOption.REVIEW));
            param.setFlowBackOption(taskInfo.getFlowBackOption());
        }
        taskInfoService.saveTaskBeforeSubmit(param, taskInfo.getId());

        // 增加埋点事件，案件出岗（审批出岗）记录
        if (StringUtils.isNotEmpty(taskInfo.getEventGroupId())) {
            MainEventLog mainEventLog = new MainEventLog();
            mainEventLog.setApplyNo(taskInfo.getBusinessNo());
            mainEventLog.setProcessType(taskInfo.getFlowTemplateId());
            mainEventLog.setEventType(MainEventTypeEnum.FINISH_TASK.getCode());
            mainEventLog.setProcessInstanceId(taskInfo.getProcessInstanceId());
            mainEventLog.setNodeName(taskInfo.getUserDefinedIndex());
            mainEventLog.setTaskId(taskInfo.getTaskId());
            mainEventLog.setEventGroupId(taskInfo.getEventGroupId());
            mainEventLog.setUserId(taskInfo.getAssign());
            mainEventLog.setStartTime(new Date());
            mainEventLog.setEventResult(param.getOperationType());
            mainEventLogService.save(mainEventLog);
        }

        IResponse<Boolean> response = null;

        // 标记当前操作  回调中使用
        JSONObject jsonObject = Optional.ofNullable(param.getExtendParams())
            .orElse(new JSONObject());
        jsonObject.put(FlowConstant.BUSINESS_NO, taskInfo.getBusinessNo());
        jsonObject.put(FlowConstant.LAST_OPERATION, param.getOperationType().toUpperCase());
        jsonObject.put(FlowConstant.LAST_APPROVE_REASON, param.getApproveSuggest());
        jsonObject.put(FlowConstant.LAST_APPROVE_REMARK, param.getRemark());
        jsonObject.put(FlowConstant.BIZ_OPERATION_TYPE, param.getOperationType());
        jsonObject.put("loanAutoApproveCallBack","yes");
        if (LoanProcessTypeEnum.AUTO_SUBMIT.getCode().equals(param.getPassType())) {//区分是否自动审批
            jsonObject.put(FlowConstant.LAST_OPERATOR_LOGIN_NAME, param.getUseName());
        } else if (LoanProcessTypeEnum.XC_APPROVE.getCode().equals(param.getPassType())) {//区分星程审核单
            jsonObject.put(FlowConstant.LAST_OPERATOR_LOGIN_NAME, "星程审核");
        } else if (LoanProcessTypeEnum.AUTO_LOAN_CANCEL.getCode().equals(param.getPassType())) {
            jsonObject.put(FlowConstant.LAST_OPERATOR_LOGIN_NAME,taskInfo.getAssign());
        } else {
            jsonObject.put(FlowConstant.LAST_OPERATOR_LOGIN_NAME, SecurityUtils.getUsername());
        }
        if (UseSceneEnum.AFFILIATED_COMPANY_FILING_NEW.equals(useSceneEnum)){
            param.setExtendParams(jsonObject);
        }
        FlowTaskOperationEnum operationEnum = null;

        log.info("复议申请工作流申请数据2：{}",JSONObject.toJSONString(param));
        try {
            operationEnum = FlowTaskOperationEnum.valueOf(param.getOperationType().toUpperCase());
            FlowBackOption backOpt = param.getBackOption();
            param.setBackOption(null);
            switch (operationEnum) {
                case REFUSE:
                    response = workflowWrapperService.refuseTask(param, response);
                    //修改主状态
                    this.updateCaseMainByRefuse(taskInfo,caseBaseInfo);
                    break;
                case BACK:
                    response = workflowWrapperService.backTask(param, response);
                    //修改主状态
                    this.updateCaseMainByBack(taskInfo,caseBaseInfo);
                    break;
                case SUBMIT:
                    //通过，备注没有写时，默认空字符串
//                    if(StringUtil.isBlank ( param.getRemark () )){
//                        param.setRemark ( "-" );
//                    }
                    if (StringUtils.isEmpty(param.getRemark())) {
                        param.setRemark("通过");
                    }
                    response = workflowWrapperService.agreeTask(param, response);
                    if(!param.getFinallyAgree()){
                        break;
                    }

                    //修改主状态
                    this.updateCaseMainBySubmit(taskInfo,caseBaseInfo);

                    break;
                case INSPECTION:
                    //已质检
                    if (StringUtils.isEmpty(param.getRemark())) {
                        param.setRemark("已质检");
                    }
                    response = workflowWrapperService.agreeTask(param, response);
                    break;
                case BACK2DEALER:
                    param.setBackOption(backOpt);
                    response = workflowWrapperService.back2dealerTask(param, response);
                    //修改主状态
                    this.updateCaseMainByBack2dealer(taskInfo,caseBaseInfo);
                    break;
                case ANTIFRAUD:
                    if (StringUtils.isEmpty(param.getRemark())) {
                        param.setRemark("反欺诈审核");
                    }
                    response = workflowWrapperService.agreeTask(param, response);
                    break;
                case VOTE_AGREE:
                    response = workflowWrapperService.voteAgreeTask(param, response);
                    break;
                case VOTE_DISAGREE:
                    response = workflowWrapperService.voteDisagreeTask(param, response);
                    break;
                case VOTE_ABSTAINED:
                    response = workflowWrapperService.voteAbstainedTask(param, response);
                    break;
                case REFUSECHECK:
                    response = workflowWrapperService.agreeTask(param,response);
                    break;
                case FACEREVIEW:
                    response = workflowWrapperService.faceReviewTask(param, response);
                    break;
                default:
                    log.warn("no match workflow operation.");
            }
        } catch (Throwable throwable) {
            log.error("invoke flow {} error.", param.getOperationType(), throwable);
            return IResponse.fail(throwable.getMessage());
        } finally {
            if (response == null) {
                record.setApproveSuggestName(record.getApproveSuggestName() + " - 异常");
                recordService.updateById(record);
            } else if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
                    // 结束本地任务
                    final WorkflowTaskInfo newTaskInfo = new WorkflowTaskInfo();
                    newTaskInfo.setStatus(FlowStatusEnum.END.getCode());
                    newTaskInfo.setId(taskInfo.getId());
                    taskInfoService.updateById(newTaskInfo);
                if (UseSceneEnum.AFFILIATED_COMPANY_FILING_NEW.equals(useSceneEnum)){
                    // 查询之前的流程信息
                    WorkTaskApproveRecord approveRecord =new WorkTaskApproveRecord();
                    org.springframework.beans.BeanUtils.copyProperties(taskInfo,approveRecord,new String[]{"id"});
                    approveRecord.setTaskNodeName(taskInfo.getTaskNodeName());
                    approveRecord.setBizDataId(taskInfo.getBusinessNo());
                    approveRecord.setApprovalUser(SecurityUtils.getUser().getUserRealName());
                    approveRecord.setBusinessId(taskInfo.getBusinessNo());
                    approveRecord.setBusinessType("01");
                    approveRecord.setRemark(param.getRemark());
                    approveRecord.setApprovalOpinion(operationEnum.getDesc());
                    approveRecord.setCreateBy(SecurityUtils.getUsername());
                    approveRecord.setApprovalOpinionDescribe(param.getRemark());
                    workTaskApproveRecordService.save(approveRecord);
                }
            } else {
                // remove record
                recordService.removeById(record.getId());
                if (casePassRecord.getId() != null) {
                    recordService.removeById(casePassRecord.getId());
                }
                log.warn("invoke flow {} error. code: {} msg: {}", param.getOperationType(), response.getCode(), response.getMsg());
            }
        }

        return response;
    }

    /**
     * 退回经销商专用
     *
     * 恢复流程(含本地)
     *
     * @param processInstanceId
     * @return
     */
    public IResponse resumeProcess(String processInstanceId) {
        log.info("signalInstance=====00");
        final IResponse<Boolean> response = flowRunFeign.signalInstance(processInstanceId);
        log.info("signalInstance=====10");
        return response;
    }

    /**
     * 持有人转交
     *
     * @param param
     * @return
     * @throws '如果登陆人不是持有人'
     */
    public IResponse transfer(TaskTransferParam param, UseSceneEnum useSceneEnum) {
        return workflowWrapperService.transfer(param, false, useSceneEnum);
    }

    /**
     * 领取任务
     * <p>
     * 只有特定任务可以被领取，takenable = true
     *
     * @return
     */
    public IResponse takenTask(TaskTakenParam param, UseSceneEnum useSceneEnum) {
        final WorkflowTaskInfo info = workflowWrapperService.getTaskInfo(param.getTaskId());

        final TakenTask takenTask = new TakenTask();
        takenTask.setTaskId(param.getTaskId());
        takenTask.setUserId(SecurityUtils.getUsername());
        takenTask.setSystemCode(sysCode);

        IResponse<Boolean> response = null;
        try {
            response = flowRunFeign.takenTask(takenTask);
        } catch (Throwable throwable) {
            log.error("invoke flow take taken error.", throwable);
            return IResponse.fail(throwable.getMessage());
        } finally {
            if (response == null) {

            } else if (CaseConstants.CODE_SUCCESS.equals(response.getCode())) {
                final WorkflowTaskInfo taskInfo = new WorkflowTaskInfo();
                taskInfo.setId(info.getId());
                taskInfo.setAssign(takenTask.getUserId());

                taskInfoService.updateById(taskInfo);

            } else {
                log.warn("invoke flow task taken error. code:{} msg:{}", response.getCode(), response.getMsg());
            }
        }

        return response;

    }

    /**
     * 管理员/组长  指定任务处理人
     * <p>
     * 如果任务可以被领取 - 领取 不能领取 - 转交（无视持有人）
     *
     * @param param
     * @return
     */
    public IResponse assignByManager(TaskTransferParam param, UseSceneEnum useSceneEnum) {
        final WorkflowTaskInfo taskInfo = workflowWrapperService.getTaskInfo(param.getTaskId());
        if (taskInfo.getTakenable()) {
            final TaskTakenParam takenParam = new TaskTakenParam();
            takenParam.setTaskId(param.getTaskId());
            takenParam.setUserId(param.getTargetUserId());
            return takenTask(takenParam, useSceneEnum);
        } else {
            return workflowWrapperService.transfer(param, true, useSceneEnum);
        }
    }

    /**
     * 恢复挂起的流程
     *
     * @param processInstanceId
     * @return
     */
    public IResponse resumeFlow(String processInstanceId) {
        return flowRunFeign.resumeFlow(processInstanceId);
    }

    /**
     * 查询工作流提供的 可执行操作 submit/back/skip
     * <p>
     * 场景一，退回   前端选择退回到某个节点 场景二，拒绝   前端选择跳过到结束节点
     *
     * @param taskId
     * @return
     */
    public IResponse<List<TaskOperation>> queryTaskOperationList(String taskId) {
        return flowRunFeign.getTaskOperateList(taskId);
    }

    /**
     * 登陆人 任务统计（工作流系统视角）
     * <p>
     * if some error o
     *
     * @return
     */
    public IResponse<List<UserTaskStatistics>> queryUserStatistics() {
        return flowRunFeign.queryUserStatistics(sysCode, SecurityUtils.getUsername(), CommonConstants.COMMON_YES);
    }

    /**
     * 修改参数值
     * <p>
     * if some error o
     *
     * @return
     */
    public IResponse setFlowVariableByFlowInstance(FlowVariable flowVariable) {
        return flowRunFeign.setFlowVariableByFlowInstance(flowVariable);
    }

    /**
     * 工作流拒绝，修改主状态
     * @param taskInfo 工作流任务表
     */
    private void updateCaseMainByRefuse(WorkflowTaskInfo taskInfo,CaseBaseInfo caseBaseInfo){
        if (ObjectUtils.isEmpty(caseBaseInfo)){
            return;
        }
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());

        //获取信审流程
        if (AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR).equals(taskInfo.getFlowTemplateId())){
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("CREDIT_CAR_"+taskInfo.getUserDefinedIndex());
            if (ObjectUtils.isEmpty(workflowNodeEnum)){
                return;
            }
            switch (workflowNodeEnum){
                case CREDIT_CAR_FIRST_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3120);
                    break;
                case CREDIT_CAR_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3220);
                    break;
                case CREDIT_CAR_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3320);
                    break;
                case CREDIT_CAR_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3420);
                    break;
                case CREDIT_CAR_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3520);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");
            }
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }

    }

    /**
     * 工作流退回，修改主状态
     * @param taskInfo 工作流任务表
     */
    private void updateCaseMainByBack(WorkflowTaskInfo taskInfo,CaseBaseInfo caseBaseInfo){
        //获取申请编号
        if (ObjectUtils.isEmpty(caseBaseInfo)){
            return;
        }
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
        //获取信审流程
        if (AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR).equals(taskInfo.getFlowTemplateId())){
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("CREDIT_CAR_"+taskInfo.getUserDefinedIndex());
            switch (workflowNodeEnum){
                case CREDIT_CAR_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3231);
                    break;
                case CREDIT_CAR_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3331);
                    break;
                case CREDIT_CAR_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3431);
                    break;
                case CREDIT_CAR_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3531);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");
            }
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        else if (AfsEnumUtil.key(WorkflowNodeEnum.LOAN).equals(taskInfo.getFlowTemplateId())){
            AfsEnumUtil.key(WorkflowNodeEnum.LOAN);
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("LOAN_"+taskInfo.getUserDefinedIndex());

            switch (workflowNodeEnum){
                case LOAN_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4231);
                    break;
                case LOAN_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4331);
                    break;
                case LOAN_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4431);
                    break;
                case LOAN_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4531);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");

            }
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }

    }

    /**
     * 工作流提交，修改主状态
     * @param taskInfo 工作流任务表
     */
    private void updateCaseMainBySubmit(WorkflowTaskInfo taskInfo,CaseBaseInfo caseBaseInfo){
        if (ObjectUtils.isEmpty(caseBaseInfo)){
            return;
        }
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
        //获取信审流程
        if (AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR).equals(taskInfo.getFlowTemplateId())){
            AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR);
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("CREDIT_CAR_"+taskInfo.getUserDefinedIndex());
            switch (workflowNodeEnum){
                case CREDIT_CAR_FIRST_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3110);
                    break;
                case CREDIT_CAR_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3210);
                    break;
                case CREDIT_CAR_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3310);
                    break;
                case CREDIT_CAR_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3410);
                    break;
                case CREDIT_CAR_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3510);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");
            }
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        else if (AfsEnumUtil.key(WorkflowNodeEnum.LOAN).equals(taskInfo.getFlowTemplateId())){
            AfsEnumUtil.key(WorkflowNodeEnum.LOAN);
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("LOAN"+"_"+taskInfo.getUserDefinedIndex());
            switch (workflowNodeEnum){
                case LOAN_FIRST_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4110);
                    break;
                case LOAN_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4210);
                    break;
                case LOAN_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4310);
                    break;
                case LOAN_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4410);
                    break;
                case LOAN_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4510);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");

            }
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }

    }

    /**
     * 工作流退回经销商，修改主状态
     * @param taskInfo 工作流任务表
     */
    private void updateCaseMainByBack2dealer(WorkflowTaskInfo taskInfo,CaseBaseInfo caseBaseInfo){
        if (ObjectUtils.isEmpty(caseBaseInfo)){
            return;
        }
        CaseMainUpdateCondition caseMainUpdateCondition = new CaseMainUpdateCondition();
        caseMainUpdateCondition.setApplyNo(caseBaseInfo.getApplyNo());
        caseMainUpdateCondition.setPreApplyNo(caseBaseInfo.getOrderId());
        StatusDTO caseStatusDTO = new StatusDTO();
        //获取信审流程
        if (AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR).equals(taskInfo.getFlowTemplateId())){
            AfsEnumUtil.key(WorkflowNodeEnum.CREDIT_CAR);
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("CREDIT_CAR_"+taskInfo.getUserDefinedIndex());
            switch (workflowNodeEnum){
                case CREDIT_CAR_FIRST_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3130);
                    break;
                case CREDIT_CAR_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3230);
                    break;
                case CREDIT_CAR_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3330);
                    break;
                case CREDIT_CAR_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3430);
                    break;
                case CREDIT_CAR_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_3530);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");
            }
            caseStatusDTO.setStatusCode(AfsEnumUtil.key(BusinessStateInEnum.REVISE_PARSE));
            caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(BusinessStateInEnum.REVISE_PARSE));
            caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }
        else if (AfsEnumUtil.key(WorkflowNodeEnum.LOAN).equals(taskInfo.getFlowTemplateId())){
            AfsEnumUtil.key(WorkflowNodeEnum.LOAN);
            WorkflowNodeEnum workflowNodeEnum = WorkflowNodeEnum.valueOf("LOAN_"+taskInfo.getUserDefinedIndex());

            switch (workflowNodeEnum){
                case LOAN_FIRST_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4130);
                    break;
                case LOAN_SECOND_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4230);
                    break;
                case LOAN_DEPT_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4330);
                    break;
                case LOAN_DEPT_DIRECTOR_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4430);
                    break;
                case LOAN_GENERAL_MANAGER_NODE:
                    caseMainUpdateCondition.setStatus(CaseCodeEnum.CASE_4530);
                    break;
                default:
                    log.info("NoMatchTaskNodeEnumIn:credit-car!");

            }
            caseStatusDTO.setStatusCode(AfsEnumUtil.key(ApplyStatusEnum.LOAN_RETURN));
            caseStatusDTO.setStatusDescription(AfsEnumUtil.desc(ApplyStatusEnum.LOAN_RETURN));
            caseMainUpdateCondition.setCaseStatusDTO(caseStatusDTO);
            caseMainInfoService.updateCaseMain(caseMainUpdateCondition);
        }

    }


    /**
     * 草稿预审批撤回
     */
    public IResponse<Boolean> cancelFlow(String processInstanceId) {
        return flowRunFeign.cancelFlow(processInstanceId);
    }

    /**
     * 流程异常恢复方法
     * @param exceptionId
     * @return
     */
    public IResponse exceptionRetry(String exceptionId){
        final IResponse<Boolean> response = flowRunFeign.exceptionRetry(exceptionId);
        return response;
    }


    /**
     * 异常待提交，指定处理人
     *
     * @param
     * @return
     */
    public IResponse assignTaskWithNoSuitUserException(String userId,String flowParseExceptionId) {
        final IResponse<Boolean> response = flowRunFeign.assignTaskWithNoSuitUserException(userId,flowParseExceptionId);
        return response;
    }

    public IResponse suspendFlow(String processinstanceid){
        final IResponse<Boolean> response = flowRunFeign.suspendFlow(processinstanceid);
        return response;
    }

    public IResponse changeUserTotalTaskLimit(String userId,Integer totTaskLimit){
        final IResponse<Boolean> response = flowGroupManaFeign.changeUserTotalTaskLimit(sysCode,userId,totTaskLimit);
        return response;
    }

    public IResponse<List<FlowVariable>> listVariables(String flowInstanceId){
        IResponse<List<FlowVariable>> response = flowRunFeign.listVariables(flowInstanceId);
        return response;
    }

    public IResponse listUser(String groupId){
        RequestUser requestUser = new RequestUser();
        requestUser.setGroupId(groupId);
        requestUser.setSystemCode(sysCode);
        requestUser.setPageIndex(1L);
        requestUser.setPageSize(1000L);
        final IResponse<ResponseUser> response =  flowGroupManaFeign.listUser(requestUser);
        return response;
    }
}
