package com.ruicar.afs.cloud.afscase.apply.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("filing_product_group")
public class FilingProductGroup extends BaseEntity<FilingProductGroup> {

    /**
     * 产品组名称
     */
    private String productGroupName;
    /**
     * 父ID
     */
    private String parentId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 状态
     */
    private String status;

    @TableField(exist = false)
    private String title;
    @TableField(exist = false)
    private String value;

}
