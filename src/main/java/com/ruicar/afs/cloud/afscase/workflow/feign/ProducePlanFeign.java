package com.ruicar.afs.cloud.afscase.workflow.feign;

import com.ruicar.afs.cloud.common.core.util.IResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * The interface Produce plan feign.
 */
@FeignClient(name = "${com.ruicar.service-names.product-server:product-server}",contextId = "ProducePlanFeign-productPlan")
public interface ProducePlanFeign {

    /**
     * Gets product by id.
     *
     * @param productId the product id
     * @return the product by id
     */
    @PostMapping("/productPlan/getPlanInfo")
    IResponse getProductById(@RequestParam("productId") String productId);

    /**
     * 查询产品对应id
     * @param list
     * @return
     */
    @PostMapping("/productPlan/checkProduceByNames")
    List<String> checkProduceByNames(@RequestBody List<String> list);
}
