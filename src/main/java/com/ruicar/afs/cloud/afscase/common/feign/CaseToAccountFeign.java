package com.ruicar.afs.cloud.afscase.common.feign;

import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(value = "${com.ruicar.service-names.contract-account-service}", contextId = "CaseToAccountFeign")
public interface CaseToAccountFeign {

    /**
     * 根据流水号和子事件查询凭证号
     * @param transNo
     * @param childCode
     * @return
     */
    @GetMapping("/accountVoucherInfo/getVoucherNoByTransNo")
    public IResponse<String> getVoucherNoByTransNo(@RequestParam String transNo, @RequestParam String childCode);

    /**
     * 冲销凭证信息
     * @param childCodes
     * @param contractNo
     * @param transNo
     * @return
     */
    @ApiOperation("冲销凭证信息")
    @PostMapping(value = "/accountVoucherInfo/reversalVoucher")
    IResponse reversalVoucher(@RequestBody List<String> childCodes, @RequestParam(value = "contractNo") String contractNo, @RequestParam(value = "transNo") String transNo);


}
