package com.ruicar.afs.cloud.afscase;

import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.ruicar.afs.cloud.common.core.security.annotation.EnableAfsFeignClients;
import com.ruicar.afs.cloud.common.core.security.annotation.EnableAfsResourceServer;
import com.ruicar.afs.cloud.common.job.annotation.EnableAfsCloudJob;
import com.ruicar.afs.cloud.common.swagger.annotation.EnableAfsSwagger2;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/12 17:56
 * @
 */
@EnableAfsSwagger2
@SpringCloudApplication
@EnableAfsFeignClients(basePackages = {"com.ruicar.afs"})
@EnableAfsResourceServer(details = true)
@EnableRetry
@Configuration
@EnableDiscoveryClient
@EnableScheduling
@EnableAfsCloudJob
@ComponentScan({"com.ruicar.afs.cloud.afscase",
        "com.ruicar.afs.cloud.invoice",
        "com.ruicar.afs.cloud.bizcommon",
        "com.ruicar.afs.cloud.seats",
        "com.ruicar.afs.cloud.parameter.commom",
        "com.ruicar.afs.cloud.image",
        "com.ruicar.afs.cloud.batch.cache",
        "com.ruicar.afs.cloud.workflow",
        "com.ruicar.afs.cloud.message",
        "com.ruicar.afs.cloud.risk",
        "com.ruicar.afs.cloud.call",
        "com.ruicar.afs.cloud.channel",
        "com.ruicar.afs.cloud.graphicsmagick",
        "com.ruicar.afs.cloud.manage",
        "com.ruicar.afs.cloud.product.sdk",
        "com.ruicar.afs.cloud.interfaces.wlease.ocr",
        "com.ruicar.afs.cloud.interfaces.cbs",
        "com.ruicar.afs.cloud.interfaces.direct",
        "com.ruicar.afs.cloud.interfaces.baihang",
        "com.ruicar.afs.cloud.interfaces.checkinsurance",
        "com.ruicar.afs.cloud.zhengxin",
        "com.ruicar.afs.cloud.common.scan",
        "com.ruicar.afs.cloud.loan",
        "com.ruicar.afs.cloud.common.config",
        "com.ruicar.afs.cloud.wx",
        "com.ruicar.afs.cloud.eisoa",
        "com.ruicar.afs.cloud.filecenter.ftp",
        "com.ruicar.afs.cloud.applyservice",
        "com.ruicar.afs.cloud.filecenter.ftp",
        "com.ruicar.afs.cloud.deepseek",
        "com.ruicar.afs.cloud.badge",})
public class AfsCaseApplication {
    public static void main(String[] args) {
        SpringApplication.run(AfsCaseApplication.class, args);
    }
}
