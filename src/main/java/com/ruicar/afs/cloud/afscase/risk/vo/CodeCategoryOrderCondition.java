package com.ruicar.afs.cloud.afscase.risk.vo;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import lombok.Data;

/**
 * @description: 规则码分类排序表
 * @author: quanzong666 
 * @date: 
 */
@Data
public class CodeCategoryOrderCondition extends CodeCategoryOrder {
  /**
     * 每页显示内容，默认为10
     */
    private Long pageSize = 10L;
    /**
     * 当前页码
     */
    private Long pageNumber = 1L;
}
