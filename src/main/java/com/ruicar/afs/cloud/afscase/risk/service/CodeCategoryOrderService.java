package com.ruicar.afs.cloud.afscase.risk.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.ruicar.afs.cloud.afscase.risk.entity.CodeCategoryOrder;
import com.ruicar.afs.cloud.afscase.risk.vo.CodeCategoryOrderCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import java.util.List;
import java.util.Map;

/**
 * 规则码分类排序表
 *
 * @description: 规则码分类排序表service
 */
public interface CodeCategoryOrderService extends IService<CodeCategoryOrder> {
    /**
     * 分页查询所有符合条件的规则码分类排序表信息
     * @param condition 参数
     * @return 返回结果
     */
    IResponse queryByPage(CodeCategoryOrderCondition condition);

    /**
     * 查询所有符合条件的规则码分类排序表信息
     * @param condition 参数
     * @return 返回结果
     */
    List<CodeCategoryOrder> queryByCondition(CodeCategoryOrderCondition condition);

    /**
     * 查询所有规则码
     * @return
     */
    public List<Map<String, String>> getAllRuleCodes();

    /**
     * 查询所有展示规则码
     * @return
     */
    public List<Map<String, String>> getAllShowRuleCodes();

    /**
     * 保存或更新规则码分类排序表
     * @param codeCategoryOrder
     * @return
     */
    IResponse saveOrUpdateCco(CodeCategoryOrder codeCategoryOrder);
}
