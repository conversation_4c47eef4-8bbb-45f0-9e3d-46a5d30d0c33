package com.ruicar.afs.cloud.afscase.risk.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 规则码分类排序表
 * @author: quanzong666 
 * @date: 
 */
@Data
@TableName(value = "code_category_order")
@Slf4j
public class CodeCategoryOrder extends BaseEntity<CodeCategoryOrder> {
    
    /**
     * 规则码
     */
    @ExcelProperty(value = "规则码", index = 0)
    private String ruleCode;

    /**
     * 分类
     */
    @ExcelProperty(value = "分类", index = 1)
    private String category;

    /**
     * 是否隐藏
     */
    @ExcelProperty(value = "是否隐藏", index = 2)
    private String hiddenFlag;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序", index = 3)
    private int sortOrder;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 4)
    private String remark;

}
