package com.ruicar.afs.cloud.afscase.approvetask.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.admin.api.dto.UserSimpleInfoDto;
import com.ruicar.afs.cloud.admin.api.feign.AfsUserFeign;
import com.ruicar.afs.cloud.afscase.approvemakelabel.entity.ApproveMakeLabel;
import com.ruicar.afs.cloud.afscase.approvemakelabel.service.ApproveMakeLabelService;
import com.ruicar.afs.cloud.afscase.approveprev.service.CaseApprovePrevInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.condition.WorkTaskPoolCondition;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseSubmitRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseSubmitRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkTaskPoolService;
import com.ruicar.afs.cloud.afscase.approvetask.vo.CaseBaseInfoVo;
import com.ruicar.afs.cloud.afscase.approvetask.vo.EffectCorrectVO;
import com.ruicar.afs.cloud.afscase.autoaudit.feign.ApplyInfoFeign;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.effecttime.service.CaseEffectRecordService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.task.utils.ConvertHanzi2PinyinUtil;
import com.ruicar.afs.cloud.afscase.processor.enums.WorkflowType;
import com.ruicar.afs.cloud.afscase.processor.util.WorkflowTypeUtil;
import com.ruicar.afs.cloud.afscase.remind.entity.CaseRemindDetail;
import com.ruicar.afs.cloud.afscase.remind.service.RemindService;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessStateInEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPhaseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LabelPositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ProcessTypeEnum;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.interfaces.call.dto.CallReqData;
import com.ruicar.afs.cloud.interfaces.call.dto.CallResData;
import com.ruicar.afs.cloud.interfaces.call.emuns.CallType;
import com.ruicar.afs.cloud.interfaces.call.service.CallSystemService;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import com.ruicar.afs.cloud.third.system.invoke.dto.Request;
import com.ruicar.afs.cloud.third.system.invoke.dto.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:案件查询
 * @created 2020/5/27 14:31
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("案件")
@RequestMapping("/approve")
public class CaseInfoTaskController {

    private WorkTaskPoolService workTaskPoolService;

    private CaseCarInfoService caseCarInfoService;

    private ApproveMakeLabelService approveMakeLabelService;

    private CaseChannelInfoService caseChannelInfoService;

    private ChannelBaseInfoService channelBaseInfoService;

    private ChannelRiskInfoService channelRiskInfoService;

    private AddressService addressService;

    private RemindService remindService;

    private CaseApprovePrevInfoService caseApprovePrevInfoService;

    private CaseEffectRecordService caseEffectRecordService;

    private CaseBaseInfoService caseBaseInfoService;

    private CallSystemService callSystemService;

    private UserDetailsInfoFeign userDetailsInfoFeign;

    private AfsUserFeign afsUserFeign;

    private WorkflowTaskInfoService workflowTaskInfoService;

    private final FlowConfigProperties flowConfigProperties;

    private final CaseSubmitRecordService caseSubmitRecordService;

    private final ApplyInfoFeign applyInfoFeign;

    private static final  String APPLY_STATUS_BANK_NOT_SIGN="20";

    @PostMapping(value = "/queryCaseInfoTaskList")
    @ApiOperation(value = "多条件分页获取案件信息")
    public IResponse<IPage<CaseBaseInfoVo>> queryCaseInfoTaskList(@ModelAttribute WorkTaskPoolCondition condition) {
        //业务状态（内）为待分配查询时默认增加分配失败
        List allocationList = new ArrayList();
        if(AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(condition.getBusinessStatusIn())){
            allocationList.add(AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION));
            allocationList.add(AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGN));
            condition.setBusinessStatusIn("");
        }else if(StringUtils.isNotEmpty(condition.getBusinessStatusIn())){
            allocationList.add(condition.getBusinessStatusIn());
        }
        condition.setStatusList(allocationList);
        if (StringUtils.isNotBlank(condition.getProcessType())) {
            List<WorkflowType> afsFlowKeyList = WorkflowTypeUtil.getAllFlowType(AfsEnumUtil.getEnum(condition.getProcessType(), ProcessTypeEnum.class));
            List<String> flowKeyStrList = afsFlowKeyList.stream().map(WorkflowType::getAfsFlowKey).collect(Collectors.toList());
            condition.setFlowList(flowKeyStrList);
        }
        if(CollectionUtil.isNotEmpty(condition.getApplyNoSet())){
            condition.setApplyNoSet(new HashSet<>(condition.getApplyNoSet()));
        }else{
            condition.setApplyNoSet(null);
        }
        //获取标签Id
        String[] str = condition.getTag();
        List labelIdList = new ArrayList();
        if (str!=null&&str.length>0) {
            for (int i = 0; i < str.length; i++) {
                labelIdList.add(str[i]);
            }
        }
        if (labelIdList != null && labelIdList.size() > 0) {
            condition.setLabelList(labelIdList);
        }

        //根据用户中文名获取用户信息
        List<String> updateByList = new ArrayList<>();
        if(StringUtils.isNotEmpty(condition.getUpdateBy())){
            IResponse<List<UserSimpleInfoDto>> userInfos = afsUserFeign.getUserByUserRealName(condition.getUpdateBy());
            updateByList.add(condition.getUpdateBy());
            if (userInfos.getData()!=null) {
                List<UserSimpleInfoDto> userInfoList = userInfos.getData();
                for (UserSimpleInfoDto dto: userInfoList){
                    updateByList.add(dto.getUsername());
                }
            }
        }
        condition.setUpdateByList(updateByList);
        IPage<CaseBaseInfoVo> pageResult = workTaskPoolService.queryCaseInfoCustTaskList(
                new Page(condition.getPageNumber(), condition.getPageSize()),
                condition);
        List<String> appplyNoList= new ArrayList<>();
        pageResult.getRecords().forEach(caseBaseInfoVo -> {
            appplyNoList.add(caseBaseInfoVo.getApplyNo());
        });
        if(CollectionUtil.isNotEmpty(appplyNoList)){
            pageResult= getCaseBaseInfoLsit(appplyNoList, pageResult);

        }
        return IResponse.success(pageResult);

    }

    @PostMapping(value = "/queryCaseAssignedList")
    @ApiOperation(value = "多条件分页获取已分配案件信息")
    public IResponse<IPage<CaseBaseInfoVo>> queryCaseAssignedList(@ModelAttribute WorkTaskPoolCondition condition) {

        if (StringUtils.isNotBlank(condition.getProcessType())) {
            List<WorkflowType> afsFlowKeyList = WorkflowTypeUtil.getAllFlowType(AfsEnumUtil.getEnum(condition.getProcessType(), ProcessTypeEnum.class));
            List<String> flowKeyStrList = afsFlowKeyList.stream().map(WorkflowType::getAfsFlowKey).collect(Collectors.toList());
            condition.setFlowList(flowKeyStrList);
        }
        if(CollectionUtil.isNotEmpty(condition.getApplyNoSet())){
            condition.setApplyNoSet(new HashSet<>(condition.getApplyNoSet()));
        }else{
            condition.setApplyNoSet(null);
        }
        //获取标签Id
        String[] str = condition.getTag();
        List labelIdList = new ArrayList();
        if (str!=null&&str.length>0) {
            for (int i = 0; i < str.length; i++) {
                labelIdList.add(str[i]);
            }
        }
        if (labelIdList != null && labelIdList.size() > 0) {
            condition.setLabelList(labelIdList);
        }

        //根据用户中文名获取用户信息
        List<String> updateByList = new ArrayList<>();
        if(StringUtils.isNotEmpty(condition.getUpdateBy())){
            IResponse<List<UserSimpleInfoDto>> userInfos = afsUserFeign.getUserByUserRealName(condition.getUpdateBy());
            updateByList.add(condition.getUpdateBy());
            if (userInfos.getData()!=null) {
                List<UserSimpleInfoDto> userInfoList = userInfos.getData();
                for (UserSimpleInfoDto dto: userInfoList){
                    updateByList.add(dto.getUsername());
                }
            }
        }
        condition.setUpdateByList(updateByList);
        IPage<CaseBaseInfoVo> pageResult = workTaskPoolService.queryCaseAssignedList(
                new Page(condition.getPageNumber(), condition.getPageSize()),
                condition);
        List<String> appplyNoList= new ArrayList<>();
        pageResult.getRecords().forEach(caseBaseInfoVo -> {
            appplyNoList.add(caseBaseInfoVo.getApplyNo());
        });
        if(CollectionUtil.isNotEmpty(appplyNoList)){
            pageResult= getCaseBaseInfoLsit(appplyNoList, pageResult);

        }
        return IResponse.success(pageResult);

    }

    /**
     * @description:组装数据
     * <AUTHOR>
     * @created 2020/9/22 13:57
     * @version 1.0
     */
    public  IPage<CaseBaseInfoVo>  getCaseBaseInfoLsit(List<String> applyNoList,IPage<CaseBaseInfoVo> pageResult ){

        Map<String,CaseCarInfo> caseCarInfoMap=caseCarInfoService.list(Wrappers.<CaseCarInfo>query().lambda()
                .in(CaseCarInfo::getApplyNo, applyNoList)).stream().collect(Collectors.toMap(CaseCarInfo::getApplyNo, caseCarInfo -> caseCarInfo));

        Map<String,CaseChannelInfo> caseChannelInfoMap = caseChannelInfoService.list(Wrappers.<CaseChannelInfo>query().lambda()
                .in(CaseChannelInfo::getApplyNo, applyNoList)).stream().collect(Collectors.toMap(CaseChannelInfo::getApplyNo, caseChannelInfo -> caseChannelInfo));

        Set<String> channelCodeSet = caseChannelInfoMap.values().stream().map(CaseChannelInfo::getDealerNo)
                .collect(Collectors.toSet());
        Map<String,ChannelBaseInfo> channelBaseInfoMap = channelCodeSet.isEmpty() ? new HashMap<>() :  channelBaseInfoService.list(
                Wrappers.<ChannelBaseInfo>lambdaQuery()
                        .in(ChannelBaseInfo::getChannelCode,channelCodeSet)
        ).stream().collect(Collectors.toMap(ChannelBaseInfo::getChannelCode,Function.identity()));

        Set<Long> channelIdSet = channelBaseInfoMap.values().stream().map(ChannelBaseInfo::getChannelId).collect(Collectors.toSet());
        Map<String,ChannelRiskInfo> channelRiskInfoMap = channelIdSet.isEmpty() ? new HashMap<>() : channelRiskInfoService
                .list(Wrappers.<ChannelRiskInfo>lambdaQuery()
                        .in(ChannelRiskInfo::getChannelId,channelIdSet))
                .stream().collect(Collectors.toMap(cr-> String.format("%s_%s",cr.getChannelId(),cr.getBusinessType()),Function.identity()));

        Map<String,List<ApproveMakeLabel>> labelMap = approveMakeLabelService.list(Wrappers.<ApproveMakeLabel>query().lambda()
                .in(ApproveMakeLabel::getApplyNo, applyNoList)
                .eq(ApproveMakeLabel::getLabelLocation, LabelPositionEnum.LIST.getCode())
                .eq(ApproveMakeLabel::getLabelPhase, LabelPhaseEnum.CREDITADUIT))
                .stream().collect(Collectors.groupingBy(ApproveMakeLabel::getApplyNo));

        Map<String,WorkflowTaskInfo> taskMap = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .in(WorkflowTaskInfo::getBusinessNo,applyNoList)
                .eq(WorkflowTaskInfo::getStatus, FlowStatusEnum.ACTIVE.getCode())
                .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getApproveTemplateId()))
                .stream().collect(Collectors.toMap(WorkflowTaskInfo::getBusinessNo,Function.identity()));

        Map<String,String> nameMap = new HashMap<>();
        if(!taskMap.isEmpty()){
            List<String> userNameList = taskMap.values().stream().map(WorkflowTaskInfo::getAssign).distinct().collect(Collectors.toList());
            userNameList.addAll(pageResult.getRecords().stream().map(CaseBaseInfoVo::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
            IResponse<List<UserSimpleInfoDto>> response = afsUserFeign.getSimpleInfoByUserLoginNames(userNameList);
            if(CommonConstants.SUCCESS.equals(response.getCode())){
                nameMap = response.getData()
                        .stream().collect(Collectors.toMap(UserSimpleInfoDto::getUserName,UserSimpleInfoDto::getUserRealName));
            }
        }

        for(int i=0;  i< applyNoList.size();i++){
            CaseBaseInfoVo caseBaseInfoVo = pageResult.getRecords().get(i);
            String applyNo = caseBaseInfoVo.getApplyNo();
            BusinessStateInEnum caseStatusEnum = Objects.isNull(caseBaseInfoVo.getBusinessStateIn())
                    ? null
                    : (BusinessStateInEnum) AfsEnumUtil.getEnum(caseBaseInfoVo.getBusinessStateIn(),BusinessStateInEnum.class);
            caseBaseInfoVo.setBusinessStateInDesc(AfsEnumUtil.desc(caseStatusEnum));
            //当合同号码和案件状态同时为空，显示案件状态为放款待提交
            if(StringUtils.isBlank(caseBaseInfoVo.getContractNo())&&StringUtils.isBlank(caseBaseInfoVo.getApplyStatus())) {
                caseBaseInfoVo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_SUBMIT.getState());
            }
            /*
             * 查询风险等级
             *
             * */
            CaseChannelInfo caseChannelInfo = caseChannelInfoMap.get(applyNo);
            ChannelBaseInfo channelBaseInfo = null;;
            /**---------------------------------   渠道风控信息    --------------------------------**/
            if(ObjectUtils.isNotNull(caseChannelInfo)) {
                channelBaseInfo = channelBaseInfoMap.get(caseChannelInfo.getDealerNo());
                if (ObjectUtils.isNotEmpty(channelBaseInfo)) {
                    ChannelRiskInfo channelRiskInfo = channelRiskInfoMap.get(String.format("%s_%s",channelBaseInfo.getChannelId(),caseBaseInfoVo.getBusinessType()));
                    if (ObjectUtils.isNotNull(channelRiskInfo)) {
                        caseBaseInfoVo.setQualityGrade(channelRiskInfo.getQualityGrade());
                        caseBaseInfoVo.setChannelGrade(channelRiskInfo.getChannelGrade());
                        caseBaseInfoVo.setChannelCity(channelBaseInfo.getChannelCity());
                        caseBaseInfoVo.setChannelFullName(channelBaseInfo.getChannelFullName());
                    }
                }
                caseBaseInfoVo.setSellerRealName(caseChannelInfo.getSaleAdvisor());
            }
            caseBaseInfoVo.setCarName(caseCarInfoMap.get(applyNo)!=null?caseCarInfoMap.get(applyNo).getModelName():null);
            caseBaseInfoVo.setBrandName(caseCarInfoMap.get(applyNo)!=null?caseCarInfoMap.get(applyNo).getBrandName():null);

            /**---------------------------------   渠道风控信息    --------------------------------**/
            if (ObjectUtils.isNotEmpty(channelBaseInfo)) {
                //直营时取 直营车商名称
                if (caseChannelInfo.getChannelBelong().equals(AfsEnumUtil.key(ChannelBelongEnum.DIRECT))
                        &&StringUtils.isNotBlank(caseChannelInfo.getCarDealersId())) {
                    ChannelBaseInfo channelInfoDirect = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda()
                            .eq(ChannelBaseInfo::getId, Long.parseLong(caseChannelInfo.getCarDealersId())));
                    if (ObjectUtils.isNotEmpty(channelInfoDirect)) {
                        caseBaseInfoVo.setChannelCity(addressService.getLabelByCode(channelInfoDirect.getChannelCity()));
                    }
                }else{
                    caseBaseInfoVo.setChannelCity(addressService.getLabelByCode(channelBaseInfo.getChannelCity()));
                }
            }

            /* *  标签信息 **/
            List<ApproveMakeLabel> labelList = labelMap.get(applyNo);
            caseBaseInfoVo.setLabelList(labelList);
            if(AfsEnumUtil.key(BusinessStateInEnum.PAUSE).equals(caseBaseInfoVo.getBusinessStateIn())) {
                // 暂停原因
                Optional<CaseRemindDetail> remindDetailOptional = remindService.queryNewestRemindOpt(caseBaseInfoVo.getApplyNo());
                if (remindDetailOptional.isPresent()) {
                    caseBaseInfoVo.setParseMessage(remindDetailOptional.get().getRemindContent());
                }
            }

            // 当前处理人
            WorkflowTaskInfo workTaskPool = taskMap.get(applyNo);
            if(workTaskPool!=null){
                if (org.apache.commons.lang3.StringUtils.isNotBlank( workTaskPool.getAssign ())) {
                    String realName = nameMap.get(workTaskPool.getAssign());
                    caseBaseInfoVo.setTaskNodeName(StringUtil.isNotBlank( realName )?realName:workTaskPool.getAssign());
                }
            }
            if(caseBaseInfoVo.getUpdateBy()!=null && !caseBaseInfoVo.getUpdateBy().isEmpty()) {
                caseBaseInfoVo.setUpdateBy(nameMap.get(caseBaseInfoVo.getUpdateBy()));
            }
            //设置订单最新提交时间
            List<CaseSubmitRecord> submitRecords = caseSubmitRecordService.list(Wrappers.<CaseSubmitRecord>lambdaQuery()
                    .eq(CaseSubmitRecord::getApplyNo, applyNo)
                    .orderByDesc(CaseSubmitRecord::getSubmitDate));
            if (CollectionUtil.isNotEmpty(submitRecords)) {
                caseBaseInfoVo.setLatestSubmitDate(submitRecords.get(0).getSubmitDate());
            }else {
                //若为空，则取值进件时间
                caseBaseInfoVo.setLatestSubmitDate(caseBaseInfoVo.getPassFirstDate());
            }
        }
        return pageResult;
    }

    /**
     * @Description 异常时效数据修复，重新计算
     * <AUTHOR>
     * @Date 2020/12/28 17:49
     */
    @PostMapping("/effectCorrect")
    public IResponse effectCorrect(@RequestBody EffectCorrectVO effectCorrectVO){
        List<String> applyNoArr = effectCorrectVO.getApplyNoArr();
        if(CollectionUtil.isNotEmpty(applyNoArr)){
            applyNoArr.forEach(caseEffectRecordService::effectCorrect);
        }
        return IResponse.success("操作成功");
    }


    /**
     * 呼叫中心接口
     */
    @PostMapping("/callCenter")
    public IResponse callCenter(){
        CallReqData data = new CallReqData();
        //获取当前用户对应的七鱼帐号
        IResponse info = userDetailsInfoFeign.info(SecurityUtils.getUsername());
        if (info.getData()!=null) {
            Object objectData = info.getData();
            JSONObject jsonObject = (JSONObject) JSON.toJSON(objectData);
            Object sysUser = jsonObject.get("sysUser");
            JSONObject object = (JSONObject) JSON.toJSON(sysUser);
            String userId = object.getString("userId");
            UserCollocation userCollocation = new UserCollocation();
            userCollocation = userCollocation.selectOne(Wrappers.<UserCollocation>query().lambda().eq(UserCollocation::getUserId, userId));
            data.setStaffName(userCollocation.getStaffName());
        }
        if (StringUtils.isNotBlank(data.getStaffName())) {
            Request<CallType, CallReqData> request = new Request<>(null, null, data);
            Response<CallResData> response = callSystemService.callThirdSystem(request);
            CallResData resData = response.getData();
            return IResponse.success(resData);
        }
        return IResponse.success(null);
    }

    /**
     * 销售团队管理-订单查询
     *
     */
    @PostMapping(value = "/queryCaseInfoOrderList")
    @ApiOperation(value = "多条件分页获取案件信息")
    public IResponse<IPage<CaseBaseInfoVo>> queryCaseInfoOrderList(@ModelAttribute WorkTaskPoolCondition condition) {

        log.info(" ========================================================= 进入到这里了吗？？？？  ========================================================= ");
        //如果是中文转换成拼音
        condition.setUpdateBy(ConvertHanzi2PinyinUtil.convertHanzi2Pinyin(condition.getUpdateBy(), true));

        //业务状态（内）为待分配查询时默认增加分配失败
        List allocationList = new ArrayList();
        if(AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION).equals(condition.getBusinessStatusIn())){
            log.info("====================================================== 测试点1号 ===============================================================");
            allocationList.add(AfsEnumUtil.key(BusinessStateInEnum.ALLOCATION));
            allocationList.add(AfsEnumUtil.key(BusinessStateInEnum.TASK_ASSIGN));
            condition.setBusinessStatusIn("");
        }
        condition.setStatusList(allocationList);
        if (StringUtils.isNotBlank(condition.getProcessType())) {
            log.info("====================================================== 测试点2号 ===============================================================");
            List<WorkflowType> afsFlowKeyList = WorkflowTypeUtil.getAllFlowType(AfsEnumUtil.getEnum(condition.getProcessType(), ProcessTypeEnum.class));
            List<String> flowKeyStrList = afsFlowKeyList.stream().map(WorkflowType::getAfsFlowKey).collect(Collectors.toList());
            condition.setFlowList(flowKeyStrList);
        }
        if(CollectionUtil.isNotEmpty(condition.getApplyNoSet())){
            log.info("====================================================== 测试点3号 ===============================================================");
            condition.setApplyNoSet(new HashSet<>(condition.getApplyNoSet()));
        }else{
            log.info("====================================================== 测试点4号 ===============================================================");
            condition.setApplyNoSet(null);
        }
        //获取标签Id
        String[] str = condition.getTag();
        List labelIdList = new ArrayList();
        if (str!=null&&str.length>0) {
            for (int i = 0; i < str.length; i++) {
                labelIdList.add(str[i]);
            }
        }
        if (labelIdList != null && labelIdList.size() > 0) {
            condition.setLabelList(labelIdList);
        }

        log.info(" ====================================================== 测试点5号 =============================================================== {}",condition.toString());

        IPage<CaseBaseInfoVo> pageResult = workTaskPoolService.queryCaseInfoOrderList(
                new Page(condition.getPageNumber(), condition.getPageSize()),
                condition);
        List<CaseBaseInfoVo> records = pageResult.getRecords();

        if(ObjectUtils.isNotEmpty(records) && records.size() > 0){
            log.info(" ====================================================== 测试点6号 =============================================================== {}",records.size());
        }else{
            log.info(" ====================================================== 测试点6号 =============================================================== 0");
        }

        for (int i = 0; i < records.size(); i++) {
            CaseBaseInfoVo caseBaseInfoVo = records.get(i);
            BusinessStateInEnum caseStatusEnum = Objects.isNull(caseBaseInfoVo.getBusinessStateIn())
                    ? null
                    : (BusinessStateInEnum) AfsEnumUtil.getEnum(caseBaseInfoVo.getBusinessStateIn(),BusinessStateInEnum.class);
            caseBaseInfoVo.setBusinessStateInDesc(AfsEnumUtil.desc(caseStatusEnum));
        }
        return IResponse.success(pageResult);
    }

}

