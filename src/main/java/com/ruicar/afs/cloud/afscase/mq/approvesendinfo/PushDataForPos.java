package com.ruicar.afs.cloud.afscase.mq.approvesendinfo;

import com.ruicar.afs.cloud.afscase.imagemanage.vo.FileDataVO;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.dto.SyncLoanGpsRuleDTO;
import com.ruicar.afs.cloud.afscase.reconsiderationprocess.vo.ReconsiderationMqDTO;
import com.ruicar.afs.cloud.bizcommon.print.vo.TemplateDataSendVo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveNoticeFraudDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveOpenResult;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSpecialSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CallBackApplyDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CallBackApplyResultDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseUpdateInfoSend;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ReconsiderationDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.SendUrgentInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.UrgentForbidDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.common.SealBaseDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.ApplyGpsOrderHistoryDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.DataPostSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanApproveInsuAndInvoiceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanDiscardDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanSecondaryProgressStatusInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanToContractDataMessage;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.SendToApplyActiveMsgDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.margin.MarginInfoDto;
import com.ruicar.afs.cloud.common.mq.rabbit.anno.send.AfsRabbitMqClient;
import com.ruicar.afs.cloud.common.mq.rabbit.anno.send.AfsRabbitMqSender;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.image.vo.AttachmentAllDataVO;
import com.ruicar.afs.cloud.image.vo.AttachmentDataSendVo;

/**
 * The interface Push data for pos.
 *
 * <AUTHOR>
 * @version 1.0
 * @description: MQ通知发送
 * @created 2020 /6/19 10:03
 */
@AfsRabbitMqClient
public interface PushDataForPos {
    /**
     * 常规审批通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendApprovalNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);


    /**
     * 常规留言通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendMessageNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 常规附条件核准
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendConditionalApproval(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 退回合作商通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendBackToPartnersNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 放款审核保险、发票数据同步接口
     *
     * @param loanApproveInsuAndInvoiceDTO the loan approve insu and invoice dto
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendLoanApproveInsuAndInvoice(AfsTransEntity<LoanApproveInsuAndInvoiceDTO> loanApproveInsuAndInvoiceDTO);

    /**
     * 车晓回调更新GPS派单状态
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendToGpsStatus(AfsTransEntity<ApplyGpsOrderHistoryDTO> afsTransEntity);

    /**
     * 批量取消案件通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendCancleToPartnersNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 进件数据落库结果通知
     *
     * @param resultInfo the result info
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void incomingApplyResult(AfsTransEntity<ApproveSubmitInfo> resultInfo);

    /**
     * 推送合同数据到合同系统
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @date 2020 /7/8contract
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.contract}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendLoanMsgToContract(AfsTransEntity<LoanToContractDataMessage> resultInfo);

    /**
     * 放款合同数据落库结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /7/3 21:39
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void loanApplyResult(AfsTransEntity<LoanSubmitInfo> resultInfo);

    /**
     * 加急结果通知
     *
     * @param resultInfo the result info
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}",exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendTurnUrgentResult(AfsTransEntity<ApproveSubmitInfo> resultInfo);

    /**
     * GPS安装规则通知
     *
     * @param afsTransEntity the afs trans entity
     * <AUTHOR>
     * @date 2020 /6/30
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendGpsMsgToApplyNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 资产变更审批结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /7/30 10:13
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}",exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendChangeAssetsResult(AfsTransEntity<ApproveSpecialSubmitInfo> resultInfo);

    /**
     * 复议申请审批结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}",exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendReconsideResult(AfsTransEntity<ReconsiderationDto> resultInfo);

    /**
     * 资产变更落库结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /7/30 10:13
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}",exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void changeAssetsResult(AfsTransEntity<ApproveSpecialSubmitInfo> resultInfo);


    /**
     * 资产变更审批通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendApprovalNoticForAssets(AfsTransEntity<ApproveSpecialSubmitInfo> afsTransEntity);

    /**
     * 复议审批审批通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendApprovalNoticForReconsideration(AfsTransEntity<ReconsiderationDto> afsTransEntity);


    /**
     * 资产变更留言通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendMessageNoticForAssets(AfsTransEntity<ApproveSpecialSubmitInfo> afsTransEntity);

    /**
     * 附件新增通知发送
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendAttachmentInfo(AfsTransEntity<AttachmentDataSendVo> afsTransEntity);

    /**
     * 复议落库结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /08/03 16:22
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}",exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendReconsiderationResult(AfsTransEntity<ReconsiderationDto> resultInfo);

    /**
     * 模板新增通知发送
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendTemplateInfo(AfsTransEntity<TemplateDataSendVo> afsTransEntity);

    /**
     * 推送模板数据到合同系统
     *
     * @param afsTransEntity the afs trans entity
     * <AUTHOR>
     * @date 2020 /8/17
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.caseToContractManage}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendTemplateToContract(AfsTransEntity<TemplateDataSendVo> afsTransEntity);

    /**
     * 放款模式规则通知
     *
     * @param afsTransEntity the afs trans entity
     * <AUTHOR>
     * @date 2020 /8/29
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendLoanModelToApplyNotic(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * 通知进件系统撤回申请结果
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendCallBackNotice(AfsTransEntity<CallBackApplyResultDto> afsTransEntity);

    /**
     * 推送审批撤回申请至进件系统
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendApproveCallBackApply(AfsTransEntity<CallBackApplyDto> afsTransEntity);

    /**
     * 审批过程‘打开’状态通知
     *
     * @param resultInfo the result info
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void incomingApproveOpenResult(AfsTransEntity<ApproveOpenResult> resultInfo);

    /**
     * 附条件核准、核准、退回经销商数据推送
     *
     * @param caseUpdateInfoSend the case update info send
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void caseUpdateInfoSend(AfsTransEntity<CaseUpdateInfoSend> caseUpdateInfoSend);

    /**
     * 经我处理追加留言通知
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendDealWithMessage(AfsTransEntity<ApproveSubmitInfo> afsTransEntity);

    /**
     * add by fangchenliang
     * 同步案件文件至进件系统
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void syncFileData(AfsTransEntity<FileDataVO> afsTransEntity);

    /**
     * add by fangchenliang
     * 同步案件GPS规则至进件系统
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void syncGpsRuleData(AfsTransEntity<SyncLoanGpsRuleDTO> afsTransEntity);

    /**
     * add by fangchenliang
     * 印章同步至进件系统
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void syncSealBaseData(AfsTransEntity<SealBaseDTO> afsTransEntity);

    /**
     * Send urgent data.
     *
     * @param afsTransEntity the afs trans entity
     * @description: 推送合作商加急次数.数据.月初
     * <AUTHOR>
     * @created 2020 /11/25 9:26
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendUrgentData(AfsTransEntity<SendUrgentInfo> afsTransEntity);

    /**
     * Send urgent forbid.
     *
     * @param afsTransEntity the afs trans entity
     * @description: 禁止合作商加急
     * <AUTHOR>
     * @created 2020 /11/25 10:55
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendUrgentForbid(AfsTransEntity<UrgentForbidDto> afsTransEntity);

    /**
     * 推送影像管理数据到合同系统
     *
     * @param afsTransEntity the afs trans entity
     * <AUTHOR>
     * @date 2020 /12/03
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.caseToContractManage}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendAttachmentListToIcos(AfsTransEntity<AttachmentAllDataVO> afsTransEntity);

    /**
     * 推送影像管理数据到进件系统
     *
     * @param afsTransEntity the afs trans entity
     * <AUTHOR>
     * @date 2020 /12/03
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendAttachmentListToWow(AfsTransEntity<AttachmentAllDataVO> afsTransEntity);

    /**
     * 激活描述通知进件
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendToActiveMsgNotice(AfsTransEntity<SendToApplyActiveMsgDTO> afsTransEntity);

    /**
     * 二次欺诈确认欺诈通知wow
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendFraudReslt(AfsTransEntity<ApproveNoticeFraudDto> afsTransEntity);

    /**
     * 复议审批结果通知进件
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendReconsiderationApproveResultToApply(AfsTransEntity<ReconsiderationMqDTO> afsTransEntity);

    /**
     * 推送保证金数据到合同系统
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.contract}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void sendMarginListToIcos(AfsTransEntity<MarginInfoDto> afsTransEntity);

    /**
     * 二次放款审批状态更新
     *
     * @param afsTransEntity the afs trans entity
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void updateStatusSender(AfsTransEntity<LoanSecondaryProgressStatusInfo> afsTransEntity);

    /**
     * 取消放款数据落库结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /7/3 21:39
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void LoanDataPostNewNotic(AfsTransEntity<DataPostSubmitInfo> resultInfo);

    /**
     * 放款资料后置数据落库结果通知
     *
     * @param resultInfo the result info
     * <AUTHOR>
     * @created 2020 /7/3 21:39
     * @version 1.0
     */
    @AfsRabbitMqSender(serverAlias = "${com.ruicar.afs.cloud.rabbitQueue.serverAlia}", queueName = "${com.ruicar.afs.cloud.rabbitQueue.queueName.case}", exchangeName = "${com.ruicar.afs.cloud.rabbitQueue.exchangeName}")
    void LoanCannelNotic(AfsTransEntity<LoanDiscardDTO> resultInfo);
}
