package com.ruicar.afs.cloud.afscase.vehicle.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.vehicle.condition.Condition;
import com.ruicar.afs.cloud.afscase.vehicle.condition.MultipartFileCondition;
import com.ruicar.afs.cloud.afscase.vehicle.condition.VehicleModelCondition;
import com.ruicar.afs.cloud.afscase.vehicle.dao.VehicleExportData;
import com.ruicar.afs.cloud.afscase.vehicle.service.impl.VehicleModeValid;
import com.ruicar.afs.cloud.afscase.vehicle.service.VehicleInfoService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.vehicle.enums.IsShowEnum;
import com.ruicar.afs.cloud.vehicle.enums.ModelClassEnum;
import com.ruicar.afs.cloud.vehicle.enums.VehicleSaleNetEnum;
import com.ruicar.afs.cloud.vehicle.vo.NewVehicleModelVO;
import com.ruicar.afs.cloud.afscase.vehicle.vo.VehicleModelVO;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.vehicle.vo.VehicleModelNewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <p>Description:车型库管理相关</p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @CreateDate 2020-5-29
 */

@Slf4j
@RestController
@AllArgsConstructor
@Api("车型库管理信息")
@RequestMapping("/vehicle")
public class VehicleModelController {

    private VehicleInfoService vehicleInfoService;

    @Autowired
    private VehicleModeValid vehicleModeValid;

    /**
     * 导出车型库数据模板
     */
    @ApiOperation(value = "导出车型库数据模板")
    @GetMapping(value = "/model/exportTemplateExcel")
    public void downloadExcel(HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("比亚迪_车型库数据模板_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        List<NewVehicleModelVO> vehicleImportDataList = new ArrayList<>();
        NewVehicleModelVO example = new NewVehicleModelVO();
        example.setNo(1);
        example.setBrandName("腾势");
        example.setSeriesName("腾势N7");
        example.setSaleNet("腾势");
        example.setVehicleModelCode("11111");
        example.setModelClass("燃油");
        example.setPrice(BigDecimal.valueOf(99999));
        example.setIsShow("隐藏");
        example.setCrmVerificationGroup("12343");
        example.setModelName("DM-i1050尊享型晌雅蓝旷达米24款。");
        vehicleImportDataList.add(example);
        ExcelWriter excelWriterBuilder = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet htSheetWrite = EasyExcel.writerSheet(0, "车型库数据模板").head(NewVehicleModelVO.class).build();
        excelWriterBuilder.write(vehicleImportDataList, htSheetWrite);
        excelWriterBuilder.finish();

    }

    /**
     * 车型库导入接口
     */
    @PostMapping(value = "/model/import")
    @ApiOperation(value = "车型库导入接口")
    public IResponse vehicleModelImport(@ModelAttribute MultipartFileCondition condition) throws IOException {
        log.info("file name and traceId,{}",condition.getUploadFile().getOriginalFilename());
        MultipartFile file = condition.getUploadFile();
        log.info("开始导入文件: {}", file.getOriginalFilename());

        try (InputStream inputStream = file.getInputStream()) {
            List<NewVehicleModelVO> dataList = EasyExcel.read(inputStream)
                    .head(NewVehicleModelVO.class)
                    .sheet()
                    .doReadSync();
            if (CollectionUtils.isEmpty(dataList)) {
                return IResponse.fail("导入失败:文件内容为空或未正确填写模板数据");
            }
            for (int i = 0; i < dataList.size(); i++) {
                NewVehicleModelVO row = dataList.get(i);
                if (!StrUtil.equalsAny(row.getIsShow(), "显示", "隐藏")) {
                    String errorMsg = String.format("导入失败:第%d行数据异常: 进件是否显示字段应为'显示'或'隐藏'", i + 1);
                    log.error(errorMsg);
                    return IResponse.fail(errorMsg);
                }
            }
            vehicleInfoService.vehicleImportInfo(dataList);
            return IResponse.success("导入成功，共处理" + dataList.size() + "条数据");
        } catch (Exception e) {
            log.error("导入失败", e);
            return IResponse.fail(e.getMessage());
        }
    }

    /**
     * 下载校验错误文件
     */
    @GetMapping(value = "/model/exportErrorFile")
    @ApiOperation(value = "下载校验错误文件")
    public void exportErrorFile(HttpServletResponse response, @RequestParam("traceId") String traceId) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("加勒比_车型库数据导入失败原因_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        ExcelWriter excelWriterBuilder = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet htSheetWrite = EasyExcel.writerSheet(0, "导入失败原因").head(VehicleExportData.class).build();
        //内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        List<VehicleExportData> exportDataList = vehicleModeValid.queryErrorFile(traceId);
        excelWriterBuilder.write(exportDataList, htSheetWrite);
        excelWriterBuilder.finish();
    }

    /**
     * 车型库查询
     */
    @PostMapping(value = "/model/query")
    @ApiOperation(value = "车型库查询")
    public IResponse<Page<VehicleModelVO>> vehicleModelQuery(@RequestBody VehicleModelCondition condition) {
        Page<VehicleModelVO> pageInfo = vehicleInfoService.queryModelInfoByCondition(condition);
        return IResponse.success(pageInfo);
    }

    /**
     * 车型库查询
     * 改进：数据字典加入
     */
    @PostMapping(value = "/model/queryVehicleModel")
    @ApiOperation(value = "车型库查询")
    public IResponse<Page<VehicleModelVO>> vehicleModelNewQuery(@RequestBody VehicleModelCondition condition) {
        Page<VehicleModelVO> pageInfo = vehicleInfoService.queryModelNewInfoByCondition(condition);
        return IResponse.success(pageInfo);
    }

    /**
     * 车型库新增
     */
    @PostMapping(value = "/model/add")
    @ApiOperation(value = "车型库新增")
    public IResponse vehicleModelAdd(@RequestBody VehicleModelNewVO vehicleModelVO) {
        return vehicleInfoService.addVehicleModelInfo(vehicleModelVO);
    }

    /**
     * 车型库编辑
     */
    @PostMapping(value = "/model/edit")
    @ApiOperation(value = "车型库编辑")
    public IResponse vehicleModelEdit(@RequestBody VehicleModelNewVO vehicleModelVO) {
        return vehicleInfoService.editVehicleModelInfo(vehicleModelVO);
    }

    /**
     * 车型库删除
     */
    @PostMapping(value = "/model/delByIds/{ids}")
    @ApiOperation(value = "车型库删除")
    public IResponse vehicleModelDelete(@PathVariable Long[] ids) {
        return vehicleInfoService.deleteVehicleModelInfo(ids);
    }

    /**
     * 车型库导出接口
     */
    @PostMapping(value = "/model/export")
    @ApiOperation(value = "车型库导出接口")
    public IResponse vehicleModelExport(HttpServletResponse response, @RequestBody List<String> modelIdList) throws IOException {
        List<NewVehicleModelVO> vehicleModelVOS = vehicleInfoService.queryModelInfoByList(modelIdList);
        AtomicInteger i = new AtomicInteger(1);
        vehicleModelVOS.stream().forEach(s->{
            s.setNo(i.getAndIncrement());
            s.setPrice(s.getPrice().multiply(BigDecimal.valueOf(10000)));
        });
        if(CollectionUtils.isNotEmpty(vehicleModelVOS)){
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("比亚迪_导出指定车型数据报表_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            ExcelWriter excelWriterBuilder = EasyExcel.write(response.getOutputStream()).build();
            WriteSheet htSheetWrite = EasyExcel.writerSheet(0, "导出指定车型数据").head(NewVehicleModelVO.class).build();

            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            excelWriterBuilder.write(vehicleModelVOS, htSheetWrite);
            excelWriterBuilder.finish();
            return IResponse.success("成功");
        }else {
            return IResponse.fail("失败");
        }
    }

    /**
     * 车型库按筛选条件全量导出接口
     */
    @PostMapping("/model/exportAll")
    @ApiOperation(value = "车型库按筛选条件全量导出接口")
    public void exportVehicle(HttpServletResponse response, @RequestBody Condition condition) throws IOException {
        List<NewVehicleModelVO> vehicleModelVOS = vehicleInfoService.queryVehicle(condition);
        AtomicInteger i = new AtomicInteger(1);
        vehicleModelVOS.stream().forEach(s -> {
            s.setNo(i.getAndIncrement());
            if (s.getSaleNet() != null) {
                s.setSaleNet(AfsEnumUtil.desc(VehicleSaleNetEnum.valueOf(s.getSaleNet())));
            }
            if (s.getModelClass() != null) {
                s.setModelClass(ModelClassEnum.getModelClassEnum(s.getModelClass()).getDesc());
            }
            if (s.getIsShow() != null) {
                if (IsShowEnum.NOT_SHOW.getCode().equals(s.getIsShow())) {
                    s.setIsShow(IsShowEnum.NOT_SHOW.getDesc());
                } else if (IsShowEnum.IS_SHOW.getCode().equals(s.getIsShow())) {
                    s.setIsShow(IsShowEnum.IS_SHOW.getDesc());
                }
            }
            if (s.getPrice() != null) {
                s.setPrice(s.getPrice().multiply(BigDecimal.valueOf(10000)));
            }
        });
        if(CollectionUtils.isNotEmpty(vehicleModelVOS)){
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("比亚迪_导出车型数据_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            ExcelWriter excelWriterBuilder = EasyExcel.write(response.getOutputStream()).build();
            WriteSheet htSheetWrite = EasyExcel.writerSheet(0, "导出筛选条件下全部车型数据").head(NewVehicleModelVO.class).build();

            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            excelWriterBuilder.write(vehicleModelVOS, htSheetWrite);
            excelWriterBuilder.finish();
        }
    }
}
