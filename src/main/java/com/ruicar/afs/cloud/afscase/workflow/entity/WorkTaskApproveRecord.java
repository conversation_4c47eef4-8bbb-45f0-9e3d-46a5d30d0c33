package com.ruicar.afs.cloud.afscase.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 工作流审批记录表
 * <AUTHOR>
 * @Date 2020/07/21 10:18
 */
@Data
@TableName("work_task_approve_record")
public class WorkTaskApproveRecord extends BaseEntity<WorkTaskApproveRecord> {

    /**
     * 流程id（这个是自己生成的，只做自己对应的流程）
     */
    private String bizDataId;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 业务类型：新车、二手车
     */
    private String businessType;

    /**
     * 审核人
     */
    private String approvalUser;

    /**
     * 审批意见
     */
    private String approvalOpinion;
    /**
     * 审批意见描述
     */
    private String approvalOpinionDescribe;
    /**
     * 备注
     */
    private String remark;

    /**
     * 任务节点id
     */
    private String taskNodeId;

    /**
     * 任务节点名称
     */
    private String taskNodeName;

    /**
     * 流程id
     */
    private String taskId;


    /**
     * 节点对应审批菜单key
     */
    @ApiModelProperty(value = "节点对应审批菜单key")
    private String targetWebKey;

    /**
     * 流程包ID
     */
    @ApiModelProperty(value = "流程包ID")
    private String flowPackageId;

    /**
     * 流程模版ID
     */
    @ApiModelProperty(value = "流程模版ID")
    private String flowTemplateId;

    /**
     * 节点业务key 可以用以路由到指定页面
     */
    @ApiModelProperty(value = "节点业务key 可以用以路由到指定页面")
    private String userDefinedIndex;




}
