package com.ruicar.afs.cloud.afscase.writeoff.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffClearInfo;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ClearStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffClearInfoService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


/**
 * 服务费清账模块
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffClear")
public class WriteOffClearInfoController {

    private final WriteOffClearInfoService writeOffClearInfoService;

    @PostMapping("/queryList")
    @ApiOperation(value = "查询数据")
    public IResponse queryList(@RequestBody QueryCondition<WriteOffClearInfo> queryCondition) {
        WriteOffClearInfo condition = queryCondition.getCondition();
        Page<WriteOffClearInfo> page = writeOffClearInfoService.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffClearInfo>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffClearInfo::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffClearInfo::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffClearInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .eq(StrUtil.isNotBlank(condition.getWriteOffType()), WriteOffClearInfo::getWriteOffType, condition.getWriteOffType())
                .eq(StrUtil.isNotBlank(condition.getFeeClearStatus()), WriteOffClearInfo::getFeeClearStatus, condition.getFeeClearStatus())
                .eq(StrUtil.isNotBlank(condition.getPayClearStatus()), WriteOffClearInfo::getPayClearStatus, condition.getPayClearStatus())
                .orderByDesc(WriteOffClearInfo::getCreateTime));
        return IResponse.success(page);
    }


    @PostMapping(value = "/resetById")
    @ApiOperation(value = "根据id失败重置")
    public IResponse resetById(@RequestParam Long id) {
        WriteOffClearInfo clearInfo = writeOffClearInfoService.getById(id);
        Assert.isTrue(clearInfo != null, "id不正确");
        if (ClearStatusEnum.CLEAR_FAIL.getCode().equals(clearInfo.getFeeClearStatus())) {
            clearInfo.setFeeClearStatus(ClearStatusEnum.WAIT_CLEAR.getCode());
        }
        if (ClearStatusEnum.CLEAR_FAIL.getCode().equals(clearInfo.getPayClearStatus())) {
            clearInfo.setPayClearStatus(ClearStatusEnum.WAIT_CLEAR.getCode());
        }
        writeOffClearInfoService.updateById(clearInfo);
        return IResponse.success("重置成功");
    }

    @PostMapping(value = "/resetAll")
    @ApiOperation(value = "失败重置所有")
    public IResponse resetAll() {
        writeOffClearInfoService.update(Wrappers.<WriteOffClearInfo>lambdaUpdate()
                .eq(WriteOffClearInfo::getFeeClearStatus, ClearStatusEnum.CLEAR_FAIL.getCode())
                .set(WriteOffClearInfo::getFeeClearStatus, ClearStatusEnum.WAIT_CLEAR.getCode()));
        writeOffClearInfoService.update(Wrappers.<WriteOffClearInfo>lambdaUpdate()
                .eq(WriteOffClearInfo::getPayClearStatus, ClearStatusEnum.CLEAR_FAIL.getCode())
                .set(WriteOffClearInfo::getPayClearStatus, ClearStatusEnum.WAIT_CLEAR.getCode()));
        return IResponse.success("重置成功");
    }


    @PostMapping("/exportData")
    @ApiOperation(value = "导出核销项清账明细")
    public void exportData(@RequestBody WriteOffClearInfo condition, HttpServletResponse response) {
        writeOffClearInfoService.exportData(condition, response);
    }
}
