package com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.approvetask.entity.WorkProcessScheduleInfo;
import com.ruicar.afs.cloud.afscase.approvetask.service.WorkProcessScheduleInfoService;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.entity.CaseBackToPartnersInfo;
import com.ruicar.afs.cloud.afscase.backtopartnersinfo.service.CaseBackToPartnersInfoService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsApply;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsDevice;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsApplyService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsDeviceService;
import com.ruicar.afs.cloud.afscase.channel.common.Constants;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAffiliatedUnits;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelReceivablesAccount;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelWitnessInfo;
import com.ruicar.afs.cloud.afscase.channel.sender.CaseToApplyUpdatePayeeInfoSender;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAccountInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAffiliatedUnitsService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelWitnessInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.handling.entity.HandlingInfo;
import com.ruicar.afs.cloud.afscase.handling.service.business.HandlingInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CasePayeeInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseReceiptInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustIndividualService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseDiscountDetailService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFinancingItemsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CasePayeeInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseReceiptInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.AddedFinancingItems;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInsuranceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.LoanBankCardInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.service.AddedFinancingItemsService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInsuranceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CaseCarDepositService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CasePriorityChangeService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanBankCardInfoService;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.entity.LoanGpsRuleInfo;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.service.LoanGpsRuleInfoService;
import com.ruicar.afs.cloud.afscase.loanmoderuleinfo.entity.LoanModeRuleInfo;
import com.ruicar.afs.cloud.afscase.loanmoderuleinfo.service.LoanModeRuleInfoService;
import com.ruicar.afs.cloud.afscase.margin.service.bussiness.MarginBusinessService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.PushDataForPos;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveLoanInfoService;
import com.ruicar.afs.cloud.afscase.mq.sender.ManageAssetsChangeCaseSender;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowProcessBusinessRefInfo;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowProcessBusinessRefInfoService;
import com.ruicar.afs.cloud.bizcommon.business.dto.FinDiscountPlanDTO;
import com.ruicar.afs.cloud.bizcommon.business.dto.PlanRateDTO;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinPlanRate;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRentAdjustDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRepaymentPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinTermsDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyRentAdjustDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.FinMainInfoService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinDiscountPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinPlanRateService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinRepaymentPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinTermsDetailsService;
import com.ruicar.afs.cloud.channel.config.ChannelApiConfig;
import com.ruicar.afs.cloud.channel.feign.ChannelApiFeign;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AccountTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AddressTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AuthorizeWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarFunctionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarNatureEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelOnlineEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ChannelTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CollectionTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ContactTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRelationEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.DiscountTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.FlowNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.InsuranceTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsDefaultDeductCardEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsTypeNumEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.LoanModelEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PaymentObjectEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.SexEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.TrueOrFalseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ValidStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.VerStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.casemaininfo.condition.CasePayeeInfoUpdateCondition;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.CaseContractInfoDto;
import com.ruicar.afs.cloud.common.modules.casemaininfo.dto.CasePayeeInfoDto;
import com.ruicar.afs.cloud.common.modules.contract.dto.ComAttachmentFileDTO;
import com.ruicar.afs.cloud.common.modules.contract.enums.PaymentModeEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCarDeposit;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.AttachmentDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.BackMsgDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.BasicCustCompanyDetailDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.BasicInvoiceInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanApproveInsuAndInvoiceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanResultInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanToContractDataMessage;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.MqCarInsuranceInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.MqCarInvoiceInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.PaymentPool;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.SendToApplyActiveMsgDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.SendToApplyContractInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractAddPriceItemsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractAffiliatedUnitDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractBankCardDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCarDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCarInvoiceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractChannelInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractChannelUniteInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractContractCustRelationDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCustAddressDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCustBaseInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCustContactDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCustContactsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractCustPersonalDetailDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractDepositDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractDiscountDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractFinRentAdjustDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractFinancialAgreementDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractFinancingItemsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractGpsApplyDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractGpsDeviceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractHandlingDetailsDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractInsuranceInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto.ContractMainInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.gpsdto.LoanGpsRuleDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.gpsdto.LoanGpsRuleInfoDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.loanmodeldto.LoanModelRuleDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.loanmodeldto.LoanModelRuleInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.margin.MarginInfoDto;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.parameter.commom.enums.PaymentMethodEnum;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.afscase.common.utils.CaseConstants.CODE_SUCCESS;

/**
 * <AUTHOR>
 * @Date 2020/7/4
 * @description 放款审核MQ组装数据实现类
 */
@Slf4j
@Service
@AllArgsConstructor
@ConditionalOnProperty(prefix = "com.ruicar.afs.cloud.mq.rabbit", name = "enable")
public class ApproveLoanInfoServiceImpl implements ApproveLoanInfoService {
    private final PushDataForPos infoSender;
    private final CaseBackToPartnersInfoService caseBackToPartnersInfoService;
    private final CaseContractInfoService caseContractInfoService;
    private final CaseBaseInfoService caseBaseInfoService;
    private final CaseCustInfoService caseCustInfoService;
    private final CaseCustContactService caseCustContactService;
    private final CaseCustAddressService caseCustAddressService;
    private final CaseCustIndividualService caseCustIndividualService;
    private final CaseCarInfoService caseCarInfoService;
    private final CarInvoiceInfoService carInvoiceInfoService;
    private final CarGpsApplyService carGpsApplyService;
    private final CarInsuranceInfoService carInsuranceInfoService;
    private final CaseCostInfoService caseCostInfoService;
    private final CaseFinancingItemsService caseFinancingItemsService;
    private final ChannelAffiliatedUnitsService affiliatedUnitsService;
    private final LoanBankCardInfoService loanBankCardInfoService;
    private final ChannelAccountInfoService accountInfoService;
    private final CaseChannelInfoService caseChannelInfoService;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final CaseDiscountDetailService caseDiscountDetailService;
    private final WorkProcessScheduleInfoService scheduleInfoService;
    private final CarGpsDeviceService carGpsDeviceService;
    private final ApplyRentAdjustDetailsService rentAdjustDetailsService;
    private final LoanModeRuleInfoService loanModeRuleInfoService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final ChannelWitnessInfoService channelWitnessInfoService;
    private final LoanGpsRuleInfoService loanGpsRuleInfoService;
    private final AddedFinancingItemsService addedFinancingItemsService;
    private final ManageAssetsChangeCaseSender manageAssetsChangeCaseSender;
    private final CaseChannelUniteInfoService caseChannelUniteInfoService;
    private final CaseEnterpriseCustomerDetailsService caseEnterpriseCustomerDetailsService;
    private final CaseReceiptInfoService caseReceiptInfoService;
    private final CasePayeeInfoService casePayeeInfoService;
    private final HandlingInfoService handlingInfoService;
    private final MarginBusinessService marginBusinessService;
    private final CaseCarDepositService caseCarDepositService;
    private final FinDiscountPlanService finDiscountPlanService;
    private final ChannelApiFeign channelApiFeign;
    private final ChannelApiConfig channelApiConfig;
    private final FinPlanRateService finPlanRateService;
    private final FinTermsDetailsService finTermsDetailsService;
    private final FinRepaymentPlanService finRepaymentPlanService;
    private final ChannelRiskInfoService channelRiskInfoService;
    private final FinMainInfoService finMainInfoService;
    private final WorkflowProcessBusinessRefInfoService workflowProcessBusinessRefInfoService;
    private final CasePriorityChangeService casePriorityChangeService;

    /**
     * 收款账号
     */
    @Autowired
    private ChannelAccountInfoService channelAccountInfoService;

    private final CaseToApplyUpdatePayeeInfoSender caseToApplyUpdatePayeeInfoSender;

    @ApiOperation("退回合作商通知")
    @Override
    public Boolean backToPartnersNotic(String contractNo, String stageId, String taskId) {
        log.info("-----------------退回合作商数据组装begin---------------->");
        Assert.isTrue(StringUtils.isNotBlank(contractNo) && StringUtils.isNotBlank(stageId) && StringUtils.isNotBlank(taskId), "退回合作商信息为空！");
        List<CaseBackToPartnersInfo> caseBackToPartnersInfoList = caseBackToPartnersInfoService.list(Wrappers.<CaseBackToPartnersInfo>lambdaQuery()
                .eq(CaseBackToPartnersInfo::getContractNo, contractNo)
                .eq(CaseBackToPartnersInfo::getTaskId, Long.valueOf(taskId)));
        if (CollectionUtils.isNotEmpty(caseBackToPartnersInfoList)) {
            List<BackMsgDto> backMsgDtoList = new ArrayList<>();
            for (CaseBackToPartnersInfo caseBackToPartnersInfo : caseBackToPartnersInfoList) {
                backMsgDtoList.add(BackMsgDto.builder()
                        .fileType(caseBackToPartnersInfo.getFileType())
                        .backDesc(caseBackToPartnersInfo.getBackDesc())
                        .backWords(caseBackToPartnersInfo.getFileTypeName()+"|"+caseBackToPartnersInfo.getBackWords()).build());
            }
            ArrayList<AttachmentDTO> attachmentDtoList = new ArrayList<>();
            //草稿状态改为合格状态
            this.comAttachmentFileService.updateFileStatusByBusiNo(contractNo);
            List<ComAttachmentFile> files = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBelongNo, contractNo)
                    .eq(ComAttachmentFile::getBusiNo, contractNo));
            files.forEach(comAttachmentFile -> {
                AttachmentDTO attachmentDTO = new AttachmentDTO();
                log.info("copy====>file==={}=======>{}", comAttachmentFile.getId(), comAttachmentFile.getFileStatus());
                BeanUtil.copyProperties(comAttachmentFile, attachmentDTO, CopyOptions.create().setIgnoreNullValue(true));
                attachmentDtoList.add(attachmentDTO);
            });
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .approveType(ApplyStatusEnum.LOAN_RETURN.getState())
                    .backMsgDtoList(backMsgDtoList)
                    .attachmentDtoList(attachmentDtoList)
                    .operateTime(new Date())
                    .stageId(stageId)
                    .taskId(taskId)
                    .contractNo(contractNo).build();
            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();
            log.info("-----------------退回合作商通知begin---------------->");

            //获取发票信息
            List<CarInvoiceInfo> carInvoiceInfos=carInvoiceInfoService.list(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(CarInvoiceInfo::getContractNo,contractNo));
            //获取保险信息
            List<CarInsuranceInfo> carInsuranceInfos  =carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                    .eq(CarInsuranceInfo::getContractNo,contractNo));
            LoanApproveInsuAndInvoiceDTO loandto=new LoanApproveInsuAndInvoiceDTO();
            List<MqCarInvoiceInfo> carInvoiceInfoList=new ArrayList<>();
            List<MqCarInsuranceInfo> carInsuranceInfoList=new ArrayList<>();
            for(CarInvoiceInfo carInvoiceInfo:carInvoiceInfos){
                MqCarInvoiceInfo carInvoice=new MqCarInvoiceInfo();
                BeanUtils.copyProperties(carInvoiceInfo,carInvoice);
                carInvoiceInfoList.add(carInvoice);
            }
            for(CarInsuranceInfo carInsuranceInfo:carInsuranceInfos){
                MqCarInsuranceInfo mqCarInsuranceInfo=new MqCarInsuranceInfo();
                BeanUtils.copyProperties(carInsuranceInfo,mqCarInsuranceInfo);
                carInsuranceInfoList.add(mqCarInsuranceInfo);
            }
            loandto.setCarInsuranceInfos(carInsuranceInfoList);
            loandto.setCarInvoiceInfos(carInvoiceInfoList);
            approveSubmitInfo.setLoanApproveInsuAndInvoiceDTO(loandto);
            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder().transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(approveSubmitInfo).build());
            log.info("-----------------退回合作商通知end---------------->");
            return true;
        } else {
            log.info("-----------------合同:{},流程:{},未查询到退回合作商退回原因，请联系管理员进行处理！---------------->", contractNo, taskId);
            log.info("-----------------退回合作商数据组装end---------------->");
            log.info("-----------------退回合作商通知失败---------------->");
            return false;
        }
    }

    @ApiOperation("放款自动审核外部审核不通过退回合作商通知")
    @Override
    public Boolean loanAutoBackToPartnersNotic(List<CaseBackToPartnersInfo> infoList) {
        log.info("-----------------放款自动审核外部审核不通过退回合作商数据组装begin---------------->");
        if (CollectionUtils.isNotEmpty(infoList)) {

            // 查询合同信息
            List<CaseContractInfo> tCaseContractInfoList =
                    caseContractInfoService.list(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, infoList.get(0).getContractNo()));

            if(ObjectUtils.isEmpty(tCaseContractInfoList) || tCaseContractInfoList.size() == 0){
                throw new AfsBaseException("没有查询到需要用到的合同信息数据");
            }

            //草稿状态改为合格状态
            this.comAttachmentFileService.updateFileStatusByBusiNo(infoList.get(0).getContractNo());

            CaseContractInfo caseContractInfo = tCaseContractInfoList.get(0);
            if(!ObjectUtils.isEmpty(caseContractInfo)){
                caseContractInfo.setWorkflowPrevStep(1);
                caseContractInfo.setFlowNode(FlowNodeEnum.CHANNEL.getCode());
                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_RETURN.getState());
                caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
                caseContractInfoService.updateById(caseContractInfo);
            }

            // 查询stageId
            List<WorkflowProcessBusinessRefInfo> list = workflowProcessBusinessRefInfoService.list(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                    .select(WorkflowProcessBusinessRefInfo::getProcessInstanceId)
                    .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, infoList.get(0).getApplyNo())
                    .orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime));

            if(ObjectUtils.isEmpty(list) || list.size() == 0){
                throw new AfsBaseException("没有查询到需要用到的stageId参数的数据");
            }

            List<BackMsgDto> backMsgDtoList = new ArrayList<>();
            for (CaseBackToPartnersInfo caseBackToPartnersInfo : infoList) {
                backMsgDtoList.add(BackMsgDto.builder()
                        .fileType(caseBackToPartnersInfo.getFileType())
                        .backDesc(caseBackToPartnersInfo.getBackDesc())
                        .backWords(caseBackToPartnersInfo.getFileTypeName()+"|"+caseBackToPartnersInfo.getBackWords()).build());
            }
            ArrayList<AttachmentDTO> attachmentDtoList = new ArrayList<>();

            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .approveType(ApplyStatusEnum.LOAN_RETURN.getState())
                    .backMsgDtoList(backMsgDtoList)
                    .attachmentDtoList(attachmentDtoList)
                    .operateTime(new Date())
                    .stageId(list.get(0).getProcessInstanceId())
                    .taskId(String.valueOf(infoList.get(0).getTaskId()))
                    .contractNo(infoList.get(0).getContractNo()).build();
            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();
            log.info("----------------- 放款自动审核不通过，退回合作商数据组装end，开始组装发票和保单信息 ---------------->");
            //获取发票信息
            List<CarInvoiceInfo> carInvoiceInfos=carInvoiceInfoService.list(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(CarInvoiceInfo::getContractNo,infoList.get(0).getContractNo()));
            //获取保险信息
            List<CarInsuranceInfo> carInsuranceInfos  =carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                    .eq(CarInsuranceInfo::getContractNo,infoList.get(0).getContractNo()));
            LoanApproveInsuAndInvoiceDTO loandto=new LoanApproveInsuAndInvoiceDTO();
            List<MqCarInvoiceInfo> carInvoiceInfoList=new ArrayList<>();
            List<MqCarInsuranceInfo> carInsuranceInfoList=new ArrayList<>();
            for(CarInvoiceInfo carInvoiceInfo:carInvoiceInfos){
                MqCarInvoiceInfo carInvoice=new MqCarInvoiceInfo();
                BeanUtils.copyProperties(carInvoiceInfo,carInvoice);
                carInvoiceInfoList.add(carInvoice);
            }
            for(CarInsuranceInfo carInsuranceInfo:carInsuranceInfos){
                MqCarInsuranceInfo mqCarInsuranceInfo=new MqCarInsuranceInfo();
                BeanUtils.copyProperties(carInsuranceInfo,mqCarInsuranceInfo);
                carInsuranceInfoList.add(mqCarInsuranceInfo);
            }
            loandto.setCarInsuranceInfos(carInsuranceInfoList);
            loandto.setCarInvoiceInfos(carInvoiceInfoList);
            approveSubmitInfo.setLoanApproveInsuAndInvoiceDTO(loandto);
            log.info("----------------- 放款自动审核不通过，发票和保单信息组装end ---------------->");

            log.info("-----------------放款自动审核不通过，退回合作商通知begin---------------->");

            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder().transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(approveSubmitInfo).build());
            log.info("-----------------放款自动审核不通过，退回合作商通知end---------------->");
            return true;
        } else {
            log.info("-----------------合同:{},未查询到退回合作商退回原因，请联系管理员进行处理！---------------->", infoList.get(0).getContractNo());
            log.info("-----------------放款自动审核不通过，退回合作商数据组装end---------------->");
            log.info("-----------------放款自动审核不通过，退回合作商通知失败---------------->");
            return false;
        }
    }

    @ApiOperation("放款自动审核外部审核不通过退回合作商通知")
    @Override
    public Boolean autoBackToApplyNotic(List<CaseBackToPartnersInfo> infoList) {

        log.info("-----------------放款自动退回合作商数据组装begin---------------->");
        if (CollectionUtils.isNotEmpty(infoList)) {

            // 查询合同信息
            CaseContractInfo caseContractInfo =
                    caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, infoList.get(0).getContractNo()));

            //草稿状态改为合格状态
            this.comAttachmentFileService.updateFileStatusByBusiNo(infoList.get(0).getContractNo());

            if(!ObjectUtils.isEmpty(caseContractInfo)){
                caseContractInfo.setWorkflowPrevStep(1);
                caseContractInfo.setFlowNode(FlowNodeEnum.CHANNEL.getCode());
                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_RETURN.getState());
                caseContractInfo.setPriority(casePriorityChangeService.priorityChange(caseContractInfo));
                caseContractInfoService.updateById(caseContractInfo);
            }

            // 查询stageId
            List<WorkflowProcessBusinessRefInfo> list = workflowProcessBusinessRefInfoService.list(Wrappers.<WorkflowProcessBusinessRefInfo>lambdaQuery()
                    .select(WorkflowProcessBusinessRefInfo::getProcessInstanceId)
                    .eq(WorkflowProcessBusinessRefInfo::getBusinessNo, infoList.get(0).getApplyNo())
                    .orderByDesc(WorkflowProcessBusinessRefInfo::getCreateTime));

            List<BackMsgDto> backMsgDtoList = new ArrayList<>();
            for (CaseBackToPartnersInfo caseBackToPartnersInfo : infoList) {
                backMsgDtoList.add(BackMsgDto.builder()
                        .fileType(caseBackToPartnersInfo.getFileType())
                        .backDesc(caseBackToPartnersInfo.getBackDesc())
                        .backWords(caseBackToPartnersInfo.getFileTypeName()+"|"+caseBackToPartnersInfo.getBackWords()).build());
            }
            ArrayList<AttachmentDTO> attachmentDtoList = new ArrayList<>();
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .approveType(ApplyStatusEnum.LOAN_RETURN.getState())
                    .backMsgDtoList(backMsgDtoList)
                    .attachmentDtoList(attachmentDtoList)
                    .operateTime(new Date())
                    .stageId(ObjectUtil.isNotEmpty(list) && list.size() > 0 ? list.get(0).getProcessInstanceId() : "")
                    .taskId(String.valueOf(infoList.get(0).getTaskId()))
                    .backTimes(caseContractInfo.getBackTimes()+1)
                    .isAutoBack(CaseConstants.YES)
                    .contractNo(infoList.get(0).getContractNo()).build();

            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();
            //获取发票信息
            List<CarInvoiceInfo> carInvoiceInfos=carInvoiceInfoService.list(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(CarInvoiceInfo::getContractNo,infoList.get(0).getContractNo()));
            //获取保险信息
            List<CarInsuranceInfo> carInsuranceInfos  =carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                    .eq(CarInsuranceInfo::getContractNo,infoList.get(0).getContractNo()));
            LoanApproveInsuAndInvoiceDTO loandto = new LoanApproveInsuAndInvoiceDTO();
            List<MqCarInvoiceInfo> carInvoiceInfoList=new ArrayList<>();
            List<MqCarInsuranceInfo> carInsuranceInfoList=new ArrayList<>();
            for(CarInvoiceInfo carInvoiceInfo:carInvoiceInfos){
                MqCarInvoiceInfo carInvoice=new MqCarInvoiceInfo();
                BeanUtils.copyProperties(carInvoiceInfo,carInvoice);
                carInvoiceInfoList.add(carInvoice);
            }
            for(CarInsuranceInfo carInsuranceInfo:carInsuranceInfos){
                MqCarInsuranceInfo mqCarInsuranceInfo=new MqCarInsuranceInfo();
                BeanUtils.copyProperties(carInsuranceInfo,mqCarInsuranceInfo);
                carInsuranceInfoList.add(mqCarInsuranceInfo);
            }
            loandto.setCarInsuranceInfos(carInsuranceInfoList);
            loandto.setCarInvoiceInfos(carInvoiceInfoList);
            approveSubmitInfo.setLoanApproveInsuAndInvoiceDTO(loandto);

            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder().transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(approveSubmitInfo).build());
            log.info("-----------------放款自动审核不通过，退回合作商通知end---------------->");
            return true;
        } else {
            log.info("-----------------放款自动审核不通过，退回合作商通知失败---------------->");
            return false;
        }
    }

    /**
     * @param
     * @description 通知进件合同状态接口数据
     * <AUTHOR>
     * @date 2020/7/6
     */
    @ApiOperation("通知进件合同状态")
    @Override
    public void sendToApplyNotic(String contractNo, ApplyStatusEnum statusEnum) {
        log.info("------------通知进件合同状态【" + statusEnum + "】组装begin----------->");
        if (null != contractNo) {
            WorkProcessScheduleInfo scheduleInfo = scheduleInfoService.getOne(Wrappers.<WorkProcessScheduleInfo>lambdaQuery()
                    .eq(WorkProcessScheduleInfo::getContractNo, contractNo), false);
            ArrayList<AttachmentDTO> attachmentDtoList = new ArrayList<>();
            if (statusEnum.equals(ApplyStatusEnum.LOAN_APPROVE_DONE)) {
                List<ComAttachmentFile> files = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getBelongNo, contractNo)
                        .eq(ComAttachmentFile::getBusiNo, contractNo));
                files.forEach(comAttachmentFile -> {
                    AttachmentDTO attachmentDTO = new AttachmentDTO();
                    BeanUtil.copyProperties(comAttachmentFile, attachmentDTO, CopyOptions.create().setIgnoreNullValue(true));
                    if(FileStatusEnum.DRAFT.getCode().equals(attachmentDTO.getFileStatus())){
                        attachmentDTO.setFileStatus(FileStatusEnum.STANDARD.getCode());
                    }
                    attachmentDtoList.add(attachmentDTO);
                });

                //放款审核通过后，若是个人转个体户的，把进件类型或客户类型改成企业
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, contractNo));
                String applyNo = caseContractInfo.getApplyNo();
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));
                if(Constants.IF_PERSONAL_TO_ENTERPRISE.equals(caseBaseInfo.getIfPersonalToEnterprise())){
                    caseBaseInfo.setInputType(Constants.IF_PERSONAL_TO_ENTERPRISE);
                    caseBaseInfoService.updateById(caseBaseInfo);
                    CaseCustInfo mianCust = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                            .eq(CaseCustInfo::getApplyNo, applyNo).
                            eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
                    mianCust.setCustType(Constants.IF_PERSONAL_TO_ENTERPRISE);
                    caseCustInfoService.updateById(mianCust);
                }


            }
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .contractNo(contractNo)
                    .attachmentDtoList(attachmentDtoList)
                    .stageId(null != scheduleInfo ? scheduleInfo.getStageId() : "")
                    .approveType(statusEnum.getState())
                    .operateTime(new Date()).build();
            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();
            log.info("-----------------通知进件合同状态【" + statusEnum + "】组装end---------------->");

            //获取发票信息
            List<CarInvoiceInfo> carInvoiceInfos=carInvoiceInfoService.list(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(CarInvoiceInfo::getContractNo,contractNo));
            //获取保险信息
            List<CarInsuranceInfo> carInsuranceInfos  =carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                    .eq(CarInsuranceInfo::getContractNo,contractNo));
            LoanApproveInsuAndInvoiceDTO loandto=new LoanApproveInsuAndInvoiceDTO();
            List<MqCarInvoiceInfo> carInvoiceInfoList=new ArrayList<>();
            List<MqCarInsuranceInfo> carInsuranceInfoList=new ArrayList<>();
            for(CarInvoiceInfo carInvoiceInfo:carInvoiceInfos){
                MqCarInvoiceInfo carInvoice=new MqCarInvoiceInfo();
                BeanUtil.copyProperties(carInvoiceInfo,carInvoice);
                carInvoiceInfoList.add(carInvoice);
            }
            for(CarInsuranceInfo carInsuranceInfo:carInsuranceInfos){
                MqCarInsuranceInfo mqCarInsuranceInfo=new MqCarInsuranceInfo();
                BeanUtil.copyProperties(carInsuranceInfo,mqCarInsuranceInfo);
                carInsuranceInfoList.add(mqCarInsuranceInfo);
            }
            loandto.setCarInsuranceInfos(carInsuranceInfoList);
            loandto.setCarInvoiceInfos(carInvoiceInfoList);

            approveSubmitInfo.setLoanApproveInsuAndInvoiceDTO(loandto);


            log.info("-----------------通知进件合同状态发送begin---------------->");
            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(approveSubmitInfo).build());
            log.info("-----------------通知进件合同状态发送end---------------->");
        }
    }

    /**
     * @param
     * @description 通知进件合同状态接口数据
     * <AUTHOR>
     * @date 2020/7/6
     */
    @ApiOperation("通知进件合同状态")
    @Override
    public void sendLoanToApplyNotic(String contractNo, ApplyStatusEnum statusEnum,String mortgageClaim) {
        log.info("------------通知进件合同状态2【" + statusEnum + "】组装begin-----------> , 入参={}",mortgageClaim);
        if (null != contractNo) {
            WorkProcessScheduleInfo scheduleInfo = scheduleInfoService.getOne(Wrappers.<WorkProcessScheduleInfo>lambdaQuery()
                    .eq(WorkProcessScheduleInfo::getContractNo, contractNo), false);
            ArrayList<AttachmentDTO> attachmentDtoList = new ArrayList<>();
            if (statusEnum.equals(ApplyStatusEnum.LOAN_APPROVE_DONE)) {
                List<ComAttachmentFile> files = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getBelongNo, contractNo)
                        .eq(ComAttachmentFile::getBusiNo, contractNo));
                files.forEach(comAttachmentFile -> {
                    AttachmentDTO attachmentDTO = new AttachmentDTO();
                    BeanUtil.copyProperties(comAttachmentFile, attachmentDTO, CopyOptions.create().setIgnoreNullValue(true));
                    if(FileStatusEnum.DRAFT.getCode().equals(attachmentDTO.getFileStatus())){
                        attachmentDTO.setFileStatus(FileStatusEnum.STANDARD.getCode());
                    }
                    attachmentDtoList.add(attachmentDTO);
                });

                //放款审核通过后，若是个人转个体户的，把进件类型或客户类型改成企业
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, contractNo));
                String applyNo = caseContractInfo.getApplyNo();
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, applyNo));
                if(Constants.IF_PERSONAL_TO_ENTERPRISE.equals(caseBaseInfo.getIfPersonalToEnterprise())){
                    caseBaseInfo.setInputType(Constants.IF_PERSONAL_TO_ENTERPRISE);
                    caseBaseInfoService.updateById(caseBaseInfo);
                    CaseCustInfo mianCust = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                            .eq(CaseCustInfo::getApplyNo, applyNo).
                            eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
                    mianCust.setCustType(Constants.IF_PERSONAL_TO_ENTERPRISE);
                    caseCustInfoService.updateById(mianCust);
                }


            }
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .contractNo(contractNo)
                    .attachmentDtoList(attachmentDtoList)
                    .stageId(null != scheduleInfo ? scheduleInfo.getStageId() : "")
                    .approveType(statusEnum.getState())
                    .mortgageClaim(mortgageClaim)
                    .operateTime(new Date()).build();
            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().sendToApplyContractInfoDTO(sendToApplyContractInfoDTO).build();
            log.info("-----------------通知进件合同状态【" + statusEnum + "】组装end---------------->");

            //获取发票信息
            List<CarInvoiceInfo> carInvoiceInfos=carInvoiceInfoService.list(Wrappers.<CarInvoiceInfo>query().lambda()
                    .eq(CarInvoiceInfo::getContractNo,contractNo));
            //获取保险信息
            List<CarInsuranceInfo> carInsuranceInfos  =carInsuranceInfoService.list(Wrappers.<CarInsuranceInfo>query().lambda()
                    .eq(CarInsuranceInfo::getContractNo,contractNo));
            LoanApproveInsuAndInvoiceDTO loandto=new LoanApproveInsuAndInvoiceDTO();
            List<MqCarInvoiceInfo> carInvoiceInfoList=new ArrayList<>();
            List<MqCarInsuranceInfo> carInsuranceInfoList=new ArrayList<>();
            for(CarInvoiceInfo carInvoiceInfo:carInvoiceInfos){
                MqCarInvoiceInfo carInvoice=new MqCarInvoiceInfo();
                BeanUtil.copyProperties(carInvoiceInfo,carInvoice);
                carInvoiceInfoList.add(carInvoice);
            }
            for(CarInsuranceInfo carInsuranceInfo:carInsuranceInfos){
                MqCarInsuranceInfo mqCarInsuranceInfo=new MqCarInsuranceInfo();
                BeanUtil.copyProperties(carInsuranceInfo,mqCarInsuranceInfo);
                carInsuranceInfoList.add(mqCarInsuranceInfo);
            }
            loandto.setCarInsuranceInfos(carInsuranceInfoList);
            loandto.setCarInvoiceInfos(carInvoiceInfoList);

            approveSubmitInfo.setLoanApproveInsuAndInvoiceDTO(loandto);


            log.info("-----------------通知进件合同状态发送begin---------------->");
            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(approveSubmitInfo).build());
            log.info("-----------------通知进件合同状态发送end---------------->");
        }
    }

    /**
     * @param
     * @description 通知进件合同申请状态接口数据
     * <AUTHOR>
     * @date 2020/9/2
     */
    @ApiOperation("通知进件批量取消申请")
    @Override
    public void sendListToApplyNotic(String applyNoList) {
        log.info("------------通知进件批量取消申请组装begin----------->");
        if (null != applyNoList) {
            ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder().applyNoList(applyNoList).build();
            log.info("-----------------通知进件批量取消申请组装end---------------->");

            log.info("-----------------通知进件批量取消申请发送begin---------------->");
            infoSender.sendCancleToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_MODIFY_ORDER)
                    .data(approveSubmitInfo).build());
            log.info("-----------------通知进件批量取消申请发送end---------------->");
        }
    }

    /**
     * @param
     * @Description 通知进件GPS状态接口数据
     * <AUTHOR>
     * @Date 2020/08/07
     */
    @ApiOperation("通知进件GPS状态")
    @Override
    public void sendToApplyNotic(String contractNo, String status) {
        log.info("------------通知进件GPS状态【" + status + "】组装begin----------->");
        if (null != contractNo) {
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .contractNo(contractNo)
                    .stageId(null)
                    .status(status)
                    .operateTime(new Date()).build();
            ApproveSubmitInfo submitInfo =
                    ApproveSubmitInfo.builder()
                            .sendToApplyContractInfoDTO(sendToApplyContractInfoDTO)
                            .build();
            log.info("-----------------通知进件GPS状态【" + status + "】组装end---------------->");

            log.info("-----------------通知进件GPS状态发送begin---------------->");
            infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                    .data(submitInfo).build());
            log.info("-----------------通知进件GPS状态发送end---------------->");
        }
    }

    /**
     * 放款审核保险、发票数据同步接口
     * @param loanApproveInsuAndInvoiceDTO
     */
    @Override
    public void sendLoanApproveInsuAndInvoice(LoanApproveInsuAndInvoiceDTO loanApproveInsuAndInvoiceDTO){
        log.info("------------退回经销商与通过审核同步放款审核保险、发票数据同步接口"+ JSONObject.toJSONString(loanApproveInsuAndInvoiceDTO) + "----------->");
        infoSender.sendLoanApproveInsuAndInvoice(AfsTransEntity.<LoanApproveInsuAndInvoiceDTO>builder()
                .transCode(MqTransCode.LOAN_APPROVE_INSU_AND_INVOICE)
                .data(loanApproveInsuAndInvoiceDTO).build());
        log.info("-----------------放款审核保险、发票数据同步接口---------------->");
    }
    /**
     * @param
     * @return
     * @description 推送接口信息到合同系统
     * <AUTHOR>
     * @date 2020/7/8
     */
    @Override
    public void sendLoanMsgToContract(String contractNo,String applyNo) {
        Assert.isTrue(StringUtils.isNotBlank(contractNo), "合同号【" + contractNo + "】不存在，推送数据失败");
        LoanToContractDataMessage loanToContractDataMessage = null;
        try {
            //设定序列化要使用的服务 处理枚举字典不一致的问题
            AfsEnumUtil.setUseService("afs-contract");
            log.info("********************推送合同系统数据组装begin********************》");
            loanToContractDataMessage = addData(contractNo);
            log.info("********************推送合同系统数据组装end********************》");
            log.info(JSONObject.toJSONString(loanToContractDataMessage));
            infoSender.sendLoanMsgToContract(AfsTransEntity.<LoanToContractDataMessage>builder()
                    .transCode(MqTransCode.AFS_POS_CASE_CONTRACT_CTM_DATA)
                    .data(loanToContractDataMessage).build());
            log.info("==============合同mq推送结束==============");
        } catch (Exception e) {
            log.error("", e);
            throw new AfsBaseException("推送合同系统数据组装失败");
        } finally {
            //清除要设定服务，不清除会导致自己服务出现问题
            AfsEnumUtil.clear();
            log.info("==============合同清除设定服务==============");
        }
    }

    /**
     * @param
     * @description 组装接口数据
     * <AUTHOR>
     * @date 2020/7/8
     */
    public LoanToContractDataMessage addData(String contractNo) throws Exception {
        ContractMainInfoDTO mainInfo = new ContractMainInfoDTO();
        List<ContractCustPersonalDetailDTO> personalDetail = new ArrayList();
        List<ContractCustBaseInfoDTO> baseInfo = new ArrayList();
        List<ContractContractCustRelationDTO> custRelation = new ArrayList();
        List<ContractCustContactDetailsDTO> custContractDetail = new ArrayList();
        List<ContractCustAddressDetailsDTO> addressDetail = new ArrayList();
        List<ContractCarDetailsDTO> carDetail = new ArrayList();
        List<ContractCarInvoiceDTO> carInvoice = new ArrayList();
        ContractGpsApplyDTO gpsApply = new ContractGpsApplyDTO();
        List<ContractGpsDeviceDTO> gpsDevice = new ArrayList();
        List<ContractInsuranceInfoDTO> insuranceInfo = new ArrayList();
        List<ContractFinancialAgreementDTO> fnancialAgreement = new ArrayList();
        List<ContractFinancingItemsDTO> financingItems = new ArrayList();
        ContractAffiliatedUnitDTO affiliatedUnit = new ContractAffiliatedUnitDTO();
        ContractBankCardDTO bankCard = new ContractBankCardDTO();
        List<ContractChannelInfoDTO> channelInfo = new ArrayList();
        List<ContractDiscountDetailsDTO> discountDetails = new ArrayList();
        List<ContractCustContactsDTO> bsicCustContacts = new ArrayList<>();
        List<ContractFinRentAdjustDetailsDTO> finRentAdjustDetailsDTOList = new ArrayList<>();
        List<ContractAddPriceItemsDTO> addPriceItems = new ArrayList<>();
        List<ComAttachmentFileDTO> fileDTOList = new ArrayList<>();
        ContractChannelUniteInfoDTO channelUniteInfoDTO = new ContractChannelUniteInfoDTO();
        List<BasicCustCompanyDetailDTO> basicCustCompanyDetailList=new ArrayList<>();
        BasicInvoiceInfoDTO basicInvoiceInfoDTO = new BasicInvoiceInfoDTO();
        List<ContractHandlingDetailsDto> handlingDetailsDtos=new ArrayList<>();
        List<PaymentPool> paymentPools = new ArrayList<>();
        ContractDepositDTO contractDepositDTO = new ContractDepositDTO();

        log.info("====================合同主信息=======================》");
        CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, contractNo));
        if (null == caseContractInfo) {
            throw new Exception("合同号【" + contractNo + "】主信息不存在");
        }
        BeanUtils.copyProperties(caseContractInfo, mainInfo);
        if (StringUtils.isBlank(mainInfo.getApplyNo())) {
            throw new Exception("合同编号【" + contractNo + "】->申请编号不存在");
        }
        mainInfo.setBusinessType((BusinessTypeEnum) AfsEnumUtil.getEnum(caseContractInfo.getBusinessType(), BusinessTypeEnum.class));
        mainInfo.setContractStatus(caseContractInfo.getContractStatus());
        mainInfo.setLendingMode((LoanModelEnum) AfsEnumUtil.getEnum(caseContractInfo.getLendingMode(), LoanModelEnum.class));
        mainInfo.setLoanApproveDate(caseContractInfo.getLendingPassDate());
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery().eq(CaseBaseInfo::getApplyNo, mainInfo.getApplyNo()));
        mainInfo.setIntoFirstDate(caseBaseInfo.getPassFirstDate());
        mainInfo.setCaseApproveDate(caseBaseInfo.getPassLastDate());
        mainInfo.setCarNature((CarNatureEnum) AfsEnumUtil.getEnum(caseBaseInfo.getCarNature(), CarNatureEnum.class));
        mainInfo.setProductId(caseBaseInfo.getProductId());
        mainInfo.setProductName(caseBaseInfo.getProductName());
        mainInfo.setCarPurpose(caseBaseInfo.getCarPurpose());
        String ifPersonalToEnterprise = caseBaseInfo.getIfPersonalToEnterprise();
        mainInfo.setIfPersonalToEnterprise(ifPersonalToEnterprise);
        String inputType = Constants.IF_PERSONAL_TO_ENTERPRISE.equals(ifPersonalToEnterprise) ?
                Constants.IF_PERSONAL_TO_ENTERPRISE : caseBaseInfo.getInputType();
        mainInfo.setInputType(inputType);
        mainInfo.setRentType(caseBaseInfo.getRentType());
        mainInfo.setCarType(caseBaseInfo.getCarType());
        mainInfo.setIsWithhold(caseBaseInfo.getIsWithhold());
        mainInfo.setChannelBelong(caseBaseInfo.getChannelBelong());
        //订单来源系统标识
        mainInfo.setSystemType(caseBaseInfo.getSystemType());
        mainInfo.setBelongingCapital(caseBaseInfo.getBelongingCapital());
        List<AddedFinancingItems> addedFinancingItems = addedFinancingItemsService.list(Wrappers.<AddedFinancingItems>lambdaQuery().eq(AddedFinancingItems::getApplyNo, mainInfo.getApplyNo()));
        for (AddedFinancingItems addItem : addedFinancingItems) {
            ContractAddPriceItemsDTO addItemDto = new ContractAddPriceItemsDTO();
            BeanUtils.copyProperties(addItem, addItemDto);
            addItemDto.setContractNo(mainInfo.getContractNo());
            addPriceItems.add(addItemDto);
        }

        List<CaseCustInfo> caseCustInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(mainInfo.getApplyNo()), CaseCustInfo::getApplyNo, mainInfo.getApplyNo()));
        //主共担是否存在夫妻关系标记
        Boolean isCouple = false;
        if (null != caseCustInfoList) {
            log.info("========合同客户基本信息-合同与用户角色关系信息==========》");
            for (CaseCustInfo caseCustInfo : caseCustInfoList) {
                if (CustRelationEnum.COUPLE.getCode().equals(caseCustInfo.getCustRelation())) {
                    isCouple = true;
                }
                //合同客户基本信息
                ContractCustBaseInfoDTO contractCustBaseInfoDTO = new ContractCustBaseInfoDTO();
                BeanUtils.copyProperties(caseCustInfo, contractCustBaseInfoDTO);
                contractCustBaseInfoDTO.setContractNo(contractNo);
                contractCustBaseInfoDTO.setCustType((CustTypeEnum) AfsEnumUtil.getEnum(caseCustInfo.getCustType(), CustTypeEnum.class));
                contractCustBaseInfoDTO.setIsLongTerm((WhetherEnum) AfsEnumUtil.getEnum(caseCustInfo.getIsLongTerm(), WhetherEnum.class));
                contractCustBaseInfoDTO.setCustRole((CustRoleEnum) AfsEnumUtil.getEnum(caseCustInfo.getCustRole(), CustRoleEnum.class));
                baseInfo.add(contractCustBaseInfoDTO);

                //合同与用户角色关系信息
                ContractContractCustRelationDTO contractContractCustRelationDTO = new ContractContractCustRelationDTO();
                BeanUtils.copyProperties(caseCustInfo, contractContractCustRelationDTO);
                contractContractCustRelationDTO.setId(caseCustInfo.getId());
                contractContractCustRelationDTO.setContractNo(contractNo);
                contractContractCustRelationDTO.setCustRole((CustRoleEnum) AfsEnumUtil.getEnum(caseCustInfo.getCustRole(), CustRoleEnum.class));
                custRelation.add(contractContractCustRelationDTO);

                /**组装联系方式*/
                if (!ObjectUtils.isEmpty(caseCustInfo.getTelPhone())) {
                    ContractCustContactDetailsDTO custContact = new ContractCustContactDetailsDTO();
                    custContact.setId(caseCustInfo.getId());
                    custContact.setContactNumber(caseCustInfo.getTelPhone());
                    custContact.setContactType(ContactTypeEnum.PHONE);
                    custContractDetail.add(custContact);
                }
            }
        }
        ContractCustBaseInfoDTO contactBaseInfoDTO = new ContractCustBaseInfoDTO();
        ContractCustPersonalDetailDTO personalDetailDTO = new ContractCustPersonalDetailDTO();
        ContractContractCustRelationDTO relationDTO = new ContractContractCustRelationDTO();
        ContractCustContactDetailsDTO contactDetailsDTO = new ContractCustContactDetailsDTO();
        ContractCustAddressDetailsDTO addressDetailsDTO = new ContractCustAddressDetailsDTO();
        if (CollectionUtils.isNotEmpty(baseInfo)) {
            for (ContractCustBaseInfoDTO contractCustBaseInfoDTO : baseInfo) {
                if(CustTypeEnum.PERSON.equals(contractCustBaseInfoDTO.getCustType())){
                    CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId, contractCustBaseInfoDTO.getId()));
                    if (null != caseCustIndividual) {
                        log.info("========合同个人客户【" + contractCustBaseInfoDTO.getId() + "】明细信息==========》");
                        ContractCustPersonalDetailDTO custPersonalDetailDTO = new ContractCustPersonalDetailDTO();
                        BeanUtils.copyProperties(caseCustIndividual, custPersonalDetailDTO);
                        custPersonalDetailDTO.setSex((SexEnum) AfsEnumUtil.getEnum(caseCustIndividual.getSex(), SexEnum.class));
                        custPersonalDetailDTO.setUnclearedLoan(Convert.toBigDecimal(caseCustIndividual.getOutstandingLoan(), BigDecimal.ZERO));
                        personalDetail.add(custPersonalDetailDTO);

                        /**组装联系方式*/
                        if (caseCustIndividual!=null && !ObjectUtils.isEmpty(caseCustIndividual.getUnitTelPhone())) {
                            ContractCustContactDetailsDTO custContact = new ContractCustContactDetailsDTO();
                            custContact.setId(caseCustIndividual.getCustId());
                            custContact.setContactNumber(caseCustIndividual.getUnitTelPhone());
                            custContact.setContactType(ContactTypeEnum.WORK_PHONE);
                            custContractDetail.add(custContact);
                        }
                    }
                }else if(CustTypeEnum.COMPANY.equals(contractCustBaseInfoDTO.getCustType())){
                    log.info("====================公司客户信息明细信息====================》");
                    if(CustRoleEnum.MIANCUST.equals(contractCustBaseInfoDTO.getCustRole())){
                        //赋值基础信息客户姓名为企业名称
                        CaseEnterpriseCustomerDetails enterpriseCustomerDetails = caseEnterpriseCustomerDetailsService.getOne(
                                new LambdaQueryWrapper<CaseEnterpriseCustomerDetails>().eq(CaseEnterpriseCustomerDetails::getCustId,contractCustBaseInfoDTO.getId())
                        );
                        if(enterpriseCustomerDetails != null){
                            contractCustBaseInfoDTO.setCustName(enterpriseCustomerDetails.getEnterpriseName());
                            contractCustBaseInfoDTO.setCertNo(enterpriseCustomerDetails.getSocunicrtCode());
                        }
                    }
                    List<CaseEnterpriseCustomerDetails> enterpriseCustomers = caseEnterpriseCustomerDetailsService.list(Wrappers.<CaseEnterpriseCustomerDetails>lambdaQuery()
                            .eq(CaseEnterpriseCustomerDetails::getApplyNo, caseBaseInfo.getApplyNo()));
                    enterpriseCustomers.forEach(comEnterpriseCustomer ->{
                        BasicCustCompanyDetailDTO basicCustCompanyDetailDTO=new BasicCustCompanyDetailDTO();
                        basicCustCompanyDetailDTO.setCustNo(String.valueOf(comEnterpriseCustomer.getCustId()));
                        basicCustCompanyDetailDTO.setCompanyName(comEnterpriseCustomer.getEnterpriseName());
                        basicCustCompanyDetailDTO.setOrganizationNo(comEnterpriseCustomer.getSocunicrtCode());
                        basicCustCompanyDetailDTO.setCompanyType(comEnterpriseCustomer.getNatureEnterprise());
                        basicCustCompanyDetailDTO.setCompanyIndustry(comEnterpriseCustomer.getEnterprisesEngagedIndustry());
                        /**组装联系方式*/
                        if (!ObjectUtils.isEmpty(comEnterpriseCustomer.getEnterpriseContactMobilePhone())) {
                            ContractCustContactDetailsDTO custContact = new ContractCustContactDetailsDTO();
                            custContact.setId(comEnterpriseCustomer.getCustId());
                            custContact.setContactNumber(comEnterpriseCustomer.getEnterpriseContactMobilePhone());
                            custContact.setContactType(ContactTypeEnum.WORK_PHONE);
                            custContractDetail.add(custContact);
                        }
                        basicCustCompanyDetailList.add(basicCustCompanyDetailDTO);
                    });
                    log.info("====================公司客户信息明细信息====================》");
                }


                List<CaseCustAddress> custAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>lambdaQuery()
                        .eq(CaseCustAddress::getCustId, contractCustBaseInfoDTO.getId()));
                if (CollectionUtils.isNotEmpty(custAddressList)) {
                    log.info("=======合同客户【" + contractCustBaseInfoDTO.getId() + "】地址=============》");
                    for (CaseCustAddress custAddress : custAddressList) {
                        ContractCustAddressDetailsDTO contractCustAddressDetailsDTO = new ContractCustAddressDetailsDTO();
                        BeanUtils.copyProperties(custAddress, contractCustAddressDetailsDTO);
                        contractCustAddressDetailsDTO.setAddressType((AddressTypeEnum) AfsEnumUtil.getEnum(custAddress.getAddressType(), AddressTypeEnum.class));
                        contractCustAddressDetailsDTO.setValidStatus((ValidStatusEnum) AfsEnumUtil.getEnum(custAddress.getValidStatus(), ValidStatusEnum.class));
                        contractCustAddressDetailsDTO.setIsDefault((WhetherEnum) AfsEnumUtil.getEnum(custAddress.getIsDefault(), WhetherEnum.class));
                        addressDetail.add(contractCustAddressDetailsDTO);

                        if (AfsEnumUtil.key(TrueOrFalseEnum.IS_TRUE).equals(custAddress.getIsDefault())) {
                            /**邮寄地址*/
                            ContractCustAddressDetailsDTO mailingAddressDetailsDTO = new ContractCustAddressDetailsDTO();
                            BeanUtils.copyProperties(custAddress, mailingAddressDetailsDTO);
                            mailingAddressDetailsDTO.setAddressType((AddressTypeEnum) AfsEnumUtil.getEnum("10", AddressTypeEnum.class));
                            mailingAddressDetailsDTO.setValidStatus((ValidStatusEnum) AfsEnumUtil.getEnum(custAddress.getValidStatus(), ValidStatusEnum.class));
                            addressDetail.add(mailingAddressDetailsDTO);
                        }
                    }
                }



                log.info("========合同客户联系人信息==========》");
                List<CaseCustContact> caseCustContactList = caseCustContactService.list(Wrappers.<CaseCustContact>lambdaQuery()
                        .eq(CaseCustContact::getCustId, contractCustBaseInfoDTO.getId().toString()));
                if (CollectionUtils.isNotEmpty(caseCustContactList)) {
                    for (CaseCustContact caseCustContact : caseCustContactList) {
                        log.info("========合同客户联系人【" + caseCustContact.getId() + "】==========》");
                        ContractCustContactsDTO contractCustContactsDTO = new ContractCustContactsDTO();
                        BeanUtils.copyProperties(caseCustContact, contractCustContactsDTO);
                        contractCustContactsDTO.setContractNo(contractNo);
                        bsicCustContacts.add(contractCustContactsDTO);

                        /**如果是夫妻关系，并且，主共担里面没有夫妻关系的数据，则添加客户信息主表为联系人*/
                        if (CustRelationEnum.COUPLE.getCode().equals(caseCustContact.getCustRelation())) {
                            contactBaseInfoDTO.setId(caseCustContact.getId());
                            contactBaseInfoDTO.setCertNo(caseCustContact.getCertNo());
                            contactBaseInfoDTO.setCertType(caseCustContact.getCertType());
                            contactBaseInfoDTO.setContractNo(contractNo);
                            contactBaseInfoDTO.setCustName(caseCustContact.getCustName());
                            contactBaseInfoDTO.setCustRole(CustRoleEnum.CONTACT);
                            contactBaseInfoDTO.setTelPhone(caseCustContact.getTelPhone());
                            contactBaseInfoDTO.setCustType(CustTypeEnum.PERSON);
                            //合同客户详细信息
                            personalDetailDTO.setCustId(caseCustContact.getId());
                            personalDetailDTO.setUnitName(caseCustContact.getCompanyName());
                            personalDetailDTO.setMonthlyIncome(caseCustContact.getMonthlyIncome());

                            //合同与用户角色关系信息
                            relationDTO.setId(caseCustContact.getId());
                            relationDTO.setContractNo(contractNo);
                            relationDTO.setCustRole(CustRoleEnum.CONTACT);
                            if (!isCouple) {
                                relationDTO.setCustRelation(CustRelationEnum.COUPLE.getCode());
                            }
                            //合同客户联系方式
                            contactDetailsDTO.setId(caseCustContact.getId());
                            contactDetailsDTO.setContactNumber(caseCustContact.getTelPhone());
                            contactDetailsDTO.setContactType(ContactTypeEnum.PHONE);
                            contactDetailsDTO.setValidStatus(ValidStatusEnum.EFFECTIVE);
                            //合同客户地址信息
                            addressDetailsDTO.setCustId(caseCustContact.getId());
                            addressDetailsDTO.setAddressType(AddressTypeEnum.LIVING);
                            addressDetailsDTO.setProvince(caseCustContact.getLivingProvince());
                            addressDetailsDTO.setCity(caseCustContact.getLivingCity());
                            addressDetailsDTO.setCounty(caseCustContact.getLivingCounty());
                            addressDetailsDTO.setTown(caseCustContact.getTown());
                            addressDetailsDTO.setStreet(caseCustContact.getLivingStreet());
                            addressDetailsDTO.setDetailAddress(caseCustContact.getDetailAddress());
                            addressDetailsDTO.setValidStatus(ValidStatusEnum.EFFECTIVE);

                        }
                    }
                }
            }
        }
        if (!ObjectUtils.isEmpty(contactBaseInfoDTO.getId())) {
            baseInfo.add(contactBaseInfoDTO);
            personalDetail.add(personalDetailDTO);
            custRelation.add(relationDTO);
            custContractDetail.add(contactDetailsDTO);
            addressDetail.add(addressDetailsDTO);
        }
        List<CaseCarInfo> caseCarInfoList = caseCarInfoService
                .list(Wrappers.<CaseCarInfo>lambdaQuery().eq(CaseCarInfo::getContractNo, contractNo));
        if (CollectionUtils.isNotEmpty(caseCarInfoList)) {
            log.info("===============车辆申请信息===============》");
            for (CaseCarInfo caseCarInfo : caseCarInfoList) {
                ContractCarDetailsDTO contractCarDetailsDTO = new ContractCarDetailsDTO();
                BeanUtils.copyProperties(caseCarInfo, contractCarDetailsDTO);
                contractCarDetailsDTO.setCarFunction((CarFunctionEnum) AfsEnumUtil.getEnum(caseCarInfo.getCarFunction(), CarFunctionEnum.class));
                contractCarDetailsDTO.setIsCirTrans((WhetherEnum) AfsEnumUtil.getEnum(caseCarInfo.getIsCirTrans(), WhetherEnum.class));
                contractCarDetailsDTO.setIsMortgage((WhetherEnum) AfsEnumUtil.getEnum(caseCarInfo.getIsMortgage(), WhetherEnum.class));
                contractCarDetailsDTO.setDealerName(StringUtil.isNotEmpty(caseCarInfo.getCarDealerName()) ? caseCarInfo.getCarDealerName() : "");
                carDetail.add(contractCarDetailsDTO);

                final List<CarInvoiceInfo> invoiceInfos = carInvoiceInfoService
                        .list(Wrappers.<CarInvoiceInfo>lambdaQuery().eq(CarInvoiceInfo::getCarId, caseCarInfo.getId()));
                if (null != invoiceInfos) {
                    for (CarInvoiceInfo carInvoiceInfo : invoiceInfos) {
                        log.info("========发票信息表carID【" + caseCarInfo.getId() + "】===========》");
                        ContractCarInvoiceDTO contractCarInvoiceDTO = new ContractCarInvoiceDTO();
                        BeanUtils.copyProperties(carInvoiceInfo, contractCarInvoiceDTO);
                        carInvoice.add(contractCarInvoiceDTO);

                    }
                }
                CarGpsApply carGpsApply = carGpsApplyService
                        .getOne(Wrappers.<CarGpsApply>lambdaQuery().eq(CarGpsApply::getContractNo, contractNo));
                log.info("====================GPS申请信息====================》");
                if (null != carGpsApply) {
                    BeanUtils.copyProperties(carGpsApply, gpsApply);
                    gpsApply.setIsRobberyBag((WhetherEnum) AfsEnumUtil.getEnum(carGpsApply.getIsRobberyBag(), WhetherEnum.class));
                }

                List<CarGpsDevice> carGpsDeviceList = carGpsDeviceService
                        .list(Wrappers.<CarGpsDevice>lambdaQuery().eq(CarGpsDevice::getCarId, caseCarInfo.getId()));
                if (CollectionUtils.isNotEmpty(carGpsDeviceList)) {
                    log.info("===========GPS设备信息关联carID【" + caseCarInfo.getId() + "】============》");
                    for (CarGpsDevice carGpsDevice : carGpsDeviceList) {
                        ContractGpsDeviceDTO contractGpsDeviceDTO = new ContractGpsDeviceDTO();
                        BeanUtils.copyProperties(carGpsDevice, contractGpsDeviceDTO);
                        contractGpsDeviceDTO.setCarVin(caseCarInfo.getCarVin());
                        gpsDevice.add(contractGpsDeviceDTO);
                    }
                }

                List<CarInsuranceInfo> carInsuranceInfoList = carInsuranceInfoService
                        .list(Wrappers.<CarInsuranceInfo>lambdaQuery().eq(CarInsuranceInfo::getCarId, caseCarInfo.getId()));
                if (CollectionUtils.isNotEmpty(carInsuranceInfoList)) {
                    log.info("===========保险信息carID【" + caseCarInfo.getId() + "】===========》");
                    for (CarInsuranceInfo carInsuranceInfo : carInsuranceInfoList) {
                        ContractInsuranceInfoDTO contractInsuranceInfoDTO = new ContractInsuranceInfoDTO();
                        BeanUtils.copyProperties(carInsuranceInfo, contractInsuranceInfoDTO);
                        contractInsuranceInfoDTO.setInsuranceType((InsuranceTypeEnum) AfsEnumUtil.getEnum(carInsuranceInfo.getInsuranceType(), InsuranceTypeEnum.class));
                        insuranceInfo.add(contractInsuranceInfoDTO);
                    }
                }
                if (!ObjectUtils.isEmpty(caseCarInfo)) {
                    if (caseBaseInfo.getAffiliatedWay().equals(AffiliatedWayEnum.PERSONAL_AFFILIATED.getCode())
                            || caseBaseInfo.getAffiliatedWay().equals(AffiliatedWayEnum.PERSONAL_COMPANY_AFFILIATED.getCode())) {
                        affiliatedUnit.setAffiliatedName(caseCarInfo.getIndBusinessName());
                        affiliatedUnit.setSocUniCrtCode(caseCarInfo.getIndBusinessUsci());
                    }

                    if (caseBaseInfo.getAffiliatedWay().equals(AffiliatedWayEnum.COMPANY_AFFILIATED.getCode())
                            || caseBaseInfo.getAffiliatedWay().equals(AffiliatedWayEnum.NETWORK_CAR_AFFILIATED.getCode())) {
                        //add by likang 挂靠公司获取修改
                        ChannelAffiliatedUnits channelAffiliatedUnits = affiliatedUnitsService.getAffiliatedCompany(caseCarInfo.getAffCompanyId());
                        if (null != channelAffiliatedUnits) {
                            log.info("====================挂靠单位carID【" + caseCarInfo.getId() + "】====================》");
                            BeanUtils.copyProperties(channelAffiliatedUnits, affiliatedUnit);
                        }
                    }
                    affiliatedUnit.setContractNo(contractNo);
                    affiliatedUnit.setCarId(caseCarInfo.getId());
                    affiliatedUnit.setCooperPlat(caseCarInfo.getCooperPlat());
                    affiliatedUnit.setAffiliatedType((AffiliatedWayEnum) AfsEnumUtil.getEnum(caseBaseInfo.getAffiliatedWay(), AffiliatedWayEnum.class));
                } else {
                    throw new Exception("合同编号【" + contractNo + "】->车辆信息不存在");
                }
            }
        } else {
            throw new Exception("合同编号【" + contractNo + "】->车辆信息不存在");
        }

        List<FinCostDetails> finCostDetailsList = caseCostInfoService
                .list(Wrappers.<FinCostDetails>lambdaQuery().eq(FinCostDetails::getApplyNo, mainInfo.getApplyNo()));
        if (CollectionUtils.isNotEmpty(finCostDetailsList)) {
            log.info("====================金融协议====================》");
            for (FinCostDetails costInfo : finCostDetailsList) {
                ContractFinancialAgreementDTO financialAgreementDTO = new ContractFinancialAgreementDTO();
                BeanUtils.copyProperties(costInfo, financialAgreementDTO);
                financialAgreementDTO.setDiscountType((AfsEnumUtil.getEnum(costInfo.getDiscountType(), DiscountTypeEnum.class)).toString());
                financialAgreementDTO.setContractNo(contractNo);
                financialAgreementDTO.setStructuredMonth(costInfo.getStructuredMonth());
                financialAgreementDTO.setCostType((CostTypeEnum) AfsEnumUtil.getEnum(costInfo.getCostType(), CostTypeEnum.class));
                fnancialAgreement.add(financialAgreementDTO);
                List<HandlingInfo> handlinglist =handlingInfoService.list(Wrappers.<HandlingInfo>lambdaQuery()
                        .eq(HandlingInfo::getApplyNo,mainInfo.getApplyNo())
                        .eq(HandlingInfo::getDelFlag,"0").eq(HandlingInfo::getPaymentMethod, PaymentMethodEnum.DEDUCT));
                if(CollectionUtils.isNotEmpty(handlinglist)){
                    for (HandlingInfo handlingInfo : handlinglist) {
                        ContractHandlingDetailsDto handlingDetailsDto=new ContractHandlingDetailsDto();
                        BeanUtils.copyProperties(handlingInfo,handlingDetailsDto);
                        handlingDetailsDto.setContractNo(contractNo);
                        switch (handlingInfo.getHandlingType()){
                            case FIRM:
                                if("service".equals(handlingDetailsDto.getCostType())){
                                    handlingDetailsDto.setFixedAmount(financialAgreementDTO.getFirmServiceAmt());
                                }else if("handling".equals(handlingDetailsDto.getCostType())){
                                    handlingDetailsDto.setFixedAmount(financialAgreementDTO.getFirmHandlingAmt());
                                } else{
                                    throw new AfsBaseException("费用类型[" + handlingInfo.getCostType() + "],暂不支持");
                                }
                                break;
                            case CUSTOM:
                                if("service".equals(handlingDetailsDto.getCostType())){
                                    handlingDetailsDto.setFixedAmount(financialAgreementDTO.getCustServiceAmt());
                                }else if("handling".equals(handlingDetailsDto.getCostType())){
                                    handlingDetailsDto.setFixedAmount(financialAgreementDTO.getCustHandlingAmt());
                                }  else{
                                    throw new AfsBaseException("费用类型[" + handlingInfo.getCostType() + "],暂不支持");
                                }
                              break;
                            case PURCHASE_TAX:
                                //购置税手续费
                                handlingDetailsDto.setFixedAmount(financialAgreementDTO.getPurchaseHandlingAmt());
                                break;
                            default:
                                throw new AfsBaseException("类型[" + handlingInfo.getHandlingType() + "],暂不支持");
                        }
                        handlingDetailsDtos.add(handlingDetailsDto);
                    }
                }
                List<FinDiscountDetails> discountDetailList = caseDiscountDetailService
                        .list(Wrappers.<FinDiscountDetails>lambdaQuery().eq(null != costInfo, FinDiscountDetails::getCostId, costInfo.getId()));
                if (CollectionUtils.isNotEmpty(discountDetailList)) {
                    log.info("===========合同贴息明细信息关联costID【" + costInfo.getId() + "】==========》");
                    for (FinDiscountDetails caseDiscountDetail : discountDetailList) {
                        ContractDiscountDetailsDTO discountDetailsDTO = new ContractDiscountDetailsDTO();
                        BeanUtils.copyProperties(caseDiscountDetail, discountDetailsDTO);
                        discountDetailsDTO.setContractNo(contractNo);
                        discountDetailsDTO.setCostType((CostTypeEnum) AfsEnumUtil.getEnum(costInfo.getCostType(), CostTypeEnum.class));
                        discountDetails.add(discountDetailsDTO);
                    }
                    FinMainInfo finMainInfos = finMainInfoService.getOne(Wrappers.<FinMainInfo>query().lambda().eq(FinMainInfo::getApplyNo, mainInfo.getApplyNo()));
                    if (CollectionUtils.isNotEmpty(discountDetails) && null != finMainInfos){
                        /** sp自主贴息**/
                        boolean bool = true;
                        for (ContractDiscountDetailsDTO caseDiscountDetail : discountDetails) {
                            if ("1".equals(caseDiscountDetail.getDiscountId())) {
                                log.info("discountDetails.add.finMainInfos -->{}", JSON.toJSONString(finMainInfos));
                                if (null != finMainInfos && "1".equals(finMainInfos.getAutoDiscountType())) {
                                    bool = false;
                                    caseDiscountDetail.setDiscountAmt(caseDiscountDetail.getDiscountAmt().add(finMainInfos.getAutoDiscountAmt()));
                                    caseDiscountDetail.setMaxDiscountAmt(caseDiscountDetail.getMaxDiscountAmt().add(finMainInfos.getAutoDiscountAmt()));
                                }
                            }
                        }
                        if (bool) {
                            for (FinDiscountDetails caseDiscountDetail : discountDetailList) {
                                log.info("discountDetails.new.finMainInfos -->{}", JSON.toJSONString(finMainInfos));
                                ContractDiscountDetailsDTO discountDetailsDTO = new ContractDiscountDetailsDTO();
                                BeanUtils.copyProperties(caseDiscountDetail, discountDetailsDTO);
                                discountDetailsDTO.setDiscountAmt(finMainInfos.getAutoDiscountAmt());
                                discountDetailsDTO.setMaxDiscountAmt(finMainInfos.getAutoDiscountAmt());
                                discountDetailsDTO.setDiscountId("1");
                                discountDetails.add(discountDetailsDTO);
                            }
                        }
                    }else if (CollectionUtils.isEmpty(discountDetails) && null != finMainInfos){
                        log.info("new.discountDetails -->{}", JSON.toJSONString(finMainInfos));
                        ContractDiscountDetailsDTO discountDetailsDTO = new ContractDiscountDetailsDTO();
                        discountDetailsDTO.setContractNo(contractNo);
                        discountDetailsDTO.setMaxDiscountAmt(finMainInfos.getAutoDiscountAmt());
                        discountDetailsDTO.setDiscountAmt(finMainInfos.getAutoDiscountAmt());
                        discountDetailsDTO.setDiscountId("1");
                        discountDetailsDTO.setCostId(costInfo.getId());
                        discountDetailsDTO.setTotalToTotal("0");
                        discountDetails.add(discountDetailsDTO);
                    }
                }
            }
        }
        /** 贴息明细及产品明细 */
        List<FinDiscountPlanDTO> finDiscountPlanDTOList = null;
        List<PlanRateDTO> planRateDTOList = null;
        List<FinTermsDetails> finTermsDetails = null;
        List<FinRepaymentPlan> finRepaymentPlans = null;
        try {
            finDiscountPlanDTOList = getFindiscountPlans(mainInfo.getApplyNo());
            planRateDTOList = getPlanRates(mainInfo.getApplyNo());
            finTermsDetails = getFinTermsDetails(mainInfo.getApplyNo());
            finRepaymentPlans = getFinRepaymentPlans(mainInfo.getApplyNo());

        } catch (Exception e) {
            log.error("applyNo:{} 保存贴息及产品明细失败!错误信息:",mainInfo.getApplyNo(),e);
        }

        List<FinFinancingItems> financingItemsList = caseFinancingItemsService
                .list(Wrappers.<FinFinancingItems>lambdaQuery().eq(FinFinancingItems::getApplyNo, mainInfo.getApplyNo()));
        if (null != financingItemsList) {
            log.info("====================合同融资项目表====================》");
            for (FinFinancingItems finFinancingItems : financingItemsList) {
                ContractFinancingItemsDTO contractFinancingItemsDTO = new ContractFinancingItemsDTO();
                contractFinancingItemsDTO.setContractNo(contractNo);
                BeanUtils.copyProperties(finFinancingItems, contractFinancingItemsDTO);
                financingItems.add(contractFinancingItemsDTO);
            }
        }

        List<FinRentAdjustDetails> finRentAdjustDetailsList = rentAdjustDetailsService
                .list(Wrappers.<FinRentAdjustDetails>lambdaQuery().eq(FinRentAdjustDetails::getApplyNo, mainInfo.getApplyNo()));
        if (CollectionUtil.isNotEmpty(finRentAdjustDetailsList)) {
            log.info("====================合同租金调整表====================》");
            for (FinRentAdjustDetails finRentAdjustDetails : finRentAdjustDetailsList) {
                ContractFinRentAdjustDetailsDTO contractFinRentAdjustDetailsDTO = new ContractFinRentAdjustDetailsDTO();
                contractFinRentAdjustDetailsDTO.setContractNo(contractNo);
                BeanUtils.copyProperties(finRentAdjustDetails, contractFinRentAdjustDetailsDTO);
                finRentAdjustDetailsDTOList.add(contractFinRentAdjustDetailsDTO);
            }
        }

        LoanBankCardInfo loanBankCardInfo = loanBankCardInfoService
                .getOne(Wrappers.<LoanBankCardInfo>lambdaQuery().eq(LoanBankCardInfo::getContractNo, contractNo)
                        .eq(LoanBankCardInfo::getIsDefaultDeductCard, AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT)));
        if (null != loanBankCardInfo) {
            log.info("====================合同银行卡信息====================》");
            BeanUtils.copyProperties(loanBankCardInfo, bankCard);
            bankCard.setAuthorizeWay((AuthorizeWayEnum) AfsEnumUtil.getEnum(loanBankCardInfo.getAuthorizeWay(), AuthorizeWayEnum.class));
            /**银行卡激活默认签约成功*/
            bankCard.setVerStatus(VerStatusEnum.alreadySign);
        }

        CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                .eq(CaseChannelInfo::getApplyNo, mainInfo.getApplyNo()));
        ChannelBaseInfo channelBaseInfo = new ChannelBaseInfo();
        if (null != caseChannelInfo) {
            channelBaseInfo = channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>lambdaQuery()
                    .eq(StringUtils.isNotBlank(caseChannelInfo.getDealerNo()), ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
        }
        /**
         * 收款方账户信息
         */
        List<CasePayeeInfo> casePayeeInfos = casePayeeInfoService
                .list(Wrappers.<CasePayeeInfo>lambdaQuery().eq(CasePayeeInfo::getApplyNo, mainInfo.getApplyNo()));
        if (CollectionUtils.isNotEmpty(casePayeeInfos)) {
            log.info("合同号【" + contractNo + "】===========收款方账户信息===========》");
            for (CasePayeeInfo casePayeeInfo : casePayeeInfos) {
                if(StringUtils.isBlank(casePayeeInfo.getAfterPayFlag())||  "0".equals(casePayeeInfo.getAfterPayFlag())){//非后置支付
                    //-------------------------判断合作商是否修改收款账号start-----------------------------------
                    // 获取到启用的收款账号
                    Map<String, List<ChannelReceivablesAccount>> channelReceivablesAccountMap = Optional.ofNullable(
                                    channelAccountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                                                    .eq(ChannelReceivablesAccount::getChannelId,channelBaseInfo.getChannelId())
                                                    .eq(ChannelReceivablesAccount::getStatus, ChannelOnlineEnum.ENABLE.getCode()))
                                            .stream()
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.groupingBy(ChannelReceivablesAccount::getCollectionType)))
                            .orElse(new HashMap<>());

                    List<ChannelReceivablesAccount> channelReceivablesAccounts = channelReceivablesAccountMap.get(casePayeeInfo.getCollectionType());
                    if(CollUtil.isNotEmpty(channelReceivablesAccounts)) {
                        //更新案件合同和收款账号信息
                        ChannelReceivablesAccount account = channelReceivablesAccounts.get(0);
                        caseContractInfo.setReceivingId(account.getId());
                        caseContractInfo.setReceivingAccount(account.getReceivingAccount());
                        caseContractInfo.setReceivingBank(account.getReceivingBank());
                        caseContractInfoService.updateById(caseContractInfo);
                        casePayeeInfo.setReceiptId(account.getId().toString());
                        casePayeeInfo.setReceiptAccount(account.getReceivingAccount());
                        casePayeeInfo.setReceiptBankCode(account.getReceivingBank());
                        casePayeeInfoService.updateById(casePayeeInfo);

                        // 异步修改进件端收款信息
                        CaseContractInfoDto caseContractInfoDto = new CaseContractInfoDto();
                        CasePayeeInfoDto casePayeeInfoDto = new CasePayeeInfoDto();
                        BeanUtils.copyProperties(caseContractInfo, caseContractInfoDto);
                        BeanUtils.copyProperties(casePayeeInfo, casePayeeInfoDto);

                        List<CaseContractInfoDto> caseContractInfoDtos = new ArrayList<>();
                        List<CasePayeeInfoDto> casePayeeInfoDtos = new ArrayList<>();
                        caseContractInfoDtos.add(caseContractInfoDto);
                        casePayeeInfoDtos.add(casePayeeInfoDto);

                        CasePayeeInfoUpdateCondition casePayeeInfoUpdateCondition = new CasePayeeInfoUpdateCondition();
                        casePayeeInfoUpdateCondition.setCasePayeeInfoDtos(casePayeeInfoDtos);
                        casePayeeInfoUpdateCondition.setCaseContractInfoDtos(caseContractInfoDtos);

                        caseToApplyUpdatePayeeInfoSender
                                .sendCaseToApplyUpdatePayeeInfo(AfsTransEntity.<CasePayeeInfoUpdateCondition>builder()
                                        .transCode(MqTransCode.AFS_CASE_TO_APPLY_UPDATE_PAYEE_INFO)
                                        .data(casePayeeInfoUpdateCondition).build());
                    }
                    //-------------------------判断合作商是否修改收款账号end-----------------------------------
                    List<ChannelReceivablesAccount> accountList = accountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                            .eq(ChannelReceivablesAccount::getId, casePayeeInfo.getReceiptId()));
                    Assert.isTrue(CollectionUtils.isNotEmpty(accountList), "合同号【" + contractNo + "】=>根据receivingId未查询到收款信息：");
                    PaymentPool paymentPool = new PaymentPool();
                    paymentPool.setContractNo(caseContractInfo.getContractNo());
                    paymentPool.setFinanceItemCode(casePayeeInfo.getCollectionType());
                    paymentPool.setPaymentAmt(casePayeeInfo.getCollectionAmount());
                    if("1".equals(casePayeeInfo.getAutoFlag())){
                        paymentPool.setPaymentMode(PaymentModeEnum.autoPayment);
                    }else{
                        paymentPool.setPaymentMode(PaymentModeEnum.manualPayment);
                    }
                    paymentPool.setRecBankCity(accountList.get(0).getOpeningBankCity());
                    paymentPool.setRecBankProvince(accountList.get(0).getOpeningBankProvince());
                    paymentPool.setReceiptAccount(accountList.get(0).getReceivingAccount());
                    paymentPool.setReceiptAccountName(accountList.get(0).getReceivingName());
                    paymentPool.setReceiptBankCode(accountList.get(0).getReceivingBank());
                    paymentPool.setReceiptBankName(accountList.get(0).getReceivingSubBranch());
                    paymentPool.setReceiptEbankCode(accountList.get(0).getElectronicBankNo());
                    paymentPool.setRemake(casePayeeInfo.getRemark());
                    paymentPools.add(paymentPool);
                    log.info("合同号【" + contractNo + "】===========paymentPool添加完毕===========》");

                    ContractFinancingItemsDTO contractFinancingItemsDTO = new ContractFinancingItemsDTO();
                    contractFinancingItemsDTO.setCarId(null);//?
                    contractFinancingItemsDTO.setContractNo(contractNo);
                    contractFinancingItemsDTO.setFinanceItemAmt(casePayeeInfo.getCollectionAmount());
                    contractFinancingItemsDTO.setFinanceItemCode(casePayeeInfo.getCollectionType());
                    contractFinancingItemsDTO.setFinanceItemName(AfsEnumUtil.desc(CollectionTypeEnum.getDescByStatus(casePayeeInfo.getCollectionType())));
                    contractFinancingItemsDTO.setIsCalLoanAmt("Y");
                    contractFinancingItemsDTO.setIsParent(null);
                    contractFinancingItemsDTO.setFinanceItemTotal(1);
                    contractFinancingItemsDTO.setId(null);
                    contractFinancingItemsDTO.setUpperId(null);
                    financingItems.add(contractFinancingItemsDTO);
                    log.info("合同号【" + contractNo + "】===========financingItems添加完毕===========》");
                }
            }
        }

        if (null != caseChannelInfo) {
            mainInfo.setSubjectCode(caseChannelInfo.getSubjectCode());
            mainInfo.setSubjectName(caseChannelInfo.getSubjectName());
            mainInfo.setDeptAttributionId(caseChannelInfo.getDeptAttributionId());
            mainInfo.setDeptAttributionTitle(caseChannelInfo.getDeptAttributionTitle());
            List<ChannelRiskInfo> channelRiskInfoList=channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>lambdaQuery().eq(ChannelRiskInfo::getChannelId,channelBaseInfo.getId()));
            log.info("====================获取风控信息数据====================》{}",channelRiskInfoList);
            //0对应01新车  1对应02二手车
            if (caseContractInfo.getBusinessType() == "02") {
            }
            List<ChannelWitnessInfo> channelWitnessInfos = channelWitnessInfoService.list(Wrappers.<ChannelWitnessInfo>query().lambda()
                    .eq(ChannelWitnessInfo::getWitnessIdCard, caseContractInfo.getWitnessId()));

            if (null != channelBaseInfo) {
                log.info("====================合同渠道信息记录信息====================》");
                mainInfo.setChannelType((ChannelTypeEnum) AfsEnumUtil.getEnum(channelBaseInfo.getChannelType(), ChannelTypeEnum.class));
                mainInfo.setChannelName(channelBaseInfo.getChannelFullName());
                //String id = AfsEnumUtil.key(PaymentObjectEnum.partners).equals(channelBaseInfo.getPaymentObject())?channelBaseInfo.getChannelId():caseChannelInfo.get;
                mainInfo.setChannelId(channelBaseInfo.getId());
                /**直营车商的案件从渠道案件表获取直营车商ID可在渠道付款账户表查询付款账户信息*/
                List<ChannelReceivablesAccount> account = new ArrayList<>();

                //修复channelBaseInfo.getPaymentObject()有可能为""的问题  谭友杰 2022/4/27
                if (!StringUtils.isNotBlank(channelBaseInfo.getPaymentObject())) {
                    channelBaseInfo.setPaymentObject("0");
                }

                //0-主体，1-车商
                if (AfsEnumUtil.key(PaymentObjectEnum.partners).equals(channelBaseInfo.getPaymentObject())) {
                    account = accountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                            .eq(ChannelReceivablesAccount::getId,caseContractInfo.getReceivingId())
                            .eq(ChannelReceivablesAccount::getStatus, ChannelOnlineEnum.ENABLE.getCode()));
                } else if (AfsEnumUtil.key(PaymentObjectEnum.car_dealer).equals(channelBaseInfo.getPaymentObject())) {
                    account = accountInfoService.list(Wrappers.<ChannelReceivablesAccount>query().lambda()
                            .eq(ChannelReceivablesAccount::getId,caseContractInfo.getReceivingId())
                            .eq(ChannelReceivablesAccount::getStatus, ChannelOnlineEnum.ENABLE.getCode()));
                }

                Assert.isTrue(CollectionUtils.isNotEmpty(account), "合同号【" + contractNo + "】渠道ID【" + channelBaseInfo.getId() + "】=>付款账户信息为空：");
                log.info("====================合同渠道账户信息记录信息====================》");
                for (ChannelReceivablesAccount channelReceivablesAccount : account) {
                    ContractChannelInfoDTO contractChannelInfoDTO = new ContractChannelInfoDTO();
                    BeanUtils.copyProperties(channelReceivablesAccount, contractChannelInfoDTO);
                    contractChannelInfoDTO.setCollectionType((AfsEnumUtil.getEnum(channelReceivablesAccount.getCollectionType(), CollectionTypeEnum.class)).toString());
                    contractChannelInfoDTO.setContractNo(contractNo);
                    contractChannelInfoDTO.setContactName(channelBaseInfo.getChannelAdmin());
                    contractChannelInfoDTO.setContactPhone(channelBaseInfo.getChannelAdminTel());
                    contractChannelInfoDTO.setChannelType(mainInfo.getChannelType());
                    contractChannelInfoDTO.setSocUniCrtCode(channelBaseInfo.getSocUniCrtCode());
                    contractChannelInfoDTO.setPartnerCode(channelBaseInfo.getChannelCode());
                    contractChannelInfoDTO.setChannelFullName(channelBaseInfo.getChannelFullName());
                    contractChannelInfoDTO.setAccountType((AccountTypeEnum) AfsEnumUtil.getEnum(channelReceivablesAccount.getAccountType(), AccountTypeEnum.class));
                    contractChannelInfoDTO.setOnlineDate(channelBaseInfo.getOnlineDate());
                    contractChannelInfoDTO.setRegistrationDate(channelBaseInfo.getRegistrationDate());
                    contractChannelInfoDTO.setLegalPerson(channelBaseInfo.getLegalPerson());
                    contractChannelInfoDTO.setLegalPersonIdCard(channelBaseInfo.getLegalPersonIdCard());
                    contractChannelInfoDTO.setChannelAdminMail(channelBaseInfo.getChannelAdminMail());
                    contractChannelInfoDTO.setChannelAdminProvince(channelBaseInfo.getChannelAdminProvince());
                    contractChannelInfoDTO.setChannelAdminCity(channelBaseInfo.getChannelAdminCity());
                    contractChannelInfoDTO.setChannelAdminAddress(channelBaseInfo.getChannelAdminAddress());
                    contractChannelInfoDTO.setChannelProvince(channelBaseInfo.getChannelProvince());
                    contractChannelInfoDTO.setChannelCity(channelBaseInfo.getChannelCity());
                    contractChannelInfoDTO.setAccountAttribute((BusinessTypeEnum) AfsEnumUtil.getEnum(channelReceivablesAccount.getAccountAttribute(), BusinessTypeEnum.class));
                    contractChannelInfoDTO.setChannelBelong(channelBaseInfo.getChannelBelong());
                    contractChannelInfoDTO.setChannelId(channelBaseInfo.getChannelId());
                    Long carDealersId = null;
                    if (StringUtils.isNotBlank(caseChannelInfo.getCarDealersId())) {
                        carDealersId = Long.valueOf(caseChannelInfo.getCarDealersId());
                    }
                    contractChannelInfoDTO.setCardealerId(carDealersId);
                    contractChannelInfoDTO.setCardealerName(caseChannelInfo.getCarDealersName());
                    if (AfsEnumUtil.key(PaymentObjectEnum.partners).equals(channelBaseInfo.getPaymentObject())) {
                        contractChannelInfoDTO.setIsDirect(WhetherEnum.NO.getCode());
                    } else if (AfsEnumUtil.key(PaymentObjectEnum.car_dealer).equals(channelBaseInfo.getPaymentObject())) {
                        contractChannelInfoDTO.setIsDirect(WhetherEnum.YES.getCode());
                    }
                    if (CollectionUtils.isNotEmpty(channelWitnessInfos)) {
                        contractChannelInfoDTO.setWitnessName(channelWitnessInfos.get(0).getWitnessName());
                        contractChannelInfoDTO.setWitnessPhone(channelWitnessInfos.get(0).getWitnessContactNumber());
                    }
                    contractChannelInfoDTO.setSapCode(channelBaseInfo.getSpaCode());
                    contractChannelInfoDTO.setSapSupplierCode(channelBaseInfo.getSpaSupplierCode());
                    log.info("====================合同渠道信息====================》{}",contractChannelInfoDTO);
                    channelInfo.add(contractChannelInfoDTO);
                }
            }
        }

        /**同步附件*/
        List<ComAttachmentFile> contractfiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBelongNo, contractNo)
                .eq(ComAttachmentFile::getBusiNo, contractNo));
        contractfiles.forEach(comAttachmentFile -> {
            ComAttachmentFileDTO attachmentDTO = new ComAttachmentFileDTO();
            BeanUtil.copyProperties(comAttachmentFile, attachmentDTO, CopyOptions.create().setIgnoreNullValue(true));
            fileDTOList.add(attachmentDTO);
        });

        List<ComAttachmentFile> applyFiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBelongNo, caseBaseInfo.getApplyNo())
                .eq(ComAttachmentFile::getBusiNo, caseBaseInfo.getApplyNo()));
        applyFiles.forEach(comAttachmentFile -> {
            ComAttachmentFileDTO attachmentDTO = new ComAttachmentFileDTO();
            BeanUtil.copyProperties(comAttachmentFile, attachmentDTO, CopyOptions.create().setIgnoreNullValue(true));
            fileDTOList.add(attachmentDTO);
        });

        /**
         * 联合方信息
         */
        final CaseChannelUniteInfo channelUniteInfo = caseChannelUniteInfoService.getOne(Wrappers.<CaseChannelUniteInfo>lambdaQuery()
                .eq(CaseChannelUniteInfo::getApplyNo, caseBaseInfo.getApplyNo()));
        if(null != channelUniteInfo){
            BeanUtils.copyProperties(channelUniteInfo, channelUniteInfoDTO);
        }
        if(IsTypeNumEnum.YES.getCode().equals(channelUniteInfoDTO.getIsChannelUnite())&&"RT0003".equals(mainInfo.getSubjectCode())&&"06".equals(channelUniteInfoDTO.getUniteId())) {
            IResponse iResponse = channelApiFeign.getInfoByJointPartyCode("09",this.makeChannelHeader());
            Assert.isTrue(CODE_SUCCESS.equals(iResponse.getCode())&&iResponse.getData()!=null, "合同号【" + contractNo + "】主体CODE【" + mainInfo.getSubjectCode() + "】=>配置有问题：");
            HashMap hashMap = (HashMap) iResponse.getData();
            channelUniteInfoDTO.setRatio(new BigDecimal(String.valueOf(hashMap.get("clearingRatio"))));
            channelUniteInfoDTO.setUniteId((String)hashMap.get("jointPartyCode"));
            channelUniteInfoDTO.setUniteName((String)hashMap.get("jointPartyFullName"));
        }

        /**
         * 承租人开票信息
         */
        CaseReceiptInfo caseReceiptInfo =  caseReceiptInfoService.getOne(Wrappers.<CaseReceiptInfo>query().lambda()
                .eq(CaseReceiptInfo::getApplyNo, mainInfo.getApplyNo()));
        if (null != caseReceiptInfo) {
            BeanUtils.copyProperties(caseReceiptInfo, basicInvoiceInfoDTO);
            basicInvoiceInfoDTO.setApplyPrincipalStatus(caseReceiptInfo.getInvoiceDisposableFlag());
            basicInvoiceInfoDTO.setContractNo(contractNo);
        }
        MarginInfoDto marginInfoDto=  marginBusinessService.getMarginDetailDto( mainInfo.getApplyNo(),contractNo);


        //车辆订金
        CaseCarDeposit caseCarDeposit = caseCarDepositService.getOne(Wrappers.<CaseCarDeposit>lambdaQuery().eq(CaseCarDeposit::getApplyNo, mainInfo.getApplyNo()));
        if(!ObjectUtils.isEmpty(caseCarDeposit)){
            BeanUtils.copyProperties(caseCarDeposit, contractDepositDTO);
        }
        log.info("ApproveLoanInfoService.addData.ApplyCarDeposit -->{}", JSON.toJSONString(caseCarDeposit));

        log.info("====================合同信息组装完成====================》");
        return LoanToContractDataMessage.builder()
                .mainInfo(mainInfo)
                .baseInfo(baseInfo)
                .custContractDetail(custContractDetail)
                .addressDetail(addressDetail)
                .custRelation(custRelation)
                .personalDetail(personalDetail)
                .carDetail(carDetail)
                .carInvoice(carInvoice)
                .insuranceInfo(insuranceInfo)
                .gpsApply(gpsApply)
                .gpsDevice(gpsDevice)
                .fnancialAgreement(fnancialAgreement)
                .financingItems(financingItems)
                .affiliatedUnit(affiliatedUnit)
                .discountDetails(discountDetails)
                .bankCard(bankCard)
                .channelInfo(channelInfo)
                .bsicCustContacts(bsicCustContacts)
                .finRentAdjustDetailsList(finRentAdjustDetailsDTOList)
                .addPriceItems(addPriceItems)
                .comAttachementFile(fileDTOList)
                .contractChannelUniteInfo(channelUniteInfoDTO)
                .basicCustCompanyDetailDTOS(basicCustCompanyDetailList) //add by wangxu 2021/11/26 缺失企业地址参数，补充
                .handlingDetailsDtos(handlingDetailsDtos)
                .marginInfoDto(marginInfoDto)
                .paymentPools(paymentPools)
                .basicInvoiceInfoDTO(basicInvoiceInfoDTO)
                .finDiscountPlanDTOS(finDiscountPlanDTOList)
                .planRateDTOS(planRateDTOList)
                .finTermsDetailsList(finTermsDetails)
                .finRepaymentPlanList(finRepaymentPlans)
                .depositDTO(contractDepositDTO)
                .build();
    }

    private List<FinRepaymentPlan> getFinRepaymentPlans(String applyNo) {
        log.info("申请编号:{} 推送合同激活开始获取还款计划明细!",applyNo);
        return finRepaymentPlanService.list(Wrappers.<FinRepaymentPlan>lambdaQuery()
                .eq(StringUtils.isNotBlank(applyNo), FinRepaymentPlan::getApplyNo, applyNo));
    }

    private List<FinTermsDetails> getFinTermsDetails(String applyNo) {
        log.info("申请编号:{} 推送合同激活开始获取产品期数明细!",applyNo);
        return finTermsDetailsService.list(Wrappers.<FinTermsDetails>lambdaQuery()
                        .eq(StringUtils.isNotBlank(applyNo), FinTermsDetails::getApplyNo, applyNo));
    }

    /**
     * 获取产品利率明细
     * @param applyNo
     * @return
     */
    private List<PlanRateDTO> getPlanRates(String applyNo) {
        List<FinPlanRate> finPlanRates = finPlanRateService
                .list(Wrappers.<FinPlanRate>lambdaQuery()
                        .eq(StringUtils.isNotBlank(applyNo), FinPlanRate::getApplyNo, applyNo));
        if (CollectionUtils.isEmpty(finPlanRates)) {
            return null;
        }
        List<PlanRateDTO> planRateDTOList = new ArrayList<>(finPlanRates.size());
        finPlanRates.forEach(finPlanRate -> {
            PlanRateDTO dto = new PlanRateDTO();
            BeanUtils.copyProperties(finPlanRate, dto);
            planRateDTOList.add(dto);
        });
        return planRateDTOList;
    }

    /**
     * 获取贴息明细
     * @param applyNo   申请编号
     * @return
     */
    private List<FinDiscountPlanDTO> getFindiscountPlans(String applyNo) {
        log.info("申请编号:{} 推送合同激活开始获取贴息明细!",applyNo);
        List<FinDiscountPlan> finDiscountPlans = finDiscountPlanService
                .list(Wrappers.<FinDiscountPlan>lambdaQuery()
                        .eq(StringUtils.isNotBlank(applyNo), FinDiscountPlan::getApplyNo, applyNo));
        if (CollectionUtils.isEmpty(finDiscountPlans)) {
            return null;
        }
        List<FinDiscountPlanDTO> finDiscountPlanDTOList = new ArrayList<>(finDiscountPlans.size());
        finDiscountPlans.forEach(finDiscountPlan -> {
            FinDiscountPlanDTO dto = new FinDiscountPlanDTO();
            BeanUtils.copyProperties(finDiscountPlan, dto);
            finDiscountPlanDTOList.add(dto);
        });
        return finDiscountPlanDTOList;
    }

    @ApiOperation("放款数据落库结果通知")
    @Override
    public void loanApplyResult(LoanResultInfoDto dto) {
        AfsTransEntity<LoanSubmitInfo> transEntity = new AfsTransEntity();
        LoanSubmitInfo loanSubmitInfo = new LoanSubmitInfo();
        loanSubmitInfo.setLoanResultInfoDto(dto);
        transEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_MODIFY_LOAN);
        transEntity.setData(loanSubmitInfo);
        infoSender.loanApplyResult(transEntity);
    }

    @ApiOperation("GPS安装规则数据推送")
    @Override
    public void sendGpsMsgToApply(Long id) {
        log.info("-----------------GPS安装规则数据组装begin---------------->");

        Assert.isTrue(null != id, "规则【" + id + "】不存在，推送数据失败");

        LoanGpsRuleInfo loanGpsRuleInfo = loanGpsRuleInfoService.getById(id);
        LoanGpsRuleInfoDTO loanGpsRuleInfoDTO = new LoanGpsRuleInfoDTO();
        BeanUtils.copyProperties(loanGpsRuleInfo, loanGpsRuleInfoDTO);

        Long ruleId = loanGpsRuleInfo.getRuleId();
        Assert.isTrue(null != ruleId, "规则【" + id + "】规则编号不存在，推送数据失败");
        LoanGpsRuleDTO loanGpsRuleDTO = LoanGpsRuleDTO.builder()
                .loanGpsRuleInfoDTO(loanGpsRuleInfoDTO).build();
        ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder()
                .loanGpsRuleDTO(loanGpsRuleDTO)
                .build();
        log.info("-----------------GPS安装规则数据组装end---------------->");

        log.info("-----------------GPS安装规则通知begin---------------->");
        infoSender.sendGpsMsgToApplyNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_MODIFY_ORDER)
                .data(approveSubmitInfo).build());
        log.info("-----------------GPS安装规则通知end---------------->");
    }

    @ApiOperation("放款模式规则数据推送")
    @Override
    public void sendLoanModelToApply(Long id) {
        log.info("-----------------放款模式规则数据组装begin---------------->");

        Assert.isTrue(null != id, "规则【" + id + "】不存在，推送数据失败");

        LoanModeRuleInfo loanModeRuleInfo = loanModeRuleInfoService.getById(id);
        LoanModelRuleInfoDto loanModelRuleInfoDto = new LoanModelRuleInfoDto();
        BeanUtils.copyProperties(loanModeRuleInfo, loanModelRuleInfoDto);

        Long ruleId = loanModeRuleInfo.getRuleId();
        Assert.isTrue(null != ruleId, "规则【" + id + "】规则编号不存在，推送数据失败");

        LoanModelRuleDto loanModelRuleDto = LoanModelRuleDto.builder()
                .loanModelRuleInfoDto(loanModelRuleInfoDto).build();
        ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder()
                .loanModelRuleDto(loanModelRuleDto)
                .build();
        log.info("-----------------放款模式规则数据组装end---------------->");

        log.info("-----------------放款模式规则通知begin---------------->");
        infoSender.sendLoanModelToApplyNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_MODIFY_ORDER)
                .data(approveSubmitInfo).build());
        log.info("----------------放款模式规则通知end---------------->");
    }

    /**
     * 通知 合同系统GPS状态
     *
     * @param contractNo
     * @param status
     */
    @ApiOperation("通知进件GPS状态")
    @Override
    public void sendGpsStatusToContract(String contractNo, String status) {
        log.info("------------通知进件GPS状态【" + status + "】组装begin----------->");
        if (null != contractNo) {
            SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                    .contractNo(contractNo)
                    .stageId(null)
                    .status(status)
                    .operateTime(new Date()).build();
            ApproveSubmitInfo submitInfo =
                    ApproveSubmitInfo.builder()
                            .sendToApplyContractInfoDTO(sendToApplyContractInfoDTO)
                            .build();
            log.info("-----------------通知合同GPS状态【" + status + "】组装end---------------->");

            log.info("-----------------通知合同GPS状态发送begin---------------->");
            manageAssetsChangeCaseSender.sendGpsStatusToContract(AfsTransEntity.<ApproveSubmitInfo>builder()
                    .transCode(MqTransCode.BOSS_GPS_APPROVED_INFO_ICOS)
                    .data(submitInfo).build());
            log.info("-----------------通知合同GPS状态发送end---------------->");
        }
    }

    /**
     * 同步激活信息给进件系统
     *
     * @param contractNo
     * @param steps
     */
    @ApiOperation("通知进件激活状态")
    @Override
    public void sendActiveStatusToWow(String contractNo, int steps) {
        log.info("--合同{}通知进件,激活状态:{}=>组装begin---", contractNo, steps);
        String msg = "";
        switch (steps) {
            case 0:
                msg = "归档超期";
                break;
            case 1:
                msg = "额度不足";
                break;
            case 2:
                msg = "放款审核通过";
                break;
            case 3:
                msg = "命中黑名单";
                break;
            case 4:
                msg = "命中暂停规则";
                break;
            case 5:
                msg = "GPS审核不通过";
                break;
            case 6:
                msg = "审批时效";
                break;
            case 7:
                msg = "合同激活失败";
                break;
            case 100:
                msg = "合同激活成功";
                break;
            default:
                msg = "合同未激活";
                break;
        }
        if (StrUtil.isNotBlank(contractNo)) {
            SendToApplyActiveMsgDTO sendToApplyActiveMsgDTO = SendToApplyActiveMsgDTO.builder()
                    .contractNo(contractNo)
                    .activeMsg(msg)
                    .build();
            log.info("-----------------通知进件激活信息发送begin---------------->");
            infoSender.sendToActiveMsgNotice(AfsTransEntity.<SendToApplyActiveMsgDTO>builder()
                    .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_ACTIVE_NOTICE)
                    .data(sendToApplyActiveMsgDTO).build());
            log.info("-----------------通知进件激活信息态发送end---------------->");
        }
    }
    /**
     * 获取渠道请求头
     *
     * @return
     */
    public Map makeChannelHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", channelApiConfig.getChannelClientId());
        headers.put("clientSecret", channelApiConfig.getChannelClientSecret());
        return headers;
    }

//    public static void main(String[] args) {
//        FinCostDetails costInfo = new FinCostDetails();
//        costInfo.setStructuredMonth(3);
//        ContractFinancialAgreementDTO financialAgreementDTO = new ContractFinancialAgreementDTO();
//        BeanUtils.copyProperties(costInfo, financialAgreementDTO);
//
//        System.out.println(JSON.toJSONString(financialAgreementDTO));
//
//    }
}
