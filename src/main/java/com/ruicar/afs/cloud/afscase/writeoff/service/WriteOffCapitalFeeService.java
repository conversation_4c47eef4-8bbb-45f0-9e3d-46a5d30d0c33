package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalFee;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import javax.servlet.http.HttpServletResponse;

public interface WriteOffCapitalFeeService extends IService<WriteOffCapitalFee> {

    /**
     * 导出资方核销项金额明细
     * @param condition
     * @param response
     */
    void exportHxxFee(WriteOffCapitalFee condition, HttpServletResponse response);

    /**
     * 资方核销项生成提取项
     * @return
     */
    IResponse createTqRels();
}
