package com.ruicar.afs.cloud.afscase.job.writeoff;

import cn.hutool.core.util.StrUtil;
import com.ruicar.afs.cloud.afscase.writeoff.service.ChannelServiceFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffApportionInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffClearInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffRuleService;
import com.ruicar.afs.cloud.common.job.core.biz.model.ReturnT;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJob;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJobHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;


/**
 * 服务费定时任务
 */
@AfsJob
@Slf4j
@AllArgsConstructor
@Component
public class WriteOffRuleJob {

    private final WriteOffRuleService writeOffRuleService;
    private final WriteOffApportionInfoService writeOffApportionInfoService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final ChannelServiceFeeService channelServiceFeeService;
    private final WriteOffClearInfoService writeOffClearInfoService;

    @AfsJobHandler(value = "writeOffCalRuleJob")
    public ReturnT<String> writeOffCalRuleJob(String param) {
        log.info("启动【服务费计算定时任务】");
        if (StrUtil.isNotBlank(param)) {
            String[] split = param.split("@");
            Assert.isTrue(split.length == 2, "参数输入异常");
            boolean zjdFlag = "zjd".equals(split[1]);
            writeOffRuleService.writeOffCalRuleJob(split[0], zjdFlag, null, null, null);
        }else {
            writeOffRuleService.writeOffCalRuleJob(null, false, null, null, null);
        }
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "writeOffApportionJob")
    public ReturnT<String> writeOffApportionJob(String param) {
        if (StrUtil.isNotBlank(param)) {
            log.info("开始-社会店服务费分摊");
            writeOffRuleService.spWriteOffApportionJob(param);
        } else {
            log.info("启动【服务费分摊定时任务】");
            log.info("开始-直营店服务费分摊");
            writeOffRuleService.directWriteOffApportionJob();
            log.info("开始-社会店服务费分摊");
            writeOffRuleService.spWriteOffApportionJob(null);
            log.info("开始-外部订单-直营店服务费分摊");
            writeOffApportionInfoService.outDirectApportionJob();
            log.info("开始-外部订单-社会店服务费分摊");
            writeOffApportionInfoService.outSpApportionJob();
        }
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "tempWriteOffRepairJob")
    public ReturnT<String> tempWriteOffRepairJob(String param) {
        log.info("启动【服务费数据修复临时定时任务】,参数：{}", param);
        writeOffRuleService.dataRepairTempJob(param);
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "writeOffTimeSyncJob")
    public ReturnT<String> writeOffTimeSyncJob(String param) {
        log.info("启动【服务费时间同步定时任务】,参数：{}", param);
        writeOffBaseInfoService.writeOffTimeSyncJob(param);
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "writeOffDueCancelJob")
    public ReturnT<String> writeOffDueCancelJob(String param) {
        log.info("启动【服务费超期自动取消定时任务】,参数：{}", param);
        writeOffBaseInfoService.writeOffDueCancelJob(param);
        log.info("启动【服务费回司超期自动冻结定时任务】,参数：{}", param);
        writeOffBaseInfoService.writeOffReturnExpireFrozenJob(param);
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "writeOffGetCbsResultJob")
    public ReturnT<String> writeOffGetCbsResultJob(String param) {
        log.info("启动【服务费提取查询cbs付款结果定时任务】,参数：{}", param);
        channelServiceFeeService.getCbsPayResult(new ArrayList<>());
        return ReturnT.SUCCESS;
    }

    @AfsJobHandler(value = "writeOffClearJob")
    public ReturnT<String> writeOffClearJob(String param) {
        log.info("启动【服务费清账定时任务】,参数：{}", param);
        writeOffClearInfoService.writeOffClearJob(param);
        return ReturnT.SUCCESS;
    }
}
