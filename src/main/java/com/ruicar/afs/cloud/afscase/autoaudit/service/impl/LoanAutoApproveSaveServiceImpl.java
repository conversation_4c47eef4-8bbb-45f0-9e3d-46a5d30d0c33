package com.ruicar.afs.cloud.afscase.autoaudit.service.impl;


import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.enums.RealTimeDataTypeEnum;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.autoaudit.service.LoanAutoApproveSaveService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanApproveService;
import com.ruicar.afs.cloud.afscase.message.entity.MessageTemplate;
import com.ruicar.afs.cloud.afscase.message.service.MessageTemplateService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PriorityEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.enums.common.SubmissionTypeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.message.sendmessage.service.MessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.concurrent.TimeUnit;


@Service
@AllArgsConstructor
@Slf4j
public class LoanAutoApproveSaveServiceImpl  implements LoanAutoApproveSaveService {

    private final LoanApproveService loanApproveService;
    private final CaseContractInfoService caseContractInfoService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final CaseChannelInfoService caseChannelInfoService;
    private final CaseBaseInfoService caseBaseInfoService;
    private final MessageTemplateService messageTemplateService;
    private final MessageService messageService;
    private final ApplyServiceFeign applyServiceFeign;

    /**
     * 放款自动审核通过后逻辑
     * @param contractInfo 合同信息
     * @param flag 标识符 1 - 首次提交 2 - 非首次提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void loanAutoApprovedLogic(CaseContractInfo contractInfo, String flag){

        log.info("第{}次放款逻辑提交...",flag);

        int priorityValue=Integer.valueOf(contractInfo.getPriority());
        if(priorityValue>=Integer.valueOf(PriorityEnum.ORDINARY.getCode())){
            contractInfo.setPriority(PriorityEnum.ORDINARY.getCode());
        }
        //修改放款审核状态（修改为预审批状态）
        contractInfo.setApplyStatus(ApplyStatusEnum.LOAN_WAIT_APPROVE.getState());//放款待确认
        log.info("*****************" + contractInfo.getContractNo() + "放款审核状态change:" + ApplyStatusEnum.LOAN_WAIT_APPROVE.getState() + "*****************");
        contractInfo.setLoanAuditor("admin");


        if(AfsEnumUtil.key(SubmissionTypeEnum.FIRST_TIME).equals(flag)){

            log.warn("放款审核一次直接通过，准备参数。。。");
            LoanApproveSubmitVO submitVO = new LoanApproveSubmitVO();
            submitVO.setContractNo(contractInfo.getContractNo());
            CaseApproveRecord approveRecord = new CaseApproveRecord();
            approveRecord.setApplyNo(contractInfo.getApplyNo());
            submitVO.setApproveRecord(approveRecord);

            // 更新状态 & 通知进件
            log.warn("放款审核一次直接通过，开始更新状态并且通知进件。。。");
            loanApproveService.newReviewSubmit(submitVO,contractInfo);
            //草稿状态改为合格状态
            log.warn("放款审核一次直接通过，将草稿状态修改为合格状态。。。");
            comAttachmentFileService.updateFileStatusByBusiNo(contractInfo.getContractNo());
            //modify by likang 插入挂靠实时数据
            log.warn("放款审核一次直接通过，插入挂靠实时数据。。。");
            caseChannelInfoService.insertAffiliatedRealtimeData(contractInfo.getApplyNo(), RealTimeDataTypeEnum.CONTRACT.getIndex());

            // 激活合同
            ThreadUtil.execute(()->{
                try {
                    /**等待0.5秒执行*/
                    TimeUnit.MILLISECONDS.sleep(500);
                    loanApproveService.doApproveDone(submitVO.getContractNo());
                }catch (Throwable e){
                    log.error("复审通过后置事件完成,运行结果",e);
                }
            });

            // 锁定合同目录
            try{
                applyServiceFeign.saveLockInfoByApplyNo(contractInfo.getApplyNo(),CaseConstants.MESSAGE_TYPE_TWO);
            } catch (Exception e){
                log.error("保存锁定合同目录信息失败！");
            }

        }else{
            caseContractInfoService.updateById(contractInfo);
        }
    }

    /**
     * 放款自动审核通过的时候发送短信
     * @param applyNo
     */
    public void sendMessage(String applyNo){
        try {
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, applyNo));
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo,applyNo));
            MessageTemplate messageTemplate = messageTemplateService.getOne(Wrappers.<MessageTemplate>query().lambda()
                    .eq(MessageTemplate::getTemplateType,"14")
                    .eq(MessageTemplate::getTemplateId,"loan_submit_financier")
                    .eq(MessageTemplate::getStatus, AfsEnumUtil.key(YesOrNoEnum.yes)));
            Assert.isTrue(messageTemplate!=null,"放款审核通过通知-金融专员短信模板不存在！");
            String msg = messageTemplate.getTemplateContent().replace("dealerName",caseBaseInfo.getDealerName())
                    .replace("custNameRepeat",caseBaseInfo.getCustNameRepeat())
                    .replace("applyNo",applyNo);
            log.info("合同号{}，放款审核通过通知-金融专员发送短信{}，手机号码为{}",applyNo, msg, caseChannelInfo.getSalePhone());
            boolean b = messageService.sendSms(caseChannelInfo.getSalePhone(), msg);
            Assert.isTrue(b,"放款审核通过通知-金融专员发送短信失败");
        }catch (Exception e){
            log.error(e.getMessage());
        }
    }
}
