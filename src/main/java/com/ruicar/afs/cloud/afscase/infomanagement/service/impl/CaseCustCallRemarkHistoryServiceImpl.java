package com.ruicar.afs.cloud.afscase.infomanagement.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.vo.PreApproveVO;
import com.ruicar.afs.cloud.afscase.callcenter.entiry.SoftPhoneCall;
import com.ruicar.afs.cloud.afscase.callcenter.vo.CaseCustCallDetailVO;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.FinCostDetailsService;
import com.ruicar.afs.cloud.afscase.mq.sender.CaseToModelSender;
import com.ruicar.afs.cloud.afscase.risk.entity.ThirdData;
import com.ruicar.afs.cloud.afscase.risk.mapper.ThirdDataMapper;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.call.api.service.CallCenterService;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CallSubmitInfo;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.deepseek.config.DeepSeekProperties;
import com.ruicar.afs.cloud.deepseek.entity.ConfTemplateConfiguration;
import com.ruicar.afs.cloud.deepseek.entity.DeepseekIntelligentResults;
import com.ruicar.afs.cloud.deepseek.enums.CallStatusEnum;
import com.ruicar.afs.cloud.deepseek.service.ConfTemplateConfigurationService;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CaseTelCheckInfoCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustCallRemarkHistory;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseCustCallRemarkHistoryMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseCustInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustCallRemarkHistoryService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.deepseek.service.DeepseekIntelligentResultsService;
import com.ruicar.afs.cloud.deepseek.utils.RiskRuleConstants;
import com.ruicar.afs.cloud.eisoa.service.BydEisoaService;
import com.ruicar.afs.cloud.image.config.FileProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/27 16:55
 */
@Service
@AllArgsConstructor
@Slf4j
public class CaseCustCallRemarkHistoryServiceImpl extends ServiceImpl<CaseCustCallRemarkHistoryMapper, CaseCustCallRemarkHistory> implements CaseCustCallRemarkHistoryService {


    private CaseCustCallRemarkHistoryMapper caseCustCallRemarkHistoryMapper;
    private CaseCustInfoService caseCustInfoService;
    private CaseCustInfoMapper caseCustInfoMapper;
    private ConfTemplateConfigurationService confTemplateConfigurationService;
    private DeepseekIntelligentResultsService deepseekIntelligentResultsService;
    private StringRedisTemplate stringRedisTemplate;
    private DeepSeekProperties deepSeekProperties;
    private CallCenterService callCenterService;
    private FileProperties fileProperties;
    private BydEisoaService bydEisoaService;
    private CaseCarInfoService caseCarInfoService;
    private CaseBaseInfoService caseBaseInfoService;
    private CaseToModelSender caseToModelSender;
    private FinCostDetailsService finCostDetailsService;
    private CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private ThirdDataMapper thirdDataMapper;

    @Override
    public Page<CaseCustCallDetailVO> queryCallDetailList(Page page, CaseTelCheckInfoCondition condition) {
        return caseCustCallRemarkHistoryMapper.selectCallDetailList(page, condition);
    }

    @Async("ioAsyncTaskExecutor")
    @Override
    public void putPhoneCallAsr(SoftPhoneCall softPhoneCall){
        //录音文件路径
        File file = null;
        String str = null;
        String fileUrl =  callCenterService.getFileServerPrefix() + File.separator + softPhoneCall.getMediano() +"?file=" + softPhoneCall.getRecordFile();
        String tempPath = fileProperties.getTempDir() + softPhoneCall.getCallId() + ".wav";
        log.info("录音文件url={},下载临时文件地址={}",fileUrl,tempPath);
        try {
            //下载录音文件保存到本地
            HttpUtil.downloadFile(fileUrl,tempPath);
            file = new File(tempPath);
            if (file.length() > 0){
                //获取观云台token
                String token = null;
                if (StrUtil.isNotEmpty(stringRedisTemplate.opsForValue().get("access_token"))){
                    token = stringRedisTemplate.opsForValue().get("access_token");
                }else {
                    token = bydEisoaService.getEisoaToken();
                }
                str = bydEisoaService.getAsrAudio(token,tempPath);
                if (StrUtil.isNotBlank(str)){
                    CaseCustCallRemarkHistory history = this.getOne(Wrappers.<CaseCustCallRemarkHistory>lambdaQuery()
                            .eq(CaseCustCallRemarkHistory::getCallId,softPhoneCall.getCallId()));
                    if (ObjectUtil.isNotEmpty(history)){
                        this.update(Wrappers.<CaseCustCallRemarkHistory>lambdaUpdate()
                                .eq(CaseCustCallRemarkHistory::getCallId,softPhoneCall.getCallId())
                                .set(CaseCustCallRemarkHistory::getAutomaticRecognitionTime, DateUtil.date())
                                .set(CaseCustCallRemarkHistory::getAutomaticRecognition,str));
                        this.callDeepSeek(history,str);
                    }
                }
                file.delete();
            } else {
                log.info("录音文件下载失败,callId={}",softPhoneCall.getCallId());
            }
        }catch (Exception e){
            log.error("调用ASR接口失败");
        } finally {
            CaseCustCallRemarkHistory history = this.getOne(Wrappers.<CaseCustCallRemarkHistory>lambdaQuery()
                    .eq(CaseCustCallRemarkHistory::getCallId,softPhoneCall.getCallId()));
            if (ObjectUtil.isNotEmpty(history)){
                AfsTransEntity<CallSubmitInfo> transEntity = returnMessage(softPhoneCall,history,str);
                caseToModelSender.sendCaseToModelCall(transEntity);
                log.info("MQ推送质检系统成功");
            }
        }
    }

    private AfsTransEntity<CallSubmitInfo> returnMessage(SoftPhoneCall softPhoneCall,CaseCustCallRemarkHistory history,String dialogueDetails) {
        AfsTransEntity<CallSubmitInfo> transEntity = new AfsTransEntity();
        CallSubmitInfo callSubmitInfo = new CallSubmitInfo();
        callSubmitInfo.setApplyNo(history.getApplyNo());
        callSubmitInfo.setCallId(history.getCallId());
        callSubmitInfo.setReviewName(history.getRemarkBy());
        callSubmitInfo.setDialogueDetails(dialogueDetails);
        //判断与承租人关系
        String type = telCheckrelationship(history.getApplyNo(),history.getTelPhone());
        callSubmitInfo.setCallRecipient(type);
        callSubmitInfo.setTheCaller(history.getCustName());
        callSubmitInfo.setLanguageType(history.getAudioLanguageType());
        callSubmitInfo.setTalkTime(softPhoneCall.getRingTime());
        callSubmitInfo.setCallType(history.getCallType());
        if (ObjectUtil.isNotEmpty(softPhoneCall.getRingTime()) && ObjectUtil.isNotEmpty(softPhoneCall.getEnd())){
            long times = DateUtil.between(softPhoneCall.getRingTime(),softPhoneCall.getEnd(), DateUnit.SECOND);
            callSubmitInfo.setCallDuration(String.valueOf(times));
        }
        callSubmitInfo.setCallRecording(callCenterService.getFileServerPrefix() + File.separator + softPhoneCall.getMediano() +"?file=" + softPhoneCall.getRecordFile());
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, history.getApplyNo()));
        if (ObjectUtil.isNotEmpty(caseCarInfo)){
            callSubmitInfo.setBrandCode(caseCarInfo.getBrandCode());
        }
        FinCostDetails finCostDetails = finCostDetailsService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo,history.getApplyNo()));
        callSubmitInfo.setDownPayScale(finCostDetails.getDownPayScale());
        //查询预审批信息
        IResponse response = caseUseApplyServiceFeign.findApproveByApplyNo(history.getApplyNo());
        if (ObjectUtil.isNotNull(response.getData())){
            PreApproveVO preApproveVO = JSON.parseObject(JSON.toJSONString(response.getData()),PreApproveVO.class);
            if (StrUtil.equals("NBGC",preApproveVO.getSource())){
                callSubmitInfo.setIsNgc("1");
            }else {
                callSubmitInfo.setIsNgc("0");
            }
        }
        String bairongRisk = "1";
        ThirdData selectData = thirdDataMapper.selectOne(Wrappers.<ThirdData>query().lambda()
                .eq(ThirdData::getApproveId, history.getApplyNo())
                .orderByDesc(ThirdData::getCreateTime).last("limit 1"));
        if (selectData != null && selectData.getResponse() != null){
            JSONObject jsonObject = JSON.parseObject(selectData.getResponse());
            JSONArray codeList = Optional.ofNullable(jsonObject.getJSONObject("decisionDetailedResults")).map(res -> res.getJSONArray("ruleCustomIds")).orElse(null);
            if (codeList != null){
                for (Object o : codeList) {
                    if (RiskRuleConstants.RISK_RULES.contains(o.toString())){
                        bairongRisk = "0";
                        break;
                    }
                }
            }
        }
        callSubmitInfo.setBairongRisk(bairongRisk);
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo,history.getApplyNo()));
        callSubmitInfo.setOperateWay(caseBaseInfo.getOperateWay());
        callSubmitInfo.setProductName(caseBaseInfo.getProductName());
        callSubmitInfo.setLesseeGrade(caseBaseInfo.getLesseeGrade());
        transEntity.setTransCode(MqTransCode.AFS_CASE_MODEL_CALL);
        transEntity.setData(callSubmitInfo);
        return transEntity;
    }

    private String telCheckrelationship(String applyNo, String telPhone) {
        String role = CustRoleEnum.CONTACT.getDesc();
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getTelPhone,telPhone)
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(custInfo)) {
            if (StrUtil.equals(CustRoleEnum.MIANCUST.getCode(), custInfo.getCustRole())) {
                role = CustRoleEnum.MIANCUST.getDesc();
            } else if (StrUtil.equals(CustRoleEnum.GUARANTOR.getCode(), custInfo.getCustRole())) {
                role = "保证人";
            }
        }else {
            String custRole = caseCustInfoMapper.getCustByPhone(applyNo,telPhone);
            if (StrUtil.isNotEmpty(custRole)){
                role = "工作单位";
            }
        }
        return role;
    }

    @Override
    @Async("ioAsyncTaskExecutor")
    public void againCallDeepSeek(CaseCustCallRemarkHistory callRemarkHistory, String automaticRecognition) {
        log.info("开始重新调用deepseek");
        String diction = null;
        try {
            DeepseekIntelligentResults results = deepseekIntelligentResultsService.getOne(Wrappers.<DeepseekIntelligentResults>lambdaQuery()
                    .eq(DeepseekIntelligentResults::getCallId,callRemarkHistory.getCallId()));
            if (ObjectUtil.isNotEmpty(results)){
                deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                        .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.RUNNING.getCode())
                        .eq(DeepseekIntelligentResults::getCallId,results.getCallId()));
            }else {
                DeepseekIntelligentResults deepseekIntelligentResults = new DeepseekIntelligentResults();
                deepseekIntelligentResults.setApplyNo(callRemarkHistory.getApplyNo());
                deepseekIntelligentResults.setCallId(callRemarkHistory.getCallId());
                deepseekIntelligentResults.setAutomaticRecognition(automaticRecognition);
                deepseekIntelligentResults.setStatus(CallStatusEnum.RUNNING.getCode());
                deepseekIntelligentResultsService.save(deepseekIntelligentResults);
            }
            //判断与承租人关系
            String type = telCheckInfo(callRemarkHistory.getApplyNo(),callRemarkHistory.getTelPhone());
            String templateType = dicData(type,"templateScenarios");
            //判断通话适用模板
            ConfTemplateConfiguration configuration = confTemplateConfigurationService.getOne(Wrappers.<ConfTemplateConfiguration>lambdaQuery()
                    .eq(ConfTemplateConfiguration::getTemplateType,templateType)
                    .eq(ConfTemplateConfiguration::getStatus, WhetherEnum.YES.getCode()));
            if (ObjectUtil.isNotEmpty(configuration)){
                //组合话术
                List<String> list = new ArrayList<>();
                String[] strings = configuration.getTemplateElement().split(",");
                for (String string : strings){
                    list.add(dicData(string,"templateElements"));
                }
                diction = combinatorialScript(automaticRecognition,list);
                String data = deepseekIntelligentResultsService.callDeepSeek(diction,deepSeekProperties.getApiKey());
                if (StrUtil.isNotEmpty(data)){
                    String pureJson = extractPureJson(data);
                    deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                            .set(DeepseekIntelligentResults::getConversationInformation,diction)
                            .set(StrUtil.isNotEmpty(pureJson),DeepseekIntelligentResults::getIntelligentSummary,pureJson)
                            .set(DeepseekIntelligentResults::getStatus,StrUtil.isNotEmpty(data) ? CallStatusEnum.SUCCESS.getCode() : CallStatusEnum.FAIL.getCode())
                            .eq(DeepseekIntelligentResults::getCallId,callRemarkHistory.getCallId()));
                }else {
                    deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                            .set(DeepseekIntelligentResults::getConversationInformation,diction)
                            .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.FAIL.getCode())
                            .eq(DeepseekIntelligentResults::getCallId,callRemarkHistory.getCallId()));
                }
            }else {
                deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                        .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.FAIL.getCode())
                        .eq(DeepseekIntelligentResults::getCallId,callRemarkHistory.getCallId()));
                log.info("没有取到{}对应场景模板",templateType);
            }
        }catch (Exception e){
            deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                    .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.FAIL.getCode())
                    .set(DeepseekIntelligentResults::getConversationInformation,diction)
                    .eq(DeepseekIntelligentResults::getCallId,callRemarkHistory.getCallId()));
            log.error("调用deepseek失败：{}",e);
        }

    }

    @Override
    public void callDeepSeek(CaseCustCallRemarkHistory history, String str) {
        log.info("开始解析录音对话");
        String diction = null;
        try {
            DeepseekIntelligentResults results = deepseekIntelligentResultsService.getOne(Wrappers.<DeepseekIntelligentResults>lambdaQuery()
                    .eq(DeepseekIntelligentResults::getCallId,history.getCallId()));
            if (ObjectUtil.isNotEmpty(results)){
                deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                        .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.RUNNING.getCode())
                        .eq(DeepseekIntelligentResults::getCallId,results.getCallId()));
            }else {
                DeepseekIntelligentResults deepseekIntelligentResults = new DeepseekIntelligentResults();
                deepseekIntelligentResults.setApplyNo(history.getApplyNo());
                deepseekIntelligentResults.setCallId(history.getCallId());
                deepseekIntelligentResults.setAutomaticRecognition(str);
                deepseekIntelligentResults.setStatus(CallStatusEnum.RUNNING.getCode());
                deepseekIntelligentResultsService.save(deepseekIntelligentResults);
            }
            //判断与承租人关系
            String type = telCheckInfo(history.getApplyNo(),history.getTelPhone());
            String templateType = dicData(type,"templateScenarios");
            DeepseekIntelligentResults results1 = deepseekIntelligentResultsService.getOne(Wrappers.<DeepseekIntelligentResults>lambdaQuery()
                    .eq(DeepseekIntelligentResults::getCallId,history.getCallId()));
            if (!StrUtil.equals(results1.getStatus(),CallStatusEnum.SUCCESS.getCode())) {
                //判断通话适用模板
                ConfTemplateConfiguration configuration = confTemplateConfigurationService.getOne(Wrappers.<ConfTemplateConfiguration>lambdaQuery()
                        .eq(ConfTemplateConfiguration::getTemplateType, templateType)
                        .eq(ConfTemplateConfiguration::getStatus, WhetherEnum.YES.getCode()));
                if (ObjectUtil.isNotEmpty(configuration)) {
                    //组合话术
                    List<String> list = new ArrayList<>();
                    String[] strings = configuration.getTemplateElement().split(",");
                    for (String string : strings) {
                        list.add(dicData(string, "templateElements"));
                    }
                    diction = combinatorialScript(str, list);
                    //获取认证token
                    String data = deepseekIntelligentResultsService.callDeepSeek(diction,deepSeekProperties.getApiKey());
                    if (StrUtil.isNotEmpty(data)) {
                        String pureJson = extractPureJson(data);
                        deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                                .set(DeepseekIntelligentResults::getConversationInformation, diction)
                                .set(StrUtil.isNotEmpty(pureJson), DeepseekIntelligentResults::getIntelligentSummary, pureJson)
                                .set(DeepseekIntelligentResults::getStatus, StrUtil.isNotEmpty(data) ? CallStatusEnum.SUCCESS.getCode() : CallStatusEnum.FAIL.getCode())
                                .eq(DeepseekIntelligentResults::getCallId, history.getCallId()));
                    } else {
                        deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                                .set(DeepseekIntelligentResults::getConversationInformation, diction)
                                .set(DeepseekIntelligentResults::getStatus, CallStatusEnum.FAIL.getCode())
                                .eq(DeepseekIntelligentResults::getCallId, history.getCallId()));
                    }
                } else {
                    deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                            .set(DeepseekIntelligentResults::getStatus, CallStatusEnum.FAIL.getCode())
                            .eq(DeepseekIntelligentResults::getCallId, history.getCallId()));
                    log.info("没有取到{}对应场景模板", templateType);
                }
            }else {
                log.info("通话已经解析成功，不用再次解析");
            }
        }catch (Exception e){
            deepseekIntelligentResultsService.update(Wrappers.<DeepseekIntelligentResults>lambdaUpdate()
                    .set(DeepseekIntelligentResults::getStatus,CallStatusEnum.FAIL.getCode())
                    .set(DeepseekIntelligentResults::getConversationInformation,diction)
                    .eq(DeepseekIntelligentResults::getCallId,history.getCallId()));
            log.error("调用deepseek失败：{}",e);
        }
    }

    private static String extractPureJson(String str) {
        JSONObject jsonObject = JSON.parseObject(str);
        String content = jsonObject.getString("content");
        // 找到第一个{和最后一个}的位置
        int start = content.indexOf("{");
        int end = content.lastIndexOf("}");

        if (start >= 0 && end > start) {
            return content.substring(start, end + 1);
        }
        return content; // 如果没有找到，返回原内容
    }

    private String combinatorialScript(String str, List<String> list) {
        String content = getContent(list);
        String dialogue = getDialogue(str);
        JSONArray messages = new JSONArray();

        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content",content);

        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", dialogue);

        messages.add(systemMessage);
        messages.add(userMessage);

        return messages.toString();
    }

    private String getDialogue(String str) {
        String[] lines = str.split("\n");
        StringBuilder result = new StringBuilder();
        for (String line : lines){
            int colonIndex = line.indexOf(":");
            if (colonIndex > 0) {
                String content = line.substring(colonIndex + 1).trim();
                result.append(content).append(";");
            }
        }
        if (result.length() > 0) {
            result.setLength(result.length() - 1);
        }
        return result.toString();
    }

    private String getContent(List<String> list){
        String preface = deepSeekProperties.getPreface();
        StringBuilder sb = new StringBuilder();
        sb.append(preface);
        for (String s : list){
            sb.append(s);
        }
        return sb.toString();
    }

    private String telCheckInfo(String applyNo, String telPhone) {
        String role = CustRoleEnum.CONTACT.getDesc();
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, applyNo)
                .eq(CaseCustInfo::getTelPhone,telPhone)
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(custInfo)){
            if (StrUtil.equals(CustRoleEnum.MIANCUST.getCode(),custInfo.getCustRole())){
                role = CustRoleEnum.MIANCUST.getDesc();
            } else if (StrUtil.equals(CustRoleEnum.GUARANTOR.getCode(),custInfo.getCustRole())) {
                role = "保证人";
            }
        }else {
            String custRole = caseCustInfoMapper.getCustByPhone(applyNo,telPhone);
            if (StrUtil.isNotEmpty(custRole)){
                role = StrUtil.equals(CustRoleEnum.MIANCUST.getCode(),custRole) ? "承租人单位" : "保证人单位";
            }else {
                String cust = caseCustInfoMapper.getGuarantorSpouseByPhone(applyNo,telPhone);
                if (StrUtil.isNotEmpty(cust)){
                    return "保证人配偶";
                }
            }
        }
        return role;
    }

    /**
     * 字典翻译
     * @param param
     * @param dicType
     * @return
     */
    public static String dicData(String param,String dicType){
        String result = "";
        Map<String, List<DicDataDto>> listMap = DicHelper.getDicMaps(dicType);
        List<DicDataDto> list = listMap.get(dicType);
        for(DicDataDto dicDataDto : list){
            if(dicDataDto.getTitle().equals(param)){
                result = dicDataDto.getValue();
            }
        }
        return result;

    }
}
