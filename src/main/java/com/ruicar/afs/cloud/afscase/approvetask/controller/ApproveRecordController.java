package com.ruicar.afs.cloud.afscase.approvetask.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.admin.api.feign.AfsUserFeign;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.vo.PreApproveVO;
import com.ruicar.afs.cloud.afscase.loanapprove.task.enums.ContractSuspendEnum;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskBindingParams;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowStatusEnum;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskBindingParamsService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Description 审批记录
 * <AUTHOR>
 * @Date 2020/06/01 14:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/approveRecord")
public class ApproveRecordController {

    CaseApproveRecordService caseApproveRecordService;
    private final WorkflowTaskInfoService workflowTaskInfoService;
    private final FlowConfigProperties flowConfigProperties;
    private final AfsUserFeign afsUserFeign;
    private final CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private UserDetailsInfoFeign userDetailsInfoFeign;
    private WorkflowTaskBindingParamsService workflowTaskBindingParamsService;
    /**
     * @Description 查询置顶申请编号所有操作记录
     * <AUTHOR>
     * @Date 2020/6/1 14:30
     */
    @GetMapping("/listAllRecord")
    public IResponse listAllRecord(@RequestParam("applyNo") String applyNo,@RequestParam(value = "userScene",required = false) String userScene) {
        List<String> useSceneValList = new ArrayList<>();

        if(StringUtils.isNotBlank(userScene) && UseSceneEnum.LOAN_DATA_POST_NEW.getValue().equals(userScene)){
            useSceneValList.add(UseSceneEnum.LOAN_DATA_POST_NEW.getValue());
        }else if(StringUtils.isNotBlank(userScene) && UseSceneEnum.LOAN_CANNEL.getValue().equals(userScene)){
            useSceneValList.add(UseSceneEnum.LOAN_CANNEL.getValue());
        }else{
            useSceneValList.add(UseSceneEnum.APPROVE.getValue());
            useSceneValList.add(UseSceneEnum.CHANGE_ASSETS.getValue());
            useSceneValList.add(UseSceneEnum.RECONSIDER.getValue());
            useSceneValList.add(UseSceneEnum.GENERAL_LOAN.getValue());
            useSceneValList.add(UseSceneEnum.APPROVE_INSPECTION.getValue());
            useSceneValList.add(UseSceneEnum.SPECIAL_BUSINESS.getValue());
            useSceneValList.add(UseSceneEnum.SPECIAL_APPROVE.getValue());
            useSceneValList.add(UseSceneEnum.SALVAGED.getValue());
            useSceneValList.add(UseSceneEnum.LOAN_DATA_POST_NEW.getValue());
            useSceneValList.add(UseSceneEnum.LOAN_CANNEL.getValue());
        }

        List<CaseApproveRecord> result = caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                        .eq(CaseApproveRecord::getApplyNo, applyNo)
                        .in(CaseApproveRecord::getUseScene, useSceneValList)
                        .ne(CaseApproveRecord::getApproveSuggest,"preApprove")
                        .ne(CaseApproveRecord::getApproveSuggest,"final6approved")
                        .orderByAsc(CaseApproveRecord::getId));

        log.info("查询日志1：入参applyNo={}，入参useSceneValList={}，查询到的日志信息={}",applyNo,JSON.toJSONString(useSceneValList),JSON.toJSONString(result));

        //查询预审批信息
        IResponse response = caseUseApplyServiceFeign.findApproveByApplyNo(applyNo);
        if (ObjectUtil.isNotNull(response.getData())){
            PreApproveVO preApproveVO = JSON.parseObject(JSON.toJSONString(response.getData()),PreApproveVO.class);
            CaseApproveRecord record = new CaseApproveRecord();
            record.setApplyNo(applyNo);
            record.setUseScene(UseSceneEnum.APPROVE.getValue());
            record.setApproveSuggest("initiate");
            record.setDisposeNodeName("预审批");
            record.setDisposeStaff("系统");
            record.setApproveSuggestName("通过");
            record.setApproveEndTime(preApproveVO.getApproveDate());
            record.setApproveRemark(preApproveVO.getApplyNo());
            result.add(0,record);
        }

        // 翻译原因字典，拼接至审批建议
        for (CaseApproveRecord caseApproveRecord : result) {
            if (ApproveTypeEnum.REMIND.getValue().equals(caseApproveRecord.getApproveType()) || ApproveTypeEnum.PROCESS.getValue().equals(caseApproveRecord.getApproveType())) {
                if(UseSceneEnum.RECONSIDER.getValue().equals(caseApproveRecord.getUseScene())){
                    caseApproveRecord.setApproveRemark("说明："+caseApproveRecord.getApproveReason()+","+"意见："+caseApproveRecord.getApproveRemark());
                }else{
                    // 翻译原因字典，拼接至审批建议
                    transferReason(caseApproveRecord);
                }
            }
        }
        return IResponse.success(result);
    }


    /**
     * @Description 翻译原因字典，拼接至审批建议
     * <AUTHOR>
     * @Date 2020/6/17 15:05
     */
    private void transferReason(CaseApproveRecord caseApproveRecord) {
        String approveReason = caseApproveRecord.getApproveReason();
        if (StrUtil.isNotBlank(approveReason)) {
            StringBuffer buffer = new StringBuffer();
            String[] reasonArr = approveReason.replaceAll("\\[", "")
                    .replaceAll("\\]", "")
                    .replaceAll("\"", "")
                    .split(",");
            DicHelper.getDicMaps(caseApproveRecord.getApproveSuggest());
            caseApproveRecord.setApproveRemark(buffer + caseApproveRecord.getApproveRemark());
        }
    }

    /**
    * @Description 获取二次欺诈流程的操作日志
    * <AUTHOR>
    * @Date 2020/12/7 16:00
    */
    @GetMapping("/listFraudApproveRecords")
    public IResponse listFraudApproveRecords(@RequestParam String stageId){
        return IResponse.success(caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                    .eq(CaseApproveRecord::getStageId,stageId)
                    .orderByAsc(CaseApproveRecord::getCreateTime)
        ));
    }
    @GetMapping("/listApproveRecords")
    public IResponse listApproveRecords(@RequestParam String applyNo) {
        List<CaseApproveRecord> result = new ArrayList<>();
        CaseApproveRecord caseApproveRecord = caseApproveRecordService.getOne(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getApplyNo, applyNo)
                .eq(CaseApproveRecord::getUseScene, UseSceneEnum.APPROVE.getValue())
                .eq(CaseApproveRecord::getDisposeNodeName,"开始节点")
                .orderByAsc(CaseApproveRecord::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotNull(caseApproveRecord)){
            result.add(caseApproveRecord);
        }

        CaseApproveRecord record = caseApproveRecordService.getOne(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getApplyNo,applyNo)
                .eq(CaseApproveRecord::getUseScene,UseSceneEnum.APPROVE.getValue())
                .eq(CaseApproveRecord::getDisposeNodeName,"初审")
                .orderByAsc(CaseApproveRecord::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotNull(record)){
            record.setDisposeNodeName("订单审核中");
            record.setApproveEndTime(record.getClickTime());
            record.setApproveRemark(null);
            result.add(record);
        }

        List<CaseApproveRecord> list = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getApplyNo,applyNo)
                .eq(CaseApproveRecord::getUseScene,UseSceneEnum.APPROVE.getValue())
                .in(CaseApproveRecord::getApproveSuggest, ContractSuspendEnum.YES.getValue(),ContractSuspendEnum.NO.getValue()));
        if (CollectionUtil.isNotEmpty(list)){
            List<WorkflowTaskBindingParams> paramsList = workflowTaskBindingParamsService.list(Wrappers.<WorkflowTaskBindingParams>lambdaQuery()
                    .eq(WorkflowTaskBindingParams::getIsShow,YesOrNoEnum.yes.key()));
            if (CollectionUtil.isNotEmpty(paramsList)){
                Map map = new HashMap();
                for (WorkflowTaskBindingParams params : paramsList){
                    map.put(params.getParamCode(),params.getParamDesc());
                }
                for (CaseApproveRecord approveRecord : list){
                    StringBuilder sb = new StringBuilder();
                    if (StrUtil.isNotEmpty(approveRecord.getApproveReason())){
                        String[] str = approveRecord.getApproveReason().split(",");
                        for (String s : str){
                            if (map.containsKey(s)){
                                sb.append(map.get(s));
                                sb.append(",");
                            }
                        }
                    }
                    if (sb.length() > 0){
                        String str = sb.toString().substring(0,sb.toString().length() -1);
                        approveRecord.setApproveRemark(str);
                        result.add(approveRecord);
                    }
                }
            }
        }
        //设置视频面审相关审批节点
        List<CaseApproveRecord> spList = caseApproveRecordService.list(Wrappers.<CaseApproveRecord>lambdaQuery()
                .eq(CaseApproveRecord::getApplyNo,applyNo)
                .eq(CaseApproveRecord::getUseScene,UseSceneEnum.APPROVE.getValue())
                .eq(CaseApproveRecord::getApproveSuggest,FlowTaskOperationEnum.FACEREVIEW.getCode()));
        if (!spList.isEmpty()) {
            for (CaseApproveRecord approveRecord : spList) {
                approveRecord.setDisposeNodeName(approveRecord.getApproveSuggestName());
                result.add(approveRecord);
            }
        }
        if(ObjectUtil.isNotEmpty(result) && result.size() > 0){
            return IResponse.success(result);
        }

        return IResponse.fail("");

    }

    /**
     *@Description 获取放款的操作日志
     */
    @GetMapping("/listGeneralloanRecords")
    public IResponse listGeneralloanRecords(@RequestParam String applyNo) {
        List<String> useSceneValList = new ArrayList<>();
        useSceneValList.add(UseSceneEnum.GENERAL_LOAN.getValue());
        List<CaseApproveRecord> result = caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                        .eq(CaseApproveRecord::getApplyNo, applyNo)
                        .in(CaseApproveRecord::getUseScene, useSceneValList)
                        .orderByAsc(CaseApproveRecord::getId));
        result=  result.stream ().filter (caseApproveRecord -> StringUtil.isNotBlank ( caseApproveRecord.getDisposeNodeName ())).collect( Collectors.toList ());
        List<WorkflowTaskInfo> workTaskPoolList = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getLoanTemplateId())
                .orderByDesc(WorkflowTaskInfo::getCreateTime));
        if(CollectionUtil.isNotEmpty(workTaskPoolList)){
            if(workTaskPoolList.size()>0) {
                WorkflowTaskInfo workTaskPool=workTaskPoolList.get(0);
                if(workTaskPool!=null&&workTaskPool.getStatus().equals(FlowStatusEnum.ACTIVE.getCode())) {
                    CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                    if (StringUtils.isNotBlank(workTaskPool.getAssign())) {
                        String realName = afsUserFeign.getSimpleInfoByUserLoginNames(Collections.singletonList(workTaskPool.getAssign())).getData().get(0).getUserRealName();
                        caseApproveRecord.setDisposeStaff(StringUtil.isNotBlank(realName) ? realName : workTaskPool.getAssign());
                        caseApproveRecord.setApproveStartTime(workTaskPool.getCreateTime());
                        caseApproveRecord.setApproveEndTime(workTaskPool.getCreateTime());
                    }
                    caseApproveRecord.setDisposeNodeName("待审核人员");
                    result.add(caseApproveRecord);
                }
            }
        }
        return IResponse.success(result);
    }

    /**
     *@Description 获取放款的操作日志
     */
    @GetMapping("/listSpecialRecords")
    public IResponse listSpecialRecords(@RequestParam String applyNo) {
        List<String> useSceneValList = new ArrayList<>();
        useSceneValList.add(UseSceneEnum.SPECIAL_BUSINESS.getValue());
        List<CaseApproveRecord> result = caseApproveRecordService.list(
                Wrappers.<CaseApproveRecord>lambdaQuery()
                        .eq(CaseApproveRecord::getApplyNo, applyNo)
                        .in(CaseApproveRecord::getUseScene, useSceneValList)
                        .orderByAsc(CaseApproveRecord::getId));
        result=  result.stream ().filter (caseApproveRecord -> StringUtil.isNotBlank ( caseApproveRecord.getDisposeNodeName ())).collect( Collectors.toList ());
        List<WorkflowTaskInfo> workTaskPoolList = workflowTaskInfoService.list(Wrappers.<WorkflowTaskInfo>query().lambda()
                .eq(WorkflowTaskInfo::getBusinessNo,applyNo)
                .eq(WorkflowTaskInfo::getFlowTemplateId,flowConfigProperties.getSpecialLoanTemplateId())
                .orderByDesc(WorkflowTaskInfo::getCreateTime));
        if(CollectionUtil.isNotEmpty(workTaskPoolList)){
            if(workTaskPoolList.size()>0) {
                WorkflowTaskInfo workTaskPool=workTaskPoolList.get(0);
                if(workTaskPool!=null&&workTaskPool.getStatus().equals(FlowStatusEnum.ACTIVE.getCode())) {
                    CaseApproveRecord caseApproveRecord = new CaseApproveRecord();
                    if (StringUtils.isNotBlank(workTaskPool.getAssign())) {
                        String realName = afsUserFeign.getSimpleInfoByUserLoginNames(Collections.singletonList(workTaskPool.getAssign())).getData().get(0).getUserRealName();
                        caseApproveRecord.setDisposeStaff(StringUtil.isNotBlank(realName) ? realName : workTaskPool.getAssign());
                        caseApproveRecord.setApproveStartTime(workTaskPool.getCreateTime());
                        caseApproveRecord.setApproveEndTime(workTaskPool.getCreateTime());
                    }
                    caseApproveRecord.setDisposeNodeName("待审核人员");
                    result.add(caseApproveRecord);
                }
            }
        }

        return IResponse.success(result);
    }
    public String getUserByLoginName(String loginName) {
        IResponse response = userDetailsInfoFeign.info(loginName);
        if (response.getData()!=null) {
            Object data = response.getData();
            JSONObject jsonObject = (JSONObject) JSON.toJSON(data);
            Object sysUser = jsonObject.get("sysUser");
            JSONObject object = (JSONObject) JSON.toJSON(sysUser);
            String userRealName = object.getString("userRealName");
            return userRealName;
        }
        throw new RuntimeException("查询用户信息异常");
    }
}
