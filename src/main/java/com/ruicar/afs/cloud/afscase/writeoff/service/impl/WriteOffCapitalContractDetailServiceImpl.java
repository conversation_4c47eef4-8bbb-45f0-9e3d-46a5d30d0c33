package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.dto.ContractDetailManageSearchDTO;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalContractDetail;
import com.ruicar.afs.cloud.afscase.writeoff.feign.WriteOffFeign;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffCapitalContractDetailMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalContractDetailService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.BasicContractInfoVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.CapitalContractExcelVo;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.contract.enums.ContractStatusEnum;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Service
public class WriteOffCapitalContractDetailServiceImpl extends ServiceImpl<WriteOffCapitalContractDetailMapper, WriteOffCapitalContractDetail> implements WriteOffCapitalContractDetailService {

    private final WriteOffFeign writeOffFeign;

    @Override
    public void exportContractDetail(WriteOffCapitalContractDetail condition, HttpServletResponse response) {
        Assert.isTrue(condition.getContractStatus() != null, "查询参数异常");
        String excelName = null;
        switch (condition.getContractStatus()) {
            case contractCancel: excelName = "租金贷合同取消合同";break;
            case advanceSettle: excelName = "租金贷提前结清合同";break;
            case contractEffective: excelName = "租金贷已回款合同";break;
            default: throw new RuntimeException("参数异常");
        }
        boolean allFlag = ContractStatusEnum.contractEffective == condition.getContractStatus();
        List<WriteOffCapitalContractDetail> list = this.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffCapitalContractDetail::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getApplyNo()), WriteOffCapitalContractDetail::getApplyNo, condition.getApplyNo())
                .eq(StrUtil.isNotBlank(condition.getContractNo()), WriteOffCapitalContractDetail::getContractNo, condition.getContractNo())
                .eq(StrUtil.isNotBlank(condition.getBelongingCapital()), WriteOffCapitalContractDetail::getBelongingCapital, condition.getBelongingCapital())
                .eq(StrUtil.isNotBlank(condition.getWriteOffFlag()), WriteOffCapitalContractDetail::getWriteOffFlag, condition.getWriteOffFlag())
                .eq(StrUtil.isNotBlank(condition.getReceiveFinishFlag()), WriteOffCapitalContractDetail::getReceiveFinishFlag, condition.getReceiveFinishFlag())
                .like(StrUtil.isNotBlank(condition.getProductName()), WriteOffCapitalContractDetail::getProductName, condition.getProductName())
                .eq(!allFlag, WriteOffCapitalContractDetail::getContractStatus, condition.getContractStatus())
                .orderByDesc(WriteOffCapitalContractDetail::getCreateTime));
        List<CapitalContractExcelVo> exportVoList = list.stream().map(detail -> {
            CapitalContractExcelVo excelVo = new CapitalContractExcelVo();
            BeanUtil.copyProperties(detail, excelVo);
            excelVo.setContractStatus(detail.getContractStatus().getDesc());
            excelVo.setBelongingCapital(BelongingCapitalEnum.getDescByCode(detail.getBelongingCapital()));
            excelVo.setWriteOffFlag("1".equals(detail.getWriteOffFlag()) ? "已生成" : "未生成");
            excelVo.setReceiveFinishFlag("1".equals(detail.getReceiveFinishFlag()) ? "已完成" : "未完成");
            return excelVo;
        }).toList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode(excelName + "明细导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), CapitalContractExcelVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, excelName).build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }

    @Override
    public IResponse updateContracts() {
        List<String> contractNoList = this.list(Wrappers.<WriteOffCapitalContractDetail>lambdaQuery()
                        .select(WriteOffCapitalContractDetail::getContractNo))
                .stream().map(WriteOffCapitalContractDetail::getContractNo).toList();
        ContractDetailManageSearchDTO searchDTO = new ContractDetailManageSearchDTO();
        searchDTO.setContractNoList(contractNoList);
        IResponse<List<BasicContractInfoVo>> response = writeOffFeign.queryZjdReturn(searchDTO);
        Assert.isTrue("0000".equals(response.getCode()), "租金贷已回款合同查询异常");
        List<WriteOffCapitalContractDetail> detailList = new ArrayList<>();
        for (BasicContractInfoVo infoVo : response.getData()) {
            WriteOffCapitalContractDetail capitalContractDetail = new WriteOffCapitalContractDetail();
            capitalContractDetail.setApplyNo(infoVo.getApplyNo());
            capitalContractDetail.setContractNo(infoVo.getContractNo());
            capitalContractDetail.setContractStatus(infoVo.getContractStatus());
            capitalContractDetail.setBelongingCapital(infoVo.getBelongingCapital());
            capitalContractDetail.setCapitalReturnTime(infoVo.getCapitalReturnTime());
            capitalContractDetail.setChannelFullName(infoVo.getChannelFullName());
            capitalContractDetail.setProductName(infoVo.getProductName());
            capitalContractDetail.setWriteOffFlag("0");
            capitalContractDetail.setReceiveFinishFlag("0");
            detailList.add(capitalContractDetail);
        }
        if (detailList.size() > 0) {
            this.saveBatch(detailList);
        }
        return IResponse.success("合同更新成功");
    }
}
