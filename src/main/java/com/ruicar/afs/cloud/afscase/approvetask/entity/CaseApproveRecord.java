package com.ruicar.afs.cloud.afscase.approvetask.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CurrentPositionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-13 17:20
 */
@Data
@TableName("case_approve_record")
public class CaseApproveRecord extends BaseEntity<CaseApproveRecord> {

    @ApiModelProperty(value = "唯一标识")
    @JsonSerialize(using= ToStringSerializer.class)
    @JSONField(serializeUsing = com.alibaba.fastjson.serializer.ToStringSerializer.class)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 合同号码
     */
    private String contractNo;
    /**
     * 流程实例ID
     */
    private String stageId;
    /**
     * 使用场景;信审/质检
     */
    private String useScene;
    /**
     * 审批建议;审批建议、复议建议
     */
    private String approveSuggest;
    /**
     * 审批建议翻译值
     */
    private String approveSuggestName;
    /**
     * 审批日志类型;审批类型：流程、留言、锁定 等
     */
    private String approveType;
    /**
     * 审批原因;审批原因、复议要求
     */
    private String approveReason;
    /**
     * 审批备注;审批备注、复议意见
     */
    private String approveRemark;
    /**
     * 审批备注;审批留言/家访信息
     */
    private String approveMessage;
    /**
     * 处理节点
     */
    private String disposeNode;
    /**
     * 处理节点名称
     */
    private String disposeNodeName;
    /**
     * 处理人员
     */
    private String disposeStaff;
    /**
     * 审批到岗时间;领取任务时间
     */
    private Date approveStartTime;
    /**
     * 审批结束时间
     */
    private Date approveEndTime;
    /**
     * add by yk.Li 申请件当前所处阶段
     */
    private CurrentPositionEnum currentPosition;

    /**
     * 流程节点
     */
    private String flowNode;
    /**
     * 关联留言表
     */
    private String mindId;

    /**任务ID*/
    private String taskId;
    /**
     * 案件业务状态(内)
     */
    @TableField(exist = false)
    private String businessStateIn;
    /**
     * 经我处理追加留言标识
     */
    @TableField(exist = false)
    private String dealWithFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 选择审批组
     */
    @TableField(exist = false)
    private String reviewApproveGroup;
    /**
     * 点击时间
     */
    private Date clickTime;

    @TableField(exist = false)
    private String custName;
}
