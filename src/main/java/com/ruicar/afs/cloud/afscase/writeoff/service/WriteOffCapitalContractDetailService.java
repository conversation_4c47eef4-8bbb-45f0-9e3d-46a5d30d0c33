package com.ruicar.afs.cloud.afscase.writeoff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalContractDetail;
import com.ruicar.afs.cloud.common.core.util.IResponse;

import javax.servlet.http.HttpServletResponse;

public interface WriteOffCapitalContractDetailService extends IService<WriteOffCapitalContractDetail> {

    /**
     * 导出资方合同明细数据
     * @param condition
     * @param response
     */
    void exportContractDetail(WriteOffCapitalContractDetail condition, HttpServletResponse response);

    /**
     * 资方合同明细更新
     * @return
     */
    IResponse updateContracts();
}
