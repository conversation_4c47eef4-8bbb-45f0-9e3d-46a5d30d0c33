package com.ruicar.afs.cloud.afscase.loanapprove.condition;

import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.workflow.enums.FlowTaskOperationEnum;
import java.util.List;
import lombok.Data;

/**
 * @Description 审批提交vo
 * <AUTHOR>
 * @Date 2020/06/15 14:57
 */
@Data
public class LoanApproveSubmitVO {
    private CaseApproveRecord approveRecord;

    /**
     * 工作流程操作命令
     *
     * @see FlowTaskOperationEnum
     */
    private String operationType;

    /**
     * 审批原因code列表
     */
    private List<String> approveSuggestList;

    /**
     * 审批原因中文列表
     */
    private List<String> approveSuggestNameList;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 通过类型
     */
    private String passType;

    /**
     * 通过说明
     */
    private String passReason;

    /**
     * 操作人员
     */
    private String useName;

    /**
     * 抵押要求
     */
    private String mortgageClaim;
}
