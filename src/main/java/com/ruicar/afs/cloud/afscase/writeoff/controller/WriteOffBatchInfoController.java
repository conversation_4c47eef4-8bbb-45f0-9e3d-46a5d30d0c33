package com.ruicar.afs.cloud.afscase.writeoff.controller;

import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBatchInfo;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBatchInfoService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


/**
 * @author: zhangjin
 * @description 服务费批次信息
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/writeOffBatch")
public class WriteOffBatchInfoController {
    private final WriteOffBatchInfoService writeOffBatchInfoService;

    @PostMapping("/queryByCondition")
    @ApiOperation(value = "条件查询")
    public IResponse queryByCondition(@RequestBody QueryCondition<WriteOffBatchInfo> queryCondition) {
        return writeOffBatchInfoService.queryByCondition(queryCondition);
    }

    @PostMapping("/exportData")
    @ApiOperation("数据导出")
    public void exportData(@RequestBody WriteOffBatchInfo queryCondition, HttpServletResponse httpServletResponse){
        writeOffBatchInfoService.exportData(queryCondition, httpServletResponse);
    }
}
