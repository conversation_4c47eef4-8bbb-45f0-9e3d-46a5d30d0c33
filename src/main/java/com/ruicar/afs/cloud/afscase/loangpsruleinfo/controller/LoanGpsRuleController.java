package com.ruicar.afs.cloud.afscase.loangpsruleinfo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.condition.LoanGpsRuleCondition;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.entity.LoanGpsRuleInfo;
import com.ruicar.afs.cloud.afscase.loangpsruleinfo.service.LoanGpsRuleInfoService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service.ApproveLoanInfoService;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description GPS安装规则控制层
 * @date 2020/5/18 17:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/loanGpsRule")
@Api("GPS安装规则配置")
public class LoanGpsRuleController {

    private final LoanGpsRuleInfoService service;
    private final ApproveLoanInfoService approveLoanInfoService;

    /**
     * 获取GPS安装规则配置数据
     *
     * @param queryCondition
     * @return
     */
    @PostMapping(value = "/getGpsRuleList")
    @ApiOperation(value = "多条件分页获取GPS安装规则配置数据")
    public IResponse<IPage<LoanGpsRuleInfo>> getGpsRuleList(@RequestBody QueryCondition<LoanGpsRuleCondition> queryCondition) {
        LoanGpsRuleCondition condition = queryCondition.getCondition();
        return IResponse.success(service.page(new Page(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<LoanGpsRuleInfo>query().lambda()
                .eq(StringUtils.isNotEmpty(condition.getIsEnable()), LoanGpsRuleInfo::getIsEnable, condition.getIsEnable())
                .like(StringUtils.isNotEmpty(condition.getGpsRuleName()), LoanGpsRuleInfo::getGpsRuleName, condition.getGpsRuleName())
                .like(StringUtils.isNotEmpty(condition.getGpsRuleDesc()), LoanGpsRuleInfo::getGpsRuleDesc, condition.getGpsRuleDesc())
                .eq(null != condition.getEffectTime(), LoanGpsRuleInfo::getEffectTime, condition.getEffectTime())
                .eq(null != condition.getFailureTime(), LoanGpsRuleInfo::getFailureTime, condition.getFailureTime())));
    }

    /**
     * 新增放款GPS安装规则配置数据
     *
     * @param loanGpsRule
     * @return
     */
    @PostMapping(value = "/addRule")
    @ApiOperation(value = "新增放款暂复核则成功")
    public IResponse<Boolean> addRule(@RequestBody LoanGpsRuleInfo loanGpsRule) {
        service.save(loanGpsRule);
        return new IResponse<Boolean>().setMsg("新增GPS安装规则成功！");
    }

    /**
     * 更新规则表业务id
     *
     * @param loanGpsRule
     * @return
     */
    @PostMapping(value = "/updateRuleId")
    @ApiOperation(value = "更新规则表业务id")
    public IResponse<Boolean> updateRuleId(@RequestBody LoanGpsRuleInfo loanGpsRule) {
        //重置同步状态
        loanGpsRule.setIsSynchronous(WhetherEnum.NO.getCode());
        service.updateById(loanGpsRule);
        return new IResponse<Boolean>().setMsg("保存成功，规则已生效！");
    }


    /**
     * 编辑放款GPS安装规则配置数据
     *
     * @param loanGpsRule
     * @return
     */
    @PostMapping(value = "/editRule")
    @ApiOperation(value = "修改放款GPS安装规则成功")
    public IResponse<Boolean> editRule(@RequestBody LoanGpsRuleInfo loanGpsRule) {
        service.updateById(loanGpsRule);
        return new IResponse<Boolean>().setMsg("修改GPS安装规则成功！");
    }

    /**
     * 批量删除GPS安装规则成功
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/delByIds/{ids}")
    @ApiOperation(value = "批量删除GPS安装规则成功")
    public IResponse<Boolean> delByIds(@PathVariable Long[] ids) {
        //使规则信息表数据失效
        service.deActiveRuleByRuleNo(ids);
        //删除GPS安装规则数据
        service.removeByIds(Arrays.asList(ids));
        return new IResponse<Boolean>().setMsg("删除GPS安装规则成功！");
    }

    /**
     * 启用GPS安装规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/openRuleById/{id}")
    @ApiOperation(value = "启用GPS安装规则数据")
    public IResponse<Boolean> openRuleById(@PathVariable Long id) {
        service.activeRule(id);
        return new IResponse<Boolean>().setMsg("启用GPS安装规则成功！");
    }

    /**
     * 停用GPS安装规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/closeRuleById/{id}")
    @ApiOperation(value = "停用GPS安装规则数据")
    public IResponse<Boolean> closeRuleById(@PathVariable Long id) {
        service.deActiveRule(id);
        return new IResponse<Boolean>().setMsg("停用GPS安装规则成功！");
    }

    /**
     * 同步GPS安装规则数据
     *
     * @param id
     * @return
     */
    @PostMapping(value = "/synchronousById/{id}")
    @ApiOperation(value = "停用GPS安装规则数据")
    public IResponse<Boolean> synchronousById(@PathVariable Long id) {
        //更新GPS同步信息
        service.updateGpsRulesInfo(id);
        LoanGpsRuleInfo loanGpsRuleInfo=service.getById(id);
        //推送GPS安装信息到进件系统
        service.syncGpsRuleData(loanGpsRuleInfo);
        return new IResponse<Boolean>().setMsg("GPS安装信息推送成功！");
    }
}
