package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.vo.ChannelReceiveAccountVo;
import com.ruicar.afs.cloud.afscase.common.feign.CaseToChannelFeign;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.feign.ServiceFeeFeign;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.dto.PermissionResultDto;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffPayRecord;
import com.ruicar.afs.cloud.afscase.writeoff.enums.ChannelServiceFeeEnum;
import com.ruicar.afs.cloud.afscase.writeoff.enums.OverdueStatusEnum;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffPayRecordMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInvoiceRelService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRecordService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPermissionService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.FeeSubmitVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.PayRecordExcelVo;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.uid.AfsSequenceGenerator;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.PaymentStatusEnum;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.enums.common.FrozenStatusEnum;
import com.ruicar.afs.cloud.interfaces.cbs.dto.CbsPayRespBaseDTO;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPayDetailReq;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPaySearchBusReq;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPaySearchReqDTO;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPaySendReqDTO;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPayStatusReqDTO;
import com.ruicar.afs.cloud.interfaces.cbs.dto.req.CbsPaySummaryReq;
import com.ruicar.afs.cloud.interfaces.cbs.dto.resp.CbsPayRespStatusDTO;
import com.ruicar.afs.cloud.interfaces.cbs.enums.CBSRPPayStatusEnum;
import com.ruicar.afs.cloud.interfaces.cbs.service.CbsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@AllArgsConstructor
@Service
public class WriteOffPayRecordServiceImpl extends ServiceImpl<WriteOffPayRecordMapper, WriteOffPayRecord> implements WriteOffPayRecordService {

    private final WriteOffPermissionService writeOffPermissionService;
    private final WriteOffBaseInfoService writeOffBaseInfoService;
    private final AfsSequenceGenerator afsSequenceGenerator;
    private final WorkflowHelper workflowHelper;
    private final CbsService cbsService;
    private final WorkflowTaskInfoService workflowTaskInfoService;
    private final StringRedisTemplate redisTemplate;
    private final WriteOffBaseInvoiceRelService writeOffBaseInvoiceRelService;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final CaseToChannelFeign caseToChannelFeign;
    private final ServiceFeeFeign serviceFeeFeign;
    private static final String LOCK_CBS_PAY = "l:cbs:p:r";

    /**
     * 按条件查询
     * @param queryCondition
     * @return
     */
    @Override
    public IResponse queryByCondition(QueryCondition<WriteOffPayRecord> queryCondition) {
        WriteOffPayRecord condition = queryCondition.getCondition();
        List<String> frozenNoList = new ArrayList<>();
        if (condition.getFrozenStatus() != null) {
            frozenNoList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .select(WriteOffBaseInfo::getCaseNo)
                    .eq(WriteOffBaseInfo::getFrozenStatus, condition.getFrozenStatus())
                    .isNotNull(WriteOffBaseInfo::getCaseNo)).stream().map(WriteOffBaseInfo::getCaseNo).toList();
        }
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        Page<WriteOffPayRecord> page = this.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffPayRecord>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffPayRecord::getChannelCode, resultDto.getHitCodeList())
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffPayRecord::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getBusinessNo()), WriteOffPayRecord::getBusinessNo, condition.getBusinessNo())
                .eq(StrUtil.isNotBlank(condition.getBatchNo()), WriteOffPayRecord::getBatchNo, condition.getBatchNo())
                .eq(condition.getPayStatus() != null, WriteOffPayRecord::getPayStatus, condition.getPayStatus())
                .in(frozenNoList.size() > 0, WriteOffPayRecord::getCaseNo, frozenNoList)
                .eq(StrUtil.isNotBlank(condition.getStatus()), WriteOffPayRecord::getStatus, condition.getStatus())
                .like(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffPayRecord::getWriteOffMonth, condition.getWriteOffMonth())
                .ge(condition.getPayTimeStart() != null, WriteOffPayRecord::getWaitPayTime, condition.getPayTimeStart())
                .le(condition.getPayTimeStop() != null, WriteOffPayRecord::getWaitPayTime, condition.getPayTimeStop())
                .le(!condition.isCaseAll(), WriteOffPayRecord::getWaitPayTime, new Date())
                .orderByDesc(WriteOffPayRecord::getCreateTime));
        if (page.getRecords().size() <= 0) {
            return IResponse.success(page);
        }
        List<String> caseNos = page.getRecords().stream().map(WriteOffPayRecord::getCaseNo).toList();
        Map<String, List<WriteOffBaseInfo>> caseMap = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                        .select(WriteOffBaseInfo::getCaseNo, WriteOffBaseInfo::getFrozenStatus)
                        .in(WriteOffBaseInfo::getCaseNo, caseNos))
                .stream().collect(Collectors.groupingBy(WriteOffBaseInfo::getCaseNo));
        boolean flag = false;
        FrozenStatusEnum frozenStatusEnum = null;
        for (WriteOffPayRecord record : page.getRecords()) {
            List<WriteOffBaseInfo> baseInfoList = caseMap.get(record.getCaseNo());
            flag = false;
            if (baseInfoList.size() > 1) {
                for (WriteOffBaseInfo baseInfo : baseInfoList) {
                    if (FrozenStatusEnum.NORMAL != baseInfo.getFrozenStatus()) {
                        flag = true;
                        frozenStatusEnum = baseInfo.getFrozenStatus();
                    }
                }
                if (flag) {
                    record.setFrozenStatus(frozenStatusEnum);
                } else {
                    record.setFrozenStatus(FrozenStatusEnum.NORMAL);
                }
            } else {
                record.setFrozenStatus(baseInfoList.get(0).getFrozenStatus());
            }
        }
        return IResponse.success(page);
    }

    /**
     * 数据导出
     * @param condition
     * @param response
     */
    @Override
    public void exportData(WriteOffPayRecord condition, HttpServletResponse response) {
        List<String> frozenNoList = new ArrayList<>();
        if (condition.getFrozenStatus() != null) {
            frozenNoList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                    .select(WriteOffBaseInfo::getCaseNo)
                    .eq(WriteOffBaseInfo::getFrozenStatus, condition.getFrozenStatus())
                    .isNotNull(WriteOffBaseInfo::getCaseNo)).stream().map(WriteOffBaseInfo::getCaseNo).toList();
        }
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        List<WriteOffPayRecord> list = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffPayRecord::getChannelCode, resultDto.getHitCodeList())
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffPayRecord::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getBusinessNo()), WriteOffPayRecord::getBusinessNo, condition.getBusinessNo())
                .eq(StrUtil.isNotBlank(condition.getBatchNo()), WriteOffPayRecord::getBatchNo, condition.getBatchNo())
                .eq(condition.getPayStatus() != null, WriteOffPayRecord::getPayStatus, condition.getPayStatus())
                .in(frozenNoList.size() > 0, WriteOffPayRecord::getCaseNo, frozenNoList)
                .eq(StrUtil.isNotBlank(condition.getStatus()), WriteOffPayRecord::getStatus, condition.getStatus())
                .like(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffPayRecord::getWriteOffMonth, condition.getWriteOffMonth())
                .ge(condition.getPayTimeStart() != null, WriteOffPayRecord::getWaitPayTime, condition.getPayTimeStart())
                .le(condition.getPayTimeStop() != null, WriteOffPayRecord::getWaitPayTime, condition.getPayTimeStop())
                .orderByDesc(WriteOffPayRecord::getCreateTime));
        List<DicDataDto> payStatusList = DicHelper.getDicMaps("paymentStatusQuery")
                .getOrDefault("paymentStatusQuery", new ArrayList<>());
        Map<String, String> dicMap = new HashMap<>();
        for (DicDataDto dicDataDto : payStatusList) {
            dicMap.put(dicDataDto.getValue(),dicDataDto.getTitle());
        }
        List<PayRecordExcelVo> exportVoList = list.stream().map(record -> {
            PayRecordExcelVo exportVo = new PayRecordExcelVo();
            BeanUtil.copyProperties(record, exportVo);
            if (ChannelServiceFeeEnum.STATUS_0.code.equals(record.getStatus())) {
                exportVo.setStatus("未提取");
            } else if (ChannelServiceFeeEnum.STATUS_1.code.equals(record.getStatus())) {
                exportVo.setStatus("提取中");
            } else if (ChannelServiceFeeEnum.STATUS_2.code.equals(record.getStatus())) {
                exportVo.setStatus("已撤回");
            } else if (ChannelServiceFeeEnum.STATUS_3.code.equals(record.getStatus())) {
                exportVo.setStatus("提取完成");
            } else {
                exportVo.setStatus("异常");
            }
            if (record.getPayStatus() != null) {
                exportVo.setPayStatus(dicMap.get(record.getPayStatus().toString()));
            }
            return exportVo;
        }).toList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("服务费付款账单详情导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), PayRecordExcelVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, "服务费付款账单详情").build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }

    /**
     * 发起提取流程
     * @param submitVo
     * @return
     */
    @Override
    public IResponse submitApprove(FeeSubmitVo submitVo) {
        List<Long> idList = submitVo.getRelIdList();
        Assert.isTrue(CollUtil.isNotEmpty(idList), "勾选数据不能为空");
        //未提取数据
        List<String> statusList = new ArrayList<>();
        statusList.add(ChannelServiceFeeEnum.STATUS_0.code);
        statusList.add(ChannelServiceFeeEnum.STATUS_2.code);
        List<WriteOffPayRecord> payRecordList = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                .in(WriteOffPayRecord::getId, idList)
                .in(WriteOffPayRecord::getStatus, statusList)
                .le(WriteOffPayRecord::getWaitPayTime, DateUtil.endOfDay(new Date())));
        Assert.isTrue(idList.size() == payRecordList.size(), "请选择未提取或已撤回并且满足支付时间的数据发起!");
        // 校验数据
        List<String> caseNoList = payRecordList.stream().map(WriteOffPayRecord::getCaseNo).toList();
        List<WriteOffBaseInfo> frozenList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .select(WriteOffBaseInfo::getApplyNo)
                .in(WriteOffBaseInfo::getCaseNo, caseNoList)
                .in(WriteOffBaseInfo::getFrozenStatus, List.of(FrozenStatusEnum.FROZEN, FrozenStatusEnum.SUSPEND_PAYMENT)));
        if (CollUtil.isNotEmpty(frozenList)) {
            throw new AfsBaseException("核销项编号为：" + frozenList.get(0).getApplyNo() + "的核销项已冻结或专项管理，不可发起流程！");
        }
        List<WriteOffBaseInfo> reFrozenList = writeOffBaseInfoService.list(Wrappers.<WriteOffBaseInfo>lambdaQuery()
                .select(WriteOffBaseInfo::getApplyNo)
                .in(WriteOffBaseInfo::getCaseNo, caseNoList)
                .eq(WriteOffBaseInfo::getReturnOverdueStatus, OverdueStatusEnum.FROZEN.getCode()));
        if (!reFrozenList.isEmpty()) {
            throw new AfsBaseException("编号为：" + reFrozenList.get(0).getApplyNo() + "的核销项处于回司超期冻结状态，不可发起流程！");
        }
        //生成流程编号
        Long seqno = afsSequenceGenerator.genNext("serviceDraw", DateUtil.year(DateUtil.date()) + "");
        String businessNo = "FWFTQ" + DateUtil.year(DateUtil.date()) + StringUtils.leftPad(String.valueOf(seqno), 6, '0');
        StartFlowRequestBo requestBo = new StartFlowRequestBo();
        requestBo.setPackageId("write-off-application");
        requestBo.setTemplateId("service-charge-withdrawal");
        requestBo.setBusinessNo(businessNo);
        requestBo.setSubject("经销商服务费提取流程发起" + businessNo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FlowConstant.BUSINESS_NO, businessNo);
        jsonObject.put(FlowConstant.APPROVAL_USER, SecurityUtils.getUsername());
        jsonObject.put(FlowConstant.APPROVAL_OPINION, "发起");
        jsonObject.put(FlowConstant.TASK_NODE_NAME, "流程发起");
        jsonObject.put("periodFlag", "1");
        requestBo.setParams(jsonObject);
        IResponse response = workflowHelper.startFlow(requestBo, UseSceneEnum.SERVICE_CHARGE_WITHDRAWAL);
        if ("0000".equals(response.getCode())) {
            for (WriteOffPayRecord payRecord : payRecordList) {
                payRecord.setBusinessNo(businessNo);
                payRecord.setFileBusiNo(submitVo.getBusiNo());
                payRecord.setStatus(ChannelServiceFeeEnum.STATUS_1.code);
                payRecord.setSubmitTime(new Date());
                payRecord.setPayStatus(PaymentStatusEnum.waitPayment);
            }
            this.updateBatchById(payRecordList);
        } else {
            log.error("服务费提取工作流发起失败{}", response.getMsg());
            return IResponse.fail("发起流程失败");
        }
        return IResponse.success("发起流程成功");
    }

    /**
     * 查询cbs付款结果，激活等待节点
     * @return
     */
    @Override
    public IResponse getCbsPayResult() {
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(LOCK_CBS_PAY, "lock", 2, TimeUnit.HOURS);
        if (Boolean.FALSE.equals(lock)) {
            throw new AfsBaseException("在查询中，请勿重复点击");
        }
        List<WriteOffPayRecord> payRecordList = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                .eq(WriteOffPayRecord::getPayStatus, PaymentStatusEnum.paying));
        if (payRecordList.size() <= 0) {
            log.info("不存在付款中的服务费");
            return IResponse.success("不存在付款中的服务费");
        }
        //根据流程分组
        Map<String, List<WriteOffPayRecord>> collect = payRecordList.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getBusinessNo));
        for (Map.Entry<String, List<WriteOffPayRecord>> entry : collect.entrySet()) {
            try {
                boolean passFlag = true;
                String businessNo = entry.getKey();
                List<WriteOffPayRecord> recordList = entry.getValue();
                //根据经销商分组
                Map<String, List<WriteOffPayRecord>> channelRelList = recordList.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getChannelCode));
                for (Map.Entry<String, List<WriteOffPayRecord>> channelEntry : channelRelList.entrySet()) {
                    String channelCode = channelEntry.getKey();
                    List<WriteOffPayRecord> channelRecords = channelEntry.getValue();
                    String cbsBusNbr = channelRecords.get(0).getCbsBusNbr();
                    String cbsRefNbr = channelRecords.get(0).getCbsRefNbr();
                    if (StrUtil.isBlank(cbsBusNbr)) {
                        log.error("[服务费查询cbs结果]异常，cbs返回流水号为空，流程编号:{}，经销商编号:{}", businessNo, channelCode);
                        passFlag = false;
                    } else {
                        CbsPayStatusReqDTO req = new CbsPayStatusReqDTO();
                        req.setBusNbr(cbsBusNbr);
                        req.setRefNbr(cbsRefNbr);
                        IResponse<CbsPayRespStatusDTO> resp = cbsService.getPayResult(req);
                        log.info("流程编号:{}，经销商编号:{}，付款结果查询响应：{}", businessNo, channelCode, JSON.toJSONString(resp));
                        if(CommonConstants.SUCCESS.equals(resp.getCode())){
                            CbsPayRespStatusDTO respBody = resp.getData();
                            if (respBody.getStatus() == CBSRPPayStatusEnum.SUCCESS) {
                                for (WriteOffPayRecord channelRel : channelRecords) {
                                    channelRel.setPayStatus(PaymentStatusEnum.successPayment);
                                }
                                log.info("付款成功，流程编号:{}，经销商编号:{}", businessNo, channelCode);
                            }else if (respBody.getStatus() == CBSRPPayStatusEnum.FAILED || CBSRPPayStatusEnum.SEND_BACK == respBody.getStatus()) {
                                for (WriteOffPayRecord channelRel : channelRecords) {
                                    channelRel.setPayStatus(PaymentStatusEnum.failPayment);
                                    channelRel.setPayFailReason(respBody.getRemark());
                                }
                                log.warn("付款失败，流程编号:{}，经销商编号:{}", businessNo, channelCode);
                                passFlag = false;
                            }else if (respBody.getStatus() == CBSRPPayStatusEnum.UN_DONE || CBSRPPayStatusEnum.UN_KNOW == respBody.getStatus()) {
                                log.warn("暂无付款结果，流程编号:{}，经销商编号:{}", businessNo, channelCode);
                                passFlag = false;
                            }else {
                                passFlag = false;
                            }
                            this.updateBatchById(channelRecords);
                        }else{
                            log.warn("经销商编号:{},付款结果查询接口调用失败：{}", channelCode, resp.getMsg());
                            passFlag = false;
                        }
                    }
                }
                //如果该流程所有经销商都付款成功，才能激活等待节点
                List<WriteOffPayRecord> invoiceRelList = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                        .select(WriteOffPayRecord::getPayStatus)
                        .eq(WriteOffPayRecord::getBusinessNo, businessNo));
                for (WriteOffPayRecord invoiceRel : invoiceRelList) {
                    if (PaymentStatusEnum.successPayment != invoiceRel.getPayStatus()) {
                        passFlag = false;
                        break;
                    }
                }
                if (passFlag) {
                    WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                            .eq(WorkflowTaskInfo::getBusinessNo, businessNo).orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1"));
                    IResponse response = workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
                    if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                        log.error("[服务费提取]激活等待节点失败，流程编号:{}", businessNo);
                        throw new AfsBaseException("流程发起失败!");
                    }
                    log.info("[服务费提取]激活等待节点成功，流程编号:{}", businessNo);
                }
            } catch (Exception e) {
                log.error("[服务费提取查询付款结果]出现异常:{},流程编号:{}", e.getMessage(), entry.getKey());
            }
        }
        redisTemplate.delete(LOCK_CBS_PAY);
        return IResponse.success("查询成功");
    }

    /**
     * 人工付款，激活等待节点
     * @param idList
     * @return
     */
    @Override
    public IResponse artificialPay(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            throw new AfsBaseException("勾选数据不能为空");
        }
        log.info("服务费人工付款接收到请求参数：{}", idList);
        //付款失败
        List<WriteOffPayRecord> payRecordList = this.list(Wrappers.<WriteOffPayRecord>query().lambda()
                .in(WriteOffPayRecord::getId, idList)
                .eq(WriteOffPayRecord::getPayStatus, PaymentStatusEnum.failPayment)
                .orderByAsc(WriteOffPayRecord::getCreateTime));

        Assert.isTrue(idList.size() == payRecordList.size(), "请选择付款失败的数据!");
        List<String> busiList = new ArrayList<>();
        for (WriteOffPayRecord payRecord : payRecordList) {
            payRecord.setPayStatus(PaymentStatusEnum.successPayment);
            busiList.add(payRecord.getBusinessNo());
        }
        this.updateBatchById(payRecordList);
        //如果该流程所有数据都付款成功，激活等待节点
        List<WriteOffPayRecord> list = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery().in(WriteOffPayRecord::getBusinessNo, busiList));
        Map<String, List<WriteOffPayRecord>> collect = list.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getBusinessNo));
        for (Map.Entry<String, List<WriteOffPayRecord>> entry : collect.entrySet()) {
            String businessNo = entry.getKey();
            List<WriteOffPayRecord> records = entry.getValue();
            List<WriteOffPayRecord> paySuccessList = records.stream().filter(k -> k.getPayStatus() == PaymentStatusEnum.successPayment).toList();
            if (paySuccessList.size() == records.size()) {
                //激活等待节点
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query().lambda()
                        .select(WorkflowTaskInfo::getProcessInstanceId)
                        .eq(WorkflowTaskInfo::getBusinessNo, businessNo).orderByDesc(WorkflowTaskInfo::getCreateTime).last("limit 1"));
                IResponse response = workflowHelper.resumeProcess(workflowTaskInfo.getProcessInstanceId());
                if (!CommonConstants.SUCCESS.equals(response.getCode())) {
                    log.error("[服务费提取]激活等待节点失败，流程编号:{}", businessNo);
                    throw new AfsBaseException("流程发起失败!");
                }
                log.info("[服务费提取]激活等待节点成功，流程编号:{}", businessNo);
            }
        }
        return IResponse.success("人工付款操作成功");
    }

    /**
     * 失败记录发起重新支付
     * @param idList
     * @return
     */
    @Override
    public IResponse failedRecordInitiatesRepay(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new AfsBaseException("勾选数据不能为空");
        }
        log.info("服务费失败流程发起再次付款流程接收到请求参数：{}", idList);
        //付款失败
        List<WriteOffPayRecord> records = this.list(Wrappers.<WriteOffPayRecord>lambdaQuery()
                .in(WriteOffPayRecord::getId, idList)
                .eq(WriteOffPayRecord::getPayStatus, PaymentStatusEnum.failPayment)
                .orderByAsc(WriteOffPayRecord::getCreateTime));
        Assert.isTrue(idList.size() == records.size(), "只能选择付款失败的数据!");
        // 以流程编号分组
        Map<String, List<WriteOffPayRecord>> listMap = records.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getBusinessNo));
        listMap.forEach((businessNo, v) -> {
            List<WriteOffPayRecord> recordList = this.list(
                    Wrappers.<WriteOffPayRecord>lambdaQuery()
                            .eq(WriteOffPayRecord::getBusinessNo, businessNo)
                            .eq(WriteOffPayRecord::getPayStatus, PaymentStatusEnum.failPayment)
                            .eq(WriteOffPayRecord::getStatus, ChannelServiceFeeEnum.STATUS_1.code));
            if (CollUtil.isNotEmpty(recordList)) {
                // 服务费失败重新支付key
                String lockPrefix = "S:F:F:Repay" + businessNo;
                if (Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockPrefix, "lock", 60, TimeUnit.SECONDS))) {
                    // 发起再次付款
                    this.recordCbsPay(recordList, false, businessNo);
                    redisTemplate.delete(lockPrefix);
                }
            }
        });
        return IResponse.success("操作成功");
    }

    /**
     * 老页面的提取支付
     * @param writeOffBaseInvoiceRels
     * @param flowFlag
     * @param businessNo
     */
    @Override
    public void relCbsPay(List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels, Boolean flowFlag, String businessNo) {
        log.info("[服务费cbs付款]拿到流程编号:{}", businessNo);
        Assert.isTrue(CollUtil.isNotEmpty(writeOffBaseInvoiceRels), "提取数据为空");
        IResponse<String> stringResponse = serviceFeeFeign.getWriteOffPayAccount();
        Assert.isTrue("0000".equals(stringResponse.getCode()), "服务费付款账号获取失败");
        String payAccount = stringResponse.getData();
        log.info("服务费提取获取到付款账号：{}",payAccount);
        //根据经销商分组
        Map<String, List<WriteOffBaseInvoiceRel>> dealerMap = writeOffBaseInvoiceRels.stream()
                .collect(Collectors.groupingBy(WriteOffBaseInvoiceRel::getDealerCode));
        int channelNum = 0;
        for (String dealerCode : dealerMap.keySet()) {
            channelNum++;
            String refNbr = businessNo + "_" + channelNum;
            List<CbsPayDetailReq> detailList = new ArrayList<>();
            List<WriteOffBaseInvoiceRel> offBaseInvoiceRelList = dealerMap.get(dealerCode);
            WriteOffBaseInvoiceRel baseInvoiceRelOne = offBaseInvoiceRelList.get(0);
            if(!flowFlag){
                refNbr = getRefNbr(baseInvoiceRelOne.getCbsRefNbr());
            }
            if (!List.of(PaymentStatusEnum.waitPayment, PaymentStatusEnum.failPayment)
                    .contains(baseInvoiceRelOne.getPayStatus())) {
                //已经调过cbs付款接口，跳过
                log.info("已经调过cbs付款接口，跳过，经销商：{}-{}", baseInvoiceRelOne.getDealerCode(), baseInvoiceRelOne.getDealerName());
                continue;
            }
            ChannelReceiveAccountVo receiveInfo = null;
            try {
                //查询经销商收款信息
                ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getChannelByDealerId(dealerCode);
                IResponse<ChannelReceiveAccountVo> response = caseToChannelFeign.getChannelReceiveInfo(
                        channelBaseInfo.getChannelId());
                Assert.isTrue("0000".equals(response.getCode()), "经销商编号:" + dealerCode + "获取服务费收款信息失败");
                receiveInfo = response.getData();
                //从字典获取收款银行编码
                List<DicDataDto> bankCodeList = DicHelper.getDicMaps("payBank")
                        .getOrDefault("payBank", new ArrayList<>());
                Assert.isTrue(CollUtil.isNotEmpty(bankCodeList), "获取收款银行编码失败");
                CbsPayDetailReq detail = new CbsPayDetailReq();
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (WriteOffBaseInvoiceRel rel : offBaseInvoiceRelList) {
                    //获取该经销商总放款金额
                    String amount = String.valueOf(
                            ObjectUtils.isNull(rel.getInvoiceAmount()) ? BigDecimal.ZERO : rel.getInvoiceAmount());
                    totalAmount = totalAmount.add(new BigDecimal(amount));
                }
                totalAmount = totalAmount.setScale(2);
                detail.setBnkNam(receiveInfo.getReceivingSubBranch());
                detail.setWagMny(totalAmount.toString());
                detail.setRevNam(receiveInfo.getReceivingName());
                detail.setRevAcc(receiveInfo.getReceivingAccount());
                detail.setEacCty(receiveInfo.getOpeningBankCity());
                detail.setExtTx1(refNbr);
                detail.setReceiveBankNbr(receiveInfo.getElectronicBankNo());
                detail.setNUsage("服务费--" + baseInvoiceRelOne.getDealerName());
                //付款账号取值
                detail.setPaymentAct(payAccount);
                detailList.add(detail);
                CbsPaySendReqDTO req = new CbsPaySendReqDTO();
                CbsPaySummaryReq summary = new CbsPaySummaryReq();
                summary.setTrsAmt(totalAmount.toString());
                summary.setTrsUse("服务费--" + baseInvoiceRelOne.getDealerName());
                req.setSummary(summary);
                req.setDetailList(detailList);
                req.setRefNbr(refNbr);
                IResponse<String> typeRes = serviceFeeFeign.getCbsPaymentType(
                        getBankCodeByTitle(bankCodeList, receiveInfo.getReceivingBank()));
                Assert.isTrue("0000".equals(typeRes.getCode()), "获取cbs业务类型失败");
                req.setType(typeRes.getData());
                IResponse<CbsPayRespBaseDTO> payResponse = cbsService.pay(req);
                if (!CommonConstants.SUCCESS.equals(payResponse.getCode())) {
                    log.warn("[服务费提取Cbs]流程编号:{},经销商编号:{},CBS付款接口响应失败", businessNo, dealerCode);
                    log.info("CBS付款接口响应失败，执行交易查询：{}", refNbr);
                    CbsPaySearchReqDTO searchReq = new CbsPaySearchReqDTO();
                    searchReq.setRefNbr(refNbr);
                    setCbsSearchBusData(searchReq, detailList, payAccount);
                    IResponse<CbsPayRespBaseDTO> searchResp = cbsService.sendStatusSearch(searchReq);
                    CbsPayRespBaseDTO searchData = searchResp.getData();
                    log.info("CBS付款接口响应失败，交易查询结果：{}--{}", refNbr, searchResp);
                    if (CommonConstants.SUCCESS.equals(searchResp.getCode()) && searchData != null
                            && StrUtil.isNotBlank(searchData.getBusNbr())) {
                        log.info("CBS付款接口响应失败，交易查询结果，存在有效交易记录，视为当次交易有效 {}", refNbr);
                        String busNbr = searchData.getBusNbr();
                        for (WriteOffBaseInvoiceRel invoiceRel : offBaseInvoiceRelList) {
                            invoiceRel.setCbsRefNbr(refNbr);
                            invoiceRel.setCbsBusNbr(busNbr);
                            invoiceRel.setPayStatus(PaymentStatusEnum.paying);
                            invoiceRel.setPayAccount(payAccount);
                            invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                        }
                        writeOffBaseInvoiceRelService.updateBatchById(offBaseInvoiceRelList);
                    } else {
                        log.info("CBS付款接口响应失败，交易查询结果，响应失败，视为当次交易无效 {}", refNbr);
                        for (WriteOffBaseInvoiceRel invoiceRel : offBaseInvoiceRelList) {
                            invoiceRel.setCbsRefNbr(refNbr);
                            invoiceRel.setPayStatus(PaymentStatusEnum.failPayment);
                            invoiceRel.setPayAccount(payAccount);
                            invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                            invoiceRel.setPayFailReason("cbs付款接口响应失败，交易查询结果，响应失败，视为当次交易无效");
                        }
                        writeOffBaseInvoiceRelService.updateBatchById(offBaseInvoiceRelList);
                    }
                } else {
                    //cbs发送成功
                    String busNbr = payResponse.getData().getBusNbr();
                    for (WriteOffBaseInvoiceRel invoiceRel : offBaseInvoiceRelList) {
                        invoiceRel.setCbsRefNbr(refNbr);
                        invoiceRel.setCbsBusNbr(busNbr);
                        invoiceRel.setPayStatus(PaymentStatusEnum.paying);
                        invoiceRel.setPayAccount(payAccount);
                        invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                    }
                    writeOffBaseInvoiceRelService.updateBatchById(offBaseInvoiceRelList);
                    log.info("[服务费提取Cbs]流程编号:{},经销商编号:{},请求流水号:{},CBS付款发送成功", businessNo, dealerCode,refNbr);
                }
            } catch (Exception e) {
                log.error("[服务费提取Cbs]出现异常:{},流程编号:{},经销商编号:{},", e.getMessage(), businessNo,
                        dealerCode);
                log.error("服务费提取出现异常，修改付款状态为失败：{}", refNbr);
                for (WriteOffBaseInvoiceRel invoiceRel : offBaseInvoiceRelList) {
                    invoiceRel.setCbsRefNbr(refNbr);
                    invoiceRel.setPayStatus(PaymentStatusEnum.failPayment);
                    invoiceRel.setPayAccount(payAccount);
                    invoiceRel.setPayReceiveAccount(
                            receiveInfo != null && StrUtil.isNotBlank(receiveInfo.getReceivingAccount())
                                    ? receiveInfo.getReceivingAccount() : "");
                    invoiceRel.setPayFailReason("refNbr:"+refNbr+"接口异常：" + e.getMessage());
                }
                writeOffBaseInvoiceRelService.updateBatchById(offBaseInvoiceRelList);
            }
        }
    }

    /**
     * 新页面的分期提取支付
     * @param payRecordList
     * @param flowFlag
     * @param businessNo
     */
    @Override
    public void recordCbsPay(List<WriteOffPayRecord> payRecordList, Boolean flowFlag, String businessNo) {
        log.info("[服务费cbs付款]拿到流程编号:{}", businessNo);
        Assert.isTrue(CollUtil.isNotEmpty(payRecordList), "提取数据为空");
        IResponse<String> stringResponse = serviceFeeFeign.getWriteOffPayAccount();
        Assert.isTrue("0000".equals(stringResponse.getCode()), "服务费付款账号获取失败");
        String payAccount = stringResponse.getData();
        log.info("服务费提取获取到付款账号：{}",payAccount);
        //根据经销商分组
        Map<String, List<WriteOffPayRecord>> dealerMap = payRecordList.stream().collect(Collectors.groupingBy(WriteOffPayRecord::getChannelCode));
        int channelNum = 0;
        for (String dealerCode : dealerMap.keySet()) {
            channelNum++;
            String refNbr = businessNo + "_" + channelNum;
            List<CbsPayDetailReq> detailList = new ArrayList<>();
            List<WriteOffPayRecord> recordList = dealerMap.get(dealerCode);
            WriteOffPayRecord payRecordOne = recordList.get(0);
            if(!flowFlag){
                refNbr = getRefNbr(payRecordOne.getCbsRefNbr());
            }
            if (!List.of(PaymentStatusEnum.waitPayment, PaymentStatusEnum.failPayment).contains(payRecordOne.getPayStatus())) {
                //已经调过cbs付款接口，跳过
                log.info("已经调过cbs付款接口，跳过，经销商：{}-{}", payRecordOne.getChannelCode(), payRecordOne.getChannelFullName());
                continue;
            }
            ChannelReceiveAccountVo receiveInfo = null;
            try {
                //查询经销商收款信息
                ChannelBaseInfo channelBaseInfo = channelBaseInfoService.getChannelByDealerId(dealerCode);
                IResponse<ChannelReceiveAccountVo> response = caseToChannelFeign.getChannelReceiveInfo(channelBaseInfo.getChannelId());
                Assert.isTrue("0000".equals(response.getCode()), "经销商编号:" + dealerCode + "获取服务费收款信息失败");
                receiveInfo = response.getData();
                //从字典获取收款银行编码
                List<DicDataDto> bankCodeList = DicHelper.getDicMaps("payBank")
                        .getOrDefault("payBank", new ArrayList<>());
                Assert.isTrue(CollUtil.isNotEmpty(bankCodeList), "获取收款银行编码失败");
                CbsPayDetailReq detail = new CbsPayDetailReq();
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (WriteOffPayRecord record : recordList) {
                    //获取该经销商总放款金额
                    String amount = String.valueOf(ObjectUtils.isNull(record.getPayAmount()) ? BigDecimal.ZERO : record.getPayAmount());
                    totalAmount = totalAmount.add(new BigDecimal(amount));
                }
                totalAmount = totalAmount.setScale(2);
                detail.setBnkNam(receiveInfo.getReceivingSubBranch());
                detail.setWagMny(totalAmount.toString());
                detail.setRevNam(receiveInfo.getReceivingName());
                detail.setRevAcc(receiveInfo.getReceivingAccount());
                detail.setEacCty(receiveInfo.getOpeningBankCity());
                detail.setExtTx1(refNbr);
                detail.setReceiveBankNbr(receiveInfo.getElectronicBankNo());
                detail.setNUsage("服务费--" + payRecordOne.getChannelFullName());
                //付款账号取值
                detail.setPaymentAct(payAccount);
                detailList.add(detail);
                CbsPaySendReqDTO req = new CbsPaySendReqDTO();
                CbsPaySummaryReq summary = new CbsPaySummaryReq();
                summary.setTrsAmt(totalAmount.toString());
                summary.setTrsUse("服务费--" + payRecordOne.getChannelFullName());
                req.setSummary(summary);
                req.setDetailList(detailList);
                req.setRefNbr(refNbr);
                IResponse<String> typeRes = serviceFeeFeign.getCbsPaymentType(getBankCodeByTitle(bankCodeList, receiveInfo.getReceivingBank()));
                Assert.isTrue("0000".equals(typeRes.getCode()), "获取cbs业务类型失败");
                req.setType(typeRes.getData());
                IResponse<CbsPayRespBaseDTO> payResponse = cbsService.pay(req);
                if (!CommonConstants.SUCCESS.equals(payResponse.getCode())) {
                    log.warn("[服务费提取Cbs]流程编号:{},经销商编号:{},CBS付款接口响应失败", businessNo, dealerCode);
                    log.info("CBS付款接口响应失败，执行交易查询：{}", refNbr);
                    CbsPaySearchReqDTO searchReq = new CbsPaySearchReqDTO();
                    searchReq.setRefNbr(refNbr);
                    setCbsSearchBusData(searchReq, detailList, payAccount);
                    IResponse<CbsPayRespBaseDTO> searchResp = cbsService.sendStatusSearch(searchReq);
                    CbsPayRespBaseDTO searchData = searchResp.getData();
                    log.info("CBS付款接口响应失败，交易查询结果：{}--{}", refNbr, searchResp);
                    if (CommonConstants.SUCCESS.equals(searchResp.getCode()) && searchData != null && StrUtil.isNotBlank(searchData.getBusNbr())) {
                        log.info("CBS付款接口响应失败，交易查询结果，存在有效交易记录，视为当次交易有效 {}", refNbr);
                        String busNbr = searchData.getBusNbr();
                        for (WriteOffPayRecord invoiceRel : recordList) {
                            invoiceRel.setCbsRefNbr(refNbr);
                            invoiceRel.setCbsBusNbr(busNbr);
                            invoiceRel.setPayStatus(PaymentStatusEnum.paying);
                            invoiceRel.setPayAccount(payAccount);
                            invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                            invoiceRel.setSuccessPayTime(new Date());
                        }
                        this.updateBatchById(recordList);
                    } else {
                        log.info("CBS付款接口响应失败，交易查询结果，响应失败，视为当次交易无效 {}", refNbr);
                        for (WriteOffPayRecord invoiceRel : recordList) {
                            invoiceRel.setCbsRefNbr(refNbr);
                            invoiceRel.setPayStatus(PaymentStatusEnum.failPayment);
                            invoiceRel.setPayAccount(payAccount);
                            invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                            invoiceRel.setPayFailReason("cbs付款接口响应失败，交易查询结果，响应失败，视为当次交易无效");
                        }
                        this.updateBatchById(recordList);
                    }
                } else {
                    //cbs发送成功
                    String busNbr = payResponse.getData().getBusNbr();
                    for (WriteOffPayRecord invoiceRel : recordList) {
                        invoiceRel.setCbsRefNbr(refNbr);
                        invoiceRel.setCbsBusNbr(busNbr);
                        invoiceRel.setPayStatus(PaymentStatusEnum.paying);
                        invoiceRel.setPayAccount(payAccount);
                        invoiceRel.setPayReceiveAccount(receiveInfo.getReceivingAccount());
                        invoiceRel.setSuccessPayTime(new Date());
                    }
                    this.updateBatchById(recordList);
                    log.info("[服务费提取Cbs]流程编号:{},经销商编号:{},请求流水号:{},CBS付款发送成功", businessNo, dealerCode,refNbr);
                }
            } catch (Exception e) {
                log.error("[服务费提取Cbs]出现异常:{},流程编号:{},经销商编号:{},", e.getMessage(), businessNo, dealerCode);
                log.error("服务费提取出现异常，修改付款状态为失败：{}", refNbr);
                for (WriteOffPayRecord invoiceRel : recordList) {
                    invoiceRel.setCbsRefNbr(refNbr);
                    invoiceRel.setPayStatus(PaymentStatusEnum.failPayment);
                    invoiceRel.setPayAccount(payAccount);
                    invoiceRel.setPayReceiveAccount(
                            receiveInfo != null && StrUtil.isNotBlank(receiveInfo.getReceivingAccount())
                                    ? receiveInfo.getReceivingAccount() : "");
                    invoiceRel.setPayFailReason("refNbr:"+refNbr+"接口异常：" + e.getMessage());
                }
                this.updateBatchById(recordList);
            }
        }
    }

    private void setCbsSearchBusData(CbsPaySearchReqDTO searchReq, List<CbsPayDetailReq> detailList, String payAccount) {
        searchReq.setBusList(new ArrayList<>());
        for (CbsPayDetailReq detailReq : detailList) {
            CbsPaySearchBusReq req = new CbsPaySearchBusReq();
            //补充字段
            req.setCltAcc(payAccount);
            req.setRefNbr(searchReq.getRefNbr());
            req.setTrsAmt(detailReq.getWagMny());
            req.setRevAcc(detailReq.getRevAcc());
            searchReq.getBusList().add(req);
        }
    }

    private static String getBankCodeByTitle(List<DicDataDto> bankCodeList, String title) {
        for (DicDataDto dicDataDto : bankCodeList) {
            if (dicDataDto.getTitle().equals(title)) {
                return dicDataDto.getValue();
            }
        }
        return "";
    }

    private static String getRefNbr(String refNbr) {
        if (refNbr.contains("_R_")) {
            // 找到 "R" 后面的数字并进行自增
            int index = refNbr.lastIndexOf("_R_") + 3;
            String numberPart = refNbr.substring(index);
            int number = Integer.parseInt(numberPart);
            number++;
            refNbr = refNbr.substring(0, index) + number;
        } else {
            // 如果不包含 "R"，则在字符串后面加上 "_R_1"
            refNbr = refNbr + "_R_1";
        }
        return refNbr;
    }
}
