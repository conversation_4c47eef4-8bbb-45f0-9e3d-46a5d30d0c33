package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 服务费综合查询返回结果
 */
@Data
public class WriteOffBaseDraftExcelVO implements Serializable {

    @ExcelProperty(value = "服务费核销项编号")
    private String applyNo;
    @ExcelProperty(value = "业务模式")
    private String writeOffType;
    @ExcelProperty(value = "核销项月份")
    private String writeOffMonth;
    @ExcelProperty(value = "核销项状态")
    private String statusName;
    @ExcelProperty(value = "经销商编号")
    private String organId;
    @ExcelProperty(value = "经销商名称")
    private String organName;
    /**
     * 应开票金额
     */
    @ExcelProperty(value = "应开票金额")
    private String invoiceAmount;
    @ExcelProperty(value = "未含税金额")
    private BigDecimal excludeTaxAmount;
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;
    /**
     * 待开票金额
     */
    @ExcelProperty(value = "待开票金额")
    private BigDecimal amountToBeInvoiced;
    /**
     * 正在核销金额
     */
    @ExcelProperty(value = "正在核销金额")
    private BigDecimal amountBeingWrittenOff;
    /**
     * 已放款金额
     */
    @ExcelProperty(value = "已放款金额")
    private BigDecimal amountLoaned;
    /**
     * 待放款金额
     */
    @ExcelProperty(value = "待放款金额")
    private BigDecimal amountToBeReleased;
    @ExcelProperty(value = "奖惩前金额")
    private BigDecimal beforeAmount;
    @ExcelProperty(value = {"合同取消扣减服务费","上月留扣"})
    private BigDecimal lastToCancelPunishAmount;
    @ExcelProperty(value = {"合同取消扣减服务费","本月新增应扣"})
    private BigDecimal cancelPunishAmount;
    @ExcelProperty(value = {"合同取消扣减服务费","本月实扣"})
    private BigDecimal actualCancelPunishAmount;
    @ExcelProperty(value = {"合同取消扣减服务费","下月留扣"})
    private BigDecimal nextToCancelPunishAmount;
    @ExcelProperty(value = {"提前结清扣减服务费","上月留扣"})
    private BigDecimal lastToEarlySettleAmount;
    @ExcelProperty(value = {"提前结清扣减服务费","本月新增应扣"})
    private BigDecimal earlySettlementAmount;
    @ExcelProperty(value = {"提前结清扣减服务费","本月实扣"})
    private BigDecimal actualEarlySettleAmount;
    @ExcelProperty(value = {"提前结清扣减服务费","下月留扣"})
    private BigDecimal nextToEarlySettleAmount;
    @ExcelProperty(value = {"其他扣减","上月留扣"})
    private BigDecimal lastToOtherPunishAmount;
    @ExcelProperty(value = {"其他扣减","本月新增应扣"})
    private BigDecimal punishAmount;
    @ExcelProperty(value = {"其他扣减","本月实扣"})
    private BigDecimal actualOtherPunishAmount;
    @ExcelProperty(value = {"其他扣减","下月留扣"})
    private BigDecimal nextToOtherPunishAmount;
    @ExcelProperty(value = "导入奖励金额")
    private BigDecimal prizeAmount;
    @ExcelProperty(value = "导入扣罚金额")
    private BigDecimal importPunishAmount;
    @ExcelProperty(value = "发票性质")
    private String receiveElectronicFlag;
    @ExcelProperty(value = "冻结状态")
    private String freezeStatus;
    @ExcelProperty(value = "发票上传时间期限")
    private String invoiceUploadDeadline;
    @ExcelProperty(value = "发票回司时间期限")
    private String returningCompanyDeadline;
    @ExcelProperty(value = "时间同步")
    private String timeSyncFlag;
    @ExcelProperty(value = "奖励事项")
    private String prizeRemark;
    @ExcelProperty(value = "扣罚事项")
    private String punishRemark;
    @ExcelProperty(value = "核销项说明")
    private String writeOffExplain;
}
