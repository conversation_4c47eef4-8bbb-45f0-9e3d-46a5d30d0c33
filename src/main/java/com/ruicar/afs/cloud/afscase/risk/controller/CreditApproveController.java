package com.ruicar.afs.cloud.afscase.risk.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.risk.entity.BaiHangSpendInfo;
import com.ruicar.afs.cloud.afscase.risk.entity.DwCreditInfo;
import com.ruicar.afs.cloud.afscase.risk.entity.ManualReviewInformation;
import com.ruicar.afs.cloud.afscase.risk.entity.TongDunInfo;
import com.ruicar.afs.cloud.afscase.risk.entity.TongDunOriginalInfo;
import com.ruicar.afs.cloud.afscase.risk.enums.BaiHangLevelEnum;
import com.ruicar.afs.cloud.afscase.risk.service.BaiHangSpendInfoService;
import com.ruicar.afs.cloud.afscase.risk.service.DwCreditInfoService;
import com.ruicar.afs.cloud.afscase.risk.service.ManualReviewInformationService;
import com.ruicar.afs.cloud.afscase.risk.service.ThirdDataService;
import com.ruicar.afs.cloud.afscase.risk.service.TongDunOriginalService;
import com.ruicar.afs.cloud.afscase.risk.service.TongDunService;
import com.ruicar.afs.cloud.afscase.risk.vo.InfoVO;
import com.ruicar.afs.cloud.afscase.risk.vo.ManualReviewInformationVo;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.interfaces.baihang.dto.BaiHangQueryReq;
import com.ruicar.afs.cloud.interfaces.baihang.dto.BaiHangQueryRsp;
import com.ruicar.afs.cloud.interfaces.baihang.service.BaiHangService;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.BlacklistDto;
import com.ruicar.afs.cloud.interfaces.capc.service.blacklist.BlacklistService;
import com.ruicar.afs.cloud.parameter.commom.enums.CustType;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1
 * @deprecation 征信结果查询
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/creditApprove")
public class CreditApproveController {

    /**
     * 点微 service
     */
    private  DwCreditInfoService dwCreditInfoService;
    /**
     * 同盾 service
     */
    private TongDunService tongDunService;
    private final CaseCustInfoService caseCustInfoService;
    private final StringRedisTemplate stringRedisTemplate;
    private final TongDunOriginalService tongDunOriginalService;
    private BlacklistService blacklistService;
    private final ThirdDataService thirdDataService;
    private final BaiHangService baiHangService;
    private final BaiHangSpendInfoService baiHangSpendInfoService;
    private ManualReviewInformationService manualReviewInformationService;


    @PostMapping("/queryDwCreditByNo")
    public IResponse<DwCreditInfo> queryDwCreditByNo(@RequestBody InfoVO vo) {
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode())
        );
        log.info("点微报告查询主借人信息{}", caseCustInfo);
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            List<DwCreditInfo> infoList = dwCreditInfoService.list(Wrappers.<DwCreditInfo>lambdaQuery()
                    .eq(DwCreditInfo::getCid, caseCustInfo.getCertNo())
                    .orderByDesc(DwCreditInfo::getCreateTime));
            return new IResponse().setData(infoList);
        }

        return new IResponse().setData(null);
    }

    @PostMapping("/queryTdCreditByNo")
    public IResponse<TongDunInfo> queryTdCreditByNo(@RequestBody InfoVO vo) {
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        List<TongDunInfo> tongDunInfo = tongDunService.list(Wrappers.<TongDunInfo>lambdaQuery()
                .eq(TongDunInfo :: getIdNumber, caseCustInfo.getCertNo())
                .orderByDesc(TongDunInfo :: getCreateTime));
        return new IResponse().setData(tongDunInfo);
    }

    @PostMapping("/queryReportUrl")
    public IResponse queryReportUrl(@RequestBody InfoVO vo){
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        List<TongDunInfo> tongDunInfo = tongDunService.list(Wrappers.<TongDunInfo>lambdaQuery()
                .eq(TongDunInfo :: getIdNumber, caseCustInfo.getCertNo())
                .orderByDesc(TongDunInfo :: getCreateTime));

        if(ObjectUtil.isNotEmpty(tongDunInfo)){
            TongDunInfo tdInfo = tongDunInfo.get(0);
            tongDunService.queryReportUrl(tdInfo);
            tongDunService.updateById(tdInfo);
        }
        return IResponse.success("操作完成");

    }

    @PostMapping("/queryUrlOfRisk")
    public IResponse<TongDunInfo> queryUrlOfRisk(@RequestBody InfoVO vo){
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        List<TongDunInfo> tongDunInfo = tongDunService.list(Wrappers.<TongDunInfo>lambdaQuery()
                .eq(TongDunInfo :: getIdNumber, caseCustInfo.getCertNo())
                .orderByDesc(TongDunInfo :: getCreateTime));
        if(ObjectUtil.isNotEmpty(tongDunInfo)){
            TongDunInfo tdInfo = tongDunInfo.get(0);
            String rhReport = stringRedisTemplate.opsForValue().get("risk:rhReport"+tdInfo.getRiskNo());
            tdInfo.setOssPath(rhReport);
            return new IResponse().setData(tdInfo);
        }
        return IResponse.fail("未查询到征信结果");
    }

    @ApiOperation(value = "通过申请编号查询同盾原始报文")
    @PostMapping("/queryTdOriginalInfoByNo")
    public IResponse<JSONArray> queryTdOriginalInfoByNo(@RequestBody InfoVO vo) {
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode())
        );
        log.info("同盾报告查询主借人信息{}", caseCustInfo);
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            List<TongDunOriginalInfo> infoList = tongDunOriginalService.list(Wrappers.<TongDunOriginalInfo>lambdaQuery()
                    .eq(TongDunOriginalInfo::getIdNumber, caseCustInfo.getCertNo())
                    .orderByDesc(TongDunOriginalInfo::getCreateTime));
            if (ObjectUtil.isNotEmpty(infoList)) {
                String report = infoList.get(0).getOriginalMsg();
                JSONArray js = new JSONArray();
                js.add(JSON.parse(report));
                return new IResponse().setData(js);
            }
        }

        return new IResponse().setData("未查询到同盾报告信息");
    }


    @PostMapping("/queryReport")
    public IResponse queryReport(@RequestBody InfoVO vo){
        List<CaseCustInfo> caseCustInfo = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode())
                .eq(CaseCustInfo::getCustType, CustType.PERSON.getIndex()));

        if (ObjectUtil.isNotEmpty(caseCustInfo)) {
            List<TongDunInfo> tongDunInfo = tongDunService.list(Wrappers.<TongDunInfo>lambdaQuery()
                    .eq(TongDunInfo :: getIdNumber, caseCustInfo.get(0).getCertNo())
                    .orderByDesc(TongDunInfo :: getCreateTime));
            if(ObjectUtil.isNotEmpty(tongDunInfo)){
                TongDunInfo tdInfo = tongDunInfo.get(0);
                tongDunService.queryReportUrl(tdInfo);
                tongDunService.updateById(tdInfo);
            }
        }
        return IResponse.success("操作完成");

    }

    @PostMapping("/queryReportOfRisk")
    public IResponse<TongDunInfo> queryReportOfRisk(@RequestBody InfoVO vo){
        List<CaseCustInfo> caseCustInfo = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));

        if (ObjectUtil.isNotEmpty(caseCustInfo)) {
            List<TongDunInfo> tongDunInfo = tongDunService.list(Wrappers.<TongDunInfo>lambdaQuery()
                    .eq(TongDunInfo :: getIdNumber, caseCustInfo.get(0).getCertNo())
                    .orderByDesc(TongDunInfo :: getCreateTime));
            if(ObjectUtil.isNotEmpty(tongDunInfo)){
                TongDunInfo tdInfo = tongDunInfo.get(0);
                String rhReport = stringRedisTemplate.opsForValue().get("risk:rhReport"+tdInfo.getRiskNo());
                tdInfo.setOssPath(rhReport);
                return new IResponse().setData(tdInfo);
            }
        }

        return IResponse.fail("未查询到担保人征信结果");
    }


    @PostMapping("/queryBlackListInfo")
    public IResponse queryBlackListInfo(@RequestBody InfoVO vo){
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, vo.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode())
        );

        BlacklistDto blacklistDto = new BlacklistDto();
        blacklistDto.setMobile(caseCustInfo.getTelPhone());//只校验手机号是否命中黑名单

        log.info("大数据报告调用智慧黑名单接口查询入参：" + blacklistDto);
        String blackResult = blacklistService.getRiskFromBlacklist(blacklistDto);
        log.info("大数据报告调用智慧黑名单接口查询出参：" + blackResult);

        if ("true".equals(blackResult)) {
            return IResponse.success("命中黑名单");
        }
        return IResponse.success("未命中黑名单");
    }

    @RequestMapping("/queryThirdUrl")
    public IResponse<String> queryThirdUrl(@RequestBody InfoVO vo) {
        return thirdDataService.queryThirdUrlByApplyNo(vo.getApplyNo());
    }


    @RequestMapping("/queryThirdData")
    public IResponse queryThirdDataByApplyNo(@RequestBody InfoVO vo) {
        return thirdDataService.queryThirdDataByApplyNo(vo.getApplyNo());
    }

    /**
     * 通过申请编号判断当前订单是否为TOP20经销商
     * @param applyNo 申请编号
     * @return 返回结果
     */
    @RequestMapping("/isTopChannelByApplyNo")
    public IResponse isTopChannelByApplyNo(@RequestParam String applyNo) {
        if(thirdDataService.isTopChannelByApplyNo(applyNo)){
           return IResponse.success("");
        }

        return IResponse.fail("");
    }

    /**
     * 百行征信普惠评分-消费能力等级查询
     * @return
     */
    @GetMapping("/baiHangLevel")
    public IResponse baiHangLevel(@RequestParam(value = "applyNo") String applyNo) {
        List<BaiHangSpendInfo> spendInfoList = baiHangSpendInfoService.list(Wrappers.<BaiHangSpendInfo>lambdaQuery()
                .eq(BaiHangSpendInfo::getApplyNo, applyNo)
                .orderByDesc(BaiHangSpendInfo::getCreateTime));
        BaiHangSpendInfo infoOne = null;
        if (spendInfoList.size() > 0 && "000".equals(spendInfoList.get(0).getRetCode())) {
            //调用成功过，直接返回
            infoOne = spendInfoList.get(0);
        }else {
            //调用百行征信接口
            CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                    .eq(CaseCustInfo::getApplyNo, applyNo)
                    .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            AfsUser user = SecurityUtils.getUser();
            BaiHangQueryReq baiHangQueryReq = new BaiHangQueryReq();
            baiHangQueryReq.setEdsOperateusername(user.getUserRealName());
            baiHangQueryReq.setEdsOperateuserid(user.getUsername());
            BaiHangQueryReq.EdsParams edsParams = new BaiHangQueryReq.EdsParams();
            edsParams.setMobile(custInfo.getTelPhone());
            edsParams.setIdCard(custInfo.getCertNo());
            baiHangQueryReq.setEdsParams(edsParams);

            IResponse<BaiHangQueryRsp> iResponse = baiHangService.baiHangQuery(baiHangQueryReq);
            Assert.isTrue("0000".equals(iResponse.getCode()), iResponse.getMsg());
            BaiHangQueryRsp data = iResponse.getData();
            BaiHangSpendInfo baiHangSpendInfo = new BaiHangSpendInfo();
            baiHangSpendInfo.setApplyNo(applyNo);
            baiHangSpendInfo.setRiskLevel(data.getData().get(0).getRiskLevel());
            baiHangSpendInfo.setReason(data.getData().get(0).getReason());
            baiHangSpendInfo.setScoreId(data.getData().get(0).getScoreID());
            baiHangSpendInfo.setRetCode(data.getData().get(0).getRetCode());
            baiHangSpendInfoService.save(baiHangSpendInfo);
            infoOne = baiHangSpendInfo;
        }
        if (StrUtil.isNotBlank(infoOne.getReason())) {
            StringBuilder builder = new StringBuilder();
            String[] split = infoOne.getReason().split(",");
            for (int i = 0; i < split.length; i++) {
                String reasonCode = split[i];
                BaiHangLevelEnum levelEnum = BaiHangLevelEnum.create(reasonCode);
                if (i == split.length - 1) {
                    builder.append(levelEnum == null ? reasonCode : levelEnum.getDesc());
                } else {
                    builder.append(levelEnum == null ? reasonCode : levelEnum.getDesc()).append(",");
                }
            }
            infoOne.setReason(builder.toString());
        }
        List<BaiHangSpendInfo> list = new ArrayList<>();
        list.add(infoOne);
        return IResponse.success(list);
    }

    /**
     * 百行征信普惠评分-消费能力等级-本地数据库查询
     * @return
     */
    @GetMapping("/baiHangLocal")
    public IResponse baiHangLocal(@RequestParam(value = "applyNo") String applyNo) {
        List<BaiHangSpendInfo> spendInfoList = baiHangSpendInfoService.list(Wrappers.<BaiHangSpendInfo>lambdaQuery()
                .eq(BaiHangSpendInfo::getApplyNo, applyNo)
                .orderByDesc(BaiHangSpendInfo::getCreateTime));
        if (spendInfoList.size() > 0) {
            BaiHangSpendInfo infoOne = spendInfoList.get(0);
            if (StrUtil.isNotBlank(infoOne.getReason())) {
                StringBuilder builder = new StringBuilder();
                String[] split = infoOne.getReason().split(",");
                for (int i = 0; i < split.length; i++) {
                    String reasonCode = split[i];
                    BaiHangLevelEnum levelEnum = BaiHangLevelEnum.create(reasonCode);
                    if (i == split.length - 1) {
                        builder.append(levelEnum == null ? reasonCode : levelEnum.getDesc());
                    } else {
                        builder.append(levelEnum == null ? reasonCode : levelEnum.getDesc()).append(",");
                    }
                }
                infoOne.setReason(builder.toString());
            }
            List<BaiHangSpendInfo> list = new ArrayList<>();
            list.add(infoOne);
            return IResponse.success(list);
        }
        return IResponse.success(null);
    }

    @PostMapping("/updateManualReviewInformation")
    public IResponse updateManualReviewInformation(@RequestBody List<ManualReviewInformationVo> list){
        if (CollectionUtil.isNotEmpty(list)){
            for (ManualReviewInformationVo vo : list){
                ManualReviewInformation manualReviewInformation = new ManualReviewInformation();
                manualReviewInformation.setId(Long.valueOf(vo.getId()));
                manualReviewInformation.setApplyNo(vo.getApplyNo());
                manualReviewInformation.setIsAbnormal(vo.getIsAbnormal());
                manualReviewInformationService.updateById(manualReviewInformation);
            }
        }
        return IResponse.success("保存成功");
    }

    @PostMapping("/checkReviewInformation")
    public IResponse checkReviewInformation(@RequestBody ManualReviewInformation information){
        boolean flag = true;
        List<ManualReviewInformation> list = manualReviewInformationService.list(Wrappers.<ManualReviewInformation>lambdaQuery()
                .eq(ManualReviewInformation::getApplyNo,information.getApplyNo())
                .isNull(ManualReviewInformation::getIsAbnormal));
        if (list.size() > 0){
            flag = false;
        }
        return IResponse.success(flag);
    }
}
