package com.ruicar.afs.cloud.afscase.autoaudit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.autoaudit.annotations.AuditField;
import com.ruicar.afs.cloud.afscase.autoaudit.condition.CardDetectRecordChangeCondition;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.AttachmentAtomBusinessRel;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.AttachmentAuditAtom;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.BusinessManualReviewInfo;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.CardDetectRecordChange;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.CaseManualReviewInfo;
import com.ruicar.afs.cloud.afscase.autoaudit.entity.PropertyLicenseAiRecognition;
import com.ruicar.afs.cloud.afscase.autoaudit.mapper.AttachmentAtomBusinessRelMapper;
import com.ruicar.afs.cloud.afscase.autoaudit.mapper.AttachmentAuditAtomMapper;
import com.ruicar.afs.cloud.afscase.autoaudit.mapper.CaseManualReviewInfoMapper;
import com.ruicar.afs.cloud.afscase.autoaudit.service.AttachmentAuditInformationService;
import com.ruicar.afs.cloud.afscase.autoaudit.service.BusinessManualReviewInfoService;
import com.ruicar.afs.cloud.afscase.autoaudit.service.CardDetectRecordChangeService;
import com.ruicar.afs.cloud.afscase.autoaudit.service.PropertyLicenseAiRecognitionService;
import com.ruicar.afs.cloud.afscase.autoaudit.vo.AttachmentAuditAtomVo;
import com.ruicar.afs.cloud.afscase.autoaudit.vo.AttachmentAuditInfoVo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.CaseBusinessLicenseInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekDrivingInfo;
import com.ruicar.afs.cloud.afscase.caseocr.entity.DeepSeekOperatorInfo;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseApplyResidenceService;
import com.ruicar.afs.cloud.afscase.caseocr.service.CaseBusinessLicenseInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekDrivingInfoService;
import com.ruicar.afs.cloud.afscase.caseocr.service.DeepSeekOperatorInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelAffiliatedUnits;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelAffiliatedUnitsService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.BusinessLicenseApply;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.BusinessLicenseApprove;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.BusinessLicenseApproveBase;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.BusinessLicenseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBusinessLicense;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBusinessLicenseArtificial;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarStyleDetail;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelUniteInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseFacePhotoInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.enums.DeepSeekFileType;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseBusinessLicenseArtificialMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseBusinessLicenseMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarStyleDetailService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelUniteInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseFacePhotoInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInsuranceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.CarInvoiceInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.entity.LoanBankCardInfo;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInsuranceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.CarInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanBankCardInfoService;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyFinancingItemsService;
import com.ruicar.afs.cloud.bizcommon.business.service.FinMainInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AddressTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsDefaultDeductCardEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.feign.AddressFeign;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.deepseek.entity.AttorneyAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.BusinessAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.CertificateAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.entity.StatementAutomaticRecognition;
import com.ruicar.afs.cloud.deepseek.enums.CheckTypeEnum;
import com.ruicar.afs.cloud.deepseek.mapper.StatementAutomaticRecognitionMapper;
import com.ruicar.afs.cloud.deepseek.service.AttorneyAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.BusinessAutomaticRecognitionService;
import com.ruicar.afs.cloud.deepseek.service.CertificateAutomaticRecognitionService;
import com.ruicar.afs.cloud.enums.common.YesOrNoEnum;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.entity.SignInterviewInfo;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.image.service.SignInterviewInfoService;
import com.ruicar.afs.cloud.parameter.commom.enums.CarPurpose;
import com.ruicar.afs.cloud.parameter.commom.enums.CostType;
import com.ruicar.afs.cloud.parameter.commom.enums.CustType;
import com.ruicar.afs.cloud.parameter.commom.enums.InsuranceTypeEnums;
import com.ruicar.afs.cloud.seats.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/23 9:49
 */
@Service
@Slf4j
@AllArgsConstructor
public class AttachmentAuditInformationServiceImpl implements AttachmentAuditInformationService {

    private final CaseBaseInfoService caseBaseInfoService;
    private final CaseCustInfoService caseCustInfoService;
    private final CarInvoiceInfoService carInvoiceInfoService;
    private final LoanBankCardInfoService loanBankCardInfoService;
    private final CaseCarInfoService caseCarInfoService;
    private final CarInsuranceInfoService carInsuranceInfoService;
    private final ChannelAffiliatedUnitsService channelAffiliatedUnitsService;
    private final AttachmentAuditAtomMapper attachmentAuditAtomMapper;
    private final AttachmentAtomBusinessRelMapper attachmentAtomBusinessRelMapper;
    private final ApplyCostDetailsService applyCostDetailsService;
    private final ApplyFinancingItemsService applyFinancingItemsService;
    private final SignInterviewInfoService signInterviewInfoService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final CaseChannelUniteInfoService caseChannelUniteInfoService;
    private final CaseContractInfoService caseContractInfoService;
    private final FinMainInfoService finMainInfoService;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final CaseCustAddressService caseCustAddressService;
    private final AddressService addressService;
    private final CaseCarStyleDetailService carStyleDetailInfoService;
    private final CaseCostInfoService caseCostInfoService;
    private final CaseBusinessLicenseInfoService caseBusinessLicenseInfoService;
    private final CardDetectRecordChangeService cardDetectRecordChangeService;
    private CaseUseApplyServiceFeign applyServiceFeign;
    private ApplyServiceFeign tApplyServiceFeign;
    private final CaseFacePhotoInfoService facePhotoInfoService;
    private CaseApplyResidenceService caseApplyResidenceService;
    private StatementAutomaticRecognitionMapper statementAutomaticRecognitionMapper;
    private final AddressFeign addressFeign;
    private CaseManualReviewInfoMapper caseManualReviewInfoMapper;
    private final DeepSeekDrivingInfoService deepSeekDrivingInfoService;
    private final DeepSeekOperatorInfoService deepSeekOperatorInfoService;
    private final PropertyLicenseAiRecognitionService propertyLicenseAiRecognitionService;
    private final CaseBusinessLicenseMapper caseBusinessLicenseMapper;
    private final CaseBusinessLicenseArtificialMapper caseBusinessLicenseArtificialMapper;
    private BusinessAutomaticRecognitionService businessAutomaticRecognitionService;
    private BusinessManualReviewInfoService businessManualReviewInfoService;
    private AttorneyAutomaticRecognitionService attorneyAutomaticRecognitionService;
    private CertificateAutomaticRecognitionService certificateAutomaticRecognitionService;

    /**
     * 获取附件核对信息
     *
     * @param busiNo
     * @param attachmentCode
     * @param fileId
     * @return
     */
    @Override
    public List<String> getAttachmentAuditInformation(String busiNo, String attachmentCode, String fileId) {
        List<String> list = new ArrayList<>();

        List<AttachmentAuditAtomVo> vo = attachmentAuditAtomMapper.queryAtomsByAttachmentId(Long.valueOf(attachmentCode));
        if (CollectionUtil.isEmpty(vo)) {
            return list;
        }
        Map map = handleInfo(busiNo, fileId);
        if (CollectionUtil.isEmpty(map)) {
            return list;
        }
        List<CaseCustInfo> caseCustInfos = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, busiNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));

        vo.stream().forEach(s -> {
            if ("guarantorName".equals(s.getAtomKey())) {
                caseCustInfos.stream().forEach(caseCustInfo -> {
                    list.add(s.getShowName() + caseCustInfo.getCustName());
                });
            } else if ("guarantorCertNo".equals(s.getAtomKey())) {
                caseCustInfos.stream().forEach(caseCustInfo -> {
                    list.add(s.getShowName() + caseCustInfo.getCertNo());
                });
            } else if ("guarantorDate".equals(s.getAtomKey())) {
                caseCustInfos.stream().forEach(caseCustInfo -> {
                    list.add(s.getShowName() + DateUtil.formatDate(caseCustInfo.getCertEndDate()) + "  身份到期大于30天");
                    //承租人身份证到期提示
                    if (cn.hutool.core.date.DateUtil.betweenDay(new Date(), caseCustInfo.getCertEndDate(), false) < 30) {
                        list.add(s.getShowName() + DateUtil.formatDate(caseCustInfo.getCertEndDate()) + "  身份到期小于30天");
                    }
                });
            } else if ("interviewInfo".equals(s.getAtomKey())) {
                //获取面签信息
                List<ComAttachmentFile> comAttachmentFileList = comAttachmentFileService.list(new LambdaQueryWrapper<ComAttachmentFile>()
                        .eq(ComAttachmentFile::getBusiNo, busiNo)
                        .eq(ComAttachmentFile::getAttachmentCode, attachmentCode));
                log.info("comAttachmentFileList===" + comAttachmentFileList);

                List<String> ids = comAttachmentFileList.stream().map(item -> {
                    return item.getId().toString();
                }).collect(Collectors.toList());

                List<SignInterviewInfo> signInterviewInfoList = signInterviewInfoService.list(new QueryWrapper<SignInterviewInfo>().in("com_attachement_file_id", ids));
                log.info("signInterviewInfoList===" + signInterviewInfoList);
                //封装面签信息
                signInterviewInfoList.forEach(item -> {
                    String custRole = item.getCustRole();
                    String custType = item.getCustType();
                    String preCustName = null;
                    if (CaseConstants.PRINCIPAL_BORROWER.equals(custRole) && CustType.PERSON.getIndex().equals(custType)) {
                        preCustName = "主借人";
                    } else if (CaseConstants.PRINCIPAL_BORROWER.equals(custRole) && CustType.COMPANY.getIndex().equals(custType)) {
                        preCustName = "主借企业法人";
                    } else if (CaseConstants.GUARANTOR.equals(custRole) && CustType.PERSON.getIndex().equals(custType)) {
                        preCustName = "担保人";
                    } else if (CaseConstants.GUARANTOR.equals(custRole) && CustType.COMPANY.getIndex().equals(custType)) {
                        preCustName = "企业担保法人";
                    }
                    list.add(preCustName + "姓名:" + item.getCustName());
                    String faceStatus = "1".equals(item.getFaceStatus()) ? "不通过" : "通过";
                    list.add("人脸识别:" + faceStatus);
                });
            } else if (map.get(s.getAtomKey()) != null) {
                list.add(s.getShowName() + map.get(s.getAtomKey()));
            }
        });
        log.info("list====" + list);
        return list;
    }

    /**
     *获取信审附件核对信息
     * @param busiNo
     * @param attachmentCode
     * @param refreshOcr 是否是重新调用身份证OCR
     * @param intelligentRecognition
     * @return
     */
    @Override
    public List<String> getAttachmentCreditInformation(String busiNo, String attachmentCode, String fileId, String refreshOcr,String intelligentRecognition)  {
        List<String> list = new ArrayList<>();
        List<ComAttachmentManagement> managements = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().eq(ComAttachmentManagement::getId, Long.valueOf(attachmentCode)));
        if (CollectionUtil.isEmpty(managements)) {
            return list;
        }
        List<CaseCustInfo> caseCustInfos = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, busiNo)
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");

        //车辆信息
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, busiNo), false);
        String uniqueCode = managements.get(0).getUniqueCode();
        String idCardBelongCustRole = getIdCardBelongCustRole(managements);
        if (StrUtil.isNotBlank(idCardBelongCustRole)) {
            log.info("信审身份证OCR,当前角色={}", idCardBelongCustRole);
            //承租人、保证人身份证
            boolean mainCustRoleFlag = true; // 是否承租人
            CaseCustInfo caseCustInfoGuarantor = new CaseCustInfo();
            if (StrUtil.equals(CustRoleEnum.GUARANTOR.getCode(), idCardBelongCustRole)) {
                mainCustRoleFlag = false;
                caseCustInfoGuarantor = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                                .eq(CaseCustInfo::getApplyNo, busiNo)
                                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));
            }
            // 总 applyInfo 原来的返回字段 、approveInfo 智能识别的返回字段 、caseManualReviewInfo 信审人员编辑后的返回字段
            JSONObject res = new JSONObject();
            JSONObject applyInfo = new JSONObject(true);
            CaseCustInfo caseCustInfo = caseCustInfos.get(0);
            applyInfo.put("客户姓名", mainCustRoleFlag ? caseCustInfo.getCustName():caseCustInfoGuarantor.getCustName());
            applyInfo.put("身份证号码", mainCustRoleFlag ? caseCustInfo.getCertNo():caseCustInfoGuarantor.getCertNo());
            applyInfo.put("证件到期日", ft.format(mainCustRoleFlag ? caseCustInfo.getCertEndDate():caseCustInfoGuarantor.getCertEndDate()));

            //户籍所在地
            List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, mainCustRoleFlag? caseCustInfo.getId():caseCustInfoGuarantor.getId())
                    .eq(CaseCustAddress::getAddressType, AddressTypeEnum.CENSUS.getCode()));

            if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                CaseCustAddress custAddress = caseCustAddressList.get(0);
                List<String> codes = new ArrayList<>();
                codes.add(custAddress.getProvince());
                codes.add(custAddress.getCity());
                codes.add(custAddress.getStreet());
                codes.add(custAddress.getCounty());
                Map<String, String> addrTemp = addressService.getCodeLabelMap(codes);
                String addr = custAddress.getDetailAddress() != null ? custAddress.getDetailAddress() : "";
                String pro = addrTemp.get(custAddress.getProvince());
                String cityAddr = addrTemp.get(custAddress.getCity());
                String street = addrTemp.get(custAddress.getStreet());
                String county = addrTemp.get(custAddress.getCounty());
                String address = pro + cityAddr + street + county + addr;

                applyInfo.put("户籍所在地", address);
            }

            res.put("apply", applyInfo);

            JSONObject approveInfo = new JSONObject();
            CardDetectRecordChangeCondition cardDetectRecordChangeCondition = new CardDetectRecordChangeCondition();
            cardDetectRecordChangeCondition.setRefreshOcr(refreshOcr);
            cardDetectRecordChangeCondition.setAttachmentCode(attachmentCode);
            cardDetectRecordChangeCondition.setApplyNo(busiNo);
            cardDetectRecordChangeCondition.setIsUpdate(CommonConstants.COMMON_NO);
            buildJsonInfoForIdCard(approveInfo, cardDetectRecordChangeCondition);
            res.put("approve", approveInfo);

            JSONObject caseManualReviewInfo = new JSONObject();
            cardDetectRecordChangeCondition.setIsUpdate(CommonConstants.COMMON_YES);
            // 信审人员编辑后的 一定不是刷新和查预审批的，避免临时刷新身份证OCR导致多次保存数据
            cardDetectRecordChangeCondition.setRefreshOcr(CommonConstants.COMMON_NO);
            cardDetectRecordChangeCondition.setDirectQueryPre(CommonConstants.COMMON_NO);
            buildJsonInfoForIdCard(caseManualReviewInfo, cardDetectRecordChangeCondition);
            res.put("caseManualReviewInfo", caseManualReviewInfo);

            for (Map.Entry<String, Object> entry : res.entrySet()) {
                list.add(entry.getKey()+"："+entry.getValue());
            }
        } else if (AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode().equals(uniqueCode)) {//驾驶证
            // 查询驾驶证相关数据信息
            DeepSeekDrivingInfo seekDrivingInfo = deepSeekDrivingInfoService.getOne(Wrappers.<DeepSeekDrivingInfo>lambdaQuery()
                    .eq(DeepSeekDrivingInfo::getApplyNo, busiNo)
                    .eq(DeepSeekDrivingInfo::getFileId, Long.parseLong(fileId)));
            if (seekDrivingInfo != null) {
                String driverResult = "ds".equals(seekDrivingInfo.getAnalysisType()) ? seekDrivingInfo.getDsResult() : seekDrivingInfo.getOcrResult();
                JSONObject driverRes = StrUtil.isBlank(driverResult) ? new JSONObject() : JSONObject.parseObject(driverResult);
                driverRes.put("caseManualReviewInfo", seekDrivingInfo.getManualReviewResults());
                for (Map.Entry<String, Object> entry : driverRes.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.OPERATOR_CERTIFICATE.getCode().equals(managements.get(0).getUniqueCode())) {
            // 查询营运人证相关数据信息
            DeepSeekOperatorInfo seekOperatorInfo = deepSeekOperatorInfoService.getOne(Wrappers.<DeepSeekOperatorInfo>lambdaQuery()
                    .eq(DeepSeekOperatorInfo::getApplyNo, busiNo)
                    .eq(DeepSeekOperatorInfo::getFileId, Long.parseLong(fileId)));
            if (seekOperatorInfo != null) {
                JSONObject operatorRes = StrUtil.isBlank(seekOperatorInfo.getDsResult()) ? new JSONObject() : JSONObject.parseObject(seekOperatorInfo.getDsResult());
                operatorRes.put("caseManualReviewInfo", seekOperatorInfo.getManualReviewResults());
                for (Map.Entry<String, Object> entry : operatorRes.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.APPLY_RESIDENCE.getCode().equals(uniqueCode)) {
            JSONObject res = new JSONObject();
            PropertyLicenseAiRecognition propertyLicenseAiRecognition = propertyLicenseAiRecognitionService.getOne(Wrappers.<PropertyLicenseAiRecognition>lambdaQuery()
                    .eq(PropertyLicenseAiRecognition::getApplyNo, busiNo)
                    .eq(PropertyLicenseAiRecognition::getAuditType,"intelligent"));
            if (EmptyUtils.isNotEmpty(propertyLicenseAiRecognition)) {
                JSONObject jsonObject = new JSONObject();
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    String replace = propertyLicenseAiRecognition.getResult().replace("：", "");
                    ObjectNode   jsonNode = (ObjectNode)mapper.readTree(replace);
                    jsonNode.remove("权属地");
                    jsonNode.remove("不动产单元号");
                    jsonNode.remove("权利类型");
                    jsonNode.remove("面积");
                    jsonNode.remove("使用期限");
                    jsonNode.remove("权利其他状况");
                    String valueAsString = mapper.writeValueAsString((jsonNode));
                    res.put("apply", valueAsString);
                }catch (JsonProcessingException e){
                    throw new AfsBaseException("json转换异常"+e.getMessage());
                }
                DeepSeekFileType seekFileType = DeepSeekFileType.getByDesc(propertyLicenseAiRecognition.getFileType());
                buildJsonInfoFromAnnotatedFields(seekFileType,jsonObject,propertyLicenseAiRecognition);
                res.put("approve", jsonObject);
                JSONObject estate = new JSONObject();
                estate.put("文件类型", propertyLicenseAiRecognition.getFileType());
                res.put("estate", estate);

                PropertyLicenseAiRecognition manual = propertyLicenseAiRecognitionService.getOne(Wrappers.<PropertyLicenseAiRecognition>lambdaQuery()
                        .eq(PropertyLicenseAiRecognition::getApplyNo, busiNo)
                        .eq(PropertyLicenseAiRecognition::getAuditType,"manual"));
                if(EmptyUtils.isNotEmpty(manual)){
                    JSONObject manualJsonObject = new JSONObject();
                    buildJsonInfoFromAnnotatedFields(seekFileType,manualJsonObject,manual);
                    res.put("manualJsonObject", manualJsonObject);
                }

                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    list.add(entry.getKey() + "：" + entry.getValue());
                }
            }
        } else if (StrUtil.equals(managements.get(0).getAttachmentName(), "面签照") && StrUtil.equals(
            managements.get(0).getBusiNode(), "orderApply")) {
            // 面签照信息
            List<CaseFacePhotoInfo> listed = facePhotoInfoService.lambdaQuery()
                .eq(CaseFacePhotoInfo::getApplyNo, busiNo)
                .eq(CaseFacePhotoInfo::getFileId, fileId)
                .orderByDesc(CaseFacePhotoInfo::getCreateTime)
                .list();
            if (CollUtil.isNotEmpty(listed)) {
                Map<String, CaseFacePhotoInfo> map = listed.stream()
                    .collect(Collectors.toMap(CaseFacePhotoInfo::getType, Function.identity(), (s1, s2) -> s2));
                // applyInfo 原来的返回字段 、approveInfo 智能识别的返回字段 、caseManualReviewInfo 信审人员编辑后的返回字段
                JSONObject res = new JSONObject();
                JSONObject applyInfo = new JSONObject(true);
                CaseFacePhotoInfo ds = map.get("ds");
                if (ds != null && ds.getDsResult()!=null&&ds.getDsResult()) {
                    applyInfo.put("deepseek识别","成功");
                    applyInfo.put("水印提取日期时间",ds.getPhotoDateTime());
                    applyInfo.put("水印提取城市",ds.getCity());
                    applyInfo.put("水印提取具体地点",ds.getPlace());
                } else {
                    applyInfo.put("deepseek识别","失败");
                    if (ds != null && Boolean.FALSE.equals(ds.getDsResult())) {
                        applyInfo.put("deepseek识别内容",ds.getResult());
                    }
                }
                CaseFacePhotoInfo face = map.get("face");
                if (face != null) {
                    applyInfo.put("人脸识别时间",cn.hutool.core.date.DateUtil.formatDateTime(face.getIdentifyDate()));
                    applyInfo.put("人脸识别结果",StrUtil.equals(face.getFaceResult(),"1")?"一致":StrUtil.equals(face.getFaceResult(),"0")?"不一致":"识别失败");
                } else {
                    applyInfo.put("人脸识别时间","无");
                }
                res.put("apply", applyInfo);

                JSONObject approveInfo = new JSONObject();
                Boolean approveSame = buildJsonInfoForFacePhoto(approveInfo, ds, face, false);
                JSONObject caseManualReviewInfo = new JSONObject();
                buildJsonInfoForFacePhoto(caseManualReviewInfo, ds, face, true);
                // 人工结果优先
                approveInfo.put("isPass", Map.of(
                    "label","智能识别结果",
                    "value", approveSame?"1":"0"
                ));

                res.put("approve", approveInfo);
                res.put("caseManualReviewInfo", caseManualReviewInfo);
                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }

            }
        } else if (AttachmentUniqueCodeEnum.VEHICLE_PHOTOS.getCode().equals(uniqueCode)
                || "carPhotos".equals(uniqueCode)) {//车辆照片
            if (CollectionUtil.isNotEmpty(caseCustInfos)) {
                list.add("客户姓名：" + caseCustInfos.get(0).getCustName());
            }
            if (ObjectUtils.isNotEmpty(caseCarInfo)) {
                list.add("车辆颜色：" + caseCarInfo.getCarColor());
                list.add("品牌：" + caseCarInfo.getBrandName());
                list.add("车型：" + caseCarInfo.getModelName());
                list.add("Vin码：" + caseCarInfo.getCarVin());
            }
        } else if (AttachmentUniqueCodeEnum.MOTOR_VEHICLE_REGISTRATION.getCode().equals(uniqueCode)) {//车辆登记证

            if (CollectionUtil.isNotEmpty(caseCustInfos)) {
                list.add("客户姓名：" + caseCustInfos.get(0).getCustName());
                list.add("客户证件号码：" + caseCustInfos.get(0).getCertNo());
            }
            if (ObjectUtils.isNotEmpty(caseCarInfo)) {
                list.add("车架号：" + caseCarInfo.getCarVin());
                list.add("发动机号：" + caseCarInfo.getEngineNo());
                CaseCarStyleDetail caseCarStyleDetail = carStyleDetailInfoService.getOne(Wrappers.<CaseCarStyleDetail>query().lambda()
                        .eq(CaseCarStyleDetail::getCarId, caseCarInfo.getId()));
                if (ObjectUtils.isNotEmpty(caseCarStyleDetail)) {
                    String isGreen = "否";
                    if ("1".equals(caseCarStyleDetail.getIsGreen())) {
                        isGreen = "是";
                    }
                    list.add("是否新能源：" + isGreen);
                }

                //车龄
                if (ObjectUtils.isEmpty(caseCarInfo.getFirstLandingDate())) {
                    list.add("车辆颜色：" + caseCarInfo.getCarColor());
                    list.add("初登日期：" + ft.format(caseCarInfo.getFirstLandingDate()));
                    list.add("车龄：" + "");
                    list.add("车加融年限：" + "");
                } else {
                    int day = this.dateDay(caseCarInfo.getFirstLandingDate(), new Date());
                    float year = (float) day / 365;
                    BigDecimal carYear = new BigDecimal(year).setScale(1, BigDecimal.ROUND_UP);//保留一位，向上取整
                    log.info("信审影像件风险提示车龄计算:" + carYear);

                    list.add("车辆颜色：" + caseCarInfo.getCarColor());
                    list.add("初登日期：" + ft.format(caseCarInfo.getFirstLandingDate()));
                    list.add("车龄：" + carYear);

                    //返回结果加入融资期限
                    FinCostDetails finInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                            .eq(FinCostDetails::getApplyNo, busiNo)
                    );
                    if (ObjectUtils.isNotEmpty(finInfo)) {
                        String ageTerm = this.getAgeTrem(carYear, finInfo.getLoanTerm());
                        list.add("车加融年限：" + ageTerm);
                    }
                }

            }

        } else if (AttachmentUniqueCodeEnum.ECAR_ASSESS_REPORT.getCode().equals(uniqueCode)) {//车辆评估报告
            if (CollectionUtil.isNotEmpty(caseCustInfos)) {
                list.add("客户姓名：" + caseCustInfos.get(0).getCustName());
            }
            if (ObjectUtils.isNotEmpty(caseCarInfo)) {
                list.add("评估价格：" + caseCarInfo.getEvaluatingPrice());
            }
        } else if (AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode().equals(uniqueCode)
                || AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode().equals(uniqueCode)) {//保证人身份证

            List<CaseCustInfo> caseCustInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                    .eq(CaseCustInfo::getApplyNo, busiNo)
                    .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));

            if (CollectionUtil.isNotEmpty(caseCustInfoList)) {
                list.add("担保人姓名：" + caseCustInfoList.get(0).getCustName());
                list.add("身份证号码：" + caseCustInfoList.get(0).getCertNo());
                list.add("证件到期日：" + ft.format(caseCustInfoList.get(0).getCertEndDate()));
            }
        } else if (AttachmentUniqueCodeEnum.BUSINESS_LICENSE.getCode().equals(uniqueCode)&& !AfsEnumUtil.key(YesOrNoEnum.YES).equals(intelligentRecognition)) {//营业执照
            CaseBusinessLicenseInfo caseBusinessLicenseInfo = caseBusinessLicenseInfoService.getOne(Wrappers.<CaseBusinessLicenseInfo>query().lambda()
                    .eq(CaseBusinessLicenseInfo::getApplyNo, caseCarInfo.getApplyNo()));
            if (ObjectUtils.isNotEmpty(caseBusinessLicenseInfo)) {
                list.add("机构名称：" + caseBusinessLicenseInfo.getAffiliatedName());
                list.add("统一社会信用代码：" + caseBusinessLicenseInfo.getSocUniCrtCode());
                list.add("法定代表人：" + caseBusinessLicenseInfo.getLegalName());
                list.add("地址/住所：" + caseBusinessLicenseInfo.getAddress());
                list.add("成立时间：" + (ObjectUtil.isNotEmpty(caseBusinessLicenseInfo.getEstablishmentTime()) ? ft.format(caseBusinessLicenseInfo.getEstablishmentTime()) : ""));
                list.add("营业期限：" + caseBusinessLicenseInfo.getPeriodRel());
            }
        } else if (AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode().equals(uniqueCode)) {
            //收入材料
            StatementAutomaticRecognition statementAutomaticRecognition = statementAutomaticRecognitionMapper.selectOne(Wrappers.lambdaQuery(StatementAutomaticRecognition.class)
                    .eq(StatementAutomaticRecognition::getApplyNo, busiNo)
                    .eq(StatementAutomaticRecognition::getCheckType, CheckTypeEnum.STATEMENT.getCode())
                    .eq(StatementAutomaticRecognition::getFileId, fileId));
            if (ObjectUtil.isEmpty(statementAutomaticRecognition)) {
                statementAutomaticRecognition = statementAutomaticRecognitionMapper.selectOne(Wrappers.lambdaQuery(StatementAutomaticRecognition.class)
                        .eq(StatementAutomaticRecognition::getApplyNo, busiNo)
                        .eq(StatementAutomaticRecognition::getCheckType, CheckTypeEnum.STATEMENT.getCode())
                        .isNull(StatementAutomaticRecognition::getFileId)
                        .last("limit 1"));
            }
            if (statementAutomaticRecognition != null) {
                String jsonStr = statementAutomaticRecognition.getResult();
                JSONObject res = JSONObject.parseObject(jsonStr);
                //设置信审人工审核结果
                CaseManualReviewInfo caseManualReviewInfo = caseManualReviewInfoMapper.selectOne(Wrappers.lambdaQuery(CaseManualReviewInfo.class)
                        .eq(CaseManualReviewInfo::getApplyNo, busiNo)
                        .eq(CaseManualReviewInfo::getFileId, fileId));
                if (ObjectUtil.isEmpty(caseManualReviewInfo)) {
                    caseManualReviewInfo = caseManualReviewInfoMapper.selectOne(Wrappers.lambdaQuery(CaseManualReviewInfo.class)
                            .eq(CaseManualReviewInfo::getApplyNo, busiNo)
                            .isNull(CaseManualReviewInfo::getFileId)
                            .last("limit 1"));
                }
                if (caseManualReviewInfo != null) {
                    res.put("caseManualReviewInfo", caseManualReviewInfo.getManualReviewResults());
                }
                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.BUSINESS_LICENSE.getCode().equals(uniqueCode)&&AfsEnumUtil.key(YesOrNoEnum.YES).equals(intelligentRecognition)) {
            //营业执照识别结果
            CaseBusinessLicense caseBusinessLicense = caseBusinessLicenseMapper.selectOne(Wrappers.lambdaQuery(CaseBusinessLicense.class)
                    .eq(CaseBusinessLicense::getApplyNo,busiNo));
            if (caseBusinessLicense!=null) {
                //进件信息-基础信息
                BusinessLicenseApply apply = new BusinessLicenseApply();
                apply.setUnitName(caseBusinessLicense.getUnitName());
                apply.setSocUniCrtCode(caseBusinessLicense.getSocUniCrtCode());
                apply.setAffiliatedName(caseBusinessLicense.getAffiliatedName());
                //智能识别结果-基本信息
                BusinessLicenseApproveBase base = new BusinessLicenseApproveBase();
                //智能识别结果-具体识别信息
                BusinessLicenseApprove approve = new BusinessLicenseApprove();
                approve.setUnitNameComparison(caseBusinessLicense.getUnitNameComparison());
                approve.setAffiliatedNameComparison(caseBusinessLicense.getAffiliatedNameComparison());
                approve.setValidityDateComparison(caseBusinessLicense.getValidityDateComparison());
                approve.setOfficialSeal(caseBusinessLicense.getOfficialSeal());
                approve.setOfficialSealLetteringComparison(caseBusinessLicense.getOfficialSealLetteringComparison());
                approve.setCopyOrScan(caseBusinessLicense.getCopyOrScan());
                approve.setIsEstablishmentTime(caseBusinessLicense.getIsEstablishmentTime());
                //智能识别结果-人工操作结果
                CaseBusinessLicenseArtificial artificial = caseBusinessLicenseArtificialMapper.selectOne(Wrappers.lambdaQuery(CaseBusinessLicenseArtificial.class)
                        .eq(CaseBusinessLicenseArtificial::getApplyNo,busiNo));
                //汇总输出
                BusinessLicenseInfo licenseInfo = new BusinessLicenseInfo(apply,base,approve,artificial);
                licenseInfo.generateFinalRes();
                JSONObject res = JSONObject.parseObject(JSONObject.toJSONString(licenseInfo));
                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.OPERATING_LICENSE.getCode().equals(uniqueCode)) {
            //经营许可证
            BusinessAutomaticRecognition businessAutomaticRecognition = businessAutomaticRecognitionService.getOne(Wrappers.<BusinessAutomaticRecognition>lambdaQuery()
                    .eq(BusinessAutomaticRecognition::getApplyNo,busiNo)
                    .eq(BusinessAutomaticRecognition::getFileId, fileId));
            if (ObjectUtil.isNotEmpty(businessAutomaticRecognition)){
                String jsonStr = businessAutomaticRecognition.getResult();
                JSONObject res = JSONObject.parseObject(jsonStr);
                BusinessManualReviewInfo businessManualReviewInfo = businessManualReviewInfoService.getOne(Wrappers.<BusinessManualReviewInfo>lambdaQuery()
                        .eq(BusinessManualReviewInfo::getApplyNo,busiNo)
                        .eq(BusinessManualReviewInfo::getFileId,fileId));
                if (ObjectUtil.isNotEmpty(businessManualReviewInfo)){
                    res.put("caseManualReviewInfo", businessManualReviewInfo.getManualReviewResults());
                }
                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.POWER_ATTORNEY.getCode().equals(uniqueCode)) {
            //委托授权书
            AttorneyAutomaticRecognition attorneyAutomaticRecognition = attorneyAutomaticRecognitionService.getOne(Wrappers.<AttorneyAutomaticRecognition>lambdaQuery()
                    .eq(AttorneyAutomaticRecognition::getApplyNo,busiNo)
                    .eq(AttorneyAutomaticRecognition::getFileId,fileId));
            if (ObjectUtil.isNotEmpty(attorneyAutomaticRecognition)){
                JSONObject object = new JSONObject();
                JSONObject res = JSONObject.parseObject(attorneyAutomaticRecognition.getOrderInfo());
                object.put("apply",res);
                JSONObject approve = new JSONObject();
                approve.put("合规性校验",attorneyAutomaticRecognition.getComplianceCheckIntelligent());
                approve.put("授权有效期是否满1年",attorneyAutomaticRecognition.getAuthorizationValidityIntelligent());
                approve.put("是否有印章",attorneyAutomaticRecognition.getHasSealIntelligent());
                approve.put("单位名称校验",attorneyAutomaticRecognition.getCompanyNameCheckIntelligent());
                approve.put("客户信息是否一致",attorneyAutomaticRecognition.getCustomerInfoConsistencyIntelligent());
                object.put("approve",approve);
                JSONObject info = new JSONObject();
                info.put("合规性校验",attorneyAutomaticRecognition.getComplianceCheckManual());
                info.put("授权有效期是否满1年",attorneyAutomaticRecognition.getAuthorizationValidityManual());
                info.put("是否有印章",attorneyAutomaticRecognition.getSealCheckManual());
                info.put("单位名称校验",attorneyAutomaticRecognition.getCompanyNameCheckManual());
                info.put("客户信息是否一致",attorneyAutomaticRecognition.getCustomerInfoConsistencyManual());
                object.put("caseManualReviewInfo",info);

                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        } else if (AttachmentUniqueCodeEnum.APPLY_CERTIFICATE.getCode().equals(uniqueCode)) {
            //证明书
            CertificateAutomaticRecognition certificateAutomaticRecognition = certificateAutomaticRecognitionService.getOne(Wrappers.<CertificateAutomaticRecognition>lambdaQuery()
                    .eq(CertificateAutomaticRecognition::getApplyNo,busiNo)
                    .eq(CertificateAutomaticRecognition::getFileId,fileId));
            if (ObjectUtil.isNotEmpty(certificateAutomaticRecognition)){
                JSONObject object = new JSONObject();
                JSONObject res = JSONObject.parseObject(certificateAutomaticRecognition.getOrderInfo());
                object.put("apply",res);
                JSONObject approve = new JSONObject();
                approve.put("合规性校验",certificateAutomaticRecognition.getComplianceCheckIntelligent());
                approve.put("签署时间是否合规",certificateAutomaticRecognition.getSigningTimeIntelligent());
                approve.put("是否有印章",certificateAutomaticRecognition.getHasSealIntelligent());
                approve.put("单位名称校验",certificateAutomaticRecognition.getCompanyNameCheckIntelligent());
                approve.put("客户信息是否一致",certificateAutomaticRecognition.getCustomerInfoConsistencyIntelligent());
                approve.put("收入是否一致",certificateAutomaticRecognition.getIncomeIntelligent());
                object.put("approve",approve);
                JSONObject info = new JSONObject();
                info.put("合规性校验",certificateAutomaticRecognition.getComplianceCheckManual());
                info.put("签署时间是否合规",certificateAutomaticRecognition.getSigningTimeManual());
                info.put("是否有印章",certificateAutomaticRecognition.getSealCheckManual());
                info.put("单位名称校验",certificateAutomaticRecognition.getCompanyNameCheckManual());
                info.put("客户信息是否一致",certificateAutomaticRecognition.getCustomerInfoConsistencyManual());
                info.put("收入是否一致",certificateAutomaticRecognition.getIncomeManual());
                object.put("caseManualReviewInfo",info);

                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    list.add(entry.getKey()+"："+entry.getValue());
                }
            }
        }
        log.info("信审审核信息返回list：" + list);
        return list;
    }

    /**
     * 是否属于身份证
     * @param managements managements
     * @return CustRoleEnum or null
     */
    public String getIdCardBelongCustRole(List<ComAttachmentManagement> managements) {
        String uniqueCode = managements.get(0).getUniqueCode();
        if (StrUtil.equalsAny(uniqueCode, AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode(), AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode())) {
            return CustRoleEnum.MIANCUST.getCode();
        }
        if (StrUtil.equalsAny(uniqueCode, AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode(), AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode())) {
            return CustRoleEnum.GUARANTOR.getCode();
        }
        return null;
    }

    /**
     * 身份证信息
     * @param approveInfo 审核信息
     * @param cardDetectRecordChangeCondition 条件
     */
    public void buildJsonInfoForIdCard(JSONObject approveInfo, CardDetectRecordChangeCondition cardDetectRecordChangeCondition) {
        IResponse<CardDetectRecordChange> cardDetectRecordChangeIResponse = cardDetectRecordChangeService.getByCondition(cardDetectRecordChangeCondition);
        CardDetectRecordChange cardDetectRecordChange = cardDetectRecordChangeIResponse.getData();
        if (ObjectUtil.isEmpty(cardDetectRecordChange)) {
            return;
        }
        if (StrUtil.equals(CommonConstants.COMMON_NO, cardDetectRecordChange.getIsUpdate())) {
            approveInfo.put("sameAsOwnName", Map.of(
                    "label","与本人姓名一致",
                    "value", cardDetectRecordChange.getSameAsOwnName()
            ));
            approveInfo.put("sameAsOwnIdCard", Map.of(
                    "label","与本人证件号一致",
                    "value", cardDetectRecordChange.getSameAsOwnIdCard()
            ));
            approveInfo.put("isPass", Map.of(
                    "label","智能识别结果",
                    "value", cardDetectRecordChange.getIsPass()
            ));
        }
        approveInfo.put("definitionDetectFlag", Map.of(
                "label","清晰度",
                "value", cardDetectRecordChange.getDefinitionDetectFlag()
        ));
        approveInfo.put("imageCompleteFlag", Map.of(
                "label","完整性",
                "value", cardDetectRecordChange.getImageCompleteFlag()
        ));
        approveInfo.put("spotDetectFlag", Map.of(
                "label","无光斑",
                "value", cardDetectRecordChange.getSpotDetectFlag()
        ));
        approveInfo.put("nonPicinpicFlag", Map.of(
                "label","非画中画",
                "value", cardDetectRecordChange.getNonPicinpicFlag()
        ));
        approveInfo.put("cardOriginalFlag", Map.of(
                "label","身份证原件",
                "value", cardDetectRecordChange.getCardOriginalFlag()
        ));
        approveInfo.put("imageOriginalFlag", Map.of(
                "label","原图",
                "value", cardDetectRecordChange.getImageOriginalFlag()
        ));
        approveInfo.put("psDetectFlag", Map.of(
                "label","非PS",
                "value", cardDetectRecordChange.getPsDetectFlag()
        ));
    }

    /**
     * 面签照信息
     * @param approveInfo 审核信息
     * @param ds 条件
     * @param face 条件
     */
    public Boolean buildJsonInfoForFacePhoto(JSONObject approveInfo, CaseFacePhotoInfo ds,CaseFacePhotoInfo face,Boolean human) {
        boolean result = true;
        if(face!=null){
            String  value;
            if(human){
                value = StrUtil.isNotBlank(face.getFaceResultHuman()) ? face.getFaceResultHuman() : "";
            }else {
                value = StrUtil.isNotBlank(face.getFaceResult()) ? face.getFaceResult() : "0";
            }
            if(StrUtil.equals(CommonConstants.COMMON_NO, value)){
                result = false;
            }
            approveInfo.put("faceResultHuman", Map.of(
                "label","人脸与身份证照片比对",
                "value", value
            ));

            String  value1;
            if(human){
                value1 = StrUtil.isNotBlank(face.getFaceResultHuman()) ? face.getFaceResultHuman() : "";
            }else {
                value1 = StrUtil.isNotBlank(face.getFaceResult()) ? "1" : "0";
            }
            if(StrUtil.equals(CommonConstants.COMMON_NO, value1)){
                result = false;
            }
            approveInfo.put("isTwoMan", Map.of(
                "label","是否两人同框",
                "value",value1
            ));
        } else {
            result = false;
        }
        if (ds != null) {
            String value1;
            if (human) {
                value1 = ds.getDsResultHuman() != null ? ds.getDsResultHuman() ? "1" : "0" : "";
            } else {
                value1 = ds.getDsResult() != null && ds.getDsResult() ? "1" : "0";
            }
            if (StrUtil.equals(CommonConstants.COMMON_NO, value1)) {
                result = false;
            }
            approveInfo.put("dsResultHuman", Map.of(
                "label","是否有水印定位",
                "value", value1
            ));
            String value2;
            if(human){
                value2 = StrUtil.isBlank(ds.getPhotoDateTimeHuman()) ? "" : ds.getPhotoDateTimeHuman();
            } else {
                value2 = StrUtil.isBlank(ds.getPhotoDateTime()) ? "0" : "1";
            }
            if(StrUtil.equals(CommonConstants.COMMON_NO, value2)){
                result = false;
            }
            approveInfo.put("photoDateTimeHuman", Map.of(
                "label","是否有时间",
                "value", value2
            ));
        }else {
            result = false;
        }
        return result;
    }

    private int dateDay(Date startDate, Date endDate) {
        Calendar calendarStartDate = Calendar.getInstance();
        Calendar calendarEndDate = Calendar.getInstance();
        calendarStartDate.setTime(startDate);
        calendarEndDate.setTime(endDate);
        if (startDate.after(endDate)) {
            Calendar swap = calendarStartDate;
            calendarStartDate = calendarEndDate;
            calendarEndDate = swap;
        }

        int days = calendarEndDate.get(6) - calendarStartDate.get(6);
        int y2 = calendarEndDate.get(1);

        while (calendarStartDate.get(1) < y2) {
            days += calendarStartDate.getActualMaximum(6);
            calendarStartDate.add(1, 1);
        }

        return days;
    }

    /**
     * 获取车加融年限
     *
     * @param carYear
     * @param loanTerm
     * @return
     */
    private String getAgeTrem(BigDecimal carYear, Integer loanTerm) {
        DecimalFormat df = new DecimalFormat("0.0");
        float num = (float) loanTerm / 12;
        String loanTermAge = df.format(num);

        BigDecimal ageTerm = carYear.add(new BigDecimal(loanTermAge));
        log.info("获取车加融年限：" + ageTerm);
        return ageTerm.toString();
    }


    @Override
    public Boolean addAttachmentAuditAtom(AttachmentAuditAtomVo attachmentAuditAtomVo) {
        this.check(attachmentAuditAtomVo);
        AttachmentAuditAtom attachmentAuditAtom = AttachmentAuditAtom.convertFromVo(attachmentAuditAtomVo);
        this.attachmentAuditAtomMapper.insert(attachmentAuditAtom);
        insetRelInfo(attachmentAuditAtomVo, attachmentAuditAtom);
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveFacePhotoReviewResults(CaseFacePhotoInfo facePhotoInfo) {
        return facePhotoInfoService.lambdaUpdate()
            .set(CaseFacePhotoInfo::getFaceResultHuman, facePhotoInfo.getFaceResultHuman())
            .set(CaseFacePhotoInfo::getDsResultHuman, facePhotoInfo.getDsResultHuman())
            .set(CaseFacePhotoInfo::getPhotoDateTimeHuman, facePhotoInfo.getPhotoDateTimeHuman())
            .eq(CaseFacePhotoInfo::getApplyNo,facePhotoInfo.getApplyNo())
            .eq(CaseFacePhotoInfo::getFileId,facePhotoInfo.getFileId())
            .update();
    }

    /**
     * 新增原子
     *
     * @param attachmentAuditAtomVo
     * @param attachmentAuditAtom
     */
    private void insetRelInfo(AttachmentAuditAtomVo attachmentAuditAtomVo, AttachmentAuditAtom attachmentAuditAtom) {
        if (attachmentAuditAtomVo.getRelList() != null) {
            attachmentAuditAtomVo.getRelList().forEach(id -> {
                AttachmentAtomBusinessRel attachmentAtomBusinessRel = new AttachmentAtomBusinessRel();
                attachmentAtomBusinessRel.setAtomId(attachmentAuditAtom.getId());
                attachmentAtomBusinessRel.setAttachmentId(id);
                attachmentAtomBusinessRelMapper.insert(attachmentAtomBusinessRel);
            });
        }
    }

    @Override
    public Boolean modifyAttachmentAuditAtom(AttachmentAuditAtomVo attachmentAuditAtomVo) {
        this.check(attachmentAuditAtomVo);
        AttachmentAuditAtom attachmentAuditAtomHis = this.attachmentAuditAtomMapper.selectById(attachmentAuditAtomVo.getId());
        AttachmentAuditAtom attachmentAuditAtom = AttachmentAuditAtom.convertFromVo(attachmentAuditAtomVo);
        BeanUtil.copyProperties(attachmentAuditAtom, attachmentAuditAtomHis, "createBy", "createTime", "id");
        this.attachmentAuditAtomMapper.updateById(attachmentAuditAtomHis);
        attachmentAtomBusinessRelMapper.delete(Wrappers.<AttachmentAtomBusinessRel>lambdaQuery().eq(AttachmentAtomBusinessRel::getAtomId, attachmentAuditAtomHis.getId()));
        insetRelInfo(attachmentAuditAtomVo, attachmentAuditAtomHis);
        return Boolean.TRUE;
    }

    @Override
    public IResponse query(QueryCondition<AttachmentAuditAtomVo> queryCondition) {
        return IResponse.success(this.attachmentAuditAtomMapper.queryAtoms(new Page(queryCondition.getPageNumber(), queryCondition.getPageSize()), queryCondition.getCondition()));
    }

    /**
     * 检查原子key是否重复
     *
     * @param attachmentAuditAtomVo
     * @return
     */
    private boolean check(AttachmentAuditAtomVo attachmentAuditAtomVo) {
        long i = attachmentAuditAtomMapper.selectCount(Wrappers.<AttachmentAuditAtom>lambdaQuery()
                .eq(AttachmentAuditAtom::getAtomKey, attachmentAuditAtomVo.getAtomKey())
                .ne(attachmentAuditAtomVo.getId() != null, AttachmentAuditAtom::getId, attachmentAuditAtomVo.getId()));
        Assert.isTrue(i <= 0, MessageFormat.format("原子key:[{0}]已存在,不可保存", attachmentAuditAtomVo.getAtomKey()));
        return Boolean.TRUE;
    }

    /**
     * 处理参数
     *
     * @return
     */
    private Map handleInfo(String busiNo, String fileId) {
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getCaseByApplyNo(busiNo);
        if (caseBaseInfo == null) {
            return null;
        }
        AttachmentAuditInfoVo vo = new AttachmentAuditInfoVo();
        //合作商
        vo.setChannelFullName(caseBaseInfo.getChannelFullName());
        //车商
        vo.setDealerName(caseBaseInfo.getDealerName());
        vo.setProductName(caseBaseInfo.getProductName());
        vo.setAffiliatedType(AffiliatedWayEnum.NO.getCode().equals(caseBaseInfo.getAffiliatedWay()) ? "否" : "是");
        vo.setLoanAmtRepeat(caseBaseInfo.getLoanAmtRepeat());
        vo.setAutoPass(caseBaseInfo.getAutoPass().equals(AfsEnumUtil.key(WhetherEnum.NO)) ? "否" : "是");
        //信审核准日期
        vo.setPassLastDate(caseBaseInfo.getPassLastDate());
        //购车目的
        vo.setCarPurpose(CarPurpose.getName(caseBaseInfo.getCarPurpose()));
        List<CaseCustInfo> list = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, busiNo));
        list.stream().forEach(caseCustInfo -> {
            if (CustRoleEnum.MIANCUST.getCode().equals(caseCustInfo.getCustRole())) {
                vo.setMiancustName(caseCustInfo.getCustName());
                vo.setMiancustCertNo(caseCustInfo.getCertNo());
                vo.setCustDate(DateUtil.formatDate(caseCustInfo.getCertEndDate()) + "  身份到期大于30天");
                //承租人身份证到期提示
                if (cn.hutool.core.date.DateUtil.betweenDay(new Date(), caseCustInfo.getCertEndDate(), false) < 30) {
                    vo.setCustDate(DateUtil.formatDate(caseCustInfo.getCertEndDate()) + "  身份到期小于30天");
                }
            }
        });
        //获取当前文件是否做了ocr
        ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                .eq(StrUtil.isNotBlank(fileId), ComAttachmentFile::getFileId, fileId));
        if (comAttachmentFile != null) {
            vo.setIsOcr(Objects.equals("1", comAttachmentFile.getIsOcr()) ? "是" : "否");
        }
        //获取发票信息
        CarInvoiceInfo carInvoiceInfo = carInvoiceInfoService.getOne(Wrappers.<CarInvoiceInfo>query().lambda()
                .eq(CarInvoiceInfo::getApplyNo, busiNo), false);
        if (carInvoiceInfo != null) {
            //发票代码
            vo.setInvoiceCode(carInvoiceInfo.getInvoiceCode());
            //发票号码
            vo.setInvoiceNumber(carInvoiceInfo.getInvoiceNumber());
            //开票日期
            vo.setInvoiceDate(carInvoiceInfo.getInvoiceDate() != null ? DateUtil.formatDate(carInvoiceInfo.getInvoiceDate()) : "");
            //发票状态
            vo.setCheckResult(carInvoiceInfo.getCheckResult());
        }
        //发票信息
        LoanBankCardInfo loanBankCardInfo = loanBankCardInfoService.getOne(Wrappers.<LoanBankCardInfo>query().lambda()
                .eq(LoanBankCardInfo::getApplyNo, busiNo).eq(LoanBankCardInfo::getIsDefaultDeductCard, AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT)));
        if (loanBankCardInfo != null) {
            //持卡人
            vo.setAccountName(loanBankCardInfo.getAccountName());
            //开户行
            vo.setBankCode(this.getBankName(loanBankCardInfo.getBankCode()));
            //银行卡号
            vo.setAccountNo(loanBankCardInfo.getAccountNo());
            //鉴权结果
            vo.setVerStatus(loanBankCardInfo.getVerStatus());
        }
        //车辆信息
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, busiNo), false);
        if (caseCarInfo != null) {
            //车架号
            vo.setCarVin(caseCarInfo.getCarVin());
            //车辆颜色
            vo.setCarColor(caseCarInfo.getCarColor());
            //国标码
            vo.setGbCode(caseCarInfo.getGbCode());
            //发动机号
            vo.setEngineNo(caseCarInfo.getEngineNo());
            //车辆制造日期
            vo.setVehicleMadeDate(caseCarInfo.getVehicleMadeDate() != null ? DateUtil.formatDate(caseCarInfo.getVehicleMadeDate()) : "");
            //匹配结果
            vo.setCarResult("");
            //车型
            vo.setModelName(caseCarInfo.getModelName());
            //车名称
            vo.setBrandName(caseCarInfo.getBrandName());
            //车系
            vo.setSeriesName(caseCarInfo.getSeriesName());
            //实际销售价
            vo.setSalePrice(caseCarInfo.getSalePrice());
            //合同号
            vo.setContractNo(caseCarInfo.getContractNo());
            //挂靠信息
            if (StringUtils.isNotBlank(caseCarInfo.getIndBusinessName())) {
                //挂靠公司名称
                vo.setAffiliatedName(caseCarInfo.getIndBusinessName());
                //个体工商户社会统一信用代码
                vo.setSocunicrtCode(caseCarInfo.getIndBusinessUsci());
                //法人姓名
                vo.setLegalPersonName(caseCarInfo.getLegalPersonName());
                //法人联系方式
                vo.setLegalPersonTel(caseCarInfo.getLegalPersonTel());
                //注册详细地址
                String door = caseCarInfo.getRegistDoors() != null ? caseCarInfo.getRegistDoors() : "";
                String pro = StringUtils.isBlank(caseCarInfo.getAffiliatedProvince()) ? "" : addressService.getLabelByCode(caseCarInfo.getAffiliatedProvince());
                String city = StringUtils.isBlank(caseCarInfo.getAffiliatedCity()) ? "" : addressService.getLabelByCode(caseCarInfo.getAffiliatedCity());
                String county = StringUtils.isBlank(caseCarInfo.getRegistCounty()) ? "" : addressService.getLabelByCode(caseCarInfo.getRegistCounty());
                //挂靠地址目前取值四级
                vo.setAddress(pro + city + county + door);
            }
        }
        //保单信息
        List<CarInsuranceInfo> carInsuranceInfoList = carInsuranceInfoService
                .list(Wrappers.<CarInsuranceInfo>lambdaQuery().eq(CarInsuranceInfo::getCarId, caseCarInfo.getId()));
        carInsuranceInfoList.stream().forEach(carInsuranceInfo -> {
            // //商业险
            if (InsuranceTypeEnums.BUSINESS.getCode().equals(carInsuranceInfo.getInsuranceType())) {
                //商业险保费
                vo.setInsuranceMoney(carInsuranceInfo.getInsuranceMoney());
                //商业险融资额
                vo.setInsuranceMoneyAmt(carInsuranceInfo.getInsuranceAmt());
                //生效日期
                vo.setInsuranceTime((carInsuranceInfo.getInsuranceStartTime() != null ? DateUtil.formatDate(carInsuranceInfo.getInsuranceStartTime()) : "") + "---" + (carInsuranceInfo.getInsuranceEndTime() != null ? DateUtil.formatDate(carInsuranceInfo.getInsuranceEndTime()) : ""));
            } else if (InsuranceTypeEnums.COMPULSORY.getCode().equals(carInsuranceInfo.getInsuranceType())) {
                //交强险保费
                vo.setJqxInsuranceMoney(carInsuranceInfo.getInsuranceMoney());
                //交强险金额
                vo.setJqxInsuranceAmt(carInsuranceInfo.getInsuranceAmt());
            }
        });
        if (StringUtils.isNotBlank(caseCarInfo.getAffCompanyId())) {
            //挂靠公司
            //modfiy by likang 修改挂靠公司取数逻辑
            ChannelAffiliatedUnits channelAffiliatedUnits = channelAffiliatedUnitsService.getAffiliatedCompany(caseCarInfo.getAffCompanyId());
            if (channelAffiliatedUnits != null) {
                //挂靠公司名称
                vo.setAffiliatedName(channelAffiliatedUnits.getAffiliatedName());
                //挂靠公司统一信用代码
                vo.setSocunicrtCode(channelAffiliatedUnits.getSocUniCrtCode());
                //申请人姓名/挂靠公司名称
                vo.setMianOraffiliatedName(channelAffiliatedUnits.getAffiliatedName());
                //法人姓名
                vo.setLegalPersonName(channelAffiliatedUnits.getLegalPersonName());
                //法人联系方式
                vo.setLegalPersonTel(channelAffiliatedUnits.getLegalPersonTel());
                //注册地址
                vo.setAddress(channelAffiliatedUnits.getProvince() + channelAffiliatedUnits.getCity() + channelAffiliatedUnits.getAddress());
            }
        }
        // 查询挂靠公司信息
        // 查询办理人类型，默认为法人
        IResponse<ApplyAffiliatedUnit> affiliatedUnit = tApplyServiceFeign.getAffiliatedUnit(busiNo);
        if (CaseConstants.CODE_SUCCESS.equals(affiliatedUnit.getCode())) {
            if (ObjectUtil.isNotEmpty(affiliatedUnit.getData())) {
                ApplyAffiliatedUnit data = affiliatedUnit.getData();

                // 办理人
                vo.setSignatoryType(CaseConstants.SIGN_TYPE_AUTHORIZER.equals(data.getSignatoryType()) ? "授权代表" : (CaseConstants.SIGN_TYPE_LEGAL.equals(data.getSignatoryType()) ? "法人代表" : ""));

                if (CaseConstants.SIGN_TYPE_AUTHORIZER.equals(data.getSignatoryType())) {
                    // 授权姓名代表
                    vo.setAuthorizerName(data.getAuthorizerName());
                }
            }
        }
        //联合方信息
        CaseChannelUniteInfo uniteInfoService = caseChannelUniteInfoService.getOne(Wrappers.<CaseChannelUniteInfo>query().lambda()
                .eq(CaseChannelUniteInfo::getApplyNo, busiNo));
        if (null != uniteInfoService) {
            vo.setUniteName(uniteInfoService.getUniteName());
        }
        //合同信息
        CaseContractInfo contractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo, busiNo));
        if (null != contractInfo) {
            //抵押合同号码
            vo.setMortContractNo(contractInfo.getMortContractNo());
        }
        //融资租赁合同承租人签约时间
        IResponse iResponse = applyServiceFeign.getSignDateByBusinessNo(contractInfo.getContractNo());
        String date = JSON.parseObject(JSON.toJSONString(iResponse.getData()), String.class);
        vo.setSignDate(date);
        if (null != contractInfo && null != contractInfo.getLastGenerationDate()) {
            //合同最后签约时间
            vo.setLastGenerationDate(cn.hutool.core.date.DateUtil.format(contractInfo.getLastGenerationDate(), DatePattern.CHINESE_DATE_TIME_FORMAT));
        } else {
            vo.setLastGenerationDate("无");
        }
        //融资信息
        FinMainInfo finMainInfo = finMainInfoService.getOne(Wrappers.<FinMainInfo>query().lambda()
                .eq(FinMainInfo::getApplyNo, busiNo));
        if (null != finMainInfo) {
            //垫资凭证最低金额total_loan_amt
            vo.setOutLoanAmt(finMainInfo.getTotalLoanAmt().subtract(new BigDecimal(5000)));
            //附加项总金额
            vo.setAddContractAmt(finMainInfo.getAddContractAmt());
            //车损最低保额
            vo.setRzAmt(finMainInfo.getTotalLoanAmt().multiply(new BigDecimal(0.7)).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        //处理融资信息
        handleCost(vo, busiNo);
        //处理融资项目
        handleFinFinancingItems(vo, busiNo);
        Map map = JSON.parseObject(JSONObject.toJSONString(vo), Map.class);
        return map;
    }

    private String getBankName(String bankCode) {
        if (StringUtils.isBlank(bankCode)) {
            return bankCode;
        }
        Map<String, List<DicDataDto>> dicMap = DicHelper.getDicMaps("bankCode");
        List<DicDataDto> dicList = dicMap.get("bankCode");
        String bankName = bankCode;
        List<DicDataDto> listData = dicList.stream().filter(s -> s.getValue().equals(bankCode)).collect(Collectors.toList());
        if (listData.size() > 0) {
            bankName = listData.get(0).getTitle();
        }
        return bankName;
    }

    /**
     * 处理融资信息
     *
     * @param vo
     * @param applyNo
     */
    private void handleCost(AttachmentAuditInfoVo vo, String applyNo) {
        try {
            List<FinCostDetails> finCostDetailsList = applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda()
                    .eq(FinCostDetails::getApplyNo, applyNo));
            if (CollectionUtil.isNotEmpty(finCostDetailsList)) {
                BigDecimal downPayAmt = BigDecimal.ZERO;
                BigDecimal marginAmt = BigDecimal.ZERO;
                BigDecimal serviceAmt = BigDecimal.ZERO;
                BigDecimal handlingAmt = BigDecimal.ZERO;
                BigDecimal carLoanAmt = BigDecimal.ZERO;
                for (FinCostDetails finCostDetails : finCostDetailsList) {
                    downPayAmt = downPayAmt.add(finCostDetails.getDownPayAmt() != null ? finCostDetails.getDownPayAmt() : BigDecimal.ZERO);
                    marginAmt = marginAmt.add(finCostDetails.getCustMarginAmt() != null ? finCostDetails.getCustMarginAmt() : BigDecimal.ZERO);
                    marginAmt = marginAmt.add(finCostDetails.getFirmMarginAmt() != null ? finCostDetails.getFirmMarginAmt() : BigDecimal.ZERO);
                    serviceAmt = serviceAmt.add(finCostDetails.getCustServiceAmt() != null ? finCostDetails.getCustServiceAmt() : BigDecimal.ZERO);
                    serviceAmt = serviceAmt.add(finCostDetails.getFirmServiceAmt() != null ? finCostDetails.getFirmServiceAmt() : BigDecimal.ZERO);
                    handlingAmt = handlingAmt.add(finCostDetails.getCustHandlingAmt() != null ? finCostDetails.getCustHandlingAmt() : BigDecimal.ZERO);
                    handlingAmt = handlingAmt.add(finCostDetails.getFirmHandlingAmt() != null ? finCostDetails.getFirmHandlingAmt() : BigDecimal.ZERO);
                    if (CostTypeEnum.CARAMT.getCode().equals(finCostDetails.getCostType())) {
                        BigDecimal amt = finCostDetails.getContractAmt().multiply(finCostDetails.getDownPayScale()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_DOWN);
                        carLoanAmt = finCostDetails.getContractAmt().subtract(amt);
                    }

                }
                //首付租金
                vo.setDownPayAmt(downPayAmt);
                //保证金
                vo.setMarginAmt(marginAmt);
                //服务费
                vo.setServiceAmt(serviceAmt);
                //手续费
                vo.setHandlingAmt(handlingAmt);
                //车辆融资额
                vo.setCarLoanAmt(carLoanAmt);
                //月供
                FinCostDetails finCostDetails = finCostDetailsList.get(0);
                vo.setMonthPayAmt(finCostDetails.getMonthPayAmt());
            }
        } catch (Throwable throwable) {
            log.warn("[{}]融资信息处理失败", applyNo);
            log.error("fetch user info error.", throwable);
        }
    }

    /**
     * 处理融资项目
     *
     * @param vo
     * @param applyNo
     */
    private void handleFinFinancingItems(AttachmentAuditInfoVo vo, String applyNo) {
        try {
            List<FinFinancingItems> itemList = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>lambdaQuery()
                    .eq(FinFinancingItems::getApplyNo, applyNo));
            BigDecimal jpzhAmt = BigDecimal.ZERO;
            BigDecimal gpsAmt = BigDecimal.ZERO;
            BigDecimal syxAmt = BigDecimal.ZERO;
            BigDecimal jqxAmt = BigDecimal.ZERO;
            BigDecimal gzAmt = BigDecimal.ZERO;
            for (FinFinancingItems finFinancingItems : itemList) {
                if (CostType.JPAMT.getIndex().equals(finFinancingItems.getFinanceItemCode())) {
                    //精品装潢
                    jpzhAmt = finFinancingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : finFinancingItems.getFinanceItemAmt();
                } else if (CostType.GPSAMT.getIndex().equals(finFinancingItems.getFinanceItemCode())) {
                    //gps
                    gpsAmt = finFinancingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : finFinancingItems.getFinanceItemAmt();
                } else if (CostType.SYXAMT.getIndex().equals(finFinancingItems.getFinanceItemCode())) {
                    //商业险
                    if (finFinancingItems.getFinanceItemAmt() != null) {
                        syxAmt = finFinancingItems.getFinanceItemAmt();
                    }
                } else if (CostType.JQXAMT.getIndex().equals(finFinancingItems.getFinanceItemCode())) {
                    //交强险
                    if (finFinancingItems.getFinanceItemAmt() != null) {
                        jqxAmt = finFinancingItems.getFinanceItemAmt();
                    }
                } else if (CostType.GZSAMT.getIndex().equals(finFinancingItems.getFinanceItemCode())) {
                    //购置
                    gzAmt = finFinancingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : finFinancingItems.getFinanceItemAmt();
                }
            }
            vo.setJpzhAmt(jpzhAmt);
            vo.setGpsPrdContractAmt(gpsAmt);
            if ((BigDecimal.ZERO).compareTo(jqxAmt) != 0) {
                vo.setJqxAmt(jqxAmt);
            } else {
                vo.setJqxAmt(null);
            }
            vo.setGzsAmt(gzAmt);
            if ((BigDecimal.ZERO).compareTo(syxAmt) != 0) {
                vo.setSyxAmt(syxAmt);
            } else {
                vo.setSyxAmt(null);
            }
            vo.setBxAmt(syxAmt.add(jqxAmt));
        } catch (Throwable throwable) {
            log.warn("[{}]融资项目处理失败", applyNo);
            log.error("fetch user info error.", throwable);
        }
    }

    private void buildJsonInfoFromAnnotatedFields(DeepSeekFileType currentFileType,JSONObject approveInfo, Object entity) {
        for (Field field : entity.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(AuditField.class)) {
                try {
                    field.setAccessible(true);
                    AuditField annotation = field.getAnnotation(AuditField.class);

                    // 获取注解中的 type（支持多类型）
                    DeepSeekFileType[] allowedTypes = annotation.type();
                    // 如果注解未指定 type 或当前文件类型在允许的类型列表中，则注入字段
                    if (allowedTypes.length == 0 || Arrays.asList(allowedTypes).contains(currentFileType)) {
                        String key = annotation.key().isEmpty() ? field.getName() : annotation.key();
                        String label = annotation.label().isEmpty() ? field.getName() : annotation.label();
                        Object value = field.get(entity);
                        approveInfo.put(key, Map.of("label", label, "value", value));
                    }
                } catch (IllegalAccessException e) {
                    log.error("反射注入字段失败", e);
                }
            }
        }
    }
}
