package com.ruicar.afs.cloud.afscase.infomanagement.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.admin.api.feign.AfsLocationFeign;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.approveprev.entity.CaseApprovePrevInfo;
import com.ruicar.afs.cloud.afscase.approveprev.service.CaseApprovePrevInfoService;
import com.ruicar.afs.cloud.afscase.approvetask.condition.BalckListCondition;
import com.ruicar.afs.cloud.afscase.approvetask.condition.WorkTaskPoolCondition;
import com.ruicar.afs.cloud.afscase.approvetask.enums.ApproveNodeEnum;
import com.ruicar.afs.cloud.afscase.approvetask.vo.CustInfoVo;
import com.ruicar.afs.cloud.afscase.approvetask.vo.CustSummaryInfoVo;
import com.ruicar.afs.cloud.afscase.approvetask.vo.HistoryInfoVo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelWitnessInfo;
import com.ruicar.afs.cloud.afscase.channel.enums.BlackListEnum;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelWitnessInfoService;
import com.ruicar.afs.cloud.afscase.channel.utils.BlackDic;
import com.ruicar.afs.cloud.afscase.common.service.impl.CommonUserServiceImpl;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.common.utils.DicUtils;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CaseInfoQueryCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CaseTelCheckInfoCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CreditCustomerCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.ApplyGuarantee;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCarInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustCallRemark;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustChangeRecord;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustContact;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustIndividual;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseRedundantInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseCustInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCarInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCostInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustCallRemarkService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustChangeRecordService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustContactService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustIndividualService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseRedundantInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.AddressDetailsVO;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.AddressVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.BalckEnterpriseVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.BalckListContactVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.BalckListCustInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.BalckListVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CaseCustInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CaseRiskCustInfoVO;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CommonCustInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.ContactVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CreditCustomerInfoVO;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CustPageAreaVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.CustomerInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.DetailsListVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.HireDriverInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.InterfaceInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.MainBaseInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.MainCompanyBaseInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.MainWorkInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.NbgcCarInfoVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.RedundantSignVo;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.ShowVo;
import com.ruicar.afs.cloud.afscase.loanactivatepool.entity.LoanActivatePool;
import com.ruicar.afs.cloud.afscase.loanactivatepool.service.LoanActivateService;
import com.ruicar.afs.cloud.afscase.record.condition.CaseChangeCondition;
import com.ruicar.afs.cloud.afscase.risk.controller.DecisionEngineController;
import com.ruicar.afs.cloud.afscase.workflow.entity.CaseRiskCustInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.service.CaseRiskCustInfoService;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.aggregate.dto.CertNoDto;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.data.datasource.annotation.DS;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AddressTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CostTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustomerRelationEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.HouseTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IndustryTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PositionEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ProfessionalTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RoleTypeCreditEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.SexEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UnitNatureEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ValidStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.contract.cms.vo.BasicCustBaseInfoVo;
import com.ruicar.afs.cloud.common.modules.contract.cms.vo.CustBaseInfoVo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustInfoDto;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.parameter.commom.enums.CustRelationEnums;
import com.ruicar.afs.cloud.parameter.commom.enums.MaritalStatusEnums;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.service.UserCollocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-21 20:52
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api("客户信息")
@RequestMapping("/caseCustInfo")
public class CaseCustInfoController {

    private final CaseCustInfoService caseCustInfoService;
    private final CaseCustIndividualService caseCustIndividualService;
    private final CaseCustContactService caseCustContactService;
    private final CaseCustAddressService caseCustAddressService;
    private final CaseCarInfoService caseCarInfoService;
    private final CaseCostInfoService caseCostInfoService;
    private final AddressService addressService;
    private final CaseBaseInfoService caseBaseInfoService;
    private final CaseRedundantInfoService redundantInfoService;
    private final CaseApprovePrevInfoService caseApprovePrevInfoService;
    private final CaseContractInfoService caseContractInfoService;
    private final LoanActivateService loanActivateService;
    private final ChannelWitnessInfoService channelWitnessInfoService;
    private final CaseEnterpriseCustomerDetailsService caseEnterpriseCustomerDetailsService;
    private final CaseCustCallRemarkService callRemarkService;
    private final CommonUserServiceImpl commonUserService;
    private final UserCollocationService userCollocationService;
    private final CaseCustChangeRecordService caseCustChangeRecordService;
    private DecisionEngineController decisionEngineController;
    private final CaseRiskCustInfoService riskCustInfoService;
    private final CaseCustIndividualService custIndividualService;
    private final CaseCustContactService custContactService;
    private WorkflowTaskInfoService workflowTaskInfoService;
    private CaseCustInfoMapper caseCustInfoMapper;

    private final AfsLocationFeign afsLocationFeign;

    private final ApplyContractFeign applyContractFeign;
    private ApplyConfig applyConfig;
    private final String TYPE_NORMAL = "1"; // 常规
    private final String TYPE_PREV = "2";   // 预审
    /**
     * queryPageArea
     * <p>Description: 根据申请编号查询是否存在共借人或担保人</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryBalckList")
    @ApiOperation(value = "黑灰名单库中的证件号码（身份证或统一社会信用代码），单位名称，手机号码等信息进行分别匹配")
    public IResponse<BalckListVo> queryBalckList(@RequestBody BalckListCondition condition) {
//             获取个人为联系人时联系人的客户名称，手机号码以及证件号码，与申请人关系和姓名
        List<BalckListContactVo> balckListContactVos =  caseCustContactService.queryBalckCustContact(condition.getApplyNo());
        List<ShowVo> showVos = new ArrayList<>();
        for(BalckListContactVo  balckListContactVo :balckListContactVos) {
            //根据手机号码，公司名称和证件号码进行获取
            String telPhone = balckListContactVo.getTelPhone();
            //公司名称
            String companyName = balckListContactVo.getCompanyName();
            /** 联系类型;手机号、单位电话、qq、微信、其他手机号 */
            String certNo = balckListContactVo.getCertNo();
            // 联系类型;手机号、单位电话、qq、微信、其他手机号
            BalckListVo balckListVo1 = caseCustContactService.getByIdCode(BlackListEnum.CERTIFICATES.getCode(),certNo);
            //根据手机号码，公司名称和证件号码进行获取
            BalckListVo balckListVo2 = caseCustContactService.getByIdCode(BlackListEnum.PHONE.getCode(),telPhone);
            //公司名称
            BalckListVo balckListVo3 = caseCustContactService.getByIdCode(BlackListEnum.COMPANYNAME.getCode(),companyName);
            ShowVo showVo = new ShowVo();
            showVo.setCustName(balckListContactVo.getCustName());
            if(StringUtils.isNotBlank(balckListContactVo.getCustRelation())){
                showVo.setCustRole(CustRoleEnum.BALCKCONTACT.getCode());
            }
            if(Objects.nonNull(balckListVo1)){
                if(StringUtils.isNotBlank(balckListVo1.getIdCode()) && balckListVo1.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_BALCK)) {
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
//                    如果显示标识为灰 ， 则设置状态为灰
                }else if(StringUtils.isNotBlank(balckListVo1.getIdCode())  && balckListVo1.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_GRAY)){
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if(null == balckListVo1){
//                 否则设置空
                showVo.setCertNo("");
            }
            if (Objects.nonNull(balckListVo2)) {
                if(StringUtils.isNotBlank(balckListVo2.getIdCode()) &&  balckListVo2.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_BALCK)) {
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
//                    如果显示标识为灰 ， 则设置状态为灰
                }else if(StringUtils.isNotBlank(balckListVo2.getIdCode()) &&  balckListVo2.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_GRAY)) {
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if( Objects.isNull(balckListVo2)) {
//                 否则设置空
                showVo.setTelPhone("");
            }
            if (Objects.nonNull(balckListVo3)){
                if( StringUtils.isNotBlank(balckListVo3.getIdCode()) && balckListVo3.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_BALCK)) {
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
//                    如果显示标识为灰 ， 则设置状态为灰
                }else if(StringUtils.isNotBlank(balckListVo3.getIdCode()) && balckListVo3.getBlackGrayListIdentification().equals(BlackDic.BLACKGRAYLISTIDENTIFICATION_GRAY)) {
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if(Objects.isNull(balckListVo3)){
                //否则设置空
                showVo.setCompanyName("");
            }
            showVos.add(showVo);
        }
////        获取企业信息的名称，手机号码 社会统一代码 企业名称
        List<BalckEnterpriseVo>  balckEnterpriseVos = caseCustContactService.queryBalckEnterprise(condition.getApplyNo());
        for(BalckEnterpriseVo  balckEnterpriseVo :balckEnterpriseVos){
            //根据手机号码，公司名称和证件号码进行获取
            String socunicrtCode = balckEnterpriseVo.getSocunicrtCode();
            String enterpriseName = balckEnterpriseVo.getEnterpriseName();
            String enterpriseTelPhone = balckEnterpriseVo.getTelPhone();
            BalckListVo blv1 = caseCustContactService.getByIdCode(BlackListEnum.CERTIFICATES.getCode(),socunicrtCode);
            BalckListVo blv2 = caseCustContactService.getByIdCode(BlackListEnum.COMPANYNAME.getCode(),enterpriseName);
            BalckListVo blv3 = caseCustContactService.getByIdCode(BlackListEnum.PHONE.getCode(),enterpriseTelPhone);
            ShowVo showVo = new ShowVo();
            showVo.setCustName(balckEnterpriseVo.getEnterpriseName());
            showVo.setCustRole(balckEnterpriseVo.getCustRelation());
            if(Objects.nonNull(blv1)){
                if(StringUtils.isNotBlank(blv1.getIdCode())&& blv1.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else if(StringUtils.isNotBlank(blv1.getIdCode())&& blv1.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if (Objects.isNull(blv1)){
                showVo.setCertNo("");
            }
            if(Objects.nonNull(blv2)){
                if(StringUtils.isNotBlank(blv2.getIdCode())&& blv2.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else if(StringUtils.isNotBlank(blv2.getIdCode())&& blv2.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);

                }
            }else if(Objects.isNull(blv2)){
                showVo.setCompanyName("");
            }
            if(Objects.nonNull(blv3)){
                if(StringUtils.isNotBlank(blv3.getIdCode())&& blv3.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else  if(StringUtils.isNotBlank(blv3.getIdCode())&& blv3.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if(Objects.isNull(blv3)){
                showVo.setTelPhone("");
            }
            showVos.add(showVo);
        }
//        获取个人为主借人或担保人的信息
        List<BalckListCustInfoVo> balckListCustInfoVos = caseCustContactService.queryBalckListCustInfo(condition.getApplyNo());
        for(BalckListCustInfoVo balckListCustInfoVo :balckListCustInfoVos){
            if(CollectionUtil.isNotEmpty(balckEnterpriseVos) && balckListCustInfoVo.getCustRole().equals(AfsEnumUtil.key(CustRoleEnum.MIANCUST))){
                continue;
            }
            String blcTelPhone = balckListCustInfoVo.getTelPhone();
            String blcCertNo = balckListCustInfoVo.getCertNo();
            String blcCompanyName = balckListCustInfoVo.getCompanyName();
            BalckListVo blci3 = caseCustContactService.getByIdCode(BlackListEnum.PHONE.getCode(),blcTelPhone);
            BalckListVo blci1 = caseCustContactService.getByIdCode(BlackListEnum.CERTIFICATES.getCode(),blcCertNo);
            BalckListVo blci2 = caseCustContactService.getByIdCode(BlackListEnum.COMPANYNAME.getCode(),blcCompanyName);
            ShowVo showVo = new ShowVo();
            showVo.setCustName(balckListCustInfoVo.getCustName());
            showVo.setCustRole(balckListCustInfoVo.getCustRelation());
            if(Objects.nonNull(blci1)){
                if(StringUtils.isNotBlank(blci1.getIdCode())&& blci1.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else if(StringUtils.isNotBlank(blci1.getIdCode())&& blci1.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setCertNo(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }

            }else if (Objects.isNull(blci1)){
                showVo.setCertNo("");
            }
            if(Objects.nonNull(blci2)){
                if(StringUtils.isNotBlank(blci2.getIdCode())&& blci2.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else if(StringUtils.isNotBlank(blci2.getIdCode())&& blci2.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setCompanyName(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if(Objects.isNull(blci2)){
                showVo.setCompanyName("");
            }
            if(Objects.nonNull(blci3)){
                if(StringUtils.isNotBlank(blci3.getIdCode())&& blci3.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK)){
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_BLACK);
                }else  if(StringUtils.isNotBlank(blci3.getIdCode())&& blci3.getBlackGrayListIdentification().equals(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY)){
                    showVo.setTelPhone(BlackDic.SHOW_BALCK_GRAY_STATS_GRAY);
                }
            }else if(Objects.isNull(blci3)){
                showVo.setTelPhone("");
            }
            showVos.add(showVo);
        }
        List<ShowVo> listZhuJie = new ArrayList<>();
        if(showVos.size() > 0){
            List<ShowVo> listDanBao = new ArrayList<>();
            List<ShowVo> listLianXi = new ArrayList<>();
            for (ShowVo showVo:showVos) {
                if(CustRoleEnum.MIANCUST.getCode().equals(showVo.getCustRole())){
                    listZhuJie.add(showVo);
                }else if(CustRoleEnum.GUARANTOR.getCode().equals(showVo.getCustRole())){
                    listDanBao.add(showVo);
                }else if(CustRoleEnum.BALCKCONTACT.getCode().equals(showVo.getCustRole())){
                    listLianXi.add(showVo);
                }
            }
            listZhuJie.addAll(listDanBao);
            listZhuJie.addAll(listLianXi);
        }
        return IResponse.success(listZhuJie);
    }


    /**
     * queryPageArea
     * <p>Description: 根据申请编号查询是否存在共借人或担保人</p>
     *
     * @param
     * @return
     */



    @PostMapping(value = "/queryPageArea")
    @ApiOperation(value = "根据申请编号查询页面显示要素")
    public IResponse<CustPageAreaVo> queryPageArea(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {

        CustPageAreaVo custPageAreaVo = new CustPageAreaVo();
        /**  共借人  **/
        CaseCustInfo commonCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.COMMONCSUT.getCode()));
        custPageAreaVo.setCommon(ObjectUtils.isNotEmpty(commonCustInfo));
        /**  担保人  **/
        List<CaseCustInfo> guranCustInfo = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));
        custPageAreaVo.setGuaran(CollectionUtils.isNotEmpty(guranCustInfo));
        return IResponse.success(custPageAreaVo);
    }

    /**
     * queryApproveTaskList
     * <p>Description:主借人基本信息查询 </p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryMainBaseInfo")
    @ApiOperation(value = "按照申请编号查询主借人基本信息")
    public IResponse<MainBaseInfoVo> queryApproveTaskList(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        MainBaseInfoVo mainBaseInfoVo = new MainBaseInfoVo();
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            BeanUtils.copyProperties(caseCustInfo, mainBaseInfoVo);
            // 需求-319-信审端-增加“人证取得时间勾选项”
            mainBaseInfoVo.setNetworkCarCertStartDate(caseCustInfo.getNetworkCarCertStartDate());
            // 获取是否雇佣司机字段值，added by Alex, add time: 2022-6-6 19:52:59
            String hasHiredDriver = null;
            try {
                hasHiredDriver = caseBaseInfoService.queryHasHiredDriverByApplyNo(caseInfoQueryCondition.getApplyNo());
            } catch (AfsBaseException e) {
                log.error("获取是否雇佣司机字段值异常：",e);
                return IResponse.fail("获取是否雇佣司机字段值异常");
            }
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                    .eq(CaseBaseInfo::getApplyNo,caseInfoQueryCondition.getApplyNo()));
            if (ObjectUtil.isNotEmpty(caseBaseInfo)){
                mainBaseInfoVo.setCarPurpose(caseBaseInfo.getCarPurpose());
            }
            mainBaseInfoVo.setHasHiredDriver(hasHiredDriver);
            mainBaseInfoVo.setId(caseCustInfo.getId().toString());
            List list = this.getEvidenceIndividual(caseInfoQueryCondition.getApplyNo(), caseCustInfo);
            if (CollectionUtils.isNotEmpty(list)) {
                RedundantSignVo signVo = JSONObject.parseObject(JSONObject.toJSONString(list.get(0)), RedundantSignVo.class);
                CaseCustInfo custInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(1)), CaseCustInfo.class);
                mainBaseInfoVo.setBackSign(signVo.getBackSign());
                mainBaseInfoVo.setReconsiderSign(signVo.getReconsiderSign());
                if (ObjectUtils.isNotEmpty(custInfo)) {
                    mainBaseInfoVo.setTelPhoneOld(custInfo.getTelPhone());
                    mainBaseInfoVo.setCertStartDateOld(custInfo.getCertStartDate());
                    mainBaseInfoVo.setCertEndDateOld(custInfo.getCertEndDate());
                    mainBaseInfoVo.setCertNoOld(custInfo.getCertNo());
                    mainBaseInfoVo.setCertTypeOld(custInfo.getCertType());
                    mainBaseInfoVo.setCustNameOld(custInfo.getCustName());
                    mainBaseInfoVo.setIsLongTermOld(custInfo.getIsLongTerm());
                    mainBaseInfoVo.setEmailOld(custInfo.getEmail());
                }
            }

            //返回结果加入融资期限
            FinCostDetails finCostInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                    .eq(FinCostDetails::getApplyNo, caseInfoQueryCondition.getApplyNo())
                    .eq(FinCostDetails::getCostType, CostTypeEnum.CARAMT.getCode())
            );
            mainBaseInfoVo.setLoanTerm(null != finCostInfo ? finCostInfo.getLoanTerm() : 0);

            //获取历史匹配数据
            List<HistoryInfoVo> historyInfo = this.getHistoryInfo(caseInfoQueryCondition.getApplyNo());

            if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)) {
                /**  个人客户信息  **/
                CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                        .eq(CaseCustIndividual::getCustId, caseCustInfo.getId()));
                if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                    BeanUtils.copyProperties(caseCustIndividual, mainBaseInfoVo);
                }
                /**  户籍地址  **/
                List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                        .eq(CaseCustAddress::getAddressType, AddressTypeEnum.CENSUS.getCode()));
                if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                    CaseCustAddress custAddress = caseCustAddressList.get(0);
                    mainBaseInfoVo.setProvince(custAddress.getProvince());
                    mainBaseInfoVo.setCity(custAddress.getCity());
                    mainBaseInfoVo.setCounty(custAddress.getCounty());
                    mainBaseInfoVo.setTown(custAddress.getTown());
                    mainBaseInfoVo.setStreet(custAddress.getStreet());
                    mainBaseInfoVo.setDetailAddress(custAddress.getDetailAddress());
                }

                CaseCustIndividual individual = JSONObject.parseObject(JSONObject.toJSONString(list.get(3)), CaseCustIndividual.class);
                if (ObjectUtils.isNotEmpty(individual)) {
                    mainBaseInfoVo.setMaritalStatusOld(individual.getMaritalStatus());
                    mainBaseInfoVo.setAgeOld(individual.getAge());
                    mainBaseInfoVo.setBirthdayOld(individual.getBirthday());
                    mainBaseInfoVo.setHighestEducationOld(individual.getHighestEducation());
                    mainBaseInfoVo.setNationalityOld(individual.getNationality());
                    mainBaseInfoVo.setWorkAgeOld(individual.getWorkAge());

                }

                //根据客户姓名匹配塞入历史数据
                for (HistoryInfoVo history : historyInfo) {
                    if (mainBaseInfoVo.getCustName().equals(history.getCustName())) {
                        mainBaseInfoVo.setPhoneNoList(history.getPhoneNoList());
                        mainBaseInfoVo.setIdCardNoList(history.getIdCardNoList());
                        mainBaseInfoVo.setAddressList(history.getAddressList());
                        mainBaseInfoVo.setUnitNameList(history.getUnitNameList());
                    }
                }
                return IResponse.success(mainBaseInfoVo);
            } else if (ObjectUtils.isNotEmpty(caseCustInfo) && caseCustInfo.getCustType().equals(CaseConstants.ENTERPRISE)) {
                CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, caseCustInfo.getId()));

                if (ObjectUtils.isNotEmpty(details)) {
                    BeanUtils.copyProperties(details, mainBaseInfoVo);
                }

                CaseEnterpriseCustomerDetails customerDetails =
                        JSONObject.parseObject(JSONObject.toJSONString(list.get(2)), CaseEnterpriseCustomerDetails.class);
                if (ObjectUtils.isNotEmpty(customerDetails)) {
                    mainBaseInfoVo.setEnterpriseNameOld(customerDetails.getEnterpriseName());
                    mainBaseInfoVo.setSocunicrtCodeOld(customerDetails.getSocunicrtCode());
                    mainBaseInfoVo.setCompanyPhoneOld(customerDetails.getCompanyPhone());
                    mainBaseInfoVo.setNatureEnterpriseOld(customerDetails.getNatureEnterprise());
                    mainBaseInfoVo.setEnterprisesEngagedIndustryOld(customerDetails.getEnterprisesEngagedIndustry());
                    mainBaseInfoVo.setCompanyLicenseDateOld(customerDetails.getCompanyLicenseDate());

                }
                //根据企业类型客户姓名匹配塞入历史数据
                for (HistoryInfoVo history : historyInfo) {
                    if (mainBaseInfoVo.getCustName().equals(history.getCustName())) {
                        mainBaseInfoVo.setPhoneNoList(history.getPhoneNoList());
                        mainBaseInfoVo.setAddressList(history.getAddressList());
                        mainBaseInfoVo.setUnitNameList(history.getUnitNameList());
                        mainBaseInfoVo.setSocUniCrtCodeList(history.getSocUniCrtCodeList());
                    }
                }
                return IResponse.success(mainBaseInfoVo);
            }
        }
        return IResponse.fail("数据错误9999");
    }



    /**
     * queryApproveTaskList
     * <p>Description: 获取历史匹配数据 </p>
     *
     * @param applyNo
     * @return
     */
    private List<HistoryInfoVo> getHistoryInfo(String applyNo) {
        HistoryInfoVo historyInfoVo = new HistoryInfoVo();
        historyInfoVo.setApplyNo(applyNo);
        IResponse<HistoryInfoVo> historyInfoList = this.getProfilerInfo(historyInfoVo);
        List<HistoryInfoVo> historyInfo = (List<HistoryInfoVo>) historyInfoList.getData();
        return historyInfo;
    }

    /**
     * queryMainWorkInfo主借人工作信息查询
     * <p>Description:queryMainWorkInfo </p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryMainWorkInfo")
    @ApiOperation(value = "按照申请编号查询主借人工作信息")
    public IResponse<MainWorkInfoVo> queryMainWorkInfo(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        MainWorkInfoVo mainWorkInfoVo = new MainWorkInfoVo();
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            BeanUtils.copyProperties(caseCustInfo, mainWorkInfoVo);
            mainWorkInfoVo.setId(caseCustInfo.getId().toString());
            /**  个人客户信息  **/
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                    .eq(CaseCustIndividual::getCustId, caseCustInfo.getId()));
            if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                BeanUtils.copyProperties(caseCustIndividual, mainWorkInfoVo);
                mainWorkInfoVo.setFamilyIncome(caseCustIndividual.getOtherIncome());
            }
        }
        List list=this.getEvidenceIndividual(caseInfoQueryCondition.getApplyNo(),caseCustInfo);
        if(CollectionUtils.isNotEmpty(list)){
            RedundantSignVo signVo= JSONObject.parseObject(JSONObject.toJSONString(list.get(0)),RedundantSignVo.class);
            CaseCustIndividual individual= JSONObject.parseObject(JSONObject.toJSONString(list.get(3)),CaseCustIndividual.class);
            if(ObjectUtils.isNotEmpty(individual)){
                mainWorkInfoVo.setUnitNameOld(individual.getUnitName());
                mainWorkInfoVo.setMonthlyIncomeOld(individual.getMonthlyIncome());
                mainWorkInfoVo.setOutstandingLoanOld(individual.getOutstandingLoan());
                mainWorkInfoVo.setProfessionalTypeOld(individual.getProfessionalType());
                mainWorkInfoVo.setTotalIncomeOld(individual.getTotalIncome());
                mainWorkInfoVo.setPositionOld(individual.getPosition());
                mainWorkInfoVo.setUnitTelPhoneOld(individual.getUnitTelPhone());
                mainWorkInfoVo.setIndustryTypeOld(individual.getIndustryType());
            }
            mainWorkInfoVo.setBackSign(signVo.getBackSign());
            mainWorkInfoVo.setReconsiderSign(signVo.getReconsiderSign());
        }
        // 获取是否雇佣司机字段值，added by Alex, add time: 2022-6-6 19:37:09
        String hasHiredDriver = null;
        try {
            hasHiredDriver = caseBaseInfoService.queryHasHiredDriverByApplyNo(caseInfoQueryCondition.getApplyNo());
        } catch (AfsBaseException e) {
            log.error("获取是否雇佣司机字段值异常：",e);
            return IResponse.fail("获取是否雇佣司机字段值异常");
        }
        mainWorkInfoVo.setHasHiredDriver(hasHiredDriver);

        //获取历史匹配数据
        List<HistoryInfoVo> historyInfo = this.getHistoryInfo(caseInfoQueryCondition.getApplyNo());
        if (ObjectUtil.isNotNull(historyInfo.get(0))){
            if (CollectionUtil.isNotEmpty(historyInfo.get(0).getUnitNameList())) {
                mainWorkInfoVo.setUnitNameList(historyInfo.get(0).getUnitNameList());
            }
            if (CollectionUtil.isNotEmpty(historyInfo.get(0).getUnitPhoneList())){
                mainWorkInfoVo.setUnitPhoneList(historyInfo.get(0).getUnitPhoneList());
            }
        }
        return IResponse.success(mainWorkInfoVo);
    }
    /**
     * queryContactList
     * <p>Description: 查询联系人列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryContactList")
    @ApiOperation(value = "按照申请编号查询联系人列表")
    public IResponse<List<ContactVo>> queryContactList(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        List<ContactVo> contactVoList = new ArrayList<>();
        //获取历史匹配数据
        List<HistoryInfoVo> historyInfo = this.getHistoryInfo(caseInfoQueryCondition.getApplyNo());

        /**  联系人信息列表  **/
        List<CaseCustContact> caseCustContactList = caseCustContactService.list(Wrappers.<CaseCustContact>query().lambda()
                .eq(CaseCustContact::getApplyNo, caseInfoQueryCondition.getApplyNo()));
        CaseRedundantInfo redundantInfo = redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(caseInfoQueryCondition.getApplyNo()), CaseRedundantInfo::getApplyNo, caseInfoQueryCondition.getApplyNo()));
        if (CollectionUtils.isNotEmpty(caseCustContactList)) {
            for (CaseCustContact caseCustContact : caseCustContactList) {
                ContactVo contactVo = new ContactVo();
                contactVo.setBackSign(WhetherEnum.NO.getCode());
                contactVo.setReconsiderSign(WhetherEnum.NO.getCode());
                contactVo.setDetailAddress(this.getAddress(caseCustContact.getDetailAddressTemp()));
                //根据联系人姓名匹配塞入历史数据
                for (HistoryInfoVo historyInfo1 : historyInfo) {
                    if (caseCustContact.getCustName().equals(historyInfo1.getCustName())) {
                        contactVo.setPhoneNoList(historyInfo1.getPhoneNoList());
                        contactVo.setIdCardNoList(historyInfo1.getIdCardNoList());
                        contactVo.setAddressList(historyInfo1.getAddressList());
                    }
                }
                BeanUtils.copyProperties(caseCustContact, contactVo);
                contactVoList.add(contactVo);

                if (ObjectUtils.isNotEmpty(redundantInfo)) {
                    String backSign = redundantInfo.getBackSign();
                    if (WhetherEnum.YES.getCode().equals(backSign)) {
                        String backEvidence = redundantInfo.getBackEvidence();
                        if (StringUtils.isNotBlank(backEvidence)) {
                            JSONObject jsonObject = JSONObject.parseObject(backEvidence);
                            JSONArray contactList = jsonObject.getJSONArray("caseContactList");
                            if (ObjectUtils.isNotEmpty(contactList) && contactList.size() > 0){
                                List<CaseCustContact> list=contactList.toJavaList(CaseCustContact.class);
                                for (CaseCustContact info : list) {
                                    if (caseCustContact.getId().equals(info.getId())){
                                        if (caseCustContact.getCustName() != info.getCustName()) {
                                            contactVo.setCustNameOld(info.getCustName());
                                        }
                                        if (caseCustContact.getTelPhone() != info.getTelPhone()) {
                                            contactVo.setTelPhoneOld(info.getTelPhone());
                                        }
                                        if (caseCustContact.getCertNo() != info.getCertNo()) {
                                            contactVo.setCertNoOld(info.getCertNo());
                                        }
                                        if (caseCustContact.getCompanyName() != info.getCompanyName()) {
                                            contactVo.setCompanyNameOld(info.getCompanyName());
                                        }
                                        if (EmptyUtils.isNotEmpty(caseCustContact.getMonthlyIncome())&&caseCustContact.getMonthlyIncome().compareTo(info.getMonthlyIncome())!=0) {
                                            contactVo.setMonthlyIncomeOld(info.getMonthlyIncome());
                                        }
                                        if (caseCustContact.getDetailAddressTemp() != info.getDetailAddressTemp()) {
                                            contactVo.setDetailAddressTempOld(info.getDetailAddressTemp());
                                        }
                                        if (caseCustContact.getCustRelation() != info.getCustRelation()) {
                                            contactVo.setCustRelationOld(info.getCustRelation());
                                        }
                                        if (caseCustContact.getCertType() != info.getCertType()) {
                                            contactVo.setCertTypeOld(info.getCertType());
                                        }
                                        if (caseCustContact.getDetailAddress() != info.getDetailAddress()) {
                                            contactVo.setDetailAddressOld(this.getAddress(info.getDetailAddressTemp()));
                                        }

                                        contactVo.setBackSign(WhetherEnum.YES.getCode());

                                        contactVo.setReconsiderSign(WhetherEnum.NO.getCode());
                                    }
                                }
                            }
                        }
                    }
                    String reconsiderSign = redundantInfo.getReconsiderSign();
                    if (WhetherEnum.YES.getCode().equals(reconsiderSign)) {
                        String reconsiderEvidence = redundantInfo.getReconsiderEvidence();
                        if (StringUtils.isNotBlank(reconsiderEvidence)) {
                            JSONObject jsonObject = JSONObject.parseObject(reconsiderEvidence);
                            JSONArray contactList = jsonObject.getJSONArray("caseContactList");
                            if (ObjectUtils.isNotEmpty(contactList) && contactList.size() > 0){
                                List<CaseCustContact> list=contactList.toJavaList(CaseCustContact.class);
                                for (CaseCustContact info : list) {
                                    if (caseCustContact.getId().equals(info.getId())){
                                        if (caseCustContact.getCustName() != info.getCustName()) {
                                            contactVo.setCustNameOld(info.getCustName());
                                        }
                                        if (caseCustContact.getTelPhone() != info.getTelPhone()) {
                                            contactVo.setTelPhoneOld(info.getTelPhone());
                                        }
                                        if (caseCustContact.getCertNo() != info.getCertNo()) {
                                            contactVo.setCertNoOld(info.getCertNo());
                                        }
                                        if (caseCustContact.getCompanyName() != info.getCompanyName()) {
                                            contactVo.setCompanyNameOld(info.getCompanyName());
                                        }
                                        if (EmptyUtils.isNotEmpty(caseCustContact.getMonthlyIncome())&&!caseCustContact.getMonthlyIncome().equals(info.getMonthlyIncome())) {
                                            contactVo.setMonthlyIncomeOld(info.getMonthlyIncome());
                                        }
                                        if (caseCustContact.getDetailAddressTemp() != info.getDetailAddressTemp()) {
                                            contactVo.setDetailAddressTempOld(info.getDetailAddressTemp());
                                        }
                                        if (caseCustContact.getCustRelation() != info.getCustRelation()) {
                                            contactVo.setCustRelationOld(info.getCustRelation());
                                        }
                                        if (caseCustContact.getDetailAddress() != info.getDetailAddress()) {
                                            contactVo.setDetailAddressOld(this.getAddress(info.getDetailAddressTemp()));
                                        }
                                        contactVo.setBackSign(WhetherEnum.NO.getCode());
                                        contactVo.setReconsiderSign(WhetherEnum.YES.getCode());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return IResponse.success(contactVoList);
    }
    /**
     * 查询担保人基本信息列表(个人和企业)
     * @return
     */
    @PostMapping(value = "/getCaseCustInfoList")
    @ApiOperation(value = "查询担保人基本信息列表(个人和企业)")
    public IResponse<List<CaseCustInfoVo>>  getCaseCustInfoList(@ModelAttribute CaseInfoQueryCondition condition ){
        if (condition.getApplyNo().isEmpty()){
            return IResponse.fail("数据异常");
        }
        List<CaseCustInfoVo> caseCustInfoVos = new ArrayList<>();
        /**  客户信息  **/
        List<CaseCustInfo> caseinfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, condition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CaseConstants.GUARANTOR));
        if (!caseinfoList.isEmpty()){

            final CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                .eq(CaseBaseInfo::getApplyNo, condition.getApplyNo())
            );

            for (CaseCustInfo custInfo:caseinfoList
                 ) {
                CaseCustInfoVo caseCustInfoVo = new CaseCustInfoVo();
                if (custInfo.getCustType().equals(CaseConstants.PERSONAL)){
                    BeanUtils.copyProperties(custInfo,caseCustInfoVo);
                    caseCustInfoVo.setId(custInfo.getId());
                    CaseCustAddress caseCustAddress = caseCustAddressService.getOne(Wrappers.<CaseCustAddress>lambdaQuery()
                            .eq(CaseCustAddress::getApplyNo,custInfo.getApplyNo())
                            .eq(CaseCustAddress::getCustId,custInfo.getId())
                            .eq(CaseCustAddress::getAddressType,"3"));
                    String[] addressData = {caseCustAddress.getProvince(), caseCustAddress.getCity(), caseCustAddress.getCounty(),
                            caseCustAddress.getTown(), caseCustAddress.getStreet(), caseCustAddress.getDetailAddress()};
                    caseCustInfoVo.setAddress(this.getAddress(addressData));
                    // 不太确定
                    if (CaseConstants.PERSONAL.equals(caseBaseInfo.getInputType())){
                        Map<String, List<DicDataDto>> enterpriseCustRelationMap = DicHelper.getDicMaps("custRelation");
                        String translate = DicUtils.translate(custInfo.getCustRelation(), enterpriseCustRelationMap);
                        caseCustInfoVo.setCustRelationName(translate);
                    }else if (CaseConstants.ENTERPRISE.equals(caseBaseInfo.getInputType())){
                        Map<String, List<DicDataDto>> enterpriseCustRelationMap = DicHelper.getDicMaps("enterpriseCustRelation");
                        String translate = DicUtils.translate(custInfo.getCustRelation(), enterpriseCustRelationMap);
                        caseCustInfoVo.setCustRelationName(translate);
                    }

                    if (ObjectUtils.isNotEmpty(custInfo)){
                        CaseCustIndividual individual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>lambdaQuery()
                                .eq(CaseCustIndividual::getCustId, custInfo.getId()));
                        if(individual != null ){
                            caseCustInfoVo.setUnitName(individual.getUnitName());
                            caseCustInfoVo.setMonthlyIncome(individual.getMonthlyIncome());
                        }
                    }else {
                        return IResponse.success("服务异常");
                    }
                    caseCustInfoVos.add(caseCustInfoVo);
                }else if (custInfo.getCustType().equals(CaseConstants.ENTERPRISE)){
                    BeanUtils.copyProperties(custInfo,caseCustInfoVo);
                    CaseEnterpriseCustomerDetails customerDetails = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>lambdaQuery()
                            .eq(CaseEnterpriseCustomerDetails::getCustId,custInfo.getId()));
                    caseCustInfoVo.setAnnualIncomeOfEnterprises(customerDetails.getAnnualIncomeOfEnterprises());
                    caseCustInfoVo.setEnterpriseContactMobilePhone(customerDetails.getEnterpriseContactMobilePhone());
                    caseCustInfoVo.setEnterpriseName(customerDetails.getEnterpriseName());
                    caseCustInfoVo.setSocunicrtCode(customerDetails.getSocunicrtCode());
                    caseCustInfoVo.setBusinessContacts(customerDetails.getBusinessContacts());
                    caseCustInfoVo.setCompanyPhone(customerDetails.getCompanyPhone());
                    caseCustInfoVo.setId(custInfo.getId());
                    caseCustInfoVos.add(caseCustInfoVo);
                }
            }
        }
        return IResponse.success(caseCustInfoVos);
    }

    /**
     * 查询担保人信息(基础，明细，地址)
     * @return
     */
    @PostMapping(value = "/detailsList")
    @ApiOperation(value = "查询担保人信息(基础，明细，地址)(个人企业)")
    public  IResponse<DetailsListVo>  detailsList (@ModelAttribute CaseInfoQueryCondition condition ){
        DetailsListVo detailsListVo = new DetailsListVo();
        CaseCustInfo custInfo = caseCustInfoService.getById(condition.getId());
        detailsListVo.setCaseCustInfo(custInfo);
        if (ObjectUtils.isNotEmpty(custInfo)){
            if (custInfo.getCustType().equals(CaseConstants.PERSONAL)){
                CaseCustIndividual individual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>lambdaQuery()
                        .eq(CaseCustIndividual::getCustId, custInfo.getId()));
                detailsListVo.setCaseCustIndividual(individual);
            }else {
                CaseEnterpriseCustomerDetails caseEnterpriseCustomer = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>lambdaQuery()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, custInfo.getId()));
                detailsListVo.setCaseEnterpriseCustomerDetails(caseEnterpriseCustomer);
            }
            List<CaseCustAddress> addressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>lambdaQuery()
                    .eq(CaseCustAddress::getCustId, custInfo.getId()));
            detailsListVo.setCaseCustAddressList(addressList);
        }else {
            return IResponse.success("服务异常");
        }
        return IResponse.success(detailsListVo);
    }

    /**
     * queryCommomCustInfo
     * <p>Description: 查询共借人/担保人信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryCommomCustInfo")
    @ApiOperation(value = "按照申请编号查询担保人信息")
    public IResponse<CommonCustInfoVo> queryCommomCustInfo(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        CommonCustInfoVo commonCustInfoVo = new CommonCustInfoVo();
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, caseInfoQueryCondition.getCustRole())
                .eq(CaseCustInfo::getCustType,caseInfoQueryCondition.getInputType()));
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            BeanUtils.copyProperties(caseCustInfo, commonCustInfoVo);
            commonCustInfoVo.setId(caseCustInfo.getId().toString());
            if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)) {
                /**  个人客户信息  **/
                CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                        .eq(CaseCustIndividual::getCustId, caseCustInfo.getId()));
                if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                    BeanUtils.copyProperties(caseCustIndividual, commonCustInfoVo);
                    commonCustInfoVo.setFamilyIncome(caseCustIndividual.getOtherIncome());
                }
                /**  户籍地址  **/
                List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                        .eq(CaseCustAddress::getAddressType, AddressTypeEnum.CENSUS.getCode()));
                if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                    commonCustInfoVo.setCensus(addressService.getLabelByCode(caseCustAddressList.get(0).getProvince()) + addressService.getLabelByCode(caseCustAddressList.get(0).getCity()) );
                    commonCustInfoVo.setProvince(caseCustAddressList.get(0).getProvince() != null ? caseCustAddressList.get(0).getProvince() : "");
                    commonCustInfoVo.setCity(caseCustAddressList.get(0).getCity() != null ? caseCustAddressList.get(0).getCity() : "");
                }
                List list = this.getEvidenceIndividual(caseInfoQueryCondition.getApplyNo(), caseCustInfo);
                if (CollectionUtils.isNotEmpty(list)) {
                    RedundantSignVo signVo = JSONObject.parseObject(JSONObject.toJSONString(list.get(0)), RedundantSignVo.class);
                    CaseCustInfo custInfo = JSONObject.parseObject(JSONObject.toJSONString(list.get(1)), CaseCustInfo.class);
                    CaseCustIndividual individual = JSONObject.parseObject(JSONObject.toJSONString(list.get(2)), CaseCustIndividual.class);
                    if (ObjectUtils.isNotEmpty(custInfo)) {
                        commonCustInfoVo.setTelPhoneOld(custInfo.getTelPhone());
                    }
                    if (ObjectUtils.isNotEmpty(individual)) {
                        commonCustInfoVo.setMaritalStatusOld(individual.getMaritalStatus());
                        commonCustInfoVo.setUnitNameOld(individual.getUnitName());
                    }
                    commonCustInfoVo.setBackSign(signVo.getBackSign());
                    commonCustInfoVo.setReconsiderSign(signVo.getReconsiderSign());
                }
                return IResponse.success(commonCustInfoVo);
            } else if (caseCustInfo.getCustType().equals(CaseConstants.ENTERPRISE)) {
                CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, caseCustInfo.getId()));

                if (ObjectUtils.isNotEmpty(details)) {
                    BeanUtils.copyProperties(details, commonCustInfoVo);
                }
                List<CaseCustAddress> list = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
                commonCustInfoVo.setCaseCustAddressList(list);
                return IResponse.success(commonCustInfoVo);
            }

        }
        return IResponse.success(commonCustInfoVo);
    }
    /**
     * queryAddressList
     * <p>Description: 查询地址列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryAddressList")
    @ApiOperation(value = "按照申请编号查询地址列表")
    public IResponse<List<AddressVo>> queryAddressList(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        /**  按照客户类型获取客户  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        List<AddressVo> addressVoList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            /**  地址信息列表  **/
            List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
            //冗余数据
            List<CaseCustAddress> list=this.getEvidenceForAddress(caseInfoQueryCondition.getApplyNo(),caseCustInfo.getId());
            if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                for (CaseCustAddress caseCustAddress : caseCustAddressList) {
                    AddressVo addressVo = new AddressVo();
                    BeanUtils.copyProperties(caseCustAddress, addressVo);
                    String[] addressData = {caseCustAddress.getProvince(), caseCustAddress.getCity(), caseCustAddress.getCounty(),
                            caseCustAddress.getTown(), caseCustAddress.getStreet(), caseCustAddress.getDetailAddress()};
                    addressVo.setDetailAddress(this.getAddress(addressData));
                    //保存冗余数据
                    if(CollectionUtils.isNotEmpty(list)){
                        RedundantSignVo signVo= JSONObject.parseObject(JSONObject.toJSONString(list.get(0)),RedundantSignVo.class);
                        addressVo.setBackSign(signVo.getBackSign());
                        addressVo.setReconsiderSign(signVo.getReconsiderSign());
                    }
                    addressVoList.add(addressVo);
                }
            }
        }
        return IResponse.success(addressVoList);
    }

    /**
     * queryAddressList
     * <p>Description: 处理地址详细信息</p>
     *
     * @param
     * @return
     */
    private  String getAddress(String[] detailAddressTemp){
        StringBuilder detail= new StringBuilder();
        if(detailAddressTemp==null||detailAddressTemp.length==0){
            return detail.toString() ;
        }

        for(int i=0; i<detailAddressTemp.length;i++ ){
            if(StringUtil.isBlank ( detailAddressTemp[i] )){
                continue;
            }
            String label = "";
            if(i+1 == detailAddressTemp.length){
                label = detailAddressTemp[i];
            }else{
                label = addressService.getLabelByCode(detailAddressTemp[i]);
            }
            if(StringUtil.isBlank (detail.toString())){
                detail.append(label);
            } else if ("99999999".equals(label) || "99999999".equals(detailAddressTemp[i])) {

            } else {
                detail.append("/"+label);
            }
        }
        return detail.toString();
    }
    /**
     * removeAddressById
     * @param
     * @return
     */
    @PostMapping(value = "/removeAddressById")
    @ApiOperation(value = "根据id删除地址")
    public IResponse<String> removeAddressById(@RequestParam("addressId") String id) {
        if (ObjectUtils.isNotEmpty(id)){
            caseCustAddressService.removeById(id);
            return IResponse.success("success");
        }
        return IResponse.fail("error");
    }

    @PostMapping(value = "/updateAddressById")
    @ApiOperation(value = "根据id更新客户地址")
    public IResponse<String> updateAddressById(@RequestBody CaseCustAddress caseCustAddress) {
        if (ObjectUtils.isNotEmpty(caseCustAddress)) {
            //如果地址类型是现居住地址，需要解析经纬度
            if (CaseConstants.RESIDENTIAL_ADDRESS.equals(caseCustAddress.getAddressType())){
                HashMap<String, Double> hashMap = this.getLatLng(caseCustAddress);
                if (!hashMap.isEmpty()) {
                    //经度
                    Double lng = hashMap.get("lng");
                    //纬度
                    Double lat = hashMap.get("lat");
                    //将经纬度保存到数据库中
                    caseCustAddress.setLongitude(lng);
                    caseCustAddress.setLatitude(lat);
                }
            }
            StringBuilder stringBuilder = new StringBuilder();
            CaseCustAddress custAddress = caseCustAddressService.getById(caseCustAddress.getId());
            IResponse<String> custAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(custAddress.getDetailAddressTemp()));
            IResponse<String> caseCustAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(caseCustAddress.getDetailAddressTemp()));

            if(!Objects.equals(custAddress.getAddressType(),caseCustAddress.getAddressType())) {
                stringBuilder.append("地址类型")
                        .append(AddressTypeEnum.getDesc(custAddress.getAddressType()))
                        .append("修改为").append(AddressTypeEnum.getDesc(caseCustAddress.getAddressType())).append(";");
            }
            if (!Arrays.equals(custAddress.getDetailAddressTemp(), caseCustAddress.getDetailAddressTemp())) {
                stringBuilder.append("地址信息").append(custAddressResponse.getData()).append("[").append(AddressTypeEnum.getDesc(custAddress.getAddressType())).append("]")
                        .append(custAddress.getDetailAddressTemp()[custAddress.getDetailAddressTemp().length - 1])
                        .append("修改为").append(caseCustAddressResponse.getData())
                        .append(caseCustAddress.getDetailAddressTemp()[caseCustAddress.getDetailAddressTemp().length - 1])
                        .append("[").append(AddressTypeEnum.getDesc(caseCustAddress.getAddressType())).append("]").append(";");
            }
            if(!Objects.equals(custAddress.getHouseType(),caseCustAddress.getHouseType())) {
                stringBuilder.append("房产类型")
                        .append(custAddress.getHouseType()==null?"":HouseTypeEnum.getDesc(custAddress.getHouseType()))
                        .append("修改为").append(HouseTypeEnum.getDesc(caseCustAddress.getHouseType())).append(";");
            }
            if(!Objects.equals(custAddress.getHouseStartDate(),caseCustAddress.getHouseStartDate())) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
                stringBuilder.append("起始时间")
                        .append(custAddress.getHouseStartDate()==null?"":dateFormat.format(custAddress.getHouseStartDate()))
                        .append("修改为").append(dateFormat.format(caseCustAddress.getHouseStartDate())).append("。");
            }
            caseCustAddressService.updateById(caseCustAddress);
            if (EmptyUtils.isNotEmpty(stringBuilder)) {
                CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
                caseCustChangeRecord.setChangeContent(stringBuilder.toString());
                caseCustChangeRecord.setApplyNo(caseCustAddress.getApplyNo());
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                        .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setChangeType("地址信息修改");
                caseCustChangeRecordService.save(caseCustChangeRecord);
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo,caseCustAddress.getApplyNo())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime)
                        .last("limit 1"));
                if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                    decisionEngineController.updateDecisionEngine(caseCustAddress.getApplyNo());
                }
            }
            return IResponse.success("success");
        }
        return IResponse.success("error");
    }

    @PostMapping(value = "/updateContactById")
    @ApiOperation(value = "根据id更新联系人地址")
    public IResponse<String> updateContactById(@RequestBody CaseCustContact caseCustContact) {
        if (ObjectUtils.isNotEmpty(caseCustContact)) {
            CaseCustContact custContactById = caseCustContactService.getById(caseCustContact.getId());
            StringBuilder stringBuilder = new StringBuilder();
            if(!Objects.equals(custContactById.getCustName(),caseCustContact.getCustName())) {
                stringBuilder.append("联系人姓名")
                        .append(custContactById.getCustName())
                        .append("修改为").append(caseCustContact.getCustName()).append(";");
            }
            if(!Objects.equals(custContactById.getCustRelation(),caseCustContact.getCustRelation())) {
                stringBuilder.append("联系人").append("[").append(custContactById.getCustName()).append("]").append("与申请人关系")
                        .append(CustomerRelationEnum.getDesc(custContactById.getCustRelation()))
                        .append("修改为").append(CustomerRelationEnum.getDesc(caseCustContact.getCustRelation())).append(";");
            }
            if(!Objects.equals(custContactById.getTelPhone(),caseCustContact.getTelPhone())) {
                stringBuilder.append("联系人").append("[").append(custContactById.getCustName()).append("]").append("手机号码")
                        .append(custContactById.getTelPhone())
                        .append("修改为").append(caseCustContact.getTelPhone()).append(";");
            }
            if (EmptyUtils.isNotEmpty(stringBuilder)) {
                CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
                caseCustChangeRecord.setChangeContent(stringBuilder.toString());
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                        .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setApplyNo(caseCustContact.getApplyNo());
                caseCustChangeRecord.setChangeType("联系人信息编辑");
                caseCustChangeRecordService.save(caseCustChangeRecord);
            }
            caseCustContactService.updateById(caseCustContact);
            if (EmptyUtils.isNotEmpty(stringBuilder)){
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo,caseCustContact.getApplyNo())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime)
                        .last("limit 1"));
                if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                    decisionEngineController.updateDecisionEngine(caseCustContact.getApplyNo());
                }
            }
            return IResponse.success("success");
        }
        return IResponse.success("error");
    }

    /**
     * <p>Description: 根据id查询客户地址信息</p>
     * @param id
     * @return
     */
    @PostMapping(value = "/queryAddressById")
    @ApiOperation(value = "根据id查询客户地址信息")
    public IResponse<CaseCustAddress> queryAddressById(@RequestParam("addressId") String id) {
        CaseCustAddress caseCustAddress = new CaseCustAddress();
        if (StringUtils.isNotBlank(id)) {
            caseCustAddress = caseCustAddressService.getById(id);
        }
        return IResponse.success(caseCustAddress);
    }

    /**
     * <p>Description: 根据id查询客户联系人信息</p>
     * @param id
     * @return
     */
    @PostMapping(value = "/queryContactById")
    @ApiOperation(value = "根据id查询客户地址信息")
    public IResponse<CaseCustContact> queryContactById(@RequestParam("contactId") String id) {
        CaseCustContact caseCustContact = new CaseCustContact();
        if (ObjectUtils.isNotEmpty(id)) {
            caseCustContact = caseCustContactService.getById(id);
        }
        return IResponse.success(caseCustContact);
    }

    /**
     * saveAddress
     * <p>Description: 新增地址</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveAddress")
    @ApiOperation(value = "保存地址信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveAddress(@ModelAttribute AddressVo addressVo) {
        /**  按照客户id获取客户  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getById(addressVo.getCustId());
        CaseCustAddress caseCustAddress = new CaseCustAddress();
        BeanUtils.copyProperties(addressVo, caseCustAddress);
        caseCustAddress.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());
        caseCustAddress.setApplyNo(caseCustInfo.getApplyNo());
        caseCustAddressService.save(caseCustAddress);
        return IResponse.success("客户地址新增成功");
    }

    /**
     * saveAddress
     * <p>Description: 更新客户基本信息（个人）</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveMainInfo")
    @ApiOperation(value = "更新客户基本信息（个人）")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveMainInfo(@RequestBody MainBaseInfoVo mainBaseInfoVo) {
        /**  按照客户id获取客户  **/
        if (ObjectUtils.isNotEmpty(mainBaseInfoVo)){
            //更新客户基本信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getById(mainBaseInfoVo.getId());
            List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                    .eq(CaseCustAddress::getAddressType, AddressTypeEnum.CENSUS.getCode()));
            caseCustInfo.setNewPhone(mainBaseInfoVo.getNewPhone());
            CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
            StringBuilder stringBuilder = new StringBuilder();
            if(!Objects.equals(caseCustInfo.getTelPhone(),mainBaseInfoVo.getTelPhone())){
                stringBuilder.append("客户手机号码")
                        .append(caseCustInfo.getTelPhone()).append("修改为").append(mainBaseInfoVo.getTelPhone()).append(";");
            }
            if(!Objects.equals(caseCustInfo.getNetworkCarCertStartDate(),mainBaseInfoVo.getNetworkCarCertStartDate())){
                // 2024-04-22，如果前端勾选为未取得（传值9999-12-31）
                if (Objects.equals("9999-12-31", String.valueOf(mainBaseInfoVo.getNetworkCarCertStartDate()))) {
                    stringBuilder.append("客户人证取得时间").append("修改为未取得");
                } else {
                    // 客户人证取得时间未取得修改为date
                    if (Objects.equals("9999-12-31", String.valueOf(caseCustInfo.getNetworkCarCertStartDate()))) {
                        stringBuilder.append("客户人证取得时间未取得")
                                .append("修改为")
                                .append(mainBaseInfoVo.getNetworkCarCertStartDate()).append(";");
                    } else {
                        stringBuilder.append("客户人证取得时间").append(caseCustInfo.getNetworkCarCertStartDate())
                                .append("修改为")
                                .append(mainBaseInfoVo.getNetworkCarCertStartDate()).append(";");
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(caseCustAddressList)){
                if (!ArrayUtil.equals(caseCustAddressList.get(0).getDetailAddressTemp(),mainBaseInfoVo.getDetailAddressTemp())){
                    IResponse<String> custAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(caseCustAddressList.get(0).getDetailAddressTemp()));
                    IResponse<String> caseCustAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(mainBaseInfoVo.getDetailAddressTemp()));
                    stringBuilder.append("户籍所在地").append(custAddressResponse.getData()).append(caseCustAddressList.get(0).getDetailAddress())
                            .append("修改为").append(caseCustAddressResponse.getData()).append(mainBaseInfoVo.getDetailAddress())
                            .append(";");
                }
            }
            if (EmptyUtils.isNotEmpty(stringBuilder)) {
                caseCustChangeRecord.setChangeContent(stringBuilder.toString());
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                        .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setApplyNo(mainBaseInfoVo.getApplyNo());
                caseCustChangeRecord.setChangeType("客户基本信息修改");
                caseCustChangeRecordService.save(caseCustChangeRecord);
            }
            caseCustInfo.setTelPhone(mainBaseInfoVo.getTelPhone());
            // 需求-319-信审端-增加“人证取得时间勾选项”
            caseCustInfo.setNetworkCarCertStartDate(mainBaseInfoVo.getNetworkCarCertStartDate());
            caseCustInfoService.updateById(caseCustInfo);
            //更新客户个人信息
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId,Long.parseLong(mainBaseInfoVo.getId()))
            );
            caseCustIndividual.setMaritalStatus(mainBaseInfoVo.getMaritalStatus());
            caseCustIndividual.setWorkAge(mainBaseInfoVo.getWorkAge());
            caseCustIndividual.setDrivingType(mainBaseInfoVo.getDrivingType());
            caseCustIndividual.setDrivingLicenceNo(mainBaseInfoVo.getDrivingLicenceNo());
            caseCustIndividual.setHighestEducation(mainBaseInfoVo.getHighestEducation());
            caseCustIndividualService.updateById(caseCustIndividual);

            //更新户籍地址
            if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                caseCustAddressList.get(0).setProvince(mainBaseInfoVo.getProvince());
                caseCustAddressList.get(0).setCity(mainBaseInfoVo.getCity());
                caseCustAddressList.get(0).setCounty(mainBaseInfoVo.getCounty());
                caseCustAddressList.get(0).setTown(mainBaseInfoVo.getTown());
                caseCustAddressList.get(0).setStreet(mainBaseInfoVo.getStreet());
                caseCustAddressList.get(0).setDetailAddress(mainBaseInfoVo.getDetailAddress());
                caseCustAddressService.updateById(caseCustAddressList.get(0));
            }
            if (EmptyUtils.isNotEmpty(stringBuilder)){
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo,mainBaseInfoVo.getApplyNo())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime)
                        .last("limit 1"));
                if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                    decisionEngineController.updateDecisionEngine(mainBaseInfoVo.getApplyNo());
                }
            }
            return IResponse.success("更新客户基本信息成功");
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * saveAddress
     * <p>Description: 更新客户基本信息（企业）</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveCompanyMainInfo")
    @ApiOperation(value = "更新客户基本信息(企业)")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveCompanyMainInfo(@RequestBody MainCompanyBaseInfoVo mainBaseInfoVo) {
        /**  按照客户id获取客户  **/
        if (ObjectUtils.isNotEmpty(mainBaseInfoVo)){
            //更新客户基本信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getById(mainBaseInfoVo.getId());
            if(ObjectUtils.isNotEmpty(caseCustInfo)) {
                //企业法人手机号码
                caseCustInfo.setTelPhone(mainBaseInfoVo.getTelPhone());
                caseCustInfoService.updateById(caseCustInfo);
                //企业联系号码修改
                CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, caseCustInfo.getId()));
                if (ObjectUtils.isNotEmpty(details)) {
                    details.setCompanyPhone(mainBaseInfoVo.getCompanyPhone());
                    caseEnterpriseCustomerDetailsService.updateById(details);
                }
                //企业地址信息修改
                List<CaseCustAddress> addressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
                if (CollectionUtils.isNotEmpty(addressList)) {
                    for (int i=0;i<addressList.size();i++){
                        List<CaseCustAddress> caseCustAddressesList = mainBaseInfoVo.getCaseCustAddressList();
                        for (int j=0;j<caseCustAddressesList.size();j++){
                            if (addressList.get(i).getAddressType().equals(caseCustAddressesList.get(j).getAddressType())){
                                addressList.get(i).setProvince(caseCustAddressesList.get(j).getProvince());
                                addressList.get(i).setCity(caseCustAddressesList.get(j).getCity());
                                addressList.get(i).setCounty(caseCustAddressesList.get(j).getCounty());
                                addressList.get(i).setTown(caseCustAddressesList.get(j).getTown());
                                addressList.get(i).setStreet(caseCustAddressesList.get(j).getStreet());
                                addressList.get(i).setDetailAddress(caseCustAddressesList.get(j).getDetailAddress());
                                caseCustAddressService.updateById(addressList.get(i));
                            }
                        }
                    }
                }
                // 更新是否雇佣司机字段
                CaseBaseInfo caseBaseInfo = caseBaseInfoService.getCaseByApplyNo(mainBaseInfoVo.getApplyNo());
                if(mainBaseInfoVo.getHasHiredDriver()!=null){
                    if(caseBaseInfo.getHasHiredDriver()==null || !caseBaseInfo.getHasHiredDriver().equals(mainBaseInfoVo.getHasHiredDriver())){
                        caseBaseInfo.setHasHiredDriver(mainBaseInfoVo.getHasHiredDriver());
                        caseBaseInfoService.updateById(caseBaseInfo);
                    }
                }
                return IResponse.success("更新客户基本信息成功");
            }
        }
        return IResponse.fail("客户信息为空");
    }


    /**
     * saveAddress
     * <p>Description: 更新客户共借人基本信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveCommomCustInfo")
    @ApiOperation(value = "更新客户共借人基本信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveCommomCustInfo(@RequestBody CommonCustInfoVo commonCustInfoVo) {
        /**  按照客户id获取客户  **/
        if (ObjectUtils.isNotEmpty(commonCustInfoVo)){
            //更新共借人基本信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getById(commonCustInfoVo.getId());
            caseCustInfo.setNewPhone(commonCustInfoVo.getNewPhone());
            caseCustInfo.setTelPhone(commonCustInfoVo.getTelPhone());
            caseCustInfoService.updateById(caseCustInfo);

            //更新共借人个人信息
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId,Long.parseLong(commonCustInfoVo.getId()))
            );
            caseCustIndividual.setMaritalStatus(commonCustInfoVo.getMaritalStatus());
            caseCustIndividual.setWorkAge(commonCustInfoVo.getWorkAge());
            caseCustIndividual.setHighestEducation(commonCustInfoVo.getHighestEducation());
            caseCustIndividualService.updateById(caseCustIndividual);

            //更新共借人户籍地址
            List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                    .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                    .eq(CaseCustAddress::getAddressType, AddressTypeEnum.CENSUS.getCode()));
            if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                caseCustAddressList.get(0).setProvince(commonCustInfoVo.getProvince());
                caseCustAddressList.get(0).setCity(commonCustInfoVo.getCity());
                caseCustAddressList.get(0).setCounty(commonCustInfoVo.getCounty());
                caseCustAddressList.get(0).setTown(commonCustInfoVo.getTown());
                caseCustAddressList.get(0).setStreet(commonCustInfoVo.getStreet());
                caseCustAddressList.get(0).setDetailAddress(commonCustInfoVo.getDetailAddress());
                caseCustAddressService.updateById(caseCustAddressList.get(0));
            }

            return IResponse.success("更新客户基本信息成功");
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * saveAddress
     * <p>Description: 更新客户担保人基本信息（个人）</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveGuarantorInfo")
    @ApiOperation(value = "更新客户担保人基本信息（个人）")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String>
    saveGuarantorInfo(@RequestBody CommonCustInfoVo commonCustInfoVo) {
        /**  按照客户id获取客户  **/
        if (ObjectUtils.isNotEmpty(commonCustInfoVo)) {
            //更新担保人基本信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getById(commonCustInfoVo.getId());
            if (ObjectUtils.isNotEmpty(caseCustInfo)) {
                StringBuilder stringBuilder = new StringBuilder();
                if (!Objects.equals(caseCustInfo.getTelPhone(), commonCustInfoVo.getTelPhone())) {
                    stringBuilder.append("保证人手机号码")
                            .append(caseCustInfo.getTelPhone())
                            .append("修改为").append(commonCustInfoVo.getTelPhone()).append(";");
                }
                caseCustInfo.setTelPhone(commonCustInfoVo.getTelPhone());
                caseCustInfoService.updateById(caseCustInfo);

                //更新担保人个人信息
                CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                        Wrappers.<CaseCustIndividual>lambdaQuery()
                                .eq(CaseCustIndividual::getCustId, Long.parseLong(commonCustInfoVo.getId()))
                );
                if (!Objects.equals(caseCustIndividual.getUnitName(), commonCustInfoVo.getUnitName())) {
                    stringBuilder.append("保证人单位名称")
                            .append(caseCustIndividual.getUnitName())
                            .append("修改为").append(commonCustInfoVo.getUnitName()).append(";");
                }
                caseCustIndividual.setUnitName(commonCustInfoVo.getUnitName());
                if (!Objects.equals(caseCustIndividual.getUnitTelPhone(), commonCustInfoVo.getUnitTelPhone())) {
                    stringBuilder.append("保证人公司电话")
                            .append(caseCustIndividual.getUnitTelPhone())
                            .append("修改为").append(commonCustInfoVo.getUnitTelPhone()).append(";");
                }
                caseCustIndividual.setUnitTelPhone(commonCustInfoVo.getUnitTelPhone());
                if (!Objects.equals(caseCustIndividual.getSpousePhone(), commonCustInfoVo.getSpousePhone())) {
                    stringBuilder.append("保证人配偶手机号码")
                            .append(caseCustIndividual.getSpousePhone())
                            .append("修改为").append(commonCustInfoVo.getSpousePhone()).append(";");
                }
                caseCustIndividual.setSpousePhone(commonCustInfoVo.getSpousePhone());
                caseCustIndividualService.updateById(caseCustIndividual);

                //更新担保人地址信息
                List<CaseCustAddress> caseCustAddressesList = commonCustInfoVo.getCaseCustAddressList();
                List<CaseCustAddress> addressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
                if (CollectionUtils.isNotEmpty(addressList) && CollectionUtils.isNotEmpty(caseCustAddressesList)) {
                    for (CaseCustAddress caseCustAddress : addressList) {
                        for (CaseCustAddress custAddress : caseCustAddressesList) {
                            if (caseCustAddress.getAddressType().equals(custAddress.getAddressType())) {
                                if (!Objects.equals(caseCustAddress.getDetailAddress(), custAddress.getDetailAddress())) {
                                    IResponse<String> custAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(caseCustAddress.getDetailAddressTemp()));
                                    IResponse<String> caseCustAddressResponse = afsLocationFeign.nameByCodeList(Arrays.asList(custAddress.getDetailAddressTemp()));
                                    switch (caseCustAddress.getAddressType()) {
                                        case "1" : {
                                            stringBuilder.append("保证人户籍地址")
                                                    .append(custAddressResponse.getData())
                                                    .append(caseCustAddress.getDetailAddress())
                                                    .append("修改为")
                                                    .append(caseCustAddressResponse.getData())
                                                    .append(custAddress.getDetailAddress()).append(";");
                                            break;
                                        }
                                        case "2" : {
                                            stringBuilder.append("保证人现居住地址")
                                                    .append(custAddressResponse.getData())
                                                    .append(caseCustAddress.getDetailAddress())
                                                    .append("修改为")
                                                    .append(caseCustAddressResponse.getData())
                                                    .append(custAddress.getDetailAddress()).append(";");
                                            break;
                                        }
                                        case "3" : {
                                            stringBuilder.append("保证人单位地址")
                                                    .append(custAddressResponse.getData())
                                                    .append(caseCustAddress.getDetailAddress())
                                                    .append("修改为")
                                                    .append(caseCustAddressResponse.getData())
                                                    .append(custAddress.getDetailAddress()).append(";");
                                            break;
                                        }
                                        default : {
                                            break;
                                        }
                                    }
                                }
                                caseCustAddress.setProvince(custAddress.getProvince());
                                caseCustAddress.setCity(custAddress.getCity());
                                caseCustAddress.setCounty(custAddress.getCounty());
                                caseCustAddress.setTown(custAddress.getTown());
                                caseCustAddress.setStreet(custAddress.getStreet());
                                caseCustAddress.setDetailAddress(custAddress.getDetailAddress());

                                //如果地址类型是现居住地址，需要解析经纬度
                                if (CaseConstants.RESIDENTIAL_ADDRESS.equals(custAddress.getAddressType())) {
                                    HashMap<String, Double> hashMap = this.getLatLng(custAddress);
                                    if (!hashMap.isEmpty()) {
                                        //经度
                                        Double lng = hashMap.get("lng");
                                        //纬度
                                        Double lat = hashMap.get("lat");
                                        //将经纬度保存到数据库中
                                        caseCustAddress.setLongitude(lng);
                                        caseCustAddress.setLatitude(lat);
                                    }
                                }
                                caseCustAddressService.updateById(caseCustAddress);
                            }
                        }
                    }
                }
                if (EmptyUtils.isNotEmpty(stringBuilder)) {
                    CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
                    caseCustChangeRecord.setChangeContent(stringBuilder.toString());
                    UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                            .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                    caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                    caseCustChangeRecord.setApplyNo(caseCustInfo.getApplyNo());
                    caseCustChangeRecord.setChangeType("保证人信息修改");
                    caseCustChangeRecordService.save(caseCustChangeRecord);
                    WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                            .eq(WorkflowTaskInfo::getBusinessNo,caseCustInfo.getApplyNo())
                            .orderByDesc(WorkflowTaskInfo::getCreateTime)
                            .last("limit 1"));
                    if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                        decisionEngineController.updateDecisionEngine(caseCustInfo.getApplyNo());
                    }
                }
                    return IResponse.success("更新客户基本信息成功");
            }
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * saveAddress
     * <p>Description: 更新客户担保人基本信息（企业）</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveCompanyGuarantorInfo")
    @ApiOperation(value = "更新客户担保人基本信息（企业）")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveCompanyGuarantorInfo(@RequestBody CommonCustInfoVo commonCustInfoVo) {
        /**  按照客户id获取客户  **/
        if (ObjectUtils.isNotEmpty(commonCustInfoVo)){
            //更新担保人基本信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getById(commonCustInfoVo.getId());
            if(ObjectUtils.isNotEmpty(caseCustInfo)) {
                //企业联系号码修改
                CaseEnterpriseCustomerDetails details = caseEnterpriseCustomerDetailsService.getOne(Wrappers.<CaseEnterpriseCustomerDetails>query().lambda()
                        .eq(CaseEnterpriseCustomerDetails::getCustId, caseCustInfo.getId()));
                if (ObjectUtils.isNotEmpty(details)) {
                    //企业联系手机
                    details.setEnterpriseContactMobilePhone(commonCustInfoVo.getEnterpriseContactMobilePhone());
                    //企业联系电话
                    details.setCompanyPhone(commonCustInfoVo.getCompanyPhone());
                    caseEnterpriseCustomerDetailsService.updateById(details);
                }
                //企业地址信息修改
                List<CaseCustAddress> addressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId()));
                if (CollectionUtils.isNotEmpty(addressList)) {
                    for (int i=0;i<addressList.size();i++){
                        List<CaseCustAddress> caseCustAddressesList = commonCustInfoVo.getCaseCustAddressList();
                        for (int j=0;j<caseCustAddressesList.size();j++){
                            if (addressList.get(i).getAddressType().equals(caseCustAddressesList.get(j).getAddressType())){
                                addressList.get(i).setProvince(caseCustAddressesList.get(j).getProvince());
                                addressList.get(i).setCity(caseCustAddressesList.get(j).getCity());
                                addressList.get(i).setCounty(caseCustAddressesList.get(j).getCounty());
                                addressList.get(i).setTown(caseCustAddressesList.get(j).getTown());
                                addressList.get(i).setStreet(caseCustAddressesList.get(j).getStreet());
                                addressList.get(i).setDetailAddress(caseCustAddressesList.get(j).getDetailAddress());
                                caseCustAddressService.updateById(addressList.get(i));
                            }
                        }
                    }
                }
                return IResponse.success("更新客户基本信息成功");
            }
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * saveAddress
     * <p>Description: 更新客户工作信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveWorkInfo")
    @ApiOperation(value = "更新客户工作信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveWorkInfo(@ModelAttribute MainWorkInfoVo mainWorkInfoVo) {
        if (ObjectUtils.isNotEmpty(mainWorkInfoVo)) {
            //更新客户个人工作信息
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId, Long.parseLong(mainWorkInfoVo.getId()))
            );
            CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
            StringBuilder stringBuilder = new StringBuilder();
            if (!Objects.equals(caseCustIndividual.getUnitName(), mainWorkInfoVo.getUnitName())) {
                stringBuilder.append("单位名称")
                        .append(caseCustIndividual.getUnitName()).append("修改为").append(mainWorkInfoVo.getUnitName()).append(";");
                caseCustIndividual.setUnitName(mainWorkInfoVo.getUnitName());

            }
            if (!Objects.equals(caseCustIndividual.getUnitType(), mainWorkInfoVo.getUnitType())) {
                stringBuilder.append("单位性质")
                        .append(UnitNatureEnum.getDesc(caseCustIndividual.getUnitType()))
                        .append("修改为").append(UnitNatureEnum.getDesc(mainWorkInfoVo.getUnitType())).append(";");
            caseCustIndividual.setUnitType(mainWorkInfoVo.getUnitType());
            }
            if (!Objects.equals(caseCustIndividual.getIndustryType(), mainWorkInfoVo.getIndustryType())) {
                stringBuilder.append("行业类型")
                        .append(IndustryTypeEnum.getDesc(caseCustIndividual.getIndustryType()))
                        .append("修改为").append(IndustryTypeEnum.getDesc(mainWorkInfoVo.getIndustryType())).append(";");
                caseCustIndividual.setIndustryType(mainWorkInfoVo.getIndustryType());
            }
            if (!Objects.equals(caseCustIndividual.getProfessionalType(), mainWorkInfoVo.getProfessionalType())) {
                stringBuilder.append("职业类型")
                        .append(ProfessionalTypeEnum.getDesc(caseCustIndividual.getProfessionalType()))
                        .append("修改为").append(ProfessionalTypeEnum.getDesc(mainWorkInfoVo.getProfessionalType())).append(";");
                caseCustIndividual.setProfessionalType(mainWorkInfoVo.getProfessionalType());
            }
            if (!Objects.equals(caseCustIndividual.getPosition(), mainWorkInfoVo.getPosition())) {
                stringBuilder.append("职务")
                        .append(PositionEnum.getDesc(caseCustIndividual.getPosition()))
                        .append("修改为").append(PositionEnum.getDesc(mainWorkInfoVo.getPosition())).append(";");
                caseCustIndividual.setPosition(mainWorkInfoVo.getPosition());
            }
            if (!Objects.equals(caseCustIndividual.getUnitTelPhone(), mainWorkInfoVo.getUnitTelPhone())) {
                stringBuilder.append("公司电话")
                        .append(caseCustIndividual.getUnitTelPhone()).append("修改为").append(mainWorkInfoVo.getUnitTelPhone()).append(";");
                caseCustIndividual.setUnitTelPhone(mainWorkInfoVo.getUnitTelPhone());
            }
            BigDecimal currentMi = Optional.ofNullable(caseCustIndividual.getMonthlyIncome()).orElse(new BigDecimal("0"));
            BigDecimal inputMi = Optional.ofNullable(mainWorkInfoVo.getMonthlyIncome()).orElse(new BigDecimal("0"));
            if (currentMi.compareTo(inputMi) != 0) {
                    stringBuilder.append("本人月收入")
                            .append(currentMi).append("修改为").append(inputMi).append(";");
                    caseCustIndividual.setMonthlyIncome(mainWorkInfoVo.getMonthlyIncome());
            }
            if (EmptyUtils.isNotEmpty(stringBuilder)) {
                caseCustChangeRecord.setChangeContent(stringBuilder.toString());
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda().eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setApplyNo(mainWorkInfoVo.getApplyNo());
                caseCustChangeRecord.setChangeType("工作信息修改");
                caseCustChangeRecordService.save(caseCustChangeRecord);
            }
                caseCustIndividual.setOutstandingLoan(mainWorkInfoVo.getOutstandingLoan());
                caseCustIndividual.setOtherIncome(mainWorkInfoVo.getFamilyIncome());
                caseCustIndividualService.updateById(caseCustIndividual);
                // 更新CaseOrderInfo信息中的是否雇佣司机,added by Alex, add time: 2022-6-10 01:28:03----------start
                // 如果前端传了订单申请编号则使用传入的值
                String applyNo = mainWorkInfoVo.getApplyNo();
                CaseBaseInfo caseBaseInfo;
                if (StringUtils.isBlank(applyNo)) {
                    // 如果前端未传入订单申请编号，则根据custId查询客户信息主表获取申请编号
                    CaseCustInfo caseCustInfo = caseCustInfoService.getById(caseCustIndividual.getCustId());
                    if (caseCustInfo == null) {
                        throw new AfsBaseException(String.format("客户信息不存在： %s", caseCustIndividual.getCustId()));
                    }
                    applyNo = caseCustInfo.getApplyNo();
                }
                if (StringUtils.isBlank(applyNo)) {
                    throw new AfsBaseException(String.format("客户信息中未保存订单申请编号： %s", caseCustIndividual.getCustId()));
                }
                // 根据订单申请编号查询订单申请信息
                caseBaseInfo = caseBaseInfoService.getCaseByApplyNo(applyNo);
                if (caseBaseInfo == null) {
                    throw new AfsBaseException(String.format("当前案件不存在： %s", applyNo));
                }
                // 判断是否修改了是否雇佣司机的值
                if (mainWorkInfoVo.getHasHiredDriver() == null) {
                    throw new AfsBaseException("参数hasHiredDriver为空");
                }
                // 判断是否雇佣司机字段是否需要更新，如果原数据字段为空或入参与当前值不同，则更新
                if (caseBaseInfo.getHasHiredDriver() == null || !caseBaseInfo.getHasHiredDriver().equals(mainWorkInfoVo.getHasHiredDriver())) {
                    caseBaseInfo.setHasHiredDriver(mainWorkInfoVo.getHasHiredDriver());
                    caseBaseInfoService.updateById(caseBaseInfo);
                }
                if (EmptyUtils.isNotEmpty(stringBuilder)){
                    WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                            .eq(WorkflowTaskInfo::getBusinessNo,mainWorkInfoVo.getApplyNo())
                            .orderByDesc(WorkflowTaskInfo::getCreateTime)
                            .last("limit 1"));
                    if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                        decisionEngineController.updateDecisionEngine(mainWorkInfoVo.getApplyNo());
                    }
                }
            // 更新CaseOrderInfo信息中的是否雇佣司机,added by Alex, add time: 2022-6-10 01:28:03----------start
                return IResponse.success("更新客户基本信息成功");
            }
            return IResponse.fail("客户信息为空");
        }

    /**1
     * saveAddress
     * <p>Description: 更新共借人工作信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveCommonWorkInfo")
    @ApiOperation(value = "更新共借人工作信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveCommonWorkInfo(@ModelAttribute CommonCustInfoVo commonCustInfoVo) {
        if (ObjectUtils.isNotEmpty(commonCustInfoVo)) {
            //更新客户个人工作信息
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId,Long.parseLong(commonCustInfoVo.getId()))
            );
            caseCustIndividual.setUnitName(commonCustInfoVo.getUnitName());
            caseCustIndividual.setIndustryType(commonCustInfoVo.getIndustryType());
            caseCustIndividual.setProfessionalType(commonCustInfoVo.getProfessionalType());
            caseCustIndividual.setPosition(commonCustInfoVo.getPosition());
            caseCustIndividual.setUnitTelPhone(commonCustInfoVo.getUnitTelPhone());
            caseCustIndividual.setMonthlyIncome(commonCustInfoVo.getMonthlyIncome());
            caseCustIndividual.setOutstandingLoan(commonCustInfoVo.getOutstandingLoan());
            caseCustIndividual.setOtherIncome(commonCustInfoVo.getFamilyIncome());

            caseCustIndividualService.updateById(caseCustIndividual);
            return IResponse.success("更新客户基本信息成功");
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * saveAddress
     * <p>Description: 更新担保人工作信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveGuarantorWorkInfo")
    @ApiOperation(value = "更新担保人工作信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveGuarantorWorkInfo(@ModelAttribute CommonCustInfoVo commonCustInfoVo) {
        if (ObjectUtils.isNotEmpty(commonCustInfoVo)) {
            //更新客户个人工作信息
            CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(
                    Wrappers.<CaseCustIndividual>lambdaQuery()
                            .eq(CaseCustIndividual::getCustId,Long.parseLong(commonCustInfoVo.getId()))
            );
            caseCustIndividual.setUnitName(commonCustInfoVo.getUnitName());
            caseCustIndividual.setIndustryType(commonCustInfoVo.getIndustryType());
            caseCustIndividual.setProfessionalType(commonCustInfoVo.getProfessionalType());
            caseCustIndividual.setPosition(commonCustInfoVo.getPosition());
            caseCustIndividual.setUnitTelPhone(commonCustInfoVo.getUnitTelPhone());
            caseCustIndividual.setMonthlyIncome(commonCustInfoVo.getMonthlyIncome());
            caseCustIndividual.setOutstandingLoan(commonCustInfoVo.getOutstandingLoan());
            caseCustIndividual.setOtherIncome(commonCustInfoVo.getFamilyIncome());

            caseCustIndividualService.updateById(caseCustIndividual);
            return IResponse.success("更新客户基本信息成功");
        }
        return IResponse.fail("客户信息为空");
    }
    /**
     * saveContact
     * <p>Description: 新增地址</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/saveContact")
    @ApiOperation(value = "保存联系人信息")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveContact(@RequestBody ContactVo contactVo) {
        /**  按照客户类型获取客户  **/
        CaseCustContact caseCustContact = new CaseCustContact();
        BeanUtils.copyProperties(contactVo, caseCustContact);
        caseCustContact.setValidStatus(ValidStatusEnum.EFFECTIVE.getCode());
        caseCustContact.setIsDefault(WhetherEnum.NO.getCode());
        caseCustContact.setDelFlag("0");
        caseCustContact.setCreateTime(new Date());
        caseCustContact.setCreateBy(SecurityUtils.getUsername());

        List<CaseCustContact> caseCustNameList = caseCustContactService.list(Wrappers.<CaseCustContact>query().lambda()
                .eq(ObjectUtils.isNotEmpty(contactVo.getApplyNo()),CaseCustContact::getApplyNo, contactVo.getApplyNo())
                .eq(ObjectUtils.isNotEmpty(contactVo.getCustName()),CaseCustContact::getCustName, contactVo.getCustName()));
        if(CollectionUtils.isEmpty(caseCustNameList)){
            List<CaseCustContact> caseTelPhoneList = caseCustContactService.list(Wrappers.<CaseCustContact>query().lambda()
                    .eq(ObjectUtils.isNotEmpty(contactVo.getApplyNo()),CaseCustContact::getApplyNo, contactVo.getApplyNo())
                    .eq(ObjectUtils.isNotEmpty(contactVo.getTelPhone()),CaseCustContact::getTelPhone,contactVo.getTelPhone()));
            if(CollectionUtils.isEmpty(caseTelPhoneList)){
                String stringBuilder = "新增了联系人" +
                        caseCustContact.getCustName() +
                        "联系人与申请人的关系为" +
                        CustomerRelationEnum.getDesc(caseCustContact.getCustRelation()) +
                        "联系人的手机号码为" + caseCustContact.getTelPhone();
                CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
                caseCustChangeRecord.setChangeContent(stringBuilder);
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                        .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setApplyNo(contactVo.getApplyNo());
                caseCustChangeRecord.setChangeType("联系人信息新增");
                caseCustChangeRecordService.save(caseCustChangeRecord);
                caseCustContactService.save(caseCustContact);
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo,contactVo.getApplyNo())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime)
                        .last("limit 1"));
                if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                    decisionEngineController.updateDecisionEngine(contactVo.getApplyNo());
                }
                return IResponse.success("保存联系人信息");
            }else {
                return IResponse.fail("手机号重复");
            }

        }

        return IResponse.fail("联系人信息重复");
    }

    /**
     * queryContactList
     * <p>Description: 查询联系人列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryContactListOld")
    @ApiOperation(value = "按照申请编号查询冗余联系人列表")
    public IResponse<List<ContactVo>> queryContactListOld(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        List<ContactVo> contactVoList = new ArrayList<>();
        /**  联系人信息列表  **/
        List<CaseCustContact> caseCustContactList = this.getEvidenceForContact(caseInfoQueryCondition.getApplyNo());

        if (CollectionUtils.isNotEmpty(caseCustContactList)) {
            for (CaseCustContact caseCustContact : caseCustContactList) {
                ContactVo contactVo = new ContactVo();
                BeanUtils.copyProperties(caseCustContact, contactVo);
                contactVoList.add(contactVo);
            }
        }
        return IResponse.success(contactVoList);
    }
    /**
     * @description: 根据id删除联系人
     * @param id
     * @return
     */
    @PostMapping(value = "/removeContactById")
    @ApiOperation(value = "根据id删除联系人")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> removeContactById(@RequestParam("contactId") String id){
        if (ObjectUtils.isNotEmpty(id)){
            CaseCustContact caseCustContact = caseCustContactService.getById(id);
            String stringBuilder = "删除了联系人" +
                    caseCustContact.getCustName() +
                    "联系人与申请人关系为" +
                    CustomerRelationEnum.getDesc(caseCustContact.getCustRelation()) +
                    "手机号码为" + caseCustContact.getTelPhone();
            if (EmptyUtils.isNotEmpty(stringBuilder)) {
                CaseCustChangeRecord caseCustChangeRecord = new CaseCustChangeRecord();
                caseCustChangeRecord.setChangeContent(stringBuilder);
                UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                        .eq(UserCollocation::getLoginName, SecurityUtils.getUser().getUsername()));
                caseCustChangeRecord.setUserRealName(userCollocation.getUserRealName());
                caseCustChangeRecord.setApplyNo(caseCustContact.getApplyNo());
                caseCustChangeRecord.setChangeType("联系人信息删除");
                caseCustChangeRecordService.save(caseCustChangeRecord);
            }
            caseCustContactService.removeById(Long.valueOf(id));
            if (EmptyUtils.isNotEmpty(stringBuilder)){
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>lambdaQuery()
                        .eq(WorkflowTaskInfo::getBusinessNo,caseCustContact.getApplyNo())
                        .orderByDesc(WorkflowTaskInfo::getCreateTime)
                        .last("limit 1"));
                if (!StrUtil.equals(workflowTaskInfo.getUserDefinedIndex(), ApproveNodeEnum.FIRST_NODE.getCode())){
                    decisionEngineController.updateDecisionEngine(caseCustContact.getApplyNo());
                }
            }
            return IResponse.success("success");
        }
        return IResponse.fail("error");
    }

    /**
     * @Description 征信报告主借人信息查询
     * <AUTHOR>
     * @Date 2020/9/9 20:28
     */
    @GetMapping("/creditCustInfo")
    public IResponse getCustInfo(@ModelAttribute CreditCustomerCondition condition){

        List<CreditCustomerInfoVO>  customerInfoVoList= new ArrayList<>();
        if(StrUtil.equals(condition.getType(),TYPE_NORMAL)){
            List<CaseCustInfo> custInfList = caseCustInfoService.list(
                    Wrappers.<CaseCustInfo>lambdaQuery()
                            .eq(CaseCustInfo::getApplyNo,condition.getApplyNo())
            );
            if(CollectionUtils.isNotEmpty(custInfList)){
                final CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>lambdaQuery()
                    .eq(CaseBaseInfo::getApplyNo, condition.getApplyNo())
                );
                custInfList.forEach(cust->{
                    CreditCustomerInfoVO customerInfoVO = CreditCustomerInfoVO.builder().build();
                    if(CustRoleEnum.MIANCUST.getCode().equals(cust.getCustRole())){
                        customerInfoVO.setRoleType(RoleTypeCreditEnum.ROLO_BRO.getCode());
                    }
                    else if(CustRoleEnum.COMMONCSUT.getCode().equals(cust.getCustRole())) {
                        customerInfoVO.setRoleType(RoleTypeCreditEnum.ROLO_CON.getCode());
                    }
                    else if(CustRoleEnum.GUARANTOR.getCode().equals(cust.getCustRole())) {
                        customerInfoVO.setRoleType(RoleTypeCreditEnum.ROLO_GU.getCode());
                    }
                    customerInfoVO.setCustName(cust.getCustName());
                    customerInfoVO.setCertNo(cust.getCertNo());
                    List<DicDataDto> custRelation = null;
                    if ("0".equals(caseBaseInfo.getInputType())){
                        // 企业
                        Map<String, List<DicDataDto>> enterpriseCustRelationMap = DicHelper.getDicMaps("enterpriseCustRelation");
                        custRelation = Optional.ofNullable(enterpriseCustRelationMap.get("enterpriseCustRelation"))
                            .orElse(Collections.EMPTY_LIST);
                    }else if ("1".equals(caseBaseInfo.getInputType())){
                        // 个人
                        Map<String, List<DicDataDto>> enterpriseCustRelationMap = DicHelper.getDicMaps("custRelation");
                        custRelation = Optional.ofNullable(enterpriseCustRelationMap.get("custRelation"))
                            .orElse(Collections.EMPTY_LIST);
                    }
                    if (custRelation != null){
                        custRelation.stream()
                            .filter(dto -> dto.getValue().equals(cust.getCustRelation()))
                            .findFirst()
                            .ifPresent(dto -> customerInfoVO.setCustRelation(dto.getTitle()));
                    }
                    customerInfoVoList.add(customerInfoVO);
                });
            }
        }else if(StrUtil.equals(condition.getType(),TYPE_PREV)){
            CreditCustomerInfoVO customerInfoVO = CreditCustomerInfoVO.builder().build();
            CaseApprovePrevInfo prevInfo = caseApprovePrevInfoService.getOne(
                    Wrappers.<CaseApprovePrevInfo>lambdaQuery()
                            .eq(CaseApprovePrevInfo::getApplyNo,condition.getApplyNo())
            );
            if(Objects.nonNull(prevInfo)){
                customerInfoVO.setCustName(prevInfo.getCustName());
                customerInfoVO.setCertNo(prevInfo.getCertNo());
                customerInfoVO.setRoleType(RoleTypeCreditEnum.ROLO_BRO.getCode());
            }
            customerInfoVoList.add(customerInfoVO);
        }
        return IResponse.success(customerInfoVoList);
    }

    /**
     * queryCustSummaryInfoApplyNo
     * <p>Description: 根据申请编号查询客户概况信息</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryCustSummaryInfoApplyNo")
    @ApiOperation(value = "根据申请编号查询客户概况信息")
    public IResponse<CustInfoVo> queryCustSummaryInfoApplyNo(@ModelAttribute WorkTaskPoolCondition workTaskPoolCondition) {
        CustInfoVo custInfoVo = new CustInfoVo();
        CaseCarInfo caseCarInfo = caseCarInfoService.getOne(Wrappers.<CaseCarInfo>query().lambda()
                .eq(CaseCarInfo::getApplyNo, workTaskPoolCondition.getApplyNo())
        );
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo, workTaskPoolCondition.getApplyNo())
        );

        /**  车型  **/
        custInfoVo.setModelName(caseCarInfo.getModelName());
        /**  首付比例 贷款期限 贷款金额 **/
        FinCostDetails finCostInfo = caseCostInfoService.getOne(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, caseCarInfo.getApplyNo())
                .eq(FinCostDetails::getCostType, CostTypeEnum.CARAMT.getCode())
        );
        if (ObjectUtils.isNotEmpty(finCostInfo)) {
            custInfoVo.setDownPayScale(finCostInfo.getDownPayScale());
            custInfoVo.setLoanTerms(finCostInfo.getLoanTerm());
            custInfoVo.setLoanAmt(caseBaseInfo.getLoanAmtRepeat());
        }

        /**  共借人担保人信息  **/
        List<CaseCustInfo> caseCustInfoList = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, workTaskPoolCondition.getApplyNo())
                .ne(CaseCustInfo::getCustRole,CustRoleEnum.MIANCUST.getCode())
                .orderByAsc(CaseCustInfo::getCustRelation)
        );
        List<CustSummaryInfoVo> custSummaryInfoVoList = new ArrayList<>();
        String borrowerCertno = "";//主借人身份证号
        String coborrowerCertno = "";//共借人身份证号
        if (CollectionUtils.isNotEmpty(caseCustInfoList)) {
            for (CaseCustInfo caseCustInfo : caseCustInfoList) {

                CustSummaryInfoVo custSummaryInfoVo = new CustSummaryInfoVo();
                custSummaryInfoVo.setCustRelation(caseCustInfo.getCustRelation());
                custSummaryInfoVo.setCustName(caseCustInfo.getCustName());

                CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                        .eq(CaseCustIndividual::getCustId, caseCustInfo.getId())
                );
                if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                    custSummaryInfoVo.setAge(caseCustIndividual.getAge());
                    custSummaryInfoVo.setUnitName(caseCustIndividual.getUnitName());
                    custSummaryInfoVo.setSex(caseCustIndividual.getSex());
                    custSummaryInfoVo.setMaritalStatus(caseCustIndividual.getMaritalStatus());
                }
                String province = addressService.getLabelByCode(caseCarInfo.getPurchaseProvince());
                String city = addressService.getLabelByCode(caseCarInfo.getPurchaseCity());
                /**  购车地址  **/
                //只取城市
                custSummaryInfoVo.setPurchaseAddress(province+city);

                String plateProvince = addressService.getLabelByCode(caseCarInfo.getLicenseProvince());
                String plateCity = addressService.getLabelByCode(caseCarInfo.getLicenseCity());
                /**  上牌地址  **/
                custSummaryInfoVo.setPlateAddress(plateProvince + plateCity);
                /**  居住地、工作地  **/
                List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                        .eq(CaseCustAddress::getAddressType, AddressTypeEnum.LIVING.getCode())
                        .orderByDesc(CaseCustAddress::getCreateTime)
                );
                if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                    String livingProvince = addressService.getLabelByCode(caseCustAddressList.get(0).getProvince());
                    String livingCity = addressService.getLabelByCode(caseCustAddressList.get(0).getCity());
                    custSummaryInfoVo.setLivingAddress(livingProvince+livingCity);
                }
                caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
                        .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                        .eq(CaseCustAddress::getAddressType, AddressTypeEnum.WORK.getCode())
                        .orderByDesc(CaseCustAddress::getCreateTime)
                );
                if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
                    CaseCustAddress address = caseCustAddressList.get(0);
                    log.info("===============address:"+address);
                    String workProvince =  addressService.getLabelByCode(caseCustAddressList.get(0).getProvince());
                    String workCity =  addressService.getLabelByCode(caseCustAddressList.get(0).getCity());
                    custSummaryInfoVo.setWorkAddress(workProvince+workCity);
                }
                //主借人
                if (caseCustInfo.getCustRole().equals(AfsEnumUtil.key(CustRoleEnum.MIANCUST.getCode()))) {
                    borrowerCertno = caseCustInfo.getCertNo();
                } else if (caseCustInfo.getCustRole().equals(AfsEnumUtil.key(CustRoleEnum.COMMONCSUT.getCode()))) {
                    coborrowerCertno = caseCustInfo.getCertNo();
                }
                custSummaryInfoVoList.add(custSummaryInfoVo);
            }
        }

        /**  TODO GJQ 累加贷款总额  **/
        CertNoDto certNoDto = new CertNoDto();
        certNoDto.setBorrowerCertno(borrowerCertno);
        certNoDto.setCoborrowerCertno(coborrowerCertno);
        custInfoVo.setTotalLoanAmt(caseBaseInfo.getTotalLoanAmt());
        custInfoVo.setCustInfoList(custSummaryInfoVoList);

        return IResponse.success(custInfoVo);
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(false);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * @description: 解析个人客户信息详情冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public CaseCustIndividual getEvidenceForCustIndividual(String jsonStr, Long id) {
        CaseCustIndividual caseCust = new CaseCustIndividual();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseCustIndividualList = jsonObject.getJSONArray("caseCustIndividualList");
            if (ObjectUtils.isNotEmpty(caseCustIndividualList) && caseCustIndividualList.size() > 0) {
                caseCustIndividualList.toJavaList(CaseCustIndividual.class).forEach(caseCustIndividual -> {
                    if (caseCustIndividual.getCustId().equals(id)) {
                        BeanUtils.copyProperties(caseCustIndividual, caseCust);
                    }
                });
            }
        }
        return caseCust;
    }

    /**
     * @description: 解析企业客户信息详情冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public CaseEnterpriseCustomerDetails getcaseEnterpriseCustomerDetails(String jsonStr, Long id) {
        CaseEnterpriseCustomerDetails caseCust = new CaseEnterpriseCustomerDetails();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseEnterpriseCustomerDetailsList = jsonObject.getJSONArray("caseEnterpriseCustomerDetailsList");
            if (ObjectUtils.isNotEmpty(caseEnterpriseCustomerDetailsList) && caseEnterpriseCustomerDetailsList.size() > 0) {
                caseEnterpriseCustomerDetailsList.toJavaList(CaseCustIndividual.class).forEach(caseCustIndividual -> {
                    if (caseCustIndividual.getCustId().equals(id)) {
                        BeanUtils.copyProperties(caseCustIndividual, caseCust);
                    }
                });
            }
        }
        return caseCust;
    }
    /**
     * @description: 解析客户信息详情冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public CaseCustInfo getEvidenceForCustInfo(String jsonStr, Long id) {
        CaseCustInfo caseCust = new CaseCustInfo();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseCustList = jsonObject.getJSONArray("caseCustInfoList");
            if (ObjectUtils.isNotEmpty(caseCustList) && caseCustList.size() > 0) {
                caseCustList.toJavaList(CaseCustInfo.class).forEach(caseCustInfo -> {
                    if (caseCustInfo.getId().equals(id)) {
                        BeanUtils.copyProperties(caseCustInfo, caseCust);
                    }
                });
            }
        }
        return caseCust;
    }
    /**
     * @description: 解析地址冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public List<CaseCustAddress> getEvidenceAddress(String jsonStr, Long id) {
        List<CaseCustAddress> list= new ArrayList();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray addressList = jsonObject.getJSONArray("custAddressList");
            if (ObjectUtils.isNotEmpty(addressList) && addressList.size() > 0) {
                addressList.toJavaList(CaseCustAddress.class).forEach(address -> {
                    if(address.getCustId().equals(id)&&!address.getAddressType().equals(AfsEnumUtil.key(AddressTypeEnum.CENSUS.getCode()))){
                        CaseCustAddress custAddress = new CaseCustAddress();
                        BeanUtils.copyProperties(address, custAddress);
                        list.add(custAddress);
                    }
                });
            }
        }

        return list;
    }
    /**
     * @description: 解析联系人冗余数据
     * <AUTHOR>
     * @created 2020/8/16 20:27
     * @version 1.0
     */
    public List<CaseCustContact> getEvidenceContractInfo(String jsonStr) {
        List<CaseCustContact> list= new ArrayList();
        if (StringUtils.isNotBlank(jsonStr)) {
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONArray caseContactList = jsonObject.getJSONArray("caseContactList");
            if (ObjectUtils.isNotEmpty(caseContactList) && caseContactList.size() > 0) {
                list=caseContactList.toJavaList(CaseCustContact.class);
            }
        }

        return list;
    }
    /**
     * @description: 获取冗余数据
     * <AUTHOR>
     * @created 2020/8/16 21:42
     * @version 1.0
     */
    public List getEvidenceIndividual(String applyNo, CaseCustInfo caseCustInfo) {

        RedundantSignVo signVo= new RedundantSignVo();
        signVo.setBackSign(WhetherEnum.NO.getCode());
        signVo.setReconsiderSign(WhetherEnum.NO.getCode());
        CaseCustInfo caseCust = null;
        CaseCustIndividual custIndividual = null;
        CaseEnterpriseCustomerDetails Details=null;
        List list= new ArrayList();
        CaseRedundantInfo redundantInfo = redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(applyNo), CaseRedundantInfo::getApplyNo, applyNo));
        if (ObjectUtils.isNotEmpty(redundantInfo)) {
            String backSign = redundantInfo.getBackSign();
            if (WhetherEnum.YES.getCode().equals(backSign)) {
                String backEvidence = redundantInfo.getBackEvidence();
                if (StringUtils.isNotBlank(backEvidence)) {
                    caseCust = this.getEvidenceForCustInfo(backEvidence, caseCustInfo.getId());
                    if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)){
                        custIndividual = this.getEvidenceForCustIndividual(backEvidence, caseCustInfo.getId());
                    }else {
                        Details =this.getcaseEnterpriseCustomerDetails(backEvidence,caseCustInfo.getId());
                    }

                    signVo.setBackSign(WhetherEnum.YES.getCode());
                    signVo.setReconsiderSign(WhetherEnum.NO.getCode());
                }
            }
            String reconsiderSign = redundantInfo.getReconsiderSign();
            if (WhetherEnum.YES.getCode().equals(reconsiderSign)) {
                String reconsiderEvidence = redundantInfo.getReconsiderEvidence();
                if (StringUtils.isNotBlank(reconsiderEvidence)) {
                    caseCust = this.getEvidenceForCustInfo(reconsiderEvidence, caseCustInfo.getId());
                    if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)){
                        custIndividual = this.getEvidenceForCustIndividual(reconsiderEvidence, caseCustInfo.getId());
                    }else {
                        Details =this.getcaseEnterpriseCustomerDetails(reconsiderEvidence,caseCustInfo.getId());
                    }
                    signVo.setBackSign(WhetherEnum.NO.getCode());
                    signVo.setReconsiderSign(WhetherEnum.YES.getCode());
                }
            }
        }
        list.add(signVo);
        list.add(caseCust);
        list.add(Details);
        list.add(custIndividual);
        return list;
    }

    /**
     * @description: 获取地址冗余数据
     * <AUTHOR>
     * @created 2020/8/16 21:42
     * @version 1.0
     */
    public List<CaseCustAddress> getEvidenceForAddress(String applyNo, Long id ) {
        List list= new ArrayList();
        List<CaseCustAddress> addressList = new ArrayList<>();
        RedundantSignVo signVo= new RedundantSignVo();
        signVo.setBackSign(WhetherEnum.NO.getCode());
        signVo.setReconsiderSign(WhetherEnum.NO.getCode());
        CaseRedundantInfo redundantInfo = redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(applyNo), CaseRedundantInfo::getApplyNo, applyNo));
        if (ObjectUtils.isNotEmpty(redundantInfo)) {
            String backSign = redundantInfo.getBackSign();
            if (WhetherEnum.YES.getCode().equals(backSign)) {
                String backEvidence = redundantInfo.getBackEvidence();
                if (StringUtils.isNotBlank(backEvidence)) {
                    addressList = this.getEvidenceAddress(backEvidence,id);
                    signVo.setBackSign(WhetherEnum.YES.getCode());
                    signVo.setReconsiderSign(WhetherEnum.NO.getCode());
                }
            }
            String reconsiderSign = redundantInfo.getReconsiderSign();
            if (WhetherEnum.YES.getCode().equals(reconsiderSign)) {
                String reconsiderEvidence = redundantInfo.getReconsiderEvidence();
                if (StringUtils.isNotBlank(reconsiderEvidence)) {
                    addressList = this.getEvidenceAddress(reconsiderEvidence, id);
                    signVo.setBackSign(WhetherEnum.NO.getCode());
                    signVo.setReconsiderSign(WhetherEnum.YES.getCode());
                }
            }
        }
        list.add(signVo);
        list.add(addressList);
        return list;
    }

    /**
     * @description: 获取联系人冗余数据
     * <AUTHOR>
     * @created 2020/8/16 21:42
     * @version 1.0
     */
    public List<CaseCustContact> getEvidenceForContact(String applyNo) {
        List<CaseCustContact> list= new ArrayList();
        CaseRedundantInfo redundantInfo = redundantInfoService.getOne(Wrappers.<CaseRedundantInfo>query().lambda()
                .eq(StringUtils.isNotBlank(applyNo), CaseRedundantInfo::getApplyNo, applyNo));
        if (ObjectUtils.isNotEmpty(redundantInfo)) {
            String backSign = redundantInfo.getBackSign();
            if (WhetherEnum.YES.getCode().equals(backSign)) {
                String backEvidence = redundantInfo.getBackEvidence();
                if (StringUtils.isNotBlank(backEvidence)) {
                    list = this.getEvidenceContractInfo(backEvidence);
                }
            }
            String reconsiderSign = redundantInfo.getReconsiderSign();
            if (WhetherEnum.YES.getCode().equals(reconsiderSign)) {
                String reconsiderEvidence = redundantInfo.getReconsiderEvidence();
                if (StringUtils.isNotBlank(reconsiderEvidence)) {
                    list = this.getEvidenceContractInfo(reconsiderEvidence);
                }
            }
        }
        return list;
    }


    @PostMapping(value = "/getCustomerDetailInfo")
    @ApiOperation(value = "根据申请编号获取客户详细信息")
    public IResponse<InterfaceInfoVo> getCustomerDetailInfo(@RequestParam(value = "applyNo",required = false) String applyNo) {

        log.info("-----------------------根据申请编号获取客户详细信息，入参为：{}-----------------------",applyNo);
        if(StringUtils.isBlank(applyNo)||"null".equals(applyNo)){
            return IResponse.fail("入参申请号不能为空");
        }

        //01-主借人、02-共借人、03-担保人
        String mainRole = "01";
        String commonRole = "02";
        String assureRole = "03";
        String man = "M";

        List<CaseContractInfo> contractInfos = caseContractInfoService.list(Wrappers.<CaseContractInfo>query().lambda().eq(CaseContractInfo::getContractNo, applyNo));
        if(CollectionUtil.isNotEmpty(contractInfos)){
            //通过applyNo去查询申请号，如果查询到了，说明入参applyNo传入的是合同号
            applyNo = contractInfos.get(0).getApplyNo();
            log.info("-----------------------根据申请编号获取客户详细信息，使用applyNo查询申请编号，结果为：{}-----------------------",applyNo);
        }

        CustomerInfoVo mainCustomerInfoVo = caseCustInfoService.getCustomerInfo(applyNo,mainRole);
        CustomerInfoVo commonCustomerInfoVo = caseCustInfoService.getCustomerInfo(applyNo,commonRole);
        CustomerInfoVo assureCustomerInfoVo = caseCustInfoService.getCustomerInfo(applyNo,assureRole);
        InterfaceInfoVo otherInfo = caseCustInfoService.getCustomerOtherInfo(applyNo);
        List<CaseContractInfo> caseContractInfos = caseContractInfoService.list(Wrappers.<CaseContractInfo>query().lambda().eq(CaseContractInfo::getApplyNo, applyNo));
        if(CollectionUtil.isNotEmpty(caseContractInfos)){
            ChannelWitnessInfo channelWitnessInfo = channelWitnessInfoService.getOne(Wrappers.<ChannelWitnessInfo>query().lambda()
                    .eq(ChannelWitnessInfo::getChannelId, otherInfo.getChannelId())
                    .eq(ChannelWitnessInfo::getWitnessIdCard, caseContractInfos.get(0).getWitnessId())
                    .eq(ChannelWitnessInfo::getBusinessAttributes, caseContractInfos.get(0).getBusinessType()));
            if (channelWitnessInfo!=null){
                otherInfo.setOperatorUser(channelWitnessInfo.getWitnessName());
            }
        }

        InterfaceInfoVo interfaceInfoVo = new InterfaceInfoVo();
        if(mainCustomerInfoVo!=null) {
            interfaceInfoVo.setBorrower(mainCustomerInfoVo.getCustName());
            interfaceInfoVo.setBorrowerIdCardType(mainCustomerInfoVo.getCertType());
            interfaceInfoVo.setBorrowerIdCard(mainCustomerInfoVo.getCertNo());
            interfaceInfoVo.setBorrowerPhone(mainCustomerInfoVo.getTelPhone());
            interfaceInfoVo.setBorrowerAddress(mainCustomerInfoVo.getDetailAddress());
            interfaceInfoVo.setBorrowerSex(man.equals(mainCustomerInfoVo.getSex()) ? SexEnum.man.toString() : SexEnum.woman.toString());
            interfaceInfoVo.setBorrowerMarriage(mainCustomerInfoVo.getMaritalStatus());
        }
        if(commonCustomerInfoVo!=null) {
            interfaceInfoVo.setCoborrower(commonCustomerInfoVo.getCustName());
            interfaceInfoVo.setCoborrowerIdCardType(commonCustomerInfoVo.getCertType());
            interfaceInfoVo.setCoborrowerIdCard(commonCustomerInfoVo.getCertNo());
            interfaceInfoVo.setCoborrowerPhone(commonCustomerInfoVo.getTelPhone());
            interfaceInfoVo.setCoborrowerSex(man.equals(commonCustomerInfoVo.getSex()) ? SexEnum.man.toString() : SexEnum.woman.toString());
            interfaceInfoVo.setCoborrowerRelation(commonCustomerInfoVo.getCustRelation());
            interfaceInfoVo.setCoborrowerAddress(commonCustomerInfoVo.getDetailAddress());
        }
        if(assureCustomerInfoVo!=null) {
            interfaceInfoVo.setGuarantor(assureCustomerInfoVo.getCustName());
            interfaceInfoVo.setGuarantorIdCardType(assureCustomerInfoVo.getCertType());
            interfaceInfoVo.setGuarantorIdCard(assureCustomerInfoVo.getCertNo());
            interfaceInfoVo.setGuarantorPhone(assureCustomerInfoVo.getTelPhone());
            interfaceInfoVo.setGuarantorSex(man.equals(assureCustomerInfoVo.getSex()) ? SexEnum.man.toString() : SexEnum.woman.toString());
            interfaceInfoVo.setGuarantorRelation(assureCustomerInfoVo.getCustRelation());
            interfaceInfoVo.setGuarantorAddress(assureCustomerInfoVo.getDetailAddress());
        }
        if(otherInfo != null){
            interfaceInfoVo.setApplicationNo(applyNo);
            interfaceInfoVo.setApplicationStatus(otherInfo.getApplicationStatus());
            interfaceInfoVo.setApplicationDate(otherInfo.getApplicationDate());
            interfaceInfoVo.setDealer(otherInfo.getDealer());
            interfaceInfoVo.setDealerAddress(otherInfo.getDealerAddress());
            interfaceInfoVo.setDealerPhone(otherInfo.getDealerPhone());
            interfaceInfoVo.setOperatorUser(otherInfo.getOperatorUser());
            interfaceInfoVo.setAssetCondition(otherInfo.getAssetCondition());
            interfaceInfoVo.setMaker(otherInfo.getMaker());
            interfaceInfoVo.setBrand(otherInfo.getBrand());
            interfaceInfoVo.setModel(otherInfo.getModel());
            interfaceInfoVo.setKineticParameter(otherInfo.getKineticParameter());
            interfaceInfoVo.setVin(otherInfo.getVin());
            interfaceInfoVo.setColor(otherInfo.getColor());
            interfaceInfoVo.setPrice(otherInfo.getPrice());
            if(ObjectUtils.isNotEmpty(otherInfo.getPledgeFlag())){
                if ("1".equals(otherInfo.getPledgeFlag())){
                    interfaceInfoVo.setPledgeFlag("yes");
                } else if ("0".equals(otherInfo.getPledgeFlag())){
                    interfaceInfoVo.setPledgeFlag("no");
                } else {
                    interfaceInfoVo.setPledgeFlag(null);
                }
            }
        }
        return IResponse.success(interfaceInfoVo);
    }


    @PostMapping(value = "/getCustomerInfo")
    @ApiOperation(value = "根据申请编号获取客户信息")
    public IResponse<BasicCustBaseInfoVo> getCustomerInfo(@RequestParam(value = "applyNo",required = false) String applyNo) {
        log.info("------------------根据申请编号获取客户信息入参：[{}]------------------",applyNo);
        if(StringUtils.isBlank(applyNo)||"null".equals(applyNo)){
            return IResponse.fail("申请编号入参不能为空！");
        }
        //01-主借人、02-共借人、03-担保人
        String mainRole = "01";
        //合同激活
        String activate = "activate";
        //获取主借人信息
        List<CaseCustInfo> custInfos = caseCustInfoService.list(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getCustRole, mainRole)
                .and(wrapper1 -> wrapper1.eq(CaseCustInfo::getApplyNo, applyNo)
                        .or(wrapper2 -> wrapper2.eq(CaseCustInfo::getCertNo, applyNo))
                        .or(wrapper3 -> wrapper3.eq(CaseCustInfo::getTelPhone, applyNo))));
        if(custInfos!= null && custInfos.size() >0){
            CaseCustInfo custInfo = custInfos.get(0);
            //获取银行卡信息
            List<String> applyNos = new ArrayList<>();
            for(CaseCustInfo caseCustInfo : custInfos){
                applyNos.add(caseCustInfo.getApplyNo());
            }
            List<CustBaseInfoVo> bankInfos = caseCustInfoService.getBankInfoByApplyNo(applyNos);
            if(bankInfos != null && bankInfos.size() > 0){
                int size = bankInfos.size();
                int removeSize = 0;
                for(int i =bankInfos.size()-1;i>=0;i-- ){
                    LoanActivatePool pool = loanActivateService.getOne(Wrappers.<LoanActivatePool>query().lambda()
                            .eq(LoanActivatePool::getContractNo, bankInfos.get(i).getContractNo()));
                    if(mainRole.equals(bankInfos.get(i).getCustRole())){
                        bankInfos.get(i).setDefaultFlag("1");
                    }else{
                        bankInfos.get(i).setDefaultFlag("0");
                    }
                    if(pool != null && activate.equals(pool.getActStatus())){
                        bankInfos.remove(i);
                        removeSize++;
                        applyNos.remove(pool.getApplyNo());
                    }
                }
                if(removeSize == size){
                    log.info("----------------------查询到的合同均已激活,不返回数据--------------------");
                    return IResponse.fail("查询到的合同均已激活,不返回数据");
                }
            }
            BasicCustBaseInfoVo basicCustBaseInfoVo = new BasicCustBaseInfoVo();
            if(CollectionUtils.isNotEmpty(bankInfos)){
                basicCustBaseInfoVo.setCustBaseInfoList(bankInfos);
                basicCustBaseInfoVo.setLoanNum(String.valueOf(bankInfos.size()));
            }else{
                List<CustBaseInfoVo> vos = new ArrayList<>();
                for (String applyNum : applyNos){
                    CustBaseInfoVo vo = new CustBaseInfoVo();
                    CaseBaseInfo one = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda().eq(CaseBaseInfo::getApplyNo, applyNum));
                    vo.setContractNo(applyNum);
                    vo.setContractStatus(one.getBusinessStateIn());
                    /*************角色类型查不到,暂时给空值**************/
                    vo.setCustRole(null);
                    vos.add(vo);
                }
                basicCustBaseInfoVo.setCustBaseInfoList(vos);
                basicCustBaseInfoVo.setLoanNum(String.valueOf(applyNos.size()));
            }
            basicCustBaseInfoVo.setCustName(custInfo.getCustName());
            basicCustBaseInfoVo.setPhone(custInfo.getTelPhone());
            basicCustBaseInfoVo.setCertNo(custInfo.getCertNo());
            basicCustBaseInfoVo.setCustRole(mainRole);
            log.info("------------------根据申请编号获取客户信息查询到的数据为：[{}]------------------",basicCustBaseInfoVo);
            return IResponse.success(basicCustBaseInfoVo);
        }
        log.info("------------------查询不到主借人信息,不返回数据------------------");
        return IResponse.fail("查询结果不存在");
    }

    /**
     * @description: 主借人雇佣司机查询
     * <AUTHOR>
     * @created 2020/12/4 11:24
     * @version 1.0
     */
    @PostMapping(value = "/queryHireDriver")
    @ApiOperation(value = "按照申请编号查询主借人基本信息")
    public IResponse<List<HireDriverInfoVo>> queryHireDriver(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        List<HireDriverInfoVo> hireDriverInfoVoList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            CaseCustIndividual  custIndividual=caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                    .eq(CaseCustIndividual::getCustId,caseCustInfo.getId()));
            if(ObjectUtils.isNotEmpty(custIndividual)){
                HireDriverInfoVo hireDriverInfoVo = new HireDriverInfoVo();
                hireDriverInfoVo.setApplyNo(caseInfoQueryCondition.getApplyNo());
                hireDriverInfoVo.setBuckleCore(custIndividual.getBuckleCore());
                hireDriverInfoVo.setDriverCertNo(custIndividual.getDriverCertNo());
                hireDriverInfoVo.setDriverLicenceNo(custIndividual.getDriverLicenceNo());
                hireDriverInfoVo.setDriverName(custIndividual.getDriverName());
                hireDriverInfoVo.setTrafficNetwork(custIndividual.getTrafficNetwork());
                hireDriverInfoVo.setIsDriver(custIndividual.getIsDriver());
                hireDriverInfoVoList.add(hireDriverInfoVo);
            }
        }
        return IResponse.success(hireDriverInfoVoList);
    }
    /**
     * @description: 更新雇佣司机信息
     * <AUTHOR>
     * @created 2020/12/4 11:38
     * @version 1.0
     */
    @PostMapping(value = "/saveHireDriver")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<String> saveHireDriver(@ModelAttribute HireDriverInfoVo hireDriverInfoVo) {
        if (ObjectUtils.isNotEmpty(hireDriverInfoVo)) {
            /**  客户信息  **/
            CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                    .eq(CaseCustInfo::getApplyNo, hireDriverInfoVo.getApplyNo())
                    .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            if (ObjectUtils.isNotEmpty(caseCustInfo)) {
                CaseCustIndividual  custIndividual=caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                        .eq(CaseCustIndividual::getCustId,caseCustInfo.getId()));
                if(ObjectUtils.isNotEmpty(custIndividual)){
                    //更新司机证件档案编号
                    custIndividual.setDriverLicenceNo(hireDriverInfoVo.getDriverLicenceNo());
                    caseCustIndividualService.updateById(custIndividual);
                }
            }
            return IResponse.success("更新雇佣司机信息");
        }
        return IResponse.fail("客户信息为空");
    }

    /**
     * 查询历史信息匹配
     * */
    @PostMapping(value = "/getHistoryInfo")
    @Transactional(rollbackFor = Exception.class)
    public IResponse<HistoryInfoVo> getProfilerInfo(@ModelAttribute HistoryInfoVo profilerInfo) {
        List<HistoryInfoVo> list = caseCustInfoService.getProfilerInfo(profilerInfo);
        return IResponse.success(list);
    }

    /**
     * queryContactList
     * <p>Description: 查询联系人列表</p>
     *
     * @param
     * @return
     */
    @PostMapping(value = "/queryContactListAll")
    @ApiOperation(value = "按照申请编号查询联系人列表")
    public IResponse queryContactListAll(@ModelAttribute CaseInfoQueryCondition caseInfoQueryCondition) {
        return IResponse.success(callRemarkService.getCaseTelCheckInfoConditions(caseInfoQueryCondition.getApplyNo()));
    }

    @PostMapping(value = "/updateRemarkById")
    @ApiOperation(value = "根据id更新联系人信息")
    public IResponse<String> updateRemarkById(@RequestBody CaseTelCheckInfoCondition caseTelCheckInfoCondition){
        Long id = caseTelCheckInfoCondition.getId();
        // 1对多 存储致电备注
        CaseCustCallRemark callRemark = callRemarkService.getOne(Wrappers.<CaseCustCallRemark>lambdaQuery()
            .eq(CaseCustCallRemark::getContactId, id.toString())
            .eq(CaseCustCallRemark::getCreateBy, SecurityUtils.getUsername())
        );

         callRemark = Optional.ofNullable(callRemark)
            .orElseGet(() -> {
                CaseCustCallRemark tmp = new CaseCustCallRemark();
                tmp.setContactId(id.toString());
                return tmp;
            });
        callRemark.setRemark(caseTelCheckInfoCondition.getRemark());
        callRemark.setCallType(caseTelCheckInfoCondition.getCallType());

        callRemarkService.saveOrUpdate(callRemark);

        return IResponse.success("success");

    }

    public HashMap<String,Double> getLatLng(CaseCustAddress caseCustAddress){
        String province = caseCustAddress.getProvince();
        String city = caseCustAddress.getCity();
        String county = caseCustAddress.getCounty();
        ArrayList<String> stringList = new ArrayList<>();
        if (StringUtils.isNotBlank(province)){
            stringList.add(province);
        }
        if (StringUtils.isNotBlank(city)){
            stringList.add(city);
        }
        if (StringUtils.isNotBlank(county)){
            stringList.add(county);
        }
        String newAddress = afsLocationFeign.nameByCodeList(stringList).getData() + caseCustAddress.getDetailAddress();
        HashMap<String, Double> hashMap = new HashMap<>();
        if (StringUtils.isNotBlank(newAddress)) {
        }
        return hashMap;
    }


    private Map<String, String> applyCaseHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }


    @PostMapping(value = "/queryGuarantee")
    public IResponse queryGuarantee(@RequestBody ApplyGuarantee applyGuarantee){
        ApplyGuarantee guarantee = applyContractFeign.queryGuarantee(applyGuarantee, applyCaseHeader());
        if(EmptyUtils.isNotEmpty(guarantee)){
            return IResponse.success(guarantee);
        }else {
            return IResponse.fail("未获取到保单信息");
        }
    }


    @PostMapping(value = "/queryGuaranteeHistoryInfo")
     public IResponse queryGuaranteeHistoryInfo(@RequestBody QueryCondition<CaseChangeCondition> queryCondition){
        IResponse iResponse = applyContractFeign.queryGuaranteeHistoryInfo(queryCondition, applyCaseHeader());
        return IResponse.success(iResponse.getData());
    }

    @PostMapping(value = "/guaranteeCheck")
    public IResponse<String> guaranteeCheck(@RequestBody ApplyGuarantee applyGuarantee ){
        String retrunInfo = applyContractFeign.guaranteeCheck(applyGuarantee,applyCaseHeader());
        return IResponse.success(retrunInfo);
    }


    @PostMapping(value = "/queryUpdateHistoryData")
    public IResponse queryUpdateHistoryData(@RequestParam("applyNo") String applyNo ){
        List<CaseCustChangeRecord>  caseCustChangeRecordList = caseCustChangeRecordService.list(Wrappers.<CaseCustChangeRecord>lambdaQuery().eq(CaseCustChangeRecord::getApplyNo, applyNo));
        return IResponse.success(caseCustChangeRecordList);
    }

    @PostMapping(value = "/queryCustInfo")
    public IResponse<?> queryCustInfo(@RequestParam("applyNo") String applyNo ){
        CaseRiskCustInfo risk = riskCustInfoService.getOne(
            Wrappers.<CaseRiskCustInfo>lambdaQuery().eq(CaseRiskCustInfo::getApplyNo, applyNo));
        if (risk == null) {
            return IResponse.success("");
        }
        CaseCustInfo origin = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query()
            .lambda()
            .eq(CaseCustInfo::getApplyNo, applyNo)
            .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));

        CaseCustIndividual one = custIndividualService.lambdaQuery()
            .eq(CaseCustIndividual::getCustId, origin.getId())
            .one();

        CaseCustContact custContact = custContactService.lambdaQuery()
            .eq(CaseCustContact::getApplyNo, applyNo)
            .eq(CaseCustContact::getCustRelation, CustRelationEnums.spouse.getIndex())
            .one();
        // 1户籍地址 2居住地址 3单位地址
        Map<String, AddressVo> addressMap = new HashMap<>();
        List<CaseCustAddress> caseCustAddressList = caseCustAddressService.list(Wrappers.<CaseCustAddress>query().lambda()
            .eq(CaseCustAddress::getCustId,origin.getId()));
        if (CollectionUtils.isNotEmpty(caseCustAddressList)) {
            for (CaseCustAddress caseCustAddress : caseCustAddressList) {
                AddressVo addressVo = new AddressVo();
                BeanUtils.copyProperties(caseCustAddress, addressVo);
                String[] addressData = {caseCustAddress.getProvince(), caseCustAddress.getCity(), caseCustAddress.getCounty(),
                    caseCustAddress.getTown(), caseCustAddress.getStreet(), caseCustAddress.getDetailAddress()};
                addressVo.setDetailAddress(this.getAddress(addressData));
                addressMap.put(addressVo.getAddressType(),addressVo);
            }
        }

        log.info("addressMap={}", JSONUtil.parse(addressMap));
        List<CaseRiskCustInfoVO> list = new ArrayList<>();
        {
            // 姓名
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("姓名");
            infoVO.setOrigin(origin.getCustName());
            String custName = risk.getCustName();
            infoVO.setRisk(custName);
            infoVO.setResult(StrUtil.isBlank(custName) ? "" : StrUtil.equals(custName, origin.getCustName()) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 证件号码
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("证件号码");
            infoVO.setOrigin(origin.getCertNo());
            String idCard = risk.getIdCard();
            infoVO.setRisk(idCard);
            infoVO.setResult(StrUtil.isBlank(idCard) ? "" : StrUtil.equals(idCard, origin.getCertNo()) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 手机号码
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("手机号码");
            infoVO.setOrigin(origin.getTelPhone());
            String phone = risk.getPhone();
            infoVO.setRisk(phone);
            infoVO.setResult(StrUtil.isBlank(phone) ? "" : StrUtil.equals(phone, origin.getTelPhone()) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 婚姻状况
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("婚姻状况");
            String originMaritalStatus = MaritalStatusEnums.getName(one.getMaritalStatus());
            infoVO.setOrigin(originMaritalStatus);
            String maritalStatus = risk.getMaritalStatus();
            infoVO.setRisk(maritalStatus);
            infoVO.setResult(StrUtil.isBlank(maritalStatus) ? "" : StrUtil.equals(maritalStatus, originMaritalStatus) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 性别
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("性别");
            Enum anEnum = AfsEnumUtil.getEnum(one.getSex(), SexEnum.class);
            String desc = anEnum!=null?AfsEnumUtil.desc(anEnum):one.getSex();
            String sex = StrUtil.isNotBlank(desc) ? desc : one.getSex();
            infoVO.setOrigin(sex);
            String gender = risk.getGender();
            infoVO.setRisk(gender);
            infoVO.setResult(StrUtil.isBlank(gender) ? "" : StrUtil.equals(gender, sex) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 学历
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("学历");
            String education = one.getHighestEducation();
            String highestEducationName = DicUtils.dicData(education, "highestEducation");
            infoVO.setOrigin(highestEducationName);
            String qualification = risk.getQualification();
            infoVO.setRisk(qualification);
            infoVO.setResult(StrUtil.isBlank(qualification) ? "" : StrUtil.equals(qualification, highestEducationName) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 出生日期
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("出生日期");
            String birthDay = DateUtil.format(one.getBirthday(), "yyyyMMdd");
            infoVO.setOrigin(birthDay);
            String birthDayStr = risk.getBirthDay();
            infoVO.setRisk(birthDayStr);
            infoVO.setResult(StrUtil.isBlank(birthDayStr) ? "" : StrUtil.equals(birthDayStr.replaceAll("\\.",""), birthDay) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 国籍
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("国籍");
            String nationality1 = "00001".equals(one.getNationality()) ? "中国" : "";
            infoVO.setOrigin(nationality1);
            String nationality = risk.getNationality();
            infoVO.setRisk(nationality);
            infoVO.setResult(StrUtil.isBlank(nationality) ? "" : StrUtil.equals(nationality, nationality1) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 户籍地址
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("户籍地址");
            AddressVo addressVo = addressMap.get("1");
            String detailAddress = addressVo.getDetailAddress();
            infoVO.setOrigin(detailAddress);
            String replaceSpaceAfter = "";
            if (StrUtil.isNotBlank(detailAddress)) {
                replaceSpaceAfter="中国" + detailAddress.replaceAll("/","");
            }
            String permanentAddress = risk.getPermanentAddress();
            infoVO.setRisk(permanentAddress);
            infoVO.setResult(StrUtil.isBlank(permanentAddress) ? "" : StrUtil.equals(permanentAddress, replaceSpaceAfter) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 单位名称
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("单位名称");
            infoVO.setOrigin(one.getUnitName());
            String companyName = risk.getCompanyName();
            infoVO.setRisk(companyName);
            infoVO.setResult(StrUtil.isBlank(companyName) ? "" : StrUtil.equals(companyName, one.getUnitName()) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 单位性质
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("单位性质");
            String desc = UnitNatureEnum.getDesc(one.getUnitType());
            infoVO.setOrigin(desc);
            String companyNature = risk.getCompanyNature();
            infoVO.setRisk(companyNature);
            infoVO.setResult(StrUtil.isBlank(companyNature) ? "" : StrUtil.equals(companyNature, desc) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 行业类型
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("行业类型");
            String desc = IndustryTypeEnum.getDesc(one.getIndustryType());
            infoVO.setOrigin(desc);
            String industryType = risk.getIndustryType();
            infoVO.setRisk(industryType);
            infoVO.setResult(StrUtil.isBlank(industryType) ? "" : StrUtil.equals(industryType, desc) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 职业类型
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("职业类型");
            String jobsTypeName = DicUtils.dicData(one.getProfessionalType(), "jobsType");
            infoVO.setOrigin(jobsTypeName);
            String occupationType = risk.getOccupationType();
            infoVO.setRisk(occupationType);
            infoVO.setResult(StrUtil.isBlank(occupationType) ? "" : StrUtil.equals(occupationType, jobsTypeName) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 职务
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("职务");
            String desc = PositionEnum.getDesc(one.getPosition());
            desc = desc != null ? desc : one.getPosition();
            infoVO.setOrigin(StrUtil.isNotBlank(desc) ? desc : "");
            String position = risk.getPosition();
            infoVO.setRisk(position);
            infoVO.setResult(StrUtil.isBlank(position) ? "" : StrUtil.equals(position, desc) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 公司电话
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("公司电话");
            infoVO.setOrigin(StrUtil.isNotBlank(one.getUnitTelPhone())?one.getUnitTelPhone():"");
            String companyOfficeNumber = risk.getCompanyOfficeNumber();
            infoVO.setRisk(companyOfficeNumber);
            infoVO.setResult(StrUtil.isBlank(companyOfficeNumber) ? "" : StrUtil.equals(companyOfficeNumber, one.getUnitTelPhone()) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 单位地址
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("单位地址");
            String detailAddress = addressMap.get("3").getDetailAddress();
            infoVO.setOrigin(detailAddress);
            String replaceSpaceAfter = "";
            if (StrUtil.isNotBlank(detailAddress)) {
                replaceSpaceAfter="中国" + detailAddress.replaceAll("/","");
            }
            String companyAddress = risk.getCompanyAddress();
            infoVO.setRisk(companyAddress);
            infoVO.setResult(StrUtil.isBlank(companyAddress) ? "" : StrUtil.equals(companyAddress, replaceSpaceAfter) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 居住地址
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("居住地址");
            String detailAddress = addressMap.get("2").getDetailAddress();
            infoVO.setOrigin(detailAddress);
            String replaceSpaceAfter = "";
            if (StrUtil.isNotBlank(detailAddress)) {
                replaceSpaceAfter="中国" + detailAddress.replaceAll("/","");
            }
            String residentialAddress = risk.getResidentialAddress();
            infoVO.setRisk(residentialAddress);
            infoVO.setResult(StrUtil.isBlank(residentialAddress) ? "" : StrUtil.equals(residentialAddress, replaceSpaceAfter) ? "1" : "0");
            list.add(infoVO);
        }
        {
            // 居住状况
            CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
            infoVO.setItem("居住状况");
            String houseType = addressMap.get("2").getHouseType();
            String desc = HouseTypeEnum.getDesc(houseType);
            desc = desc != null ? desc : houseType;
            infoVO.setOrigin(StrUtil.isNotBlank(desc) ? desc : "");
            String residentialStatus = risk.getResidentialStatus();
            infoVO.setRisk(residentialStatus);
            infoVO.setResult(StrUtil.isBlank(residentialStatus) ? "" : StrUtil.equals(residentialStatus, desc) ? "1" : "0");
            list.add(infoVO);
        }
        if (custContact != null) {
            {
                // 配偶姓名
                CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
                infoVO.setItem("配偶姓名");
                String custName = custContact.getCustName();
                custName = StrUtil.isNotBlank(custName) ? custName : "";
                infoVO.setOrigin(custName);
                String spouseName = risk.getSpouseName();
                infoVO.setRisk(spouseName);
                String replaceSpaceAfter = "";
                if (StrUtil.isNotBlank(spouseName) && StrUtil.equals("--", spouseName)) {
                    replaceSpaceAfter = spouseName.replaceAll("--","");
                } else {
                    replaceSpaceAfter = spouseName;
                }
                infoVO.setResult(StrUtil.isBlank(spouseName) ? "" : StrUtil.equals(replaceSpaceAfter, custName) ? "1" : "0");
                list.add(infoVO);
            }
            {
                // 配偶证件号码
                CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
                infoVO.setItem("配偶证件号码");
                String certNo = custContact.getCertNo();
                certNo = StrUtil.isNotBlank(certNo) ? certNo : "";
                infoVO.setOrigin(certNo);
                String spouseCertNo = risk.getSpouseCertNo();
                infoVO.setRisk(spouseCertNo);
                String replaceSpaceAfter = "";
                if (StrUtil.isNotBlank(spouseCertNo) && StrUtil.equals("--", spouseCertNo)) {
                    replaceSpaceAfter = spouseCertNo.replaceAll("--","");
                } else {
                    replaceSpaceAfter = spouseCertNo;
                }
                infoVO.setResult(StrUtil.isBlank(spouseCertNo)? "" : StrUtil.equals(replaceSpaceAfter, certNo) ? "1" : "0");
                list.add(infoVO);
            }
            {
                // 配偶手机号
                CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
                infoVO.setItem("配偶手机号");
                String telPhone = custContact.getTelPhone();
                telPhone = StrUtil.isNotBlank(telPhone) ? telPhone : "";
                infoVO.setOrigin(telPhone);
                String spousePhone = risk.getSpousePhone();
                infoVO.setRisk(spousePhone);
                String replaceSpaceAfter = "";
                if (StrUtil.isNotBlank(spousePhone) && StrUtil.equals("--", spousePhone)) {
                    replaceSpaceAfter = spousePhone.replaceAll("--","");
                } else {
                    replaceSpaceAfter = spousePhone;
                }
                infoVO.setResult(StrUtil.isBlank(spousePhone) ? "" : StrUtil.equals(replaceSpaceAfter, telPhone) ? "1" : "0");
                list.add(infoVO);
            }
            {
                // 配偶单位名称
                CaseRiskCustInfoVO infoVO = new CaseRiskCustInfoVO();
                infoVO.setItem("配偶单位名称");
                String spouseCompany = one.getSpouseCompany();
                spouseCompany = StrUtil.isNotBlank(spouseCompany) ? spouseCompany : "";
                infoVO.setOrigin(spouseCompany);
                String spouseCompanyName = risk.getSpouseCompanyName();
                infoVO.setRisk(spouseCompanyName);
                String replaceSpaceAfter = "";
                if (StrUtil.isNotBlank(spouseCompanyName) && StrUtil.equals("--", spouseCompanyName)) {
                    replaceSpaceAfter = spouseCompanyName.replaceAll("--","");
                } else {
                    replaceSpaceAfter = spouseCompanyName;
                }
                infoVO.setResult(StrUtil.isBlank(spouseCompanyName) ? "" : StrUtil.equals(replaceSpaceAfter, spouseCompany) ? "1" : "0");
                list.add(infoVO);
            }
        }
        return IResponse.success(list);
    }

    /**
     * 查询对接资产管理系统所需客户信息
     * @param caseInfoQueryCondition
     * @return
     */
    @PostMapping(value = "/queryCustIndividual")
    @ApiOperation(value = "查询对接资产管理系统所需客户信息")
    @DS("afs_case_slave")
    public IResponse<MainBaseInfoVo> queryCustIndividualForAsset(@RequestBody CaseInfoQueryCondition caseInfoQueryCondition) {
        MainBaseInfoVo mainBaseInfoVo = new MainBaseInfoVo();
        /**  客户信息  **/
        CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, caseInfoQueryCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        if (ObjectUtils.isNotEmpty(caseCustInfo)) {
            if (caseCustInfo.getCustType().equals(CaseConstants.PERSONAL)) {
                /**  个人客户信息  **/
                CaseCustIndividual caseCustIndividual = caseCustIndividualService.getOne(Wrappers.<CaseCustIndividual>query().lambda()
                        .eq(CaseCustIndividual::getCustId, caseCustInfo.getId()));
                if (ObjectUtils.isNotEmpty(caseCustIndividual)) {
                    mainBaseInfoVo.setRegisteredResidenceType(caseCustIndividual.getRegisteredResidenceType());
                }

                return IResponse.success(mainBaseInfoVo);
            } else if (ObjectUtils.isNotEmpty(caseCustInfo) && caseCustInfo.getCustType().equals(CaseConstants.ENTERPRISE)) {

                return IResponse.success(mainBaseInfoVo);
            }
        }
        return IResponse.fail("数据错误9999");
    }

    @PostMapping(value = "/getLocationByAddress")
    @ApiOperation("地址查询经纬度")
    public IResponse<?> getLocationByAddress(@RequestBody AddressDetailsVO addressVO){
        return caseCustAddressService.getLocationByAddress(addressVO);
    }

    @PostMapping(value = "/getByApplyNo")
    public IResponse<?> getByApplyNo(@RequestBody NbgcCarInfoVo nbgcCarInfoVo){
        IResponse<?> iResponse = applyContractFeign.getByApplyNo(nbgcCarInfoVo, applyCaseHeader());
        if(StringUtil.equals(CommonConstants.SUCCESS,iResponse.getCode())&&EmptyUtils.isNotEmpty(iResponse)){
            return IResponse.success(iResponse.getData());
        }else {
            return IResponse.success("");
        }
    }

    /**
     * 更新案件查询-客户信息主表的手机号码
     *
     * @param caseCustInfoDto       caseCustInfoDto
     * @return the response
     */
    @PostMapping(value = "/updateCaseCustInfoDto")
    public IResponse updateCaseCustInfoDto(@RequestBody CaseCustInfoDto caseCustInfoDto) {
        if (StringUtils.isNotBlank(caseCustInfoDto.getApplyNo()) && StringUtils.isNotBlank(caseCustInfoDto.getNewPhone())) {
            CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>lambdaQuery()
                            .eq(CaseCustInfo::getApplyNo, caseCustInfoDto.getApplyNo())
                            .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));;
            log.info("CaseCustInfo 修改之前：{}", caseCustInfo);
            if (caseCustInfo != null) {
                caseCustInfo.setTelPhone(caseCustInfoDto.getNewPhone());
                caseCustInfoService.updateById(caseCustInfo);
            }
            log.info("CaseCustInfo 修改之后：{}", caseCustInfo);
        }
        return IResponse.success("ok");
    }

    /**
     * 工作单位企查查失信核查
     * @param unitName 单位名称
     * @return 工作单位企查查失信核查
     */
    @PostMapping(value = "/shiXinCheck")
    public IResponse shiXinCheck(@RequestParam("unitName") String unitName){
        if (StrUtil.isEmpty(unitName)) {
            throw new AfsBaseException("企查查失信核查失败: 请输入正确的单位名称！");
        }
        log.info("shiXinCheck接收到的参数={}", unitName);
        IResponse iResponse = applyContractFeign.shiXinCheck(unitName, applyCaseHeader());
        log.info("shiXinCheck接收进件服务的结果={}", iResponse);
        if (CaseConstants.CODE_SUCCESS.equals(iResponse.getCode())) {
            return IResponse.success(iResponse.getData());
        }
        return IResponse.fail("企查查失信核查失败! 请检查单位名称是否正确！");
    }
}

