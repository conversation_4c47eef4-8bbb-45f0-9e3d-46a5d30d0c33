package com.ruicar.afs.cloud.afscase.writeoff.feign;

import com.ruicar.afs.cloud.afscase.writeoff.dto.ContractDetailManageSearchDTO;
import com.ruicar.afs.cloud.afscase.writeoff.vo.BasicContractInfoVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.InputTaxVo;
import com.ruicar.afs.cloud.afscase.writeoff.vo.SettleContractDetailVO;
import com.ruicar.afs.cloud.common.core.feign.annotations.AfsFeignClear;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * The interface Write off feign.
 */
@FeignClient(value = "${com.ruicar.service-names.basic-server}", contextId = "basic-invoice-write")
public interface WriteOffFeign {

    /**
     * 传值
     *
     * @param inputTaxVo the input tax vo
     * @return response
     */
    @ApiOperation(value = "传值")
    @PostMapping(value = "/inputTax/addInputTax")
    @AfsFeignClear
    IResponse addInputTax(@RequestBody List<InputTaxVo> inputTaxVo);

    /**
     * Query write off response.
     *
     * @param searchVo the search vo
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryWriteOff")
    @ApiOperation(value = "查询满足服务费的合同")
    public IResponse<List<BasicContractInfoVo>> queryWriteOff(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * Query zjd write off response.
     *
     * @param searchVo the search vo
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryZjdWriteOff")
    @ApiOperation(value = "查询满足服务费的租金贷合同")
    public IResponse<List<BasicContractInfoVo>> queryZjdWriteOff(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * Query write advance response.
     *
     * @param searchVo the search vo
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryWriteAdvance")
    @ApiOperation(value = "根据合同号查询提前结清和合同关闭的合同")
    public IResponse<List<BasicContractInfoVo>> queryWriteAdvance(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * Query write bad response.
     *
     * @param searchVo the search vo
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryWriteBad")
    @ApiOperation(value = "根据合同号查询呆账核销的合同")
    public IResponse<List<String>> queryWriteBad(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * 查询提前结清合同信息
     *
     * @param channelId
     * @param zjdFlag
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryEarlyContract")
    @ApiOperation(value = "查询提前结清合同信息")
    IResponse<List<SettleContractDetailVO>> queryEarlyContract(@RequestParam String channelId, @RequestParam Boolean zjdFlag);

    /**
     * Query write cancel response.
     *
     * @param searchVo the search vo
     * @return the response
     */
    @PostMapping(value = "/contractMainInfo/queryWriteCancel")
    @ApiOperation(value = "根据合同号查询取消的合同")
    public IResponse<List<BasicContractInfoVo>> queryWriteCancel(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * 查询租金贷最新已回款的合同
     * @param searchVo
     * @return
     */
    @PostMapping(value = "/contractMainInfo/queryZjdReturn")
    @ApiOperation(value = "查询租金贷最新已回款的合同")
    IResponse<List<BasicContractInfoVo>> queryZjdReturn(@RequestBody ContractDetailManageSearchDTO searchVo);

    /**
     * 通过申请编号更新资方所属渠道
     * @param applyNo 申请编号
     * @param contractNo 合同编号
     * @param belongingCapital 渠道归属
     * @return 返回数据
     */
    @GetMapping("/basicBelongingCapital/updateBelongCapitalByApplyNo")
    @ApiOperation("通过申请编号更新资方所属渠道")
    boolean updateBelongCapitalByApplyNo(@RequestParam("applyNo") String applyNo,@RequestParam("contractNo") String contractNo, @RequestParam("belongingCapital") String belongingCapital);
}
