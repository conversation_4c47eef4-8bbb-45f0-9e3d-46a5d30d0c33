package com.ruicar.afs.cloud.afscase.writeoff.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务费计算规则分类
 */
@AllArgsConstructor
@Getter
public enum CalRuleEnum {

    /**
     * Pay cal rule enum.
     * 支付标准
     */
    PAY("pay", "支付标准"),
    /**
     * City cal rule enum.
     * 城市服务系数
     */
    CITY("city", "城市服务系数"),
    /**
     * Channel cal rule enum.
     * 经销商服务系数
     */
    CHANNEL("channel", "经销商服务系数"),
    /**
     * Flaw cal rule enum.
     * 经销商服务瑕疵评价系数
     */
    FLAW("flaw", "经销商服务瑕疵评价系数"),
    /**
     * 提前结清合同规则
     */
    EARLY_PAY("earlyPay", "提前结清合同规则");

    private String code;
    private String desc;

    private static Map<String, CalRuleEnum> map = new HashMap<>(CalRuleEnum.values().length);

    static {
        for (CalRuleEnum value : CalRuleEnum.values()) {
            map.put(value.getCode(), value);
        }
    }

    /**
     * Create cal rule enum.
     *
     * @param code the code
     * @return the cal rule enum
     */
    public static CalRuleEnum create(String code) {
        return map.get(code);
    }
}
