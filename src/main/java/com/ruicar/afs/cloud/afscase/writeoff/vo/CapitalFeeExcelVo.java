package com.ruicar.afs.cloud.afscase.writeoff.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 租金贷核销项金额excel导出
 */
@Data
public class CapitalFeeExcelVo implements Serializable {

    /**
     * 核销项编号
     */
    @ExcelProperty(value = "核销项编号")
    private String applyNo;
    /**
     * 核销项账期
     */
    @ExcelProperty(value = "核销项账期")
    private String writeOffMonth;
    /**
     * 业务模式
     */
    @ExcelProperty(value = "业务模式")
    private String writeOffType;
    /**
     * 经销商编号
     */
    @ExcelProperty(value = "经销商编号")
    private String channelCode;
    /**
     * 经销商名称
     */
    @ExcelProperty(value = "经销商名称")
    private String channelFullName;
    /**
     * 已到票金额
     */
    @ExcelProperty(value = "已到票金额")
    private BigDecimal receiveInvoiceAmt;
    /**
     * 历史已确认金额
     */
    @ExcelProperty(value = "历史已确认金额")
    private BigDecimal hisConfirmAmt;
    /**
     * 本次确认金额
     */
    @ExcelProperty(value = "本次确认金额")
    private BigDecimal thisConfirmAmt;
    /**
     * 累计已确认金额
     */
    @ExcelProperty(value = "累计已确认金额")
    private BigDecimal totalConfirmAmt;
    /**
     * 未确认金额
     */
    @ExcelProperty(value = "未确认金额")
    private BigDecimal waitConfirmAmt;
}
