package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: z<PERSON><PERSON>
 * @description 服务费支付规则详情
 * @date: 2024/8/6 10:42
 */
@Data
@TableName(value = "write_off_pay_rule_detail", autoResultMap = true)
public class WriteOffPayRuleDetail extends BaseEntity<WriteOffPayRuleDetail> {
    /**
     * WriteOffPayRule的id
     */
    private Long payRuleId;
    /**
     * 期数
     */
    private Integer termNo;
    /**
     * 延后月份
     */
    private Integer delayMonth;
    /**
     * 支付比例
     */
    private BigDecimal payRate;
}
