package com.ruicar.afs.cloud.afscase.approvetask.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.enums.common.CapitalOrderStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;



@Data
public class ApplyOrderInfoVo {
    
        /**
         * 申请编号
         */
        private String applyNo;
        /**
         * 客户姓名
         */
        private String custName;
        /**
         * 业务类型;新车、二手车
         */
        private String businessType;

        
        private String businessTypeName;
        /**
         * 主体属性
         */
        private String custType;
        /**
         * 运营方式
         */
        private String operateWay;
        /**
         * 车辆类型
         */
        private String carType;

        
        private String carTypeName;
        /**
         * 车辆属性
         */
        private String carNature;
        /**
         * 挂靠方式
         */
        private String affiliatedWay;
        /**
         * 购车目的
         */
        private String carPurpose;

        
        private String carPurposeName;
        /**
         * 产品ID
         */
        private String productId;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 申请状态
         */
        private String applyStatus;
        /**
         * 申请报单员
         */
        private String applyReporter;
        /**
         * 进件首次提交日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date intoFirstDate;
        /**
         * 核准日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date riskPassDate;
        /**
         * 核准日期（模板打印日期）
         */
        
        private String riskPassDateStr;
        /**
         * 案件评分
         */
        private BigDecimal caseScore;
        /**
         * 车商ID
         */
        private String dealerId;
        /**
         * 车商名称
         */
        private String dealerName;
        /**
         * 销售顾问
         */
        private String saleAdvisor;
        /**
         * 联系方式
         */
        private String salePhone;
        /**
         * 定位地址
         */
        private String locationAddress;
        /**
         * 经度
         */
        private String longitude;
        /**
         * 纬度
         */
        private String latitude;
        /**
         * 是否置顶
         */
        private String isTopping;
        /**
         * 置顶时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date toppingDate;
        /**
         * 备注
         */
        private String remarks;
        /**
         * 信贷专员联系方式
         */
        private String sellerPhone;
        /**
         * 信贷专员
         */
        private String sellerRealName;
        /**
         * 部门ID
         */
        private String deptId;
        /**
         * 合作商名称，不存库
         */
        
        private String channelName;
        /**
         * 签约标号
         */
        private String signFlag;
        /**
         * 合同重出标识
         */
        private String reappearFlag;
        /**
         * 撤销/拒绝日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date cancelDate;
        /**
         * 预审批ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long preId;
        /**
         * 贷款总金额
         */
        
        private BigDecimal money;
        /**
         * /**
         * 审批备注
         */
        
        private String recordRemarks;
        /**
         * 拒绝原因
         */
        private String refuseReason;
        /**
         * 是否附条件核准
         */
        private String isConsider;
        /**
         * 贷款期限
         */
        
        private Integer term;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date firstCommitTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date startTime;
        /**
         * 合同状态
         */
        
        private String viewContractStatus;

        /**
         * 是否归档
         */
        private String isFiling;
        /**
         * 放款操作人
         */
        
        private String contractCreateBy;
        /**
         * 是否已复议
         */
        
        private String isReconsider;

        /**
         * 是否经销商担保
         */
        private String ifDealerGuarantee;
        /**
         * 是否符合复议条件
         */
        
        private String isRejectReconsider;
        /**
         * 是否复议资料已提交
         */
        
        private String isReconsiderCom;
        /**
         * 退回原因
         */
        private String backReason;
        /**
         * 附条件核准原因-数据字典
         */
        private String considerReason;
        /**
         * 附条件核准原因-中文留言
         */
        private String considerReasonDesc;
        /**
         * 草稿订单编号
         */
        private String informalApplyNo;

        /** 租赁类型 0直租/1回租*/
        @TableField("rent_type")
        private String rentType ;
        /** 进件类型 0个人/1个人*/
        @TableField("input_type")
        private String inputType ;

        /** 是否是预审批的单子*/
        private String isPreApprove;

        /** 预审批订单编号*/
        @TableField("order_id")
        private String orderId ;

        /**
         * 渠道归属 00-SP 01-直营
         */
        private String channelBelong;

        /**
         * 直租类型 1-直租 2-直转回
         */
        private String directLeasingType;
        /**
         * 是否合同撤信审回首节点 0否 1是
         */
        
        private String isBackFirst="0";

        /**
         * 起租备注
         */
        private String rentRemark;

        /**
         * 是否需要代扣 1是 0否
         */
        private String isWithhold;

        
        private List<String> oldRemark;

        
        private String subjectCode;

        /**
         * 订单类型 0 零售 1 小微
         */
        @TableField("order_type")
        private String orderType;

        /**
         * 是否自查征信(0 是 1 否)
         */
        @TableField("is_self_check")
        private String isSelfCheck;

        /**
         * 是否复制单(0 是 1 否)
         */
        @TableField("copy_flag")
        private String copyFlag = "1";

        /**
         * 是否雇佣司机，1-是，0-否（默认值：0）
         */
        @TableField("has_hired_driver")
        private String hasHiredDriver;
        /**
         *   是否个人转企业，枚举 ：1 是，0 否
         */
        @ApiModelProperty("是否个人转企业，枚举 ：1 是，0 否")
        private String ifPersonalToEnterprise;

        @ApiModelProperty("业务子类")
        private String businessSubclass;

        @JSONField(name = "is_joint_lease")
        private String isJointLease;

        @ApiModelProperty(value="联合方审批状态,1：自动通过，2：自动拒绝，3：人工审批")
        private String applyJointStatus;

        /**
         * 系统标识，0 本地进单 1 路由平台进单-重型
         */
        private String systemType = "0";
        /**
         * 路由订单编号
         */
        private String routerOrderId;

        /**
         * 签署方式
         */
        private String authorizeWay;

        /**
         * 最终提交时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date finalBeginTime;

        /**
         * 是否人证后置
         */
        private String retentionOfwitnesses;
        /**
         * 是否加急 0否1是
         */
        private String isUrgent;
        /**
         * 影像后置【0否1是】
         */
        private String postImage;
        /**
         * 影像后置失败已执行决策引擎【0否1是】
         */
        private String executionRisk;
        /**
         * 影像后置操作标记【1：修改基本信息0：补充资料】
         */
        private String editFlag;
        /**
         * 是否需要视频面审【0否，1是】
         */
        private Integer faceReview;
        /**
         * 客户手机号码
         */
        
        private String custPhone;

        /**
         * 是否先放后抵
         */
        
        private String IsMortgage;

        /**
         * 是否属于TOP20经销商
         */
        
        private String IsTopChannel;

        /**
         * 抵押要求，0-抵押，1-免抵
         */
        private String mortgageClaim;

        /**
         * 抵押要求，0-抵押，1-免抵
         */
        private String mortgageClaimName;
        /**
         * 所属资方
         * @see  BelongingCapitalEnum
         */
        private String belongingCapital;

        private String belongingCapitalName;
        /**
         * 资方订单状态
         * @see CapitalOrderStatusEnum
         */
        private String capitalOrderStatus;
        /**
         * 资方签约时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date capitalSignTime;
        /**
         * 资方签约链接
         */
        private String capitalSignUrl;
        /**
         * 签约链接失效时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date capitalSignUrlExpireTime;

        /**
         * 抵押要求，1-免抵，0-抵押
         */
        private String decisionMortgageFlag;

        /**
         * 资方银行订单号
         */
        private String capitalApplyId;

        /**
         * 资方拒绝原因
         */
        private String backReasonForCapital;
        /**
         * 决策引擎住房类资料减免标志 1 -是 0-否
         */
        private String housingReductionFlag;

        /**
         * 产品名称
         */
        
        private String carProductName;

        /**
         * 抵押要求-前端录入，0-抵押，1-免抵
         */
        private String decisionMortgageResult;

        /**
         * 是否变更抵押要求状态，0-否，为变更，1-是,变更
         */
        private String modifyDecisionMortgageStatus;


        /**
         * 租金贷产品是否回退
         */
        private String rentLoanBack;
    }
