package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import lombok.Data;

import java.util.List;

/**
 * 服务费计算规则分类
 */
@Data
@TableName(value = "write_off_rule", autoResultMap = true)
public class WriteOffRule extends BaseEntity<WriteOffRule> {

    /**
     * 规则分类
     */
    private String type;
    /**
     * 规则分类名称
     */
    private String typeName;
    /**
     * 所属资方(多选)
     *
     * @see BelongingCapitalEnum
     */
    private String belongingCapital;
    /**
     * 状态;0-未生效；1-生效
     */
    private String status;
    /**
     * 所属资方(前端展示)
     */
    @TableField(exist = false)
    private List<String> belongCapitalList;
    /**
     * 所属资方(前端展示)
     */
    @TableField(exist = false)
    private String belongCapitalName;
    /**
     * 1表示复制操作(前端使用)
     */
    @TableField(exist = false)
    private String copyFlag;
}
