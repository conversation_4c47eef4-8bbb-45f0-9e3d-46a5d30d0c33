package com.ruicar.afs.cloud.afscase.report.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.itextpdf.text.pdf.PdfReader;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.report.entry.CreditInquiryRecords;
import com.ruicar.afs.cloud.afscase.report.service.CreditInquiryRecordsService;
import com.ruicar.afs.cloud.afscase.report.service.ZxReportService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CasePreApplyCustInfoDto;
import com.ruicar.afs.cloud.common.scan.util.GeneratePdfFileUtils;
import com.ruicar.afs.cloud.filecenter.FileCenterHelper;
import com.ruicar.afs.cloud.filecenter.FileType;
import com.ruicar.afs.cloud.image.config.FileProperties;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.zhengxin.condition.ZxReportCondition;
import com.ruicar.afs.cloud.zhengxin.entity.ComReportFile;
import com.ruicar.afs.cloud.zhengxin.service.ComReportFileService;
import com.ruicar.afs.cloud.zhengxin.service.ZxThirdService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class ZxReportServiceImpl implements ZxReportService {

    private static class ImageType {
        private static final String DEFAULT_NAME = "image";
        private static final String PNG = "png";
        private static final String BASE64_PREFIX = "data:image/png;base64,";
    }

    private final CaseBaseInfoService caseBaseInfoService;
    private final ZxThirdService zxThirdService;
    private final FileProperties fileProperties;
    private final GeneratePdfFileUtils generatePdfFileUtils;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final CaseCustInfoService caseCustInfoService;
    private final CreditInquiryRecordsService creditInquiryRecordsService;
    private final CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private final ApplyConfig applyConfig;
    private ComReportFileService comReportFileService;

    public String queryLesseeReport(ZxReportCondition zxReportCondition){
        //发送查询申请
        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda().eq(CaseBaseInfo::getApplyNo, zxReportCondition.getApplyNo()));
        ZxReportCondition zxReportCondition1 = new ZxReportCondition();
        zxReportCondition1.setApplyNo(zxReportCondition.getApplyNo());
        zxReportCondition1.setCertName(caseBaseInfo.getCustNameRepeat());
        zxReportCondition1.setCertNo(caseBaseInfo.getCertNoRepeat());
        //获取承租人身份证正反面
        zxReportCondition1.setCertFile(getLesseeCert(zxReportCondition.getApplyNo(),"lessee"));
        //获取承租人授权书
        zxReportCondition1.setAuthoFile(getLesseeAutho(zxReportCondition.getApplyNo(),"lessee"));
        zxReportCondition1.setType("1");
        //发送查询申请
        String queryId = zxThirdService.queryPersonReport(zxReportCondition1);
        if (StrUtil.isNotEmpty(queryId)){
            //获取征信报告保存到临时目录
            String filePath = zxThirdService.getPersonReport(queryId,zxReportCondition.getApplyNo(),"lessee",zxReportCondition1);
            String username = SecurityUtils.getUsername();//获取当前用户登录名
            String newFilePath = fileProperties.getTempDir() + zxReportCondition.getApplyNo() + username + "水印.pdf";
            try {
                //添加水印
                PdfReader reader = new PdfReader(filePath);
                ByteArrayOutputStream byteArrayOutputStream = generatePdfFileUtils.setWaterMarkFont(reader,username);
                FileOutputStream fileOutputStream = new FileOutputStream(newFilePath);
                byteArrayOutputStream.writeTo(fileOutputStream);
                fileOutputStream.flush();
                fileOutputStream.close();
                File file = new File(filePath);
                file.delete();
                return newFilePath;
            }catch (Exception e){
                log.error("下载{}承租人征信报告失败:{}",zxReportCondition.getApplyNo(),e);
            }
        }else {
            log.error("编号{}承租人发送征信查询申请失败!",zxReportCondition.getApplyNo());
        }
        return null;
    }

    public String queryGuarantorReport(CaseCustInfo custInfo){
        ZxReportCondition zxReportCondition = new ZxReportCondition();
        zxReportCondition.setApplyNo(custInfo.getApplyNo());
        zxReportCondition.setCertName(custInfo.getCustName());
        zxReportCondition.setCertNo(custInfo.getCertNo());
        //获取保证人身份证正反面
        zxReportCondition.setCertFile(getLesseeCert(custInfo.getApplyNo(),"guarantor"));
        //获取保证人授权书
        zxReportCondition.setAuthoFile(getLesseeAutho(custInfo.getApplyNo(),"guarantor"));
        String queryId = zxThirdService.queryPersonReport(zxReportCondition);
        ThreadUtil.sleep(500);
        //获取征信报告添加水印并保存到临时目录
        String filePath = zxThirdService.getPersonReport(queryId,zxReportCondition.getApplyNo(),"guarantor",zxReportCondition);
        String username = SecurityUtils.getUsername();//获取当前用户登录名
        String newFilePath = fileProperties.getTempDir() + zxReportCondition.getApplyNo() + username + "水印.pdf";
        try {
            PdfReader reader = new PdfReader(filePath);
            ByteArrayOutputStream byteArrayOutputStream = generatePdfFileUtils.setWaterMarkFont(reader,username);
            FileOutputStream fileOutputStream = new FileOutputStream(newFilePath);
            byteArrayOutputStream.writeTo(fileOutputStream);
            fileOutputStream.flush();
            fileOutputStream.close();
            File file = new File(filePath);
            file.delete();
        }catch (Exception e){
            log.error("下载{}担保人征信报告失败:{}",custInfo.getApplyNo(),e);
        }
        return newFilePath;
    }

    public String downReport(ComReportFile comReportFile){
        String username = SecurityUtils.getUsername();//获取当前用户登录名
        log.info("当前用户登录名：{}",username);
        String newFilePath = fileProperties.getTempDir() + comReportFile.getBusiNo() + username + "水印.pdf";
        byte[] pwd = null;
        if (StrUtil.isNotEmpty(comReportFile.getThumbnailId())){
            pwd = Base64.getDecoder().decode(comReportFile.getThumbnailId().getBytes());
        }
        try {
            //获取pdf字节流
            if (ObjectUtil.isNotEmpty(comReportFile.getFileId())){
                byte[] bytes = FileCenterHelper.downLoadFile(comReportFile.getFileId(), FileType.ORIGINAL);
                PdfReader reader = new PdfReader(bytes,pwd);
                ByteArrayOutputStream byteArrayOutputStream = generatePdfFileUtils.setWaterMarkFont(reader,username);
                FileOutputStream fileOutputStream = new FileOutputStream(newFilePath);
                byteArrayOutputStream.writeTo(fileOutputStream);
                fileOutputStream.flush();
                fileOutputStream.close();
            }else {
                String key = zxThirdService.getPersonReportById(comReportFile);
                if (StrUtil.isNotEmpty(key)){
                    ComReportFile reportFile = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                            .eq(ComReportFile::getBusiNo,comReportFile.getBusiNo())
                            .eq(ComReportFile::getAttachmentCode,StrUtil.equals(comReportFile.getAttachmentCode(),AttachmentUniqueCodeEnum.LESSEE_REPORT_FILE.getCode()) ? AttachmentUniqueCodeEnum.LESSEE_REPORT_FILE.getCode() : AttachmentUniqueCodeEnum.GUARANTOR_REPORT_FILE.getCode())
                            .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
                    byte[] bytes = FileCenterHelper.downLoadFile(key, FileType.ORIGINAL);
                    PdfReader reader = new PdfReader(bytes,Base64.getDecoder().decode(reportFile.getThumbnailId()));
                    ByteArrayOutputStream byteArrayOutputStream = generatePdfFileUtils.setWaterMarkFont(reader,username);
                    FileOutputStream fileOutputStream = new FileOutputStream(newFilePath);
                    byteArrayOutputStream.writeTo(fileOutputStream);
                    fileOutputStream.flush();
                    fileOutputStream.close();
                }
            }

        }catch (Exception e){
            log.error("下载征信报告失败:{}",e);
            return null;
        }
        return newFilePath;
    }

    /**
     * 下载报告并添加水印 - 本地 无前置机调接口
     * @param comReportFile 报告信息
     * @return 添加水印后的报告路径
     */
    @Override
    public String downReportLocal(ComReportFile comReportFile){
        String username = SecurityUtils.getUsername();//获取当前用户登录名
        log.info("当前用户登录名：{}",username);
        String newFilePath = fileProperties.getTempDir() + comReportFile.getBusiNo() + username + "水印.pdf";
        try {
            //获取pdf字节流
            if (ObjectUtil.isNotNull(comReportFile.getFileId())){
                byte[] bytes = FileCenterHelper.downLoadFile(comReportFile.getFileId(), FileType.ORIGINAL);
                PdfReader reader = new PdfReader(bytes);
                ByteArrayOutputStream byteArrayOutputStream = generatePdfFileUtils.setWaterMarkFont(reader,username);
                FileOutputStream fileOutputStream = new FileOutputStream(newFilePath);
                byteArrayOutputStream.writeTo(fileOutputStream);
                fileOutputStream.flush();
                fileOutputStream.close();
            }
        }catch (Exception e){
            log.error("下载征信报告失败:{}",e);
            return null;
        }
        return newFilePath;
    }

    public String getLesseeCert(String applyNo,String type){
        try {
            List<ComAttachmentManagement> list = new ArrayList<>();
            if ("lessee".equals(type)){
                list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                        .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_BACK.getCode()).or()
                        .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.MAIN_BORROWER_IDCARD_FRONT.getCode()));
            } else {
                list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                        .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode()).or()
                        .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode()));
            }
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(list.get(0).getId()))
                    .eq(ComAttachmentFile::getBusiNo,applyNo));
            ComAttachmentFile comAttachmentFile1 = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(list.get(1).getId()))
                    .eq(ComAttachmentFile::getBusiNo,applyNo));
            String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".jpg";
            String tempPath1 = fileProperties.getTempDir() + comAttachmentFile1.getFileId() + ".jpg";
            FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(),FileType.ORIGINAL,tempPath);
            FileCenterHelper.downLoadFile(comAttachmentFile1.getFileId(),FileType.ORIGINAL,tempPath1);
            File file = new File(tempPath);
            File file1 = new File(tempPath1);
            BufferedImage image = ImageIO.read(file);
            BufferedImage image1 = ImageIO.read(file1);

            int width = image.getWidth() + image1.getWidth();
            int height = Math.max(image.getHeight(),image1.getHeight());
            BufferedImage result = new BufferedImage(width,height,BufferedImage.TYPE_INT_RGB);

            result.createGraphics().drawImage(image,0,0,null);
            result.createGraphics().drawImage(image1,image.getWidth(),0,null);
            String filename = fileProperties.getTempDir() + System.currentTimeMillis() + ".jpg";
            ImageIO.write(result,"jpg",new File(filename));
            file.delete();
            file1.delete();
            return convertToBase64(filename);
        }catch (Exception e){
            log.error("获取身份证正反面失败:{}",e);
        }
            return null;
        }


    public String getLesseeAutho(String applyNo,String type){
        try {
            ComAttachmentManagement comAttachmentManagement = new ComAttachmentManagement();
            if ("lessee".equals(type)){
                comAttachmentManagement = comAttachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                        .eq(ComAttachmentManagement::getAttachmentName,"个人征信授权书(承租人)"));
            }else {
                comAttachmentManagement = comAttachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                        .eq(ComAttachmentManagement::getAttachmentName,"个人征信授权书(保证人)"));
            }
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(comAttachmentManagement.getId()))
                    .eq(ComAttachmentFile::getBusiNo,applyNo).last("limit 1"));
            String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".pdf";
            FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(),FileType.ORIGINAL,tempPath);
            return convertToBase64(tempPath);
        }catch (Exception e){
            log.error("获取征信授权书失败:{}",e);
        }
        return null;
    }

    public boolean addRecord(ZxReportCondition zxReportCondition,ComReportFile comReportFile){
        AfsUser afsUser = SecurityUtils.getUser();
        List<String> list = SecurityUtils.getRoles();
        CreditInquiryRecords creditInquiryRecords = new CreditInquiryRecords();
        creditInquiryRecords.setApplyNo(zxReportCondition.getApplyNo());
        creditInquiryRecords.setUserName(afsUser.getUserRealName());
        creditInquiryRecords.setUserNo(afsUser.getUsername());
        log.info("获取用户权限{}",list);
        if (list.contains("AUDIT_REPORT")){
            creditInquiryRecords.setUserRole("初审");
        } else if (list.contains("AUDIT_SUPERVISOR_REPORT")) {
            creditInquiryRecords.setUserRole("审查");
        } else if (list.contains("APPROVER_REPORT")) {
            creditInquiryRecords.setUserRole("审批");
        } else if (list.contains("REPORT_ADMIN")) {
            creditInquiryRecords.setUserRole("业务管理员");
        }
        creditInquiryRecords.setQueryDate(DateUtil.now().split(" ")[0]);
        creditInquiryRecords.setQueryTime(DateUtil.now().split(" ")[1]);
        if ("1".equals(zxReportCondition.getQueryType())){
            creditInquiryRecords.setType("我的代办任务");
        }else if ("2".equals(zxReportCondition.getQueryType())){
            creditInquiryRecords.setType("信审综合查询");
        }
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo,zxReportCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        creditInquiryRecords.setCustName(custInfo.getCustName());
        creditInquiryRecords.setCustNo(custInfo.getCertNo());
        creditInquiryRecords.setPhone(custInfo.getTelPhone());
        creditInquiryRecords.setReportTime(comReportFile.getUploadTime());
        return creditInquiryRecordsService.save(creditInquiryRecords);
    }

    /**
     * 添加征信报告查询记录(从进件端获取承租人信息)
     *
     * @param zxReportCondition
     * @param comReportFile
     * @return
     */
    @Override
    public boolean addRecordByApplyInfo(ZxReportCondition zxReportCondition, ComReportFile comReportFile) {
        AfsUser afsUser = SecurityUtils.getUser();
        List<String> list = SecurityUtils.getRoles();
        CreditInquiryRecords creditInquiryRecords = new CreditInquiryRecords();
        creditInquiryRecords.setApplyNo(zxReportCondition.getApplyNo());
        creditInquiryRecords.setUserName(afsUser.getUserRealName());
        creditInquiryRecords.setUserNo(afsUser.getUsername());
        log.info("获取用户权限{}",list);
        if (list.contains("AUDIT_REPORT")){
            creditInquiryRecords.setUserRole("初审");
        } else if (list.contains("AUDIT_SUPERVISOR_REPORT")) {
            creditInquiryRecords.setUserRole("审查");
        } else if (list.contains("APPROVER_REPORT")) {
            creditInquiryRecords.setUserRole("审批");
        } else if (list.contains("REPORT_ADMIN")) {
            creditInquiryRecords.setUserRole("业务管理员");
        }
        creditInquiryRecords.setQueryDate(DateUtil.now().split(" ")[0]);
        creditInquiryRecords.setQueryTime(DateUtil.now().split(" ")[1]);
        if ("1".equals(zxReportCondition.getQueryType())){
            creditInquiryRecords.setType("我的代办任务");
        }else if ("2".equals(zxReportCondition.getQueryType())){
            creditInquiryRecords.setType("信审综合查询");
        }
        CaseCustInfo custInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo,zxReportCondition.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
        if (ObjectUtil.isNull(custInfo)){
            IResponse<CasePreApplyCustInfoDto> iResponse = caseUseApplyServiceFeign.getApproveInfoByPreId(zxReportCondition.getApplyNo(), getApplyHeader());
            if (CommonConstants.SUCCESS.equals(iResponse.getCode())) {
                CasePreApplyCustInfoDto casePreApplyCustInfoDto = iResponse.getData();
                if (ObjectUtil.isNotNull(casePreApplyCustInfoDto)) {
                    custInfo = new CaseCustInfo();
                    custInfo.setCustName(casePreApplyCustInfoDto.getCustName());
                    custInfo.setCertNo(casePreApplyCustInfoDto.getCardNo());
                    custInfo.setTelPhone(casePreApplyCustInfoDto.getPhoneno());
                }
            }
        }
        creditInquiryRecords.setCustName(custInfo.getCustName());
        creditInquiryRecords.setCustNo(custInfo.getCertNo());
        creditInquiryRecords.setPhone(custInfo.getTelPhone());
        creditInquiryRecords.setReportTime(comReportFile.getUploadTime());
        return creditInquiryRecordsService.save(creditInquiryRecords);
    }

    public String convertToBase64(String filePath) throws IOException{
        File file = new File(filePath);
        FileInputStream fis = new FileInputStream(file);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = new byte[1024];
        int byteRead;
        while ((byteRead = fis.read(bytes)) != -1){
            byteArrayOutputStream.write(bytes,0,byteRead);
        }
        byte[] imageBytes = byteArrayOutputStream.toByteArray();
        file.delete();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    /**
     * 获取 header
     * @return header
     */
    private Map<String, String> getApplyHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }

}
