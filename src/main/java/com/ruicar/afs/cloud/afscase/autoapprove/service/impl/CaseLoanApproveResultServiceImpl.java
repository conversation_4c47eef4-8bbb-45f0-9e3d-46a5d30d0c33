package com.ruicar.afs.cloud.afscase.autoapprove.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.autoapprove.entity.CaseLoanApproveResult;
import com.ruicar.afs.cloud.afscase.autoapprove.mapper.CaseLoanApproveResultMapper;
import com.ruicar.afs.cloud.afscase.autoapprove.service.CaseLoanApproveResultService;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class CaseLoanApproveResultServiceImpl extends ServiceImpl<CaseLoanApproveResultMapper, CaseLoanApproveResult> implements CaseLoanApproveResultService {


    /**
     * 通过申请编号查询放款自动审核结果
     * @param applyNo 申请编号
     * @return 返回的结果
     */
    @Override
    public CaseLoanApproveResult getResultByApplyNo(String applyNo) {

        return this.getOne(Wrappers.<CaseLoanApproveResult>lambdaQuery().select(CaseLoanApproveResult::getApproveResult).eq(CaseLoanApproveResult::getApplyNo, applyNo));

    }

    /**
     * 通过申请编号删除放款自动审核结果
     * @param applyNo 申请编号
     * @return 返回的结果
     */
    @Override
    public boolean removeResultByApplyNo(String applyNo){

        return this.remove(Wrappers.<CaseLoanApproveResult>lambdaQuery().eq(CaseLoanApproveResult::getApplyNo, applyNo).eq(CaseLoanApproveResult::getResultVerifyFlag,"0"));
    }

    /**
     * 通过申请编号查询是否存在放款自动审核结果
     * @param applyNo 申请编号
     * @return 返回的结果 true - 表示，不存在，false - 表示已经存在
     */
    public boolean queryResultIsExist(String applyNo){

        long count = this.count(Wrappers.<CaseLoanApproveResult>lambdaQuery().eq(CaseLoanApproveResult::getApplyNo, applyNo).eq(CaseLoanApproveResult::getResultVerifyFlag, WhetherEnum.YES.getCode()));

        if(count > 0){ return false; }

        return true;
    }

    /**
     * 保存放款自动审核结果
     * @param applyNo 申请编码
     * @param approveStatus 审核状态
     * @param approveResult 审核结果
     * @return
     */
    @Override
    public boolean saveLoanAutoResult(String applyNo,String approveStatus,String approveResult){

        // 保存之前先判断是否存在
        long count = this.count(Wrappers.<CaseLoanApproveResult>lambdaQuery().eq(CaseLoanApproveResult::getApplyNo, applyNo));

        if(count == 0){
            CaseLoanApproveResult result = new CaseLoanApproveResult();
            result.setApplyNo(applyNo);
            result.setApproveStatus(approveStatus);
            result.setApproveResult(approveResult);
            result.setResultVerifyFlag(WhetherEnum.NO.getKey());
            log.info("放款自动审核结果保存完成！");
            return this.save(result);
        }

        log.info("放款自动审核结果保存失败！");
        return true;

    }

    /**
     * 通过申请编号查询放款自动审核结果
     * @param applyNo 申请编号
     * @return 返回的结果
     */
    @Override
    public List<CaseLoanApproveResult> getgetLoanResult(String applyNo,String approveStatus,String approveResult) {

        LambdaQueryWrapper<CaseLoanApproveResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseLoanApproveResult::getApplyNo,applyNo)
                .eq(CaseLoanApproveResult::getApproveStatus,approveStatus)
                .eq(CaseLoanApproveResult::getApproveResult,approveResult);

        return this.list(queryWrapper);

    }

    /**
     * 通过申请编号修改放款自动审核结果中的确认标识
     * @param applyNo 申请编号
     * @param flag 修改后的标识符
     */
    public void updateResultVerifyFlagByApplyNo(String applyNo,String flag){

        LambdaQueryWrapper<CaseLoanApproveResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseLoanApproveResult::getApplyNo,applyNo);
        List<CaseLoanApproveResult> tCaseLoanApproveResultList = this.list(queryWrapper);
        for (CaseLoanApproveResult list : tCaseLoanApproveResultList){
            list.setResultVerifyFlag(flag);
        }
        this.updateBatchById(tCaseLoanApproveResultList);
    }
}
