package com.ruicar.afs.cloud.afscase.workflow.event;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.enums.RealTimeDataTypeEnum;
import com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.approvetask.entity.CaseApproveRecord;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseContractLockInfoService;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelRiskInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelRiskInfoService;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseChannelInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseChannelInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.loanapprove.condition.LoanApproveSubmitVO;
import com.ruicar.afs.cloud.afscase.loanapprove.service.LoanApproveService;
import com.ruicar.afs.cloud.afscase.message.entity.MessageTemplate;
import com.ruicar.afs.cloud.afscase.message.service.MessageTemplateService;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.PushDataForPos;
import com.ruicar.afs.cloud.afscase.risk.service.ThirdDataService;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApproveTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.YesOrNoEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanApproveInsuAndInvoiceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.SendToApplyContractInfoDTO;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.message.sendmessage.service.MessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@Slf4j
@Component
public class LoanAuditEventListener implements ApplicationListener<LoanAuditEvent> {

    private CaseApproveRecordService caseApproveRecordService;
    private LoanApproveService loanApproveService;
    private CaseChannelInfoService caseChannelInfoService;
    private ComAttachmentFileService comAttachmentFileService;
    private CaseContractInfoService caseContractInfoService;
    private final PushDataForPos infoSender;
    private final ApplyServiceFeign applyServiceFeign;
    private CaseBaseInfoService caseBaseInfoService;
    private final ChannelBaseInfoService channelBaseInfoService;
    private final ChannelRiskInfoService channelRiskInfoService;
    private MessageTemplateService messageTemplateService;
    private final MessageService messageService;
    private final CaseContractLockInfoService caseContractLockInfoService;
    private final ThirdDataService thirdDataService;

    /**
     * 推送流程进度到业务系统
     *
     * @param event
     */
    @Override
    public void onApplicationEvent(LoanAuditEvent event) {

        CaseApproveRecord record = new CaseApproveRecord();
        record.setUseScene(UseSceneEnum.APPROVE.getValue());
        record.setApproveType(ApproveTypeEnum.PROCESS.getValue());
        record.setApproveEndTime(new Date());
        record.setApproveStartTime(new Date());
        record.setDisposeStaff("系统");

        // 事件个性字段
        record.setApplyNo(event.getApplyNo());
        record.setStageId(event.getStageId());
        record.setApproveSuggest(event.getApproveSuggest());
        record.setApproveSuggestName(event.getApproveSuggestName());
        record.setApproveReason(event.getApproveReason());
        record.setApproveRemark(event.getApproveRemark());
        if (StringUtils.hasLength(event.getDisposeStaff())) {
            record.setDisposeStaff(event.getDisposeStaff());
        }

        if (event.getWithLog()) {
            caseApproveRecordService.save(record);
        }

        switch (event.getNormalSubmitType()){
            case LOAN_PRE_APPROVE:

                final LoanApproveSubmitVO submitVO = new LoanApproveSubmitVO();
                submitVO.setContractNo(event.getContractNo());

                final CaseApproveRecord approveRecord = new CaseApproveRecord();
                approveRecord.setApplyNo(event.getApplyNo());
                submitVO.setApproveRecord(approveRecord);

                // 保存TOP20经销商信息和抵押要求信息
                CaseContractInfo tCaseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                        .eq(CaseContractInfo::getContractNo,event.getContractNo()));

                if(tCaseContractInfo != null){
                    // TOP20经销商信息
                    boolean topChannelByApplyNo = thirdDataService.isTopChannelByApplyNo(event.getApplyNo());
                    if(topChannelByApplyNo){
                        tCaseContractInfo.setIsTopChannel(CaseConstants.YES);
                    }else{
                        tCaseContractInfo.setIsTopChannel(CaseConstants.NO);
                    }

                    // 抵押要求信息
                    tCaseContractInfo.setMortgageClaim(CaseConstants.NO);

                    String businessType = tCaseContractInfo.getBusinessType();
                    // 查询渠道编码
                    CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                            .eq(CaseChannelInfo::getApplyNo, event.getApplyNo()));
                    // 做非空校验
                    if(null != caseChannelInfo && null != caseChannelInfo.getDealerNo() && !"".equals(caseChannelInfo.getDealerNo())) {
                        // 查询渠道ID
                        ChannelBaseInfo channelBaseInfo =
                                channelBaseInfoService.getOne(Wrappers.<ChannelBaseInfo>query().lambda().eq(ChannelBaseInfo::getChannelCode, caseChannelInfo.getDealerNo()));
                        // 进行非空校验
                        if(null != channelBaseInfo && null != channelBaseInfo.getChannelId() && !"".equals(channelBaseInfo.getChannelId())) {
                            List<ChannelRiskInfo> tChannelRiskInfoList = channelRiskInfoService.list(Wrappers.<ChannelRiskInfo>query().lambda()
                                    .eq(ChannelRiskInfo::getChannelId, channelBaseInfo.getChannelId())
                                    .eq(ChannelRiskInfo::getBusinessType, businessType));

                            // 查询是否先放后抵
                            if(ObjectUtils.isNotEmpty(tChannelRiskInfoList) && tChannelRiskInfoList.size() > 0) {
                                // 如果满足条件，则保存免抵
                                if(CaseConstants.YES.equals(tCaseContractInfo.getIsTopChannel()) &&  tChannelRiskInfoList.get(0).getIsMortgage().equals(CaseConstants.YES)){
                                    tCaseContractInfo.setMortgageClaim(CaseConstants.YES);
                                }
                            }
                        }
                    }

                    caseContractInfoService.updateById(tCaseContractInfo);

                    submitVO.setMortgageClaim(tCaseContractInfo.getMortgageClaim());
                }

                // 更新状态 & 通知进件
                loanApproveService.reviewSubmit(submitVO);
                // 草稿状态改为合格状态
                comAttachmentFileService.updateFileStatusByBusiNo(event.getContractNo());
                // modify by likang 插入挂靠实时数据
                caseChannelInfoService.insertAffiliatedRealtimeData(event.getApplyNo(),RealTimeDataTypeEnum.CONTRACT.getIndex());

                // 锁定合同目录
                try{
                    applyServiceFeign.saveLockInfoByApplyNo(event.getApplyNo(),CaseConstants.MESSAGE_TYPE_TWO);
                } catch (Exception e){
                    log.error("保存锁定合同目录信息失败，原因1={}"+e.getMessage());
                    log.error("保存锁定合同目录信息失败，原因2={}"+e);
                }

                //放款审核通过发送短信
                sendMessage(event.getApplyNo());

                break;
            case LOAN_REFUSE:
                CaseContractInfo caseContractInfo = caseContractInfoService.getOne(Wrappers.<CaseContractInfo>query().lambda()
                        .eq(CaseContractInfo::getContractNo,event.getContractNo()));

                //修改放款审核状态-审核拒绝
                caseContractInfo.setBusinessStateIn(ApplyStatusEnum.LOAN_REFUSE.getState());
                caseContractInfo.setApplyStatus(ApplyStatusEnum.LOAN_REFUSE.getState());
                caseContractInfo.setApproveCompleteTime(new Date());
                caseContractInfoService.updateById(caseContractInfo);

                SendToApplyContractInfoDTO sendToApplyContractInfoDTO = SendToApplyContractInfoDTO.builder()
                        .contractNo(event.getContractNo())
                        .stageId(null != event.getStageId() ? event.getStageId() : "")
                        .approveType(ApplyStatusEnum.LOAN_REFUSE.getState())
                        .operateTime(new Date()).build();

                LoanApproveInsuAndInvoiceDTO loanApproveInsuAndInvoiceDTO = LoanApproveInsuAndInvoiceDTO.builder()
                        .carInvoiceInfos(new ArrayList<>())
                        .carInsuranceInfos(new ArrayList<>())
                        .build();

                log.info("放款拒绝单推送案件构建数据{}", JSON.toJSONString(sendToApplyContractInfoDTO));
                ApproveSubmitInfo approveSubmitInfo = ApproveSubmitInfo.builder()
                        .sendToApplyContractInfoDTO(sendToApplyContractInfoDTO)
                        .loanApproveInsuAndInvoiceDTO(loanApproveInsuAndInvoiceDTO)
                        .build();
                log.info("放款拒绝单推送案件数据{}", JSON.toJSONString(approveSubmitInfo));

                //通知进件系统
                infoSender.sendBackToPartnersNotic(AfsTransEntity.<ApproveSubmitInfo>builder()
                        .transCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_LOAN_NOTICE)
                        .data(approveSubmitInfo).build());

                break;
            default:
                break;
        }
    }

    private void sendMessage(String applyNo){
        try {
            CaseChannelInfo caseChannelInfo = caseChannelInfoService.getOne(Wrappers.<CaseChannelInfo>query().lambda()
                    .eq(CaseChannelInfo::getApplyNo, applyNo));
            CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                    .eq(CaseBaseInfo::getApplyNo,applyNo));
            MessageTemplate messageTemplate = messageTemplateService.getOne(Wrappers.<MessageTemplate>query().lambda()
                    .eq(MessageTemplate::getTemplateType,"14")
                    .eq(MessageTemplate::getTemplateId,"loan_submit_financier")
                    .eq(MessageTemplate::getStatus, AfsEnumUtil.key(YesOrNoEnum.yes)));
            Assert.isTrue(messageTemplate!=null,"放款审核通过通知-金融专员短信模板不存在！");
            String msg = messageTemplate.getTemplateContent().replace("dealerName",caseBaseInfo.getDealerName())
                    .replace("custNameRepeat",caseBaseInfo.getCustNameRepeat())
                    .replace("applyNo",applyNo);
            log.info("合同号{}，放款审核通过通知-金融专员发送短信{}，手机号码为{}",applyNo, msg, caseChannelInfo.getSalePhone());
            boolean b = messageService.sendSms(caseChannelInfo.getSalePhone(), msg);
            Assert.isTrue(b,"放款审核通过通知-金融专员发送短信失败");
        }catch (Exception e){
            log.error(e.getMessage());
        }
    }

}


