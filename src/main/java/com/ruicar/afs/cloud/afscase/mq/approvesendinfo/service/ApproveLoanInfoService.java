package com.ruicar.afs.cloud.afscase.mq.approvesendinfo.service;

import com.ruicar.afs.cloud.afscase.backtopartnersinfo.entity.CaseBackToPartnersInfo;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanApproveInsuAndInvoiceDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.LoanResultInfoDto;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * The interface Approve loan info service.
 *
 * <AUTHOR>
 * @Date 2020 /7/4
 * @description 放款审核MQ -Service
 */
public interface ApproveLoanInfoService {
    /**
     * Back to partners notic boolean.
     *
     * @param contractNo the contract no
     * @param stageId    the stage id
     * @param taskId     the task id
     * @return the boolean
     * @description 退回合作商信息通知进件系统
     * <AUTHOR>
     * @date 2020 /6/30
     */
    Boolean backToPartnersNotic(String contractNo, String stageId,String taskId);

    /**
     * 放款自动审核退回经销商
     * @param infoList 参数
     * @return 返回的结果
     */
    Boolean loanAutoBackToPartnersNotic(List<CaseBackToPartnersInfo> infoList);

    /**
     * 放款自动审核自动退回经销商
     * @param infoList 参数
     * @return 返回结果
     */
    Boolean autoBackToApplyNotic(List<CaseBackToPartnersInfo> infoList);

    /**
     * 通知进件GPS状态
     *
     * @param contractNo the contract no
     * @param status     the status
     */
    void sendToApplyNotic(String contractNo, String status);

    /**
     * 放款审核保险、发票数据同步接口
     *
     * @param loanApproveInsuAndInvoiceDTO the loan approve insu and invoice dto
     */
    void sendLoanApproveInsuAndInvoice(LoanApproveInsuAndInvoiceDTO loanApproveInsuAndInvoiceDTO);

    /**
     * Send loan msg to contract.
     *
     * @param contractNo the contract no
     * @param applyNo    the apply no
     * @return
     * @description 推送接口信息到合同系统
     * <AUTHOR>
     * @date 2020 /7/8
     */
    void sendLoanMsgToContract(String contractNo,String applyNo);

    /**
     * 通知进件合同状态
     *
     * @param contractNo the contract no
     * @param statusEnum the status enum
     */
    void sendToApplyNotic(String contractNo, ApplyStatusEnum statusEnum);

    /**
     * 放款审核通过通知进件合同状态
     * @param contractNo 入参1
     * @param statusEnum 入参2
     * @param mortgageClaim 入参3
     */
    void sendLoanToApplyNotic(String contractNo, ApplyStatusEnum statusEnum,String mortgageClaim);

    /**
     * 批量通知进件合同状态
     *
     * @param applyNoList the apply no list
     */
    void sendListToApplyNotic(String applyNoList);

    /**
     * Loan apply result.
     *
     * @param resultInfoDto the result info dto
     * @Description:放款申请落库通知
     * @Author: fangchenliang
     * @Date: 2020 /7/14 11:30
     * @Param: [logInfo]
     * @Return: void
     */
    void loanApplyResult(LoanResultInfoDto resultInfoDto);

    /**
     * Send gps msg to apply.
     *
     * @param id the id
     * @return
     * @description 推送GPS安装信息到进件系统
     * <AUTHOR>
     * @date 2020 /7/8
     */
    void sendGpsMsgToApply(Long id);

    /**
     * Send loan model to apply.
     *
     * @param id the id
     * @return
     * @description 推送放款模式信息到进件系统
     * <AUTHOR>
     * @date 2020 /8/29
     */
    void sendLoanModelToApply(Long id);

    /**
     * 同步激活信息给进件系统
     *
     * @param contractNo the contract no
     * @param steps      the steps
     */
    void sendActiveStatusToWow(String contractNo, int steps);

    /**
     * Send gps status to contract.
     *
     * @param contractNo the contract no
     * @param status     the status
     */
    @ApiOperation("通知合同GPS状态")
    void sendGpsStatusToContract(String contractNo, String status);
}
