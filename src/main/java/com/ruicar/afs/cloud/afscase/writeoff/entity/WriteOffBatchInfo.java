package com.ruicar.afs.cloud.afscase.writeoff.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: zhangjin
 * @description 服务费批次信息
 */
@Data
@TableName(value = "write_off_batch_info", autoResultMap = true)
public class WriteOffBatchInfo extends BaseEntity<WriteOffBatchInfo> {
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 核销项编号
     */
    private String applyNos;
    /**
     * 核销期数
     */
    private String writeOffMonth;
    /**
     * 经销商code
     */
    private String channelCode;
    /**
     * 经销商name
     */
    private String channelFullName;
    /**
     * 案件编号
     */
    private String caseNo;
    /**
     * 批次总金额
     */
    private BigDecimal batchAmount;
    /**
     * 已提取金额
     */
    private BigDecimal paidAmount;
    /**
     * 剩余提取金额
     */
    private BigDecimal residueAmount;
    /**
     * 发起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

}
