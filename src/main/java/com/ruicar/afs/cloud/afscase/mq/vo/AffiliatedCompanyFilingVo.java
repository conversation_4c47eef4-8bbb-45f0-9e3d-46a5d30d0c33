package com.ruicar.afs.cloud.afscase.mq.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "备案待办任务信息")
public class AffiliatedCompanyFilingVo {
    /**
     * 任务备忘录
     */
    private String taskMemo;
    /**
     * 流程id
     */
    private String flowId;
    /**
     * 任务id
     */
    private String taskId;

    /**
     * 节点标识
     */
    private String targetWebKey;
    /**
     * 处理人
     */
    private String assign;
    /**
     * 当前节点
     */
    private String taskNodeName;
    private String channelName;
    private String affiliatedName;
    private String filingType;
    private String id;
    private Date createDate;
    private String processInstanceId;
    private String filingProductGroup;
}
