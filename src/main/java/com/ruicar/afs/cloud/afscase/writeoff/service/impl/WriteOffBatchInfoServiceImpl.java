package com.ruicar.afs.cloud.afscase.writeoff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.writeoff.dto.PermissionResultDto;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBatchInfo;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBatchInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBatchInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPermissionService;
import com.ruicar.afs.cloud.afscase.writeoff.vo.BatchInfoExcelVo;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Slf4j
@AllArgsConstructor
@Service
public class WriteOffBatchInfoServiceImpl extends ServiceImpl<WriteOffBatchInfoMapper, WriteOffBatchInfo> implements WriteOffBatchInfoService {

    private final WriteOffPermissionService writeOffPermissionService;

    /**
     * 条件查询
     * @param queryCondition
     * @return
     */
    @Override
    public IResponse queryByCondition(QueryCondition<WriteOffBatchInfo> queryCondition) {
        WriteOffBatchInfo condition = queryCondition.getCondition();
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        Page<WriteOffBatchInfo> page = this.page(new Page<>(queryCondition.getPageNumber(), queryCondition.getPageSize()), Wrappers.<WriteOffBatchInfo>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffBatchInfo::getChannelCode, resultDto.getHitCodeList())
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffBatchInfo::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getBatchNo()), WriteOffBatchInfo::getBatchNo, condition.getBatchNo())
                .like(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffBatchInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .like(StrUtil.isNotBlank(condition.getApplyNos()), WriteOffBatchInfo::getApplyNos, condition.getApplyNos())
                .orderByDesc(WriteOffBatchInfo::getCreateTime));
        return IResponse.success(page);
    }

    /**
     * 数据导出
     * @param condition
     * @param response
     */
    @Override
    public void exportData(WriteOffBatchInfo condition, HttpServletResponse response) {
        //获取查询权限
        PermissionResultDto resultDto = writeOffPermissionService.getAllowedChannel(SecurityUtils.getUser().getUsername());
        List<WriteOffBatchInfo> list = this.list(Wrappers.<WriteOffBatchInfo>lambdaQuery()
                .in(!resultDto.getIsAll(), WriteOffBatchInfo::getChannelCode, resultDto.getHitCodeList())
                .like(StrUtil.isNotBlank(condition.getChannelFullName()), WriteOffBatchInfo::getChannelFullName, condition.getChannelFullName())
                .eq(StrUtil.isNotBlank(condition.getBatchNo()), WriteOffBatchInfo::getBatchNo, condition.getBatchNo())
                .like(StrUtil.isNotBlank(condition.getWriteOffMonth()), WriteOffBatchInfo::getWriteOffMonth, condition.getWriteOffMonth())
                .like(StrUtil.isNotBlank(condition.getApplyNos()), WriteOffBatchInfo::getApplyNos, condition.getApplyNos())
                .orderByDesc(WriteOffBatchInfo::getCreateTime));
        List<BatchInfoExcelVo> exportVoList = list.stream().map(record -> {
            BatchInfoExcelVo exportVo = new BatchInfoExcelVo();
            BeanUtil.copyProperties(record, exportVo);
            return exportVo;
        }).toList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        ExcelWriter excelWriterBuilder = null;
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncodeUtil.encode("服务费批次详情导出") + ".xlsx");
            excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream(), BatchInfoExcelVo.class).build();
            WriteSheet htSheetWrite = EasyExcelFactory.writerSheet(0, "服务费批次详情").build();
            excelWriterBuilder.write(exportVoList, htSheetWrite);
        } catch (Exception e) {
            throw new AfsBaseException("下载失败");
        } finally {
            if (excelWriterBuilder != null) {
                excelWriterBuilder.finish();
            }
        }
    }
}
