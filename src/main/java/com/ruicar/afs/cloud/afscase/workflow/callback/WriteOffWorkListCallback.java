package com.ruicar.afs.cloud.afscase.workflow.callback;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.writeoff.dto.SubmitOrBackResultDTO;
import com.ruicar.afs.cloud.afscase.writeoff.entity.ChannelServiceFee;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInfo;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffBaseInvoiceRel;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffCapitalFee;
import com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffInvoiceInfo;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffBaseInvoiceRelMapper;
import com.ruicar.afs.cloud.afscase.writeoff.mapper.WriteOffInvoiceInfoMapper;
import com.ruicar.afs.cloud.afscase.writeoff.service.ChannelServiceFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffBaseInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffCapitalFeeService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffInvoiceInfoService;
import com.ruicar.afs.cloud.afscase.writeoff.service.WriteOffPayRuleService;
import com.ruicar.afs.cloud.afscase.writeoff.service.impl.WriteOffApprovalInfoServiceImpl;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.dto.mq.enums.FlowTaskOperationEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffExtractEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffStatusEnum;
import com.ruicar.afs.cloud.enums.common.WriteOffTypeEnum;
import com.ruicar.afs.cloud.workflow.sdk.api.adapter.CommonAdapter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 核销回调
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Component
public class WriteOffWorkListCallback implements CommonAdapter {
    private WriteOffInvoiceInfoMapper writeOffInvoiceInfoMapper;
    private WriteOffInvoiceInfoService writeOffInvoiceInfoService;
    private WriteOffBaseInfoMapper writeOffBaseInfoMapper;
    private WriteOffBaseInfoService writeOffBaseInfoService;
    private WriteOffApprovalInfoServiceImpl writeOffApprovalInfoCondition;
    private final CaseUseApplyServiceFeign caseUseApplyServiceFeign;
    private final StringRedisTemplate redisTemplate;
    private ChannelServiceFeeService channelServiceFeeService;
    private WriteOffBaseInvoiceRelMapper writeOffBaseInvoiceRelMapper;
    private final WriteOffCapitalFeeService writeOffCapitalFeeService;
    private final WriteOffPayRuleService writeOffPayRuleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> execute(String flowPackageId, String flowTemplateId, String flowInstanceId,
                                       String extParam, Map<String, String> flowVariables) {
        //拿申请编号
        String applyNo = flowVariables.get("businessNo");
        log.info("拿到服务费发票流程业务编号：{}",applyNo);
        String channelCode = flowVariables.get("channelCode");
        log.info("核销项所属经销商编号：{}",channelCode);
        String state = flowVariables.get(FlowConstant.BIZ_OPERATION_TYPE);
        String isAdvancePay = flowVariables.get(FlowConstant.IS_ADVANCE_PAY);
        String isPass = flowVariables.get(FlowConstant.IS_PASS);
        Assert.isTrue(StrUtil.isAllNotBlank(state, applyNo, channelCode), "流程参数异常");
        String prefix = "feefp" + channelCode;
        boolean lockFlag = true;
        if (!Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(prefix, "lock", 50, TimeUnit.SECONDS))) {
            log.warn("服务费发票流程结束,获取锁失败，睡眠9秒钟");
            lockFlag = false;
            ThreadUtil.sleep(9000);
        }
        //查询核销项信息
        WriteOffBaseInfo writeOffBaseInfo = writeOffBaseInfoMapper.selectOne(Wrappers.<WriteOffBaseInfo>lambdaQuery().eq(WriteOffBaseInfo::getApplyNo, applyNo));
        if(writeOffBaseInfo!=null) {
            WriteOffInvoiceInfo writeOffInvoiceInfo = new WriteOffInvoiceInfo();
            //查询中间表
            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelMapper.selectList(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery().eq(WriteOffBaseInvoiceRel::getCaseNo, writeOffBaseInfo.getCaseNo()));
            //当前申请批次号
            String currentBatchNo = writeOffBaseInvoiceRels.get(0).getCurrentBatchNo();
            switch (FlowTaskOperationEnum.valueOf(state.toUpperCase())) {
                case SUBMIT:
                    //通过,给提取传值
                    ChannelServiceFee channelServiceFee = new ChannelServiceFee();
                    channelServiceFee.setChannelCode(writeOffBaseInfo.getOrganId());
                    channelServiceFee.setChannelName(writeOffBaseInfo.getOrganName());
                    channelServiceFee.setAmount(writeOffBaseInfo.getAmountBeingWrittenOff());
                    channelServiceFeeService.addOrUpdateData(channelServiceFee);
                    //设置核销项状态
                    if (CaseConstants.YES.equals(isAdvancePay) && CaseConstants.YES.equals(isPass)) {
                        writeOffBaseInfo.setWriteOffStatus(WriteOffStatusEnum.ADVANCE_PAY);
                    }else {
                        writeOffBaseInfo.setWriteOffStatus(WriteOffStatusEnum.CLOSED);
                    }
                    //将正在核销金额设置待放款金额
                    writeOffBaseInfo.setAmountToBeReleased(writeOffBaseInfo.getAmountToBeReleased().add(writeOffBaseInfo.getAmountBeingWrittenOff()));
                    //正在核销金额置空
                    writeOffBaseInfo.setAmountBeingWrittenOff(BigDecimal.ZERO);

                    //更新发票状态
                    writeOffInvoiceInfo.setStatus(WriteOffStatusEnum.SUBMIT_FINANCE);
                    writeOffInvoiceInfoMapper.update(writeOffInvoiceInfo, Wrappers.<WriteOffInvoiceInfo>lambdaQuery().eq(WriteOffInvoiceInfo::getCurrentBatchNo, currentBatchNo));

                    if (WriteOffTypeEnum.ZJD.getCode().equals(writeOffBaseInfo.getWriteOffType())) {
                        WriteOffCapitalFee capitalFee = writeOffCapitalFeeService.getOne(Wrappers.<WriteOffCapitalFee>lambdaQuery()
                                .eq(WriteOffCapitalFee::getApplyNo, writeOffBaseInfo.getApplyNo()));
                        if (capitalFee == null) {
                            capitalFee = new WriteOffCapitalFee();
                        }
                        capitalFee.setApplyNo(writeOffBaseInfo.getApplyNo());
                        capitalFee.setWriteOffMonth(writeOffBaseInfo.getWriteOffMonth());
                        capitalFee.setWriteOffType(writeOffBaseInfo.getWriteOffType());
                        capitalFee.setChannelCode(writeOffBaseInfo.getOrganId());
                        capitalFee.setChannelFullName(writeOffBaseInfo.getOrganName());
                        capitalFee.setReceiveInvoiceAmt(writeOffBaseInfo.getInvoiceAmount());
                        capitalFee.setHisConfirmAmt(BigDecimal.ZERO);
                        capitalFee.setWaitConfirmAmt(capitalFee.getReceiveInvoiceAmt());
                        capitalFee.setThisConfirmAmt(BigDecimal.ZERO);
                        capitalFee.setTotalConfirmAmt(BigDecimal.ZERO);
                        writeOffCapitalFeeService.saveOrUpdate(capitalFee);
                    }else {
                        //更新中间表状态
                        WriteOffBaseInvoiceRel writeOffBaseInvoiceRel = new WriteOffBaseInvoiceRel();
                        writeOffBaseInvoiceRel.setStatus(AfsEnumUtil.key(WriteOffExtractEnum.NOTEXTRACTED));
                        writeOffBaseInvoiceRelMapper.update(writeOffBaseInvoiceRel,Wrappers.<WriteOffBaseInvoiceRel>lambdaUpdate()
                                .eq(WriteOffBaseInvoiceRel::getCurrentBatchNo,currentBatchNo));
                        //生成服务费支付账单
                        writeOffPayRuleService.createPayBill(currentBatchNo, List.of(writeOffBaseInfo));
                    }
                    break;
                default:
                    throw new RuntimeException("不支持的操作类型" + state);
            }
            //更新数据库核销项状态
            writeOffBaseInfoMapper.updateById(writeOffBaseInfo);
        }else{
            //批量
            //拿到中间表数据
            List<WriteOffBaseInvoiceRel> writeOffBaseInvoiceRels = writeOffBaseInvoiceRelMapper.selectList(Wrappers.<WriteOffBaseInvoiceRel>lambdaQuery().eq(WriteOffBaseInvoiceRel::getBusinessNo, applyNo));
            //拿到批次号
            String currentBatchNo = writeOffBaseInvoiceRels.get(0).getCurrentBatchNo();
            //拿到发票信息
            List<WriteOffInvoiceInfo> writeOffInvoiceInfos = writeOffInvoiceInfoMapper.selectList(Wrappers.<WriteOffInvoiceInfo>lambdaQuery().eq(WriteOffInvoiceInfo::getCurrentBatchNo, currentBatchNo));
            //拿到核销项信息
            List<String> applyNos = writeOffBaseInvoiceRels.stream().map(WriteOffBaseInvoiceRel::getApplyNo).collect(Collectors.toList());
            List<WriteOffBaseInfo> writeOffBaseInfos = writeOffBaseInfoMapper.selectList(Wrappers.<WriteOffBaseInfo>lambdaQuery().in(WriteOffBaseInfo::getApplyNo, applyNos));
            switch (FlowTaskOperationEnum.valueOf(state.toUpperCase())) {
                case SUBMIT:
                    //通过
                    writeOffInvoiceInfos.forEach(i->{
                        //更新发票状态
                        i.setStatus(WriteOffStatusEnum.SUBMIT_FINANCE);
                    });
                    //给提取推送数据
                    writeOffBaseInfos.forEach(i->{
                        ChannelServiceFee channelServiceFee = new ChannelServiceFee();
                        channelServiceFee.setChannelCode(i.getOrganId());
                        channelServiceFee.setChannelName(i.getOrganName());
                        channelServiceFee.setAmount(i.getAmountBeingWrittenOff());
                        channelServiceFeeService.addOrUpdateData(channelServiceFee);

                        //设置核销项状态
                        if (CaseConstants.YES.equals(isAdvancePay) && CaseConstants.YES.equals(isPass)) {
                            i.setWriteOffStatus(WriteOffStatusEnum.ADVANCE_PAY);
                        }else {
                            i.setWriteOffStatus(WriteOffStatusEnum.CLOSED);
                        }
                        //将正在核销金额设置待放款金额
                        i.setAmountToBeReleased(i.getAmountToBeReleased().add(i.getAmountBeingWrittenOff()));
                        //正在核销金额置空
                        i.setAmountBeingWrittenOff(BigDecimal.ZERO);
                        if (WriteOffTypeEnum.ZJD.getCode().equals(i.getWriteOffType())) {
                            WriteOffCapitalFee capitalFee = writeOffCapitalFeeService.getOne(Wrappers.<WriteOffCapitalFee>lambdaQuery()
                                    .eq(WriteOffCapitalFee::getApplyNo, i.getApplyNo()));
                            if (capitalFee == null) {
                                capitalFee = new WriteOffCapitalFee();
                            }
                            capitalFee.setApplyNo(i.getApplyNo());
                            capitalFee.setWriteOffMonth(i.getWriteOffMonth());
                            capitalFee.setWriteOffType(i.getWriteOffType());
                            capitalFee.setChannelCode(i.getOrganId());
                            capitalFee.setChannelFullName(i.getOrganName());
                            capitalFee.setReceiveInvoiceAmt(i.getInvoiceAmount());
                            capitalFee.setHisConfirmAmt(BigDecimal.ZERO);
                            capitalFee.setWaitConfirmAmt(capitalFee.getReceiveInvoiceAmt());
                            capitalFee.setThisConfirmAmt(BigDecimal.ZERO);
                            capitalFee.setTotalConfirmAmt(BigDecimal.ZERO);
                            writeOffCapitalFeeService.saveOrUpdate(capitalFee);
                        }
                    });
                    if (!WriteOffTypeEnum.ZJD.getCode().equals(writeOffBaseInfos.get(0).getWriteOffType())) {
                        //更新中间表状态
                        List<Long> relId = writeOffBaseInvoiceRels.stream().map(WriteOffBaseInvoiceRel::getId).collect(Collectors.toList());
                        WriteOffBaseInvoiceRel writeOffBaseInvoiceRel = new WriteOffBaseInvoiceRel();
                        writeOffBaseInvoiceRel.setStatus(AfsEnumUtil.key(WriteOffExtractEnum.NOTEXTRACTED));
                        writeOffBaseInvoiceRelMapper.update(writeOffBaseInvoiceRel,Wrappers.<WriteOffBaseInvoiceRel>lambdaUpdate()
                                .in(WriteOffBaseInvoiceRel::getId,relId));
                        //生成服务费支付账单
                        writeOffPayRuleService.createPayBill(currentBatchNo, writeOffBaseInfos);
                    }
                    break;
                default:
                    throw new RuntimeException("不支持的操作类型" + state);
            }
            //更新发票状态
            writeOffInvoiceInfoService.updateBatchById(writeOffInvoiceInfos);
            //更新数据库核销项状态
            writeOffBaseInfoService.updateBatchById(writeOffBaseInfos);
        }

        //数据同步 到进件
        if (CaseConstants.YES.equals(isAdvancePay) && CaseConstants.YES.equals(isPass)) {
            log.info("先付后票，暂不同步进件");
            return flowVariables;
        }
        SubmitOrBackResultDTO submitOrBackResultDTO = new SubmitOrBackResultDTO();
        submitOrBackResultDTO.setBusinessNo(applyNo);
        submitOrBackResultDTO.setType(state);
        IResponse iResponse = caseUseApplyServiceFeign.submitOrBackResult(submitOrBackResultDTO,writeOffApprovalInfoCondition.makeApplyHeader());
        if (!CaseConstants.CODE_SUCCESS.equals(iResponse.getCode())) {
            throw new AfsBaseException("发票审核结束节点同步进件失败");
        }
        log.info("核销结束回调:{}", flowVariables);
        if (lockFlag) {
            redisTemplate.delete(prefix);
        }
        return flowVariables;
    }

}
