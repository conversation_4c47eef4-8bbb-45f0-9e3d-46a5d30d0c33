package com.ruicar.afs.cloud.afscase.infomanagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.apply.config.ApplyConfig;
import com.ruicar.afs.cloud.afscase.approvetask.service.CaseApproveRecordService;
import com.ruicar.afs.cloud.afscase.cargpsmanage.entity.CarGpsApply;
import com.ruicar.afs.cloud.afscase.cargpsmanage.service.CarGpsApplyService;
import com.ruicar.afs.cloud.afscase.common.utils.Const;
import com.ruicar.afs.cloud.afscase.infomanagement.condition.CaseContractCondition;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.ApplyGuarantee;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseBaseInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseContractInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CasePolicyCheck;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CasePolicyCheckRecord;
import com.ruicar.afs.cloud.afscase.infomanagement.enums.InsuranceCheckResCodeEnum;
import com.ruicar.afs.cloud.afscase.infomanagement.enums.InsuranceOrganizationCodeEnum;
import com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseContractInfoMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseBaseInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseContractInfoService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CasePolicyCheckRecordService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CasePolicyCheckService;
import com.ruicar.afs.cloud.afscase.infomanagement.vo.EffectiveContractVO;
import com.ruicar.afs.cloud.afscase.loanapprove.task.condition.LoanApproveTaskCondition;
import com.ruicar.afs.cloud.afscase.mq.approvesendinfo.ArchieLoanMoneySender;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.entity.CaseConfParam;
import com.ruicar.afs.cloud.afscase.paramconfmanagement.service.CaseConfParamService;
import com.ruicar.afs.cloud.afscase.util.Md5AndSalt;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.ArchiveMQNodeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.GpsInstallTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.GpsOrderStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.GpsStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.archive.ApplyCreditPool;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.common.util.EmptyUtils;
import com.ruicar.afs.cloud.interfaces.checkinsurance.config.CheckInsuranceConfig;
import com.ruicar.afs.cloud.interfaces.checkinsurance.dto.CheckInsuranceQueryReq;
import com.ruicar.afs.cloud.interfaces.checkinsurance.dto.CheckInsuranceQueryResp;
import com.ruicar.afs.cloud.interfaces.checkinsurance.dto.Request;
import com.ruicar.afs.cloud.interfaces.checkinsurance.dto.Response;
import com.ruicar.afs.cloud.interfaces.checkinsurance.service.CheckInsuranceService;
import com.ruicar.afs.cloud.product.sdk.service.feign.ProductFeign;
import com.ruicar.afs.cloud.seats.entity.UserCollocation;
import com.ruicar.afs.cloud.seats.service.UserCollocationService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date create on 2020-05-13 15:20
 */
@Service
@AllArgsConstructor
@Data
@Slf4j
public class CaseContractInfoServiceImpl extends ServiceImpl<CaseContractInfoMapper, CaseContractInfo> implements CaseContractInfoService {

    private CaseContractInfoMapper caseContractInfoMapper;

    private CaseApproveRecordService caseApproveRecordService;

    private UserCollocationService userCollocationService;

    private CarGpsApplyService carGpsApplyService;

    private final CaseConfParamService caseConfParamService;

    private final ApplyContractFeign applyContractFeign;

    private ApplyConfig applyConfig;

    private CheckInsuranceService checkInsuranceService;

    private CheckInsuranceConfig checkInsuranceConfig;

    private static final String CACHE_KEY_PRE = "check-insurance-order:";

    private final StringRedisTemplate redisTemplate;

    private CaseBaseInfoService caseBaseInfoService;

    private CasePolicyCheckService casePolicyCheckService;

    private CasePolicyCheckRecordService casePolicyCheckRecordService;

    private final ProductFeign productFeign;

    private final ArchieLoanMoneySender archieLoanMoneySender;



    @Override
    public void updateApplyStatus(String contractNo, String applyStatus) {
        CaseContractInfo caseContractInfo = this.getContractByContractNo(contractNo);
        if (ObjectUtils.isNotEmpty(caseContractInfo)) {
            caseContractInfo.setApplyStatus(applyStatus);
            this.updateById(caseContractInfo);
        }
    }

    @Override
    public void updateBusinessStage(String contractNo, String businessStage) {
        CaseContractInfo caseContractInfo = this.getContractByContractNo(contractNo);
        if (ObjectUtils.isNotEmpty(caseContractInfo)) {
            caseContractInfo.setBusinessStage(businessStage);
            this.updateById(caseContractInfo);
        }
    }

    @Override
    public List<CaseContractInfo> getAllContractInfoByWorkflowStatus() {
        List<String> status = new ArrayList<>();
        status.add(AfsEnumUtil.key(GpsStatusEnum.PASS_AUDIT));
        status.add(AfsEnumUtil.key(GpsStatusEnum.UN_SEND_TASK));
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        Long time = calendar.getTimeInMillis();
        List<CaseConfParam> params = caseConfParamService.list(Wrappers.<CaseConfParam>query().lambda().eq(CaseConfParam::getParamType, Const.GPS_AUDIT_TIME.toString()));
        log.info("GPS配置条件" + params.size());
        Long before = null;
        if (params.size() > 0) {
            log.info("GPS配置条件有效期" + Long.valueOf(params.get(0).getParamValue()).longValue());
            before = time - (86400000 * (Long.valueOf(params.get(0).getParamValue()).longValue()));
        } else {
            before = time - (86400000 * 15);
        }
        String thisTime = this.getYearMonthDayHourMinuteSecond(time);
        String beForeTime = this.getYearMonthDayHourMinuteSecond(before);
        return this.list(Wrappers.<CaseContractInfo>lambdaQuery()
                .notIn(CaseContractInfo::getGpsWorkflowStatus, status).between(beForeTime != null && thisTime != null, CaseContractInfo::getCreateTime, beForeTime, thisTime));
    }

    @Override
    public List<CarGpsApply> getCarGpsApplyList() {
        List<String> status = new ArrayList<>();
        status.add(AfsEnumUtil.key(GpsStatusEnum.PASS_AUDIT));
        status.add(AfsEnumUtil.key(GpsStatusEnum.UN_SEND_TASK));
        status.add(AfsEnumUtil.key(GpsOrderStatusEnum.END_EXAMINE));
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        Long time = calendar.getTimeInMillis();
        // 获取15天前的毫秒数
        List<CaseConfParam> params = caseConfParamService.list(Wrappers.<CaseConfParam>query().lambda().eq(CaseConfParam::getParamType, Const.GPS_AUDIT_TIME.toString()));
        log.info("GPS配置条件" + params.size());
        Long before = null;
        if (params.size() > 0) {
            log.info("GPS配置条件有效期" + Long.valueOf(params.get(0).getParamValue()).longValue());
            before = time - (86400000 * (Long.valueOf(params.get(0).getParamValue()).longValue()));
        } else {
            before = time - (86400000 * 15);
        }
        String thisTime = this.getYearMonthDayHourMinuteSecond(time);
        String beForeTime = this.getYearMonthDayHourMinuteSecond(before);
        return carGpsApplyService.list(Wrappers.<CarGpsApply>lambdaQuery()
                .notIn(CarGpsApply::getApplyStatus, status).between(beForeTime != null && thisTime != null, CarGpsApply::getCreateTime, beForeTime, thisTime)
                .notIn(CarGpsApply::getGpsType, AfsEnumUtil.key(GpsInstallTypeEnum.noInstall)));
    }

    @Override
    public List<CarGpsApply> getCarGpsApplyExamineList() {
        List<String> status = new ArrayList<>();
        status.add(AfsEnumUtil.key(GpsStatusEnum.UN_PASS_AUDIT));
        status.add(AfsEnumUtil.key(GpsOrderStatusEnum.END_EXAMINE));
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        Long time = calendar.getTimeInMillis();
        // 获取15天前的毫秒数
        List<CaseConfParam> params = caseConfParamService.list(Wrappers.<CaseConfParam>query().lambda().eq(CaseConfParam::getParamType, Const.GPS_AUDIT_TIME.toString()));
        log.info("GPS配置条件" + params.size());
        Long before = null;
        if (params.size() > 0) {
            log.info("GPS配置条件有效期" + Long.valueOf(params.get(0).getParamValue()).longValue());
            before = time - (86400000 * (Long.valueOf(params.get(0).getParamValue()).longValue()));
        } else {
            before = time - (86400000 * 15);
        }
        String thisTime = this.getYearMonthDayHourMinuteSecond(time);
        String beForeTime = this.getYearMonthDayHourMinuteSecond(before);
        return carGpsApplyService.list(Wrappers.<CarGpsApply>lambdaQuery()
                .in(CarGpsApply::getApplyStatus, status).eq(CarGpsApply::getNeedVerify, CommonConstants.COMMON_YES.toString()).between(beForeTime != null && thisTime != null, CarGpsApply::getCreateTime, beForeTime, thisTime)
                .notIn(CarGpsApply::getGpsType, AfsEnumUtil.key(GpsInstallTypeEnum.noInstall)));
    }

    /**
     * 将毫秒转换为年月日时分秒
     *
     * <AUTHOR>
     */
    public String getYearMonthDayHourMinuteSecond(long timeMillis) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        calendar.setTimeInMillis(timeMillis);
        int year = calendar.get(Calendar.YEAR);

        int month = calendar.get(Calendar.MONTH) + 1;
        String mToMonth = null;
        if (String.valueOf(month).length() == 1) {
            mToMonth = "0" + month;
        } else {
            mToMonth = String.valueOf(month);
        }

        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String dToDay = null;
        if (String.valueOf(day).length() == 1) {
            dToDay = "0" + day;
        } else {
            dToDay = String.valueOf(day);
        }

        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        String hToHour = null;
        if (String.valueOf(hour).length() == 1) {
            hToHour = "0" + hour;
        } else {
            hToHour = String.valueOf(hour);
        }

        int minute = calendar.get(Calendar.MINUTE);
        String mToMinute = null;
        if (String.valueOf(minute).length() == 1) {
            mToMinute = "0" + minute;
        } else {
            mToMinute = String.valueOf(minute);
        }

        int second = calendar.get(Calendar.SECOND);
        String sToSecond = null;
        if (String.valueOf(second).length() == 1) {
            sToSecond = "0" + second;
        } else {
            sToSecond = String.valueOf(second);
        }
        return year + "-" + mToMonth + "-" + dToDay + " " + hToHour + ":" + mToMinute + ":" + sToSecond;
    }


    @Override
    public void updateWorkflowStatus(String contractNo, GpsStatusEnum statusEnum) {
        CaseContractInfo caseContractInfo = this.getContractByContractNo(contractNo);
        if (ObjectUtils.isNotEmpty(caseContractInfo)) {
            caseContractInfo.setGpsWorkflowStatus(AfsEnumUtil.key(statusEnum));
            this.updateById(caseContractInfo);
        }
    }

    @Override
    public void updateLoanAuditor(String contractNo, String userName) {
        CaseContractInfo caseContractInfo = this.getContractByContractNo(contractNo);
        if (ObjectUtils.isNotEmpty(caseContractInfo)) {
            caseContractInfo.setLoanAuditor(userName);
            this.updateById(caseContractInfo);
        }
    }

    @Override
    public List<CaseContractInfo> getOverdueContractByChannel(String dealerNo) {
        return this.caseContractInfoMapper.getOverdueContractByChannel(dealerNo);
    }

    @Override
    public List<CaseContractInfo> getOverdueContractByDirect(String carDealersId) {
        return this.caseContractInfoMapper.getOverdueContractByDirect(carDealersId);
    }

    @Override
    public void saveReviewSign(String contractNo, Boolean status) {
        String reviewSign = "";
        if (status) {
            reviewSign = "yes";
        } else {
            reviewSign = "no";
        }
        CaseContractInfo caseContractInfo = this.getContractByContractNo(contractNo);
        if (ObjectUtils.isNotEmpty(caseContractInfo)) {
            caseContractInfo.setReviewSign(reviewSign);
            this.updateById(caseContractInfo);
        }
    }

    @Override
    public void savePause(String contractNo, String dec) {
        CaseContractInfo caseContractInfo = this.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, contractNo));
        String isLock = caseContractInfo.getIsLock();
        if (!isLock.equals(WhetherEnum.YES.getCode())) {
            caseContractInfo.setRemarks(dec);
        }
        caseContractInfo.setApplyStatus(ApplyStatusEnum.SUSPEND_WAIT.getState());
        this.updateById(caseContractInfo);
    }

    /**
     * 判断是否有代理组长
     *
     * @return
     */
    @Override
    public Boolean getGroupLeader() {
        Boolean result = false;
        if (StringUtils.isNotBlank(SecurityUtils.getUsername())) {
            UserCollocation userCollocation = userCollocationService.getOne(Wrappers.<UserCollocation>query().lambda()
                    .eq(UserCollocation::getLoginName, SecurityUtils.getUsername()));
            if (ObjectUtils.isNotEmpty(userCollocation) && StringUtils.isNotBlank(userCollocation.getAgentGroupLeaderLoginName())) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 根据经销商获取获取名下合同号码
     */
    public List<String> getContractNoFromChannel(String channelNo) {
        return this.baseMapper.getContractNoFromChannel(channelNo);
    }

    /**
     * 通过申请编号获取合同信息
     * add by fangchenliang
     */
    @Override
    public CaseContractInfo getContractByContractNo(String contractNo) {
        return this.getOne(Wrappers.<CaseContractInfo>lambdaQuery().eq(CaseContractInfo::getContractNo, contractNo), false);
    }

    @Override
    public List<CaseContractInfo> getReviewSignCase() {
        return this.baseMapper.getReviewSignCase();
    }

    @Override
    public List<CaseBaseInfo> getInvoiceContractInfoList(LoanApproveTaskCondition loanApproveTaskCondition, List<String> caseCustInfoApplyNoList) {
        return this.baseMapper.getInvoiceContractInfoList(loanApproveTaskCondition, caseCustInfoApplyNoList);
    }

    @Override
    public List<EffectiveContractVO> getCurrentDayContractList(Date date) {
        return this.baseMapper.getCurrentDayContractList(date);
    }

    @Override
    public List<CaseBaseInfo> getLoanWaitSubmitCase() {
        return this.baseMapper.getLoanWaitSubmitCase();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void updateLoanSubmit(CaseContractInfo caseContractInfo) {
        this.updateById(caseContractInfo);
    }

    @Override
    public List<CaseContractInfo> getUnActivateCaseContractInfoList(String channelCode) {
        return caseContractInfoMapper.getUnActivateCaseContractInfoList(channelCode);
    }

    /**
     * 通过条件查询所有满足条件的合同信息
     *
     * @param caseContractCondition 条件
     * @return 返回的数据
     */
    @Override
    public Page<CaseContractCondition> getAllApprovedContractInfo(CaseContractCondition caseContractCondition) {
        return this.baseMapper.getAllApprovedContractInfo(
                new Page<>(caseContractCondition.getPageNumber(), caseContractCondition.getPageSize()),
                caseContractCondition.getApplyNo(),
                caseContractCondition.getContractNo(),
                caseContractCondition.getFinalCheckResult());
    }

    /**
     * 通过条件查询所有满足条件的合同信息
     *
     * @param caseContractCondition 条件
     * @return 返回的数据
     */
    @Override
    public List<CaseContractCondition> getAllApprovedContractInfoNoPage(CaseContractCondition caseContractCondition) {
        return this.baseMapper.getAllApprovedContractInfoNoPage(
                caseContractCondition.getApplyNo(),
                caseContractCondition.getFinalCheckResult());
    }


    /**
     * <!--
     * @param caseContractCondition caseContractCondition
     * @return return
     * @throws ParseException ParseException
     */
    @Override
    public IResponse policyCheck(CaseContractCondition caseContractCondition) throws ParseException {
        String applyNo = caseContractCondition.getApplyNo();
        // 1、获取保单信息
        List<CasePolicyCheckRecord> casePolicyCheckRecords = casePolicyCheckRecordService.list(Wrappers.<CasePolicyCheckRecord>lambdaQuery()
                .eq(CasePolicyCheckRecord::getApplyNo, applyNo)
                .eq(CasePolicyCheckRecord::getDelFlag, "0")
                .orderByDesc(CasePolicyCheckRecord::getVersion)
        );
        CasePolicyCheckRecord casePolicyCheckRecord = new CasePolicyCheckRecord();
        ApplyGuarantee queryGuarantee = new ApplyGuarantee();
        AtomicBoolean hasRecordFlag = new AtomicBoolean(false);
        if (CollectionUtil.isNotEmpty(casePolicyCheckRecords)) {
            // 是否保存或更新过保单OCR识别的json数据, 取最新的一条
            casePolicyCheckRecord = casePolicyCheckRecords.get(0);
            casePolicyCheckRecord.setVersion(casePolicyCheckRecord.getVersion() + 1);
            queryGuarantee.setApplyNo(applyNo);
            queryGuarantee.setJsonData(casePolicyCheckRecord.getJsonData());
            hasRecordFlag.set(true);
            log.info("已经保存过-查询的是案件端的记录={}", JSONUtil.parse(queryGuarantee));
            log.info("已经保存过-查询的是案件端的记录={}", JSONUtil.parse(casePolicyCheckRecord));
        } else {
            // 没有保存或更新过,从进件端拿
            queryGuarantee = getApplyGuarantee(caseContractCondition);
            log.info("没有保存过-查询的是进件端的记录={}", JSONUtil.parse(queryGuarantee));
            if (ObjectUtil.isNull(queryGuarantee)) {
                throw new AfsBaseException(MessageFormat.format("未查询到OCR识别的信息!申请编号={0}!", applyNo));
            }
            casePolicyCheckRecord = new CasePolicyCheckRecord();
            casePolicyCheckRecord.setApplyNo(applyNo);
            casePolicyCheckRecord.setJsonData(queryGuarantee.getJsonData());
            casePolicyCheckRecord.setVersion(0L);
            BeanUtil.copyProperties(queryGuarantee, casePolicyCheckRecord, "id");
            log.info("已经保存过-查询的是案件端的记录={}", JSONUtil.parse(casePolicyCheckRecord));
        }
        log.info("casePolicyCheckRecord1={}", JSONUtil.parse(casePolicyCheckRecord));
        // 2、根据保单信息获取验真结果
        // 2.1、组装请求 Vo,更新案件的保单表
        CheckInsuranceQueryReq checkInsuranceQueryReq = new CheckInsuranceQueryReq();
        checkInsuranceQueryReq = encapsulationRequestVo(queryGuarantee
                , null
                , casePolicyCheckRecord
                , hasRecordFlag.get()
        );
        if (!hasRecordFlag.get()) {
            log.info("暂未验真过！这里要进行一遍属性copy！");
            // 还没验真过
            BeanUtil.copyProperties(queryGuarantee, casePolicyCheckRecord);
            BeanUtil.copyProperties(checkInsuranceQueryReq, casePolicyCheckRecord);
        }
        casePolicyCheckRecord.setId(null);
        // 0 代表已进行验真的
        casePolicyCheckRecord.setCheckType("0");
        log.info("casePolicyCheckRecord2={}", JSONUtil.parse(casePolicyCheckRecord));
        casePolicyCheckRecordService.save(casePolicyCheckRecord);

        // 2.2、请求第三方接口
        Request policyCheckReq = Request.genRequest("", null, checkInsuranceQueryReq);
        checkPolicyCheckReqParam(policyCheckReq);
        Response policyCheckVoResponse = checkInsuranceService.checkInsuranceQuery(policyCheckReq, applyNo);
        log.info("==========policyCheckVoResponse={}===========", JSONUtil.parse(policyCheckVoResponse));
        CheckInsuranceQueryResp policyCheckResVo = (CheckInsuranceQueryResp) policyCheckVoResponse.getData();
        if (ObjectUtil.isEmpty(policyCheckResVo)) {
            throw new AfsBaseException(MessageFormat.format("获取中银保信结果失败!申请编号为{0}!", applyNo));
        }
        // 3、根据第二步返回的验真结果作出相应反馈
        return policyCheckResHandler(checkInsuranceQueryReq, policyCheckResVo, caseContractCondition, casePolicyCheckRecord);
    }

    /**
     * 设置默认值
     * @param policyCheckReq policyCheckReq
     * @throws ParseException AfsBaseException
     */
    public void checkPolicyCheckReqParam(Request policyCheckReq) throws ParseException {
        Object data = policyCheckReq.getData();
        if (ObjectUtil.isEmpty(data)) {
            throw new AfsBaseException("封装参数失败!");
        }
        CheckInsuranceQueryReq requestData = (CheckInsuranceQueryReq)data;
        if (StrUtil.isBlank(requestData.getPolicyNo())) {
            requestData.setPolicyNo("000000");
        }
        if (StrUtil.isBlank(requestData.getCompanyCode())) {
            // 默认中国人民财产保险股份有限公司
            requestData.setCompanyCode("000002");
        }
        if (StrUtil.isBlank(requestData.getPremiumAmount())) {
            requestData.setPremiumAmount("0.00");
        }
        if (StrUtil.isBlank(requestData.getPolicyEffectiveStats())) {
            requestData.setPolicyEffectiveStats("01");
        }
        if (StrUtil.isBlank(requestData.getValidTypes())) {
            requestData.setValidTypes(checkInsuranceConfig.getValidTypes());
        }
        if (StrUtil.isBlank(requestData.getPolicyProType())) {
            requestData.setPolicyProType(checkInsuranceConfig.getPolicyProType());
        }
        if (StrUtil.isBlank(requestData.getEncryMethod())) {
            requestData.setEncryMethod("0");
        }
        if (StrUtil.isEmpty(requestData.getEffectiveDateEnd())) {
            requestData.setEffectiveDateEnd(LocalDate.now().toString());
        } else {
            if (requestData.getEffectiveDateEnd().contains("CST")) {
                // 格式化endDate
                String formatCst2Ymd = formatCst2Ymd(requestData.getEffectiveDateEnd());
                log.info("Request格式化后的endDate={}", formatCst2Ymd);
                requestData.setEffectiveDateEnd(formatCst2Ymd);
            }
        }
    }

    @Override
    public void archiveSync(String applyNo) {

        CaseContractInfo caseContractInfo = this.getOne(Wrappers.<CaseContractInfo>query().lambda()
                .eq(CaseContractInfo::getApplyNo,applyNo));

        Assert.isTrue(EmptyUtils.isNotEmpty(caseContractInfo), "未查询到案件信息");

        CaseBaseInfo caseBaseInfo = caseBaseInfoService.getOne(Wrappers.<CaseBaseInfo>query().lambda()
                .eq(CaseBaseInfo::getApplyNo,applyNo));

        log.info("======caseChannelInfo======{}", JSON.toJSONString(caseContractInfo));
        ApplyCreditPool applyCreditPool = new ApplyCreditPool();
        applyCreditPool.setOrderType(caseBaseInfo.getOrderType());
        applyCreditPool.setCreateBy(caseBaseInfo.getCreateBy());
        applyCreditPool.setContractNo(caseContractInfo.getContractNo());
        applyCreditPool.setApplyNo(caseContractInfo.getApplyNo());
        applyCreditPool.setNode(AfsEnumUtil.key(ArchiveMQNodeEnum.LOAN));
        applyCreditPool.setChannelId(caseContractInfo.getDealerNo());
        applyCreditPool.setChannelName(caseContractInfo.getDealerName());
        applyCreditPool.setDeptId(caseBaseInfo.getDeptId());
        applyCreditPool.setChannelCode(caseContractInfo.getDealerNo());
        applyCreditPool.setChannelBelong(caseBaseInfo.getChannelBelong());
        applyCreditPool.setRentType(caseBaseInfo.getRentType());
        IResponse productResp = productFeign.getProductById(caseContractInfo.getProductId().toString());
        log.info("======订单号{}的产品信息为======{}",caseContractInfo.getApplyNo(), JSON.toJSONString(productResp));
        String productNumber = "";
        if (CommonConstants.SUCCESS.equals(productResp.getCode())) {
            JSONObject productPlan = Optional.ofNullable(productResp.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
            productNumber = productPlan.getString("productNumber");
        }
        applyCreditPool.setProductNumber(productNumber);
        log.info("======applyCreditPool======{}",JSON.toJSONString(applyCreditPool));
        AfsTransEntity<ApplyCreditPool> transEntity = new AfsTransEntity<>();
        transEntity.setTransCode(MqTransCode.AFS_ARCHIVE_CREDIT_POOL);
        transEntity.setData(applyCreditPool);
        archieLoanMoneySender.sendCreditPool(transEntity);
        log.info("推送成功-----,{}", JSON.toJSONString(transEntity));
    }


    /**
     * 格式化
     * @param cstDateTimeString cst字符串
     * @return 格式化后的
     */
    public static String formatCst2Ymd(String cstDateTimeString) throws ParseException {
        DateFormat formate1 = new SimpleDateFormat("yyyy-MM-dd");
        DateFormat formate2 = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
        Date date = formate2.parse(cstDateTimeString);
        return formate1.format(date);
    }

    /**
     * 获取保单信息
     *
     * @param applyNo 申请编号
     * @return
     */
    @Override
    public ApplyGuarantee getApplyGuarantee(String applyNo) {
        CaseContractCondition caseContractCondition = new CaseContractCondition();
        caseContractCondition.setApplyNo(applyNo);
        ApplyGuarantee queryGuarantee = getApplyGuarantee(caseContractCondition);
        log.info("Guarantee={}", JSONUtil.parse(queryGuarantee));
        if (ObjectUtil.isNull(queryGuarantee)) {
            throw new AfsBaseException(MessageFormat.format("申请号:{0},未获取到保单OCR识别的数据!", applyNo));
        }
        return queryGuarantee;
    }

    /**
     * 获取历史验真记录(已进行验真的)
     *
     * @param applyNo 申请编号
     * @return
     */
    @Override
    public List<CasePolicyCheckRecord> getPolicyCheckHistory(String applyNo) {
        return getHistory(applyNo, "0");
    }

    /**
     * 获取历史验真记录(未进行验真的，也就是编辑后未进行验真)
     *
     * @param applyNo 申请编号
     * @return
     */
    public List<CasePolicyCheckRecord> getPolicyCheckHistoryAuto(String applyNo) {
        return getHistory(applyNo, "1");
    }

    public List<CasePolicyCheckRecord> getHistory(String applyNo, String checkType) {
        log.info("getHistory_parameter={},applyNo={}", checkType, applyNo);
        List<CasePolicyCheckRecord> casePolicyCheckRecords = casePolicyCheckRecordService.list(Wrappers.<CasePolicyCheckRecord>query().lambda()
                .eq(CasePolicyCheckRecord::getApplyNo, applyNo)
                .eq(CasePolicyCheckRecord::getCheckType, checkType)
                .eq(CasePolicyCheckRecord::getDelFlag, "0")
                .orderByDesc(CasePolicyCheckRecord::getUpdateTime)
        );
        log.info("getHistory_casePolicyCheckRecords={}", JSONUtil.parse(casePolicyCheckRecords));
        if (CollectionUtil.isEmpty(casePolicyCheckRecords)) {
            return Collections.EMPTY_LIST;
        }
        return casePolicyCheckRecords;
    }

    /**
     * 获取保单信息
     *
     * @param applyNo 申请编号
     * @return
     */
    @Override
    public CasePolicyCheckRecord getCaseApplyGuarantee(String applyNo) {
        // 1、获取保单信息
        List<CasePolicyCheckRecord> casePolicyCheckRecords = casePolicyCheckRecordService.list(Wrappers.<CasePolicyCheckRecord>lambdaQuery()
                .eq(CasePolicyCheckRecord::getApplyNo, applyNo)
                .eq(CasePolicyCheckRecord::getDelFlag, "0")
                .orderByDesc(CasePolicyCheckRecord::getUpdateTime)
        );
        if (CollectionUtil.isEmpty(casePolicyCheckRecords)) {
            CaseContractCondition condition = new CaseContractCondition();
            condition.setApplyNo(applyNo);
            List<CaseContractCondition> allApprovedContractInfoNoPage = this.getAllApprovedContractInfoNoPage(condition);
            if (CollectionUtil.isEmpty(allApprovedContractInfoNoPage)) {
                throw new AfsBaseException("没有需要验真的合同！");
            }
            CaseContractCondition caseContractInfo = allApprovedContractInfoNoPage.get(0);
            // 需要返回一个空的
            CasePolicyCheckRecord casePolicyCheckRecord = new CasePolicyCheckRecord();
            casePolicyCheckRecord.setApplyNo(applyNo);
            casePolicyCheckRecord.setContractNo(caseContractInfo.getContractNo());
            casePolicyCheckRecord.setVersion(0L);
            casePolicyCheckRecord.setCreateBy(SecurityUtils.getUsername());
            casePolicyCheckRecord.setCreateTime(new Date());
            casePolicyCheckRecord.setUpdateTime(new Date());
            casePolicyCheckRecord.setUpdateBy(SecurityUtils.getUsername());
            casePolicyCheckRecord.setAppntName("");
            casePolicyCheckRecord.setPolicyNo("");
            casePolicyCheckRecord.setPremiumAmount("");
            casePolicyCheckRecord.setEffectiveDateEnd(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            casePolicyCheckRecord.setPolicyEffectiveStats("01");
            casePolicyCheckRecord.setCompanyCodeInit("");
            casePolicyCheckRecord.setAppntCertNo("");
            casePolicyCheckRecord.setAppntCertNoInit("");
            casePolicyCheckRecord.setFinalCheckResult("0");
            // 保单险种类型
            casePolicyCheckRecord.setPolicyProType(checkInsuranceConfig.getPolicyProType());
            casePolicyCheckRecord.setIdentiInsured("0");
            casePolicyCheckRecord.setValidTypes(checkInsuranceConfig.getValidTypes());
            // 为 1 代表只是存到记录表，并未进行保单验真
            casePolicyCheckRecord.setCheckType("1");
            log.info("Case_Policy_Check_The_Creat={}", JSONUtil.parse(casePolicyCheckRecord));
            casePolicyCheckRecordService.save(casePolicyCheckRecord);
            log.info("Case_Policy_Check_The_Creat={}", JSONUtil.parse(casePolicyCheckRecord));
            return casePolicyCheckRecord;
        }
        // 保存或更新过保单OCR识别的json数据
        log.info("已经保存过-查询的是案件端的记录={}", JSONUtil.parse(casePolicyCheckRecords));
        return casePolicyCheckRecords.get(0);
    }

    /**
     * 更新
     *
     * @param caseContractCondition 更新
     * @return boolean
     */
    @Override
    public Boolean updateCaseApplyGuarantee(CaseContractCondition caseContractCondition) {
        updateCaseApplyGuaranteeCheck(caseContractCondition);
        List<CasePolicyCheckRecord> casePolicyCheckRecords = casePolicyCheckRecordService.list(Wrappers.<CasePolicyCheckRecord>lambdaQuery()
                .eq(CasePolicyCheckRecord::getApplyNo, caseContractCondition.getApplyNo())
                .eq(CasePolicyCheckRecord::getDelFlag, "0")
                .orderByDesc(CasePolicyCheckRecord::getUpdateTime)
        );
        if (CollectionUtil.isEmpty(casePolicyCheckRecords)) {
            throw new AfsBaseException("请先进行一次保单验真后方可更新保单信息！");
        }
        log.info("已经保存过-查询的是案件端的记录={}", JSONUtil.parse(casePolicyCheckRecords));
        CasePolicyCheckRecord casePolicyCheckRecord = new CasePolicyCheckRecord();
        CasePolicyCheckRecord casePolicyCheckRecordByQuery = casePolicyCheckRecords.get(0);
        BeanUtil.copyProperties(casePolicyCheckRecordByQuery, casePolicyCheckRecord, "id");
        casePolicyCheckRecord.setVersion(casePolicyCheckRecordByQuery.getVersion() + 1);
        casePolicyCheckRecord.setCreateBy(SecurityUtils.getUsername());
        casePolicyCheckRecord.setCreateTime(new Date());
        casePolicyCheckRecord.setUpdateTime(new Date());
        casePolicyCheckRecord.setUpdateBy(SecurityUtils.getUsername());
        // 可更新的
        casePolicyCheckRecord.setAppntName(caseContractCondition.getAppntName());
        casePolicyCheckRecord.setPolicyNo(caseContractCondition.getPolicyNo());
        casePolicyCheckRecord.setPremiumAmount(caseContractCondition.getPremiumAmount());
        casePolicyCheckRecord.setEffectiveDateEnd(new SimpleDateFormat("yyyy-MM-dd").format(caseContractCondition.getEffectiveDateEnd()));
        casePolicyCheckRecord.setPolicyEffectiveStats(caseContractCondition.getPolicyEffectiveStats());
        casePolicyCheckRecord.setCompanyCodeInit(caseContractCondition.getCompanyCodeInit());
        casePolicyCheckRecord.setAppntCertNo(caseContractCondition.getAppntCertNoInit());
        casePolicyCheckRecord.setAppntCertNoInit(caseContractCondition.getAppntCertNoInit());
        casePolicyCheckRecord.setFinalCheckResult("0");
        // 为 1 代表只是存到记录表，并未进行保单验真（负责会被保单验真记录误伤）
        casePolicyCheckRecord.setCheckType("1");
        log.info("Case_Policy_Check_The_Updatest={}", JSONUtil.parse(casePolicyCheckRecord));
        casePolicyCheckRecordService.save(casePolicyCheckRecord);
        return true;
    }

    /**
     * 非空验证
     * @param caseContractCondition 更新条件
     */
    private void updateCaseApplyGuaranteeCheck(CaseContractCondition caseContractCondition) {
        if (!StrUtil.isAllNotBlank(caseContractCondition.getApplyNo(), caseContractCondition.getContractNo())) {
            throw new AfsBaseException("更新保单信息失败！请联系管理员处理！");
        }
        if (StrUtil.isBlank(caseContractCondition.getPremiumAmount())) {
            throw new AfsBaseException("更新保单信息失败！保费金额不可为空！");
        }
        if (ObjectUtil.isEmpty(caseContractCondition.getEffectiveDateEnd())) {
            throw new AfsBaseException("更新保单信息失败！合同终止日期不可为空！");
        }
        if (StrUtil.isBlank(caseContractCondition.getPolicyNo())) {
            throw new AfsBaseException("更新保单信息失败！保单号不可为空！");
        }
        if (StrUtil.isBlank(caseContractCondition.getAppntName())) {
            throw new AfsBaseException("更新保单信息失败！姓名不可为空！");
        }
        if (StrUtil.isBlank(caseContractCondition.getAppntCertNoInit())) {
            throw new AfsBaseException("更新保单信息失败！身份证号不可为空！");
        }
        // 对公司名称进行校验
        if (StrUtil.isBlank(caseContractCondition.getCompanyCodeInit())
                || !StringUtils.endsWithAny(caseContractCondition.getCompanyCodeInit(), "公司", "保险")
                || StrUtil.isBlank(dealCompanyCode(caseContractCondition.getCompanyCodeInit()))
        ) {
            throw new AfsBaseException("更新保单信息失败！公司名称输入不正确！");
        }
    }

    /**
     * 对要更新的数据进行封装
     * @param caseContractCondition
     * @return
     */
    @NotNull
    private static void fillCasePolicyCheck(CaseContractCondition caseContractCondition
            , CheckInsuranceQueryResp policyCheckResVo
            , CasePolicyCheck casePolicyCheck) {
        casePolicyCheck.setApplyNo(caseContractCondition.getApplyNo());
        casePolicyCheck.setContractNo(caseContractCondition.getContractNo());
        casePolicyCheck.setFourPointCheck(Optional.ofNullable(policyCheckResVo.getData().getElementsConsistent()).orElse(""));
        casePolicyCheck.setFourPointNumber(0);
        casePolicyCheck.setInsuranceExpensesCheck(Optional.ofNullable(policyCheckResVo.getData().getPremiumConsistent()).orElse(""));
        casePolicyCheck.setInsuranceExpensesNumber(0);
        casePolicyCheck.setPolicyStatusCheck(Optional.ofNullable(policyCheckResVo.getData().getPolicyStatusConsistent()).orElse(""));
        casePolicyCheck.setPolicyStatusNumber(0);
        casePolicyCheck.setPolicyEndDateCheck(Optional.ofNullable(policyCheckResVo.getData().getContractEndDate()).orElse(""));
        casePolicyCheck.setPolicyEndDateNumber(0);
    }

    /**
     * 对要更新的数据进行封装
     * @param caseContractCondition
     * @return
     */
    @NotNull
    private static void fillCasePolicyCheck(CaseContractCondition caseContractCondition
            , CheckInsuranceQueryResp policyCheckResVo
            , CasePolicyCheckRecord casePolicyCheck) {
        casePolicyCheck.setApplyNo(caseContractCondition.getApplyNo());
        casePolicyCheck.setContractNo(caseContractCondition.getContractNo());
        casePolicyCheck.setFourPointCheck(Optional.ofNullable(policyCheckResVo.getData().getElementsConsistent()).orElse(""));
        casePolicyCheck.setFourPointNumber(0);
        casePolicyCheck.setInsuranceExpensesCheck(Optional.ofNullable(policyCheckResVo.getData().getPremiumConsistent()).orElse(""));
        casePolicyCheck.setInsuranceExpensesNumber(0);
        casePolicyCheck.setPolicyStatusCheck(Optional.ofNullable(policyCheckResVo.getData().getPolicyStatusConsistent()).orElse(""));
        casePolicyCheck.setPolicyStatusNumber(0);
        casePolicyCheck.setPolicyEndDateCheck(Optional.ofNullable(policyCheckResVo.getData().getContractEndDate()).orElse(""));
        casePolicyCheck.setPolicyEndDateNumber(0);
    }

    /**
     * 获取保单信息
     *
     * @param caseContractCondition
     * @return
     */
    private ApplyGuarantee getApplyGuarantee(CaseContractCondition caseContractCondition) {
        ApplyGuarantee applyGuarantee = new ApplyGuarantee();
        applyGuarantee.setApplyNo(caseContractCondition.getApplyNo());
        ApplyGuarantee queryGuarantee = applyContractFeign.queryGuarantee(applyGuarantee, applyCaseHeader());
        return queryGuarantee;
    }

    private Map<String, String> applyCaseHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getApplyClientId());
        headers.put("clientSecret", applyConfig.getApllyClientSecret());
        return headers;
    }

    /**
     * 根据调用第三方接口返回的验真结果作出相应反馈
     * v.	依次对每一项校验结果进行解析。1
     * 1.	当输出结果00时，该项校验通过。1
     * 2.	当输出结果01，02时，该项校验不通过。1
     * 3.	当输出结果03时，记录异常次数为1，该项核验不输出结果，等待下次调用，重新调用该项校验，仍然输出03，则该项校验不通过。
     * 4.	当输出结果99时，该项核验不输出结果，等待下次调用。
     * 5.	当4项中某一项核验不通过时，本次保单输出结果不通过，记录保单核验异常。
     * 6.	当4项中的某一项没有结果输出时，针对具体项目，重新调用，直至保单核验输出结论或者超出最大校验时间。
     *
     * @param policyCheckResVo
     * @return IResponse
     */
    private IResponse policyCheckResHandler(CheckInsuranceQueryReq checkInsuranceQueryReq
            , CheckInsuranceQueryResp policyCheckResVo
            , CaseContractCondition caseContractCondition
            , CasePolicyCheckRecord casePolicyCheckRecord
            ) {
        // 保存到历史记录
        BeanUtil.copyProperties(checkInsuranceQueryReq, casePolicyCheckRecord);
        casePolicyCheckRecord.setCreateTime(new Date());
        casePolicyCheckRecord.setCreateBy(SecurityUtils.getUsername());
        StringBuilder sb = new StringBuilder();
        boolean checkSuccess = false;
        if (Objects.equals(policyCheckResVo.getRetCode(), "200") && ObjectUtil.isNotNull(policyCheckResVo.getData())) {
            CheckInsuranceQueryResp.Data checkResVoData = policyCheckResVo.getData();
            if (Objects.equals(checkResVoData.getQueryStatus(), "000")) {
                // 查询成功：成功并有数据
                // 检查 四要素、保费金额、保单生效状态、保单险种类型、保险合同生效日期、保险合同终止日期：是否一致、 以及 收费标识
                checkSuccess = checkConcordanceAndIdentificationOfCharges(checkResVoData, casePolicyCheckRecord, sb);
            } else if (Objects.equals(checkResVoData.getQueryStatus(), "001")) {
                // 查询失败：接口请求无异常，数据未查询到数据的情况
                throw new AfsBaseException("查询失败: 接口请求无异常，数据未查询到数据的情况，请联系管理员处理！");
            }
        } else {
            // 交易请求失败：所有失败统一归类为 500（包含无权限，用户名密码不正确，机构解密失败，必传参数未传，传入的参数不在指定范围内等）
            throw new AfsBaseException(MessageFormat.format("交易失败:{0}, 请联系管理员处理！", InsuranceCheckResCodeEnum.CALL_FAIL.getName()));
        }

        CasePolicyCheck casePolicyCheck = new CasePolicyCheck();
        casePolicyCheck.setCreateBy(SecurityUtils.getUsername());
        casePolicyCheck.setCreateTime(new Date());
        fillCasePolicyCheck(caseContractCondition, policyCheckResVo, casePolicyCheck);
        fillCasePolicyCheck(caseContractCondition, policyCheckResVo, casePolicyCheckRecord);
        casePolicyCheck.setFinalCheckResult(checkSuccess ? "1" : "0");
        casePolicyCheckRecord.setFinalCheckResult(checkSuccess ? "1" : "0");
        // 无论是否成功都要更新保单验真表
        log.info("更新保单验真表Case-Policy-Check:{},错误消息：{}", JSONUtil.parse(casePolicyCheck), sb);
        log.info("更新保单验真记录表Case-Policy-Check:{}", JSONUtil.parse(casePolicyCheckRecord));
        casePolicyCheck.setUpdateTime(new Date());
        casePolicyCheck.setUpdateBy(SecurityUtils.getUsername());
        casePolicyCheckRecord.setUpdateTime(new Date());
        casePolicyCheckRecord.setUpdateBy(SecurityUtils.getUsername());
        log.info("save_before_casePolicyCheck={}", JSONUtil.parse(casePolicyCheck));
        log.info("save_before_casePolicyCheckRecord={}", JSONUtil.parse(casePolicyCheckRecord));
        // 保存历史记录信息
        CasePolicyCheck casePolicyCheckByQuery = casePolicyCheckService.getOne(
                Wrappers.<CasePolicyCheck>lambdaQuery()
                        .eq(CasePolicyCheck::getApplyNo, casePolicyCheckRecord.getApplyNo())
                        .eq(CasePolicyCheck::getDelFlag, "0")
        );
        casePolicyCheck.setValidTypes(checkInsuranceConfig.getValidTypes());
        casePolicyCheck.setAppntName(casePolicyCheckRecord.getAppntName());
        casePolicyCheck.setAppntCertNo(casePolicyCheckRecord.getAppntCertNo());
        casePolicyCheck.setPolicyNo(casePolicyCheckRecord.getPolicyNo());
        casePolicyCheck.setIdentiInsured(casePolicyCheckRecord.getIdentiInsured());
        casePolicyCheck.setPremiumAmount(casePolicyCheckRecord.getPremiumAmount());
        casePolicyCheck.setPolicyProType(casePolicyCheckRecord.getPolicyProType());
        casePolicyCheck.setCompanyCode(casePolicyCheckRecord.getCompanyCode());
        casePolicyCheck.setPolicyEffectiveStats(casePolicyCheckRecord.getPolicyEffectiveStats());
        casePolicyCheck.setEffectiveDateTake(casePolicyCheckRecord.getEffectiveDateTake());
        casePolicyCheck.setEffectiveDateEnd(casePolicyCheckRecord.getEffectiveDateEnd());
        casePolicyCheck.setJsonData(casePolicyCheckRecord.getJsonData());
        casePolicyCheck.setSerialNum(casePolicyCheckRecord.getSerialNum());
        if (ObjectUtil.isNotNull(casePolicyCheckByQuery)) {
            casePolicyCheck.setId(casePolicyCheckByQuery.getId());
            casePolicyCheckService.updateById(casePolicyCheck);
        } else {
            casePolicyCheckService.save(casePolicyCheck);
        }
        casePolicyCheckRecordService.updateById(casePolicyCheckRecord);
        if(checkSuccess) {
            return IResponse.success(casePolicyCheck);
        }
        return IResponse.fail(sb.toString());
    }

    /**
     * 检查 四要素、保费金额、保单生效状态、保单险种类型、保险合同生效日期、保险合同终止日期：是否一致、 以及 收费标识
     *
     * @param checkResVoData
     * @param sb             返回的 包含错误信息的校验结果
     */
    private boolean checkConcordanceAndIdentificationOfCharges(CheckInsuranceQueryResp.Data checkResVoData
            , CasePolicyCheckRecord casePolicyCheckRecord
            , StringBuilder sb) {
        Map<String, InsuranceCheckResCodeEnum> codeToMessageMap = new HashMap<>();
        codeToMessageMap.put("00", InsuranceCheckResCodeEnum.FOUR_ELEMENTS_CONSISTENCY);
        codeToMessageMap.put("01", InsuranceCheckResCodeEnum.FOUR_ELEMENTS_INCONSISTENCY);
        codeToMessageMap.put("02", InsuranceCheckResCodeEnum.FOUR_ELEMENTS_APPLY_NO_NOT_EXIT);
        codeToMessageMap.put("03", InsuranceCheckResCodeEnum.FOUR_ELEMENTS_EMPTY);
        codeToMessageMap.put("99", InsuranceCheckResCodeEnum.FOUR_ELEMENTS_OTHER);
        boolean checkAccess = true;
        // 四要素是否一致
        if (StringUtils.isNotBlank(checkResVoData.getElementsConsistent())) {
            String code = checkResVoData.getElementsConsistent();
            // 使用Map来查找code对应的消息
            InsuranceCheckResCodeEnum enumValue = codeToMessageMap.getOrDefault(code, null);
            if (enumValue != null && !StrUtil.equals(code, "00")) {
                sb.append(String.format("验证四要素是否一致:【%s】", enumValue.getName()));
                checkAccess = false; // 如果找到了对应的code，则设置checkAccess为false
            }
            casePolicyCheckRecord.setFourPointCheck(code);
        }

        // 保费金额是否一致
        if (StringUtils.isNotBlank(checkResVoData.getPremiumConsistent())) {
            String code = checkResVoData.getPremiumConsistent();
            // 使用Map来查找code对应的消息
            InsuranceCheckResCodeEnum enumValue = codeToMessageMap.getOrDefault(code, null);
            if (enumValue != null && !StrUtil.equals(code, "00")) {
                sb.append(String.format("验证保费金额是否一致:【%s】", enumValue.getName()));
                checkAccess = false;
            }
            casePolicyCheckRecord.setInsuranceExpensesCheck(code);
        }

        // 保单生效状态是否一致
        if (StringUtils.isNotBlank(checkResVoData.getPolicyStatusConsistent())) {
            Map<String, InsuranceCheckResCodeEnum> policyStatusMap = new HashMap<>();
            policyStatusMap.put("00", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS);
            policyStatusMap.put("01", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS_ONE);
            policyStatusMap.put("02", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS_DISCONTINUE);
            policyStatusMap.put("03", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS_TERMINATE);
            policyStatusMap.put("04", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS_FIVE);
            policyStatusMap.put("05", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_STATUS_OUTWEIGH_FIVE);
            policyStatusMap.put("06", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_APPLY_NO_NOT_EXIT);
            policyStatusMap.put("07", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_EMPTY);
            policyStatusMap.put("99", InsuranceCheckResCodeEnum.POLICY_IN_FORCE_OTHER);
            String code = checkResVoData.getPolicyStatusConsistent();
            InsuranceCheckResCodeEnum enumValue = policyStatusMap.getOrDefault(code, null);
            if (enumValue != null && !StrUtil.equals(code, "00")) {
                sb.append(String.format("验证保单生效状态是否一致:【%s】", enumValue.getName()));
                checkAccess = false;
            }
            casePolicyCheckRecord.setPolicyStatusCheck(code);
        }

        // 验证保险合同终止日期是否一致
        if (StringUtils.isNotBlank(checkResVoData.getContractEndDate())) {
            String code = checkResVoData.getContractEndDate();
            // 使用Map来查找code对应的消息
            InsuranceCheckResCodeEnum enumValue = codeToMessageMap.getOrDefault(code, null);
            if (enumValue != null && !StrUtil.equals(code, "00")) {
                sb.append(String.format("验证保险合同终止日期是否一致:【%s】", enumValue.getName()));
                checkAccess = false;
            }
            casePolicyCheckRecord.setPolicyEndDateCheck(code);
        }
        log.info("checkResVoData={},checkAccess={},sb={}", JSONUtil.parse(checkResVoData), checkAccess, sb.toString());
        return checkAccess;
    }

    /**
     * 封装请求参数
     *
     * @param applyGuarantee
     * @return
     */
    public CheckInsuranceQueryReq encapsulationRequestVo(ApplyGuarantee applyGuarantee
            , String validType
            , CasePolicyCheckRecord casePolicyCheckRecord
            , boolean hasRecordFlag) {
        // 根据实际OCR识别的数据进行封装到请求第三方接口的入参
        log.info("====================checkInsuranceQueryReq:{}==============: ", JSONUtil.parse(applyGuarantee));
        CheckInsuranceQueryReq checkInsuranceQueryReq = analyzingPolicyInformation(applyGuarantee
                , validType
                , casePolicyCheckRecord
                , hasRecordFlag
        );
        log.info("====================checkInsuranceQueryReq:{}==============: ", JSONUtil.parse(checkInsuranceQueryReq));
        return checkInsuranceQueryReq;
    }

    private CheckInsuranceQueryReq analyzingPolicyInformation(ApplyGuarantee applyGuarantee
            , String validType
            , CasePolicyCheckRecord casePolicyCheckRecord
            , boolean hasRecordFlag) {
        CheckInsuranceQueryReq checkInsuranceQueryReq = new CheckInsuranceQueryReq();

        checkInsuranceQueryReq.setUserName(Md5AndSalt.getPasswordByDefaultSolt(checkInsuranceConfig.getUserName()));
        checkInsuranceQueryReq.setPassword(Md5AndSalt.getPasswordByDefaultSolt(checkInsuranceConfig.getPassword()));

        checkInsuranceQueryReq.setAppntCertNo(dealCertNo(applyGuarantee.getApplyNo()));
        checkInsuranceQueryReq.setValidTypes(StrUtil.isBlank(validType) ? checkInsuranceConfig.getValidTypes() : validType);
        checkInsuranceQueryReq.setEncryMethod("0");

        // 保单险种类型
        checkInsuranceQueryReq.setPolicyProType(checkInsuranceConfig.getPolicyProType());
        checkInsuranceQueryReq.setIdentiInsured("0");
        // 保单生效状态
        checkInsuranceQueryReq.setPolicyEffectiveStats("01");
        boolean updatePremiumAmountFlag = false;
        boolean updateCompanyCodeFlag = false;
        // 处理  保险合计(元) 需要累加情况
        BigDecimal amount = BigDecimal.ZERO;
        if (!hasRecordFlag) {
            log.info("通过json解析获取！hasRecordFlag={}", hasRecordFlag);
            // 解析保单信息获取必要信息
            String guaranteeJsonData = applyGuarantee.getJsonData();
            if (StringUtils.isNotBlank(guaranteeJsonData)) {
                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = new JSONObject();
                try {
                    // json 数据可能为数组或对象 即 [ | { 开头
                    jsonArray = JSON.parseArray(guaranteeJsonData);
                } catch (JSONException | ClassCastException exception) {
                    try {
                        jsonObject = JSON.parseObject(guaranteeJsonData);
                    } catch (JSONException jsonException) {
                        log.warn("解析json失败！该json为非数组非对象,applyNo={},jsonData={}", applyGuarantee.getApplyNo(), guaranteeJsonData);
                    }
                }

                if (!jsonArray.isEmpty()) {
                    log.info("根据获取的保单信息解析的是（数组） jsonArray: {}", JSONUtil.parse(jsonArray));
                    for (Object structural : jsonArray) {
                        JSONObject jsonData = JSON.parseObject(structural.toString());
                        JSONArray groups = jsonData.getJSONArray("Groups");
                        if (ObjectUtil.isNotEmpty(groups) && !groups.isEmpty()) {
                            for (Object group : groups) {
                                JSONObject jsonDataGroup = JSON.parseObject(group.toString());
                                JSONArray lines = jsonDataGroup.getJSONArray("Lines");
                                if (ObjectUtil.isNotEmpty(lines) && !lines.isEmpty()) {
                                    for (Object line : lines) {
                                        JSONObject jsonDataLine = JSON.parseObject(line.toString());
                                        JSONObject key = jsonDataLine.getJSONObject("Key");
                                        String autoName = key.getString("AutoName");
                                        if (ObjectUtil.isNotEmpty(key) && StringUtils.isNotEmpty(autoName)) {
                                            JSONObject value = jsonDataLine.getJSONObject("Value");
                                            String autoContent = value.getString("AutoContent");
                                            if (StrUtil.containsAny(autoName, "保险单号")) {
                                                checkInsuranceQueryReq.setPolicyNo(autoContent);
                                            }
                                            if (StrUtil.containsAny(autoName, "本保单投保人为", "投保人")) {
                                                checkInsuranceQueryReq.setAppntName(autoContent);
                                            }
                                            // 保险机构代码: 保险人公司名称|标题|机构
                                            if (StrUtil.containsAny(autoName, "公司名称", "保险人公司名称", "机构", "标题")) {
                                                if (!updateCompanyCodeFlag) {
                                                    updateCompanyCodeFlag = StrUtil.contains(autoName, "公司名称");
                                                    String getCodeByName = dealCompanyCode(autoContent);
                                                    if (StrUtil.isNotBlank(getCodeByName)) {
                                                        // 根据公司名称查到对应公司代码的时候才去更新
                                                        checkInsuranceQueryReq.setCompanyCode(getCodeByName);
                                                        casePolicyCheckRecord.setCompanyCodeInit(autoContent);
                                                    }
                                                }
                                            }

                                            // 保费金额 皆有可能有保险费：重要提示/保险费合计(￥
                                            if (StrUtil.equalsAny(autoName, "保险费合计(￥", "保险费合计RMB", "保险费合计（人民币大写）", "保险费合计(人民币大写)")
                                                    && premiumAmountMatchesCheck(autoContent)
                                            ) {
                                                checkInsuranceQueryReq.setPremiumAmount(dealPremiumAmount(autoContent));
                                                updatePremiumAmountFlag = true;
                                            }
                                            if (!updatePremiumAmountFlag && (StrUtil.equalsAny(autoName, "保险费合计")
                                                    && premiumAmountMatchesCheck(autoContent)
                                            )) {
                                                updatePremiumAmountFlag = true;
                                                checkInsuranceQueryReq.setPremiumAmount(dealPremiumAmount(autoContent));
                                            }
                                            if (!updatePremiumAmountFlag && (StrUtil.equalsAny(autoName, "保险合计(元)", "保费合计(元)", "保险费(元)"))
                                            ) {
                                                if (!StringUtils.isBlank(dealPremiumAmount(autoContent))
                                                        && premiumAmountMatchesCheck(autoContent)
                                                ) {
                                                    // 此种需要累加
                                                    amount = amount.add(new BigDecimal(dealPremiumAmount(autoContent)));
                                                    checkInsuranceQueryReq.setPremiumAmount(amount.toString());
                                                }
                                            }

//                                    保险合同生效日期\保险合同终止日期：保险期间：
                                            if (StrUtil.containsAny(autoName, "保险期间")) {
                                                String[] effectiveDate = dealEffectiveDates(autoContent);
                                                if (effectiveDate.length == 2) {
                                                    // 需要长度为 2才能正常切割为生效日期和终止日期
                                                    checkInsuranceQueryReq.setEffectiveDateTake(formatDate(effectiveDate[0]));
                                                    checkInsuranceQueryReq.setEffectiveDateEnd(formatDate(effectiveDate[1]));
                                                }
                                            }
                                            // 身份证号 被保险人身份证号码(统一社会信用代码) 不一定会有 没有的话会直接使用之前调用合同基本信息
                                            if (StrUtil.containsAny(autoName, "被保险人身份证号码(统一社会信用代码)")) {
                                                checkInsuranceQueryReq.setAppntCertNo(dealCertNoByStr(autoContent));
                                                casePolicyCheckRecord.setAppntCertNoInit(autoContent);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else if (!jsonObject.isEmpty()) {
                    log.info("根据获取的保单信息解析的是（对象） jsonObject: {}", JSONUtil.parse(jsonObject));
                    // 如果是对象
                    JSONArray fieldList = jsonObject.getJSONArray("FieldList");
                    log.info("根据获取的保单信息解析 fieldList: {}", JSONUtil.parse(fieldList));
                    if (ObjectUtil.isNotEmpty(fieldList) && !fieldList.isEmpty()) {
                        for (Object field : fieldList) {
                            log.info("根据获取的保单信息解析 field: {}", JSONUtil.parse(field));
                            JSONObject jsonFieldObj = JSON.parseObject(field.toString());
                            log.info("根据获取的保单信息解析 jsonFieldObj: {}", JSONUtil.parse(jsonFieldObj));
                            if (ObjectUtil.isNotEmpty(jsonFieldObj) && !jsonFieldObj.isEmpty()) {
                                String key = jsonFieldObj.getString("key");
                                String value = jsonFieldObj.getString("value");
                                log.info("根据获取的保单信息解析 key: {}, value: {}", key, value);
                                if (StringUtils.isBlank(value)) {
                                    //如果值为空，不做处理
                                    continue;
                                }
                                switch (key) {
                                    case "BXDH": {
                                        // 保险单号
                                        checkInsuranceQueryReq.setPolicyNo(value);
                                        break;
                                    }
                                    case "BBXR_MC": {
                                        // 被保险人名称
                                        checkInsuranceQueryReq.setAppntName(value);
                                        break;
                                    }
                                    case "BXGSMC": {
                                        // 保险公司名称 要根据这个去找机构代码
                                        checkInsuranceQueryReq.setCompanyCode(getCodeByName(value));
                                        casePolicyCheckRecord.setCompanyCodeInit(value);
                                        break;
                                    }
                                    case "BXFHJXX": {
                                        // 保险费合计小写
                                        checkInsuranceQueryReq.setPremiumAmount(value);
                                        break;
                                    }
                                    case "BXQJQ": {
                                        // 保修期间起
                                        checkInsuranceQueryReq.setEffectiveDateTake(formatDateTime(value));
                                        break;
                                    }
                                    case "BXQJZ": {
                                        // 保修期间止
                                        checkInsuranceQueryReq.setEffectiveDateEnd(formatDateTime(value));
                                        break;
                                    }
                                    case "BBXR_SFZHM": {
                                        // 被保险人身份证号码
                                        checkInsuranceQueryReq.setAppntCertNo(dealCertNoByStr(value));
                                        casePolicyCheckRecord.setAppntCertNoInit(value);
                                        break;
                                    }
                                    default: {
                                        // 解析的其他数据无需处理
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            log.info("通过record获取！hasRecordFlag={}", hasRecordFlag);
            BeanUtil.copyProperties(casePolicyCheckRecord, checkInsuranceQueryReq, "id");
            checkInsuranceQueryReq.setAppntCertNo(dealCertNoByStr(casePolicyCheckRecord.getAppntCertNoInit()));
            checkInsuranceQueryReq.setCompanyCode(getCodeByName(casePolicyCheckRecord.getCompanyCodeInit()));
            if (StrUtil.isBlank(checkInsuranceQueryReq.getValidTypes())) {
                checkInsuranceQueryReq.setValidTypes(StrUtil.isBlank(validType) ? checkInsuranceConfig.getValidTypes() : validType);
            }
            if (StrUtil.isBlank(checkInsuranceQueryReq.getPolicyProType())) {
                checkInsuranceQueryReq.setPolicyProType(checkInsuranceConfig.getPolicyProType());
            }
            log.info("复制的保存的历史记录数据!申请编号={}", casePolicyCheckRecord.getApplyNo());
            log.info("复制的保存的历史记录数据!casePolicyCheckRecord={}", JSONUtil.parse(casePolicyCheckRecord));
            log.info("复制的保存的历史记录数据!checkInsuranceQueryReq={}", JSONUtil.parse(checkInsuranceQueryReq));
        }
        // 生成流水号
        checkInsuranceQueryReq.setSerialNum(dealSerialNum(checkInsuranceConfig.getOrganizationCode()));

        return checkInsuranceQueryReq;
    }

    private boolean premiumAmountMatchesCheck(String autoContent) {
        return autoContent.matches("\\d+(\\.\\d+)?") || autoContent.matches("\\d+(\\.\\d+)?元\\)");
    }

    /**
     * 根据申请编号获取身份证号
     *
     * @param applyNo
     * @return
     */
    private String dealCertNo(String applyNo) {
        return dealCertNo(applyNo, true);
    }

    private String dealCertNo(String applyNo, boolean md5Flag) {
        CaseBaseInfo caseByApplyNo = caseBaseInfoService.getCaseByApplyNo(applyNo);
        if (md5Flag) {
            return dealCertNoByStr(caseByApplyNo.getCertNoRepeat());
        }
        return caseByApplyNo.getCertNoRepeat();
    }

    /**
     * 根据申请编号获取身份证号
     *
     * @param certNo
     * @return
     */
    private String dealCertNoByStr(String certNo) {
        if (StringUtils.isNotBlank(certNo) && (certNo.endsWith("X"))) {
            // 身份证号最后一位明文为大写 X
            return Md5AndSalt.md5Hex(certNo.substring(0, certNo.length() - 1)) +"X";
        }
        return Md5AndSalt.md5Hex(certNo);
    }

    /**
     * 处理保费总额
     *
     * @param autoContent
     * @return 保费总额
     */
    private static String dealPremiumAmount(String autoContent) {
        log.info("================处理保费总额================{}", autoContent);
        log.info("================处理保费总额================{}", getFirstValueByKeyword(autoContent, "元"));
        log.info("================处理保费总额================{}", getPremiumAmountByFirstValue(getFirstValueByKeyword(autoContent, "元")));
        return getPremiumAmountByFirstValue(getFirstValueByKeyword(autoContent, "元"));
    }

    /**
     * 获取保费总额
     *
     * @param
     * @return
     */
    private static String getPremiumAmountByFirstValue(String firstValue) {
        String regex = "([0-9]+(?:\\.[0-9]{2}))";
        Matcher matcher = Pattern.compile(regex).matcher(firstValue);
        return matcher.find() ? matcher.group(1) : "";
    }


    /**
     * 处理保险机构代码
     *
     * @param autoContent
     * @return 保险机构代码
     */
    private static String dealCompanyCode(String autoContent) {
        String getCodeByName = getCodeByName(getFirstValueByKeyword(autoContent, "公司") + "公司");
        if (StringUtils.isBlank(getCodeByName)) {
            // 处理特殊情况
            if ("太平洋保险".equals(autoContent)) {
                return InsuranceOrganizationCodeEnum.CHINA_PACIFIC_PROPERTY_INSURANCE.getCode();
            } else if("中国人民保险".equals(autoContent)) {
                return InsuranceOrganizationCodeEnum.PEOPLE_PROPERTY_CASUALTY_INSURANCE.getCode();
            }
        }
        return getCodeByName;
    }

    /**
     * 根据关键字提取值
     *
     * @param autoContent
     * @param keyword
     * @return 匹配到的值
     */
    private static String getFirstValueByKeyword(String autoContent, String keyword) {
        int index = autoContent.indexOf(keyword);
        return index != -1 ? autoContent.substring(0, index).trim() : "";

    }

    /**
     * 根据名称获取保险机构代码
     *
     * @param name
     * @return 保险机构代码
     */
    private static String getCodeByName(String name) {
        return InsuranceOrganizationCodeEnum.getCodeByName(name);
    }

    /**
     * 生成流水号
     * 接入机构代码+交易日期 (YYYYMMDD+10位流水（每日从1计数，不足10位左补0，不能重复))
     *
     * @param organizationCode 接入机构代码
     * @return 流水号
     */
    private String dealSerialNum(String organizationCode) {
        // 获取当前日期
        String todayStr = getCurrentDate();

        // 更新并获取当日计数并格式化为10位数字，不足10位左补0
        String dailyCountStr = formatDailyCount(incrementAndGetDailyCounter());

        // 组合流水号
        return organizationCode + todayStr + dailyCountStr;
    }

    /**
     * 格式化当日计数为10位数字，不足10位左补0
     *
     * @param count 当日计数
     * @return 格式化后的字符串
     */
    private static String formatDailyCount(Long count) {
        return String.format("%010d", count);
    }

    /**
     * 更新并获取当日计数
     *
     * @return 当日计数
     */
    private synchronized Long incrementAndGetDailyCounter() {
        String key = CACHE_KEY_PRE + "daily_count";
        Long currentCount = redisTemplate.opsForValue().increment(key, 1L);

        // 当日首次递增时设置过期时间
        if (currentCount != null && currentCount == 1) {
            setExpirationTime(key);
        }

        return currentCount;
    }

    /**
     * 设置过期时间为当前日期的明天零点（当地时区）
     *
     * @param key Redis键
     */
    private void setExpirationTime(String key) {
        LocalDateTime tomorrowMidnight = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        long expirationTime = tomorrowMidnight.atZone(ZoneId.systemDefault()).toEpochSecond();
        redisTemplate.expireAt(key, Instant.ofEpochSecond(expirationTime));
    }


    /**
     * 获取当前日期 yyyyMMdd
     *
     * @return
     */
    public static String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return LocalDate.now().format(formatter);
    }


    /**
     * 处理合同生效日期和终止日期
     * 自2022年11月30日18时起至2023年11月30日24时止
     * 返回 [生效日期, 终止日期]
     *
     * @param policyDate
     * @return
     */
    private static String[] dealEffectiveDates(String policyDate) {
        Matcher matcher = Pattern.compile("(\\d{4}[年-]\\d{1,2}[月-]\\d{1,2}[日-].*?)(\\d{4}[年-]\\d{1,2}[月-]\\d{1,2}[日-])").matcher(policyDate);
        if (matcher.find()) {
            return new String[]{matcher.group(1), matcher.group(2)};
        }
        return new String[0];
    }

    /**
     * 格式化日期为yyyy-MM-dd
     *
     * @param dateStr
     * @return
     */
    private static String formatDate(String dateStr) {
        Matcher matcher = Pattern.compile("(\\d{4})年(\\d{1,2})月(\\d{1,2})日").matcher(dateStr);
        if (matcher.find()) {
            String year = matcher.group(1);
            String month = matcher.group(2).length() == 1 ? "0" + matcher.group(2) : matcher.group(2);
            String day = matcher.group(3).length() == 1 ? "0" + matcher.group(3) : matcher.group(3);

            return year + "-" + month + "-" + day;
        }
        return "";
    }

    /**
     * 格式化日期为yyyy-MM-dd
     *
     * @param dateTime dateTime
     * @return
     */
    public static String formatDateTime(String dateTime) {
        Matcher matcher = Pattern.compile("(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})").matcher(dateTime);
        if (matcher.matches()) {
            String year = matcher.group(1);
            String month = matcher.group(2);
            String day = matcher.group(3);

            return String.format("%s-%s-%s", year, month, day);
        } else {
            log.error("保单验真 格式化日期为出现异常: Invalid date format :{}", dateTime);
            return null;
        }
    }

    /**
     * 通过合同编号查询退回次数
     * @param contractNo 合同编号
     * @return 返回结果
     */
    public IResponse<Integer> getBackTimesByContractNo(String contractNo){

        List<CaseContractInfo> list = this.list(Wrappers.<CaseContractInfo>lambdaQuery().select(CaseContractInfo::getBackTimes).eq(CaseContractInfo::getContractNo, contractNo));
        log.warn("通过合同编号查询退回次数，其中入参={},返回结果={}",contractNo,JSONObject.toJSONString(list));
        if(ObjectUtils.isNotEmpty(list) && list.size() > 0){
            Integer backTimes = list.get(0).getBackTimes();
            if(null != backTimes){
                log.warn("通过合同编号查询退回次数，其中返回结果={}",JSONObject.toJSONString(backTimes));
                return IResponse.success(backTimes);
            }else{
                log.warn("通过合同编号查询退回次数，22222222");
            }
        }

        log.warn("通过合同编号查询退回次数，其中返回结果固定=0");
        return IResponse.success(0);

    }
}
