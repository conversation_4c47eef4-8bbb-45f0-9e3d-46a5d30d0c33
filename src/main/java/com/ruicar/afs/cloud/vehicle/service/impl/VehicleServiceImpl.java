package com.ruicar.afs.cloud.vehicle.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.parameter.commom.enums.CarType;
import com.ruicar.afs.cloud.vehicle.enums.DelFlagEnum;
import com.ruicar.afs.cloud.vehicle.enums.IsShowEnum;
import com.ruicar.afs.cloud.vehicle.enums.ModelClassEnum;
import com.ruicar.afs.cloud.vehicle.enums.SeriesGroupEnum;
import com.ruicar.afs.cloud.vehicle.enums.VehicleSaleNetEnum;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruicar.afs.cloud.vehicle.vo.VehicleAddModelVO;
import com.ruicar.afs.cloud.vehicle.vo.VehicleBrandNewVO;
import com.ruicar.afs.cloud.vehicle.vo.VehicleCondition;
import com.ruicar.afs.cloud.vehicle.vo.VehicleInfoImportData;
import com.ruicar.afs.cloud.vehicle.vo.VehicleManufacturerNewVO;
import com.ruicar.afs.cloud.vehicle.vo.VehicleModelNewVO;
import com.ruicar.afs.cloud.vehicle.vo.VehicleSeriesNewVO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.PreIsDeleteEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.VehicleTypeEnum;
import com.ruicar.afs.cloud.vehicle.query.VehicleReportCondition;
import com.ruicar.afs.cloud.vehicle.query.VehicleTreeCondition;
import com.ruicar.afs.cloud.vehicle.entity.VehicleAddModel;
import com.ruicar.afs.cloud.vehicle.entity.VehicleBrand;
import com.ruicar.afs.cloud.vehicle.entity.VehicleManufacturer;
import com.ruicar.afs.cloud.vehicle.entity.VehicleModel;
import com.ruicar.afs.cloud.vehicle.entity.VehicleSeries;
import com.ruicar.afs.cloud.vehicle.enums.ImportTypeEnum;
import com.ruicar.afs.cloud.vehicle.enums.VehicleSourceEnum;
import com.ruicar.afs.cloud.vehicle.mapper.VehicleInfoMapper;
import com.ruicar.afs.cloud.vehicle.service.VehicleAddModelService;
import com.ruicar.afs.cloud.vehicle.service.VehicleBrandService;
import com.ruicar.afs.cloud.vehicle.service.VehicleCommonService;
import com.ruicar.afs.cloud.vehicle.service.VehicleManufacturerService;
import com.ruicar.afs.cloud.vehicle.service.VehicleModelService;
import com.ruicar.afs.cloud.vehicle.service.VehicleSeriesService;
import com.ruicar.afs.cloud.vehicle.service.VehicleService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: afs-backend
 * @description: 车型库--调用车三百接口实现类
 * @author: hqd
 * @date: 2021-07-01 14:39
 **/
@Service
@AllArgsConstructor
@Slf4j
public class VehicleServiceImpl extends ServiceImpl<VehicleInfoMapper, VehicleBrand> implements VehicleService {
    private final VehicleAddModelService vehicleAddModelService;
    private final VehicleBrandService vehicleBrandService;
    private final VehicleModelService vehicleModelService;
    private final VehicleManufacturerService vehicleManufacturerService;
    private final VehicleSeriesService vehicleSeriesService;
    private VehicleCommonService vehicleCommonService;
    private final String che300Str = "30000";


    /**
     * 前端查询品牌
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleBrandNewVO> queryBrand(VehicleCondition vehicleCondition) {
        List<VehicleBrandNewVO> vehicleBrandList = this.baseMapper.queryBrand(vehicleCondition);
        return vehicleBrandList;
    }

    /**
     * 前端查询制造商
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleManufacturerNewVO> queryManufacturer(VehicleCondition vehicleCondition) {
        List<VehicleManufacturerNewVO> vehicleManufacturerList = this.baseMapper.queryManufacturer(vehicleCondition);
        return vehicleManufacturerList;
    }

    /**
     * 前端查询车系
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleSeriesNewVO> querySeries(VehicleCondition vehicleCondition) {
        List<VehicleSeriesNewVO> vehicleSeriesList = this.baseMapper.querySeries(vehicleCondition);
        return vehicleSeriesList;
    }

    /**
     * 前端查询车型信息
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleModelNewVO> queryModel(VehicleCondition vehicleCondition) {
        log.info("车系查询-销售网络-入参 {}", vehicleCondition);
        List<VehicleModelNewVO> vehicleModelList = this.baseMapper.queryModel(vehicleCondition);
        log.info("车系结果输出 {}", JSON.toJSONString(vehicleModelList));
        return vehicleModelList;
    }

    /**
     * 前端接口--根据车型id-查询车型具体信息
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleModelNewVO> queryModelByModelId(VehicleCondition vehicleCondition) {
        log.info("get vehicleCondition,{}", JSON.toJSONString(vehicleCondition));
        List<VehicleModelNewVO> vehicleModelList = this.baseMapper.queryModelByModelId(vehicleCondition);
        log.info("get vehicleModelList,{}", JSON.toJSONString(vehicleModelList));
        return vehicleModelList;
    }

    /**
     * 前端接口--根据车型id-查询车型具体信息
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleModelNewVO> queryModelByModel(VehicleCondition vehicleCondition) {
        log.info("get vehicleCondition,{}", JSON.toJSONString(vehicleCondition));
        List<VehicleModelNewVO> vehicleModelList = this.baseMapper.queryModelByModel(vehicleCondition);
        log.info("get vehicleModelList,{}", JSON.toJSONString(vehicleModelList));
        return vehicleModelList;
    }

    /**
     * 后端接口--根据车型id-查询车型具体信息
     *
     * @param condition
     * @return
     */
    private VehicleReportCondition conditionConvert(VehicleReportCondition condition){
        log.info("车型库入参 {},{}", condition, JSON.toJSONString(condition));

        List<VehicleSeriesNewVO> series = condition.getSeries();
        List<VehicleSeriesNewVO> newVOList = new ArrayList<>();

        //在车系id不存在，车系名和品牌名都存在的情况，进行校验
        HashSet<String> hashSet = new HashSet<>();

        //车型名称不为空
        if(CollectionUtil.isNotEmpty(series)){
            if(StringUtils.isNotEmpty(series.get(0).getSeriesName())){
                List<VehicleSeries> list = vehicleSeriesService.list(Wrappers.<VehicleSeries>lambdaQuery()
                        .eq(VehicleSeries::getSeriesName, series.get(0).getSeriesName())
                        .eq(VehicleSeries::getDelFlag, PreIsDeleteEnum.NOT_DELETE));

                if(CollectionUtil.isEmpty(list)){
                    log.info("series name is invalid，{}",JSON.toJSONString(condition.getSeries()));
                    return condition;
                }

                newVOList = list.stream().map(t->{
                    VehicleSeriesNewVO vo = new VehicleSeriesNewVO();
                    //车系名对应车系id保存，用于后续品牌名对应车系id比对
                    hashSet.add(t.getSeriesId());
                    vo.setSeriesId(t.getSeriesId());
                    return vo;
                }).collect(Collectors.toList());
                //车系id为空，车系名称不为空，校验保留
                condition.setSeries(newVOList);
            } else {
                log.info("series name is null,{}", JSON.toJSONString(condition.getSeries()));
            }
        }

        //车型名称不为空
        String brandName = condition.getBrandName();
        if(StringUtils.isEmpty(brandName)){
            hashSet.clear();
            log.info("brandName is null,{}" ,JSON.toJSONString(condition));
            return condition;
        }

        //车品牌匹配
        List<VehicleBrand> vehicleBrandList = vehicleBrandService.list(Wrappers.<VehicleBrand>lambdaQuery()
                .eq(VehicleBrand::getBrandName, brandName)
                .eq(VehicleBrand::getDelFlag, PreIsDeleteEnum.NOT_DELETE));

        if (CollectionUtils.isEmpty(vehicleBrandList)) {
            log.info("vehicleBrand is null,{}", JSON.toJSONString(condition));
            condition.setPartnerId("0");
            return condition;
        }

        List<String> idList = new ArrayList<>();
        vehicleBrandList.forEach(t->{
            idList.add(t.getBrandId());
        });

        //车制造商通过品牌id匹配查询
        log.info("vehicleBrand is not null,{},{}" ,JSON.toJSONString(vehicleBrandList),JSON.toJSONString(idList));
        List<VehicleManufacturer> vehicleManufacturers = vehicleManufacturerService.list(Wrappers.<VehicleManufacturer>lambdaQuery()
                .in(VehicleManufacturer::getBrandId, idList)
                .eq(VehicleManufacturer::getDelFlag, PreIsDeleteEnum.NOT_DELETE));

        if(CollectionUtils.isEmpty(vehicleManufacturers)){
            log.info(" vehicleManufacturer is null,{}" ,JSON.toJSONString(idList));
            condition.setPartnerId("0");
            return condition;
        }

        idList.clear();
        vehicleManufacturers.forEach(t->{
            idList.add(t.getSeriesGroupId());
        });

        //车系匹配查询
        log.info("vehicleManufacturer is not null,{},{}", JSON.toJSONString(vehicleManufacturers), JSON.toJSONString(idList));
        List<VehicleSeries> vehicleSeries = vehicleSeriesService.list(Wrappers.<VehicleSeries>lambdaQuery()
                .in(VehicleSeries::getSeriesGroupId, idList)
                .eq(VehicleSeries::getDelFlag, PreIsDeleteEnum.NOT_DELETE));

        if (CollectionUtils.isEmpty(vehicleSeries)) {
            log.info(" vehicleSeries is null,{}", JSON.toJSONString(vehicleSeries));
            condition.setPartnerId("0");
            return condition;
        }

        //车系名称为空
        log.info("vehicleSeries is not null,{}", JSON.toJSONString(vehicleSeries));
        if (CollectionUtils.isEmpty(condition.getSeries())) {

            newVOList = vehicleSeries.stream().map(m->{
                VehicleSeriesNewVO vo = new VehicleSeriesNewVO();
                vo.setSeriesId(m.getSeriesId());
                return vo;
            }).collect(Collectors.toList());
            log.info("brand name is valid and series name is null ,{}", JSON.toJSONString(newVOList));
            condition.setSeries(newVOList);

        }else {

            List<VehicleSeriesNewVO> vehicleSeriesNewVOList = new ArrayList<>();
            //车系名称对应车系id不为空，以车系名称对应的车型id为基准
            vehicleSeries.forEach(m->{
                if (CollectionUtils.isNotEmpty(hashSet)) {
                    if (hashSet.contains(m.getSeriesId())) {
                        VehicleSeriesNewVO vo = new VehicleSeriesNewVO();
                        vo.setSeriesId(m.getSeriesId());
                        vehicleSeriesNewVOList.add(vo);
                    }
                }else {
                    VehicleSeriesNewVO vo = new VehicleSeriesNewVO();
                    vo.setSeriesId(m.getSeriesId());
                    vehicleSeriesNewVOList.add(vo);
                }
            });

            log.info("get vehicleSeriesNewVOs ,{}", JSON.toJSONString(vehicleSeriesNewVOList));
            //标记车系名称对应的车系id与品牌名称对应车系id不匹配
            if(vehicleSeriesNewVOList.size() <= 0){
                log.info("series name and brand name match error,{},{}",JSON.toJSONString(condition),JSON.toJSONString(hashSet));
                condition.setPartnerId("0");
            }

            //以车系名称对应的车型id为基准，满足则有效
            condition.setSeries(vehicleSeriesNewVOList);

            hashSet.clear();
        }

        log.info("get final condition,{}",JSON.toJSONString(condition));
        return condition;
    }

    /**
     * 前端接口--根据车型condition-查询车型具体信息
     *
     * @param condition
     * @return
     */
    @Override
    public Page<VehicleModelNewVO> queryModelByCondition(VehicleReportCondition condition) {

        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        condition = conditionConvert(condition);
//        if(StringUtils.isNotEmpty(condition.getPartnerId())){
//            return new Page<VehicleModelNewVO>();
//        }

        List<VehicleSeriesNewVO> series = condition.getSeries();
        if(CollectionUtils.isNotEmpty(series) || StringUtils.isNotEmpty(condition.getBrandName())){
            VehicleSeriesNewVO vehicleSeriesNewVO = series.get(0);
            if(StringUtils.isNotEmpty(vehicleSeriesNewVO.getSeriesId())){

                List<String> list = new ArrayList<>();
                series.forEach(t->{
                    list.add(t.getSeriesId());
                });

                log.info("车型库模糊匹配入参 {},{},{}", condition, JSON.toJSONString(page),JSON.toJSONString(list));
                IPage<VehicleModel> iPage = vehicleModelService.page(page, Wrappers.<VehicleModel>query().lambda()
                        .like(StringUtils.isNotBlank(condition.getModelId()),VehicleModel::getModelId,condition.getModelId())
                        .like(StringUtils.isNotBlank(condition.getModelName()),VehicleModel::getModelName,condition.getModelName())
                        .in(CollectionUtils.isNotEmpty(list),VehicleModel::getSeriesId, list)
                        .orderByDesc(VehicleModel::getUpdateTime)
                );

                log.info("车型库模糊匹配出参 {}", JSON.toJSONString(iPage));
                return iPageConvert(iPage);
            }
        }
        log.info("车型库模糊匹配入参 {},{}", condition, JSON.toJSONString(page));
        IPage<VehicleModel> listPage = vehicleModelService.page(page, Wrappers.<VehicleModel>query().lambda()
                .like(StringUtils.isNotBlank(condition.getModelId()),VehicleModel::getModelId,condition.getModelId())
                .like(StringUtils.isNotBlank(condition.getModelName()),VehicleModel::getModelName,condition.getModelName())
                .orderByDesc(VehicleModel::getUpdateTime)
        );

        log.info("车型库模糊匹配出参 {}", JSON.toJSONString(listPage));
        return iPageConvert(listPage);
    }

    /**
     * 前端接口--根据车型condition-查询车型具体信息
     *
     * @param condition
     * @return
     */
    @Override
    public Page<VehicleModelNewVO> caseQueryModelByCondition(VehicleReportCondition condition) {

        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        condition = conditionConvert(condition);
        List<VehicleSeriesNewVO> series = condition.getSeries();
        if(StrUtil.isNotEmpty(series.get(0).getSeriesName())||StrUtil.isNotEmpty(series.get(0).getSeriesId())|| StringUtils.isNotEmpty(condition.getBrandName())){
            VehicleSeriesNewVO vehicleSeriesNewVO = series.get(0);
            if(StringUtils.isNotEmpty(vehicleSeriesNewVO.getSeriesId())){

                List<String> list = new ArrayList<>();
                series.forEach(t->{
                    list.add(t.getSeriesId());
                });

                log.info("车型库匹配入参 {},{},{}", condition, JSON.toJSONString(page),JSON.toJSONString(list));
                IPage<VehicleModel> iPage = vehicleModelService.page(page, Wrappers.<VehicleModel>query().lambda()
                        .eq(StringUtils.isNotBlank(condition.getModelId()),VehicleModel::getModelId,condition.getModelId())
                        .eq(StringUtils.isNotBlank(condition.getModelName()),VehicleModel::getModelName,condition.getModelName())
                        .in(CollectionUtils.isNotEmpty(list),VehicleModel::getSeriesId, list)
                        .orderByDesc(VehicleModel::getUpdateTime)
                );

                log.info("车型库匹配出参 {}", JSON.toJSONString(iPage));
                return iPageConvert(iPage);
            }else {
                return new Page<>();
            }
        }

        log.info("车型库匹配入参 {},{}", condition, JSON.toJSONString(page));
        IPage<VehicleModel> listPage = vehicleModelService.page(page, Wrappers.<VehicleModel>query().lambda()
                .eq(StringUtils.isNotBlank(condition.getModelId()),VehicleModel::getModelId,condition.getModelId())
                .eq(StringUtils.isNotBlank(condition.getModelName()),VehicleModel::getModelName,condition.getModelName())
                .orderByDesc(VehicleModel::getUpdateTime)
        );

        log.info("车型库匹配出参 {}", JSON.toJSONString(listPage));
        return iPageConvert(listPage);
    }

    /**
     * 后端接口--IPage转换
     *
     * @param listPage
     * @return
     */
    private Page<VehicleModelNewVO> iPageConvert(IPage<VehicleModel> listPage){

        listPage.getRecords().forEach(t->{
            VehicleModelNewVO vo = new VehicleModelNewVO();
            BeanUtil.copyProperties(t, vo);
        });

        List<VehicleModelNewVO> newVOList = new ArrayList<>();
        List<VehicleModel> records = listPage.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            records.forEach(t -> {
                VehicleModelNewVO vo = new VehicleModelNewVO();
                BeanUtil.copyProperties(t, vo);
                if (StringUtils.isNotEmpty(vo.getSeriesId())) {
                    VehicleSeries series = vehicleSeriesService.lambdaQuery().eq(StrUtil.isNotBlank(vo.getSeriesId()), VehicleSeries::getSeriesId, vo.getSeriesId()).one();
//                    VehicleModelNewVO brandNameAndSeriesName = getBrandNameAndSeriesName(vo);
                    if(ObjectUtils.isNotEmpty(series)){
                        vo.setSeriesName(series.getSeriesName());
                        vo.setBrandName(series.getSeriesGroupName());
                    }

                    newVOList.add(vo);

                }
            });
        }

        Page<VehicleModelNewVO> page = new Page<>();
        page.setTotal(listPage.getTotal());
        page.setRecords(newVOList);
        page.setPages(listPage.getPages());
        page.setSize(listPage.getSize());
        page.setCurrent(listPage.getCurrent());

        log.info("车型库出参转换 {}", JSON.toJSONString(page));
        return page;
    }

    /**
     * 后端接口--根据车型condition-查询车型名和品牌名
     *
     * @param
     * @return
     */
    private VehicleModelNewVO getBrandNameAndSeriesName(VehicleModelNewVO vo){
        VehicleCondition condition = new VehicleCondition();
        condition.setSeriesId(vo.getSeriesId());

        log.info("get seriesId,{}", vo.getSeriesId());
        List<VehicleSeries> vehicleSeries = vehicleSeriesService.list(Wrappers.<VehicleSeries>lambdaQuery()
                .eq(VehicleSeries::getSeriesId,condition.getSeriesId()));
        if(CollectionUtils.isNotEmpty(vehicleSeries)){
            //获取车系名
            log.info("get seriesName,{}", vehicleSeries.get(0).getSeriesName());
            vo.setSeriesName(vehicleSeries.get(0).getSeriesName());

            log.info("get seriesGroupId,{}", vehicleSeries.get(0).getSeriesGroupId());
            condition.setSeriesGroupId(vehicleSeries.get(0).getSeriesGroupId());
        }

        List<VehicleManufacturer> vehicleManufacturers = vehicleManufacturerService.list(Wrappers.<VehicleManufacturer>lambdaQuery()
                .eq(VehicleManufacturer::getSeriesGroupId,condition.getSeriesGroupId()));
        if(CollectionUtils.isNotEmpty(vehicleManufacturers)){

            log.info("get brandId,{}", vehicleManufacturers.get(0).getBrandId());
            condition.setBrandId(vehicleManufacturers.get(0).getBrandId());
        }

        List<VehicleBrand> vehicleBrands = vehicleBrandService.list(Wrappers.<VehicleBrand>lambdaQuery()
                .eq(VehicleBrand::getBrandId, condition.getBrandId()));
        if(CollectionUtils.isNotEmpty(vehicleBrands)){
            //获取品牌名
            log.info("get brandName,{}", vehicleBrands.get(0).getBrandName());
            vo.setBrandName(vehicleBrands.get(0).getBrandName());
        }

        log.info("获取车型名和品牌名,{}", JSON.toJSONString(vo));
        return vo;
    }

    /**
     * 前端接口--根据车型信息-查询车价格
     *
     * @param
     * @return
     */
    @Override
    public VehicleModelNewVO queryNewCarPrice(String modelId, String modelName,String carType, String zone) {

        if(StringUtils.isBlank(modelId) && StringUtils.isBlank(modelName)){
            log.error("车型id与车型名称必传其一，{},{}", modelId, modelName);
            return null;
        }

        if(VehicleTypeEnum.PASSENGER_CAR.getCode().equals(carType)){
            log.info("get PASSENGER_CAR modelId,carType,{},{}",modelId,carType);
           return getPassengerNewCarPrice(modelId,modelName,zone);
        }else if(VehicleTypeEnum.COMMERCIAL_CAR.getCode().equals(carType)){
            log.info("get COMMERCIAL_CAR modelId,carType,{},{}",modelId,carType);
            return getCommercialNewCarPrice(modelId,modelName,zone);
        }

        return new VehicleModelNewVO();
    }

    /**
     * 后端接口--根据车型信息-查询商用车车价格
     *
     * @param
     * @return
     */
    private VehicleModelNewVO getCommercialNewCarPrice(String modelId,String modelName, String zone){
        VehicleModelNewVO vo = new VehicleModelNewVO();
        return vo;
    }

    /**
     * 后端接口--根据车型信息-查询乘用车车价格
     *
     * @param
     * @return
     */
    private VehicleModelNewVO getPassengerNewCarPrice(String modelId,String modelName, String zone){
        VehicleModelNewVO vo = new VehicleModelNewVO();
        return vo;
    }

    /**
     * ("前端接口--根据品牌名称、车型名称-模糊查询")
     *
     * @param vehicleModelNewVO
     * @return
     */
    @Override
    public List<VehicleModelNewVO> queryVehicleInfo(VehicleModelNewVO vehicleModelNewVO) {
        List<VehicleModelNewVO> vehicleModelList = this.baseMapper.queryVehicleInfo(vehicleModelNewVO);
        return vehicleModelList;
    }

    @Override
    public IResponse importVehicleModel(List<VehicleModelNewVO> vehicleModelNewVOList) {

        List<VehicleModel> list = new ArrayList<>();
        vehicleModelNewVOList.forEach(t->{

            List<VehicleModel> vehicleModelsList= vehicleModelService.list(Wrappers.<VehicleModel>query().lambda()
                    .eq(VehicleModel::getModelId ,t.getModelId()));
            if(CollectionUtils.isNotEmpty(vehicleModelsList)){
                VehicleModel vo = vehicleModelsList.get(0);
                VehicleModel vehicleModel = new VehicleModel();
                BeanUtil.copyProperties(vo ,vehicleModel);

                vehicleModel.setModelId(t.getModelId());
                vehicleModel.setOwnPrice(t.getOwnPrice());
                vehicleModel.setVehicleLevel(t.getVehicleLevel());
                vehicleModel.setIsUnpopular(t.getIsUnpopular());
                vehicleModel.setOwnParentType(t.getOwnParentType());
                vehicleModel.setOwnSubTypeDesc(t.getOwnSubTypeDesc());
                vehicleModel.setOwnSubType(t.getOwnSubType());
                vehicleModel.setOwnParentTypeDesc(t.getOwnParentTypeDesc());
                vehicleModel.setUpdateBy(t.getUpdateBy());
                vehicleModel.setUpdateTime(new Date());
                vehicleModel.setImportFlag(ImportTypeEnum.ADD_BY_OPERATOR.getCode());
                list.add(vehicleModel);
            }

        });

        log.info("save batch vehicleModel, {}", JSON.toJSONString(list));
        boolean b = vehicleModelService.saveOrUpdateBatch(list);
        if(b){
            return new IResponse().setMsg("success").setCode("0000");
        }

        return new IResponse().setMsg("fail");
    }

    @Override
    public List<VehicleModelNewVO> queryModelByList(List<VehicleModelNewVO> vehicleModelNewVOList) {
        List<VehicleModel> list = vehicleModelNewVOList.stream().map(t->{
            VehicleModel vehicleModel = new VehicleModel();
            vehicleModel.setModelId(t.getModelId());
            return vehicleModel;
        }).collect(Collectors.toList());

        log.info("select vehicle model by modelIds,{}", JSON.toJSONString(list));

        List<VehicleModelNewVO> newVOList = new ArrayList<>();
        list.forEach(t->{
            List<VehicleModel> vehicleModelsList= vehicleModelService.list(Wrappers.<VehicleModel>query().lambda()
                    .eq(VehicleModel::getModelId ,t.getModelId()));
            if(CollectionUtils.isNotEmpty(vehicleModelsList)){
                VehicleModelNewVO vo = new VehicleModelNewVO();
                BeanUtil.copyProperties(vehicleModelsList.get(0), vo);

                VehicleModelNewVO brandNameAndSeriesName = getBrandNameAndSeriesName(vo);
                if(ObjectUtils.isNotEmpty(brandNameAndSeriesName)){
                    vo.setSeriesName(brandNameAndSeriesName.getSeriesName());
                    vo.setBrandName(brandNameAndSeriesName.getBrandName());
                }
                newVOList.add(vo);
            }
        });

        log.info("get vehicle model by modelIds,{}", JSON.toJSONString(newVOList));

        return newVOList;
    }

    /**
     * 前端接口--根据品牌名称--模糊查询品牌列表
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleBrandNewVO> fuzzyQueryBrand(VehicleCondition vehicleCondition) {
        List<VehicleBrandNewVO> vehicleModelList = this.baseMapper.fuzzyQueryBrand(vehicleCondition);
        return vehicleModelList;
    }

    /**
     * 前端接口--根据车型名称 模糊查询车型信息
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleModelNewVO> fuzzyQueryModel(VehicleCondition vehicleCondition) {
        List<VehicleModelNewVO> vehicleModelList = this.baseMapper.fuzzyQueryModel(vehicleCondition);
        return vehicleModelList;
    }

    /**
     * 车300-添加车型
     *
     * @param vehicleModelNewVO
     * @return
     */
    @Override
    public IResponse submitAddNewCarModel(VehicleModelNewVO vehicleModelNewVO) {
        return IResponse.fail("接口已废弃");
    }


    /**
     * 车型库添加通知接口
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public IResponse receiveDealVehicleResult(VehicleCondition vehicleCondition) {
        String handleResultCode = vehicleCondition.getHandle_result_code();
        String orderNo = vehicleCondition.getOrder_no();
        VehicleAddModel vehicleAddModel = vehicleAddModelService.getOne(Wrappers.<VehicleAddModel>lambdaQuery().eq(VehicleAddModel::getOrderNo, orderNo));
        log.info("新增车型原数据：【" + vehicleAddModel + "】");
        if (vehicleAddModel != null) {
            vehicleAddModel.setHandleResultCode(handleResultCode);
            if (vehicleCondition.getNew_model_id() != null) {
                log.info("-----订单号【" + orderNo + "】车型Id:", vehicleCondition.getNew_model_id());
                vehicleAddModel.setNewModelId(vehicleCondition.getNew_model_id());
            }
            vehicleAddModel.setHandleResultMsg(vehicleCondition.getHandle_result_msg());
            vehicleAddModel.setVihicleType(Integer.parseInt(vehicleCondition.getVehicleType()));
            //1：新增成功
            if ("1".equals(handleResultCode)) {
                log.info("-----订单号【" + orderNo + "】新增车型成功之后会根据车型id识别品牌、制造商、车系等相关字段");
                vehicleAddModelService.updateById(vehicleAddModel);
                return IResponse.success("成功");
            }
            //2：已经存在无需添加；3：废除此项请求
            else {
                vehicleAddModelService.updateById(vehicleAddModel);
                log.info("-----订单号【" + orderNo + "】新增失败【" + handleResultCode + "】2-已存在无需添加、3-废除请求");
                return IResponse.success("成功");
            }
        } else {
            log.info("-----未查询到改订单编号【" + orderNo + "】");
            return IResponse.fail("未查询到改订单编号【" + orderNo + "】");
        }
    }

    /**
     * 车300推送车型库变更信息给中远
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public IResponse synchronizedMissingVehicleData(VehicleCondition vehicleCondition) {
        log.info("车三百返回数据:{}", JSONObject.toJSONString(vehicleCondition));
        if (vehicleCondition == null || StringUtils.isEmpty(vehicleCondition.getOrder_no())) {
            return IResponse.fail("入参不正确，订单编号为空！");
        }
        String orderNo = vehicleCondition.getOrder_no();
        List<VehicleAddModel> list = vehicleAddModelService.list(Wrappers.<VehicleAddModel>lambdaQuery().eq(VehicleAddModel::getOrderNo, orderNo));
        if (CollectionUtil.isNotEmpty(list)) {
            VehicleAddModel vehicleAddModel = list.get(0);

            //创建人
            final String createBy = vehicleAddModel.getCreateBy();

            //制造商
            String vihicleType = String.valueOf(vehicleAddModel.getVihicleType());

            //品牌
            if (CollectionUtil.isNotEmpty(vehicleCondition.getBrand())) {
                VehicleBrandNewVO brand = vehicleCondition.getBrand().get(0);
                //查询是否重复
                VehicleBrand vehicleBrandData = vehicleBrandService.getOne(
                        Wrappers.<VehicleBrand>lambdaQuery()
                                .eq(VehicleBrand::getBrandId, che300Str + brand.getBrandId())
                                        .eq(VehicleBrand::getVehicleType, vihicleType)
                                                .eq(VehicleBrand::getSource, VehicleSourceEnum.CHE300.getCode())
                );
                if (vehicleBrandData == null) {
                    log.info("----------开始保存该订单编号【" + orderNo + "】的品牌信息---------start");
                    VehicleBrand vehicleBrand = new VehicleBrand();
                    vehicleBrand.setBrandId(che300Str + brand.getBrandId());
                    vehicleBrand.setBrandName(brand.getBrandName());
                    vehicleBrand.setBrandInitial(brand.getBrandInitial());
                    vehicleBrand.setSource(3);
                    vehicleBrand.setVehicleType(Integer.parseInt(vihicleType));
                    vehicleBrand.setCreateBy(createBy);
                    vehicleBrandService.save(vehicleBrand);
                    log.info("----------结束保存该订单编号【" + orderNo + "】的品牌信息--------end");
                }
            }
            //车系
            if (CollectionUtil.isNotEmpty(vehicleCondition.getSeries())) {
                String seriesGroupId = null;
                String seriesGroupName = null;
                VehicleSeriesNewVO series = vehicleCondition.getSeries().get(0);
                //查询是否重复
                VehicleSeries vehicleServiceData = vehicleSeriesService.getOne(
                        Wrappers.<VehicleSeries>lambdaQuery()
                                .eq(VehicleSeries::getSeriesId, che300Str + series.getSeriesId())
                                .eq(VehicleSeries::getVehicleType, vihicleType)
                                .eq(VehicleSeries::getSource, VehicleSourceEnum.CHE300.getCode())
                );
                if (vehicleServiceData == null) {
                    log.info("----------开始保存该订单编号【" + orderNo + "】的车系信息--------start");
                    VehicleSeries vehicleSeries = new VehicleSeries();
                    vehicleSeries.setSeriesId(che300Str + series.getSeriesId());
                    vehicleSeries.setSeriesName(series.getSeriesName());
                    //乘用车
                    if (VehicleTypeEnum.PASSENGER_CAR.getCode().equals(vihicleType)) {
                        seriesGroupId = series.getSeriesGroupId();
                        vehicleSeries.setSeriesGroupId(seriesGroupId);
                        seriesGroupName = series.getSeriesGroupName();
                    }
                    //商用车
                    if (VehicleTypeEnum.COMMERCIAL_CAR.getCode().equals(vihicleType)) {
                        //3 + 30000 + brandid
                        seriesGroupId = "3" + che300Str + series.getBrandId();
                        vehicleSeries.setSeriesGroupId(seriesGroupId);
                        seriesGroupName = series.getBrandName();
                    }
                    vehicleSeries.setSource(3);
                    vehicleSeries.setVehicleType(Integer.parseInt(vihicleType));
                    vehicleSeries.setCreateBy(createBy);
                    vehicleSeriesService.save(vehicleSeries);
                    log.info("----------开始保存该订单编号【" + orderNo + "】的车系信息--------end");
                }
                //保存制造商信息
                if (StringUtils.isNotBlank(seriesGroupId)) {
                    VehicleManufacturer vehicleManufacturerData = vehicleManufacturerService.getOne(
                            Wrappers.<VehicleManufacturer>lambdaQuery()
                                    .eq(VehicleManufacturer::getSeriesGroupId, seriesGroupId)
                                    .eq(VehicleManufacturer::getVehicleType, vihicleType)
                                    .eq(VehicleManufacturer::getSource, VehicleSourceEnum.CHE300.getCode())
                    );
                    if (ObjectUtils.isEmpty(vehicleManufacturerData)) {
                        log.info("----------开始保存订单编号【" + orderNo + "】的制造商信息---------start");
                        VehicleManufacturer vehicleManufacturer = new VehicleManufacturer();
                        vehicleManufacturer.setBrandId(che300Str + series.getBrandId());
                        vehicleManufacturer.setSeriesGroupId(seriesGroupId);
                        vehicleManufacturer.setSeriesGroupName(seriesGroupName);
                        vehicleManufacturer.setSource(3);
                        vehicleManufacturer.setVehicleType(Integer.parseInt(vihicleType));
                        vehicleManufacturer.setCreateBy(createBy);
                        vehicleManufacturerService.save(vehicleManufacturer);
                        log.info("----------结束保存订单编号【" + orderNo + "】的制造商信息--------end");
                    }
                }
            }
            //车型
            if (CollectionUtil.isNotEmpty(vehicleCondition.getModel())) {
                VehicleModelNewVO model = vehicleCondition.getModel().get(0);
                String modelId = model.getModelId();
                //查询是否重复
                VehicleModel vehicleModelData = vehicleModelService.getOne(
                        Wrappers.<VehicleModel>lambdaQuery()
                                .eq(VehicleModel::getModelId, che300Str + modelId)
                                .eq(VehicleModel::getVehicleType, vihicleType)
                                .eq(VehicleModel::getSource, VehicleSourceEnum.CHE300.getCode())
                );
                //无此车型数据需插入，有则更新最新的车型数据
                VehicleModel vehicleModel = new VehicleModel();
                vehicleModel.setSeriesId(che300Str + model.getSeriesId());
                vehicleModel.setModelId(che300Str + modelId);
                vehicleModel.setModelName(model.getModelName());
                if(ObjectUtils.isNotEmpty(model.getPrice())){
                    vehicleModel.setPrice(model.getPrice());
                }
                vehicleModel.setLiter(model.getLiter());
                vehicleModel.setGearType(model.getGearType());
                vehicleModel.setModelYear(model.getModelYear());
                vehicleModel.setDischargeStandard(model.getDischargeStandard());
                vehicleModel.setStopSale(model.getStopSale());
                if (ObjectUtils.isNotEmpty(model.getEnginePower())) {
                    vehicleModel.setEnginePower(model.getEnginePower());
                    vehicleModel.setMaxEnginePower(String.valueOf(model.getEnginePower()));
                }
                if (ObjectUtils.isNotEmpty(model.getMaxPowerKw())) {
                    vehicleModel.setMaxPowerKw(model.getMaxPowerKw());
                }
                if (ObjectUtils.isNotEmpty(model.getCargoLength())) {
                    vehicleModel.setCargoLength(model.getCargoLength());
                } else {
                    vehicleModel.setCargoLength(BigDecimal.ZERO);
                }
                vehicleModel.setVehicleType(Integer.parseInt(vihicleType));
                //是否为商用车
                if (VehicleTypeEnum.COMMERCIAL_CAR.getCode().equals(vihicleType)) {
                    //获取商用车车型信息
                    vehicleCommonService.getCommercialModelInfo(vehicleModel, model.getSeriesId(), modelId, model.getModelName(), model.getLevel());
                } else if (VehicleTypeEnum.PASSENGER_CAR.getCode().equals(vihicleType)) {
                    //获取乘用车车型信息
                    vehicleCommonService.getPassengerModelInfo(vehicleModel, model.getSeriesId(), modelId, model.getModelName());
                } else {
                    log.error("车辆类型解析错误！");
                    return IResponse.fail("车辆类型解析错误！");
                }
                if (ObjectUtils.isEmpty(vehicleModel)) {
                    log.error("设置车型信息异常！modelId:{}", modelId);
                    throw new RuntimeException("设置车型信息异常！");
                }
                if (vehicleModelData == null) {
                    log.info("----------开始保存该订单编号【" + orderNo + "】的车型信息--------start");
                    vehicleModel.setSource(Integer.parseInt(VehicleSourceEnum.CHE300.getCode()));
                    vehicleModel.setCreateBy(createBy);
                    vehicleAddModel.setCreateTime(new Date());
                    vehicleModelService.save(vehicleModel);
                    log.info("----------开始保存该订单编号【" + orderNo + "】的车型信息--------end");
                } else {
                    log.info("----------开始更新该订单编号【" + orderNo + "】的车型信息--------start");
                    vehicleModel.setId(vehicleModelData.getId());
                    vehicleModelService.updateById(vehicleModel);
                    log.info("----------结束更新该订单编号【" + orderNo + "】的车型信息--------end");
                }
            }
            return IResponse.success("成功");
        } else {
            return IResponse.fail("未查询到改订单编号【" + orderNo + "】");
        }
    }

    /**
     * 前端接口----车三百新增车型状态展示信息
     *
     * @param
     * @return
     */
    @Override
    public IResponse queryModelStatus(QueryCondition<VehicleReportCondition> condition) {
        VehicleReportCondition reportCondition = condition.getCondition();
        Page page = new Page(condition.getPageNumber(), condition.getPageSize());
        IPage<VehicleAddModelVO> queryVehicleAddModelList = this.baseMapper.queryVehicleAddModelList(reportCondition, page);
        return IResponse.success(queryVehicleAddModelList);
    }

    /**
     * 根据vin码查询车型列表
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public IResponse identifyModelByVin(VehicleCondition vehicleCondition) {
        return IResponse.fail("接口已废弃");
    }

    /**
     * 渠道端查询车型tree
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleTreeCondition> queryVehicleBrandTree(VehicleCondition vehicleCondition) {
        List<VehicleTreeCondition> vehicleManufacturerTree = this.baseMapper.queryVehicleBrandTree(vehicleCondition);

        return vehicleManufacturerTree;
    }

    /**
     * 渠道端查询车型tree
     *
     * @param vehicleCondition
     * @return
     */
    @Override
    public List<VehicleTreeCondition> queryVehicleManufacturerTree(VehicleCondition vehicleCondition) {
        List<VehicleTreeCondition> vehicleManufacturerTree = this.baseMapper.queryVehicleManufacturerTree(vehicleCondition);

        return vehicleManufacturerTree;
    }

    @Override
    public IResponse vehicleImportInfo(List<VehicleInfoImportData> vehicleImportDataList) {
        List<VehicleSeries> vehicleSeriesList = new ArrayList<>();
        List<VehicleModel> vehicleModelList = new ArrayList<>();
        int modelId = Integer.parseInt(vehicleModelService.queryMaxModelId());
        int seriesId = Integer.parseInt(vehicleSeriesService.queryMaxSeriesId());
        for (VehicleInfoImportData vehicleImportData : vehicleImportDataList) {
            VehicleModel vehicleModel = new VehicleModel();
            VehicleSeries vSeries = vehicleSeriesService.getOne(Wrappers.<VehicleSeries>lambdaQuery()
                    .eq(VehicleSeries::getSeriesName, vehicleImportData.getSeriesName())
                    .eq(VehicleSeries::getDelFlag, PreIsDeleteEnum.NOT_DELETE));
            VehicleSeries v = vehicleSeriesList.stream().filter(vehicleSeries->Objects.equals(vehicleSeries.getSeriesName(), vehicleImportData.getSeriesName())).findFirst().orElse(null);
            if (!Objects.isNull(vSeries)) {
                vehicleModel.setSeriesId(vSeries.getSeriesId());
            } else if(v!=null){
                vehicleModel.setSeriesId(v.getSeriesId());
            }else {
                seriesId++;
                vehicleModel.setSeriesId(String.valueOf(seriesId));
            }
            modelId++;
            vehicleModel.setModelId(String.valueOf(modelId));
            vehicleModel.setModelName(vehicleImportData.getModelName());
            vehicleModel.setPrice(vehicleImportData.getPrice().divide(BigDecimal.valueOf(10000)));
            vehicleModel.setSaleNet(VehicleSaleNetEnum.DYNASTY);
            vehicleModel.setVehicleModelCode(vehicleImportData.getVehicleModelCode());
            vehicleModel.setCrmVerificationGroup(vehicleImportData.getCrmVerificationGroup());
            if(IsShowEnum.NOT_SHOW.getDesc().equals(vehicleImportData.getIsShow())) {
                vehicleModel.setIsShow(IsShowEnum.NOT_SHOW.getCode());
            }else if(IsShowEnum.IS_SHOW.getDesc().equals(vehicleImportData.getIsShow())){
                vehicleModel.setIsShow(IsShowEnum.IS_SHOW.getCode());
            }
            vehicleModel.setVehicleType(Integer.valueOf(VehicleTypeEnum.PASSENGER_CAR.getCode()));
            vehicleModel.setOwnParentType(Integer.valueOf(CarType.PASSENGER_VEHICLE.getIndex()));
            if (ModelClassEnum.BEV.getDesc().equals(vehicleImportData.getModelClass())){
                vehicleModel.setModelClass(ModelClassEnum.BEV.getCode());
            }else if(ModelClassEnum.ICE.getDesc().equals(vehicleImportData.getModelClass())){
                vehicleModel.setModelClass(ModelClassEnum.ICE.getCode());
            }else if(ModelClassEnum.HEV.getDesc().equals(vehicleImportData.getModelClass())){
                    vehicleModel.setModelClass(ModelClassEnum.HEV.getCode());
            }

            vehicleModel.setSource(Integer.valueOf(VehicleSourceEnum.CHE300.getCode()));
            vehicleModelList.add(vehicleModel);


            VehicleSeries vehicleSeries = new VehicleSeries();
            vehicleSeries.setSeriesId(vehicleModel.getSeriesId());
            if(AfsEnumUtil.desc(SeriesGroupEnum.BYD).equals(vehicleImportData.getSeriesGroupName())) {
                vehicleSeries.setSeriesGroupId(AfsEnumUtil.key(SeriesGroupEnum.BYD));
            }else if(AfsEnumUtil.desc(SeriesGroupEnum.TS).equals(vehicleImportData.getSeriesGroupName())){
                vehicleSeries.setSeriesGroupId(AfsEnumUtil.key(SeriesGroupEnum.TS));
            }
            vehicleSeries.setSeriesName(vehicleImportData.getSeriesName());
            vehicleSeries.setSeriesId(vehicleModel.getSeriesId());
            vehicleSeries.setSeriesGroupName(vehicleImportData.getSeriesGroupName());
            vehicleSeries.setDelFlag(AfsEnumUtil.key(DelFlagEnum.NORMAL));
            vehicleSeries.setVehicleType(Integer.valueOf(VehicleTypeEnum.PASSENGER_CAR.getCode()));
            vehicleSeries.setSource(Integer.valueOf(VehicleSourceEnum.CHE300.getCode()));
            if (Objects.isNull(vSeries)&&v==null) {
                vehicleSeriesList.add(vehicleSeries);
            }
        }
        vehicleModelService.saveBatch(vehicleModelList);
        log.info("车型库--车型表导入完成！", vehicleModelList);
        vehicleSeriesList = vehicleSeriesList.stream().distinct().collect(Collectors.toList());
        vehicleSeriesService.saveBatch(vehicleSeriesList);
        log.info("车型库--车系表导入完成！", vehicleSeriesList);
        return IResponse.success("车型库导入完成！");
    }

    /**
     * 根据筛选条件进行查询用于全量导出
     * @param vo
     * @return
     */
    @Override
    public List<VehicleModelNewVO> queryVehicle(VehicleModelNewVO vo) {
        //根据车型ID、车系名称、车型名称、品牌名称-模糊查询车型具体信息 返回list
        List<VehicleModelNewVO> list = this.baseMapper.queryVehicleByCondition(vo);
        return list;
    }

    /**
     * 根据车型编码查询车型信息
     * @param vehicleModelCodeList 车型编码集合
     * @return 查询车型信息
     */
    @Override
    public List<VehicleModelNewVO> queryModelByVehicleModelCode(List<String> vehicleModelCodeList) {
        List<VehicleModelNewVO> list = new ArrayList<>();
        for (String vehicleModelCode : vehicleModelCodeList) {
            /**
             * -- series_id、model_name、model_id、price
             * SELECT a.series_id,a.model_name,a.model_id,a.price FROM leasing_vehicle.vehicle_model a WHERE a.del_flag='0' AND a.vehicle_model_code='入参'
             * -- series_group_id、series_id
             * SELECT a.series_group_id FROM leasing_vehicle.vehicle_series a WHERE a.del_flag='0' AND a.series_id='series_id'
             * -- brand_initial（curLetter）、brand_id
             * SELECT a.brand_initial,a.brand_id FROM leasing_vehicle.vehicle_brand a WHERE a.brand_id='series_group_id'
             */
            VehicleModelNewVO v = new VehicleModelNewVO();
            VehicleModel vehicleModel = vehicleModelService.getOne(Wrappers.<VehicleModel>lambdaQuery()
                    .eq(VehicleModel::getVehicleModelCode, vehicleModelCode));
            if (ObjectUtil.isNull(vehicleModel)) {
                continue;
            }
            v.setSeriesId(vehicleModel.getSeriesId());
            v.setModelName(vehicleModel.getModelName());
            v.setModelId(vehicleModel.getModelId());
            v.setPrice(vehicleModel.getPrice());
            v.setSaleNet(vehicleModel.getSaleNet());
            v.setIsGreen(vehicleModel.getIsGreen());
            v.setVehicleModelCode(vehicleModel.getVehicleModelCode());
            v.setModelClass(vehicleModel.getModelClass());
            v.setCrmVerificationGroup(vehicleModel.getCrmVerificationGroup());

            VehicleSeries vehicleSeries = vehicleSeriesService.getOne(Wrappers.<VehicleSeries>lambdaQuery()
                        .eq(VehicleSeries::getSeriesId, vehicleModel.getSeriesId()));
            v.setSeriesGroupId(vehicleSeries.getSeriesGroupId());
            v.setSeriesName(vehicleSeries.getSeriesName());
            v.setSeriesGroupName(vehicleSeries.getSeriesGroupName());
            VehicleBrand vehicleBrand = vehicleBrandService.getOne(Wrappers.<VehicleBrand>lambdaQuery()
                            .eq(VehicleBrand::getBrandId, vehicleSeries.getSeriesGroupId()));
            v.setBrandInitial(vehicleBrand.getBrandInitial());
            v.setBrandId(vehicleBrand.getBrandId());
            v.setBrandName(vehicleBrand.getBrandName());
            list.add(v);
        }
        return list;
    }
}
