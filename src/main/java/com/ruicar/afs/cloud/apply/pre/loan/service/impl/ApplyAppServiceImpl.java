package com.ruicar.afs.cloud.apply.pre.loan.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.apply.business.service.ApplyAdminService;
import com.ruicar.afs.cloud.apply.business.service.ApplySignHistoryService;
import com.ruicar.afs.cloud.apply.business.service.ApplySignRelationService;
import com.ruicar.afs.cloud.apply.business.service.TsysParamConfigService;
import com.ruicar.afs.cloud.apply.common.entity.ApplyAddPriceItems;
import com.ruicar.afs.cloud.apply.common.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarInvoice;
import com.ruicar.afs.cloud.apply.common.entity.ApplyContractInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustAddressDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustBaseInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustContacts;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustHistory;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustPersonalDetail;
import com.ruicar.afs.cloud.apply.common.entity.ApplyEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyGpsInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyOrderInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplySignHistory;
import com.ruicar.afs.cloud.apply.common.entity.ApplySignRelation;
import com.ruicar.afs.cloud.apply.common.entity.LoanGpsRuleInfo;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConfig;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConstants;
import com.ruicar.afs.cloud.apply.contract.condition.LoanAppFormStateCondition;
import com.ruicar.afs.cloud.apply.contract.condition.LoanAppSignPeopleListCondition;
import com.ruicar.afs.cloud.apply.contract.enums.CertTypePictureEnum;
import com.ruicar.afs.cloud.apply.contract.service.ApplyAddPriceItemsService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCarInvoiceService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyContractInfoService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCustHistoryService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyGpsInfoService;
import com.ruicar.afs.cloud.apply.contract.service.LoanGpsRuleInfoService;
import com.ruicar.afs.cloud.apply.pre.app.enums.AppEnum;
import com.ruicar.afs.cloud.apply.pre.approve.utils.ImageUploadUtil;
import com.ruicar.afs.cloud.apply.pre.loan.condition.ApplyAppFaceCondition;
import com.ruicar.afs.cloud.apply.pre.loan.condition.ApplyAppFormStateCondition;
import com.ruicar.afs.cloud.apply.pre.loan.condition.ApplyAppSignAgreeMentCondition;
import com.ruicar.afs.cloud.apply.pre.loan.condition.CaseSubmitInfoCondition;
import com.ruicar.afs.cloud.apply.pre.loan.controller.FilterParamsController;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyAffiliatedUnitService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyAppService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCarDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustAddressService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustBaseInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustContactsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustPersonalService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyOrderInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.vo.FilterParamsVO;
import com.ruicar.afs.cloud.apply.pre.smallprogram.dto.ChangeBankInfoDTO;
import com.ruicar.afs.cloud.apply.pre.smallprogram.service.ApplySmallProgramService;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyFinancingItemsService;
import com.ruicar.afs.cloud.bizcommon.cfca.dto.CfCaSignatureDto;
import com.ruicar.afs.cloud.bizcommon.cfca.service.CfCaSignatureService;
import com.ruicar.afs.cloud.bizcommon.enums.TemplatePrintType;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.service.AfsUser;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessTypeEnum;
import com.ruicar.afs.cloud.common.rules.RuleHelper;
import com.ruicar.afs.cloud.common.rules.constants.RuleRunEnum;
import com.ruicar.afs.cloud.common.rules.dto.RuleRunResult;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.enums.common.CapitalOrderStatusEnum;
import com.ruicar.afs.cloud.image.config.FileProperties;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentClassEnum;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.BusiNodeEnum;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.enums.IsElectronicEnum;
import com.ruicar.afs.cloud.image.mapper.ComAttachmentManagementMapper;
import com.ruicar.afs.cloud.image.service.ComAttaManageService;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.image.vo.UploadInputVo;
import com.ruicar.afs.cloud.parameter.commom.enums.AffiliatedWay;
import com.ruicar.afs.cloud.parameter.commom.enums.CarNature;
import com.ruicar.afs.cloud.parameter.commom.enums.CarType;
import com.ruicar.afs.cloud.parameter.commom.enums.CustType;
import com.ruicar.afs.cloud.parameter.commom.enums.WhetherEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.Socket;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.APP_FORM_STATE_ONE;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.APP_FORM_STATE_ZERO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.BUSINESS_TYPE_NEW_CAR;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.BUSINESS_TYPE_OLD_CAR;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.CAR_DEALER;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.CERTIFICATE_STATE_ONE;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.CERTIFICATE_STATE_TWO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.CERTIFICATE_STATE_ZERO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.COMMON_BORROWER;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.COM_BORROWER_ID_CARD_FRONT;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.GUARANTOR;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.GUARANTOR_BORROWER_ID_CARD_FRONT;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.LOGIN_FACE_PHOTO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.MAIN_BORROWER_ID_CARD_FRONT;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.MESSAGE_TYPE_ZERO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.NO;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.ONE;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.PRINCIPAL_BORROWER;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.WITNESSES;
import static com.ruicar.afs.cloud.apply.common.utils.ApplyConstants.YES;


/**
 * @description: 进件App ServiceImpl
 * @author: mingzhi.li
 * @date: 2020/8/17 21:11
 */
@AllArgsConstructor
@Service
@Slf4j
public class ApplyAppServiceImpl implements ApplyAppService {

    private final ApplyCustBaseInfoService applyCustBaseInfoService;
    private final ComAttaManageService comAttaManageService;
    private final ComAttachmentFileService attachmentFileService;
    private final FileProperties fileProperties;
    private final CfCaSignatureService cfCaSignatureService;
    private final ApplyCustAddressService applyCustAddressService;
    private final ApplySignRelationService applySignRelationService;
    private final ApplySignHistoryService applySignHistoryService;
    private final ApplyOrderInfoService applyOrderInfoService;
    private final ApplyAffiliatedUnitService applyAffiliatedUnitService;
    private final ApplyCarDetailsService applyCarDetailsService;
    private final ApplyCustPersonalService custPersonalService;
    private final ApplyCustContactsService applyCustContactsService;
    private final ApplyCarInvoiceService applyCarInvoiceService;
    private final ApplyGpsInfoService applyGpsInfoService;
    private final LoanGpsRuleInfoService loanGpsRuleInfoService;
    private final ApplyCostDetailsService applyCostDetailsService;
    private final ApplyFinancingItemsService financingItemsService;
    private final ApplyAddPriceItemsService applyAddPriceItemsService;
    private final ApplyContractInfoService applyContractInfoService;
    private final ApplyEnterpriseCustomerDetailsService enterpriseCustomerDetailsService;
    private final ApplyCustHistoryService applyCustHistoryService;
    private final ApplyAdminService applyAdminService;
    private final ApplySmallProgramService applySmallProgramService;
    private final ComAttachmentManagementMapper managementMapper;
    private final ImageUploadUtil imageUploadUtil;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final ApplyConfig config;
    private final TsysParamConfigService tSysParamConfigService;
    private final FilterParamsController filterParamsController;

    /**
     * 根据客户id获取获取上传附件配置参数
     * @param custId
     * @return
     */
    @Override
    public ComAttachmentManagement getComAttachmentManagement(Long custId) {
        ApplyCustBaseInfo info = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getId, custId));
        Assert.isTrue(info!=null,"用户信息不存在"+custId);
        ComAttachmentManagement management =null;
        if(PRINCIPAL_BORROWER.equals(info.getCustRole())){
            //客户角色：主借人
            management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode, MAIN_BORROWER_ID_CARD_FRONT)
                    .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.ORDER_APPLY.getCode()));
        }else if(COMMON_BORROWER.equals(info.getCustRole())){
            //共借人
            management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode, COM_BORROWER_ID_CARD_FRONT)
                    .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.ORDER_APPLY.getCode()));
        }else if(GUARANTOR.equals(info.getCustRole())){
            //担保人
            management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode, GUARANTOR_BORROWER_ID_CARD_FRONT)
                    .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.ORDER_APPLY.getCode()));
        }else if(CAR_DEALER.equals(info.getCustRole())){
            //车商
            management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode, CertTypePictureEnum.CONTRACT_INVOICE_ID_CARD_FRONT.getIndex())
                    .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.LOAN_APPLY.getCode()));
        }
        return management;
    }

    /**
     * 根据附件人脸识别标，获取配置参数信息
     * @return
     */
    @Override
    public ComAttachmentManagement getUserNameManagement() {
        ComAttachmentManagement management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                .eq(ComAttachmentManagement::getUniqueCode, LOGIN_FACE_PHOTO));
        return management;
    }

    /**
     * 电子签名（贷后银行卡变更）
     * @param signatureDto
     * @return
     */
    @Override
    public IResponse electronicBankInfo(CfCaSignatureDto signatureDto) {
        log.info("根据签约场景获取客户信息开始");
        ChangeBankInfoDTO info =applySmallProgramService.getBankCardInfo(signatureDto.getCaseNo());
        if(info == null){
            log.info("银行卡信息异常,异常编号"+signatureDto.getCaseNo());
            return IResponse.fail("银行卡信息异常");
        }
        //客户姓名
        CfCaSignatureDto dto = new CfCaSignatureDto();
        dto.setCustName(info.getCustName());
        dto.setCertNo(info.getCertNo());//证件号码
        dto.setTelPhone(info.getPhone());//手机号码
        dto.setDetailAddress("");//详细地址
        dto.setKeyword(info.getKeyWord());//关键字
        dto.setFile(signatureDto.getFile());//签约图片文件流
        dto.setRotationAngle(signatureDto.getRotationAngle());
        log.info("签约签字开始===========》》"+dto.getCustName());
        ComAttachmentFile attachmentFile =null;
        List<ComAttachmentFile> fileList = applySmallProgramService.getFileList(signatureDto.getCaseNo());
        if(fileList !=null || fileList.size()>0){
            attachmentFile =fileList.get(0);
        }
        if(attachmentFile== null){
            return IResponse.fail("文件id不存在");
        }
        byte[] fileByte = attachmentFileService.downloadAttachmentFile(attachmentFile);
        if (fileByte.length > 0) {
            dto.setFileByte(fileByte);
        }else{
            log.info("服务器下载待签名文件失败！"+attachmentFile.getFileName());
            return IResponse.fail("服务器下载待签名文件失败！"+attachmentFile.getFileName());
        }
        log.info("组装附件上传实体类vo开始");
        UploadInputVo vo = getInputVo(attachmentFile);
        String outPath = fileProperties.getTempDir() + File.separator + attachmentFile.getFileName();
        File outFile = FileUtil.touch(outPath);
        byte[] file = new byte[0];
        try {
            file = cfCaSignatureService.getCfCaSignature(dto);
        } catch (Exception e) {
            log.info("合成签名文件失败"+attachmentFile.getFileName());
            e.printStackTrace();
        }
        ComAttachmentFile comAttachmentFile =null;
        if(file!=null && file.length>0){

            outFile = FileUtil.writeBytes(file,outPath);
            //附件上传
            comAttachmentFile = attachmentFileService.storeFileToOss(outFile, vo);
            //删除临时文件
            FileUtil.del(outFile);
        }else{
            log.info("签名失败，失败客户"+dto.getCustName());
            return IResponse.fail("签名失败"+dto.getCustName());
        }
        return IResponse.success(comAttachmentFile);
    }

    /**
     * 组装附件上传实体类
     * @param attachmentFile
     * @return
     */
    public UploadInputVo getInputVo(ComAttachmentFile attachmentFile){
        UploadInputVo vo = new UploadInputVo();
        vo.setAttachmentId(attachmentFile.getAttachmentCode());
        vo.setBusiNo(attachmentFile.getBusiNo());
        vo.setBelongNo(attachmentFile.getBusiNo());
        vo.setAttachmentName(attachmentFile.getAttachmentName());
        vo.setFileName(attachmentFile.getFileName());
        vo.setFileType(TemplatePrintType.PDF.name());
        vo.setArchiveClass(attachmentFile.getArchiveClass());
        vo.setIsDelete(NO);
        vo.setIsElectronic(YES);
        vo.setRemake(attachmentFile.getRemake());
        vo.setFileSource("com_print_form_manage");
        return vo;
    }

    /**
     * 保存签约历史记录
     * @param dto
     * @param comAttachmentFile
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSignHistory(CfCaSignatureDto dto, ComAttachmentFile comAttachmentFile, ApplySignRelation relation, String fileSource) {

        ApplySignHistory history = new ApplySignHistory();
        history.setBusinessNo(comAttachmentFile.getBusiNo());//申请编号
        history.setCustId(dto.getCustId());//客户id
        history.setCustName(dto.getCustName());//客户姓名
        history.setFileId(comAttachmentFile.getId().toString());//文件file_id
        history.setFileName(comAttachmentFile.getFileName());//文件名称
        history.setTempleId(Long.valueOf(comAttachmentFile.getAttachmentCode()));//模版id
        history.setTempleName(comAttachmentFile.getAttachmentName());//模版名称
        history.setFileName(comAttachmentFile.getFileName());//文件名称
        history.setRelationId(relation.getId());
        history.setCertNo(dto.getCertNo());
        history.setTelPhone(dto.getTelPhone());
        history.setFileSource(fileSource);
        applySignHistoryService.save(history);//签约关联关联表
        return Boolean.TRUE;
    }

    /**
     * 获取角色最新签约附件
     * @param condition
     * @return
     */
    @Override
    public List<ComAttachmentFile> getApplyFileList(ApplyAppSignAgreeMentCondition condition) {
        List<ComAttachmentFile> fileList = new ArrayList<>();
        log.info("从合同签约关系表获取签约数据开始=========》》》》");
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getCustId, condition.getCustId())
                .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()));
        if(signRelationList !=null && signRelationList.size()>0){
            log.info("签约文件list组装开始");
            for(ApplySignRelation relation :signRelationList){
                List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                        .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo()).orderByDesc(ComAttachmentFile::getCreateTime));
                if(attachmentList !=null && attachmentList.size()>0){
                    attachmentList.get(0).setSignStatus(relation.getStatus());
                    fileList.add(attachmentList.get(0));
                }
            }
        }
        return fileList;
    }

    /**
     * 获取所有进件最新签约附件
     * @param condition
     * @return
     */
    @Override
    public List<ComAttachmentFile> getApplyFileListAll(ApplyAppFormStateCondition condition) {
        List<ComAttachmentFile> fileList = new ArrayList<>();
        log.info("从合同签约关系表获取签约数据开始=========》》》》");
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()));
        if(signRelationList !=null && signRelationList.size()>0){
            log.info("签约文件list组装开始");
            for(ApplySignRelation relation :signRelationList){
                List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                        .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo()).orderByDesc(ComAttachmentFile::getCreateTime));
                if(attachmentList !=null && attachmentList.size()>0){
                    fileList.add(attachmentList.get(0));
                }
            }
        }
        return fileList;
    }

    /**
     * 进件所有角色是否全部生成合同
     * @param condition
     * @return
     */
    @Override
    public String getApplyFileIsComplete(ApplyAppFormStateCondition condition) {
        String isComplete=YES;
        //查询有多少个角色
        List<ApplyCustBaseInfo> custBaseInfos = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo()));
        if(custBaseInfos !=null && custBaseInfos.size()>0){
            for(ApplyCustBaseInfo info :custBaseInfos){
                log.info("从合同签约关系表获取签约数据开始=========》》》》");
                List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                        .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo())
                        .eq(ApplySignRelation::getCustId,info.getId()));
                if(signRelationList ==null || signRelationList.size()==0){
                    isComplete=NO;
                    break;
                }
            }
        }
        // 主借人
        String signMainManState = APP_FORM_STATE_ONE;
        List<ApplySignRelation> mainList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()).eq(ApplySignRelation::getCustRole, PRINCIPAL_BORROWER));
        if(CollectionUtil.isNotEmpty(mainList)){
            for(ApplySignRelation signRelation:mainList){
                if(NO.equals(signRelation.getStatus())){
                    signMainManState = APP_FORM_STATE_ZERO;
                    break;
                }
            }
        }else{
            signMainManState = APP_FORM_STATE_ZERO;
        }
        condition.setSignMainManState(signMainManState);
        // 共借人
        String signWereBorrowedState = APP_FORM_STATE_ONE;
        List<ApplySignRelation> borrowedList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()).eq(ApplySignRelation::getCustRole, COMMON_BORROWER));
        if(CollectionUtil.isNotEmpty(borrowedList)){
            for(ApplySignRelation signRelation:borrowedList){
                if(NO.equals(signRelation.getStatus())){
                    signWereBorrowedState = APP_FORM_STATE_ZERO;
                    break;
                }
            }
        }else{
            signWereBorrowedState = APP_FORM_STATE_ZERO;
        }
        condition.setSignWereBorrowedState(signWereBorrowedState);
        // 保证人
        String signGuarantorState = APP_FORM_STATE_ONE;
        List<ApplySignRelation> guarantorList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()).eq(ApplySignRelation::getCustRole, GUARANTOR));
        if(CollectionUtil.isNotEmpty(guarantorList)){
            for(ApplySignRelation signRelation:guarantorList){
                if(NO.equals(signRelation.getStatus())){
                    signGuarantorState = APP_FORM_STATE_ZERO;
                    break;
                }
            }
        }else{
            signGuarantorState = APP_FORM_STATE_ZERO;
        }
        condition.setSignGuarantorState(signGuarantorState);

        return isComplete;
    }

    /**
     * 放款合同-获取单个角色签约附件
     * @param condition
     * @return
     */
    @Override
    public List<ComAttachmentFile> getLoanFileList(LoanAppSignPeopleListCondition condition) {
        List<ComAttachmentFile> fileList = new ArrayList<>();
        log.info("从合同签约关系表获取签约数据开始=========》》》》");
        List<ApplySignRelation> signRelationList = null;
        if(WITNESSES.equals(condition.getCustRole())){
            signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getCustRole, condition.getCustRole())
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()));
        }else{
            signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getCustId, condition.getCustId())
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()));
        }
        if(signRelationList !=null && signRelationList.size()>0){
            log.info("签约文件list组装开始");
            for(ApplySignRelation relation :signRelationList){
                List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                        .eq(ComAttachmentFile::getBusiNo, condition.getContractNo()).orderByDesc(ComAttachmentFile::getCreateTime));
                if(attachmentList !=null && attachmentList.size()>0){
                    attachmentList.get(0).setSignStatus(relation.getStatus());
                    fileList.add(attachmentList.get(0));
                }
            }
        }
        return fileList;
    }

    /**
     * 获取放款所有签约附件
     * @param condition
     * @return
     */
    @Override
    public List<ComAttachmentFile> getLoanFileListAll(LoanAppFormStateCondition condition) {
        List<ComAttachmentFile> fileList = new ArrayList<>();
        log.info("从合同签约关系表获取签约数据开始=========》》》》");
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()));
        if(signRelationList !=null && signRelationList.size()>0){
            log.info("签约文件list组装开始");
            for(ApplySignRelation relation :signRelationList){
                List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                        .eq(ComAttachmentFile::getBusiNo, condition.getContractNo()).orderByDesc(ComAttachmentFile::getCreateTime));
                if(attachmentList !=null && attachmentList.size()>0){
                    fileList.add(attachmentList.get(0));
                }
            }
        }
        return fileList;
    }

    /**
     * 放款所有角色是否全部生成合同
     * @param condition
     * @return
     */
    @Override
    public String getLoanFileIsComplete(LoanAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo) {
        String isComplete=YES;
        //查询有多少个角色
        List<ApplyCustBaseInfo> custBaseInfos = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo()));
        if(custBaseInfos !=null && custBaseInfos.size()>0){
            for(ApplyCustBaseInfo info :custBaseInfos){
                List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                        .eq(ApplySignRelation::getBusinessNo,condition.getContractNo())
                        .eq(ApplySignRelation::getCustId,info.getId()));
                if(signRelationList ==null || signRelationList.size()==0){
                    isComplete=NO;
                    break;
                }
            }
        }

        //开票方个人 判断是否生成签约关联关系表
        ApplyCarInvoice applyCarInvoice = applyCarInvoiceService.getOne(Wrappers.<ApplyCarInvoice>query().lambda()
                .eq(ApplyCarInvoice::getApplyNo, condition.getApplyNo())
                .eq(ApplyCarInvoice::getInvoiceNature,"personal"));
        if(BusinessTypeEnum.OLD_CAR.getCode().equals(applyOrderInfo.getBusinessType()) && applyCarInvoice !=null){
            List<ApplySignRelation> carDealerSignRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo())
                    .eq(ApplySignRelation::getCustRole,CAR_DEALER));
            if(carDealerSignRelationList ==null || carDealerSignRelationList.size()==0){
                isComplete=NO;
            }
        }
        log.info(condition.getContractNo() + "放款合同是否已生成：" + isComplete);
        return isComplete;
    }

    /**
     * 获取角色签约附件
     * @param condition
     * @return
     */
    @Override
    public List<ComAttachmentFile> getPreFileList(ApplyAppSignAgreeMentCondition condition) {
        return applySmallProgramService.getSignFileList(condition);
    }

    @Deprecated
    private List<ComAttachmentFile> preFileList(ApplyAppSignAgreeMentCondition condition) {
        List<ComAttachmentFile> fileList = new ArrayList<>();
        log.info("从合同签约关系表获取签约数据开始=========》》》》");
        if(MESSAGE_TYPE_ZERO.equals(condition.getSignScene())){
            //预审批获取签约文件
            List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getCustId, condition.getPreId())
                    .eq(ApplySignRelation::getBusinessNo,condition.getPreId().toString()));
            if(signRelationList !=null && signRelationList.size()>0){
                log.info("签约文件list组装开始");
                for(ApplySignRelation relation :signRelationList){
                    List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                            .eq(ComAttachmentFile::getBusiNo, condition.getPreId().toString()).orderByDesc(ComAttachmentFile::getCreateTime));
                    if(attachmentList !=null && attachmentList.size()>0){
                        attachmentList.get(0).setSignStatus(relation.getStatus());
                        fileList.add(attachmentList.get(0));
                    }
                }
            }
        }else{
            //主借人、共借人、担保人获取签约文件（小程序签约征信授权书）
            List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getCustId, condition.getCustId())
                    .eq(ApplySignRelation::getBusinessNo,condition.getApplyNo()));
            if(signRelationList !=null && signRelationList.size()>0){
                log.info("签约文件list组装开始");
                for(ApplySignRelation relation :signRelationList){
                    // add by sijun.yu 2020-11-2 征信授权书
                    ComAttachmentManagement comAttachmentManagement = comAttachmentManagementService.getUniqueCodeByTempleId(relation.getTempleId());
                    if(comAttachmentManagement != null){
                        String uniqueCode = comAttachmentManagement.getUniqueCode();
                        if("mainLetterOfAuthorization".equals(uniqueCode) || "borrowLetterOfAuthorization".equals(uniqueCode) || "guaranteeLetterOfAuthorization".equals(uniqueCode)){
                            List<ComAttachmentFile> attachmentList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                                    .eq(ComAttachmentFile::getAttachmentCode, relation.getTempleId().toString())
                                    .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo()).orderByDesc(ComAttachmentFile::getCreateTime));
                            if(attachmentList !=null && attachmentList.size()>0){
                                attachmentList.get(0).setSignStatus(relation.getStatus());
                                fileList.add(attachmentList.get(0));
                            }
                        }
                    }
                }
            }
        }
        return fileList;
    }

    /**
     * 获取进件flag业务类型数据信息
     * @param condition
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getBusinessTypeArea(ApplyAppFormStateCondition condition) {
        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>query().lambda().eq(ApplyOrderInfo::getApplyNo, condition.getApplyNo()));
        if(applyOrderInfo !=null && StringUtils.isNotEmpty(applyOrderInfo.getAffiliatedWay())){
            //是否已录入：是
            condition.setBusinessTypeState(APP_FORM_STATE_ONE);
            if(!StringUtils.isBlank(applyOrderInfo.getRemarks())){
                //是否已录入：是
                condition.setRemarkState(APP_FORM_STATE_ONE);
            }else{
                //是否已录入：否
                condition.setRemarkState(APP_FORM_STATE_ZERO);
            }
            condition.setBusinessType(applyOrderInfo.getBusinessType());//业务类型
            condition.setAffiliatedWay(applyOrderInfo.getAffiliatedWay());//挂靠方式
            condition.setCarNature(applyOrderInfo.getCarNature());//车辆属性
        }else{
            //是否已录入：否
            condition.setBusinessTypeState(APP_FORM_STATE_ZERO);
            condition.setRemarkState(APP_FORM_STATE_ZERO);
        }
        return condition;
    }

    /**
     * 获取进件flag挂靠信息
     * @param condition
     * @param applyOrderInfo
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getAffiliatedInfoArea(ApplyAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo) {

        List<ApplyAffiliatedUnit> applyAffiliatedUnitList = applyAffiliatedUnitService.list(Wrappers.<ApplyAffiliatedUnit>query().lambda().eq(ApplyAffiliatedUnit::getApplyNo, condition.getApplyNo()));
        if(AffiliatedWay.NO.getIndex().equals(applyOrderInfo.getAffiliatedWay())){
            //挂靠方式无，前端无需验证是否已录入完成
            condition.setAffiliatedState(APP_FORM_STATE_ONE);
        }else{
            if(applyAffiliatedUnitList !=null && applyAffiliatedUnitList.size()>0){
                //modify by sijun.yu 2020-12-19 挂靠信息是否修改
                ApplyAffiliatedUnit affiliatedUnit = applyAffiliatedUnitList.get(0);
                if(YES.equals(affiliatedUnit.getModifyFlag()) && !AffiliatedWay.PERSONAL_AFFILIATED.equals(applyOrderInfo.getAffiliatedWay())){
                    condition.setAffiliatedState(APP_FORM_STATE_ZERO);//是否已录入：否
                }else{
                    condition.setAffiliatedState(APP_FORM_STATE_ONE);//是否已录入：是
                }
            }else{
                condition.setAffiliatedState(APP_FORM_STATE_ZERO);//是否已录入：否
            }
        }
        return condition;
    }

    /**
     * 获取进件flag资产信息
     * @param condition
     * @param applyOrderInfo
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getAssetsInfoArea(ApplyAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo) {
        List<ApplyCarDetails> applyCarDetailsList = applyCarDetailsService.list(Wrappers.<ApplyCarDetails>query().lambda().eq(ApplyCarDetails::getApplyNo, condition.getApplyNo()));
        if(applyCarDetailsList !=null && applyCarDetailsList.size()>0){
            //是否已录入：是
            condition.setAssetsState(APP_FORM_STATE_ONE);
            //业务类型：二手车 或者车辆属性：挂牌新车
            if(BUSINESS_TYPE_OLD_CAR.equals(applyOrderInfo.getBusinessType())){
                //判断车牌号是否为空
                log.info("======================车辆手续信息区域组装开始========================》");
                if(StrUtil.isNotBlank(applyCarDetailsList.get(0).getLicensePlate())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getDealerName())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getDealerPhone())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getDealerProvince())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getDealerCity())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getDealerAddress())
                        &&applyCarDetailsList.get(0).getYearTransNum()!=null
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getIsCirTrans())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getIsMortgage())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getRegistProvince())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getRegistCity())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getLicenseProvince())
                        &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getLicenseCity())){
                    //已录入车辆手续信息
                    condition.setCarFormAmitieState(APP_FORM_STATE_ONE);
                }else {
                    //已录入车辆手续信息
                    condition.setCarFormAmitieState(APP_FORM_STATE_ZERO);
                }
            }else{
                if(CarNature.PLATE_CAR.getIndex().equals(applyOrderInfo.getCarNature())){
                    if(applyCarDetailsList.get(0).getMileage()!=null//里程数
                            &&applyCarDetailsList.get(0).getVehicleMadeDate()!=null//出厂日期
                            &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getCarBelongs()) //所有人
                            &&StrUtil.isNotBlank(applyCarDetailsList.get(0).getBelongsCertNo())){//所有人证件号码
                        //已录入车辆手续信息
                        condition.setCarFormAmitieState(APP_FORM_STATE_ONE);
                    }else {
                        //已录入车辆手续信息
                        condition.setCarFormAmitieState(APP_FORM_STATE_ZERO);
                    }
                }else {
                    condition.setCarFormAmitieState(APP_FORM_STATE_ONE);
                }

            }
            //modify by sijun.yu 2020-12-23 换车标识
            if(YES.equals(applyCarDetailsList.get(0).getChangeFlag())){
                condition.setAssetsState(APP_FORM_STATE_ZERO);
                condition.setCarFormAmitieState(APP_FORM_STATE_ZERO);
            }
        }else{
            //是否已录入：否
            condition.setAssetsState(APP_FORM_STATE_ZERO);
            //未录入车辆手续信息
            condition.setCarFormAmitieState(APP_FORM_STATE_ZERO);
        }
        if(APP_FORM_STATE_ONE.equals(condition.getBusinessTypeState()) &&
                APP_FORM_STATE_ONE.equals(condition.getAffiliatedState()) &&
                APP_FORM_STATE_ONE.equals(condition.getAssetsState()) &&
                APP_FORM_STATE_ONE.equals(condition.getCarFormAmitieState())){
            //当所有信息都是已录入情况更新  业务选择区域是否录入完成：是
            condition.setBusinessChoiceState(APP_FORM_STATE_ONE);
        }else{
            //当所有信息都是已录入情况更新  业务选择区域是否录入完成：否
            condition.setBusinessChoiceState(APP_FORM_STATE_ZERO);
        }
        return condition;
    }

    /**
     * 获取进件flag客户信息
     * @param condition
     * @param applyOrderInfo
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getCustInfoArea(ApplyAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo) {

        List<ApplyCustBaseInfo> custBaseInfos = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo()));

        //主借人信息总状态
        condition.setMainManState(APP_FORM_STATE_ZERO);
        //共借人信息总状态
        condition.setWereBorrowedState(APP_FORM_STATE_ZERO);
        //保证人总状态
        condition.setGuarantorState(APP_FORM_STATE_ZERO);
        //客户信息总状态
        condition.setCustomerState(APP_FORM_STATE_ZERO);

        //主借人基本信息
        condition.setMainManBasicState(APP_FORM_STATE_ZERO);
        //主借人地址信息
        condition.setMainManAddressState(APP_FORM_STATE_ZERO);
        //主借人工作信息
        condition.setMainManWorkState(APP_FORM_STATE_ZERO);
        //主借人其他信息
        condition.setMainManOtherState(APP_FORM_STATE_ZERO);

        //共借人基本信息
        condition.setWereBorrowedBasicState(APP_FORM_STATE_ZERO);
        //共借人地址信息
        condition.setWereBorrowedAddressState(APP_FORM_STATE_ZERO);
        //共借人工作信息
        condition.setWereBorrowedWorkState(APP_FORM_STATE_ZERO);

        //保证人基本信息
        condition.setGuarantorBasicState(APP_FORM_STATE_ZERO);
        //保证人地址信息
        condition.setGuarantorAddressState(APP_FORM_STATE_ZERO);
        //保证人工作信息
        condition.setGuarantorWorkState(APP_FORM_STATE_ZERO);

        List<String> custRolesOfCurApply = new ArrayList<>(custBaseInfos != null ? custBaseInfos.size() : 0);
        if(custBaseInfos != null && custBaseInfos.size() > 0 ){
            for(ApplyCustBaseInfo cust: custBaseInfos){
                if (StringUtils.isNotBlank(cust.getCustRole())) {
                    custRolesOfCurApply.add(cust.getCustRole());
                }
                if (CustType.COMPANY.getIndex().equals(cust.getCustType())){
                    setEnterpriseCustomerStates(condition,cust);
                }
                else if (CustType.PERSON.getIndex().equals(cust.getCustType())) {
                    setPersonalCustomerStates(condition,cust);
                }
            }
        }

        //紧急联系人
        condition.setEmergencyContactState(APP_FORM_STATE_ZERO);
        List<ApplyCustContacts> custContacts = applyCustContactsService.list(Wrappers.<ApplyCustContacts>query().lambda()
                .eq(ApplyCustContacts::getApplyNo, condition.getApplyNo()));
        if(CollectionUtil.isNotEmpty(custContacts)) {
            ApplyCustContacts custContact = custContacts.get(0);
            if (StrUtil.isAllNotBlank(custContact.getCustName(),custContact.getTelPhone(),custContact.getCustRelation())) {
                //紧急了联系人：已录入
                condition.setEmergencyContactState(APP_FORM_STATE_ONE);
            }
        }
        if (APP_FORM_STATE_ONE.equals(condition.getEmergencyContactState())) {
            boolean flag = true;
            if (custRolesOfCurApply.contains(PRINCIPAL_BORROWER)) {
                flag = APP_FORM_STATE_ONE.equals(condition.getMainManState());
            }
            if (custRolesOfCurApply.contains(COMMON_BORROWER)) {
                flag = APP_FORM_STATE_ONE.equals(condition.getWereBorrowedState());
            }
            if (custRolesOfCurApply.contains(GUARANTOR)) {
                flag = APP_FORM_STATE_ONE.equals(condition.getGuarantorState());
            }
            if (flag) {
                condition.setCustomerState(APP_FORM_STATE_ONE);
            }
        }

        return condition;
    }

    /**
     * 获取进件flag 协议签约状态
     * @param condition
     * @param applyOrderInfo
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getSignInfoArea(ApplyAppFormStateCondition condition, ApplyOrderInfo applyOrderInfo) {
        log.info("获取进件flag协议签约状态，{} 参数{}", condition.getApplyNo(), JSON.toJSONString(condition));
        condition.setSignedAgreementState(NO);
        condition.setSignApplyFlag(NO);
        //首先判断重出标识，以及是否已经生成了征信授权书的签约文件
        List<ApplyCustBaseInfo> custBaseInfoList = applyCustBaseInfoService.getCustBaseInfoList(condition.getApplyNo());
        int maxFailNumber = Integer.parseInt(tSysParamConfigService.getParamValue("signFailNumber", "time", "3"));

        //关联签约关系表
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda().eq(ApplySignRelation::getBusinessNo, condition.getApplyNo()));

        //有签约关系，判断是否已pc签约
        if (CollUtil.isNotEmpty(signRelationList)) {
            signCheckWithSignRele(condition, maxFailNumber, signRelationList, null, null);
            String signApplyFlag = YES;
            for (ApplySignRelation applySignRelation : signRelationList) {
                if (WhetherEnum.No.getIndex().equals(applySignRelation.getStatus())) {
                    signApplyFlag = NO;
                }
            }
            condition.setSignApplyFlag(signApplyFlag);
        } else {
            //无签约关系,判断是否已pc签约,获取征信授权书
            signCheckWithoutSignRela(condition, custBaseInfoList, null, null);
        }
        String belongingCapital = applyOrderInfo.getBelongingCapital();
        if (StrUtil.startWithIgnoreCase(belongingCapital, ApplyConstants.CAPITAL_BANK_PREFIX)) {
            condition.setBelongingCapital(belongingCapital);
            condition.setBelongingCapitalDesc(BelongingCapitalEnum.getDescByCode(belongingCapital));
            condition.setSignCapitalFlag(NO);
            if (StrUtil.isNotBlank(applyOrderInfo.getCapitalOrderStatus()) && CapitalOrderStatusEnum.APPLY_CAPITAL_SIGN_FLAG_SET.contains(applyOrderInfo.getCapitalOrderStatus())) {
                condition.setSignCapitalFlag(YES);
            }
        }
        return condition;
    }


    /**
     * 签约判断（有签约关系时）
     * @param condition condition
     * @param maxFailNumber 最大失败次数
     * @param signRelationList  签约关系
     * @return true/false
     */
    private boolean signCheckWithSignRele(ApplyAppFormStateCondition condition, int maxFailNumber, List<ApplySignRelation> signRelationList,ComAttachmentManagement loanApply,ComAttachmentFile comAttachmentFile) {
        ApplyCustBaseInfo baseInfo;
        for(ApplySignRelation relation: signRelationList){
            switch (relation.getCustRole()){
                case PRINCIPAL_BORROWER:
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition,AttachmentUniqueCodeEnum.CREDIT_AUTHORIZATION_FILE.getCode());
                    // 旧的找不到就找新的
                    List<ComAttachmentFile> comFiles = getComAttachmentFiles(condition,AttachmentUniqueCodeEnum.CREDIT_AUTHORIZATION_FILE_NEW.getCode());
                    baseInfo = applyCustBaseInfoService.getCustBaseInfo(condition.getApplyNo(), relation.getCustRole());
                    if(ObjectUtil.isNotNull(comAttachmentFile) || CollectionUtil.isNotEmpty(comFiles)){
                        condition.setSignedAgreementState(YES);
                        updateReppearFlag(baseInfo);
                        log.info("{},已授权通过-主借人，app可以提交,{}",condition.getApplyNo(),comAttachmentFile);
                        return true;
                    }
                case COMMON_BORROWER:
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition,AttachmentUniqueCodeEnum.BORROW_AUTHORIZATION_FILE.getCode());
                    baseInfo = applyCustBaseInfoService.getCustBaseInfo(condition.getApplyNo(), relation.getCustRole());
                    if(ObjectUtil.isNotNull(comAttachmentFile)){
                        condition.setSignedAgreementState(YES);
                        updateReppearFlag(baseInfo);
                        log.info("{},已授权通过-共借人，app可以提交,{}",condition.getApplyNo(),comAttachmentFile);
                        return true;
                    }
                case GUARANTOR:
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition, AttachmentUniqueCodeEnum.GUARANTEE_AUTHORIZATION_FILE.getCode());
                    List<ApplyCustBaseInfo> list = applyCustBaseInfoService.getCustBaseInfos(condition.getApplyNo(), relation.getCustRole());
                    if (list != null && list.size() > 0) {
                        baseInfo = list.get(0);
                        if (ObjectUtil.isNotNull(comAttachmentFile)) {
                            condition.setSignedAgreementState(YES);
                            updateReppearFlag(baseInfo);
                            log.info("{},已授权通过-担保人，app可以提交,{}", condition.getApplyNo(), comAttachmentFile);
                            return true;
                        }
                    }
                default:break;
            }
        }
        return false;
    }

    private ComAttachmentFile getComAttachmentFile(ApplyAppFormStateCondition condition,String unCode) {
        ComAttachmentManagement loanApply;
        ComAttachmentFile attachmentFile;
        loanApply = comAttachmentManagementService.getAttachmentManagement("orderApply",unCode );
        attachmentFile = attachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda().eq(ComAttachmentFile::getBusiNo, condition.getApplyNo())
                .eq(ComAttachmentFile::getAttachmentCode, loanApply.getId().toString()).eq(ComAttachmentFile::getFileSource, "com_attachment_management"));
        return attachmentFile;
    }

    private List<ComAttachmentFile> getComAttachmentFiles(ApplyAppFormStateCondition condition,String unCode) {
        List<ComAttachmentManagement> loanApply = comAttachmentManagementService.getAttachmentManagements("orderApply", unCode);
        List<ComAttachmentFile> attachmentFiles = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(loanApply)) {
            loanApply.forEach(
                comAttachmentManagement -> {
                    ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
                    comAttachmentFile = attachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda().eq(ComAttachmentFile::getBusiNo, condition.getApplyNo())
                            .eq(ComAttachmentFile::getAttachmentCode, comAttachmentManagement.getId().toString()).eq(ComAttachmentFile::getFileSource, "com_attachment_management"));
                    attachmentFiles.add(comAttachmentFile);
                }
            );
        }
        return attachmentFiles;
    }

    /**
     * 更新重出标识
     * @param baseInfo
     */
    private void updateReppearFlag(ApplyCustBaseInfo baseInfo) {
        if (ObjectUtil.isNotNull(baseInfo) && ONE.equals(baseInfo.getReappearFlag())) {
            baseInfo.setReappearFlag("0");
            applyCustBaseInfoService.updateById(baseInfo);
        }
    }


    /**
     * 无签约关系时，判断是否pc已电子签约
     * @param condition condition
     * @param custBaseInfoList 客户信息
     * @return true/false
     */
    private boolean signCheckWithoutSignRela(ApplyAppFormStateCondition condition, List<ApplyCustBaseInfo> custBaseInfoList,ComAttachmentManagement loanApply,ComAttachmentFile comAttachmentFile) {
        if(CollectionUtil.isNotEmpty(custBaseInfoList)){
            for (ApplyCustBaseInfo baseInfo : custBaseInfoList) {
                if (PRINCIPAL_BORROWER.equals(baseInfo.getCustRole())) {//主借人
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition,AttachmentUniqueCodeEnum.CREDIT_AUTHORIZATION_FILE.getCode());
                    if(ObjectUtil.isNotNull(comAttachmentFile)){
                        condition.setSignedAgreementState(YES);
                        if("1".equals(baseInfo.getReappearFlag())){
                            baseInfo.setReappearFlag("0");
                            applyCustBaseInfoService.updateById(baseInfo);
                        }
                        log.info("{},已授权通过-主借人，app可以提交,{}",condition.getApplyNo(),comAttachmentFile);
                        return true;
                    }
                } else if (COMMON_BORROWER.equals(baseInfo.getCustRole())) {
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition,AttachmentUniqueCodeEnum.BORROW_AUTHORIZATION_FILE.getCode());
                    if(ObjectUtil.isNotNull(comAttachmentFile)){
                        condition.setSignedAgreementState(YES);
                        if("1".equals(baseInfo.getReappearFlag())){
                            baseInfo.setReappearFlag("0");
                            applyCustBaseInfoService.updateById(baseInfo);
                        }
                        log.info("{},已授权通过-共借人，app可以提交,{}",condition.getApplyNo(),comAttachmentFile);
                        return true;
                    }
                } else if (GUARANTOR.equals(baseInfo.getCustRole())) {
                    //判断是否已电子授权通过或预审通过
                    comAttachmentFile = getComAttachmentFile(condition,AttachmentUniqueCodeEnum.GUARANTEE_AUTHORIZATION_FILE.getCode());
                    if(ObjectUtil.isNotNull(comAttachmentFile)){
                        condition.setSignedAgreementState(YES);
                        if("1".equals(baseInfo.getReappearFlag())){
                            baseInfo.setReappearFlag("0");
                            applyCustBaseInfoService.updateById(baseInfo);
                        }
                        log.info("{},已授权通过-担保人，app可以提交,{}",condition.getApplyNo(),comAttachmentFile);
                        return true;
                    }
                }
            }
        }else {
            log.info("未签约,{}",condition);
            condition.setSignedAgreementState(NO);
            return true;
        }
        return false;
    }

    /**
     * 获取进件flag 协议签约状态
     * @param condition
     * @return
     */
    @Override
    public ApplyAppFormStateCondition getAttachmentsArea(ApplyAppFormStateCondition condition,ApplyOrderInfo orderInfoByApplyNo) {
        //获取影像件规则数据
        CaseSubmitInfoCondition caseSubmitInfoCondition = new CaseSubmitInfoCondition();
        caseSubmitInfoCondition.setApplyNo(condition.getApplyNo());
        IResponse<FilterParamsVO> paramsVOIResponse = filterParamsController.getFilterParams(caseSubmitInfoCondition);
        FilterParamsVO filterParamsVO = paramsVOIResponse.getData();
        List<String> errorMsgList= new ArrayList<>();
        String busiNo = condition.getBusiNo();
        String busiType = condition.getBusiType();
        JSONObject json = (JSONObject) JSONObject.toJSON(filterParamsVO);
        log.info("进件规则：{}",JSONObject.toJSONString(filterParamsVO));
        List<ComAttachmentManagement> managementList = comAttaManageService.getManagementByRuleGroup(json, busiType);
        managementList.forEach(comAttachmentManagement -> {
            List<ComAttachmentFile> fileList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda().
                    eq(ComAttachmentFile::getAttachmentCode, String.valueOf(comAttachmentManagement.getId()))
                    .eq(ComAttachmentFile::getBusiNo, busiNo));
            comAttachmentManagement.setFileList(fileList);
            if(comAttachmentManagement.getAttachmentClass()== AttachmentClassEnum.SUBCLASS.getCode() &&comAttachmentManagement.getNeedNums()>fileList.size()){
                errorMsgList.add(comAttachmentManagement.getAttachmentName());
            }
        });

        Map<Long, List<ComAttachmentManagement>> subclassAttachmentManagementMaps = managementList.stream()
                .collect(Collectors.groupingBy(ComAttachmentManagement::getParentId));
        managementList.forEach(management->{
            if(management.getAttachmentClass()==AttachmentClassEnum.GROUP.getCode()){
                int filenums=0;
                List<ComAttachmentManagement> attachmentManagementList=subclassAttachmentManagementMaps.get(management.getId());
                if(!CollectionUtils.isEmpty(attachmentManagementList)){
                    for(ComAttachmentManagement comAttachmentManagement : attachmentManagementList){
                        if(comAttachmentManagement.getFileList().size()>0){
                            filenums=filenums+1;
                        }
                    }
                    if(management.getNeedNums()>filenums){
                        errorMsgList.add(management.getAttachmentName());
                    }
                }
            }
        });
        if(errorMsgList.size()>0){
            condition.setImageAttachmentsState(APP_FORM_STATE_ZERO);
            condition.setErrorMsg(errorMsgList.toString());
        }else{
            condition.setImageAttachmentsState(APP_FORM_STATE_ONE);
        }
        return condition;
    }

    /**
     * 获取放款flag发票信息
     * @param condition
     * @param applyOrderInfo
     * @return
     */
    @Override
    public LoanAppFormStateCondition getInvoiceTypeArea(LoanAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo) {
        List<ApplyCarInvoice> carInvoiceList = applyCarInvoiceService.list(Wrappers.<ApplyCarInvoice>query().lambda().eq(ApplyCarInvoice::getApplyNo, condition.getApplyNo()));
        //update applyContractInfo.getLendingMode  放款模式 && applyCarDetails.getFullModelNum  整车型号/车辆型号去掉
        String invoiceState = APP_FORM_STATE_ONE;
        if (carInvoiceList != null && carInvoiceList.size() > 0 && APP_FORM_STATE_ONE.equals(invoiceState)) {
            if (BUSINESS_TYPE_NEW_CAR.equals(condition.getBusinessType())) {
                if (StringUtils.isBlank(carInvoiceList.get(0).getInvoiceUnit())) {
                    //新车验证关键字开票单位是否已录入
                    invoiceState = APP_FORM_STATE_ZERO;
                } else {
                    invoiceState = APP_FORM_STATE_ONE;
                }
            } else {
                //二手车验证开票单位性质
                if (StringUtils.isBlank(carInvoiceList.get(0).getInvoiceNature())) {
                    invoiceState = APP_FORM_STATE_ZERO;
                } else {
                    //二手车根据开票单位性质动态验证：个人验证出卖方名称；企业验证开票单位名称
                    if (ApplyConstants.INVOICE_PERSONAL.equals(carInvoiceList.get(0).getInvoiceNature())) {
                        if (StringUtils.isBlank(carInvoiceList.get(0).getSaleName()) || StringUtils.isBlank(carInvoiceList.get(0).getSaleCertNo())
                                || StringUtils.isBlank(carInvoiceList.get(0).getSalePhone()) || ObjectUtils.isEmpty(carInvoiceList.get(0).getSaleCertEndDate())) {
                            invoiceState = APP_FORM_STATE_ZERO;
                        } else {
                            invoiceState = APP_FORM_STATE_ONE;
                        }
                    } else {
                        if (StringUtils.isBlank(carInvoiceList.get(0).getInvoiceUnit())) {
                            invoiceState = APP_FORM_STATE_ZERO;
                        } else {
                            invoiceState = APP_FORM_STATE_ONE;
                        }
                    }
                }
            }
        } else {
            invoiceState = APP_FORM_STATE_ZERO;
        }
        log.info("设置车辆发票信息状态:{} {}", condition.getApplyNo(), condition);
        // 二手车无需录入发票信息
        if (BUSINESS_TYPE_OLD_CAR.equals(condition.getBusinessType())) {
            invoiceState = APP_FORM_STATE_ONE;
        }
        condition.setInvoiceState(invoiceState);
        return condition;
    }

    /**
     * 获取放款flagGps信息
     * @param condition
     * @param applyOrderInfo
     * @param contractInfo
     * @return
     */
    @Override
    public LoanAppFormStateCondition getGpsInfoArea(LoanAppFormStateCondition condition,ApplyOrderInfo applyOrderInfo,ApplyContractInfo contractInfo) {
        List<ApplyGpsInfo> gpsList = applyGpsInfoService.list(Wrappers.<ApplyGpsInfo>query().lambda().eq(ApplyGpsInfo::getApplyNo, condition.getApplyNo()));
        if(gpsList !=null && gpsList.size()>0){
            ApplyGpsInfo gpsInfo = gpsList.get(0);
            if (StrUtil.isNotBlank(gpsInfo.getGpsSupplier())){
                condition.setIsGpsState(APP_FORM_STATE_ONE);
            } else {
                condition.setIsGpsState(APP_FORM_STATE_ZERO);
            }
        }else{
            condition.setIsGpsState(APP_FORM_STATE_ZERO);
        }
        return condition;
    }

    /**
     * 获取Gps规则信息
     * @param orderInfo
     * @return
     */
    public String gpsRule(ApplyOrderInfo orderInfo) {
        JSONObject runParams=new JSONObject();
        String isGreatWall= ApplyConstants.UN_GREAT_WALL;

        String isInternet;
        List<FinCostDetails> costDetails = applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, orderInfo.getApplyNo()).eq(FinCostDetails::getCostType, ApplyConstants.COST_TYPE_CAR_LOAN));
        ApplyCarDetails carDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>query().lambda().eq(ApplyCarDetails::getApplyNo, orderInfo.getApplyNo()));
        if(carDetails.getBrandName().indexOf(ApplyConstants.IS_GREAT_WALL)>=0){
            isGreatWall=ApplyConstants.GREAT_WALL;
        }
        if(StringUtils.isBlank(carDetails.getIsInternet()) || carDetails.getIsInternet().equals(ApplyConstants.UN_INTERNET)){
            isInternet=ApplyConstants.IS_NOT_INTERNET;
        }else{
            isInternet=ApplyConstants.IS_INTERNET;
        }
        BigDecimal loanAmt=BigDecimal.ZERO;
        for (FinCostDetails applyCostDetails : costDetails) {
            loanAmt=loanAmt.add(applyCostDetails.getLoanAmt());
        }
        //是否长城品牌
        runParams.put("isGreatWall",isGreatWall);
        //业务类型
        runParams.put("businessType",orderInfo.getBusinessType());
        //车辆类型
        runParams.put("carType",orderInfo.getCarType());
        //车辆属性
        runParams.put("carNature",orderInfo.getCarNature());
        //是否车联网
        runParams.put("isOnline",isInternet);
        //贷款总额
        runParams.put("loanAmount",String.valueOf(loanAmt));
        //挂靠方式
        runParams.put("affiliatedWay",orderInfo.getAffiliatedWay());
        List<LoanGpsRuleInfo> rules = loanGpsRuleInfoService.list();
        List<String> ruleNoList = rules.stream().map(s -> String.valueOf(s.getId())).collect(Collectors.toList());
        RuleRunResult ruleRunResult = RuleHelper.runRule(runParams, ruleNoList, true, RuleRunEnum.SERIAL);
        if(ruleRunResult.getResults().size()>0){
            LoanGpsRuleInfo loanGpsRuleInfo=loanGpsRuleInfoService.getById(ruleRunResult.getResults().get(0).getRuleNo());
            if(loanGpsRuleInfo!=null){
                return loanGpsRuleInfo.getInstallType();
            }
        }
        return null;
    }

    /**
     * 获取放款flag附加带信息
     * @param condition
     * @return
     */
    @Override
    public LoanAppFormStateCondition getFinancingItemsArea(LoanAppFormStateCondition condition) {
        List<FinFinancingItems> finFinancingItemsList = financingItemsService.list(Wrappers.<FinFinancingItems>query().lambda().eq(FinFinancingItems::getApplyNo, condition.getApplyNo()));
        if(finFinancingItemsList !=null && finFinancingItemsList.size()>0){
            //是否附加贷业务：否
            condition.setIsAttachedInfoState(APP_FORM_STATE_ZERO);
            for(FinFinancingItems items: finFinancingItemsList){
                if(Boolean.TRUE.equals(items.getIsView())){
                    //附加贷业务是否展示：是
                    condition.setIsAttachedInfoState(APP_FORM_STATE_ONE);
                    break;
                }else{
                    //附加贷业务是否展示：是
                    condition.setIsAttachedInfoState(APP_FORM_STATE_ZERO);
                }
            }
        }else{
            //是否附加贷业务：否
            condition.setIsAttachedInfoState(APP_FORM_STATE_ZERO);
        }
        List<ApplyAddPriceItems> addPriceItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda().eq(ApplyAddPriceItems::getApplyNo, condition.getApplyNo()));
        if(addPriceItems !=null && addPriceItems.size()>0){
            for(ApplyAddPriceItems items: addPriceItems){
                BigDecimal addFinanceAmt = items.getAddFinanceAmt() == null ? BigDecimal.ZERO : items.getAddFinanceAmt();
                if(addFinanceAmt.compareTo(BigDecimal.ZERO)==0){
                    //附加贷业务是否录入：否
                    condition.setAttachedInfoState(APP_FORM_STATE_ZERO);
                    break;
                }else{
                    //附加贷业务是否录入：是
                    condition.setAttachedInfoState(APP_FORM_STATE_ONE);
                }
            }
        }else{
            //附加贷业务是否录入：否
            condition.setAttachedInfoState(APP_FORM_STATE_ZERO);
        }
        return condition;
    }

    /**
     * 获取放款flag证件有效期信息
     * @param condition
     * @return
     */
    @Override
    public LoanAppFormStateCondition getCustInfoArea(LoanAppFormStateCondition condition) {

        // modify by sijun.yu 2021-3-4 取当前日期
        SimpleDateFormat dateFormat = new SimpleDateFormat(DatePattern.NORM_DATE_PATTERN);
        Date date = DateUtil.parse(dateFormat.format(new Date()), DatePattern.NORM_DATE_PATTERN);
        ApplyCustHistory custHistory = applyCustHistoryService.getOne(Wrappers.<ApplyCustHistory>query().lambda()
                .eq(ApplyCustHistory::getApplyNo, condition.getApplyNo())
                .eq(ApplyCustHistory::getCustRole, PRINCIPAL_BORROWER));
        if(custHistory !=null){
            if(Boolean.TRUE.equals(custHistory.getIsLongTerm())){
                condition.setCertificateMainState(CERTIFICATE_STATE_TWO);
            }else{
                Date certEndDate=custHistory.getCertEndDate();
                long diff = certEndDate.getTime() - date.getTime();
                long days = diff / (1000 * 60 * 60 * 24);
                if((certEndDate.after(date) && days<=15) || certEndDate.equals(date)){
                    //还有15天就要到期了
                    condition.setCertificateMainState(CERTIFICATE_STATE_ONE);
                }else if(certEndDate.before(date)){
                    //已到期
                    condition.setCertificateMainState(CERTIFICATE_STATE_ZERO);
                }else{
                    //未到期
                    condition.setCertificateMainState(CERTIFICATE_STATE_TWO);
                }
            }
        }else{
            ApplyCustBaseInfo mainCustInfo = this.applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                    .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo()).eq(ApplyCustBaseInfo::getCustRole, PRINCIPAL_BORROWER));
            if(mainCustInfo !=null){
                if(Boolean.TRUE.equals(mainCustInfo.getIsLongTerm())){
                    condition.setCertificateMainState(CERTIFICATE_STATE_TWO);
                }else{
                    Date certEndDate=mainCustInfo.getCertEndDate();
                    long diff = certEndDate.getTime() - date.getTime();
                    long days = diff / (1000 * 60 * 60 * 24);
                    if((certEndDate.after(date) && days<=15) || certEndDate.equals(date)){
                        //还有15天就要到期了
                        condition.setCertificateMainState(CERTIFICATE_STATE_ONE);
                    }else if(certEndDate.before(date)){
                        //已到期
                        condition.setCertificateMainState(CERTIFICATE_STATE_ZERO);
                    }else{
                        //未到期
                        condition.setCertificateMainState(CERTIFICATE_STATE_TWO);
                    }
                }
            }
        }

        ApplyCustHistory custBorrower = applyCustHistoryService.getOne(Wrappers.<ApplyCustHistory>query().lambda()
                .eq(ApplyCustHistory::getApplyNo, condition.getApplyNo())
                .eq(ApplyCustHistory::getCustRole, COMMON_BORROWER));
        if(custBorrower !=null){
            if(Boolean.TRUE.equals(custBorrower.getIsLongTerm())){
                condition.setCertificateBorrowedState(CERTIFICATE_STATE_TWO);
            }else{
                Date certEndDate=custBorrower.getCertEndDate();
                long diff = certEndDate.getTime() - date.getTime();
                long days = diff / (1000 * 60 * 60 * 24);
                if((certEndDate.after(date) && days<=15) || certEndDate.equals(date)){
                    //还有15天就要到期了
                    condition.setCertificateBorrowedState(CERTIFICATE_STATE_ONE);
                }else if(certEndDate.before(date)){
                    //已到期
                    condition.setCertificateBorrowedState(CERTIFICATE_STATE_ZERO);
                }else{
                    //未到期
                    condition.setCertificateBorrowedState(CERTIFICATE_STATE_TWO);
                }
            }
        }else{
            ApplyCustBaseInfo borrowerCustInfo = this.applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                    .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo()).eq(ApplyCustBaseInfo::getCustRole, COMMON_BORROWER));
            if(borrowerCustInfo !=null){
                if(Boolean.TRUE.equals(borrowerCustInfo.getIsLongTerm())){
                    condition.setCertificateBorrowedState(CERTIFICATE_STATE_TWO);
                }else{
                    Date certEndDate=borrowerCustInfo.getCertEndDate();
                    long diff = certEndDate.getTime() - date.getTime();
                    long days = diff / (1000 * 60 * 60 * 24);
                    if((certEndDate.after(date) && days<=15) || certEndDate.equals(date)){
                        //还有15天就要到期了
                        condition.setCertificateBorrowedState(CERTIFICATE_STATE_ONE);
                    }else if(certEndDate.before(date)){
                        //已到期
                        condition.setCertificateBorrowedState(CERTIFICATE_STATE_ZERO);
                    }else{
                        //未到期
                        condition.setCertificateBorrowedState(CERTIFICATE_STATE_TWO);
                    }
                }
            }
        }
        return condition;
    }

    /**
     * 获取放款flag总状态信息
     * @param condition
     * @return
     */
    @Override
    public LoanAppFormStateCondition getOtherInfoArea(LoanAppFormStateCondition condition, ApplyOrderInfo applyOrderInfo) {
        String loanInfoState = APP_FORM_STATE_ZERO;
        log.info("设置请款 信息录入总状态: {} {}", condition.getApplyNo(), JSONObject.toJSONString(condition));
        if (APP_FORM_STATE_ONE.equals(condition.getCarState())
                && APP_FORM_STATE_ONE.equals(condition.getInvoiceState())
                && APP_FORM_STATE_ONE.equals(condition.getBankCardState())
                && APP_FORM_STATE_ONE.equals(condition.getIsGpsState())
                && APP_FORM_STATE_ONE.equals(condition.getPayInfoStatus())) {
            // rentType = 1 ,回租没有保险信息
            if ("1".equals(applyOrderInfo.getRentType())) {
                loanInfoState = APP_FORM_STATE_ONE;
            } else {
                if (APP_FORM_STATE_ONE.equals(condition.getInsuranceState())) {
                    loanInfoState = APP_FORM_STATE_ONE;
                }
            }
        }
        condition.setLoanInfoState(loanInfoState);

        // 合同签约总状态
        String signContractState = APP_FORM_STATE_ONE;
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda().eq(ApplySignRelation::getBusinessNo,condition.getContractNo()));
        if(CollectionUtil.isNotEmpty(signRelationList)){
            for(ApplySignRelation relation: signRelationList){
                if(YES.equals(relation.getStatus())){
                    condition.setIsNeedSign(APP_FORM_STATE_ONE);
                    break;
                }else{
                    condition.setIsNeedSign(APP_FORM_STATE_ZERO);
                }
            }
            // modify by sijun.yu 2021-3-2 重出标识
            for(ApplySignRelation relation: signRelationList){
                if(YES.equals(relation.getReappearFlag())){
                    condition.setReappearFlag(YES);
                    break;
                }
            }
            //主借人状态变更成未签约
            String signMainManState = APP_FORM_STATE_ONE;//主借人是否签约
            List<ApplySignRelation> mainList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()).eq(ApplySignRelation::getCustRole, PRINCIPAL_BORROWER));
            if(CollectionUtil.isNotEmpty(mainList)){
                for(ApplySignRelation signRelation:mainList){
                    if(NO.equals(signRelation.getStatus())){
                        signMainManState = APP_FORM_STATE_ZERO;
                        signContractState = APP_FORM_STATE_ZERO;
                        break;
                    }
                }
            }else{
                signMainManState = APP_FORM_STATE_ZERO;
            }
            condition.setSignMainManState(signMainManState);
            //共借人状态变更成未签约
            String signWereBorrowedState = APP_FORM_STATE_ONE;//共借人是否签约
            List<ApplySignRelation> borrowedList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()).eq(ApplySignRelation::getCustRole, COMMON_BORROWER));
            if(CollectionUtil.isNotEmpty(borrowedList)){
                for(ApplySignRelation signRelation:borrowedList){
                    if(NO.equals(signRelation.getStatus())){
                        signWereBorrowedState = APP_FORM_STATE_ZERO;
                        signContractState = APP_FORM_STATE_ZERO;
                        break;
                    }
                }
            }else{
                signWereBorrowedState = APP_FORM_STATE_ZERO;
            }
            condition.setSignWereBorrowedState(signWereBorrowedState);
            //担保人状态变更成未签约
            String signGuarantorState = APP_FORM_STATE_ONE;//保证人是否签约
            List<ApplySignRelation> guarantorList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()).eq(ApplySignRelation::getCustRole, GUARANTOR));
            if(CollectionUtil.isNotEmpty(guarantorList)){
                for(ApplySignRelation signRelation:guarantorList){
                    if(NO.equals(signRelation.getStatus())){
                        signGuarantorState = APP_FORM_STATE_ZERO;
                        signContractState = APP_FORM_STATE_ZERO;
                        break;
                    }
                }
            }else{
                signGuarantorState = APP_FORM_STATE_ZERO;
            }
            condition.setSignGuarantorState(signGuarantorState);
            //见证人状态变更成未签约
            String signWitnessesState = APP_FORM_STATE_ONE;//见证人是否签约
            List<ApplySignRelation> witnessessList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()).eq(ApplySignRelation::getCustRole, WITNESSES));
            if(CollectionUtil.isNotEmpty(witnessessList)){
                for(ApplySignRelation signRelation:witnessessList){
                    if(NO.equals(signRelation.getStatus())){
                        signWitnessesState = APP_FORM_STATE_ZERO;
                        signContractState = APP_FORM_STATE_ZERO;
                        break;
                    }
                }
            }else{
                signWitnessesState = APP_FORM_STATE_ZERO;
            }
            condition.setSignWitnessesState(signWitnessesState);
            //车商状态变更成未签约
            String signCarDealerState = APP_FORM_STATE_ONE;//车商是否签约
            List<ApplySignRelation> dealerList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                    .eq(ApplySignRelation::getBusinessNo,condition.getContractNo()).eq(ApplySignRelation::getCustRole, CAR_DEALER));
            if(CollectionUtil.isNotEmpty(dealerList)){
                for(ApplySignRelation signRelation:dealerList){
                    if(NO.equals(signRelation.getStatus())){
                        signCarDealerState = APP_FORM_STATE_ZERO;
                        signContractState = APP_FORM_STATE_ZERO;
                        break;
                    }
                }
            }else{
                signCarDealerState = APP_FORM_STATE_ZERO;
            }
            condition.setSignCarDealerState(signCarDealerState);
        }else{
            //表示没有生成电子签合同，所以是否必须电子签为否
            condition.setIsNeedSign(APP_FORM_STATE_ZERO);
            signContractState = APP_FORM_STATE_ZERO;
        }
        //总状态新增车辆信息 录入状态
        if (APP_FORM_STATE_ZERO.equals(condition.getCarState()) && APP_FORM_STATE_ONE.equals(signContractState)){
            signContractState = APP_FORM_STATE_ZERO;
        }
        condition.setSignContractState(signContractState);
        return condition;
    }

    /**
     * 获取放款签约见证人
     * @param user
     * @return
     */
    @Override
    public LoanAppSignPeopleListCondition getWitnessesInfo(AfsUser user,String applyNo) {

        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        Assert.isTrue(contractInfo != null,"合同信息不允许为空");
        LoanAppSignPeopleListCondition witnessesCondition = new LoanAppSignPeopleListCondition();
        witnessesCondition.setContractNo(contractInfo.getContractNo());
        witnessesCondition.setApplyNo(applyNo);
        witnessesCondition.setCustRole(WITNESSES);
        // 签约关联关系表
        List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                .eq(ApplySignRelation::getBusinessNo,witnessesCondition.getContractNo())
                .eq(ApplySignRelation::getCustRole,WITNESSES).orderByDesc(ApplySignRelation::getCreateTime));
        if(CollectionUtil.isNotEmpty(signRelationList)){
            for(ApplySignRelation relation: signRelationList){
                if(NO.equals(relation.getStatus()) || StringUtils.isBlank(relation.getStatus())){
                    witnessesCondition.setSignState(NO);
                    witnessesCondition.setId(user.getId());
                    break;
                }else{
                    witnessesCondition.setSignState(YES);
                    witnessesCondition.setSignDate(ObjectUtil.isNotEmpty(relation.getSignDate()) ? relation.getSignDate() : relation.getUpdateTime());
                    witnessesCondition.setId(relation.getCustId());
                }
            }
        }else{
            witnessesCondition.setSignState(NO);
            witnessesCondition.setId(user.getId());
        }
        // 通过用户ID查询用户信息
        IResponse vo = applyAdminService.getUserInfoByUserId(witnessesCondition.getId());
        if("0000".equals(vo.getCode())){
            Map map = (Map) vo.getData();
            witnessesCondition.setCustName((String) map.get("userRealName"));//客户姓名
            witnessesCondition.setCertNo((String) map.get("identityNumber"));//证件号码
            witnessesCondition.setTelPhone((String) map.get("phone"));//手机号码
        }
        return witnessesCondition;
    }

    /**
     * 获取放款签约车商信息
     * @param applyNo
     * @param applyCarInvoice
     * @return
     */
    @Override
    public LoanAppSignPeopleListCondition geCarDealerInfo(String applyNo,ApplyCarInvoice applyCarInvoice) {

        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        Assert.isTrue(contractInfo != null,"合同信息不允许为空");
        LoanAppSignPeopleListCondition carDealerCondition = new LoanAppSignPeopleListCondition();
        carDealerCondition.setApplyNo(applyNo);
        carDealerCondition.setCustRole(CAR_DEALER);
        if(applyCarInvoice !=null){
            carDealerCondition.setCustName(applyCarInvoice.getSaleName());//客户姓名
            carDealerCondition.setCertNo(applyCarInvoice.getSaleCertNo());//证件号码
            carDealerCondition.setTelPhone(applyCarInvoice.getSalePhone());//手机号码
            carDealerCondition.setId(applyCarInvoice.getId());//签约人主键id
            carDealerCondition.setContractNo(contractInfo.getContractNo());
            //关联签约关系表
            if(StringUtils.isNotBlank(carDealerCondition.getContractNo())){
                List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda()
                        .eq(ApplySignRelation::getCustId, carDealerCondition.getId())
                        .eq(ApplySignRelation::getBusinessNo,carDealerCondition.getContractNo())
                        .eq(ApplySignRelation::getCustRole,carDealerCondition.getCustRole()));
                if(signRelationList !=null && signRelationList.size()>0){
                    for(ApplySignRelation relation: signRelationList){
                        if(NO.equals(relation.getStatus()) || StringUtils.isBlank(relation.getStatus())){
                            carDealerCondition.setSignState(NO);
                            break;
                        }else{
                            carDealerCondition.setSignState(YES);
                            carDealerCondition.setSignDate(ObjectUtil.isNotEmpty(relation.getSignDate()) ? relation.getSignDate() : relation.getUpdateTime());
                        }
                    }
                }
            }else{
                carDealerCondition.setSignState(NO);
            }
            return carDealerCondition;
        }
        return carDealerCondition;
    }
    /**
     * 获取放款flag 附件状态
     * @param condition
     * @return
     */
    @Override
    public LoanAppFormStateCondition getLoanAttachmentsArea(LoanAppFormStateCondition condition,ApplyOrderInfo orderInfoByApplyNo) {
        //订单主表
        ApplyCarDetails carDetailsByApplyNo = this.applyCarDetailsService.getCarDetailsByApplyNo(condition.getApplyNo());
        if (ObjectUtil.isNotNull(carDetailsByApplyNo)) {
            if(CarType.COMMERCIAL_VEHICLE.getIndex().equals(orderInfoByApplyNo.getCarType())){
                if(ObjectUtil.isNotNull(carDetailsByApplyNo.getVehicleMadeDate())){
                    condition.setOtherState(APP_FORM_STATE_ONE);
                }else{
                    condition.setOtherState(APP_FORM_STATE_ZERO);
                }
            }
        }
        //获取影像件规则数据
        CaseSubmitInfoCondition caseSubmitInfoCondition = new CaseSubmitInfoCondition();
        caseSubmitInfoCondition.setApplyNo(condition.getApplyNo());
        IResponse<FilterParamsVO> paramsVOIResponse = filterParamsController.getFilterParams(caseSubmitInfoCondition);
        FilterParamsVO filterParamsVO = paramsVOIResponse.getData();
        Set<String> errorMsgSet= new HashSet<>();
        String busiNo = condition.getBusiNo();
        String busiType = condition.getBusiType();
        JSONObject json = (JSONObject) JSONObject.toJSON(filterParamsVO);
        log.info("放款规则：{}",JSONObject.toJSONString(filterParamsVO));
        List<ComAttachmentManagement> managementList = comAttaManageService.getManagementByRuleGroup(json, busiType);
        log.info("查询请款状态接口 影像件信息:{} {} {} {}", condition.getApplyNo(), JSONObject.toJSONString(condition), json.toJSONString(), JSONArray.toJSON(managementList));
        List<String> status = new ArrayList<>();
        status.add(FileStatusEnum.DRAFT.getCode());
        status.add(FileStatusEnum.STANDARD.getCode());
        status.add(FileStatusEnum.REVISE.getCode());
        status.add(FileStatusEnum.WAITAPPROVE.getCode());
        for (ComAttachmentManagement comAttachmentManagement : managementList) {
            List<ComAttachmentFile> fileList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda().
                    eq(ComAttachmentFile::getAttachmentCode, String.valueOf(comAttachmentManagement.getId()))
                    .eq(ComAttachmentFile::getBusiNo, busiNo)
                    .and(wrapper -> wrapper.in(ComAttachmentFile::getFileStatus, status)
                            .or()
                            .eq(ComAttachmentFile::getIsElectronic, IsElectronicEnum.YES.getCode()).ne(ComAttachmentFile::getFileStatus, FileStatusEnum.DISCARD.getCode()))
            );
            comAttachmentManagement.setFileList(fileList);
            if (comAttachmentManagement.getAttachmentClass() == AttachmentClassEnum.SUBCLASS.getCode() && comAttachmentManagement.getNeedNums() > fileList.size()) {
                errorMsgSet.add(comAttachmentManagement.getAttachmentName());
            }
        }

        Map<Long, List<ComAttachmentManagement>> subclassAttachmentManagementMaps = managementList.stream()
                .collect(Collectors.groupingBy(ComAttachmentManagement::getParentId));
        for (ComAttachmentManagement management : managementList) {
            if (management.getAttachmentClass() == AttachmentClassEnum.GROUP.getCode()) {
                int filenums = 0;
                List<ComAttachmentManagement> attachmentManagementList = subclassAttachmentManagementMaps.get(management.getId());
                if (!CollectionUtils.isEmpty(attachmentManagementList)) {
                    for (ComAttachmentManagement comAttachmentManagement : attachmentManagementList) {
                        if (comAttachmentManagement.getFileList().size() > 0) {
                            filenums = filenums + 1;
                        }
                    }
                    if (management.getNeedNums() > filenums) {
                        errorMsgSet.add(management.getAttachmentName());
                    }
                }
            }
        }

        if(errorMsgSet.size()>0){
            condition.setImageAttachmentsState(APP_FORM_STATE_ZERO);
            condition.setErrorMsg(errorMsgSet.toString());
        }else{
            condition.setImageAttachmentsState(APP_FORM_STATE_ONE);
        }
        return condition;
    }

    /**
     * 人脸识别接口
     * @param condition
     * @return
     */
    @Override
    public IResponse signToFace(ApplyAppFaceCondition condition) {

        JSONObject json = new JSONObject();
        //根据客户id判断角色，拿到角色对应小类标识记录
        if(WITNESSES.equals(condition.getCustRole())){
            IResponse comAttach = applyAdminService.getUserFileByUserId(condition.getCustId().toString());
            if("0000".equals(comAttach.getCode())){
                Map map = (Map) comAttach.getData();
                ComAttachmentFile attachmentFile = new ComAttachmentFile();
                attachmentFile.setFileId((String) map.get("fileId"));
                attachmentFile.setFileName((String) map.get("fileName"));
                byte[] fileByte = attachmentFileService.downloadAttachmentFile(attachmentFile);
                try {
                    if (fileByte.length > 0) {
                        String databaseImageContent = Base64.encodeBase64String(fileByte);
                        json.put("databaseImageContent", databaseImageContent);//被对比图片
                    }
                } catch (Exception e) {
                    log.error("文件下载失败:" + e.getMessage());
                }
            }else{
                return IResponse.fail("见证人脸对比图片不能为空");
            }
        }else{
            List<ApplySignRelation> signRelationList = applySignRelationService.list(Wrappers.<ApplySignRelation>query().lambda().eq(ApplySignRelation::getCustId, condition.getCustId()));
            if(signRelationList!=null && signRelationList.size()>0){
                ApplySignRelation relation = signRelationList.get(0);
                // 车商
                if(CAR_DEALER.equals(relation.getCustRole())){
                    ComAttachmentManagement comAttachmentManagement = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                            .eq(ComAttachmentManagement::getUniqueCode, CertTypePictureEnum.CONTRACT_INVOICE_ID_CARD_FRONT.getIndex())
                            .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.LOAN_APPLY.getCode()));
                    Assert.isTrue(comAttachmentManagement != null,"车商的客户信息不存在");
                    ApplyContractInfo contractInfo = applyContractInfoService.getOne(Wrappers.<ApplyContractInfo>query().lambda().eq(ApplyContractInfo::getApplyNo, condition.getApplyNo()));
                    Assert.isTrue(contractInfo != null,"合同号不能为空");
                    List<ComAttachmentFile> comAttachmentFileList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(ComAttachmentFile::getBusiNo, contractInfo.getContractNo())
                            .eq(ComAttachmentFile::getAttachmentCode,comAttachmentManagement.getId().toString()).orderByDesc(ComAttachmentFile::getCreateTime));
                    if(comAttachmentFileList !=null && comAttachmentFileList.size()>0){
                        ComAttachmentFile attachmentFile = comAttachmentFileList.get(0);
                        byte[] fileByte = attachmentFileService.downloadAttachmentFile(attachmentFile);
                        try {
                            if (fileByte.length > 0) {
                                String databaseImageContent = Base64.encodeBase64String(fileByte);
                                json.put("databaseImageContent", databaseImageContent);//被对比图片
                            }
                        } catch (Exception e) {
                            log.error("文件下载失败:" + e.getMessage());
                        }
                    }else{
                        return IResponse.fail("人脸对比图片不能为空");
                    }
                }else{
                    ComAttachmentManagement comAttachmentManagement = getComAttachmentManagement(condition.getCustId());
                    if(comAttachmentManagement == null){
                        return IResponse.fail("客户信息不存在");
                    }
                    List<ComAttachmentFile> comAttachmentFileList = attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo())
                            .eq(ComAttachmentFile::getAttachmentCode,comAttachmentManagement.getId().toString()).orderByDesc(ComAttachmentFile::getCreateTime));
                    if(comAttachmentFileList !=null && comAttachmentFileList.size()>0){
                        ComAttachmentFile attachmentFile = comAttachmentFileList.get(0);
                        byte[] fileByte = attachmentFileService.downloadAttachmentFile(attachmentFile);
                        try {
                            if (fileByte.length > 0) {
                                String databaseImageContent = Base64.encodeBase64String(fileByte);
                                json.put("databaseImageContent", databaseImageContent);//被对比图片
                            }
                        } catch (Exception e) {
                            log.error("文件下载失败:" + e.getMessage());
                        }
                    }else{
                        return IResponse.fail("人脸对比图片不能为空");
                    }
                }
            }
        }
        json.put("queryImagePackage", condition.getQueryImagePackage());
        json.put("databaseImageType","1");
        return IResponse.success(json);
    }

    /**
     * 人脸识别面签照上传
     * @param info
     * @return
     */
    @Override
    public IResponse faceToFaceSign(ApplyCustBaseInfo info,byte[] imageByte,ApplyAppFaceCondition condition) {
        // 放款申请
        if(BusiNodeEnum.LOAN_APPLY.getCode().equals(condition.getBusiNode())){
            // 承租人
            String file = AppEnum.LOAN_APPLY_FACE_PHOTO.getIndex();
            String fileName = AppEnum.LOAN_APPLY_FACE_PHOTO.getName();
            // 共同承租人
            if(COMMON_BORROWER.equals(info.getCustRole())){
                file = AppEnum.LOAN_APPLY_BORROWER_FACE_PHOTO.getIndex();
                fileName = AppEnum.LOAN_APPLY_BORROWER_FACE_PHOTO.getName();
            }
            // 担保人
            else if(GUARANTOR.equals(info.getCustRole())){
                file = AppEnum.LOAN_APPLY_GUARANTOR_FACE_PHOTO.getIndex();
                fileName = AppEnum.LOAN_APPLY_GUARANTOR_FACE_PHOTO.getName();
            }
            ApplyContractInfo contractInfo = applyContractInfoService.getOne(Wrappers.<ApplyContractInfo>query().lambda().eq(ApplyContractInfo::getApplyNo, info.getApplyNo()));
            Assert.isTrue(contractInfo!=null,"合同信息不能为空！");
            String md5 = imageUploadUtil.uploadImageForSignMd5(fileName, "jpg",imageByte);
            this.setImageStore(file, md5, contractInfo.getContractNo(),condition);
        }else{
            // 承租人
            String file = AppEnum.ORDER_APPLY_FACE_PHOTO.getIndex();
            String fileName = AppEnum.ORDER_APPLY_FACE_PHOTO.getName();
            // 共同承租人
            if(COMMON_BORROWER.equals(info.getCustRole())){
                file = AppEnum.ORDER_APPLY_BORROWER_FACE_PHOTO.getIndex();
                fileName = AppEnum.ORDER_APPLY_BORROWER_FACE_PHOTO.getName();
            }
            // 担保人
            else if(GUARANTOR.equals(info.getCustRole())){
                file = AppEnum.ORDER_APPLY_GUARANTOR_FACE_PHOTO.getIndex();
                fileName = AppEnum.ORDER_APPLY_GUARANTOR_FACE_PHOTO.getName();
            }
            String md5 = imageUploadUtil.uploadImageForSignMd5(fileName, "jpg",imageByte);
            this.setImageStore(file, md5, info.getApplyNo(),condition);
        }
        return IResponse.success("上传成功");
    }


    /**
     * 保存MD5到file
     *
     * @param uniqueCode
     */
    public void setImageStore(String uniqueCode, String md5, String params,ApplyAppFaceCondition condition) {
        //查询manage文件表内的数据
        if(StringUtils.isBlank(condition.getBusiNode())){
            condition.setBusiNode(BusiNodeEnum.ORDER_APPLY.getCode());
        }
        ComAttachmentManagement comAttachmentManagement = this.managementMapper.selectOne(Wrappers.<ComAttachmentManagement>query().lambda()
                .eq(ComAttachmentManagement::getUniqueCode, uniqueCode)
                .eq(ComAttachmentManagement::getBusiNode,condition.getBusiNode()));
        //先查询
        if(ObjectUtil.isNotNull(comAttachmentManagement)){
            List<ComAttachmentFile> fileList = this.attachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getBusiNo, params)
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(comAttachmentManagement.getId())));
            if(CollectionUtil.isNotEmpty(fileList)){
                fileList.forEach(x -> attachmentFileService.removeById(x.getId()));
                log.info("{}", params, comAttachmentManagement.getAttachmentName() + "删除成功");
            }
            ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
            comAttachmentFile.setBelongNo(params);
            comAttachmentFile.setBusiNo(params);
            comAttachmentFile.setAttachmentCode(String.valueOf(comAttachmentManagement.getId()));
            comAttachmentFile.setAttachmentName(comAttachmentManagement.getAttachmentName());
            comAttachmentFile.setFileStatus(FileStatusEnum.DRAFT.getCode());
            comAttachmentFile.setIsElectronic(IsElectronicEnum.YES.getCode());
            comAttachmentFile.setFileType("jpg");
            comAttachmentFile.setFileId(md5);
            comAttachmentFile.setHistoryVersion(ApplyConstants.ZERO);
            comAttachmentFile.setFileName(comAttachmentManagement.getAttachmentName() + ".jpg");
            comAttachmentFile.setUploadTime(new Date());
            comAttachmentFile.setCreateBy(SecurityUtils.getUsername());
            comAttachmentFile.setFileSource("com_attachment_management");
            this.attachmentFileService.save(comAttachmentFile);
            log.info("{}", params, comAttachmentManagement.getAttachmentName() + "上传成功");
        }
    }

    /**
     * 签名比对
     * @param dto
     */
    public void checkSignResult(CfCaSignatureDto dto){

        byte[] bytes = dto.getSignByte();
        //从图片中获取手写字迹，与预审客户/主共保/见证人姓名做比对
        Socket socket = null;
        int wordSplitMode = 1;
        byte[] writingTrackData = null;
        try {
            socket = new Socket(config.getSocketIp(), config.getPort());
            socket.setSoTimeout(30000);
            byte[] request = ImageUploadUtil.requestToBytes(bytes, dto.getCustName(), wordSplitMode, writingTrackData);
            OutputStream outputStream = socket.getOutputStream();
            outputStream.write(request);
            //接收响应
            InputStream in = socket.getInputStream();
            byte[] b = new byte[1024 * 1024];
            int len = 0;
            String strText = "";
            while ((len = in.read(b)) != -1) {
                strText = new String(b, 0, len);
            }
            strText = "{" + strText.split("\\{")[1];
            JSONObject jsonObject = JSONObject.parseObject(strText);
            boolean pass = Boolean.parseBoolean(jsonObject.getString("pass"));
            outputStream.flush();
            outputStream.close();
            in.close();
            socket.close();
            if(!pass){
                log.warn("{},电子签名与姓名不匹配!{}", dto.getCustName(), dto.getApplyNo());
                throw new AfsBaseException(dto.getCustName()+"签名比对未通过，请重新签署!");
            }
        } catch (IOException e) {
            throw new AfsBaseException("签名比对出错，请重新签署!");
        }
    }

    private void setPersonalCustomerStates(ApplyAppFormStateCondition condition,ApplyCustBaseInfo custBaseInfo) {
        if(PRINCIPAL_BORROWER.equals(custBaseInfo.getCustRole())){
            //主借人详细信息获取
            ApplyCustPersonalDetail applyCustPersonalDetail = custPersonalService.getOne(Wrappers.<ApplyCustPersonalDetail>query().lambda()
                    .eq(ApplyCustPersonalDetail::getCustId, custBaseInfo.getId()));
            if(applyCustPersonalDetail !=null){
                condition.setMainManMaritalStatus(applyCustPersonalDetail.getMaritalStatus());
                applyCustPersonalDetail.setDrivingType("0");
                if(StringUtils.isNotBlank(applyCustPersonalDetail.getDrivingType())){
                    //主借人其他信息录入判断关键字：驾驶证类型
                    condition.setMainManOtherState(APP_FORM_STATE_ONE);
                }

                if(StringUtils.isNotBlank(applyCustPersonalDetail.getUnitName())){
                    //主借人工作信息录入判断关键字：单位名称、工作年限（2024.05.14业务要求去掉）
                    condition.setMainManWorkState(APP_FORM_STATE_ONE);
                }

                if(StringUtils.isNotBlank(custBaseInfo.getCertNo()) && StringUtils.isNotBlank(applyCustPersonalDetail.getNationality()) ){
                    //主借人基本信息录入判断关键字：客户姓名，国籍
                    condition.setMainManBasicState(APP_FORM_STATE_ONE);
                }
            }
            List<ApplyCustAddressDetails> detailsList = applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                    .eq(ApplyCustAddressDetails::getCustId, custBaseInfo.getId()));
            if(detailsList !=null && detailsList.size()>=3){
                //主借人地址信息录入判断关键：至少已录入三条记录（户籍地址，居住地址，单位地址）
                condition.setMainManAddressState(APP_FORM_STATE_ONE);
            }

            //主借人其他信息已录入
            if(APP_FORM_STATE_ONE.equals(condition.getMainManOtherState())
                    //主借人工作信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getMainManWorkState())
                    //主借人地址信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getMainManAddressState())
                    //主借人基本信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getMainManBasicState())){
                //主借人总状态
                condition.setMainManState(APP_FORM_STATE_ONE);
            }
            else {
                condition.setMainManState(APP_FORM_STATE_ZERO);
            }
            condition.setMainManIsLock(custBaseInfo.getIsLock()==null?"":custBaseInfo.getIsLock());
        }
        if(COMMON_BORROWER.equals(custBaseInfo.getCustRole())){
            //共借人详情查询
            ApplyCustPersonalDetail applyCustPersonalDetail = custPersonalService.getOne(Wrappers.<ApplyCustPersonalDetail>query().lambda()
                    .eq(ApplyCustPersonalDetail::getCustId, custBaseInfo.getId()));
            if(applyCustPersonalDetail !=null){

                if(StringUtils.isNotBlank(applyCustPersonalDetail.getUnitName()) && StringUtils.isNotBlank(applyCustPersonalDetail.getPosition())){
                    //共借人工作信息录入判断关键字：单位名称、职务
                    condition.setWereBorrowedWorkState(APP_FORM_STATE_ONE);
                }

                if(StringUtils.isNotBlank(custBaseInfo.getCertNo()) && StringUtils.isNotBlank(applyCustPersonalDetail.getNationality()) ){
                    //共借人基本信息录入判断关键字：客户姓名，国籍
                    condition.setWereBorrowedBasicState(APP_FORM_STATE_ONE);
                }
            }
            //共借人id
            condition.setWereBorrowedId(custBaseInfo.getId());
            if(StringUtils.isBlank(custBaseInfo.getIsLock())){
                condition.setWereBorrowedIsLock(NO);
            }else{
                condition.setWereBorrowedIsLock(custBaseInfo.getIsLock());
            }

            List<ApplyCustAddressDetails> detailsList = applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                    .eq(ApplyCustAddressDetails::getCustId, custBaseInfo.getId()));
            if(detailsList !=null && detailsList.size()>=3){
                //主借人地址信息录入判断关键：至少已录入三条记录（户籍地址，居住地址，单位地址）
                condition.setWereBorrowedAddressState(APP_FORM_STATE_ONE);
            }

            //共借人工作信息已录入
            if( APP_FORM_STATE_ONE.equals(condition.getWereBorrowedWorkState())
                    //共借人地址信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getWereBorrowedAddressState())
                    //共借人基本信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getWereBorrowedBasicState())){
                //共借人总状态
                condition.setWereBorrowedState(APP_FORM_STATE_ONE);
            }
            else {
                condition.setWereBorrowedState(APP_FORM_STATE_ZERO);
            }
            //共借人玄武状态
            if(custBaseInfo.getRiskQueriedFlag()!=null){
                condition.setRiskQueriedBorrowFlag(custBaseInfo.getRiskQueriedFlag());
            }
            condition.setBorrowedRelation(custBaseInfo.getCustRelation());//modify by sijun.yu 2020-12-23 共借人，与主借人关系
        }
        if(GUARANTOR.equals(custBaseInfo.getCustRole())){
            //担保人详情查询
            ApplyCustPersonalDetail applyCustPersonalDetail = custPersonalService.getOne(Wrappers.<ApplyCustPersonalDetail>query().lambda()
                    .eq(ApplyCustPersonalDetail::getCustId, custBaseInfo.getId()));
            if(applyCustPersonalDetail !=null){

                if(StringUtils.isNotBlank(applyCustPersonalDetail.getUnitName())){
                    //担保人工作信息录入判断关键字：单位名称、职务（2024.05.14业务要求去掉）
                    condition.setGuarantorWorkState(APP_FORM_STATE_ONE);
                }

                if(StringUtils.isNotBlank(custBaseInfo.getCertNo()) && StringUtils.isNotBlank(applyCustPersonalDetail.getNationality()) ){
                    //担保人基本信息录入判断关键字：客户姓名，国籍
                    condition.setGuarantorBasicState(APP_FORM_STATE_ONE);
                }
            }
            //担保人id
            condition.setGuarantorId(custBaseInfo.getId());
            if(StringUtils.isBlank(custBaseInfo.getIsLock())){
                condition.setGuarantorIsLock(NO);
            }else{
                condition.setGuarantorIsLock(custBaseInfo.getIsLock());
            }

            List<ApplyCustAddressDetails> detailsList = applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                    .eq(ApplyCustAddressDetails::getCustId, custBaseInfo.getId()));
            if(detailsList !=null && detailsList.size()>=3){
                //担保人地址信息录入判断关键：至少已录入三条记录（户籍地址，居住地址，单位地址）
                condition.setGuarantorAddressState(APP_FORM_STATE_ONE);
            }

            //担保人人工作信息已录入
            if( APP_FORM_STATE_ONE.equals(condition.getGuarantorWorkState())
                    //担保人地址信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getGuarantorAddressState())
                    //担保人基本信息已录入
                    && APP_FORM_STATE_ONE.equals(condition.getGuarantorBasicState())){
                //担保人总状态
                condition.setGuarantorState(APP_FORM_STATE_ONE);
            }
            else {
                condition.setGuarantorState(APP_FORM_STATE_ZERO);
            }
            //担保人玄武状态
            if(custBaseInfo.getRiskQueriedFlag()!=null){
                condition.setRiskQueriedGuarantorFlag(custBaseInfo.getRiskQueriedFlag());
            }
            condition.setGuarantorRelation(custBaseInfo.getCustRelation());//modify by sijun.yu 2020-12-23 保证人，与主借人关系
        }
    }

    private void setEnterpriseCustomerStates(ApplyAppFormStateCondition condition,ApplyCustBaseInfo custBaseInfo) {
        String basicState = APP_FORM_STATE_ZERO;
        String addressState = APP_FORM_STATE_ZERO;
        String totalState = APP_FORM_STATE_ZERO;

        ApplyEnterpriseCustomerDetails detailInfo = enterpriseCustomerDetailsService.lambdaQuery()
                .eq(ApplyEnterpriseCustomerDetails::getApplyNo,custBaseInfo.getApplyNo())
                .eq(ApplyEnterpriseCustomerDetails::getCustId,custBaseInfo.getId()).one();

        if (detailInfo != null) {
            if (StringUtils.isNotBlank(detailInfo.getEnterpriseName())
                    && StringUtils.isNotBlank(detailInfo.getSocunicrtCode())
            && StringUtils.isNotBlank(detailInfo.getCompanyPhone())) {
                basicState = APP_FORM_STATE_ONE;
            }
        }
        Long addressCount = applyCustAddressService.lambdaQuery()
                .eq(ApplyCustAddressDetails::getApplyNo,custBaseInfo.getApplyNo())
                .eq(ApplyCustAddressDetails::getCustId,custBaseInfo.getId()).count();
        if (addressCount != null && addressCount > 1) {
            addressState = APP_FORM_STATE_ONE;
        }
        if (APP_FORM_STATE_ONE.equals(basicState) && APP_FORM_STATE_ONE.equals(addressState)) {
            totalState = APP_FORM_STATE_ONE;
        }
        final String custRole = custBaseInfo.getCustRole();
        if (PRINCIPAL_BORROWER.equals(custRole)) {
            condition.setMainManBasicState(basicState);
            condition.setMainManAddressState(addressState);
            condition.setMainManState(totalState);
            condition.setMainManIsLock(StringUtils.defaultString(custBaseInfo.getIsLock(),NO));
        }
        else if (COMMON_BORROWER.equals(custRole)) {
            condition.setWereBorrowedBasicState(basicState);
            condition.setWereBorrowedAddressState(addressState);
            condition.setWereBorrowedState(totalState);
            condition.setWereBorrowedIsLock(StringUtils.defaultString(custBaseInfo.getIsLock(),NO));
        }
        else if (GUARANTOR.equals(custRole)) {
            condition.setGuarantorBasicState(basicState);
            condition.setGuarantorAddressState(addressState);
            condition.setGuarantorState(totalState);
            condition.setGuarantorIsLock(StringUtils.defaultString(custBaseInfo.getIsLock(),NO));
        }
    }
}
