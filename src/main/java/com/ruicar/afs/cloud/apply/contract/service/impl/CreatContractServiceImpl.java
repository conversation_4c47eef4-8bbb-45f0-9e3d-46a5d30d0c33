package com.ruicar.afs.cloud.apply.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.apply.affilited.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.apply.archive.config.CaseApiConfig;
import com.ruicar.afs.cloud.apply.archive.dto.CaseCarInfo;
import com.ruicar.afs.cloud.apply.archive.dto.ConsultFileInfoDto;
import com.ruicar.afs.cloud.apply.archive.feign.ArchiveFeign;
import com.ruicar.afs.cloud.apply.business.service.ApplySignRelationService;
import com.ruicar.afs.cloud.apply.business.service.TsysParamConfigService;
import com.ruicar.afs.cloud.apply.common.entity.ApplyAddPriceItems;
import com.ruicar.afs.cloud.apply.common.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.apply.common.entity.ApplyBankCard;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarInvoice;
import com.ruicar.afs.cloud.apply.common.entity.ApplyChannelInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyContractInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustAddressDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustBaseInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustContacts;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustPersonalDetail;
import com.ruicar.afs.cloud.apply.common.entity.ApplyEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyOrderInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyPayeeInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyReceiptInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplySignRelation;
import com.ruicar.afs.cloud.apply.common.entity.ApplySubjectInfo;
import com.ruicar.afs.cloud.apply.common.entity.ChannelUniteInfo;
import com.ruicar.afs.cloud.apply.common.entity.PreApproveInfo;
import com.ruicar.afs.cloud.apply.common.enums.GuarantorTypeEnum;
import com.ruicar.afs.cloud.apply.common.enums.IsLeaseNoticeEnum;
import com.ruicar.afs.cloud.apply.common.enums.RepaymentFrequencyEnum;
import com.ruicar.afs.cloud.apply.common.feign.Apply2CaseFeign;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConfig;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConstants;
import com.ruicar.afs.cloud.apply.common.utils.ApplyUtils;
import com.ruicar.afs.cloud.apply.common.utils.CapitalMappingUtils;
import com.ruicar.afs.cloud.apply.contract.condition.LoanAppSignPeopleListCondition;
import com.ruicar.afs.cloud.apply.contract.dto.ApplyCarDeposit;
import com.ruicar.afs.cloud.apply.contract.dto.RepaymentPlanDto;
import com.ruicar.afs.cloud.apply.contract.enums.CertTypeEnum;
import com.ruicar.afs.cloud.apply.contract.enums.GuaranteeTypeEnum;
import com.ruicar.afs.cloud.apply.contract.mapper.ApplyContractInfoMapper;
import com.ruicar.afs.cloud.apply.contract.mapper.ApplySubjectInfoMapper;
import com.ruicar.afs.cloud.apply.contract.service.ApplyAddPriceItemsService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyBankCardService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCarDepositService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCarInvoiceService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyContractInfoService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCustAddressDetailsService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyPayeeInfoService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyReceiptInfoService;
import com.ruicar.afs.cloud.apply.contract.service.ApplySubjectInfoService;
import com.ruicar.afs.cloud.apply.contract.service.CreatContractService;
import com.ruicar.afs.cloud.apply.contract.vo.ApplyCarUnitVo;
import com.ruicar.afs.cloud.apply.contract.vo.ApplyMainCustVO;
import com.ruicar.afs.cloud.apply.contract.vo.ApplyTemplateVO;
import com.ruicar.afs.cloud.apply.contract.vo.BankCardSigningTemplateVO;
import com.ruicar.afs.cloud.apply.contract.vo.BoutiqueItem;
import com.ruicar.afs.cloud.apply.contract.vo.ContractTemplateVO;
import com.ruicar.afs.cloud.apply.contract.vo.DecorationItem;
import com.ruicar.afs.cloud.apply.contract.vo.FinanceItemVO;
import com.ruicar.afs.cloud.apply.contract.vo.GuarantorSignVO;
import com.ruicar.afs.cloud.apply.contract.vo.PreAuthorTemplateVO;
import com.ruicar.afs.cloud.apply.contract.vo.RepaymentVO;
import com.ruicar.afs.cloud.apply.contract.vo.TemplateRuleVO;
import com.ruicar.afs.cloud.apply.pre.approve.service.PreApproveService;
import com.ruicar.afs.cloud.apply.pre.loan.condition.ApplyAppSignPeopleListCondition;
import com.ruicar.afs.cloud.apply.pre.loan.mapper.ChannelUniteInfoMapper;
import com.ruicar.afs.cloud.apply.pre.loan.mq.sender.CaseLoanInfoSender;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyAffiliatedUnitService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCarDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyChannelInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustBaseInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustContactsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustPersonalService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyOrderInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyReportService;
import com.ruicar.afs.cloud.basic.outer.api.common.dto.RepaymentPlanDTO;
import com.ruicar.afs.cloud.basic.outer.api.common.feign.CommonOuterQueryFeign;
import com.ruicar.afs.cloud.basic.outer.api.common.gateway.ContractToApplyGateWay;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinUiContent;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyFinancingItemsService;
import com.ruicar.afs.cloud.bizcommon.business.service.FinMainInfoService;
import com.ruicar.afs.cloud.bizcommon.business.service.FinUiContentService;
import com.ruicar.afs.cloud.bizcommon.cfca.dto.CfCaSignatureDto;
import com.ruicar.afs.cloud.bizcommon.print.condition.ConsultPrintInfoCondition;
import com.ruicar.afs.cloud.bizcommon.print.entity.ComPrintFormManage;
import com.ruicar.afs.cloud.bizcommon.print.enums.ServiceClientTypeEnum;
import com.ruicar.afs.cloud.bizcommon.print.service.ComPrintFormManageService;
import com.ruicar.afs.cloud.bizcommon.print.vo.ChannelInfoUniteInfoVo;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsDefaultDeductCardEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsTypeNumEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.NatureEnterpriseEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.OperateWayNewEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.PayDepositObjectEnum;
import com.ruicar.afs.cloud.common.modules.contract.enums.SignTypeEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.loan.AttachmentDTO;
import com.ruicar.afs.cloud.common.modules.engineering.enums.LoanApplyStatusEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.common.rules.dto.RuleResult;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.enums.apply.AffiliatedWayEnum;
import com.ruicar.afs.cloud.enums.common.BelongingCapitalEnum;
import com.ruicar.afs.cloud.enums.common.CapitalOrderStatusEnum;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.IsDeleteEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.loan.sdk.dto.StatusInfoReqDTO;
import com.ruicar.afs.cloud.loan.sdk.dto.StatusInfoRespDTO;
import com.ruicar.afs.cloud.loan.sdk.dto.VehicleAndCapitalTypeDTO;
import com.ruicar.afs.cloud.loan.sdk.enums.VehicleTypeEnum;
import com.ruicar.afs.cloud.loan.sdk.service.RentLoansService;
import com.ruicar.afs.cloud.loan.sdk.utils.RentLoanUtil;
import com.ruicar.afs.cloud.parameter.commom.enums.CarType;
import com.ruicar.afs.cloud.parameter.commom.enums.CostType;
import com.ruicar.afs.cloud.parameter.commom.enums.CustType;
import com.ruicar.afs.cloud.parameter.commom.enums.OperateWay;
import com.ruicar.afs.cloud.parameter.commom.enums.PaymentMethodEnum;
import com.ruicar.afs.cloud.parameter.commom.enums.SubjectCodeEnum;
import com.ruicar.afs.cloud.parameter.commom.enums.WhetherEnum;
import com.ruicar.afs.cloud.parameter.commom.utils.VinUtils;
import com.ruicar.afs.cloud.product.sdk.dto.FinancialProductDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 生成合同模板实现类
 * @author: sijun.yu
 * @date: 2020/7/23 16:23
 */
@Service
@Slf4j
public class CreatContractServiceImpl extends ServiceImpl<ApplyContractInfoMapper, ApplyContractInfo> implements CreatContractService {

    private static final String CACHE_KEY_SIGN = "lock-afs-contract-sign:";
    private static final String ORDER_KEY_SIGN = "lock-afs-order-sign:";
    private static final String FORMAT = "yyyy年MM月dd日";
    private static final String SHARE_KEY = "lock-afs-best-sign-message:";
    private static final String SHARE_CAPIATL_KEY = "lock-afs-best-sign-capital-message:";

    @Autowired
    private ComPrintFormManageService comPrintFormManageService;
    @Autowired
    private ApplyOrderInfoService orderInfoService;
    @Autowired
    private ApplyContractInfoService applyContractInfoService;
    @Autowired
    private ApplyCarDetailsService applyCarDetailsService;
    @Autowired
    private ApplyCustBaseInfoService applyCustBaseInfoService;
    @Autowired
    private ApplyCostDetailsService applyCostDetailsService;
    @Autowired
    private ApplyChannelInfoService applyChannelInfoService;
    @Autowired
    private ApplyAddPriceItemsService applyAddPriceItemsService;
    @Autowired
    private ApplyFinancingItemsService applyFinancingItemsService;
    @Autowired
    private ApplySignRelationService applySignRelationService;
    @Autowired
    private ApplyCustPersonalService applyCustPersonalServicel;
    @Autowired
    private ApplyCustAddressDetailsService applyCustAddressDetailsService;
    @Autowired
    private AddressService addressService;
    @Autowired
    private ApplyCustContactsService applyCustContactsService;
    @Autowired
    private ApplyBankCardService applyBankCardService;
    @Autowired
    private ApplyAffiliatedUnitService applyAffiliatedUnitService;
    @Autowired
    private PreApproveService preApproveService;
    @Autowired
    private ApplyReportService applyReportService;
    @Autowired
    private ApplyCarInvoiceService applyCarInvoiceService;
    @Autowired
    private ComAttachmentFileService comAttachmentFileService;
    @Autowired
    private ApplyOrderInfoService applyOrderInfoService;
    @Autowired
    private ComAttachmentManagementService comAttachmentManagementService;
    @Autowired
    private ApplySubjectInfoService applySubjectInfoService;
    @Resource
    private ContractToApplyGateWay applyContractFeign;
    @Autowired
    private ApplyConfig applyConfig;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ApplyEnterpriseCustomerDetailsService customerDetailsService;
    @Resource
    private ChannelUniteInfoMapper channelUniteInfoMapper;
    @Resource
    private CommonOuterQueryFeign commonOuterQueryFeign;
    @Resource
    private CaseLoanInfoSender caseLoanInfoSender;
    @Resource
    private ApplyReceiptInfoService applyReceiptInfoService;
    @Autowired
    private ApplyPayeeInfoService applyPayeeInfoService;
    @Resource
    private ApplySubjectInfoMapper applySubjectInfoMapper;
    @Autowired
    private ApplyCarDepositService applyCarDepositService;
    @Autowired
    private FinMainInfoService finMainInfoService;
    @Autowired
    private ApplyEnterpriseCustomerDetailsService detailsService;
    @Autowired
    private TsysParamConfigService tSysParamConfigService;
    @Resource
    private FinUiContentService uiContentService;
    @Autowired
    private RentLoansService rentLoansService;
    @Resource
    private Apply2CaseFeign apply2CaseFeign;
    @Resource
    private ArchiveFeign archiveFeign;
    @Autowired
    private CaseApiConfig caseApiConfig;


    /**
     * 获取模板规则数据
     *
     * @param applyNo
     * @return
     */
    public TemplateRuleVO getTemplateRule(String applyNo, String serviceClientType) {
        log.info("======================= 通过规则获取模板，申请编号为{}，服务端类型为{} ===========================",applyNo,serviceClientType);

        TemplateRuleVO ruleVO = new TemplateRuleVO();
        ApplyOrderInfo orderInfo = orderInfoService.getOrderInfoByApplyNo(applyNo);
        Assert.isTrue(orderInfo != null,applyNo + "订单信息不存在");
        ruleVO.setBusinessType(orderInfo.getBusinessType());
        ruleVO.setAffiliatedWay(orderInfo.getAffiliatedWay());
        ruleVO.setCarNature(orderInfo.getCarNature());
        ruleVO.setCarType(orderInfo.getCarType());
        ruleVO.setCarPurpose(orderInfo.getCarPurpose());
        ruleVO.setOperateWay(orderInfo.getOperateWay());
        ruleVO.setInputType(orderInfo.getInputType());
        ruleVO.setOrderType(orderInfo.getOrderType());// 订单类型
        ruleVO.setRentType(orderInfo.getRentType());// 租赁类型
        String capital = Optional.ofNullable(orderInfo.getBelongingCapital())
                .filter(c -> StrUtil.startWithIgnoreCase(orderInfo.getBelongingCapital(), ApplyConstants.CAPITAL_BANK_PREFIX))
                .orElse(BelongingCapitalEnum.FD.getCode());
        ruleVO.setBelongingCapital(capital);
        ruleVO.setIfPersonalToEnterprise(orderInfo.getIfPersonalToEnterprise());// 是否个人转企业
        // 联合方信息
        ChannelUniteInfo channelUniteInfo = channelUniteInfoMapper.selectOne(Wrappers.<ChannelUniteInfo>query().lambda().eq(ChannelUniteInfo::getApplyNo, applyNo));
        if (channelUniteInfo != null) {
            ruleVO.setUniteName(channelUniteInfo.getUniteName());
            ruleVO.setUniteId(channelUniteInfo.getUniteId());
        }
        // 渠道信息
        ApplyChannelInfo channelInfo = applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        if (channelInfo != null) {
            ruleVO.setChannelName(channelInfo.getChannelName());
            ruleVO.setChannelBelong(channelInfo.getChannelBelong());
            ruleVO.setChannelId(channelInfo.getChannelId());
            ruleVO.setChannelCity(channelInfo.getChannelCity());
            ApplyCarInvoice carInvoice = applyCarInvoiceService.getApplyCarInvoice(applyNo);
            if (carInvoice != null) {
                if (channelInfo.getChannelName().equals(carInvoice.getInvoiceUnit())) {
                    ruleVO.setOfficeEqualDealer("yes");
                }
            }
            ruleVO.setChannelInfo(channelInfo);
        }
        // 车辆信息
        ApplyCarDetails carDetails = applyCarDetailsService.getCarDetailsByApplyNo(applyNo);
        if (carDetails != null) {
            ruleVO.setLicenseProvince(carDetails.getLicenseProvince());
            ruleVO.setLicenseCity(carDetails.getLicenseCity());
            ruleVO.setPracticesCity(carDetails.getPurchaseCity());
            ruleVO.setCarBrand(carDetails.getBrandName());
        }
        // 承租人
        ApplyCustBaseInfo mainBaseInfo = this.applyCustBaseInfoService.getCustBaseInfo(applyNo, ApplyConstants.PRINCIPAL_BORROWER);
        if (ObjectUtil.isNotNull(mainBaseInfo)) {
            ruleVO.setMainCustSignResult(StringUtils.isNotEmpty(mainBaseInfo.getSignResult()) ? mainBaseInfo.getSignResult() : "0");
            ruleVO.setMainCustSignFailNumber(mainBaseInfo.getSignFailNumber() != null ? mainBaseInfo.getSignFailNumber() : 0);
            if (mainBaseInfo.getCustType().equals(ApplyConstants.ENTERPRISE) || (orderInfo != null && ApplyConstants.IF_PERSONAL_TO_ENTERPRISE.equals(orderInfo.getIfPersonalToEnterprise()))) {
                ApplyEnterpriseCustomerDetails detailsServiceOne = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                        .eq(ApplyEnterpriseCustomerDetails::getCustId, mainBaseInfo.getId())
                        .eq(ApplyEnterpriseCustomerDetails::getApplyNo, mainBaseInfo.getApplyNo()));
                ruleVO.setNatureEnterprise(detailsServiceOne.getNatureEnterprise());
            }
            // 该合同是新范本274上线之前（旧版本1）还是之后（新版本2）
            // 获取当前时间并转换为LocalDateTime对象
            LocalDateTime createTimeLocalDate = mainBaseInfo.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            // 创建一个包含时分秒的日期时间对象代表发版日期
            LocalDateTime onlineLocalDateTime = LocalDateTime.of(2024, 4, 25, 22, 0, 0);
            // 默认新版本
            ruleVO.setContractVersion("2");
            if (createTimeLocalDate.isBefore(onlineLocalDateTime)) {
                // 之前代表旧版本
                ruleVO.setContractVersion("1");
            }
        }
        // 是否有第一担保人
        String custGuarantor = applyCustBaseInfoService.isExitsFirstGuarantor(applyNo);
        if(ApplyConstants.YES.equals(custGuarantor)){
            ruleVO.setIsFirstGuarantor("yes");
        }
        //担保人
        List<ApplyCustBaseInfo> guarnBaseInfoList = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.GUARANTOR));
        if (CollectionUtil.isNotEmpty(guarnBaseInfoList)) {
            guarnBaseInfoList.forEach(guarnBase -> {
                if (guarnBase.getCustType().equals(ApplyConstants.PERSONAL)) {
                    GuarantorTypeEnum guarantorTypeEnum = (GuarantorTypeEnum) AfsEnumUtil.getEnum(guarnBase.getFirstGuarantor(), GuarantorTypeEnum.class);
                    switch (guarantorTypeEnum) {
                        //第一担保人
                        case FIRST:
                            ruleVO.setHasBail(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                            break;
                        //第二担保人
                        case SECOND:
                            ruleVO.setHasBailSecond(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                            break;
                        //第三担保人
                        case THIRD:
                            ruleVO.setHasBailThird(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                            break;
                        default:
                            log.info("担保人类型不存在:{}", guarantorTypeEnum);
                    }
                }
            });
        }
        // 银行卡签约改为list
        List<ApplyBankCard> bankCard = applyBankCardService.getBankCardByApplyNo(applyNo);
        for (ApplyBankCard applyBankCard : bankCard) {
            if (AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT).equals(applyBankCard.getIsDefaultDeductCard())) {
                if (bankCard != null) {
                    ruleVO.setBankCode(applyBankCard.getBankCode());
                    ruleVO.setAuthorizeWay(StrUtil.isNotBlank(applyBankCard.getAuthorizeWay()) ? applyBankCard.getAuthorizeWay() : "offline");//默认纸质签约
                } else {
                    ruleVO.setAuthorizeWay("offline");// 默认纸质签约
                }
            }
        }
        // 是否有附加品
        List<FinFinancingItems> itemsList = applyFinancingItemsService.getApplyFinancingItemsList(applyNo);
        if (itemsList != null && itemsList.size() > 0) {
            BigDecimal totalAmt = itemsList.stream().map(FinFinancingItems::getFinanceItemAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalAmt.compareTo(BigDecimal.ZERO) > 0) {
                ruleVO.setHasAppend("yes");
            }
        }
        // 主产品信息
        FinCostDetails costDetails = applyCostDetailsService.getApplyCostDetails(applyNo, CostType.CARAMT.getIndex());
        if (costDetails != null) {
            if (null != costDetails.getRepaymentMethod()) {
                ruleVO.setRepayMode(costDetails.getRepaymentMethod());
                ruleVO.setProductName(costDetails.getProductName());//产品名称
            }
        }
        // 发票信息
        ApplyCarInvoice carInvoice = applyCarInvoiceService.getApplyCarInvoice(applyNo);
        if (carInvoice != null && orderInfo != null) {
            if (ApplyConstants.BUSINESS_TYPE_OLD_CAR.equals(orderInfo.getBusinessType())) {
                ruleVO.setInvoiceNature(carInvoice.getInvoiceNature());
            }
        }
        // 合同信息
        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        log.info("查询合同的条件为:{},查询到的合同信息为:{}",applyNo,JSON.toJSONString(contractInfo));

        if (ObjectUtil.isNotNull(contractInfo) && contractInfo.getStartDate() != null) {
            ruleVO.setIsLoanActive("yes");
        }
        if(ObjectUtil.isNotNull(contractInfo) && StringUtils.isNotEmpty(contractInfo.getAuthorizeWay())){
            log.info("合同信息存在且获取到签约方式数据！");
            ruleVO.setSignWay(contractInfo.getAuthorizeWay());
        }else{
            log.info("合同信息不存在，或者没有获取到签约方式数据！");
        }

        // 主体信息
        ApplySubjectInfo applySubjectInfo = applySubjectInfoMapper.selectOne(new LambdaQueryWrapper<ApplySubjectInfo>().eq(ApplySubjectInfo::getApplyNo, applyNo));
        if (applySubjectInfo != null) {
            ruleVO.setSubjectCode(StringUtils.isNotBlank(applySubjectInfo.getSubjectCode()) ? applySubjectInfo.getSubjectCode() : "");
            ruleVO.setSubjectName(StringUtils.isNotBlank(applySubjectInfo.getSubjectName()) ? applySubjectInfo.getSubjectName() : "");
        }
        ruleVO.setServiceClientType(serviceClientType);
        log.info("合同模板规则：{}, 申请编号:{}", JSONObject.toJSONString(ruleVO), applyNo);
        return ruleVO;
    }

    @Override
    public List<ComAttachmentFile> bankCardSignFile(String applyNo, String bankCode) {
        // 组装模板需要的参数
        ApplyBankCard applyBankCard = applyBankCardService.getBankCardByApplyNo(applyNo).stream().findAny().get();
        Assert.isTrue(ObjectUtils.isNotEmpty(applyBankCard), "模板打印参数为空");
        BankCardSigningTemplateVO bankCardSigningTemplateVO = Convert.convert(BankCardSigningTemplateVO.class, applyBankCard);
        JSONObject json = JSON.parseObject(JSON.toJSONString(bankCardSigningTemplateVO));
        // 规则VO
        TemplateRuleVO ruleVO = new TemplateRuleVO();
        ruleVO.setBankCode(bankCode);
        ruleVO.setServiceClientType(AfsEnumUtil.key(ServiceClientTypeEnum.APP));
        JSONObject jsonRule = JSON.parseObject(JSON.toJSONString(ruleVO));
        List<ComAttachmentFile> fileList = comPrintFormManageService.batchPrint(json, jsonRule, applyNo, "printBankSign", AfsEnumUtil.key(ServiceClientTypeEnum.APP));
        return fileList;
    }


    /**
     * 合同信息录入-合同模板参数
     *
     * @param applyNo
     * @return
     */
    @Override
    public ContractTemplateVO getContractTemplateParam(String applyNo, String serviceClientType) {

        BigDecimal purchaseTaxAmt = BigDecimal.ZERO; //购置税
        BigDecimal insuranceAmt = BigDecimal.ZERO; //保险
        BigDecimal gpsAmt = BigDecimal.ZERO; //gps加融
        BigDecimal jpzhAmt = BigDecimal.ZERO;//精品装潢
        //保证金
        BigDecimal bond = BigDecimal.ZERO;
        //保险金额
        BigDecimal rance = BigDecimal.ZERO;
        //精品+gps
        BigDecimal equipment = BigDecimal.ZERO;
        // 购置税
        BigDecimal purchaseTax = BigDecimal.ZERO;
        // 其他(费用)
        BigDecimal otherAmt = BigDecimal.ZERO;
        // 模板VO
        ContractTemplateVO templateVO = new ContractTemplateVO();
        //贷款知情函（LCV）
        List<DecorationItem> items = new ArrayList();
        DecorationItem decorationItem1 = new DecorationItem();
        DecorationItem decorationItem2 = new DecorationItem();
        DecorationItem decorationItem3 = new DecorationItem();
        DecorationItem decorationItem4 = new DecorationItem();
        DecorationItem decorationItem5 = new DecorationItem();
        items.add(decorationItem1);
        items.add(decorationItem2);
        items.add(decorationItem3);
        items.add(decorationItem4);
        items.add(decorationItem5);
        //附加贷进件申请
        List<FinFinancingItems> zhFinItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo).eq(FinFinancingItems::getFinanceItemCode, ApplyConstants.DECORATE));
        //附加贷实际成交价
        List<ApplyAddPriceItems> zhAddItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                .eq(ApplyAddPriceItems::getApplyNo, applyNo).eq(ApplyAddPriceItems::getFinanceItemCode, ApplyConstants.DECORATE));
        if (zhFinItems.size() > 0) {
            //装潢小类金额
            List<FinFinancingItems> zhFinFinacingItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                    .eq(FinFinancingItems::getUpperId, zhFinItems.get(0).getId()));
            for (int i = 0; i < zhFinFinacingItems.size(); i++) {
                items.get(i).setFinanceItemName(zhFinFinacingItems.get(i).getFinanceItemName());
                items.get(i).setFinanceItemAmt(zhFinFinacingItems.get(i).getFinanceItemAmt() == null ? BigDecimal.ZERO : zhFinFinacingItems.get(i).getFinanceItemAmt());
            }
        }
        if (zhAddItems.size() > 0) {
            //装潢小类实际成交价
            List<ApplyAddPriceItems> zhAddFinacingItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                    .eq(ApplyAddPriceItems::getUpperId, zhAddItems.get(0).getId()));
            for (int i = 0; i < zhAddFinacingItems.size(); i++) {
                items.get(i).setAddFinanceAmt(zhAddFinacingItems.get(i).getAddFinanceAmt() == null ? BigDecimal.ZERO : zhAddFinacingItems.get(i).getAddFinanceAmt());
            }
        }
        templateVO.setZhItems(items);
        //贷款知情确认函二手车
        BoutiqueItem boutiqueItem = new BoutiqueItem();
        //精品附加贷
        List<FinFinancingItems> jpFinFinacingItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo).eq(FinFinancingItems::getFinanceItemCode, ApplyConstants.BOUTIQUE));
        //精品小类
        if (jpFinFinacingItems.size() > 0) {
            List<FinFinancingItems> jpFinItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                    .eq(FinFinancingItems::getUpperId, jpFinFinacingItems.get(0).getId().toString()));
            if (jpFinItems.size() > 0) {
                for (FinFinancingItems financingItems : jpFinItems) {
                    if (financingItems.getFinanceItemCode().equals(ApplyConstants.NAVIGATION)) {
                        boutiqueItem.setFinanceItemAmt1(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.FILM)) {
                        boutiqueItem.setFinanceItemAmt2(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.TACHOGRAPH)) {
                        boutiqueItem.setFinanceItemAmt3(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.INSOLE_COVER)) {
                        boutiqueItem.setFinanceItemAmt4(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.SOUND)) {
                        boutiqueItem.setFinanceItemAmt5(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.DUST_COVER)) {
                        boutiqueItem.setFinanceItemAmt6(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.CAR_COVER)) {
                        boutiqueItem.setFinanceItemAmt7(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.CAR_DISINFECTION)) {
                        boutiqueItem.setFinanceItemAmt8(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.DEEP_DISINFECTION)) {
                        boutiqueItem.setFinanceItemAmt9(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.POLISH_AND_WAXE)) {
                        boutiqueItem.setFinanceItemAmt10(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.HEADLIGHT)) {
                        boutiqueItem.setFinanceItemAmt11(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    }
                }
            }
        }
        //精品实际成交价
        List<ApplyAddPriceItems> jpAddFinacingItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                .eq(ApplyAddPriceItems::getApplyNo, applyNo).eq(ApplyAddPriceItems::getFinanceItemCode, ApplyConstants.BOUTIQUE));
        //精品实际成交价小类
        if (jpAddFinacingItems.size() > 0) {
            List<ApplyAddPriceItems> jpAddItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                    .eq(ApplyAddPriceItems::getUpperId, jpAddFinacingItems.get(0).getId().toString()));
            if (jpAddItems.size() > 0) {
                for (ApplyAddPriceItems addPriceItems : jpAddItems) {
                    if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.NAVIGATION)) {
                        boutiqueItem.setAddFinanceAmt1(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.FILM)) {
                        boutiqueItem.setAddFinanceAmt2(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.TACHOGRAPH)) {
                        boutiqueItem.setAddFinanceAmt3(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.INSOLE_COVER)) {
                        boutiqueItem.setAddFinanceAmt4(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.SOUND)) {
                        boutiqueItem.setAddFinanceAmt5(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.DUST_COVER)) {
                        boutiqueItem.setAddFinanceAmt6(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.CAR_COVER)) {
                        boutiqueItem.setAddFinanceAmt7(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.CAR_DISINFECTION)) {
                        boutiqueItem.setAddFinanceAmt8(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.DEEP_DISINFECTION)) {
                        boutiqueItem.setAddFinanceAmt9(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.POLISH_AND_WAXE)) {
                        boutiqueItem.setAddFinanceAmt10(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.HEADLIGHT)) {
                        boutiqueItem.setAddFinanceAmt11(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    }
                }
            }
        }
        //保险费用合计
        List<FinFinancingItems> finItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda().eq(FinFinancingItems::getApplyNo, applyNo));
        if (finItems.size()>0) {
            for (FinFinancingItems financingItems : finItems) {
                if (CostType.SYXAMT.getIndex().equals(financingItems.getFinanceItemCode()) || CostType.JQXAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    rance = rance.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.JPAMT.getIndex().equals(financingItems.getFinanceItemCode()) || CostType.GPSAMT.getIndex().equals(financingItems.getFinanceItemCode())){
                    equipment = equipment.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.GZSAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    purchaseTax = purchaseTax.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.OTHERAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    otherAmt = otherAmt.add(financingItems.getFinanceItemAmt());
                }
            }
        }
        templateVO.setJpItems(boutiqueItem);
        // 订单信息
        ApplyOrderInfo orderInfo = orderInfoService.getOrderInfoByApplyNo(applyNo);
        orderInfo.setCarTypeName(ApplyUtils.dicData(orderInfo.getCarType(), "carType"));
        orderInfo.setBusinessTypeName(ApplyUtils.dicData(orderInfo.getBusinessType(), "businessType"));
        orderInfo.setCarPurposeName(ApplyUtils.dicData(orderInfo.getCarPurpose(), "carPurpose"));
        orderInfo.setBelongingCapitalName(ApplyUtils.dicData(orderInfo.getBelongingCapital(), "belongingCapitalName"));
        orderInfo.setOperateWay(OperateWay.getName(orderInfo.getOperateWay()));
        orderInfo.setRiskPassDateStr(DatePattern.CHINESE_DATE_FORMAT.format(orderInfo.getRiskPassDate()));
        templateVO.setOrder(orderInfo);
        //发票信息
        ApplyCarInvoice applyCarInvoice = applyCarInvoiceService.getApplyCarInvoice(applyNo);
        templateVO.setCarInvoice(applyCarInvoice);
        // 渠道信息
        ApplyChannelInfo channelInfo = applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        channelInfo.setReceiptBankName(channelInfo.getReceiptBankCode());
        JSONObject jsonObject = applyChannelInfoService.getChannelDetailInfo(channelInfo.getChannelId(), "");
        Assert.notNull(jsonObject, "经销商信息查询失败!");
        JSONObject channelBaseInfo = jsonObject.getJSONObject("data").getJSONObject("channelBaseInfo");
        Assert.notNull(channelBaseInfo, "经销商信息查询失败!");
        ChannelBaseInfo channelBase = JSONObject.parseObject(JSONObject.toJSONString(channelBaseInfo),ChannelBaseInfo.class);
        channelInfo.setSocUniCrtCode(channelBase.getSocUniCrtCode());
        channelInfo.setChannelAdmin(channelBase.getChannelAdmin());
        channelInfo.setChannelAdminTel(channelBase.getChannelAdminTel());
        if(!ObjectUtil.isNull(channelBase.getChannelProvince()) && !ObjectUtil.isNull(channelBase.getChannelCity()) && !ObjectUtil.isNull(channelBase.getChannelAddress())){
            StringBuffer str = new StringBuffer();
            if (StringUtil.isNotEmpty(channelBase.getChannelProvince())) {
                str.append(this.provinceOrCity(channelBase.getChannelProvince()));
            }
            if (StringUtil.isNotEmpty(channelBase.getChannelCity())) {
                str.append(this.provinceOrCity(channelBase.getChannelCity()));
            }
            if (StringUtil.isNotEmpty(channelBase.getChannelCity())) {
                str.append(this.provinceOrCity(channelBase.getChannelCity()));
            }
            channelInfo.setChannelAddress(str + channelBase.getChannelAddress() == null ? "" : channelBase.getChannelAddress());
        }else {
            channelInfo.setChannelAddress(channelBase.getChannelAddress());
        }
        templateVO.setChannel(channelInfo);
        // 合同信息
        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        if (null != contractInfo.getLoanDate()) {
            contractInfo.setLoanTime(DatePattern.CHINESE_DATE_FORMAT.format(contractInfo.getLoanDate()));
        }
        if (null != contractInfo.getStartDate()) {
            contractInfo.setStartTime(DatePattern.CHINESE_DATE_FORMAT.format(contractInfo.getStartDate()));
        }
        templateVO.setContract(contractInfo);
        // 车辆信息
        ApplyCarDetails carDetails = applyCarDetailsService.getCarDetailsByApplyNo(applyNo);
        templateVO.setCarDetails(carDetails);
        // 主借人信息
        ApplyMainCustVO mainCustVO = this.applyMainCustVO(applyNo, ApplyConstants.PRINCIPAL_BORROWER, true);
        templateVO.setMainCust(mainCustVO);
        ApplyCarUnitVo applyCarUnitVo = new ApplyCarUnitVo();
        applyCarUnitVo.setName(mainCustVO.getCustName());
        applyCarUnitVo.setCode(mainCustVO.getCertNo());
        applyCarUnitVo.setAddress(mainCustVO.getMailingAddress());
        applyCarUnitVo.setPhone(mainCustVO.getTelPhone());
        templateVO.setCarUnit(applyCarUnitVo);

        // 担保人签章信息
        List<ApplyMainCustVO> applyMainCustInfo = this.applyGuaranteeCustVO(applyNo, ApplyConstants.GUARANTOR, false);
        GuarantorSignVO guarantorSignVO = new GuarantorSignVO();
        guarantorSignVO.setBailCustFir("");
        guarantorSignVO.setBailCustSco("");
        guarantorSignVO.setBailCustThr("");
        log.info("保证人个数：" + applyMainCustInfo.size());
        if (null != applyMainCustInfo) {
            if (applyMainCustInfo.size() >= 1) {
                guarantorSignVO.setBailCustFir("保证人一签章:__________________");
            }
            if (applyMainCustInfo.size() >= 2) {
                guarantorSignVO.setBailCustSco("保证人二签章:__________________");
            }
            if (applyMainCustInfo.size() >= 3) {
                guarantorSignVO.setBailCustThr("保证人三签章:__________________");
            }
        }
        templateVO.setGuarantorSignature(guarantorSignVO);
        // 担保人信息..
        List<ApplyMainCustVO> bailCustVO = this.applyGuaranteeCustVO(applyNo, ApplyConstants.GUARANTOR, false);
        //担保人信息顺位 第一担保人--> 个人 --> 企业
        List<ApplyMainCustVO> collectBailCustList = bailCustVO.stream().sorted(Comparator.comparing(ApplyMainCustVO::getIsFirstGuarantor).reversed()
                .thenComparing(Comparator.comparing(ApplyMainCustVO::getCustType))
                .thenComparing(Comparator.comparing(ApplyMainCustVO::getFirstGuarantor)))
                .collect(Collectors.toList());
        if(bailCustVO.size() > 3){
            collectBailCustList = collectBailCustList.subList(0,4);
        }
        //担保人排序完成后，顺位赋值
        for (int i = 0; i < collectBailCustList.size(); i++) {
            collectBailCustList.get(i).setFirstGuarantor("0"+(i+1));
        }
        log.info("collectBailCustList-->{}",JSONObject.toJSONString(collectBailCustList));
        ApplyCustBaseInfo one = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER)
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo));
        if (one.getCustType().equals(ApplyConstants.ENTERPRISE)
                || ApplyConstants.IF_PERSONAL_TO_ENTERPRISE.equals(orderInfo.getIfPersonalToEnterprise())) {
            ApplyEnterpriseCustomerDetails detailsServiceOne = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                    .eq(ApplyEnterpriseCustomerDetails::getCustId,one.getId())
                    .eq(ApplyEnterpriseCustomerDetails::getApplyNo,one.getApplyNo()));
            if (detailsServiceOne.getNatureEnterprise().equals(ApplyConstants.GTGSH_COMPANY)){
                for (ApplyMainCustVO guarantee:bailCustVO) {
                    //判断担保人类型是否为企业 为企业时查询企业信息表
                    if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                        ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                                .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                        if (ObjectUtils.isNotEmpty(enterprise)) {
                            guarantee.setCustName(enterprise.getEnterpriseName());
                            guarantee.setCertNo(enterprise.getSocunicrtCode());
                            guarantee.setTelPhone(enterprise.getCompanyPhone());
                        }
                    }
                    if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust1(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust2(guarantee);
                    }
                    if ("04".equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust3(guarantee);
                    }
                }
            } else {
                for (ApplyMainCustVO guarantee : collectBailCustList) {
                    //判断担保人类型是否为企业 为企业时查询企业信息表
                    if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                        ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                                .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                        if (ObjectUtils.isNotEmpty(enterprise)) {
                            guarantee.setCustName(enterprise.getEnterpriseName());
                            guarantee.setCertNo(enterprise.getSocunicrtCode());
                            guarantee.setTelPhone(enterprise.getCompanyPhone());
                        }
                    }
                    if (GuaranteeTypeEnum.DBR1.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust1(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust2(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust3(guarantee);
                    }
                }
            }
        }else {
            for (ApplyMainCustVO guarantee : collectBailCustList) {
                //判断担保人类型是否为企业 为企业时查询企业信息表
                if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                    ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                            .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                    if (ObjectUtils.isNotEmpty(enterprise)) {
                        guarantee.setCustName(enterprise.getEnterpriseName());
                        guarantee.setCertNo(enterprise.getSocunicrtCode());
                        guarantee.setTelPhone(enterprise.getCompanyPhone());
                    }
                }
                if (GuaranteeTypeEnum.DBR1.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust1(guarantee);
                }
                if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust2(guarantee);
                }
                if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust3(guarantee);
                }
            }
        }
        //因为合同上要求，无担保人就显示无
        if (templateVO.getBailCust1() == null) {
            ApplyMainCustVO guarantee1 = new ApplyMainCustVO();
            guarantee1.setCustName("无");
            templateVO.setBailCust1(guarantee1);
        }
        if (templateVO.getBailCust2() == null) {
            ApplyMainCustVO guarantee2 = new ApplyMainCustVO();
            guarantee2.setCustName("无");
            templateVO.setBailCust2(guarantee2);
        }
        if (templateVO.getBailCust3() == null) {
            ApplyMainCustVO guarantee3 = new ApplyMainCustVO();
            guarantee3.setCustName("无");
            templateVO.setBailCust3(guarantee3);
        }
        templateVO.setPrintDate3(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        //纸质为空，电子为当前日期
        if (AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)){
            templateVO.setPrintDate(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        }
        //车辆融资信息
        FinCostDetails carCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, CostType.CARAMT.getIndex());
        if (carCostDetails != null) {
            if (carCostDetails.getBasicPoint() != null && carCostDetails.getBasicPoint() != 0) {
                carCostDetails.setRateType("浮动");
            }
            if (carCostDetails.getMarginAmount() != null) {
                carCostDetails.setMarginAmount(carCostDetails.getMarginAmount().compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.00") : carCostDetails.getMarginAmount());
            }
            bond = bond.add(carCostDetails.getCustMarginAmt() == null ? BigDecimal.ZERO : carCostDetails.getCustMarginAmt());

            if (StringUtils.isNotEmpty(carCostDetails.getRepaymentFrequency())) {
                carCostDetails.setRepaymentFrequencyName(AfsEnumUtil.getEnum(carCostDetails.getRepaymentFrequency(), RepaymentFrequencyEnum.class) != null ?
                        AfsEnumUtil.desc(AfsEnumUtil.getEnum(carCostDetails.getRepaymentFrequency(), RepaymentFrequencyEnum.class)) : "");
            }
            //小微订单租赁期限 = 租赁月份/还款频率；
            if (StringUtils.isNotEmpty(orderInfo.getOrderType()) && ApplyConstants.ORDER_TYPE_XW.equals(orderInfo.getOrderType())
                    && org.springframework.util.StringUtils.hasLength(carCostDetails.getRepaymentFrequency()) && carCostDetails.getLoanTerm() != null) {
                Integer frequency = BigDecimal.ZERO.compareTo(new BigDecimal(carCostDetails.getRepaymentFrequency())) < 0
                        ? Integer.parseInt(carCostDetails.getRepaymentFrequency()) : 1;
                carCostDetails.setLoanTerm(carCostDetails.getLoanTerm() / frequency);
            }
            carCostDetails.setRepaymentMethodName(ApplyUtils.dicData(carCostDetails.getRepaymentMethod(), "repaymentMethod"));
        }
        //设置手续费
        carCostDetails.setCustHandlingAmt(getCustHandlingFeeAmt(carCostDetails));
        templateVO.setCarCost(carCostDetails);
        //附加贷融资信息
        FinCostDetails addCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, ApplyConstants.COST_TYPE_ADD_LOAN);
        if (bond.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            bond = new BigDecimal("0.00");
        }
        templateVO.setAddCost(addCostDetails);
        //银行卡信息
        Integer term = 0;
        if (carCostDetails != null && addCostDetails != null) {
            term = carCostDetails.getLoanTerm() > addCostDetails.getLoanTerm() ? carCostDetails.getLoanTerm() : addCostDetails.getLoanTerm();
        } else if (carCostDetails != null) {
            term = carCostDetails.getLoanTerm();
        } else if (addCostDetails != null) {
            term = addCostDetails.getLoanTerm();
        }
        List<ApplyBankCard> applyBankCards = applyBankCardService.getBankCardByApplyNo(applyNo);
        //是否不存在个人银行卡信息
        for (ApplyBankCard applyBankCard : applyBankCards) {
            if (AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT).equals(applyBankCard.getIsDefaultDeductCard())) {
                // add by sijun.yu 2021-1-3 开户银行不能为空
                if (AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
                    Assert.isTrue(applyBankCard != null && StringUtils.isNotEmpty(applyBankCard.getBankCode()), "开户银行不能为空");
                }
                if (applyBankCard != null) {
                    applyBankCard.setCertPhone(applyBankCard.getBankPhone());
                    String dickey= ApplyConstants.CARD_SIGN_PRE+applyBankCard.getBankCode();
                    List<DicDataDto> dicDataDtos = DicHelper.getDicMaps(dickey).get(dickey);
                    for (DicDataDto dicDataDto : dicDataDtos) {
                        if (Objects.equals(dicDataDto.getValue(),applyBankCard.getCertType())) {
                            applyBankCard.setCertTypeName(dicDataDto.getTitle());
                            break;
                        }
                    }
                    if (StrUtil.isBlank(applyBankCard.getCertTypeName())) {
                        applyBankCard.setCertTypeName(CertTypeEnum.IDCARD.getName());
                    }
                    applyBankCard.setDeadlineDate((DateUtil.offset(orderInfo.getRiskPassDate(), DateField.MONTH, term + 4)).toString("yyyy-MM-dd"));
                    applyBankCard.setBankBranch(applyBankCard.getBankBranch());
                    templateVO.setBankCard(applyBankCard);
                }
            }
        }
        //挂靠信息
        ApplyAffiliatedUnit applyAffiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(applyNo);
        if (applyAffiliatedUnit != null) {
            if (StringUtils.isNotEmpty(applyAffiliatedUnit.getRegistProvince()) && StringUtils.isNotEmpty(applyAffiliatedUnit.getRegistCity())) {
                log.info("=====挂靠信息getRegistProvince：" + applyAffiliatedUnit.getRegistProvince());
                log.info("=====挂靠信息getRegistCity：" + applyAffiliatedUnit.getRegistProvince());
                StringBuffer str = new StringBuffer();
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistProvince())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistProvince()));
                }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistCity())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistCity()));
                }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistCounty())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistCounty()));
                }
                // 暂时注释 if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistStreet())) { str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistStreet())); }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistDoors())) {
                    str.append(applyAffiliatedUnit.getRegistDoors());
                }
                applyAffiliatedUnit.setRegistDetailAddress(str.toString());
            }
            applyAffiliatedUnit.setPhone(applyAffiliatedUnit.getLegalPhone());
            if (StrUtil.equals(ApplyConstants.SIGN_TYPE_AUTHORIZER, applyAffiliatedUnit.getSignatoryType())) {
                applyAffiliatedUnit.setPhone(applyAffiliatedUnit.getAuthorizerPhone());
            }
            templateVO.setUnit(applyAffiliatedUnit);

            ApplyCarUnitVo applyCarUnit = new ApplyCarUnitVo();
            applyCarUnit.setName(applyAffiliatedUnit.getAffiliatedName());
            applyCarUnit.setCode(applyAffiliatedUnit.getSocUniCrtCode());
            applyCarUnit.setAddress(applyAffiliatedUnit.getRegistDetailAddress());
            applyCarUnit.setPhone(applyAffiliatedUnit.getEnterprisePhone());
            templateVO.setCarUnit(applyCarUnit);
        }
        //各种费用项信息
        FinanceItemVO financeItemVO = new FinanceItemVO();
        //加装设备费=GPS费用+精品装潢  GPS费用=（GPS加融+GPS成本价）
        //租赁车辆总价款 =  裸车价（车款 ） +保险费+ 购置税 + 加装设备费+ 牌照费
        //融资总额（客户融资额）=  租赁车辆总价款 - 首付租金（首付金额）- （放款时扣除的）保证金和手续费
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal equipmentCost = BigDecimal.ZERO;
        BigDecimal safetyControlManageFee = BigDecimal.ZERO;
        BigDecimal totalLoans = BigDecimal.ZERO;
        BigDecimal totalAmt = BigDecimal.ZERO;
        BigDecimal amountAfterDiscount = BigDecimal.ZERO;
        //主合同租赁车辆总价款  裸车价（车款 ） +保险费+ 购置税 + 主合同加装设备费+ 牌照费
        BigDecimal mainTotalPrice = BigDecimal.ZERO;
        //主合同加装设备费 GPS费用+精品装潢
        BigDecimal mainEquipmentCost = BigDecimal.ZERO;
        if (carCostDetails.getGpsCostPrice() != null) {
            equipmentCost = equipmentCost.add(gpsAmt).add(carCostDetails.getGpsCostPrice()).add(jpzhAmt);
            safetyControlManageFee = safetyControlManageFee.add(carCostDetails.getGpsCostPrice());
        } else {
            //gps成本价为空时
            equipmentCost = equipmentCost.add(gpsAmt).add(jpzhAmt);
        }
        if (equipmentCost.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            equipmentCost = new BigDecimal("0.00");
        }
        mainEquipmentCost = mainEquipmentCost.add(gpsAmt).add(jpzhAmt);
        if (mainEquipmentCost.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            mainEquipmentCost = new BigDecimal("0.00");
        }

        totalPrice = totalPrice.add(carDetails.getSalePrice()).add(insuranceAmt).add(purchaseTaxAmt).add(equipmentCost);
        totalLoans = totalLoans.add(totalPrice).subtract(carCostDetails.getDownPayAmt()).subtract(carCostDetails.getCustMarginAmt() == null ? BigDecimal.ZERO : carCostDetails.getCustMarginAmt());

        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        String channelBelong = applyOrderInfo.getChannelBelong();
        if ("00".equals(channelBelong) || "03".equals(channelBelong)) {//渠道归属SP、总对总        ==> "02".equals(channelBelong)  == 修改为 ==> "03".equals(channelBelong)
            List<ApplyPayeeInfo> applyPayeeInfos = applyPayeeInfoService.getApplyPayeeInfoByApplyNo(applyNo);
            if (CollectionUtil.isEmpty(applyPayeeInfos)) {
                throw new AfsBaseException("请完善收款方账号信息");
            }
            amountAfterDiscount = amountAfterDiscount.add(totalLoans).subtract(carCostDetails.getGpsCostPrice() == null ? BigDecimal.ZERO : carCostDetails.getGpsCostPrice());
        } else {
            //合同剩余价款增加首付判断
            // 如果首付收款方式是收款核销，那么就不扣首付
            totalAmt = totalAmt.add(totalPrice);
            if (!AfsEnumUtil.key(PaymentMethodEnum.WRITE_OFF).equals(carCostDetails.getDownPaymentMethod())) {
                totalAmt = totalAmt.subtract(carCostDetails.getDownPayAmt());
            }
            if (StringUtils.isNotEmpty(orderInfo.getOrderType()) && ApplyConstants.ORDER_TYPE_XW.equals(orderInfo.getOrderType())) {
                BigDecimal deductAmount = applyPayeeInfoService.getDeductAmount(applyNo);
                totalAmt = totalAmt.subtract(deductAmount);
            }
            amountAfterDiscount = amountAfterDiscount.add(totalAmt).subtract(carCostDetails.getGpsCostPrice() == null ? BigDecimal.ZERO : carCostDetails.getGpsCostPrice());

        }
        ApplyCarDeposit applyCarDeposit = applyCarDepositService.getOne(Wrappers.<ApplyCarDeposit>lambdaQuery()
                .eq(ApplyCarDeposit::getApplyNo, applyNo));
        if (ObjectUtil.isNotNull(applyCarDeposit)) {
            if (AfsEnumUtil.key(PaymentMethodEnum.WRITE_OFF).equals(carCostDetails.getDownPaymentMethod())
                    && ApplyConstants.YES_FLAG.equals(applyCarDeposit.getPayDepositFlag())
                    && ApplyConstants.YES_FLAG.equals(applyCarDeposit.getDeductFlag())
                    && PayDepositObjectEnum.CAR_SHOP.getCode().equals(applyCarDeposit.getPayDepositObject())) {
                amountAfterDiscount = amountAfterDiscount.subtract(applyCarDeposit.getDepositAmount());
            }
        }
        mainTotalPrice = mainTotalPrice.add(carDetails.getSalePrice()).add(insuranceAmt).add(purchaseTaxAmt).add(mainEquipmentCost);
        financeItemVO.setEquipmentCost(equipment);
        financeItemVO.setTotalPrice(totalPrice);
        financeItemVO.setSafetyControlManageFee(safetyControlManageFee);
        financeItemVO.setTotalLoans(totalLoans);
        financeItemVO.setAmountAfterDiscount(amountAfterDiscount);
        financeItemVO.setMainEquipmentCost(mainEquipmentCost);
        financeItemVO.setMainTotalPrice(mainTotalPrice);
        financeItemVO.setInsurancePremium(rance);
        financeItemVO.setBond(bond);
        financeItemVO.setPurchaseTax(purchaseTax);
        financeItemVO.setOtherFee(otherAmt);
        //设置手续费
        financeItemVO.setServiceFee(carCostDetails.getCustHandlingAmt());
        templateVO.setFinItems(financeItemVO);
        //获取直营申请开票信息
        ApplyReceiptInfo applyReceiptInfoByApplyNo = applyReceiptInfoService.getApplyReceiptInfoByApplyNo(applyNo);
        if (ObjectUtils.isNotEmpty(applyReceiptInfoByApplyNo)) {
            templateVO.setApplyReceiptInfo(applyReceiptInfoByApplyNo);
        }
        //融资费用主表查询
        FinMainInfo finMainInfo = finMainInfoService.getFinMainInfoByApplyNo(applyNo);
        //月租金（平均）租金总额（不含留购金）/ 租赁期数，保留2位，四舍五入
        BigDecimal totalRent = carCostDetails.getTotalRent().divide(new BigDecimal(carCostDetails.getLoanTerm()),2,BigDecimal.ROUND_HALF_UP);
        finMainInfo.setRedTotalRent(totalRent);
        // todo 盖章规则
        ChannelInfoUniteInfoVo infoVo = applyCarInvoiceService.applyToChannelUniteInfo(contractInfo.getContractNo());
        templateVO.setChannelInfoUniteInfo(infoVo);
        templateVO.setTotalInfo(finMainInfo);

        // 融资租赁合同十一条修改
        // 查询项目名称
        FinUiContent finUiContent = uiContentService.getFinUiContentByApplyNo(applyNo);
        String productName = "";
        if (finUiContent != null) {
            FinancialProductDTO dto = JSONObject.parseObject(finUiContent.getUiContent(), FinancialProductDTO.class);
            productName = dto.getBasicInfo().getProductName();

            // 加载数据字典
            IResponse<List<String>> productGroupForClear = apply2CaseFeign.getDictByKey("productGroupForClear");

            if(ApplyConstants.CODE_SUCCESS.equals(productGroupForClear.getCode())){
                List<String> productNameList = productGroupForClear.getData();
                log.info("字典加载的数据={}，产品名={}，结果={}",JSON.toJSONString(productNameList),productName,productNameList.contains(productName));
                if(CollUtil.isNotEmpty(productNameList) && productNameList.contains(productName)) {
                    // 专项产品返回特殊数据
                    templateVO.setAPeriodNumber("12");
                    templateVO.setALeaseCharge("/");
                    templateVO.setARepaidPeriodNumber("/");
                    templateVO.setAx("/");
                    templateVO.setBRepaidPeriodNumber("24");
                    templateVO.setBx("0");
                    templateVO.setCx("8");
                }else{
                    // 非专项产品，返回正常数据
                    templateVO.setAPeriodNumber("/");
                    templateVO.setALeaseCharge("0");
                    templateVO.setARepaidPeriodNumber("12");
                    templateVO.setAx("2");
                    templateVO.setBRepaidPeriodNumber("/");
                    templateVO.setBx("/");
                    templateVO.setCx("0");
                }
            }else{
                throw new AfsBaseException("专项产品组字典参数获取失败");
            }

        }

        log.info("合同模板参数VO：{}", JSONObject.toJSONString(templateVO));
        return templateVO;
    }
    private BigDecimal getCustHandlingFeeAmt(FinCostDetails carCostDetails) {

        return (carCostDetails.getHandlingFeeAmount() == null ? BigDecimal.ZERO : carCostDetails.getHandlingFeeAmount())
                .add(carCostDetails.getPurchaseHandlingAmt() == null ? BigDecimal.ZERO : carCostDetails.getPurchaseHandlingAmt())
                .add(carCostDetails.getCustHandlingAmt() == null ? BigDecimal.ZERO : carCostDetails.getCustHandlingAmt());
    }


    /**
     * 返回还款计划表数据-PC
     *
     * @return
     */
    @Override
    public List<ComAttachmentFile> returnRepaymentPlanData(String applyNo, String contractNo) {
        Map<String, String> headers = new HashMap<>(2);
        log.info("生成还款计划表模版数据所用入参:{}", applyNo);
        ContractTemplateVO contractTemplateVO = new ContractTemplateVO();
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, "PC");
        //获取客户信息
        ApplyMainCustVO applyMainCustVO = this.applyMainCustVO(applyNo, ApplyConstants.PRINCIPAL_BORROWER, true);
        log.info("获取还款计划表主借人数据：{}", applyMainCustVO);

        //获取合同信息
        ApplyContractInfo contractInfoByContractNo = applyContractInfoService.getContractInfoByContractNo(contractNo);
        if (contractInfoByContractNo.getStartDate() != null) {
            ruleVO.setIsLoanActive("yes");
        }
        log.info("获取还款计划表合同信息:{}", contractInfoByContractNo);
        contractTemplateVO.setMainCust(applyMainCustVO);
        contractTemplateVO.setContract(contractInfoByContractNo);

        //获取还款计划表数据
        headers.put("clientId", applyConfig.getContractClientId());
        headers.put("clientSecret", applyConfig.getContractClientSecret());
        List<RepaymentPlanDto> repaymentList = ((JSONArray) ((JSONObject) JSONArray.parse(JSONObject.toJSONString((applyContractFeign.getRepaymentPlanInfoList(contractNo, headers)).getData()))).get("Repayment")).toJavaList(RepaymentPlanDto.class);
        List<RepaymentVO> repaymentVOList = new ArrayList<>();
        repaymentList.forEach(repaymentPlanDto -> {
            RepaymentVO repaymentVO = new RepaymentVO();
            repaymentVO.setBenJin(repaymentPlanDto.getReceivablePrinciple());
            repaymentVO.setLiXi(repaymentPlanDto.getReceivableInterest());
            repaymentVO.setHuanKuanRiQi(DateUtil.date(repaymentPlanDto.getDueDate()));
            repaymentVO.setNo(repaymentPlanDto.getTermNo());
            repaymentVO.setYueGong(repaymentPlanDto.getReceivableRent());
            repaymentVOList.add(repaymentVO);
        });
        contractTemplateVO.setRepay(repaymentVOList);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(contractTemplateVO);
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        Assert.isTrue(contractNo != null, "合同编号不能为空");

        List<ComAttachmentFile> fileList = comPrintFormManageService.batchPrint(jsonTemp, jsonRule, null, contractNo, "printLoanApply", "PC");
        return fileList;
    }

    /**
     * 合同信息录入-批量模板生成
     *
     * @param applyNo
     */
    @Override
    public List<ComAttachmentFile> contractBatchPrint(String applyNo, String contractNo, String serviceClientType) {
        log.info("合同批量生成申请编号:{} 合同编号:{}", applyNo, contractNo);
        //兼容旧逻辑，设置查询打印模板及规则匹配时主体编号为中远上海 RT0001
        String subjectCode = SubjectCodeEnum.MULTI_SUBJECT_SH.getCode();
        //add panhj 20210109 模板字段校验
        checkInfo(applyNo);
        // 获取模板参数
        ContractTemplateVO templateVO = getContractTemplateParam(applyNo, serviceClientType);
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        log.info("模板信息{}", jsonTemp.toJSONString());
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        jsonRule.put("subjectCode", subjectCode);
        Assert.isTrue(contractNo != null, "合同编号不能为空");
        List<ComAttachmentFile> printLoanApply = null;

        ComPrintFormManage guaranteeForm = comPrintFormManageService.getPrintFormManageByUniqueCodeAndSubjectCode("liabilityGuaranteeDirect", subjectCode);
        Assert.isTrue(guaranteeForm != null, "连带责任保证书不存在");
//        获取协议书id
        ComPrintFormManage xysForm = comPrintFormManageService.getPrintFormManageByUniqueCodeAndSubjectCode("agreementDirect", subjectCode);
        Assert.isTrue(xysForm != null, "协议书不存在");
        List<Long> templeIds = new ArrayList<>();
        //将连带责任保证书排除，之后单独生成
        templeIds.add(guaranteeForm.getId());
//        主借人的协议书中不允许存在担保人信息，所以将协议书排除，然后单独生成
        templeIds.add(xysForm.getId());
        printLoanApply = comPrintFormManageService.batchPrintNoSeal(jsonTemp, jsonRule, templeIds, contractNo, "printLoanApply", serviceClientType);

        //将模板参数中担保人一信息置空以进行主借人协议书
        ApplyMainCustVO bailCust1 = templateVO.getBailCust1();
        templateVO.setBailCust1(null);
        log.info("获取协议书模板文件" + JSONObject.toJSON(xysForm) + " contractNo:" + contractNo + " serviceClientType:" + serviceClientType + " templateVO：" + JSONObject.toJSON(templateVO) + " jsonRule:" + JSONObject.toJSON(jsonRule));
        ComAttachmentFile comAttachmentFileByXys = this.setSinglePrintByRule(xysForm, contractNo, serviceClientType, templateVO, jsonRule);
        log.info("协议书数据" + JSONObject.toJSON(comAttachmentFileByXys));
        if (ObjectUtils.isNotEmpty(comAttachmentFileByXys)) {
            comAttachmentFileByXys.setBelongNo((comAttachmentFileByXys.getBelongNo() == null ? "" : comAttachmentFileByXys.getBelongNo()) + "_" + templateVO.getMainCust().getCustId());
            printLoanApply.add(comAttachmentFileByXys);
        }
//        生成完毕后，将担保人1信息重新赋值
        templateVO.setBailCust1(bailCust1);

//        step1: 查询该合同下的所有担保人
        List<ApplyCustBaseInfo> applyMainCustInfo = applyCustBaseInfoService.getCustBaseInfos(applyNo, AfsEnumUtil.key(CustRoleEnum.GUARANTOR));
        List<ApplyMainCustVO> guaranteeVoList = new ArrayList<>();
        for (ApplyCustBaseInfo custBaseInfo : applyMainCustInfo) {
            guaranteeVoList.add(getByCustInfo(custBaseInfo, applyNo));
        }
        //循环的担保人，生成单个的连带责任保证书和单个的协议书
        for (ApplyMainCustVO guaranteeVoOne : guaranteeVoList) {
            templateVO.setBailCust1(guaranteeVoOne);
            ComAttachmentFile attachmentFileByGuaranteeForm = this.setSinglePrintByRule(guaranteeForm, contractNo, serviceClientType, templateVO, jsonRule);
            log.info("连带责任保证书数据内容" + JSONObject.toJSON(attachmentFileByGuaranteeForm));
            if (ObjectUtils.isNotEmpty(attachmentFileByGuaranteeForm)) {
                attachmentFileByGuaranteeForm.setBelongNo((attachmentFileByGuaranteeForm.getBelongNo() == null ? "" : attachmentFileByGuaranteeForm.getBelongNo()) + "_" + templateVO.getBailCust1().getCustId());
            printLoanApply.add(attachmentFileByGuaranteeForm);
        }

            log.info("获取担保人协议书模板文件" + JSONObject.toJSON(xysForm) + " contractNo:" + contractNo + " serviceClientType:" + serviceClientType + " templateVO：" + JSONObject.toJSON(templateVO) + " jsonRule:" + JSONObject.toJSON(jsonRule));
            ComAttachmentFile attachmentFileByXys = this.setSinglePrintByRule(xysForm, contractNo, serviceClientType, templateVO, jsonRule);
            log.info("协议书数据" + JSONObject.toJSON(attachmentFileByXys));
            if (ObjectUtils.isNotEmpty(attachmentFileByXys)) {
                attachmentFileByXys.setBelongNo((attachmentFileByXys.getBelongNo() == null ? "" : attachmentFileByXys.getBelongNo()) + "_" + templateVO.getBailCust1().getCustId());
                printLoanApply.add(attachmentFileByXys);
            }
        }
        List<ComAttachmentFile> fileList = printLoanApply;
        if (fileList != null && fileList.size() > 0 && AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
            applySignRelationService.saveSignRelationList(fileList, applyNo);
            applyContractInfoService.updateReappearFlag(contractNo);
        }
        return fileList;
    }

    /**
     * 合同模板生成-多主体
     *
     * @param applyNo
     * @param contractNo
     * @param serviceClientType
     * @param subjectCode
     * @return
     */
    @Override
    public List<List<ComAttachmentFile>> contractBatchPrintBySubjectCode(String applyNo, String contractNo, String serviceClientType, String subjectCode) {

        log.info("多主体合同生成: 申请编号{} 合同编号:{} 主体编号: {}", applyNo, contractNo, subjectCode);
        this.checkInfo(applyNo);
        // 获取模板参数
        ContractTemplateVO templateVO = getContractTemplateParam(applyNo, serviceClientType);
        log.info("模板数据templateVO--> {}",JSONObject.toJSONString(templateVO));
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        Assert.isTrue(contractNo != null, "合同编号不能为空");
        log.info("规则信息==> {}", jsonRule.toJSONString());
        List<List<ComAttachmentFile>> returnList = new ArrayList<>();
        //需排除批量生成的合同模板
        List<Long> skipBatchPrintIdList = new ArrayList<>();

        //查询连带责任保证书的影像件
        ComAttachmentManagement comAttachmentManagement = comAttachmentManagementService.getOne(new LambdaQueryWrapper<ComAttachmentManagement>().eq(ComAttachmentManagement::getUniqueCode, "liabilityGuaranteeDirect"));
        List<ComPrintFormManage> skipBatchPrintFormManageList = new ArrayList<>();
        if (comAttachmentManagement != null) {
            //查询所有归属于保证书影像件的保证书模板 如果有全部剔除批量生成
            skipBatchPrintFormManageList = comPrintFormManageService.list(new LambdaQueryWrapper<ComPrintFormManage>()
                    .eq(ComPrintFormManage::getAttachmentSubClass, comAttachmentManagement.getId().toString()));
            skipBatchPrintIdList = skipBatchPrintFormManageList.stream().map(k -> k.getId()).collect(Collectors.toList());
        }
        List<ComAttachmentFile> printLoanApply = comPrintFormManageService.batchPrint(jsonTemp, jsonRule, skipBatchPrintIdList, contractNo, "printLoanApply", serviceClientType);
        returnList.add(printLoanApply);
        //如果此订单担保人超出三个, 查询连带责任担保书是否存在
        List<ApplyCustBaseInfo> guarantorList = applyCustBaseInfoService.list(new LambdaQueryWrapper<ApplyCustBaseInfo>().eq(ApplyCustBaseInfo::getApplyNo, applyNo).eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode()));
        if(guarantorList.size() > 3){
            //需要生成多分的连带责任保证书(担保人顺位大于03的)
            List<ApplyCustBaseInfo> needPrintList = guarantorList.stream().sorted(Comparator.comparing(ApplyCustBaseInfo::getIsFirstGuarantor).reversed()
                    .thenComparing(Comparator.comparing(ApplyCustBaseInfo::getCustType))
                    .thenComparing(Comparator.comparing(ApplyCustBaseInfo::getFirstGuarantor)))
                    .collect(Collectors.toList()).subList(3,guarantorList.size());
            log.info("担保人数量为[{}] 需要生成连带责任担保书数量为[{}]",guarantorList.size(),needPrintList.size());
            //根据规则匹配出需要生成的模板id（找到连带责任担保书的模板）
            List<Long> templateIdList = comPrintFormManageService.printTempleRule(jsonRule, contractNo, "printLoanApply");
            if (templateIdList.size() > 0 && skipBatchPrintIdList.size() > 0) {
                //查询模板中归属于连带责任担保书影像件的模板id
                templateIdList.retainAll(skipBatchPrintIdList);
                log.info("根据规则匹配出需要生成责任书的模板[{}]", JSON.toJSONString(templateIdList));

                List<ComPrintFormManage> needPrintManageList = skipBatchPrintFormManageList.stream().filter(k -> templateIdList.contains(k.getId())).collect(Collectors.toList());
                List<ComAttachmentFile> printFileList = new ArrayList<>();
                for (ApplyCustBaseInfo applyCustBaseInfo : needPrintList) {
                    templateVO.setBailCust1(getByCustInfo(applyCustBaseInfo, applyNo));
                    for (ComPrintFormManage manage : needPrintManageList) {
                        //连带责任担保书打印默认使用模板中担保人一的模板值替换规则
                        ComAttachmentFile file = this.setSinglePrintByRule(manage, contractNo, serviceClientType, templateVO, jsonRule);
                        applySignRelationService.addSignRealtionByFile(contractNo, applyCustBaseInfo, file);
                        printFileList.add(file);
                    }
                }
                if (needPrintList.size() > 0 && printFileList.size() > 0) {
                    returnList.add(printFileList);
                }
            }
        }
        return returnList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ComAttachmentFile> buildContractBatchPrintBySubjectCode(String applyNo, String contractNo, String serviceClientType) {
        String key= MessageFormat.format("{0}{1}", "contractGeneration",contractNo);
        String lockValue=applyNo+System.currentTimeMillis();

        // 校验银行卡是否已经签约 - 线下签约的时候也需要校验
        applyBankCardService.checkBankCardSignStatus(applyNo);

        try {
            this.checkCarVin(applyNo);
            Boolean res = stringRedisTemplate.opsForValue().setIfAbsent(key, lockValue, 2, TimeUnit.MINUTES);
            Assert.isTrue(res, "生成中，请刷新页面");
            ApplyContractInfo contractInfo = applyContractInfoService.getOne(Wrappers.<ApplyContractInfo>query().lambda().eq(ApplyContractInfo::getApplyNo,applyNo));
            ApplySubjectInfo subjectInfo = applySubjectInfoService.getOne(new LambdaQueryWrapper<ApplySubjectInfo>().eq(ApplySubjectInfo::getApplyNo,applyNo));
            // 是否为多主体，打印模板方案不同
            boolean isAloneSubject = subjectInfo == null || StringUtil.isBlank(subjectInfo.getSubjectCode()) ? true :  false;
            List<ComAttachmentFile> fileList = null;
            List<ComAttachmentFile> guarantorList = new ArrayList<>();
            if(isAloneSubject){
                throw new AfsBaseException("主体数据不存在");
            }else{
                List<List<ComAttachmentFile>> listList = contractBatchPrintBySubjectCode(applyNo, contractNo, serviceClientType,subjectInfo.getSubjectCode());
                fileList = listList.get(0);
                //需要单独生成签约关系的文件--待处理
                if(listList.size() > 1){
                    guarantorList = listList.get(1);
                }
            }

            contractInfo.setPaperReappearFlag(WhetherEnum.No.getIndex());
            contractInfo.setLastGenerationDate(new Date());

            // 合同信息表
            applyContractInfoService.updateById(contractInfo);

            /** 生成签约关系规则为: 个人件 + 企业性质为个体工商户的企业件 */
            ApplyCustBaseInfo baseInfo = applyCustBaseInfoService.getCustBaseInfo(applyNo, CustRoleEnum.MIANCUST.getCode());
            if (StringUtils.equals(AfsEnumUtil.key(CustTypeEnum.COMPANY), baseInfo.getCustType())) {
                //企业件
                ApplyEnterpriseCustomerDetails companyDetail = detailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                        .eq(ApplyEnterpriseCustomerDetails::getCustId, baseInfo.getId())
                        .eq(ApplyEnterpriseCustomerDetails::getApplyNo, baseInfo.getApplyNo()));
                if (!StringUtils.equals(companyDetail.getNatureEnterprise(), NatureEnterpriseEnum.GETIGONGSHANGHU.getCode())) {
                    //企业件非个体工商户不生成签约关系, 修改合同主表为线下签约  如果签约方式为null 表示生成了电签合同的 可电签
                    contractInfo.setSignType(AfsEnumUtil.key(SignTypeEnum.offline));
                    applyContractInfoService.updateById(contractInfo);
                    fileList.addAll(guarantorList);
                    return fileList;
                }
            }
            if (fileList.size() > 0) {
                //重置订单合同电签签约状态为未签署
                ApplyOrderInfo orderInfo = applyOrderInfoService.getOrderInfoByApplyNo(contractInfo.getApplyNo());
                orderInfo.setSignFlag(IsTypeNumEnum.NO.getCode());
                applyOrderInfoService.updateById(orderInfo);
                applySignRelationService.saveContractSignRelation(fileList,applyNo, contractNo);
            }
            fileList.addAll(guarantorList);

            // 返回数据
            return fileList;
        }finally {
            String value=stringRedisTemplate.opsForValue().get( key );
            if(lockValue.equals( value )) {
                stringRedisTemplate.delete( key );
            }
        }
    }

    private ApplyMainCustVO getByCustInfo(ApplyCustBaseInfo custBaseInfo, String applyNo) {
        ApplyMainCustVO applyMainCustVO = this.mergeInfo(custBaseInfo, applyNo, false);
        applyMainCustVO.setCustId(custBaseInfo.getId());
        orderInfoService.getOrderInfoByApplyNo(applyNo);
        //判断担保人类型是否为企业 为企业时查询企业信息表
        if(ApplyConstants.ENTERPRISE.equals(applyMainCustVO.getCustType())){
            ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                    .eq(ApplyEnterpriseCustomerDetails::getCustId, applyMainCustVO.getCustId()));
            if (ObjectUtils.isNotEmpty(enterprise)) {
                applyMainCustVO.setCustName(enterprise.getEnterpriseName());
                applyMainCustVO.setCertNo(enterprise.getSocunicrtCode());
            }
        }
        return applyMainCustVO;
    }


    /**
     * @param file              打印模板管理实体类
     * @param contractNo        合同号码
     * @param serviceClientType pc端/app端
     * @param templateVO        模板参数
     * @param jsonRule          模板规则
     * @return 返回单个打印模板
     */
    private ComAttachmentFile setSinglePrintByRule(ComPrintFormManage file, String contractNo, String serviceClientType, ContractTemplateVO templateVO, JSONObject jsonRule) {
        if (ApplyConstants.YES.equals(file.getIsEnable())) {
            List<RuleResult> ruleResults = comPrintFormManageService.queryIdByRule(jsonRule, file.getId().toString());
            JSONObject mainCustJson = (JSONObject) JSONObject.toJSON(templateVO);
            for (RuleResult result : ruleResults) {
                if (!"".equals(Convert.toStr(result.getResult(), ""))) {
                    ComAttachmentFile attachmentFile = comPrintFormManageService.printNoSeal(mainCustJson, result.getResult().toString(), contractNo, serviceClientType);
                    if (attachmentFile != null) {
                        return attachmentFile;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 合同信息录入-单个模板
     *
     * @param applyNo
     * @param contractNo
     * @param attachmentFile
     * @return
     */
    public ComAttachmentFile contractSinglePrint(String applyNo, String contractNo, ComAttachmentFile attachmentFile, String serviceClientType) {

        // 获取模板参数
        ContractTemplateVO templateVO = getContractTemplateParam(applyNo, serviceClientType);
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);

        ComAttachmentFile comAttachmentFile = comPrintFormManageService.print(jsonTemp, attachmentFile.getAttachmentCode(), contractNo, serviceClientType);
        return comAttachmentFile;
    }

    /**
     * 进件申请-合同模板参数
     *
     * @param applyNo
     * @return
     */
    public ApplyTemplateVO getApplyTemplatePara(String applyNo, String serviceClientType) {

        ApplyTemplateVO templateVO = new ApplyTemplateVO();
        // 订单信息
        ApplyOrderInfo orderInfo = orderInfoService.getOrderInfoByApplyNo(applyNo);
        Assert.isTrue(orderInfo != null, "订单信息不存在");
        orderInfo.setCarTypeName(ApplyUtils.dicData(orderInfo.getCarType(), "carType"));
        orderInfo.setBusinessTypeName(ApplyUtils.dicData(orderInfo.getBusinessType(), "businessType"));
        orderInfo.setCarPurposeName(ApplyUtils.dicData(orderInfo.getCarPurpose(), "carPurpose"));
        orderInfo.setBelongingCapitalName(ApplyUtils.dicData(orderInfo.getBelongingCapital(), "belongingCapitalName"));
        templateVO.setOrder(orderInfo);
        //发票信息
        ApplyCarInvoice applyCarInvoice = applyCarInvoiceService.getApplyCarInvoice(applyNo);
        templateVO.setCarInvoice(applyCarInvoice);
        // 主借人信息
        ApplyMainCustVO mainCustVO = this.applyMainCustVO(applyNo, ApplyConstants.PRINCIPAL_BORROWER, true);
        templateVO.setMainCust(mainCustVO);
        // 共借人信息
        ApplyMainCustVO coCustVO = this.applyMainCustVO(applyNo, ApplyConstants.COMMON_BORROWER, false);
        templateVO.setCoCust(coCustVO);
        // 担保人信息
        this.applyMainCustVO(applyNo, ApplyConstants.GUARANTOR, false);
        //担保人
        List<ApplyCustBaseInfo> guarnBaseInfoList = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.GUARANTOR));
        if (CollectionUtil.isNotEmpty(guarnBaseInfoList)){
            guarnBaseInfoList.forEach(guarnBase -> {
                if (guarnBase.getCustType().equals(ApplyConstants.PERSONAL)) {
                    GuarantorTypeEnum guarantorTypeEnum = (GuarantorTypeEnum) AfsEnumUtil.getEnum(guarnBase.getFirstGuarantor(), GuarantorTypeEnum.class);
                    switch (guarantorTypeEnum) {
                        //第一担保人
                        case FIRST:
                            templateVO.setBailCustOne(guarnBase);
                            break;
                        //第二担保人
                        case SECOND:
                            templateVO.setBailCustTwo(guarnBase);
                            break;
                        //第三担保人
                        case THIRD:
                            templateVO.setBailCustThree(guarnBase);
                            break;
                        default:
                            log.info("担保人类型不存在:{}", guarantorTypeEnum);
                    }
                }
            });
        }

        // 联系人
        List<ApplyCustContacts> contactsList = applyCustContactsService.getCustContactsList(applyNo);
        int parentCount = 0;
        int otherCount = 0;
        ApplyCustContacts parentCusVo = new ApplyCustContacts();
        ApplyCustContacts otherCustVo = new ApplyCustContacts();
        if (contactsList != null && contactsList.size() > 0) {
            for (ApplyCustContacts applyCustContacts : contactsList) {
                if (ApplyConstants.PARENT.equals(applyCustContacts.getCustRelation()) || ApplyConstants.FAMILY.equals(applyCustContacts.getCustRelation()) || ApplyConstants.BROTHER.equals(applyCustContacts.getCustRelation()) || ApplyConstants.OTHER.equals(applyCustContacts.getCustRelation())) {
                    parentCount += 1;
                }
                if (ApplyConstants.FRIEND.equals(applyCustContacts.getCustRelation()) || ApplyConstants.ENTERPRISE_THIRD.equals(applyCustContacts.getCustRelation()) || ApplyConstants.SUPPLIER.equals(applyCustContacts.getCustRelation()) || ApplyConstants.BANK_FACTORING.equals(applyCustContacts.getCustRelation())
                        || ApplyConstants.PERSONAL_THIRD.equals(applyCustContacts.getCustRelation()) || ApplyConstants.COLLEAGUE.equals(applyCustContacts.getCustRelation())) {
                    otherCount += 1;
                }
            }
            for (ApplyCustContacts custContacts : contactsList) {
                // 紧急联系人-夫妻
                String custRelation = custContacts.getCustRelation();
                if (ApplyConstants.SPOUSE.equals(custRelation)) {
                    ApplyCustContacts spouseCustVO = new ApplyCustContacts();
                    BeanUtils.copyProperties(custContacts, spouseCustVO);
                    if (StringUtils.isNotEmpty(spouseCustVO.getLivingProvince()) && StringUtils.isNotEmpty(spouseCustVO.getLivingCity())) {
                        spouseCustVO.setLivingDetailAddress(this.provinceOrCity(spouseCustVO.getLivingProvince()) + this.provinceOrCity(spouseCustVO.getLivingCity()) + (spouseCustVO.getDetailAddress() == null ? "" : spouseCustVO.getDetailAddress()));
                    }
                    templateVO.setSpouseCust(spouseCustVO);
                }
                // 紧急联系人-父母或其他亲属，最多放两个栏位
                else if (ApplyConstants.PARENT.equals(custRelation) || ApplyConstants.BROTHER.equals(custRelation) || ApplyConstants.FAMILY.equals(custRelation) || ApplyConstants.OTHER.equals(custRelation)) {
                    //有父母-子女，其他亲属关系，先取这条记录放入父母或其他亲属栏位
                    if (parentCusVo.getCustRelation() == null) {
                        BeanUtils.copyProperties(custContacts, parentCusVo);
                        // 与主借人关系
                        parentCusVo.setCustRelation(ApplyUtils.dicData(custRelation, "custRelation"));
                        if (StringUtils.isNotEmpty(parentCusVo.getLivingProvince()) && StringUtils.isNotEmpty(parentCusVo.getLivingCity())) {
                            parentCusVo.setLivingDetailAddress(this.provinceOrCity(parentCusVo.getLivingProvince()) + this.provinceOrCity(parentCusVo.getLivingCity()) + (parentCusVo.getDetailAddress() == null ? "" : parentCusVo.getDetailAddress()));
                        }
                        templateVO.setParentCust(parentCusVo);
                    }
                    if (parentCount > 1 && otherCount == 0) {
                        BeanUtils.copyProperties(custContacts, otherCustVo);
                        // 与主借人关系
                        otherCustVo.setCustRelation(ApplyUtils.dicData(custRelation, "custRelation"));
                        if (StringUtils.isNotEmpty(otherCustVo.getLivingProvince()) && StringUtils.isNotEmpty(otherCustVo.getLivingCity())) {
                            otherCustVo.setLivingDetailAddress(this.provinceOrCity(otherCustVo.getLivingProvince()) + this.provinceOrCity(otherCustVo.getLivingCity()) + (otherCustVo.getDetailAddress() == null ? "" : otherCustVo.getDetailAddress()));
                        }
                        templateVO.setOtherCust(otherCustVo);
                    }
                }
                //紧急联系人-其他联系人
                else if (ApplyConstants.FRIEND.equals(custRelation) || ApplyConstants.ENTERPRISE_THIRD.equals(custRelation) || ApplyConstants.SUPPLIER.equals(custRelation) || ApplyConstants.BANK_FACTORING.equals(custRelation)
                        || ApplyConstants.PERSONAL_THIRD.equals(custRelation) || ApplyConstants.COLLEAGUE.equals(custRelation)) {
                    if (parentCount == 0) {
                        if (parentCusVo.getCustRelation() == null) {
                            BeanUtils.copyProperties(custContacts, parentCusVo);
                            // 与主借人关系
                            parentCusVo.setCustRelation(ApplyUtils.dicData(custRelation, "custRelation"));
                            if (StringUtils.isNotEmpty(parentCusVo.getLivingProvince()) && StringUtils.isNotEmpty(parentCusVo.getLivingCity())) {
                                parentCusVo.setLivingDetailAddress(this.provinceOrCity(parentCusVo.getLivingProvince()) + this.provinceOrCity(parentCusVo.getLivingCity()) + (parentCusVo.getDetailAddress() == null ? "" : parentCusVo.getDetailAddress()));
                            }
                            templateVO.setOtherCust(parentCusVo);
                        }
                        BeanUtils.copyProperties(custContacts, otherCustVo);
                        otherCustVo.setCustRelation(ApplyUtils.dicData(custRelation, "custRelation"));
                        if (StringUtils.isNotEmpty(otherCustVo.getLivingProvince()) && StringUtils.isNotEmpty(otherCustVo.getLivingCity())) {
                            otherCustVo.setLivingDetailAddress(this.provinceOrCity(otherCustVo.getLivingProvince()) + this.provinceOrCity(otherCustVo.getLivingCity()) + (otherCustVo.getDetailAddress() == null ? "" : otherCustVo.getDetailAddress()));
                        }
                        templateVO.setParentCust(otherCustVo);
                    } else {
                        BeanUtils.copyProperties(custContacts, otherCustVo);
                        otherCustVo.setCustRelation(ApplyUtils.dicData(custRelation, "custRelation"));
                        if (StringUtils.isNotEmpty(otherCustVo.getLivingProvince()) && StringUtils.isNotEmpty(otherCustVo.getLivingCity())) {
                            otherCustVo.setLivingDetailAddress(this.provinceOrCity(otherCustVo.getLivingProvince()) + this.provinceOrCity(otherCustVo.getLivingCity()) + (otherCustVo.getDetailAddress() == null ? "" : otherCustVo.getDetailAddress()));
                        }
                        templateVO.setOtherCust(otherCustVo);
                    }
                }
            }
        }

        // 车款融资信息
        BigDecimal totalContractAmt = BigDecimal.ZERO;
        BigDecimal totalLoanAmt = BigDecimal.ZERO;
        FinCostDetails carCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, ApplyConstants.COST_TYPE_CAR_LOAN);
        if (carCostDetails != null) {
            totalContractAmt = totalContractAmt.add(carCostDetails.getContractAmt() == null ? BigDecimal.ZERO : carCostDetails.getContractAmt());
            totalLoanAmt = totalLoanAmt.add(carCostDetails.getLoanAmt() == null ? BigDecimal.ZERO : carCostDetails.getLoanAmt());
        }
        templateVO.setCarCost(carCostDetails);
        // 附加贷融资信息
        FinCostDetails addCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, ApplyConstants.COST_TYPE_ADD_LOAN);
        if (addCostDetails != null) {
            totalContractAmt = totalContractAmt.add(addCostDetails.getContractAmt() == null ? BigDecimal.ZERO : addCostDetails.getContractAmt());
            totalLoanAmt = totalLoanAmt.add(addCostDetails.getLoanAmt() == null ? BigDecimal.ZERO : addCostDetails.getLoanAmt());
        }
        // 贷款金额
        templateVO.setTotalLoanAmt(totalLoanAmt);
        templateVO.setAddCost(addCostDetails);
        // 车辆信息
        ApplyCarDetails carDetails = applyCarDetailsService.getCarDetailsByApplyNo(applyNo);
        carDetails.setLicenseDetail(this.provinceOrCity(carDetails.getLicenseProvince()) + this.provinceOrCity(carDetails.getLicenseCity()));
        templateVO.setCarDetails(carDetails);
        // 二手车首次登记日期
        templateVO.setFirstLandingTime(carDetails.getFirstLandingDate());
        // 渠道信息
        ApplyChannelInfo channelInfo = applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        channelInfo.setReceiptBankName(channelInfo.getReceiptBankCode());
        channelInfo.setChannelProvinceName(this.provinceOrCity(channelInfo.getChannelProvince()));
        channelInfo.setChannelCityName(this.provinceOrCity(channelInfo.getChannelCity()));
        templateVO.setChannel(channelInfo);
        // 附加品金额
        List<FinFinancingItems> finFinancingItemsList = applyFinancingItemsService.getFinancingItemsList(applyNo, carDetails.getId());
        BigDecimal addAmt = BigDecimal.ZERO;
        if (finFinancingItemsList.size() > 0) {
            for (FinFinancingItems financingItems : finFinancingItemsList) {
                addAmt = addAmt.add(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
            }
        }
        templateVO.setAddAmt(addAmt);
        // 合同价格
        templateVO.setTotalContractAmt(totalContractAmt.add(addAmt));
        templateVO.setPrintDate(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));

        return templateVO;
    }


    /**
     * 获取进件请求头
     *
     * @return
     */
    public Map makeCaseHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getCaseClientId());
        headers.put("clientSecret", applyConfig.getCaseClientSecret());
        return headers;
    }

    /**
     * 进件申请-批量模板生成
     *
     * @param applyNo
     */
    @Override
    public List<ComAttachmentFile> applyBatchPrint(String applyNo, String serviceClientType) {
        // 获取模板参数
        ApplyTemplateVO templateVO = this.getApplyTemplatePara(applyNo, serviceClientType);
        log.info("进件申请阶段模板数据templateVO--> {}",JSONObject.toJSONString(templateVO));
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);

        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        log.info("进件申请阶段规则信息==> {}", jsonRule.toJSONString());

        // add by sijun.yu 2020-10-25 做了电子签约模板
        List<Long> templeIds = new ArrayList<Long>();
        if (AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
            templeIds = applySignRelationService.getSignTempleIdList(applyNo);
        } else {
            templeIds = applySignRelationService.getTempleIdList(applyNo);
        }
        ApplyCustBaseInfo custInfo = applyCustBaseInfoService.getCustBaseInfo(applyNo, ApplyConstants.PRINCIPAL_BORROWER);
        if (custInfo != null && WhetherEnum.YES.getIndex().equals(custInfo.getSignResult()) && StringUtils.isEmpty(custInfo.getIsLock())) {
            ComPrintFormManage comPrintFormManage = comPrintFormManageService.getPrintFormManageByUniqueCode(AttachmentUniqueCodeEnum.MAIN_LETTER_OF_AUTHORIZATION.getCode());
            if (CollectionUtil.isEmpty(templeIds)) {
                templeIds = new ArrayList<Long>();
            }
            templeIds.add(comPrintFormManage.getId());
        }

        List<ComAttachmentFile> fileList = comPrintFormManageService.batchPrintNoSeal(jsonTemp, jsonRule, templeIds, applyNo, "printOrderApply", serviceClientType);
        if (fileList != null && fileList.size() > 0 && AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
            applySignRelationService.saveSignRelationList(fileList, applyNo);
            applyReportService.updateReappearFlag(applyNo);
        }
        return fileList;
    }

    /**
     * 进件申请-单个模板生成
     *
     * @param applyNo
     * @param attachmentFile
     * @return
     */
    public ComAttachmentFile applySinglePrint(String applyNo, ComAttachmentFile attachmentFile, String serviceClientType) {

        // 获取模板参数
        ApplyTemplateVO templateVO = this.getApplyTemplatePara(applyNo, serviceClientType);
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        ComAttachmentFile comAttachmentFile = comPrintFormManageService.print(jsonTemp, attachmentFile.getAttachmentCode(), applyNo, serviceClientType);
        return comAttachmentFile;
    }

    /**
     * 进件申请-单个模板生成
     *
     * @param applyNo
     * @param attachmentFile
     * @return
     */
    @Override
    public ComAttachmentFile applySinglePrintByCustId(String applyNo, ComAttachmentFile attachmentFile, String serviceClientType) {

        // 获取模板参数
        ApplyTemplateVO templateVO = this.getApplyTemplatePara(applyNo, serviceClientType);
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        ComAttachmentFile comAttachmentFile = comPrintFormManageService.print(jsonTemp, attachmentFile.getAttachmentCode(), applyNo, serviceClientType);
        return comAttachmentFile;
    }

    /**
     * 进件征信授权书-模板生成
     *
     * @param applyNo
     */
    public List<ComAttachmentFile> applyAuthorBatchPrint(String busiType, String applyNo, String certNo, String serviceClientType) {

        List<ComAttachmentFile> fileList = new ArrayList<ComAttachmentFile>();
        // 模板VO
        ApplyTemplateVO templateVO = new ApplyTemplateVO();
        templateVO.setPrintDate(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        ApplyCustBaseInfo applyCustBaseInfo = applyCustBaseInfoService.getCustInfoByCertNo(applyNo, certNo);
        Assert.isTrue(applyCustBaseInfo != null, "客户信息不能为空");
        // 主借人信息
        if (ApplyConstants.PRINCIPAL_BORROWER.equals(applyCustBaseInfo.getCustRole())) {
            ComPrintFormManage comPrintFormManage = comPrintFormManageService.getPrintFormManageByUniqueCode(AttachmentUniqueCodeEnum.MAIN_LETTER_OF_AUTHORIZATION.getCode());
            Assert.isTrue(comPrintFormManage != null, "承租人征信授权书不存在");
            ApplyMainCustVO mainCustVO = this.applyMainCustVO(applyNo, ApplyConstants.PRINCIPAL_BORROWER, true);
            templateVO.setMainCust(mainCustVO);
            JSONObject originJson = (JSONObject) JSONObject.toJSON(templateVO);
            ComAttachmentFile attachmentFile = comPrintFormManageService.print(originJson, comPrintFormManage.getId().toString(), applyNo, serviceClientType);
            if (attachmentFile != null) {
                fileList.add(attachmentFile);
            }
        }
        // 担保人信息
        else if (ApplyConstants.GUARANTOR.equals(applyCustBaseInfo.getCustRole())) {
            ComPrintFormManage comPrintFormManage = comPrintFormManageService.getPrintFormManageByUniqueCode(AttachmentUniqueCodeEnum.GUARANTEE_LETTER_OF_AUTHORIZATION.getCode());
            Assert.isTrue(comPrintFormManage != null, "担保人征信授权书不存在");
            ApplyMainCustVO bailCustVO = this.applyMainCustVO(applyNo, ApplyConstants.GUARANTOR, false);
            templateVO.setBailCust(bailCustVO);
            JSONObject originJson = (JSONObject) JSONObject.toJSON(templateVO);
            ComAttachmentFile attachmentFile = comPrintFormManageService.print(originJson, comPrintFormManage.getId().toString(), applyNo, serviceClientType);
            if (attachmentFile != null) {
                fileList.add(attachmentFile);
            }
        }
        if (fileList != null && fileList.size() > 0 && AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
            applySignRelationService.saveSignRelationList(fileList, applyNo);
        }
        return fileList;
    }

    /**
     * 预审批-模板参数
     *
     * @param busiType
     * @param id
     * @return
     */
    public PreAuthorTemplateVO getPreAuthorTemplatePara(String busiType, Long id) {

        // 模板VO
        PreAuthorTemplateVO templateVO = new PreAuthorTemplateVO();
        // 订单信息
        PreApproveInfo preApproveInfo = preApproveService.getOne(Wrappers.<PreApproveInfo>query().lambda().eq(PreApproveInfo::getId, id).eq(PreApproveInfo::getBusinessType, busiType));
        templateVO.setPreInfo(preApproveInfo);
        return templateVO;
    }


    /**
     * 核准函-模板参数
     *
     * @param
     * @param
     * @return
     */
    public ContractTemplateVO getApproverLetterTemplatePara(String applyNo,String serviceClientType) {

        BigDecimal purchaseTaxAmt = BigDecimal.ZERO; //购置税
        BigDecimal insuranceAmt = BigDecimal.ZERO; //保险
        BigDecimal gpsAmt = BigDecimal.ZERO; //gps加融
        BigDecimal jpzhAmt = BigDecimal.ZERO;//精品装潢
        //保证金
        BigDecimal bond = BigDecimal.ZERO;
        //保险金额
        BigDecimal rance = BigDecimal.ZERO;
        //精品+gps
        BigDecimal equipment = BigDecimal.ZERO;
        // 购置税
        BigDecimal purchaseTax = BigDecimal.ZERO;
        // 其他(费用)
        BigDecimal otherAmt = BigDecimal.ZERO;
        // 模板VO
        ContractTemplateVO templateVO = new ContractTemplateVO();
        //贷款知情函（LCV）
        List<DecorationItem> items = new ArrayList();
        DecorationItem decorationItem1 = new DecorationItem();
        DecorationItem decorationItem2 = new DecorationItem();
        DecorationItem decorationItem3 = new DecorationItem();
        DecorationItem decorationItem4 = new DecorationItem();
        DecorationItem decorationItem5 = new DecorationItem();
        items.add(decorationItem1);
        items.add(decorationItem2);
        items.add(decorationItem3);
        items.add(decorationItem4);
        items.add(decorationItem5);
        //附加贷进件申请
        List<FinFinancingItems> zhFinItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo).eq(FinFinancingItems::getFinanceItemCode, ApplyConstants.DECORATE));
        //附加贷实际成交价
        List<ApplyAddPriceItems> zhAddItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                .eq(ApplyAddPriceItems::getApplyNo, applyNo).eq(ApplyAddPriceItems::getFinanceItemCode, ApplyConstants.DECORATE));
        if (zhFinItems.size() > 0) {
            //装潢小类金额
            List<FinFinancingItems> zhFinFinacingItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                    .eq(FinFinancingItems::getUpperId, zhFinItems.get(0).getId()));
            for (int i = 0; i < zhFinFinacingItems.size(); i++) {
                items.get(i).setFinanceItemName(zhFinFinacingItems.get(i).getFinanceItemName());
                items.get(i).setFinanceItemAmt(zhFinFinacingItems.get(i).getFinanceItemAmt() == null ? BigDecimal.ZERO : zhFinFinacingItems.get(i).getFinanceItemAmt());
            }
        }
        if (zhAddItems.size() > 0) {
            //装潢小类实际成交价
            List<ApplyAddPriceItems> zhAddFinacingItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                    .eq(ApplyAddPriceItems::getUpperId, zhAddItems.get(0).getId()));
            for (int i = 0; i < zhAddFinacingItems.size(); i++) {
                items.get(i).setAddFinanceAmt(zhAddFinacingItems.get(i).getAddFinanceAmt() == null ? BigDecimal.ZERO : zhAddFinacingItems.get(i).getAddFinanceAmt());
            }
        }
        templateVO.setZhItems(items);
        //贷款知情确认函二手车
        BoutiqueItem boutiqueItem = new BoutiqueItem();
        //精品附加贷
        List<FinFinancingItems> jpFinFinacingItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo).eq(FinFinancingItems::getFinanceItemCode, ApplyConstants.BOUTIQUE));
        //精品小类
        if (jpFinFinacingItems.size() > 0) {
            List<FinFinancingItems> jpFinItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                    .eq(FinFinancingItems::getUpperId, jpFinFinacingItems.get(0).getId().toString()));
            if (jpFinItems.size() > 0) {
                for (FinFinancingItems financingItems : jpFinItems) {
                    if (financingItems.getFinanceItemCode().equals(ApplyConstants.NAVIGATION)) {
                        boutiqueItem.setFinanceItemAmt1(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.FILM)) {
                        boutiqueItem.setFinanceItemAmt2(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.TACHOGRAPH)) {
                        boutiqueItem.setFinanceItemAmt3(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.INSOLE_COVER)) {
                        boutiqueItem.setFinanceItemAmt4(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.SOUND)) {
                        boutiqueItem.setFinanceItemAmt5(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.DUST_COVER)) {
                        boutiqueItem.setFinanceItemAmt6(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.CAR_COVER)) {
                        boutiqueItem.setFinanceItemAmt7(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.CAR_DISINFECTION)) {
                        boutiqueItem.setFinanceItemAmt8(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.DEEP_DISINFECTION)) {
                        boutiqueItem.setFinanceItemAmt9(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.POLISH_AND_WAXE)) {
                        boutiqueItem.setFinanceItemAmt10(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    } else if (financingItems.getFinanceItemCode().equals(ApplyConstants.HEADLIGHT)) {
                        boutiqueItem.setFinanceItemAmt11(financingItems.getFinanceItemAmt() == null ? BigDecimal.ZERO : financingItems.getFinanceItemAmt());
                    }
                }
            }
        }
        //精品实际成交价
        List<ApplyAddPriceItems> jpAddFinacingItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                .eq(ApplyAddPriceItems::getApplyNo, applyNo).eq(ApplyAddPriceItems::getFinanceItemCode, ApplyConstants.BOUTIQUE));
        //精品实际成交价小类
        if (jpAddFinacingItems.size() > 0) {
            List<ApplyAddPriceItems> jpAddItems = applyAddPriceItemsService.list(Wrappers.<ApplyAddPriceItems>query().lambda()
                    .eq(ApplyAddPriceItems::getUpperId, jpAddFinacingItems.get(0).getId().toString()));
            if (jpAddItems.size() > 0) {
                for (ApplyAddPriceItems addPriceItems : jpAddItems) {
                    if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.NAVIGATION)) {
                        boutiqueItem.setAddFinanceAmt1(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.FILM)) {
                        boutiqueItem.setAddFinanceAmt2(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.TACHOGRAPH)) {
                        boutiqueItem.setAddFinanceAmt3(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.INSOLE_COVER)) {
                        boutiqueItem.setAddFinanceAmt4(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.SOUND)) {
                        boutiqueItem.setAddFinanceAmt5(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.DUST_COVER)) {
                        boutiqueItem.setAddFinanceAmt6(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.CAR_COVER)) {
                        boutiqueItem.setAddFinanceAmt7(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.CAR_DISINFECTION)) {
                        boutiqueItem.setAddFinanceAmt8(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.DEEP_DISINFECTION)) {
                        boutiqueItem.setAddFinanceAmt9(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.POLISH_AND_WAXE)) {
                        boutiqueItem.setAddFinanceAmt10(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    } else if (addPriceItems.getFinanceItemCode().equals(ApplyConstants.HEADLIGHT)) {
                        boutiqueItem.setAddFinanceAmt11(addPriceItems.getAddFinanceAmt() == null ? BigDecimal.ZERO : addPriceItems.getAddFinanceAmt());
                    }
                }
            }
        }
        //保险费用合计
        List<FinFinancingItems> finItems = applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda().eq(FinFinancingItems::getApplyNo, applyNo));
        if (finItems.size()>0) {
            for (FinFinancingItems financingItems : finItems) {
                if (CostType.SYXAMT.getIndex().equals(financingItems.getFinanceItemCode()) || CostType.JQXAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    rance = rance.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.JPAMT.getIndex().equals(financingItems.getFinanceItemCode()) || CostType.GPSAMT.getIndex().equals(financingItems.getFinanceItemCode())){
                    equipment = equipment.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.GZSAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    purchaseTax = purchaseTax.add(financingItems.getFinanceItemAmt());
                }
                if (CostType.OTHERAMT.getIndex().equals(financingItems.getFinanceItemCode())) {
                    otherAmt = otherAmt.add(financingItems.getFinanceItemAmt());
                }
            }
        }
        templateVO.setJpItems(boutiqueItem);
        // 订单信息
        ApplyOrderInfo orderInfo = orderInfoService.getOrderInfoByApplyNo(applyNo);
        orderInfo.setCarTypeName(ApplyUtils.dicData(orderInfo.getCarType(), "carType"));
        orderInfo.setBusinessTypeName(ApplyUtils.dicData(orderInfo.getBusinessType(), "businessType"));
        orderInfo.setCarPurposeName(ApplyUtils.dicData(orderInfo.getCarPurpose(), "carPurpose"));
        orderInfo.setBelongingCapitalName(ApplyUtils.dicData(orderInfo.getBelongingCapital(), "belongingCapitalName"));
        orderInfo.setOperateWay(OperateWay.getName(orderInfo.getOperateWay()));
        orderInfo.setRiskPassDateStr(DatePattern.CHINESE_DATE_FORMAT.format(orderInfo.getRiskPassDate()));
        templateVO.setOrder(orderInfo);
        //发票信息
        ApplyCarInvoice applyCarInvoice = applyCarInvoiceService.getApplyCarInvoice(applyNo);
        templateVO.setCarInvoice(applyCarInvoice);
        // 渠道信息
        ApplyChannelInfo channelInfo = applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        channelInfo.setReceiptBankName(channelInfo.getReceiptBankCode());

        JSONObject jsonObject = applyChannelInfoService.getChannelDetailInfo(channelInfo.getChannelId(), "");
        Assert.notNull(jsonObject, "经销商信息查询失败!");
        JSONObject channelBaseInfo = jsonObject.getJSONObject("data").getJSONObject("channelBaseInfo");
        Assert.notNull(channelBaseInfo, "经销商信息查询失败!");
        ChannelBaseInfo channelBase = JSONObject.parseObject(JSONObject.toJSONString(channelBaseInfo),ChannelBaseInfo.class);
        channelInfo.setSocUniCrtCode(channelBase.getSocUniCrtCode());
        channelInfo.setChannelAdmin(channelBase.getChannelAdmin());
        channelInfo.setChannelAdminTel(channelBase.getChannelAdminTel());
        if(!ObjectUtil.isNull(channelBase.getChannelProvince()) && !ObjectUtil.isNull(channelBase.getChannelCity()) && !ObjectUtil.isNull(channelBase.getChannelAddress())){
            StringBuffer str = new StringBuffer();
            if (StringUtil.isNotEmpty(channelBase.getChannelProvince())) {
                str.append(this.provinceOrCity(channelBase.getChannelProvince()));
            }
            if (StringUtil.isNotEmpty(channelBase.getChannelCity())) {
                str.append(this.provinceOrCity(channelBase.getChannelCity()));
            }
            if (StringUtil.isNotEmpty(channelBase.getChannelCity())) {
                str.append(this.provinceOrCity(channelBase.getChannelCity()));
            }
            channelInfo.setChannelAddress(str + channelBase.getChannelAddress() == null ? "" : channelBase.getChannelAddress());
        }else {
            channelInfo.setChannelAddress(channelBase.getChannelAddress());
        }
        templateVO.setChannel(channelInfo);
        // 合同信息
        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        if (null != contractInfo.getLoanDate()) {
            contractInfo.setLoanTime(DatePattern.CHINESE_DATE_FORMAT.format(contractInfo.getLoanDate()));
        }
        if (null != contractInfo.getStartDate()) {
            contractInfo.setStartTime(DatePattern.CHINESE_DATE_FORMAT.format(contractInfo.getStartDate()));
        }
        templateVO.setContract(contractInfo);
        // 车辆信息
        ApplyCarDetails carDetails = applyCarDetailsService.getCarDetailsByApplyNo(applyNo);
        templateVO.setCarDetails(carDetails);
        // 主借人信息
        ApplyMainCustVO mainCustVO = this.applyMainCustVO(applyNo, ApplyConstants.PRINCIPAL_BORROWER, true);
        templateVO.setMainCust(mainCustVO);

        // 担保人签章信息
        List<ApplyMainCustVO> applyMainCustInfo = this.applyGuaranteeCustVO(applyNo, ApplyConstants.GUARANTOR, false);
        GuarantorSignVO guarantorSignVO = new GuarantorSignVO();
        guarantorSignVO.setBailCustFir("");
        guarantorSignVO.setBailCustSco("");
        guarantorSignVO.setBailCustThr("");
        log.info("保证人个数：" + applyMainCustInfo.size());
        if (null != applyMainCustInfo) {
            if (applyMainCustInfo.size() >= 1) {
                guarantorSignVO.setBailCustFir("保证人一签章:__________________");
            }
            if (applyMainCustInfo.size() >= 2) {
                guarantorSignVO.setBailCustSco("保证人二签章:__________________");
            }
            if (applyMainCustInfo.size() >= 3) {
                guarantorSignVO.setBailCustThr("保证人三签章:__________________");
            }
        }
        templateVO.setGuarantorSignature(guarantorSignVO);
        // 担保人信息..
        List<ApplyMainCustVO> bailCustVO = this.applyGuaranteeCustVO(applyNo, ApplyConstants.GUARANTOR, false);
        //担保人信息顺位 第一担保人--> 个人 --> 企业
        List<ApplyMainCustVO> collectBailCustList = bailCustVO.stream().sorted(Comparator.comparing(ApplyMainCustVO::getIsFirstGuarantor).reversed()
                        .thenComparing(Comparator.comparing(ApplyMainCustVO::getCustType))
                        .thenComparing(Comparator.comparing(ApplyMainCustVO::getFirstGuarantor)))
                .collect(Collectors.toList());
        if(bailCustVO.size() > 3){
            collectBailCustList = collectBailCustList.subList(0,4);
        }
        //担保人排序完成后，顺位赋值
        for (int i = 0; i < collectBailCustList.size(); i++) {
            collectBailCustList.get(i).setFirstGuarantor("0"+(i+1));
        }
        log.info("collectBailCustList-->{}",JSONObject.toJSONString(collectBailCustList));
        ApplyCustBaseInfo one = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER)
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo));
        if (one.getCustType().equals(ApplyConstants.ENTERPRISE)
                || ApplyConstants.IF_PERSONAL_TO_ENTERPRISE.equals(orderInfo.getIfPersonalToEnterprise())) {
            ApplyEnterpriseCustomerDetails detailsServiceOne = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                    .eq(ApplyEnterpriseCustomerDetails::getCustId,one.getId())
                    .eq(ApplyEnterpriseCustomerDetails::getApplyNo,one.getApplyNo()));
            if (detailsServiceOne.getNatureEnterprise().equals(ApplyConstants.GTGSH_COMPANY)){
                for (ApplyMainCustVO guarantee:bailCustVO) {
                    //判断担保人类型是否为企业 为企业时查询企业信息表
                    if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                        ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                                .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                        if (ObjectUtils.isNotEmpty(enterprise)) {
                            guarantee.setCustName(enterprise.getEnterpriseName());
                            guarantee.setCertNo(enterprise.getSocunicrtCode());
                            guarantee.setTelPhone(enterprise.getCompanyPhone());
                        }
                    }
                    if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust1(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust2(guarantee);
                    }
                    if ("04".equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust3(guarantee);
                    }
                }
            } else {
                for (ApplyMainCustVO guarantee : collectBailCustList) {
                    //判断担保人类型是否为企业 为企业时查询企业信息表
                    if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                        ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                                .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                        if (ObjectUtils.isNotEmpty(enterprise)) {
                            guarantee.setCustName(enterprise.getEnterpriseName());
                            guarantee.setCertNo(enterprise.getSocunicrtCode());
                            guarantee.setTelPhone(enterprise.getCompanyPhone());
                        }
                    }
                    if (GuaranteeTypeEnum.DBR1.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust1(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust2(guarantee);
                    }
                    if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                        templateVO.setBailCust3(guarantee);
                    }
                }
            }
        }else {
            for (ApplyMainCustVO guarantee : collectBailCustList) {
                //判断担保人类型是否为企业 为企业时查询企业信息表
                if (ApplyConstants.ENTERPRISE.equals(guarantee.getCustType())) {
                    ApplyEnterpriseCustomerDetails enterprise = customerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>lambdaQuery()
                            .eq(ApplyEnterpriseCustomerDetails::getCustId, guarantee.getCustId()));
                    if (ObjectUtils.isNotEmpty(enterprise)) {
                        guarantee.setCustName(enterprise.getEnterpriseName());
                        guarantee.setCertNo(enterprise.getSocunicrtCode());
                        guarantee.setTelPhone(enterprise.getCompanyPhone());
                    }
                }
                if (GuaranteeTypeEnum.DBR1.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust1(guarantee);
                }
                if (GuaranteeTypeEnum.DBR2.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust2(guarantee);
                }
                if (GuaranteeTypeEnum.DBR3.getCode().equals(guarantee.getFirstGuarantor())) {
                    templateVO.setBailCust3(guarantee);
                }
            }
        }
        //因为合同上要求，无担保人就显示无
        if (templateVO.getBailCust1() == null) {
            ApplyMainCustVO guarantee1 = new ApplyMainCustVO();
            guarantee1.setCustName("无");
            templateVO.setBailCust1(guarantee1);
        }
        if (templateVO.getBailCust2() == null) {
            ApplyMainCustVO guarantee2 = new ApplyMainCustVO();
            guarantee2.setCustName("无");
            templateVO.setBailCust2(guarantee2);
        }
        if (templateVO.getBailCust3() == null) {
            ApplyMainCustVO guarantee3 = new ApplyMainCustVO();
            guarantee3.setCustName("无");
            templateVO.setBailCust3(guarantee3);
        }
        templateVO.setPrintDate3(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        //纸质为空，电子为当前日期
        if (AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)){
            templateVO.setPrintDate(DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        }
        //车辆融资信息
        FinCostDetails carCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, CostType.CARAMT.getIndex());
        if (carCostDetails != null) {
            if (carCostDetails.getBasicPoint() != null && carCostDetails.getBasicPoint() != 0) {
                carCostDetails.setRateType("浮动");
            }
            if (carCostDetails.getMarginAmount() != null) {
                carCostDetails.setMarginAmount(carCostDetails.getMarginAmount().compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal("0.00") : carCostDetails.getMarginAmount());
            }
            bond = bond.add(carCostDetails.getCustMarginAmt() == null ? BigDecimal.ZERO : carCostDetails.getCustMarginAmt());

            if (StringUtils.isNotEmpty(carCostDetails.getRepaymentFrequency())) {
                carCostDetails.setRepaymentFrequencyName(AfsEnumUtil.getEnum(carCostDetails.getRepaymentFrequency(), RepaymentFrequencyEnum.class) != null ?
                        AfsEnumUtil.desc(AfsEnumUtil.getEnum(carCostDetails.getRepaymentFrequency(), RepaymentFrequencyEnum.class)) : "");
            }
            //小微订单租赁期限 = 租赁月份/还款频率；
            if (StringUtils.isNotEmpty(orderInfo.getOrderType()) && ApplyConstants.ORDER_TYPE_XW.equals(orderInfo.getOrderType())
                    && org.springframework.util.StringUtils.hasLength(carCostDetails.getRepaymentFrequency()) && carCostDetails.getLoanTerm() != null) {
                Integer frequency = BigDecimal.ZERO.compareTo(new BigDecimal(carCostDetails.getRepaymentFrequency())) < 0
                        ? Integer.parseInt(carCostDetails.getRepaymentFrequency()) : 1;
                carCostDetails.setLoanTerm(carCostDetails.getLoanTerm() / frequency);
            }
            carCostDetails.setRepaymentMethodName(ApplyUtils.dicData(carCostDetails.getRepaymentMethod(), "repaymentMethod"));
        }

        //设置手续费
        carCostDetails.setCustHandlingAmt(getCustHandlingFeeAmt(carCostDetails));
        templateVO.setCarCost(carCostDetails);
        //附加贷融资信息
        FinCostDetails addCostDetails = applyCostDetailsService.getApplyCostDetails(applyNo, ApplyConstants.COST_TYPE_ADD_LOAN);
        if (bond.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            bond = new BigDecimal("0.00");
        }
        templateVO.setAddCost(addCostDetails);
        //银行卡信息
        Integer term = 0;
        if (carCostDetails != null && addCostDetails != null) {
            term = carCostDetails.getLoanTerm() > addCostDetails.getLoanTerm() ? carCostDetails.getLoanTerm() : addCostDetails.getLoanTerm();
        } else if (carCostDetails != null) {
            term = carCostDetails.getLoanTerm();
        } else if (addCostDetails != null) {
            term = addCostDetails.getLoanTerm();
        }
        List<ApplyBankCard> applyBankCards = applyBankCardService.getBankCardByApplyNo(applyNo);
        //是否不存在个人银行卡信息
        for (ApplyBankCard applyBankCard : applyBankCards) {
            if (AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT).equals(applyBankCard.getIsDefaultDeductCard())) {
                // add by sijun.yu 2021-1-3 开户银行不能为空
                if (AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
                    Assert.isTrue(applyBankCard != null && StringUtils.isNotEmpty(applyBankCard.getBankCode()), "开户银行不能为空");
                }
                if (applyBankCard != null) {
                    applyBankCard.setCertPhone(applyBankCard.getBankPhone());
                    String dickey= ApplyConstants.CARD_SIGN_PRE+applyBankCard.getBankCode();
                    List<DicDataDto> dicDataDtos = DicHelper.getDicMaps(dickey).get(dickey);
                    for (DicDataDto dicDataDto : dicDataDtos) {
                        if (Objects.equals(dicDataDto.getValue(),applyBankCard.getCertType())) {
                            applyBankCard.setCertTypeName(dicDataDto.getTitle());
                            break;
                        }
                    }
                    if (StrUtil.isBlank(applyBankCard.getCertTypeName())) {
                        applyBankCard.setCertTypeName(CertTypeEnum.IDCARD.getName());
                    }
                    applyBankCard.setDeadlineDate((DateUtil.offset(orderInfo.getRiskPassDate(), DateField.MONTH, term + 4)).toString("yyyy-MM-dd"));
                    applyBankCard.setBankBranch(applyBankCard.getBankBranch());
                    templateVO.setBankCard(applyBankCard);
                }
            }
        }
        //挂靠信息
        ApplyAffiliatedUnit applyAffiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(applyNo);
        if (applyAffiliatedUnit != null) {
            if (StringUtils.isNotEmpty(applyAffiliatedUnit.getRegistProvince()) && StringUtils.isNotEmpty(applyAffiliatedUnit.getRegistCity())) {
                log.info("=====挂靠信息getRegistProvince：" + applyAffiliatedUnit.getRegistProvince());
                log.info("=====挂靠信息getRegistCity：" + applyAffiliatedUnit.getRegistProvince());
                StringBuffer str = new StringBuffer();
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistProvince())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistProvince()));
                }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistCity())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistCity()));
                }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistCounty())) {
                    str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistCounty()));
                }
                // 暂时注释 if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistStreet())) { str.append(this.provinceOrCity(applyAffiliatedUnit.getRegistStreet())); }
                if (StringUtil.isNotEmpty(applyAffiliatedUnit.getRegistDoors())) {
                    str.append(applyAffiliatedUnit.getRegistDoors());
                }
                applyAffiliatedUnit.setRegistDetailAddress(str + applyAffiliatedUnit.getRegistAddress() == null ? "" : applyAffiliatedUnit.getRegistAddress());
            }
            templateVO.setUnit(applyAffiliatedUnit);
        }
        //各种费用项信息
        FinanceItemVO financeItemVO = new FinanceItemVO();
        //加装设备费=GPS费用+精品装潢  GPS费用=（GPS加融+GPS成本价）
        //租赁车辆总价款 =  裸车价（车款 ） +保险费+ 购置税 + 加装设备费+ 牌照费
        //融资总额（客户融资额）=  租赁车辆总价款 - 首付租金（首付金额）- （放款时扣除的）保证金和手续费
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal equipmentCost = BigDecimal.ZERO;
        BigDecimal safetyControlManageFee = BigDecimal.ZERO;
        BigDecimal totalLoans = BigDecimal.ZERO;
        BigDecimal amountAfterDiscount = BigDecimal.ZERO;
        //主合同租赁车辆总价款  裸车价（车款 ） +保险费+ 购置税 + 主合同加装设备费+ 牌照费
        BigDecimal mainTotalPrice = BigDecimal.ZERO;
        //主合同加装设备费 GPS费用+精品装潢
        BigDecimal mainEquipmentCost = BigDecimal.ZERO;
        if (carCostDetails.getGpsCostPrice() != null) {
            equipmentCost = equipmentCost.add(gpsAmt).add(carCostDetails.getGpsCostPrice()).add(jpzhAmt);
            safetyControlManageFee = safetyControlManageFee.add(carCostDetails.getGpsCostPrice());
        } else {
            //gps成本价为空时
            equipmentCost = equipmentCost.add(gpsAmt).add(jpzhAmt);
        }
        if (equipmentCost.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            equipmentCost = new BigDecimal("0.00");
        }
        mainEquipmentCost = mainEquipmentCost.add(gpsAmt).add(jpzhAmt);
        if (mainEquipmentCost.compareTo(BigDecimal.ZERO) == 0) {
            //初始化一个0.00方便合同显示
            mainEquipmentCost = new BigDecimal("0.00");
        }

        totalPrice = totalPrice.add(carDetails.getSalePrice()).add(insuranceAmt).add(purchaseTaxAmt).add(equipmentCost);
        totalLoans = totalLoans.add(totalPrice).subtract(carCostDetails.getDownPayAmt()).subtract(carCostDetails.getCustMarginAmt() == null ? BigDecimal.ZERO : carCostDetails.getCustMarginAmt());

        ApplyCarDeposit applyCarDeposit = applyCarDepositService.getOne(Wrappers.<ApplyCarDeposit>lambdaQuery()
                .eq(ApplyCarDeposit::getApplyNo, applyNo));
        if (ObjectUtil.isNotNull(applyCarDeposit)) {
            if (AfsEnumUtil.key(PaymentMethodEnum.WRITE_OFF).equals(carCostDetails.getDownPaymentMethod())
                    && ApplyConstants.YES_FLAG.equals(applyCarDeposit.getPayDepositFlag())
                    && ApplyConstants.YES_FLAG.equals(applyCarDeposit.getDeductFlag())
                    && PayDepositObjectEnum.CAR_SHOP.getCode().equals(applyCarDeposit.getPayDepositObject())) {
                amountAfterDiscount = amountAfterDiscount.subtract(applyCarDeposit.getDepositAmount());
            }
        }
        mainTotalPrice = mainTotalPrice.add(carDetails.getSalePrice()).add(insuranceAmt).add(purchaseTaxAmt).add(mainEquipmentCost);
        financeItemVO.setEquipmentCost(equipment);
        financeItemVO.setTotalPrice(totalPrice);
        financeItemVO.setSafetyControlManageFee(safetyControlManageFee);
        financeItemVO.setTotalLoans(totalLoans);
        financeItemVO.setAmountAfterDiscount(amountAfterDiscount);
        financeItemVO.setMainEquipmentCost(mainEquipmentCost);
        financeItemVO.setMainTotalPrice(mainTotalPrice);
        financeItemVO.setInsurancePremium(rance);
        financeItemVO.setBond(bond);
        financeItemVO.setPurchaseTax(purchaseTax);
        financeItemVO.setOtherFee(otherAmt);
        //设置手续费
        financeItemVO.setServiceFee(carCostDetails.getCustHandlingAmt());
        templateVO.setFinItems(financeItemVO);
        //获取直营申请开票信息
        ApplyReceiptInfo applyReceiptInfoByApplyNo = applyReceiptInfoService.getApplyReceiptInfoByApplyNo(applyNo);
        if (ObjectUtils.isNotEmpty(applyReceiptInfoByApplyNo)) {
            templateVO.setApplyReceiptInfo(applyReceiptInfoByApplyNo);
        }

        //融资费用主表查询
        FinMainInfo finMainInfo = finMainInfoService.getFinMainInfoByApplyNo(applyNo);
        //月租金（平均）租金总额（不含留购金）/ 租赁期数，保留2位，四舍五入
        BigDecimal totalRent = carCostDetails.getTotalRent().divide(new BigDecimal(carCostDetails.getLoanTerm()),2,BigDecimal.ROUND_HALF_UP);
        finMainInfo.setRedTotalRent(totalRent);
        // todo 盖章规则
        ChannelInfoUniteInfoVo infoVo = applyCarInvoiceService.applyToChannelUniteInfo(contractInfo.getContractNo());
        templateVO.setChannelInfoUniteInfo(infoVo);
        templateVO.setTotalInfo(finMainInfo);

        log.info("合同模板参数VO：{}", JSONObject.toJSONString(templateVO));
        return templateVO;

    }

    /**
     * 预审批-批量模板生成
     *
     * @param busiType
     * @param id
     * @return
     */
    public List<ComAttachmentFile> preAuthorBatchPrint(String busiType, Long id, String serviceClientType) {

        // 获取模板参数
        PreAuthorTemplateVO templateVO = this.getPreAuthorTemplatePara(busiType, id);
        log.info("预申请阶段模板数据templateVO--> {}",JSONObject.toJSONString(templateVO));
        // 规则VO
        TemplateRuleVO ruleVO = new TemplateRuleVO();
        ruleVO.setBusinessType(busiType);
        ruleVO.setServiceClientType(serviceClientType);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        log.info("预申请阶段规则信息==> {}", jsonRule.toJSONString());
        List<ComAttachmentFile> fileList;
        try {
            fileList = comPrintFormManageService.batchPrint(jsonTemp, jsonRule, id.toString(), "printPreApply", serviceClientType);
        } catch (Exception e) {
            log.error("预申请阶段生成模板失败", e);
            throw new AfsBaseException("预申请阶段生成模板失败! 请稍后重试!");
        }
        if (CollectionUtil.isNotEmpty(fileList) && AfsEnumUtil.key(ServiceClientTypeEnum.APP).equals(serviceClientType)) {
            applySignRelationService.savePreRelationList(fileList);
        }
        return fileList;
    }


    /**
     * 核准函-核准函模板生成 - 进件审核后
     *
     * @param
     * @param
     * @return
     */
    @Override
    public List<ComAttachmentFile> approverLetterBatchPrint(String applyNo,String serviceClientType) {

        // 获取模板参数
        ContractTemplateVO templateVO = this.getApproverLetterTemplatePara(applyNo,serviceClientType);
        log.info("进件后阶段核准函模板数据templateVO--> {}",JSONObject.toJSONString(templateVO));
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        log.info("进件后阶段核准函模板规则信息==> {}", jsonRule.toJSONString());
        List<ComAttachmentFile> fileList = comPrintFormManageService.batchPrint(jsonTemp, jsonRule, applyNo, "printApproveLetter", serviceClientType);
        return fileList;
    }

    /**
     * 核准函---模板打印生成（不跑规则）
     */
    @Override
    public ComAttachmentFile approverLetterNoRulePrint(String applyNo,String serviceClientType,String printId) {
        // 获取模板参数
        ContractTemplateVO templateVO = this.getApproverLetterTemplatePara(applyNo,serviceClientType);
        log.info("进件后阶段核准函模板数据templateVO--> {}",JSONObject.toJSONString(templateVO));
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        return comPrintFormManageService.print(jsonTemp, printId, applyNo, serviceClientType);
    }

    /**
     * 预审批-单个模板生成
     *
     * @param busiType
     * @param busiNo
     * @param attachmentFile
     * @return
     */
    public ComAttachmentFile preAuthorSinglePrint(String busiType, String busiNo, ComAttachmentFile attachmentFile, String serviceClientType) {

        // 获取模板参数
        PreAuthorTemplateVO templateVO = this.getPreAuthorTemplatePara(busiType, Long.valueOf(busiNo));
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        ComAttachmentFile comAttachmentFile = comPrintFormManageService.print(jsonTemp, attachmentFile.getAttachmentCode(), busiNo, serviceClientType);
        return comAttachmentFile;
    }

    /**
     * 翻译省份城市
     */
    public String provinceOrCity(String provinceOrCity) {
        provinceOrCity = StringUtils.isNotEmpty(provinceOrCity) && provinceOrCity.length() < 6 ? StrUtil.padAfter(provinceOrCity,6,"0") : provinceOrCity;
        log.info("======地区编码：" + provinceOrCity);
        String province = addressService.getLabelByCode(provinceOrCity);
        Assert.isTrue(StringUtils.isNotEmpty(province), "省份城市不允许为空");
        return province;
    }

    /**
     * 主共保信息
     *
     * @param applyNo
     * @param custRule
     * @param flag
     * @return
     */
    public ApplyMainCustVO applyMainCustVO(String applyNo, String custRule, boolean flag) {
        ApplyCustBaseInfo applyMainCustInfo = new ApplyCustBaseInfo();
        if (CustRoleEnum.GUARANTOR.getCode().equals(custRule)){
             applyMainCustInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.GUARANTOR.getCode())
                .eq(ApplyCustBaseInfo::getIsFirstGuarantor,WhetherEnum.YES.getIndex()));
        }else {
             applyMainCustInfo = applyCustBaseInfoService.getCustBaseInfo(applyNo, custRule);
        }
        //获取进件类型
        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>query().lambda()
                .eq(ApplyOrderInfo::getApplyNo, applyNo));
        if (AfsEnumUtil.key(CustTypeEnum.COMPANY).equals(applyOrderInfo.getInputType())
                || ApplyConstants.IF_PERSONAL_TO_ENTERPRISE.equals(applyOrderInfo.getIfPersonalToEnterprise())) {
            //获取承租人id
            ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                    .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                    .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            //获取企业名称
            List<ApplyEnterpriseCustomerDetails> customerDetails = customerDetailsService.list(Wrappers.<ApplyEnterpriseCustomerDetails>query().lambda()
                    .eq(ApplyEnterpriseCustomerDetails::getCustId, custBaseInfo.getId()));
            if (ObjectUtils.isNotNull(customerDetails) && applyMainCustInfo != null) {
                applyMainCustInfo.setCustName(customerDetails.get(0).getEnterpriseName());
                //企业单修改法人身份证号为企业唯一编号
                applyMainCustInfo.setCertNo(customerDetails.get(0).getSocunicrtCode());
            }
        }
        return this.mergeInfo(applyMainCustInfo, applyNo, flag);
    }

    /**
     * 保证人信息
     *
     * @param applyNo
     * @param custRule
     * @param flag
     * @return
     */
    public List<ApplyMainCustVO> applyGuaranteeCustVO(String applyNo, String custRule, boolean flag) {
        //担保人信息
        List<ApplyCustBaseInfo> applyMainCustInfo = applyCustBaseInfoService.getCustBaseInfos(applyNo, custRule);
        List<ApplyMainCustVO> guaranteeVoList = new ArrayList<>();
        for (ApplyCustBaseInfo custBaseInfo : applyMainCustInfo) {
            ApplyMainCustVO applyMainCustVO = this.mergeInfo(custBaseInfo, applyNo, flag);
            guaranteeVoList.add(applyMainCustVO);
        }
        return guaranteeVoList;
    }

    /**
     * 整合主共保信息
     *
     * @param applyMainCustInfo
     * @param flag
     * @return
     */
    public ApplyMainCustVO mergeInfo(ApplyCustBaseInfo applyMainCustInfo, String applyNo, boolean flag) {
        ApplyMainCustVO mainCustVO = null;
        if (applyMainCustInfo != null) {
            mainCustVO = new ApplyMainCustVO();
            BeanUtils.copyProperties(applyMainCustInfo, mainCustVO);
            ApplyOrderInfo orderInfo = orderInfoService.getOrderInfoByApplyNo(applyNo);
            mainCustVO.setMail(applyMainCustInfo.getEmail());
            mainCustVO.setCustId(applyMainCustInfo.getId());
            /** 企业件只需要企业经营地地址信息 */
            if(org.apache.commons.lang3.StringUtils.equals(CustType.COMPANY.getIndex(),applyMainCustInfo.getCustType())
                    || (ApplyConstants.IF_PERSONAL_TO_ENTERPRISE.equals(orderInfo.getIfPersonalToEnterprise())
                    && ApplyConstants.PRINCIPAL_BORROWER.equals(applyMainCustInfo.getCustRole()))){
                ApplyCustAddressDetails permanentAddressDetails = applyCustAddressDetailsService.getApplyCustAddressDetails(applyMainCustInfo.getId(), ApplyConstants.PLACE_BUSINESS_ADDRESS);
                if (permanentAddressDetails != null) {
                    //户籍地址
                    mainCustVO.setPermanentAddress(getAddressByCode(permanentAddressDetails));
                }
            }
            /** 个人件需要居住地址, 户籍地址, 单位地址 */
            else {
                ApplyCustAddressDetails permanentAddressDetails = applyCustAddressDetailsService.getApplyCustAddressDetails(applyMainCustInfo.getId(), ApplyConstants.PERMANENT_ADDRESS);
                if (permanentAddressDetails != null) {
                    //户籍地址
                    mainCustVO.setPermanentAddress(getAddressByCode(permanentAddressDetails));
                }
                //现居住地址
                ApplyCustAddressDetails applyCustAddressDetails = applyCustAddressDetailsService.getApplyCustAddressDetails(applyMainCustInfo.getId(), ApplyConstants.RESIDENTIAL_ADDRESS);
                if (applyCustAddressDetails != null) {
                    mainCustVO.setLivingAddress(getAddressByCode(applyCustAddressDetails));
                    if (applyCustAddressDetails.getIsDefault() != null && applyCustAddressDetails.getIsDefault()) {
                        mainCustVO.setProvinceName(this.provinceOrCity(applyCustAddressDetails.getProvince()));//省份名称
                        mainCustVO.setCityName(this.provinceOrCity(applyCustAddressDetails.getCity()));//城市名称
                        mainCustVO.setMailingAddress(mainCustVO.getLivingAddress());//是否通讯地址；邮寄地址
                    }
                    //房产类型
                    mainCustVO.setHouseType(ApplyUtils.dicData(applyCustAddressDetails.getHouseType(), "houseType"));
                    mainCustVO.setPostalCode(applyCustAddressDetails.getPostalCode());
                }
                //单位地址
                ApplyCustAddressDetails addressDetails = applyCustAddressDetailsService.getApplyCustAddressDetails(applyMainCustInfo.getId(), ApplyConstants.COMPANY_ADDRESS);
                if (addressDetails != null) {
                    log.info("======单位地址：" + JSONObject.toJSONString(addressDetails));
                    mainCustVO.setWorkingAddress(getAddressByCode(addressDetails));//单位地址
                    if (addressDetails.getIsDefault() != null && addressDetails.getIsDefault()) {
                        mainCustVO.setProvinceName(this.provinceOrCity(addressDetails.getProvince()));//省份名称
                        mainCustVO.setCityName(this.provinceOrCity(addressDetails.getCity()));//城市名称
                        mainCustVO.setMailingAddress(mainCustVO.getWorkingAddress());//是否通讯地址；邮寄地址
                    }
                }
                //查询客户明细
                ApplyCustPersonalDetail applyCustPersonalDetail = applyCustPersonalServicel.getCustPersonalDetail(applyNo, applyMainCustInfo.getId());
                if (applyCustPersonalDetail != null) {
                    BeanUtils.copyProperties(applyCustPersonalDetail, mainCustVO);
                    //婚姻状态
                    mainCustVO.setMaritalStatus(ApplyUtils.dicData(applyCustPersonalDetail.getMaritalStatus(), "maritalStatus"));
                    //性别
                    mainCustVO.setSex(ApplyUtils.dicData(applyCustPersonalDetail.getSex(), "sex"));
                    //最高学历
                    mainCustVO.setHighestEducation(ApplyUtils.dicData(applyCustPersonalDetail.getHighestEducation(), "highestEducation"));
                    //行业类型
                    mainCustVO.setIndustryType(ApplyUtils.dicData(applyCustPersonalDetail.getIndustryType(), "industryType"));
                    //职业类型
                    mainCustVO.setJobsType(ApplyUtils.dicData(applyCustPersonalDetail.getJobsType(), "jobsType"));
                    //职位
                    mainCustVO.setPosition(ApplyUtils.dicData(applyCustPersonalDetail.getPosition(), "position"));
                    //单位名称
                    mainCustVO.setUnitName(applyCustPersonalDetail.getUnitName());
                    if (applyCustPersonalDetail.getMonthlyIncome() != null) {
                        //月收入
                        mainCustVO.setMonthlyIncome(ApplyUtils.numberToCnMontrayUnit(applyCustPersonalDetail.getMonthlyIncome()));
                        //年收入
                        mainCustVO.setYearlyIncome(ApplyUtils.numberToCnMontrayUnit(applyCustPersonalDetail.getMonthlyIncome().multiply(new BigDecimal(12))));
                    }
                }
                //证件类型
                mainCustVO.setCertType(ApplyUtils.dicData(applyMainCustInfo.getCertType(), "certType"));
                if (flag) {
                    //主借人户口
                    mainCustVO.setIsLocalAccount("本地户口");
                } else {
                    //与主借人关系
                    mainCustVO.setCustRelation(ApplyUtils.dicData(applyMainCustInfo.getCustRelation(), "custRelation"));
                }
                mainCustVO.setCertNo(applyMainCustInfo.getCertNo());
            }

        }
        return mainCustVO;
    }

    /**
     * 根据地址code拼接正确的地址
     *
     * @param permanentAddressDetails
     * @return
     */
    private String getAddressByCode(ApplyCustAddressDetails permanentAddressDetails) {
        StringBuffer str = new StringBuffer();
        if (StringUtil.isNotEmpty(permanentAddressDetails.getProvince())) {
            str.append(this.provinceOrCity(permanentAddressDetails.getProvince()));
        }
        if (StringUtil.isNotEmpty(permanentAddressDetails.getCity()) && !StringUtils.equals(permanentAddressDetails.getCity(), ApplyConstants.LOCATION_NULL_DEFAULT)) {
            str.append(this.provinceOrCity(permanentAddressDetails.getCity()));
        }
        if (StringUtil.isNotEmpty(permanentAddressDetails.getCounty()) && !StringUtils.equals(permanentAddressDetails.getCounty(), ApplyConstants.LOCATION_NULL_DEFAULT)) {
            str.append(this.provinceOrCity(permanentAddressDetails.getCounty()));
        }
        if (StringUtil.isNotEmpty(permanentAddressDetails.getTown()) && !StringUtils.equals(permanentAddressDetails.getTown(), ApplyConstants.LOCATION_NULL_DEFAULT)) {
            str.append(this.provinceOrCity(permanentAddressDetails.getTown()));
        }
        if (StringUtil.isNotEmpty(permanentAddressDetails.getStreet()) && !StringUtils.equals(permanentAddressDetails.getStreet(), ApplyConstants.LOCATION_NULL_DEFAULT)) {
            str.append(this.provinceOrCity(permanentAddressDetails.getStreet()));
        }
        str.append(permanentAddressDetails.getDetailAddress() == null ? "" : permanentAddressDetails.getDetailAddress());
        //户籍地址
        return str.toString();
    }

    /**
     * 校验模板数据
     *
     * @param applyNo
     */
    public void checkInfo(String applyNo) {

        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        if (applyOrderInfo == null) {
            throw new AfsBaseException("订单信息不存在！");
        }
        ApplyCarDetails applyCarDetails = applyCarDetailsService.getCarDetailsByApplyNo(applyNo);
        if (applyCarDetails == null) {
            throw new AfsBaseException("车辆信息不存在！");
        }
        //银行卡信息
        List<ApplyBankCard> applyBankCards = applyBankCardService.getBankCardByApplyNo(applyNo);
        for (ApplyBankCard applyBankCard : applyBankCards) {
            if (AfsEnumUtil.key(IsDefaultDeductCardEnum.ISDEFAULT).equals(applyBankCard.getIsDefaultDeductCard())) {
                if (applyBankCard == null) {
                    throw new AfsBaseException("银行卡信息未录入完整！");
                } else {
                    if (StringUtils.isBlank(applyBankCard.getAuthorizeWay()) || StringUtils.isBlank(applyBankCard.getCertNo())
                            || StringUtils.isBlank(applyBankCard.getBankCode()) || StringUtils.isBlank(applyBankCard.getAccountNo())) {
                        throw new AfsBaseException("银行卡信息未录入完整！");
                    }
                }
            }
        }
    }

    /**
     * 进件申请-签约关系表
     *
     * @param applyNo
     * @param serviceClientType
     */
    public List<ApplyAppSignPeopleListCondition> applyOrderSign(String applyNo, String serviceClientType) {

        final String redisKey = SHARE_KEY + applyNo;
        stringRedisTemplate.opsForValue().setIfAbsent(redisKey, String.valueOf(System.currentTimeMillis()), 3, TimeUnit.MINUTES);
        // 客户签约状态
        List<ApplyAppSignPeopleListCondition> SignConditionList = new ArrayList<ApplyAppSignPeopleListCondition>();

        try {
            List<ApplyAppSignPeopleListCondition> conditionList = new ArrayList<ApplyAppSignPeopleListCondition>();
            // 根据规则返回对应签约模板
            Assert.isTrue(StringUtils.isNotEmpty(applyNo), "申请编号不能为空");
            // 通过applyNo获取签约状态
            log.info("获取签约人列表==从上上签查询签约状态");
            List<Map<String, String>> mapLists = applySignRelationService.updateContractStatus(applyNo, ApplyConstants.MESSAGE_TYPE_ONE);
            TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
            JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
            List<Long> templeIds = comPrintFormManageService.printTempleRule(jsonRule, applyNo, "printOrderApply");
            log.info("打印模板生成规则:{}", JSONObject.toJSONString(templeIds));
            // 订单信息
            ApplyOrderInfo orderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
            Assert.isTrue(orderInfo != null, "订单信息不存在");
            // 签约人列表
            List<ApplyCustBaseInfo> custList = applyCustBaseInfoService.getCustBaseInfoSignList(applyNo);
            Assert.isTrue(CollectionUtil.isNotEmpty(custList), "客户信息不存在");
            custList.forEach(x -> {
                ApplyAppSignPeopleListCondition people = new ApplyAppSignPeopleListCondition();
                BeanUtils.copyProperties(x, people);
                people.setCustId(ObjectUtil.isNull(x.getId()) ? StrUtil.EMPTY : x.getId().toString());
                people.setBusinessType(orderInfo.getBusinessType());
                people.setSignFailNumber(x.getSignFailNumber() == null ? 0 : x.getSignFailNumber());
                people.setTelPhone(x.getTelPhone());
                String reappearFlag = x.getReappearFlag() == null ? WhetherEnum.No.getIndex() : x.getReappearFlag();
                String uniqueCode = "";
                // 主承租人
                if (ApplyConstants.PRINCIPAL_BORROWER.equals(x.getCustRole())) {
                    uniqueCode = AttachmentUniqueCodeEnum.MAIN_LETTER_OF_AUTHORIZATION.getCode();
                    people.setMainManReappearFlag(reappearFlag);
                    people.setSignType("承租人签约");
                }
                // 共同承租人
                else if (ApplyConstants.COMMON_BORROWER.equals(x.getCustRole())) {
                    uniqueCode = AttachmentUniqueCodeEnum.BORROW_LETTER_OF_AUTHORIZATION.getCode();
                    people.setWereBorrowedReappearFlag(reappearFlag);
                    people.setSignType("共同承租人签约");
                }
                // 担保人
                else if (ApplyConstants.GUARANTOR.equals(x.getCustRole())) {
                    uniqueCode = AttachmentUniqueCodeEnum.GUARANTEE_LETTER_OF_AUTHORIZATION.getCode();
                    people.setGuarantorReappearFlag(reappearFlag);
                    people.setSignType("担保人签约");
                }
                if (WhetherEnum.YES.getIndex().equals(x.getSignResult()) && StringUtils.isNotEmpty(uniqueCode)) {
                    List<ComPrintFormManage> printFormManage = comPrintFormManageService.getPrintFormManagesByUniqueCode(uniqueCode);
                    if (CollectionUtil.isNotEmpty(printFormManage)) {
                        templeIds.removeAll(printFormManage.stream().map(ComPrintFormManage::getId).collect(Collectors.toList()));
                    }
                }
                people.setReappearFlag(StringUtils.isEmpty(orderInfo.getReappearFlag()) ? WhetherEnum.No.getIndex() : orderInfo.getReappearFlag());
                conditionList.add(people);
            });
            ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>query().lambda().eq(ApplyOrderInfo::getApplyNo, applyNo));
            // 保存签约关系表
            String key = MessageFormat.format("{0}{1}", ORDER_KEY_SIGN, applyNo);
            String lockValue = applyNo + System.currentTimeMillis();
            Boolean res = stringRedisTemplate.opsForValue().setIfAbsent(key, lockValue, 1, TimeUnit.MINUTES);
            if (res) {
                try {
                    if(StrUtil.isNotBlank(applyOrderInfo.getRouterOrderId())){
                        applySignRelationService.saveOrderSignRelation2(applyNo, templeIds);
                    }else{
                        applySignRelationService.saveOrderSignRelation(applyNo, templeIds);
                    }
                } finally {
                    String value = stringRedisTemplate.opsForValue().get(key);
                    if (lockValue.equals(value)) {
                        stringRedisTemplate.delete(key);
                    }
                }
            }

            if (CollectionUtil.isNotEmpty(conditionList)) {
                int size = conditionList.size();
                List<String> fidList = new ArrayList<>();
                for (int i = 0; i < size; i++) {
                    ApplyAppSignPeopleListCondition people = conditionList.get(i);

                    // 更新签约状态
                    applySignRelationService.updateSignRelationStatus(applyNo, people.getId(),mapLists,people.getTelPhone(),fidList);

                    List<ApplySignRelation> signRelationList = applySignRelationService.getSignRelationList(applyNo, people.getId());
                    if (CollectionUtil.isNotEmpty(signRelationList)) {
                        for (ApplySignRelation relation : signRelationList) {
                            if (WhetherEnum.No.getIndex().equals(relation.getStatus()) || StringUtils.isEmpty(relation.getStatus())) {
                                people.setSignState(WhetherEnum.No.getIndex());
                                break;
                            } else {
                                people.setSignState(WhetherEnum.YES.getIndex());
                                people.setSignDate(ObjectUtil.isNotEmpty(relation.getSignDate()) ? relation.getSignDate() : relation.getUpdateTime());
                            }
                        }
                        SignConditionList.add(people);
                    }
                }
            }

        } finally {
            stringRedisTemplate.delete(redisKey);
        }

        return SignConditionList;
    }

    /**
     * 进件申请，获取签约关系
     *
     * @param applyNo           the apply no
     * @param serviceClientType the service client type
     * @return the list
     */
    @Override
    public IResponse applyOrderSignCapital(String applyNo, String serviceClientType) {
        Assert.isTrue(StringUtils.isNotEmpty(applyNo), "申请编号不能为空");
        ApplyOrderInfo orderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        Assert.isTrue(orderInfo != null, "订单信息不存在");
        log.info("进件申请，获取签约关系,orderInfo:{}", JSONUtil.parse(orderInfo));
        if (!StrUtil.startWithIgnoreCase(orderInfo.getBelongingCapital(), ApplyConstants.CAPITAL_BANK_PREFIX)) {
            return IResponse.fail("该订单资方已拒绝，系统自动切换为弗迪资方！");
        }
        ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                .eq(ApplyCustBaseInfo::getCustType, CustTypeEnum.PERSON)
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER));
        Assert.isTrue(ObjectUtil.isNotEmpty(custBaseInfo), "客户信息不存在");
        return getApplyAppSignPeopleListConditions(custBaseInfo, orderInfo);
    }

    /**
     * 进件申请，获取签约关系
     * @param custBaseInfo
     * @param orderInfo
     * @return
     */
    public IResponse getApplyAppSignPeopleListConditions(ApplyCustBaseInfo custBaseInfo, ApplyOrderInfo orderInfo) {

        List<ApplyAppSignPeopleListCondition> signConditionList = new ArrayList<>();
        ApplyAppSignPeopleListCondition people = new ApplyAppSignPeopleListCondition();

        BeanUtils.copyProperties(custBaseInfo, people);
        people.setCustId(ObjectUtil.isNull(custBaseInfo.getId()) ? StrUtil.EMPTY : custBaseInfo.getId().toString());
        people.setBusinessType(orderInfo.getBusinessType());
        people.setSignFailNumber(custBaseInfo.getSignFailNumber() == null ? 0 : custBaseInfo.getSignFailNumber());
        people.setTelPhone(custBaseInfo.getTelPhone());
        String reappearFlag = custBaseInfo.getReappearFlag() == null ? WhetherEnum.No.getIndex() : custBaseInfo.getReappearFlag();
        people.setMainManReappearFlag(reappearFlag);
        people.setReappearFlag(StringUtils.isEmpty(orderInfo.getReappearFlag()) ? WhetherEnum.No.getIndex() : orderInfo.getReappearFlag());

        people.setSignType("银行征信授权协议");
        if (StrUtil.isBlank(orderInfo.getCapitalOrderStatus())
                || StrUtil.equals(AfsEnumUtil.key(CapitalOrderStatusEnum.AUTH_SIGNING), orderInfo.getCapitalOrderStatus())) {
            // 授权协议签署中
            people.setSignState(WhetherEnum.NO.getIndex());
            people.setSignDate(null);
            people.setSignUrl("");
            people.setSignUrlExpireTime(null);

            if (StrUtil.isAllNotBlank(orderInfo.getCapitalApplyId(), orderInfo.getCapitalSignUrl())) {
                // 主动查询资方签约状态
                StatusInfoRespDTO respDTO = this.getStatusInfo(orderInfo);
                if (ObjectUtil.isNotEmpty(respDTO) && ObjectUtil.isNotEmpty(respDTO.getStatus())
                        && CapitalOrderStatusEnum.APPLY_CAPITAL_SIGN_FLAG_SET.contains(respDTO.getStatus())) {
                    // 授权协议签署完成
                    String[] datePatterns = new String[]{DatePattern.NORM_DATETIME_PATTERN, DatePattern.NORM_DATE_PATTERN};
                    orderInfo.setCapitalSignTime(DateUtil.parse(respDTO.getApproveTime(), datePatterns));
                    orderInfo.setApplyStatus(ApplyConstants.APPLY_STATUS_BANK_SIGNED);
                    orderInfo.setCapitalOrderStatus(AfsEnumUtil.key(CapitalOrderStatusEnum.AUTH_SIGNING_COMPLETE));

                    people.setSignState(WhetherEnum.YES.getIndex());
                    people.setSignDate(orderInfo.getCapitalSignTime());
                    people.setSignUrl(orderInfo.getCapitalSignUrl());
                    people.setSignUrlExpireTime(orderInfo.getCapitalSignUrlExpireTime());

                    // 更新之前再检查一遍当前订单的所属资方，避免拒绝后变更
                    ApplyOrderInfo dbCurrOrderInfo = orderInfoService.getOrderInfoByApplyNo(orderInfo.getApplyNo());
                    if (StrUtil.equals(BelongingCapitalEnum.FD.getCode(), dbCurrOrderInfo.getBelongingCapital())) {
                        return IResponse.fail("该订单资方已拒绝，系统自动切换为弗迪资方！");
                    }
                    applyOrderInfoService.updateById(orderInfo);
                }
            }
        } else if (CapitalOrderStatusEnum.APPLY_CAPITAL_SIGN_FLAG_SET.contains(orderInfo.getCapitalOrderStatus())) {
            // 授权协议签署完成
            people.setSignState(WhetherEnum.YES.getIndex());
            people.setSignDate(orderInfo.getCapitalSignTime());
            people.setSignUrl("");
            people.setSignUrlExpireTime(null);
        }
        signConditionList.add(people);
        log.info("调用applyOrderSignCapital方法结束，返回的结果={}",JSON.toJSONString(signConditionList));
        return IResponse.success(signConditionList);
    }

    /**
     * 调用资方查询订单状态接口
     * @param applyOrderInfo
     * @return StatusInfoRespDTO
     */
    @Override
    public StatusInfoRespDTO getStatusInfo(ApplyOrderInfo applyOrderInfo){
        StatusInfoReqDTO reqDTO = new StatusInfoReqDTO();
        reqDTO.setApplyNo(applyOrderInfo.getApplyNo());

        IResponse<StatusInfoRespDTO> respIResponse;
        // 获取车辆类型
        VehicleTypeEnum type = getVehicleTypeEnum(applyOrderInfo);
        respIResponse = rentLoansService.processEDITransaction(RentLoanUtil.buildRequestBody(reqDTO), StatusInfoReqDTO.class, StatusInfoRespDTO.class, CapitalMappingUtils.getCapitalEnum(applyOrderInfo.getBelongingCapital()), type);
        if (!"0000".equals(respIResponse.getCode()) || respIResponse.getData() == null) {
            throw new AfsBaseException("调用资方接口响应失败");
        } else {
            return respIResponse.getData();
        }
    }

    @Override
    public VehicleTypeEnum getVehicleTypeEnum(ApplyOrderInfo applyOrderInfo) {
        VehicleTypeEnum type = VehicleTypeEnum.COMMERCIAL;
        if (AfsEnumUtil.key(AffiliatedWayEnum.NO).equals(applyOrderInfo.getAffiliatedWay())
                && OperateWayNewEnum.OPERATE_WAY_01.getCode().equals(applyOrderInfo.getOperateWay())
                && CarTypeEnum.PASSENGER_CAR_2.getCode().equals(applyOrderInfo.getCarType())) {
            type = VehicleTypeEnum.PASSENGER;
        }
        return type;
    }

    @Override
    public Map<String, VehicleAndCapitalTypeDTO> getVehicleTypeEnumMapByApplyNoList(List<String> applyNoList) {

        List<ApplyOrderInfo> applyOrderInfoList = applyOrderInfoService.list(Wrappers.<ApplyOrderInfo>lambdaQuery()
                .select(ApplyOrderInfo::getApplyNo,
                        ApplyOrderInfo::getAffiliatedWay,
                        ApplyOrderInfo::getOperateWay,
                        ApplyOrderInfo::getCarType,
                        ApplyOrderInfo::getBelongingCapital)
                .in(ApplyOrderInfo::getApplyNo,applyNoList));

        Map<String,VehicleAndCapitalTypeDTO> map = new HashMap<>();
        for(ApplyOrderInfo applyOrderInfo : applyOrderInfoList){
            VehicleAndCapitalTypeDTO dto = new VehicleAndCapitalTypeDTO();
            VehicleTypeEnum type = VehicleTypeEnum.COMMERCIAL;
            if (AfsEnumUtil.key(AffiliatedWayEnum.NO).equals(applyOrderInfo.getAffiliatedWay())
                    && OperateWayNewEnum.OPERATE_WAY_01.getCode().equals(applyOrderInfo.getOperateWay())
                    && CarTypeEnum.PASSENGER_CAR_2.getCode().equals(applyOrderInfo.getCarType())) {
                type = VehicleTypeEnum.PASSENGER;
            }
            dto.setVehicleTypeEnum(type);
            dto.setCapitalEnum(CapitalMappingUtils.getCapitalEnum(applyOrderInfo.getBelongingCapital()));
            map.put(applyOrderInfo.getApplyNo(),dto);
        }
        return map;
    }

    @Override
    public VehicleAndCapitalTypeDTO getVehicleTypeEnumByApplyNo(String applyNo) {

        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>lambdaQuery()
                .select(ApplyOrderInfo::getApplyNo,
                        ApplyOrderInfo::getAffiliatedWay,
                        ApplyOrderInfo::getOperateWay,
                        ApplyOrderInfo::getCarType,
                        ApplyOrderInfo::getBelongingCapital)
                .eq(ApplyOrderInfo::getApplyNo,applyNo));

        VehicleAndCapitalTypeDTO dto = new VehicleAndCapitalTypeDTO();
        VehicleTypeEnum type = VehicleTypeEnum.COMMERCIAL;
        if (AfsEnumUtil.key(AffiliatedWayEnum.NO).equals(applyOrderInfo.getAffiliatedWay())
                && OperateWayNewEnum.OPERATE_WAY_01.getCode().equals(applyOrderInfo.getOperateWay())
                && CarTypeEnum.PASSENGER_CAR_2.getCode().equals(applyOrderInfo.getCarType())) {
            type = VehicleTypeEnum.PASSENGER;
        }
        dto.setVehicleTypeEnum(type);
        dto.setCapitalEnum(CapitalMappingUtils.getCapitalEnum(applyOrderInfo.getBelongingCapital()));

        return dto;
    }

    /**
     * 进件申请-生成该客户的签约文件
     *
     * @param dto
     * @return
     */
    public List<ComAttachmentFile> applyCustSignFile(CfCaSignatureDto dto) {

        // 获取模板参数
        String applyNo = dto.getApplyNo();
        Assert.isTrue(StringUtils.isNotEmpty(applyNo), "申请编号不能为空");
        // 返回签约文件
        List<ComAttachmentFile> fileList = new ArrayList<>();
        Assert.isTrue(dto.getCustId() != null, "客户id不能为空");
        List<ApplySignRelation> signRelationList = applySignRelationService.getSignRelationList(applyNo, dto.getCustId());
        if (CollectionUtil.isNotEmpty(signRelationList)) {
            List<Long> templeIds = new ArrayList<Long>();
            signRelationList.forEach(x -> {
                if (WhetherEnum.No.getIndex().equals(x.getStatus())) {
                    templeIds.add(x.getTempleId());
                    x.setReappearFlag(WhetherEnum.No.getIndex());// 重出标识为0
                    applySignRelationService.updateById(x);
                } else {
                    List<ComAttachmentFile> attachmentList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(ComAttachmentFile::getBusiNo, applyNo)
                            .eq(ComAttachmentFile::getAttachmentCode, x.getTempleId().toString())
                            .orderByDesc(ComAttachmentFile::getCreateTime));
                    if (CollectionUtil.isNotEmpty(attachmentList)) {
                        ComAttachmentFile attachmentFile = attachmentList.get(0);
                        attachmentFile.setSignStatus(x.getStatus());
                        fileList.add(attachmentFile);
                    }
                }
            });
            // 未签约文件
            if (CollectionUtil.isNotEmpty(templeIds)) {
                String serviceClientType = dto.getServiceClientType();
                Assert.isTrue(StringUtils.isNotEmpty(serviceClientType), "客户端来源不能为空");
                ApplyTemplateVO templateVO = this.getApplyTemplatePara(applyNo, serviceClientType);
                JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
                templeIds.forEach(x -> {
                    ComAttachmentFile attachmentFile = comPrintFormManageService.printNoSeal(jsonTemp, x.toString(), applyNo, serviceClientType);
                    attachmentFile.setSignStatus(WhetherEnum.No.getIndex());
                    fileList.add(attachmentFile);
                });
            }
            // 订单主表-重出标识为0
            if (applySignRelationService.getRoleSignFlag(Boolean.TRUE, applyNo, dto.getCustId())) {
                ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
                if (applyOrderInfo != null) {
                    applyOrderInfo.setReappearFlag(WhetherEnum.No.getIndex());
                    applyOrderInfoService.updateById(applyOrderInfo);
                }
            }
            // 客户表-重出标识为0
            ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getById(dto.getCustId());
            if (custBaseInfo != null && WhetherEnum.YES.getIndex().equals(custBaseInfo.getReappearFlag())) {
                custBaseInfo.setReappearFlag(WhetherEnum.No.getIndex());
                applyCustBaseInfoService.updateById(custBaseInfo);
            }
        }
        return fileList;
    }

    /**
     * 放款申请-生成签约关系表
     *
     * @param applyNo
     * @param contractNo
     * @param serviceClientType  app - 电子签约  pc - 非电子签约
     * @return
     */
    public List<LoanAppSignPeopleListCondition> contractOrderSign(String applyNo, String contractNo, String serviceClientType) {

        // 订单信息
        Assert.isTrue(StringUtils.isNotEmpty(applyNo), "申请编号不能为空");

        final String redisKey = SHARE_KEY + applyNo;
        stringRedisTemplate.opsForValue().setIfAbsent(redisKey, String.valueOf(System.currentTimeMillis()), 3, TimeUnit.MINUTES);

        List<LoanAppSignPeopleListCondition> conditionList = new ArrayList<>();
        try {

            // 通过applyNo获取签约状态
            List<Map<String, String>> mapLists = applySignRelationService.updateContractStatus(applyNo, ApplyConstants.MESSAGE_TYPE_TWO);
            ApplyOrderInfo orderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
            Assert.isTrue(orderInfo != null, "订单信息不存在");
            // 合同信息
            Assert.isTrue(StringUtils.isNotEmpty(contractNo), "申请编号不能为空");
            ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByContractNo(contractNo);
            Assert.isTrue(contractInfo != null, "合同信息不允许为空");
            String reappearFlag = contractInfo.getReappearFlag() == null ? WhetherEnum.No.getIndex() : contractInfo.getReappearFlag();
            List<ApplyCustBaseInfo> custList = applyCustBaseInfoService.getCustBaseInfoSignList(applyNo);
            Assert.isTrue(CollectionUtil.isNotEmpty(custList), "客户信息不存在");

            // 如果满足条件第三方公司挂靠
            if(AfsEnumUtil.key(AffiliatedWayEnum.COMPANY_AFFILIATED).equals(orderInfo.getAffiliatedWay()) ||
                    AfsEnumUtil.key(AffiliatedWayEnum.PERSONAL_COMPANY_AFFILIATED).equals(orderInfo.getAffiliatedWay())){

                // 如果是第三方公司挂靠或者个人公司挂靠的话，则不允许签署方式为空
                if(StringUtils.isBlank(contractInfo.getAuthorizeWay())){
                    throw new AfsBaseException("生成签约合同前，请先保存签约方式！");
                }

                if("0".equals(contractInfo.getAuthorizeWay())){

                    // 1. 查询挂靠公司和法人信息、
                    List<ApplyAffiliatedUnit> tApplyAffiliatedUnitList = applyAffiliatedUnitService.list(Wrappers.<ApplyAffiliatedUnit>lambdaQuery().eq(ApplyAffiliatedUnit::getApplyNo, applyNo));
                    log.info("查询到的该挂靠公司数量为{}，具体内容为{}",tApplyAffiliatedUnitList.size(),JSON.toJSONString(tApplyAffiliatedUnitList));
                    if(ObjectUtil.isEmpty(tApplyAffiliatedUnitList)){
                        throw new AfsBaseException("没有获取到该合同的挂靠公司信息，请确认！");
                    }
                    if(tApplyAffiliatedUnitList.size() > 1){
                        throw new AfsBaseException("该合同存在多个挂靠公司信息，请确认！");
                    }
                    // 2. 保存挂靠公司信息
                    ApplyCustBaseInfo applyCustBaseInfo = new ApplyCustBaseInfo();
                    applyCustBaseInfo.setApplyNo(applyNo);
                    applyCustBaseInfo.setCustName(tApplyAffiliatedUnitList.get(0).getAffiliatedName());
                    applyCustBaseInfo.setCustType(orderInfo.getInputType());
                    applyCustBaseInfo.setCustRole(AfsEnumUtil.key(CustRoleEnum.AFFILIATED));
                    applyCustBaseInfo.setTelPhone(tApplyAffiliatedUnitList.get(0).getEnterprisePhone());
                    applyCustBaseInfo.setId(Long.valueOf(tApplyAffiliatedUnitList.get(0).getId()));
                    applyCustBaseInfo.setCertNo(tApplyAffiliatedUnitList.get(0).getSocUniCrtCode());
                    custList.add(applyCustBaseInfo);
                    // 3. 保存挂靠公司法人信息，判断如果办理人是法人，则保存法人信息，如果是授权人，则保存授权人信息
                    if(ApplyConstants.SIGN_TYPE_AUTHORIZER.equals(tApplyAffiliatedUnitList.get(0).getSignatoryType())){

                        // 授权人
                        ApplyCustBaseInfo applyCustBaseInfo2 = new ApplyCustBaseInfo();
                        applyCustBaseInfo2.setApplyNo(applyNo);
                        applyCustBaseInfo2.setCustName(tApplyAffiliatedUnitList.get(0).getAuthorizerName());
                        applyCustBaseInfo2.setCustType(orderInfo.getInputType());
                        applyCustBaseInfo2.setCustRole(AfsEnumUtil.key(CustRoleEnum.AFFILIATEDSQR));
                        applyCustBaseInfo2.setTelPhone(tApplyAffiliatedUnitList.get(0).getAuthorizerPhone());
                        applyCustBaseInfo2.setId(Long.valueOf(tApplyAffiliatedUnitList.get(0).getId()));
                        applyCustBaseInfo2.setCertNo(tApplyAffiliatedUnitList.get(0).getAuthorizerIdcard());
                        custList.add(applyCustBaseInfo2);

                    }else{

                        // 法人
                        ApplyCustBaseInfo applyCustBaseInfo2 = new ApplyCustBaseInfo();
                        applyCustBaseInfo2.setApplyNo(applyNo);
                        applyCustBaseInfo2.setCustName(tApplyAffiliatedUnitList.get(0).getLegalName());
                        applyCustBaseInfo2.setCustType(orderInfo.getInputType());
                        applyCustBaseInfo2.setCustRole(AfsEnumUtil.key(CustRoleEnum.AFFILIATEDFR));
                        applyCustBaseInfo2.setTelPhone(tApplyAffiliatedUnitList.get(0).getLegalPhone());
                        applyCustBaseInfo2.setId(Long.valueOf(tApplyAffiliatedUnitList.get(0).getId()));
                        applyCustBaseInfo2.setCertNo(tApplyAffiliatedUnitList.get(0).getLegalPersonIdcard());
                        custList.add(applyCustBaseInfo2);

                    }
                }
            }

            log.info(" ======================== 所有需要处理的签约人数据信息为{} ======================== ",JSON.toJSONString(custList));

            // 根据规则返回对应签约模板
            TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
            JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
            List<Long> templeIds = comPrintFormManageService.printTempleRule(jsonRule, contractNo, "printLoanApply");
            // 保存签约关系表
            String key = MessageFormat.format("{0}{1}", CACHE_KEY_SIGN, contractNo);
            String lockValue = contractNo + System.currentTimeMillis();
            Boolean res = stringRedisTemplate.opsForValue().setIfAbsent(key, lockValue, 1, TimeUnit.MINUTES);
            if (res) {
                try {
                    applySignRelationService.saveContractSignRelation(applyNo, contractNo, null, null, custList, templeIds);
                } finally {
                    String value = stringRedisTemplate.opsForValue().get(key);
                    if (lockValue.equals(value)) {
                        stringRedisTemplate.delete(key);
                    }
                }
            }
            // 签约人: 主借人、共借人、保证人
            List<String> fidList = new ArrayList<>();
            custList.forEach(x -> {
                log.info(" ======================== 开始循环处理所有的签约人信息，当前签约人角色为{} ======================== ",x.getCustRole());
                LoanAppSignPeopleListCondition peopleCondition = new LoanAppSignPeopleListCondition();
                BeanUtils.copyProperties(x, peopleCondition);
                peopleCondition.setContractNo(contractNo);
                peopleCondition.setReappearFlag(reappearFlag);
                peopleCondition.setCustId(x.getId());
                peopleCondition.setBusinessType(orderInfo.getBusinessType());
                // 更新签约状态
                applySignRelationService.updateSignRelationStatus(contractNo, applyNo, x.getId(), x.getCustRole(),mapLists,peopleCondition.getTelPhone(),fidList);

                //关联签约关系表
                List<ApplySignRelation> signRelationList = applySignRelationService.getSignRelationList(contractNo, x.getId(), x.getCustRole());
                if (CollectionUtil.isNotEmpty(signRelationList)) {
                    for (ApplySignRelation relation : signRelationList) {
                        if (WhetherEnum.No.getIndex().equals(relation.getStatus()) || StringUtils.isBlank(relation.getStatus())) {
                            peopleCondition.setSignState(WhetherEnum.No.getIndex());
                            break;
                        } else {
                            peopleCondition.setSignState(WhetherEnum.YES.getIndex());
                            peopleCondition.setSignDate(ObjectUtil.isNotEmpty(relation.getSignDate()) ? relation.getSignDate() : relation.getUpdateTime());
                        }
                    }
                } else {
                    peopleCondition.setSignState(WhetherEnum.No.getIndex());
                }
                conditionList.add(peopleCondition);
            });
        } finally {
            stringRedisTemplate.delete(redisKey);
        }

        return conditionList;
    }

    /**
     * 放款申请-获取客户签约文件
     *
     * @param dto
     * @return
     */
    public List<ComAttachmentFile> contractCustSignFile(CfCaSignatureDto dto) {

        log.info("放款申请-获取客户签约文件。入参为{}",JSON.toJSONString(dto));

        String applyNo = dto.getApplyNo();
        Assert.isTrue(StringUtils.isNotEmpty(applyNo), "申请编号不能为空");
        this.checkInfo(applyNo);// 校验模板数据正确性
        String contractNo = dto.getContractNo();

        // 判断是否需要取消合同重出标识
        long count = comAttachmentFileService.count(new LambdaQueryWrapper<ComAttachmentFile>()
                .eq(ComAttachmentFile::getBusiNo, contractNo)
                .ne(ComAttachmentFile::getBelongNo,contractNo)
                .eq(ComAttachmentFile::getRemake,AfsEnumUtil.key(ServiceClientTypeEnum.APP)));
        log.info("判断是否需要取消合同重出标识，查询到已生成的模板数量={}",count);
        if(count == 0){
            ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByContractNo(contractNo);
            log.info("判断是否需要取消合同重出标识，获取到的合同信息={}",JSON.toJSONString(contractInfo));
            if (contractInfo != null) {
                contractInfo.setReappearFlag(WhetherEnum.No.getIndex());
                applyContractInfoService.updateById(contractInfo);
            }
        }

        // 主借人、共借人、保证人
        Assert.isTrue(dto.getCustId() != null, "客户id不能为空");

        // 校验银行卡是否签约
        applyBankCardService.checkBankCardSignStatus(applyNo);

        List<ApplySignRelation> signRelationList = applySignRelationService.getSignRelationList(contractNo, dto.getCustId(),dto.getCustRole());
        log.info("更新签约状态，查询需要更新的签约关系表中的数据信息，返回的数据={}",JSON.toJSONString(signRelationList));
        // 签约文件
        List<ComAttachmentFile> fileList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(signRelationList)) {
            log.info("判断是否存在签约关系，存在签约关系，之后开始判断是否需要更新签约状态！");
            Map<Long,String> idAndRoleMap = new HashedMap();
            Map<Long,Long> idAndTempleIdMap = new HashedMap();
            for (ApplySignRelation signRelation : signRelationList) {
                Integer signCount = applySignRelationService.getSignTempleIdCount(contractNo, signRelation.getTempleId());
                log.info("更新签约状态，查询已完成签约的合同的数量，其中查询参数contractNp={},TempleId={},返回结果={}",contractNo,signRelation.getTempleId(),signCount);
                if (signCount == 0) {
                    log.info("更新签约状态，没有查询到已签约完成且符合条件的合同，需要更新签约状态！");
                    idAndTempleIdMap.put(signRelation.getId(),signRelation.getTempleId());
                    idAndRoleMap.put(signRelation.getId(),signRelation.getCustRole());
                } else {
                    log.info("更新签约关系表状态，查询到了已签约完成且符合条件的合同，不需要更新签约状态！开始查询需要返回的签约文件信息！");
                    List<ComAttachmentFile> attachmentList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(ComAttachmentFile::getBusiNo, contractNo)
                            .eq(ComAttachmentFile::getAttachmentCode, signRelation.getTempleId().toString())
                            .orderByDesc(ComAttachmentFile::getCreateTime));
                    log.info("更新签约关系表状态，查询到的签约文件结果={}",JSON.toJSONString(attachmentList));
                    if (CollectionUtil.isNotEmpty(attachmentList)) {
                        log.info("更新签约关系表状态，从ComAttachmentFile表中查询到符合条件的数据，开始赋值签约状态用户返回数据，其中签约状态={}",signRelation.getStatus());
                        ComAttachmentFile attachmentFile = attachmentList.get(0);
                        attachmentFile.setSignStatus(signRelation.getStatus());
                        fileList.add(attachmentFile);
                        log.info("更新签约关系表状态，第1处赋值后的数据={}",JSON.toJSONString(fileList));
                    }else{
                        log.info("更新签约关系表状态，没有从ComAttachmentFile表中查询到符合条件的数据！");
                    }
                }
            }
            // 未签约文件
            if (CollectionUtil.isNotEmpty(idAndTempleIdMap)) {
                log.info("更新签约状态，开始更新签约关系表状态！");
                String serviceClientType = dto.getServiceClientType();
                Assert.isTrue(StringUtils.isNotEmpty(serviceClientType), "客户端来源不能为空");
                ContractTemplateVO templateVO = this.getContractTemplateParam(applyNo, serviceClientType);
                JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);

                Set<Long> set2 = idAndTempleIdMap.keySet();
                for (Long key : set2) {
                    Long x = idAndTempleIdMap.get(key);
                    ApplySignRelation signRelation = applySignRelationService.getSignRelationForCustId(contractNo, x, dto.getCustId(), idAndRoleMap.get(key));
                    String reappearFlag = signRelation.getReappearFlag() == null ? WhetherEnum.No.getIndex() : signRelation.getReappearFlag();
                    ComPrintFormManage printFormManage = comPrintFormManageService.getById(x);
                    Assert.isTrue(printFormManage != null, "模板配置信息不全");
                    List<ComAttachmentFile> attachmentFileList = comAttachmentFileService.getComAttachmentFileList(contractNo, printFormManage.getAttachmentSubClass(), x.toString(), "com_print_form_manage");

                    log.info("判断之前是否生成过合同模板，是否需要重出合同模板!");
                    if (CollectionUtil.isNotEmpty(attachmentFileList) && WhetherEnum.No.getIndex().equals(reappearFlag)) {
                        log.info("查询影像件文件模板数量为：{}，合同重定向：{}",attachmentFileList.size(),reappearFlag);
                        ComAttachmentFile attachmentFile = attachmentFileList.get(0);
                        attachmentFile.setSignStatus(signRelation.getStatus());
                        fileList.add(attachmentFile);
                        log.info("更新签约关系表状态，第2处赋值后的数据={}",JSON.toJSONString(fileList));
                    } else {
                        log.info("查询影像件文件合同重定向：{}",reappearFlag);
                        ComAttachmentFile attachmentFile = comPrintFormManageService.printNoSeal(jsonTemp, x.toString(), contractNo, serviceClientType);
                        attachmentFile.setSignStatus(WhetherEnum.No.getIndex());
                        fileList.add(attachmentFile);
                        log.info("更新签约关系表状态，第3处赋值后的数据={}",JSON.toJSONString(fileList));
                        // 生成模板之后，还原重出标识
                        List<ApplySignRelation> relationList = applySignRelationService.getSignRelationListByTempleId(contractNo, x);
                        if (CollectionUtil.isNotEmpty(relationList)) {
                            relationList.forEach(y -> {
                                y.setReappearFlag(WhetherEnum.No.getIndex());
                                applySignRelationService.updateById(y);
                            });
                        }
                    }
                }

            }
        }else{
            log.info("判断是否存在签约关系，不存在签约关系，不需要更新签约状态！");
        }

        return fileList;
    }

    /**
     * 起租日通知书-生成
     *
     * @param applyNo
     */
    @Override
    public List<ComAttachmentFile> leaseNoticePrint(String applyNo, String contractNo, String serviceClientType) {
        log.info("起租日通知书批量生成申请编号:{} 合同编号:{}", applyNo, contractNo);
        //兼容旧逻辑，设置查询打印模板及规则匹配时主体编号为中远上海 RT0001
        ApplySubjectInfo subjectInfo = applySubjectInfoService.getOne(new LambdaQueryWrapper<ApplySubjectInfo>().eq(ApplySubjectInfo::getApplyNo, applyNo));
        String subjectCode = subjectInfo == null || StringUtil.isBlank(subjectInfo.getSubjectCode()) ? SubjectCodeEnum.MULTI_SUBJECT_SH.getCode() : subjectInfo.getSubjectCode();
        //模板字段校验
        checkInfo(applyNo);
        // 获取模板参数
        ContractTemplateVO templateVO = getContractTemplateParam(applyNo, serviceClientType);
        //还款计划表单独获取
        if (AfsEnumUtil.key(ServiceClientTypeEnum.PC).equals(serviceClientType) && templateVO.getContract().getStartDate() != null) {
            Map<String, String> headers = new HashMap<>(2);
            headers.put("clientId", applyConfig.getContractBasicClientId());
            headers.put("clientSecret", applyConfig.getContractBasicClientSecret());
            IResponse<List<RepaymentPlanDTO>> response = commonOuterQueryFeign.getRepaymentPlanInfoList(contractNo, headers);
            if (CommonConstants.SUCCESS.equals(response.getCode())) {
                List<RepaymentPlanDTO> repaymentList = response.getData();
                if (CollectionUtil.isNotEmpty(repaymentList)) {
                    List<RepaymentVO> repaymentVOList = new ArrayList<>();
                    BigDecimal zjSum = BigDecimal.ZERO;
                    for (RepaymentPlanDTO repaymentPlanDto : repaymentList) {
                        RepaymentVO repaymentVO = new RepaymentVO();
                        repaymentVO.setBenJin(precisionNotRounded(repaymentPlanDto.getReceivablePrinciple()));
                        repaymentVO.setLiXi(precisionNotRounded(repaymentPlanDto.getReceivableInterest()));
                        repaymentVO.setHuanKuanRiQi(DateUtil.date(repaymentPlanDto.getDueDate()));
                        repaymentVO.setNo(repaymentPlanDto.getTermNo());
                        repaymentVO.setYueGong(precisionNotRounded(repaymentPlanDto.getReceivableRent()));
                        repaymentVOList.add(repaymentVO);
                        zjSum = zjSum.add(repaymentPlanDto.getReceivableRent());
                    }
                    templateVO.setRepay(repaymentVOList);
                } else {
                    log.info("调用合同服务，还款计划表信息为空,{}", contractNo);
                }
            }
        }
        // 规则VO
        TemplateRuleVO ruleVO = this.getTemplateRule(applyNo, serviceClientType);
        // 生成模板
        JSONObject jsonTemp = (JSONObject) JSONObject.toJSON(templateVO);
        log.info("起租通知模板信息{}", jsonTemp.toJSONString());
        JSONObject jsonRule = (JSONObject) JSONObject.toJSON(ruleVO);
        jsonRule.put("subjectCode", subjectCode);
        Assert.isTrue(contractNo != null, "合同编号不能为空");
        List<ComAttachmentFile> fileList = comPrintFormManageService.batchPrint(jsonTemp, jsonRule, null, contractNo, "printLeaseNotice", serviceClientType);
        if (fileList.size() > 0) {
            ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
            contractInfo.setIsLeaseNotice(AfsEnumUtil.key(IsLeaseNoticeEnum.YES));
            applyContractInfoService.updateById(contractInfo);
        }
        //起租通知书后置处理
        leaseStartDateNoticeAfterHandle(applyNo, contractNo, fileList);
        return fileList;
    }

    /**
     * 调阅模板生成
     *
     * @param consultFileList
     * @param serviceClientType
     * @return
     */
    @Override
    public ComAttachmentFile consultApplyPrint(List<ConsultFileInfoDto> consultFileList, String serviceClientType) {

        List<ConsultPrintInfoCondition> consultPrintInfoConditionList = new ArrayList<>();
        for (int i =1 ; i<=consultFileList.size() ; i++){
            /*校验数据**/
            /*查询车架号**/
            IResponse<CaseCarInfo> response = archiveFeign.getCaseVehicleInfo(consultFileList.get(i-1).getApplyNo(),makeHeader());
            if(ObjectUtil.isNotEmpty(response)&&ObjectUtil.isNotEmpty(response.getData())){
                CaseCarInfo caseCarInfo =response.getData();
                ConsultPrintInfoCondition condition = new ConsultPrintInfoCondition();
                BeanUtils.copyProperties(consultFileList.get(i-1), condition);
                condition.setKey(String.valueOf(i));
                if(ObjectUtil.isNotEmpty(caseCarInfo.getCarVin())){
                    condition.setVin(caseCarInfo.getCarVin());
                }else {
                    throw new AfsBaseException(consultFileList.get(i-1).getApplyNo()+"未查到车架号信息！");
                }

                condition.setFileType(consultFileList.get(i-1).getFileTypeName());

                consultPrintInfoConditionList.add(condition);
            }else {
                throw new AfsBaseException("获取订单[{}]车辆信息失败！",consultFileList.get(i-1).getApplyNo());
            }
        }


        // 获取模板参数
        JSONObject jsonTemp = new JSONObject();
        jsonTemp.put("printDate",DatePattern.CHINESE_DATE_FORMAT.format(new Date()));
        jsonTemp.put("printList",consultPrintInfoConditionList);

        /*获取模板**/
        ComPrintFormManage comPrintFormManage = comPrintFormManageService.getOne(Wrappers.<ComPrintFormManage>lambdaQuery()
                .eq(ComPrintFormManage::getPrintFormId,"CONSULTAPPLY")
        );

        ComAttachmentFile comAttachmentFile = comPrintFormManageService.consultPrint(jsonTemp, String.valueOf(comPrintFormManage.getId()),consultPrintInfoConditionList, serviceClientType);

        return comAttachmentFile;
    }

    public Map makeHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", caseApiConfig.getCaseClientId());
        headers.put("clientSecret", caseApiConfig.getCaseClientSecret());
        return headers;
    }

    /**
     * 精度处理：保留两位小数,不四舍五入
     */
    private BigDecimal precisionNotRounded(BigDecimal val) {
        if (val == null) {
            return null;
        }
        return val.setScale(2, BigDecimal.ROUND_DOWN);
    }


    private void leaseStartDateNoticeAfterHandle(String applyNo, String contractNo, List<ComAttachmentFile> fileList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(fileList)) {
            log.info("合同号[{}]起租日通知书文件为空,不做处理", contractNo);
            return;
        }

        ComAttachmentFile attachmentFile = fileList.get(0);
        log.info("合同号[{}]起租日通知书文件(合同号_文件ID_附件名)=[{}]", contractNo, contractNo + "_" + attachmentFile.getFileId()
                + "_" + attachmentFile.getAttachmentName());
        ComPrintFormManage printFormManage = comPrintFormManageService.getById(Long.valueOf(attachmentFile.getAttachmentCode()));
        attachmentFile.setBelongNo(contractNo);
        // printFormManage.getAscriptionSubClass() 为 com_attachment_management表的id
        attachmentFile.setAttachmentCode(printFormManage.getAscriptionSubClass());
        attachmentFile.setId(null);
        attachmentFile.setFileSource("com_attachment_management");
        boolean updateFlag = comAttachmentFileService.save(attachmentFile);
        log.info("合同号[{}]保存起租日通知书文件记录完成,保存结果=[{}],附件ID=[{}]", contractNo, updateFlag, attachmentFile.getId());

        /**
         * 影像文件查询接口"/viewFile"未对文件有效性进行过滤，如果存在再次生成，则会展示多份;
         * 查询影像文件代码(commons)是公共的，不进行改动。如果存在历史文件，则删除(不包含当前生成的文件)，日志打印文件ID;
         */
        List<ComAttachmentFile> comAttachmentFilesHis = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getAttachmentCode, attachmentFile.getAttachmentCode())
                .eq(ComAttachmentFile::getBusiNo, contractNo)
                .eq(ComAttachmentFile::getBelongNo, attachmentFile.getBelongNo())
                .eq(ComAttachmentFile::getDelFlag, IsDeleteEnum.NO.getCode())
                // 排除当前生成的文件
                .ne(ComAttachmentFile::getId, attachmentFile.getId()));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(comAttachmentFilesHis)) {
            List<String> fileIds = comAttachmentFilesHis.stream().map(ComAttachmentFile::getFileId).collect(Collectors.toList());
            List<Long> deletetIds = comAttachmentFilesHis.stream().map(ComAttachmentFile::getId).collect(Collectors.toList());
            boolean deleteFlag = comAttachmentFileService.removeByIds(deletetIds);
            log.info("合同号[{}]下删除起租日通知书文件ID[{}]记录完成,删除结果=[{}]", contractNo, fileIds, deleteFlag);
        }

        ComAttachmentFile record = comAttachmentFileService.getById(attachmentFile.getId());
        if (Objects.nonNull(record)) {
            AttachmentDTO attachmentDTO = new AttachmentDTO();
            BeanUtil.copyProperties(record, attachmentDTO);
            AfsTransEntity<AttachmentDTO> transEntity = new AfsTransEntity<>();
            transEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_SYNC_ATTACHMENT_FILE);
            transEntity.setData(attachmentDTO);
            caseLoanInfoSender.sendFileToCase(transEntity);
            log.info("合同号[{}]起租日通知书文件数据同步至案件库发送MQ成功 data={}", contractNo, attachmentDTO);
        }
    }


    /**
     * 获取产品请求头
     *
     * @return
     */
    public Map getProductHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", applyConfig.getProductClientId());
        headers.put("clientSecret", applyConfig.getProductClientSecret());
        return headers;
    }

    public IResponse checkCarVin(String applyNo){
        ApplyCarDetails carDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>query().lambda().eq(ApplyCarDetails::getApplyNo, applyNo));
        // VIN校验、applyComAttach/checkHasContract
        if (carDetails!=null && org.apache.commons.lang3.StringUtils.isNotEmpty(carDetails.getCarVin())) {
            if (carDetails.getCarVin().length() > 17 ){
                return new IResponse().fail("车架号不可大于17位");
            }
            Boolean flag = VinUtils.isValidVin(carDetails.getCarVin());
            if (!flag) {
                return new IResponse().fail("车架号第九位校验错误");
            }
        }
        //车架号唯一校验
        List<ApplyCarDetails> carDetailsList = applyCarDetailsService.
                checkCarVin(carDetails.getCarVin(), AfsEnumUtil.key(LoanApplyStatusEnum.LOAN_CANCEL),carDetails.getApplyNo());
        if(carDetailsList.size()>0) {
            //拍卖车，如存在重复车架号订单为“已结清”状态时，可生成合同
            ApplyOrderInfo applyOrder = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
            if (StrUtil.equals(CarType.AUCTION_VEHICLE.getIndex(), applyOrder.getCarType())) {
                for (int i = 0; i < carDetailsList.size(); i++) {
                    //判断合同是否结清
                    ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(carDetailsList.get(i).getApplyNo());
                    if (!AfsEnumUtil.key(ApplyConstants.CONTRACT_STATUS_CLOSE).equals(contractInfo.getContractStatus())) {
                        return IResponse.fail("车架号重复,不可重复提交");
                    }
                }
            } else {
                return IResponse.fail("车架号重复,不可重复提交");
            }
        }
        return new IResponse().success("车架号校验通过");
    }

}
