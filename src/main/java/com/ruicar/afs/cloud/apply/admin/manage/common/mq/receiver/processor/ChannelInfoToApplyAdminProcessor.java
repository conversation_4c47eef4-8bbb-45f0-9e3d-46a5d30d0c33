package com.ruicar.afs.cloud.apply.admin.manage.common.mq.receiver.processor;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.admin.service.SysUserService;
import com.ruicar.afs.cloud.apply.admin.auth.util.ChannelDic;
import com.ruicar.afs.cloud.apply.admin.auth.util.ChineseCharacterUtil;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysChannelAuthRegion;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysChannelBasicInfo;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysDepartment;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysDeptAuthRegion;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysRole;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysUser;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysUserPower;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysUserRole;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysUserRoleTemp;
import com.ruicar.afs.cloud.apply.admin.manage.entity.SysUserTemp;
import com.ruicar.afs.cloud.apply.admin.manage.enums.ApplyAdminConfig;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyChannelAuthRegionService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyChannelBasicInfoService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyDepartmentService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyDeptAuthRegionService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyRoleService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyUserRoleService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyUserRoleTempService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyUserService;
import com.ruicar.afs.cloud.apply.admin.manage.service.ApplyUserTempService;
import com.ruicar.afs.cloud.apply.admin.manage.service.SysUserPowerService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.modules.apply.dto.ChannelBasicInfoDTO;
import com.ruicar.afs.cloud.common.modules.apply.enums.CarTypeEnum;
import com.ruicar.afs.cloud.common.modules.apply.enums.CreatUserEnum;
import com.ruicar.afs.cloud.common.modules.apply.enums.LockFlagEnum;
import com.ruicar.afs.cloud.common.modules.apply.enums.StatusEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.listener.AfsMqBizProcessor;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Component
@ConditionalOnProperty(prefix = "com.ruicar.afs.cloud.mq.rabbit", name = "enable")
public class ChannelInfoToApplyAdminProcessor implements AfsMqBizProcessor<ChannelBasicInfoDTO> {


    private final ApplyChannelAuthRegionService applyChannelAuthRegionService;
    private final ApplyDeptAuthRegionService applyDeptAuthRegionService;
    private final ApplyChannelBasicInfoService applyChannelBasicInfoService;
    private final ApplyDepartmentService applyDepartmentService;
    private final ApplyUserTempService applyUserTempService;
    private final ApplyAdminConfig applyAdminConfig;
    private final ApplyUserService applyUserService;
    private final SysUserPowerService sysUserPowerService;
    private final ApplyRoleService applyRoleService;
    private final ApplyUserRoleService applyUserRoleService;
    private final ApplyUserRoleTempService applyUserRoleTempService;
    @Override
    public boolean processMessage(ChannelBasicInfoDTO channelBasicInfo) throws Exception {
        log.info("初始化创建用户"+ channelBasicInfo);

        if (ObjectUtils.isEmpty(channelBasicInfo.getRegions())) {
            log.info("经销商:{}初始化创建用户,授权区域regions对象不能为空",channelBasicInfo.getChannelFullName());
            return false;
        }
        if (ObjectUtils.isEmpty(channelBasicInfo.getType())) {
            log.info("经销商:{}初始化创建用户,同步类型type字段不能为空",channelBasicInfo.getChannelFullName());
            return false;
        }
        Long channelId = channelBasicInfo.getRegions().get(0).getChannelId();


        // 查询新车的原有数据
        List<SysChannelAuthRegion> newList1 = applyChannelAuthRegionService.list(Wrappers.<SysChannelAuthRegion>query().lambda()
                .eq(SysChannelAuthRegion::getChannelId, channelId).eq(SysChannelAuthRegion::getBusinessType, CarTypeEnum.newCar));
        // 查询二手车原来的数据
        List<SysChannelAuthRegion> oldList1 = applyChannelAuthRegionService.list(Wrappers.<SysChannelAuthRegion>query().lambda()
                .eq(SysChannelAuthRegion::getChannelId, channelId).eq(SysChannelAuthRegion::getBusinessType,CarTypeEnum.oldCar));

        List<SysChannelAuthRegion> list2 = JSONUtil.parseArray(channelBasicInfo.getRegions()).toList(SysChannelAuthRegion.class);

        //获取两个list的不同
        if(newList1.size()>0){
            List<String> codes = getLose(newList1,list2,CarTypeEnum.newCar.toString());
            //根据区域code码和渠道id删除部门授权区域
            if (codes.size()>0) {
                //删除部门的授权区域
                applyDeptAuthRegionService.remove(Wrappers.<SysDeptAuthRegion>query().lambda()
                        .eq(SysDeptAuthRegion::getChannelId, channelId)
                        .in(SysDeptAuthRegion::getCode, codes).eq(SysDeptAuthRegion::getBusinessType,CarTypeEnum.newCar));
            }
        }
        // 去排除二手车的数据
        if(oldList1.size()>0){
            List<String> codes = getLose(oldList1,list2,CarTypeEnum.oldCar.toString());
            //根据区域code码和渠道id删除部门授权区域
            if (codes.size()>0) {
                //删除部门的授权区域
                applyDeptAuthRegionService.remove(Wrappers.<SysDeptAuthRegion>query().lambda()
                        .eq(SysDeptAuthRegion::getChannelId, channelId)
                        .in(SysDeptAuthRegion::getCode, codes).eq(SysDeptAuthRegion::getBusinessType,CarTypeEnum.oldCar));
            }
        }

//        }
        //根据渠道id删除渠道授权区域
        applyChannelAuthRegionService.remove(Wrappers.<SysChannelAuthRegion>query().lambda().eq(SysChannelAuthRegion::getChannelId, channelId));

        //保存新授权区域
        List<SysChannelAuthRegion> list = JSONUtil.parseArray(channelBasicInfo.getRegions()).toList(SysChannelAuthRegion.class);
        int size = list.size();
        if (size <= 1000) {
            applyChannelAuthRegionService.saveBatch(list);
        } else {
            log.info("渠道授权区域表写入数据:" + list.size());
            log.info("渠道授权区域表写入数据object:" + JSON.toJSONString(list.get(0)));
            List<SysChannelAuthRegion> insertLists = list.stream().filter(s->"110000".equals(s.getCode())).collect(Collectors.toList());
            log.info("渠道授权区域表只存4条数据:" + insertLists.size());
            applyChannelAuthRegionService.saveBatch(insertLists);
        }


        //step1: 保存渠道基本信息
        SysChannelBasicInfo one = applyChannelBasicInfoService.getOne(Wrappers.<SysChannelBasicInfo>query().lambda()
                .eq(SysChannelBasicInfo::getChannelId, channelBasicInfo.getChannelId()));
        if (one != null) {
            log.info("传递过来的数据："+channelBasicInfo);
            BeanUtils.copyProperties(channelBasicInfo, one);
            one.setNewCarUserHierarchy(channelBasicInfo.getNewCarUserHierarchy());
            one.setOldCarUserHierarchy(channelBasicInfo.getOldCarUserHierarchy());
            one.setChannelBelong(channelBasicInfo.getChannelBelong());
            one.setChoiceCardealerSwitch(channelBasicInfo.getChoiceCardealerSwitch());
            //add by mingzhi.li  2020-10-09 11:27:39 二手车进件是否关联车商
            one.setChoiceCardealerSwitchOld(channelBasicInfo.getChoiceCardealerSwitchOld());
            /**
             * 清除部门归属
             */
            one.setSubjectAttributionId(channelBasicInfo.getSubjectAttributionId());
            one.setSignType(StringUtils.isNoneBlank(channelBasicInfo.getSignType())?channelBasicInfo.getSignType():"");
            log.info("变更后数据："+one);
            applyChannelBasicInfoService.updateById(one);
        } else {
            SysChannelBasicInfo basicInfo = new SysChannelBasicInfo();
            BeanUtils.copyProperties(channelBasicInfo, basicInfo);
            applyChannelBasicInfoService.save(basicInfo);
        }

        //step2: 创建顶级部门
        SysDepartment dept = new SysDepartment();
        dept.setChannelId(channelBasicInfo.getChannelId());
        dept.setIsParent(false);
        dept.setTitle(channelBasicInfo.getChannelFullName());
        dept.setParentId(Long.valueOf(AfsEnumUtil.key(CreatUserEnum.DEFAULT_DEPT_PARENT)));
        dept.setStatus(LockFlagEnum.YES.getValue());
        dept.setSortOrder(BigDecimal.ZERO);
        List<SysDepartment> applyDepartments = applyDepartmentService.list(Wrappers.<SysDepartment>query().lambda().
                eq(SysDepartment::getChannelId, channelBasicInfo.getChannelId()));
        Long deptId;
        if (ObjectUtils.isEmpty(applyDepartments)) {
            applyDepartmentService.save(dept);
            deptId = dept.getId();
        } else {
            deptId = applyDepartments.get(0).getId();
        }

        /**
         * step3: 为部门增加授权区域
         * 部门授权区域初始化会保存两条数据，第一条是deptid=0的，第二条是真实的deptId。
         * 这样做是为了，后面增加部门时，方便获取上级部门的授权区域
         */
        //  ice 理解这个是为了清空之前的所有的关于这个区域的关联的省市区
        applyDeptAuthRegionService.remove(Wrappers.<SysDeptAuthRegion>query().lambda().eq(SysDeptAuthRegion::getChannelId, channelBasicInfo.getChannelId()).eq(SysDeptAuthRegion::getDeptId,AfsEnumUtil.key(CreatUserEnum.DEFAULT_DEPT_PARENT)));
        List<SysChannelAuthRegion> newCarChannelAuthRegion = applyChannelAuthRegionService.list(Wrappers.<SysChannelAuthRegion>query().lambda().
                eq(SysChannelAuthRegion::getChannelId, channelBasicInfo.getChannelId()).eq(SysChannelAuthRegion::getBusinessType, CarTypeEnum.newCar));
        List<SysChannelAuthRegion> oldCarChannelAuthRegion = applyChannelAuthRegionService.list(Wrappers.<SysChannelAuthRegion>query().lambda().
                eq(SysChannelAuthRegion::getChannelId, channelBasicInfo.getChannelId()).eq(SysChannelAuthRegion::getBusinessType, CarTypeEnum.oldCar));

        List<SysChannelAuthRegion> channelAuthRegion = new ArrayList<>();
        channelAuthRegion.addAll(newCarChannelAuthRegion);
        channelAuthRegion.addAll(oldCarChannelAuthRegion);
        // 这个是为了把渠道传递过来的是省市区 SysChannelAuthRegion 表  转成  SysDeptAuthRegion 表里的数据
        List<SysDeptAuthRegion> sysDeptAuthRegions = JSONUtil.parseArray(channelAuthRegion).toList(SysDeptAuthRegion.class);
        for (SysDeptAuthRegion d : sysDeptAuthRegions) {
            d.setDeptId(Long.valueOf(AfsEnumUtil.key(CreatUserEnum.DEFAULT_DEPT_PARENT)));
            d.setIsChecked(true);
        }
        //第一次保存父级的授权区域  暨 deptId=0
        int deptSize = sysDeptAuthRegions.size();
        if (deptSize <= 1000) {
            applyDeptAuthRegionService.saveBatch(sysDeptAuthRegions);
        } else {
            for (int i = 0; i < deptSize / 1000; i++) {
                if (deptSize / 1000 - i > 1) {
                    applyDeptAuthRegionService.saveBatch(sysDeptAuthRegions.subList(1000 * i, 1000 * i + 999));
                } else {
                    applyDeptAuthRegionService.saveBatch(sysDeptAuthRegions.subList(1000 * i, deptSize - 1));
                }

            }
        }
        // 先删除一次原来管理员带着的数据
        applyDeptAuthRegionService.remove(Wrappers.<SysDeptAuthRegion>query().lambda().eq(SysDeptAuthRegion::getChannelId, channelBasicInfo.getChannelId()).eq(SysDeptAuthRegion::getDeptId,deptId));
        List<SysDeptAuthRegion> listRegion = JSONUtil.parseArray(channelAuthRegion).toList(SysDeptAuthRegion.class);
        BeanUtils.copyProperties(sysDeptAuthRegions, listRegion);
        for (SysDeptAuthRegion d : listRegion) {
            d.setDeptId(deptId);
            d.setIsChecked(true);
        }

        int sizeRegion = listRegion.size();
        if (sizeRegion <= 1000) {
            applyDeptAuthRegionService.saveBatch(listRegion);
        } else {
            for (int i = 0; i < sizeRegion / 1000; i++) {
                if (sizeRegion / 1000 - i > 1) {
                    applyDeptAuthRegionService.saveBatch(listRegion.subList(1000 * i, 1000 * i + 999));
                } else {
                    applyDeptAuthRegionService.saveBatch(listRegion.subList(1000 * i, sizeRegion - 1));
                }

            }
        }


        /**
         * step5: 创建用户
         * 渠道管理员账号的规则，还是取名称前6位，不足6位用0补足，如果重复在名称后追加01-99的数字
         */
        SysUser user = new SysUser();

        // 声明一个接受最新的用户信息
        SysUser newUser = new SysUser();
        String channelName = channelBasicInfo.getChannelFullName();
        String regex = "[^0-9a-zA-Z\u4E00-\u9FFF]"; //去除特殊字符，只保留数字字母汉子
        channelName = channelName.replaceAll(regex, "");
        if (channelName.length() > 6) {
            channelName = channelName.substring(0, 6);
        }
        String userName = StringUtils.rightPad(ChineseCharacterUtil.getLowerCase(channelName, false), 6, "0");
        List<SysUserTemp> userNames = applyUserTempService.list(Wrappers.<SysUserTemp>query().lambda().eq(SysUserTemp::getUsername,userName));
        if (userNames != null && userNames.size() > 0) {
            for (int i = 1; i <= 99; i++) {
                String p = StringUtils.leftPad(String.valueOf(i), 2, "0");
                String newUserName = userName + p;
                List<SysUserTemp> un = applyUserTempService.list(Wrappers.<SysUserTemp>query().lambda().eq(SysUserTemp::getUsername,newUserName));
                if(un.size()>0){

                }else {
                    userName = newUserName;
                    break;
                }
//                for(String name :userNames){
//                    if(name.toUpperCase().contains(newUserName)){
//
//                    }else {
//                        userName = newUserName;
//                        break;
//                    }
//                }
//                if (!userNames.contains(newUserName.toUpperCase())) {
//                    userName = newUserName;
//                    break;
//                }
            }
        }
        user.setLastPassChangeTime(0L);
        user.setLastLoginFailTime(0L);
        user.setPassErrorCount(0);
        user.setUsername(channelBasicInfo.getChannelCode());
        user.setPhone(channelBasicInfo.getPhone());
        user.setUserEmail(channelBasicInfo.getEmail());
        user.setChannelId(channelBasicInfo.getChannelId());
        user.setPassword(SysUserService.ENCODER.encode(applyAdminConfig.getDefaultPassword()));
        user.setUserRealName(channelBasicInfo.getChannelAdmin());
        user.setLockFlag(CommonConstants.STATUS_NORMAL);
        user.setDeptId(deptId);
        user.setIdentityNumber(channelBasicInfo.getIdentityNumber());
        user.setCreateTime(new Date(System.currentTimeMillis()));
        // 查询这个合作商是否有管理员了
        List<SysUserPower> channelPower = sysUserPowerService.list(Wrappers.<SysUserPower>query().lambda().eq(SysUserPower::getChannelId,channelBasicInfo.getChannelId()).eq(SysUserPower::getUserManage, StatusEnum.YES.getValue().toString()));
        List<SysUser> u =new ArrayList<>();
        if(channelPower.size()>0){
            // 说明这个合作商已经有管理员了，那么去获取这个合作商管理员的账号
            u= applyUserService.list(Wrappers.<SysUser>query().lambda().eq(SysUser::getUserId,channelPower.get(0).getUserId()));
        }
        //step6: 为用户绑定角色
        Long newRoleId = null;
        Long oldRoleId = null;
        if (u.size()<=0) {
            // 把这条数据也存到临时表里
            SysUserTemp temp =new SysUserTemp();
            temp.setUsername(user.getUsername());
            temp.setUserRealName(user.getUserRealName());
            temp.setUserEmail(user.getUserEmail());
            temp.setPassword(user.getPassword());
            temp.setPhone(user.getPhone());
            temp.setDeptId(user.getDeptId());
            temp.setChannelId(user.getChannelId());
            temp.setIdentityNumber(user.getIdentityNumber());
            temp.setNewStatus(StatusEnum.PASS.getValue());
            temp.setOldStatus(StatusEnum.PASS.getValue());
            temp.setLockFlag(StatusEnum.NO.getValue());
            temp.setCreateTime(new Date(System.currentTimeMillis()));
            applyUserTempService.save(temp);
            // 再把这条数据查出来
            SysUserTemp sysTemp=applyUserTempService.getOne(Wrappers.<SysUserTemp>query().lambda().eq(SysUserTemp::getUsername,user.getUsername()));
            user.setUserId(sysTemp.getId());
            applyUserService.save(user);
            newUser =user;


            if (newCarChannelAuthRegion.size() > 0 && oldCarChannelAuthRegion.size() > 0) {
                // 说明这个角色新车二手车都有
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_CHANNEL_ADMIN)));
                    newRoleId = newRole.getId();
                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_CHANNEL_ADMIN)));
                    oldRoleId = oldRole.getId();
                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_DIRECT_ADMIN)));

                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_DIRECT_ADMIN)));
                    newRoleId = newRole.getId();
                    oldRoleId = oldRole.getId();

                }

            } else if (newCarChannelAuthRegion.size() > 0) {
                // 进去新车
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_CHANNEL_ADMIN)));

                    newRoleId = newRole.getId();
                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_DIRECT_ADMIN)));
                    newRoleId = newRole.getId();
                }

            } else if (oldCarChannelAuthRegion.size() > 0) {
                // 进来二手车
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_CHANNEL_ADMIN)));
                    oldRoleId = oldRole.getId();
                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_DIRECT_ADMIN)));
                    oldRoleId = oldRole.getId();
                }
            }


            //step6: 为用户绑定角色
            if (newCarChannelAuthRegion != null && newCarChannelAuthRegion.size() > 0) {
                SysUserRole userNewRole = new SysUserRole();
                userNewRole.setUserId(user.getUserId());
                userNewRole.setRoleId(newRoleId);
                userNewRole.setBusinessType(CarTypeEnum.newCar);
                applyUserRoleService.save(userNewRole);
                // 下面是为了给临时表赋值
                SysUserRoleTemp roleTemp =new SysUserRoleTemp();
                roleTemp.setUserId(user.getUserId());
                roleTemp.setRoleId(newRoleId);
                roleTemp.setBusinessType(CarTypeEnum.newCar);
                applyUserRoleTempService.save(roleTemp);


            }
            if (oldCarChannelAuthRegion != null && oldCarChannelAuthRegion.size() > 0) {
                SysUserRole userOldRole = new SysUserRole();
                userOldRole.setUserId(user.getUserId());
                userOldRole.setRoleId(oldRoleId);
                userOldRole.setBusinessType(CarTypeEnum.oldCar);
                applyUserRoleService.save(userOldRole);
                // 下面是为了给临时表赋值
                SysUserRoleTemp roleTemp =new SysUserRoleTemp();
                roleTemp.setUserId(user.getUserId());
                roleTemp.setRoleId(oldRoleId);
                roleTemp.setBusinessType(CarTypeEnum.oldCar);
                applyUserRoleTempService.save(roleTemp);
            }




        } else {
            // 去找到属于总账户的
            // 先声明一个总账号角色的id
            SysUser sy =new SysUser();
            if(u.size()==1){
                sy=u.get(0);
            }else {
                // 先查询出来所有的总账号角色信息
                List<SysRole> ro = applyRoleService.list(Wrappers.<SysRole>query().lambda()
                        .in(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_CHANNEL_ADMIN),AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_CHANNEL_ADMIN), AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_DIRECT_ADMIN),AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_DIRECT_ADMIN)));
                // 放角色id 的集合
                List<Long> roIds=new ArrayList<>();
                if(ro.size()>0){
                    for (SysRole ros : ro) {
                        roIds.add(ros.getId());
                    }
                }else {
                    throw new AfsBaseException("获取合作商角色有误！");
                }
                for (int i=0;i<u.size();i++){
                    // 去查询用户是不是总账
                    List<SysUserRole> sysUserRoles =applyUserRoleService.list(Wrappers.<SysUserRole>query().lambda().in(SysUserRole::getUserId,u.get(i).getUserId()).in(SysUserRole::getRoleId,roIds));
                    if(sysUserRoles.size()>0){
                        sy=u.get(i);
                        break;
                    }
                }
                if(sy==null){
                    throw new AfsBaseException("获取合作商信息有误！");
                }
            }

            // 重新查询一次数据
            SysUser us =applyUserService.getById(sy);
            //  更新用户数据
            us.setUserEmail(channelBasicInfo.getEmail());
            us.setPhone(channelBasicInfo.getPhone());
            us.setIdentityNumber(channelBasicInfo.getIdentityNumber());
            us.setUserRealName(channelBasicInfo.getChannelAdmin());
            us.setLockFlag(StatusEnum.NO.getValue());
            applyUserService.updateById(us);
            newUser=us;
            // 更新临时表的管理员数据
            SysUserTemp usTemp=applyUserTempService.getOne(Wrappers.<SysUserTemp>query().lambda().eq(SysUserTemp::getId,us.getUserId()));
            usTemp.setUserEmail(channelBasicInfo.getEmail());
            usTemp.setPhone(channelBasicInfo.getPhone());
            usTemp.setIdentityNumber(channelBasicInfo.getIdentityNumber());
            usTemp.setUserRealName(channelBasicInfo.getChannelAdmin());
            applyUserTempService.update(usTemp,Wrappers.<SysUserTemp>query().lambda().eq(SysUserTemp::getId,us.getUserId()));
            //  先清除正式表的所有数据
            applyUserRoleService.remove(Wrappers.<SysUserRole>query().lambda().eq(SysUserRole::getUserId, sy.getUserId()));
            // 再清除临时表的所有数据
            applyUserRoleTempService.remove(Wrappers.<SysUserRoleTemp>query().lambda().eq(SysUserRoleTemp::getUserId,sy.getUserId()));

            //step6: 为用户绑定角色
            if (newCarChannelAuthRegion.size() > 0 && oldCarChannelAuthRegion.size() > 0) {
                // 说明这个角色新车二手车都有
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_CHANNEL_ADMIN)));

                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_CHANNEL_ADMIN)));
                    newRoleId = newRole.getId();
                    oldRoleId = oldRole.getId();

                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_DIRECT_ADMIN)));

                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_DIRECT_ADMIN)));
                    newRoleId = newRole.getId();
                    oldRoleId = oldRole.getId();
                }

            } else if (newCarChannelAuthRegion.size() > 0) {
                // 进去新车
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_CHANNEL_ADMIN)));
                    newRoleId = newRole.getId();


                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole newRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_NEW_DIRECT_ADMIN)));
                    newRoleId = newRole.getId();


                }

            } else if (oldCarChannelAuthRegion.size() > 0) {
                // 进来二手车
                if (!ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进入这里说明是SP 或者总对总
                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_CHANNEL_ADMIN)));

                    oldRoleId = oldRole.getId();

                } else if (ChannelDic.CHANNEL_BELONG_DIRECTLY.equals(channelBasicInfo.getChannelBelong())) {
                    // 进来说明  直营
                    SysRole oldRole = applyRoleService.getOne(Wrappers.<SysRole>query().lambda()
                            .eq(SysRole::getRoleCode, AfsEnumUtil.key(CreatUserEnum.ROLE_OLD_DIRECT_ADMIN)));

                    oldRoleId = oldRole.getId();
                }
            }

            //step6: 为用户绑定角色
            if (newCarChannelAuthRegion != null && newCarChannelAuthRegion.size() > 0) {
                SysUserRole userNewRole = new SysUserRole();
                userNewRole.setUserId(sy.getUserId());
                userNewRole.setRoleId(newRoleId);
                userNewRole.setBusinessType(CarTypeEnum.newCar);
                applyUserRoleService.save(userNewRole);

                // 给临时表绑定角色
                SysUserRoleTemp roleTemp =new SysUserRoleTemp();
                roleTemp.setUserId(sy.getUserId());
                roleTemp.setRoleId(newRoleId);
                roleTemp.setBusinessType(CarTypeEnum.newCar);
                applyUserRoleTempService.save(roleTemp);
            }
            if (oldCarChannelAuthRegion != null && oldCarChannelAuthRegion.size() > 0) {
                SysUserRole userOldRole = new SysUserRole();
                userOldRole.setUserId(sy.getUserId());
                userOldRole.setRoleId(oldRoleId);
                userOldRole.setBusinessType(CarTypeEnum.oldCar);
                applyUserRoleService.save(userOldRole);

                // 给临时表绑定角色
                SysUserRoleTemp roleTemp =new SysUserRoleTemp();
                roleTemp.setUserId(sy.getUserId());
                roleTemp.setRoleId(oldRoleId);
                roleTemp.setBusinessType(CarTypeEnum.oldCar);
                applyUserRoleTempService.save(roleTemp);
            }
        }

        // 这边说明是修改权限数据
        SysUserPower power=sysUserPowerService.getOne(Wrappers.<SysUserPower>query().lambda().eq(SysUserPower::getUserId,newUser.getUserId()));
        if(power==null){
            power =new SysUserPower();
            // 说明是新增那么权限表
            power.setUserId(newUser.getUserId());
            power.setChannelId(newUser.getChannelId());
            if(newCarChannelAuthRegion.size() > 0){
                // 说明有新车
                power.setNewApplyPower(StatusEnum.NO.getValue().toString());
                power.setNewAccountPower(StatusEnum.NO.getValue().toString());
            }else {
                power.setNewApplyPower(StatusEnum.NOTHING.getValue().toString());
                power.setNewAccountPower(StatusEnum.NOTHING.getValue().toString());
            }
            if(oldCarChannelAuthRegion.size()>0){
                // 说明有二手车
                power.setOdlApplyPower(StatusEnum.NO.getValue().toString());
                power.setOldAccountPower(StatusEnum.NO.getValue().toString());
            }else {
                power.setOdlApplyPower(StatusEnum.NOTHING.getValue().toString());
                power.setOldAccountPower(StatusEnum.NOTHING.getValue().toString());
            }
            power.setLockFlag(StatusEnum.NO.getValue().toString());
            power.setUserManage(StatusEnum.YES.getValue().toString());
            sysUserPowerService.save(power);

        }else {
            //  说明之前有那么就去修改
            if(newCarChannelAuthRegion.size() > 0){
                // 说明有新车
                if(StatusEnum.NOTHING.getValue().toString().equals(power.getNewApplyPower())||power.getNewApplyPower()==null||power.getNewApplyPower()==""){
                    power.setNewApplyPower(StatusEnum.NO.getValue().toString());
                }
                if(StatusEnum.NOTHING.getValue().toString().equals(power.getNewAccountPower())||power.getNewAccountPower()==null||power.getNewAccountPower()==""){
                    power.setNewAccountPower(StatusEnum.NO.getValue().toString());
                }
            }else {
                power.setNewApplyPower(StatusEnum.NOTHING.getValue().toString());
                power.setNewAccountPower(StatusEnum.NOTHING.getValue().toString());
            }
            if(oldCarChannelAuthRegion.size()>0){
                // 说明有二手车
                if(StatusEnum.NOTHING.getValue().toString().equals(power.getOdlApplyPower())||power.getOdlApplyPower()==null||power.getOdlApplyPower()==""){
                    power.setOdlApplyPower(StatusEnum.NO.getValue().toString());
                }
                if(StatusEnum.NOTHING.getValue().toString().equals(power.getOldAccountPower())||power.getOldAccountPower()==null||power.getOldAccountPower()==""){
                    power.setOldAccountPower(StatusEnum.NO.getValue().toString());
                }
            }else {
                power.setOdlApplyPower(StatusEnum.NOTHING.getValue().toString());
                power.setOldAccountPower(StatusEnum.NOTHING.getValue().toString());
            }
            sysUserPowerService.update(power,Wrappers.<SysUserPower>query().lambda().eq(SysUserPower::getUserId,newUser.getUserId()));
            // 再查询一次权限
            SysUserPower newPower =sysUserPowerService.getOne(Wrappers.<SysUserPower>query().lambda().eq(SysUserPower::getUserId,newUser.getUserId()));
            if(StatusEnum.NOTHING.getValue().toString().equals(newPower.getOldAccountPower().toString())){
                applyUserTempService.update(Wrappers.<SysUserTemp>lambdaUpdate().set(SysUserTemp::getOldStatus,null).eq(SysUserTemp::getId,newUser.getUserId()));
            }else
            if(StatusEnum.NOTHING.getValue().toString().equals(newPower.getNewAccountPower().toString())){
                applyUserTempService.update(Wrappers.<SysUserTemp>lambdaUpdate().set(SysUserTemp::getNewStatus,null).eq(SysUserTemp::getId,newUser.getUserId()));
            }
        }
        return true;
    }


    private static List<String> getLose(List<SysChannelAuthRegion> list1, List<SysChannelAuthRegion> list2,String type) {
        List<String> newList = new ArrayList<String>();
        // list1：原来的数据  list2:现在的数据

        // 第一步先把现在所有的数据放在集合里面
        if (list2 != null && !list2.isEmpty()) {
            Map<String, String> dataMap = new HashMap<String, String>();
            for (SysChannelAuthRegion id : list2) {
                if(type.equals(id.getBusinessType().toString())){
                    dataMap.put(id.getCode(), id.getCode());
                }
            }

            //  第二部 把原来的数据现在没有的，就把这个数据存储
            if (list1.size()>0){
                for (SysChannelAuthRegion id : list1) {
                    if (!dataMap.containsKey(id.getCode())) {
                        newList.add(id.getCode());
                    }
                }
            }
            return newList;
        } else {
            return newList;
        }
    }

    @Override
    public MqTransCode getCode() {
        return MqTransCode.AFS_CHANNEL_TO_APPLY_ADMIN_CHANNEL_INFO;
    }
}

