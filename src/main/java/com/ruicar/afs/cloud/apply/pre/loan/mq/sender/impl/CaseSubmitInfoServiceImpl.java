package com.ruicar.afs.cloud.apply.pre.loan.mq.sender.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.apply.business.service.ApplyRecordDetailsService;
import com.ruicar.afs.cloud.apply.business.service.TsysParamConfigService;
import com.ruicar.afs.cloud.apply.business.service.WorkflowAgencyTaskService;
import com.ruicar.afs.cloud.apply.common.entity.ApplyAffiliatedUnit;
import com.ruicar.afs.cloud.apply.common.entity.ApplyBestSignRecord;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarInvoice;
import com.ruicar.afs.cloud.apply.common.entity.ApplyChannelInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyContractInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustAddressDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustBaseInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustContacts;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustPersonalDetail;
import com.ruicar.afs.cloud.apply.common.entity.ApplyEnterpriseCustomerDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyModifyHistory;
import com.ruicar.afs.cloud.apply.common.entity.ApplyOprRecord;
import com.ruicar.afs.cloud.apply.common.entity.ApplyOrderInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyRemindDetails;
import com.ruicar.afs.cloud.apply.common.entity.ChannelUniteInfo;
import com.ruicar.afs.cloud.apply.common.entity.ChannelUrgentConfig;
import com.ruicar.afs.cloud.apply.common.entity.PreApproveInfo;
import com.ruicar.afs.cloud.apply.common.entity.WorkflowAgencyTask;
import com.ruicar.afs.cloud.apply.common.entity.WorkflowRecordDetails;
import com.ruicar.afs.cloud.apply.common.enums.ChannelBelongEnum;
import com.ruicar.afs.cloud.apply.common.feign.Apply2CaseFeign;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConstants;
import com.ruicar.afs.cloud.apply.contract.condition.ApplyComparisonCondition;
import com.ruicar.afs.cloud.apply.contract.condition.ApplyResidenceComparisionCondition;
import com.ruicar.afs.cloud.apply.contract.condition.CertificateComparisionCondition;
import com.ruicar.afs.cloud.apply.contract.enums.CertTypeEnum;
import com.ruicar.afs.cloud.apply.contract.enums.CustomerRelationEnum;
import com.ruicar.afs.cloud.apply.contract.enums.MaritalEnum;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCarInvoiceService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyContractInfoService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCustAddressDetailsService;
import com.ruicar.afs.cloud.apply.margin.service.MarginInfoService;
import com.ruicar.afs.cloud.apply.pre.approve.enums.ApproveEnum;
import com.ruicar.afs.cloud.apply.pre.approve.feign.ConfigServiceFeign;
import com.ruicar.afs.cloud.apply.pre.approve.service.BestSignApplyService;
import com.ruicar.afs.cloud.apply.pre.approve.service.PreApproveService;
import com.ruicar.afs.cloud.apply.pre.config.CertificateConfig;
import com.ruicar.afs.cloud.apply.pre.loan.condition.CaseLoanApproveResultCondition;
import com.ruicar.afs.cloud.apply.pre.loan.condition.CaseSubmitInfoCondition;
import com.ruicar.afs.cloud.apply.pre.loan.condition.ContractCancelCondition;
import com.ruicar.afs.cloud.apply.pre.loan.controller.OrderMngController;
import com.ruicar.afs.cloud.apply.pre.loan.dto.TranSitCheckDto;
import com.ruicar.afs.cloud.apply.pre.loan.feign.AdminUserFegin;
import com.ruicar.afs.cloud.apply.pre.loan.feign.CaseRemindFeign;
import com.ruicar.afs.cloud.apply.pre.loan.feign.CasebaseQueryFeign;
import com.ruicar.afs.cloud.apply.pre.loan.mq.annotations.ResidenceOcrField;
import com.ruicar.afs.cloud.apply.pre.loan.mq.dto.CaseApplyResidenceDto;
import com.ruicar.afs.cloud.apply.pre.loan.mq.dto.CaseBusinessLicenceDto;
import com.ruicar.afs.cloud.apply.pre.loan.mq.dto.CaseDrivingLicenceDto;
import com.ruicar.afs.cloud.apply.pre.loan.mq.dto.CaseSbmitInfoDto;
import com.ruicar.afs.cloud.apply.pre.loan.mq.dto.CertificateDto;
import com.ruicar.afs.cloud.apply.pre.loan.mq.sender.CaseSubmitInfoSender;
import com.ruicar.afs.cloud.apply.pre.loan.mq.sender.service.CaseSubmitInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyAffiliatedUnitService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCarDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyChannelInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustAddressService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustBaseInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustContactsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustPersonalService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyEnterpriseCustomerDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyModifyHistoryService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyOprRecordService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyOrderInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyRemindDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.CaseMainInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ChannelUniteInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ChannelUrgentConfigService;
import com.ruicar.afs.cloud.apply.pre.loan.service.business.HandlingInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.vo.AssetChangeVo;
import com.ruicar.afs.cloud.apply.router.dto.ParamManageDTO;
import com.ruicar.afs.cloud.apply.router.service.ApplyRouterService;
import com.ruicar.afs.cloud.apply.router.service.HeavyRouterFtpFileService;
import com.ruicar.afs.cloud.common.modules.engineering.dto.SysUserDTO;
import com.ruicar.afs.cloud.message.sendmessage.service.MessageService;
import com.ruicar.afs.cloud.zhengxin.entity.ApplyZxReportCount;
import com.ruicar.afs.cloud.zhengxin.service.ApplyZxReportCountService;
import com.ruicar.afs.cloud.apply.shangshangqian.service.BestSignService;
import com.ruicar.afs.cloud.bizcommon.business.dto.FinDiscountPlanDTO;
import com.ruicar.afs.cloud.bizcommon.business.dto.HandlingInfoDto;
import com.ruicar.afs.cloud.bizcommon.business.dto.MarginInfoDto;
import com.ruicar.afs.cloud.bizcommon.business.dto.PlanRateDTO;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountCost;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinDiscountPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinFinancingItems;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinMainInfo;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinPlanRate;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRentAdjustDetails;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinRepaymentPlan;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinTermsDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyDiscountDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyFinancingItemsService;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyRentAdjustDetailsService;
import com.ruicar.afs.cloud.bizcommon.business.service.FinMainInfoService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinDiscountCostServer;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinDiscountPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinPlanRateService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinRepaymentPlanService;
import com.ruicar.afs.cloud.bizcommon.business.service.data.FinTermsDetailsService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.core.util.SpringContextHolder;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.AffiliatedWayEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.BusinessTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarNatureEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CarTypeEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.IsTypeNumEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.OrderStatusEnum;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.RedFlushEnum;
import com.ruicar.afs.cloud.common.modules.apply.enums.StatusEnum;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveInformDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ApproveSpecialSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CallBackApplyDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseBaseInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCarInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCarStyleDetailDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseChannelInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseChannelUniteInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustAddressDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustContactDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustIndividualDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseCustInfoDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseEnterpriseCustomerDetailsDTO;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.CaseUrgentDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ComAttachmentFileDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.OrderSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.ReconsiderationDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.RepealDealDto;
import com.ruicar.afs.cloud.common.modules.dto.mq.approve.UrgentInfoSubmitInfo;
import com.ruicar.afs.cloud.common.modules.dto.mq.approveprev.ApprovePrevInfoDto;
import com.ruicar.afs.cloud.common.modules.engineering.enums.ImportApplyStatusEnum;
import com.ruicar.afs.cloud.common.modules.engineering.utils.ResponseUtils;
import com.ruicar.afs.cloud.common.modules.enums.ApplySceneEnum;
import com.ruicar.afs.cloud.common.modules.enums.OprTypeEnum;
import com.ruicar.afs.cloud.common.mq.rabbit.message.AfsTransEntity;
import com.ruicar.afs.cloud.common.mq.rabbit.message.MqTransCode;
import com.ruicar.afs.cloud.filecenter.FileCenterHelper;
import com.ruicar.afs.cloud.filecenter.FileType;
import com.ruicar.afs.cloud.filecenter.dto.UploadResult;
import com.ruicar.afs.cloud.image.config.FileProperties;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.enums.FileTypeEnum;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.interfaces.direct.dto.BydDirectDealerReqData;
import com.ruicar.afs.cloud.interfaces.direct.enums.BydDirectDealerTypeEnum;
import com.ruicar.afs.cloud.interfaces.direct.enums.LoanAutoResultTypeEnum;
import com.ruicar.afs.cloud.interfaces.direct.service.BydDirecrtDealerService;
import com.ruicar.afs.cloud.interfaces.ocr.dto.OcrReqData;
import com.ruicar.afs.cloud.interfaces.ocr.dto.OcrResData;
import com.ruicar.afs.cloud.interfaces.ocr.enums.OCRType;
import com.ruicar.afs.cloud.interfaces.ocr.service.OcrService;
import com.ruicar.afs.cloud.interfaces.wlease.ocr.dto.TencentBizLicenseResp;
import com.ruicar.afs.cloud.interfaces.wlease.router.enums.RouterApplyStatusEnum;
import com.ruicar.afs.cloud.parameter.commom.enums.AProveBusinessTypeEnum;
import com.ruicar.afs.cloud.parameter.commom.enums.AffiliatedWay;
import com.ruicar.afs.cloud.parameter.commom.enums.AssetChangeEnums;
import com.ruicar.afs.cloud.parameter.commom.enums.CarNature;
import com.ruicar.afs.cloud.parameter.commom.enums.CarType;
import com.ruicar.afs.cloud.parameter.commom.enums.CostType;
import com.ruicar.afs.cloud.parameter.commom.enums.CustRelationEnums;
import com.ruicar.afs.cloud.parameter.commom.enums.FormalReviewEnums;
import com.ruicar.afs.cloud.parameter.commom.enums.OrderOprType;
import com.ruicar.afs.cloud.parameter.commom.enums.WhetherEnum;
import com.ruicar.afs.cloud.third.system.invoke.dto.Request;
import com.ruicar.afs.cloud.third.system.invoke.dto.Response;
import com.ruicar.afs.cloud.vehicle.service.VehicleHelper;
import com.ruicar.afs.cloud.vehicle.vo.VehicleSeriesVO;
import com.ruicar.afs.cloud.vehicle.vo.VehicleStyleVO;
import com.ruicar.afs.cloud.zhengxin.condition.ZxReportCondition;
import com.ruicar.afs.cloud.zhengxin.entity.ComReportFile;
import com.ruicar.afs.cloud.zhengxin.service.ComReportFileService;
import com.ruicar.afs.cloud.zhengxin.service.ZxThirdService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 进件申请提交案件审批实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class CaseSubmitInfoServiceImpl implements CaseSubmitInfoService {

    private final CaseSubmitInfoSender caseSubmitInfoSender;
    private final ApplyCustAddressService applyCustAddressService;
    private final ApplyCustBaseInfoService applyCustBaseInfoService;
    private final ApplyCustContactsService applyCustContactsService;
    private final ApplyCustPersonalService applyCustPersonalService;
    private final ApplyAffiliatedUnitService applyAffiliatedUnitService;
    private final ApplyCarDetailsService applyCarDetailsService;
    private final ApplyCarInvoiceService applyCarInvoiceService;
    private final ApplyCostDetailsService applyCostDetailsService;
    private final ApplyDiscountDetailsService applyDiscountDetailsService;
    private final ApplyFinancingItemsService applyFinancingItemsService;
    private final ApplyOrderInfoService applyOrderInfoService;
    private final ApplyChannelInfoService applyChannelInfoService;
    private final ApplyRecordDetailsService applyRecordDetailsService;
    private final ApplyRentAdjustDetailsService applyRentAdjustDetailsService;
    private final ApplyModifyHistoryService applyModifyHistoryService;
    private final ApplyOprRecordService applyOprRecordService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final WorkflowAgencyTaskService workflowAgencyTaskService;
    private final ApplyRemindDetailsService applyRemindDetailsService;
    private final TsysParamConfigService tSysParamConfigService;
    private final ChannelUrgentConfigService channelUrgentConfigService;
    private final ComAttachmentManagementService attachmentManagementService;
    private final ApplyContractInfoService applyContractInfoService;
    private final ApplyEnterpriseCustomerDetailsService applyEnterpriseCustomerDetailsService;
    private final ChannelUniteInfoService channelUniteInfoService;
    private final HandlingInfoService handlingInfoService;
    private final MarginInfoService marginInfoService;
    private final FinDiscountPlanService finDiscountPlanService;
    private final FinPlanRateService finPlanRateService;
    private final FinTermsDetailsService finTermsDetailsService;
    private final FinRepaymentPlanService finRepaymentPlanService;
    private final FinDiscountCostServer finDiscountCostServer;
    private final FinMainInfoService finMainInfoService;
    private final CasebaseQueryFeign casebaseQueryFeign;
    private final VehicleHelper vehicleHelper;
    private final ApplyRouterService applyRouterService;
    private final HeavyRouterFtpFileService heavyRouterFtpFileService;
    private final BydDirecrtDealerService bydDirecrtDealerService;
    private final CaseMainInfoService caseMainInfoService;
    private final OcrService ocrService;
    private final Apply2CaseFeign apply2CaseFeign;
    private final ApplyCustAddressDetailsService applyCustAddressDetailsService;
    private final ConfigServiceFeign configServiceFeign;
    private final FileProperties fileProperties;
    private final ZxThirdService zxThirdService;
    private final CaseRemindFeign caseRemindFeign;
    private final ComReportFileService comReportFileService;
    private final CertificateConfig certificateConfig;
    private final ApplyZxReportCountService applyZxReportCountService;
    private final AdminUserFegin adminUserFegin;
    private final MessageService messageService;
    private final PreApproveService preApproveService;

    private final BestSignService bestSignService;
    private final BestSignApplyService bestSignApplyService;

    private static final String DEFAULT_VIDEO_FORMAT = ".mp4";

    /**
     * 进件提交报文组装
     *
     * @param applyNo
     * @return
     */
    @Override
    public AfsTransEntity<OrderSubmitInfo> returnMessage(String applyNo) {

        AfsTransEntity<OrderSubmitInfo> transEntity = new AfsTransEntity();
        CaseSbmitInfoDto checkData = this.getData(applyNo);
        //数据组装
        OrderSubmitInfo orderSubmitInfo = this.orderSubmitInfoAssemble(checkData, applyNo);
        if(ObjectUtils.isNull(SecurityUtils.getUser())){
            orderSubmitInfo.setOperatorRealName(null);
        }else {
            if("1".equals(checkData.getApplyOrderInfo().getSystemType())){
                orderSubmitInfo.setOperatorRealName(checkData.getApplyOrderInfo().getApplyReporter());
            }else{
                orderSubmitInfo.setOperatorRealName(SecurityUtils.getUser().getUserRealName());
            }
        }
        //查询审批记录
        List<WorkflowRecordDetails> recordDetailsList = applyRecordDetailsService.list(Wrappers.<WorkflowRecordDetails>query().lambda().eq(WorkflowRecordDetails::getBusinessNo, applyNo));
        //查询操作记录为复议的数据
        List<ApplyOprRecord> applyOprRecordList = this.applyOprRecordService.list(Wrappers.<ApplyOprRecord>query()
                .lambda().eq(StringUtil.isNotEmpty(applyNo), ApplyOprRecord::getApplyNo, applyNo)
                .eq(ApplyOprRecord::getApplyType, String.valueOf(OrderOprType.RECONSIDER)));
        List<ApplyRemindDetails> remindDetailsList = applyRemindDetailsService.list(Wrappers.<ApplyRemindDetails>query().lambda()
                .eq(StringUtil.isNotEmpty(applyNo), ApplyRemindDetails::getApplyNo, applyNo)
                .eq(ApplyRemindDetails::getRemindType, AProveBusinessTypeEnum.SEND_BACK_TO_DEALER.getCode()));
        //订单草稿状态
        if (ApplyConstants.APPLY_STATUS_DRAFT.equals(checkData.getApplyOrderInfo().getApplyStatus())
                ||ApplyConstants.APPLY_STATUS_SUBMIT_WAIT.equals(checkData.getApplyOrderInfo().getApplyStatus())
                || ApplyConstants.CALL_BACK_WAIT_SUBMIT.equals(checkData.getApplyOrderInfo().getApplyStatus())
                || ApplyConstants.APPLY_STATUS_REVIEWPENDINGSUBMISSION.equals(checkData.getApplyOrderInfo().getApplyStatus())
                ||ApplyConstants.APPLY_STATUS_CONTRACT_BACK_WAIT_SUBMIT.equals(checkData.getApplyOrderInfo().getApplyStatus())) {

            //查找撤回记录
            List<ApplyOprRecord> callBackOprRecord = applyOprRecordService.list(Wrappers.<ApplyOprRecord>query()
                    .lambda().eq(StringUtil.isNotEmpty(applyNo), ApplyOprRecord::getApplyNo, applyNo)
                    .eq(ApplyOprRecord::getApplyType, OrderOprType.BACK));
            //判断是否是撤回后重新提交（1、撤回待处理状态）
            if (ApplyConstants.CALL_BACK_WAIT_SUBMIT.equals(checkData.getApplyOrderInfo().getApplyStatus())) {
                //常规审批,查询过玄武，撤回后重新提交
                orderSubmitInfo.setIsCallBackSubmit(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                List<WorkflowAgencyTask> agencyTaskServiceOne = this.workflowAgencyTaskService.list(Wrappers.<WorkflowAgencyTask>query()
                        .lambda().eq(WorkflowAgencyTask::getBusinessNo, applyNo)
                        .orderByDesc(WorkflowAgencyTask::getCreateTime));
                ;
                if(agencyTaskServiceOne.size()>0) {
                    orderSubmitInfo.setDataId(agencyTaskServiceOne.get(0).getProcessId());
                }
                //更新订单状态为已提交
                checkData.getApplyOrderInfo().setApplyStatus(ApplyConstants.APPLY_STATUS_SUBMIT);
                checkData.getApplyOrderInfo().setApplyNo(applyNo);
                orderSubmitInfo.setTimestamp(new Date());
                this.applyOrderInfoService.updateOrderApplyStatus(checkData.getApplyOrderInfo());
                log.info("当前状态（撤回待处理），撤回后重新提交：{}", applyNo);
            }
            //判断是否是撤回后重新提交（2、草稿状态）
            else if ((ApplyConstants.APPLY_STATUS_DRAFT.equals(checkData.getApplyOrderInfo().getApplyStatus())||(ApplyConstants.APPLY_STATUS_SUBMIT_WAIT.equals(checkData.getApplyOrderInfo().getApplyStatus())))
                    && CollectionUtil.isEmpty(recordDetailsList)
                    && CollectionUtil.isEmpty(remindDetailsList) && CollectionUtil.isNotEmpty(callBackOprRecord)) {
                orderSubmitInfo.setIsCallBackSubmit(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO.getCode());
                orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                //更新订单状态为已提交
                checkData.getApplyOrderInfo().setApplyStatus(ApplyConstants.APPLY_STATUS_SUBMIT);
                checkData.getApplyOrderInfo().setApplyNo(applyNo);
                orderSubmitInfo.setTimestamp(new Date());
                this.applyOrderInfoService.updateOrderApplyStatus(checkData.getApplyOrderInfo());
                log.info("当前状态（草稿），撤回后重新提交：{}", applyNo);
            }
            //判断是否是放款撤回首节点
            else if(ApplyConstants.APPLY_STATUS_CONTRACT_BACK_WAIT_SUBMIT.equals(checkData.getApplyOrderInfo().getApplyStatus())){
                orderSubmitInfo.setIsCallBackSubmit(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                //更新订单状态为已提交
                checkData.getApplyOrderInfo().setApplyStatus(ApplyConstants.APPLY_STATUS_SUBMIT);
                checkData.getApplyOrderInfo().setApplyNo(applyNo);
                orderSubmitInfo.setTimestamp(new Date());
                orderSubmitInfo.setIsLoanCallBackSubmit(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                this.applyOrderInfoService.updateOrderApplyStatus(checkData.getApplyOrderInfo());
                log.info("当前状态（撤回首节点待处理），撤回后重新提交：{}", applyNo);
            }
            //没有审批记录,留言为空,首次提交
            else if (CollectionUtil.isEmpty(recordDetailsList) && CollectionUtil.isEmpty(remindDetailsList)) {
                //常规审批，首次提交，新增
                orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                orderSubmitInfo.setOprType(OprTypeEnum.OPRADD.getCode());
                //更新订单状态为已提交
                checkData.getApplyOrderInfo().setApplyStatus(ApplyConstants.APPLY_STATUS_SUBMIT);
                checkData.getApplyOrderInfo().setIntoFirstDate(new Date());
                checkData.getApplyOrderInfo().setApplyNo(applyNo);
                this.applyOrderInfoService.updateOrderApplyStatus(checkData.getApplyOrderInfo());
                /**首次提交修改状态*/
                this.comAttachmentFileService.updateFileStatusByBusiNo(applyNo);
                log.info("订单首次提交：{}", applyNo);
            } else if (CollectionUtil.isNotEmpty(applyOprRecordList)
                    && checkData.getApplyOrderInfo().getApplyStatus().equals(ApplyConstants.APPLY_STATUS_REVIEWPENDINGSUBMISSION)) {
                //判断该订单是否已超期，不可正式复议
                long day = DateUtil.betweenDay(new Date(), checkData.getApplyOrderInfo().getIntoFirstDate(), true);
                int i = Integer.parseInt(tSysParamConfigService.getParamValue("reconsiderCancel", "day", "30"));
                if (day > i) {
                    throw new AfsBaseException("订单已超期，不可正式复议");
                }
                //存在复议信息,复议
                //更新订单状态为待审核
                this.applyOrderInfoService.updateApplyStatus(applyNo, ApplyConstants.APPLY_STATUS_PENDING,null);
                //正式复议,场景正式，修改
                orderSubmitInfo.setApplyScene(ApplySceneEnum.FORMALREVIEW.getCode());
                orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                log.info("正式复议提交：{}", applyNo);
            } else if (CollectionUtil.isNotEmpty(remindDetailsList)) {
                //留言不为空
                remindDetailsList.forEach(applyRemindDetails -> {
                    //如果是流程外退回->提交，更新订单状态为已提交，场景常规审批,修改
                    if (AProveBusinessTypeEnum.SEND_BACK_TO_DEALER.getCode().equals(applyRemindDetails.getRemindType())) {
                        //复议流程外退回
                        if (String.valueOf(OrderOprType.RECONSIDER).equals(applyRemindDetails.getBusinessType())) {
                            orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                            orderSubmitInfo.setApplyScene(ApplySceneEnum.FORMALREVIEW.getCode());
                            //修改订单为已提交
                            this.applyOrderInfoService.updateApplyStatus(applyNo, ApplyConstants.APPLY_STATUS_PENDING,null);
                            /**
                             * 判断更新路由订单状态
                             */
                            ParamManageDTO paramManageDTO = new ParamManageDTO(applyNo, RouterApplyStatusEnum.APPLY_APPROVE.getState(), RouterApplyStatusEnum.APPLY_APPROVE.getDesc());
                            applyRouterService.setRouterOrderStatus(paramManageDTO);
                        } else {
                            //常规流程退回
                            orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                            orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                            //修改订单为已提交
                            this.applyOrderInfoService.updateApplyStatus(applyNo, ApplyConstants.APPLY_STATUS_SUBMIT,null);
                        }
                        log.info("流程外退回订单重新提交：{}", applyNo);
                    }
                });
            }
        }
        else if (ApplyConstants.APPLY_STATUS_SUSPEND.equals(checkData.getApplyOrderInfo().getApplyStatus())) {
            //待办任务内有复议退回,就是正式复议的退回
            //查询待办任务
            List<WorkflowAgencyTask> agencyTaskServicelist = this.workflowAgencyTaskService.list(Wrappers.<WorkflowAgencyTask>query()
                    .lambda().eq(WorkflowAgencyTask::getBusinessNo, applyNo)
                    .orderByDesc(WorkflowAgencyTask::getCreateTime));
            if (CollectionUtil.isNotEmpty(agencyTaskServicelist)) {
                WorkflowAgencyTask agencyTaskServiceOne=agencyTaskServicelist.get(0);
                //查审批记录
                List<WorkflowRecordDetails> workflowRecordDetailsList = this.applyRecordDetailsService.list(Wrappers.<WorkflowRecordDetails>query().lambda()
                        .eq(WorkflowRecordDetails::getBusinessNo, applyNo).eq(WorkflowRecordDetails::getApproveResult, AProveBusinessTypeEnum.BACK.getCode())
                        .eq(WorkflowRecordDetails::getProcessId, agencyTaskServiceOne.getProcessId()));
                //如果是复议退回
                if (String.valueOf(OrderOprType.RECONSIDER).equals(agencyTaskServiceOne.getBusinessType())) {
                    //退回的审批记录
                    recordDetailsList.forEach(workflowRecordDetails -> {
                        if (AProveBusinessTypeEnum.BACK.getCode().equals(workflowRecordDetails.getApproveResult())) {
                            if (ObjectUtil.isNotNull(workflowRecordDetails)) {
                                orderSubmitInfo.setDataId(agencyTaskServiceOne.getProcessId());
                            }
                            this.applyOrderInfoService.updateApplyStatus(applyNo, ApplyConstants.APPLY_STATUS_REPLY,null);
                            orderSubmitInfo.setLeaveMessage(assemblyData(applyNo).toString());
                            orderSubmitInfo.setApplyScene(ApplySceneEnum.FORMALREVIEW.getCode());
                            //复议退回修改
                            orderSubmitInfo.setOprType(OprTypeEnum.UPDATE_RECONSIDER.getCode());
                            //移除代办任务
                            this.workflowAgencyTaskService.removeById(agencyTaskServiceOne);
                            log.info("复议退回订单提交：{}", applyNo);
                        }
                    });
                } else if (String.valueOf(OrderOprType.NORMAL).equals(agencyTaskServiceOne.getBusinessType())) {
                    //进件退回,常规审批,修改
                    orderSubmitInfo.setApplyScene(ApplySceneEnum.GENERALAPPROVAL.getCode());
                    orderSubmitInfo.setOprType(OprTypeEnum.OPRUPDATE.getCode());
                    if (CollectionUtil.isNotEmpty(workflowRecordDetailsList)) {
                        orderSubmitInfo.setDataId(agencyTaskServiceOne.getProcessId());
                    }
                    //退回(修订暂停)->提交信审->修订回复
                    this.applyOrderInfoService.updateApplyStatus(applyNo, ApplyConstants.APPLY_STATUS_REPLY,null);
                    orderSubmitInfo.setLeaveMessage(assemblyData(applyNo).toString());
                    //移除代办任务
                    this.workflowAgencyTaskService.removeById(agencyTaskServiceOne);
                    //退回后-提交修改文件历史版本
                    this.attachmentManagementService.updateFileVersionByBusiNo(applyNo, "orderApply");
                    log.info("进退回后-提交修改文件历史版本：{}", applyNo);
                    //影像件信息
                    List<ComAttachmentFile> comAttachmentFileList = this.comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                            .eq(StringUtil.isNotEmpty(applyNo), ComAttachmentFile::getBusiNo, applyNo));
                    log.info("进件订单退回（进件影像件）：{}", comAttachmentFileList);
                    //影像件
                    ArrayList<ComAttachmentFileDto> comAttachmentFileListDto = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(comAttachmentFileList)) {
                        for (ComAttachmentFile comAttachmentFile : comAttachmentFileList) {
                            ComAttachmentFileDto comAttachmentFileDto = new ComAttachmentFileDto();
                            BeanUtil.copyProperties(comAttachmentFile, comAttachmentFileDto);
                            comAttachmentFileListDto.add(comAttachmentFileDto);
                        }
                    }
                    orderSubmitInfo.setComAttachmentFileListDto(comAttachmentFileListDto);
                    log.info("进件订单退回（修订暂停->修订回复）：{}", applyNo);
                }
            }
        }
        else if (checkData.getApplyOrderInfo().getApplyStatus().equals(ApplyConstants.APPLY_STATUS_REVOKE)) {
            //撤销的订单不可再次提交
            throw new AfsBaseException("申请编号为:" + checkData.getApplyOrderInfo().getApplyNo() + "的订单已撤销,不可重复提交");
        }
        orderSubmitInfo.setTimestamp(new Date());

        transEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_NEW_ORDER);
        transEntity.setData(orderSubmitInfo);
        return transEntity;
    }

    /**
     * 获取进件数据
     */
    @Override
    public CaseSbmitInfoDto getData(String applyNo) {
        CaseSbmitInfoDto caseSbmitInfoDto = new CaseSbmitInfoDto();
        ApplyOrderInfo orderInfoByApplyNo = this.applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        caseSbmitInfoDto.setApplyOrderInfo(orderInfoByApplyNo);
        log.info("订单申请信息：{}",JSONObject.toJSONString(orderInfoByApplyNo));
        //车辆信息
        ApplyCarDetails applyCarDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>query().lambda()
                .eq(ApplyCarDetails::getApplyNo, applyNo));
        caseSbmitInfoDto.setApplyCarDetails(applyCarDetails);
        log.info("车辆信息：{}",JSONObject.toJSONString(applyCarDetails));
        //根据申请编号查询客户基础信息
        List<ApplyCustBaseInfo> applyCustBaseInfoList = this.applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo));
        log.info("根据申请编号查询客户基础信息：{}",JSONObject.toJSONString(applyCustBaseInfoList));
        caseSbmitInfoDto.setApplyCustBaseInfoList(applyCustBaseInfoList);
        //获取地址信息,根据applyNo
        List<ApplyCustAddressDetails> applyCustAddressDetailsList = this.applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                .eq(ApplyCustAddressDetails::getApplyNo, applyNo));
        log.info("获取地址信息：{}",JSONObject.toJSONString(applyCustAddressDetailsList));
        caseSbmitInfoDto.setAddressDetailsList(applyCustAddressDetailsList);
        //获取客户联系人信息list
        List<ApplyCustContacts> applyCustContactsList = applyCustContactsService.list(Wrappers.<ApplyCustContacts>query().lambda()
                .eq(ApplyCustContacts::getApplyNo, applyNo));
        log.info("获取客户联系人信息list：{}",JSONObject.toJSONString(applyCustContactsList));
        caseSbmitInfoDto.setApplyCustContactsList(applyCustContactsList);
        //融资信息
        List<FinCostDetails> applyCostDetailsList = this.applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, applyNo));
        log.info("融资信息：{}",JSONObject.toJSONString(applyCostDetailsList));
        caseSbmitInfoDto.setFinCostDetailsList(applyCostDetailsList);
        /**贴息方案表  金融产品利率表 */
        List<FinDiscountPlan> finDiscountPlans = finDiscountPlanService.list(Wrappers.<FinDiscountPlan>query().lambda()
                .eq(FinDiscountPlan::getApplyNo, applyNo).eq(FinDiscountPlan::getDelFlag,WhetherEnum.No.getIndex()));
        log.info("贴息方案表：{}",JSONObject.toJSONString(finDiscountPlans));
        caseSbmitInfoDto.setFinDiscountPlans(finDiscountPlans);
        List<FinPlanRate> finPlanRates = finPlanRateService.list(Wrappers.<FinPlanRate>lambdaQuery()
                .eq(FinPlanRate::getApplyNo, applyNo).eq(FinPlanRate::getDelFlag,WhetherEnum.No.getIndex()));
        log.info("金融产品利率表：{}",JSONObject.toJSONString(finPlanRates));
        caseSbmitInfoDto.setFinPlanRates(finPlanRates);
        /**订单期数明细 还款计划明细 */
        caseSbmitInfoDto.setFinTermsDetailsList(getFinTermsDetails(applyNo));
        caseSbmitInfoDto.setFinRepaymentPlanList(getRepaymentPlans(applyNo));
        //影像件信息
        List<ComAttachmentFile> comAttachmentFileList = this.comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                .eq(ComAttachmentFile::getBusiNo, applyNo)
                .eq(ComAttachmentFile::getBelongNo,applyNo)
                .eq(ComAttachmentFile::getFileSource, "com_attachment_management")
        );
        caseSbmitInfoDto.setAttachmentFileList(comAttachmentFileList);
        //融资项目信息
        List<FinFinancingItems> finFinancingItemsList = this.applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda()
                .eq(FinFinancingItems::getApplyNo, applyNo));
        caseSbmitInfoDto.setFinancingItemsList(finFinancingItemsList);
        //贴息信息
        List<FinDiscountDetails> applyDiscountDetailsList = this.applyDiscountDetailsService.list(Wrappers.<FinDiscountDetails>query().lambda()
                .eq(FinDiscountDetails::getApplyNo, applyNo));
        caseSbmitInfoDto.setDiscountDetailsList(applyDiscountDetailsList);
        //查询款式信息
        String styleId = applyCarDetails.getStyleId();
        if(styleId == null){
            styleId = "";
        }
        String styleName = applyCarDetails.getStyleName();
        if(styleName == null){
            styleName = "";
        }
        VehicleStyleVO styleVO = vehicleHelper.getModelDetailInfo(styleId, applyCarDetails.getModelId(), styleName);
        caseSbmitInfoDto.setTsysVehicleDetail(styleVO);
        //查询车型信息
        VehicleSeriesVO vehicleSeriesVO = vehicleHelper.getSeriesByBranCodeName(applyCarDetails.getBrandId(),applyCarDetails.getModelName());
        log.info("查询车型信息：{}",JSONObject.toJSONString(vehicleSeriesVO));
        caseSbmitInfoDto.setTsysVehicleModel(vehicleSeriesVO);
        //查询渠道信息
        ApplyChannelInfo channelInfoByApplyNo = this.applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        caseSbmitInfoDto.setApplyChannelInfo(channelInfoByApplyNo);
        //查询租金调整信息
        List<FinRentAdjustDetails> applyRentAdjustDetailsList = this.applyRentAdjustDetailsService.getRentAdjustDetails(applyNo);
        caseSbmitInfoDto.setRentAdjustDetailsList(applyRentAdjustDetailsList);
        //联合方信息
        List<ChannelUniteInfo> channelUniteInfoList = channelUniteInfoService.list(Wrappers.<ChannelUniteInfo>query().lambda()
                .eq(ChannelUniteInfo::getApplyNo, applyNo));
        caseSbmitInfoDto.setChannelUniteInfoList(channelUniteInfoList);

        return caseSbmitInfoDto;
    }

    private List<FinRepaymentPlan> getRepaymentPlans(String applyNo) {
        return finRepaymentPlanService.list(Wrappers.<FinRepaymentPlan>lambdaQuery()
                .eq(FinRepaymentPlan::getApplyNo, applyNo).eq(FinRepaymentPlan::getDelFlag,WhetherEnum.No.getIndex()));
    }

    /**
     * 数据组装
     *
     * @param checkData
     * @return
     */
    @Override
    public OrderSubmitInfo orderSubmitInfoAssemble(CaseSbmitInfoDto checkData, String applyNo) {
        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);

        OrderSubmitInfo orderSubmitInfo = new OrderSubmitInfo();
        //案件-客户基础信息list
        ArrayList<CaseCustInfoDto> caseCustInfoList = this.caseCustBaseInfo(checkData.getApplyCustBaseInfoList());
        //案件-客户个人明细list
        ArrayList<CaseCustIndividualDto> caseCustIndividualList = this.caseCustIndividualList(checkData.getApplyCustBaseInfoList());
        if (ObjectUtils.isNotEmpty(caseCustIndividualList)){
            orderSubmitInfo.setCaseCustIndividualList(caseCustIndividualList);
        }
        //案件-客户企业明细list
        ArrayList<CaseEnterpriseCustomerDetailsDTO> caseEnterpriseCustomerDetailsDTOS = this.caseEnterpriseCustomerDetailsList(checkData.getApplyCustBaseInfoList());
        if(ObjectUtils.isNotEmpty(caseEnterpriseCustomerDetailsDTOS)){
            orderSubmitInfo.setCaseEnterpriseCustomerDetailsDTOArrayList(caseEnterpriseCustomerDetailsDTOS);
        }
        //联合方信息
        ArrayList<CaseChannelUniteInfoDto> caseChannelUniteInfoDtos = this.channelUniteInfoList(checkData.getChannelUniteInfoList());
        if (ObjectUtils.isNotEmpty(caseChannelUniteInfoDtos)){
            orderSubmitInfo.setCaseChannelUniteInfoDto(caseChannelUniteInfoDtos);
        }
        //案件-客户地址明细list
        ArrayList<CaseCustAddressDto> caseCustAddressDtos = this.caseCustAddressList(checkData.getAddressDetailsList());
        //案件-客户联系人明细list
        ArrayList<CaseCustContactDto> caseCustContactDtos = this.caseCustContactList(checkData.getApplyCustContactsList());
        //案件-渠道
        CaseChannelInfoDto caseChannelInfoDto = this.caseChannelInfo(checkData.getApplyChannelInfo(), checkData.getApplyOrderInfo());
        //案件-基本信息
        CaseBaseInfoDto caseBaseInfoDto = this.caseBaseInfoDTO(checkData.getApplyOrderInfo(), checkData.getApplyCustBaseInfoList(), checkData.getFinCostDetailsList());
        //案件-车辆信息
        ArrayList<CaseCarInfoDto> caseCarInfoDtos = this.caseCarInfoList(checkData.getApplyCarDetails(), checkData.getApplyOrderInfo());
        //案件-融资信息
        ArrayList<FinCostDetails> caseCostInfoDtos = this.caseCostInfoList(checkData.getFinCostDetailsList());
        //案件-融资项目信息
        ArrayList<FinFinancingItems> caseFinancingItemList = this.caseFinancingItemList(checkData.getFinancingItemsList());
        //案件-贴息明细
        ArrayList<FinDiscountDetails> caseDiscountDetailList = this.caseDiscountDetailList(checkData.getDiscountDetailsList());
        //案件-车辆样式信息
        ArrayList<CaseCarStyleDetailDto> caseCarStyleDetailList = this.caseCarStyleDetailList(checkData.getApplyCarDetails(), checkData.getTsysVehicleDetail(), checkData.getTsysVehicleModel());
        //租金调整
        ArrayList<FinRentAdjustDetails> adjustDetailsDtoArrayList = this.applyRentAdjustDetails(checkData.getRentAdjustDetailsList());
        //影像件
        ArrayList<ComAttachmentFileDto> attachmentFileDtoArrayList = this.comAttachmentFile(checkData.getAttachmentFileList());
        // 融资费用主表
        List<FinMainInfo> finMainInfos = this.financingCharges(applyNo);
        List<MarginInfoDto> dtos = marginInfoService.getMarginInfoDtos(applyNo);
        orderSubmitInfo.setMarginInfoDtos(dtos);
        List<HandlingInfoDto> handlingInfoDtos=handlingInfoService.getHandlingInfoDtos(applyNo);
        orderSubmitInfo.setHandlingInfoDtos(handlingInfoDtos);
        //设置贴息明细 及 产品费率明细
        List<FinDiscountPlanDTO> finDiscountPlans = setFindiscountPlanDTO(applyNo);
        orderSubmitInfo.setFinDiscountPlanDtos(finDiscountPlans);
        List<FinDiscountCost> finDiscountCostList = finDiscountCostServer.list(Wrappers.<FinDiscountCost>query().lambda()
                .eq(FinDiscountCost::getApplyNo, applyNo).eq(FinDiscountCost::getDelFlag, WhetherEnum.No.getIndex()));
        ArrayList<FinDiscountCost> finDiscountCosts = new ArrayList<>(finDiscountCostList);
        orderSubmitInfo.setFinDiscountCostList(finDiscountCosts);
        List<PlanRateDTO> finPlanRates = setPlanRateDTO(applyNo);
        orderSubmitInfo.setFinPlanRateDtos(finPlanRates);
        //设置期数明细及还款计划
        orderSubmitInfo.setFinTermsDetailsList(getFinTermsDetails(applyNo));
        orderSubmitInfo.setFinRepaymentPlanList(getRepaymentPlans(applyNo));
        orderSubmitInfo.setMarginInfoDtos(dtos);
        orderSubmitInfo.setCaseCustAddressList(caseCustAddressDtos);
        orderSubmitInfo.setCaseCustInfoList(caseCustInfoList);
        orderSubmitInfo.setCaesCustContactList(caseCustContactDtos);
        orderSubmitInfo.setCaseChannelInfo(caseChannelInfoDto);
        orderSubmitInfo.setCaseBaseInfo(caseBaseInfoDto);
        orderSubmitInfo.setCaseCarInfoList(caseCarInfoDtos);
        orderSubmitInfo.setCaseCostInfoList(caseCostInfoDtos);
        orderSubmitInfo.setCaseFinancingItemList(caseFinancingItemList);
        orderSubmitInfo.setCaseDiscountDetailList(caseDiscountDetailList);
        orderSubmitInfo.setCaseCarStyleDetailList(caseCarStyleDetailList);
        orderSubmitInfo.setApplyRentAdjustDetailsList(adjustDetailsDtoArrayList);
        orderSubmitInfo.setComAttachmentFileListDto(attachmentFileDtoArrayList);
        orderSubmitInfo.setApplyNo(applyNo);
        orderSubmitInfo.setCaseFinMainInfo(finMainInfos);
        orderSubmitInfo.setEditFlag(applyOrderInfo.getEditFlag());
        if(AfsEnumUtil.key(ImportApplyStatusEnum.APPLY_STATUS_DRAFT).equals(applyOrderInfo.getApplyStatus())){
            orderSubmitInfo.setOprType( OprTypeEnum.OPRADD.getCode());
        }else{
            orderSubmitInfo.setOprType( OprTypeEnum.OPRUPDATE.getCode());
        }
        return orderSubmitInfo;
    }

    private List<FinMainInfo> financingCharges(String applyNo) {
        List<FinMainInfo> finMainInfo = finMainInfoService.list(Wrappers.<FinMainInfo>query().lambda()
                .eq(FinMainInfo::getApplyNo, applyNo));
        return finMainInfo;
    }
    /**
     * 获取订单期数明细
     * @param applyNo
     * @return
     */
    private List<FinTermsDetails> getFinTermsDetails(String applyNo) {
        List<FinTermsDetails> list = finTermsDetailsService.list(Wrappers.<FinTermsDetails>query().lambda()
                .eq(FinTermsDetails::getApplyNo, applyNo).eq(FinTermsDetails::getDelFlag,WhetherEnum.No.getIndex()));
        return list;
    }

    /**
     * 设置费率明细dto
     * @param applyNo
     * @return
     */
    private List<PlanRateDTO> setPlanRateDTO(String applyNo) {
        List<PlanRateDTO> list = finPlanRateService.list(Wrappers.<FinPlanRate>lambdaQuery().eq(FinPlanRate::getApplyNo,applyNo)
                .eq(FinPlanRate::getDelFlag,WhetherEnum.No.getIndex())).stream().map(finPlanRate -> {
            PlanRateDTO dto =new PlanRateDTO();
            BeanUtils.copyProperties(finPlanRate,dto);
            return dto;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 设置贴息明细DTO
     * @param applyNo
     * @return
     */
    private List<FinDiscountPlanDTO> setFindiscountPlanDTO(String applyNo) {

        List<FinDiscountPlanDTO> list =finDiscountPlanService.list(Wrappers.<FinDiscountPlan>lambdaQuery().eq(FinDiscountPlan::getApplyNo,applyNo)
        .eq(FinDiscountPlan::getDelFlag,WhetherEnum.No.getIndex())).stream().map(finDiscountPlan -> {
            FinDiscountPlanDTO dto =new FinDiscountPlanDTO();
            BeanUtils.copyProperties(finDiscountPlan,dto);
            return dto;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 企业客户明细信息
     *
     * @param
     * @return
     */
    @Override
    public ArrayList<CaseEnterpriseCustomerDetailsDTO> caseEnterpriseCustomerDetailsList(List<ApplyCustBaseInfo> applyCustBaseInfoList) {
        ArrayList<CaseEnterpriseCustomerDetailsDTO> applyEnterpriseCustomerDetailsArrayList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCustBaseInfoList)) {
            for (ApplyCustBaseInfo applyCustBaseInfo : applyCustBaseInfoList) {
                if (applyCustBaseInfo.getCustType().equals(ApplyConstants.ENTERPRISE)) {
                    CaseEnterpriseCustomerDetailsDTO applyEnterpriseCustomerDetailsDTO = new CaseEnterpriseCustomerDetailsDTO();
                    ApplyEnterpriseCustomerDetails applyEnterpriseCustomerDetails = this.applyEnterpriseCustomerDetailsService.getOne(Wrappers.<ApplyEnterpriseCustomerDetails>query().lambda()
                            .eq(ApplyEnterpriseCustomerDetails::getCustId, applyCustBaseInfo.getId()));
                    if (ObjectUtil.isNull(applyEnterpriseCustomerDetails)) {
                        throw new AfsBaseException("个人信息不存在");
                    }
                    //根据客户编号查询客户关联的地址信息
                    List<ApplyCustAddressDetails> applyCustAddressDetailsList = this.applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                            .eq(ApplyCustAddressDetails::getCustId, applyCustBaseInfo.getId()));
                    if (CollectionUtil.isEmpty(applyCustAddressDetailsList)) {
                        throw new AfsBaseException("地址信息不存在");
                    }
                    if (applyCustAddressDetailsList.size() <= 1) {
                        throw new AfsBaseException("请添加地址信息");
                    }
                    ApplyCustAddressDetails applyCustAddressDetails = applyCustAddressDetailsList.get(1);
                    if (ObjectUtil.isNull(applyCustAddressDetails) || StringUtil.isEmpty(String.valueOf(applyCustAddressDetails.getCustId()))) {
                        throw new AfsBaseException("客户号不能为空");
                    }
                    applyEnterpriseCustomerDetailsDTO.setCustId(applyEnterpriseCustomerDetails.getCustId());
                    applyEnterpriseCustomerDetailsDTO.setAnnualIncomeOfEnterprises(applyEnterpriseCustomerDetails.getAnnualIncomeOfEnterprises());
                    applyEnterpriseCustomerDetailsDTO.setBusinessContacts(applyEnterpriseCustomerDetails.getBusinessContacts());
                    applyEnterpriseCustomerDetailsDTO.setClientProperty(applyEnterpriseCustomerDetails.getClientProperty());
                    applyEnterpriseCustomerDetailsDTO.setCompanyPhone(applyEnterpriseCustomerDetails.getCompanyPhone());
                    applyEnterpriseCustomerDetailsDTO.setCreditInvestigation(applyEnterpriseCustomerDetails.getCreditInvestigation());
                    applyEnterpriseCustomerDetailsDTO.setEnterpriseContactMobilePhone(applyEnterpriseCustomerDetails.getEnterpriseContactMobilePhone());
                    applyEnterpriseCustomerDetailsDTO.setEnterpriseName(applyEnterpriseCustomerDetails.getEnterpriseName());
                    applyEnterpriseCustomerDetailsDTO.setEnterprisesEngagedIndustry(applyEnterpriseCustomerDetails.getEnterprisesEngagedIndustry());
                    applyEnterpriseCustomerDetailsDTO.setEnterprisesType(applyEnterpriseCustomerDetails.getEnterprisesType());
                    applyEnterpriseCustomerDetailsDTO.setNatureEnterprise(applyEnterpriseCustomerDetails.getNatureEnterprise());
                    applyEnterpriseCustomerDetailsDTO.setRemarks(applyEnterpriseCustomerDetails.getRemarks());
                    applyEnterpriseCustomerDetailsDTO.setSocunicrtCode(applyEnterpriseCustomerDetails.getSocunicrtCode());
                    applyEnterpriseCustomerDetailsDTO.setApplyNo(applyEnterpriseCustomerDetails.getApplyNo());
                    applyEnterpriseCustomerDetailsDTO.setCustomerChannel(applyEnterpriseCustomerDetails.getCustomerChannel());
                    applyEnterpriseCustomerDetailsDTO.setCompanyLicenseDate(applyEnterpriseCustomerDetails.getCompanyLicenseDate());
                    applyEnterpriseCustomerDetailsDTO.setEnterpriseStatus(applyEnterpriseCustomerDetails.getEnterpriseStatus());
                    applyEnterpriseCustomerDetailsArrayList.add(applyEnterpriseCustomerDetailsDTO);
                }
            }
        }
        return applyEnterpriseCustomerDetailsArrayList;


    }

    /**
     * 获取退回订单-进件留言
     *
     * @param applyNo
     * @return
     */
    @Override
    public StringBuilder assemblyData(String applyNo) {
        StringBuilder message = new StringBuilder();
        //查询修订回复留言
        List<ApplyRemindDetails> detailsList = this.applyRemindDetailsService.list(Wrappers.<ApplyRemindDetails>query().lambda().eq(ApplyRemindDetails::getApplyNo, applyNo));
        if (CollectionUtil.isNotEmpty(detailsList)) {
            for (ApplyRemindDetails applyRemindDetails : detailsList) {
                //进件留言,且未发送至审批
                if (WhetherEnum.YES.getIndex().equals(applyRemindDetails.getIsSelf()) && StringUtil.isEmpty(applyRemindDetails.getRemindType())) {
                    if (StringUtil.isEmpty(applyRemindDetails.getIsReturn())) {
                        applyRemindDetails.setIsReturn(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO.getCode());
                        applyRemindDetailsService.updateById(applyRemindDetails);
                        message = message.append(applyRemindDetails.getMsgContent()).append(",");
                    }
                }
            }
            log.info("进件订单退回（修订暂停->修订回复）：{},进件留言组装", applyNo);
        }
        return message;
    }

    /**
     * 案件需要的客户基础信息list
     *
     * @param applyCustBaseInfoList
     * @return
     */
    @Override
    public ArrayList<CaseCustInfoDto> caseCustBaseInfo(List<ApplyCustBaseInfo> applyCustBaseInfoList) {
        ArrayList<CaseCustInfoDto> caseCustInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCustBaseInfoList)) {
            for (ApplyCustBaseInfo applyCustBaseInfo : applyCustBaseInfoList) {
                CaseCustInfoDto custBaseInfo = new CaseCustInfoDto();
                if (ObjectUtil.isNull(applyCustBaseInfo) || StringUtil.isEmpty(applyCustBaseInfo.getApplyNo())) {
                    throw new AfsBaseException("客户基本信息不存在或申请编号不存在--客户基础信息");
                }
                custBaseInfo.setId(applyCustBaseInfo.getId());
                custBaseInfo.setApplyNo(applyCustBaseInfo.getApplyNo());
                custBaseInfo.setCertNo(applyCustBaseInfo.getCertNo());
                custBaseInfo.setCertType(applyCustBaseInfo.getCertType());
                custBaseInfo.setCustName(applyCustBaseInfo.getCustName());
                custBaseInfo.setCustRelation(applyCustBaseInfo.getCustRelation());
                custBaseInfo.setCustRole(applyCustBaseInfo.getCustRole());
                custBaseInfo.setCustType(applyCustBaseInfo.getCustType());
                custBaseInfo.setCertStartDate(applyCustBaseInfo.getCertStartDate());
                custBaseInfo.setCertEndDate(applyCustBaseInfo.getCertEndDate());
                custBaseInfo.setTelPhone(applyCustBaseInfo.getTelPhone());
                custBaseInfo.setEmail(applyCustBaseInfo.getEmail());
                custBaseInfo.setEmployNo(applyCustBaseInfo.getEmployNo());
                custBaseInfo.setSignType(StringUtils.isNotBlank(applyCustBaseInfo.getSignType())?applyCustBaseInfo.getSignType():null);
                if (StringUtils.isNotBlank(applyCustBaseInfo.getIsShowFile())&&applyCustBaseInfo.getIsShowFile().equals(IsTypeNumEnum.NO.getCode())){
                    custBaseInfo.setIsShowFile(IsTypeNumEnum.YES.getCode());
                }
                custBaseInfo.setFirstGuarantor(applyCustBaseInfo.getFirstGuarantor());
                custBaseInfo.setIsFirstGuarantor(applyCustBaseInfo.getIsFirstGuarantor());
                if (Boolean.TRUE.equals(applyCustBaseInfo.getIsLongTerm())) {
                    custBaseInfo.setIsLongTerm(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
                } else if (Boolean.FALSE.equals(applyCustBaseInfo.getIsLongTerm())) {
                    custBaseInfo.setIsLongTerm(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO.getCode());
                }
                caseCustInfoList.add(custBaseInfo);
            }
        }
        return caseCustInfoList;
    }

    /**
     * 案件需要的个人客户信息明细
     *
     * @param applyCustBaseInfoList
     * @return
     */
    @Override
    public ArrayList<CaseCustIndividualDto> caseCustIndividualList(List<ApplyCustBaseInfo> applyCustBaseInfoList) {
        ArrayList<CaseCustIndividualDto> caseCustIndividualList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCustBaseInfoList)) {
            for (ApplyCustBaseInfo applyCustBaseInfo : applyCustBaseInfoList) {
                CaseCustIndividualDto caseCustIndividualDTO = new CaseCustIndividualDto();
                //获取个人明细信息,根据客户主表id
                if (applyCustBaseInfo.getCustType().equals(ApplyConstants.PERSONAL)) {
                    ApplyCustPersonalDetail applyCustPersonalDetail = this.applyCustPersonalService.getOne(Wrappers.<ApplyCustPersonalDetail>query().lambda()
                            .eq(ApplyCustPersonalDetail::getCustId, applyCustBaseInfo.getId()));
                    if (ObjectUtil.isNull(applyCustPersonalDetail)) {
                        throw new AfsBaseException("个人信息不存在");
                    }
                    //根据客户编号查询客户关联的地址信息
                    List<ApplyCustAddressDetails> applyCustAddressDetailsList = this.applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda()
                            .eq(ApplyCustAddressDetails::getCustId, applyCustBaseInfo.getId()));
                    if (CollectionUtil.isEmpty(applyCustAddressDetailsList)) {
                        throw new AfsBaseException("地址信息不存在");
                    }
                    if (applyCustAddressDetailsList.size() <= 1) {
                        throw new AfsBaseException("请添加地址信息");
                    }
                    ApplyCustAddressDetails applyCustAddressDetails = applyCustAddressDetailsList.get(1);
                    if (ObjectUtil.isNull(applyCustPersonalDetail) || StringUtil.isEmpty(String.valueOf(applyCustPersonalDetail.getCustId()))) {
                        throw new AfsBaseException("客户号不能为空");
                    }
                    //wjy修改(2023-05-09)，新增字段【子女数量】【邮箱】【户籍地址】
                    caseCustIndividualDTO.setEmail(applyCustPersonalDetail.getEmail());//邮箱地址
                    caseCustIndividualDTO.setNumberOffspring(applyCustPersonalDetail.getNumberOffspring());//子女人数
                    caseCustIndividualDTO.setRegisteredResidenceType(applyCustPersonalDetail.getRegisteredResidenceType());//户籍类型 0本地/1非本地 默认本地0
                    //wjy修改(2023-05-12)，新增字段【配偶姓名】【配偶电话】【配偶单位名称】【微信/QQ号】
                    caseCustIndividualDTO.setSpouseName(applyCustPersonalDetail.getSpouseName());//配偶姓名
                    caseCustIndividualDTO.setSpousePhone(applyCustPersonalDetail.getSpousePhone());//配偶电话
                    caseCustIndividualDTO.setSpouseCompany(applyCustPersonalDetail.getSpouseCompany());//配偶单位名称
                    caseCustIndividualDTO.setWechatQq(applyCustPersonalDetail.getWechatQq());//微信/QQ号
                    caseCustIndividualDTO.setAge(applyCustPersonalDetail.getAge());
                    caseCustIndividualDTO.setCustId((applyCustPersonalDetail.getCustId()));
                    caseCustIndividualDTO.setSpellName(applyCustPersonalDetail.getSpellName());
                    caseCustIndividualDTO.setBirthday(applyCustPersonalDetail.getBirthday());
                    caseCustIndividualDTO.setSex(applyCustPersonalDetail.getSex());
                    caseCustIndividualDTO.setAge(applyCustPersonalDetail.getAge());
                    caseCustIndividualDTO.setFamilyNumbers(applyCustPersonalDetail.getFamilyNumbers());
                    caseCustIndividualDTO.setMaritalStatus(applyCustPersonalDetail.getMaritalStatus());
                    caseCustIndividualDTO.setWorkAge(applyCustPersonalDetail.getWorkAge());
                    caseCustIndividualDTO.setDrivingType(applyCustPersonalDetail.getDrivingType());
                    caseCustIndividualDTO.setDrivingLicenceNo(applyCustPersonalDetail.getDrivingLicenceNo());
                    caseCustIndividualDTO.setHighestEducation(applyCustPersonalDetail.getHighestEducation());
                    caseCustIndividualDTO.setProfessional(applyCustPersonalDetail.getProfessional());
                    caseCustIndividualDTO.setProfessionalType(applyCustPersonalDetail.getJobsType());
                    caseCustIndividualDTO.setUnitName(applyCustPersonalDetail.getUnitName());
                    caseCustIndividualDTO.setUnitType(applyCustPersonalDetail.getUnitNature());
                    caseCustIndividualDTO.setIndustryType(applyCustPersonalDetail.getIndustryType());
                    caseCustIndividualDTO.setEmployeeType(applyCustPersonalDetail.getEmployeeType());
                    caseCustIndividualDTO.setPosition(applyCustPersonalDetail.getPosition());
                    caseCustIndividualDTO.setScale(applyCustPersonalDetail.getCompanyScale());
                    caseCustIndividualDTO.setUnitTelPhone(applyCustPersonalDetail.getCompanyPhone());
                    caseCustIndividualDTO.setWorkStartDate(applyCustPersonalDetail.getWorkStartDate());
                    caseCustIndividualDTO.setDriverLicenceNo(applyCustPersonalDetail.getDriverLicenceNo());
                    caseCustIndividualDTO.setAnnualIncome(applyCustPersonalDetail.getAnnualIncome());
                    caseCustIndividualDTO.setAgeOfApplicant(applyCustPersonalDetail.getAgeOfApplicant());
                    caseCustIndividualDTO.setClientProperty(applyCustPersonalDetail.getClientProperty());
                    caseCustIndividualDTO.setCustomerChannel(applyCustPersonalDetail.getCustomerChannel());
                    if (applyCustPersonalDetail.getMonthlyIncome() != null) {
                        caseCustIndividualDTO.setMonthlyIncome(applyCustPersonalDetail.getMonthlyIncome());
                    }
                    if (applyCustPersonalDetail.getTotalIncome() != null) {
                        caseCustIndividualDTO.setTotalIncome(applyCustPersonalDetail.getTotalIncome());
                    }
                    if (applyCustPersonalDetail.getOutstandingLoan() != null) {
                        caseCustIndividualDTO.setOutstandingLoan(applyCustPersonalDetail.getOutstandingLoan());
                    }
                    if (applyCustPersonalDetail.getMonthlyIncome() != null) {
                        caseCustIndividualDTO.setOtherIncome(applyCustPersonalDetail.getTotalIncome());
                    }
                    if (applyCustPersonalDetail.getMonthlySpend() != null) {
                        caseCustIndividualDTO.setMonthlySpend(applyCustPersonalDetail.getMonthlySpend());
                    }
                    if (applyCustPersonalDetail.getMonthlyPayment() != null) {
                        caseCustIndividualDTO.setMonthlyPayment(applyCustPersonalDetail.getMonthlyPayment());
                    }
                    caseCustIndividualDTO.setNationality(applyCustPersonalDetail.getNationality());
                    caseCustIndividualDTO.setHouseType(applyCustAddressDetails.getHouseType());
                    caseCustIndividualDTO.setHouseArea(applyCustAddressDetails.getHouseArea());
                    caseCustIndividualDTO.setHouseStartDate(applyCustAddressDetails.getHouseStartDate());
                    if (StringUtil.isNotEmpty(applyCustPersonalDetail.getIsDriver()) && WhetherEnum.YES.getIndex().equals(applyCustPersonalDetail.getIsDriver())) {
                        caseCustIndividualDTO.setIsDriver(applyCustPersonalDetail.getIsDriver());
                        caseCustIndividualDTO.setDriverName(applyCustPersonalDetail.getDriverName());
                        caseCustIndividualDTO.setDriverCertNo(applyCustPersonalDetail.getDriverCertNo());
                        caseCustIndividualDTO.setDriverLicenceNo(applyCustPersonalDetail.getDriverLicenceNo());
                    }
                    caseCustIndividualList.add(caseCustIndividualDTO);
                }
            }
        }
        return caseCustIndividualList;
    }


    /**
     * 案件需要的地址明细信息
     *
     * @param applyCustAddressDetailsList
     * @return
     */
    @Override
    public ArrayList<CaseCustAddressDto> caseCustAddressList
    (List<ApplyCustAddressDetails> applyCustAddressDetailsList) {
        ArrayList<CaseCustAddressDto> custCustAddressList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCustAddressDetailsList)) {
            for (ApplyCustAddressDetails applyCustAddressDetails : applyCustAddressDetailsList) {
                CaseCustAddressDto caseCustAddressDTO = new CaseCustAddressDto();
                if (ObjectUtil.isNull(applyCustAddressDetails) || StringUtil.isEmpty(applyCustAddressDetails.getApplyNo())) {
                    throw new AfsBaseException("客户地址信息不存在或申请编号不存在");
                }
                if (ObjectUtil.isNull(applyCustAddressDetails) || StringUtil.isEmpty(String.valueOf(applyCustAddressDetails.getCustId()))) {
                    throw new AfsBaseException("客户号不能为空");
                }
                caseCustAddressDTO.setApplyNo(applyCustAddressDetails.getApplyNo());
                caseCustAddressDTO.setCustId(applyCustAddressDetails.getCustId());
                caseCustAddressDTO.setHouseType(applyCustAddressDetails.getHouseType());
                caseCustAddressDTO.setAddressType(applyCustAddressDetails.getAddressType());
                caseCustAddressDTO.setHouseArea(applyCustAddressDetails.getHouseArea());
                caseCustAddressDTO.setHouseStartDate(applyCustAddressDetails.getHouseStartDate());
                caseCustAddressDTO.setProvince(applyCustAddressDetails.getProvince());
                caseCustAddressDTO.setCity(applyCustAddressDetails.getCity());
                caseCustAddressDTO.setCounty(applyCustAddressDetails.getCounty());
                caseCustAddressDTO.setStreet(applyCustAddressDetails.getStreet());
                caseCustAddressDTO.setGbCode(applyCustAddressDetails.getGbCode());
                caseCustAddressDTO.setDetailAddress(applyCustAddressDetails.getDetailAddress());
                caseCustAddressDTO.setPostalCode(applyCustAddressDetails.getPostalCode());
                caseCustAddressDTO.setIsDefault(applyCustAddressDetails.getIsDefault()==null ? null : String.valueOf(applyCustAddressDetails.getIsDefault()));
                caseCustAddressDTO.setTown(applyCustAddressDetails.getTown());
                caseCustAddressDTO.setLongitude(applyCustAddressDetails.getLongitude());
                caseCustAddressDTO.setLatitude(applyCustAddressDetails.getLatitude());
                custCustAddressList.add(caseCustAddressDTO);

            }
        }
        return custCustAddressList;
    }

    /**
     * 案件需要的客户联系人信息明细
     *
     * @param applyCustContactsList
     * @return
     */
    @Override
    public ArrayList<CaseCustContactDto> caseCustContactList(List<ApplyCustContacts> applyCustContactsList) {
        ArrayList<CaseCustContactDto> caseCustContactList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCustContactsList)) {
            for (ApplyCustContacts contacts : applyCustContactsList) {
                //获取客户基本信息
                ApplyCustBaseInfo custBaseInfo = this.applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                        .eq(ApplyCustBaseInfo::getApplyNo, contacts.getApplyNo()).eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER));
                if (ObjectUtil.isNull(contacts) || StringUtil.isEmpty(contacts.getApplyNo())) {
                    throw new AfsBaseException("联系人信息不存在或申请编号不存在");
                }
                if (ObjectUtil.isNull(custBaseInfo)) {
                    throw new AfsBaseException("客户基础信息不存在");
                }
                CaseCustContactDto caseCustContactDTO = new CaseCustContactDto();
                caseCustContactDTO.setCustId(String.valueOf(custBaseInfo.getId()));
                caseCustContactDTO.setApplyNo(contacts.getApplyNo());
                caseCustContactDTO.setCustRelation(contacts.getCustRelation());
                caseCustContactDTO.setCustName(contacts.getCustName());
                caseCustContactDTO.setTelPhone(contacts.getTelPhone());
                caseCustContactDTO.setCertType(contacts.getCertType());
                caseCustContactDTO.setCertNo(contacts.getCertNo());
                caseCustContactDTO.setId(contacts.getId());
                caseCustContactDTO.setCompanyName(contacts.getCompanyName());
                caseCustContactDTO.setCompanyPhone(contacts.getCompanyPhone());
                if (contacts.getMonthlyIncome() != null) {
                    caseCustContactDTO.setMonthlyIncome(contacts.getMonthlyIncome());
                }
                caseCustContactDTO.setLivingProvince(contacts.getLivingProvince());
                caseCustContactDTO.setLivingCity(contacts.getLivingCity());
                caseCustContactDTO.setLivingCounty(contacts.getLivingCounty());
                caseCustContactDTO.setLivingStreet(contacts.getLivingStreet());
                caseCustContactDTO.setDetailAddress(contacts.getDetailAddress());
                caseCustContactDTO.setTown(contacts.getTown());
                caseCustContactDTO.setFirstFlag(contacts.getFirstFlag());
                /**
                 * 添加驾驶证档案编号字段
                 * added by Alex, add time: 2022-6-6 16:55:05
                 */
                caseCustContactDTO.setDriverLicenseNo(contacts.getDriverLicenseNo());
                caseCustContactList.add(caseCustContactDTO);
                caseCustContactDTO.setEmail(contacts.getEmail());
            }
        }
        return caseCustContactList;
    }

    /**
     * 案件渠道信息
     *
     * @param applyChannelInfo
     * @param orderInfoByApplyNo
     * @return
     */
    @Override
    public CaseChannelInfoDto caseChannelInfo(ApplyChannelInfo applyChannelInfo, ApplyOrderInfo orderInfoByApplyNo) {
        CaseChannelInfoDto caseChannelInfoDTO = new CaseChannelInfoDto();
        if (ObjectUtil.isNotNull(applyChannelInfo)) {
            caseChannelInfoDTO.setDealerNo(applyChannelInfo.getChannelCode());
            caseChannelInfoDTO.setDealerName(applyChannelInfo.getChannelName());
            //直营
            if (StringUtil.isNotEmpty(applyChannelInfo.getDirectCarDealersId())) {
                caseChannelInfoDTO.setCarDealersId(applyChannelInfo.getDirectCarDealersId());
                caseChannelInfoDTO.setCarDealersName(applyChannelInfo.getDirectCarDealersName());
            } else if (StringUtil.isNotEmpty(applyChannelInfo.getOrdinaryCarDealersId())) {
                //普通
                caseChannelInfoDTO.setCarDealersId(applyChannelInfo.getOrdinaryCarDealersId());
                caseChannelInfoDTO.setCarDealersName(applyChannelInfo.getOrdinaryCarDealersName());
            }
        }
        if (StringUtil.isEmpty(orderInfoByApplyNo.getApplyNo())) {
            throw new AfsBaseException("申请编号不存在");
        }
        //车商id

        caseChannelInfoDTO.setApplyNo(orderInfoByApplyNo.getApplyNo());
        caseChannelInfoDTO.setSaleAdvisor(orderInfoByApplyNo.getSellerRealName());
        caseChannelInfoDTO.setSalePhone(orderInfoByApplyNo.getSellerPhone());
        caseChannelInfoDTO.setDeptAttributionId(applyChannelInfo.getDeptAttributionId());
        caseChannelInfoDTO.setDeptAttributionTitle(applyChannelInfo.getDeptAttributionTitle());
        caseChannelInfoDTO.setSubjectCode(applyChannelInfo.getSubjectCode());
        caseChannelInfoDTO.setSubjectName(applyChannelInfo.getSubjectName());
        return caseChannelInfoDTO;
    }

    /**
     * 案件需要的案件基本信息
     *
     * @param orderInfoByApplyNo    订单信息
     * @param applyCustBaseInfoList 客户基本信息
     * @param finCostDetailsList    融资信息
     * @return
     */
    @Override
    public CaseBaseInfoDto caseBaseInfoDTO(ApplyOrderInfo orderInfoByApplyNo, List<ApplyCustBaseInfo> applyCustBaseInfoList, List<FinCostDetails> finCostDetailsList) {

        CaseBaseInfoDto caseBaseInfoDTO = new CaseBaseInfoDto();
        caseBaseInfoDTO.setApplyNo(orderInfoByApplyNo.getApplyNo());
        caseBaseInfoDTO.setCarType(orderInfoByApplyNo.getCarType());
        caseBaseInfoDTO.setOperateWay(orderInfoByApplyNo.getOperateWay());
        caseBaseInfoDTO.setCustType(orderInfoByApplyNo.getCustType());
        caseBaseInfoDTO.setInputType(orderInfoByApplyNo.getInputType());
        caseBaseInfoDTO.setRentType(orderInfoByApplyNo.getRentType());
        caseBaseInfoDTO.setIfDealerGuarantee(orderInfoByApplyNo.getIfDealerGuarantee());
        caseBaseInfoDTO.setChannelBelong(orderInfoByApplyNo.getChannelBelong());
        caseBaseInfoDTO.setDirectLeasingType(orderInfoByApplyNo.getDirectLeasingType());
        if (orderInfoByApplyNo.getCarNature().equals(CarNature.PLATE_CAR.getIndex())) {
            caseBaseInfoDTO.setBusinessType(AProveBusinessTypeEnum.PLATE.getCode());
        }
        // modify by sijun.yu 2020-11-11 签约成功
        if (WhetherEnum.YES.getIndex().equals(orderInfoByApplyNo.getSignFlag())) {
            caseBaseInfoDTO.setAutomaticTag(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
        } else {
            caseBaseInfoDTO.setAutomaticTag(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO.getCode());
        }
        if (CollectionUtil.isNotEmpty(applyCustBaseInfoList)) {
            //不是修訂暫停的給歷史纍加貸額
            if (!ApplyConstants.APPLY_STATUS_SUSPEND.equals(orderInfoByApplyNo.getApplyStatus())) {
                BigDecimal loanAmount = BigDecimal.ZERO;
                BigDecimal loanAmountHistory = BigDecimal.ZERO;
                String flag = tSysParamConfigService.getParamValue("aggregate-system", "getLoanhistory", WhetherEnum.No.getIndex());
                if (WhetherEnum.YES.getIndex().equals(flag)) {
                    throw new AfsBaseException("接口已废弃");
                }
                if (Convert.toBigDecimal(loanAmountHistory, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                    if (CollectionUtil.isNotEmpty(finCostDetailsList)) {
                        for (FinCostDetails finCostDetails : finCostDetailsList) {
                            loanAmount = loanAmount.add(finCostDetails.getLoanAmt());
                        }
                    }
                }
                //累加金额 = 历史金额+本案件金额
                caseBaseInfoDTO.setTotalLoanAmt(loanAmount);
            }
        }
        caseBaseInfoDTO.setCarNature(orderInfoByApplyNo.getCarNature());
        caseBaseInfoDTO.setBusinessType(orderInfoByApplyNo.getBusinessType());
        caseBaseInfoDTO.setAffiliatedWay(orderInfoByApplyNo.getAffiliatedWay());
        caseBaseInfoDTO.setCarPurpose(orderInfoByApplyNo.getCarPurpose());
        if (StringUtil.isNotEmpty(orderInfoByApplyNo.getProductId())) {
            caseBaseInfoDTO.setProductId(Long.valueOf(orderInfoByApplyNo.getProductId()));
        }
        caseBaseInfoDTO.setProductName(orderInfoByApplyNo.getProductName());
        caseBaseInfoDTO.setRemarks(orderInfoByApplyNo.getRemarks());
        caseBaseInfoDTO.setIsPreApprove(orderInfoByApplyNo.getIsPreApprove());
        caseBaseInfoDTO.setOrderId(orderInfoByApplyNo.getOrderId());
        caseBaseInfoDTO.setDeptId(orderInfoByApplyNo.getDeptId());
        caseBaseInfoDTO.setOrderType(orderInfoByApplyNo.getOrderType());
        caseBaseInfoDTO.setIsSelfCheck(orderInfoByApplyNo.getIsSelfCheck());
        caseBaseInfoDTO.setApplyJointStatus(orderInfoByApplyNo.getApplyJointStatus());
        /**
         * 是司雇佣司机
         * added by Alex, add time 2022-6-6 09:17:25
         */
        caseBaseInfoDTO.setHasHiredDriver(orderInfoByApplyNo.getHasHiredDriver());
        // 设置提报人账号，如果提报人账号为空则取创建人账号，add time: 2022-6-29 15:43:43
        caseBaseInfoDTO.setCreateBy(orderInfoByApplyNo.getApplyReporter()!=null?orderInfoByApplyNo.getApplyReporter():orderInfoByApplyNo.getCreateBy());
        caseBaseInfoDTO.setBusinessSubclass(orderInfoByApplyNo.getBusinessSubclass());
        caseBaseInfoDTO.setSystemType(orderInfoByApplyNo.getSystemType());
        caseBaseInfoDTO.setRouterOrderId(orderInfoByApplyNo.getRouterOrderId());
        caseBaseInfoDTO.setPostImage(orderInfoByApplyNo.getPostImage());
        //设置金融专员手机号
        caseBaseInfoDTO.setSellerPhone(SecurityUtils.getUser().getUserExtInfo().getString("phone"));
        return caseBaseInfoDTO;
    }

    /**
     * 案件需要的车辆信息
     *
     * @param applyCarDetails
     * @param orderInfoByApplyNo
     * @return
     */
    @Override
    public ArrayList<CaseCarInfoDto> caseCarInfoList(ApplyCarDetails applyCarDetails, ApplyOrderInfo
            orderInfoByApplyNo) {
        ArrayList<CaseCarInfoDto> caseCarInfoList = new ArrayList<>();
        CaseCarInfoDto caseCarInfoDTO = new CaseCarInfoDto();
        //如果存在挂靠方式，就查询挂靠信息
        if (!orderInfoByApplyNo.getAffiliatedWay().equals(AffiliatedWay.NO.getIndex())) {
            //挂靠信息
            ApplyAffiliatedUnit applyAffiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(applyCarDetails.getApplyNo());
            if (ObjectUtil.isNull(applyAffiliatedUnit)) {
                throw new AfsBaseException("车辆挂靠信息不存在");
            }
            caseCarInfoDTO.setIndBusinessUsci(applyAffiliatedUnit.getSocUniCrtCode());
            caseCarInfoDTO.setAffiliatedType(applyAffiliatedUnit.getAffiliatedType());
            caseCarInfoDTO.setAffCompanyId(applyAffiliatedUnit.getAffiliatedId());
            caseCarInfoDTO.setIndBusinessName(applyAffiliatedUnit.getAffiliatedName());
            caseCarInfoDTO.setCooperPlat(applyAffiliatedUnit.getPlatformId());
            caseCarInfoDTO.setIsNominal(applyAffiliatedUnit.getIsNominal());
            caseCarInfoDTO.setLegalPersonName(applyAffiliatedUnit.getLegalName());
            caseCarInfoDTO.setLegalPersonTel(applyAffiliatedUnit.getLegalPhone());
            caseCarInfoDTO.setRegisteredAddress(applyAffiliatedUnit.getRegistAddress());
            caseCarInfoDTO.setAffiliatedProvince(applyAffiliatedUnit.getRegistProvince());
            caseCarInfoDTO.setAffiliatedCity(applyAffiliatedUnit.getRegistCity());
            caseCarInfoDTO.setRegistCounty(applyAffiliatedUnit.getRegistCounty());
            caseCarInfoDTO.setRegistDoors(applyAffiliatedUnit.getRegistDoors());
            caseCarInfoDTO.setRegistStreet(applyAffiliatedUnit.getRegistStreet());
            caseCarInfoDTO.setRegistAddressTemp(applyAffiliatedUnit.getRegistAddressTemp());
            caseCarInfoDTO.setEnterprisePhone(applyAffiliatedUnit.getEnterprisePhone());
            caseCarInfoDTO.setLegalPersonIdcard(applyAffiliatedUnit.getLegalPersonIdcard());
            caseCarInfoDTO.setRegistrationDate(applyAffiliatedUnit.getRegistrationDate());
        }
        caseCarInfoDTO.setId(applyCarDetails.getId());
        caseCarInfoDTO.setApplyNo(applyCarDetails.getApplyNo());
        caseCarInfoDTO.setRequestType(applyCarDetails.getRequestType());
        if (StringUtil.isNotEmpty(applyCarDetails.getFullModelNum())) {
            caseCarInfoDTO.setFullModelNum(applyCarDetails.getFullModelNum());
        }
        if (StringUtil.isNotEmpty(applyCarDetails.getIsMortgage())) {
            caseCarInfoDTO.setIsMortgage(applyCarDetails.getIsMortgage());
        }
        caseCarInfoDTO.setCarVin(applyCarDetails.getCarVin());
        caseCarInfoDTO.setCarManufacturer(applyCarDetails.getCarManufacturer());
        caseCarInfoDTO.setBrandCode(applyCarDetails.getBrandId());
        caseCarInfoDTO.setBrandId(applyCarDetails.getBrandId());
        caseCarInfoDTO.setBrandName(applyCarDetails.getBrandName());
        caseCarInfoDTO.setSeriesGroupId(applyCarDetails.getSeriesGroupId());
        caseCarInfoDTO.setSeriesGroupName(applyCarDetails.getSeriesGroupName());
        caseCarInfoDTO.setSeriesId(applyCarDetails.getSeriesId());
        caseCarInfoDTO.setSeriesName(applyCarDetails.getSeriesName());
        caseCarInfoDTO.setModelId(applyCarDetails.getModelId());
        caseCarInfoDTO.setModelName(applyCarDetails.getModelName());
        caseCarInfoDTO.setIsStopSale(applyCarDetails.getIsStopSale());
        caseCarInfoDTO.setIsLicensePlate(applyCarDetails.getIsLicensePlate());
        caseCarInfoDTO.setLevelType(applyCarDetails.getLevelType());
        caseCarInfoDTO.setReportUrl(applyCarDetails.getReportUrl());
        caseCarInfoDTO.setGuidePrice(applyCarDetails.getGuidePrice());
        caseCarInfoDTO.setSalePrice(applyCarDetails.getSalePrice());
        caseCarInfoDTO.setFuelType(applyCarDetails.getFuelType());
        caseCarInfoDTO.setEngineNo(applyCarDetails.getEngineNo());
        caseCarInfoDTO.setLicensePlate(applyCarDetails.getLicensePlate());
        caseCarInfoDTO.setCarColor(applyCarDetails.getCarColor());
        caseCarInfoDTO.setCarDisplacement(applyCarDetails.getCarDisplacement());
        caseCarInfoDTO.setCarTransmission(applyCarDetails.getCarTransmission());
        caseCarInfoDTO.setCarFunction(applyCarDetails.getCarFunction());
        caseCarInfoDTO.setLicenseProvince(applyCarDetails.getLicenseProvince());
        caseCarInfoDTO.setLicenseCity(applyCarDetails.getLicenseCity());
        caseCarInfoDTO.setPurchaseProvince(applyCarDetails.getPurchaseProvince());
        caseCarInfoDTO.setPurchaseCity(applyCarDetails.getPurchaseCity());
        //特殊业务-死牌-上海限牌过户-add by gjh
        if ("01".equals(applyCarDetails.getSpecialBusiness())) {
            caseCarInfoDTO.setSpecialBusiness(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.YES.getCode());
        } else {
            caseCarInfoDTO.setSpecialBusiness(com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.WhetherEnum.NO.getCode());
        }
        if (applyCarDetails.getTotalPassenger() != null) {
            caseCarInfoDTO.setTotalPassenger(String.valueOf(applyCarDetails.getTotalPassenger()));
        }
        caseCarInfoDTO.setMileage(applyCarDetails.getMileage());
        caseCarInfoDTO.setCarBelongs(applyCarDetails.getCarBelongs());
        caseCarInfoDTO.setBelongsCertNo(applyCarDetails.getBelongsCertNo());
        caseCarInfoDTO.setBelongsAddress(applyCarDetails.getBelongsAddress());
        caseCarInfoDTO.setSecondSalePrice(applyCarDetails.getSecondSalePrice());
        caseCarInfoDTO.setEvaluatingPrice(applyCarDetails.getEvaluatingPrice());
        caseCarInfoDTO.setVehicleMadeDate(applyCarDetails.getVehicleMadeDate());
        caseCarInfoDTO.setFirstLandingDate(applyCarDetails.getFirstLandingDate());
        caseCarInfoDTO.setCarAppraiser(applyCarDetails.getCarAppraiser());
        caseCarInfoDTO.setCarDealerId(applyCarDetails.getDealerId());
        caseCarInfoDTO.setCarDealerName(applyCarDetails.getDealerName());
        caseCarInfoDTO.setCarDealerPhone(applyCarDetails.getDealerPhone());
        caseCarInfoDTO.setCarDealerProvince(applyCarDetails.getDealerProvince());
        caseCarInfoDTO.setCarDealerCity(applyCarDetails.getDealerCity());
        caseCarInfoDTO.setCarDealerCounty(applyCarDetails.getDealerCounty());
        caseCarInfoDTO.setCarDealerTown(applyCarDetails.getDealerTown());
        caseCarInfoDTO.setCarDealerStreet(applyCarDetails.getDealerStreet());
        caseCarInfoDTO.setCarDealerAddress(applyCarDetails.getDealerAddress());
        caseCarInfoDTO.setRegistProvince(applyCarDetails.getRegistProvince());
        caseCarInfoDTO.setRegistCity(applyCarDetails.getRegistCity());
        caseCarInfoDTO.setYearTransNum(applyCarDetails.getYearTransNum());
        caseCarInfoDTO.setTransNums(applyCarDetails.getTransNums());
        caseCarInfoDTO.setInvoiceTime(applyCarDetails.getInvoiceTime());
        caseCarInfoDTO.setIsFirstLicense(applyCarDetails.getIsFirstLicense());
        caseCarInfoDTO.setFirstLicenseProvince(applyCarDetails.getFirstLicenseProvince());
        caseCarInfoDTO.setFirstLicenseCity(applyCarDetails.getFirstLicenseCity());
        caseCarInfoDTO.setIsInternet(applyCarDetails.getIsInternet());
        caseCarInfoDTO.setConfigure(applyCarDetails.getConfigure());
        caseCarInfoDTO.setYearStyle(applyCarDetails.getYearStyle());
        caseCarInfoDTO.setVersion(applyCarDetails.getVersion());
        if (WhetherEnum.YES.getIndex().equals(applyCarDetails.getIsCirTrans())) {
            caseCarInfoDTO.setIsCirTrans(WhetherEnum.YES.getIndex());
        } else if (WhetherEnum.No.getIndex().equals(applyCarDetails.getIsCirTrans())) {
            caseCarInfoDTO.setIsCirTrans(WhetherEnum.NO.getIndex());
        } else {
            caseCarInfoDTO.setIsCirTrans(applyCarDetails.getIsCirTrans());
        }
        caseCarInfoDTO.setCarKind(applyCarDetails.getCarKind());
        caseCarInfoDTO.setCarUse(applyCarDetails.getCarUse());
        caseCarInfoDTO.setCarBodyClass(applyCarDetails.getCarBodyClass());
        caseCarInfoDTO.setFirstLicenseAddress(applyCarDetails.getFirstLicenseAddress());
        caseCarInfoDTO.setIsParallel(applyCarDetails.getIsParallel());
        caseCarInfoDTO.setShiftCarStatus(applyCarDetails.getShiftCarStatus());
        caseCarInfoDTO.setIsLcv(applyCarDetails.getIsLcv());
        caseCarInfoDTO.setCargoLength(applyCarDetails.getCargoLength()); // 货箱长度
        caseCarInfoDTO.setEngineType(applyCarDetails.getEngineType()); // 发动机型号
        caseCarInfoDTO.setMaxEnginePower(applyCarDetails.getMaxEnginePower()); // 最大马力
        caseCarInfoDTO.setMaxPowerKw(applyCarDetails.getMaxPowerKw()); // 最大功率
        caseCarInfoDTO.setDischargeStandard(applyCarDetails.getDischargeStandard()); //排放标准
        caseCarInfoDTO.setLiter(applyCarDetails.getLiter());//排量 单位L
        caseCarInfoDTO.setIsEnergyCar(applyCarDetails.getIsEnergyCar());//是否新能源
        caseCarInfoList.add(caseCarInfoDTO);
        return caseCarInfoList;
    }


    /**
     * 案件需要的融资信息
     *
     * @param applyCostDetailsList
     * @return
     */
    @Override
    public ArrayList<FinCostDetails> caseCostInfoList(List<FinCostDetails> applyCostDetailsList
    ) {
        ArrayList<FinCostDetails> caseCostInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(applyCostDetailsList)) {
            for (FinCostDetails applyCostDetails : applyCostDetailsList) {
                FinCostDetails finCostDetails = new FinCostDetails();
                BeanUtil.copyProperties(applyCostDetails, finCostDetails);
                caseCostInfoList.add(finCostDetails);
            }
        }
        return caseCostInfoList;
    }


    /**
     * 案件需要的融资项目信息
     *
     * @param list
     * @return
     */
    @Override
    public ArrayList<FinFinancingItems> caseFinancingItemList(List<FinFinancingItems> list) {
        ArrayList<FinFinancingItems> caseFinancingItemList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (FinFinancingItems applyFinancingItems : list) {
                FinFinancingItems finFinancingItems = new FinFinancingItems();
                BeanUtil.copyProperties(applyFinancingItems, finFinancingItems);
                caseFinancingItemList.add(finFinancingItems);
            }
        }
        return caseFinancingItemList;
    }

    /**
     * 案件需要的贴息明细
     *
     * @param list
     * @return
     */
    @Override
    public ArrayList<FinDiscountDetails> caseDiscountDetailList(List<FinDiscountDetails> list) {
        ArrayList<FinDiscountDetails> caseDiscountDetailList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (FinDiscountDetails applyDiscountDetails : list) {
                FinDiscountDetails discountDetails = new FinDiscountDetails();
                BeanUtil.copyProperties(applyDiscountDetails, discountDetails);
                caseDiscountDetailList.add(discountDetails);
            }
        }
        return caseDiscountDetailList;
    }

    /**
     * 案件需要的车型详细信息
     *
     * @param applyCarDetails
     * @param vehicleDetail
     * @param tsysVehicleModel
     * @return
     */
    @Override
    public ArrayList<CaseCarStyleDetailDto> caseCarStyleDetailList(ApplyCarDetails applyCarDetails, VehicleStyleVO vehicleDetail, VehicleSeriesVO tsysVehicleModel) {
        ArrayList<CaseCarStyleDetailDto> caseCarStyleDetailList = new ArrayList<>();
        CaseCarStyleDetailDto caseCarStyleDetailDTO = new CaseCarStyleDetailDto();

        if (ObjectUtil.isNotNull(applyCarDetails)) {
            caseCarStyleDetailDTO.setCarId(applyCarDetails.getId());
            caseCarStyleDetailDTO.setIsGreen(applyCarDetails.getIsGreen());
            caseCarStyleDetailDTO.setDisplacement(applyCarDetails.getCarDisplacement());
            caseCarStyleDetailDTO.setFuelType(applyCarDetails.getFuelType());
            if (applyCarDetails.getTotalPassenger() != null) {
                caseCarStyleDetailDTO.setPassengers(String.valueOf(applyCarDetails.getTotalPassenger()));
            }
        }
        if (ObjectUtil.isNotNull(vehicleDetail)) {
            caseCarStyleDetailDTO.setCarLength(vehicleDetail.getCarLength());
            caseCarStyleDetailDTO.setEmissionStandard(vehicleDetail.getBlowOff());
            caseCarStyleDetailDTO.setTotalQuality(vehicleDetail.getWight());
            caseCarStyleDetailDTO.setCarType("");
            caseCarStyleDetailDTO.setCarTypeDetail("");
            caseCarStyleDetailDTO.setCarModelId(vehicleDetail.getModelId());// add by sijun.yu 2020-10-14
            caseCarStyleDetailDTO.setWheelbase(vehicleDetail.getWheelBase());
        }
        if (ObjectUtil.isNotNull(tsysVehicleModel)) {
            caseCarStyleDetailDTO.setType(tsysVehicleModel.getVehicleType()+"");
        }
        caseCarStyleDetailList.add(caseCarStyleDetailDTO);
        return caseCarStyleDetailList;
    }

    /**
     * 影像件
     *
     * @param attachmentFileList
     * @return
     */
    @Override
    public ArrayList<ComAttachmentFileDto> comAttachmentFile(List<ComAttachmentFile> attachmentFileList) {
        ArrayList<ComAttachmentFileDto> comAttachmentFileList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(attachmentFileList)) {
            for (ComAttachmentFile comAttachmentFile : attachmentFileList) {
                ComAttachmentFileDto comAttachmentFileDto = new ComAttachmentFileDto();
                BeanUtil.copyProperties(comAttachmentFile, comAttachmentFileDto);
                comAttachmentFileDto.setFileStatus(FileStatusEnum.STANDARD.getCode());//合格
                comAttachmentFileList.add(comAttachmentFileDto);
            }
        }
        return comAttachmentFileList;
    }

    /**
     * 联合方信息
     * @param channelUniteInfoList
     * @return
     */
    public ArrayList<CaseChannelUniteInfoDto> channelUniteInfoList(List<ChannelUniteInfo> channelUniteInfoList) {
        ArrayList<CaseChannelUniteInfoDto> list = new ArrayList<>();
        log.info("========联合方组装数据=========");
        if (CollectionUtil.isNotEmpty(channelUniteInfoList)) {
            for(ChannelUniteInfo channelUniteInfo :channelUniteInfoList){
                log.info("========联合方组装数据=====ing====");

                CaseChannelUniteInfoDto info = new CaseChannelUniteInfoDto();
                BeanUtil.copyProperties(channelUniteInfo, info);
                list.add(info);
                log.info("========联合方组装数据=====inging====");

            }
        }
        log.info("========联合方组装数据=====end====");
        return list;
    }

    /**
     * 租金调整信息
     *
     * @param rentAdjustDetailsList
     * @return
     */
    @Override
    public ArrayList<FinRentAdjustDetails> applyRentAdjustDetails
    (List<FinRentAdjustDetails> rentAdjustDetailsList) {
        ArrayList<FinRentAdjustDetails> adjustDetailsDtoArrayList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(rentAdjustDetailsList)) {
            for (FinRentAdjustDetails applyRentAdjustDetails : rentAdjustDetailsList) {
                FinRentAdjustDetails rentAdjustDetails = new FinRentAdjustDetails();
                if (ObjectUtil.isNotNull(applyRentAdjustDetails)) {
                    BeanUtil.copyProperties(applyRentAdjustDetails, rentAdjustDetails);
                }
                adjustDetailsDtoArrayList.add(rentAdjustDetails);
            }
        }
        return adjustDetailsDtoArrayList;
    }

    /**
     * MQ加急
     *
     * @param applyNo
     * @return
     * @throws ParseException
     */
    @Override
    public AfsTransEntity<UrgentInfoSubmitInfo> returnEmergencyMessage(String applyNo) throws ParseException {
        AfsTransEntity<UrgentInfoSubmitInfo> afsTransEntity = new AfsTransEntity();
        UrgentInfoSubmitInfo urgentInfoSubmitInfo = new UrgentInfoSubmitInfo();
        CaseUrgentDto caseUrgentDto = new CaseUrgentDto();
        caseUrgentDto.setApplyNo(applyNo);
        JSONObject object = SecurityUtils.getUser().getUserExtInfo().getJSONObject("channelInfo");
        String channelId = object.getString("channelId");
        String channelCode = object.getString("channelCode");
        //剩余加急次数
        ChannelUrgentConfig urgentConfig = this.channelUrgentConfigService.getOne(Wrappers.<ChannelUrgentConfig>query().lambda()
                .eq(StringUtil.isNotEmpty(channelId), ChannelUrgentConfig::getChannelId, Long.valueOf(channelId)));
        if(ObjectUtils.isEmpty(urgentConfig)){
            caseUrgentDto.setResidueUrgent("0");
        }else{
            caseUrgentDto.setResidueUrgent(String.valueOf(urgentConfig.getSurplusNumbers()));
        }
        caseUrgentDto.setChannelId(channelId);
        caseUrgentDto.setChannelCode(channelCode);
        urgentInfoSubmitInfo.setCaseUrgent(caseUrgentDto);
        afsTransEntity.setData(urgentInfoSubmitInfo);
        afsTransEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_TURN_URGENT);
        return afsTransEntity;
    }

    /**
     * 资产变更
     *
     * @param applyNo
     * @return
     * @throws ParseException
     */
    @Override
    public AfsTransEntity<ApproveSpecialSubmitInfo> returnAssertChangeMessage(String applyNo, List<ComAttachmentFile> fileList) throws ParseException {

        // 判断是否可以进行资产变更
        AfsTransEntity<ApproveSpecialSubmitInfo> afsTransEntity = new AfsTransEntity<>();
        ApproveSpecialSubmitInfo approveSpecialSubmitInfo = new ApproveSpecialSubmitInfo();
        ApproveInformDto approveInformDto = new ApproveInformDto();
        approveInformDto.setApplyNo(applyNo);
        ApplyModifyHistory modifyHistory = applyModifyHistoryService.getModifyHistoryByApplyNo(applyNo);
        JSONObject jsonObject = new JSONObject();
        //变更前
        String beforeParameter = modifyHistory.getBeforeParameter();
        //变更后
        String afterParameter = modifyHistory.getAfterParameter();
        Assert.isTrue(StringUtil.isNotEmpty(afterParameter), "请保存资产变更信息之后再提交");
        AssetChangeVo afterVo = JSONObject.parseObject(afterParameter).toJavaObject(AssetChangeVo.class);
        //add by sijun.yu 2020-10-19 价格不一致判断
        String bodyContent = modifyHistory.getBodyContent();
        List<FinCostDetails> finCostDetailsList = JSONObject.parseObject(bodyContent).getJSONArray("finCostDetailsList").toJavaList(FinCostDetails.class);
        Assert.isTrue(CollectionUtil.isNotEmpty(finCostDetailsList), "金融产品不能为空");
        finCostDetailsList.forEach(x -> {
            if (CostType.CARAMT.getIndex().equals(x.getCostType())) {
                Assert.isTrue(x.getContractAmt().compareTo(afterVo.getSalePrice()) == 0, "车辆价格调整之后，请修改金融产品再提交");
            }
        });
        Assert.isTrue(WhetherEnum.No.getIndex().equals(afterVo.getChangeFlag()), "换车之后，请修改金融产品再提交");
        //变更后object
        jsonObject.put("bodyContent", applyModifyHistoryService.getLastBodyContent(bodyContent, applyNo));
        jsonObject.put("afterParameter", afterParameter);
        jsonObject.put("beforeParameter", beforeParameter);
        approveSpecialSubmitInfo.setApplyNo(applyNo);
        approveSpecialSubmitInfo.setChangeDataJson(jsonObject);
        //查询操作表信息
        List<ApplyOprRecord> oprRecordList = applyOprRecordService.list(Wrappers.<ApplyOprRecord>query().lambda().eq(ApplyOprRecord::getApplyNo, applyNo)
                .eq(ApplyOprRecord::getApplyType, OrderOprType.ASSERTCHANGE).orderByDesc(ApplyOprRecord::getCreateTime));
        //存在资产变更操作记录
        if (CollectionUtil.isNotEmpty(oprRecordList)) {
            ApplyOprRecord applyOprRecord = oprRecordList.get(0);
            //未审批结束
            if (applyOprRecord.getApproveDate() == null && StringUtil.isNotEmpty(applyOprRecord.getProcessId())) {
                //查询资产变更的退回的审批记录信息
                List<WorkflowRecordDetails> detailsList = applyRecordDetailsService.list(Wrappers.<WorkflowRecordDetails>query().lambda()
                        .eq(WorkflowRecordDetails::getBusinessNo, String.valueOf(applyOprRecord.getId())).eq(WorkflowRecordDetails::getApproveResult, AProveBusinessTypeEnum.BACK.getCode()).orderByDesc(WorkflowRecordDetails::getCreateTime));
                if (CollectionUtil.isNotEmpty(detailsList)) {
                    //资产变更-修订暂停->修订回复-流程id
                    approveInformDto.setStageId(applyOprRecord.getProcessId());
                    approveSpecialSubmitInfo.setApproveInformDto(approveInformDto);
                    //更新资产变更状态为修订回复
                    applyOprRecord.setApplyStatus(AssetChangeEnums.REVISIONREPLY.getCode());
                    this.applyOprRecordService.updateById(applyOprRecord);
                }
                //资产变更退回留言
                log.info("{}，资产变更-修订暂停->修订回复", applyNo);
            } else {
                approveInformDto.setStageId("");
                approveSpecialSubmitInfo.setApproveInformDto(approveInformDto);
            }
            if (ObjectUtil.isNull(modifyHistory.getVersion())) {
                Integer version = applyModifyHistoryService.getMaxVersion(applyNo);
                if (ObjectUtil.isNull(version)) {
                    modifyHistory.setVersion(ApplyConstants.ONE_INT);
                } else {
                    modifyHistory.setVersion(version + ApplyConstants.ONE_INT);
                }
                modifyHistory.setOprId(applyOprRecord.getId());
                applyModifyHistoryService.updateById(modifyHistory);
            }
        }
        // 影像资料
        if (CollectionUtil.isNotEmpty(fileList)) {
            ArrayList<ComAttachmentFileDto> comAttachmentFileListDto = new ArrayList<ComAttachmentFileDto>();
            fileList.forEach(x -> {
                ComAttachmentFileDto dto = new ComAttachmentFileDto();
                BeanUtil.copyProperties(x, dto);
                dto.setBusiNo(applyNo);
                dto.setBelongNo(applyNo);
                dto.setHistoryVersion(modifyHistory.getVersion().toString());
                comAttachmentFileListDto.add(dto);
            });
            approveSpecialSubmitInfo.setComAttachmentFileListDto(comAttachmentFileListDto);
        }
        afsTransEntity.setData(approveSpecialSubmitInfo);
        afsTransEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_ASSETS_RECONSIDER);
        return afsTransEntity;
    }

    /**
     * 申请复议
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Override
    public AfsTransEntity<ReconsiderationDto> returnSaveReconsiderMessage(CaseSubmitInfoCondition condition) throws ParseException {
        AfsTransEntity<ReconsiderationDto> afsTransEntity = new AfsTransEntity<>();
        ReconsiderationDto reconsiderationDto = new ReconsiderationDto();
        //查询订单信息
        ApplyOrderInfo orderInfoByApplyNo = this.applyOrderInfoService.getOrderInfoByApplyNo(condition.getApplyNo());
        PreApproveInfo preApproveInfo = this.preApproveService.getById(orderInfoByApplyNo.getPreId());
        //查询客户信息
        ApplyCustBaseInfo custBaseInfo = this.applyCustBaseInfoService.getCustBaseInfo(condition.getApplyNo(), ApplyConstants.PRINCIPAL_BORROWER);
        //判断是否有在途，如果有，不允许复议
        TranSitCheckDto tranSitCheckDto = new TranSitCheckDto();
        tranSitCheckDto.setAffiliatedWay((com.ruicar.afs.cloud.enums.apply.AffiliatedWayEnum)AfsEnumUtil.getEnum(orderInfoByApplyNo.getAffiliatedWay(), com.ruicar.afs.cloud.enums.apply.AffiliatedWayEnum.class));
        tranSitCheckDto.setCarType(orderInfoByApplyNo.getCarType());
        tranSitCheckDto.setCertNo(custBaseInfo.getCertNo());
        tranSitCheckDto.setIsSpecialProducts(preApproveInfo.getIsSpecialProducts());
        IResponse<?> transitRes = SpringContextHolder.getBean(OrderMngController.class).transitCheck(tranSitCheckDto);
        if (Objects.equals(transitRes.getCode(),CommonConstants.SUCCESS)) {
            if (Boolean.parseBoolean(transitRes.getData().toString())) {
                throw new AfsBaseException("存在在途订单，无法复议");
            }
        }else {
            throw new AfsBaseException(transitRes.getMsg());
        }
        //查询复议信息，
        ApplyOprRecord oprRecord = applyOprRecordService.getOne(Wrappers.<ApplyOprRecord>query().lambda().eq(ApplyOprRecord::getApplyNo, condition.getApplyNo()));
        //保存复议操作信息
        if(oprRecord!=null&&oprRecord.getApplyStatus()!=null){
            reconsiderationDto.setApplyStatus(oprRecord.getApplyStatus());
            applyOprRecordService.removeById(oprRecord.getId());
        }else {
            reconsiderationDto.setApplyStatus(FormalReviewEnums.REFUSALREVIEW.getCode());
        }
        oprRecord = new ApplyOprRecord();
        oprRecord.setApplyNo(condition.getApplyNo());
        oprRecord.setCertNo(custBaseInfo.getCertNo());
        oprRecord.setCustName(orderInfoByApplyNo.getCustName());
        oprRecord.setApplyStatus(FormalReviewEnums.REFUSALREVIEW.getCode());
        oprRecord.setApplyType(OrderOprType.RECONSIDER);
        oprRecord.setApplyReason(condition.getReconsiderReason());
        oprRecord.setCreateTime(new Date());
        oprRecord.setCreateBy(orderInfoByApplyNo.getApplyReporter());
        applyOprRecordService.save(oprRecord);

        ApplyOprRecord applyOprRecord = new ApplyOprRecord();
        if (ObjectUtil.isNotNull(orderInfoByApplyNo)) {
            //订单不是已拒绝的
            if (!ApplyConstants.APPLY_STATUS_REFUSE.equals(orderInfoByApplyNo.getApplyStatus())) {
                throw new AfsBaseException("编号:" + condition.getApplyNo() + "不是已拒绝订单,不可进行复议操作");
            }

            IResponse caseRecoverFlag = casebaseQueryFeign.queryCaseBaseInfo(condition.getApplyNo());
            if (ResponseUtils.checkIfSuccess(caseRecoverFlag)) {
                throw new AfsBaseException("编号:" + condition.getApplyNo() + "正在捞回,不可进行复议操作");
            }

            //订单操作表数据
            List<ApplyOprRecord> list = this.applyOprRecordService.list(Wrappers.<ApplyOprRecord>query().lambda()
                    .eq(ApplyOprRecord::getApplyNo, condition.getApplyNo()).eq(ApplyOprRecord::getApplyType, OrderOprType.RECONSIDER).orderByDesc(ApplyOprRecord::getCreateTime));
            if (CollectionUtil.isNotEmpty(list) && list.size() == 1) {
                applyOprRecord = list.get(0);
            } else {
                throw new AfsBaseException("编号:" + condition.getApplyNo() + "该订单,不可重复复议操作！");
            }
            // 影像资料
            if (CollectionUtil.isNotEmpty(condition.getContractFile())) {
                ArrayList<ComAttachmentFileDto> comAttachmentFileListDto = new ArrayList<ComAttachmentFileDto>();
                condition.getContractFile().forEach(x -> {
                    ComAttachmentFileDto dto = new ComAttachmentFileDto();
                    BeanUtil.copyProperties(x, dto);
                    comAttachmentFileListDto.add(dto);
                });
                reconsiderationDto.setComAttachmentFileListDto(comAttachmentFileListDto);
            }
            reconsiderationDto.setApplyNo(applyOprRecord.getApplyNo());
            reconsiderationDto.setReconReason(applyOprRecord.getApplyReason());
            afsTransEntity.setData(reconsiderationDto);
            afsTransEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_ASSETS_RECONSIDERATION);
            log.info("再次复议推送成功");
        } else {
            throw new AfsBaseException("编号:" + condition.getApplyNo() + "未查询到匹配订单");
        }
        return afsTransEntity;
    }

    /**
     * 申请复议(路由平台推送)
     *
     * @param condition
     * @return
     * @throws ParseException
     */
    @Override
    public AfsTransEntity<ReconsiderationDto> routerReturnSaveReconsiderMessage(CaseSubmitInfoCondition condition) throws ParseException {
        AfsTransEntity<ReconsiderationDto> afsTransEntity = new AfsTransEntity<>();
        ReconsiderationDto reconsiderationDto = new ReconsiderationDto();
        //查询订单信息
        ApplyOrderInfo orderInfoByApplyNo = this.applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>lambdaQuery()
                .eq(ApplyOrderInfo::getRouterOrderId, condition.getApplyNo()));
        if (ObjectUtil.isNotNull(orderInfoByApplyNo)) {
            //查询客户信息
            ApplyCustBaseInfo custBaseInfo = this.applyCustBaseInfoService.getCustBaseInfo(orderInfoByApplyNo.getApplyNo(), ApplyConstants.PRINCIPAL_BORROWER);
            //查询复议信息，
            ApplyOprRecord oprRecord = applyOprRecordService.getOne(Wrappers.<ApplyOprRecord>query().lambda().eq(ApplyOprRecord::getApplyNo, orderInfoByApplyNo.getApplyNo()));
            //保存复议操作信息
            if(oprRecord!=null&&oprRecord.getApplyStatus()!=null){
                reconsiderationDto.setApplyStatus(oprRecord.getApplyStatus());
                applyOprRecordService.removeById(oprRecord.getId());
            }else {
                reconsiderationDto.setApplyStatus(FormalReviewEnums.REFUSALREVIEW.getCode());
            }
            oprRecord = new ApplyOprRecord();
            oprRecord.setApplyNo(orderInfoByApplyNo.getApplyNo());
            oprRecord.setCertNo(custBaseInfo.getCertNo());
            oprRecord.setCustName(orderInfoByApplyNo.getCustName());
            oprRecord.setApplyStatus(FormalReviewEnums.REFUSALREVIEW.getCode());
            oprRecord.setApplyType(OrderOprType.RECONSIDER);
            oprRecord.setApplyReason(condition.getReconsiderReason());
            oprRecord.setCreateTime(new Date());
            oprRecord.setCreateBy(orderInfoByApplyNo.getApplyReporter());
            applyOprRecordService.save(oprRecord);

            ApplyOprRecord applyOprRecord = new ApplyOprRecord();
            //订单不是已拒绝的
            if (!ApplyConstants.APPLY_STATUS_REFUSE.equals(orderInfoByApplyNo.getApplyStatus())) {
                throw new AfsBaseException("编号:" + orderInfoByApplyNo.getApplyNo() + "不是已拒绝订单,不可进行复议操作");
            }

            IResponse caseRecoverFlag = casebaseQueryFeign.queryCaseBaseInfo(orderInfoByApplyNo.getApplyNo());
            if (ResponseUtils.checkIfSuccess(caseRecoverFlag)) {
                throw new AfsBaseException("编号:" + orderInfoByApplyNo.getApplyNo() + "正在捞回,不可进行复议操作");
            }

            //订单操作表数据
            List<ApplyOprRecord> list = this.applyOprRecordService.list(Wrappers.<ApplyOprRecord>query().lambda()
                    .eq(ApplyOprRecord::getApplyNo, orderInfoByApplyNo.getApplyNo()).eq(ApplyOprRecord::getApplyType, OrderOprType.RECONSIDER).orderByDesc(ApplyOprRecord::getCreateTime));
            if (CollectionUtil.isNotEmpty(list) && list.size() == 1) {
                applyOprRecord = list.get(0);
            } else {
                throw new AfsBaseException("编号:" + orderInfoByApplyNo.getApplyNo() + "该订单,不可重复复议操作！");
            }
            // 影像资料
            if (CollectionUtil.isNotEmpty(condition.getRouterFileList())) {
                //上传附件
                List<ComAttachmentFile> comAttachmentFileList = heavyRouterFtpFileService.saveRouterFiles(condition.getRouterFileList(), orderInfoByApplyNo.getApplyNo());
                ArrayList<ComAttachmentFileDto> comAttachmentFileListDto = new ArrayList<ComAttachmentFileDto>();
                comAttachmentFileList.forEach(x -> {
                    ComAttachmentFileDto dto = new ComAttachmentFileDto();
                    BeanUtil.copyProperties(x, dto);
                    comAttachmentFileListDto.add(dto);
                });
                reconsiderationDto.setComAttachmentFileListDto(comAttachmentFileListDto);
            }
            reconsiderationDto.setApplyNo(applyOprRecord.getApplyNo());
            reconsiderationDto.setReconReason(applyOprRecord.getApplyReason());
            afsTransEntity.setData(reconsiderationDto);
            afsTransEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_ASSETS_RECONSIDERATION);
            log.info("再次复议推送成功");
        } else {
            throw new AfsBaseException("编号:" + condition.getApplyNo() + "未查询到匹配订单");
        }
        return afsTransEntity;
    }


    /**
     * <p>description：</p>
     * 撤回申请组装DTO
     *
     * @throws ParseException
     * <AUTHOR>
     * @date 2020年8月31日
     */
    @Override
    public AfsTransEntity<CallBackApplyDto> returnCallBackMessage(String applyNo) throws ParseException {
        AfsTransEntity<CallBackApplyDto> afsTransEntity = new AfsTransEntity<>();
        ApplyOprRecord applyOprRecord = this.applyOprRecordService.getOne(Wrappers.<ApplyOprRecord>query()
                .lambda().eq(StringUtil.isNotEmpty(applyNo), ApplyOprRecord::getApplyNo, applyNo)
                .eq(ApplyOprRecord::getApplyType, OrderOprType.BACK));
        CallBackApplyDto callBackApplyDto = new CallBackApplyDto();
        callBackApplyDto.setApplyNo(applyNo);
        callBackApplyDto.setApplyReporter(SecurityUtils.getUser().getUserRealName());
        if(ObjectUtils.isNotEmpty(applyOprRecord)) {
            //信审撤回需要留言，放款不需要
            callBackApplyDto.setCallBackReason(applyOprRecord.getApplyReason());
        }
        callBackApplyDto.setTimestamp(new Date());
        ApplyChannelInfo applyChannelInfo = applyChannelInfoService.getOne(Wrappers.<ApplyChannelInfo>query().lambda()
                .eq(ApplyChannelInfo::getApplyNo, applyNo));
        callBackApplyDto.setChannelName(applyChannelInfo.getChannelName());
        afsTransEntity.setData(callBackApplyDto);
        afsTransEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_CALL_BACK);
        return afsTransEntity;
    }


    /**
     * 数据校验
     *
     * @param applyNo 申请编号
     */
    @Override
    public CaseSbmitInfoDto checkData(String applyNo) {

        CaseSbmitInfoDto caseSbmitInfoDto = new CaseSbmitInfoDto();
        ApplyOrderInfo orderInfoByApplyNo = this.applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        caseSbmitInfoDto.setApplyOrderInfo(orderInfoByApplyNo);
        //车辆信息
        ApplyCarDetails applyCarDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>query().lambda().eq(ApplyCarDetails::getApplyNo, applyNo));
        if (ObjectUtil.isNull(applyCarDetails)) {
            throw new AfsBaseException("请先保存资产信息");
        }
        //add by sijun.yu 2021-1-5 判断是否修改过业务选择
        if (ApplyConstants.YES.equals(applyCarDetails.getChangeFlag())) {
            throw new AfsBaseException("请先保存资产信息");
        }
        caseSbmitInfoDto.setApplyCarDetails(applyCarDetails);
        //根据申请编号查询客户基础信息
        List<ApplyCustBaseInfo> applyCustBaseInfoList = this.applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda().eq(ApplyCustBaseInfo::getApplyNo, applyNo));
        if (CollectionUtil.isEmpty(applyCustBaseInfoList)) {
            throw new AfsBaseException("请先保存客户信息");
        }
        caseSbmitInfoDto.setApplyCustBaseInfoList(applyCustBaseInfoList);
        //获取地址信息,根据applyNo
        List<ApplyCustAddressDetails> applyCustAddressDetailsList = this.applyCustAddressService.list(Wrappers.<ApplyCustAddressDetails>query().lambda().eq(ApplyCustAddressDetails::getApplyNo, applyNo));
        if (CollectionUtil.isEmpty(applyCustAddressDetailsList)) {
            throw new AfsBaseException("请先保存地址信息");
        }
        applyCustAddressDetailsList.forEach(applyCustAddressDetails -> {
            Assert.isTrue(StringUtil.isNotEmpty(applyCustAddressDetails.getProvince()), "地址省市不可为空");
            Assert.isTrue(StringUtil.isNotEmpty(applyCustAddressDetails.getCity()), "地址省市不可为空");
        });
        caseSbmitInfoDto.setAddressDetailsList(applyCustAddressDetailsList);
        //获取客户联系人信息list
        List<ApplyCustContacts> applyCustContactsList = applyCustContactsService.list(Wrappers.<ApplyCustContacts>query().lambda().eq(ApplyCustContacts::getApplyNo, applyNo));
        if (CollectionUtil.isEmpty(applyCustContactsList)) {
            throw new AfsBaseException("请先保存客户联系人信息");
        }
        caseSbmitInfoDto.setApplyCustContactsList(applyCustContactsList);
        //融资信息
        List<FinCostDetails> applyCostDetailsList = this.applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda()
                .eq(FinCostDetails::getApplyNo, applyNo));
        if (CollectionUtil.isEmpty(applyCostDetailsList)) {
            throw new AfsBaseException("请先保存金融产品信息");
        }
        //身份验重
        ApplyCustBaseInfo applyCustBaseInfo = applyCustBaseInfoService.getCustBaseInfo(applyNo, ApplyConstants.PRINCIPAL_BORROWER);
        String flag = tSysParamConfigService.getParamValue("electronic", "electronic", ApplyConstants.NO);
        if (ApplyConstants.YES.equals(flag)) {
            ComAttachmentManagementService.UnionCodeAndBusNode mainCreditPaper = ComAttachmentManagementService.UnionCodeAndBusNode.builder().busNode("orderApply").unionCode(AttachmentUniqueCodeEnum.MAIN_LETTER_OF_AUTHORIZATION.getCode()).build();
            ComAttachmentManagementService.UnionCodeAndBusNode borrowCreditPaper = ComAttachmentManagementService.UnionCodeAndBusNode.builder().busNode("orderApply").unionCode(AttachmentUniqueCodeEnum.BORROW_LETTER_OF_AUTHORIZATION.getCode()).build();
            ComAttachmentManagementService.UnionCodeAndBusNode guaranteeCreditPaper = ComAttachmentManagementService.UnionCodeAndBusNode.builder().busNode("orderApply").unionCode(AttachmentUniqueCodeEnum.GUARANTEE_LETTER_OF_AUTHORIZATION.getCode()).build();
            Map<ComAttachmentManagementService.UnionCodeAndBusNode, Long> unionCodeAndBusNodeLongMap = attachmentManagementService.getAttachmentManagementIdByUnionCode(Arrays.asList(mainCreditPaper, borrowCreditPaper, guaranteeCreditPaper));
            List<String> manaIDList = unionCodeAndBusNodeLongMap.values().stream().map(Object::toString).collect(Collectors.toList());
            List<ComAttachmentFile> attachmentFiles = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery().eq(ComAttachmentFile::getBusiNo, applyNo).in(ComAttachmentFile::getAttachmentCode, manaIDList));
            int maxFailNumber = Integer.parseInt(tSysParamConfigService.getParamValue("signFailNumber", "time", "3"));
            //是否进行过电子授权
            if (applyCustBaseInfo != null) {
                if (applyCustBaseInfo.getCertType().equals(CertTypeEnum.IDCARD.getIndex())) {
                    List<ComAttachmentFile> creditList = attachmentFiles.stream()
                            .filter(attachmentFile -> StringUtils.equalsIgnoreCase(attachmentFile.getAttachmentCode(), unionCodeAndBusNodeLongMap.getOrDefault(mainCreditPaper, -1L).toString()))
                            .sorted(Comparator.comparing(ComAttachmentFile::getCreateTime).reversed())
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(creditList)) {
                        if (StringUtil.isEmpty(applyCustBaseInfo.getSignResult())) {
                            throw new AfsBaseException("请先电子授权");
                        } else if (applyCustBaseInfo.getSignResult().equals(ApplyConstants.NO)) {
                            if (applyCustBaseInfo.getSignFailNumber() == null) {
                                throw new AfsBaseException("主借人电子授权未通过，请重试！");
                            } else if (applyCustBaseInfo.getSignFailNumber() < maxFailNumber) {
                                throw new AfsBaseException("主借人电子授权未通过，请重试！");
                            }
                        }
                    }
                }
            } else {
                throw new AfsBaseException("请先保存主借人信息");
            }
        }
        //网约车额度校验
        ApplyAffiliatedUnit applyAffiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(applyNo);
        if (applyAffiliatedUnit != null) {
            //add by panhj 2020-12-20 校验挂靠公司黑名单,只校验除个体工商挂靠之外的
            if (!AffiliatedWayEnum.PERSONAL_AFFILIATED.getCode().equals(applyAffiliatedUnit.getAffiliatedType() == null ? "" : applyAffiliatedUnit.getAffiliatedType()) && StringUtil.isNotEmpty(applyAffiliatedUnit.getSocUniCrtCode())) {

            }
            // add by sijun.yu 2020-12-8 个人公司挂靠控制提交不能超过2单
            else if (AffiliatedWay.PERS_COMP_AFFILIATED.getIndex().equals(applyAffiliatedUnit.getAffiliatedType())) {
                List<ApplyOrderInfo> applyOrderInfos = applyAffiliatedUnitService.countPersCompAffiliated(applyAffiliatedUnit.getSocUniCrtCode(), applyAffiliatedUnit.getApplyNo());
                if (applyOrderInfos.size() > 0) {
                    int count = 0;
                    for (ApplyOrderInfo applyOrderInfo : applyOrderInfos) {
                        ApplyContractInfo applyContractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyOrderInfo.getApplyNo());
                        if (applyContractInfo == null || !ApplyConstants.CONTRACT_STATUS_CLOSE.equals(applyContractInfo.getContractStatus())) {
                            count += 1;
                        }
                    }
                    if (count >= 2) {
                        throw new AfsBaseException("挂靠公司申请数量已达到上限，释放后重新提交");
                    }
                }
            }
        }
        //二手车VIN查重
        if (BusinessTypeEnum.OLD_CAR.getCode().equals(orderInfoByApplyNo.getBusinessType()) || orderInfoByApplyNo.getCarNature().equals(ApplyConstants.BRAND_NEW_CAR)) {

        }
        caseSbmitInfoDto.setFinCostDetailsList(applyCostDetailsList);
        //影像件信息
        List<ComAttachmentFile> comAttachmentFileList = this.comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda().eq(ComAttachmentFile::getBusiNo, applyNo).eq(ComAttachmentFile::getFileSource, "com_attachment_management"));
        caseSbmitInfoDto.setAttachmentFileList(comAttachmentFileList);
        //融资项目信息
        List<FinFinancingItems> finFinancingItemsList = this.applyFinancingItemsService.list(Wrappers.<FinFinancingItems>query().lambda().eq(FinFinancingItems::getApplyNo, applyNo));
        caseSbmitInfoDto.setFinancingItemsList(finFinancingItemsList);
        List<FinDiscountDetails> applyDiscountDetailsList = this.applyDiscountDetailsService.list(Wrappers.<FinDiscountDetails>query().lambda().eq(FinDiscountDetails::getApplyNo, applyNo));
        caseSbmitInfoDto.setDiscountDetailsList(applyDiscountDetailsList);
        //查询车型信息
        VehicleSeriesVO vehicleSeriesVO = vehicleHelper.getSeriesByBranCodeName(applyCarDetails.getBrandId(),applyCarDetails.getModelName());
        caseSbmitInfoDto.setTsysVehicleModel(vehicleSeriesVO);
        //查询款式信息
        String styleId = applyCarDetails.getStyleId();
        if(styleId == null){
            styleId = "";
        }
        String styleName = applyCarDetails.getStyleName();
        if(styleName == null){
            styleName = "";
        }
        VehicleStyleVO styleVO = vehicleHelper.getModelDetailInfo(styleId, applyCarDetails.getModelId(), styleName);
        caseSbmitInfoDto.setTsysVehicleDetail(styleVO);
        //查询渠道信息
        ApplyChannelInfo channelInfoByApplyNo = this.applyChannelInfoService.getChannelInfoByApplyNo(applyNo);
        caseSbmitInfoDto.setApplyChannelInfo(channelInfoByApplyNo);
        //查询租金调整信息
        List<FinRentAdjustDetails> applyRentAdjustDetailsList = this.applyRentAdjustDetailsService.getRentAdjustDetails(applyNo);
        caseSbmitInfoDto.setRentAdjustDetailsList(applyRentAdjustDetailsList);
        //联系人验重
        applyCustBaseInfoService.checkMainCustAndContactsCerNo(applyCustContactsList, applyCustBaseInfo);
        return caseSbmitInfoDto;
    }

    /**
     * 撤销
     *
     * @param condition
     * @return
     */
    public AfsTransEntity<RepealDealDto> applyCancel(ContractCancelCondition condition) {
        AfsTransEntity<RepealDealDto> transEntity = new AfsTransEntity<>();
        //合同取消
        RepealDealDto repealDealDto = new RepealDealDto();
        repealDealDto.setApplyNo(condition.getApplyNo());
        List<WorkflowRecordDetails> recordDetailsList = applyRecordDetailsService.list(Wrappers.<WorkflowRecordDetails>query().lambda().eq(WorkflowRecordDetails::getBusinessNo, condition.getApplyNo()).orderByDesc(WorkflowRecordDetails::getCreateTime));
        if (CollectionUtil.isNotEmpty(recordDetailsList)) {
            for (WorkflowRecordDetails workflowRecordDetails : recordDetailsList) {
                if (AProveBusinessTypeEnum.BACK.getCode().equals(workflowRecordDetails.getApproveResult())) {
                    repealDealDto.setStageId(workflowRecordDetails.getProcessId());
                    break;
                }
            }
        }
        repealDealDto.setBusinessStateIn(ApplyConstants.APPLY_STATUS_REVOKE);
        repealDealDto.setRationale(condition.getRationale());
        repealDealDto.setOprStaff(condition.getUserRealName());
        log.info("*******进件撤销******" + repealDealDto);
        transEntity.setTransCode(MqTransCode.AFS_POS_APPLY_CASE_CTM_APPROVE_CASE_REPEAL_SEND);
        transEntity.setData(repealDealDto);
        //更新订单状态为撤销
        ApplyOrderInfo orderInfoByApplyNo = applyOrderInfoService.getOrderInfoByApplyNo(condition.getApplyNo());
        ApplyContractInfo contractInfo = applyContractInfoService.getContractInfoByAppplyNo(condition.getApplyNo());
        if (ObjectUtil.isNotNull(orderInfoByApplyNo)) {
            orderInfoByApplyNo.setApplyStatus(ApplyConstants.APPLY_STATUS_REVOKE);
            orderInfoByApplyNo.setCancelDate(new Date());
            this.applyOrderInfoService.updateById(orderInfoByApplyNo);
            log.info("申请编号：{},更新订单状态：撤销", condition.getApplyNo());
            if(ObjectUtils.isNotEmpty(contractInfo)){
                contractInfo.setContractStatus(ApplyConstants.CONTRACT_STATUS_CANCEL);
                contractInfo.setCancelDate(new Date());
                this.applyContractInfoService.updateById(contractInfo);
                log.info("放款编号：{},更新订单状态：撤销", condition.getApplyNo());
            }
        } else {
            throw new AfsBaseException("申请编号：" + condition.getApplyNo() + "不存在对应订单信息");
        }
        return transEntity;
    }

    /**
     * 附条件核准到期拒绝，定时任务
     *
     * @param applyNo 申请编号
     */
    @Override
    public void conditionCancelSender(String applyNo) {
        AfsTransEntity<String> afsTransEntity = new AfsTransEntity<>();
        afsTransEntity.setTransCode(MqTransCode.AFS_CASE_CTM_POS_APPLY_CANCEL_SEND);
        afsTransEntity.setData(applyNo);
        caseSubmitInfoSender.conditionCancelSender(afsTransEntity);
    }

    /**
     * 校验订单状态
     *
     * @param applyNo
     */
    @Override
    public void checkApplyStatus(String applyNo) {
        ApplyOrderInfo orderInfoByApplyNo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        //核对订单状态
        applyOrderInfoService.checkApplyOrderStatus(orderInfoByApplyNo);
        //雇佣司机判断
        ApplyCustPersonalDetail applyCustPersonalDetail = applyCustPersonalService.getPersonalDetailForMain(applyNo, ApplyConstants.PRINCIPAL_BORROWER);
        if (ObjectUtil.isNotNull(applyCustPersonalDetail)) {
            //中卡，重卡雇佣司机不可为空
            if (CarType.COMMERCIAL_VEHICLE.getIndex().equals(orderInfoByApplyNo.getCarType())) {
                if (StringUtil.isEmpty(applyCustPersonalDetail.getIsDriver())) {
                    throw new AfsBaseException("是否雇佣司机不可为空");
                }
                //如果雇佣司机选择是1,驾驶证件，司机姓名，司机身份证号码，司机驾驶证档案编号都不可为空
                if (WhetherEnum.YES.getIndex().equals(applyCustPersonalDetail.getIsDriver())) {
                    if (StringUtil.isEmpty(applyCustPersonalDetail.getDrivingType())
                            || StringUtil.isEmpty(applyCustPersonalDetail.getDriverCertNo())
                            || StringUtil.isEmpty(applyCustPersonalDetail.getDriverName())) {
                        throw new AfsBaseException("请先完善雇佣司机信息");
                    }
                }
            }
        }
    }

    /**
     * 校验主借人与共借人，保证人关系
     *
     * @param applyNo
     */
    @Override
    public void checkRelation(String applyNo) {
        //主借人客户信息详情
        ApplyCustPersonalDetail personalDetailForMain = applyCustPersonalService.getPersonalDetailForMain(applyNo, ApplyConstants.PRINCIPAL_BORROWER);
        //订单信息
        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOrderInfoByApplyNo(applyNo);
        Assert.isTrue((applyOrderInfo != null), "该订单不存在，请核实");
        if (ObjectUtil.isNotNull(personalDetailForMain)) {
            //联系人-夫妻
            ApplyCustContacts custContacts = applyCustContactsService.getCustContacts(applyNo, CustomerRelationEnum.MAN_AND_WIFE.getIndex());
            ApplyCustBaseInfo commonBorrower = applyCustBaseInfoService.getCustBaseInfo(applyNo, ApplyConstants.COMMON_BORROWER);
            ApplyCustBaseInfo guarantor = applyCustBaseInfoService.getCustBaseInfo(applyNo, ApplyConstants.GUARANTOR);
            if (ObjectUtil.isNotNull(custContacts)) {
                Assert.isTrue(StringUtil.isNotEmpty(custContacts.getCertNo()), "第一联系人为夫妻，证件号不能为空");
                //主借人已婚
                if (MaritalEnum.MARRIED.getIndex().equals(personalDetailForMain.getMaritalStatus())) {
                    //共借人校验
                    if (ObjectUtil.isNotNull(commonBorrower)) {
                        //证件号相同
                        if (custContacts.getCertNo().equals(commonBorrower.getCertNo())) {
                            //且与主借人是夫妻关系,担保人不可选择夫妻
                            if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(commonBorrower.getCustRelation())) {
                                //担保人也是夫妻关系，则抛错提示
                                if (ObjectUtil.isNotNull(guarantor)) {
                                    if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(guarantor.getCustRelation())) {
                                        throw new AfsBaseException("保证人不可选择夫妻关系");
                                    }
                                }
                            }
                        } else if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(commonBorrower.getCustRelation())) {
                            //担保人不是联系人中得配偶，不可选择夫妻关系
                            throw new AfsBaseException("共借人不是主借人配偶,不可选择夫妻关系");
                        }
                    }
                    //担保人校验
                    if (ObjectUtil.isNotNull(guarantor)) {
                        //证件号相同
                        if (custContacts.getCertNo().equals(guarantor.getCertNo())) {
                            //且与主借人是夫妻关系
                            if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(guarantor.getCustRelation())) {
                                //共借人也是夫妻关系，则抛错提示
                                if (ObjectUtil.isNotNull(commonBorrower)) {
                                    if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(commonBorrower.getCustRelation())) {
                                        throw new AfsBaseException("共借人不可选择夫妻关系");
                                    }
                                }
                            }
                        } else if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(guarantor.getCustRelation())) {
                            //担保人不是联系人中得配偶，不可选择夫妻关系
                            throw new AfsBaseException("保证人不是主借人配偶,不可选择夫妻关系");
                        }
                    }
                }
            } else if (MaritalEnum.UNMARRIED.getIndex().equals(personalDetailForMain.getMaritalStatus())) {
                //主借人未婚,共保与主借人关系，不可以选择夫妻关系,主借人联系人不能有已婚关系
                List<ApplyCustContacts> custContactsList = applyCustContactsService.getCustContactsList(applyNo);
                if (CollectionUtil.isNotEmpty(custContactsList)) {
                    custContactsList.forEach(applyCustContacts -> {
                        if (ObjectUtil.isNotNull(applyCustContacts)) {
                            //如果是夫妻关系，抛错
                            if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(applyCustContacts.getCustRelation())) {
                                throw new AfsBaseException("主借人未婚，不可存在有夫妻关系的联系人");
                            }
                        }
                    });
                }
                if (ObjectUtil.isNotNull(commonBorrower)) {
                    //共借人不可选夫妻关系
                    if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(commonBorrower.getCustRelation())) {
                        throw new AfsBaseException("主借人未婚，不可存在有夫妻关系的共借人");
                    }
                }
                if (ObjectUtil.isNotNull(guarantor)) {
                    //担保人不可选夫妻关系
                    if (CustomerRelationEnum.MAN_AND_WIFE.getIndex().equals(guarantor.getCustRelation())) {
                        throw new AfsBaseException("主借人未婚，不可存在有夫妻关系的保证人");
                    }
                }
            }
        }
    }

    /**
     * @param formalApplyNo  申请编号
     * @param applyOrderInfo 订单表信息
     */
    @Override
    public void oldCarCer(String formalApplyNo, ApplyOrderInfo applyOrderInfo) {
        //如果是认证二手车
        if (CarNatureEnum.CA_OLD_CAR.getCode().equals(applyOrderInfo.getCarNature())) {
            ApplyCarDetails carDetailsByApplyNo = applyCarDetailsService.getCarDetailsByApplyNo(formalApplyNo);
            String vin = carDetailsByApplyNo.getCarVin();
            //二手车认证
            applyCarDetailsService.doSign(vin, formalApplyNo);
        }
    }

    /**
     * 校验贴息数据是否为空
     *
     * @param applyNo
     * @return
     */
    @Override
    public Boolean checkDiscountData(String applyNo) {
        Boolean isTrue = Boolean.TRUE;
        List<FinDiscountDetails> detailsList = applyDiscountDetailsService.list(Wrappers.<FinDiscountDetails>lambdaQuery().eq(FinDiscountDetails::getApplyNo, applyNo));
        if (CollectionUtil.isNotEmpty(detailsList)) {
            for (FinDiscountDetails details : detailsList) {
                if (StrUtil.isBlank(details.getTotalToTotal())) {
                    isTrue = false;
                    break;
                }
            }
        }
        Assert.isTrue(isTrue, "total_to_total字段为空");
        return isTrue;
    }

    /**
     * 通过申请编号进行外部审核校验
     * @param applyCustBaseInfo 入参
     * @return 返回的结果
     */
    public List<String> externalLoanApprove(ApplyCustBaseInfo applyCustBaseInfo){

        String applyNo = applyCustBaseInfo.getApplyNo();
        List<String> list = new ArrayList<>();

        if(StringUtil.isBlank(applyNo)){
            throw new AfsBaseException("执行外部审核校验的时候，入参申请编号不能为空！");
        }

        StringBuilder sb = new StringBuilder();

        // 删除记录
        casebaseQueryFeign.removeResultByApplyNo(applyNo);

        // 生成新的保存数据
        CaseLoanApproveResultCondition caseLoanApproveResult = new CaseLoanApproveResultCondition();
        caseLoanApproveResult.setApplyNo(applyNo);

        // 查询合作商信息
        ApplyChannelInfo info = applyChannelInfoService.getOne(Wrappers.<ApplyChannelInfo>lambdaQuery()
                .select(ApplyChannelInfo::getChannelName, ApplyChannelInfo::getChannelBelong)
                .eq(ApplyChannelInfo::getApplyNo, applyNo));

        log.info("开始执行放款自动审核 - 外部审核，查询apply_channel_info表，其中入参1 = {}, 返回的结果 = {}",applyNo,JSONObject.toJSONString(info));

        // 查询订单信息
        ApplyOrderInfo applyOrderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>lambdaQuery()
                .select(ApplyOrderInfo::getAffiliatedWay,ApplyOrderInfo::getCarType)
                .eq(ApplyOrderInfo::getApplyNo, applyNo));

        // 挂靠方式
        String affiliatedWay = applyOrderInfo.getAffiliatedWay();
        // 车辆类型
        String carType = applyOrderInfo.getCarType();
        boolean flag = false;

        if(AfsEnumUtil.key(AffiliatedWayEnum.PERSONAL_COMPANY_AFFILIATED).equals(affiliatedWay) || AfsEnumUtil.key(CarTypeEnum.TEST_CAR).equals(carType)){

            flag = true;
        }


        // 判断经销商渠道归属，看是【社会经销商】还是【直营经销商】
        //拍卖车不进行检验直接通过
        if(ChannelBelongEnum.SP.getKey().equals(info.getChannelBelong()) || StrUtil.equals(CarType.AUCTION_VEHICLE.getIndex(), carType)){
            // 社会经销商，调用汇联易-发票查验接口校验
            // 1. 发票号查询发票信息不能为空，获取作废标记 = N的数据执行以下校验。
            // 2. 车架号与系统车架号一致。
            // 3. 购方识别号需要与承租人身份证号一致。当为挂靠或试乘试驾时，比对挂靠公司的统一社会信用代码。
            // 4. 开票金额与系统开票金额一致。
            // 5. 开票日期与系统开票日期一致。
            // 6. 发票代码与系统发票代码一致。
            // 7. 发票号码与系统发票号码一致。
            // 8. 发动机号与系统发动机号码一致。
            // 9. 销方名称与系统销方名称一致。

            // 社会店目前没有校验直接通过
            sb.append("外部审核校验通过").append("\r\n");
            caseLoanApproveResult.setApproveResult(sb.toString());
            // 2 - 外部审核通过，但是内部审核和Ocr审核还未进行校验
            casebaseQueryFeign.saveLoanAutoResult(caseLoanApproveResult.getApplyNo(),AfsEnumUtil.key(LoanAutoResultTypeEnum.OTHER),caseLoanApproveResult.getApproveResult());
            return list;

        }else if(ChannelBelongEnum.DIRECT.getKey().equals(info.getChannelBelong())){

            // 查询录入的发票信息
            ApplyCarInvoice applyCarInvoice = applyCarInvoiceService.getOne(Wrappers.<ApplyCarInvoice>lambdaQuery()
                    .select(ApplyCarInvoice::getCarVin, ApplyCarInvoice::getInvoiceDate,ApplyCarInvoice::getEngineNo,ApplyCarInvoice::getInvoiceNo,
                            ApplyCarInvoice::getInvoiceUnit, ApplyCarInvoice::getInvoiceClass,ApplyCarInvoice::getBuyerName,ApplyCarInvoice::getBuyerIdcardNo,
                            ApplyCarInvoice::getSaleName,ApplyCarInvoice::getInvoiceCode,ApplyCarInvoice::getInvoiceAmt)
                    .eq(ApplyCarInvoice::getApplyNo, applyNo));

            // 对比证件名称的数据
            String name = applyCarInvoice.getBuyerName();
            // 对比证件号的数据
            String certNo = applyCarInvoice.getBuyerIdcardNo();

            BydDirectDealerReqData data = new BydDirectDealerReqData();

            if(StringUtils.isBlank(applyCarInvoice.getCarVin())){
                list.add("直营系统校验不通过，没有获取到需要查询的车架号信息！");
                sb.append("直营系统校验不通过，没有获取到需要查询的车架号信息！").append("\r\n");
                caseLoanApproveResult.setApproveResult(sb.toString());
                casebaseQueryFeign.saveLoanAutoResult(caseLoanApproveResult.getApplyNo(),AfsEnumUtil.key(LoanAutoResultTypeEnum.FAILURE),caseLoanApproveResult.getApproveResult());
                return list;
            }

            data.setVin(applyCarInvoice.getCarVin());
            // 直营经销商，调用比亚迪_融资租赁系统_接口清单_V1.0接口校验
            IResponse iResponse = bydDirecrtDealerService.callThirdSystem(new Request<>(null, BydDirectDealerTypeEnum.AUTOREP, data));

            if(ApplyConstants.CODE_FAILURE.equals(iResponse.getCode())){
                // 表示获取数据失败
                log.warn("直营系统校验不通过，通过车架号查询对应的数据信息失败!");
                list.add("直营系统校验不通过，通过车架号查询对应的数据信息失败!");
                sb.append("直营系统校验不通过，通过车架号查询对应的数据信息失败!").append("\r\n");
                caseLoanApproveResult.setApproveResult(sb.toString());
                casebaseQueryFeign.saveLoanAutoResult(caseLoanApproveResult.getApplyNo(),AfsEnumUtil.key(LoanAutoResultTypeEnum.FAILURE),caseLoanApproveResult.getApproveResult());
                return list;
            }

            String strData = iResponse.getData().toString();
            JSONObject resultJson = JSONObject.parseObject(strData);
            JSONArray data1 = resultJson.getJSONArray("data");

            JSONArray invoiceList = null;
            for (Object arr : data1){
                JSONObject parse = JSONObject.parseObject(arr.toString());
                if(ObjectUtil.isNotEmpty(parse.getJSONArray("invoiceList"))){
                    invoiceList = parse.getJSONArray("invoiceList");
                }
            }

            if(ObjectUtil.isNotEmpty(invoiceList)){

                // 1. 车架号查询发票信息不能为空，获取发票类型为bill_1：蓝票的未删除发票执行以下校验。
                // 循环处理所有的发票信息
                for(Object invoice : invoiceList){
                    JSONObject invoiceJson = JSONObject.parseObject(String.valueOf(invoice));
                    // 判断是否为有效票，发票需要为未删除的蓝票
                    if(AfsEnumUtil.key(OrderStatusEnum.BILL_1).equals(invoiceJson.getString("orderStatus")) && WhetherEnum.NO.getIndex().equals(invoiceJson.getString("isDelete"))){

                        // 2. 客户名称需要与承租人一致，当为挂靠或试乘试驾时，比对挂靠公司的公司名称。且身份证号需要与承租人一致。当为挂靠或试乘试驾时，比对挂靠公司的统一社会信用代码。
                        // 接口中获取到的客户名称
                        String customerName = invoiceJson.getString("customerName");
                        // 接口中获取到的身份证号
                        String purchaseTaxCode = invoiceJson.getString("purchaseTaxCode");

                        // 客户名称对比
                        if(StringUtils.isBlank(customerName)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的客户名称！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的客户名称！");
                        }else{
                            // 判断是企业
                            if(flag){
                                String tCustomerName = customerName.replaceAll("\\(","（").replaceAll("\\)", "）");
                                String tName = name.replaceAll("\\(","（").replaceAll("\\)", "）");
                                if(!tCustomerName.equals(tName)){
                                    log.warn("直营系统校验不通过，客户名称【{}】与系统录入的挂靠公司名称不一致！",customerName);
                                    list.add("直营系统校验不通过，客户名称【"+customerName+"】与系统录入的挂靠公司名称不一致！");
                                }
                            }else{
                                if(!customerName.equals(name)){
                                    log.warn("直营系统校验不通过，客户名称【{}】与系统录入的承租人姓名不一致！",customerName);
                                    list.add("直营系统校验不通过，客户名称【"+customerName+"】与系统录入的承租人姓名不一致！");
                                }
                            }
                        }

                        // 客户身份证号对比
                        if(StringUtils.isBlank(purchaseTaxCode)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的客户证件号！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的客户证件号！");
                        }else{
                            // 数据库中记录的挂靠公司统一信用代码
                            if(!purchaseTaxCode.equals(certNo)){
                                if(flag){
                                    log.warn("直营系统校验不通过，客户证件号【{}】与系统录入的挂靠公司统一信息代码不一致！",purchaseTaxCode);
                                    list.add("直营系统校验不通过，客户证件号【"+purchaseTaxCode+"】与系统录入的挂靠公司统一信息代码不一致！");
                                }else{
                                    log.warn("直营系统校验不通过，客户证件号【{}】与系统录入的承租人身份证号不一致！",purchaseTaxCode);
                                    list.add("直营系统校验不通过，客户证件号【"+purchaseTaxCode+"】与系统录入的承租人身份证号不一致！");
                                }
                            }
                        }

                        // 4. 开票金额与系统开票金额一致。
                        // 接口中获取到的开票金额
                        BigDecimal actualAmount = invoiceJson.getBigDecimal("actualAmount");
                        if(actualAmount.compareTo(applyCarInvoice.getInvoiceAmt()) != 0){
                            log.warn("直营系统校验不通过，开票金额【{}】与系统录入的开票金额不一致!",actualAmount);
                            list.add("直营系统校验不通过，开票金额【"+actualAmount+"】与系统录入的开票金额不一致!");
                        }
                        // 5. 开票日期与系统开票日期一致。
                        // 接口中获取到的开票日期
                        String invoiceDate = invoiceJson.getString("invoiceDate");
                        if(StringUtils.isBlank(invoiceDate)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的开票日期！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的开票日期！");
                        }else{
                            Date tInvoiceDate = applyCarInvoice.getInvoiceDate();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            String iDate = sdf.format(tInvoiceDate);
                            if(!invoiceDate.equals(iDate)){
                                log.warn("直营系统校验不通过，开票日期【{}】与系统录入的开票日期不一致！",invoiceDate);
                                list.add("直营系统校验不通过，开票日期【"+invoiceDate+"】与系统录入的开票日期不一致！");
                            }
                        }

                        // 6. 发票代码与系统发票代码一致。
                        // 接口中获取到的的发票代码
                        if(ApplyConstants.PAPER_INVOICE.equals(applyCarInvoice.getInvoiceClass())){
                            String code = invoiceJson.getString("code");
                            if(StringUtils.isBlank(code)){
                                log.warn("直营系统校验不通过，没有获取到直营系统返回的发票代码！");
                                list.add("直营系统校验不通过，没有获取到直营系统返回的发票代码！");
                            }else{
                                if(!code.equals(applyCarInvoice.getInvoiceCode())){
                                    log.warn("直营系统校验不通过，发票代码【{}】与系统录入的发票代码不一致！",code);
                                    list.add("直营系统校验不通过，发票代码【"+code+"】与系统录入的发票代码不一致！");
                                }
                            }
                        }

                        // 7. 发票号码与系统发票号码一致。
                        // 接口中获取到的发票号码
                        String num = invoiceJson.getString("num");
                        if(StringUtils.isBlank(num)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的发票号码！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的发票号码！");
                        }else{
                            if(!num.equals(applyCarInvoice.getInvoiceNo())){
                                log.warn("直营系统校验不通过，发票号码【{}】与系统录入的发票号码不一致!",num);
                                list.add("直营系统校验不通过，发票号码【"+num+"】与系统录入的发票号码不一致!");
                            }
                        }

                        // 8. 发动机号与系统发动机号码一致。
                        // 接口中获取到的发动机号
                        String engineerNum = invoiceJson.getString("engineerNum");
                        if(StringUtils.isBlank(engineerNum)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的发动机号！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的发动机号！");
                        } else {
                            if(!engineerNum.equals(applyCarInvoice.getEngineNo())){
                                log.warn("直营系统校验不通过，发动机号【{}】与系统录入的发动机号不一致!",engineerNum);
                                list.add("直营系统校验不通过，发动机号【"+engineerNum+"】与系统录入的发动机号不一致!");
                            }
                        }

                        // 9. 销方名称与系统销方名称一致。（2023-12-06乔老师要求变更为开票单位）
                        // 接口中获取到的销方名称 -
                        String saleName = invoiceJson.getString("saleName");
                        if(StringUtils.isBlank(saleName)){
                            log.warn("直营系统校验不通过，没有获取到直营系统返回的销方名称！");
                            list.add("直营系统校验不通过，没有获取到直营系统返回的销方名称！");
                        }else{
                            if(!saleName.equals(applyCarInvoice.getInvoiceUnit())){
                                log.warn("申请编号={}，直营系统校验不通过，销方名称【{}】与系统录入的开票单位【{}】不一致!",applyNo,saleName,applyCarInvoice.getInvoiceUnit());
                                list.add("直营系统校验不通过，销方名称【"+saleName+"】与系统录入的开票单位名称不一致!");
                            }
                        }
                    }

                    if(AfsEnumUtil.key(OrderStatusEnum.BILL_2).equals(invoiceJson.getString("orderStatus"))){
                        log.warn("直营系统校验不通过，该发票已作废!");
                        list.add("直营系统校验不通过，该发票已作废!");
                    }

                    if(WhetherEnum.YES.getIndex().equals(invoiceJson.getString("isDelete"))){
                        log.warn("直营系统校验不通过，该发票已删除!");
                        list.add("直营系统校验不通过，该发票已删除!");
                    }

                    if(AfsEnumUtil.key(RedFlushEnum.IS_RED_FLUSH).equals(invoiceJson.getString("isRedFlush"))){
                        log.warn("直营系统校验不通过，该发票为红冲!");
                        list.add("直营系统校验不通过，该发票为红冲!");
                    }

                    if(AfsEnumUtil.key(OrderStatusEnum.BILL_3).equals(invoiceJson.getString("orderStatus"))){
                        log.warn("直营系统校验不通过，该发票为红票!");
                        list.add("直营系统校验不通过，该发票为红票!");
                    }
                }
            }else{
                list.add("外部校验不通过，没有获取到该车架号的发票信息！");
            }

        }else{

            list.add("外部校验不通过，经销商渠道归属匹配失败，目前没有对应的放款自动审核逻辑！");
        }

        if(ObjectUtil.isNotEmpty(list) && list.size() > 0){

            for(String str : list){
                sb.append(str).append("\r\n");
            }

            caseLoanApproveResult.setApproveStatus(AfsEnumUtil.key(LoanAutoResultTypeEnum.FAILURE));
        }else{
            // 外部审核通过，但是内部审核和Ocr审核还未进行校验
            caseLoanApproveResult.setApproveStatus(AfsEnumUtil.key(LoanAutoResultTypeEnum.OTHER));
            sb.append("外部审核校验通过").append("\r\n");
        }
        caseLoanApproveResult.setApproveResult(sb.toString());
        casebaseQueryFeign.saveLoanAutoResult(caseLoanApproveResult.getApplyNo(),caseLoanApproveResult.getApproveStatus(),caseLoanApproveResult.getApproveResult());

        return list;

    }

    /**
     * 进行房产证 Ocr 校验(此页面用不到识别的结果，所以采用异步)
     *
     * @param condition 参数
     */
    @Async("ioAsyncTaskExecutor")
    @Override
    public void residenceOcr(CaseSubmitInfoCondition condition) {
        log.info("进件信息提交，开始进行房产证Ocr识别，识别参数{}",JSONObject.toJSONString(condition));

        CaseApplyResidenceDto caseApplyResidenceDto = new CaseApplyResidenceDto();
        String conditionApplyNo = condition.getApplyNo();
        caseApplyResidenceDto.setApplyNo(conditionApplyNo);
        StringBuilder errorMsg = new StringBuilder();
        try{
            // 删除原来的房产证信息
            caseMainInfoService.deleteApplyResidenceOcr(caseApplyResidenceDto);
            // 房产证 OCR
            List<ComAttachmentManagement> list =
                    attachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().
                            eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.APPLY_RESIDENCE.getCode()));
            // 处理新增的房产证信息
            if (CollectionUtils.isEmpty(list)) {
                log.warn("房产证识别，没有获取到房产证的影像件配置信息！list 没有数据！表 unique_Code='applyResidence'");
                return;
            }
            // 获取房产证附件小类的信息
            ComAttachmentManagement comAttachmentManagement = list.get(0);
            // 获取上传的房产证列表
            List<ComAttachmentFile> fileList = comAttachmentFileService
                    .list(Wrappers.<ComAttachmentFile>lambdaQuery()
                            .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(comAttachmentManagement.getId()))
                            .eq(ComAttachmentFile::getBusiNo, conditionApplyNo));
            if (CollectionUtil.isEmpty(fileList)) {
                log.info("房产证识别，没有获取到房产证的影像件配置信息！fileList 没有数据！Attachment_Code = '{}' ,a.busi_no = '{}'", comAttachmentManagement.getId(), conditionApplyNo);
                return;
            }
            // 要对房产证比对的信息
            ApplyResidenceComparisionCondition applyResidenceComparisionCondition = new ApplyResidenceComparisionCondition();
            // 主借人的基础信息
            List<ApplyCustBaseInfo> applyCustBaseInfoList =
                    applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                            .eq(ApplyCustBaseInfo::getApplyNo, conditionApplyNo)
                            .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER));
            if (CollectionUtil.isEmpty(applyCustBaseInfoList)) {
                log.warn("房产证 Ocr，未查询到主借人信息！applyCustBaseInfoList 没有数据！applyNo = '{}' ,custRole = '{}'", conditionApplyNo, "01");
                return;
            }
            // 根据合同号获取直系亲属名称列表, 最后要判断房产证权利人姓名是否在其中
            List<ApplyCustContacts> applyCustContacts = applyCustContactsService.list(Wrappers.<ApplyCustContacts>query().lambda()
                    .eq(ApplyCustContacts::getApplyNo, conditionApplyNo)
                    .in(ApplyCustContacts::getCustRelation, CustRelationEnums.spouse.getIndex(),CustRelationEnums.children.getIndex(),CustRelationEnums.parent.getIndex()));

            List<String> nameList = applyCustContacts.stream()
                    .map(ApplyCustContacts::getCustName)
                    .collect(Collectors.toList());
            log.info("房产证权利人匹配组:{},{},{}",applyCustBaseInfoList,applyCustContacts,nameList);
            // 主借人基本信息
            ApplyCustBaseInfo applyCustBaseInfo = applyCustBaseInfoList.get(0);
            // 将主借人加入其中 提取权利人姓名，并校验其中是否涵盖客户/配偶名字/所填直系亲属名称之一
            nameList.add(applyCustBaseInfo.getCustName());
            applyResidenceComparisionCondition.setNameList(nameList);

            // 坐落地址需比对客户现居住地址/户籍地址，输出：一致/不一致
            List<ApplyCustAddressDetails> applyCustAddressDetails =
                    applyCustAddressDetailsService.list(Wrappers.<ApplyCustAddressDetails>lambdaQuery()
                            .select(ApplyCustAddressDetails::getDetailAddress)
                    .eq(ApplyCustAddressDetails::getApplyNo, conditionApplyNo)
                    .eq(ApplyCustAddressDetails::getCustId, applyCustBaseInfo.getId())
                    .in(ApplyCustAddressDetails::getAddressType, "1", "2")
            );
            List<String> addressList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(applyCustAddressDetails)) {
                addressList = applyCustAddressDetails.stream()
                        .map(ApplyCustAddressDetails::getDetailAddress)
                        .toList();
                applyResidenceComparisionCondition.setAddressList(addressList);
            }

            log.info("上传的房产证列表 fileList :{}", fileList);
            // 遍历房产证的影像件配置信息进行OCR识别
            for (ComAttachmentFile comAttachmentFile : fileList) {
                byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
                String imageBaseStr = Base64Utils.encodeToString(tData);
                OcrReqData ocrReqData = new OcrReqData(imageBaseStr);

                log.info("调用房产证 OCR 识别接口识别的请求数据为{}!", ocrReqData);
                Response<OcrResData> response = ocrService.callThirdSystem(
                        new Request<>(null, OCRType.PROP_OWNER_CERT_OCR, ocrReqData));
                log.info("申请编号 {},页面识别的结果为 {}", conditionApplyNo, JSONObject.toJSONString(response));
                if (!response.isSuccess()) {
                    log.info("房产证识别，文件 id 为 {} 的房产证图片识别失败！", comAttachmentFile.getId());
                    errorMsg.append(String.format("文件 id 为 %s 的房产证图片识别失败 ! \r\n", comAttachmentFile.getId()));
                    continue;
                }
                OcrResData data = response.getData();
                JSONObject respData = JSONObject.parseObject(JSONObject.toJSONString(data));
                log.info("房产证OCR识别返回的数据-----------{}---------------------", respData);
                JSONArray pageInfo= respData.getJSONArray("respData");
                if (pageInfo.isEmpty()) {
                    log.warn("房产证识别异常,未获取到respData中的数据!");
                    continue;
                }
                for(Object arrayInfo:pageInfo){
                    JSONObject infoJson = (JSONObject) arrayInfo;
                    JSONArray infoResults = infoJson.getJSONArray("Result");
                    for(Object resultInfo:infoResults){
                        JSONObject info = (JSONObject) resultInfo;
                        JSONArray resultList = info.getJSONArray("ResultList");
                        for(Object item:resultList){
                            JSONObject itemInfo = (JSONObject) item;
                            log.info("产证类型：" + itemInfo.get("type"));
                            JSONArray fieldList = itemInfo.getJSONArray("FieldList");
                            if(ObjectUtil.isNotEmpty(fieldList)) {
                                for (Object field : fieldList) {
                                    JSONObject fieldInfo = (JSONObject) field;
                                    Field[] fields = caseApplyResidenceDto.getClass().getDeclaredFields();
                                    for (Field fieldModel : fields) {
                                        ResidenceOcrField ocrField = fieldModel.getAnnotation(ResidenceOcrField.class);
                                        if (ObjectUtil.isNotEmpty(ocrField) && ocrField.value().equals(fieldInfo.get("chn_key"))) {
                                            if (ObjectUtil.isEmpty(fieldInfo.get("value"))) {
                                                continue;
                                            }
                                            fieldModel.setAccessible(true);
                                            try {
                                                fieldModel.set(caseApplyResidenceDto, fieldInfo.get("value"));
                                            } catch (IllegalAccessException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                    log.info("chn_key:" + fieldInfo.get("chn_key") + " value:" + fieldInfo.get("value"));
                                }
                            }
                        }
                    }
                }
            }

            log.info("房产证Ocr识别要进行对比的数据：{}", JSON.toJSONString(applyResidenceComparisionCondition));
            log.info("房产证Ocr识别组装的结果：{}", JSON.toJSONString(caseApplyResidenceDto));
            // 正反面识别完成之后，校验:调用公共方法记录不同字段的识别失败信息
            checkResidenceDtoFieldsBlankFlag(caseApplyResidenceDto, errorMsg);
            // 1、权利人姓名
            if (StrUtil.isNotBlank(caseApplyResidenceDto.getName())) {
                caseApplyResidenceDto.setNameConsistentFlag("0");
                for(String name:nameList){
                    if(caseApplyResidenceDto.getName().contains(name)){
                        caseApplyResidenceDto.setNameConsistentFlag("1");
                    }
                }
                if("0".equals(caseApplyResidenceDto.getNameConsistentFlag())){
                    errorMsg.append("权利人姓名并未涵盖客户/配偶名字/所填直系亲属名称之一！\r\n");
                }
            }
            // 2、坐落地址
            if (StrUtil.isNotBlank(caseApplyResidenceDto.getAddress())) {
                if (!addressList.contains(caseApplyResidenceDto.getAddress())) {
                    errorMsg.append("坐落地址与客户现居住地址/户籍地址不一致！\r\n");
                    caseApplyResidenceDto.setAddressConsistentFlag("0");
                } else {
                    caseApplyResidenceDto.setAddressConsistentFlag("1");
                }
            }
            // 3、不动产登记号与历史存量客户的不动产登记号是否存在一致情况，增加内部信息展示
            // 查询；历史存量客户的不动产登记号 ：这个是指历史识别的所有的不动产登记号做匹配,如果有一致的就输出数字，代表历史总共有几个相同的
            if (StrUtil.isNotBlank(caseApplyResidenceDto.getRegistrationNumber())) {
                Integer registrationNumberCount = caseMainInfoService.countRegistrationNumber(caseApplyResidenceDto);
                caseApplyResidenceDto.setRegistrationNumberConsistentNum(String.valueOf(registrationNumberCount));
                if (registrationNumberCount == 0) {
                    errorMsg.append("不动产登记号与历史存量客户的不动产登记号不存在一致情况！\r\n");
                }
            }
            caseApplyResidenceDto.setRecognizeWarnMsg(errorMsg.toString());

            try{
                if (!caseMainInfoService.residenceOcr(caseApplyResidenceDto)) {
                    log.warn("1房产证识别，房产证信息保存失败！");
                }
            } catch (Exception e) {
                log.warn("房产证识别信息保存过程中出现了异常！{}",e);
            }
        } catch (Exception e) {
            log.warn("房产证识别过程中出现了异常，现在使用默认参数录入！{}", e.getMessage(), e);
            caseApplyResidenceDto = new CaseApplyResidenceDto();
            caseApplyResidenceDto.setApplyNo(conditionApplyNo);
            caseApplyResidenceDto.setRecognizeWarnMsg(String.valueOf(errorMsg));

            // 删除原来的房产证信息
            caseMainInfoService.deleteApplyResidenceOcr(caseApplyResidenceDto);
            caseApplyResidenceDto.setRecognizeWarnMsg("房产证识别过程中出现了异常!");
            try{
                if (!caseMainInfoService.residenceOcr(caseApplyResidenceDto)) {
                    log.warn("2房产证识别，房产证信息保存失败！");
                }
            } catch (Exception e2){
                log.warn("房产证识别信息保存过程中出现了异常！{}", e2.getMessage(), e2);
            }
        }
        log.info("房产证Ocr识别最终的结果：{}", JSON.toJSONString(caseApplyResidenceDto));
    }

    /**
     * 进行机动车登记证书 OCR 校验
     * @Async("execTaskExecutor")
     * @param condition 参数
     */
    @Override
    public void certificateOcr(CaseSubmitInfoCondition condition,boolean flag) {
        log.info("放款申请信息提交，开始进行进行机动车登记证书Ocr识别，识别参数{}", JSONUtil.parse(condition));

        CertificateDto certificateDto = new CertificateDto();
        String applyNo = condition.getApplyNo();
        ApplyContractInfo applyContractInfo = applyContractInfoService.getContractInfoByAppplyNo(applyNo);
        if (ObjectUtil.isEmpty(applyContractInfo)) {
            log.warn("根据申请编号{}未获取到applyContractInfo信息!", applyNo);
            return;
        }
        certificateDto.setApplyNo(applyNo);
        certificateDto.setContractNo(applyContractInfo.getContractNo());
        StringBuilder errorMsg = new StringBuilder();
        try{
            // 删除原来的机动车登记证书信息
            log.info("开始删除原来的机动车登记证书信息,申请编号-{}", applyNo);
            caseMainInfoService.deleteCertificateOcr(certificateDto);

            List<ComAttachmentFile> fileList = new ArrayList<>();
            if(flag){
                // 机动车登记证书 OCR
                List<ComAttachmentManagement> list =
                        attachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().
                                eq(ComAttachmentManagement::getUniqueCode, "certificate"));
                // 处理新增的机动车登记证书信息
                if (CollectionUtils.isEmpty(list)) {
                    log.warn("机动车登记证书识别，没有获取到机动车登记证书的影像件配置信息！list 没有数据！表 unique_Code='MOTOR_VEHICLE_REGISTRATION'");
                    return;
                }
                // 获取机动车登记证书附件小类的信息
                ComAttachmentManagement comAttachmentManagement = list.get(0);
                // 获取上传的机动车登记证书列表, 要通过 ContractNo去查
                fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                                .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(comAttachmentManagement.getId()))
                                .eq(ComAttachmentFile::getFileStatus, StatusEnum.STANDARD.getValue())
                                .eq(ComAttachmentFile::getBusiNo, applyContractInfo.getContractNo()));
                if (CollectionUtil.isEmpty(fileList)) {
                    log.info("机动车登记证书识别，没有获取到机动车登记证书的影像件配置信息！fileList 没有数据！Attachment_Code = '{}' ,a.busi_no = '{}'", comAttachmentManagement.getId(), applyNo);
                    return;
                }
            }else{
                fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getBusiNo, applyContractInfo.getContractNo())
                        .eq(ComAttachmentFile::getAttachmentName, AfsEnumUtil.desc(ApproveEnum.BC_MOTOR_VEHICLE_REGISTRATION_CERTIFICATE))
                        .orderByAsc(ComAttachmentFile::getCreateTime));

                if (CollectionUtil.isEmpty(fileList)) {
                    log.info("机动车登记证书识别，没有获取到机动车登记证书的影像件配置信息！fileList 没有数据！a.busi_no = '{}'", applyNo);
                    return;
                }
            }

            // 要对机动车登记证书比对的信息
            CertificateComparisionCondition certificateComparisionCondition = new CertificateComparisionCondition();

            log.info("上传的机动车登记证书列表 fileList :{}", JSONUtil.parse(fileList));
            // 遍历机动车登记证书的影像件配置信息进行OCR识别
            for (ComAttachmentFile comAttachmentFile : fileList) {
                byte[] tData = FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL);
                String imageBaseStr = Base64Utils.encodeToString(tData);
                OcrReqData ocrReqData = new OcrReqData(imageBaseStr);

                log.info("调用机动车登记证书 OCR 识别接口识别的请求数据为{}!", ocrReqData);
                Response<OcrResData> response = ocrService.callThirdSystem(
                        new Request<>(null, OCRType.VEHICLE_REGCERT_OCR, ocrReqData));
                log.info("申请编号 {},页面识别的结果为 {}", applyNo, JSONObject.toJSONString(response));
                if (!response.isSuccess()) {
                    log.info("机动车登记证书识别，文件 id 为 {} 的机动车登记证书图片识别失败！", comAttachmentFile.getId());
                    errorMsg.append(String.format("文件 id 为 %s 的机动车登记证书图片识别失败 ! \r\n", comAttachmentFile.getId()));
                    continue;
                }
                OcrResData data = response.getData();
                JSONObject respData = JSONObject.parseObject(JSONObject.toJSONString(data));
                log.info("机动车登记证书OCR识别返回的数据{}", respData);
                JSONArray resultListArray = checkAndGetResultListArray(respData);
                if (resultListArray == null || resultListArray.isEmpty()) {
                    continue;
                }
                for (Object o : resultListArray) {
                    // 第一页：
                    //      机动车登记证摘要信息栏：1机动车所有人、2机动车所有人身份证号码、7机动车登记证书编号
                    //      机动车登记证机动车信息栏：3车架号、4发动机号
                    // 第二页：5抵押权人、6抵押权人身份证明号码、7机动车登记证书编号
                    JSONObject resultListObj = (JSONObject) o;
                    // 第一页
                    if ("JDCDJZ_ZYXX".equals(resultListObj.getString("cls_result"))) {
                        // 机动车登记证摘要信息栏
                        JSONArray fieldListArray = resultListObj.getJSONArray("FieldList");
                        for (Object fieldListObj : fieldListArray) {
                            log.info("当前字段jsonfieldObj={}", fieldListObj);
                            JSONObject fieldObj = (JSONObject) fieldListObj;
                            String value = fieldObj.getString("value");
                            if (StrUtil.equals("SFZM", fieldObj.getString("key"))) {
                                // 姓名/身份证类型/身份证号码
                                String[] split = value.split("/");
                                if (split.length > 2) {
                                    String ownName = split[0];
                                    String ownCertNo = split[2];
                                    certificateComparisionCondition.setOwnName(ownName);
                                    certificateComparisionCondition.setOwnCertNo(ownCertNo);
                                }
                                continue;
                            }
                            if (StrUtil.equals("DJZBH", fieldObj.getString("key"))) {
                                // 登记证编号
                                certificateComparisionCondition.setCertificateNumberFirstPage(value);
                            }
                        }
                    } else if ("JDCDJZ_JDCXX".equals(resultListObj.getString("cls_result"))) {
                        // 机动车登记证机动车信息栏
                        JSONArray fieldListArray = resultListObj.getJSONArray("FieldList");
                        for (Object fieldListObj : fieldListArray) {
                            log.info("当前字段jsonfieldObj={}", fieldListObj);
                            JSONObject fieldObj = (JSONObject) fieldListObj;
                            String value = fieldObj.getString("value");
                            if (StrUtil.equals("CJH", fieldObj.getString("key"))) {
                                // 车架号
                                certificateComparisionCondition.setCarVin(value);
                                continue;
                            }
                            if (StrUtil.equals("FDJH", fieldObj.getString("key"))) {
                                // 发动机号
                                certificateComparisionCondition.setEngineNo(value);
                                continue;
                            }
                        }
                    } else if ("JDCDJZ_DJL".equals(resultListObj.getString("cls_result"))) {
                        // 第二页
                        JSONArray fieldListArray = resultListObj.getJSONArray("FieldList");
                        for (Object fieldListObj : fieldListArray) {
                            log.info("当前字段jsonfieldObj={}", fieldListObj);
                            JSONObject fieldObj = (JSONObject) fieldListObj;
                            String value = fieldObj.getString("value");
                            if (StrUtil.equals("DJZSBH", fieldObj.getString("key"))) {
                                // 登记证书编号
                                certificateComparisionCondition.setCertificateNumberRegistrationBar(value);
                                continue;
                            }
                        }
                        JSONArray detailListArray = resultListObj.getJSONArray("DetailList");
                        if (detailListArray == null || detailListArray.isEmpty()) {
                            continue;
                        }
                        log.info("当前字段detailListArray={}", detailListArray);
                        JSONObject detailListObj = (JSONObject)detailListArray.get(0);
                        JSONArray fieldListByDetailArray = detailListObj.getJSONArray("FieldList");
                        if (fieldListByDetailArray != null && !fieldListByDetailArray.isEmpty()) {
                            for (Object fieldListObj : fieldListByDetailArray) {
                                dealXmAndSfzmmc(fieldListObj, certificateComparisionCondition);
                            }
                        } else {
                            JSONArray detailDataArray = detailListObj.getJSONArray("DetailData");
                            JSONObject detailDataObj = (JSONObject) detailDataArray.get(0);
                            JSONArray fieldListByDetailDataArray = detailDataObj.getJSONArray("FieldList");
                            for (Object fieldListObj : fieldListByDetailDataArray) {
                                dealXmAndSfzmmc(fieldListObj, certificateComparisionCondition);
                            }
                        }
                    }
                }
            }

            log.info("机动车登记证书Ocr识别要进行对比的数据：{}", JSON.toJSONString(certificateComparisionCondition));
            // 正反面识别完成之后，校验:调用公共方法记录不同字段的识别失败信息
            checkCertificateDtoFieldsBlankFlag(certificateComparisionCondition, errorMsg);
            ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                    .select(ApplyCustBaseInfo::getCustName, ApplyCustBaseInfo::getCertNo)
                    .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                    .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            ApplyAffiliatedUnit affiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(applyNo);
            AtomicBoolean affiliatedFlag = new AtomicBoolean(false);
            if (ObjectUtil.isNotNull(affiliatedUnit)) {
                // 如果为挂靠单
                affiliatedFlag.set(true);
            }
            certificateDto.setOwnName(certificateComparisionCondition.getOwnName());
            // 1、机动车所有人 apply_cust_base_info custRole=01 custName
            if (StrUtil.isNotBlank(certificateDto.getOwnName())) {
                certificateDto.setOwnNameConsistentFlag("0");
                // 与承租人名称校验(挂靠订单与挂靠公司名称校验)
                if (affiliatedFlag.get()) {
                    // 获取挂靠公司名称与社会统一信用代码
                    if(StrUtil.equals(affiliatedUnit.getAffiliatedName(), certificateDto.getOwnName())){
                        certificateDto.setOwnNameConsistentFlag("1");
                    } else {
                        errorMsg.append("OCR比对，机动车登记证书OCR信息中的机动车所有人与系统录入的挂靠公司名称校验不一致！\r\n");
                    }
                } else {
                    if(StrUtil.equals(custBaseInfo.getCustName(), certificateDto.getOwnName())){
                        certificateDto.setOwnNameConsistentFlag("1");
                    } else {
                        errorMsg.append("OCR比对，机动车登记证书OCR信息中的机动车所有人与系统录入的承租人名称校验不一致！\r\n");
                    }
                }

            }

            certificateDto.setOwnCertNo(certificateComparisionCondition.getOwnCertNo());
            // 2、机动车所有人身份证号码 apply_cust_base_info custRole=01 certNo
            if (StrUtil.isNotBlank(certificateDto.getOwnCertNo())) {
                certificateDto.setOwnCertNoConsistentFlag("0");
                // 与承租人身份证号码校验(挂靠订单与挂靠公司统一信用代码校验)
                if (affiliatedFlag.get()) {
                    if(StrUtil.equals(affiliatedUnit.getSocUniCrtCode(), certificateDto.getOwnCertNo())){
                        certificateDto.setOwnCertNoConsistentFlag("1");
                    } else {
                        errorMsg.append("OCR比对，机动车登记证书OCR信息中的机动车所有人身份证号码与系统录入的挂靠公司统一信用代码校验不一致！\r\n");
                    }
                } else {
                    if (StrUtil.equals(custBaseInfo.getCertNo(), certificateDto.getOwnCertNo())) {
                        certificateDto.setOwnCertNoConsistentFlag("1");
                    } else {
                        errorMsg.append("OCR比对，机动车登记证书OCR信息中的机动车所有人身份证号码和系统录入的承租人身份证号码不一致！\r\n");
                    }
                }
            }

            ApplyCarDetails carDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>lambdaQuery()
                    .select(ApplyCarDetails::getCarVin, ApplyCarDetails::getEngineNo)
                    .eq(ApplyCarDetails::getApplyNo, applyNo));

            certificateDto.setCarVin(certificateComparisionCondition.getCarVin());
            // 3、车架号 apply_car_details carVin
            if (StrUtil.isNotBlank(certificateDto.getCarVin())) {
                // 车架号/车辆识别代码
                certificateDto.setCarVinConsistentFlag("0");
                if (StrUtil.equals(carDetails.getCarVin(), certificateDto.getCarVin())) {
                    certificateDto.setCarVinConsistentFlag("1");
                } else {
                    errorMsg.append("OCR比对，机动车登记证书OCR信息中的车架号和系统录入的车架号不一致！\r\n");
                }
            }

            certificateDto.setEngineNo(certificateComparisionCondition.getEngineNo());
            // 4、发动机号 apply_car_details engineNo
            if (StrUtil.isNotBlank(certificateDto.getEngineNo())) {
                // 发动机号码
                certificateDto.setEngineNoConsistentFlag("0");
                if (StrUtil.equals(carDetails.getEngineNo(), certificateDto.getEngineNo())) {
                    certificateDto.setEngineNoConsistentFlag("1");
                } else {
                    errorMsg.append("OCR比对，机动车登记证书OCR信息中的发动机号和系统录入的发动机号不一致！\r\n");
                }
            }

            certificateDto.setActualMortgagorName(certificateComparisionCondition.getActualMortgagorName());
            // 5、抵押权人
            if (StrUtil.isNotBlank(certificateDto.getActualMortgagorName())) {
                certificateDto.setActualMortgagorNameConsistentFlag("0");
                if (StrUtil.equals(certificateConfig.getActualMortgagorName(), certificateDto.getActualMortgagorName())) {
                    certificateDto.setActualMortgagorNameConsistentFlag("1");
                } else {
                    errorMsg.append("OCR比对，机动车登记证书OCR信息中的抵押权人不一致！\r\n");
                }
            }

            certificateDto.setActualMortgagorCertNo(certificateComparisionCondition.getActualMortgagorCertNo());
            // 6、抵押权人身份证明号码
            if (StrUtil.isNotBlank(certificateDto.getActualMortgagorCertNo())) {
                certificateDto.setActualMortgagorCertNoConsistentFlag("0");
                if (StrUtil.equals(certificateConfig.getActualMortgagorCertNo(), certificateDto.getActualMortgagorCertNo())) {
                    certificateDto.setActualMortgagorCertNoConsistentFlag("1");
                } else {
                    errorMsg.append("OCR比对，机动车登记证书OCR信息中的抵押权人身份证明号码不一致！\r\n");
                }
            }

            certificateDto.setCertificateNumberFirstPage(certificateComparisionCondition.getCertificateNumberFirstPage());
            certificateDto.setCertificateNumberRegistrationBar(certificateComparisionCondition.getCertificateNumberRegistrationBar());
            // 7、机动车登记证书编号(第一页和登记栏(一般在第三页及以后)右上角机动车登记证书编号需一致)
            if (StrUtil.isAllNotBlank(certificateDto.getCertificateNumberFirstPage(), certificateDto.getCertificateNumberRegistrationBar())) {
                certificateDto.setCertificateNumberFirstPageConsistentFlag("0");
                certificateDto.setCertificateNumberRegistrationBarConsistentFlag("0");
                if (StrUtil.equals(certificateDto.getCertificateNumberFirstPage(), certificateDto.getCertificateNumberRegistrationBar())) {
                    certificateDto.setCertificateNumberFirstPageConsistentFlag("1");
                    certificateDto.setCertificateNumberRegistrationBarConsistentFlag("1");
                } else {
                    errorMsg.append("OCR比对，机动车登记证书编号（信息栏和登记栏）不一致！\r\n");
                }
            }

            certificateDto.setRecognizeWarnMsg(errorMsg.toString());

            try{
                if (!caseMainInfoService.certificateOcr(certificateDto)) {
                    log.warn("1机动车登记证书识别，机动车登记证书信息保存失败！");
                }
            } catch (Exception e) {
                log.warn("机动车登记证书识别信息保存过程中出现了异常！{0}" ,e);
            }
        } catch (Exception e) {
            log.warn("机动车登记证书识别过程中出现了异常，现在使用默认参数录入！{}", e.getMessage(), e);
            certificateDto.setRecognizeWarnMsg(String.valueOf(errorMsg));

            // 删除原来的机动车登记证书信息
            caseMainInfoService.deleteCertificateOcr(certificateDto);
            try{
                if (!caseMainInfoService.certificateOcr(certificateDto)) {
                    log.warn("2机动车登记证书识别，机动车登记证书信息保存失败！");
                }
            } catch (Exception e2){
                log.warn("机动车登记证书识别信息保存过程中出现了异常！{}", e2.getMessage(), e2);
            }
        }
        log.info("机动车登记证书Ocr识别最终的结果：{}", JSON.toJSONString(certificateDto));
    }

    /**
     * 检查并获取 ResultListArray
     * @param respData json数据
     * @return 检查并获取 ResultListArray
     */
    private @Nullable JSONArray checkAndGetResultListArray(JSONObject respData) {
        JSONObject respDataObj = getJSONObject(respData, "respData", "机动车登记证书识别异常,未获取到 respDataObj 中的数据!");
        if (respDataObj == null) {
            return null;
        }

        JSONObject dataObj = getJSONObject(respDataObj, "data", "机动车登记证书识别异常,未获取到 dataObj 中的数据!");
        if (dataObj == null) {
            return null;
        }

        JSONArray pageInfoArray = getJSONArray(dataObj, "PageInfo", "机动车登记证书识别异常,未获取到 pageInfoArray 中的数据!");
        if (pageInfoArray == null || pageInfoArray.isEmpty()) {
            return null;
        }

        JSONObject pageInfoObj = getJSONObjectFromArray(pageInfoArray, 0, "机动车登记证书识别异常,未获取到 pageInfoObj 中的数据!");
        if (pageInfoObj == null) {
            return null;
        }

        JSONArray resultArray = getJSONArray(pageInfoObj, "Result", "机动车登记证书识别异常,未获取到 resultArray 中的数据!");
        if (resultArray == null || resultArray.isEmpty()) {
            return null;
        }

        JSONObject resultObj = getJSONObjectFromArray(resultArray, 0, "机动车登记证书识别异常,未获取到 resultObj 中的数据!");
        if (resultObj == null) {
            return null;
        }

        JSONArray resultListArray = getJSONArray(resultObj, "ResultList", "机动车登记证书识别异常,未获取到 resultListArray 中的数据!");
        if (resultListArray == null || resultListArray.isEmpty()) {
            return null;
        }
        return resultListArray;
    }

    /**
     * 处理抵押权人和抵押权人身份证明号码
     * @param fieldListObj fieldListObj
     * @param certificateComparisionCondition certificateComparisionCondition
     */
    private void dealXmAndSfzmmc(Object fieldListObj, CertificateComparisionCondition certificateComparisionCondition) {
        log.info("当前字段jsonfieldObj={}", fieldListObj);
        JSONObject fieldObj = (JSONObject) fieldListObj;
        String value = fieldObj.getString("value");
        if (StrUtil.isBlank(value)) {
            return;
        }
        if (StrUtil.equals("XM", fieldObj.getString("key"))) {
            // 抵押权人
            certificateComparisionCondition.setActualMortgagorName(value);
        }
        if (StrUtil.equals("SFZMMC", fieldObj.getString("key"))) {
            // 抵押权人身份证明号码,先给予默认值
            String actualMortgagorCertNo = value;
            String[] sfzmmcArr = value.split("/");
            if (sfzmmcArr.length > 1) {
                actualMortgagorCertNo = value.split("/")[1];
            }
            certificateComparisionCondition.setActualMortgagorCertNo(actualMortgagorCertNo);
        }
    }

    /**
     * 检查房产证信息字段是否为空，并记录识别失败的信息。
     *
     * @param residenceDto 房产证信息对象
     * @param errorMsg     错误信息
     * @return boolean     返回是否都不为空
     */
    private boolean checkResidenceDtoFieldsBlankFlag(CaseApplyResidenceDto residenceDto, StringBuilder errorMsg) {
        checkNotBlankAndLogInfo(residenceDto.getName(), "姓名信息", errorMsg);
        checkNotBlankAndLogInfo(residenceDto.getAddress(), "坐落地址", errorMsg);
        checkNotBlankAndLogInfo(residenceDto.getRegistrationNumber(), "不动产登记号", errorMsg);
        checkNotBlankAndLogInfo(residenceDto.getNatureRights(), "权利性质", errorMsg);
        return errorMsg.isEmpty();
    }


    /**
     * 读取jsonObj
     * @param obj obj
     * @param key key
     * @param errorMessage errorMessage
     * @return 读取jsonObj
     */
    private JSONObject getJSONObject(JSONObject obj, String key, String errorMessage) {
        if (obj == null || !obj.containsKey(key)) {
            log.info(errorMessage);
            return null;
        }
        return obj.getJSONObject(key);
    }

    /**
     * 读取 JSONArray
     * @param obj obj
     * @param key  key
     * @param errorMessage errorMessage
     * @return 读取 JSONArray
     */
    private JSONArray getJSONArray(JSONObject obj, String key, String errorMessage) {
        if (obj == null || !obj.containsKey(key)) {
            log.info(errorMessage);
            return null;
        }
        return obj.getJSONArray(key);
    }

    /**
     * 从JSONArray根据索引读取jsonObj
     * @param array JSONArray
     * @param index index
     * @param errorMessage errorMessage
     * @return JSONObject
     */
    private JSONObject getJSONObjectFromArray(JSONArray array, int index, String errorMessage) {
        if (array == null || array.isEmpty() || index >= array.size()) {
            log.info(errorMessage);
            return null;
        }
        return (JSONObject) array.get(index);
    }

    /**
     * 检查字符串是否为空，并记录识别失败的信息。
     *
     * @param value     要检查的字符串值
     * @param fieldName 字段名
     * @param errorMsg  错误信息
     */
    private void checkNotBlankAndLogInfo(String value, String fieldName, StringBuilder errorMsg) {
        if (StringUtils.isBlank(value)) {
            String logMessage = "房产证中的" + fieldName + "识别未成功，请更换房产证照片！";
            log.info(logMessage);
            errorMsg.append(logMessage).append("\r\n");
        }
    }

    /**
     * 检查机动车登记证书信息字段是否为空，并记录识别失败的信息。
     *
     * @param certificateComparisionCondition 机动车登记证书信息对象
     * @param errorMsg     错误信息
     */
    private void checkCertificateDtoFieldsBlankFlag(CertificateComparisionCondition certificateComparisionCondition, StringBuilder errorMsg) {
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getOwnName(), "机动车所有人", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getOwnCertNo(), "机动车所有人身份证号码", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getCarVin(), "车架号", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getEngineNo(), "发动机号", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getActualMortgagorName(), "抵押权人", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getActualMortgagorCertNo(), "抵押权人身份证明号码", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getCertificateNumberFirstPage(), "机动车登记证书编号(第一页)", errorMsg);
        checkNotBlankAndLogInfoByCertificate(certificateComparisionCondition.getCertificateNumberRegistrationBar(), "机动车登记证书编号(登记栏)", errorMsg);
    }

    /**
     * 检查字符串是否为空，并记录识别失败的信息。
     *
     * @param value     要检查的字符串值
     * @param fieldName 字段名
     * @param errorMsg  错误信息
     */
    private void checkNotBlankAndLogInfoByCertificate(String value, String fieldName, StringBuilder errorMsg) {
        if (StrUtil.isBlank(value)) {
            String logMessage = "机动车登记证书中的" + fieldName + "识别未成功，请更换机动车登记证书照片！";
            log.info(logMessage);
            errorMsg.append(logMessage).append("\r\n");
        }
    }



    /**
     * 驾驶证Ocr识别逻辑
     * @param condition
     * @return
     */
    @Async
    @Override
    public void driverInfoOcr(CaseSubmitInfoCondition condition) {

        log.info("进件信息提交，开始进行驾驶证Ocr识别，识别参数{}",JSONObject.toJSONString(condition));
        StringBuilder sb = new StringBuilder();
        try{
            // 驾驶证OCR
            List<ComAttachmentManagement> list =
                    attachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));

            CaseDrivingLicenceDto info = new CaseDrivingLicenceDto();
            info.setApplyNo(condition.getApplyNo());
            // 处理新增的驾驶证信息
            if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {

                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery()
                        .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo())
                        .ne(ComAttachmentFile::getFileStatus,"discard"));
                if (ObjectUtil.isNotEmpty(fileList) && fileList.size() > 0) {
                    ApplyComparisonCondition applyComparisonCondition = new ApplyComparisonCondition();
                    List<ApplyCustBaseInfo> infos =
                            applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>query().lambda()
                                    .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo())
                                    .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER));

                    // 查询详情信息表
                    if (ObjectUtil.isNotEmpty(infos) && infos.size() > 0) {
                        List<ApplyCustPersonalDetail> tApplyCustPersonalDetailList = applyCustPersonalService.list(Wrappers.<ApplyCustPersonalDetail>lambdaQuery().eq(ApplyCustPersonalDetail::getApplyNo, condition.getApplyNo())
                                .eq(ApplyCustPersonalDetail::getCustId, infos.get(0).getId()));

                        log.info("对比驾驶证Ocr，查询客户详情，具体详情信息为{}", JSON.toJSONString(tApplyCustPersonalDetailList));

                        if (ObjectUtil.isNotEmpty(tApplyCustPersonalDetailList) && tApplyCustPersonalDetailList.size() > 0) {
                            log.info("进入数据库中重新赋予了相关数据,其中性别为{}，国籍为{}！",tApplyCustPersonalDetailList.get(0).getSex(),tApplyCustPersonalDetailList.get(0).getNationality());
                            applyComparisonCondition.setName(infos.get(0).getCustName());
                            applyComparisonCondition.setSex(tApplyCustPersonalDetailList.get(0).getSex().trim());
                            applyComparisonCondition.setCardCode(infos.get(0).getCertNo());
                            applyComparisonCondition.setNationality(tApplyCustPersonalDetailList.get(0).getNationality().trim());
                            applyComparisonCondition.setDateOfBirth(tApplyCustPersonalDetailList.get(0).getBirthday());

                        } else {
                            log.info("驾驶证Ocr，未查询到主借人的详细信息！");
                        }
                    } else {
                        log.info("驾驶证Ocr，未查询到主借人信息！");
                    }

                    JSONArray recognizeWarnCode = null;
                    JSONArray recognizeWarnMsg = null;
                    int count = 0;
                    for (ComAttachmentFile tFileList : fileList) {
                        count ++;

                        byte[] tData = FileCenterHelper.downLoadFile(tFileList.getFileId(), FileType.ORIGINAL);

                        String imageBaseStr = Base64Utils.encodeToString(tData);
                        OcrReqData ocrReqData = new OcrReqData(imageBaseStr);

                        boolean flag = true;

                        for(int i = 0; i < 2; i++){
                            String cardSide = i == 0 ? "BACK" : "FRONT";
                            ocrReqData.setCardSide(cardSide);
                            Response<OcrResData> response = ocrService.callThirdSystem(new Request<>(null, OCRType.DRIVER_LICENSE_OCR, ocrReqData));
                            log.info("合同号{},{}，进件驾驶证信息{}页面识别的结果为{}", condition.getApplyNo(), cardSide, count, JSONObject.toJSONString(response));
                            if (!response.isSuccess()) {
                                if(flag){
                                    flag = false;
                                    continue;
                                }else{
                                    log.info("驾驶证识别，文件id为"+tFileList.getId()+"的驾驶证图片正反面皆识别失败！");
                                    sb.append("文件id为"+tFileList.getId()+"的驾驶证图片正反面皆识别失败！").append("\r\n");
                                    continue;
                                }
                            }
                            OcrResData data = response.getData();
                            JSONObject respData = JSONObject.parseObject(JSONObject.toJSONString(data));
                            JSONObject dataS = Optional.ofNullable(respData).map(json -> json.getJSONObject("respData")).orElse(new JSONObject());

                            if("FRONT".equals(cardSide)){
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Address"))) {
                                    info.setAddress(dataS.getString("Address"));
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Record"))) {
                                    info.setRecord(dataS.getString("Record"));
                                }

                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("ArchivesCode"))) {
                                    info.setArchivesCode(dataS.getString("ArchivesCode"));
                                    if(12 == info.getArchivesCode().length()){
                                        info.setArchivesCodeIsExc(ApplyConstants.NO);
                                    }else{
                                        info.setArchivesCodeIsExc(ApplyConstants.YES);
                                    }
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("CardCode"))) {
                                    info.setCardCode(dataS.getString("CardCode"));
                                    if (applyComparisonCondition.getCardCode().equals(dataS.getString("CardCode"))) {
                                        info.setCardCodeIsSame("0");
                                    } else {
                                        info.setCardCodeIsSame("1");
                                    }
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Class"))) {
                                    info.setCardClass(dataS.getString("Class"));
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("CumulativeScore"))) {
                                    info.setCumulativeScore(dataS.getString("CumulativeScore"));
                                }
                                try{
                                    if (ObjectUtil.isNotEmpty(dataS.getDate("DateOfBirth"))) {
                                        info.setDateOfBirth(dataS.getDate("DateOfBirth"));
                                        if (applyComparisonCondition.getDateOfBirth().equals(info.getDateOfBirth())) {
                                            info.setDateOfBirthIsSame("0");
                                        } else {
                                            info.setDateOfBirthIsSame("1");
                                        }
                                    }
                                }catch (Exception e){
                                    log.info("驾驶证Ocr出生日期格式转换出现异常!");
                                    sb.append("驾驶证Ocr出生日期格式转换出现异常!").append("\r\n");
                                }
                                try{
                                    if (ObjectUtil.isNotEmpty(dataS.getDate("DateOfFirstIssue"))) {
                                        info.setDateOfFirstIssue(dataS.getDate("DateOfFirstIssue"));
                                    }
                                }catch (Exception e){
                                    log.info("驾驶证Ocr初次领证日期格式转换出现异常!");
                                    sb.append("驾驶证Ocr初次领证日期格式转换出现异常!").append("\r\n");
                                }

                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("IssuingAuthority"))) {
                                    info.setIssuingAuthority(dataS.getString("IssuingAuthority"));
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Name"))) {
                                    info.setName(dataS.getString("Name"));
                                    if (applyComparisonCondition.getName().equals(dataS.getString("Name"))) {
                                        info.setNameIsSame("0");
                                    } else {
                                        info.setNameIsSame("1");
                                    }
                                }
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Nationality"))) {
                                    info.setNationality(dataS.getString("Nationality").trim());
                                    if("中国/CHN".equals(info.getNationality())){
                                        info.setNationality("中国");
                                    }
                                    if ("中国".equals(info.getNationality())) {
                                        info.setNationalityIsSame("0");
                                    } else {
                                        log.info("驾驶证Ocr国籍判断不一致，其中识别的国籍信息为{}，数据库中录入的国籍信息为{}，申请编号为{}，客户姓名为{}",info.getNationality(),applyComparisonCondition.getNationality(),info.getApplyNo(),applyComparisonCondition.getName());
                                        info.setNationalityIsSame("1");
                                    }
                                }
                                if (ObjectUtil.isNotEmpty(dataS.getJSONArray("RecognizeWarnCode"))) {
                                    if (ObjectUtil.isNotNull(recognizeWarnCode)) {
                                        recognizeWarnCode.addAll(dataS.getJSONArray("RecognizeWarnCode"));
                                    } else {
                                        recognizeWarnCode = dataS.getJSONArray("RecognizeWarnCode");
                                    }
                                }
                                if (ObjectUtil.isNotEmpty(dataS.getJSONArray("RecognizeWarnMsg"))) {
                                    if (ObjectUtil.isNotNull(recognizeWarnMsg)) {
                                        recognizeWarnMsg.addAll(dataS.getJSONArray("RecognizeWarnCode"));
                                    } else {
                                        recognizeWarnMsg = dataS.getJSONArray("RecognizeWarnCode");
                                    }
                                }

                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Sex"))) {
                                    String sex = "";
                                    info.setSex(dataS.getString("Sex"));
                                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(applyComparisonCondition.getSex())) {
                                        log.info("其中客户详情信息中的性别信息为{}",applyComparisonCondition.getSex());
                                        sex = applyComparisonCondition.getSex().contains("M") ? "男" : (applyComparisonCondition.getSex().contains("F") ? "女" : "");
                                    }
                                    if (sex.equals(dataS.getString("Sex"))) {
                                        info.setSexIsSame("0");
                                    } else {
                                        log.info("驾驶证Ocr性别判断不一致，其中识别的性别信息为{}，数据库中录入的性别信息为{}，申请编号为{}，客户姓名为{}",info.getSex(),sex,info.getApplyNo(),applyComparisonCondition.getName());
                                        info.setSexIsSame("1");
                                    }
                                }

                                try {
                                    if (ObjectUtil.isNotEmpty(dataS.getDate("StartDate"))) {
                                        info.setStartDate(dataS.getDate("StartDate"));
                                    }
                                }catch (Exception e){
                                    log.info("驾驶证Ocr开始日期格式转换出现异常!");
                                    sb.append("驾驶证Ocr开始日期格式转换出现异常!").append("\r\n");
                                }

                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("State"))) {
                                    info.setState(dataS.getString("State"));
                                }
                                // 因为新版驾驶证的截止日期返回 YYYY-MM-DD，老版驾驶证返回有效期限 X年,所以需要特殊处理
                                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("EndDate"))) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    String endDate = dataS.getString("EndDate").trim();
                                    if (endDate.contains("-") && endDate.length() == 10) {
                                        info.setEndDate(dataS.getDate("EndDate"));
                                        info.setIsLongTerm(WhetherEnum.NO.getIndex());
                                    } else if (endDate.length() == 24) {
                                        endDate = endDate.replaceAll("有效期限", "").replace("年", "-").replace("月", "-").replace("日", "");
                                        try {
                                            String substring = endDate.substring(12);
                                            Date date = sdf.parse(substring);
                                            info.setEndDate(date);
                                            info.setIsLongTerm(WhetherEnum.NO.getIndex());
                                        } catch (ParseException e) {
                                            log.info("驾驶证Ocr截止日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr截止日期类型转换出现异常!").append("\r\n");
                                        }
                                        try {
                                            String substring = endDate.substring(1,11);
                                            Date date = sdf.parse(substring);
                                            info.setStartDate(date);
                                        } catch (ParseException e) {
                                            log.info("驾驶证Ocr开始日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr开始日期类型转换出现异常!").append("\r\n");
                                        }
                                    }else if (endDate.length() == 22) {
                                        try {
                                            String substring = endDate.substring(12);
                                            Date date = sdf.parse(substring);
                                            info.setEndDate(date);
                                            info.setIsLongTerm(WhetherEnum.NO.getIndex());
                                        } catch (Exception e) {
                                            log.info("驾驶证Ocr截止日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr截止日期类型转换出现异常!").append("\r\n");
                                        }
                                        try {
                                            String substring = endDate.substring(1,11);
                                            Date date = sdf.parse(substring);
                                            info.setStartDate(date);
                                        } catch (Exception e) {
                                            log.info("驾驶证Ocr开始日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr开始日期类型转换出现异常!").append("\r\n");
                                        }
                                    }else if(endDate.contains("年") && endDate.length() == 5){
                                        try {
                                            String tYear = endDate.replace("有效期限", "").replace("年", "").trim();
                                            int year = Integer.parseInt(tYear);
                                            // 获取 Calendar 实例
                                            Calendar calendar = Calendar.getInstance();
                                            // 设置为当前日期
                                            calendar.setTime(dataS.getDate("StartDate"));
                                            // 将日期减去一天
                                            calendar.add(Calendar.YEAR, +year);
                                            Date yesterday = calendar.getTime();
                                            info.setEndDate(yesterday);
                                            info.setIsLongTerm(WhetherEnum.NO.getIndex());
                                        } catch (Exception e) {
                                            log.info("驾驶证Ocr截止日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr截止日期类型转换出现异常!").append("\r\n");
                                        }
                                    }else if("长期".equals(endDate)){
                                        try {
                                            info.setEndDate(sdf.parse("9999-12-31"));
                                            info.setIsLongTerm(WhetherEnum.YES.getIndex());
                                        } catch (Exception e) {
                                            log.info("驾驶证Ocr截止日期类型转换出现异常！");
                                            sb.append("驾驶证Ocr截止日期类型转换出现异常!").append("\r\n");
                                        }
                                    }else{
                                        log.info("暂时没有收录该截止日期的格式转换逻辑，截止日期数据为：{}",endDate);
                                    }
                                }else{
                                    log.info("没有获取到Ocr识别的截止日期数据！");
                                }

                                // 获取初次领证期限
                                // 若“有效期开始时间”减“初次领证日期”=0，则输出“6年”
                                // 若“有效期开始时间”减“初次领证日期”=6，则输出“10年”
                                // 若“有效期开始时间”减“初次领证日期”=16，则输出“长期”
                                // 若“有效期开始时间”减“初次领证日期”不等于0或者6或者16，则输出“异常请关注”
                                if(ObjectUtil.isNotEmpty(info.getStartDate()) && ObjectUtil.isNotEmpty(info.getDateOfFirstIssue())){

                                    Calendar c1 = Calendar.getInstance();
                                    c1.setTime(info.getStartDate());
                                    // 开始生效日期年份
                                    int startYear = c1.get(Calendar.YEAR);
                                    int startMonth = c1.get(Calendar.MONTH);
                                    int startDay = c1.get(Calendar.DAY_OF_MONTH);

                                    Calendar c2 = Calendar.getInstance();
                                    c2.setTime(info.getDateOfFirstIssue());
                                    // 初次领证日期年份
                                    int firstIssueYear = c2.get(Calendar.YEAR);
                                    int firstIssueMonth = c2.get(Calendar.MONTH);
                                    int firstIssueDay = c2.get(Calendar.DAY_OF_MONTH);

                                    int resultYear = startYear - firstIssueYear;
                                    int resultMonth = startMonth - firstIssueMonth;
                                    int resultDay = startDay - firstIssueDay;

                                    if(resultYear == 0 && resultMonth == 0 && resultDay == 0){
                                        info.setFirstIssuePeriod(ApplyConstants.DRIVE_STATUS_1);
                                    }else if(resultYear == 6 && resultMonth == 0 && resultDay == 0){
                                        info.setFirstIssuePeriod(ApplyConstants.DRIVE_STATUS_2);
                                    }else if(resultYear == 16 && resultMonth == 0 && resultDay == 0){
                                        info.setFirstIssuePeriod(ApplyConstants.DRIVE_STATUS_3);
                                    }else{
                                        info.setFirstIssuePeriod(ApplyConstants.DRIVE_STATUS_0);
                                    }
                                }else{
                                    info.setFirstIssuePeriod(ApplyConstants.DRIVE_STATUS_0);
                                }

                                // 获取证件有效期限
                                // 若“有效期截止时间”减“有效期开始时间”=6，则输出“6年”
                                // 若“有效期截止时间”减“有效期开始时间”=10，则输出“10年”
                                // 若“有效期截止时间”为长期，则输出“长期”
                                // 若“有效期截止时间”减“有效期开始时间”不等于6或者16或者长期，则输出“异常请关注”
                                if(StringUtil.isNotEmpty(info.getIsLongTerm()) && ApplyConstants.YES.equals(info.getIsLongTerm())){
                                    info.setCertificatePeriod(ApplyConstants.DRIVE_STATUS_3);
                                }else{
                                    if(ObjectUtil.isNotEmpty(info.getStartDate()) && ObjectUtil.isNotEmpty(info.getEndDate())){

                                        Calendar c1 = Calendar.getInstance();
                                        c1.setTime(info.getStartDate());
                                        // 开始生效日期年份
                                        int startYear = c1.get(Calendar.YEAR);
                                        int startMonth = c1.get(Calendar.MONTH);
                                        int startDay = c1.get(Calendar.DAY_OF_MONTH);

                                        Calendar c2 = Calendar.getInstance();
                                        c2.setTime(info.getEndDate());
                                        // 开始截止日期年份
                                        int endYear = c2.get(Calendar.YEAR);
                                        int endMonth = c2.get(Calendar.MONTH);
                                        int endDay = c2.get(Calendar.DAY_OF_MONTH);

                                        int resultYear = endYear - startYear;
                                        int resultMonth = endMonth - startMonth;
                                        int resultDay = endDay - startDay;

                                        if(resultYear == 6 && resultMonth == 0 && resultDay == 0){
                                            info.setCertificatePeriod(ApplyConstants.DRIVE_STATUS_1);
                                        }else if(resultYear == 10 && resultMonth == 0 && resultDay == 0){
                                            info.setCertificatePeriod(ApplyConstants.DRIVE_STATUS_2);
                                        }else{
                                            info.setCertificatePeriod(ApplyConstants.DRIVE_STATUS_0);
                                        }
                                    }else{
                                        info.setCertificatePeriod(ApplyConstants.DRIVE_STATUS_0);
                                    }
                                }

                            }else{
                                // 判断当前识别的是电子版的副页，还是纸质版的副页
                                if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("ArchivesCode"))){

                                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("Record"))) {
                                        info.setRecord(dataS.getString("Record"));
                                    }
                                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(dataS.getString("ArchivesCode"))) {
                                        info.setArchivesCode(dataS.getString("ArchivesCode"));
                                        if(12 == info.getArchivesCode().length()){
                                            info.setArchivesCodeIsExc(ApplyConstants.NO);
                                        }else{
                                            info.setArchivesCodeIsExc(ApplyConstants.YES);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    log.info("驾驶证Ocr识别组装结果：{}",JSON.toJSONString(info));

                    if (ObjectUtil.isNotNull(recognizeWarnCode)) {
                        if (recognizeWarnCode.contains("9102")) {
                            log.info("复印件告警！");
                            sb.append("复印件告警！").append("\r\n");
                        }
                        if (recognizeWarnCode.contains("9103")) {
                            log.info("翻拍件告警！");
                            sb.append("翻拍件告警!").append("\r\n");
                        }
                        if (recognizeWarnCode.contains("9106")) {
                            log.info("ps告警！");
                            sb.append("ps告警!").append("\r\n");
                        }
                    }

                    // 正反面识别完成之后，校验，是否所有需要的数据都被识别出来
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getName())) {
                        log.info("驾驶证识别，驾驶证中的姓名信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的姓名信息识别未成功！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getNameIsSame()) || "1".equals(info.getNameIsSame())) {
                        log.info("驾驶证识别，驾驶证中的姓名信息与承租人信息不一致，请重新上传照片！");
                        sb.append("驾驶证中的姓名信息与承租人信息不一致！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getCardCodeIsSame()) || "1".equals(info.getCardCodeIsSame())) {
                        log.info("驾驶证识别，驾驶证中的证件号信息与承租人信息不一致，请重新上传照片！");
                        sb.append("驾驶证中的证件号信息与承租人信息不一致！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getNationality())) {
                        log.info("驾驶证识别，驾驶证中的国籍信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的国籍信息识别未成功！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getSex())) {
                        log.info("驾驶证识别，驾驶证中的性别信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的性别信息识别未成功！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getCardCode())) {
                        log.info("驾驶证识别，驾驶证中的身份证号码信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的身份证号码信息识别未成功！").append("\r\n");
                    }
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(info.getCardClass())) {
                        log.info("驾驶证识别，驾驶证中的准驾车型信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的准驾车型信息识别未成功！").append("\r\n");
                    }
                    if (ObjectUtil.isEmpty(info.getStartDate())) {
                        log.info("驾驶证识别，驾驶证中的有效期开始时间信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的有效期开始时间信息识别未成功！").append("\r\n");
                    }
                    if (!WhetherEnum.YES.getIndex().equals(info.getIsLongTerm()) && ObjectUtil.isEmpty(info.getEndDate())) {
                        log.info("驾驶证识别，驾驶证中的有效期截止时间信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的有效期截止时间信息识别未成功！").append("\r\n");
                    }
                    if (ObjectUtil.isEmpty(info.getDateOfBirth())) {
                        log.info("驾驶证识别，驾驶证中的出生日期信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的出生日期信息识别未成功！").append("\r\n");
                    }
                    if (ObjectUtil.isEmpty(info.getDateOfFirstIssue())) {
                        log.info("驾驶证识别，驾驶证中的初次领证日期信息识别未成功，请更换驾驶证照片！");
                        sb.append("驾驶证中的初次领证日期信息识别未成功！").append("\r\n");
                    }

                    info.setRecognizeWarnMsg(sb.toString());

                    try{
                        if (!caseMainInfoService.driverLicenseOcr(info)) {
                            log.info("驾驶证识别，驾驶证信息保存失败，请联系管理员！");
                        }
                    }catch (Exception e){
                        log.info("驾驶证识别信息保存过程中出现了异常，请联系管理人员！");
                    }
                }
            }else{
                log.info("驾驶证识别，没有获取到驾驶证的影像件配置信息，请联系管理员！");
            }
        }catch (Exception e){
            log.info("驾驶证识别过程中出现了异常，现在使用默认参数录入！"+e);
            CaseDrivingLicenceDto info = new CaseDrivingLicenceDto();
            info.setApplyNo(condition.getApplyNo());
            info.setRecognizeWarnMsg(sb.toString());
            try{
                if (!caseMainInfoService.driverLicenseOcr(info)) {
                    log.info("驾驶证识别，驾驶证信息保存失败，请联系管理员！");
                }
            }catch (Exception e2){
                log.info("驾驶证识别信息保存过程中出现了异常，请联系管理人员！");
            }
        }

    }

    /**
     * 营业执照Ocr识别逻辑
     * @param condition
     * @return
     */
    @Async
    @Override
    public void businessInfoOcr(CaseSubmitInfoCondition condition,ApplyOrderInfo applyOrderInfo) {
        log.info("进件信息提交，开始进行营业执照Ocr识别，识别参数{}",JSONObject.toJSONString(condition));
        StringBuilder sb = new StringBuilder();

        try{
            //查询营业执照附件
            List<ComAttachmentManagement> list = attachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BUSINESS_LICENSE.getCode()));
            //获取进件提交时挂靠信息
            ApplyAffiliatedUnit affiliatedUnit = applyAffiliatedUnitService.getAffiliatedUnitByApplyNo(condition.getApplyNo());
            //获取工作地址
            ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                    .eq(ApplyCustBaseInfo::getApplyNo, condition.getApplyNo())
                    .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            ApplyCustPersonalDetail personalDetail = applyCustPersonalService.getOne(Wrappers.<ApplyCustPersonalDetail>lambdaQuery()
                    .eq(ApplyCustPersonalDetail::getApplyNo, condition.getApplyNo())
                    .eq(ApplyCustPersonalDetail::getCustId, custBaseInfo.getId()));
            //营业执照识别表数据赋值
            CaseBusinessLicenceDto caseBusinessLicenceDto = new CaseBusinessLicenceDto();
            caseBusinessLicenceDto.setApplyNo(condition.getApplyNo());
            // 删除原来的营业执照信息
            caseMainInfoService.deleteBusinessLicenseOcr(caseBusinessLicenceDto);
            // 处理新增的营业执照信息
            if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
                ComAttachmentFile tFileList = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>lambdaQuery().eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo, condition.getApplyNo())
                        .orderByDesc(ComAttachmentFile::getCreateTime)
                        .last("limit 1"));
                if (ObjectUtil.isNotEmpty(tFileList)) {
                    //营业执照ocr识别
                    byte[] tData = FileCenterHelper.downLoadFile(tFileList.getFileId(), FileType.ORIGINAL);
                    String imageBaseStr = Base64Utils.encodeToString(tData);
                    OcrReqData ocrReqData = new OcrReqData(imageBaseStr);
                    Response<OcrResData> ocrRes = ocrService.callThirdSystem(new Request<>(null, OCRType.BIZ_LICENSE_OCR, ocrReqData));
                    if (ocrRes.isSuccess()){
                        TencentBizLicenseResp tencentBizLicenseResp = new TencentBizLicenseResp();
                        OcrResData data = ocrRes.getData();
                        JSONObject respData = JSONObject.parseObject(JSONObject.toJSONString(data));
                        JSONObject dataS = Optional.ofNullable(respData).map(json -> json.getJSONObject("respData")).orElse(new JSONObject());
                        if (ObjectUtils.isNotEmpty(dataS)) {
                            tencentBizLicenseResp = JSONObject.parseObject(String.valueOf(dataS), TencentBizLicenseResp.class);
                        }
                        if (StringUtils.isNotBlank(tencentBizLicenseResp.getRegNum())){
                            caseBusinessLicenceDto.setSocUniCrtCode(tencentBizLicenseResp.getRegNum());
                            if (tencentBizLicenseResp.getRegNum().equals(affiliatedUnit.getSocUniCrtCode())){
                                caseBusinessLicenceDto.setSocUniCrtCodeIsSame("0");
                            }else {
                                caseBusinessLicenceDto.setSocUniCrtCodeIsSame("1");
                                log.info("营业执照Ocr统一社会信用代码判断不一致，其中识别的信息为{}，数据库中录入的统一社会信用代码为{}，申请编号为{}",tencentBizLicenseResp.getRegNum(),affiliatedUnit.getSocUniCrtCode(),condition.getApplyNo());
                            }
                        }else {
                            log.info("营业执照Ocr统一社会信用代码识别失败!");
                            sb.append("营业执照Ocr统一社会信用代码识别失败!").append("\r\n");
                        }

                        if (StringUtils.isNotBlank(tencentBizLicenseResp.getName())){
                            caseBusinessLicenceDto.setAffiliatedName(tencentBizLicenseResp.getName());
                            if (tencentBizLicenseResp.getName().equals(affiliatedUnit.getAffiliatedName())){
                                caseBusinessLicenceDto.setAffiliatedNameIsSame("0");
                            }else {
                                caseBusinessLicenceDto.setAffiliatedNameIsSame("1");
                                log.info("营业执照Ocr机构名称判断不一致，其中识别的信息为{}，数据库中录入的机构名称为{}，申请编号为{}",tencentBizLicenseResp.getName(),affiliatedUnit.getAffiliatedName(),condition.getApplyNo());
                            }
                            if (tencentBizLicenseResp.getName().equals(personalDetail.getUnitName())){
                                caseBusinessLicenceDto.setWorkNameIsSame("1");
                            }else {
                                caseBusinessLicenceDto.setWorkNameIsSame("0");
                            }
                        }else {
                            log.info("营业执照Oc机构名称识别失败!");
                            sb.append("营业执照Ocr机构名称识别失败!").append("\r\n");
                        }

                        if (StringUtils.isNotBlank(tencentBizLicenseResp.getPerson())){
                            caseBusinessLicenceDto.setLegalName(tencentBizLicenseResp.getPerson());
                            if (tencentBizLicenseResp.getPerson().equals(applyOrderInfo.getCustName())){
                                caseBusinessLicenceDto.setLegalNameIsSame("0");
                            }else {
                                caseBusinessLicenceDto.setLegalNameIsSame("1");
                                log.info("营业执照Ocr法定代表人判断不一致，其中识别的信息为{}，数据库中录入的客户姓名为{}，申请编号为{}",tencentBizLicenseResp.getPerson(),applyOrderInfo.getCustName(),condition.getApplyNo());
                            }
                        }else {
                            log.info("营业执照Ocr法定代表人识别失败!");
                            sb.append("营业执照Ocr法定代表人识别失败!").append("\r\n");
                        }

                        if (StringUtils.isNotBlank(tencentBizLicenseResp.getAddress())){
                            caseBusinessLicenceDto.setAddress(tencentBizLicenseResp.getAddress());
                            IResponse<List<String>> listResponse = configServiceFeign.nameToCodeByAddress(tencentBizLicenseResp.getAddress());
                            List<String> listAddress = listResponse.getData();
                            if (ObjectUtil.isNotNull(listAddress) && listAddress.size() > 4){
                                caseBusinessLicenceDto.setRegistProvince(listAddress.get(0));
                                caseBusinessLicenceDto.setRegistCity(listAddress.get(1));
                                caseBusinessLicenceDto.setRegistCounty(listAddress.get(2));
                                caseBusinessLicenceDto.setRegistStreet(listAddress.get(3));
                                caseBusinessLicenceDto.setRegistAddress(listAddress.get(4));
                                caseBusinessLicenceDto.setRegistDoors(listAddress.get(5));
                                if (Objects.equals(affiliatedUnit.getRegistProvince(),listAddress.get(0)) && Objects.equals(affiliatedUnit.getRegistCity(),listAddress.get(1))
                                        && Objects.equals(listAddress.get(2),affiliatedUnit.getRegistCounty()) && Objects.equals(listAddress.get(5),affiliatedUnit.getRegistDoors())){
                                    caseBusinessLicenceDto.setAddressIsSame("0");
                                }else {
                                    caseBusinessLicenceDto.setAddressIsSame("1");
                                    log.info("营业执照Ocr地址判断不一致，其中识别的信息为{}，数据库中录入的地址为{}，申请编号为{}",tencentBizLicenseResp.getAddress(),affiliatedUnit.getRegistProvince() + "/" +
                                            affiliatedUnit.getRegistCity() + "/" + affiliatedUnit.getRegistCounty() + "/" + affiliatedUnit.getRegistDoors(),condition.getApplyNo());
                                }
                            }else {
                                log.info("营业执照Ocr地址解析失败!");
                                sb.append("营业执照Ocr地址解析失败!").append("\r\n");
                            }
                        }else {
                            log.info("营业执照Ocr地址识别失败!");
                            sb.append("营业执照Ocr地址识别失败!").append("\r\n");
                        }

                        if (ObjectUtil.isNotEmpty(tencentBizLicenseResp.getRegistrationDate())){
                            caseBusinessLicenceDto.setRegistrationDate(tencentBizLicenseResp.getRegistrationDate());
                        }else {
                            log.info("营业执照Ocr注册日期识别失败!");
                            sb.append("营业执照Ocr注册日期识别失败!").append("\r\n");
                        }

                        if (ObjectUtils.isNotEmpty(tencentBizLicenseResp.getSetDate())){
                            try {
                                if (tencentBizLicenseResp.getSetDate().contains("年")){
                                    tencentBizLicenseResp.setSetDate(tencentBizLicenseResp.getSetDate().replace("年","-").replace("月","-").replace("日",""));
                                }
                                caseBusinessLicenceDto.setEstablishmentTime(DateUtil.parse(tencentBizLicenseResp.getSetDate(),"yyyy-MM-dd"));
                                Long months = DateUtil.betweenMonth(caseBusinessLicenceDto.getEstablishmentTime(),new Date(),true);
                                if (months.intValue() >= 6 && months.intValue() < 12){
                                    caseBusinessLicenceDto.setRegistrationDateContrast("02");
                                }else if (months.intValue() >= 12){
                                    caseBusinessLicenceDto.setRegistrationDateContrast("01");
                                }else {
                                    caseBusinessLicenceDto.setRegistrationDateContrast("03");
                                }
                                if (DateUtil.isSameDay(caseBusinessLicenceDto.getEstablishmentTime(),affiliatedUnit.getRegistrationDate())){
                                    caseBusinessLicenceDto.setRegistrationDateIsSame("0");
                                }else {
                                    caseBusinessLicenceDto.setRegistrationDateIsSame("1");
                                }
                            }catch (Exception e){
                                log.info("营业执照Ocr成立时间解析失败!"+e);
                                sb.append("营业执照Ocr成立时间解析失败!").append("\r\n");
                            }
                        }else {
                            log.info("营业执照Ocr成立时间识别失败!");
                            sb.append("营业执照Ocr成立时间识别失败!").append("\r\n");
                        }
                        if (StringUtils.isNotBlank(tencentBizLicenseResp.getPeriod())){
                            try{
                                caseBusinessLicenceDto.setPeriodRel(tencentBizLicenseResp.getPeriod());
                                if(tencentBizLicenseResp.getPeriod().contains("至")){
                                    String time = tencentBizLicenseResp.getPeriod().split("至")[1];
                                    if (time.equals("长期")){
                                        caseBusinessLicenceDto.setPeriod(time);
                                        caseBusinessLicenceDto.setPeriodIsSame("0");
                                        caseBusinessLicenceDto.setPeriodNum("长期");
                                    } else if (time.contains("年")) {
                                        time = time.replace("年","-").replace("月","-").replace("日","");
                                        caseBusinessLicenceDto.setPeriod(time);
                                        Long ms = DateUtil.parse(time,"yyyy-MM-dd").getTime()-System.currentTimeMillis();
                                        Long days = DateUtil.betweenDay(DateUtil.parse(time,"yyyy-MM-dd"),new Date(),true);
                                        caseBusinessLicenceDto.setPeriodNum(days.toString());
                                        if (ms.intValue() > 0){
                                            caseBusinessLicenceDto.setPeriodIsSame("0");
                                        }else {
                                            caseBusinessLicenceDto.setPeriodIsSame("1");
                                        }
                                    } else {
                                        caseBusinessLicenceDto.setPeriod(DateUtil.format(DateUtil.parse(time,"yyyy-MM-dd"),"yyyy-MM-dd"));
                                        Long ms = DateUtil.parse(time,"yyyy-MM-dd").getTime()-System.currentTimeMillis();
                                        Long days = DateUtil.betweenDay(DateUtil.parse(time,"yyyy-MM-dd"),new Date(),true);
                                        caseBusinessLicenceDto.setPeriodNum(days.toString());
                                        if (ms.intValue() > 0){
                                            caseBusinessLicenceDto.setPeriodIsSame("0");
                                        }else {
                                            caseBusinessLicenceDto.setPeriodIsSame("1");
                                        }
                                    }
                                }else{
                                    caseBusinessLicenceDto.setPeriod(tencentBizLicenseResp.getPeriod());
                                    caseBusinessLicenceDto.setPeriodIsSame("0");
                                    caseBusinessLicenceDto.setPeriodNum("长期");
                                }
                            }catch (Exception e){
                                log.info("营业执照Ocr营业期限解析失败!"+e);
                                sb.append("营业执照Ocr营业期限解析失败!").append("\r\n");
                            }
                        }else {
                            //没有返回营业期限字段，默认就是有效的2025-05-27业务需求
                            caseBusinessLicenceDto.setPeriodIsSame("0");
                            log.info("营业执照Ocr营业期限识别失败!");
                            sb.append("营业执照Ocr营业期限识别失败!").append("\r\n");
                        }

                        if (ObjectUtil.isNotNull(tencentBizLicenseResp.getRecognizeWarnCode())){
                            if (tencentBizLicenseResp.getRecognizeWarnCode().contains("9102")){
                                log.info("复印件告警！");
                                sb.append("复印件告警！").append("\r\n");
                            } else if (tencentBizLicenseResp.getRecognizeWarnCode().contains("9103")) {
                                log.info("翻拍件告警！");
                                sb.append("翻拍件告警!").append("\r\n");
                            } else if (tencentBizLicenseResp.getRecognizeWarnCode().contains("9106")) {
                                log.info("ps告警！");
                                sb.append("ps告警!").append("\r\n");
                            }
                        }
                    }else {
                        log.info("营业执照识别，文件id为"+tFileList.getId()+"的营业执照识别失败！");
                        sb.append("文件id为"+tFileList.getId()+"的营业执照识别失败！").append("\r\n");
                    }
                    caseBusinessLicenceDto.setRecognizeWarnMsg(sb.toString());
                    log.info("营业执照Ocr识别组装结果：{}",JSON.toJSONString(caseBusinessLicenceDto));
                    try{
                        if (!caseMainInfoService.businessLicenseOcr(caseBusinessLicenceDto)) {
                            log.info("营业执照识别，营业执照信息保存失败，请联系管理员！");
                        }
                    }catch (Exception e){
                        log.info("营业执照识别信息保存过程中出现了异常，请联系管理人员！");
                    }
                }
            }else{
                log.info("营业执照识别，没有获取到营业执照的影像件配置信息，请联系管理员！");
            }
        }catch (Exception e){
            log.info("营业执照识别过程中出现了异常，现在使用默认参数录入！"+e);
            CaseBusinessLicenceDto info = new CaseBusinessLicenceDto();
            info.setApplyNo(condition.getApplyNo());
            info.setRecognizeWarnMsg(sb.toString());
            // 删除原来的营业执照信息
            caseMainInfoService.deleteBusinessLicenseOcr(info);
            try{
                if (!caseMainInfoService.businessLicenseOcr(info)) {
                    log.info("营业执照识别，营业执照信息保存失败，请联系管理员！");
                }
            }catch (Exception e2){
                log.info("营业执照识别信息保存过程中出现了异常，请联系管理人员！");
            }
        }

    }

    /**
     * 获取承租人征信报告
     * @param approveInfo
     */
    @Async("ioAsyncTaskExecutor")
    @Override
    public void queryLesseeReport(PreApproveInfo approveInfo,AfsTransEntity<ApprovePrevInfoDto> transEntity){
        String queryId = null;
        String filePath = null;
        try {
            log.info("预审批提交，开始获取承租人征信报告，识别参数{}",JSONObject.toJSONString(approveInfo));
            if (ObjectUtil.isNotNull(approveInfo)){
                //判断这月是否已经查过
                ComReportFile comReportFile = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                        .eq(ComReportFile::getBelongNo,approveInfo.getCertNo())
                        .ge(ComReportFile::getCreateTime,DateUtil.format(DateUtil.lastMonth(),"yyyy-MM-dd"))
                        .lt(ComReportFile::getCreateTime,DateUtil.format(DateUtil.tomorrow(),"yyyy-MM-dd"))
                        .last("limit 1"));
                if (ObjectUtil.isNotNull(comReportFile)){
                    transEntity.getData().setCqueryid(comReportFile.getElectronicNo());
                    this.caseSubmitInfoSender.preApproveMessageSender(transEntity);
                    log.info("该{}-{}承租人这月已经查询过",approveInfo.getCertNo(),approveInfo.getCustName());
                } else {
                    //添加征信查询记录
                    ComReportFile comReportFile1 = new ComReportFile();
                    comReportFile1.setAttachmentName(approveInfo.getCustName());
                    comReportFile1.setBelongNo(approveInfo.getCertNo());
                    comReportFile1.setBusiNo(String.valueOf(approveInfo.getId()));
                    comReportFile1.setAttachmentCode(AttachmentUniqueCodeEnum.LESSEE_REPORT_FILE.getCode());
                    comReportFile1.setHistoryVersion("0");
                    comReportFile1.setIsElectronic("1");
                    comReportFileService.save(comReportFile1);
                    //添加调用统计记录
                    addRunCount();
                    ZxReportCondition zxReportCondition = new ZxReportCondition();
                    zxReportCondition.setCertName(approveInfo.getCustName());
                    zxReportCondition.setCertNo(approveInfo.getCertNo());
                    //获取承租人身份证正反面
                    zxReportCondition.setCertFile(getLesseeCert(approveInfo.getId()));
                    //获取承租人征信授权书
                    zxReportCondition.setAuthoFile(getLesseeAutho(approveInfo.getId()));
                    zxReportCondition.setType("1");
                    queryId = zxThirdService.queryPersonReport(zxReportCondition);
                    if (StringUtils.isNotBlank(queryId)){
                        filePath = zxThirdService.getPersonReport(queryId,String.valueOf(approveInfo.getId()),"lessee",zxReportCondition);
                        if (StringUtils.isNotBlank(filePath)){
                            File file = new File(filePath);
                            file.delete();
                        }else {
                            log.info("获取承租人征信报告失败");
                        }
                        transEntity.getData().setCqueryid(queryId);
                    }else {
                        log.info("承租人征信报告申请接口异常");
                    }
                    this.caseSubmitInfoSender.preApproveMessageSender(transEntity);
                }
            }else {
                log.info("承租人信息为空!");
                this.caseSubmitInfoSender.preApproveMessageSender(transEntity);
            }
        }catch (Exception e){
            log.info("获取承租人征信报告出现了异常:{}",e);
            transEntity.getData().setCqueryid(queryId);
            this.caseSubmitInfoSender.preApproveMessageSender(transEntity);
            ComReportFile comReportFile = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                    .eq(ComReportFile::getBelongNo,approveInfo.getCertNo())
                    .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
            if (StrUtil.equals(e.getMessage(),"文件上传失败")){
                comReportFile.setRemake("上传到oss失败，信审端在前置机获取报告查看");
                comReportFile.setFileStatus("1");
                zxThirdService.addSucCount();
            }else {
                comReportFile.setRemake(e.getMessage());
                comReportFile.setFileStatus("2");
                zxThirdService.addFailCount();
            }
            comReportFile.setElectronicNo(queryId);
            comReportFileService.updateById(comReportFile);
        }
    }

    public void addRunCount() {
        //获取当前日期
        String date = DateUtil.today();
        //获取征信调用次数预警阈值
        String num = tSysParamConfigService.getParamValue("applyRisk","creditReportNum",ApplyConstants.YES);
        ApplyZxReportCount applyZxReportCount = applyZxReportCountService.getOne(Wrappers.<ApplyZxReportCount>query().lambda()
                .eq(ApplyZxReportCount::getTriggerDay,date));
        if (ObjectUtil.isNotNull(applyZxReportCount)){
            if (applyZxReportCount.getRunCount() == Integer.parseInt(num)){
                //发送短信
                sendMessage();
            }
            applyZxReportCount.setRunCount(applyZxReportCount.getRunCount() + 1);
            applyZxReportCountService.updateById(applyZxReportCount);
        }else {
            applyZxReportCount = new ApplyZxReportCount();
            applyZxReportCount.setTriggerDay(date);
            applyZxReportCount.setRunCount(1);
            applyZxReportCount.setSucCount(0);
            applyZxReportCount.setFailCount(0);
            applyZxReportCountService.save(applyZxReportCount);
        }
    }

    @Async
    public void sendMessage(){
        //获取拥有监控角色的用户
        IResponse<List<SysUserDTO>> iResponse = adminUserFegin.getUsersByRoleCode("ROLE_REPORT_MONITOR");
        if (iResponse.getData() != null && iResponse.getData().size() > 0){
            for (SysUserDTO sysUserDTO : iResponse.getData()){
                log.info("用户:{},手机号:{}",sysUserDTO.getUserRealName(),sysUserDTO.getPhone());
                messageService.sendSms(sysUserDTO.getPhone(),"今日征信调用次数已超过预警值，请关注!");
            }
        }
    }

    /**
     * 获取征信授权刷脸视频
     * @param approveInfo
     * @param fid
     * @param account
     * @param certNo
     */
    @Override
    public void getFaceVideo(PreApproveInfo approveInfo, String fid, String account, String certNo) {

        log.info("开始获取征信授权刷脸视频！approveInfo:{},fid:{},account:{},certNo:{}",approveInfo,fid,account,certNo);

        /*1.获取H5刷脸签署的订单号**/
        String orderNo = bestSignService.getFaceOrderNo(fid,account,certNo);

        if (StringUtil.isNotEmpty(orderNo)){
            /*2.获取base64编码格式的视频**/
            String videoBase = bestSignService.getFaceVideo(orderNo,"1",certNo);

            if (StringUtil.isEmpty(videoBase)){
                log.info("获取征信授权视频为空！");
                return;
            }
            /*3.解密**/
            byte[] decodedBytes = Base64.getDecoder().decode(videoBase);

            String fileName =  approveInfo.getId() + "-征信授权视频"+ DEFAULT_VIDEO_FORMAT;
            String tempFile =  "/" + approveInfo.getId() + "-征信授权视频"+ DEFAULT_VIDEO_FORMAT;

            // 将解码后的字节数组写入文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) { // 输出为mp4格式
                fos.write(decodedBytes);
            } catch (IOException e) {
                log.info("解码后的字节数组写入文件异常："+e.getMessage());
                return;
            }

            ComAttachmentManagement management = attachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>lambdaQuery()
                    .like(ComAttachmentManagement::getAttachmentName, "征信授权视频")
            );

            File file = new File(tempFile);
            try{
                UploadResult uploadResult = FileCenterHelper.uploadFile(file,fileName);

                log.info("文件-" + fileName + "：上传成功！");
                String accessKey = uploadResult.getKey();


                ComAttachmentFile comAttachmentFile = new ComAttachmentFile();
                if (ObjectUtil.isNotEmpty(management)){
                    comAttachmentFile.setAttachmentCode(management.getId().toString());
                    comAttachmentFile.setAttachmentName(management.getAttachmentName());
                }
                comAttachmentFile.setFileName(fileName);
                comAttachmentFile.setBusiNo(approveInfo.getId().toString());
                comAttachmentFile.setBelongNo(approveInfo.getId().toString());
                comAttachmentFile.setFileId(accessKey);
                comAttachmentFile.setFileType(FileTypeEnum.video.getCode());
                comAttachmentFile.setFileStatus(FileStatusEnum.DRAFT.getCode() );
                comAttachmentFile.setUploadTime(new Date());
                comAttachmentFile.setFileSource("com_attachment_management");
                comAttachmentFile.setIsElectronic("1");
                comAttachmentFile.setHistoryVersion("0");
                comAttachmentFile.setArchiveClass("");

                comAttachmentFileService.save(comAttachmentFile);
            }catch (Exception e){
                log.info("上传文件异常："+e.getMessage());
            }finally {
                // 删除临时文件
                FileUtil.del(file);
            }
        }else{
            log.info("获取H5刷脸签署的订单号为空！");
        }
    }

    public String getLesseeCert(Long applyNo) {
        File file = null;
        File file1 = null;
        BufferedImage image = null;
        BufferedImage image1 = null;
        BufferedImage result = null;
        try {
            List<ComAttachmentManagement> list = attachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.PRE_BORROWER_IDCARD_FRONT.getCode()).or()
                    .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.PRE_BORROWER_IDCARD_BACK.getCode()));
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(0).getId()))
                    .eq(ComAttachmentFile::getBusiNo, String.valueOf(applyNo)).last("limit 1"));
            ComAttachmentFile comAttachmentFile1 = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(list.get(1).getId()))
                    .eq(ComAttachmentFile::getBusiNo, String.valueOf(applyNo)).last("limit 1"));
            String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".jpg";
            String tempPath1 = fileProperties.getTempDir() + comAttachmentFile1.getFileId() + ".jpg";
            FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(), FileType.ORIGINAL, tempPath);
            FileCenterHelper.downLoadFile(comAttachmentFile1.getFileId(), FileType.ORIGINAL, tempPath1);
            file = new File(tempPath);
            file1 = new File(tempPath1);
            image = ImageIO.read(file);
            image1 = ImageIO.read(file1);
            int width = image.getWidth() + image1.getWidth();
            int height = Math.max(image.getHeight(), image1.getHeight());
            result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            result.createGraphics().drawImage(image, 0, 0, null);
            result.createGraphics().drawImage(image1, image.getWidth(), 0, null);
            String filename = fileProperties.getTempDir() + System.currentTimeMillis() + ".jpg";
            if (ImageIO.write(result, "jpg", new File(filename))){
                return convertToBase64(filename);
            }else {
                new AfsBaseException("找不到对应的write");
            }
        } catch (Exception e) {
            log.info("合成身份证正反面图片失败:{}",e);
        } finally {
            if (image != null){
                image.getGraphics().dispose();
            }
            if (image1 != null){
                image1.getGraphics().dispose();
            }
            if (result != null){
                result.getGraphics().dispose();
            }
            file.delete();
            file1.delete();
        }
        return null;
    }

    public String getLesseeAutho(Long applyNo){
        String tempPath = null;
        try {
            ComAttachmentManagement comAttachmentManagement = attachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode,"creditAuthorizationFileNew")
                    .eq(ComAttachmentManagement::getAttachmentName,"个人征信授权书(承租人)")
                    .last("limit 1"));
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(comAttachmentManagement.getId()))
                    .eq(ComAttachmentFile::getBusiNo,String.valueOf(applyNo))
                    .last("limit 1"));
            if (ObjectUtil.isNotNull(comAttachmentFile)){
                tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".pdf";
                FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(),FileType.ORIGINAL,tempPath);
                return convertToBase64(tempPath);
            }else {
                log.error("获取征信授权书信息失败");
            }
        }catch (Exception e){
            log.error("查询征信授权书失败:{}",e);
        } finally {
            File file = new File(tempPath);
            file.delete();
        }
        return null;
    }

    /**
     * 下载担保人相关文件
     * @param tApplyBestSignRecordList 需要下载的文件
     */
    @Async("ioAsyncTaskExecutor")
    @Override
    public void downloadAllFile(List<ApplyBestSignRecord> tApplyBestSignRecordList){

        for(ApplyBestSignRecord applyBestSignRecord:tApplyBestSignRecordList){

            byte[] download = bestSignService.download(applyBestSignRecord.getFid(),applyBestSignRecord.getApplyNo());
            // 开始执行文件上传
            bestSignApplyService.uploadApplyFile(download,applyBestSignRecord.getApplyNo(),applyBestSignRecord.getFid(),applyBestSignRecord.getCustId());

        }
    }


    /**
     * 获取担保人征信报告
     * @param applyNo
     */
    @Async("ioAsyncTaskExecutor")
    @Override
    public void getReport(String applyNo,AfsTransEntity<OrderSubmitInfo> transEntity){
        String queryId= null;
        String filePath = null;
        ApplyCustBaseInfo applyCustBaseInfo = null;
        try {
            //查询担保人信息
            applyCustBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.GUARANTOR).last("limit 1"));
            log.info("进件提交，开始获取担保人征信报告，订单号:{}",applyNo);
            ApplyCustBaseInfo applyOrderInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>query().lambda()
                    .eq(ApplyCustBaseInfo::getApplyNo,applyNo)
                    .eq(ApplyCustBaseInfo::getCustRole, ApplyConstants.PRINCIPAL_BORROWER)
                    .last("limit 1"));
            ComReportFile comReportFile = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                    .eq(ComReportFile::getBelongNo,applyOrderInfo.getCertNo())
                    .isNotNull(ComReportFile::getElectronicNo)
                    .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
            if (ObjectUtil.isNotNull(comReportFile)){
                comReportFile.setBusiNo(applyNo);
                comReportFileService.updateById(comReportFile);
                caseRemindFeign.saveReport(comReportFile);
                transEntity.getData().setCqueryid(comReportFile.getElectronicNo());
            }
            if (ObjectUtil.isNotNull(applyCustBaseInfo)){
                //判断这月是否已经查过
                ComReportFile comReportFile2 = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                        .eq(ComReportFile::getBelongNo,applyCustBaseInfo.getCertNo())
                        .ge(ComReportFile::getCreateTime,DateUtil.format(DateUtil.lastMonth(),"yyyy-MM-dd"))
                        .lt(ComReportFile::getCreateTime,DateUtil.format(DateUtil.tomorrow(),"yyyy-MM-dd"))
                        .orderByDesc(ComReportFile::getCreateTime)
                        .last("limit 1"));
                if (ObjectUtil.isNotNull(comReportFile2)){
                    log.info("该{}-{}担保人这月已经查询过",applyCustBaseInfo.getCertNo(),applyCustBaseInfo.getCustName());
                    transEntity.getData().setEqueryid(comReportFile2.getElectronicNo());
                    caseSubmitInfoSender.sendForCaseSubmit(transEntity);
                    caseRemindFeign.saveReport(comReportFile2);
                } else {
                    ComReportFile comReportFile3 = new ComReportFile();
                    comReportFile3.setBusiNo(applyNo);
                    comReportFile3.setBelongNo(applyCustBaseInfo.getCertNo());
                    comReportFile3.setAttachmentName(applyCustBaseInfo.getCustName());
                    comReportFile3.setAttachmentCode(AttachmentUniqueCodeEnum.GUARANTOR_REPORT_FILE.getCode());
                    comReportFile3.setHistoryVersion("0");
                    comReportFile3.setIsElectronic("1");
                    comReportFileService.save(comReportFile3);
                    //添加调用统计记录
                    addRunCount();
                    ZxReportCondition zxReportCondition = new ZxReportCondition();
                    zxReportCondition.setApplyNo(applyNo);
                    zxReportCondition.setCertName(applyCustBaseInfo.getCustName());
                    zxReportCondition.setCertNo(applyCustBaseInfo.getCertNo());
                    zxReportCondition.setType("2");
                    //获取担保人身份证正反面
                    zxReportCondition.setCertFile(getGuarantorCert(applyNo));
                    //获取担保人人征信授权书
                    zxReportCondition.setAuthoFile(getGuarantorAutho(applyNo));
                    queryId = zxThirdService.queryPersonReport(zxReportCondition);
                    if (StrUtil.isNotEmpty(queryId)) {
                        filePath = zxThirdService.getPersonReport(queryId, applyNo, "guarantor", zxReportCondition);
                        if (StringUtils.isNotBlank(filePath)) {
                            File file = new File(filePath);
                            file.delete();
                        } else {
                            log.info("获取担保人征信报告失败");
                        }
                    } else {
                        log.info("保证人征信报告申请接口异常");
                    }
                    transEntity.getData().setEqueryid(queryId);
                    caseSubmitInfoSender.sendForCaseSubmit(transEntity);
                    ComReportFile comReportFile1 = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                            .eq(ComReportFile::getBelongNo, applyCustBaseInfo.getCertNo())
                            .isNotNull(ComReportFile::getElectronicNo)
                            .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
                    log.info("同步到信审参数:{}", JSONObject.toJSONString(comReportFile1));
                    caseRemindFeign.saveReport(comReportFile1);
                }
            }else {
                caseSubmitInfoSender.sendForCaseSubmit(transEntity);
                log.info("该订单没有担保人");
            }
        }catch (Exception e){
            log.info("获取保证人征信报告出现了异常:{}",e);
            transEntity.getData().setEqueryid(queryId);
            caseSubmitInfoSender.sendForCaseSubmit(transEntity);
            if (ObjectUtil.isNotNull(applyCustBaseInfo)){
                ComReportFile comReportFile1 = comReportFileService.getOne(Wrappers.<ComReportFile>query().lambda()
                        .eq(ComReportFile::getBelongNo,applyCustBaseInfo.getCertNo())
                        .orderByDesc(ComReportFile::getCreateTime).last("limit 1"));
                if (StrUtil.equals(e.getMessage(),"文件上传失败")){
                    comReportFile1.setRemake("上传到oss失败，信审端在前置机获取报告查看");
                    comReportFile1.setFileStatus("1");
                    zxThirdService.addSucCount();
                }else {
                    comReportFile1.setRemake(e.getMessage());
                    comReportFile1.setFileStatus("2");
                    zxThirdService.addFailCount();
                }
                comReportFile1.setElectronicNo(queryId);
                comReportFileService.updateById(comReportFile1);
                caseRemindFeign.saveReport(comReportFile1);
            }
        }
    }

    public String getGuarantorCert(String applyNo){
        File file = null;
        File file1 = null;
        BufferedImage image = null;
        BufferedImage image1 = null;
        BufferedImage result = null;
        try {
            List<ComAttachmentManagement> list = attachmentManagementService.list(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_FRONT.getCode()).or()
                    .eq(ComAttachmentManagement::getUniqueCode,AttachmentUniqueCodeEnum.GUARANTOR_BORROWER_IDCARD_BACK.getCode()));
            if (list.size() > 1){
                ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(list.get(0).getId()))
                        .eq(ComAttachmentFile::getBusiNo,applyNo).last("limit 1"));
                ComAttachmentFile comAttachmentFile1 = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(list.get(1).getId()))
                        .eq(ComAttachmentFile::getBusiNo,applyNo).last("limit 1"));
                String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".jpg";
                String tempPath1 = fileProperties.getTempDir() + comAttachmentFile1.getFileId() + ".jpg";
                FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(),FileType.ORIGINAL,tempPath);
                FileCenterHelper.downLoadFile(comAttachmentFile1.getFileId(),FileType.ORIGINAL,tempPath1);
                file = new File(tempPath);
                file1 = new File(tempPath1);

                image = ImageIO.read(file);
                image1 = ImageIO.read(file1);
                int width = image.getWidth() + image1.getWidth();
                int height = Math.max(image.getHeight(),image1.getHeight());
                result = new BufferedImage(width,height,BufferedImage.TYPE_INT_RGB);
                result.createGraphics().drawImage(image,0,0,null);
                result.createGraphics().drawImage(image1,image.getWidth(),0,null);
                String filename  = fileProperties.getTempDir() + System.currentTimeMillis() + ".jpg";
                if (ImageIO.write(result,"jpg",new File(filename))){
                    return convertToBase64(filename);
                }else {
                    new AfsBaseException("找不到对应的write");
                }
            }else {
                log.error("获取身份证正反面信息失败");
            }
        }catch (Exception e){
            log.error("合成身份证正反面图片失败：{}",e);
        }finally {
            if (image != null){
                image.getGraphics().dispose();
            }
            if (image1 != null){
                image1.getGraphics().dispose();
            }
            if (result != null){
                result.getGraphics().dispose();
            }
            file.delete();
            file1.delete();
        }

        return null;
    }

    public String getGuarantorAutho(String applyNo){
        ComAttachmentManagement comAttachmentManagement = attachmentManagementService.getOne(Wrappers.<ComAttachmentManagement>query().lambda()
                    .eq(ComAttachmentManagement::getAttachmentName,"个人征信授权书(保证人)").last("limit 1"));
        if (ObjectUtil.isNotNull(comAttachmentManagement)){
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>query().lambda()
                    .eq(ComAttachmentFile::getAttachmentCode,String.valueOf(comAttachmentManagement.getId()))
                    .eq(ComAttachmentFile::getBusiNo,applyNo).last("limit 1"));
            String tempPath = fileProperties.getTempDir() + comAttachmentFile.getFileId() + ".pdf";
            FileCenterHelper.downLoadFile(comAttachmentFile.getFileId(),FileType.ORIGINAL,tempPath);
            return convertToBase64(tempPath);
        }else {
            log.error("获取征信授权书信息失败");
        }
        return null;
    }

    public String convertToBase64(String filePath){
        File file = new File(filePath);
        byte[] imageBytes = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] bytes = new byte[1024];
            int byteRead;
            while ((byteRead = fis.read(bytes)) != -1){
                byteArrayOutputStream.write(bytes,0,byteRead);
            }
            imageBytes = byteArrayOutputStream.toByteArray();
        }catch (Exception e){
            log.error("文件转换base64失败:{}",e);
        }finally {
            if(fis != null){
                try{
                    fis.close();
                }catch (Exception e){
                    log.error("FileInputStream流关闭失败:{}",e);
                }
            }
            file.delete();
        }
        return Base64.getEncoder().encodeToString(imageBytes);
    }

}
