package com.ruicar.afs.cloud.apply.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarDetails;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCarInvoice;
import com.ruicar.afs.cloud.apply.common.entity.ApplyChannelInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyContractInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyCustBaseInfo;
import com.ruicar.afs.cloud.apply.common.entity.ApplyOrderInfo;
import com.ruicar.afs.cloud.apply.common.utils.ApplyConstants;
import com.ruicar.afs.cloud.apply.contract.service.ApplyCarInvoiceService;
import com.ruicar.afs.cloud.apply.contract.service.ApplyContractInfoService;
import com.ruicar.afs.cloud.apply.job.entity.StatusChangeHistory;
import com.ruicar.afs.cloud.apply.job.enums.ApplyStatusPushEnum;
import com.ruicar.afs.cloud.apply.job.enums.ChangeTypeEnum;
import com.ruicar.afs.cloud.apply.job.enums.ContractStatusPushEnum;
import com.ruicar.afs.cloud.apply.job.service.StatusChangeHistoryService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCarDetailsService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyChannelInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyCustBaseInfoService;
import com.ruicar.afs.cloud.apply.pre.loan.service.ApplyOrderInfoService;
import com.ruicar.afs.cloud.apply.vehicle.feign.VehicleInfoFeign;
import com.ruicar.afs.cloud.bizcommon.business.entity.FinCostDetails;
import com.ruicar.afs.cloud.bizcommon.business.service.ApplyCostDetailsService;
import com.ruicar.afs.cloud.bizcommon.clmbv.config.enums.ApplyCarInvoiceEnum;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.job.core.biz.model.ReturnT;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJob;
import com.ruicar.afs.cloud.common.job.core.handler.annotation.AfsJobHandler;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.common.modules.contract.cms.vo.PushLoanOrderVo;
import com.ruicar.afs.cloud.common.modules.contract.cms.vo.PushOrderStatusVo;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.interfaces.crm.dto.CrmReqData;
import com.ruicar.afs.cloud.interfaces.crm.dto.CrmResData;
import com.ruicar.afs.cloud.interfaces.crm.dto.Parameter;
import com.ruicar.afs.cloud.interfaces.crm.dto.RtnData;
import com.ruicar.afs.cloud.interfaces.crm.service.CrmService;
import com.ruicar.afs.cloud.interfaces.tengshi.service.TsService;
import com.ruicar.afs.cloud.parameter.commom.enums.CostType;
import com.ruicar.afs.cloud.parameter.commom.enums.DiscountType;
import com.ruicar.afs.cloud.vehicle.enums.VehicleSaleNetEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@AfsJob
@AllArgsConstructor
@Slf4j
@Component
public class ApplyJob {

    private final CrmService crmService;
    private final VehicleInfoFeign vehicleInfoFeign;
    private final ApplyCarDetailsService applyCarDetailsService;
    private final ApplyContractInfoService applyContractInfoService;
    private final ApplyCostDetailsService applyCostDetailsService;
    private final ApplyOrderInfoService applyOrderInfoService;
    private final ApplyCarInvoiceService applyCarInvoiceService;
    private final ApplyChannelInfoService applyChannelInfoService;
    private final ApplyCustBaseInfoService applyCustBaseInfoService;
    private final StringRedisTemplate redisTemplate;
    private final StatusChangeHistoryService statusChangeHistoryService;
    private final TsService tsService;

    private static final String INITIAL_QUERY_KEY = "push-status-to-ts-initial-done";
    private static final String LAST_QUERY_TIME_KEY = "push-status-to-ts-last-done";
    private static final int BATCH_SIZE = 64;  // 批处理的大小，对于30,000条左右的数据量（目前生产环境数据量），BATCH_SIZE 设置为64是一个很好的折中方案

    /**
     * 生产环境crm临时校验上海
     * @param param
     * @return
     */
    @AfsJobHandler(value = "prdCrmTest")
    public ReturnT<String> prdCrmTest(String param) {
        log.info("启动【生产环境crm临时校验上海的定时任务】");
        List<ApplyChannelInfo> applyChannelInfoList = applyChannelInfoService.list(Wrappers.<ApplyChannelInfo>lambdaQuery()
                .eq(ApplyChannelInfo::getChannelProvince, "310000"));
        List<String> contractStatusList = new ArrayList<>();
        contractStatusList.add(ApplyConstants.CONTRACT_STATUS_SUBMIT);
        contractStatusList.add(ApplyConstants.CONTRACT_STATUS_APPROVE);
        contractStatusList.add(ApplyConstants.CONTRACT_STATUS_PASS);
        int successCount = 0;
        int errorCount = 0;
        StringBuilder builder = new StringBuilder();
        for (ApplyChannelInfo applyChannelInfo : applyChannelInfoList) {
            String applyNo = applyChannelInfo.getApplyNo();
            String vehicleNumber = "";
            try {
                ApplyContractInfo contractInfo = applyContractInfoService.getOne(Wrappers.<ApplyContractInfo>lambdaQuery()
                        .eq(ApplyContractInfo::getApplyNo, applyNo)
                        .in(ApplyContractInfo::getContractStatus, contractStatusList));
                if (contractInfo != null) {
                    ApplyCarDetails carDetails = applyCarDetailsService.getOne(Wrappers.<ApplyCarDetails>lambdaQuery().eq(ApplyCarDetails::getApplyNo, applyNo));
                    VehicleSaleNetEnum saleNet = carDetails.getSaleNet();
                    vehicleNumber = carDetails.getCarVin();
                    if (saleNet == VehicleSaleNetEnum.SEA || saleNet == VehicleSaleNetEnum.DYNASTY) {
                        List<FinCostDetails> costDetails = applyCostDetailsService.list(Wrappers.<FinCostDetails>query().lambda().eq(FinCostDetails::getApplyNo, applyNo));
                        ApplyCarInvoice reqCarInvoice = applyCarInvoiceService.getOne(Wrappers.<ApplyCarInvoice>query().lambda()
                                .eq(ApplyCarInvoice::getApplyNo, applyNo)
                                .eq(ApplyCarInvoice::getInvoiceType, ApplyCarInvoiceEnum.INVOICETYPE_MOTOR.getIndex()));
                        ApplyOrderInfo orderInfo = applyOrderInfoService.getOne(Wrappers.<ApplyOrderInfo>lambdaQuery().eq(ApplyOrderInfo::getApplyNo, applyNo));
                        ApplyCustBaseInfo custBaseInfo = applyCustBaseInfoService.getOne(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                                .eq(ApplyCustBaseInfo::getApplyNo, applyNo)
                                .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
                        for (FinCostDetails costDetail : costDetails) {
                            if (CostType.CARAMT.getIndex().equals(costDetail.getCostType()) && DiscountType.NORMAL.getIndex().equals(costDetail.getDiscountType())) {
                                this.crmCheck(carDetails, reqCarInvoice, orderInfo, custBaseInfo, applyChannelInfo);
                                successCount++;
                                break;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                errorCount++;
                builder.append("申请编号:" + applyNo + "车架号:" + vehicleNumber + "出现异常:" + e.getMessage()+"。");
            }
        }
        log.info("符合Crm校验的数量：{}，不符合Crm校验的数量：{}", successCount, errorCount);
        log.info("失败原因：{}", builder.toString());
        return ReturnT.SUCCESS;
    }

    /**
     * crm校验
     * @param carDetails
     * @param carInvoice
     * @param orderInfo
     * @param custBaseInfo
     * @param channelInfo
     */
    private void crmCheck(ApplyCarDetails carDetails, ApplyCarInvoice carInvoice, ApplyOrderInfo orderInfo, ApplyCustBaseInfo custBaseInfo, ApplyChannelInfo channelInfo) {
        CrmReqData crmReqData = new CrmReqData();   //对传入CRM校验的参数赋值
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = sdf.format(date);
        crmReqData.setBizId(format);
        crmReqData.setSender("FDFinance");
        Parameter parameter = new Parameter();
        parameter.setFrom("FDFinance");
        parameter.setVin(carDetails.getCarVin());
        crmReqData.setParameter(parameter);
        log.info("开始调用CRM系统校验" + carDetails.getCarVin());
        IResponse<CrmResData> response = crmService.callThirdSystem(crmReqData);
        RtnData rtnData = response.getData().getRtnData();
        List<RtnData.VehicleData> vehicleDataList = rtnData.getListOfBydautovehicleeai();
        //VIN号在销服系统存在
        Assert.isTrue(vehicleDataList != null && vehicleDataList.size() == 1, "crm:请检查是否已录销服或VIN号是否录入正确");
        RtnData.VehicleData vehicleData = vehicleDataList.get(0);
        //订单类型为员工订单/零售订单
        Assert.isTrue("零售订单".equals(vehicleData.getBYDOrderType()) || "员工订单".equals(vehicleData.getBYDOrderType()), "crm:订单类型不符，请核实");
        //非试乘试驾
        Assert.isTrue("N".equals(vehicleData.getBYDTDVFlag()), "crm:申请为试乘试驾车，禁止提交");
        //非大客户
        Assert.isTrue("N".equals(vehicleData.getBYDKAFlag()), "crm:申请为大客户订单，禁止提交");
        //所属一级经销商代码与租赁系统经销商代码一致
        String bydDealerCode = vehicleData.getBYDDealerCode();
        if (!channelInfo.getChannelCode().equals(bydDealerCode)) {
            if (VehicleSaleNetEnum.SEA == carDetails.getSaleNet()) {
                //海洋网
                boolean flag = false;
                Map<String, List<DicDataDto>> crmSeaChannel = DicHelper.getDicMaps("crmSeaChannel");
                List<DicDataDto> dicDataDtoList = crmSeaChannel.get("crmSeaChannel");
                for (DicDataDto dataDto : dicDataDtoList) {
                    if (dataDto.getValue().equals(bydDealerCode) && dataDto.getTitle().equals(channelInfo.getChannelName())) {
                        flag = true;
                        break;
                    }
                }
                if(!flag){
                    throw new AfsBaseException(MessageFormat.format("crm:经销商信息不一致，请核实; 系统合作商代码:{0}, crm所属一级经销商代码:{1}",channelInfo.getChannelCode(),bydDealerCode));
                }
            } else if (VehicleSaleNetEnum.DYNASTY == carDetails.getSaleNet()) {
                //王朝网
                boolean flag = false;
                Map<String, List<DicDataDto>> crmDynastyChannel = DicHelper.getDicMaps("crmDynastyChannel");
                List<DicDataDto> dicDataDtoList = crmDynastyChannel.get("crmDynastyChannel");
                for (DicDataDto dataDto : dicDataDtoList) {
                    if (dataDto.getValue().equals(bydDealerCode) && dataDto.getTitle().equals(channelInfo.getChannelName())) {
                        flag = true;
                        break;
                    }
                }
                if(!flag){
                    throw new AfsBaseException(MessageFormat.format("crm:经销商信息不一致，请核实; 系统合作商代码:{0}, crm所属一级经销商代码:{1}",channelInfo.getChannelCode(),bydDealerCode));
                }
            } else {
                throw new AfsBaseException(MessageFormat.format("crm:经销商信息不一致，请核实; 系统合作商代码:{0}, crm所属一级经销商代码:{1}",channelInfo.getChannelCode(),bydDealerCode));
            }
        }
        //产品编码与车型代码一致
        if (!(vehicleData.getProductCode().equals(carDetails.getVehicleModelCode()))) {
            //不一致，匹配组
            Boolean flag = vehicleInfoFeign.crmCheckCode(carDetails.getCrmVerificationGroup(), vehicleData.getProductCode());
            if(!flag){
                throw new AfsBaseException(MessageFormat.format("crm:车型信息有误，请重新提交VIN信息或变更车型; 系统车型代码:{0}, crm产品编码:{1}",carDetails.getVehicleModelCode(),vehicleData.getProductCode()));
            }
        }
        //客户证件号码与租赁系统证件号码一致
        Assert.isTrue(custBaseInfo.getCertNo().equals(vehicleData.getAccountLocation()), "crm:客户身份信息有误，请核实销服与录入客户信息");
        //核准日期减购车日期≤30天
        SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
        Date bydDate = null;
        try {
            bydDate = formatter.parse(vehicleData.getBYDPurchaseDate());
        } catch (ParseException e) {
            throw new AfsBaseException("CRM购车日期转换失败！");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(bydDate);
        c.add(Calendar.DATE, 30);
        Date buyDate = c.getTime();
        Assert.isTrue(orderInfo.getRiskPassDate().getTime() < buyDate.getTime(), "crm:核准已超购车期限");
        //订单因“厂家发车原因”申请延期后，激活日期（放款提交时间）-入库日期≤限定期限（暂设定为30天）
        //发票号码一致
        if(!carInvoice.getInvoiceNo().equals(vehicleData.getBYDInvoiceNum())){
            throw new AfsBaseException(MessageFormat.format("crm:发票信息不一致，请核实;CRM返回发票号{0},系统信息{1}",vehicleData.getBYDInvoiceNum(),carInvoice.getInvoiceNo()));
        }
    }

    /**
     * 推送数据到腾势
     *
     * @param param param
     * @return return
     */
    @AfsJobHandler(value = "pushOrderStatusToTs")
    public ReturnT<String> pushOrderStatusToTs(String param) {
        log.info("启动【推送数据到腾势的定时任务】");
        boolean initialQueryDone = Boolean.TRUE.equals(redisTemplate.hasKey(INITIAL_QUERY_KEY));

        Date thisQueryTime = new Date(); // 当前查询的时间点

        // 如果尚未完成初始查询
        if (!initialQueryDone) {
            log.info("启动推送数据到腾势的定时任务初始查询, thisQueryTime = {}", thisQueryTime);
            initialQueryAndSave(thisQueryTime); // 执行初始查询
            redisTemplate.opsForValue().set(INITIAL_QUERY_KEY, "true"); // 标记初始查询已完成
        } else {
            // 如果已经完成了初始查询，则执行增量查询
            log.info("启动推送数据到腾势的定时任务增量查询, thisQueryTime = {}", thisQueryTime);
            // 从 Redis 读取并转换回 Date
            long lastQueryTimestamp = Long.parseLong(redisTemplate.opsForValue().get(LAST_QUERY_TIME_KEY));
            Date lastQueryTime = new Date(lastQueryTimestamp);
            log.info("上次查询的时间点为：{}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastQueryTime));
            List<StatusChangeHistory> statusChangeHistories = incrementalQuery(lastQueryTime);
            saveAndPush(statusChangeHistories, thisQueryTime, false); // 保存并推送增量查询的结果
        }

        // 更新Redis中最后一次查询的时间点
        redisTemplate.opsForValue().set(LAST_QUERY_TIME_KEY, String.valueOf(thisQueryTime.getTime()));
        return ReturnT.SUCCESS;
    }

    /**
     * 执行初始查询并将结果保存。
     *
     * @param queryTime 查询的时间点
     */
    private void initialQueryAndSave(Date queryTime) {
        int page = 0;
        List<StatusChangeHistory> allRecords = new ArrayList<>();
        long startTime = System.currentTimeMillis(); // 开始计时
        try {
            while (true) {
                List<StatusChangeHistory> currentRecords = findRecordsForInitialQuery(page++, BATCH_SIZE, queryTime); // 获取初始查询的批次
                if (currentRecords.isEmpty()) {
                    break; // 终止循环
                }
                // 只要applyNo字段
                Set<String> applyNoSet = currentRecords.stream().map(StatusChangeHistory::getApplyNo).collect(Collectors.toSet());
                // 只要腾势的订单状态
                List<ApplyCarDetails> applyCarDetails = applyCarDetailsService.list(Wrappers.<ApplyCarDetails>lambdaQuery()
                        .select(ApplyCarDetails::getApplyNo)
                        .eq(ApplyCarDetails::getSaleNet, VehicleSaleNetEnum.DENZA)
                        .in(ApplyCarDetails::getApplyNo, applyNoSet)
                );
                if (applyCarDetails.isEmpty()) {
                    continue; // 跳过当前批次，因为没有符合条件的车辆
                }
                // applyCarDetails里面有的,才是符合条件的
                List<StatusChangeHistory> currentRecordsFiltered = currentRecords.stream().filter(record -> applyCarDetails.stream().anyMatch(car -> car.getApplyNo().equals(record.getApplyNo()))).collect(Collectors.toList());
                if (!currentRecordsFiltered.isEmpty()) {
                    Set<String> applyNoSetFiltered = currentRecordsFiltered.stream().map(StatusChangeHistory::getApplyNo).collect(Collectors.toSet());
                    List<ApplyCustBaseInfo> applyCustBaseInfoList = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                            .select(ApplyCustBaseInfo::getApplyNo, ApplyCustBaseInfo::getTelPhone)
                            .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST.getCode())
                            .in(ApplyCustBaseInfo::getApplyNo, applyNoSetFiltered));
                    // 只要applyNo、telPhone字段
                    Map<String, String> applyNoTelPhoneMap = applyCustBaseInfoList.stream().collect(Collectors.toMap(ApplyCustBaseInfo::getApplyNo, ApplyCustBaseInfo::getTelPhone));
                    currentRecordsFiltered.forEach(record -> {
                        record.setTel(applyNoTelPhoneMap.get(record.getApplyNo()));
                    });
                    allRecords.addAll(currentRecordsFiltered); // 收集所有记录
                }

                // 检查当前批次是否为空或记录数小于 BATCH_SIZE
                if (currentRecords.size() < BATCH_SIZE) {
                    break; // 终止循环
                }
            }
            saveAndPush(allRecords, queryTime, true); // 一次性保存所有记录
        } catch (Exception e) {
            log.error("Error during initial query and save process: {}", e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis(); // 结束计时
            long totalTime = endTime - startTime; // 总处理时间
            log.info("Total processing time: {} milliseconds", totalTime);
        }
    }

    /**
     * 执行增量查询以获取自上次查询以来的新数据。
     *
     * @param lastQueryTime 上一次查询的时间点
     * @return 增量查询的结果
     */
    private List<StatusChangeHistory> incrementalQuery(Date lastQueryTime) {
        try {
            List<ApplyOrderInfo> applyOrderInfoList = applyOrderInfoService.list(Wrappers.<ApplyOrderInfo>lambdaQuery()
                    .select(ApplyOrderInfo::getApplyNo, ApplyOrderInfo::getApplyStatus)
                    .gt(ApplyOrderInfo::getUpdateTime, lastQueryTime));
            List<ApplyContractInfo> applyContractInfoList = applyContractInfoService.list(Wrappers.<ApplyContractInfo>lambdaQuery()
                    .select(ApplyContractInfo::getApplyNo, ApplyContractInfo::getContractStatus)
                    .gt(ApplyContractInfo::getUpdateTime, lastQueryTime));

            if (applyOrderInfoList.isEmpty() && applyContractInfoList.isEmpty()) {
                return Collections.emptyList();
            }

            Map<String, StatusChangeHistory> statusChangeHistoryMap = new ConcurrentHashMap<>();

            // 使用computeIfAbsent减少重复代码
            for (ApplyOrderInfo applyOrderInfo : applyOrderInfoList) {
                String applyNo = applyOrderInfo.getApplyNo();
                statusChangeHistoryMap.computeIfAbsent(applyNo, k -> new StatusChangeHistory())
                        .setApplyNo(applyNo)
                        .setApplyStatus(applyOrderInfo.getApplyStatus())
                        .setChangeType(ChangeTypeEnum.APPLY_STATUS_CHANGED);
            }

            for (ApplyContractInfo applyContractInfo : applyContractInfoList) {
                String applyNo = applyContractInfo.getApplyNo();
                statusChangeHistoryMap.computeIfAbsent(applyNo, k -> new StatusChangeHistory())
                        .setApplyNo(applyNo)
                        .setContractStatus(applyContractInfo.getContractStatus())
                        .setChangeType(ChangeTypeEnum.CONTRACT_STATUS_CHANGED);
            }
            // 取出所有applyNo
            Set<String> applyNoSet = statusChangeHistoryMap.keySet();
            // 只要腾势的订单状态
            List<ApplyCarDetails> applyCarDetails = applyCarDetailsService.list(Wrappers.<ApplyCarDetails>lambdaQuery()
                    .select(ApplyCarDetails::getApplyNo)
                    .eq(ApplyCarDetails::getSaleNet, VehicleSaleNetEnum.DENZA)
                    .in(ApplyCarDetails::getApplyNo, applyNoSet)
            );
            if (applyCarDetails.isEmpty()) {
                return Collections.emptyList();
            }
            // 排除非腾势订单，包括未选车型的
            Set<String> carApplyNos = applyCarDetails.stream()
                    .map(ApplyCarDetails::getApplyNo)
                    .collect(Collectors.toSet());
            applyNoSet.retainAll(carApplyNos);
            statusChangeHistoryMap.keySet().retainAll(applyNoSet);
            if (CollUtil.isEmpty(statusChangeHistoryMap)) {
                return Collections.emptyList();
            }

            // 查询所有applyNo的订单信息:手机号
            List<ApplyCustBaseInfo> applyCustBaseInfoList = applyCustBaseInfoService.list(Wrappers.<ApplyCustBaseInfo>lambdaQuery()
                    .select(ApplyCustBaseInfo::getApplyNo, ApplyCustBaseInfo::getTelPhone)
                    .in(ApplyCustBaseInfo::getApplyNo, applyNoSet)
                    .eq(ApplyCustBaseInfo::getCustRole, CustRoleEnum.MIANCUST));
            // 装到一个map
            Map<String, String> telMap = applyCustBaseInfoList.stream()
                    .collect(Collectors.toMap(ApplyCustBaseInfo::getApplyNo, ApplyCustBaseInfo::getTelPhone));
            // 更新changeType为ALL如果两个状态都发生了变化
            statusChangeHistoryMap.values().forEach(history -> {
                if (StrUtil.isAllNotBlank(history.getApplyStatus(), history.getContractStatus())) {
                    history.setChangeType(ChangeTypeEnum.ALL);
                }
                // setTel
                history.setTel(telMap.get(history.getApplyNo()));
            });

            // 确保至少有一个状态被更新
            return new ArrayList<>(statusChangeHistoryMap.values().stream()
                    .filter(history -> history.getApplyStatus() != null || history.getContractStatus() != null)
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("Incremental query failed with lastQueryTime: {}, Error: {}", lastQueryTime, e.getMessage(), e);
            // 其他处理逻辑，例如发送邮件通知
            return Collections.emptyList();
        }
    }



    /**
     * 保存并推送数据到腾势系统。
     *
     * @param recordsForSave 要保存的数据列表
     * @param queryTime 当前时间点
     * @param isInitialQuery 是否为初始查询
     */
    private void saveAndPush(List<StatusChangeHistory> recordsForSave, Date queryTime, boolean isInitialQuery) {
        if (recordsForSave.isEmpty()) {
            log.info("没有需要保存的数据，跳过保存和推送操作");
            return;
        }
        // 更新记录的时间戳和是否跳过的标志
        recordsForSave.forEach(record -> {
            record.setUpdateTime(queryTime);
            record.setCreateTime(queryTime);
            String isPass = Optional.ofNullable(record.getContractStatus())
                    .filter(status -> status.equals(ApplyConstants.CONTRACT_STATUS_PASS))
                    .map(status -> CommonConstants.COMMON_YES)
                    .orElse(CommonConstants.COMMON_NO);
            record.setIsSkip(isPass);
            record.setPushTime(queryTime);
        });
        log.info("启动【推送数据到腾势的定时任务】，保存和推送数据, recordsForSave size = {}", recordsForSave.size());
        if (!isInitialQuery) {
            // 非初始查询，仅推送变更的数据
            try {
                List<String> changedApplyNoList = recordsForSave.stream().map(StatusChangeHistory::getApplyNo).collect(Collectors.toList());
                if (changedApplyNoList.isEmpty()) {
                    log.info("没有需要推送的数据，跳过推送操作");
                    return;
                }
                // 获取已存在的记录
                List<StatusChangeHistory> existingRecords = statusChangeHistoryService.list(Wrappers.<StatusChangeHistory>lambdaQuery()
                        .select(StatusChangeHistory::getApplyNo,
                                StatusChangeHistory::getApplyStatus,
                                StatusChangeHistory::getContractStatus,
                                StatusChangeHistory::getId)
                        .in(StatusChangeHistory::getApplyNo, changedApplyNoList) // 只查询改变的数据，避免全量查询
                        .eq(StatusChangeHistory::getIsSkip, CommonConstants.COMMON_NO));
                log.info("启动【推送数据到腾势的定时任务】，增量查询, existingRecords size = {}", existingRecords.size());
                if (existingRecords.isEmpty()) {
                    // 缺失sku编码的补充
                    appendSkuCode(recordsForSave, queryTime, changedApplyNoList, Collections.emptyList());
                    log.info("启动【推送数据到腾势的定时任务】，增量查询, existingRecords is empty, push all records! recordsForSave = {}", JSONUtil.parse(recordsForSave));
                    asyncSaveOrUpdateBatch(recordsForSave);
                    // 异步推送数据
                    CompletableFuture.runAsync(() -> pushData(recordsForSave)).whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.error("Failed to push data to Tengshi asynchronously.", throwable);
                        }
                    });
                    return;
                }

                Set<String> keys;
                // 按变更类型分组
                Map<ChangeTypeEnum, List<StatusChangeHistory>> allChangeTypeMap = recordsForSave.stream()
                        .collect(Collectors.groupingBy(StatusChangeHistory::getChangeType));
                log.info("启动【推送数据到腾势的定时任务】，增量查询, allChangeTypeMap = {}", JSONUtil.parse(allChangeTypeMap));
                // 处理所有变更类型
                for (ChangeTypeEnum changeType : ChangeTypeEnum.values()) {
                    List<StatusChangeHistory> records = allChangeTypeMap.getOrDefault(changeType, Collections.emptyList());
                    if (records.isEmpty()) {
                        continue;
                    }
                    keys = switch (changeType) {
                        case ALL -> existingRecords.stream()
                                .map(r -> r.getApplyNo() + "-" + r.getApplyStatus() + "-" + r.getContractStatus())
                                .collect(Collectors.toCollection(HashSet::new));
                        case APPLY_STATUS_CHANGED -> existingRecords.stream()
                                .map(r -> r.getApplyNo() + "-" + r.getApplyStatus())
                                .collect(Collectors.toCollection(HashSet::new));
                        case CONTRACT_STATUS_CHANGED -> existingRecords.stream()
                                .map(r -> r.getApplyNo() + "-" + r.getContractStatus())
                                .collect(Collectors.toCollection(HashSet::new));
                    };
                    log.info("启动【推送数据到腾势的定时任务】，增量查询, keys = {}", JSONUtil.parse(keys));
                    Set<String> finalKeys = keys;
                    recordsForSave.removeIf(record -> finalKeys.contains(
                            changeType == ChangeTypeEnum.ALL ?
                                    record.getApplyNo() + "-" + record.getApplyStatus() + "-" + record.getContractStatus() :
                                    changeType == ChangeTypeEnum.APPLY_STATUS_CHANGED ?
                                            record.getApplyNo() + "-" + record.getApplyStatus() :
                                            record.getApplyNo() + "-" + record.getContractStatus()));
                }
                if (recordsForSave.isEmpty()) {
                    log.info("启动【推送数据到腾势的定时任务】，增量查询, recordsForSave is empty");
                    return;
                }

                // 批量获取 applyNo,根据applyNo到ApplyCarDetails表中获取vehicle_model_code
                List<String> applyNos = recordsForSave.stream()
                        .map(StatusChangeHistory::getApplyNo)
                        .distinct()
                        .collect(Collectors.toList());
                log.info("启动推送数据到腾势的定时任务,增量查询, applyNos = {}", JSONUtil.parse(applyNos));

                // 批量查询 ApplyCarDetails 和 StatusChangeHistory
                appendSkuCode(recordsForSave, queryTime, applyNos, existingRecords);

                log.info("启动推送数据到腾势的定时任务,增量查询, recordsForSave after filter = {}", JSONUtil.parse(recordsForSave));
                // 异步推送数据
                CompletableFuture.runAsync(() -> pushData(recordsForSave)).whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("Failed to push data to Tengshi asynchronously.", throwable);
                    }
                });
            } catch (Exception e) {
                log.error("Failed to push data to Tengshi for record: {}, Error: {}. StackTrace: {}",
                        JSONUtil.parse(recordsForSave), e.getMessage(), Arrays.toString(e.getStackTrace()), e);
            }
        }

        log.info("保存数据，记录数：{}", recordsForSave.size());
        log.info("保存数据，记录：{}", JSONUtil.parse(recordsForSave));
        // 异步保存或更新数据
        asyncSaveOrUpdateBatch(recordsForSave);
    }

    /**
     * 追加车辆型号代码
     * @param recordsForSave recordsForSave
     * @param queryTime queryTime
     * @param applyNos applyNos
     * @param existingRecords existingRecords
     */
    private void appendSkuCode(List<StatusChangeHistory> recordsForSave, Date queryTime, List<String> applyNos, List<StatusChangeHistory> existingRecords) {
        List<ApplyCarDetails> carDetailsList = applyCarDetailsService.list(Wrappers.<ApplyCarDetails>query().lambda()
                .select(ApplyCarDetails::getApplyNo, ApplyCarDetails::getVehicleModelCode)
                .in(ApplyCarDetails::getApplyNo, applyNos));
        Map<String, String> carDetailsMap = carDetailsList.stream()
                .filter(carDetails -> StrUtil.isNotBlank(carDetails.getVehicleModelCode())) // 过滤掉空值
                .collect(Collectors.toMap(ApplyCarDetails::getApplyNo, ApplyCarDetails::getVehicleModelCode));
        // 1、针对新的数据，设置车辆型号代码
        if (existingRecords.isEmpty()) {
            recordsForSave.forEach(record -> {
                String vehicleModelCode = carDetailsMap.get(record.getApplyNo());
                record.setVehicleModelCode(vehicleModelCode);
                record.setPushTime(queryTime);
            });
            return;
        }
        // 建立 applyNo -> id 的映射
        Map<String, Long> applyNoIdMap = existingRecords.stream().collect(Collectors.toMap(StatusChangeHistory::getApplyNo, StatusChangeHistory::getId));
        // 2、针对修改的数据，设置车辆型号代码和 id
        recordsForSave.forEach(record -> {
            String vehicleModelCode = carDetailsMap.get(record.getApplyNo());
            record.setVehicleModelCode(vehicleModelCode);
            Long id = applyNoIdMap.get(record.getApplyNo());
            if (id != null) {
                // 这里设置id是避免后续直接插入
                record.setId(id);
            }
            record.setPushTime(queryTime);
        });
    }


    /**
     * 异步保存或更新数据。
     * @param recordsForSave recordsForSave
     */
    private void asyncSaveOrUpdateBatch(List<StatusChangeHistory> recordsForSave) {
        // 异步保存数据
        CompletableFuture.runAsync(() -> {
            try {
                statusChangeHistoryService.saveOrUpdateBatch(recordsForSave); // 保存或更新数据
            } catch (Exception e) {
                log.error("推送数据到腾势系统-异步保存数据", e);
            }
        });
    }

    /**
     * 推送数据到腾势系统。
     *
     * @param records 要推送的数据列表
     */
    private void pushData(List<StatusChangeHistory> records) {
        List<PushOrderStatusVo> recordsForPush = records.stream()
                .map(record -> {
                    PushOrderStatusVo vo = new PushOrderStatusVo();
                    vo.setApplyNo(record.getApplyNo());
                    vo.setApplyStatus(ApplyStatusPushEnum.getDescriptionByCode(record.getApplyStatus()));
                    vo.setContractStatus(ContractStatusPushEnum.getDescriptionByCode(record.getContractStatus()));
                    vo.setVehicleModelCode(record.getVehicleModelCode());
                    vo.setPushTime(record.getPushTime());
                    vo.setTel(record.getTel());
                    return vo;
                })
                .collect(Collectors.toList());
        log.info("推送数据到腾势系统recordsForPush= {}", JSONUtil.parse(recordsForPush));
        try {
            // 推送数据到腾势系统, 腾势接口未提供，暂时跳过
            IResponse<PushLoanOrderVo> response = tsService.pushOrderStatusToTs(recordsForPush);
            log.info("推送数据到腾势系统recordsForPush: {}, Response: {}", JSONUtil.parse(recordsForPush), JSONUtil.parse(response));
        } catch (Exception e) {
            log.error("推送数据到腾势系统record: {}, Error: {}. StackTrace: {}",
                    JSONUtil.parse(records), e.getMessage(), Arrays.toString(e.getStackTrace()), e);
        }
    }

    /**
     * 执行初始查询以获取数据。
     *
     * @param page 页码
     * @param batchSize 每页的数量
     * @param queryTime 查询的时间点
     * @return 查询结果
     */
    private List<StatusChangeHistory> findRecordsForInitialQuery(int page, int batchSize, Date queryTime) {
        int offset = page * batchSize;
        log.info("Executing initial query for records. Page: {}, Batch Size: {}, Offset: {}, queryTime: {}", page, batchSize, offset, queryTime);
        return applyOrderInfoService.findRecordsForInitialQuery(queryTime, batchSize, offset);
    }
}
