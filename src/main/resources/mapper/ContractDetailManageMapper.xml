<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruicar.afs.cloud.afscase.writeoff.mapper.ContractDetailManageMapper">

    <select id="selectAllDetailManage"
            resultType="com.ruicar.afs.cloud.afscase.writeoff.entity.WriteOffContractDetailManage">
        select contract.apply_no,contract.contract_no,cbi.cust_name_repeat,cbi.business_type,cbi.belonging_capital,
        fcd.product_id,fcd.product_name,fcd.loan_amt,fcd.loan_term,fcd.cust_rate,fcd.discount_type,
        channel.channel_code,channel.channel_full_name,channel.channel_province,channel.channel_city,channel.spa_supplier_code,channel.channel_id,channel.channel_belong
        from case_contract_info contract
        left join case_base_info cbi on cbi.apply_no=contract.apply_no
        left join fin_cost_details fcd on fcd.apply_no=cbi.apply_no and fcd.cost_type = 'mainPrd' and fcd.del_flag='0'
        left join case_channel_info cci on cci.apply_no=fcd.apply_no and cci.del_flag='0'
        left join channel_base_info channel on channel.channel_code=cci.dealer_no and channel.del_flag='0'
        <where>
            contract.service_fee_check='0' and contract.del_flag='0'
            <if test="query.contractNoList != null and query.contractNoList.size() > 0">
                and contract.contract_no in
                <foreach collection="query.contractNoList" item="contractNo" separator="," open="(" close=")">
                    #{contractNo}
                </foreach>
            </if>
        </where>
    </select>

    <select id="writeOffWorkList" resultType="com.ruicar.afs.cloud.afscase.writeoff.vo.LaunchWorkVo">
        select DISTINCT
        pool.task_id,
        pool.task_node_id,
        pool.business_no,
        pool.template_name,
        pool.create_time,
        pool.flow_package_id,
        pool.user_defined_index,
        pool.flow_template_id,
        pool.assign,
        pool.task_node_name,
        pool.process_instance_id,
        info.case_no,
        info.organ_name
        FROM workflow_task_info pool
        left join write_off_base_info info on pool.business_no= info.case_no

        <where>
            pool.del_flag = '0'
            and pool.status = 'active'
            <if test="condition.userDefinedIndex != null and condition.userDefinedIndex != ''">
                and pool.user_defined_index = #{condition.userDefinedIndex}
            </if>

            <if test="condition.flowPackageId != null and condition.flowPackageId !=''">
                and pool.flow_package_id = #{condition.flowPackageId}
            </if>

            <if test="condition.flowTemplateId != null and condition.flowTemplateId != ''">
                and pool.flow_template_id = #{condition.flowTemplateId}
            </if>

            <if test="condition.assign != null and condition.assign != ''">
                and pool.assign = #{condition.assign}
            </if>

            <if test="condition.businessNo!= null and condition.businessNo!= ''">
                and pool.business_no = #{condition.businessNo}
            </if>

            <if test="condition.templateName != null and condition.templateName !=''">
                and pool.template_name = #{condition.templateName}
            </if>
            <if test="condition.taskNodeName != null and condition.taskNodeName !=''">
                and pool.task_node_name = #{condition.taskNodeName}
            </if>
            <if test="condition.organName != null and condition.organName != ''">
                and info.organ_name like concat('%',#{condition.organName},'%')
            </if>
        </where>
        order by pool.create_time desc
    </select>
</mapper>
