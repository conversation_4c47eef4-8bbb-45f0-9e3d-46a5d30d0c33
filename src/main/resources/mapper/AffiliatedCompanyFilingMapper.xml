<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruicar.afs.cloud.afscase.mq.mapper.AffiliatedCompanyFilingMapper">

    <select id="queryApproveTaskLaunchList" resultType="com.ruicar.afs.cloud.afscase.mq.vo.AffiliatedCompanyFilingVo">
        select acf.channel_name,
            acf.affiliated_name,
            acf.filing_type,
            acf.id,
            acf.filing_product_group,
            assign.create_time createDate,
            assign.task_id,
            assign.assign,
            assign.task_node_name,
            assign.process_instance_id
        from workflow_task_info assign
        left join affiliated_company_filing acf on acf.id = assign.business_no
        where assign.del_flag = '0' and acf.del_flag = '0'
          and assign.status = 'active'
          and assign.assign = #{condition.username}
          and assign.flow_template_id = #{condition.flowTemplateId}
        <if test="condition.channelName != null and condition.channelName != ''">
            and acf.channel_name = #{condition.channelName}
        </if>
        <if test="condition.affiliatedName != null and condition.affiliatedName != ''">
            and acf.affiliated_name = #{condition.affiliatedName}
        </if>
    </select>
</mapper>
