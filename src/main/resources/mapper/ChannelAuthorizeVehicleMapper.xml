<?xml version="1.0" encoding="UTF-8"?>


<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruicar.afs.cloud.apply.channlvehicle.mapper.ChannelAuthorizeVehicleMapper">

    <!-- add by ice 根据渠道id 删除信息-->
    <delete id="delChannelVehicleByChannel" >
        DELETE  FROM `channel_authorize_vehicle`  WHERE channel_id = #{channelId}
    </delete>

    <!-- add by ice 根据渠道id 和业务类型删除信息-->
    <delete id="delChannelVehicleByChannelIdAndType" >
        DELETE  FROM `channel_authorize_vehicle`  WHERE channel_id = #{channelId} and business_type= #{type}
    </delete>

</mapper>