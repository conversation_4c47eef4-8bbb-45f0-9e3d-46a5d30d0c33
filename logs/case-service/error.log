2025-07-23 14:45:37,428 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: case-service
2025-07-23 14:45:37,429 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:37,429 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: org.springframework.context.annotation.AnnotationConfigApplicationContext@2d82408, started on Wed Jul 23 14:45:36 CST 2025
2025-07-23 14:45:37,433 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 38 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, bootstrapImportSelectorConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, com.alibaba.cloud.nacos.NacosConfigBootstrapConfiguration, nacosConfigProperties, nacosConfigManager, nacosPropertySourceLocator, org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, spring.cloud.config-org.springframework.cloud.bootstrap.config.PropertySourceBootstrapProperties, org.springframework.cloud.bootstrap.encrypt.EncryptionBootstrapConfiguration, keyProperties, environmentDecryptApplicationListener, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, configurationPropertiesBeans, configurationPropertiesRebinder, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, propertySourcesPlaceholderConfigurer, com.ulisesbocchio.jasyptspringboot.configuration.EncryptablePropertyResolverConfiguration, envCopy, lazyJasyptStringEncryptor, lazyEncryptablePropertyDetector, configPropsSingleton, lazyEncryptablePropertyFilter, lazyEncryptablePropertyResolver, com.ulisesbocchio.jasyptspringboot.configuration.CachingConfiguration, refreshScopeRefreshedEventListener, com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesConfiguration, enableEncryptablePropertySourcesPostProcessor, com.ulisesbocchio.jasyptspringboot.JasyptSpringCloudBootstrapConfiguration]
2025-07-23 14:45:54,767 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@247b949c
2025-07-23 14:45:54,767 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:54,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-user, started on Wed Jul 23 14:45:54 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:45:54,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:45:56,480 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@4f0a81f6
2025-07-23 14:45:56,480 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:56,480 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-tenant, started on Wed Jul 23 14:45:56 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:45:56,481 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:45:58,101 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@696d6c1c
2025-07-23 14:45:58,101 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:58,101 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-service, started on Wed Jul 23 14:45:58 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:45:58,101 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:45:58,223 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@64225277
2025-07-23 14:45:58,223 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:58,223 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-channel-api, started on Wed Jul 23 14:45:58 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:45:58,224 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:45:58,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@25fedeb8
2025-07-23 14:45:58,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:45:58,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-rule-info, started on Wed Jul 23 14:45:58 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:45:58,302 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:15,148 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110
                                -
                                mapper[com.ruicar.afs.cloud.afscase.loanspecialbusinessinfo.mapper.LoanSpecialBusinessInfoMapper.getSpecialBusinessDealWithTaskList] is ignored, because it exists, maybe from xml file
2025-07-23 14:46:16,584 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1085a882
2025-07-23 14:46:16,585 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:16,585 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-address-info-feign, started on Wed Jul 23 14:46:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:16,585 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:16,715 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@f033ec6
2025-07-23 14:46:16,716 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:16,716 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-FlowDesignerFeign, started on Wed Jul 23 14:46:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:16,716 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:16,756 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1fd57ae2
2025-07-23 14:46:16,756 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:16,756 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyServiceFeign, started on Wed Jul 23 14:46:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:16,757 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:16,951 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@12b4dc17
2025-07-23 14:46:16,951 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:16,951 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplySubmitFeign, started on Wed Jul 23 14:46:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:16,951 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:17,039 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@782a0e57
2025-07-23 14:46:17,039 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:17,039 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-user-info, started on Wed Jul 23 14:46:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:17,040 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:17,140 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2e1cee53
2025-07-23 14:46:17,141 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:17,141 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case-channel, started on Wed Jul 23 14:46:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:17,141 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:17,304 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1587e9a6
2025-07-23 14:46:17,304 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:17,304 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-boss-for-product, started on Wed Jul 23 14:46:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:17,304 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:17,859 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@752eeec
2025-07-23 14:46:17,860 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:17,860 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-api-admin-user-service, started on Wed Jul 23 14:46:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:17,860 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:18,237 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@18c35c73
2025-07-23 14:46:18,238 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:18,238 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyFeignService, started on Wed Jul 23 14:46:18 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:18,238 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:19,400 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@4d10338
2025-07-23 14:46:19,400 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:19,400 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-CaseToModelFeign, started on Wed Jul 23 14:46:19 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:19,400 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:19,588 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@30393db8
2025-07-23 14:46:19,588 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:19,588 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-file-service, started on Wed Jul 23 14:46:19 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:19,588 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:20,108 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@71644fdf
2025-07-23 14:46:20,108 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:20,108 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-afsProductInfo, started on Wed Jul 23 14:46:20 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:20,108 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:21,096 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@53c4b0a9
2025-07-23 14:46:21,096 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:21,096 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyVehicleFeign-ApplyVehicleFeign, started on Wed Jul 23 14:46:21 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:21,096 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:23,370 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@70176150
2025-07-23 14:46:23,371 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:23,371 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case-admin-user, started on Wed Jul 23 14:46:23 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:23,371 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:23,774 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@225abd1e
2025-07-23 14:46:23,774 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:23,774 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case2assetsFeign, started on Wed Jul 23 14:46:23 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:23,774 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:24,065 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@10125d66
2025-07-23 14:46:24,065 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:24,065 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ProducePlanFeign-productPlan, started on Wed Jul 23 14:46:24 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:24,065 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:24,404 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1fbb2a97
2025-07-23 14:46:24,404 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:24,404 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyInfoFeign, started on Wed Jul 23 14:46:24 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:24,404 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:25,068 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7e7e5302
2025-07-23 14:46:25,068 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:25,068 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-afs-flow-run, started on Wed Jul 23 14:46:25 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:25,068 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:25,359 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3bf4390d
2025-07-23 14:46:25,359 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:25,360 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-vehicle-service, started on Wed Jul 23 14:46:25 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:25,360 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:25,506 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@10b6fbab
2025-07-23 14:46:25,506 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:25,506 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-location-info, started on Wed Jul 23 14:46:25 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:25,507 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:26,374 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@20aaa6f5
2025-07-23 14:46:26,374 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:26,375 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-flow-group-mana, started on Wed Jul 23 14:46:26 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:26,375 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:26,444 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3d7081f4
2025-07-23 14:46:26,444 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:26,444 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-afs-flow-task, started on Wed Jul 23 14:46:26 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:26,444 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:26,488 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7c4921f1
2025-07-23 14:46:26,488 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:26,488 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-applyRemindFeign, started on Wed Jul 23 14:46:26 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:26,488 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:26,927 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@74be2d81
2025-07-23 14:46:26,927 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:26,927 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ArchiveFeignService, started on Wed Jul 23 14:46:26 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:26,927 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:30,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@60617c46
2025-07-23 14:46:30,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:30,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-report-feign, started on Wed Jul 23 14:46:30 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:30,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:31,132 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7c6497f6
2025-07-23 14:46:31,132 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:31,132 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyAdminFeign-ApplyAdminFeign, started on Wed Jul 23 14:46:31 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:31,132 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:31,407 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6598309b
2025-07-23 14:46:31,407 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:31,408 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-afs-flow-task-history, started on Wed Jul 23 14:46:31 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:31,408 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:32,093 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6da2be07
2025-07-23 14:46:32,093 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:32,093 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case-product, started on Wed Jul 23 14:46:32 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:32,093 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:33,238 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2ca39a8b
2025-07-23 14:46:33,239 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:33,239 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-TsysParamConfigFeign, started on Wed Jul 23 14:46:33 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:33,239 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:33,566 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@61cab492
2025-07-23 14:46:33,566 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:33,566 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-getAddressByCodesFeign, started on Wed Jul 23 14:46:33 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:33,566 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:34,495 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@5aa0771d
2025-07-23 14:46:34,495 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:34,495 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-pigeon-location-context, started on Wed Jul 23 14:46:34 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:34,495 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:36,232 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3ed31dbf
2025-07-23 14:46:36,232 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:36,232 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-invoice-write, started on Wed Jul 23 14:46:36 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:36,232 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:36,563 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3149e0db
2025-07-23 14:46:36,563 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:36,563 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ServiceChargeFeign, started on Wed Jul 23 14:46:36 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:36,564 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:36,725 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1f5cb417
2025-07-23 14:46:36,726 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:36,726 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case-to-channel, started on Wed Jul 23 14:46:36 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:36,726 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:36,778 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6d378343
2025-07-23 14:46:36,778 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:36,778 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ServiceFeeFeign, started on Wed Jul 23 14:46:36 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:36,778 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:37,863 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@629994ae
2025-07-23 14:46:37,863 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:37,863 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyChannelUserFeign-ApplyChannelUserFeign, started on Wed Jul 23 14:46:37 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:37,863 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:38,115 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@32ef8290
2025-07-23 14:46:38,115 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:38,115 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-afs-contract-basic-biz, started on Wed Jul 23 14:46:38 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:38,116 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:38,750 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@201ef0d0
2025-07-23 14:46:38,750 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:38,751 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-commonOuter, started on Wed Jul 23 14:46:38 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:38,752 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:43,249 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2c55fa70
2025-07-23 14:46:43,249 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:43,249 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-case-risk, started on Wed Jul 23 14:46:43 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:43,250 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:43,778 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@42af18e3
2025-07-23 14:46:43,779 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:43,779 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-basic-outer, started on Wed Jul 23 14:46:43 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:43,779 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:43,981 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@d22747f
2025-07-23 14:46:43,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:43,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-basic-invoice-write, started on Wed Jul 23 14:46:43 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:43,982 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:44,756 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@35af2487
2025-07-23 14:46:44,757 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:44,757 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-vehicle-feign-001, started on Wed Jul 23 14:46:44 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:44,757 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:45,337 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1e7ebae3
2025-07-23 14:46:45,337 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:45,338 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-assetChangForApply-AssetChangForApplyFeign, started on Wed Jul 23 14:46:45 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:45,338 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:47,184 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@20b60f8c
2025-07-23 14:46:47,185 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:47,185 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-common, started on Wed Jul 23 14:46:47 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:47,185 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:47,346 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7d9bc433
2025-07-23 14:46:47,346 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:47,346 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-device-service, started on Wed Jul 23 14:46:47 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:47,347 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:49,032 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7e6e5d94
2025-07-23 14:46:49,032 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:49,032 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-CaseToAccountFeign, started on Wed Jul 23 14:46:49 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:49,032 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:49,113 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@439342a
2025-07-23 14:46:49,113 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:49,114 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ApplyCustInfoFeign, started on Wed Jul 23 14:46:49 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:49,114 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,070 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1f59b9d3
2025-07-23 14:46:50,071 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,071 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-workflow-service, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,071 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,228 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@651c471f
2025-07-23 14:46:50,228 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,228 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-tokens, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,228 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 15 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, afsFeignFormSupport, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, feignFormEncoder, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,802 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@4a1f8f59
2025-07-23 14:46:50,803 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,803 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sdkProductApply, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,803 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,873 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@c335f37
2025-07-23 14:46:50,873 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,874 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-OcrServeVatInvoiceFeign, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,874 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,937 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@5a08e5ec
2025-07-23 14:46:50,937 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,937 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-OcrServiceInvoiceInformationFeign, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,938 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:50,995 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7782b6f0
2025-07-23 14:46:50,995 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:50,995 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-serviceToken, started on Wed Jul 23 14:46:50 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:50,995 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:46:51,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3c976d5d
2025-07-23 14:46:51,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:46:51,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-admin-service, started on Wed Jul 23 14:46:51 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:46:51,768 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:02,132 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->389341f906cd495996af21f397d6d89d] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:47:08,518 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: case-service-1
2025-07-23 14:47:08,518 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:08,519 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0, started on Wed Jul 23 14:45:40 CST 2025, parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@2d82408
2025-07-23 14:47:08,519 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 2349 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, afsCaseApplication, bootstrapApplicationListener.BootstrapMarkerConfiguration, org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory, applyConfig, businessConfController, carThreehundredBackController, caseAdminUserController, imageManageController, printManageController, printManageServiceImpl, applyAffiliatedUnitController, applyAffiliatedUnitServiceImpl, approveAuthorityServiceImpl, affiliatedCompanyRuleController, approveInspectionRuleController, affiliatedCompanyRuleServiceImpl, approveInspectionInfoServiceImpl, approveInspectionRuleServiceImpl, approveMakeLabelController, loanLabelInfoController, approveMakeLabelServiceImpl, loanLabelInfoServiceImpl, approveMonitorController, approveMonitorServiceImpl, approveOcrController, approveOcrBatchServiceImpl, approveOcrCompareServiceImpl, approveOcrDetailServiceImpl, approveOpinionRemarksController, caseApproveOpinionRemarksServiceImpl, approvePrevController, caseApprovePrevInfoServiceImpl, preCaseCreditRuleServiceImpl, preCaseCustInfoServiceImpl, preCaseOperationRecordServiceImpl, preCaseOrderInfoServiceImpl, approveRecordController, approveSalvagedController, approveTaskController, approveTeleRecordController, caseBaseInfoController, caseInfoTaskController, caseInfoTaskErrorController, comAttachmentFileController, commonPoolTaskController, dealWithTaskController, groupLeaderTaskController, insDealWithPoolController, insDefectPoolController, insLeaderTaskPoolController, insPublicPoolController, insReplenishPoolController, insWaitCheckPoolController, insWaitPoolController, inspectionSuggestController, reconsiderationController, specialPoolTaskController, taskHistoryController, taskPoolController, telRecordController, approveTaskServiceImpl, approveSalvagedServiceImpl, approveTeleRecordServiceImpl, caseApproveRecordServiceImpl, caseContractLockInfoServiceImpl, casePassRecordServiceImpl, caseSubmitRecordServiceImpl, faceReviewHandlerServiceImpl, inspectionSuggestServiceImpl, saleCustParamInfoServiceImpl, workProcessScheduleInfoServiceImpl, workProcessScheduleInfoTempServiceImpl, workTaskPoolHistoryServiceImpl, workTaskPoolServiceImpl, approveFieldVisitInfoServiceImpl, archiveApiController, archiveController, archiveApiServiceImpl, custInfoServiceImpl, autoAuditOcrProperties, qualityAnalyzeProperties, caseLoanApproveResultController, leaveDayInfoController, loanAutoApproveBackController, loanAutoApproveController, caseLoanApproveResultServiceImpl, leaveDayInfoServiceImpl, loanAutoApproveBackServiceImpl, loanAutoApproveServiceImpl, approveRiskTipsRuleController, attachmentAuditInformationController, cardDetectRecordChangeController, caseManualReviewInfoController, loanRiskTipsRuleController, riskTipsController, activeServiceBox, loanServiceBox, propertyLicenseAiRecognitionService, approveRiskTipsRuleServiceImpl, attachmentAuditInformationServiceImpl, businessManualReviewInfoServiceImpl, cardDetectRecordChangeServiceImpl, caseLiangfuApplyServiceImpl, caseManualReviewInfoServiceImpl, loanRiskTipsRuleServiceImpl, ruleAtomDataServiceImpl, caseAutoBackHolidayController, caseAutoBackHolidayServiceImpl, caseBackToPartnersDetailServiceImpl, caseBackToPartnersInfoServiceImpl, electronBadgeController, electronBadgeInfoServiceImpl, carController, contractController, customerController, financeController, contractServiceImpl, customerServiceImpl, financeServiceImpl, vehicleServiceImpl, applyBankCardController, applyBankTypeCiccController, applyCarInvoiceController, softPhoneCallController, softPhoneCallServiceImpl, callCenterRecordController, callCenterRecordServiceImpl, carCreeperController, crawlCarPriceServiceImpl, carGpsManageController, carGpsAddressServiceImpl, carGpsApplyServiceImpl, carGpsDeviceServiceImpl, carGpsImgServiceImpl, gpsManageServiceImpl, carModelMonitorController, carModelMonitorServiceImpl, scopedTarget.carrierPigeonConfiguration, carrierPigeonConfiguration, carrierPigeonController, carrierPigeonRuleDetailController, carrierPigeonRequestService, carrierPigeonRuleDetailServiceImpl, carrierPigeonServiceImpl, caseCarrierPigeonFileSnapshotServiceImpl, caseCarrierPigeonServiceRecordServiceImpl, caseChannelPigeonDetailServiceImpl, casePigeonLocationInfoServiceImpl, caseMainInfoController, caseMainInfoServiceImpl, caseBusinessLicenceInfoController, caseDrivingLicenceInfoController, caseOcrController, caseApplyResidenceServiceImpl, caseBusinessLicenseInfoServiceImpl, caseCertificateInfoServiceImpl, caseDrivingLicenceInfoServiceImpl, caseIntelligentApproveRecordServiceImpl, caseIntelligentResultServiceImpl, deepSeekDrivingInfoServiceImpl, deepSeekOperatorInfoServiceImpl, archivedBaseInfoController, blacklistController, carDealerController, channelAffiliatedUnitsController, channelBaseInfoController, channelCaseInfoController, channelNetworkController, channelQuotaController, channelQuotaReleaseLogController, channelReceivablesController, channelReport, channelUseCaseController, channelVehicleController, channelWitnessInfoController, copyController, drawerPartyController, subBranchElecBankNoRelServiceImpl, blacklistServiceImpl, carDealerServiceImpl, channelAccountInfoServiceImpl, channelAffiliatedUnitsRelServiceImpl, channelAffiliatedUnitsServiceImpl, channelAuthorizeRegionServiceImpl, channelAuthorizeVehicleServiceImpl, channelBaseInfoServiceImpl, channelCoopeCardealerServiceImpl, channelCoopeDrawerPartyServiceImpl, channelMainBrandServiceImpl, channelNetworkServiceImpl, channelOnlineInfoServiceImpl, channelQuotaInfoServiceImpl, channelQuotaReleaseLogServiceImpl, channelReceivableAccountServiceImpl, channelRiskInfoServiceImpl, channelShareholderInfoServiceImpl, channelStockHolderServiceImpl, channelVoucherInfoServiceImpl, channelWitnessInfoImpl, commonCarDealerServiceImpl, directCarDealerServiceImpl, drawerPartyServiceImpl, subBranchElecBankNoRelCheckServiceImpl, channelQuotaTask, qualityLevelTask, residualMarginTask, caseConfig, aggregateServiceImpl, car300ServiceImpl, carCreeperServiceImpl, commonUserServiceImpl, intelligentVoiceServiceImpl, splittingTimeServiceImpl, userInfoServiceImpl, serviceBox, checkEquals, effectiveOrInvalidByDatePublishComponent, printEffectiveOrInvalidByDatePublishComponent, confCaseParamServiceImpl, confTemplateConfigurationController, confUrgentDealerController, confUrgentDealerDetailsController, confUrgentDealerDetailsServiceImpl, confUrgentDealerServiceImpl, aggregationController, aggregationServiceImpl, creditOptionInfoController, creditOptioneController, creditOptionInfoServiceImpl, CreditOptionService, creditTemplateController, CreditTemplateService, dealCorrelationController, dealCorrelationServiceImpl, OutlineAdapter, OutlineAutomaticPoolCallback, outlineUserAdapter, channelOutLineController, outlineFlowApproveController, channelOutLineServiceImpl, outlineWorkFlowServiceImpl, dictDataCaseController, dictDataCaseServiceImpl, baseComponent, dispatchComponent, abnormalRecoveryServcieImpl, dispatchInspectionSortService, dispatchInspectionStartService, dispatchPriorLoanService, dispatchPriorService, dispatchServcieImpl, dispatchStartLoanService, dispatchStartService, dispatchTimeBeforService, dispatchTimeBeforeLoanService, dispatchTopLoanSecondaryService, dispatchTopLoanService, dispatchTopService, loanCannelAbnormalRecoveryServcieImpl, loanDataPostAbnormalRecoveryServcieImpl, workTimeSync, caseEffectRecordServiceImpl, caseEffectRecycleRecordServiceImpl, executeProcessComponent, executeWorkController, executeWorkInfoController, executeWorkTaskController, fastLoanProcessor, loanCreditProcessor, loanOcrProcessor, loanOverdueProcessor, loanOvertimeProcessor, loanPreEndProcessor, loanSignProcessor, loanSuspendProcessor, loanTitleProcessor, executeWorkInfoServiceImpl, executeWorkTaskProcessor, executeWorkTaskServiceImpl, executeServiceBox, externalAggregationController, externalLoanAmountServiceImpl, externalUsedCarServiceImpl, fastLoanChannelController, fastLoanItemConfigController, fastLoanRuleController, fastLoanChannelServiceImpl, fastLoanItemConfigServiceImpl, fastLoanRuleServiceImpl, finCostDetailsLogServiceImpl, focusCarRuleConfigController, focusCarRuleConfigServiceImpl, fraudTwiceController, caseApproveFraudServiceImpl, gradeModelAO, gradeModelElementController, gradeModelElementListController, gradeModelInfoController, gradeModelResultController, gradeBigDataQuotaServiceImpl, gradeModelCalcService, gradeModelConfServiceImpl, gradeModelElementListServiceImpl, gradeModelElementServiceImpl, gradeModelInfoServiceImpl, gradeModelResultDtlServiceImpl, gradeModelResultModifyLogServiceImpl, gradeModelResultServiceImpl, gradeModelResultSubDtlServiceImpl, gradeModelSubDataelementServiceImpl, gradeScoreRelaServiceImpl, userCollocationNewController, handlingInfoServiceImpl, iamLoginProperties, iamLoginController, imageQueryController, imageManageServiceImpl, scopedTarget.baiduMapConfiguration, baiduMapConfiguration, faceIdentifyConfig, policyCheckConfig, caseBankInfoController, caseBaseInfoFeignController, caseBlackListController, caseBusinessLicenseArtificialController, caseCarDepositController, caseCarInfoController, caseChannelInfoController, caseContractController, caseCostInfoController, caseCustCallRemarkHistoryController, caseCustInfoController, caseDataPostInfoController, caseEnterpriseCustomerDetailsController, caseProductController, caseRepaymentController, caseSecondaryRepaymentController, casePayeeProgressServiceImpl, casePayeeSecondaryServiceImpl, approveAssetsChangeServiceImpl, caseBaseInfoServiceImpl, caseBusinessLicenseArtificialServiceImpl, caseBusinessLicenseServiceImpl, caseCarInfoServiceImpl, caseCarStyleDetailServiceImpl, caseChannelInfoServiceImpl, caseChannelUniteInfoServiceImpl, caseContractInfoServiceImpl, caseCostInfoOriginalServiceImpl, caseCostInfoServiceImpl, caseCustAddressServiceImpl, caseCustCallRemarkHistoryServiceImpl, caseCustCallRemarkServiceImpl, caseCustChangeRecordServiceImpl, caseCustCompanyServiceImpl, caseCustContactServiceImpl, caseCustHistoryServiceImpl, caseCustIndividualServiceImpl, caseCustInfoServiceImpl, caseDataPostInfoServiceImpl, caseDiscountDetailServiceImpl, caseDueDiligenceServiceImpl, caseEnterpriseCustomerDetailsServiceImpl, caseFacePhotoInfoServiceImpl, caseFinMainInfoServiceImpl, caseFinRentAdjustDetailsServiceImpl, caseFinancingItemsServiceImpl, caseLoanAwaitInfoServiceImpl, caseLoanCannelInfoServiceImpl, casePayeeInfoServiceImpl, casePolicyCheckRecordServiceImpl, casePolicyCheckServiceImpl, caseProductServiceImpl, caseReceiptInfoServiceImpl, caseRedundantInfoServiceImpl, caseRemarkRecordServiceImpl, depositConfigServiceImpl, electronuclearArtificialServiceImpl, finCostDetailsServiceImpl, secondaryRepaymentServiceImpl, caseOcrInterfaceRecordServiceImpl, invoiceContext, invoiceController, automaticJob, caseCallBackJob, caseEffectTimeMonitorJob, casePolicyCheckJob, caseSumEffectTimeMonitorJob, channelOutLineJob, geoAddressTask, sendApprovedSmsJob, sendCaseApprovedSmsJob, carrierPigeonServiceJob, autoUpdateBankLoanStatusJob, bankSignFileDownloadJob, cannelLoanJob, caseExamineGps, caseLoanJob, dealerFirstMortgageJob, downloadImgGps, loanActiveJob, loanApproveDoneJob, loanJobServiceImpl, mortgageJob, phoneJob, startWorkFlowJob, writeOffRuleJob, loanActivateController, contractActiveProcess, loanActivateServiceImpl, loanActivateRulesController, loanActivateRulesServiceImpl, backReasonAllocationController, caseBackConfigurationController, invoiceInspectionController, loanApproveController, loanBackReasonController, loanDataPostNewApproveController, addedFinancingItemsServiceImpl, carInsuranceInfoServiceImpl, carInvoiceInfoServiceImpl, caseBackConfigurationServiceImpl, caseBackReasonInfoServiceImpl, caseCarDepositServiceImpl, casePriorityChangeServiceImpl, casePriorityRecordServiceImpl, loanApproveServiceImpl, loanBankCardInfoServiceImpl, loanDataPostNewApproveServiceImpl, loanWorkflowServiceImpl, archivedTaskController, loanApproveRecordController, loanApproveTaskController, loanRepairApproveTaskController, loanSubmitAssignTaskController, loanWaitTaskController, passRepairTaskController, loanApproveTaskServiceImpl, loanRepairApproveTaskServiceImpl, loanWaitTaskServiceImpl, loanBackAssignRecordServiceImpl, loanDealerFirstMortgageController, loanDealerFirstMortgageServiceImpl, loanExaminationBatchInfoServiceImpl, loanExaminationExcelServiceImpl, loanExaminationInfoServiceImpl, loanExaminationRetrospectRecordServiceImpl, loanFlawFixController, loanFlawFixDetailController, caseManageAssetChangeServiceImpl, loanFlawFixDetailServiceImpl, loanFlawFixServiceImpl, loanFlawFixTaskController, loanGpsRuleController, loanGpsRuleInfoServiceImpl, loanInvoiceCheckRecordServiceImpl, loanModeRuleController, loanModeRuleInfoServiceImpl, loanOverTimeRuleController, loanOverTimeRuleServiceImpl, loanSummaryInfoController, decisionEngineResultsInfoServiceImpl, loanSummaryInfoServiceImpl, loanReviewRuleController, loanReviewRuleServiceImpl, loanSecondaryFraudInfoController, loanSecondaryFraudInfoServiceImpl, caseSignRelationServiceImpl, loanSpecialBusinessInfoController, loanSpecialBusinessInfoServiceImpl, loanSpecialOpinionInfoServiceImpl, loanSuspendRuleController, loanSuspendRuleServiceImpl, loanAutoLabelController, manualLabelController, manualLabelServiceImpl, costInfoServiceImpl, marginBusinessServiceImpl, marginInfoServiceImpl, maxLoanParamController, maxLoanParamServiceImpl, caseNoticeInfoController, caseSmsNoticeController, loanNoticeInfoController, messageTemplateController, caseNoticeInfoServiceImpl, caseSmsNoticeRecordServiceImpl, caseSmsSendRecordServiceImpl, caseSmsTemplateServiceImpl, loanNoticeInfoServiceImpl, massageTemplateServiceImpl, agingMonitorController, inspectionMonitorController, agingMonitorServiceImpl, inspectionMonitorServiceImpl, approveAttachmentServiceImpl, approveInformInfoServiceImpl, approveLoanInfoServiceImpl, failedMqInfoManagementController, caseSysCode, affiliationInfoProcessor, affiliationRelStatusProcessor, affiliationStatusProcessor, applyOrderAutoCancelProcessor, approveMortgageProxyProcessor, approvePrevProcessor, approveSpecialProcessor, attachmentFileSyncProcessor, callBackApplyProcessor, callBackResultProcessor, caseDealProcessor, casePayeeProgressStatusProcessor, channelOnlineInfoProcessor, channelQuotaUpdateProcessor, commomCarDealerProcessor, conditionalApprovalProcessor, contractActicateNoticeProcessor, flowRevisePauseProcessor, gpsApplyProcessor, loanApplyPayeeProgressProcessor, loanApplyProcessor, loanCannelProcessor, loanDataPostNewProcessor, loanDiscardProcessor, loanFlawStatusToDealerProcessor, loanSaveAssetChangeProcessor, loanSpecialBusinessProcessor, preToApplyUpdateCaseStatusCodeProcessor, reconsiderationProcessor, repealDealProcessor, saveFailedMessageProcessor, urgentCaseProcessor, witnessProcessor, caseMqAcceptRecordServiceImpl, caseMqCompareInfoServiceImpl, commissionMqSendServiceImpl, commonFailMqInfoServiceImpl, mortgageService, socialMortgageConfig, mortgageConfigController, mortgageController, vehicleMortgageRegistrationController, caseMortgageProxyInfoServiceImpl, caseMortgageRecordServiceImpl, mortgageCommonServiceImpl, mortgagePlaceConfigServiceImpl, vehicleMortgageRegistrationServiceImpl, caseCreditOptionConfController, caseCreditOptionController, caseCreditOptionConfServiceImpl, caseCreditOptionServiceImpl, caseConfParamController, caseConfParamServiceImpl, fftConfig, caseRepayPlanController, postLoanImageController, caseRepayPlanServiceImpl, postLoanImageServiceImpl, approveWorkflowServiceImpl, loanFlawFixWorkflowServiceImpl, loanSpecialBusinessWorkflowServiceImpl, reconsiderationWorkflowServiceImpl, workExceptionInfoServiceImpl, reconsiderationProcessServiceImpl, caseChangeRecordController, businessChangeRecordServiceImpl, caseChangeAtomServiceImpl, caseChangeRecordServiceImpl, channelDgdRelationController, remindController, remindServiceImpl, threadPoolConfig, financeReportController, orderReportController, zxReportController, creditInquiryRecordsServiceImpl, orderReportServiceImpl, zxReportServiceImpl, antiFraudHandController, antiFraudPushController, caseFaceReviewInfoController, creditApproveController, decisionEngineController, decisionHandInvokeController, liangFuApplyController, remindVerificationRuleController, remindVerificationRuleMappingController, tortoiseApplyController, tortoiseConfigController, tortoiseNoticeProcessorImpl, dataLiangFuService, antiFraudServiceImpl, baiHangSpendInfoServiceImpl, caseFaceReviewInfoServiceImpl, caseLiangFuApplyServiceImpl, caseLiangFuServiceImpl, caseTortoiseApplyServiceImpl, caseTortoiseDecisionHandServiceImpl, caseTortoiseExposureExceptServiceImpl, caseTortoiseFraudHandServiceImpl, caseTortoiseFraudPushServiceImpl, caseTortoisePrevServiceImpl, caseTortoiseServiceImpl, dataLiangFuQueryServiceImpl, dataQueryServiceImpl, dayBookResolveServiceImpl, decisionEngineLoanServiceImpl, dwCreditInfoServiceImpl, manualReviewInformationServiceImpl, remindVerificationRuleMappingServiceImpl, remindVerificationRuleServiceImpl, thirdDataServiceImpl, tongDunServiceAntifraudImpl, tongDunServiceImpl, tongDunServiceOriginalImpl, salesManageController, saleTeamInfoServiceImpl, saleTeamUserServiceImpl, softPhoneController, softPhoneRecordServiceImpl, softPhoneUserServiceImpl, stepController, caseStepParamServiceImpl, vehicleInfoController, vehicleModelController, importFileTraceServiceImpl, vehicleImportFileDetailImpl, vehicleInfoServiceImpl, vehicleModeValid, voiceInspectionController, voiceInspectionServiceImpl, workflowHelper, applyOrderApprovalCompletedCallback, approveLeaderSelectorAdapter, approveProcessEndCallback, approveSalvagedCallback, automaticCallback, channelServiceFeeCallback, commonException, commonTaskAssignCallback, commonTaskCreateCallback, commonUserOnlineCallback, custLevelUserAutoCallBack, custLevelUserCallback, finApprovalInsertCallBack, inspectionWorkflowCallback, loanAutoApproveCallBack, loanAwaitCallback, loanCancelCallback, loanCannelProcessEndCallback, loanDataPostNewProcessEndCallback, loanFileCallback, loanProcessEndCallback, loanRevokeWaitingTrigger, managerCandidateCallback, mortgageRevokeEndCallback, mortgageUntieRevokeEndCallback, reconsiderationEndCallBack, specialApproveCallback, specialLoanCallback, specialUserCallback, writeOffCbsCallback, writeOffDraftApprovalCallback, writeOffExtUpApplicationCallback, writeOffExtensionApplicationCallback, writeOffExtractAssignCallback, writeOffInputTaxCallback, writeOffInvoiceAssignCallback, writeOffRuAssignCallback, writeOffWorkEndCallback, writeOffWorkListCallback, caseFlowNodeProperties, caseLoanRentProperties, workflowController, workflowLoanCancelController, workflowTaskBindingParamsController, flowConfigProperties, flowAutoEnum, approveAuditEventListener, channelServiceFeeEventListener, loanAuditEventListener, loanCancelAuditEventListener, loanFileAuditEventListener, reconsiderationEventListener, specialAuditEventListener, caseLoanFinanceAutoServiceImpl, caseRiskCustInfoServiceImpl, loanCannelApproveServiceImpl, riskCreditServiceImpl, workflowProcessBusinessRefInfoServiceImpl, workflowTaskBindingParamsServiceImpl, workflowTaskInfoServiceImpl, workflowWrapperService, channelServiceFeeController, contractDetailManageController, taxWhiteListManageController, writeOffAccountCycleController, writeOffApportionController, writeOffApprovalInfoController, writeOffBaseInfoController, writeOffBaseInfoWorkController, writeOffBaseInvoiceRelController, writeOffChannelGroupController, writeOffContractDetailManageController, writeOffDeductController, writeOffFrozenInfoController, writeOffInvoiceInfoController, writeOffPermissionController, writeOffRuleController, writeOffApprovalProcessor, channelServiceFeeServiceImpl, contractDetailManageServiceImpl, financialWriteOffInvoiceServiceImpl, taxWhiteListManageMapperServiceImpl, writeOffAccountCycleDetailServiceImpl, writeOffAccountCycleServiceImpl, writeOffApportionDetailServiceImpl, writeOffApportionInfoServiceImpl, writeOffApprovalInfoServiceImpl, writeOffBaseInfoServiceImpl, writeOffBaseInvoiceRelServiceImpl, writeOffChannelGroupDetailServiceImpl, writeOffChannelGroupServiceImpl, writeOffContractDetailManageServiceImpl, writeOffDeductDetailServiceImpl, writeOffFrozenInfoServiceImpl, writeOffHistoryBaseInfoServiceImpl, writeOffHistoryBaseInvoiceRelServiceImpl, writeOffHistoryInvoiceInfoServiceImpl, writeOffInvoiceInfoServiceImpl, writeOffPermissionServiceImpl, writeOffRuleDetailServiceImpl, writeOffRuleServiceImpl, caseApiConfig, geniusInvoiceConfiguration, invoiceConfig, invoicingProperties, scopedTarget.newInvoiceProperties, newInvoiceProperties, billingResultQueryHandler, personOpenBillingHandler, invoicePushInvoiceServiceImpl, invoiceVerificationInvoiceServiceImpl, invoicingServiceImpl, newInvoicingServiceImpl, billingHandlerFactory, finDiscountCostServerImpl, finDiscountPlanServiceImpl, finPlanRateServiceImpl, finRentAdjustmentServiceImpl, finRepaymentPlanServiceImpl, finTermsDetailsServiceImpl, marginInfoCommonServiceImpl, applyCostDetailsServiceImpl, applyDiscountDetailsServiceImpl, applyFinancingItemsServiceImpl, applyRentAdjustDetailsServiceImpl, finMainInfoServiceImpl, finUiContentServiceImpl, fontsProperties, comPrintFormClassController, comPrintFormFieldMappingController, comPrintFormSealMappingController, comSealBaseInfoController, templateController, comPrintFormClassServiceImpl, comPrintFormFieldMappingServiceImpl, comPrintFormManageServiceImpl, comPrintFormSealMappingServiceImpl, comSealBaseInfoServiceImpl, signOfflineContext, signOfflineUtil, cfCaSignatureServiceImpl, subjectSignInfoServiceImpl, voucherSendPublishComponent, restTemplateConfig, voucherController, mqMessageQueueLogServiceImpl, voucherFlowServiceImpl, afsAlgorithmServiceImpl, ballonloanStrategy, context, equalProportionCalculateTypeServiceImpl, equalprincipalCalculateTypeServiceImpl, equalrentalCalculateTypeServiceImpl, financeCalculatorServiceImpl, interestBeforeCapitalTypeServiceImpl, segmentedTypeServiceImpl, unequalrentalCalculateTypeServiceImpl, gpsOleServiceImpl, gpsConfig, clmbConfig, carDetailServiceImpl, invoiceServiceImpl, dailyScheduleController, groupManageController, groupSceneController, postChangeLogController, postConfigController, regularValueController, userCollocationController, leavePostJob, shopCalendarJob, userLeavePostJob, baseHolidayServiceImpl, groupManageServiceImpl, groupSceneServiceImpl, groupUserServiceImpl, mainEventLogServiceImpl, opraHolidayServiceImpl, personalPowerParamServiceImpl, postChangeLogServiceImpl, postConfigServiceImpl, regularValueServiceImpl, seatPostConfigServiceImpl, userCollocationServiceImpl, fileProperties, fontProperties, templateProperties, afsUploadFileController, auditFileController, comAttaManageController, caseBaseInfoServiceImpl2, comAttaManageServiceImpl, comAttachmentFileServiceImpl, comAttachmentFileTempServiceImpl, comAttachmentFileUploadServiceImpl, comAttachmentManagementServiceImpl, comAttachmentRuleServiceImpl, fastDfsFileDownLoadServiceImpl, fastDfsTemplateServiceImpl, fileUploadCommonServiceImpl, ocrDiscernServiceImpl, signInterviewInfoServiceImpl, drawImg, imageUtil, pdfUtil, imageServiceImpl, batchDateCacheServiceImpl, flowNoticeAop, workFlowClientController, portalTaskAssignNotice, messageConfig, smsThreadPoolConfig, scopedTarget.messageSxServiceImpl, messageSxServiceImpl, riskControlServerConfig, riskControlTortoiseController, afsLiangFuControlServiceImpl, afsRiskControlServiceImpl, decisionEngineServiceImpl, callCenterServerConfig, callCenterServiceImpl, channelApiConfig, channelApiServiceImpl, thumbnailService, compressService, mergeService, waterMarkService, algorithmComponet, equalPrincipalRentalServiceImpl, equalprincipalServiceImpl, equalrentalServiceImpl, flexibleDiscountImpl, flexibleLoanServiceImpl, interestBeforeCapitalServiceImpl, ladderBorrowServiceImpl, structuredLoanImpl, amortizationMonthServiceImpl, discountAlgorithmServiceImpl, incomeplanAlgorithmServiceImpl, repaymentAlgorithmServiceImpl, afsExpressRunner, calcExpressRunner, redisCache, productCalcServiceImpl, productServiceImpl, bydOcrProperties, geniusAliyunOcrInvoiceConfiguration, geniusOcrServeVatInvoiceConfiguration, geniusOcrServiceInvoiceInformationConfiguration, scopedTarget.sinoOcrProperties, sinoOcrProperties, tencentOcrProperties, tmpRestTemplateConfig, scopedTarget.wleaseOcrConfiguration, wleaseOcrConfiguration, tencentOcrServiceImpl, cbsPaymentConfig, cbsSignConfig, ediConfig, ediDataConvertorHolder, ediAbcDeductReqConvertor, ediBocDeductReqConvertor, ediCmbDeductReqConvertor, ediCmbDeductRspConvertor, ediCmbQueryReqConvertor, ediCmbQueryRspConvertor, ediCmbRefundReqConvertor, ediCmbRefundRspConvertor, ediCmbSearchReqConverter, ediCmbSearchRspConverter, ediDeductReqDefaultConvertor, ediDeductRspDefaultConvertor, ediPayReqDefaultConvertor, ediPayRespDefaultConvertor, ediPayResultReqDefaultConvertor, ediPayResultRespDefaultConvertor, ediPaySearchReqDefaultConvertor, ediPaySearchRespDefaultConvertor, ediQueryReqDefaultConvertor, ediQueryRspDefaultConvertor, ediSearchReqDefaultConvertor, ediSearchRspDefaultConvertor, refundSearchReqDefaultConvertor, refundSearchRspDefaultConvertor, ediIcbcDeductReqConvertor, cardSignServiceImpl, cbsServiceImpl, ccbAccountStatementServiceImpl, bydDirectDealerProperties, bydDirecrtDealerServiceImpl, bydDirectDealerUtil, baiHangConfig, baiHangServiceImpl, checkInsuranceConfig, checkInsuranceServiceImpl, zhengXinProperties, applyZxReportCountServiceImpl, comReportFileServiceImpl, zxThirdServiceImpl, basicMqSendRecordServiceImpl, generatePdfFileUtils, contractBasicProperties, contractFeginConfig, fontConfig, scopedTarget.sftpPropertiesConfig, sftpPropertiesConfig, scopedTarget.xcSftpPropertiesConfig, xcSftpPropertiesConfig, bydEisoaProperties, bydEisoaServiceImpl, scopedTarget.openBankConfig, openBankConfig, scopedTarget.rentLoansConfig, rentLoansConfig, rentLoansServiceImpl, ftpConnectConfig, disposeInfoCapitalFileServiceImpl, deepSeekProperties, attorneyAutomaticRecognitionServiceImpl, businessAutomaticRecognitionServiceImpl, certificateAutomaticRecognitionServiceImpl, confTemplateConfigurationServiceImpl, deepseekIntelligentResultsServiceImpl, statementAutomaticRecognitionServiceImpl, bydBadgeProperties, electronBadgeServiceImpl, afsFeignClientConfiguration, resourceAuthExceptionEntryPoint, pms, scopedTarget.permitAllUrlProperties, permitAllUrlProperties, defaultSubAuthServiceImpl, afsUserDetailsServiceImpl, deadMessageController, deadMessageQueueServiceImpl, wleaseTokenServiceImpl, webSocketConfig, webSocketHandle, ioAsyncTaskExecutor, execTaskExecutor, dSTaskExecutor, org.springframework.aop.config.internalAutoProxyCreator, invoiceVerificationInvoiceService, remoteRestTemplate, simpleClientHttpRequestFactory, smsTaskExecutor, getExpressRunner, ocrInvoiceService, ocrServeVatInvoiceService, ocrServiceInvoiceInformationService, org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor, org.springframework.boot.context.internalConfigurationPropertiesBinderFactory, org.springframework.boot.context.internalConfigurationPropertiesBinder, org.springframework.boot.context.properties.BoundConfigurationProperties, org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter, com.afs.systems.ocr-serve-invoice-information-com.ruicar.afs.cloud.interfaces.wlease.ocr.config.GeniusOcrServiceInvoiceInformationProperties, loadBalancedRestTemplate, ignoreSslRestTemplate, org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration, objectPostProcessor, org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration, authenticationManagerBuilder, enableGlobalAuthenticationAutowiredConfigurer, initializeUserDetailsBeanManagerConfigurer, initializeAuthenticationProviderBeanManagerConfigurer, org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration, delegatingApplicationListener, webSecurityExpressionHandler, springSecurityFilterChain, privilegeEvaluator, conversionServicePostProcessor, org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration, requestDataValueProcessor, org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration$OAuth2ClientWebMvcSecurityConfiguration, org.springframework.security.config.annotation.web.configuration.OAuth2ClientConfiguration, org.springframework.security.config.annotation.web.configuration.HttpSecurityConfiguration, org.springframework.security.config.annotation.web.configuration.HttpSecurityConfiguration.httpSecurity, org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration, methodSecurityInterceptor, methodSecurityMetadataSource, preInvocationAuthorizationAdvice, oauth2FeignRequestInterceptor, com.ruicar.afs.cloud.common.core.security.component.AfsResourceServerAutoConfiguration, opaqueTokenIntrospector, clientJwtDecoder, securityFilterChain, ignoreUriFilter, org.springframework.retry.annotation.RetryConfiguration, org.springframework.scheduling.annotation.SchedulingConfiguration, org.springframework.context.annotation.internalScheduledAnnotationProcessor, com.ruicar.afs.cloud.common.job.configuration.AfsCloudJobConfiguration, feignAdminBizClient, afsJobExecutor, com.ruicar.afs.cloud.common.job.properties.AfsJobCloudProperties, org.springframework.boot.autoconfigure.AutoConfigurationPackages, default.com.ruicar.afs.cloud.afscase.AfsCaseApplication.FeignClientSpecification, ApplyAdminFeign-ApplyAdminFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.ApplyAdminFeign, ApplyVehicleFeign-ApplyVehicleFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.ApplyVehicleFeign, case2assetsFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.AssetsFeign, case-admin-user.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.CaseAdminUserFegin, ${com.ruicar.service-names.apply-server:afs-apply-biz}.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.CaseUseApplyServiceFeign, afs-contract-basic-biz.FeignClientSpecification, com.ruicar.afs.cloud.afscase.apply.fegin.CommonFeign, ApplyServiceFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.applyaffiliatedunit.feign.ApplyServiceFeign, ArchiveFeignService.FeignClientSpecification, com.ruicar.afs.cloud.afscase.archive.feign.ArchiveFeignService, ApplyInfoFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.autoaudit.feign.ApplyInfoFeign, pigeon-location-context.FeignClientSpecification, com.ruicar.afs.cloud.afscase.carrierpigeon.feign.ConfigFeign, case-channel.FeignClientSpecification, com.ruicar.afs.cloud.afscase.channel.feign.ChannelFeignService, CobwebDiagramFeign-CobwebDiagramFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.channel.feign.CobwebDiagramFeign, CaseToAccountFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.CaseToAccountFeign, case-to-channel.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.CaseToChannelFeign, CaseToModelFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.CaseToModelFeign, case-product.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.FinanceProductFeign, ${com.ruicar.service-names.device-server}.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.OrderContractFeign, ServiceChargeFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.common.feign.ServiceChargeFeign, ApplyChannelUserFeign-ApplyChannelUserFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.dealeroutline.feign.ApplyChannelUserFeign, ApplyFeignService.FeignClientSpecification, com.ruicar.afs.cloud.afscase.infomanagement.feign.ApplyContractFeign, case-risk.FeignClientSpecification, com.ruicar.afs.cloud.afscase.invoice.feign.RiskControlApiFeign, applyRemindFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.remind.feign.ApplyRemindFeign, afs-flow-task.FeignClientSpecification, com.ruicar.afs.cloud.afscase.remind.feign.WorkFlowFeign, ${com.ruicar.service-names.vehicle-server:afs-vehicle-biz}.FeignClientSpecification, com.ruicar.afs.cloud.afscase.vehicle.feign.VehicleInfoFeign, ApplyCustInfoFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ApplyCustInfoFeign, ApplySubmitFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ApplySubmitFeign, channelInfoFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ChannelFeign, getAddressByCodesFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ConfigServiceFeign, FlowDesignerFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.FlowDesignerFeign, afs-flow-task-history.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.FlowTaskHistoryFeign, ProducePlanFeign-productPlan.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ProducePlanFeign, productPlanRateFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ProductPlanRateFeign, ServiceFeeFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.ServiceFeeFeign, TsysParamConfigFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.workflow.feign.TsysParamConfigFeign, apply-invoice-write.FeignClientSpecification, com.ruicar.afs.cloud.afscase.writeoff.feign.ApplyWriteOffFeign, basic-invoice-write.FeignClientSpecification, com.ruicar.afs.cloud.afscase.writeoff.feign.WriteOffFeign, common-case.FeignClientSpecification, com.ruicar.afs.cloud.afscase.dispatch.contract.CaseRuleAtomFeign, cmsInterfaceFeign-CmsInterfaceFeign.FeignClientSpecification, com.ruicar.afs.cloud.afscase.dispatch.contract.CmsInterfaceFeign, boss-for-product.FeignClientSpecification, com.ruicar.afs.cloud.afscase.dispatch.feign.ForProductFeign, tokens.FeignClientSpecification, com.ruicar.afs.cloud.common.core.feign.api.RemoteTokenService, user.FeignClientSpecification, com.ruicar.afs.cloud.common.core.feign.api.RemoteUserService, tenant.FeignClientSpecification, com.ruicar.afs.cloud.common.core.feign.api.RemoteTenantService, login.FeignClientSpecification, com.ruicar.afs.cloud.common.core.security.feign.Oauth2OperationService, flow-group-mana.FeignClientSpecification, com.ruicar.afs.cloud.workflow.sdk.feign.FlowGroupManaFeign, afs-flow-run.FeignClientSpecification, com.ruicar.afs.cloud.workflow.sdk.feign.FlowRunFeign, sys-dept-info.FeignClientSpecification, com.ruicar.afs.cloud.admin.api.feign.AfsDeptFeign, sys-location-info.FeignClientSpecification, com.ruicar.afs.cloud.admin.api.feign.AfsLocationFeign, sys-role-info.FeignClientSpecification, com.ruicar.afs.cloud.admin.api.feign.AfsRoleFeign, sys-user-info.FeignClientSpecification, com.ruicar.afs.cloud.admin.api.feign.AfsUserFeign, userDevice.FeignClientSpecification, com.ruicar.afs.cloud.admin.api.feign.UserDeviceFeign, OcrServeVatInvoiceFeign.FeignClientSpecification, com.ruicar.afs.cloud.interfaces.wlease.ocr.feign.OcrServeVatInvoiceFeign, OcrServiceInvoiceInformationFeign.FeignClientSpecification, com.ruicar.afs.cloud.interfaces.wlease.ocr.feign.OcrServiceInvoiceInformationFeign, serviceToken.FeignClientSpecification, com.ruicar.afs.cloud.interfaces.genius.servicetoken.feign.ServiceTokenFeign, sdkProductApply.FeignClientSpecification, com.ruicar.afs.cloud.product.sdk.service.feign.ApplyProductCalcFeign, afsProductInfo.FeignClientSpecification, com.ruicar.afs.cloud.product.sdk.service.feign.ProductFeign, ${com.ruicar.service-names.workflow-server}.FeignClientSpecification, com.ruicar.afs.cloud.seats.feign.GroupSceneInfoFeign, api-admin-user-service.FeignClientSpecification, com.ruicar.afs.cloud.seats.feign.UserDetailsInfoFeign, sys-address-info-feign.FeignClientSpecification, com.ruicar.afs.cloud.config.api.address.feign.AddressFeign, sys-rule-info.FeignClientSpecification, com.ruicar.afs.cloud.config.api.rules.feign.AfsRuleFeign, ${com.ruicar.service-names.filecenter-server}.FeignClientSpecification, com.ruicar.afs.cloud.image.feign.FileCenterFeign, applyElectronicSignFeign.FeignClientSpecification, com.ruicar.afs.cloud.applyservice.api.feign.ApplyElectronicSignFeign, apply-approve-feign.FeignClientSpecification, com.ruicar.afs.cloud.applyservice.api.feign.ApplyPreApproveFeign, apply-report-feign.FeignClientSpecification, com.ruicar.afs.cloud.applyservice.api.feign.ApplyReportFeign, ${com.ruicar.service-names.job-admin-server:admin-service}.FeignClientSpecification, com.ruicar.afs.cloud.common.job.fegin.RemoteJobService, assetsChangeBasic.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.assetschangefeign.AssetsChangeFeign, reprint-basic.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.business.contractreprint.feign.BasicContractReprintFeign, cust-basic.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.business.custchange.feign.BasicCustChangeFeign, basic-repayment-date-feign.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.business.repaymentdate.BasicRepaymentDateFeign, common.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.common.feign.CommonFeign, commonOuter.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.common.feign.CommonOuterQueryFeign, ContractApply.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.common.gateway.ContractToApplyGateWay, credit-basic.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.creditchange.feign.BasicBankChangeFeign, basic-outer.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.margin.fegin.MarginFeign, stysettle-basic.FeignClientSpecification, com.ruicar.afs.cloud.basic.outer.api.staysettle.feign.BasicStaySettleForApplyFeign, channel-api.FeignClientSpecification, com.ruicar.afs.cloud.channel.feign.ChannelApiFeign, assetChangForApply-AssetChangForApplyFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.assetchang.feign.AssetChangForApplyFeign, bankCardForApplyFeign-BankCardForApplyFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.bankcard.feign.BankCardForApplyFeign, mana-RelationChangeFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.change.relation.feign.RelationChangeFeign, syncCmsFeign-SyncCmsFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.change.relation.feign.SyncCmsFeign, apply-file-feign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.common.feign.ApplyFileFeign, caseInfoFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.common.feign.CaseInfoFeign, contractManageCommon.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.common.feign.CommonFeign, CancelForApply.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.contractcancel.feign.CancelForApplyFeign, manage-cust-change-ManageCustChangeFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.contractcustchange.feign.ManageCustChangeFeign, contract-reprint-feign-ContractReprintFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.contractreissur.feign.ContractReprintFeign, corporateTransferForApply-CorporateTransForApplyFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.corporate.feign.CorporateTransForApplyFeign, insuranceForApply-InsuranceForApplyFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.insurance.feign.InsuranceForApplyFeign, repayment-date-feign-RepaymentDateFeign.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.repaymentdate.feign.RepaymentDateFeign, staySettleForApply.FeignClientSpecification, com.ruicar.afs.cloud.manage.outer.api.staysettle.feign.StaySettleForApplyFeign, DwDriveFeign.FeignClientSpecification, com.ruicar.afs.cloud.dianwei.sdk.feign.DwDriveFeign, vehicle-feign-001.FeignClientSpecification, com.ruicar.afs.cloud.vehicle.feign.VehicleFeign, batch-service.FeignClientSpecification, com.ruicar.afs.cloud.batch.cache.feign.BatchStartFeign, apply-service-rent-loan.FeignClientSpecification, com.ruicar.afs.cloud.loan.sdk.feign.ApplyRentLoanFeign, metaDataSourceAdvisor, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration, spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties, com.ruicar.afs.cloud.common.data.datasource.DataSourceConfig, transactionManager, org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration, websocketServletWebServerCustomizer, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow, undertowServletWebServerFactory, undertowServletWebServerFactoryCustomizer, org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration, servletWebServerFactoryCustomizer, server-org.springframework.boot.autoconfigure.web.ServerProperties, webServerFactoryCustomizerBeanPostProcessor, errorPageRegistrarBeanPostProcessor, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration, dispatcherServlet, spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration, dispatcherServletRegistration, org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, taskExecutorBuilder, spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, defaultValidator, methodValidationPostProcessor, com.ruicar.afs.cloud.common.core.tangram.config.TangramConfig, tangramEndPoint, invokeService, defaultProcess, webMvcConfigurer, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration, error, beanNameViewResolver, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration, conventionErrorViewResolver, spring.web-org.springframework.boot.autoconfigure.web.WebProperties, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, errorAttributes, basicErrorController, errorPageCustomizer, preserveErrorControllerTargetClassPostProcessor, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration, requestMappingHandlerAdapter, welcomePageHandlerMapping, localeResolver, themeResolver, flashMapManager, mvcConversionService, mvcValidator, mvcContentNegotiationManager, requestMappingHandlerMapping, mvcPatternParser, mvcUrlPathHelper, mvcPathMatcher, viewControllerHandlerMapping, beanNameHandlerMapping, routerFunctionMapping, resourceHandlerMapping, mvcResourceUrlProvider, defaultServletHandlerMapping, handlerFunctionAdapter, mvcUriComponentsContributor, httpRequestHandlerAdapter, simpleControllerHandlerAdapter, handlerExceptionResolver, mvcViewResolver, mvcHandlerMappingIntrospector, viewNameTranslator, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter, defaultViewResolver, viewResolver, requestContextFilter, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, formContentFilter, cn.hutool.extra.spring.SpringUtil, com.alibaba.cloud.circuitbreaker.sentinel.ReactiveSentinelCircuitBreakerAutoConfiguration, reactiveSentinelCircuitBreakerFactory, com.alibaba.cloud.circuitbreaker.sentinel.SentinelCircuitBreakerAutoConfiguration, sentinelCircuitBreakerFactory, com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientAutoConfiguration$ReactiveSentinelCustomizerConfiguration, reactiveConfigureRulesCustomizer, com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientAutoConfiguration$SentinelCustomizerConfiguration, configureRulesCustomizer, com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientAutoConfiguration$CircuitBreakerNameResolverConfiguration, feignClientCircuitNameResolver, com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientAutoConfiguration$CircuitBreakerListenerConfiguration, circuitBreakerRuleChangeListener, com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientAutoConfiguration, feign.sentinel-com.alibaba.cloud.circuitbreaker.sentinel.feign.SentinelFeignClientProperties, com.alibaba.cloud.nacos.NacosConfigAutoConfiguration, nacosConfigProperties, nacosRefreshHistory, nacosConfigManager, nacosContextRefresher, com.alibaba.cloud.nacos.NacosServiceAutoConfiguration, nacosServiceManager, com.ruicar.afs.cloud.common.core.config.AfsLoadBalancerConfiguration, nacosProperties, loadBalancerClientFactory, com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration, nacosServiceDiscovery, com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration, nacosDiscoveryClient, nacosWatch, com.alibaba.cloud.nacos.endpoint.NacosConfigEndpointAutoConfiguration, nacosConfigEndpoint, com.alibaba.cloud.nacos.endpoint.NacosDiscoveryEndpointAutoConfiguration, nacosDiscoveryEndpoint, org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration, com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration, nacosServiceRegistry, nacosRegistration, nacosAutoServiceRegistration, com.alibaba.cloud.nacos.utils.UtilIPv6AutoConfiguration, inetIPv6Util, com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration, sentinelWebInterceptor, sentinelWebMvcConfig, sentinelWebMvcConfigurer, spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties, com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$SentinelConverterConfiguration$SentinelJsonConfiguration, sentinel-json-flow-converter, sentinel-json-degrade-converter, sentinel-json-system-converter, sentinel-json-authority-converter, sentinel-json-param-flow-converter, com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$SentinelConverterConfiguration, com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration, sentinelResourceAspect, sentinelBeanPostProcessor, sentinelDataSourceHandler, com.alibaba.cloud.sentinel.endpoint.SentinelEndpointAutoConfiguration, sentinelEndPoint, com.alibaba.cloud.sentinel.feign.SentinelFeignAutoConfiguration, com.ruicar.afs.cloud.common.data.datasource.configure.DynamicDataSourceCreatorAutoConfiguration, dynamicDataSourceProperties, dataSourceCreator, basicDataSourceCreator, jndiDataSourceCreator, druidDataSourceCreator, com.ruicar.afs.cloud.common.data.datasource.configure.DynamicDataSourceAutoConfiguration, dataSource, dynamicDatasourceAnnotationAdvisor, dataSourceReload, com.ruicar.datasource-com.ruicar.afs.cloud.common.data.datasource.configure.DynamicDataSourceProperties, com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration, org.mybatis.spring.mapper.MapperScannerConfigurer, com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration, sqlSessionFactory, sqlSessionTemplate, ddlApplicationRunner, mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties, com.ruicar.afs.cloud.admin.sdk.config.AdminSdkConfig, dataScopeLoadService, userExtController, com.ruicar.afs.cloud.apicontrol.config.ApiControlConfig, apiControlSync, com.ruicar.afs.cloud.common.core.config.AfsSystemBasicConfiguration, afsNoPassLoginService, gitVersion, gitEndPointInfo, globalExceptionHandlerResolver, afsUserAfsCache, tenantCacheAfsCache, afsCacheFactory, configChangeListener, feignClient, org.springframework.scheduling.annotation.ProxyAsyncConfiguration, org.springframework.context.annotation.internalAsyncAnnotationProcessor, com.ruicar.afs.cloud.common.core.config.AuditAutoConfiguration, auditLogService, auditLogListener, com.ruicar.afs.cloud.common.core.config.FeignConfiguration, getHeaderInterceptor, feignConversionService, feignLoggerFactory, feignRetryer, feignContract, feignSentinelBuilder, dynamicServiceNameConfig, routeTargeter, com.ruicar.afs.cloud.common.core.config.JsonConfig, customizer, com.ruicar.afs.cloud.common.core.config.LogAutoConfiguration, sysAutoLogService, sysLogListener, sysLogAspect, com.ruicar.afs.cloud.common.core.config.MessageSourceConfig, messageSource, com.ruicar.afs.cloud.common.core.config.ParamConfiguration, paramNacosProperties, paramHelper, com.ruicar.afs.cloud.common.core.config.RestTemplateConfig, restTemplate, restTemplateCustomizer, dynamicRequest, com.ruicar.afs.cloud.common.core.config.ServiceInfoConfiguration, com.ruicar.afs.cloud.common.core.config.TransLogConfigurationForCached, transLogService, com.ruicar.afs.cloud.common.core.config.TransLogConfiguration, transLogInterceptor, leaseRestTemplateResource, transLogAspect, globalTransService, globalRequestWithFeignAop, afsRequestInterceptor, com.ruicar.afs.cloud.common.core.config.UndertowServerConfig, serverFactoryCustomizer, com.ruicar.afs.cloud.common.core.feign.api.factory.RemoteTenantServiceFallbackFactory, com.ruicar.afs.cloud.common.core.feign.api.factory.RemoteTokenServiceFallbackFactory, com.ruicar.afs.cloud.common.core.feign.api.fallback.RemoteTenantServiceFallbackImpl, com.ruicar.afs.cloud.common.core.feign.api.fallback.RemoteTokenServiceFallbackImpl, com.ruicar.afs.cloud.common.core.holder.RequestHeaderFilter, com.ruicar.afs.cloud.common.core.lifecycle.AppCloseLifeCycle, com.ruicar.afs.cloud.common.core.lifecycle.ContextHolder, com.ruicar.afs.cloud.common.core.security.service.AfsSysAdminRootServiceImpl, com.ruicar.afs.cloud.common.core.translog.filter.TransLogHeaderFilter, com.ruicar.afs.cloud.common.core.util.SpringContextHolder, com.ruicar.afs.cloud.common.data.redis.AfsRedisConfiguration, afsRedisConfig, clientResources, redisTemplate, stringRedisTemplate, afsRedisConfigReload, destroyResources, com.ruicar.afs.cloud.common.data.cache.RedisCacheManagerConfig, cacheManagerCustomizers, com.ruicar.afs.cloud.common.data.lock.config.RedissonConfiguration, lockAop, redissonClient, com.ruicar.afs.cloud.lock-com.ruicar.afs.cloud.common.data.lock.config.LockProperties, com.ruicar.afs.cloud.common.data.mybatis.MybatisConfigure, com.ruicar.afs.cloud.common.data.mybatis.MybatisPlusConfig, afsTenantHandler, dataScopeAspect, baseEntityAutoFillHandler, idGenerator, afsDbDataCrypto, afsEncResultSetInterceptor, mybatisPlusInterceptor, com.ruicar.afs.cloud.common.data.mybatis.MybatisPlusConfig#MapperScannerRegistrar#0, com.ruicar.afs.cloud.tenant-com.ruicar.afs.cloud.common.data.mybatis.plugin.tenant.TenantConfigProperties, com.ruicar.afs.cloud.data.crypto-com.ruicar.afs.cloud.common.data.mybatis.plugin.crypto.AfsDataCryptoConfigProperties, com.ruicar.afs.cloud.common.data.sequence.SequenceConfig, afsSequenceService, afsSequenceGenerator, com.ruicar.afs.cloud.common.data.uid.UidConfig, disposableWorkerIdAssigner, cachedUidGenerator, com.ruicar.afs.cloud.common.datasync.config.ParamConfig, paramSyncConfig, syncComponent, com.ruicar.afs.cloud.common.datasync.config.SyncDataKeyExtConfig, com.ruicar.afs.cloud.common.mq.rabbit.config.RabbitMqConfiguration, transCodeMapComponent, com.ruicar.afs.cloud.mq.rabbit-com.ruicar.afs.cloud.common.mq.rabbit.config.AfsRabbitMqProperties, org.springframework.amqp.rabbit.config.internalRabbitListenerAnnotationProcessor, org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry, com.ruicar.afs.cloud.afscase.writeoff.mq.WriteOffSender, com.ruicar.afs.cloud.afscase.channel.sender.CaseToApplyUpdatePayeeInfoSender, com.ruicar.afs.cloud.afscase.mq.sender.LoanSpecialBusinessSender, com.ruicar.afs.cloud.afscase.mq.approvesendinfo.ArchieLoanMoneySender, com.ruicar.afs.cloud.afscase.mq.sender.ApprovePrevSender, com.ruicar.afs.cloud.afscase.mq.approvesendinfo.PushDataForPos, com.ruicar.afs.cloud.afscase.mq.sender.ManageAssetsChangeCaseSender, com.ruicar.afs.cloud.afscase.writeoff.mq.WriteOffApprovalInfoSender, com.ruicar.afs.cloud.bizcommon.voucher.mq.sender.VoucherFlowSender, com.ruicar.afs.cloud.common.rules.config.RuleConfiguration, ruleSync, com.ruicar.afs.cloud.components.datadicsync.config.DataDicSyncConfig, dicSync, sysParamSync, com.ruicar.afs.cloud.config.api.address.service.impl.AddressServiceImpl, com.ruicar.afs.cloud.dianwei.sdk.service.impl.DwConductSdkServiceImpl, com.ruicar.afs.cloud.dianwei.sdk.service.impl.DwDriveSdkServiceImpl, com.ruicar.afs.cloud.filecenter.standstone.oss.AwsOssConfigure, com.ruicar.afs.cloud.interfaces.capc.service.business.impl.LoanServiceImpl, com.ruicar.afs.cloud.interfaces.capc.service.business.impl.PaymentServiceImpl, com.ruicar.afs.cloud.interfaces.capc.service.business.impl.TokenServiceImpl, com.ruicar.afs.cloud.interfaces.cfca.paperless.config.PaperlessClientConfiguration, electronicSealService, com.ruicar.afs.cloud.interfaces.genius.costcontrol.config.GeniusCostControlCreatePayConfiguration, costControlCreatePayService, com.ruicar.afs.cloud.interfaces.costcontrol.config.CostControlCreatePayConfiguration, com.ruicar.afs.cloud.interfaces.genius.costcontrol.config.GeniusCostControlTokenConfiguration, costControlTokenService, com.ruicar.afs.cloud.interfaces.costcontrol.config.CostControlTokenConfiguration, com.ruicar.afs.cloud.interfaces.genius.callsystem.service.impl.GeniusCallSystemServiceImpl, com.ruicar.afs.cloud.interfaces.genius.costcontrol.config.GeniusCostControlCreatePayProperties, com.ruicar.afs.cloud.interfaces.genius.costcontrol.config.GeniusCostControlTokenProperties, com.ruicar.afs.cloud.interfaces.genius.servicetoken.config.GeniusServiceTokenConfiguration, serviceTokenService, com.afs.systems.service-token-com.ruicar.afs.cloud.interfaces.genius.servicetoken.config.GeniusServiceTokenSystemProperties, com.ruicar.afs.cloud.interfaces.genius.servicetoken.service.impl.GeniusServiceTokenServiceImpl, com.ruicar.afs.cloud.interfaces.ocr.config.OcrInvoiceConfiguration, com.ruicar.afs.cloud.interfaces.ocr.config.OcrServeVatInvoiceConfiguration, com.ruicar.afs.cloud.interfaces.ocr.config.OcrServiceInvoiceInformationConfiguration, com.ruicar.afs.cloud.interfaces.servicetoken.config.ServiceTokenConfiguration, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.blacklist.BlacklistServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.electronicreceipt.ElectronicReceiptPageServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.onwayorder.OnWayOrderServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.other.GetTokenServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.ExecutePaymentServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.ExecuteWithholdServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.FourElementsServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryAccountRecordDetailServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryAmountServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryBranchNameServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryPaymentBankListServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryPaymentServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QuerySignBankListServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.QueryWithholdServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.SignServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.UnSignServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.payment.UpdateRecordFilingStatusServiceImpl, com.ruicar.afs.cloud.interfaces.wlease.capc.impl.riskrh.RiskRhInfoImpl, com.ruicar.afs.cloud.interfaces.wlease.token.config.WleaseTokenConfiguration, com.ruicar.afs.cloud.notice.config.TaskNoticeConfig, taskNotice, _taskNoticeAopImpl, com.ruicar.afs.cloud.tongdun.impl.ItdConductServiceImpl, com.ruicar.afs.cloud.vehicle.service.VehicleHelper, serverEndpointExporter, com.ruicar.afs.cloud.websocket.configuration.AutoConfigComponentConfiguration, com.ruicar.afs.cloud.workflow.sdk.config.DefaultProcessConfig, clientAdapterCallProcessor, routerNoticeProcess, taskAutoCommitProcessor, taskCancelProcessor, com.ulisesbocchio.jasyptspringboot.configuration.EncryptablePropertyResolverConfiguration, envCopy, lazyJasyptStringEncryptor, lazyEncryptablePropertyDetector, configPropsSingleton, lazyEncryptablePropertyFilter, lazyEncryptablePropertyResolver, com.ulisesbocchio.jasyptspringboot.configuration.CachingConfiguration, refreshScopeRefreshedEventListener, com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesConfiguration, enableEncryptablePropertySourcesPostProcessor, com.ulisesbocchio.jasyptspringboot.JasyptSpringBootAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration, standardJacksonObjectMapperBuilderCustomizer, spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration, jacksonObjectMapperBuilder, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration, parameterNamesModule, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration, jacksonObjectMapper, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, jsonComponentModule, jsonMixinModule, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshScopeBeanDefinitionEnhancer, org.springframework.cloud.autoconfigure.RefreshAutoConfiguration, refreshScope, loggingRebinder, legacyContextRefresher, refreshEventListener, spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties, org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration, micrometerClock, meterRegistryPostProcessor, propertiesMeterFilter, management.metrics-org.springframework.boot.actuate.autoconfigure.metrics.MetricsProperties, org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleMetricsExportAutoConfiguration, simpleMeterRegistry, simpleConfig, management.metrics.export.simple-org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleProperties, org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration, org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration, micrometerOptions, lettuceMetrics, org.springframework.boot.actuate.autoconfigure.audit.AuditEventsEndpointAutoConfiguration, org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration, applicationAvailability, org.springframework.boot.actuate.autoconfigure.availability.AvailabilityHealthContributorAutoConfiguration, org.springframework.boot.actuate.autoconfigure.beans.BeansEndpointAutoConfiguration, beansEndpoint, org.springframework.boot.actuate.autoconfigure.cache.CachesEndpointAutoConfiguration, cachesEndpoint, cachesEndpointWebExtension, org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration, servletWebChildContextFactory, managementServletContext, org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration, healthStatusAggregator, healthHttpCodeStatusMapper, healthEndpointGroups, healthContributorRegistry, healthEndpoint, healthEndpointGroupsBeanPostProcessor, org.springframework.boot.actuate.autoconfigure.health.ReactiveHealthEndpointConfiguration, reactiveHealthContributorRegistry, org.springframework.boot.actuate.autoconfigure.health.HealthEndpointWebExtensionConfiguration$MvcAdditionalHealthEndpointPathsConfiguration, healthEndpointWebMvcHandlerMapping, org.springframework.boot.actuate.autoconfigure.health.HealthEndpointWebExtensionConfiguration, healthEndpointWebExtension, org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration, management.endpoint.health-org.springframework.boot.actuate.autoconfigure.health.HealthEndpointProperties, org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration, spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties, org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration, management.info-org.springframework.boot.actuate.autoconfigure.info.InfoContributorProperties, org.springframework.boot.actuate.autoconfigure.info.InfoEndpointAutoConfiguration, infoEndpoint, org.springframework.boot.actuate.autoconfigure.condition.ConditionsReportEndpointAutoConfiguration, conditionsReportEndpoint, org.springframework.boot.actuate.autoconfigure.context.properties.ConfigurationPropertiesReportEndpointAutoConfiguration, configurationPropertiesReportEndpoint, configurationPropertiesReportEndpointWebExtension, management.endpoint.configprops-org.springframework.boot.actuate.autoconfigure.context.properties.ConfigurationPropertiesReportEndpointProperties, org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration, endpointOperationParameterMapper, endpointCachingOperationInvokerAdvisor, org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration, mbeanExporter, objectNamingStrategy, mbeanServer, spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties, org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointAutoConfiguration, jmxAnnotationEndpointDiscoverer, endpointObjectNameFactory, jmxMBeanExporter, jmxIncludeExcludePropertyEndpointFilter, eagerlyInitializeJmxEndpointExporter, management.endpoints.jmx-org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointProperties, org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration$WebEndpointServletConfiguration, servletEndpointDiscoverer, org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration, webEndpointPathMapper, endpointMediaTypes, webEndpointDiscoverer, controllerEndpointDiscoverer, pathMappedEndpoints, webExposeExcludePropertyEndpointFilter, controllerExposeExcludePropertyEndpointFilter, management.endpoints.web-org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties, org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration, environmentManager, org.springframework.boot.actuate.autoconfigure.env.EnvironmentEndpointAutoConfiguration, environmentEndpoint, environmentEndpointWebExtension, management.endpoint.env-org.springframework.boot.actuate.autoconfigure.env.EnvironmentEndpointProperties, org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration, org.springframework.boot.actuate.autoconfigure.logging.LogFileWebEndpointAutoConfiguration, management.endpoint.logfile-org.springframework.boot.actuate.autoconfigure.logging.LogFileWebEndpointProperties, org.springframework.boot.actuate.autoconfigure.logging.LoggersEndpointAutoConfiguration, loggersEndpoint, org.springframework.boot.actuate.autoconfigure.management.HeapDumpWebEndpointAutoConfiguration, heapDumpWebEndpoint, org.springframework.boot.actuate.autoconfigure.management.ThreadDumpEndpointAutoConfiguration, dumpEndpoint, org.springframework.boot.actuate.autoconfigure.metrics.JvmMetricsAutoConfiguration, jvmGcMetrics, jvmHeapPressureMetrics, jvmMemoryMetrics, jvmThreadMetrics, classLoaderMetrics, org.springframework.boot.actuate.autoconfigure.metrics.LogbackMetricsAutoConfiguration, logbackMetrics, org.springframework.boot.actuate.autoconfigure.metrics.MetricsEndpointAutoConfiguration, metricsEndpoint, org.springframework.boot.actuate.autoconfigure.metrics.SystemMetricsAutoConfiguration, uptimeMetrics, processorMetrics, fileDescriptorMetrics, diskSpaceMetrics, org.springframework.boot.actuate.autoconfigure.metrics.data.RepositoryMetricsAutoConfiguration, repositoryTagsProvider, metricsRepositoryMethodInvocationListener, metricsRepositoryMethodInvocationListenerBeanPostProcessor, org.springframework.boot.actuate.autoconfigure.metrics.integration.IntegrationMetricsAutoConfiguration, org.springframework.boot.actuate.autoconfigure.metrics.jdbc.DataSourcePoolMetricsAutoConfiguration, org.springframework.boot.actuate.autoconfigure.metrics.startup.StartupTimeMetricsListenerAutoConfiguration, startupTimeMetrics, org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration, taskScheduler, scheduledBeanLazyInitializationExcludeFilter, taskSchedulerBuilder, spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties, org.springframework.boot.actuate.autoconfigure.metrics.task.TaskExecutorMetricsAutoConfiguration, org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration, gsonBuilder, gson, standardGsonBuilderCustomizer, spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration, stringHttpMessageConverter, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration, mappingJackson2HttpMessageConverter, org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, messageConverters, org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration, restTemplateBuilderConfigurer, restTemplateBuilder, org.springframework.boot.actuate.autoconfigure.metrics.web.client.RestTemplateMetricsConfiguration, restTemplateExchangeTagsProvider, metricsRestTemplateCustomizer, org.springframework.boot.actuate.autoconfigure.metrics.web.client.HttpClientMetricsAutoConfiguration, metricsHttpClientUriTagFilter, org.springframework.boot.actuate.autoconfigure.metrics.web.servlet.WebMvcMetricsAutoConfiguration, webMvcTagsProvider, webMvcMetricsFilter, metricsHttpServerUriTagFilter, metricsWebMvcConfigurer, org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration, org.springframework.boot.actuate.autoconfigure.scheduling.ScheduledTasksEndpointAutoConfiguration, scheduledTasksEndpoint, org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration, org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.Oauth2ResourceServerConfiguration$JwtConfiguration, org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.Oauth2ResourceServerConfiguration$OpaqueTokenConfiguration, org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration, spring.security.oauth2.resourceserver-org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties, org.springframework.boot.actuate.autoconfigure.trace.http.HttpTraceEndpointAutoConfiguration, org.springframework.boot.actuate.autoconfigure.web.mappings.MappingsEndpointAutoConfiguration$ServletWebConfiguration$SpringMvcConfiguration, dispatcherServletMappingDescriptionProvider, org.springframework.boot.actuate.autoconfigure.web.mappings.MappingsEndpointAutoConfiguration$ServletWebConfiguration, servletMappingDescriptionProvider, filterMappingDescriptionProvider, org.springframework.boot.actuate.autoconfigure.web.mappings.MappingsEndpointAutoConfiguration, mappingsEndpoint, org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration, springApplicationAdminRegistrar, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration, org.springframework.boot.autoconfigure.aop.AopAutoConfiguration, org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration, org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration, lifecycleProcessor, spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties, org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration, persistenceExceptionTranslationPostProcessor, org.springframework.data.web.config.ProjectingArgumentResolverRegistrar, projectingArgumentResolverBeanPostProcessor, org.springframework.data.web.config.SpringDataWebConfiguration, pageableResolver, sortResolver, org.springframework.data.web.config.SpringDataJacksonConfiguration, jacksonGeoModule, org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration, pageableCustomizer, sortCustomizer, spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration, jdbcTemplate, org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration, namedParameterJdbcTemplate, org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration, spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties, org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor, org.springframework.boot.autoconfigure.netty.NettyAutoConfiguration, spring.netty-org.springframework.boot.autoconfigure.netty.NettyProperties, org.springframework.boot.autoconfigure.security.servlet.SpringBootWebSecurityConfiguration$ErrorPageSecurityFilterConfiguration, errorPageSecurityFilter, org.springframework.boot.autoconfigure.security.servlet.SpringBootWebSecurityConfiguration, org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration, authenticationEventPublisher, spring.security-org.springframework.boot.autoconfigure.security.SecurityProperties, org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration, securityFilterChainRegistration, org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration, dataSourceScriptDatabaseInitializer, org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration, spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration, spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties, org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration, org.springframework.transaction.config.internalTransactionAdvisor, transactionAttributeSource, transactionInterceptor, org.springframework.transaction.config.internalTransactionalEventListenerFactory, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration, transactionTemplate, org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration, platformTransactionManagerCustomizers, spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration, undertowWebServerFactoryCustomizer, org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, characterEncodingFilter, localeCharsetMappingsCustomizer, org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration, multipartConfigElement, multipartResolver, spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties, org.springframework.boot.autoconfigure.websocket.servlet.WebSocketMessagingAutoConfiguration, org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration, configurationPropertiesBeans, configurationPropertiesRebinder, org.springframework.cloud.autoconfigure.RefreshEndpointAutoConfiguration$RefreshEndpointConfiguration, refreshEndpoint, org.springframework.cloud.autoconfigure.RestartEndpointWithoutIntegrationConfiguration, org.springframework.cloud.autoconfigure.PauseResumeEndpointsConfiguration, org.springframework.cloud.autoconfigure.RefreshEndpointAutoConfiguration, org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration, compositeDiscoveryClient, org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration, simpleDiscoveryProperties, simpleDiscoveryClient, org.springframework.cloud.client.CommonsClientAutoConfiguration$ActuatorConfiguration, featuresEndpoint, org.springframework.cloud.client.CommonsClientAutoConfiguration$DiscoveryLoadBalancerConfiguration, discoveryClientHealthIndicator, discoveryCompositeHealthContributor, commonsFeatures, spring.cloud.discovery.client.health-indicator-org.springframework.cloud.client.discovery.health.DiscoveryClientHealthIndicatorProperties, org.springframework.cloud.client.CommonsClientAutoConfiguration, org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration, org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration, zoneConfig, default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification, spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration$BlockingLoadBalancerRetryConfig, loadBalancedRetryFactory, org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration, blockingLoadBalancerClient, loadBalancerServiceInstanceCookieTransformer, xForwarderHeadersTransformer, default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig, asyncLoadBalancerInterceptor, asyncRestTemplateCustomizer, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig, loadBalancedAsyncRestTemplateInitializer, org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$RetryInterceptorAutoConfiguration, loadBalancerInterceptor, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$RetryAutoConfiguration, org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration, loadBalancedRestTemplateInitializerDeprecated, loadBalancerRequestFactory, org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration, loadBalancerClientsDefaultsMappingsProvider, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration$ServiceRegistryEndpointConfiguration, serviceRegistryEndpoint, org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration, org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration, defaultsBindHandlerAdvisor, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$OkHttpClientConfiguration, connPoolFactory, okHttpClientBuilder, okHttpClientFactory, org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration, connManFactory, apacheHttpClientBuilder, apacheHttpClientFactory, org.springframework.cloud.commons.httpclient.HttpClientConfiguration, org.springframework.cloud.commons.util.UtilAutoConfiguration, inetUtilsProperties, inetUtils, org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration, compositeCompatibilityVerifier, springBootVersionVerifier, spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$CaffeineLoadBalancerCacheManagerConfiguration, caffeineLoadBalancerCacheManager, org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration, spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties, org.springframework.cloud.openfeign.clientconfig.OkHttpFeignConfiguration, httpClientConnectionPool, client, org.springframework.cloud.openfeign.loadbalancer.OkHttpFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration, org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration, feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties, org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration, org.springframework.cloud.openfeign.FeignAutoConfiguration, feignFeature, feignContext, feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties, feign.client-org.springframework.cloud.openfeign.FeignClientProperties, org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration$WebMvcServletEndpointManagementContextConfiguration, servletEndpointRegistrar, org.springframework.boot.actuate.autoconfigure.endpoint.web.ServletEndpointManagementContextConfiguration, servletExposeExcludePropertyEndpointFilter, org.springframework.boot.actuate.autoconfigure.endpoint.web.servlet.WebMvcEndpointManagementContextConfiguration, webEndpointServletHandlerMapping, controllerEndpointHandlerMapping, management.endpoints.web.cors-org.springframework.boot.actuate.autoconfigure.endpoint.web.CorsEndpointProperties, org.springframework.boot.actuate.autoconfigure.security.servlet.SecurityRequestMatchersManagementContextConfiguration$MvcRequestMatcherConfiguration, requestMatcherProvider, org.springframework.boot.actuate.autoconfigure.security.servlet.SecurityRequestMatchersManagementContextConfiguration, org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$EnableSameManagementContextConfiguration, org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration, org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration, management.server-org.springframework.boot.actuate.autoconfigure.web.server.ManagementServerProperties, caseContractLockInfoMapper, casePassRecordMapper, imageManageMapper, caseLoanAwaitInfoMapper, loanApproveTaskMapper, loanRepairApproveTaskMapper, loanWaitTaskPoolMapper, decisionEngineResultsInfoMapper, loanSummaryInfoMapper, affiliatedQuotaInfoMapper, applyAffiliatedUnitMapper, approveAuthorityMapper, affiliatedCompanyRuleMapper, approveInspectionInfoMapper, approveInspectionRuleMapper, approveMakeLabelMapper, loanLabelInfoMapper, approveMonitorMapper, approveOcrBatchMapper, approveOcrCompareMapper, approveOcrDetailMapper, caseApproveOpinionRemarksMapper, caseApprovePrevInfoMapper, preCaseCreditRuleMapper, preCaseCustInfoMapper, preCaseOperationRecordMapper, preCaseOrderInfoMapper, approveTeleRecordMapper, caseApproveRecordMapper, caseSubmitRecordMapper, inspectionSuggestMapper, saleCustParamInfoMapper, workProcessScheduleInfoMapper, workProcessScheduleInfoTempMapper, workTaskPoolHistoryMapper, workTaskPoolMapper, approveFieldVisitInfoMapper, custInfoMapper, caseLoanApproveResultMapper, leaveDayInfoMapper, loanAutoApproveBackRuleMapper, loanAutoApproveRuleMapper, approveRiskTipsRuleMapper, attachmentAtomBusinessRelMapper, attachmentAuditAtomMapper, businessManualReviewInfoMapper, cardDetectRecordChangeMapper, caseLiangfuApplyMapper, caseManualReviewInfoMapper, loanRiskTipsRuleMapper, propertyLicenseMapper, caseAutoBackHolidayMapper, caseBackToPartnersDetailMapper, caseBackToPartnersInfoMapper, electronBadgeInfoMapper, softPhoneCallMapper, callCenterRecordMapper, crawlCarPriceMapper, carGpsAddressMapper, carGpsApplyMapper, carGpsDeviceMapper, carGpsImgMapper, carModelMonitorMapper, carrierPigeonRuleDetailMapper, caseCarrierPigeonFileRecordMapper, caseCarrierPigeonFileSnapshotMapper, caseCarrierPigeonServiceRecordMapper, caseChannelPigeonDetailMapper, casePigeonLocationInfoMapper, caseMainInfoLogMapper, caseMainInfoMapper, caseApplyResidenceMapper, caseBusinessLicenseInfoMapper, caseCertificateInfoMapper, caseDrivingLicenceInfoMapper, caseIntelligentApproveRecordMapper, caseIntelligentResultMapper, deepSeekDrivingInfoMapper, deepSeekOperatorInfoMapper, blacklistMapper, carDealerMapper, caseChannelRiskInfoMapper, channelAccountInfoMapper, channelAffiliatedUnitsMapper, channelAffiliatedUnitsRelMapper, channelAuthorizeRegionMapper, channelAuthorizeVehicleMapper, channelBusiInfoMapper, channelCoopeCardealerMapper, channelCoopeDrawerPartyMapper, channelMainBrandMapper, channelNetworkMapper, channelQuotaInfoMapper, channelQuotaReleaseLogMapper, channelReceivableAccountMapper, channelRiskInfoMapper, channelShareholderInfoMapper, channelStockHolderMapper, channelVoucherInfoMapper, channelWitnessInfoMapper, commonCarDealerMapper, directCarDealerMapper, drawerPartyMapper, subBranchElecBankNoRelMapper, confCaseParamMapper, caseUrgentDealerMapper, confUrgentDealerDetailsMapper, creditOptionInfoMapper, creditOptionMapper, creditTemplateMapper, dealCorrelationMapper, channelOutLineMapper, caseEffectRecordMapper, caseEffectRecycleRecordMapper, executeWorkInfoMapper, executeWorkTaskMapper, externalLoanAmountMapper, externalUsedCarMapper, fastLoanChannelMapper, fastLoanItemConfigMapper, fastLoanRuleMapper, finCostDetailsLogMapper, focusCarRuleConfigMapper, caseApproveFraudMapper, gradeBigDataQuotaMapper, gradeModelConfMapper, gradeModelElementListMapper, gradeModelElementMapper, gradeModelInfoMapper, gradeModelResultDtlMapper, gradeModelResultMapper, gradeModelResultModifyLogMapper, gradeModelResultSubDtlMapper, gradeModelSubDataelementMapper, gradeScoreRelaMapper, handlingInfoMapper, approveAssetsChangeMapper, carStyleDetailInfoMapper, caseBaseInfoMapper, caseBusinessLicenseArtificialMapper, caseBusinessLicenseMapper, caseCarInfoMapper, caseChannelInfoMapper, caseChannelUniteInfoMapper, caseContractInfoMapper, caseCostInfoMapper, caseCostInfoOriginalMapper, caseCustAddressMapper, caseCustCallRemarkHistoryMapper, caseCustCallRemarkMapper, caseCustChangeRecordMapper, caseCustCompanyMapper, caseCustContactMapper, caseCustHistoryMapper, caseCustIndividualMapper, caseCustInfoMapper, caseDataPostInfoMapper, caseDiscountDetailMapper, caseDueDiligenceMapper, caseEnterpriseCustomerDetailsMapper, caseFacePhotoInfoMapper, caseFinMainInfoMapper, caseFinRentAdjustDetailsMapper, caseFinancingItemsMapper, caseLoanCannelInfoMapper, casePayeeInfoMapper, casePayeeProgressMapper, casePayeeSecondaryMapper, casePolicyCheckMapper, casePolicyCheckRecordMapper, caseReceiptInfoMapper, caseRedundantInfoMapper, caseRemarkRecordMapper, depositConfigMapper, electronuclearArtificialMapper, finCostDetailsMapper, geoAddressMapper, caseInterfaceRecordMapper, loanActivatePoolMapper, loanActivateRulesMapper, addedFinancingItemsMapper, carInsuranceInfoMapper, carInvoiceInfoMapper, caseBackConfigurationMapper, caseBackReasonInfoMapper, caseCarDepositMapper, casePriorityRecordMapper, loanBankCardInfoMapper, loanBackAssignRecordMapper, loanDealerFirstMortgageMapper, loanExaminationBatchInfoMapper, loanExaminationExcelMapper, loanExaminationInfoMapper, loanExaminationRetrospectRecordMapper, caseManageAssetChangeMapper, loanFlawFixDetailMapper, loanFlawFixMapper, loanGpsRuleInfoMapper, loanInvoiceCheckRecordMapper, loanModeRuleInfoMapper, loanOverTimeRuleMapper, loanReviewRuleMapper, loanSecondaryFraudInfoMapper, caseSignRelationMapper, loanSpecialBusinessInfoMapper, loanSpecialOpinionInfoMapper, loanSuspendRuleMapper, manualLabelMapper, marginInfoMapper, maxLoanParamMapper, caseNoticeInfoMapper, caseSmsNoticeRecordMapper, caseSmsSendRecordMapper, caseSmsTemplateMapper, loanNoticeInfoMapper, messageTemplateMapper, caseMqAcceptRecordMapper, caseMqCompareInfoMapper, commonFailMqInfoMapper, caseMortgageProxyInfoMapper, caseMortgageRecordMapper, mortgageConfigMapper, vehicleMortgageRegistrationMapper, caseCreditOptionConfMapper, caseCreditOptionMapper, caseConfParamMapper, reconsiderationWorkflowMapper, workExceptionInfoMapper, reconsiderationProcessMapper, caseChangeAtomMapper, caseChangeRecordMapper, remindMapper, creditInquiryRecordsMapper, antiFraudInfoMapper, baiHangSpendInfoMapper, caseFaceReviewInfoMapper, caseLiangFuApplyMapper, caseTortoiseApplyMapper, caseTortoiseDecisionHandMapper, caseTortoiseExposureExceptMapper, caseTortoiseFraudHandMapper, caseTortoiseFraudPushMapper, caseTortoisePrevMapper, dwCreditInfoMapper, manualReviewInformationMapper, remindVerificationRuleMapper, remindVerificationRuleMappingMapper, thirdDataMapper, tongDunAntifraudMapper, tongDunMapper, tongDunOriginalMapper, saleTeamInfoMapper, saleTeamUserMapper, softPhoneRecordMapper, softPhoneUserMapper, caseStepParamMapper, importFileTraceMapper, vehicleImportFileDetailMapper, voiceInspectionMapper, caseLoanFinanceAutoMapper, caseRiskCustInfoMapper, workflowProcessBusinessRefInfoMapper, workflowTaskBindingParamsMapper, workflowTaskInfoMapper, channelServiceFeeMapper, contractDetailManageMapper, financialWriteOffInvoiceMapper, taxWhiteListManageMapper, writeOffAccountCycleDetailMapper, writeOffAccountCycleMapper, writeOffApportionDetailMapper, writeOffApportionInfoMapper, writeOffApprovalInfoMapper, writeOffBaseInfoMapper, writeOffBaseInvoiceRelMapper, writeOffChannelGroupDetailMapper, writeOffChannelGroupMapper, writeOffContractDetailManageMapper, writeOffDeductDetailMapper, writeOffFrozenInfoMapper, writeOffHistoryBaseInfoMapper, writeOffHistoryBaseInvoiceRelMapper, writeOffHistoryInvoiceInfoMapper, writeOffInvoiceInfoMapper, writeOffPermissionMapper, writeOffRuleDetailMapper, writeOffRuleMapper, sysAutoLogMapper, auditLogMapper, deadMessageQueueMapper, basicMqSendRecordMapper, applyCostDetailsMapper, applyDiscountDetailsMapper, applyFinancingItemsMapper, applyRentAdjustDetailsMapper, finDiscountCostMapper, finDiscountPlanMapper, finMainInfoMapper, finPlanRateMapper, finRentAdjustmentMapper, finRepaymentPlanMapper, finTermsDetailsMapper, finUiContentMapper, marginInfoCommonMapper, afsWorkerNodeMapper, afsSequenceMapper, baseHolidayMapper, groupMapper, groupSceneMapper, groupUserMapper, mainEventLogMapper, opraHolidayMapper, personalPowerParamMapper, postChangeLogMapper, regularValueMapper, seatParamMapper, seatPostConfigMapper, userCollocationMapper, CaseBaseInfoMapper2, comAttachmentFileMapper, comAttachmentFileTempMapper, comAttachmentFileUploadMapper, comAttachmentManagementMapper, comAttachmentRuleMapper, signInterviewInfoMapper, comPrintFormClassMapper, comPrintFormFieldMappingMapper, comPrintFormManageMapper, comPrintFormSealMappingMapper, comSealBaseInfoMapper, subjectSignInfoMapper, mqMessageQueueMapper, voucherAmtMapper, applyZxReportCountMapper, comReportFileMapper, attorneyAutomaticRecognitionMapper, businessAutomaticRecognitionMapper, certificateAutomaticRecognitionMapper, confTemplateConfigurationMapper, deepseekIntelligentResultsMapper, statementAutomaticRecognitionMapper, afsBatchInfoMapper, afsBatchJobGroupInfoMapper, afsBatchJobStepBreakPointMapper, afsBatchJobStepInfoMapper, afsBatchLogGroupMapper, afsBatchLogStepMapper, sysHolidayInfoMapper, lockRedisTemplate, lockStringRedisTemplate, datasyncRedisTemplate, datasyncStringRedisTemplate, filecenterRedisTemplate, filecenterStringRedisTemplate, datasyncGlobalRedisTemplate, datasyncGlobalStringRedisTemplate, datasyncApplyRedisTemplate, datasyncApplyStringRedisTemplate, devExchangeQueueRedisTemplate, devExchangeQueueStringRedisTemplate, oldSystemRedisTemplate, oldSystemStringRedisTemplate, <EMAIL>@cd3acdd, byd-leasing, byd-leasing-afs-rabbit-template, byd-leasing-afs-rabbit-listener, AfsRabbitMqListener0, AfsRabbitMqListener1, AfsRabbitMqListener2, AfsRabbitMqListener3, AfsRabbitMqListener4, AfsRabbitMqListener5, AfsRabbitMqListener6, AfsRabbitMqListener7, AfsRabbitMqListener8, AfsRabbitMqListener9, AfsRabbitMqListener10, 66a5982be34a0dce68c70def5cd90b6c, a8b25ee46509d50a9d462d0da64d9e32, ed88ea29291df0e3bcab48a8fccbb2d9, d90ff22cb04769fa5bff45bedeff2294, 24e3bda6a3f74131a6459444e8964931, 0789e58e62ee9f391f4ad94355790a85, 10cbcd32235500aea1d9950239f9613a, 8efe7af43283236cecbd8b7c8a0293b9, 57df764ed5b653490ceee568b519dc99, d42899dec650a9c87aa7fbd363c11596, e9fd899ab029de2b58b626881a9cb05e, 96a199c9cc28f271ac7a4e5eadb5656f, c811db81247fc1fbad72407ce63d56a2, 4d368cadf2120ec91ea0209bb1db5316, 03bde48d1ab9c7aab1932f376e56f5fb, 405f65115e2a82710a1c38a82638dcee, edbb3cdb0a0cc77eb5cfc2843b333182, 7e1711dede309659b9aba9e9fe83b892, 941587075035714e361a8d66f5429fc2, 7bb8f7c40e9f1a90167cc0259abdc92e, 58414e4f6cdc736218980e06867c0ce1, cae1a46eb9fea6e60792a734e0b6ec04, 1a82a2fff40bf7729d407c2dfecd1d09, f90ea9599bd67b0df2105b8f3266bff2, 8d77fcfae0821eced19e8ba653d35baa, fd91df89d2b5452396e09a1e373c3b30, de43e34aad24fdf26a880b4bfb4e72fd, 42e93c85c5210f0b9975289b35579458, b6c02572bcd3510e851cb33f8d06412e, a7f7711703dfc6216365c6d5d7365ea1, 34e75a6510e9a5ab7451a3753c326ebb, e618cb2900c2e6cfd31faac80098ceeb, 39a4575fab6fe2610881397c9fc9a040, df2412b250fd2cc3583869524039f902, ad7c554dbc753f51118c013318ada4d5, 4ce7f451ef6cc119b329401fcc2fc6db, c8fdc2f3376aaef797a0bf94eb8e43ff, 01209630613f4e9fe91efde9d976c103, 78f2f78bdc2687bf0c0bdb5864603ecb, e05ed5749f2f20f62fe44510556a7e94, 202d646a1da537faf6a08233555acde9, 969f49542a153809bbb47753c216b600, 10bcd41a9fe9dc2985403c4ea7042480, 8502e1a19d6715cfd72da1afdc0f3a65, a50dac4e5515750be54cab1387ddb4dd, ed9c6ba4548edea2da39a6f5b88f7d59, adde81dce38266f6ee412434f9b2e17a, 3e48dd15066b4c043cfa3eca94965afc, b5d2572948431ed4ac871b0dbd338bbc, 481a2efeef327ae24c9d18b8d7b7ac4c, a1d9ee438b14ac0322376723921c02dc, 80c014b2bc89fece1d8b363edc217f6c, 11751776a3efaeb3b3be597377c7110e, eef10c7312aeb6bffc21e16448fbb6e7]
2025-07-23 14:47:15,836 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3ebc6814
2025-07-23 14:47:15,836 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:15,836 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-CobwebDiagramFeign-CobwebDiagramFeign, started on Wed Jul 23 14:47:15 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:15,836 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:15,911 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6cdb907
2025-07-23 14:47:15,912 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:15,912 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-channelInfoFeign, started on Wed Jul 23 14:47:15 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:15,912 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:15,964 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2562505
2025-07-23 14:47:15,964 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:15,964 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-productPlanRateFeign, started on Wed Jul 23 14:47:15 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:15,964 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,015 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6d51cc69
2025-07-23 14:47:16,015 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,015 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-common-case, started on Wed Jul 23 14:47:15 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,015 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,071 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7d2b9f61
2025-07-23 14:47:16,072 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,072 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-cmsInterfaceFeign-CmsInterfaceFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,072 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,150 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@28a87258
2025-07-23 14:47:16,151 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,151 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-login, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,151 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 15 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, afsFeignFormSupport, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, feignFormEncoder, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,220 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@581323d6
2025-07-23 14:47:16,220 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,221 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-dept-info, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,221 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,283 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1e8fd0b
2025-07-23 14:47:16,283 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,283 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-sys-role-info, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,283 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,335 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7e62bded
2025-07-23 14:47:16,335 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,336 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-userDevice, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,336 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,384 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@387e0d76
2025-07-23 14:47:16,385 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,385 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-applyElectronicSignFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,385 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,423 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@408ca040
2025-07-23 14:47:16,424 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,424 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-approve-feign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,424 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,470 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6d66d76a
2025-07-23 14:47:16,470 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,471 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-assetsChangeBasic, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,471 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,513 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@5e72f314
2025-07-23 14:47:16,514 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,514 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-reprint-basic, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,514 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,564 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@5fac4c95
2025-07-23 14:47:16,564 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,564 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-cust-basic, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,564 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,617 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@adf5a5f
2025-07-23 14:47:16,617 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,617 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-basic-repayment-date-feign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,617 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,668 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1327d69
2025-07-23 14:47:16,668 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,668 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-ContractApply, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,669 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,731 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1c3a70ee
2025-07-23 14:47:16,731 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,731 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-credit-basic, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,732 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,782 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1ef7bafe
2025-07-23 14:47:16,782 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,782 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-stysettle-basic, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,783 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,827 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2232589
2025-07-23 14:47:16,827 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,827 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-bankCardForApplyFeign-BankCardForApplyFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,828 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,871 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@1c88d43e
2025-07-23 14:47:16,871 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,871 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-mana-RelationChangeFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,871 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,905 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@97d3f57
2025-07-23 14:47:16,905 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,905 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-syncCmsFeign-SyncCmsFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,905 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,935 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@142e4e25
2025-07-23 14:47:16,935 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,935 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-file-feign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,935 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:16,971 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@296fc593
2025-07-23 14:47:16,971 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:16,971 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-caseInfoFeign, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:16,971 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,011 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3cf9641
2025-07-23 14:47:17,011 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,011 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-contractManageCommon, started on Wed Jul 23 14:47:16 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,011 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,046 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@5086cb12
2025-07-23 14:47:17,046 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,046 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-CancelForApply, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,046 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,080 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@70fc3b75
2025-07-23 14:47:17,080 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,080 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-manage-cust-change-ManageCustChangeFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,080 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,124 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@6c96564e
2025-07-23 14:47:17,125 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,125 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-contract-reprint-feign-ContractReprintFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,125 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,172 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@783b7a19
2025-07-23 14:47:17,173 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,173 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-corporateTransferForApply-CorporateTransForApplyFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,173 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,218 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@479693cc
2025-07-23 14:47:17,218 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,219 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-insuranceForApply-InsuranceForApplyFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,219 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,266 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@491719c
2025-07-23 14:47:17,266 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,266 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-repayment-date-feign-RepaymentDateFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,266 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,300 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@3c894e97
2025-07-23 14:47:17,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-staySettleForApply, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,301 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,337 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@7366b259
2025-07-23 14:47:17,337 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,338 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-DwDriveFeign, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,338 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,379 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@4a3c6015
2025-07-23 14:47:17,379 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,379 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-batch-service, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,381 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:17,418 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring id: org.springframework.context.annotation.AnnotationConfigApplicationContext@2dc50af2
2025-07-23 14:47:17,418 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring applicationName: 
2025-07-23 14:47:17,418 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring context: FeignContext-apply-service-rent-loan, started on Wed Jul 23 14:47:17 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@38a4e2b0
2025-07-23 14:47:17,418 [main] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [o.s.context.support.AbstractApplicationContext] LogLevel.java:84
                                -
                                spring beanDefinitionNames: 14 [org.springframework.context.annotation.internalConfigurationAnnotationProcessor, org.springframework.context.annotation.internalAutowiredAnnotationProcessor, org.springframework.context.annotation.internalCommonAnnotationProcessor, org.springframework.context.event.internalEventListenerProcessor, org.springframework.context.event.internalEventListenerFactory, propertyPlaceholderAutoConfiguration, feignClientsConfiguration, propertySourcesPlaceholderConfigurer, org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration, feignDecoder, feignEncoderPageable, feignQueryMapEncoderPageable, feignConversionService, feignClientConfigurer]
2025-07-23 14:47:42,159 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->ce19a9ff4953470ca86ca589fe1bd6b5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:48:22,172 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->c68c6ca9291d40f693f0a929a0ae15ec] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:49:02,186 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->d09d4cb8908340b9a816119fcfe8d1d5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:49:42,202 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->c222bf6c7696430fbfadb76ad8c46e23] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:50:22,222 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->d933aab06bb0409683ca46a9511d1e9a] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:51:02,238 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->dc7335625f82429aa47e3a500131ff9e] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:51:42,254 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->adf936bf934e4425a066e1d5e97befb4] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:52:22,268 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->29560ff1727b4147af473478603401ef] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:53:02,290 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->d327ab27c4574679877505a5792991a6] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:53:42,310 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->f4bee3b1834d411a8399ab5c916e7506] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:54:22,323 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->3e4a2540a7714bcb982a71fbeb513795] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:55:02,341 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->db3409a4ebd1485d9f037507a6f022f6] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:55:42,359 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->4cdb0f2b4f6a4010af74ebe403c83af0] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Connection reset
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:328)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:244)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:284)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:343)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:827)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:56:22,384 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->f0dbf993b5b24ad9b9324cf7de6e213b] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:57:02,405 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->1cefddb654c34fd9a9150f38784e44e5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:57:42,430 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->bcbc83eefb4f429584cd93b21a8c8d67] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:58:22,444 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->08197c50ab0743b0b52b80a5be8ebdad] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:59:02,463 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->052092c3f8ba4c4882db1eb740d7e78b] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 14:59:42,480 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->9ea1803a2f664c0b9c323d83fcd9529e] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:00:22,507 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->1b2da71ddda540d5a75a1b9b2b41d83f] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:01:02,521 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->880f648b962a426c98ea4f20afd0d6b9] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:01:42,545 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->bd6b49ca83a64a89b8afe336719993d8] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:02:22,559 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->d08846042ce54f6aa8ff4fa63ea8cc6b] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:03:02,575 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->a8867f6dd6a5427eb380f884c5afefc1] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:03:42,592 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->a38d00f3b1c54351bdd21729985f9fe6] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:04:22,601 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->635663872c0f4d92a772d947a059abc4] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:05:02,615 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->8991ac40d705450b9023750ef4257b4f] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:05:42,627 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->359efed283344861bff5f3c5cb51382a] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:06:22,636 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->2be553876bf049d8855550965b5689c5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:07:02,656 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->cda4d83aad1b45bcb49a087ba73e5321] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:07:42,664 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->de788ef81667493ea2a46f542d67ae8f] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:08:22,692 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->9aec55c692fb4a059c533b01400ff67d] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:09:02,715 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->e947102bed784903a39b195587797e71] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:09:42,724 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->1bf1a1e0c97d40f0ad47fd13f8854133] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:10:22,734 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->eeed33a5af444be4a38dd11e72d5f203] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:11:02,753 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->69a7fb84767942f3aee42be6fc8ec0fe] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:11:42,775 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->b287be8b1a704077a82c29011f173bdb] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:12:22,797 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->1f06e2aa0554486d92468560294d544b] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:13:02,814 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->86140f46b7e04676a2b35cb56569bcb8] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:13:42,832 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->4117af648a30485eb61e481e3ba3ab27] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:14:22,852 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->c9c65e8201c346a9bc6b14015992a0e5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:15:02,876 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->ace5786de7cf4dfe85bc5942c14d9f86] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:15:42,895 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->821b0263fb9d47129645c9ffecebce75] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:16:22,909 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->2567e1ac63b24cbaad37581fa752b7d8] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:17:02,935 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->ec2196d0abe54cb08cd98fb0a2cac307] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:17:42,947 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->b4e5846a6fc14e6f84325f220733e9f5] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:18:22,977 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->c103ece03e9b4dfeb48c9bb199ed0beb] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:19:02,997 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->dce201c6a4b3422ab49e6235238bb3b6] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:19:43,016 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->111d704d50e84d7aa82f15603efa5b14] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:20:23,029 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->042767d1eedf4fc488e0b2b58e946df4] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:21:03,056 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->d20189811aec4eeb8b9f9412811e40b1] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:21:43,084 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->3241194308d1487eb0a154ef8b4f027f] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:786)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:22:23,104 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->32e1b037cf9b4d9cbe18676ad27280d8] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Connection reset
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:328)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/java.io.BufferedInputStream.fill(BufferedInputStream.java:244)
	at java.base/java.io.BufferedInputStream.read1(BufferedInputStream.java:284)
	at java.base/java.io.BufferedInputStream.read(BufferedInputStream.java:343)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:827)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:23:03,127 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->75c2e4aab62a40d2a7295930af3671d4] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:129
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registry(FeignAdminBizClient.java:119)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:58)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
2025-07-23 15:23:27,692 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                Exception in thread "task-assign-notice" java.lang.IllegalStateException: LettuceConnectionFactory was destroyed and cannot be used anymore
2025-07-23 15:23:27,692 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.util.Assert.state(Assert.java:76)
2025-07-23 15:23:27,693 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.assertInitialized(LettuceConnectionFactory.java:1263)
2025-07-23 15:23:27,693 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:414)
2025-07-23 15:23:27,693 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
2025-07-23 15:23:27,693 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
2025-07-23 15:23:27,693 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
2025-07-23 15:23:27,694 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
2025-07-23 15:23:27,694 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
2025-07-23 15:23:27,694 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at org.springframework.data.redis.core.RedisTemplate.delete(RedisTemplate.java:723)
2025-07-23 15:23:27,694 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at com.ruicar.afs.cloud.notice.service.TaskNotice.lambda$init$0(TaskNotice.java:197)
2025-07-23 15:23:27,694 [task-assign-notice] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [java.lang.Throwable] LogLevel.java:84
                                -
                                	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-23 15:23:29,650 [pool-8-thread-1] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [c.r.a.c.b.v.component.VoucherSendPublishComponent] VoucherSendPublishComponent.java:76
                                -
                                凭证流水数据推送异常
java.lang.NullPointerException: Cannot invoke "org.springframework.context.ApplicationContext.getBean(java.lang.Class)" because "com.ruicar.afs.cloud.common.core.util.SpringContextHolder.applicationContext" is null
	at com.ruicar.afs.cloud.common.core.util.SpringContextHolder.getBean(SpringContextHolder.java:50)
	at com.ruicar.afs.cloud.bizcommon.voucher.component.VoucherSendPublishComponent.lambda$schedule$0(VoucherSendPublishComponent.java:74)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-23 15:23:31,109 [afs-job-executor-ExecutorRegistryThread] [namespace->byd_leasing_dev] [service->case-service] [traceId->] ERROR [c.r.a.cloud.common.job.client.FeignAdminBizClient] FeignAdminBizClient.java:174
                                -
                                回调服务失败
cn.hutool.http.HttpException: Unexpected end of file from server
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:511)
	at cn.hutool.http.HttpResponse.initWithDisconnect(HttpResponse.java:484)
	at cn.hutool.http.HttpResponse.<init>(HttpResponse.java:81)
	at cn.hutool.http.HttpRequest.doExecute(HttpRequest.java:1137)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:1019)
	at cn.hutool.http.HttpRequest.execute(HttpRequest.java:995)
	at com.ruicar.afs.cloud.common.job.client.FeignAdminBizClient.registryRemove(FeignAdminBizClient.java:164)
	at com.ruicar.afs.cloud.common.job.core.thread.ExecutorRegistryThread.lambda$start$0(ExecutorRegistryThread.java:96)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:952)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:762)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1688)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1589)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)
	at cn.hutool.http.HttpConnection.responseCode(HttpConnection.java:470)
	at cn.hutool.http.HttpResponse.init(HttpResponse.java:508)
	... 8 common frames omitted
