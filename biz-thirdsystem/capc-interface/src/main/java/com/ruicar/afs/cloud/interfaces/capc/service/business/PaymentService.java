package com.ruicar.afs.cloud.interfaces.capc.service.business;

import com.alibaba.fastjson.JSONObject;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.ExecutePaymentDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.ExecuteWithholdDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.QueryAccountRecordDetailDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.QueryWithholdOrPaymentDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.UpdateRecordFilingStatusDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.vo.PaymentResultVo;
import com.ruicar.afs.cloud.interfaces.capc.entity.vo.SignBankListVo;
import com.ruicar.afs.cloud.interfaces.capc.entity.vo.WithholdResultVo;
import com.ruicar.afs.cloud.third.system.invoke.dto.Response;

import java.util.List;

/**
 * 放款相关接口_服务
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
public interface PaymentService {

    /**
     * 查询可放款银行列表
     *
     * @return
     */
    public IResponse<List<SignBankListVo>> queryPaymentBank();

    /**
     * 执行放款
     *
     * @param executePaymentDto
     * @param reqSerialNo
     * @return
     */
    public IResponse<String> executePayment(ExecutePaymentDto executePaymentDto, String reqSerialNo);

    /**
     * 获取放款结果
     *
     * @param queryWithholdOrPaymentDto
     * @return
     */
    public IResponse<PaymentResultVo> queryPaymentResult(QueryWithholdOrPaymentDto queryWithholdOrPaymentDto);

    /**
     * 通过联行号获取支行名称
     *
     * @param bankLineNo
     * @return
     */
    public IResponse<String> queryBranchName(String bankLineNo);

    /**
     * 通过银行code 获取银行余额
     *
     * @param bankCode
     * @return
     */
    public IResponse<String> queryAmount(String bankCode);

    /**
     * 申请扣款
     * @param executeWithholdDto
     * @return
     */
    IResponse<String> applyDeduction(ExecuteWithholdDto executeWithholdDto);

    /**
     * 查询扣款结果   queryWithholdService
     * @param queryWithholdOrPaymentDto
     * @return
     */
    IResponse<WithholdResultVo> queryDeductionResult(QueryWithholdOrPaymentDto queryWithholdOrPaymentDto);


    /**
     * 获取收款明细信息
     * @param accountRecordDetailDto
     * @return
     */
    Response<JSONObject> queryAccountRecordDetail(QueryAccountRecordDetailDto accountRecordDetailDto);

    /**
     * 修改对公收款记录状态为归档
     * @param dto 入参
     * @return 响应
     */
    Response<String> updateRecordFilingStatus(UpdateRecordFilingStatusDto dto);
}
