package com.ruicar.afs.cloud.interfaces.capc.service.payment;

import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.interfaces.capc.entity.dto.SignDto;
import com.ruicar.afs.cloud.interfaces.capc.entity.enums.CAPCType;
import com.ruicar.afs.cloud.third.system.invoke.dto.Request;
import com.ruicar.afs.cloud.third.system.invoke.dto.Response;
import com.ruicar.afs.cloud.third.system.invoke.service.AfsInterfaceService;

/**
 * 用户签约_服务_POST
 *
 * <AUTHOR>
 * @date 2022/9/8
 */
public interface SignService extends AfsInterfaceService<CAPCType, SignDto, IResponse<String>, Request<CAPCType, SignDto>, Response<IResponse<String>>> {
}
