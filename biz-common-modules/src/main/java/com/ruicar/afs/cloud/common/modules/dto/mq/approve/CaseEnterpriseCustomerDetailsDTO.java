package com.ruicar.afs.cloud.common.modules.dto.mq.approve;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CaseEnterpriseCustomerDetailsDTO implements Serializable {



    @ApiModelProperty(value = "申请编号")
    private String applyNo;

    @ApiModelProperty(value = "客户ID")
    private Long custId;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "社会统一信用码")
    private String socunicrtCode;

    @ApiModelProperty(value = "企业联系电话")
    private String companyPhone;

    @ApiModelProperty(value = "企业性质")
    private String natureEnterprise;

    @ApiModelProperty(value = "企业从事行业")
    private String enterprisesEngagedIndustry;

    @ApiModelProperty(value = "客户属性")
    private String clientProperty;

    @ApiModelProperty(value = "企业类型")
    private String enterprisesType;

    @ApiModelProperty(value = "担保企业法人自查征信")
    private String creditInvestigation;

    @ApiModelProperty(value = "企业联系人")
    private String businessContacts;

    @ApiModelProperty(value = "企业联系手机")
    private String enterpriseContactMobilePhone;

    @ApiModelProperty(value = "企业年收入")
    private BigDecimal annualIncomeOfEnterprises;

    @ApiModelProperty(value = "备注")
    private String remarks;
    /** 客户渠道 */
    private String customerChannel;
    @ApiModelProperty(value = "证照到期日")
    private Date companyLicenseDate;
    @ApiModelProperty(value = "企业状态")
    private String enterpriseStatus;

}
