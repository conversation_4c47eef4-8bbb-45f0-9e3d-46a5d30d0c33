package com.ruicar.afs.cloud.common.modules.dto.mq.loan.contractdto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Lee
 * @date 2021-06-09 04:39:54
 * @description 客户机构信息主表
 */
@Data
public class ContractOrganCustInfoDto{

    /** 机构编号 */
    @ApiModelProperty("机构编号")
    private String applyNo;
    /** 机构名称 */
    @ApiModelProperty("机构名称")
    private String organName;
    /** 客户SapCode */
    @ApiModelProperty("客户SapCode")
    private String custSapCode ;
    /** 营业地址-省份 */
    @ApiModelProperty("营业地址-省份")
    private String businessProvince;
    /** 营业地址-城市 */
    @ApiModelProperty("营业地址-城市")
    private String businessCity;
    /** 营业地址-详情 */
    @ApiModelProperty("营业地址-详情")
    private String businessAddress;
    /** 行业类别 */
    @ApiModelProperty("行业类别")
    private String industryType;
    /** 公司性质 */
    @ApiModelProperty("公司性质")
    private String companyNature;
    /** 法定代表人 */
    @ApiModelProperty("法定代表人")
    private String legalPerson;
    /** 联系人 */
    @ApiModelProperty("联系人")
    private String contactPerson;
    /** 联系电话 */
    @ApiModelProperty("联系电话")
    private String contactNumber;
    /** 社会统一信用代码 */
    @ApiModelProperty("社会统一信用代码")
    private String socUniCrtCode;
    /** 中征码 */
    @ApiModelProperty("中征码")
    private String middleSignCode;
    /** 成立日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("成立日期")
    private Date establishmentDate;
    /** 营业执照到期日 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("营业执照到期日")
    private Date expiringDate;
    /** 注册资本 */
    @ApiModelProperty("注册资本")
    private BigDecimal registeredCapital;
    /** 营业范围 */
    @ApiModelProperty("营业范围")
    private String businessScope;
    /** 年收入 */
    @ApiModelProperty("年收入")
    private BigDecimal annualIncome;
    /** 员工人数 */
    @ApiModelProperty("员工人数")
    private String employeesNumber;
    @ApiModelProperty("null")
    private String executivesName;
    @ApiModelProperty("null")
    private String executivesCertNo;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("null")
    private Date executivesExpiringDate;
    @ApiModelProperty("null")
    private String executivesHomeProvince;
    @ApiModelProperty("null")
    private String executivesHomeCity;
    @ApiModelProperty("null")
    private String executivesHomeAddress;
    @ApiModelProperty("null")
    private String executivesContactNum;
    @ApiModelProperty("null")
    private String executivesPosition;
    /** 是否长期 */
    @ApiModelProperty("是否长期")
    private String isLongTerm;
    /** 身份证类型 */
    @ApiModelProperty("身份证类型")
    private String certType;

}
