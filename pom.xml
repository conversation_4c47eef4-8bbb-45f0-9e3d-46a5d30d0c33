<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.byd.leasing</groupId>
        <artifactId>leasing-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>case-service</artifactId>
	<properties>
        <package.name>case-service</package.name>
        <common-net.version>3.8.0</common-net.version>
        <easyexcel.version>2.2.11</easyexcel.version>
	</properties>
    <dependencies>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>callsystem-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>case-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>workflow-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>admin-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>rabbit-mq-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>biz-common-modules</artifactId>
            <version>${biz-project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>ocr-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>invoice-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>cbs-interface</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>baihang-interface</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>biz-common-param</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>cfca-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <!--数据库操作-->
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-common-data</artifactId>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <artifactId>junit</artifactId>
            <groupId>junit</groupId>
            <scope>test</scope>
        </dependency>
            <dependency>
                <groupId>com.byd.leasing</groupId>
                <artifactId>product-sdk</artifactId>
                <version>${biz-project.version}</version>
            </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-datadicsync-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-common-uid</artifactId>
        </dependency>
        <!--add by tiankai 坐席依赖 start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>seats-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>

        <!-- add by fangchenliang 影像 start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>image-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <!-- add by fangchenliang 影像 end-->
        <!-- add by fangchenliang 影像 start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>print-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <!-- add by fangchenliang 合同打印 end-->
        <!--add by gjq 长城聚合服务  start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>aggregate-system-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <!--add by gjq 长城聚合服务  end-->
        <!--add by gjq 长城车型库  start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>vehicle-system-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>voucher-sdk</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>risk-sdk</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>call-center-sdk</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>

        <!--add by gjq 长城车型库  start-->
        <!-- add by  gjq算法系统 -->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>algorithm-system-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>websocket-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>gps-system-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>archive-system-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>message-system-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-common-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>contract-basic-outer-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.51rcar.afs</groupId>
            <artifactId>afs-api-control-components</artifactId>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>channel-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>namelist-system-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${common-net.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>contract-manage-outer-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>clmbv-system-api</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <version>2.5.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- add by jinguoliang dianwei start-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>dianwei-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>dianwei-interface</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>capc-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>capc-interface</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- add by zhangyong tongdun end-->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>tongdun-sdk</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>tongdun-interface</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>flow-task-notice-sdk</artifactId>
            <version>${biz-project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--费控系统 -->
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>costcontrol-system-sdk</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>vehicle-api</artifactId>
            <version>${biz-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>byd-direct-dealer-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>zhengxin-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>byd-eisoa-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>byd-deepseek-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>check-insurance-interface</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tmri.net</groupId>
            <artifactId>third-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.tmri.tms</groupId>
            <artifactId>tms-common-securitygm</artifactId>
            <version>2.1</version>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>batch-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>contract-basic-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>wx-message-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>rent-loans-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>aws-oss-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>batch-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.byd.leasing</groupId>
            <artifactId>byd-electron-badge-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>${package.name}</finalName>
        <resources>
            <resource>
                <directory>${project.basedir}/src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>${project.basedir}/config</directory>
                <includes>
                    <include>
                        bootstrap-${package.environment}.yml
                    </include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <name>current.time</name>
                    <pattern>yyyyMMddHHmmss</pattern>
                    <timeZone>GMT+8</timeZone>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <executions>
                    <execution>
                        <id>copy-resources2</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/bin</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/shell</directory>
                                    <include>start.sh</include>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <!--docker build-->
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${google.jib.version}</version>
                <configuration>
                    <from>
                        <image>${base.font.image}</image>
                    </from>
                    <to>
                        <image>${to.image}/${package.name}:${build.version}</image>
                    </to>
                    <extraDirectories>
                        <paths>
                            <path>
                                <from>${project.build.directory}/bin</from>
                                <into>${docker.apphome}/${package.name}/bin</into>
                            </path>
                        </paths>
                    </extraDirectories>
                    <container>
                        <appRoot>${docker.apphome}/${package.name}</appRoot>
                        <entrypoint>
                            <entry>sh</entry>
                            <entry>${docker.apphome}/${package.name}/bin/start.sh</entry>
                        </entrypoint>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <filesModificationTime>EPOCH_PLUS_SECOND</filesModificationTime>
                        <ports>
                            <port>${application.port}</port>
                        </ports>
                        <environment>
                            <TZ>Asia/Shanghai</TZ>
                        </environment>
                        <labels>
                            <appname>${package.name}</appname>
                        </labels>
                        <workingDirectory>${docker.apphome}</workingDirectory>
                        <format>Docker</format>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <application.port>6001</application.port>
                <package.environment>default</package.environment>
            </properties>
        </profile>
        <profile>
            <id>dev-images</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <application.port>80</application.port>
                <package.environment>default</package.environment>
                <docker.apphome>/app</docker.apphome>
                <to.image>registry.ops.caas.byd.com/leasing-dev</to.image>
                <build.version>1.0.0-dev</build.version>
                <run.jvm.size>1024M</run.jvm.size>
                <MAIN_CLASS>com.ruicar.afs.cloud.afscase.AfsCaseApplication</MAIN_CLASS>
            </properties>
        </profile>
        <profile>
            <id>uat-images</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <application.port>80</application.port>
                <package.environment>default</package.environment>
                <docker.apphome>/app</docker.apphome>
                <to.image>registry.ops.caas.byd.com/leasing-new-uat</to.image>
                <build.version>1.0.0-uat</build.version>
                <run.jvm.size>1024M</run.jvm.size>
                <MAIN_CLASS>com.ruicar.afs.cloud.afscase.AfsCaseApplication</MAIN_CLASS>
            </properties>
        </profile>
        <profile>
            <id>prod-images</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <application.port>80</application.port>
                <package.environment>default</package.environment>
                <docker.apphome>/app</docker.apphome>
                <to.image>registry.ops.caas.byd.com/leasing-new-prod</to.image>
                <build.version>1.0.0-release</build.version>
                <run.jvm.size>1024M</run.jvm.size>
                <MAIN_CLASS>com.ruicar.afs.cloud.afscase.AfsCaseApplication</MAIN_CLASS>
            </properties>
        </profile>
    </profiles>
</project>
