package com.ruicar.afs.cloud.image.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.dispatch.feign.ForProductFeign;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.components.datadicsync.DicHelper;
import com.ruicar.afs.cloud.components.datadicsync.dto.DicDataDto;
import com.ruicar.afs.cloud.filecenter.FileCenterHelper;
import com.ruicar.afs.cloud.filecenter.FileType;
import com.ruicar.afs.cloud.filecenter.dto.UploadResult;
import com.ruicar.afs.cloud.image.condition.RevolveCondition;
import com.ruicar.afs.cloud.image.condition.ShowFileCondition;
import com.ruicar.afs.cloud.image.condition.UploadCondition;
import com.ruicar.afs.cloud.image.config.FileProperties;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.entity.ComAttachmentManagement;
import com.ruicar.afs.cloud.image.enums.AttachmentClassEnum;
import com.ruicar.afs.cloud.image.enums.AttachmentUniqueCodeEnum;
import com.ruicar.afs.cloud.image.enums.BusiNodeEnum;
import com.ruicar.afs.cloud.image.enums.FileStatusEnum;
import com.ruicar.afs.cloud.image.enums.FileTypeEnum;
import com.ruicar.afs.cloud.image.enums.IsElectronicEnum;
import com.ruicar.afs.cloud.image.feign.FileCenterFeign;
import com.ruicar.afs.cloud.image.service.ComAttaManageService;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;
import com.ruicar.afs.cloud.image.service.ComAttachmentManagementService;
import com.ruicar.afs.cloud.image.service.FileUploadCommonService;
import com.ruicar.afs.cloud.image.vo.FileTempVo;
import com.ruicar.afs.cloud.image.vo.UploadInputVo;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description:
 * @Author: fangchenliang
 * @Date: 2020/6/10 17:29
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/upload")
@Api(value = "image", description = "影像上传")
public class AfsUploadFileController {

    private final ComAttaManageService comAttaManageService;
    private final ComAttachmentFileService comAttachmentFileService;
    private final ComAttachmentManagementService comAttachmentManagementService;
    private final FileCenterFeign fileCenterFeign;
    private final ForProductFeign caseFeign;
    private final FileUploadCommonService fileUploadCommonService;
    private FileProperties fileProperties;

    @PostMapping(value = "/getUploadList")
    @ApiOperation(value = "根据业务场景和前端参数获取影像件类型")
    public IResponse<List<ComAttachmentManagement>> getUploadList(@RequestBody UploadCondition uploadCondition) {

        /**影像件上传列表由两部分组成：
         * 1.已上传文件的影像件类型，必须全部展示,是否标识是否必传，需要跑规则;
         *  （主要是由于审批更改融资信息之后退回重新上传，与第一次跑规则出来的影像件类型会存在差异，但之前上传的文件必须展示）;
         * 2.通过业务参数跑批出来的影像件类型。
         */
        String busiNo = uploadCondition.getBusiNo();
        String busiType = uploadCondition.getBusiType();
        List<ComAttachmentManagement> uploadListData = null;
        uploadCondition.resetJsonObj();
        if (busiNo != null && busiType != null) {
            List<ComAttachmentManagement> attachmentManagementFileList = comAttaManageService.getAllFileList(uploadCondition.getBusiNo(), uploadCondition.getBusiType());
            if (attachmentManagementFileList.size() > 0) {
                attachmentManagementFileList.forEach(attachmentManagement -> {
                    comAttaManageService.comAttachmentManagementHitRule(uploadCondition.getBusiData(), attachmentManagement);
                });
            }
            List<ComAttachmentManagement> attachmentManagementRuleList = comAttaManageService.getAttaListByRule(uploadCondition.getBusiData(), uploadCondition.getBusiType());
            Map<Long, ComAttachmentManagement> attaFileMap = attachmentManagementFileList.stream().collect(Collectors.toMap(ComAttachmentManagement::getId, comAttachmentManagement -> comAttachmentManagement));
            Iterator it = attachmentManagementRuleList.iterator();
            /**合并两部分的影像件类型*/
            while (it.hasNext()) {
                ComAttachmentManagement comAttachmentManagement = (ComAttachmentManagement) it.next();
                if (!attaFileMap.containsKey(comAttachmentManagement.getId())) {
                    attachmentManagementFileList.add(comAttachmentManagement);
                }
            }
            uploadListData = comAttaManageService.invokeParent(attachmentManagementFileList);
        }
        return IResponse.success(uploadListData);

    }

    @PostMapping(value = "/getUploadListByGroup")
    @ApiOperation(value = "根据业务场景和前端参数获取影像件类型")
    public IResponse<List<ComAttachmentManagement>> getUploadListByGroup(@RequestBody UploadCondition uploadCondition) {
        List<ComAttachmentManagement> uploadListData = null;
        String busiNo = uploadCondition.getBusiNo();
        String busiType = uploadCondition.getBusiType();
        String belongNo = uploadCondition.getBelongNo();
        uploadCondition.resetJsonObj();
        log.info("根据业务场景和前端参数获取影像件类型，规则报文:{}", uploadCondition.getBusiData());
        if (busiNo != null && busiType != null) {
            String attachmentId = uploadCondition.getAttachmentId();
            List<ComAttachmentManagement> managementList = new ArrayList<>();
            if (StringUtils.isNotBlank(attachmentId)) {
                managementList = comAttaManageService.getManagementById(attachmentId);
            } else {
                managementList = comAttaManageService.getManagementByRuleGroup(uploadCondition.getBusiData(), uploadCondition.getBusiType());
            }
            if (managementList.size() > 0) {
                List<String> attachmentIds = managementList.stream().map(m -> String.valueOf(m.getId())).collect(Collectors.toList());
                List<ComAttachmentFile> allFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .in(ComAttachmentFile::getAttachmentCode, attachmentIds)
                        .eq(ComAttachmentFile::getBusiNo, busiNo)
                        .eq(ComAttachmentFile::getBelongNo, belongNo));
                if (allFileList.size() > 0) {
                    Map<String, List<ComAttachmentFile>> fileMap = allFileList.stream().collect(Collectors.groupingBy(ComAttachmentFile::getAttachmentCode));
                    managementList.forEach(comAttachmentManagement -> {
                        List<ComAttachmentFile> fileList = fileMap.get(String.valueOf(comAttachmentManagement.getId()));
                        if (!CollectionUtils.isEmpty(fileList)) {
                            List<ComAttachmentFile> standardFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.STANDARD.getCode())).collect(Collectors.toList());
                            List<ComAttachmentFile> draftFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.DRAFT.getCode())).collect(Collectors.toList());
                            List<ComAttachmentFile> waitApproveFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.WAITAPPROVE.getCode())).collect(Collectors.toList());
                            List<ComAttachmentFile> reviseFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.REVISE.getCode())).collect(Collectors.toList());
                            List<ComAttachmentFile> reviseWaitApproveFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.REVISEWAITAPPROVE.getCode())).collect(Collectors.toList());
                            List<ComAttachmentFile> electronicFiles = fileList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.NOTSTANDARD.getCode()))
                                    .filter(comfile -> IsElectronicEnum.YES.getCode().equals(comfile.getIsElectronic())).collect(Collectors.toList());
                            comAttachmentManagement.setFileList(fileList);
                            comAttachmentManagement.setValidNums((CollectionUtils.isEmpty(standardFiles) ? 0 : standardFiles.size())
                                    + (CollectionUtils.isEmpty(draftFiles) ? 0 : draftFiles.size())
                                    + (CollectionUtils.isEmpty(waitApproveFiles) ? 0 : waitApproveFiles.size())
                                    + (CollectionUtils.isEmpty(reviseFiles) ? 0 : reviseFiles.size())
                                    + (CollectionUtils.isEmpty(reviseWaitApproveFiles) ? 0 : reviseWaitApproveFiles.size())
                                    + (CollectionUtils.isEmpty(electronicFiles) ? 0 : electronicFiles.size()));
                        }
                    });
                }
            }
            log.info("此时的managementList={}", managementList);
            uploadListData = comAttaManageService.invokeParent(managementList);
        }

        log.info("此时的uploadListData={}", uploadListData);
        Map<String, List<DicDataDto>> listMap = DicHelper.getDicMaps("ComplianceRequirements");
        List<DicDataDto> list = listMap.get("ComplianceRequirements");
        // 目前该逻辑只在进件执行
        if(StringUtils.isNotBlank(busiType) && "orderApply".equals(busiType)){
            // 删除所有没有被命中的驾驶证文件信息
            if(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotEmpty(uploadListData) && uploadListData.size() > 0){
                // 判断信息中是否存在驾驶证信息
                ComAttachmentManagement comAttachmentManagement = uploadListData.get(0);
                List<ComAttachmentManagement> children = comAttachmentManagement.getChildren();

                // 进件提交时信鸽流水类替代收入证明
                int count = 0;
                boolean xgFlag = false;
                boolean tFlag = true;
                // 是否删除多余的附件，命中则不删除
                boolean rmFaceFlag = true;
                boolean rmIncomeFlag = true;
                for (int i = 0; i < children.size(); i++) {
                    ComAttachmentManagement com = children.get(i);
                    if ("6887250470382352700".equals(String.valueOf(com.getId()))) {
                        tFlag = false;
                    }
                    if (AttachmentUniqueCodeEnum.APPLY_INCOME.getCode().equals(com.getUniqueCode())) {
                        count = i;
                        xgFlag = true;
                        rmIncomeFlag = false;
                    }
                    if ("面签照".equals(com.getAttachmentName()) && BusiNodeEnum.ORDER_APPLY.getCode().equals(com.getBusiNode())) {
                        rmFaceFlag = false;
                    }
                    for (DicDataDto dto : list){
                        if (children.get(i).getAttachmentName().contains(dto.getTitle())){
                            children.get(i).setComplianceRequirements(dto.getValue());
                        }
                    }
                }
                if(tFlag){

                    // 查询影像件目录id
                    List<ComAttachmentManagement> tComAttachmentManagementList = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));
                    if(ObjectUtil.isNotEmpty(tComAttachmentManagementList) && tComAttachmentManagementList.size() > 0){

                        Long id = tComAttachmentManagementList.get(0).getId();
                        List<ComAttachmentFile> tComAttachmentFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery().eq(ComAttachmentFile::getBusiNo, busiNo).eq(ComAttachmentFile::getAttachmentCode, String.valueOf(id)));
                        if(ObjectUtil.isNotEmpty(tComAttachmentFileList) && tComAttachmentFileList.size() > 0){
							log.info("规则变更删除驾驶证影像件信息,busiNo={},attachmentCode={},文件个数={}", busiNo, id, tComAttachmentFileList.size());
							comAttachmentFileService.removeBatchByIds(tComAttachmentFileList);
                        }
                    }
                }
                if (rmFaceFlag) {
                    // 删除面签照
                    rmFaceIfNoPoint(busiNo);
                }
                if (rmIncomeFlag) {
                    // 删除收入证明
                    rmIncomeIfNoPoint(busiNo);
                }
                if(xgFlag && checkCarrierPigeonInfo(busiNo,uploadCondition.getBusiData().getString("idCard"))){
                    children.remove(count);
                    comAttachmentManagement.setChildren(children);
                }

            }else {

                List<ComAttachmentManagement> tComAttachmentManagementList = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery().eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.DRIVER_LICENSE.getCode()));
                if(ObjectUtil.isNotEmpty(tComAttachmentManagementList) && tComAttachmentManagementList.size() > 0){

                    Long id = tComAttachmentManagementList.get(0).getId();
                    List<ComAttachmentFile> tComAttachmentFileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>lambdaQuery().eq(ComAttachmentFile::getBusiNo, busiNo).eq(ComAttachmentFile::getAttachmentCode, String.valueOf(id)));
                    if(ObjectUtil.isNotEmpty(tComAttachmentFileList) && tComAttachmentFileList.size() > 0){
						log.info("规则变更删除驾驶证影像件信息,busiNo={},attachmentCode={},文件个数={}", busiNo, id, tComAttachmentFileList.size());
						comAttachmentFileService.removeBatchByIds(tComAttachmentFileList);
                    }
                }
                // 删除面签照 + 流水
                rmFaceIfNoPoint(busiNo);
                rmIncomeIfNoPoint(busiNo);
            }
        }
        return IResponse.success(uploadListData);
    }

    public void rmIncomeIfNoPoint(String busiNo) {
        List<ComAttachmentManagement> comAttachmentManagementList = comAttaManageService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.APPLY_INCOME.getCode()));
        boolean remove = comAttachmentFileService.remove(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBusiNo, busiNo)
                .eq(ComAttachmentFile::getAttachmentCode, comAttachmentManagementList.get(0).getId()));
        log.info("申请编号为{}-因规则没有命中-删除收入证明结果：{}", busiNo, remove);
    }

    public void rmFaceIfNoPoint(String busiNo) {
        List<ComAttachmentManagement> comAttachmentManagementList = comAttaManageService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getAttachmentName, "面签照")
                .eq(ComAttachmentManagement::getBusiNode, BusiNodeEnum.ORDER_APPLY.getCode()));
        boolean remove = comAttachmentFileService.remove(Wrappers.<ComAttachmentFile>lambdaQuery()
                .eq(ComAttachmentFile::getBusiNo, busiNo)
                .eq(ComAttachmentFile::getAttachmentCode, comAttachmentManagementList.get(0).getId()));
        log.info("申请编号为{}-因规则没有命中-删除面签照结果：{}", busiNo, remove);
    }

    /**
     *
     * @param busNo 业务号
     * @return boolean
     */
    private boolean checkCarrierPigeonInfo(String busNo,String certNo) {
        return caseFeign.checkApplyFlow(busNo,certNo);
    }

    @PostMapping(value = "/getAllFileList")
    @ApiOperation(value = "根据业务场景和业务编号号获取影像件文件和类型")
    public IResponse<List<ComAttachmentManagement>> getAllFileList(String busiNo, String busiType) {
        List<ComAttachmentManagement> uploadFileList = new ArrayList<>();
        Map map = new HashMap();
        map.put("busiNode", busiType.split(","));
        List list = Arrays.asList(busiType.split(","));
        List<ComAttachmentFile> fileList = comAttachmentManagementService.findUploadFileByBusiNo(busiNo, list);
        Map<String, List<ComAttachmentFile>> filesMap = fileList.stream().collect(Collectors.groupingBy(ComAttachmentFile::getAttachmentCode));
        Iterator it = filesMap.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next().toString();
            List<ComAttachmentManagement> attaList = comAttaManageService.list(
                    Wrappers.<ComAttachmentManagement>query().lambda()
                            .eq(ComAttachmentManagement::getId, Long.valueOf(key)));
            attaList.get(0).setFileList(filesMap.get(key));
            uploadFileList = Stream.of(uploadFileList, attaList)
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return IResponse.success(uploadFileList);
    }

    @PostMapping(value = "/saveAttachmentFile")
    @ApiOperation(value = "上传影像件")
    public IResponse<ComAttachmentFile> uploadFile(@RequestParam("attachmentId") String attachmentId,
                                                   @RequestParam("attachmentName") String attachmentName,
                                                   @RequestParam("fileType") String fileType,
                                                   @RequestParam("fileName") String fileName,
                                                   @RequestParam(name = "fileUniqueId", required = false, defaultValue = "") String fileUniqueId,
                                                   @RequestParam(name = "fileMd5", required = false, defaultValue = "") String fileMd5,
                                                   @RequestParam("applyNo") String busiNo,
                                                   @RequestParam("belongNo") String belongNo) {
        UploadInputVo uploadInputVo = new UploadInputVo();
        ComAttachmentManagement comAttachmentManagement = comAttaManageService.getById(Long.valueOf(attachmentId));
        boolean isAllow = false;
        if (StringUtils.isNotBlank(comAttachmentManagement.getAllowFileType())) {
            String[] allowFileType = comAttachmentManagement.getAllowFileType().split(",");
            for (String value : allowFileType) {
                if (fileType != null && AfsEnumUtil.desc(AfsEnumUtil.getEnum(value, FileTypeEnum.class)).toLowerCase().contains(fileType.toLowerCase())) {
                    isAllow = true;
                }
            }
        } else {
            isAllow = true;
        }
        if (isAllow) {
            uploadInputVo.setAttachmentId(attachmentId);
            uploadInputVo.setAttachmentName(attachmentName);
            uploadInputVo.setFileType(fileType);
            uploadInputVo.setFileName(fileName);
            uploadInputVo.setFileMd5(fileMd5);
            uploadInputVo.setFileUniqueId(fileUniqueId);
            uploadInputVo.setBusiNo(busiNo);
            uploadInputVo.setBelongNo(belongNo);
            uploadInputVo.setArchiveClass(comAttachmentManagement.getArchiveClass());
            uploadInputVo.setFileSource("com_attachment_management");
            try {
                //判断流水是否加密
                List<ComAttachmentManagement> list = comAttachmentManagementService.list(Wrappers.<ComAttachmentManagement>lambdaQuery()
                        .eq(ComAttachmentManagement::getUniqueCode, AttachmentUniqueCodeEnum.BANK_STATEMENT.getCode()));
                if (CollectionUtil.isNotEmpty(list)){
                    if (StrUtil.equals(String.valueOf(list.get(0).getId()),attachmentId) && StrUtil.equals(fileType.toLowerCase(),"pdf")){
                        String tempPath = fileProperties.getTempDir() + fileName + "." + fileType;
                        //下载文件
                        FileCenterHelper.downLoadFile(fileUniqueId, FileType.ORIGINAL,tempPath);
                        if (isPDFEncrypted(tempPath)){
                            uploadInputVo.setIsEncrypted("1");
                        } else {
                            uploadInputVo.setIsEncrypted("0");
                        }
                        FileUtil.del(tempPath);
                    }
                }
                ComAttachmentFile file = comAttachmentFileService.saveComAttachmentFile(uploadInputVo);
                return IResponse.success(file);
            } catch (Exception e) {
                return IResponse.fail(e.getMessage());
            }
        } else {
            return IResponse.fail("该类型不支持【" + fileType + "】文件格式");
        }
    }

    @PostMapping("checkPasswordCorrect")
    @ApiOperation("校验密码是否正确")
    public IResponse checkPasswordCorrect(@RequestParam("applyNo") String applyNo,
                                          @RequestParam("fileId") String fileId,
                                          @RequestParam("secretCode") String secretCode){
        String tempPath = fileProperties.getTempDir() + fileId + ".pdf";
        FileCenterHelper.downLoadFile(fileId, FileType.ORIGINAL,tempPath);
        boolean flag = isPasswordCorrect(tempPath,secretCode);
        if (flag){
            ComAttachmentFile comAttachmentFile = comAttachmentFileService.getOne(Wrappers.<ComAttachmentFile>lambdaQuery()
                    .eq(ComAttachmentFile::getBusiNo,applyNo)
                    .eq(ComAttachmentFile::getFileId,fileId));
            comAttachmentFile.setSecretCode(secretCode);
            comAttachmentFile.setIsEncrypted("2");
            comAttachmentFileService.updateById(comAttachmentFile);
        }
        FileUtil.del(tempPath);
        return IResponse.success(flag);
    }

    public static boolean isPasswordCorrect(String filePath, String password) {
        try {
            // 尝试用密码加载文档
            PDDocument document = PDDocument.load(new File(filePath), password);
            document.close();
            return true;
        } catch (IOException e) {
            // 检查是否是密码错误导致的异常
            if (e.getMessage().contains("password") || e.getMessage().contains("decrypt")) {
                return false;
            }
            // 其他类型的IO异常
            log.error("Error checking PDF password:{}",e);
            return false;
        }
    }

    public static boolean isPDFEncrypted(String filePath) {
        try {
            // 尝试无密码加载
            try (PDDocument document = PDDocument.load(new File(filePath))) {
                if (document.isEncrypted()) {
                    return false;
                } else {
                    return false;
                }
            }
        } catch (InvalidPasswordException e) {
            return true;
        } catch (IOException e) {
            log.error("处理PDF时出错:{}",e);
        }
        return true;
    }

    @PostMapping(value = "/viewFile")
    @ApiOperation(value = "预览图片")
    public IResponse<ComAttachmentManagement> downloadFileByType(@RequestBody ShowFileCondition showFileCondition) {
        String busiNo = showFileCondition.getBusiNo();
        String belongNo = showFileCondition.getBelongNo();
        String version = StringUtils.isBlank(showFileCondition.getVersion()) ? "" : showFileCondition.getVersion();
        ComAttachmentManagement comAttachmentManagement = showFileCondition.getComAttachmentManagement();
        List<ComAttachmentFile> outList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(comAttachmentManagement)) {
            outList = comAttaManageService.getFileList(outList, busiNo, belongNo, comAttachmentManagement, version);
        }
        if (!CollectionUtils.isEmpty(outList)) {
            List<ComAttachmentFile> standardFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.STANDARD.getCode())).collect(Collectors.toList());
            List<ComAttachmentFile> draftFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.DRAFT.getCode())).collect(Collectors.toList());
            List<ComAttachmentFile> waitApproveFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.WAITAPPROVE.getCode())).collect(Collectors.toList());
            List<ComAttachmentFile> reviseFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.REVISE.getCode())).collect(Collectors.toList());
            List<ComAttachmentFile> reviseWaitApproveFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.REVISEWAITAPPROVE.getCode())).collect(Collectors.toList());
            List<ComAttachmentFile> electronicFiles = outList.stream().filter(comfile -> comfile.getFileStatus().equals(FileStatusEnum.NOTSTANDARD.getCode()))
                    .filter(comfile -> IsElectronicEnum.YES.getCode().equals(comfile.getIsElectronic())).collect(Collectors.toList());
            comAttachmentManagement.setValidNums((CollectionUtils.isEmpty(standardFiles) ? 0 : standardFiles.size())
                    + (CollectionUtils.isEmpty(draftFiles) ? 0 : draftFiles.size())
                    + (CollectionUtils.isEmpty(waitApproveFiles) ? 0 : waitApproveFiles.size())
                    + (CollectionUtils.isEmpty(reviseFiles) ? 0 : reviseFiles.size())
                    + (CollectionUtils.isEmpty(reviseWaitApproveFiles) ? 0 : reviseWaitApproveFiles.size())
                    + (CollectionUtils.isEmpty(electronicFiles) ? 0 : electronicFiles.size()));
        }
        if (!ObjectUtils.isEmpty(comAttachmentManagement)) {
            comAttachmentManagement.setFileList(outList);
        }
        return IResponse.success(comAttachmentManagement);
    }

    @GetMapping(value = "/getBlob/{fileType}/{accessKey}")
    @ApiOperation(value = "预览图片")
    public void getBlob(HttpServletResponse response, @PathVariable("accessKey") String accessKey, @PathVariable("fileType") String fileType) throws Exception {

        if ("pdf".equals(fileType)) {
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
        } else if ("mp4".equals(fileType)) {
            response.setContentType("video/mp4");
        } else if ("png".equals(fileType) || "PNG".equals(fileType)) {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
        } else if ("jpg".equalsIgnoreCase(fileType) || "jpeg".equalsIgnoreCase(fileType)) {
            response.setContentType(MediaType.IMAGE_JPEG_VALUE);
        } else {
            response.setContentType(MediaType.ALL_VALUE);
        }
        try {
            Response fileRes = fileCenterFeign.simpleDownFile(AfsEnumUtil.key(FileType.ORIGINAL), accessKey);
            response.getOutputStream().write(fileRes.body().asInputStream().readAllBytes());
        } catch (Exception exception) {
            log.error("文件获取失败", exception);
            if (exception instanceof HttpClientErrorException) {
                HttpClientErrorException httpClientErrorException = ((HttpClientErrorException) exception);
                response.setStatus(httpClientErrorException.getRawStatusCode());
                response.getOutputStream().write(httpClientErrorException.getResponseBodyAsByteArray());
            } else {
                response.setStatus(HttpStatus.NOT_FOUND.value());
            }
        }
        response.getOutputStream().flush();
    }

    @PostMapping(value = "/updateFile")
    @ApiOperation(value = "更新图片信息")
    public IResponse<ComAttachmentFile> updateFile(@RequestBody JSONObject fileParam) {
        JSONArray jsonArray = fileParam.getJSONArray("fileList");
        String attachmentId = fileParam.getString("attachmentId");
        log.info(fileParam.toString());
        String[] msg = {""};
        ComAttachmentManagement management = comAttaManageService.getOne(Wrappers.<ComAttachmentManagement>lambdaQuery()
                .eq(ComAttachmentManagement::getId, Long.valueOf(attachmentId)), false);
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, String> filemap = (Map<String, String>) jsonArray.get(i);
                ComAttachmentFile file = comAttachmentFileService.getById(Long.valueOf(filemap.get("id")));
                if (file != null) {
                    file.setAttachmentCode(attachmentId);
                    file.setAttachmentName(management.getAttachmentName());
                    file.setArchiveClass(management.getArchiveClass() == null ? "" : management.getArchiveClass());
                    comAttachmentFileService.updateById(file);
                    msg[0] = "更新成功";
                } else {
                    msg[0] = "未找到相应图片:" + filemap.get("id");
                }
            }
        }
        return IResponse.success(msg[0]);
    }

    @PostMapping(value = "/deleteFile")
    @ApiOperation(value = "更新图片信息")
    public IResponse<?> deleteFile(@RequestBody JSONObject fileList) {
        JSONArray jsonArray = fileList.getJSONArray("fileList");
        log.info(fileList.toString());
        String res="删除成功";
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, String> filemap = (Map<String, String>) jsonArray.get(i);
                ComAttachmentFile file = comAttachmentFileService.getById(Long.valueOf(filemap.get("id")));
                if (file == null) {
                    res = "未找到相应图片:" + filemap.get("id");
                    break;
                } else if (FileStatusEnum.STANDARD.getCode().equals(file.getFileStatus())) {
                    log.error("当前影像件{}合格,无法删除",filemap.get("id"));
                    res = "当前影像状态为合格，无法删除";
                    break;
                } else {
                    comAttachmentFileService.removeById(file);
                }
            }
        }
        return IResponse.success(res);
    }

    @PostMapping(value = "/deleteFiles")
    @ApiOperation(value = "删除图片信息（包含文件中心）")
    public IResponse<String> deleteFiles(@RequestBody List<String> accessKeys) {
        IResponse response = fileCenterFeign.deleteFiles(accessKeys);

        if (CommonConstants.FAIL.equals(response.getCode())) {
          return IResponse.fail("删除失败");
        }

        List<ComAttachmentFile> comAttachmentFiles = comAttachmentFileService.getComAttachmentFileByFileIds(accessKeys);
        if (CollUtil.isNotEmpty(comAttachmentFiles)) {
            comAttachmentFileService.removeBatchByIds(comAttachmentFiles);
        }

        return IResponse.success("删除成功");
    }

    @PostMapping(value = "/checkFile")
    @ApiOperation(value = "根据前端参数检查文件是否已上传完整")
    public IResponse checkFile(@RequestBody UploadCondition uploadCondition) {
        String busiNo = uploadCondition.getBusiNo();
        String busiType = uploadCondition.getBusiType();
        String belongNo = uploadCondition.getBelongNo();

        log.info("==={}检查文件是否已上传完整开始==", busiNo);
        uploadCondition.resetJsonObj();
        if (!ObjectUtils.isEmpty(busiNo) && !ObjectUtils.isEmpty(busiType)) {
            List<String> errorMsgList = new ArrayList<>();
            List<ComAttachmentManagement> managementList = comAttaManageService.getManagementByRuleGroup(uploadCondition.getBusiData(), uploadCondition.getBusiType());
            for (ComAttachmentManagement comAttachmentManagement : managementList) {
                // 跳过有信鸽资料的收入类证明，与提交资料保持一致
                if ("收入类材料".equals(comAttachmentManagement.getAttachmentName()) && checkCarrierPigeonInfo(busiNo,
                    uploadCondition.getBusiData().getString("idCard"))) {
                    continue;
                }
                List<String> status = new ArrayList<>();
                status.add(FileStatusEnum.DRAFT.getCode());
                status.add(FileStatusEnum.STANDARD.getCode());
                status.add(FileStatusEnum.REVISE.getCode());
                status.add(FileStatusEnum.WAITAPPROVE.getCode());
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query().lambda()
                        .eq(ComAttachmentFile::getAttachmentCode, String.valueOf(comAttachmentManagement.getId()))
                        .eq(ComAttachmentFile::getBusiNo, busiNo)
                        .eq(ComAttachmentFile::getBelongNo, belongNo)
                        .and(wrapper -> wrapper.in(ComAttachmentFile::getFileStatus, status)
                                .or()
                                .eq(ComAttachmentFile::getIsElectronic, IsElectronicEnum.YES.getCode()).ne(ComAttachmentFile::getFileStatus, FileStatusEnum.DISCARD.getCode())));
                log.info("{}=>{}文件校验数量:{},必传数量:{}", busiNo, comAttachmentManagement.getAttachmentName(), fileList.size(), comAttachmentManagement.getNeedNums());
                comAttachmentManagement.setFileList(fileList);
                if (comAttachmentManagement.getAttachmentClass() == AttachmentClassEnum.SUBCLASS.getCode() && comAttachmentManagement.getNeedNums() > fileList.size()) {
                    errorMsgList.add(comAttachmentManagement.getAttachmentName());
                }
                if (comAttachmentManagement.getAttachmentClass() == AttachmentClassEnum.SUBCLASS.getCode() && comAttachmentManagement.getMaxFileNums() < fileList.size()) {
                    errorMsgList.add(comAttachmentManagement.getAttachmentName());
                }
            }

            Map<Long, List<ComAttachmentManagement>> subclassAttachmentManagementMaps = managementList.stream()
                    .collect(Collectors.groupingBy(ComAttachmentManagement::getParentId));
            managementList.forEach(management -> {
                if (management.getAttachmentClass() == AttachmentClassEnum.GROUP.getCode()) {
                    int filenums = 0;
                    List<ComAttachmentManagement> attachmentManagementList = subclassAttachmentManagementMaps.get(management.getId());
                    if (!CollectionUtils.isEmpty(attachmentManagementList)) {
                        for (ComAttachmentManagement comAttachmentManagement : attachmentManagementList) {
                            if (comAttachmentManagement.getFileList().size() > 0) {
                                filenums = filenums + 1;
                            }
                        }
                        if (management.getNeedNums() > filenums) {
                            errorMsgList.add(management.getAttachmentName());
                        }
                        if (management.getMaxFileNums() < filenums) {
                            errorMsgList.add(management.getAttachmentName());
                        }
                    }
                }
            });
            if (errorMsgList.size() > 0) {
                return IResponse.fail(errorMsgList + "文件不全或者不符合要求！");
            } else {
                List<Long> fileListIds = managementList.stream()
                    .map(ComAttachmentManagement::getFileList)
                    .flatMap(Collection::stream)
                    .map(ComAttachmentFile::getId)
                    .toList();
                List<ComAttachmentFile> fileList = comAttachmentFileService.list(Wrappers.<ComAttachmentFile>query()
                    .lambda()
                    .eq(ComAttachmentFile::getBusiNo, busiNo)
                    .eq(ComAttachmentFile::getBelongNo, belongNo));
                fileList.stream().filter(s -> !fileListIds.contains(s.getId())).forEach(s -> {
                    // 智能初审跳过类型
                    s.setRemake("noRe");
                    comAttachmentFileService.updateById(s);
                });
                return IResponse.success("文件校验成功！");
            }
        } else {
            return IResponse.fail(busiNo + "未找相应数据,请核实附件信息！");
        }
    }

    @PostMapping(value = "/revolveFile")
    @ApiOperation(value = "保存成功")
    public IResponse revolveFile(@RequestBody RevolveCondition revolveCondition) {
        String md5 = revolveCondition.getFileId();
        String fileName = revolveCondition.getFileName();
        String fileType = revolveCondition.getFileType();
        BigDecimal de = new BigDecimal(revolveCondition.getDegree());
        BigDecimal[] results = de.divideAndRemainder(BigDecimal.valueOf(360));
        int degree = 0;
        if (results[1].compareTo(BigDecimal.ZERO) < 0) {
            degree = results[1].add(BigDecimal.valueOf(360)).intValue();
        } else {
            degree = results[1].intValue();
        }
        UploadResult uploadResult = comAttachmentFileService.revolveFile(md5, fileName, fileType, degree);
        log.info("旋转后上传OSS返回报文：{}", uploadResult);
        ComAttachmentFile comAttachmentFile = comAttachmentFileService.getById(revolveCondition.getId());
        comAttachmentFile.setFileId(uploadResult.getKey());
        comAttachmentFileService.updateById(comAttachmentFile);
        return IResponse.success("保存成功！");
    }

    /**
     * 文件上传，无需加密签名
     * @param file
     * @return
     */
    @PostMapping(value = "/simpleUploadFile",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public IResponse<?> simpleUploadFile(@RequestParam("file") MultipartFile file) {
        return fileUploadCommonService.simpleUploadFile(file);
    }

    /**
     * 文件下载
     * @param fileTempVo
     * @param response
     */
    @PostMapping(value = "/simpleDownFile")
    public void simpleDownFile(@RequestBody FileTempVo fileTempVo,HttpServletResponse response) {
        fileUploadCommonService.simpleDownFile(fileTempVo,response);
    }
}
